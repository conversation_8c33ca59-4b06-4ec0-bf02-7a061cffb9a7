/**
 * @Date: 2020/12/8 20:33
 * @Author: xiangrui.hu
 * @Description //
 **/

package main

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	_ "git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"google.golang.org/grpc"
	"log"
	"os"
	"strings"
)

func main() {
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if strings.Contains(strings.ToLower(envvar.GetModuleName()), "livetest") {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			log.Fatalf("write chassis envvar error: %v", err)
		}
		err = os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower()+"_livetest")
		if err != nil {
			fmt.Printf("write chassis envvar error: %v", err)
		}
	} else {
		err := os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower())
		if err != nil {
			fmt.Printf("write chassis envvar error: %v", err)
		}
	}

	// 拦截器
	unaryServerInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		return handler(ctx, req)
	}

	chassis.RegisterSchema("rest", InitPingResource())
	// init
	if err := change_report.InitChangeReport(); err != nil {
		log.Fatalf("init change report failed %+v", err)
	}
	if err := change_report.RegisterHttpChangeReportHandler(); err != nil {
		log.Fatalf("registry change report failed %+v", err)
	}
	handler.RegisterPrometheusMetricHandler()

	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("batask_server"),
		chassis.WithGRPCUnaryServerInterceptor(unaryServerInterceptor), // grpc拦截器
		chassis.WithDefaultProviderHandlerChain(
			change_report.ChangeReportHandlerName,
			handler.NameOfPrometheusMetricProvider,
		),
		chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		),
	); err != nil {
		log.Fatalf("Init failed with err: %s\n", err.Error())
		return
	}
	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
	}
	if err := dbutil.InitBATaskDb(); err != nil {
		log.Fatalf("initialize lfs database connections fail, error:%+v", err)
	}

	if err := localcache.Init(lcregistry.LocalCacheConfig...); err != nil {
		log.Fatalf("initialize local cache fail, error:%+v", err)
	}
	if err := redisutil.InitDefaultClient(); err != nil {
		log.Fatalf("initialize redis fail, error:%+v", err)
	}
	if err := kafkahelper.InitSaturnProducer(); err != nil {
		log.Fatalf("init saturn producer failed %v", err)
	}
	service := InitSaturnService()
	//注册saturn服务
	service.RegisterBATaskSaturn()
	if err := prometheusutil.InitBATaskMetrics(); err != nil {
		log.Fatalf("init prometheus failed %+v", err)
	}

	if err := chassis.Run(); err != nil {
		log.Fatalf("Run failed with err: %+v", err)
	}

	log.Println("batask init success!!")
}

func InitPingResource() *saturn_facade.PingResource {
	pingResource := &saturn_facade.PingResource{}
	return pingResource
}
