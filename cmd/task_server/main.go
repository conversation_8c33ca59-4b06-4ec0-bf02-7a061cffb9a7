/**
 * @Date: 2020/12/8 20:33
 * @Author: xiangrui.hu
 * @Description //
 **/

package main

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	_ "git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	saturnServer "git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/clickhouseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/esutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"google.golang.org/grpc"
	"log"
	"os"
	"strings"
)

func main() {
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if strings.Contains(strings.ToLower(envvar.GetModuleName()), "livetest") {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			log.Fatalf("write chassis envvar error: %v", err)
		}
	}

	// 拦截器
	unaryServerInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		return handler(ctx, req)
	}

	// livetest envvar change
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if strings.Contains(strings.ToLower(envvar.GetModuleName()), "livetest") {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			log.Fatalf("write chassis envvar error: %v", err)
		}
	}
	chassis.RegisterSchema("rest", InitPingResource())
	// init
	if err := change_report.InitChangeReport(); err != nil {
		log.Fatalf("init change report failed %+v", err)
	}
	if err := change_report.RegisterHttpChangeReportHandler(); err != nil {
		log.Fatalf("registry change report failed %+v", err)
	}

	// 注册recorder handler
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{})

	//注册回放的schema
	saturnServer.RegisterReplaySchemas()

	// 注册replay handler
	handler.RegisterReplayerHandler()

	handler.RegisterPrometheusMetricHandler()

	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("task_server"),
		chassis.WithGRPCUnaryServerInterceptor(unaryServerInterceptor), // grpc拦截器
		chassis.WithDefaultProviderHandlerChain(
			handler.RecorderProviderName, // 录制配置
			handler.RepalyerProviderName, // 回放配置
			change_report.ChangeReportHandlerName,
			handler.NameOfPrometheusMetricProvider,
		),
		chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		),
	); err != nil {
		log.Fatalf("Init failed with err: %s\n", err.Error())
		return
	}
	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
	}
	if err := dbutil.InitTaskDb(); err != nil {
		log.Fatalf("initialize lfs database connections fail, error:%+v", err)
	}

	if err := fileutil.S3Init(); err != nil {
		log.Fatalf("initialize s3 file server fail, error:%+v", err)
	}
	if err := localcache.Init(lcregistry.LocalCacheConfig...); err != nil {
		log.Fatalf("initialize local cache fail, error:%+v", err)
	}
	if err := redisutil.InitDefaultClient(); err != nil {
		log.Fatalf("initialize redis fail, error:%+v", err)
	}
	if err := fileutil.SlsOpsS3Init(); err != nil {
		log.Fatalf("initialize s3 fail, error:%+v", err)
	}
	if err := kafkahelper.InitSaturnProducer(); err != nil {
		log.Fatalf("init saturn producer failed %v", err)
	}
	if err := esutil.InitEsClient(); err != nil {
		log.Fatalf("init es failed %+v", err)
	}
	if err := clickhouseutil.InitClickHouse(); err != nil {
		log.Fatalf("init clickhouse failed %+v", err)
	}
	if err := spex_service.InitSpex(); err != nil {
		log.Fatalf("init spex service failed %+v", err)
	}
	service := InitSaturnService()
	//注册saturn服务
	service.RegisterSaturn()
	if err := prometheusutil.InitTaskMetrics(); err != nil {
		log.Fatalf("init prometheus failed %+v", err)
	}

	//初始化3pl masking hbase配置
	masking_forecast_hbase.InitDataHBase()

	//初始化main集群
	masking_forecast_hbase.InitMainHBase()
	if err := chassis.Run(); err != nil {
		log.Fatalf("Run failed with err: %+v", err)
	}
	log.Println("task init success!!")
}

func InitPingResource() *saturn_facade.PingResource {
	pingResource := &saturn_facade.PingResource{}
	return pingResource
}
