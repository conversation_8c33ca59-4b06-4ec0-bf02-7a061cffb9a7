package main

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	_ "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	_ "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/clickhouseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/esutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"google.golang.org/grpc"
	"log"
	"os"
	"strings"
)

func main() {
	// livetest envvar change
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if strings.Contains(strings.ToLower(envvar.GetModuleName()), "livetest") {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			log.Fatalf("write chassis envvar error: %v", err)
		}
	}

	middleware.RegisterJwtTokenHandler()
	middleware.RegisterRequestContextHandler()
	middleware.RegisterAdminAuditLogHandler()

	// 注册recorder handler
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{})
	// 注册replay handler
	handler.RegisterReplayerHandler()

	if err := change_report.InitChangeReport(); err != nil {
		log.Fatalf("init change report failed %+v", err)
	}
	if err := change_report.RegisterHttpChangeReportHandler(); err != nil {
		log.Fatalf("registry change report failed %+v", err)
	}
	unaryServerInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		return handler(ctx, req)
	}
	handler.RegisterPrometheusMetricHandler() //注册prometheus插件
	handler.RegisterSplitMarketHandler()
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("admin_server"),
		chassis.WithDefaultProviderHandlerChain(
			handler.RecorderProviderName, // 录制配置
			handler.RepalyerProviderName, // 回放配置
			handler.LogProviderHandlerName,
			handler.NameOfSplitMarketHandlerOfProvider,
			middleware.JwtTokenHandleKey,
			middleware.RequestContextHandlerName,
			handler.NameOfPrometheusMetricProvider, //将prometheus handler插件插入到调用链中
			change_report.ChangeReportHandlerName,
			middleware.AdminAuditLogHandlerName,
		),
		chassis.WithDefaultConsumerHandlerChain(
			handler.NameOfPrometheusMetricConsumer,
		),
		chassis.WithGRPCUnaryServerInterceptor(unaryServerInterceptor),
	); err != nil {
		log.Fatalf("chassis init failed with err: %+v", err)
	}

	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
	}
	if err := dbutil.InitAdminDb(); err != nil {
		log.Fatalf("initialize database fail, error:%+v", err)
	}
	if err := localcache.Init(lcregistry.LocalCacheConfig...); err != nil {
		log.Fatalf("initialize local cache fail, error:%+v", err)
	}
	if err := fileutil.S3Init(); err != nil {
		log.Fatalf("initialize s3 fail, error:%+v", err)
	}
	if err := fileutil.SlsOpsS3Init(); err != nil {
		log.Fatalf("initialize s3 fail, error:%+v", err)
	}
	if err := redisutil.InitDefaultClient(); err != nil {
		log.Fatalf("initialize redis fail, error:%+v", err)
	}
	if err := kafkahelper.InitSaturnProducer(); err != nil {
		log.Fatalf("init saturn producer failed %v", err)
	}
	if err := esutil.InitEsClient(); err != nil {
		log.Fatalf("init es client failed %+v", err)
	}
	if err := clickhouseutil.InitClickHouse(); err != nil {
		log.Fatalf("init clickhouse failed %+v", err)
	}
	if err := spex_service.InitSpex(); err != nil {
		log.Fatalf("init spex service failed %+v", err)
	}
	facade, err := InitAdminFacade()
	if err != nil {
		log.Fatalf("init admin facade fail, error:%+v", err)
	}
	facade.Register()

	//初始化metrics
	if err := prometheusutil.InitAdminMetrics(); err != nil {
		log.Fatalf("init metrics:%v, failed, err:%v", constant.PrometheusMetricAdmin, err)
	}

	//初始化3pl masking hbase配置
	masking_forecast_hbase.InitDataHBase()

	//初始化main集群
	masking_forecast_hbase.InitMainHBase()

	name := ""
	addr := ""
	if err := config.UnmarshalConfig("service_description.name", &name); err != nil {
		log.Printf("get service_description.name config error: %v\n", err)
	}
	if err := config.UnmarshalConfig("cse.protocols.rest.listenAddress", &addr); err != nil {
		log.Printf("get cse.protocols.rest.listenAddress config error: %v\n", err)
	}
	log.Printf("[%s] start listen addr: %s\n", name, addr)

	if err := chassis.Run(); err != nil {
		log.Fatalf("Run failed with err: %+v", err)
	}
	log.Println("admin sever init success!!")
}
