//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"github.com/google/wire"
)

func InitAdminFacade() (*adminfacade.AdminFacade, error) {
	wire.Build(
		adminfacade.ProviderSet,
		redisutil.Client,
	)
	return new(adminfacade.AdminFacade), nil
}
