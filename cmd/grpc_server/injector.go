//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	grpc_api "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/grpcfacade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"github.com/google/wire"
)

func InitGrpc() (*grpc_api.GrpcAPI, error) {
	wire.Build(
		grpc_api.ProviderSet,
		redisutil.Client,
	)
	return new(grpc_api.GrpcAPI), nil
}
