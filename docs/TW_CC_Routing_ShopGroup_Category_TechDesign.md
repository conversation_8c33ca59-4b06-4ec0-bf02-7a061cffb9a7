## 技术设计方案：TW CC Routing（按 Shop Group / Category / Weight+Category）

### 文档信息
- 文档类型：技术设计（Technical Design）
- 版本：v1.0
- 作者：SMR-BE
- 评审人：SMR-LPS-OPS（待填）
- 创建日期：2025-07-28
- 参考模板：`https://confluence.shopee.io/pages/viewpage.action?pageId=*********`
- 相关 PRD：`https://confluence.shopee.io/display/SSCP/2025.07+-+TW+CC+Routing+by+shop+group+and+category`

---

## 1. 摘要 / TL;DR
在 CC Routing 模块新增 3 个路由维度（Shop Group、Category、Weight+Category），提供批量导入与强校验，并在运行期为 TW 提供“建议 CC”能力以优化成本；预委后 SelectLane 的 CCFilter 保持不变。同时补充监控，比较“建议 CC”与预委实际 CC 的一致性，辅助跟踪效果。

---

## 2. 背景与问题陈述
1) “空转海/海转空”的成本优化在 TW 有显著收益，但当前 CC Routing 仅支持按 weight 维度，无法描述店铺分组、类目等更精细的分配策略。
2) 预委场景下，CC 在上游已固定（customs service pre-auth），无法 reroute；因此需在预委前提供建议 CC，并在配置层面提高灵活度。

---

## 3. 目标与非目标
### 3.1 目标
- 新增路由类型：`Shop Group`、`Category ID`、`Weight Range + Category ID`。
- 管理端支持单条配置与批量导入（含模板下载与强校验）、CC 下拉列表、查看页展示。
- 运行期在 TW 按新类型决策“建议 CC”，兼容既有 `Fixed / Weight`。
- 监控对比“建议 CC”与预委后实际 CC（来自 customs service），输出不一致告警。

### 3.2 非目标
- 非 TW 地区的立即推广（后续复用本架构）。
- 改变 SelectLane 的预委后 CCFilter 行为（保持不变）。

---

## 4. 业务与功能需求（要点）
- 路由类型新增三类，配置时 Default 行必填且不可删除。
- Weight+Category：同类目下重量区间需首尾相接、无重叠/遗漏；命中规则 `(min, max]`。
- Shop Group 依赖 `ClientTag=8 (CB CC Allocation)`，优先保证 shop → group 唯一归属（默认 `client_in_unique_group_toggle=true`）。
- Admin 支持批量导入（xlsx/csv）与模板下载；“Allocate to” 的 CC 列表从 CC 服务实时拉取。

---

## 5. 整体架构与流程

### 5.1 架构概览
```mermaid
flowchart TD
    FE[Admin FE] -->|导入/配置| SMRAdmin[SMR Admin API]
    SMRAdmin -->|保存 JSON 规则| DB[(cc_routing_rule_tab)]
    SMRAdmin -->|刷新本地缓存| SMRCache[Local Cache]

    subgraph 预委前-建议CC
      OMS[上游/OMS] -->|gRPC CCRouting| SMRCC[SMR CCRouting Service]
      SMRCC -->|ShopId→Group| LPS[LPS API,Tag=8]
      SMRCC -->|拉取规则| SMRCache
      SMRCC -->|返回建议CC| OMS
    end

    subgraph 预委后-SelectLane
      LFS[LFS] -->|SelectLane| SMRSelect[SMR SelectLane]
      SMRSelect -->|Get Pre-Auth CC| Customs[Customs Service]
      SMRSelect -->|按CC过滤| Routing[Routing Core]
      Routing --> LFS
    end

    SMRSelect -. 可选上报 .-> Mon[Monitoring]
    SMRCC -. 可选上报 .-> Mon
```

### 5.2 交互序列（PUML）
```puml
@startuml
actor FE
participant "SMR Admin" as Admin
database "cc_routing_rule_tab" as DB
participant "Local Cache" as Cache

FE -> Admin: 上传ShopGroup/Category/Weight+Category规则文件
Admin -> Admin: 解析+强校验(Default/区间连续/去重)
Admin -> DB: 写入 rule_detail(JSON)
Admin -> Cache: 刷新缓存
FE <- Admin: 成功/失败(含错误详情)
@enduml
```

---

## 6. 数据模型与存储
### 6.1 表复用
- 复用 `cc_routing_rule_tab`：字段 `rule_detail` 保存 JSON，无需改表。

### 6.2 枚举扩展（代码）
- 文件：`internal/domain/cc_routing_rule/entity.go`
  - 新增：`CCRoutingTypeShopGroup=3`、`CCRoutingTypeCategory=4`、`CCRoutingTypeWeightCategory=5`

### 6.3 规则 JSON 结构（示例）
```json
{
  "shop_group_rule_detail": {
    "rules": [
      { "client_tag_id": 8, "client_group_id": "G123", "customs_clearance": "CC_SG1" }
    ],
    "default_customs_clearance": "CC_DEFAULT"
  },
  "category_rule_detail": {
    "rules": [ { "category_id": 12345, "customs_clearance": "CC_CAT1" } ],
    "default_customs_clearance": "CC_DEFAULT"
  },
  "weight_category_rule_detail": {
    "rules": [
      { "category_id": 12345, "min_weight": 0, "max_weight": 400, "customs_clearance": "CC_WC1" },
      { "category_id": 12345, "min_weight": 400, "max_weight": 1000, "customs_clearance": "CC_WC2" }
    ],
    "default_customs_clearance": "CC_DEFAULT"
  }
}
```

### 6.4 校验规则
- Default 行必填且不可删除。
- Shop Group：`client_tag_id` 必为 8，`client_group_id` 不重复（可选校验存在性）。
- Category：`category_id` 不重复。
- Weight+Category：同类目内区间按 `(min, max]` 连续且不重叠/不遗漏。

---

## 7. 接口设计
### 7.1 Admin（新增）
- `POST /api/admin/cc_routing/rule/import_shop_group`
  - 文件格式：xlsx/csv；列：`ClientTagId, ClientGroupId, AllocateTo`
- `POST /api/admin/cc_routing/rule/import_category`
  - 列：`CategoryId, AllocateTo`
- `POST /api/admin/cc_routing/rule/import_weight_category`
  - 列：`CategoryId, MinWeight, MaxWeight, AllocateTo`
- `GET /api/admin/cc_routing/rule/template?type=shop_group|category|weight_category`
- `GET /api/admin/cc_routing/cc_list`（从 CC 服务获取 customs agent 下拉）

说明：保留已有 CRUD 接口不变。

### 7.2 运行期（建议 CC）
- gRPC（protocol 仓库改动）：`CustomsClearanceRoutingReq` 新增可选字段：
  - `shop_id`（string/int64）
  - `global_category_id`（int，建议 L3；如上游仅有多级，新增 repeated）

### 7.3 外部依赖
- LPS：`ClientTag=8` 获取 shop→group（可复用 `GetShopGroup` 或新增批量接口）。
- CC 服务：`GET /admin/customs_agent/list` 获取下拉列表。

---

## 8. 运行期算法与流程
### 8.1 CCRouting 决策
```mermaid
flowchart LR
    A[入参: product_id, weight, cc_list, shop_id可选, category_id可选] --> B{routing_type}
    B -->|Fixed| C[固定CC]
    B -->|Weight| D[命中重量区间]
    B -->|Shop Group| E[shop_id -> group Tag 8; 唯一组? 命中/默认]
B -->|Category| F[命中category/默认]
B -->|Weight+Category| G[同类目下按区间命中/默认]
C --> H{结果 属于 cc_list?}
D --> H
E --> H
F --> H
G --> H
H -->|是| I[返回CC]
H -->|否| J[报错]
```

### 8.2 SelectLane（预委后）
- 维持 TW & CB 时使用 `CCFilter`：先向 customs service 获取 pre-auth 结果 CC，按 Lane 的可用 CC 列表过滤。
- 新增监控：若有“建议 CC”，与 `CCFilter` 目标 CC 不一致时上报（不改变主流程）。

---

## 9. 配置、缓存与灰度
- 开关：`TW_CC_ROUTING_BY_SHOP_GROUP_CATEGORY_ENABLE`（默认仅 TW 打开）。
- 缓存：沿用 `DumpCCRoutingRule`；导入/更新后触发刷新。
- 灰度：按产品/比例灰度，先启“建议 CC”+不一致监控，再全量放开。

---

## 10. 监控与告警
- 指标：
  - `cc_routing_suggestion_count{type,product_id,status}`（status: hit/default/error）
  - `cc_routing_mismatch_with_preauth_count{product_id}`
  - `cc_routing_import_validate_fail_count{type}`
- 日志：记录 ruleId、operator、差异摘要；导入失败定位首个错误行。

---

## 11. 安全与合规
- 管理端接口复用既有权限体系；导入操作审计入库。
- 外部依赖（LPS/CC）调用采用现有 JWT 与超时/重试策略。

---

## 12. 兼容性、迁移与回滚
- 不改表，新增 JSON 字段兼容旧规则。
- gRPC 协议新增可选字段，向后兼容。
- 导入失败整体回滚，保留旧规则和操作日志。
- 关闭开关即降级至现有逻辑。

---

## 13. 详细实施计划（逐文件）
1) `internal/domain/cc_routing_rule/entity.go`
   - 新增枚举与 3 类 rule_detail 结构（含 JSON tag）
2) `internal/usecase/cc_routing/service.go`
   - 扩展 `CCRouting` 分支，加入强校验与监控
3) `internal/usecase/select_lane/select_lane.go`
   - 保持 CCFilter 调用，新增不一致监控（可选）
4) `internal/client/lpsclient/const.go|api.go`
   - 新增 `ClientTag=8` 与 Shop→Group 查询封装
5) `internal/client/ccclient/*`
   - 新增 customs agent 列表接口（Admin 下拉）
6) `internal/adminfacade/cc_routing.go` + 新服务 `import_service.go`
   - 新增导入/模板/下拉路由；解析+强校验+入库+刷新缓存+操作日志
7) 协议仓库
   - `CustomsClearanceRoutingReq` 新增可选字段，回归生成并升级依赖
8) 监控
   - 新增指标与上报点

---

## 14. 测试计划
### 14.1 单元测试
- JSON 编解码；区间连续/重叠/遗漏；Default 行校验；cc_list 过滤
- Shop Group 多组异常；Category 命中与默认

### 14.2 集成测试
- 导入成功/失败回滚；模板下载；LPS/CC 异常重试与降级

### 14.3 回归测试
- TW SelectLane 预委后 CCFilter 行为不变；非 TW 不受影响

---

## 15. 时间线与资源
- D+2：模型与服务分支完成（含单测）
- D+4：Admin 导入/模板/下拉与强校验完成
- D+6：协议扩展与联调，TW 小流量灰度，开启监控
- D+8：完善监控/文档，全量放开

---

## 16. 风险与缓解
- LPS Tag=8 与接口就绪：缺批量接口时先复用单接口并加缓存；灰度限流
- 上游分类层级不一致：约定使用 L3；必要时前端标注层级
- 外部依赖稳定性：超时/重试/熔断与降级策略；监控告警

---

## 17. 参考与附录
- 代码参考：
  - `internal/adminfacade/cc_routing.go`
  - `internal/usecase/cc_routing/service.go`
  - `internal/usecase/cc_routing/localcache.go`
  - `internal/usecase/select_lane/select_lane.go`
  - `internal/client/lpsclient/*`, `internal/client/ccclient/*`
- 附：Weight+Category 校验伪代码
```mermaid
flowchart TD
  A[按 category_id 分组] --> B[每组按 min 升序排序]
  B --> C{检查相邻区间}
  C -->|curr.min < prev.max| E[重叠错误]
  C -->|curr.min > prev.max| F[遗漏错误]
  C -->|curr.min == prev.max| D[通过]
  D --> C
```


