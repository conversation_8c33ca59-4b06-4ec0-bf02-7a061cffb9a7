## TW CC Routing by Shop Group & Category — 技术设计（可实施版）

### 文档信息
- **PRD**: 2025.07 - TW CC Routing by shop group and category（Confluence）
  - 链接: `https://confluence.shopee.io/display/SSCP/2025.07+-+TW+CC+Routing+by+shop+group+and+category`
- **适用仓库**: `logistics-smart-routing`
- **目标读者**: BE/FE/QE/OPS/架构
- **面向环境**: 先 TW，后按需拓展

---

## 1. 背景与目标

### 1.1 背景
- TW 存在两类成本优化诉求：
  - 空转海：将“抛货”从空运改海运，降低成本。
  - 海转空：将低于 400g 的轻小件从海转空，避免按票计费导致成本偏高。
- 预委场景下 CC 在上游已被固定，无法 reroute；期望在 CC Routing 配置层面提升灵活性，按店群、类目、重量+类目动态分配 CC。

### 1.2 目标
- 在 CC Routing 模块新增 3 个路由维度：
  - Route by Shop Group
  - Route by Category ID
  - Route by Weight Range + Category ID
- 提供批量导入、强校验、可视化查看与历史留痕，减少人工 reroute 成本。
- 运行期在 TW 生效，兼容既有 Fixed、Weight 两种类型；不影响 SelectLane 预委后的 CCFilter 行为。

---

## 2. 范围与不在范围

### 2.1 范围
- CC Routing 运行期决策扩展（Shop Group/Category/Weight+Category）。
- Admin 配置 CRUD、批量导入、模板下载、查看页适配；CC 下拉列表。
- LPS 客户端新增/封装接口以获取 Shop Group（ClientTag=8）。
- CC 客户端新增接口以获取可配置 CC 列表。
- 监控与告警（建议 CC 与预委 CC 不一致上报）。

### 2.2 不在范围
- 海外站点大规模推广（先仅 TW）。
- 订单上游协议的非兼容字段变更（均采用新增可选字段）。

---

## 3. 现状梳理与扩展点

### 3.1 现状代码关键位置
- 管理端 CC Routing 接口：`internal/adminfacade/cc_routing.go`（已有 CRUD）
- 规则实体与存储：
  - `internal/domain/cc_routing_rule/entity.go`
  - `internal/domain/cc_routing_rule/persistent.go`
  - `internal/domain/cc_routing_rule/repo.go`
- 运行期 CC 决策：`internal/usecase/cc_routing/service.go`
- 本地缓存装载：`internal/usecase/cc_routing/localcache.go`（`DumpCCRoutingRule`）
- TW SelectLane 预委后 CC 过滤：`internal/usecase/select_lane/select_lane.go`（调用 `CCFilter`）
- LPS 客户端能力（Shop Group/Tag）：`internal/client/lpsclient/api.go`、`dto.go`、`const.go`
- Customs Service 客户端（预委结果）：`internal/client/ccclient/api.go`

### 3.2 运行期扩展点
- 在 `CCRoutingService.CCRouting` 内新增 3 种路由类型的决策分支。
- 在 `select_lane.go` 保持 TW 的 `CCFilter` 逻辑不变，仅新增一致性监控。

---

## 4. 数据与模型设计

### 4.1 枚举扩展
- 文件：`internal/domain/cc_routing_rule/entity.go`
- 新增路由类型：
  - `CCRoutingTypeShopGroup = 3`
  - `CCRoutingTypeCategory = 4`
  - `CCRoutingTypeWeightCategory = 5`

### 4.2 规则明细结构（JSON 持久化）
- 仍使用表 `cc_routing_rule_tab` 的 `rule_detail` 字段保存 JSON，无需改表。
- 结构扩展：在 `CCRoutingRuleDetail` 新增以下字段（可选）：

```json
{
  "fixed_rule_detail": { "fixed_customs_clearance": "CC_ID" },
  "weight_rule_detail": {
    "rule_list": [
      { "min_weight": 0, "max_weight": 400, "customs_clearance": "CC_A" }
    ]
  },
  "shop_group_rule_detail": {
    "rules": [
      { "client_tag_id": 8, "client_group_id": "G123", "customs_clearance": "CC_SG1" }
    ],
    "default_customs_clearance": "CC_DEFAULT"
  },
  "category_rule_detail": {
    "rules": [
      { "category_id": 12345, "customs_clearance": "CC_CAT1" }
    ],
    "default_customs_clearance": "CC_DEFAULT"
  },
  "weight_category_rule_detail": {
    "rules": [
      { "category_id": 12345, "min_weight": 0, "max_weight": 400, "customs_clearance": "CC_CAT_W1" },
      { "category_id": 12345, "min_weight": 400, "max_weight": 1000, "customs_clearance": "CC_CAT_W2" }
    ],
    "default_customs_clearance": "CC_DEFAULT"
  }
}
```

### 4.3 约束与校验规则
- Shop Group：
  - `client_tag_id` 必须为 `8`（CB CC Allocation），`client_group_id` 存在且不重复。
  - Default 行必填且不可删除。
- Category：
  - `category_id` 不重复；Default 行必填且不可删除。
- Weight + Category：
  - 按 `category_id` 分组，区间规则须“首尾相接、无重叠、无遗漏”。
  - 命中条件采用现有区间约定：`weight > min_weight && weight <= max_weight`。
  - Default 行必填且不可删除。

---

## 5. 接口设计

### 5.1 管理端（Admin）
- 已有：
  - `POST /api/admin/cc_routing/rule/create`
  - `POST /api/admin/cc_routing/rule/update`
  - `GET  /api/admin/cc_routing/rule/view`
  - `POST /api/admin/cc_routing/rule/delete`
  - `GET  /api/admin/cc_routing/rule/list`

- 新增：
  1) 批量导入 Shop Group 规则
  - `POST /api/admin/cc_routing/rule/import_shop_group`
  - 入参：文件上传（xlsx/csv），列：`ClientTagId, ClientGroupId, AllocateTo`；Default 行必填。
  - 行为：解析→强校验→保存为 `routing_type=ShopGroup`。

  2) 批量导入 Category 规则
  - `POST /api/admin/cc_routing/rule/import_category`
  - 入参：`CategoryId, AllocateTo`；Default 行必填。

  3) 批量导入 Weight + Category 规则
  - `POST /api/admin/cc_routing/rule/import_weight_category`
  - 入参：`CategoryId, MinWeight, MaxWeight, AllocateTo`；Default 行必填；同类目下校验区间连续、无重叠/遗漏。

  4) 模板下载
  - `GET /api/admin/cc_routing/rule/template?type=shop_group|category|weight_category`

  5) 获取 CC 列表下拉
  - `GET /api/admin/cc_routing/cc_list`（后端从 CC 服务获取 customs agent 列表）

### 5.2 运行期（建议 CC）
- gRPC：扩展 `CustomsClearanceRoutingReq`（协议仓库）
  - 新增可选字段：
    - `shop_id`（string/int64）
    - `global_category_id`（int，建议对齐 L3；如上游只能传多层，则新增 repeated 字段承载 L1~L5）
  - 向后兼容：新增为可选字段，不影响既有调用方。

### 5.3 LPS 客户端
- 常量：`ClientTagCCAllocation = 8`
- 能力：
  - 若 LPS 暴露批量接口：`BatchGetClientEntityInfo(shop_ids, client_tag_id) -> map[shop_id][]group_id`
  - 若暂无，复用现有：`GetShopGroup(req: { mask_product_id, client_tag_id, shop_ids }) -> { shop_group_ids }`

### 5.4 CC 客户端（Customs Service）
- 新增：`ListCustomsAgents(ctx) -> []{id,name,...}`，来源 `GET /admin/customs_agent/list`

---

## 6. 运行期业务逻辑（CCRouting）

### 6.1 流程
1) 从本地缓存取该 `product_id` 的 CC 规则。
2) 按 `routing_type` 分支：
   - Fixed：校验固定 CC 在 `cc_list` 内，命中返回。
   - Weight：按 weight 区间命中，且在 `cc_list` 内。
   - Shop Group：
     - 读取 `shop_id`，通过 LPS 获取 `group_id` 列表（`client_tag_id=8`）。
     - 若多组：按 PRD 默认策略“要求唯一组”（`client_in_unique_group_toggle=true`），否则报错并监控。
     - 命中对应映射，若无命中使用 `default_customs_clearance`。
     - 结果需在 `cc_list` 内。
   - Category：
     - 读取 `global_category_id`，命中对应映射，否则默认；结果需在 `cc_list` 内。
   - Weight + Category：
     - 读取 `global_category_id` 与 `weight`，按该类目的区间命中，否则默认；结果需在 `cc_list` 内。
3) 缺少必要上下文：
   - 若 rule 依赖的上下文缺失（如无 `shop_id`/`category_id`），返回参数错误并上报。
4) 监控：记录各分支的命中量/默认命中量/失败量。

### 6.2 与 SelectLane 的协同（预委后）
- 现状保持：TW & CB 时在 `select_lane` 内调用 `CCFilter`，基于 customs service 的 pre-auth 结果过滤 Lane。
- 新增（推荐）：若能获得“预委前建议 CC”，与 pre-auth 目标 CC 对比，不一致则上报监控（不影响主流程）。

---

## 7. 导入与模板

### 7.1 模板（示例列）
- Shop Group：`ClientTagId | ClientGroupId | AllocateTo (CC)`；包含 Default 行。
- Category：`CategoryId | AllocateTo (CC)`；包含 Default 行。
- Weight+Category：`CategoryId | MinWeight | MaxWeight | AllocateTo (CC)`；包含 Default 行，分组校验区间连续。

### 7.2 后端解析与强校验
- 解析：复用 `internal/util/fileutil/excel.go|excel_xls.go`。
- 校验：
  - 字段非空、类型正确、Default 行存在。
  - Shop Group：`client_tag_id==8`、`client_group_id` 不重复；可选联查 LPS 校验 group 存在。
  - Category：`category_id` 不重复。
  - Weight+Category：按 `category_id` 分组校验区间连续/不重叠/不遗漏；命中规则 `>min && <=max`。

### 7.3 幂等与回滚
- 保存前做旧规则快照（操作日志），失败整体回滚。
- 变更完成写操作日志（含 operator、摘要 diff）。

---

## 8. 配置、缓存与灰度

### 8.1 开关
- 新增开关：`TW_CC_ROUTING_BY_SHOP_GROUP_CATEGORY_ENABLE`（默认仅 TW 生效）。

### 8.2 缓存
- 沿用 `DumpCCRoutingRule` 机制；在 create/update/import 后触发缓存刷新（按 product 维度或全量）。

### 8.3 灰度
- 按产品维度与比例灰度；先仅接入“建议 CC”链路与一致性监控，再全量启用。

---

## 9. 监控与告警

### 9.1 指标
- `cc_routing_suggestion_count{type,product_id,status}`（status：hit/default/error）
- `cc_routing_mismatch_with_preauth_count{product_id}`
- `cc_routing_import_validate_fail_count{type}`

### 9.2 日志
- 记录 ruleId、operator、差异摘要；导入失败包含首个错误行定位。

---

## 10. 详细实施清单（逐文件）

> 以下每步可独立 PR，建议按顺序推进；带有“可选”标注的项视对接依赖情况实施。

### 10.1 模型与枚举
- 文件：`internal/domain/cc_routing_rule/entity.go`
  - [ ] 新增 `CCRoutingTypeShopGroup=3`、`CCRoutingTypeCategory=4`、`CCRoutingTypeWeightCategory=5`
  - [ ] 在 `CCRoutingRuleDetail` 中新增 `ShopGroupRuleDetail`、`CategoryRuleDetail`、`WeightCategoryRuleDetail`
  - [ ] 添加 JSON tag，保持与现有 `rule_detail` 一致序列化

### 10.2 运行期决策
- 文件：`internal/usecase/cc_routing/service.go`
  - [ ] 扩展 `CCRouting` 分支，按 6.1 实现 3 种新类型逻辑
  - [ ] 结果需校验在 `cc_list` 内
  - [ ] 缺少上下文时报参数错误并监控
  - [ ] 埋点：命中/默认/失败

### 10.3 TW SelectLane 协同
- 文件：`internal/usecase/select_lane/select_lane.go`
  - [ ] 保持 `CCFilter` 调用（TW&CB）不变
  - [ ] 新增一致性监控（若能获取“建议 CC”）

### 10.4 LPS 客户端
- 文件：`internal/client/lpsclient/const.go`
  - [ ] 新增 `ClientTagCCAllocation ClientTag = 8`
- 文件：`internal/client/lpsclient/api.go`
  - [ ] 可选：封装 `GetShopGroupIdsByShopIdsAndTag(shopIds []int64, clientTag uint64)`（基于现有 `GetShopGroup` 或对接批量接口）

### 10.5 CC 客户端下拉
- 目录：`internal/client/ccclient/`
  - [ ] 新增 `ListCustomsAgents(ctx)`（GET `/admin/customs_agent/list`），供 Admin 下拉使用

### 10.6 Admin 批量导入与模板
- 文件：`internal/adminfacade/cc_routing.go`
  - [ ] 新增导入/模板/下拉接口路由
- 新增解析与服务：`internal/usecase/cc_routing/import_service.go`（建议）
  - [ ] 解析 xlsx/csv→结构体→强校验→入库（`rule_detail`）
  - [ ] Default 行校验、区间连续性校验
  - [ ] 操作日志与缓存刷新

### 10.7 协议扩展（上游）
- 仓库：`logistics-smart-routing-protocol`
  - [ ] `CustomsClearanceRoutingReq` 新增字段：`shop_id`、`global_category_id`
  - [ ] 生成 pb；本仓库引用新版依赖

### 10.8 配置与灰度
- 文件：`internal/util/configutil/*.go`
  - [ ] 新增开关读取 `TW_CC_ROUTING_BY_SHOP_GROUP_CATEGORY_ENABLE`
  - [ ] TW 场景打开

### 10.9 监控
- 文件：`pkg/monitoring/monitor_tag.go`、`internal/util/prometheusutil/*`
  - [ ] 新增指标并在关键分支上报

### 10.10 文档与操作说明
- [ ] 在 `README.md` 或 `/docs` 下补充使用说明与模板示例

---

## 11. 校验算法细节（Weight+Category）

### 11.1 连续性校验（同一 category 内）
- 输入：按 `category_id` 分组后的 `[min,max]` 闭区间列表，系统命中判定使用 `(min, max]`。
- 规则：
  - 排序后对相邻区间：`curr.min == prev.max`
  - 无交集：`curr.min >= prev.max`，若 `>` 则视为“遗漏”
  - 无重叠：若 `curr.min < prev.max` 则为“重叠”
- 错误文案：
  - 重叠：`weight range is overlapped`
  - 遗漏：`weight range is not complete`

---

## 12. 回滚与降级
- 任意导入失败 → 整体回滚；保留旧规则，日志留痕。
- LPS/CC 外部依赖异常 → 阻断导入保存；运行期建议 CC 可降级为仅 Fixed/Weight 命中。
- 关闭开关 `TW_CC_ROUTING_BY_SHOP_GROUP_CATEGORY_ENABLE` → 仅保留现有功能路径。

---

## 13. 测试用例（建议）

### 13.1 单元测试
- 枚举与序列化：新结构 JSON 编解码正确。
- Shop Group：
  - 单组命中、无命中→默认、多组返回报错。
- Category：
  - 命中/无命中→默认；`cc_list` 过滤失败。
- Weight+Category：
  - 边界：命中 `(min,max]`，相邻相接；区间重叠/遗漏校验报错。
- 导入解析：字段缺失、Default 缺失、重复/非法值校验。

### 13.2 集成测试
- Admin 导入成功/失败回滚、模板导出。
- LPS 获取 group 异常重试/降级。
- CC 下拉列表异常处理。

### 13.3 回归测试
- TW SelectLane 预委后 `CCFilter` 行为不变。
- 非 TW 不受影响。

---

## 14. 迭代与里程碑（建议）
- D+2：模型与服务分支完成，单测通过。
- D+4：Admin 导入/模板/下拉与强校验完成。
- D+6：协议扩展与上游联调，TW 小流量灰度，启用一致性监控。
- D+8：完善监控/告警与操作文档，全量放开。

---

## 15. 风险与依赖
- LPS Tag=8 与 API 就绪度：若缺批量接口，先复用 `GetShopGroup`；必要时本地缓存热身以降低调用压力。
- 上游是否能提供 `global_category_id`：建议约定使用 L3；如仅提供多层 IDs，服务端需选择一个层级或在配置端标明层级。
- 外部服务稳定性（Customs/LPS）：需完善超时、重试与降级策略。

---

## 16. 附：关键代码参考（只读）
- SelectLane CCFilter（TW 预委后）：
  - `internal/usecase/select_lane/select_lane.go`（TW&CB 场景调用 `CCFilter`）
- CC 决策入口与缓存：
  - `internal/usecase/cc_routing/service.go`
  - `internal/usecase/cc_routing/localcache.go`
- 规则存储：
  - `internal/domain/cc_routing_rule/*`
- LPS 客户端（Shop Group/Tag）：
  - `internal/client/lpsclient/api.go|dto.go|const.go`
- Customs Service 客户端（预委结果&CC 列表）：
  - `internal/client/ccclient/*`


