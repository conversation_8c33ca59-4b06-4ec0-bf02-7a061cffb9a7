# coding=utf-8
import pymysql
import json
import time


before_stat_time_list = ["20240606", "2024060601", "2024060602", "2024060603", "2024060604", "2024060605",
                  "2024060606", "2024060607", "2024060608", "2024060609", "2024060610", "2024060611"]

after_stat_time_list = ["2024060612", "2024060613", "2024060614", "2024060615", "2024060616", "2024060617", "2024060618", "2024060619",
                  "2024060620", "2024060621", "2024060622", "2024060623"]


def init_db(env):
    if env == "test":
        cnx = pymysql.connect(
            host='',
            user='',
            password='',
            database='',
            port=6606
        )
        return cnx
    if env == "live":
        cnx = pymysql.connect(
            host='',
            user='',
            password='',
            database='',
            port=6606
        )
        return cnx


def get_order_id_map(file_path_list):
    order_info_map = {}
    for file_path in file_path_list:
        with open(file_path, 'r') as file:
            # 逐行读取并处理每个JSON字符串
            for line in file:
                line = line.strip()  # 去除行尾的换行符或空格

                # 反序列化JSON字符串
                try:
                    json_data = json.loads(line)
                except Exception as e:
                    print "json load err, line", line, e
                    continue
                order_info_map[json_data["order_id"]] = json_data
    return order_info_map


def get_intersection_data(all_order_info_map, executed_order_info_map):
    intersection_data_map = {}
    for order_id in all_order_info_map:
        if order_id not in executed_order_info_map:
            intersection_data_map[order_id] = all_order_info_map[order_id]
    return intersection_data_map


def aggregation_order_info(for_aggregate_order_info_map):
    aggregate_mask_product_map = {}
    aggregate_fulfillment_product_map = {}
    aggregate_dest_zone_code_map = {}
    for order_id in for_aggregate_order_info_map:
        order_info = for_aggregate_order_info_map[order_id]
        mask_product_key = str(order_info["mask_product_id"])
        if mask_product_key in aggregate_mask_product_map:
            aggregate_mask_product_map[mask_product_key] += 1
        else:
            aggregate_mask_product_map[mask_product_key] = 1

        fulfillment_product_key = mask_product_key + "||" + str(order_info["fulfillment_product_id"])
        if fulfillment_product_key in aggregate_fulfillment_product_map:
            aggregate_fulfillment_product_map[fulfillment_product_key] += 1
        else:
            aggregate_fulfillment_product_map[fulfillment_product_key] = 1

        for dest_zone in order_info["dest_zones"]:
            dest_zone_key = fulfillment_product_key + "||" + dest_zone
            if dest_zone_key in aggregate_dest_zone_code_map:
                aggregate_dest_zone_code_map[dest_zone_key] += 1
            else:
                aggregate_dest_zone_code_map[dest_zone_key] = 1
    return aggregate_mask_product_map, aggregate_fulfillment_product_map, aggregate_dest_zone_code_map

#
# def exhibit_volume_tab(order_info_map, stat_time_list, cnx):
#     cursor = cnx.cursor()
#     # 查询需要修改的数据
#     aggregate_mask_product_map, aggregate_fulfillment_product_map, aggregate_dest_zone_code_map = aggregation_order_info(order_info_map)
#     update_sql_list = []
#     exhibit_error_result_list = []
#     # 查询mask product
#     for aggregate_mask_product, value in aggregate_mask_product_map.items():
#         update_query = "update masking_product_order_num_tab set order_num= " + str(
#             value) + " where mask_product_id =  " + aggregate_mask_product + " and stat_type = 1 and stat_time = " + stat_time
#         select_query = "select * from masking_product_order_num_tab where mask_product_id=%s and stat_type= 1 and stat_time= %s"
#         select_param = (aggregate_mask_product, stat_time)
#         cursor.execute(select_query, select_param)
#         result = cursor.fetchall()
#         if len(result) != 1:
#             exhibit_error_result_list.append("invalid data, command |||" + aggregate_mask_product + " " + str(value))
#             continue
#         current_value = 0
#         for row in result:
#             current_value = row[6]
#         update_sql_list.append(
#             "sql || current value || decr value ||| " + update_query + " " + str(current_value) + " " + str(value))
#
#     # 查询fulfillment product
#     for aggregate_fulfillment_product, value in aggregate_fulfillment_product_map.items():
#         fulfillment_product_list = aggregate_fulfillment_product.split("||")
#         update_query = "update masking_product_order_num_tab set order_num= " + str(
#             value) + " where mask_product_id =  " + fulfillment_product_list[
#                            0] + " and stat_type = 2 and stat_time = " + stat_time + " and fulfillment_product_id = " + \
#                        fulfillment_product_list[1]
#         select_query = "select * from masking_product_order_num_tab where mask_product_id=%s and stat_type= 2 and stat_time= %s and fulfillment_product_id=%s"
#         select_param = (fulfillment_product_list[0], stat_time, fulfillment_product_list[1])
#         cursor.execute(select_query, select_param)
#         result = cursor.fetchall()
#         if len(result) != 1:
#             exhibit_error_result_list.append(
#                 "invalid data, command |||" + aggregate_fulfillment_product + " " + str(value))
#             continue
#         current_value = 0
#         for row in result:
#             current_value = row[6]
#         update_sql_list.append(
#             "sql || current value || decr value ||| " + update_query + " " + str(current_value) + " " + str(value))
#
#     # 查询zone code
#     i = 0
#     total_zone_num = len(aggregate_dest_zone_code_map)
#     for aggregate_dest_zone_code, value in aggregate_dest_zone_code_map.items():
#         dest_zone_code_list = aggregate_dest_zone_code.split("||")
#         update_query = "update masking_product_order_num_tab set order_num= " + str(
#             value) + " where mask_product_id =  " + \
#                        dest_zone_code_list[
#                            0] + " and stat_type = 3 and stat_time = " + stat_time + " and fulfillment_product_id = " + \
#                        dest_zone_code_list[1] + " and zone_code = " + dest_zone_code_list[2]
#         select_query = "select * from masking_product_order_num_tab where mask_product_id=%s and stat_type= 3 and stat_time= %s and fulfillment_product_id=%s and zone_code=%s"
#         select_param = (dest_zone_code_list[0], stat_time, dest_zone_code_list[1], dest_zone_code_list[2])
#         cursor.execute(select_query, select_param)
#         result = cursor.fetchall()
#         i += 1
#         print "total zone num, current execute zone num", total_zone_num, i
#         if len(result) != 1:
#             exhibit_error_result_list.append("invalid data, command |||" + aggregate_dest_zone_code + " " + str(value))
#             continue
#         current_value = 0
#         for row in result:
#             current_value = row[6]
#         update_sql_list.append(
#             "sql || current value || decr value ||| " + update_query + " " + str(current_value) + " " + str(value))
#     # 保存结果
#     with open('./update_sql_list.txt', 'w') as rfile:
#         # 遍历列表中的每个元素
#         for item in update_sql_list:
#             # 将元素写入文件，并添加换行符
#             rfile.write(item + '\n')
#     with open('./exhibit_error_result_list.txt', 'w') as rfile:
#         # 遍历列表中的每个元素
#         for item in exhibit_error_result_list:
#             # 将元素写入文件，并添加换行符
#             rfile.write(item + '\n')


def get_aggregate_data(order_info_map):
    aggregate_mask_product_map, aggregate_fulfillment_product_map, aggregate_dest_zone_code_map = aggregation_order_info(order_info_map)
    aggregate_data_list = []
    for aggregate_mask_product, value in aggregate_mask_product_map.items():
        aggregate_data_list.append(aggregate_mask_product + " " + str(value))

    for aggregate_fulfillment_product, value in aggregate_fulfillment_product_map.items():
        aggregate_data_list.append(aggregate_fulfillment_product + " " + str(value))

    for aggregate_dest_zone_code, value in aggregate_dest_zone_code_map.items():
        aggregate_data_list.append(aggregate_dest_zone_code + " " + str(value))

    with open('./aggregate_data_list.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in aggregate_data_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')


def generate_sql_list(order_info_map, stat_time_list, stat_time_type):
    aggregate_mask_product_map, aggregate_fulfillment_product_map, aggregate_dest_zone_code_map = aggregation_order_info(order_info_map)
    # 查询mask product
    execute_sql_list = []
    for aggregate_mask_product, value in aggregate_mask_product_map.items():
        # 构建sql
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 1 and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, aggregate_mask_product) + tuple(stat_time_list)
        # 更新前查询
        execute_sql_list.append(update_query % update_param)

    # 查询fulfillment product
    for aggregate_fulfillment_product, value in aggregate_fulfillment_product_map.items():
        fulfillment_product_list = aggregate_fulfillment_product.split("||")
        # 构建sql
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 2 and fulfillment_product_id = %s and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, fulfillment_product_list[0], fulfillment_product_list[1]) + tuple(stat_time_list)
        # 更新前查询
        execute_sql_list.append(update_query % update_param)

    # 查询zone code
    for aggregate_dest_zone_code, value in aggregate_dest_zone_code_map.items():
        dest_zone_code_list = aggregate_dest_zone_code.split("||")
        # 构建sql
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 3 and fulfillment_product_id = %s and zone_code = '%s' and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, dest_zone_code_list[0], dest_zone_code_list[1], dest_zone_code_list[2]) + tuple(
            stat_time_list)
        # 更新前查询
        execute_sql_list.append(update_query % update_param)

    with open('./' + stat_time_type + '_execute_sql_list.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in execute_sql_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')


def execute_sql_list(file_path, cnx, stat_time_type):
    cursor = cnx.cursor()
    # execute_result_list = []
    with open(file_path, 'r') as file:
        # 逐行读取并处理每个JSON字符串
        i = 0
        for line in file:
            i += 1
            if i < 49:
                continue

            print "execute sql num:", i
            try:
                line = line.strip()  # 去除行尾的换行符或空格

                split_result = line.split("where")
                select_sql = "select * from masking_product_order_num_tab where" + split_result[1]
                update_sql = line
                # 更新前查询
                cursor.execute(select_sql)
                result = cursor.fetchall()
                before_result_map = {}
                for row in result:
                    before_result_map[str(row[7])] = row
                # 更新
                cursor.execute(update_sql)
                cnx.commit()
                # 更新后查询
                cursor.execute(select_sql)
                result = cursor.fetchall()
                after_result_map = {}
                for row in result:
                    after_result_map[str(row[7])] = row
                for stat_time in before_result_map:
                    before_result = before_result_map[stat_time]
                    after_result = after_result_map[stat_time]
                    execute_result = str(i) + "|" + str(time.time()) + "|before|" + ','.join(str(it) for it in before_result) + " || " + "after|" + ','.join(str(it) for it in after_result)
                    if int(before_result[8]) != int(after_result[8]) and int(after_result[8]) == 0:
                        execute_result = execute_result + "|warn data"
                    # execute_result_list.append(execute_result)
                    with open('/workspace/log/' + stat_time_type + '_execute_result_list.txt', 'a') as rfile:
                        # # 遍历列表中的每个元素
                        # for item in execute_result_list:
                        # 将元素写入文件，并添加换行符
                        rfile.write(execute_result + '\n')
                        rfile.flush()
                    print i, execute_result
            except Exception as e:
                print "execute sql error, line=", line, e

    print "execute sql end"


def update_volume_tab(order_info_map, stat_time_list, cnx):
    cursor = cnx.cursor()
    aggregate_mask_product_map, aggregate_fulfillment_product_map, aggregate_dest_zone_code_map = aggregation_order_info(order_info_map)
    before_result_list = []
    after_result_list = []
    # 查询mask product
    for aggregate_mask_product, value in aggregate_mask_product_map.items():
        # 构建sql
        select_query = "select * from masking_product_order_num_tab where mask_product_id = %s and stat_type = 1 and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        select_param = (aggregate_mask_product,) + tuple(stat_time_list)
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 1 and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, aggregate_mask_product) + tuple(stat_time_list)
        # 更新前查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            before_result_list.append(row)
        # 更新
        cursor.execute(update_query, update_param)
        cnx.commit()
        # 更新后查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            after_result_list.append(row)

    # 查询fulfillment product
    for aggregate_fulfillment_product, value in aggregate_fulfillment_product_map.items():
        fulfillment_product_list = aggregate_fulfillment_product.split("||")
        # 构建sql
        select_query = "select * from masking_product_order_num_tab where mask_product_id = %s and stat_type = 2 and fulfillment_product_id = %s and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        select_param = (fulfillment_product_list[0], fulfillment_product_list[1]) + tuple(stat_time_list)
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 2 and fulfillment_product_id = %s and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, fulfillment_product_list[0], fulfillment_product_list[1]) + tuple(stat_time_list)
        # 更新前查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            before_result_list.append(row)
        # 更新
        cursor.execute(update_query, update_param)
        cnx.commit()
        # 更新后查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            after_result_list.append(row)

    # 查询zone code
    for aggregate_dest_zone_code, value in aggregate_dest_zone_code_map.items():
        dest_zone_code_list = aggregate_dest_zone_code.split("||")
        # 构建sql
        select_query = "select * from masking_product_order_num_tab where mask_product_id = %s and stat_type = 3 and fulfillment_product_id = %s and zone_code = %s and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        select_param = (dest_zone_code_list[0], dest_zone_code_list[1], dest_zone_code_list[2]) + tuple(stat_time_list)
        update_query = "update masking_product_order_num_tab set order_num = IF(order_num < %s, 0, order_num - %s) where mask_product_id = %s and stat_type = 3 and fulfillment_product_id = %s and zone_code = %s and stat_time in ({})".format(
            ','.join(['%s'] * len(stat_time_list)))
        update_param = (value, value, dest_zone_code_list[0], dest_zone_code_list[1], dest_zone_code_list[2]) + tuple(stat_time_list)
        # 更新前查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            before_result_list.append(row)
        # 更新
        cursor.execute(update_query, update_param)
        cnx.commit()
        # 更新后查询
        cursor.execute(select_query, select_param)
        result = cursor.fetchall()
        for row in result:
            after_result_list.append(row)

    with open('./before_result_list.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in before_result_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')

    with open('./after_result_list.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in after_result_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')


def test_sql():
    mysql_connection = init_db("test")
    # all_order_info_map = get_order_id_map(["test1.txt"])
    # execute_order_info_map = get_order_id_map(["test2.txt"])
    # intersection_data_info_map = get_intersection_data(all_order_info_map, execute_order_info_map)
    # # 获取聚合结果
    # get_aggregate_data(all_order_info_map)
    # get_aggregate_data(intersection_data_info_map)
    # # 生成sql语句
    # generate_sql_list(all_order_info_map, before_stat_time_list, "before")
    # generate_sql_list(intersection_data_info_map, after_stat_time_list, "after")
    # 执行sql语句
    execute_sql_list("before_execute_sql_list.txt", mysql_connection, "before")
    execute_sql_list("after_execute_sql_list.txt", mysql_connection, "after")


def live_sql():
    mysql_connection = init_db("live")
    execute_sql_list("before_execute_sql_list.txt", mysql_connection, "before")
    # execute_sql_list("after_execute_sql_list.txt", mysql_connection, "after")


if __name__ == "__main__":
    live_sql()
