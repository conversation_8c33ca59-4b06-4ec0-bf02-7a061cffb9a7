# coding=utf-8
import json
import redis


def incr_redis():
    # 连接到 Redis
    redis_host = ''
    redis_port = 9999
    redis_db = 5
    redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db)

    # live环境
    # 连接到 Redis
    # redis_host = 'codis.sls-smartrouting.ap-sg-1-general-a.ctl.live.cache.shopee.io'
    # redis_port = 8299
    # redis_db = 1
    # redis_password = "LRCe6hUW313i"
    # redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password)

    # 读取文本文件
    file_path = './rollback.txt'  # 替换为实际的文件路径
    key_dict = {}
    with open(file_path, 'r') as file:
        # 逐行读取并处理每个JSON字符串
        i = 0
        for line in file:
            line = line.strip()  # 去除行尾的换行符或空格

            # 反序列化JSON字符串
            result_list = line.split("||")
            if len(result_list) != 2:
                print "rollback result error, line=", result_list
                continue
            key_result_list = result_list[1].strip().split()
            if len(key_result_list) != 4:
                print "rollback result error, line=", key_result_list
                continue
            if key_result_list[0] in key_dict:
                print "duplicate key, error", key_result_list
                return
            else:
                key_dict[key_result_list[0]] = key_result_list[2]
            i += 1
        print "file execute end", i

    for key, value in key_dict.items():
        current_value = int(redis_client.get(key))
        print "need execute key | current value | incr value ||", key, current_value, value

    for key, value in key_dict.items():
        current_value = int(redis_client.get(key))
        # 更新Redis
        result = redis_client.incr(key, value)
        print "execute key end, key | before value | incr value | after value ||", key, current_value, value, result


if __name__ == "__main__":
    incr_redis()
