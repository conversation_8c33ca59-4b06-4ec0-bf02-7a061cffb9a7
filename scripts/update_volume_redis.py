# coding=utf-8
import json
import redis


def decr_redis():
    # 连接到 Redis
    redis_host = ''
    redis_port = 9999
    redis_db = 5
    redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db)

    # live环境
    # 连接到 Redis
    # redis_host = ''
    # redis_port = 8299
    # redis_db = 1
    # redis_password = ""
    # redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password)
    order_file_path = './single_mpl_common_order.txt'
    order_id_map = {}
    with open(order_file_path, 'r') as o_file:
        for line in o_file:
            line = line.strip()
            order_id_map[line] = 0

    # 读取文本文件
    file_path_list = {'./orders_with_zone_1.json', './orders_with_zone_2.json'}  # 替换为实际的文件路径
    key_dict = {}
    un_common_id_num = 0
    i = 0
    for file_path in file_path_list:
        with open(file_path, 'r') as file:
            # 逐行读取并处理每个JSON字符串
            for line in file:
                line = line.strip()  # 去除行尾的换行符或空格

                # 反序列化JSON字符串
                try:
                    json_data = json.loads(line)
                except Exception as e:
                    print "json load err, line", line, e
                    continue

                if json_data["order_id"] not in order_id_map:
                    un_common_id_num += 1
                    continue

                # 拼接键（key）
                if "mask_product_id" in json_data:
                    mask_product_key = "mask_product_volume:" + "20240606" + ":" + str(json_data["mask_product_id"])
                    if mask_product_key in key_dict:
                        key_dict[mask_product_key] += 1
                    else:
                        key_dict[mask_product_key] = 1
                else:
                    print "mask_product_id not exist, line=", line

                if "fulfillment_product_id" in json_data and "mask_product_id" in json_data:
                    fulfillment_product_key = "product_volume:" + "20240606" + ":" + str(json_data["mask_product_id"]) + ":" + str(json_data["fulfillment_product_id"])
                    if fulfillment_product_key in key_dict:
                        key_dict[fulfillment_product_key] += 1
                    else:
                        key_dict[fulfillment_product_key] = 1

                    for origin_zone in json_data["origin_zones"]:
                        origin_zone_key = fulfillment_product_key + ":zone:" + origin_zone + ":origin"
                        if origin_zone_key in key_dict:
                            key_dict[origin_zone_key] += 1
                        else:
                            key_dict[origin_zone_key] = 1

                    for dest_zone in json_data["dest_zones"]:
                        dest_zone_key = fulfillment_product_key + ":zone:" + dest_zone + ":dest"
                        if dest_zone_key in key_dict:
                            key_dict[dest_zone_key] += 1
                        else:
                            key_dict[dest_zone_key] = 1
                else:
                    print "mask_product_id fulfillment_product_id not exist, line=", line
                i += 1
                if i % 100000 == 0:
                    print "execute data number", i
    print "file execute end", i
    print "un common id num", un_common_id_num

    # 观察需要执行的key
    need_execute_list = []
    for key, value in key_dict.items():
        current_value = int(redis_client.get(key))
        print "need execute key | current value | decr value ||", key, current_value, value
        need_execute_list.append("need execute key | current value | decr value || " + key + " " + str(current_value) + " " + str(value))
    with open('./need_execute_key.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in need_execute_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')

    # 真实执行的key
    rollback_data_list = []
    abnormal_data_list = []
    for key, value in key_dict.items():
        current_value = int(redis_client.get(key))
        if value > current_value:
            print "decr value large current value, key | current value | value ||", key, current_value, value
            abnormal_data_list.append("decr value large current value, key | current value | value || " + key + " " + str(current_value) + " " + str(value))
            continue
        # 更新Redis
        result = redis_client.decr(key, value)
        print "execute key end, key | before value | decr value | after value ||", key, current_value, value, result
        rollback_data_list.append("execute key end, key | before value | decr value | after value || " + key + " " + str(current_value) + " " + str(value) + " " + str(result))
    # 打开文件以写入模式
    with open('./rollback.txt', 'w') as rfile:
        # 遍历列表中的每个元素
        for item in rollback_data_list:
            # 将元素写入文件，并添加换行符
            rfile.write(item + '\n')
    with open('./abnormal.txt', 'w') as afile:
        # 遍历列表中的每个元素
        for item in abnormal_data_list:
            # 将元素写入文件，并添加换行符
            afile.write(item + '\n')


if __name__ == "__main__":
    decr_redis()
