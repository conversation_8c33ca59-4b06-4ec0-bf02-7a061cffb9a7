import concurrent.futures
import json

import requests


def read_json_file(file_path):
    with open(file_path, 'r') as file:
        need_process_orders = json.load(file)

    return need_process_orders


def get_allocation_log(order_id):
    url = "https://admin-smartrouting.ssc.shopee.co.id/api/admin/allocation/path/list"

    payload = json.dumps({"order_id": str(order_id), "page_no": 1, "page_size": 10})
    headers = {
        'JWT-Token': '',
        'Content-Type': 'application/json'}

    response = requests.request("POST", url, headers=headers, data=payload)

    return get_allocation_detail(response.json()['data']['list'][0]['request_id'])


def get_allocation_detail(request_id):
    url = "https://admin-smartrouting.ssc.shopee.co.id/api/admin/allocation/path/detail?request_id=%s" % request_id

    payload = ""
    headers = {
        'JWT-Token': ''}

    response = requests.request("GET", url, headers=headers, data=payload)

    return response.json()['data']


# 调用函数读取 CSV 文件
orders_with_zones = []
exception_orders = []


def append_final_order_results(order):
    order_id = order['order_id']
    try:
        allocation_log = get_allocation_log(order_id)
        zone_origin = allocation_log['soft_criteria']['zone_origin']
        zone_destination = allocation_log['soft_criteria']['zone_destination']
        mask_product_id = int(allocation_log['basic_info']['mask_product_id'].split('-')[0])
        orders_with_zones.append({'order_id': order_id, 'mask_product_id': mask_product_id,
                                  'fulfillment_product_id': order['fulfillment_product_id'],
                                  'origin_zones': zone_origin, 'dest_zones': zone_destination})
    except Exception as e:
        print(order_id, e)
        exception_orders.append(order_id)


with concurrent.futures.ThreadPoolExecutor(max_workers=256) as executor:
    # 提交任务到线程池并获取 Future 对象列表
    futures = [executor.submit(append_final_order_results, data) for data in read_json_file('need_decrease_orders_2.json')]

    # 获取任务结果
    for future in concurrent.futures.as_completed(futures):
        result = future.result()

with open('orders_with_zone.json', 'w', newline='') as file:
    for order in orders_with_zones:
        file.write(json.dumps(order))
        file.write('\n')
