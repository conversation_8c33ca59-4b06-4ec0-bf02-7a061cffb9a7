import concurrent.futures
import csv
import json

import requests

url = "https://smartrouting-grpc.ssc.shopee.co.id/smart_routing_protobuf.Masking/BatchGetAllocateOrderResult"


def get_order_result(order_id_list):
    payload = json.dumps({
        "header": {"request_id": "********-1ec7-4c4f-ab85-3555d8e3fa30", "account": "Hello", "token": "Hello",
                   "timestamp": 100, "caller_ip": "Hello", "mock_systems_key": "Hello", "mock_reqid": ""},
        "order_id_list": order_id_list})
    headers = {'Content-Type': 'application/json'}

    return requests.request("POST", url, headers=headers, data=payload).json()['order_result_list']


def read_csv_file(file_path):
    need_process_orders = []
    with open(file_path, 'r') as file:
        csv_reader = csv.DictReader(file)
        for row in csv_reader:
            need_process_orders.append(int(row['order_id']))

    return need_process_orders


def read_json_file(file_path):
    need_process_orders = []
    with open(file_path, 'r') as file:
        for row in file.readlines():
            need_process_orders.append(json.loads(row)['oid'])

    return need_process_orders


sync_allocate_but_in_result_tab_orders = []


def append_order_results(order_id_list):
    for order_result in get_order_result(order_id_list):
        if order_result['retcode'] == 30000:
            sync_allocate_but_in_result_tab_orders.append({'order_id': order_result['order_id'],
                                                           'fulfillment_product_id': order_result[
                                                               'fulfillment_product_id']})
        else:
            print(order_result)


with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
    # 提交任务到线程池并获取 Future 对象列表
    order_id_list = read_json_file('fallback_to_sync')
    futures = []
    for i in range(0, len(order_id_list), 1000):
        futures.append(executor.submit(append_order_results, order_id_list[i:i + 1000]))

    # 获取任务结果
    for future in concurrent.futures.as_completed(futures):
        result = future.result()

with open('need_decrease_orders_2M.json', 'w', newline='') as file:
    print(len(sync_allocate_but_in_result_tab_orders))
    file.write(json.dumps(sync_allocate_but_in_result_tab_orders))
