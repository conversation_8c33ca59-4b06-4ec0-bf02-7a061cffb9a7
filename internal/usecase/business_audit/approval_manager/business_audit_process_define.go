package approval_manager

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BusinessAuditProcess interface {
	setNext(process BusinessAuditProcess)
	execute(ctx context.Context, businessUnit approval_unit.BusinessUnit) *srerr.Error
}
