package approval_manager

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

const project = "SLS"

type AuditProcess struct {
	auditApi          auditclient.AuditApi
	businessAuditRepo business_audit.BusinessAuditRepo
}

func (p *AuditProcess) execute(ctx context.Context, businessUnitType string, configDbId int64) *srerr.Error {
	businessUnit := approval_unit.GetBusinessUnit(businessUnitType)
	if businessUnit == nil {
		logger.CtxLogErrorf(ctx, "business unit not found")
		return srerr.New(srerr.DataErr, nil, "business unit not found")
	}

	conf := configutil.GetBusinessAuditConf(ctx)

	//1. get parameters
	extraData := businessUnit.GetExtraData()
	applicationInfo := businessUnit.GetApplicationInfo(configDbId)
	operator, _ := apiutil.GetUserInfo(ctx)
	permissionCodeMap := conf.PermissionCodeMap
	permissionCode := permissionCodeMap[businessUnit.BusinessType()]
	if permissionCode == "" {
		logger.CtxLogErrorf(ctx, "business type:%s, got wrong permission code", businessUnit.BusinessType())
		return srerr.New(srerr.DataErr, nil, "got wrong permission code")
	}

	//2. make up approval request
	req := &auditclient.AuditApprovalRequest{
		PermissionCode:  permissionCode,
		Applier:         operator,
		Project:         project,
		Region:          envvar.GetCID(),
		ExtraData:       extraData,
		ApplicationInfo: applicationInfo,
	}
	ticketID, aErr := p.auditApi.AuditApproval(ctx, conf.XOpaToken, req)
	if aErr != nil {
		logger.CtxLogErrorf(ctx, "audit approval err:%v", aErr)
		return aErr
	}

	tab := businessUnit.GenerateAuditTab(ctx, uint64(configDbId))
	tab.TicketID = ticketID

	//3. 生成审批记录
	if cErr := p.businessAuditRepo.Create(ctx, tab); cErr != nil {
		logger.CtxLogErrorf(ctx, "create audit tab err:%v", cErr)
		return cErr
	}

	return nil
}
