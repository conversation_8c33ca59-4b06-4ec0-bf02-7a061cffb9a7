package approval_manager

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ApprovalExecutor interface {
	Execute(ctx context.Context, businessUnitType string, configDbId int64) *srerr.Error
}

type ApprovalExecutorImpl struct {
	auditApi          auditclient.AuditApi
	businessAuditRepo business_audit.BusinessAuditRepo
}

func NewApprovalExecutorImpl(
	auditApi auditclient.AuditApi,
	businessAuditRepo business_audit.BusinessAuditRepo) *ApprovalExecutorImpl {
	return &ApprovalExecutorImpl{
		auditApi:          auditApi,
		businessAuditRepo: businessAuditRepo,
	}
}

func (i *ApprovalExecutorImpl) Execute(ctx context.Context, businessUnitType string, configDbId int64) *srerr.Error {
	auditProcess := &AuditProcess{auditApi: i.auditApi, businessAuditRepo: i.businessAuditRepo}

	return auditProcess.execute(ctx, businessUnitType, configDbId)
}
