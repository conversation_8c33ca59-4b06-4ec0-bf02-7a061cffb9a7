package approval_listener

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ListenerExecutor interface {
	Listen(ctx context.Context, req *ListenerRequest) *srerr.Error
}

type ListenerExecutorImpl struct {
	businessAuditRepo business_audit.BusinessAuditRepo
}

func NewListenerExecutorImpl(businessAuditRepo business_audit.BusinessAuditRepo) *ListenerExecutorImpl {
	return &ListenerExecutorImpl{
		businessAuditRepo: businessAuditRepo,
	}
}

func (i *ListenerExecutorImpl) Listen(ctx context.Context, req *ListenerRequest) *srerr.Error {
	//1. 根据req获取ticket id
	ticketID := req.TicketID
	//2. 根据ticket id从db中获取对应的Business type
	tab, gErr := i.businessAuditRepo.GetTab(ctx, map[string]interface{}{
		"ticket_id = ?": ticketID,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get audit tab by ticket id:%s, err:%v", ticketID, gErr)
		return gErr
	}
	//3. 根据Business type获取对应的Business unit实例
	businessUnit := approval_unit.GetBusinessUnit(tab.BusinessType)

	//4. 调用Business unit实例的listener方法
	return businessUnit.Listener(ctx, ticketID, req.Status, tab)
}
