package allocpath

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient/allocpath"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
)

type (
	BasicInfo struct {
		OrderId              string `json:"order_id"`
		FOrderId             string `json:"forder_id"`
		RequestId            string `json:"request_id"`
		UniqueId             string `json:"unique_id"`
		RequestTime          int    `json:"request_time"`
		AllocationStatus     bool   `json:"status"`
		FulfillmentProductId string `json:"fulfillment_product_id"`
		MaskProductId        string `json:"mask_product_id"`
		ResponseData         string `json:"response_data"`
		RequestData          string `json:"request_data"`
		AllocationMethod     int    `json:"allocation_method"`
		IsWms                bool   `json:"is_wms"`
	}

	DetailData struct {
		BasicInfo         BasicInfo         `json:"basic_info"`
		HardCriteriaResp  HardCriteriaResp  `json:"hard_criteria"`
		SoftCriteriaResp  SoftCriteriaResp  `json:"soft_criteria"`
		BatchSoftCriteria BatchSoftCriteria `json:"batch_soft_criteria"`
	}

	HardCriteriaResp struct {
		Input         []string           `json:"input"`
		Output        []string           `json:"output"`
		ProductToggle map[int64][]string `json:"product_toggle"`
		Snapshot      *schema.Snapshot   `json:"snapshot"`
	}

	SoftCriteriaResp struct {
		Input               []string               `json:"input"`
		Output              []string               `json:"output"`
		RuleId              int                    `json:"rule_id"`
		VolumeRoutingRuleId int                    `json:"volume_routing_rule_id"`
		VolumeGroupInfos    []VolumeGroupInfo      `json:"volume_group_infos"`
		ShopGroupId         int                    `json:"shop_group_id"`
		RuleType            string                 `json:"rule_type"`
		ZoneOrigin          []string               `json:"zone_origin"`
		ZoneDestination     []string               `json:"zone_destination"`
		Routes              []string               `json:"routes"`
		TableTitles         []string               `json:"table_titles"`
		List                []SoftCriteriaRespItem `json:"list"`
	}

	Template struct {
		FieldName string `json:"field_name"`
		Visible   bool   `json:"visible"`
	}

	SoftCriteriaRespItem struct {
		Priority int                       `json:"priority"`
		Name     string                    `json:"name"`
		Detail   []*SoftCriteriaItemDetail `json:"detail"`
	}

	SoftCriteriaItemDetail struct {
		Input  string    `json:"input"`
		Output string    `json:"output"`
		Data   []float64 `json:"data"`
	}

	//batch soft criteria
	BatchSoftCriteria struct {
		Input                            []string                 `json:"input"`
		Output                           []string                 `json:"output"`
		SoftCriteriaRuleID               int64                    `json:"soft_criteria_rule_id"`
		ShopGroupID                      int64                    `json:"shop_group_id"`
		VolumeRuleID                     int64                    `json:"volume_rule_id"`
		RuleType                         string                   `json:"rule_type"`
		ZoneCodeList                     []string                 `json:"zone_code_list"`
		RouteCodeList                    []string                 `json:"route_code_list"`
		BatchID                          int64                    `json:"batch_id"`
		BatchName                        string                   `json:"batch_name"`
		BatchSize                        int64                    `json:"batch_size"`
		BatchTime                        string                   `json:"batch_time"`
		Steps                            []allocpath.Step         `json:"steps"`
		BatchLevelAllocationDistribution []allocpath.Distribution `json:"batch_level_allocation_distribution"`
	}

	//Step struct {
	//	SoftCriteriaDetailList []BatchSoftCriteriaDetail `json:"soft_criteria_detail_list"`
	//}
	//
	//BatchSoftCriteriaDetail struct {
	//	Step             string   `json:"step"`
	//	SoftCriteriaName string   `json:"soft_criteria_name"`
	//	InputProduct     string   `json:"input_product"`
	//	OutputProduct    string   `json:"output_product"`
	//	Titles           []string `json:"titles"` //涉及的zone
	//	Values           []string `json:"values"`
	//}
	//
	//Distribution struct {
	//	InputProduct          string `json:"input_product"`
	//	ProductName           string `json:"product_name"`
	//	MaxDailyLimitCountry  int64  `json:"max_daily_limit_country"`
	//	MinBatchLimitCountry  int64  `json:"min_batch_limit_country"`
	//	SystemVolumeProduct   int64  `json:"system_volume_product"`
	//	AllocationShippingFee string `json:"allocation_shipping_fee"`
	//}

	VolumeGroupInfo struct {
		GroupCode           string `json:"group_code"`
		FulfillmentProducts []int  `json:"fulfillment_products"`
	}
)

type (
	ProductToggle struct {
		Available AvailableType `json:"available"`
		Priority  int           `json:"priority"`
		ProductId int64         `json:"product_id"`
	}
	//HardCriteria struct {
	//	ProductToggle map[int64]ProductToggle `json:"product_toggle"`
	//}
	RouteLimit struct {
		RouteCode string  `json:"route_code"`
		Limit     float64 `json:"limit"`
	}
	ZoneLimit struct {
		ZoneCode string  `json:"zone_code"`
		Limit    float64 `json:"limit"`
	}
	SoftCriteriaDetail struct {
		Input                          int64        `json:"input"`
		Output                         int64        `json:"output"`
		MaxDailyLimitOfCountry         float64      `json:"max_daily_limit_of_country"`
		MaxDailyLimitOfRoutes          []RouteLimit `json:"max_daily_limit_of_routes"`
		MaxDailyLimitOfZoneDestination []ZoneLimit  `json:"max_daily_limit_of_zone_destination"`
		MaxDailyLimitOfZoneOrigin      []ZoneLimit  `json:"max_daily_limit_of_zone_origin"`
		MinDailyLimitOfCountry         float64      `json:"min_daily_limit_of_country"`
		MinDailyLimitOfRoutes          []RouteLimit `json:"min_daily_limit_of_routes"`
		MinDailyLimitOfZoneDestination []ZoneLimit  `json:"min_daily_limit_of_zone_destination"`
		MinDailyLimitOfZoneOrigin      []ZoneLimit  `json:"min_daily_limit_of_zone_origin"`
		ShippingFee                    float64      `json:"shipping_fee"`
		SystemVolumeOfProduct          float64      `json:"system_volume_of_product"`
		SystemVolumeOfRoutes           []RouteLimit `json:"system_volume_of_routes"`
		SystemVolumeOfZoneDestination  []ZoneLimit  `json:"system_volume_of_zone_destination"`
		SystemVolumeOfZoneOrigin       []ZoneLimit  `json:"system_volume_of_zone_origin"`
		MaxVolumeInBatch               float64      `json:"max_volume_in_batch"`
		SystemVolumeInBatch            float64      `json:"system_volume_in_batch"`
		BatchSize                      float64      `json:"batch_size"`
		ProductWeightage               float64      `json:"product_weightage"`
		ProductStatus                  bool         `json:"product_status"`
		ProductPriority                float64      `json:"product_priority"`
		WhitelistPriority              float64      `json:"whitelist_priority"`
	}
	OldSoftCriteriaDetail struct {
		Input                          int64        `json:"input"`
		Output                         int64        `json:"output"`
		MaxDailyLimitOfCountry         float64      `json:"max_daily_limit_of_country"`
		MaxDailyLimitOfRoutes          []RouteLimit `json:"max_daily_limit_of_routes"`
		MaxDailyLimitOfZoneDestination float64      `json:"max_daily_limit_of_zone_destination"`
		MaxDailyLimitOfZoneOrigin      float64      `json:"max_daily_limit_of_zone_origin"`
		MinDailyLimitOfCountry         float64      `json:"min_daily_limit_of_country"`
		MinDailyLimitOfRoutes          []RouteLimit `json:"min_daily_limit_of_routes"`
		MinDailyLimitOfZoneDestination float64      `json:"min_daily_limit_of_zone_destination"`
		MinDailyLimitOfZoneOrigin      float64      `json:"min_daily_limit_of_zone_origin"`
		ShippingFee                    float64      `json:"shipping_fee"`
		SystemVolumeOfProduct          float64      `json:"system_volume_of_product"`
		SystemVolumeOfRoutes           []RouteLimit `json:"system_volume_of_routes"`
		SystemVolumeOfZoneDestination  float64      `json:"system_volume_of_zone_destination"`
		SystemVolumeOfZoneOrigin       float64      `json:"system_volume_of_zone_origin"`
		MaxVolumeInBatch               float64      `json:"max_volume_in_batch"`
		SystemVolumeInBatch            float64      `json:"system_volume_in_batch"`
		BatchSize                      float64      `json:"batch_size"`
		ProductWeightage               float64      `json:"product_weightage"`
		ProductStatus                  bool         `json:"product_status"`
		ProductPriority                float64      `json:"product_priority"`
		WhitelistPriority              float64      `json:"whitelist_priority"`
	}

	SoftCriteria struct {
		Id     int                           `json:"id"`
		Name   string                        `json:"name"`
		Detail map[int64]*SoftCriteriaDetail `json:"detail"`
	}
	OldSoftCriteria struct {
		Id     int                              `json:"id"`
		Name   string                           `json:"name"`
		Detail map[int64]*OldSoftCriteriaDetail `json:"detail"`
	}
)

type (
	BasicInfoForWeb struct {
		OrderId              string `json:"order_id"`
		FOrderId             string `json:"forder_id"`
		RequestId            string `json:"request_id"`
		UniqueId             string `json:"unique_id"`
		RequestTime          int    `json:"request_time"`
		Status               bool   `json:"status"`
		FulfillmentProductId int64  `json:"fulfillment_product_id"`
		MaskProductId        int64  `json:"mask_product_id"`
		RowKey               string `json:"row_key"`
	}

	ListDataInfoForWeb struct {
		List     []BasicInfoForWeb `json:"list"`
		PageNo   int               `json:"page_no"`
		PageSize int               `json:"page_size"`
		Total    int               `json:"total"`
	}
)

type AvailableType int

const (
	Available   AvailableType = 1
	UnAvailable AvailableType = 2
	NotExisted  AvailableType = 3
)

func (at AvailableType) String() string {
	switch at {
	case Available:
		return "On"
	case UnAvailable:
		return "Off"
	case NotExisted:
		return "-"
	}
	return ""
}
