package allocpath

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	ap "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"math"
	"strconv"
	"strings"
)

type AllocationPathService interface {
	GetList(ctx context.Context, requestId string, orderId, fOrderId uint64, pageNo, pageSize int) (*ListDataInfoForWeb, *srerr.Error)
	GetDetail(ctx context.Context, requestId string) (*DetailData, *srerr.Error)
	GetDebugDetail(ctx context.Context, respV2 *ap.DetailV2) (*DetailData, *srerr.Error)
	GetDetailByOrderId(ctx context.Context, orderId uint64) (*DetailData, *srerr.Error)
}

const (
	maxSecond   = ***********
	milToSecond = 1e3
)

type AllocationPathSrvImpl struct {
	dataApi               *dataclient.DataApi
	lpsApi                lpsclient.LpsApi
	prodRepo              product.ProdRepo
	maskRuleVolumeService rulevolume.MaskRuleVolumeService
}

func NewAllocationPathSrvImpl(
	dataApi *dataclient.DataApi,
	lpsApi lpsclient.LpsApi,
	prodRepo product.ProdRepo,
	maskRuleVolumeService rulevolume.MaskRuleVolumeService,
) *AllocationPathSrvImpl {
	return &AllocationPathSrvImpl{
		dataApi:               dataApi,
		lpsApi:                lpsApi,
		prodRepo:              prodRepo,
		maskRuleVolumeService: maskRuleVolumeService,
	}
}

func (a *AllocationPathSrvImpl) GetDebugDetail(ctx context.Context, respV2 *ap.DetailV2) (*DetailData, *srerr.Error) {
	if respV2 == nil {
		return nil, srerr.With(srerr.ParamErr, nil, fmt.Errorf("respV2 is nil"))
	}
	return a.ConvertDataSchemaToRealV2(ctx, respV2)
}

func (a *AllocationPathSrvImpl) GetList(ctx context.Context, requestId string, orderId, fOrderId uint64, pageNo, pageSize int) (*ListDataInfoForWeb, *srerr.Error) {
	var (
		respV2 *ap.ListDataInfoV2
		err    *srerr.Error
	)

	respV2, err = a.dataApi.GetMaskingAllocationPathListV2(ctx, requestId, orderId, fOrderId, pageNo, pageSize)
	if err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}

	return ConvertListForWeb(respV2), nil
}

func (a *AllocationPathSrvImpl) GetDetail(ctx context.Context, requestId string) (*DetailData, *srerr.Error) {
	var (
		respV2 *ap.DetailV2
		err    *srerr.Error
	)

	//todo:优化项，让前端直接传row_key过来，就不用掉一遍list接口
	listResp, lErr := a.dataApi.GetMaskingAllocationPathListV2(ctx, requestId, 0, 0, 1, 1)
	if lErr != nil {
		logger.CtxLogErrorf(ctx, "GetDetail|get list err:%v", lErr)
		return nil, srerr.With(srerr.FormatErr, nil, lErr)
	}
	if len(listResp.List) == 0 {
		logger.CtxLogErrorf(ctx, "GetDetail|list is empty")
		return nil, srerr.With(srerr.InvalidRequestIdError, nil, fmt.Errorf("invalid request_id, get empty list"))
	}
	respV2, err = a.dataApi.GetMaskingAllocationPathDetailV2(ctx, listResp.List[0].RowKey)
	if err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if respV2 == nil {
		return nil, srerr.With(srerr.InvalidRequestIdError, nil, fmt.Errorf("invalid request_id"))
	}

	return a.ConvertDataSchemaToRealV2(ctx, respV2)
}

func (a *AllocationPathSrvImpl) GetDetailByOrderId(ctx context.Context, orderId uint64) (*DetailData, *srerr.Error) {
	var (
		respV2 *ap.DetailV2
		err    *srerr.Error
	)
	//1. 根据orderId从es搜索rowKey
	listResp, lErr := a.dataApi.GetMaskingAllocationPathListV3(ctx, "", orderId, 0, 1, 1)
	if lErr != nil {
		logger.CtxLogErrorf(ctx, "GetDetail|get list err:%v", lErr)
		return nil, srerr.With(srerr.FormatErr, nil, lErr)
	}
	if len(listResp.List) == 0 {
		logger.CtxLogErrorf(ctx, "GetDetail|list is empty")
		return nil, srerr.With(srerr.InvalidOrderIdError, nil, fmt.Errorf("invalid order_id, get empty list"))
	}
	//2. 根据rowKey从hbase查询数据
	respV2, err = a.dataApi.GetMaskingAllocationPathDetailV3(ctx, listResp.List[0].RowKey)
	if err != nil {
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}
	if respV2 == nil {
		return nil, srerr.With(srerr.InvalidOrderIdError, nil, fmt.Errorf("invalid order_id, get empty list"))
	}
	return a.ConvertDataSchemaToRealV2(ctx, respV2)
}

func (a *AllocationPathSrvImpl) ConvertDataSchemaToRealV2(ctx context.Context, data *ap.DetailV2) (*DetailData, *srerr.Error) {
	if len(data.Steps) != 0 {
		return a.convertBatchPath(ctx, data)
	} else {
		return a.convertSinglePath(ctx, data)
	}
}

func (a *AllocationPathSrvImpl) convertSinglePath(ctx context.Context, data *ap.DetailV2) (*DetailData, *srerr.Error) {
	productMap := a.prodRepo.GetProductSellerMap(ctx)
	softCriteriaList := make([]SoftCriteria, 0)
	if data.SoftCriteriaList != "" { //反序列化soft criteria
		if err := jsoniter.Unmarshal([]byte(data.SoftCriteriaList), &softCriteriaList); err != nil {
			logger.CtxLogErrorf(ctx, "unmarshal soft criteria err:%v", err)
			oldSoftCriteriaList := make([]OldSoftCriteria, 0)
			if err := jsoniter.Unmarshal([]byte(data.SoftCriteriaList), &oldSoftCriteriaList); err != nil {
				logger.CtxLogErrorf(ctx, "unmarshal old soft criteria err:%v", err)
				return nil, srerr.With(srerr.JsonErr, nil, err)
			}
			//兼容存量数据
			softCriteriaList = convertNewSoftCriteriaList(data.ZoneOriginCode, data.ZoneDestinationCode, oldSoftCriteriaList)
		}
	}

	hardCriteriaList := make(map[int64][]ProductToggle)
	if data.HardCriteriaList != "" { //反序列化hard criteria
		if err := jsoniter.Unmarshal([]byte(data.HardCriteriaList), &hardCriteriaList); err != nil {
			return nil, srerr.With(srerr.JsonErr, nil, err)
		}
	}

	dataSource := getDataSource(data.RequestId)
	snapshot, _ := GetSnapshot(ctx, dataSource, data.RequestData, data.UniqueId)

	var (
		volumeRuleType   string
		volumeGroupInfos []VolumeGroupInfo
	)

	if data.VolumeRuleId != 0 {
		volumeRule, err := a.maskRuleVolumeService.GetRuleVolumeByIDWithCache(ctx, uint64(data.VolumeRuleId))
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetRuleVolumeByIDWithCache|id=%d,err=%v", data.VolumeRuleId, err)
		} else {
			volumeRuleType = volumeRule.RuleType.String()
			for _, g := range volumeRule.GroupInfo.FulfillmentProductGroupInfos {
				volumeGroupInfo := VolumeGroupInfo{GroupCode: g.GroupCode, FulfillmentProducts: make([]int, 0, len(g.FulfillmentProductInfos))}
				for _, fulfillmentProductInfo := range g.FulfillmentProductInfos {
					volumeGroupInfo.FulfillmentProducts = append(volumeGroupInfo.FulfillmentProducts, fulfillmentProductInfo.FulfillmentProductID)
				}
				volumeGroupInfos = append(volumeGroupInfos, volumeGroupInfo)
			}
		}
	}

	resp := &DetailData{
		BasicInfo: BasicInfo{
			OrderId:              ConvertUint64ToString(data.OrderId),
			FOrderId:             ConvertUint64ToString(data.FOrderId),
			RequestId:            data.RequestId,
			UniqueId:             data.UniqueId,
			RequestTime:          data.RequestTime,
			AllocationStatus:     true, //hard code 为true,allocation 不会失败
			MaskProductId:        FormatProductAndName(productMap, data.MaskProductId),
			FulfillmentProductId: FormatProductAndName(productMap, data.FulfillmentProductId),
			RequestData:          data.RequestData,
			ResponseData:         data.ResponseData,
			AllocationMethod:     allocation.SingleAllocate,
		},
		HardCriteriaResp: HardCriteriaResp{
			Input:         FormatMultiProductV2(productMap, data.HardInput),
			Output:        FormatMultiProductV2(productMap, data.HardOutput),
			Snapshot:      snapshot,
			ProductToggle: FormatProductToggle(productMap, hardCriteriaList),
		},
		SoftCriteriaResp: SoftCriteriaResp{
			Input:               FormatMultiProductV2(productMap, data.SoftInput),
			Output:              FormatMultiProductV2(productMap, data.SoftOutput),
			RuleId:              data.SoftRuleId,
			VolumeRoutingRuleId: data.VolumeRuleId,
			VolumeGroupInfos:    volumeGroupInfos,
			ShopGroupId:         data.ShopGroupId,
			RuleType:            volumeRuleType,
			ZoneOrigin:          getZoneCodes(data.ZoneOriginCode),
			ZoneDestination:     getZoneCodes(data.ZoneDestinationCode),
			Routes:              ConvertNilSliceToEmpty(data.RouteCodes),
		},
	}

	softCriteriaMap := GenSoftRuleStepMap(softCriteriaList)
	resp.SoftCriteriaResp.TableTitles = FormatSoftCriteriaTableTitles(
		resp.SoftCriteriaResp.ZoneOrigin,
		resp.SoftCriteriaResp.ZoneDestination,
		resp.SoftCriteriaResp.Routes,
		softCriteriaMap,
	)
	resp.SoftCriteriaResp.List = FormatSortCriteriaList(productMap, softCriteriaList,
		resp.SoftCriteriaResp.ZoneOrigin,
		resp.SoftCriteriaResp.ZoneDestination,
		resp.SoftCriteriaResp.Routes,
		softCriteriaMap)
	if resp.BasicInfo.RequestTime > maxSecond {
		resp.BasicInfo.RequestTime = resp.BasicInfo.RequestTime / milToSecond
	}
	return resp, nil
}

func (a *AllocationPathSrvImpl) convertBatchPath(ctx context.Context, data *ap.DetailV2) (*DetailData, *srerr.Error) {
	productMap := a.prodRepo.GetProductSellerMap(ctx)

	resp := &DetailData{
		BasicInfo: BasicInfo{
			OrderId:              ConvertUint64ToString(data.OrderId),
			FOrderId:             ConvertUint64ToString(data.FOrderId),
			RequestId:            data.RequestId,
			UniqueId:             data.UniqueId,
			RequestTime:          int(data.RequestTime) / 1000,
			AllocationStatus:     true, //hard code 为true,allocation 不会失败
			MaskProductId:        FormatProductAndName(productMap, data.MaskProductId),
			FulfillmentProductId: FormatProductAndName(productMap, data.FulfillmentProductId),
			AllocationMethod:     allocation.BatchAllocate,
		},
		HardCriteriaResp: HardCriteriaResp{
			Input:  FormatMultiProductV2(productMap, data.HardInput),
			Output: FormatMultiProductV2(productMap, data.HardOutput),
			//Snapshot:      snapshot,
			//ProductToggle: FormatProductToggle(productMap, hardCriteriaList),
		},
		BatchSoftCriteria: BatchSoftCriteria{
			Input:                            objutil.IntsToStrings(data.Input),
			Output:                           objutil.IntsToStrings(data.Output),
			SoftCriteriaRuleID:               int64(data.SoftRuleId),
			ShopGroupID:                      int64(data.ShopGroupId),
			VolumeRuleID:                     int64(data.VolumeRuleId),
			RuleType:                         data.RuleType,
			ZoneCodeList:                     data.DestZoneCodeList,
			RouteCodeList:                    data.RouteCodeList,
			BatchID:                          int64(data.BatchId),
			BatchSize:                        int64(data.BatchSize),
			BatchTime:                        data.BatchTime,
			BatchName:                        data.BatchName,
			Steps:                            data.Steps,
			BatchLevelAllocationDistribution: data.BatchAllocationDistribution,
		},
	}
	return resp, nil
}

func SplitNilSliceForEmptyString(src, separator string) []string {
	if src == "" {
		return []string{}
	}
	return strings.Split(src, separator)
}

func ConvertNilSliceToEmpty(slice []string) []string {
	if len(slice) == 0 {
		return []string{}
	}
	return slice
}

func GenSoftRuleStepMap(softCriteriaList []SoftCriteria) map[string]bool {
	resp := make(map[string]bool)
	for _, sc := range softCriteriaList {
		resp[sc.Name] = true
	}
	return resp
}

func genMaxTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	resp = append(resp, "Max Daily Limit of Country")
	resp = append(resp, genMaxRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	return resp
}

func genMaxRuleVolumeTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	for i := range zoneOrg {
		resp = append(resp, "Max Daily Limit of Origin Zone "+zoneOrg[i])
	}
	for i := range zoneDst {
		resp = append(resp, "Max Daily Limit of Dest Zone "+zoneDst[i])
	}
	for i := range routes {
		resp = append(resp, "Max Daily Limit of Route "+routes[i])
	}
	return resp
}

func genRuleParcelVolumeTitle(zoneOrg, zoneDst []string, routes []string, parcelType string) []string {
	resp := make([]string, 0, len(zoneOrg)*2+len(zoneDst)*2+len(routes)*2)
	for i := range zoneOrg {
		resp = append(resp, "Max Daily "+parcelType+" Limit of Origin Zone "+zoneOrg[i])
		resp = append(resp, "System "+parcelType+" Volume of Origin Zone "+zoneOrg[i])
	}
	for i := range zoneDst {
		resp = append(resp, "Max Daily "+parcelType+" Limit of Dest Zone "+zoneDst[i])
		resp = append(resp, "System "+parcelType+" Volume of Dest Zone "+zoneDst[i])
	}
	for i := range routes {
		resp = append(resp, "Max Daily "+parcelType+" Limit of Route "+routes[i])
		resp = append(resp, "System "+parcelType+" Volume of Route "+routes[i])
	}
	return resp
}

func genMinTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	resp = append(resp, "Min Daily Limit of Country")
	resp = append(resp, genMinRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	return resp
}

func genMinRuleVolumeTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	for i := range zoneOrg {
		resp = append(resp, "Min Daily Limit of Origin Zone "+zoneOrg[i])
	}
	for i := range zoneDst {
		resp = append(resp, "Min Daily Limit of Dest Zone "+zoneDst[i])
	}
	for i := range routes {
		resp = append(resp, "Min Daily Limit of Route "+routes[i])
	}
	return resp
}

func genSystemTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	resp = append(resp, "System Volume of Product")
	resp = append(resp, genSystemRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	return resp
}

func genSystemRuleVolumeTitle(zoneOrg, zoneDst []string, routes []string) []string {
	resp := make([]string, 0)
	for i := range zoneOrg {
		resp = append(resp, "System Volume of Origin Zone "+zoneOrg[i])
	}
	for i := range zoneDst {
		resp = append(resp, "System Volume of Dest Zone "+zoneDst[i])
	}
	for i := range routes {
		resp = append(resp, "System Volume of Route "+routes[i])
	}
	return resp
}

func FormatSoftCriteriaTableTitles(zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool) []string {
	resp := make([]string, 0)

	//兼容旧的数据
	_, maxOk := softCriteriaMap[rule.MaskStepMaxCapacityName]
	if maxOk {
		resp = append(resp, genMaxTitle(zoneOrg, zoneDst, routes)...)
	}
	_, minOk := softCriteriaMap[rule.MaskStepMinVolumeName]
	if minOk {
		resp = append(resp, genMinTitle(zoneOrg, zoneDst, routes)...)
	}
	if maxOk || minOk {
		resp = append(resp, genSystemTitle(zoneOrg, zoneDst, routes)...)
	}

	//兼容新的因子
	_, maxCountryOk := softCriteriaMap[rule.MaskStepMaxCapacityCountryName]
	if maxCountryOk {
		resp = append(resp, "Max Daily Limit of Country")
	}

	_, maxZoneRouteOk := softCriteriaMap[rule.MaskStepMaxCapacityZoneRouteName]
	if maxZoneRouteOk {
		resp = append(resp, genMaxRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	}

	_, minCountryOk := softCriteriaMap[rule.MaskStepMinVolumeCountryName]
	if minCountryOk {
		resp = append(resp, "Min Daily Limit of Country")
	}

	_, minZoneRouteOk := softCriteriaMap[rule.MaskStepMinVolumeZoneRouteName]
	if minZoneRouteOk {
		resp = append(resp, genMinRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	}

	if maxCountryOk || minCountryOk {
		resp = append(resp, "System Volume of Product")
	}

	if minZoneRouteOk || maxZoneRouteOk {
		resp = append(resp, genSystemRuleVolumeTitle(zoneOrg, zoneDst, routes)...)
	}

	_, maxCodCountryOk := softCriteriaMap[rule.MaskStepMaxCodCapacityCountryName]
	if maxCodCountryOk {
		resp = append(resp, "Max Daily COD Limit of Country")
		resp = append(resp, "System COD Volume of Product")
	}
	_, maxCodZoneRouteOk := softCriteriaMap[rule.MaskStepMaxCodCapacityZoneRouteName]
	if maxCodZoneRouteOk {
		resp = append(resp, genRuleParcelVolumeTitle(zoneOrg, zoneDst, routes, "Cod")...)
	}

	_, maxBulkyCountryOk := softCriteriaMap[rule.MaskStepMaxBulkyCapacityCountryName]
	if maxBulkyCountryOk {
		resp = append(resp, "Max Daily Bulky Limit of Country")
		resp = append(resp, "System Bulky Volume of Product")
	}
	_, maxBulkyZoneRouteOk := softCriteriaMap[rule.MaskStepMaxBulkyCapacityZoneRouteName]
	if maxBulkyZoneRouteOk {
		resp = append(resp, genRuleParcelVolumeTitle(zoneOrg, zoneDst, routes, "Bulky")...)
	}

	_, maxHighValueCountryOk := softCriteriaMap[rule.MaskStepMaxHighValueCapacityCountryName]
	if maxHighValueCountryOk {
		resp = append(resp, "Max Daily HighValue Limit of Country")
		resp = append(resp, "System HighValue Volume of Product")
	}
	_, maxHighValueZoneRouteOk := softCriteriaMap[rule.MaskStepMaxHighValueCapacityZoneRouteName]
	if maxHighValueZoneRouteOk {
		resp = append(resp, genRuleParcelVolumeTitle(zoneOrg, zoneDst, routes, "HighValue")...)
	}

	_, maxDGCountryOk := softCriteriaMap[rule.MaskStepMaxDgCapacityCountryName]
	if maxDGCountryOk {
		resp = append(resp, "Max Daily DG Limit of Country")
		resp = append(resp, "System DG Volume of Product")
	}
	_, maxDGZoneRouteOk := softCriteriaMap[rule.MaskStepMaxDgCapacityZoneRouteName]
	if maxDGZoneRouteOk {
		resp = append(resp, genRuleParcelVolumeTitle(zoneOrg, zoneDst, routes, "DG")...)
	}

	if _, ok := softCriteriaMap[rule.MaskStepCheapestFeeName]; ok {
		resp = append(resp, "3PL Shipping Fee")
	}

	//不变的逻辑
	if _, ok := softCriteriaMap[rule.MaskStepMaxBatchVolumeName]; ok {
		resp = append(resp, "Batch Size")
		resp = append(resp, "Max Volume in One Batch")
		resp = append(resp, "System Volume in One Batch")
	}
	if _, ok := softCriteriaMap[entity.MaskDefaultProductPriority]; ok {
		resp = append(resp, "Seller Group product Priority/Weightage")
	}
	if _, ok := softCriteriaMap[entity.MaskDefaultProductWeightage]; ok {
		resp = append(resp, "Seller Group product Priority/Weightage")
	}
	if _, ok := softCriteriaMap[rule.MaskStepPickupEfficiencyWhitelistName]; ok {
		resp = append(resp, "Pickup Efficiency Whitelist Priority")
	}
	return resp
}

func FormatSortCriteriaList(productMap map[int64]string, softCriteriaList []SoftCriteria,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool) []SoftCriteriaRespItem {
	sortCriteriaItems := make([]SoftCriteriaRespItem, 0, len(softCriteriaList))
	for _, sc := range softCriteriaList {
		item := SoftCriteriaRespItem{
			Priority: sc.Id,
			Name:     convertSoftCriteriaItemName(sc.Name),
			Detail:   ConvertSoftCriteriaDetailToData(productMap, sc.Detail, zoneOrg, zoneDst, routes, softCriteriaMap, sc.Name),
		}
		sortCriteriaItems = append(sortCriteriaItems, item)
	}
	return sortCriteriaItems
}

func convertSoftCriteriaItemName(src string) string {
	if len(src) < 4 {
		return ""
	}
	return src[4:]
}

func convertSoftCriteriaDetailMax(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool, name string) {
	okOld := softCriteriaMap[rule.MaskStepMaxCapacityName]
	okNew := softCriteriaMap[rule.MaskStepMaxCapacityCountryName]
	zone := softCriteriaMap[rule.MaskStepMaxCapacityZoneRouteName]
	if !okOld && !okNew && !zone { // 兼容新旧因子
		return
	}

	if okOld || okNew {
		if d.MaxDailyLimitOfCountry == -1 || (name != rule.MaskStepMaxCapacityName && name != rule.MaskStepMaxCapacityCountryName) {
			detail.Data = append(detail.Data, -math.MaxInt32)
		} else {
			detail.Data = append(detail.Data, d.MaxDailyLimitOfCountry)
		}
	}

	if okOld || zone {
		for i := 0; i < len(zoneOrg); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.MaxDailyLimitOfZoneOrigin {
				//如果zone code匹配上，且对应的运力max limit为有效值，装填该值
				if d.MaxDailyLimitOfZoneOrigin[j].ZoneCode == zoneOrg[i] && d.MaxDailyLimitOfZoneOrigin[j].Limit != -1 {
					limit = d.MaxDailyLimitOfZoneOrigin[j].Limit
				}
			}
			if name != rule.MaskStepMaxCapacityName && name != rule.MaskStepMaxCapacityZoneRouteName {
				limit = -math.MaxInt32
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(zoneDst); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.MaxDailyLimitOfZoneDestination {
				//如果zone code匹配上，且对应的运力max limit为有效值，装填该值
				if d.MaxDailyLimitOfZoneDestination[j].ZoneCode == zoneDst[i] && d.MaxDailyLimitOfZoneDestination[j].Limit != -1 {
					limit = d.MaxDailyLimitOfZoneDestination[j].Limit
				}
			}
			if name != rule.MaskStepMaxCapacityName && name != rule.MaskStepMaxCapacityZoneRouteName {
				limit = -math.MaxInt32
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(routes); i++ {
			isFind := false
			for j := range d.MaxDailyLimitOfRoutes {
				if d.MaxDailyLimitOfRoutes[j].RouteCode != routes[i] {
					continue
				}
				if d.MaxDailyLimitOfRoutes[j].Limit == -1 || (name != rule.MaskStepMaxCapacityName && name != rule.MaskStepMaxCapacityCountryName) {
					detail.Data = append(detail.Data, -math.MaxInt32)
				} else {
					detail.Data = append(detail.Data, d.MaxDailyLimitOfRoutes[j].Limit)
				}
				isFind = true
				break
			}
			if !isFind {
				detail.Data = append(detail.Data, -math.MaxInt32)
			}
		}
	}
}

func convertSoftCriteriaDetailParcel(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool, name string, parcelType string) {
	var ok, okZoneRoute, notHit, notHitZoneRoute bool
	switch parcelType {
	case "COD":
		ok = softCriteriaMap[rule.MaskStepMaxCodCapacityCountryName]
		okZoneRoute = softCriteriaMap[rule.MaskStepMaxCodCapacityZoneRouteName]
		notHit = name != rule.MaskStepMaxCodCapacityCountryName
		notHitZoneRoute = name != rule.MaskStepMaxCodCapacityZoneRouteName
	case "Bulky":
		ok = softCriteriaMap[rule.MaskStepMaxBulkyCapacityCountryName]
		okZoneRoute = softCriteriaMap[rule.MaskStepMaxBulkyCapacityZoneRouteName]
		notHit = name != rule.MaskStepMaxBulkyCapacityCountryName
		notHitZoneRoute = name != rule.MaskStepMaxBulkyCapacityZoneRouteName
	case "HighValue":
		ok = softCriteriaMap[rule.MaskStepMaxHighValueCapacityCountryName]
		okZoneRoute = softCriteriaMap[rule.MaskStepMaxHighValueCapacityZoneRouteName]
		notHit = name != rule.MaskStepMaxHighValueCapacityCountryName
		notHitZoneRoute = name != rule.MaskStepMaxHighValueCapacityZoneRouteName
	case "DG":
		ok = softCriteriaMap[rule.MaskStepMaxDgCapacityCountryName]
		okZoneRoute = softCriteriaMap[rule.MaskStepMaxDgCapacityZoneRouteName]
		notHit = name != rule.MaskStepMaxDgCapacityCountryName
		notHitZoneRoute = name != rule.MaskStepMaxDgCapacityZoneRouteName
	}
	if !ok && !okZoneRoute { // 兼容新旧因子
		return
	}

	if ok {
		if d.MaxDailyLimitOfCountry == -1 || notHit {
			detail.Data = append(detail.Data, -math.MaxInt32)
		} else {
			detail.Data = append(detail.Data, d.MaxDailyLimitOfCountry)
		}
		if d.SystemVolumeOfProduct == -1 || notHit {
			detail.Data = append(detail.Data, -math.MaxInt32)
		} else {
			detail.Data = append(detail.Data, d.SystemVolumeOfProduct)
		}
	}

	if !okZoneRoute {
		return
	}
	for i := 0; i < len(zoneOrg); i++ {
		if notHitZoneRoute || len(d.MaxDailyLimitOfZoneOrigin) != len(d.SystemVolumeOfZoneOrigin) {
			detail.Data = append(detail.Data, -math.MaxInt32, -math.MaxInt32)
			continue
		}
		var limit float64 = -math.MaxInt32  //设置初始值
		var volume float64 = -math.MaxInt32 //设置初始值
		for j := range d.MaxDailyLimitOfZoneOrigin {
			//如果zone code匹配上，且对应的运力max limit为有效值，装填该值
			if d.MaxDailyLimitOfZoneOrigin[j].ZoneCode == zoneOrg[i] && d.MaxDailyLimitOfZoneOrigin[j].Limit != -1 {
				limit = d.MaxDailyLimitOfZoneOrigin[j].Limit
				volume = d.SystemVolumeOfZoneOrigin[j].Limit
			}
		}
		detail.Data = append(detail.Data, limit, volume)
	}

	for i := 0; i < len(zoneDst); i++ {
		if notHitZoneRoute || len(d.MaxDailyLimitOfZoneDestination) != len(d.SystemVolumeOfZoneDestination) {
			detail.Data = append(detail.Data, -math.MaxInt32, -math.MaxInt32)
			continue
		}
		var limit float64 = -math.MaxInt32 //设置初始值
		var volume float64 = -math.MaxInt32
		for j := range d.MaxDailyLimitOfZoneDestination {
			//如果zone code匹配上，且对应的运力max limit为有效值，装填该值
			if d.MaxDailyLimitOfZoneDestination[j].ZoneCode == zoneDst[i] && d.MaxDailyLimitOfZoneDestination[j].Limit != -1 {
				limit = d.MaxDailyLimitOfZoneDestination[j].Limit
				volume = d.SystemVolumeOfZoneDestination[j].Limit
			}
		}
		detail.Data = append(detail.Data, limit, volume)
	}

	for i := 0; i < len(routes); i++ {
		isFind := false
		for j := range d.MaxDailyLimitOfRoutes {
			if d.MaxDailyLimitOfRoutes[j].RouteCode != routes[i] {
				continue
			}
			if d.MaxDailyLimitOfRoutes[j].Limit == -1 || notHitZoneRoute || len(d.MaxDailyLimitOfRoutes) != len(d.SystemVolumeOfRoutes) {
				detail.Data = append(detail.Data, -math.MaxInt32, -math.MaxInt32)
			} else {
				detail.Data = append(detail.Data, d.MaxDailyLimitOfRoutes[j].Limit, d.SystemVolumeOfRoutes[j].Limit)
			}
			isFind = true
			break
		}
		if !isFind {
			detail.Data = append(detail.Data, -math.MaxInt32, -math.MaxInt32)
		}
	}
}

func convertSoftCriteriaDetailMin(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool) {
	okOld := softCriteriaMap[rule.MaskStepMinVolumeName]
	okNew := softCriteriaMap[rule.MaskStepMinVolumeCountryName]
	zone := softCriteriaMap[rule.MaskStepMinVolumeZoneRouteName]
	if !okOld && !okNew && !zone { // 兼容新旧因子
		return
	}

	if okOld || okNew {
		if d.MinDailyLimitOfCountry == -1 {
			detail.Data = append(detail.Data, -math.MaxInt32)
		} else {
			detail.Data = append(detail.Data, d.MinDailyLimitOfCountry)
		}
	}

	if okOld || zone {
		for i := 0; i < len(zoneOrg); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.MinDailyLimitOfZoneOrigin {
				//如果zone code匹配上，且对应的运力min limit为有效值，装填该值
				if d.MinDailyLimitOfZoneOrigin[j].ZoneCode == zoneOrg[i] && d.MinDailyLimitOfZoneOrigin[j].Limit != -1 {
					limit = d.MinDailyLimitOfZoneOrigin[j].Limit
				}
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(zoneDst); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.MinDailyLimitOfZoneDestination {
				//如果zone code匹配上，且对应的运力min limit为有效值，装填该值
				if d.MinDailyLimitOfZoneDestination[j].ZoneCode == zoneDst[i] && d.MinDailyLimitOfZoneDestination[j].Limit != -1 {
					limit = d.MinDailyLimitOfZoneDestination[j].Limit
				}
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(routes); i++ {
			isFind := false
			for j := range d.MinDailyLimitOfRoutes {
				if d.MinDailyLimitOfRoutes[j].RouteCode != routes[i] {
					continue
				}
				if d.MinDailyLimitOfRoutes[j].Limit == -1 {
					detail.Data = append(detail.Data, -math.MaxInt32)
				} else {
					detail.Data = append(detail.Data, d.MinDailyLimitOfRoutes[j].Limit)
				}
				isFind = true
				break
			}
			if !isFind {
				detail.Data = append(detail.Data, -math.MaxInt32)
			}
		}
	}
}

func convertSoftCriteriaDetailFee(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail, softCriteriaMap map[string]bool) {
	if _, ok := softCriteriaMap[rule.MaskStepCheapestFeeName]; !ok {
		return
	}
	if d.ShippingFee == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else {
		detail.Data = append(detail.Data, d.ShippingFee)
	}
}

func convertSoftCriteriaDetailBatch(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail, softCriteriaMap map[string]bool) {
	if _, ok := softCriteriaMap[rule.MaskStepMaxBatchVolumeName]; !ok {
		return
	}
	if d.BatchSize == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else {
		detail.Data = append(detail.Data, d.BatchSize)
	}
	if d.MaxVolumeInBatch == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else {
		detail.Data = append(detail.Data, d.MaxVolumeInBatch)
	}
	if d.SystemVolumeInBatch == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else {
		detail.Data = append(detail.Data, d.SystemVolumeInBatch)
	}
}

func convertSoftCriteriaDetailDefault(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail, softCriteriaMap map[string]bool) {
	_, pok := softCriteriaMap[entity.MaskDefaultProductPriority]
	_, wok := softCriteriaMap[entity.MaskDefaultProductWeightage]
	if !pok && !wok {
		return
	}
	if d.ProductPriority == -1 && d.ProductWeightage == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else if d.ProductPriority == -1 {
		detail.Data = append(detail.Data, d.ProductWeightage)
	} else {
		detail.Data = append(detail.Data, d.ProductPriority)
	}
}

func convertSoftCriteriaDetailSystem(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool, name string) {
	_, maxOkOld := softCriteriaMap[rule.MaskStepMaxCapacityName]
	_, maxOk := softCriteriaMap[rule.MaskStepMaxCapacityCountryName]
	_, maxZoneRouteOk := softCriteriaMap[rule.MaskStepMaxCapacityZoneRouteName]
	_, minOkOld := softCriteriaMap[rule.MaskStepMinVolumeName]
	_, minOk := softCriteriaMap[rule.MaskStepMinVolumeCountryName]
	_, minZoneRouteOk := softCriteriaMap[rule.MaskStepMinVolumeZoneRouteName]
	if !maxOk && !minOk && !maxOkOld && !minOkOld && !maxZoneRouteOk && !minZoneRouteOk {
		return
	}

	if maxOk || minOk || maxOkOld || minOkOld {
		if d.SystemVolumeOfProduct == -1 || (name != rule.MaskStepMaxCapacityCountryName && name != rule.MaskStepMinVolumeCountryName &&
			name != rule.MaskStepMaxCapacityName && name != rule.MaskStepMinVolumeName) {
			detail.Data = append(detail.Data, -math.MaxInt32)
		} else {
			detail.Data = append(detail.Data, d.SystemVolumeOfProduct)
		}
	}

	if minZoneRouteOk || maxZoneRouteOk {
		for i := 0; i < len(zoneOrg); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.SystemVolumeOfZoneOrigin {
				//如果zone code匹配上，且对应的运力system volume为有效值，装填该值
				if d.SystemVolumeOfZoneOrigin[j].ZoneCode == zoneOrg[i] && d.SystemVolumeOfZoneOrigin[j].Limit != -1 {
					limit = d.SystemVolumeOfZoneOrigin[j].Limit
				}
			}
			if name != rule.MaskStepMinVolumeZoneRouteName && name != rule.MaskStepMaxCapacityZoneRouteName {
				limit = -math.MaxInt32
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(zoneDst); i++ {
			var limit float64 = -math.MaxInt32 //设置初始值
			for j := range d.SystemVolumeOfZoneDestination {
				//如果zone code匹配上，且对应的运力system volume为有效值，装填该值
				if d.SystemVolumeOfZoneDestination[j].ZoneCode == zoneDst[i] && d.SystemVolumeOfZoneDestination[j].Limit != -1 {
					limit = d.SystemVolumeOfZoneDestination[j].Limit
				}
			}
			if name != rule.MaskStepMinVolumeZoneRouteName && name != rule.MaskStepMaxCapacityZoneRouteName {
				limit = -math.MaxInt32
			}
			detail.Data = append(detail.Data, limit)
		}

		for i := 0; i < len(routes); i++ {
			isFind := false
			for j := range d.SystemVolumeOfRoutes {
				if d.SystemVolumeOfRoutes[j].RouteCode != routes[i] {
					continue
				}
				if d.SystemVolumeOfRoutes[j].Limit == -1 || (name != rule.MaskStepMinVolumeZoneRouteName && name != rule.MaskStepMaxCapacityZoneRouteName) {
					detail.Data = append(detail.Data, -math.MaxInt32)
				} else {
					detail.Data = append(detail.Data, d.SystemVolumeOfRoutes[j].Limit)
				}
				isFind = true
				break
			}
			if !isFind {
				detail.Data = append(detail.Data, -math.MaxInt32)
			}
		}
	}
}

func ConvertSoftCriteriaDetailToData(productMap map[int64]string, details map[int64]*SoftCriteriaDetail,
	zoneOrg, zoneDst []string, routes []string, softCriteriaMap map[string]bool, name string) []*SoftCriteriaItemDetail {
	resp := make([]*SoftCriteriaItemDetail, 0, len(details))
	for _, d := range details {
		detail := &SoftCriteriaItemDetail{
			Input:  FormatProductAndName(productMap, d.Input),
			Output: FormatProductAndName(productMap, d.Output),
			Data:   make([]float64, 0),
		}
		convertSoftCriteriaDetailMax(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name)
		convertSoftCriteriaDetailMin(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap)
		convertSoftCriteriaDetailSystem(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name)
		convertSoftCriteriaDetailParcel(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name, "COD")
		convertSoftCriteriaDetailParcel(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name, "Bulky")
		convertSoftCriteriaDetailParcel(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name, "HighValue")
		convertSoftCriteriaDetailParcel(d, detail, zoneOrg, zoneDst, routes, softCriteriaMap, name, "DG")
		convertSoftCriteriaDetailFee(d, detail, softCriteriaMap)
		convertSoftCriteriaDetailBatch(d, detail, softCriteriaMap)
		convertSoftCriteriaDetailDefault(d, detail, softCriteriaMap)
		convertSoftCriteriaDetailWhitelistPriority(d, detail, softCriteriaMap)
		resp = append(resp, detail)
	}
	return resp
}

// 根据product id匹配seller display name
func FormatMultiProductV2(productMap map[int64]string, products []int64) []string {
	resp := make([]string, len(products))
	for i := range products {
		id := products[i]
		resp[i] = FormatProductAndName(productMap, id)
	}
	return resp
}

func FormatProductToggle(productMap map[int64]string, hardCriteriaList map[int64][]ProductToggle) map[int64][]string {
	resp := make(map[int64][]string)
	for shopId, productToggles := range hardCriteriaList {
		if _, ok := resp[shopId]; !ok {
			resp[shopId] = make([]string, len(productToggles))
		}
		for i := range productToggles {
			name := FormatProductAndName(productMap, productToggles[i].ProductId)
			resp[shopId][i] = fmt.Sprintf("%s: %s", name, productToggles[i].Available.String())
		}
	}
	return resp
}

func FormatProductAndName(productMap map[int64]string, id int64) string {
	if id == 0 || id == -1 {
		return ""
	}
	if val, ok := productMap[id]; ok {
		return fmt.Sprintf("%d-%s", id, val)
	}
	return fmt.Sprintf("%d", id)
}

func getDataSource(requestId string) DataSource {
	if strings.Contains(requestId, DataSourceKey) {
		return MaskingChannel
	}
	return Allocation
}

func GetSnapshot(ctx context.Context, dataSource DataSource, requestData, uniqueId string) (*schema.Snapshot, *srerr.Error) {
	switch dataSource {
	case Allocation:
		data := schema.AllocateRequest{}
		if requestData == "" {
			return nil, srerr.New(srerr.JsonErr, nil, "request data is empty")
		}
		if err := jsoniter.Unmarshal([]byte(requestData), &data); err != nil {
			logger.CtxLogErrorf(ctx, "GenRequestData| Failed, error=%s, data_source=%d", err.Error(), dataSource)
			return nil, srerr.With(srerr.JsonErr, nil, err)
		}
		return data.Snapshot, nil
	case MaskingChannel:
		data := schema.MChannelEstimateReq{}
		if requestData == "" {
			return nil, srerr.New(srerr.JsonErr, nil, "request data is empty")
		}
		if err := jsoniter.Unmarshal([]byte(requestData), &data); err != nil {
			logger.CtxLogErrorf(ctx, "GenRequestData| Failed, error=%s, data_source=%d", err.Error(), dataSource)
			return nil, srerr.With(srerr.JsonErr, nil, err)
		}
		for i := range data.EstimateList {
			if data.EstimateList[i].UniqueId == uniqueId {
				return data.EstimateList[i].SnapShot, nil
			}
		}
	default:
		return nil, srerr.With(srerr.LpsError, nil, fmt.Errorf("unknown data source"))
	}

	return nil, srerr.With(srerr.LpsError, nil, fmt.Errorf("unknown snapshot"))
}

func ConvertListForWeb(dataResp *ap.ListDataInfoV2) *ListDataInfoForWeb {
	if dataResp != nil {
		resp := &ListDataInfoForWeb{
			PageSize: dataResp.PageSize,
			PageNo:   dataResp.PageNo,
			Total:    dataResp.Total,
			List:     make([]BasicInfoForWeb, len(dataResp.List)),
		}
		for i := range dataResp.List {
			if dataResp.List[i].RequestTime > maxSecond {
				resp.List[i].RequestTime = dataResp.List[i].RequestTime / milToSecond
			}
			resp.List[i].Status = true
			resp.List[i].OrderId = ConvertUint64ToString(dataResp.List[i].OrderId)
			resp.List[i].FOrderId = dataResp.List[i].FOrderId
			resp.List[i].FulfillmentProductId = dataResp.List[i].FulfillmentProductId
			resp.List[i].MaskProductId = dataResp.List[i].MaskProductId
			resp.List[i].RequestId = dataResp.List[i].RequestId
			resp.List[i].UniqueId = dataResp.List[i].UniqueId
			resp.List[i].RowKey = dataResp.List[i].RowKey
		}
		return resp
	}
	return nil
}

func ConvertUint64ToString(id uint64) string {
	if id == 0 {
		return ""
	}
	return strconv.FormatUint(id, 10)
}

func getZoneCodes(code string) []string {
	result := make([]string, 0)
	if code == "" {
		return result
	}
	if !strings.Contains(code, "||") {
		result = append(result, code)

	} else {
		codes := strings.Split(code, "||")
		result = append(result, codes...)
	}

	return result
}

func convertNewSoftCriteriaList(orgZone, destZone string, oldList []OldSoftCriteria) []SoftCriteria {
	if len(oldList) == 0 {
		return nil
	}
	result := make([]SoftCriteria, len(oldList))
	for i := range oldList {
		oldCriteria := oldList[i]
		newCriteria := SoftCriteria{
			Id:   oldCriteria.Id,
			Name: oldCriteria.Name,
		}
		detailMap := make(map[int64]*SoftCriteriaDetail, 0)
		for key, detail := range oldCriteria.Detail {
			newDetail := &SoftCriteriaDetail{
				Input:                  detail.Input,
				Output:                 detail.Output,
				MaxDailyLimitOfCountry: detail.MaxDailyLimitOfCountry,
				MinDailyLimitOfCountry: detail.MinDailyLimitOfCountry,
				ShippingFee:            detail.ShippingFee,
				SystemVolumeOfProduct:  detail.SystemVolumeOfProduct,
				MaxVolumeInBatch:       detail.MaxVolumeInBatch,
				SystemVolumeInBatch:    detail.SystemVolumeInBatch,
				BatchSize:              detail.BatchSize,
				ProductWeightage:       detail.ProductWeightage,
				ProductStatus:          detail.ProductStatus,
				ProductPriority:        detail.ProductPriority,
				MaxDailyLimitOfRoutes:  detail.MaxDailyLimitOfRoutes,
				MinDailyLimitOfRoutes:  detail.MinDailyLimitOfRoutes,
				SystemVolumeOfRoutes:   detail.SystemVolumeOfRoutes,
				WhitelistPriority:      detail.WhitelistPriority,
				MaxDailyLimitOfZoneDestination: []ZoneLimit{
					{
						Limit:    detail.MaxDailyLimitOfZoneDestination,
						ZoneCode: destZone,
					},
				},
				MaxDailyLimitOfZoneOrigin: []ZoneLimit{
					{
						Limit:    detail.MaxDailyLimitOfZoneOrigin,
						ZoneCode: orgZone,
					},
				},
				MinDailyLimitOfZoneDestination: []ZoneLimit{
					{
						Limit:    detail.MinDailyLimitOfZoneDestination,
						ZoneCode: destZone,
					},
				},
				MinDailyLimitOfZoneOrigin: []ZoneLimit{
					{
						Limit:    detail.MinDailyLimitOfZoneOrigin,
						ZoneCode: orgZone,
					},
				},
				SystemVolumeOfZoneDestination: []ZoneLimit{
					{
						Limit:    detail.SystemVolumeOfZoneDestination,
						ZoneCode: destZone,
					},
				},
				SystemVolumeOfZoneOrigin: []ZoneLimit{
					{
						Limit:    detail.SystemVolumeOfZoneOrigin,
						ZoneCode: orgZone,
					},
				},
			}
			detailMap[key] = newDetail
		}
		newCriteria.Detail = detailMap
		result[i] = newCriteria
	}
	return result
}

func convertSoftCriteriaDetailWhitelistPriority(d *SoftCriteriaDetail, detail *SoftCriteriaItemDetail,
	softCriteriaMap map[string]bool) {

	ok := softCriteriaMap[rule.MaskStepPickupEfficiencyWhitelistName]
	if !ok {
		return
	}

	if d.WhitelistPriority == -1 {
		detail.Data = append(detail.Data, -math.MaxInt32)
	} else {
		detail.Data = append(detail.Data, d.WhitelistPriority)
	}
}
