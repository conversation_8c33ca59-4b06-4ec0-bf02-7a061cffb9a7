package schedule_visual

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

// DefaultReserveDataTime 默认保留10天数据
const defaultReserveDataTime = 10

type ScheduleVisualTaskServiceInterface interface {
	SyncScheduleCount(ctx context.Context, statTimeList []string) *srerr.Error
	ClearScheduleCount(ctx context.Context) *srerr.Error
}

type ScheduleVisualTaskService struct {
	ScheduleVisualRepo repository.ScheduleVisualRepoInterface
	RedisClient        *redis.Client
}

func NewScheduleVisualTaskService(scheduleVisualRepo repository.ScheduleVisualRepoInterface, redisClient *redis.Client) *ScheduleVisualTaskService {
	return &ScheduleVisualTaskService{
		ScheduleVisualRepo: scheduleVisualRepo,
		RedisClient:        redisClient,
	}
}

func (s *ScheduleVisualTaskService) SyncScheduleCount(ctx context.Context, statTimeList []string) *srerr.Error {
	//1. 获取需要同步的key
	keyList := make(map[string]bool)
	for _, statTime := range statTimeList {
		field, err := s.RedisClient.HGetAll(ctx, fmt.Sprintf(schedule_stat.ScheduleDimensionKeyPattern, statTime)).Result()
		if err != nil {
			return srerr.With(srerr.RedisErr, nil, err)
		}
		for key := range field {
			// key有可能重复，会导致同步的数据重复，这里做个去重
			keyList[key] = true
		}
	}
	//2. 查询统计的value
	var scheduleVisualTabList []*repository.ScheduleVisualTab
	for key := range keyList {
		// 统计current result
		tempList, err := s.covertToScheduleVisualTab(ctx, key, schedule_stat.CurrentResult)
		if err != nil {
			return err
		}
		scheduleVisualTabList = append(scheduleVisualTabList, tempList...)
		// 统计final result
		tempList, err = s.covertToScheduleVisualTab(ctx, key, schedule_stat.FinalResult)
		if err != nil {
			return err
		}
		scheduleVisualTabList = append(scheduleVisualTabList, tempList...)
	}
	//3. 保存统计数据
	oldScheduleVisualTabMap := make(map[string]*repository.ScheduleVisualTab)
	filterKeyMap := make(map[string]bool)
	for _, scheduleVisualTab := range scheduleVisualTabList {
		filterKey := scheduleVisualTab.BusinessID
		// filterKey存在表示已已经查询
		if _, ok := filterKeyMap[filterKey]; ok {
			continue
		}
		filterKeyMap[filterKey] = true
		condition := map[string]interface{}{
			"business_id = ?": scheduleVisualTab.BusinessID,
		}
		// 数据量大概几百条，最多几千条，不会对内存造成很大压力
		oldScheduleVisualTabList, err := s.ScheduleVisualRepo.GetScheduleVisualTabByParam(ctx, condition)
		if err != nil {
			return srerr.With(srerr.DatabaseErr, nil, err)
		}
		for _, oldScheduleVisualTab := range oldScheduleVisualTabList {
			scheduleVisualKey := fmt.Sprintf("%v--%v--%v--%v--%v--%v--%v--%v", oldScheduleVisualTab.ScheduleDimension, oldScheduleVisualTab.BusinessType, oldScheduleVisualTab.BusinessID, oldScheduleVisualTab.SupScheduleFactor, oldScheduleVisualTab.ScheduleFactor, oldScheduleVisualTab.ScheduleResult, oldScheduleVisualTab.ResultType, oldScheduleVisualTab.ScheduleChain)
			oldScheduleVisualTabMap[scheduleVisualKey] = oldScheduleVisualTab
		}
	}
	var deleteIdList []uint64
	for _, scheduleVisualTab := range scheduleVisualTabList {
		//1. 查询数据是否存在
		scheduleVisualKey := fmt.Sprintf("%v--%v--%v--%v--%v--%v--%v--%v", scheduleVisualTab.ScheduleDimension, scheduleVisualTab.BusinessType, scheduleVisualTab.BusinessID, scheduleVisualTab.SupScheduleFactor, scheduleVisualTab.ScheduleFactor, scheduleVisualTab.ScheduleResult, scheduleVisualTab.ResultType, scheduleVisualTab.ScheduleChain)
		if _, ok := oldScheduleVisualTabMap[scheduleVisualKey]; ok {
			deleteIdList = append(deleteIdList, oldScheduleVisualTabMap[scheduleVisualKey].ID)
		}
	}
	err := s.ScheduleVisualRepo.SaveScheduleVisualStat(ctx, scheduleVisualTabList, deleteIdList)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (s *ScheduleVisualTaskService) ClearScheduleCount(ctx context.Context) *srerr.Error {
	// 获取配置的清理时间（清理该时间之前的数据）
	clearTime := configutil.GetScheduleVisualClearTime(ctx)
	// 加个限制，最少保留10天内的数据
	if clearTime < defaultReserveDataTime {
		logger.CtxLogInfof(ctx, "ClearScheduleCount|clearTime less than default reserve data time, clearTime: %v, reserveTime: %v", clearTime, defaultReserveDataTime)
		return srerr.New(srerr.ClearScheduleCountError, nil, "clearTime less than default reserve data time")
	}
	// 获取60天前的时间
	nowTime := timeutil.GetLocalTime(ctx).Unix()
	deadlineTime := nowTime - (int64(clearTime) * 24 * 3600)
	err := s.ScheduleVisualRepo.ClearScheduleVisualDataByTime(ctx, deadlineTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearScheduleCount|clear data error, db error: %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (s *ScheduleVisualTaskService) covertToScheduleVisualTab(ctx context.Context, key string, resultType schedule_stat.ResultType) ([]*repository.ScheduleVisualTab, *srerr.Error) {
	var scheduleVisualTabList []*repository.ScheduleVisualTab
	fieldList, err := s.RedisClient.HGetAll(ctx, key+schedule_stat.ScheduleVisualSeparator+resultType.String()).Result()
	if err != nil {
		return scheduleVisualTabList, srerr.With(srerr.RedisErr, nil, err)
	}
	for field, value := range fieldList {
		keyValue := strings.Split(key, schedule_stat.ScheduleVisualSeparator)
		fieldValue := strings.Split(field, schedule_stat.ScheduleVisualSeparator)
		count, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			return scheduleVisualTabList, srerr.With(srerr.ParamErr, nil, err)
		}
		if len(keyValue) < (schedule_stat.KeyLength-1) || len(fieldValue) < schedule_stat.FieldLength {
			logger.CtxLogErrorf(ctx, "covertToScheduleVisualTab error|array out of bounds")
			return scheduleVisualTabList, srerr.With(srerr.ParamErr, nil, nil)
		}
		businessType, _ := strconv.ParseInt(keyValue[0], 10, 64)
		currentScheduleVisualTab := repository.ScheduleVisualTab{
			ScheduleDimension: keyValue[2],
			BusinessType:      uint(businessType),
			BusinessID:        keyValue[1],
			SupScheduleFactor: fieldValue[1],
			ScheduleFactor:    fieldValue[2],
			ScheduleResult:    fieldValue[3],
			StatNum:           count,
			ResultType:        uint(resultType),
			Ctime:             uint(timeutil.GetLocalTime(ctx).Unix()),
			Mtime:             uint(timeutil.GetLocalTime(ctx).Unix()),
			ScheduleChain:     fieldValue[0],
		}
		scheduleVisualTabList = append(scheduleVisualTabList, &currentScheduleVisualTab)
	}
	return scheduleVisualTabList, nil
}
