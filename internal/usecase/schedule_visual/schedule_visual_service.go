package schedule_visual

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

var FlowStatHeader = []string{"Channel ID+ Name", "Totally Order (checkout)", "After Hard Rules", "After Channel Toggle", "Enter Soft Rule", "Enter Channel priority/weightage", "Totally order(finish allocation)", "After Hard Rules", "Enter Soft Rule", "Enter Channel priority/weightage"}
var ResultStatHeader = []string{"Masking channel ID+Name", "F channel id +name", "Shop group ID+ name", "Allocation Factors", "Soft Criteria Rule ID", "Criteria Factors", "Order Count"}

type ScheduleVisualServiceInterface interface {
	GetFlowStat(ctx context.Context, request *admin_protocol.GetFlowStatRequest) ([]*admin_protocol.GetFlowStatResp, *srerr.Error)
	GetResultStat(ctx context.Context, request *admin_protocol.GetResultStatRequest) ([]*admin_protocol.GetResultStatResp, *srerr.Error)
	FlowStatExport(ctx context.Context, request *admin_protocol.GetFlowStatRequest) (*admin_protocol.ScheduleVisualExport, *srerr.Error)
	ResultStatExport(ctx context.Context, request *admin_protocol.GetResultStatRequest) (*admin_protocol.ScheduleVisualExport, *srerr.Error)
}

type ScheduleVisualService struct {
	ScheduleVisualRepo repository.ScheduleVisualRepoInterface
	LpsApi             lpsclient.LpsApi
}

func NewScheduleVisualService(scheduleVisualRepo repository.ScheduleVisualRepoInterface, lpsApi lpsclient.LpsApi) *ScheduleVisualService {
	return &ScheduleVisualService{
		ScheduleVisualRepo: scheduleVisualRepo,
		LpsApi:             lpsApi,
	}
}

func (s *ScheduleVisualService) GetFlowStat(ctx context.Context, request *admin_protocol.GetFlowStatRequest) ([]*admin_protocol.GetFlowStatResp, *srerr.Error) {
	//1. 查询数据
	condition := generateFlowStatFilter(request)
	scheduleVisualTabList, err := s.ScheduleVisualRepo.GetScheduleVisualTabByParam(ctx, condition)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	//2. 处理数据，构建订单流向结果
	flowStatMap := make(map[string]*admin_protocol.FlowStat)
	scheduleDimensionResultMap := make(map[string]map[string]bool)
	for _, scheduleVisualTab := range scheduleVisualTabList {
		if _, ok := scheduleDimensionResultMap[scheduleVisualTab.ScheduleDimension]; !ok {
			tempMap := make(map[string]bool)
			scheduleDimensionResultMap[scheduleVisualTab.ScheduleDimension] = tempMap
		}
		if _, ok := scheduleDimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult]; !ok {
			scheduleDimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult] = true
		}
		if _, ok := flowStatMap[scheduleVisualTab.ScheduleDimension]; !ok {
			flowStatMap[scheduleVisualTab.ScheduleDimension] = &admin_protocol.FlowStat{ScheduleResult: scheduleVisualTab.ScheduleDimension}
		}
		if _, ok := flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult]; !ok {
			flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult] = &admin_protocol.FlowStat{ScheduleResult: scheduleVisualTab.ScheduleResult}
		}
		if scheduleVisualTab.ResultType == uint(schedule_stat.CurrentResult) {
			if scheduleVisualTab.ScheduleFactor == schedule_stat.TotalOrder {
				flowStatMap[scheduleVisualTab.ScheduleDimension].EnterTotallyOrder += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.EnterHardCriteria {
				flowStatMap[scheduleVisualTab.ScheduleDimension].PassHardCriteriaCount += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.EnterSoftCriteria {
				flowStatMap[scheduleVisualTab.ScheduleDimension].EnterSoftCriteriaCount += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.EnterDefaultCriteria {
				flowStatMap[scheduleVisualTab.ScheduleDimension].EnterDefaultCriteriaCount += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.HardFactor {
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].PassHardCriteriaCount += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.SoftFactor {
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].EnterSoftCriteriaCount += scheduleVisualTab.StatNum
			}
			if strings.Contains(scheduleVisualTab.ScheduleFactor, entity.MaskDefaultProductPriority) || strings.Contains(scheduleVisualTab.ScheduleFactor, entity.MaskDefaultProductWeightage) {
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].EnterDefaultCriteriaCount += scheduleVisualTab.StatNum
			}
		}
		if scheduleVisualTab.ResultType == uint(schedule_stat.FinalResult) {
			if scheduleVisualTab.ScheduleFactor == schedule_stat.Failed {
				flowStatMap[scheduleVisualTab.ScheduleDimension].ScheduleFailed += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor != schedule_stat.Failed {
				flowStatMap[scheduleVisualTab.ScheduleDimension].ScheduleTotallyOrder += scheduleVisualTab.StatNum
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].ScheduleTotallyOrder += scheduleVisualTab.StatNum
			}
			if scheduleVisualTab.ScheduleFactor == schedule_stat.HardFactor {
				flowStatMap[scheduleVisualTab.ScheduleDimension].ScheduleBeforeSoftCriteria += scheduleVisualTab.StatNum
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].ScheduleBeforeSoftCriteria += scheduleVisualTab.StatNum
			}
			if strings.Contains(scheduleVisualTab.SupScheduleFactor, schedule_stat.SoftCriteriaRulePrefix) {
				flowStatMap[scheduleVisualTab.ScheduleDimension].ScheduleBySoftCriteria += scheduleVisualTab.StatNum
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].ScheduleBySoftCriteria += scheduleVisualTab.StatNum
			}
			if strings.Contains(scheduleVisualTab.ScheduleFactor, entity.MaskDefaultProductPriority) || strings.Contains(scheduleVisualTab.ScheduleFactor, entity.MaskDefaultProductWeightage) {
				flowStatMap[scheduleVisualTab.ScheduleDimension].ScheduleByDefaultCriteria += scheduleVisualTab.StatNum
				flowStatMap[scheduleVisualTab.ScheduleDimension+scheduleVisualTab.ScheduleResult].ScheduleByDefaultCriteria += scheduleVisualTab.StatNum
			}
		}
	}
	//3. 构建返回数据
	var getFlowStatRespList []*admin_protocol.GetFlowStatResp
	for scheduleDimension, scheduleDimensionResult := range scheduleDimensionResultMap {
		var subs []*admin_protocol.FlowStat
		for scheduleResult := range scheduleDimensionResult {
			// 去除schedule_dimension本身的影响
			if scheduleResult == scheduleDimension {
				continue
			}
			subs = append(subs, flowStatMap[scheduleDimension+scheduleResult])
		}
		dimensionFlowStat := flowStatMap[scheduleDimension]
		getFlowStatResp := admin_protocol.GetFlowStatResp{
			ScheduleResult:             dimensionFlowStat.ScheduleResult,
			EnterTotallyOrder:          dimensionFlowStat.EnterTotallyOrder,
			PassHardCriteriaCount:      dimensionFlowStat.PassHardCriteriaCount,
			PassToggleCheckCount:       dimensionFlowStat.PassToggleCheckCount,
			EnterSoftCriteriaCount:     dimensionFlowStat.EnterSoftCriteriaCount,
			EnterDefaultCriteriaCount:  dimensionFlowStat.EnterDefaultCriteriaCount,
			ScheduleTotallyOrder:       dimensionFlowStat.ScheduleTotallyOrder,
			ScheduleBeforeSoftCriteria: dimensionFlowStat.ScheduleBeforeSoftCriteria,
			ScheduleBySoftCriteria:     dimensionFlowStat.ScheduleBySoftCriteria,
			ScheduleByDefaultCriteria:  dimensionFlowStat.ScheduleByDefaultCriteria,
			ScheduleFailed:             dimensionFlowStat.ScheduleFailed,
			Subs:                       subs,
		}
		getFlowStatRespList = append(getFlowStatRespList, &getFlowStatResp)
	}

	//4. 赋值额外信息
	if request.BusinessType == schedule_stat.Allocate || request.BusinessType == schedule_stat.AllocateForecast {
		scheduleResultIdNameMap := make(map[string]string)
		productIdNameMap, err := s.LpsApi.GetAllProductIdNameList(ctx)
		if err != nil {
			return nil, err
		}
		for id, name := range productIdNameMap {
			scheduleResultIdNameMap[strconv.FormatInt(id, 10)] = name
		}
		for _, getFlowStatResp := range getFlowStatRespList {
			getFlowStatResp.ScheduleResult = fmt.Sprintf("%s-%s", getFlowStatResp.ScheduleResult, scheduleResultIdNameMap[getFlowStatResp.ScheduleResult])
			for _, getFlowStat := range getFlowStatResp.Subs {
				getFlowStat.ScheduleResult = fmt.Sprintf("%s-%s", getFlowStat.ScheduleResult, scheduleResultIdNameMap[getFlowStat.ScheduleResult])
			}
		}
	}

	return getFlowStatRespList, nil
}

func (s *ScheduleVisualService) GetResultStat(ctx context.Context, request *admin_protocol.GetResultStatRequest) ([]*admin_protocol.GetResultStatResp, *srerr.Error) {
	//1. 查询数据
	condition := generateResultStatFilter(request)
	scheduleVisualTabList, err := s.ScheduleVisualRepo.GetScheduleVisualTabByParam(ctx, condition)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	//2. 处理数据，构架订单结果数据
	groupMap := make(map[string]*repository.ScheduleVisualTab)
	for _, scheduleVisualTab := range scheduleVisualTabList {
		key := fmt.Sprintf("%v%v%v%v%v%v%v", scheduleVisualTab.ScheduleChain, scheduleVisualTab.ScheduleDimension, scheduleVisualTab.BusinessType, scheduleVisualTab.SupScheduleFactor, scheduleVisualTab.ScheduleFactor, scheduleVisualTab.ScheduleResult, scheduleVisualTab.ResultType)
		if _, ok := groupMap[key]; ok {
			groupMap[key].StatNum += scheduleVisualTab.StatNum
		} else {
			groupMap[key] = scheduleVisualTab
		}
	}
	dimensionResultMap := make(map[string]map[string]map[string][]*repository.ScheduleVisualTab)
	for _, scheduleVisualTab := range groupMap {
		if _, ok := dimensionResultMap[scheduleVisualTab.ScheduleDimension]; !ok {
			tempMap := make(map[string]map[string][]*repository.ScheduleVisualTab)
			dimensionResultMap[scheduleVisualTab.ScheduleDimension] = tempMap
		}
		if _, ok := dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult]; !ok {
			tempMap := make(map[string][]*repository.ScheduleVisualTab)
			dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult] = tempMap
		}
		if _, ok := dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult][scheduleVisualTab.ScheduleChain]; !ok {
			var tempList []*repository.ScheduleVisualTab
			dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult][scheduleVisualTab.ScheduleChain] = tempList
		}
		dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult][scheduleVisualTab.ScheduleChain] = append(dimensionResultMap[scheduleVisualTab.ScheduleDimension][scheduleVisualTab.ScheduleResult][scheduleVisualTab.ScheduleChain], scheduleVisualTab)
	}
	//3. 构建返回结果
	var getResultStatRespList []*admin_protocol.GetResultStatResp
	for scheduleDimension, resultMap := range dimensionResultMap {
		var scheduleDimensionCount int64
		var scheduleResultRespList []*admin_protocol.ScheduleResultResp
		for scheduleResult, chainMap := range resultMap {
			var scheduleResultCount int64
			var scheduleFactorStatResp []*admin_protocol.ScheduleFactorStatResp
			for _, scheduleCountList := range chainMap {
				factorStatResp, factorCount := convertScheduleListToTree(scheduleCountList)
				scheduleResultCount += factorCount
				scheduleFactorStatResp = append(scheduleFactorStatResp, factorStatResp...)
			}
			scheduleResultResp := admin_protocol.ScheduleResultResp{
				ScheduleResult:     scheduleResult,
				Count:              scheduleResultCount,
				ScheduleFactorStat: scheduleFactorStatResp,
			}
			scheduleResultRespList = append(scheduleResultRespList, &scheduleResultResp)
			scheduleDimensionCount += scheduleResultCount
		}
		getResultStatResp := admin_protocol.GetResultStatResp{
			ScheduleDimension: scheduleDimension,
			Count:             scheduleDimensionCount,
			Subs:              scheduleResultRespList,
		}
		getResultStatRespList = append(getResultStatRespList, &getResultStatResp)
	}

	//4. 赋值额外信息
	if request.BusinessType == schedule_stat.Allocate || request.BusinessType == schedule_stat.AllocateForecast {
		// 获取product信息
		scheduleResultIdNameMap := make(map[string]string)
		productIdNameMap, err := s.LpsApi.GetAllProductIdNameList(ctx)
		if err != nil {
			return nil, err
		}
		for id, name := range productIdNameMap {
			scheduleResultIdNameMap[strconv.FormatInt(id, 10)] = name
		}
		// 获取shop group信息
		shopGroupIdNameMap := make(map[string]string)
		shopGroupList, err := s.LpsApi.GetShopGroupList(ctx)
		if err != nil {
			return nil, err
		}
		for _, shopGroup := range shopGroupList {
			shopGroupIdNameMap[strconv.FormatInt(shopGroup.GroupId, 10)] = shopGroup.GroupName
		}
		// 赋值
		for _, getResultStatResp := range getResultStatRespList {
			getResultStatResp.ScheduleDimension = fmt.Sprintf("%s %s", getResultStatResp.ScheduleDimension, scheduleResultIdNameMap[getResultStatResp.ScheduleDimension])
			for _, getResultStat := range getResultStatResp.Subs {
				getResultStat.ScheduleResult = fmt.Sprintf("%s-%s", getResultStat.ScheduleResult, scheduleResultIdNameMap[getResultStat.ScheduleResult])
				convertShopGroup(getResultStat.ScheduleFactorStat, shopGroupIdNameMap)
			}
		}
	}

	return getResultStatRespList, nil
}

func (s *ScheduleVisualService) FlowStatExport(ctx context.Context, request *admin_protocol.GetFlowStatRequest) (*admin_protocol.ScheduleVisualExport, *srerr.Error) {
	// 1.获取数据
	flowStatResp, err := s.GetFlowStat(ctx, request)
	if err != nil {
		return nil, err
	}
	// 2. 生成数据
	var data [][]string
	for _, flowStat := range flowStatResp {
		var subData []string
		subData = append(subData, flowStat.ScheduleResult)
		subData = append(subData, strconv.FormatInt(flowStat.EnterTotallyOrder, 10))
		subData = append(subData, strconv.FormatInt(flowStat.PassHardCriteriaCount, 10))
		subData = append(subData, strconv.FormatInt(flowStat.PassToggleCheckCount, 10))
		subData = append(subData, strconv.FormatInt(flowStat.EnterSoftCriteriaCount, 10))
		subData = append(subData, strconv.FormatInt(flowStat.EnterDefaultCriteriaCount, 10))
		subData = append(subData, strconv.FormatInt(flowStat.ScheduleTotallyOrder, 10))
		subData = append(subData, strconv.FormatInt(flowStat.ScheduleBeforeSoftCriteria, 10))
		subData = append(subData, strconv.FormatInt(flowStat.ScheduleBySoftCriteria, 10))
		subData = append(subData, strconv.FormatInt(flowStat.ScheduleByDefaultCriteria, 10))
		data = append(data, subData)
		for _, subFlowStat := range flowStat.Subs {
			var subData1 []string
			subData1 = append(subData1, subFlowStat.ScheduleResult)
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.EnterTotallyOrder, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.PassHardCriteriaCount, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.PassToggleCheckCount, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.EnterSoftCriteriaCount, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.EnterDefaultCriteriaCount, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.ScheduleTotallyOrder, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.ScheduleBeforeSoftCriteria, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.ScheduleBySoftCriteria, 10))
			subData1 = append(subData1, strconv.FormatInt(subFlowStat.ScheduleByDefaultCriteria, 10))
			data = append(data, subData1)
		}
	}
	// 3.生成并上传excel
	url, err := s.UploadScheduleVisualFile(ctx, FlowStatHeader, data, "order flow analyze")
	if err != nil {
		return nil, err
	}
	scheduleVisualExport := admin_protocol.ScheduleVisualExport{
		Url: url,
	}
	return &scheduleVisualExport, nil
}

func (s *ScheduleVisualService) ResultStatExport(ctx context.Context, request *admin_protocol.GetResultStatRequest) (*admin_protocol.ScheduleVisualExport, *srerr.Error) {
	// 1.获取数据
	resultStatResp, err := s.GetResultStat(ctx, request)
	if err != nil {
		return nil, err
	}
	// 2. 生成数据
	var data [][]string
	for _, resultStat := range resultStatResp {
		dimensionData := make([]string, 7)
		dimensionData[0] = resultStat.ScheduleDimension
		dimensionData[6] = strconv.FormatInt(resultStat.Count, 10)
		data = append(data, dimensionData)
		for _, sub := range resultStat.Subs {
			resultData := make([]string, 7)
			resultData[1] = sub.ScheduleResult
			resultData[6] = strconv.FormatInt(sub.Count, 10)
			data = append(data, resultData)
			data = append(data, generateResultStatExportData(2, sub.ScheduleFactorStat)...)
		}
	}
	// 3.生成并上传excel
	url, err := s.UploadScheduleVisualFile(ctx, ResultStatHeader, data, "order result analyze")
	if err != nil {
		return nil, err
	}
	scheduleVisualExport := admin_protocol.ScheduleVisualExport{
		Url: url,
	}
	return &scheduleVisualExport, nil
}

func generateResultStatExportData(idx int64, scheduleFactorStatList []*admin_protocol.ScheduleFactorStatResp) [][]string {
	var data [][]string
	for _, scheduleFactorStat := range scheduleFactorStatList {
		factorData := make([]string, 7)
		factorData[idx] = scheduleFactorStat.ScheduleFactorName
		factorData[6] = strconv.FormatInt(scheduleFactorStat.Count, 10)
		data = append(data, factorData)
		data = append(data, generateResultStatExportData(idx+1, scheduleFactorStat.Subs)...)
	}
	return data
}

func (s *ScheduleVisualService) UploadScheduleVisualFile(ctx context.Context, header []string, data [][]string, sheetName string) (string, *srerr.Error) {
	// 生成excel文件
	newFile, fErr := fileutil.MakeExcel(ctx, header, data, sheetName)
	if fErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, fErr)
	}
	b, wErr := newFile.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Schedule Visual-%v%s", timeutil.FormatDate(timeutil.GetCurrentTime(ctx)), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil

}

func convertScheduleListToTree(scheduleCountList []*repository.ScheduleVisualTab) ([]*admin_protocol.ScheduleFactorStatResp, int64) {
	scheduleCountMap := make(map[string][]*repository.ScheduleVisualTab)
	for _, scheduleCount := range scheduleCountList {
		if _, ok := scheduleCountMap[scheduleCount.SupScheduleFactor]; !ok {
			var tempList []*repository.ScheduleVisualTab
			scheduleCountMap[scheduleCount.SupScheduleFactor] = tempList
		}
		scheduleCountMap[scheduleCount.SupScheduleFactor] = append(scheduleCountMap[scheduleCount.SupScheduleFactor], scheduleCount)
	}
	return generateScheduleCountTree("", scheduleCountMap)
}

func generateScheduleCountTree(supFactor string, scheduleCountMap map[string][]*repository.ScheduleVisualTab) ([]*admin_protocol.ScheduleFactorStatResp, int64) {
	if _, ok := scheduleCountMap[supFactor]; !ok {
		return nil, 0
	}
	var resultList []*admin_protocol.ScheduleFactorStatResp
	var totalCount int64
	for _, scheduleCount := range scheduleCountMap[supFactor] {
		subs, count := generateScheduleCountTree(scheduleCount.ScheduleFactor, scheduleCountMap)
		result := admin_protocol.ScheduleFactorStatResp{
			ScheduleFactorName: scheduleCount.ScheduleFactor,
			Count:              scheduleCount.StatNum + count,
			Subs:               subs,
		}
		if supFactor == "" {
			result.ScheduleFactor = "Shop Group ID-Name"
		} else if scheduleCount.ScheduleFactor == schedule_stat.HardFactor || scheduleCount.ScheduleFactor == schedule_stat.SoftFactor {
			result.ScheduleFactor = "Allocation Factors"
		} else if supFactor == schedule_stat.SoftFactor {
			result.ScheduleFactor = "Soft Criteria Rule ID"
			// rule_id去掉前缀
			if len(result.ScheduleFactorName) > 7 {
				result.ScheduleFactorName = result.ScheduleFactorName[7:]
			}
		} else {
			result.ScheduleFactor = "Criteria Factors"
		}
		totalCount += result.Count
		resultList = append(resultList, &result)
	}
	return resultList, totalCount
}

func generateFlowStatFilter(request *admin_protocol.GetFlowStatRequest) map[string]interface{} {
	condition := make(map[string]interface{})
	if request.ScheduleDimension != "" {
		condition["schedule_dimension = ?"] = request.ScheduleDimension
	}
	if request.ScheduleResult != "" {
		condition["schedule_result = ?"] = request.ScheduleResult
	}
	condition["business_type = ?"] = request.BusinessType
	if request.BusinessType == schedule_stat.Allocate {
		condition["business_id >= ?"] = request.BusinessId + "00"
		condition["business_id <= ?"] = request.BusinessId + "23"
	}
	if request.BusinessType == schedule_stat.AllocateForecast {
		condition["business_id = ?"] = request.BusinessId
	}
	return condition
}

func generateResultStatFilter(request *admin_protocol.GetResultStatRequest) map[string]interface{} {
	condition := make(map[string]interface{})
	if request.ScheduleDimension != "" {
		condition["schedule_dimension = ?"] = request.ScheduleDimension
	}
	if request.ScheduleResult != "" {
		condition["schedule_result = ?"] = request.ScheduleResult
	}
	condition["business_type = ?"] = request.BusinessType
	if request.BusinessType == schedule_stat.Allocate {
		condition["business_id >= ?"] = request.BusinessId + "00"
		condition["business_id <= ?"] = request.BusinessId + "23"
	}
	if request.BusinessType == schedule_stat.AllocateForecast {
		condition["business_id = ?"] = request.BusinessId
	}
	if request.ScheduleFactor != "" {
		condition["schedule_factor = ?"] = request.ScheduleFactor
	}
	// 只需要查final_result
	condition["result_type = ?"] = schedule_stat.FinalResult
	return condition
}

func convertShopGroup(scheduleFactorStatList []*admin_protocol.ScheduleFactorStatResp, shopGroupIdNameMap map[string]string) {
	if len(scheduleFactorStatList) < 1 {
		return
	}
	for _, scheduleFactorStat := range scheduleFactorStatList {
		if scheduleFactorStat.ScheduleFactor == "Shop Group ID-Name" {
			scheduleFactorStat.ScheduleFactorName = fmt.Sprintf("%s %s", scheduleFactorStat.ScheduleFactorName, shopGroupIdNameMap[scheduleFactorStat.ScheduleFactorName])
		}
		convertShopGroup(scheduleFactorStat.Subs, shopGroupIdNameMap)
	}
}
