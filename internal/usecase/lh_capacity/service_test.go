package lh_capacity

import (
	"strconv"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

func TestParseTimeObject(t *testing.T) {
	tests := []struct {
		name             string
		timeIntervalType entity.TimeIntervalType
		objectValue      string
		expectError      bool
		errorSubstring   string
	}{
		// Default type tests
		{
			name:             "Default type",
			timeIntervalType: entity.TimeIntervalTypeDefault,
			objectValue:      "",
			expectError:      false,
		},

		// Special Date tests
		{
			name:             "Valid special date",
			timeIntervalType: entity.TimeIntervalTypeSpecialDate,
			objectValue:      "2025-01-01",
			expectError:      false,
		},
		{
			name:             "Empty special date",
			timeIntervalType: entity.TimeIntervalTypeSpecialDate,
			objectValue:      "",
			expectError:      true,
			errorSubstring:   "special date object is empty",
		},
		{
			name:             "Invalid special date format",
			timeIntervalType: entity.TimeIntervalTypeSpecialDate,
			objectValue:      "2025/01/01",
			expectError:      true,
			errorSubstring:   "invalid special date format",
		},
		{
			name:             "Invalid calendar date",
			timeIntervalType: entity.TimeIntervalTypeSpecialDate,
			objectValue:      "2025-02-30", // February 30th doesn't exist
			expectError:      true,
			errorSubstring:   "invalid calendar date",
		},

		// Special Time Slot tests
		{
			name:             "Valid special time slot",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "10:00:00-14:00:00",
			expectError:      false,
		},
		{
			name:             "Empty special time slot",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "",
			expectError:      true,
			errorSubstring:   "special time slot object is empty",
		},
		{
			name:             "Invalid special time slot format",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "10:00:00/14:00:00",
			expectError:      true,
			errorSubstring:   "invalid special time slot format",
		},
		{
			name:             "Invalid start time format",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "10-00-00-14:00:00",
			expectError:      true,
			errorSubstring:   "invalid special time slot format",
		},
		{
			name:             "Invalid end time format",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "10:00:00-14-00-00",
			expectError:      true,
			errorSubstring:   "invalid special time slot format",
		},
		{
			name:             "End time before start time",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "14:00:00-10:00:00",
			expectError:      true,
			errorSubstring:   "invalid time slot: end time",
		},
		{
			name:             "Invalid hour in time",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "24:00:00-10:00:00",
			expectError:      true,
			errorSubstring:   "invalid start time format",
		},
		{
			name:             "Invalid minute in time",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "10:60:00-14:00:00",
			expectError:      true,
			errorSubstring:   "invalid start time format",
		},
		{
			name:             "Maximum valid time",
			timeIntervalType: entity.TimeIntervalTypeSpecialTimeSlot,
			objectValue:      "00:00:00-23:59:59",
			expectError:      false,
		},

		// Weekly Capacity tests
		{
			name:             "Valid weekly capacity",
			timeIntervalType: entity.TimeIntervalTypeWeeklyCapacity,
			objectValue:      "1,2,3,4,5",
			expectError:      false,
		},
		{
			name:             "Empty weekly capacity",
			timeIntervalType: entity.TimeIntervalTypeWeeklyCapacity,
			objectValue:      "",
			expectError:      true,
			errorSubstring:   "weekly capacity object is empty",
		},
		{
			name:             "Invalid weekday format",
			timeIntervalType: entity.TimeIntervalTypeWeeklyCapacity,
			objectValue:      "Mon,Tue,Wed",
			expectError:      true,
			errorSubstring:   "invalid week day format",
		},
		{
			name:             "Invalid weekday value",
			timeIntervalType: entity.TimeIntervalTypeWeeklyCapacity,
			objectValue:      "0,1,8", // 0 and 8 are invalid
			expectError:      true,
			errorSubstring:   "invalid week day value",
		},
		{
			name:             "Duplicate weekday",
			timeIntervalType: entity.TimeIntervalTypeWeeklyCapacity,
			objectValue:      "1,2,2,3",
			expectError:      true,
			errorSubstring:   "duplicate week day",
		},

		// Special Date and Time Slot tests
		{
			name:             "Valid special date and time slot",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-01-01 10:00:00/2025-01-01 14:00:00",
			expectError:      false,
		},
		{
			name:             "Empty special date and time slot",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "",
			expectError:      true,
			errorSubstring:   "special date and time slot object is empty",
		},
		{
			name:             "Invalid special date and time slot format",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-01-01-10:00:00/2025-01-01-14:00:00",
			expectError:      true,
			errorSubstring:   "invalid start date time",
		},
		{
			name:             "Invalid start date time",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-02-30 10:00:00/2025-01-01 14:00:00",
			expectError:      true,
			errorSubstring:   "invalid start date time",
		},
		{
			name:             "Invalid end date time",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-01-01 10:00:00/2025-01-01 24:00:00",
			expectError:      true,
			errorSubstring:   "invalid end date time",
		},
		{
			name:             "End date time before start date time",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-01-01 14:00:00/2025-01-01 10:00:00",
			expectError:      true,
			errorSubstring:   "invalid date time slot: end time",
		},
		{
			name:             "Maximum valid time in date time slot",
			timeIntervalType: entity.TimeIntervalTypeSpecialDateAndTimeSlot,
			objectValue:      "2025-01-01 00:00:00/2025-01-01 23:59:59",
			expectError:      false,
		},

		// Invalid time interval type
		{
			name:             "Invalid time interval type",
			timeIntervalType: entity.TimeIntervalType(99),
			objectValue:      "test",
			expectError:      true,
			errorSubstring:   "unsupported time interval type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parseTimeObject(tt.timeIntervalType, tt.objectValue)

			// Check if error was expected
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorSubstring != "" && !contains(err.Error(), tt.errorSubstring) {
					t.Errorf("Error message '%s' does not contain expected substring '%s'", err.Error(), tt.errorSubstring)
				}
				return
			}

			// If no error was expected, but we got one
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Verify the result based on the time interval type
			switch tt.timeIntervalType {
			case entity.TimeIntervalTypeDefault:
				if !result.IsAllTime {
					t.Errorf("Expected IsAllTime to be true for default time object")
				}
			case entity.TimeIntervalTypeSpecialDate:
				if len(result.SpecialDates) != 1 || result.SpecialDates[0] != tt.objectValue {
					t.Errorf("Expected SpecialDates to contain '%s', got %v", tt.objectValue, result.SpecialDates)
				}
			case entity.TimeIntervalTypeSpecialTimeSlot:
				parts := strings.Split(tt.objectValue, "-")
				if result.SpecialTimeSlotStartTime != parts[0] || result.SpecialTimeSlotEndTime != parts[1] {
					t.Errorf("Expected time slot to be '%s' and '%s', got '%s' and '%s'",
						parts[0], parts[1], result.SpecialTimeSlotStartTime, result.SpecialTimeSlotEndTime)
				}
			case entity.TimeIntervalTypeWeeklyCapacity:
				weekDayStrs := strings.Split(tt.objectValue, ",")
				expectedWeekDays := make([]timeutil.WeekDay, 0, len(weekDayStrs))
				for _, dayStr := range weekDayStrs {
					dayStr = strings.TrimSpace(dayStr)
					if dayStr == "" {
						continue
					}
					day, _ := strconv.Atoi(dayStr)
					expectedWeekDays = append(expectedWeekDays, timeutil.WeekDay(day))
				}
				if !weekDaysEqual(result.WeekDays, expectedWeekDays) {
					t.Errorf("Expected WeekDays to be %v, got %v", expectedWeekDays, result.WeekDays)
				}
			case entity.TimeIntervalTypeSpecialDateAndTimeSlot:
				parts := strings.Split(tt.objectValue, "/")
				startDateTimeStr := strings.TrimSpace(parts[0])
				endDateTimeStr := strings.TrimSpace(parts[1])
				if result.SpecialDateAndTimeSlotStartTime != startDateTimeStr || result.SpecialDateAndTimeSlotEndTime != endDateTimeStr {
					t.Errorf("Expected date time slot to be '%s' and '%s', got '%s' and '%s'",
						startDateTimeStr, endDateTimeStr, result.SpecialDateAndTimeSlotStartTime, result.SpecialDateAndTimeSlotEndTime)
				}
			}
		})
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// Helper function to compare two WeekDay slices
func weekDaysEqual(a, b []timeutil.WeekDay) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func TestIsValidDateFormat(t *testing.T) {
	tests := []struct {
		name     string
		dateStr  string
		expected bool
	}{
		{"Valid date", "2025-01-01", true},
		{"Invalid format with slashes", "2025/01/01", false},
		{"Invalid format with dots", "2025.01.01", false},
		{"Invalid format with missing leading zeros", "2025-1-1", false},
		{"Invalid format with extra characters", "2025-01-01X", false},
		{"Invalid format with letters", "ABCD-EF-GH", false},
		{"Empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidDateFormat(tt.dateStr)
			if result != tt.expected {
				t.Errorf("isValidDateFormat(%s) = %v, expected %v", tt.dateStr, result, tt.expected)
			}
		})
	}
}

func TestIsValidTimeFormat(t *testing.T) {
	tests := []struct {
		name     string
		timeStr  string
		expected bool
	}{
		{"Valid time", "14:30:45", true},
		{"Valid time with zeros", "00:00:00", true},
		{"Valid time at max", "23:59:59", true},
		{"Invalid hour", "24:00:00", false},
		{"Invalid minute", "14:60:00", false},
		{"Invalid second", "14:30:60", false},
		{"Invalid format with dashes", "14-30-45", false},
		{"Invalid format with missing leading zeros", "1:2:3", false},
		{"Invalid format with extra characters", "14:30:45X", false},
		{"Empty string", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidTimeFormat(tt.timeStr)
			if result != tt.expected {
				t.Errorf("isValidTimeFormat(%s) = %v, expected %v", tt.timeStr, result, tt.expected)
			}
		})
	}
}

func TestIsValidTimeSlot(t *testing.T) {
	tests := []struct {
		name      string
		startTime string
		endTime   string
		valid     bool
		hasError  bool
	}{
		{"Valid time slot", "10:00:00", "14:00:00", true, false},
		{"Same start and end time", "10:00:00", "10:00:00", false, false},
		{"End before start", "14:00:00", "10:00:00", false, false},
		{"Invalid start time format", "10-00-00", "14:00:00", false, true},
		{"Invalid end time format", "10:00:00", "14-00-00", false, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			valid, err := isValidTimeSlot(tt.startTime, tt.endTime)

			if tt.hasError && err == nil {
				t.Errorf("Expected error but got none")
			}

			if !tt.hasError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if valid != tt.valid {
				t.Errorf("isValidTimeSlot(%s, %s) = %v, expected %v",
					tt.startTime, tt.endTime, valid, tt.valid)
			}
		})
	}
}

func TestIsValidDateTimeSlot(t *testing.T) {
	tests := []struct {
		name          string
		startDateTime string
		endDateTime   string
		valid         bool
		hasError      bool
	}{
		{"Valid date time slot", "2025-01-01 10:00:00", "2025-01-01 14:00:00", true, false},
		{"Valid date time slot different days", "2025-01-01 10:00:00", "2025-01-02 10:00:00", true, false},
		{"Same start and end time", "2025-01-01 10:00:00", "2025-01-01 10:00:00", false, false},
		{"End before start", "2025-01-01 14:00:00", "2025-01-01 10:00:00", false, false},
		{"Invalid start date time format", "2025/01/01 10:00:00", "2025-01-01 14:00:00", false, true},
		{"Invalid end date time format", "2025-01-01 10:00:00", "2025/01/01 14:00:00", false, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			valid, err := isValidDateTimeSlot(tt.startDateTime, tt.endDateTime)

			if tt.hasError && err == nil {
				t.Errorf("Expected error but got none")
			}

			if !tt.hasError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if valid != tt.valid {
				t.Errorf("isValidDateTimeSlot(%s, %s) = %v, expected %v",
					tt.startDateTime, tt.endDateTime, valid, tt.valid)
			}
		})
	}
}

func TestParseWeeklyCapacityObject(t *testing.T) {
	tests := []struct {
		name           string
		weekdaysStr    string
		expectedResult entity.TimeObject
		expectError    bool
		errorSubstring string
	}{
		{
			name:        "Valid single weekday",
			weekdaysStr: "1",
			expectedResult: entity.TimeObject{
				WeekDays: []timeutil.WeekDay{timeutil.Monday},
			},
			expectError: false,
		},
		{
			name:        "Valid multiple weekdays",
			weekdaysStr: "1, 3, 5",
			expectedResult: entity.TimeObject{
				WeekDays: []timeutil.WeekDay{timeutil.Monday, timeutil.Wednesday, timeutil.Friday},
			},
			expectError: false,
		},
		{
			name:        "Valid all weekdays",
			weekdaysStr: "1, 2, 3, 4, 5, 6, 7",
			expectedResult: entity.TimeObject{
				WeekDays: []timeutil.WeekDay{
					timeutil.Monday,
					timeutil.Tuesday,
					timeutil.Wednesday,
					timeutil.Thursday,
					timeutil.Friday,
					timeutil.Saturday,
					timeutil.Sunday,
				},
			},
			expectError: false,
		},
		{
			name:        "Valid with extra spaces",
			weekdaysStr: " 2,  4,6 ",
			expectedResult: entity.TimeObject{
				WeekDays: []timeutil.WeekDay{timeutil.Tuesday, timeutil.Thursday, timeutil.Saturday},
			},
			expectError: false,
		},
		{
			name:           "Empty string",
			weekdaysStr:    "",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "weekly capacity object is empty",
		},
		{
			name:           "Invalid weekday format - non-numeric",
			weekdaysStr:    "1,2,Mon",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "invalid week day format",
		},
		{
			name:           "Invalid weekday value - out of range (negative)",
			weekdaysStr:    "1,2,-1",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "invalid week day value",
		},
		{
			name:           "Invalid weekday value - out of range (too large)",
			weekdaysStr:    "1,2,8",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "invalid week day value",
		},
		{
			name:           "Duplicate weekday",
			weekdaysStr:    "1,2,2",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "duplicate week day",
		},
		{
			name:           "Only empty values after split",
			weekdaysStr:    ",,, ",
			expectedResult: entity.TimeObject{},
			expectError:    true,
			errorSubstring: "no valid week days found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parseWeeklyCapacityObject(tt.weekdaysStr)

			// Check error expectations
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorSubstring != "" && !contains(err.Error(), tt.errorSubstring) {
					t.Errorf("Error message '%s' does not contain expected substring '%s'", err.Error(), tt.errorSubstring)
				}
				return
			}

			// If no error was expected, but we got one
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Check result
			if !weekDaysEqual(result.WeekDays, tt.expectedResult.WeekDays) {
				t.Errorf("Expected WeekDays to be %v, got %v", tt.expectedResult.WeekDays, result.WeekDays)
			}
		})
	}
}
