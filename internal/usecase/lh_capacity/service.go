package lh_capacity

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

// LHCapacityService 容量配置服务接口
type LHCapacityService interface {
	// Create 创建容量配置
	Create(ctx context.Context, req *ilh_smart_routing.CreateLHCapacityReq) (int, *srerr.Error)

	// Update 更新容量配置
	Update(ctx context.Context, req *ilh_smart_routing.UpdateLHCapacityReq) *srerr.Error

	// Get 获取容量配置
	Get(ctx context.Context, id int) (*ilh_smart_routing.GetLHCapacityResp, *srerr.Error)

	// List 列出容量配置
	List(ctx context.Context, req *ilh_smart_routing.ListLHCapacityReq) (*ilh_smart_routing.ListLHCapacityResp, *srerr.Error)

	// Delete 删除容量配置
	Delete(ctx context.Context, id int) *srerr.Error

	// UpdateStatus 更新容量配置状态
	UpdateStatus(ctx context.Context, req *ilh_smart_routing.UpdateLHCapacityStatusReq) *srerr.Error

	// Upload 通过S3文件链接批量导入容量配置
	Upload(ctx context.Context, req *ilh_smart_routing.UploadLHCapacityReq) *srerr.Error

	// Copy 复制容量配置
	Copy(ctx context.Context, req *ilh_smart_routing.CopyLHCapacityReq) (int, *srerr.Error)

	// CheckActive 检查是否存在指定条件且状态为Active的配置
	CheckActive(ctx context.Context, req *ilh_smart_routing.CheckLHCapacityReq) (*ilh_smart_routing.CheckLHCapacityResp, *srerr.Error)

	GetLHCapacitySettingInfo(ctx context.Context, ilhLineID string, dgType int, twsCode string, destinationPorts []string) (entity.ILHCapacitySettingInfo, *srerr.Error)

	// UpdatePendingLHCapacityStatusActive 更新Pending状态的LHCapacity为Active
	UpdatePendingLHCapacityStatusActive(ctx context.Context)

	// UpdateActiveLHCapacityStatusExpired 更新Active状态的LHCapacity为Expired
	UpdateActiveLHCapacityStatusExpired(ctx context.Context)
}

// LHCapacityServiceImpl 容量配置服务实现
type LHCapacityServiceImpl struct {
	lhCapacityRepo repo.LHCapacityRepo
	laneService    lane.LaneService
}

// NewLHCapacityServiceImpl 创建容量配置服务实现
func NewLHCapacityServiceImpl(
	lhCapacityRepo repo.LHCapacityRepo,
	laneService lane.LaneService,
) *LHCapacityServiceImpl {
	return &LHCapacityServiceImpl{
		lhCapacityRepo: lhCapacityRepo,
		laneService:    laneService,
	}
}

// Create 创建容量配置
func (s *LHCapacityServiceImpl) Create(ctx context.Context, req *ilh_smart_routing.CreateLHCapacityReq) (int, *srerr.Error) {
	// 先校验CapacitySettings
	if err := entity.ValidateCapacitySettings(req.CapacitySettings); err != nil {
		return 0, err
	}

	if req.ILHLineName == "" {
		lineNameMap, _ := s.laneService.GetLineIdToLineNameMap(ctx)
		req.ILHLineName = lineNameMap[req.ILHLineID]
	}

	if req.Status == rule.RuleStatusDraft {
		// 只有Active/Expired状态
		req.Status = rule.RuleStatusExpired
	}

	// 转换为实体
	config := &entity.LHCapacityConfig{
		CapacityName:     req.CapacityName,
		ILHVendorName:    req.ILHVendorName,
		ILHLineID:        req.ILHLineID,
		ILHLineName:      req.ILHLineName,
		TWS:              req.TWS,
		DestinationPort:  req.DestinationPort,
		DGType:           req.DGType,
		CapacitySettings: req.CapacitySettings,
		RuleStatus:       req.Status,
		Operator:         req.Operator,
	}
	if req.Status == rule.RuleStatusActive {
		config.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	id, err := s.lhCapacityRepo.CreateLHCapacity(ctx, config)
	if err != nil {
		return 0, srerr.With(srerr.DatabaseErr, req, err)
	}
	return id, nil
}

// Update 更新容量配置
func (s *LHCapacityServiceImpl) Update(ctx context.Context, req *ilh_smart_routing.UpdateLHCapacityReq) *srerr.Error {
	// 先校验CapacitySettings
	if err := entity.ValidateCapacitySettings(req.CapacitySettings); err != nil {
		return err
	}

	if req.ILHLineName == "" {
		lineNameMap, _ := s.laneService.GetLineIdToLineNameMap(ctx)
		req.ILHLineName = lineNameMap[req.ILHLineID]
	}

	if req.Status == rule.RuleStatusDraft {
		// 只有Active/Expired状态
		req.Status = rule.RuleStatusExpired
	}

	// 检查配置是否存在
	existConfig, err := s.lhCapacityRepo.GetLHCapacityByID(ctx, req.ID)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, req, err)
	}
	if existConfig == nil {
		return srerr.New(srerr.DataNotFound, req, "lh capacity not found with id: %d", req.ID)
	}

	config := &entity.LHCapacityConfig{
		ID:               req.ID,
		CapacityName:     req.CapacityName,
		ILHVendorName:    req.ILHVendorName,
		ILHLineID:        req.ILHLineID,
		ILHLineName:      req.ILHLineName,
		TWS:              req.TWS,
		DestinationPort:  req.DestinationPort,
		DGType:           req.DGType,
		CapacitySettings: req.CapacitySettings,
		RuleStatus:       req.Status,
		Operator:         req.Operator,
	}

	if req.Status == rule.RuleStatusActive {
		config.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	if err := s.lhCapacityRepo.UpdateLHCapacity(ctx, config); err != nil {
		return srerr.With(srerr.DatabaseErr, req, err)
	}
	return nil
}

// Get 获取容量配置
func (s *LHCapacityServiceImpl) Get(ctx context.Context, id int) (*ilh_smart_routing.GetLHCapacityResp, *srerr.Error) {
	// 获取配置
	config, err := s.lhCapacityRepo.GetLHCapacityByID(ctx, id)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}
	if config == nil {
		return nil, srerr.New(srerr.DataNotFound, id, "lh capacity not found with id: %d", id)
	}

	// 转换为响应，使用实体中的ID字段
	resp := &ilh_smart_routing.GetLHCapacityResp{
		ID:               config.ID,
		CapacityName:     config.CapacityName,
		ILHVendorName:    config.ILHVendorName,
		ILHLineID:        config.ILHLineID,
		ILHLineName:      config.ILHLineName,
		TWS:              config.TWS,
		DestinationPort:  config.DestinationPort,
		DGType:           config.DGType,
		CapacitySettings: config.CapacitySettings,
		Status:           config.RuleStatus,
		Operator:         config.Operator,
	}
	return resp, nil
}

// List 列出容量配置
func (s *LHCapacityServiceImpl) List(ctx context.Context, req *ilh_smart_routing.ListLHCapacityReq) (*ilh_smart_routing.ListLHCapacityResp, *srerr.Error) {
	// 分页查询，传递所有筛选条件
	configs, total, err := s.lhCapacityRepo.ListLHCapacities(
		ctx, req.ILHVendorName, req.LineID, req.DGType, req.DestinationPort, req.TWS, req.Pageno, req.Limit,
	)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, req, err)
	}

	// 转换为响应
	list := make([]ilh_smart_routing.LHCapacityListItem, 0, len(configs))
	for _, config := range configs {

		var (
			defaultBSAWeight   float64
			defaultAdhocWeight float64

			specialDates          []ilh_smart_routing.SpecialDateCapacity
			weeklyDays            []ilh_smart_routing.WeeklyCapacity
			specialTimeSlots      []ilh_smart_routing.SpecialTimeSlotCapacity
			specialDateTimeSlots  []ilh_smart_routing.SpecialDateTimeSlotCapacity
			productSpecialConfigs []entity.ProductSetting
		)

		// 遍历CapacitySettings以提取相应的数据
		for _, setting := range config.CapacitySettings {
			switch setting.TimeIntervalType {
			case entity.TimeIntervalTypeDefault:
				// 默认容量设置
				defaultBSAWeight = setting.BSAWeight
				defaultAdhocWeight = setting.AdhocWeight
				// 处理特殊产品保留容量信息
				if setting.ReserveStatus == entity.ReserveEnabled && len(setting.ProductSettings) > 0 {
					for _, prod := range setting.ProductSettings {
						productSpecialConfigs = append(productSpecialConfigs, entity.ProductSetting{
							ProductID:  prod.ProductID,
							Proportion: prod.Proportion,
						})
					}
				}
			case entity.TimeIntervalTypeSpecialDate:
				// 特殊日期设置
				if len(setting.TimeObject.SpecialDates) > 0 {
					// 将所有特殊日期都添加到数组中
					for _, dateStr := range setting.TimeObject.SpecialDates {
						specialDates = append(specialDates, ilh_smart_routing.SpecialDateCapacity{
							Date:      dateStr,
							BSAWeight: setting.BSAWeight,
						})
					}
				}
			case entity.TimeIntervalTypeWeeklyCapacity:
				// 周容量设置
				if len(setting.TimeObject.WeekDays) > 0 {
					// 将所有周容量都添加到数组中
					for _, weekDay := range setting.TimeObject.WeekDays {
						weeklyDays = append(weeklyDays, ilh_smart_routing.WeeklyCapacity{
							WeekDay:   int(weekDay),
							BSAWeight: setting.BSAWeight,
						})
					}
				}
			case entity.TimeIntervalTypeSpecialTimeSlot:
				// 特殊时间段设置
				if setting.TimeObject.SpecialTimeSlotStartTime != "" && setting.TimeObject.SpecialTimeSlotEndTime != "" {
					// 将特殊时间段添加到数组中
					specialTimeSlots = append(specialTimeSlots, ilh_smart_routing.SpecialTimeSlotCapacity{
						StartTime: setting.TimeObject.SpecialTimeSlotStartTime,
						EndTime:   setting.TimeObject.SpecialTimeSlotEndTime,
						BSAWeight: setting.BSAWeight,
					})
				}

			case entity.TimeIntervalTypeSpecialDateAndTimeSlot:
				// 特殊日期+时间段设置
				if setting.TimeObject.SpecialDateAndTimeSlotStartTime != "" && setting.TimeObject.SpecialDateAndTimeSlotEndTime != "" {
					// 将特殊日期时间段添加到数组中
					specialDateTimeSlots = append(specialDateTimeSlots, ilh_smart_routing.SpecialDateTimeSlotCapacity{
						StartDateTime: setting.TimeObject.SpecialDateAndTimeSlotStartTime,
						EndDateTime:   setting.TimeObject.SpecialDateAndTimeSlotEndTime,
						BSAWeight:     setting.BSAWeight,
					})
				}
			}
		}

		item := ilh_smart_routing.LHCapacityListItem{
			ID:                           config.ID,
			CapacityName:                 config.CapacityName,
			ILHVendorName:                config.ILHVendorName,
			ILHLineID:                    config.ILHLineID,
			ILHLineName:                  config.ILHLineName,
			TWS:                          config.TWS,
			DestinationPort:              config.DestinationPort,
			DGType:                       config.DGType,
			Status:                       config.RuleStatus,
			EffectiveStartTime:           config.EffectiveStartTime,
			Operator:                     config.Operator,
			DefaultBSAWeight:             defaultBSAWeight,
			DefaultAdhocWeight:           defaultAdhocWeight,
			DefaultProductSpecialConfigs: productSpecialConfigs,
			SpecialDates:                 specialDates,
			WeeklyDays:                   weeklyDays,
			SpecialTimeSlots:             specialTimeSlots,
			SpecialDateTimeSlots:         specialDateTimeSlots,
		}
		list = append(list, item)
	}

	resp := &ilh_smart_routing.ListLHCapacityResp{
		Total:  total,
		Pageno: req.Pageno,
		Count:  len(list),
		List:   list,
	}
	return resp, nil
}

// Delete 删除容量配置
func (s *LHCapacityServiceImpl) Delete(ctx context.Context, id int) *srerr.Error {
	// 删除前检查记录是否存在
	existConfig, err := s.lhCapacityRepo.GetLHCapacityByID(ctx, id)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	if existConfig == nil {
		return srerr.New(srerr.DataNotFound, id, "lh capacity not found with id: %d", id)
	}

	// 执行删除操作
	if err := s.lhCapacityRepo.DeleteLHCapacity(ctx, id); err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	return nil
}

// UpdateStatus 更新容量配置状态
func (s *LHCapacityServiceImpl) UpdateStatus(ctx context.Context, req *ilh_smart_routing.UpdateLHCapacityStatusReq) *srerr.Error {
	// 检查配置是否存在
	existConfig, err := s.lhCapacityRepo.GetLHCapacityByID(ctx, req.ID)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, req, err)
	}
	if existConfig == nil {
		return srerr.New(srerr.DataNotFound, req, "lh capacity not found with id: %d", req.ID)
	}

	// 如果要更新为Active状态，需要进行额外检查
	if req.Status == rule.RuleStatusActive {
		// 检查冲突的Active配置
		if err := s.checkConflictingActiveConfigs(ctx, existConfig); err != nil {
			return err
		}

		// 设置生效时间
		existConfig.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	// 更新状态
	if err := s.lhCapacityRepo.UpdateLHCapacityStatus(ctx, req.ID, req.Status, req.Operator, existConfig.EffectiveStartTime); err != nil {
		return srerr.With(srerr.DatabaseErr, req, err)
	}

	// 主动触发一次状态轮转
	s.UpdateActiveLHCapacityStatusExpired(ctx)

	return nil
}

// checkConflictingActiveConfigs 检查是否存在冲突的Active配置
func (s *LHCapacityServiceImpl) checkConflictingActiveConfigs(ctx context.Context, config *entity.LHCapacityConfig) *srerr.Error {
	// 获取所有Active状态的配置
	activeConfigs, err := s.lhCapacityRepo.GetLHCapacitiesByStatus(ctx, rule.RuleStatusActive)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, config, err)
	}

	// 检查是否存在冲突的Active配置
	for _, activeConfig := range activeConfigs {
		// 跳过自身
		if activeConfig.ID == config.ID {
			continue
		}

		// 检查ILHLineID是否相同，不同则跳过
		if activeConfig.ILHLineID != config.ILHLineID {
			continue
		}

		// 检查情况1: TWS或DestinationPort不完全相同但有交集
		twsIntersection := objutil.HaveIntersection(activeConfig.TWS, config.TWS)
		destPortIntersection := objutil.HaveIntersection(activeConfig.DestinationPort, config.DestinationPort)

		twsEqual := objutil.AreStringSlicesEqual(activeConfig.TWS, config.TWS)
		destPortEqual := objutil.AreStringSlicesEqual(activeConfig.DestinationPort, config.DestinationPort)

		// 如果有交集但不完全相同
		if (twsIntersection && !twsEqual) || (destPortIntersection && !destPortEqual) {
			return srerr.New(srerr.DataErr, config,
				"Found active configuration (ID: %d) with overlapping TWS or DestinationPort, cannot activate simultaneously", activeConfig.ID)
		}

		// 检查情况2: TWS和DestinationPort完全相同，且Active配置的DGType是Undefined，而新配置是DG/NonDG
		if twsEqual && destPortEqual &&
			activeConfig.DGType == rule.UndefinedDGFlag &&
			config.DGType != rule.UndefinedDGFlag {
			return srerr.New(srerr.DataErr, config,
				"Found active configuration (ID: %d) with Undefined DGType type having same TWS and DestinationPort, cannot activate specific DGType configuration", activeConfig.ID)
		}
	}

	return nil
}

// extractProductSettings 从Excel行数据中提取产品设置
func extractProductSettings(row []string) ([]entity.ProductSetting, entity.ReserveStatus, error) {
	// 默认为禁用状态
	reserveStatus := entity.ReserveDisabled

	// 检查是否启用产品预留
	reserveCapacity := ""
	if len(row) > 9 {
		reserveCapacity = row[9]
	}

	// 如果不是Y，返回空设置和禁用状态
	reserveUpper := strings.ToUpper(strings.TrimSpace(reserveCapacity))
	if reserveUpper != "Y" && reserveUpper != "YES" {
		return nil, reserveStatus, nil
	}

	// 已启用产品预留
	reserveStatus = entity.ReserveEnabled
	productSettings := []entity.ProductSetting{}

	// 处理产品ID和比例（最多5对）
	for i := 0; i < 5; i++ {
		// 计算产品ID和比例在row中的索引位置
		idIndex := 10 + i*2
		proportionIndex := 11 + i*2

		// 检查索引是否有效且值不为空
		if len(row) > idIndex && len(row) > proportionIndex &&
			strings.TrimSpace(row[idIndex]) != "" && strings.TrimSpace(row[proportionIndex]) != "" {
			productSettings = append(productSettings, entity.ProductSetting{
				ProductID:  parseInt(row[idIndex]),
				Proportion: parseFloat64(row[proportionIndex]),
			})
		}
	}

	// 对所有产品设置进行校验
	productIDMap := make(map[int]bool)
	var totalProportion float64

	for _, setting := range productSettings {
		// 校验比例值是否在0-100区间内
		if setting.Proportion < 0 || setting.Proportion > 100 {
			return nil, reserveStatus, fmt.Errorf("product proportion value (%f) must be between 0 and 100", setting.Proportion)
		}

		// 校验产品ID的有效性
		if setting.ProductID <= 0 {
			return nil, reserveStatus, fmt.Errorf("invalid product ID (%d), must be greater than 0", setting.ProductID)
		}

		// 校验是否有重复的产品ID
		if productIDMap[setting.ProductID] {
			return nil, reserveStatus, fmt.Errorf("duplicate product ID (%d) found", setting.ProductID)
		}
		productIDMap[setting.ProductID] = true

		// 累加总比例
		totalProportion += setting.Proportion
	}

	// 检查所有Proportion之和是否超过100
	if totalProportion > 100 {
		return nil, reserveStatus, fmt.Errorf("the sum of all product proportions (%f) exceeds 100", totalProportion)
	}

	return productSettings, reserveStatus, nil
}

// parseTimeIntervalType 解析时间间隔类型
func parseTimeIntervalType(value string) (entity.TimeIntervalType, error) {
	switch strings.TrimSpace(value) {
	case "Default", "default", "":
		return entity.TimeIntervalTypeDefault, nil
	case "Special Date", "special date":
		return entity.TimeIntervalTypeSpecialDate, nil
	case "Special Time Slot", "special time slot":
		return entity.TimeIntervalTypeSpecialTimeSlot, nil
	case "Weekly Capacity", "weekly capacity":
		return entity.TimeIntervalTypeWeeklyCapacity, nil
	case "Special Date + Time Slot", "special date + time slot":
		return entity.TimeIntervalTypeSpecialDateAndTimeSlot, nil
	default:
		return entity.TimeIntervalTypeDefault, fmt.Errorf("invalid time interval type: %s", value)
	}
}

// 日期格式正则表达式 (YYYY-MM-DD)
var dateRegex = regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`)

// 时间格式正则表达式 (HH:MM:SS)
// 确保时间格式为 00:00:00 到 23:59:59
var timeRegex = regexp.MustCompile(`^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$`)

// 校验日期格式是否有效 (YYYY-MM-DD)
func isValidDateFormat(dateStr string) bool {
	return dateRegex.MatchString(dateStr)
}

// 校验日期是否是有效的日历日期
func isValidDate(dateStr string) bool {
	// 首先检查格式
	if !isValidDateFormat(dateStr) {
		return false
	}

	// 尝试解析日期
	_, err := timeutil.ParseDate(dateStr)
	return err == nil
}

// 校验时间格式是否有效 (HH:MM:SS)
// 确保时间不超过 23:59:59
func isValidTimeFormat(timeStr string) bool {
	// 使用正则表达式检查基本格式
	if !timeRegex.MatchString(timeStr) {
		return false
	}

	// 解析时间以进行额外检查
	_, err := timeutil.ParseTime(timeStr)
	return err == nil
}

// 校验时间段是否有效（开始时间早于结束时间）
func isValidTimeSlot(startTime, endTime string) (bool, error) {
	// 首先检查格式
	if !isValidTimeFormat(startTime) || !isValidTimeFormat(endTime) {
		return false, fmt.Errorf("invalid time format, expected HH:MM:SS")
	}

	// 解析时间
	start, err := timeutil.ParseTime(startTime)
	if err != nil {
		return false, err
	}

	end, err := timeutil.ParseTime(endTime)
	if err != nil {
		return false, err
	}

	// 检查开始时间是否早于结束时间
	return start.Before(end), nil
}

// 校验日期时间是否有效
// 确保时间部分不超过 23:59:59
func isValidDateTime(dateTimeStr string) bool {
	// 尝试解析日期时间
	_, err := timeutil.ParseDateTime(dateTimeStr)
	if err != nil {
		return false
	}

	// 提取时间部分进行额外检查
	parts := strings.Split(dateTimeStr, " ")
	if len(parts) != 2 {
		return false
	}

	// 检查时间部分是否有效
	return isValidTimeFormat(parts[1])
}

// 校验日期时间段是否有效（开始时间早于结束时间）
func isValidDateTimeSlot(startDateTime, endDateTime string) (bool, error) {
	// 首先检查格式
	if !isValidDateTime(startDateTime) || !isValidDateTime(endDateTime) {
		return false, fmt.Errorf("invalid datetime format, expected YYYY-MM-DD HH:MM:SS")
	}

	// 解析时间
	start, err := timeutil.ParseDateTime(startDateTime)
	if err != nil {
		return false, err
	}

	end, err := timeutil.ParseDateTime(endDateTime)
	if err != nil {
		return false, err
	}

	// 检查开始时间是否早于结束时间
	return start.Before(end), nil
}

// parseDefaultTimeObject 解析默认时间对象
func parseDefaultTimeObject() (entity.TimeObject, error) {
	return entity.NewDefaultTimeObject(), nil
}

// parseSpecialDateObject 解析特殊日期对象
func parseSpecialDateObject(dateStr string) (entity.TimeObject, error) {
	// 检查是否为空
	if dateStr == "" {
		return entity.TimeObject{}, fmt.Errorf("special date object is empty")
	}

	// 强化校验日期格式
	if !isValidDateFormat(dateStr) {
		return entity.TimeObject{}, fmt.Errorf("invalid special date format: %s, expected YYYY-MM-DD", dateStr)
	}

	// 校验日期是否有效
	if !isValidDate(dateStr) {
		return entity.TimeObject{}, fmt.Errorf("invalid calendar date: %s", dateStr)
	}

	return entity.NewSpecialDateFromString(dateStr), nil
}

// parseSpecialTimeSlotObject 解析特殊时间段对象
func parseSpecialTimeSlotObject(timeSlotStr string) (entity.TimeObject, error) {
	// 检查是否为空
	if timeSlotStr == "" {
		return entity.TimeObject{}, fmt.Errorf("special time slot object is empty")
	}

	// 分割开始和结束时间
	parts := strings.Split(timeSlotStr, "-")
	if len(parts) != 2 {
		return entity.TimeObject{}, fmt.Errorf("invalid special time slot format: %s, expected HH:MM:SS-HH:MM:SS", timeSlotStr)
	}

	startTime := strings.TrimSpace(parts[0])
	endTime := strings.TrimSpace(parts[1])

	// 强化校验时间格式
	if !isValidTimeFormat(startTime) {
		return entity.TimeObject{}, fmt.Errorf("invalid start time format: %s, expected HH:MM:SS", startTime)
	}

	if !isValidTimeFormat(endTime) {
		return entity.TimeObject{}, fmt.Errorf("invalid end time format: %s, expected HH:MM:SS", endTime)
	}

	// 校验时间段是否有效（开始时间早于结束时间）
	valid, err := isValidTimeSlot(startTime, endTime)
	if err != nil {
		return entity.TimeObject{}, fmt.Errorf("error validating time slot: %v", err)
	}

	if !valid {
		return entity.TimeObject{}, fmt.Errorf("invalid time slot: end time %s must be after start time %s", endTime, startTime)
	}

	return entity.NewSpecialTimeSlotFromString(startTime, endTime), nil
}

// parseWeeklyCapacityObject 解析周容量对象
func parseWeeklyCapacityObject(weekdaysStr string) (entity.TimeObject, error) {
	// 检查是否为空
	if weekdaysStr == "" {
		return entity.TimeObject{}, fmt.Errorf("weekly capacity object is empty")
	}

	// 分割星期几字符串
	weekDayStrs := strings.Split(weekdaysStr, ",")
	weekDays := make([]timeutil.WeekDay, 0, len(weekDayStrs))
	weekDayMap := make(map[int]bool) // 用于检查重复

	// 处理每个星期几
	for _, dayStr := range weekDayStrs {
		dayStr = strings.TrimSpace(dayStr)
		if dayStr == "" {
			continue
		}

		// 转换为数字
		day, err := strconv.Atoi(dayStr)
		if err != nil {
			return entity.TimeObject{}, fmt.Errorf("invalid week day format: %s, must be a number", dayStr)
		}

		// 检查范围
		if day < 1 || day > 7 {
			return entity.TimeObject{}, fmt.Errorf("invalid week day value: %d, must be between 1 and 7", day)
		}

		// 检查重复
		if weekDayMap[day] {
			return entity.TimeObject{}, fmt.Errorf("duplicate week day: %d", day)
		}
		weekDayMap[day] = true

		weekDays = append(weekDays, timeutil.WeekDay(day))
	}

	// 确保至少有一个有效的星期几
	if len(weekDays) == 0 {
		return entity.TimeObject{}, fmt.Errorf("no valid week days found")
	}

	return entity.NewWeeklyTimeObject(weekDays), nil
}

// parseSpecialDateAndTimeSlotObject 解析特殊日期和时间段对象
func parseSpecialDateAndTimeSlotObject(dateTimeSlotStr string) (entity.TimeObject, error) {
	// 检查是否为空
	if dateTimeSlotStr == "" {
		return entity.TimeObject{}, fmt.Errorf("special date and time slot object is empty")
	}

	// 分割开始和结束日期时间
	parts := strings.Split(dateTimeSlotStr, "/")
	if len(parts) != 2 {
		return entity.TimeObject{}, fmt.Errorf("invalid special date and time slot format: %s, expected YYYY-MM-DD HH:MM:SS/YYYY-MM-DD HH:MM:SS", dateTimeSlotStr)
	}

	startDateTimeStr := strings.TrimSpace(parts[0])
	endDateTimeStr := strings.TrimSpace(parts[1])

	// 校验日期时间格式
	if !isValidDateTime(startDateTimeStr) {
		return entity.TimeObject{}, fmt.Errorf("invalid start date time: %s, expected YYYY-MM-DD HH:MM:SS", startDateTimeStr)
	}

	if !isValidDateTime(endDateTimeStr) {
		return entity.TimeObject{}, fmt.Errorf("invalid end date time: %s, expected YYYY-MM-DD HH:MM:SS", endDateTimeStr)
	}

	// 校验日期时间段是否有效（开始时间早于结束时间）
	valid, err := isValidDateTimeSlot(startDateTimeStr, endDateTimeStr)
	if err != nil {
		return entity.TimeObject{}, fmt.Errorf("error validating date time slot: %v", err)
	}

	if !valid {
		return entity.TimeObject{}, fmt.Errorf("invalid date time slot: end time %s must be after start time %s", endDateTimeStr, startDateTimeStr)
	}

	return entity.NewSpecialDateTimeSlotFromString(startDateTimeStr, endDateTimeStr), nil
}

// parseTimeObject 根据时间间隔类型解析时间对象
func parseTimeObject(timeIntervalType entity.TimeIntervalType, objectValue string) (entity.TimeObject, error) {
	objectValue = strings.TrimSpace(objectValue)

	switch timeIntervalType {
	case entity.TimeIntervalTypeDefault:
		// 默认类型，返回默认时间对象
		return parseDefaultTimeObject()

	case entity.TimeIntervalTypeSpecialDate:
		// 特殊日期格式: 2025-01-01
		return parseSpecialDateObject(objectValue)

	case entity.TimeIntervalTypeSpecialTimeSlot:
		// 特殊时间段格式: 14:00:00-18:00:00
		return parseSpecialTimeSlotObject(objectValue)

	case entity.TimeIntervalTypeWeeklyCapacity:
		// 周容量格式: 1, 2, 3, 4, 5, 6, 7 (1=周一, 2=周二, ..., 7=周日)
		return parseWeeklyCapacityObject(objectValue)

	case entity.TimeIntervalTypeSpecialDateAndTimeSlot:
		// 特殊日期和时间段格式: 2025-01-01 14:00:00/2025-01-01 18:00:00
		return parseSpecialDateAndTimeSlotObject(objectValue)

	default:
		return entity.TimeObject{}, fmt.Errorf("unsupported time interval type: %d", timeIntervalType)
	}
}

// createCapacityConfig 从Excel行数据创建容量配置
func createCapacityConfig(ctx context.Context, row []string, operator string) (*entity.LHCapacityConfig, error) {
	if len(row) < 9 {
		return nil, fmt.Errorf("row doesn't have enough columns")
	}

	vendorName := row[0]
	lineID := row[1]
	twsStr := row[2]
	destPortsStr := row[3]
	dgTypeStr := row[4]
	timeIntervalTypeStr := row[5]
	timeObjectStr := row[6]
	bsaWeightStr := row[7]
	adhocWeightStr := row[8]

	// 解析DG类型
	dgType, err := parseDGType(dgTypeStr)
	if err != nil {
		return nil, err
	}

	// 解析时间间隔类型
	timeIntervalType, err := parseTimeIntervalType(timeIntervalTypeStr)
	if err != nil {
		return nil, err
	}

	// 解析时间对象
	timeObject, err := parseTimeObject(timeIntervalType, timeObjectStr)
	if err != nil {
		return nil, err
	}

	// 提取产品设置
	productSettings, reserveStatus, err := extractProductSettings(row)
	if err != nil {
		return nil, err
	}

	// 创建容量设置
	setting := entity.CapacitySetting{
		TimeIntervalType: timeIntervalType,
		TimeObject:       timeObject,
		BSAWeight:        parseFloat64(bsaWeightStr),
		AdhocWeight:      parseFloat64(adhocWeightStr),
		ReserveStatus:    reserveStatus,
	}

	// 如果有产品设置，添加到设置中
	if len(productSettings) > 0 {
		setting.ProductSettings = productSettings
	}

	// 创建配置
	config := &entity.LHCapacityConfig{
		CapacityName:       vendorName,
		ILHVendorName:      vendorName,
		ILHLineID:          lineID,
		TWS:                strings.Split(twsStr, ","),
		DestinationPort:    strings.Split(destPortsStr, ","),
		DGType:             dgType,
		CapacitySettings:   []entity.CapacitySetting{setting},
		RuleStatus:         rule.RuleStatusExpired,
		EffectiveStartTime: timeutil.GetCurrentUnixTimeStamp(ctx),
		Operator:           operator,
	}

	return config, nil
}

// Upload imports capacity configurations through an S3 file link
func (s *LHCapacityServiceImpl) Upload(ctx context.Context, req *ilh_smart_routing.UploadLHCapacityReq) *srerr.Error {
	// Download file from S3
	fileContent, err := httputil.Get(ctx, req.FileUrl, nil, 30, nil)
	if err != nil {
		return srerr.With(srerr.S3DownloadFail, req.FileUrl, err)
	}

	// Parse Excel file
	rows, headers, err := fileutil.ParseExcel(ctx, bytes.NewReader(fileContent), true)
	if err != nil {
		return srerr.With(srerr.ExcelFileOpenError, req.FileUrl, err)
	}

	// Validate headers
	if !validateHeaders(headers) {
		return srerr.New(srerr.ExcelValidateError, nil, "Excel headers do not match expected format")
	}

	// 获取线路ID到线路名称的映射
	lineNameMap, _ := s.laneService.GetLineIdToLineNameMap(ctx)

	// 用于存储合并后的配置，key为Line ID + TWS + DestinationPort + DGType
	mergedConfigs := make(map[string]*entity.LHCapacityConfig)

	// Process each row
	for rowIndex, row := range rows {
		rowNum := rowIndex + 2 // Excel行号从1开始，第一行是表头，所以数据从第2行开始

		// Skip empty rows
		if isEmptyRow(row) {
			continue
		}

		// 检查Vendor字段是否有有效值
		if len(row) <= 0 || strings.TrimSpace(row[0]) == "" {
			return srerr.New(srerr.ExcelValidateError, nil, "Row: %d is missing a valid value for Vendor. Please complete this field.", rowNum)
		}

		// 处理Vendor字段，统一转为大写
		vendorName := strings.TrimSpace(row[0])
		// 将Vendor转为大写存储
		row[0] = strings.ToUpper(vendorName)

		// 检查Line ID是否存在于系统中
		if len(row) > 1 && strings.TrimSpace(row[1]) != "" {
			lineID := strings.TrimSpace(row[1])
			upperLineID := strings.ToUpper(lineID)

			// 将Line ID转为大写后与lineNameMap比较
			if _, exists := lineNameMap[upperLineID]; !exists {
				return srerr.New(srerr.ExcelValidateError, nil, "Row: %d; Line: %s does not exist in the system. Please check your configuration.", rowNum, lineID)
			}

			// 将原始数据替换为大写版本，以保持统一
			row[1] = upperLineID
		}

		// 检查TWS是否有有效值
		if len(row) <= 2 || strings.TrimSpace(row[2]) == "" {
			return srerr.New(srerr.ExcelValidateError, nil, "Row: %d is missing a valid value for TWS. Please complete this field.", rowNum)
		}

		// 检查Destination Port是否有有效值
		if len(row) <= 3 || strings.TrimSpace(row[3]) == "" {
			return srerr.New(srerr.ExcelValidateError, nil, "Row: %d is missing a valid value for Destination Port. Please complete this field.", rowNum)
		}

		// Validate required fields
		if err := validateRequiredFields(row); err != nil {
			return srerr.New(srerr.ExcelValidateError, nil, "Row: %d validation failed: %v", rowNum, err)
		}

		// 创建容量配置
		config, err := createCapacityConfig(ctx, row, req.Operator)
		if err != nil {
			return srerr.New(srerr.ExcelValidateError, nil, "Row: %d creation failed: %v", rowNum, err)
		}

		// 设置线路名称
		if lineName, ok := lineNameMap[config.ILHLineID]; ok {
			config.ILHLineName = lineName
		}

		// 构建唯一键，用于合并同维度数据
		key := generateConfigKey(config)

		// 如果已存在相同维度的配置，则合并CapacitySettings
		if existingConfig, exists := mergedConfigs[key]; exists {
			// 合并容量设置
			existingConfig.CapacitySettings = append(existingConfig.CapacitySettings, config.CapacitySettings...)
		} else {
			// 新建配置
			mergedConfigs[key] = config
		}
	}

	// 将合并后的配置转换为切片
	configsToSave := make([]*entity.LHCapacityConfig, 0, len(mergedConfigs))
	for key, config := range mergedConfigs {
		// 校验合并后的Config的CapacitySettings是否有效
		if err := entity.ValidateCapacitySettings(config.CapacitySettings); err != nil {
			return srerr.New(srerr.ExcelValidateError, nil, "Validation failed for merged config [%s]: %v", key, err)
		}

		// 添加到待保存列表
		configsToSave = append(configsToSave, config)
	}

	// 批量保存配置
	if err := s.lhCapacityRepo.BatchCreateLHCapacity(ctx, configsToSave); err != nil {
		return srerr.New(srerr.DatabaseErr, nil, "Failed to batch save capacity configurations: %v", err)
	}

	return nil
}

// generateConfigKey 生成用于合并的唯一键
func generateConfigKey(config *entity.LHCapacityConfig) string {
	// 对TWS和DestinationPort排序，确保顺序不同但内容相同的数组生成相同的键
	twsCopy := make([]string, len(config.TWS))
	copy(twsCopy, config.TWS)
	sort.Strings(twsCopy)

	destPortsCopy := make([]string, len(config.DestinationPort))
	copy(destPortsCopy, config.DestinationPort)
	sort.Strings(destPortsCopy)

	// 构建键: LineID + 排序后的TWS + 排序后的DestinationPort + DGType
	return fmt.Sprintf("%s_%s_%s_%d",
		config.ILHLineID,
		strings.Join(twsCopy, ","),
		strings.Join(destPortsCopy, ","),
		config.DGType)
}

// UploadResponse 定义了批量导入的响应结构
type UploadResponse struct {
	TotalCount   int             `json:"total_count"`
	SuccessCount int             `json:"success_count"`
	FailureCount int             `json:"failure_count"`
	Failures     []FailureRecord `json:"failures"`
}

// FailureRecord 定义了导入失败的记录
type FailureRecord struct {
	RowNumber int    `json:"row_number"`
	Error     string `json:"error"`
}

func parseFloat64(s string) float64 {
	if s == "" {
		return 0
	}
	v, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0
	}
	return v
}

func parseInt(s string) int {
	if s == "" {
		return 0
	}
	v, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return v
}

func isEmptyRow(row []string) bool {
	for _, cell := range row {
		if strings.TrimSpace(cell) != "" {
			return false
		}
	}
	return true
}

func validateHeaders(headers []string) bool {
	expectedHeaders := []string{
		"Vendor", "Line ID", "TWS", "Destination Port", "DG Type",
		"Custom Time Interval", "Object",
		"BSA Weight(kg)", "Adhoc Weight(kg)",
		"Reserve Capacity for special products?",
		"Reserve for Product ID 1", "Proportion 1",
		"Reserve for Product ID 2", "Proportion 2",
		"Reserve for Product ID 3", "Proportion 3",
		"Reserve for Product ID 4", "Proportion 4",
		"Reserve for Product ID 5", "Proportion 5",
	}

	if len(headers) < 9 { // 至少需要前9个必填字段
		return false
	}

	// 验证必要的前9个字段，其中第5个字段(index=4)是DG Type，可以为空
	for i := 0; i < 9 && i < len(expectedHeaders); i++ {
		if i >= len(headers) || !strings.EqualFold(strings.TrimSpace(headers[i]), strings.TrimSpace(expectedHeaders[i])) {
			return false
		}
	}

	// 对于其他可选字段，如果提供了，也需要匹配
	for i := 9; i < len(expectedHeaders) && i < len(headers); i++ {
		if !strings.EqualFold(strings.TrimSpace(headers[i]), strings.TrimSpace(expectedHeaders[i])) {
			return false
		}
	}

	return true
}

func validateRequiredFields(row []string) error {
	requiredFields := map[int]string{
		0: "Vendor",
		1: "Line ID",
		2: "TWS",
		3: "Destination Port",
		5: "Custom Time Interval",
		6: "Object",
		7: "BSA Weight (kg)",
		8: "Adhoc Weight (kg)",
		9: "Reserve Capacity for special products?",
	}

	for idx, fieldName := range requiredFields {
		if idx >= len(row) || strings.TrimSpace(row[idx]) == "" {
			// 对于"Custom Time Interval"，如果为空则默认为"Default"
			if idx == 5 {
				continue
			}
			// 对于"Object"，如果时间间隔类型是Default，则可以为空
			if idx == 6 && idx-1 < len(row) &&
				(strings.TrimSpace(row[idx-1]) == "" ||
					strings.EqualFold(strings.TrimSpace(row[idx-1]), "Default")) {
				continue
			}
			return fmt.Errorf("%s is a required field", fieldName)
		}
	}

	// 如果Custom Time Interval不是Default，则Object不能为空
	if len(row) > 5 && len(row) > 6 &&
		!strings.EqualFold(strings.TrimSpace(row[5]), "Default") &&
		!strings.EqualFold(strings.TrimSpace(row[5]), "") &&
		strings.TrimSpace(row[6]) == "" {
		return fmt.Errorf("Object is required when Custom Time Interval is not Default")
	}

	// 如果"Reserve Capacity for special products?"设置为"Y"，则至少需要一对产品ID和比例
	if len(row) > 9 && strings.ToUpper(strings.TrimSpace(row[9])) == "Y" {
		hasAtLeastOnePair := false
		// 检查是否至少有一对有效的产品ID和比例
		for i := 0; i < 5; i++ {
			idIndex := 10 + i*2
			proportionIndex := 11 + i*2

			if len(row) > idIndex && len(row) > proportionIndex &&
				strings.TrimSpace(row[idIndex]) != "" && strings.TrimSpace(row[proportionIndex]) != "" {
				hasAtLeastOnePair = true
				break
			}
		}

		if !hasAtLeastOnePair {
			return fmt.Errorf("When Reserve Capacity is Y, at least one Product ID and Proportion pair is required")
		}
	}

	return nil
}

func parseDGType(value string) (rule.DGFlag, error) {
	// 处理空值情况
	value = strings.TrimSpace(value)
	if value == "" {
		return rule.UndefinedDGFlag, nil
	}

	// Convert string value to DGFlag
	dgType := strings.ToUpper(value)
	switch dgType {
	case "DG":
		return rule.DG, nil
	case "NON-DG", "NON DG", "NONDG":
		return rule.NonDG, nil
	default:
		return rule.UndefinedDGFlag, fmt.Errorf("invalid DG type: %s", value)
	}
}

// Copy 复制容量配置
func (s *LHCapacityServiceImpl) Copy(ctx context.Context, req *ilh_smart_routing.CopyLHCapacityReq) (int, *srerr.Error) {
	// 查询原配置
	sourceConfig, err := s.lhCapacityRepo.GetLHCapacityByID(ctx, req.ID)
	if err != nil {
		return 0, err
	}
	if sourceConfig == nil {
		return 0, srerr.New(srerr.DataNotFound, req, "source LH capacity not found with id: %d", req.ID)
	}

	// 创建新配置
	sourceConfig.ID = 0 // 重置ID，让数据库生成新ID
	sourceConfig.Operator = req.Operator
	sourceConfig.RuleStatus = rule.RuleStatusExpired
	sourceConfig.CapacityName = sourceConfig.CapacityName + "_copy" // 复制的名称添加后缀

	return s.lhCapacityRepo.CreateLHCapacity(ctx, sourceConfig)
}

// CheckActive 检查是否存在指定条件且状态为Active的配置
func (s *LHCapacityServiceImpl) CheckActive(ctx context.Context, req *ilh_smart_routing.CheckLHCapacityReq) (*ilh_smart_routing.CheckLHCapacityResp, *srerr.Error) {
	// 获取Active配置
	configs, err := s.lhCapacityRepo.GetLHCapacitiesByStatus(ctx, rule.RuleStatusActive)
	if err != nil {
		return nil, err
	}

	// 检查是否存在相同条件的Active的LH Capacity
	exists := false
	for _, config := range configs {
		// 检查ILHLineID是否相同
		if config.ILHLineID != req.ILHLineID {
			continue
		}

		// 检查DGType是否匹配
		// 1. 如果配置的DGType是UndefinedDGFlag，则与所有DGType匹配
		// 2. 否则，需要DGType完全相同
		if config.DGType != rule.UndefinedDGFlag && config.DGType != req.DGType {
			continue
		}

		// 检查TWS是否有交集
		if !objutil.HaveIntersection(config.TWS, req.TWS) {
			continue
		}

		// 检查DestinationPort是否有交集
		if !objutil.HaveIntersection(config.DestinationPort, req.DestinationPort) {
			continue
		}

		// 所有条件都匹配
		exists = true
		break
	}

	return &ilh_smart_routing.CheckLHCapacityResp{
		Exists: exists,
	}, nil
}

func (s *LHCapacityServiceImpl) GetLHCapacitySettingInfo(
	ctx context.Context, ilhLineID string, dgType int, twsCode string, destinationPorts []string,
) (entity.ILHCapacitySettingInfo, *srerr.Error) {
	cacheVal, err := localcache.Get(ctx, constant.LHCapacityConfig, ilhLineID)
	if err != nil {
		return entity.ILHCapacitySettingInfo{}, srerr.With(srerr.LocalCacheErr, ilhLineID, err)
	}

	lhCapacityConfigList, ok := cacheVal.([]*entity.LHCapacityConfig)
	if !ok {
		return entity.ILHCapacitySettingInfo{}, srerr.New(srerr.LocalCacheErr, ilhLineID, "convert cache val to lh capacity failed")
	}

	// Find applicable LH capacity config
	var lhCapacityConfig *entity.LHCapacityConfig
	for _, r := range lhCapacityConfigList {
		// Skip if DG type doesn't match
		if r.DGType != rule.UndefinedDGFlag && r.DGType != rule.DGFlag(dgType) {
			logger.CtxLogDebugf(ctx, "Skipping capacity config for ilh=%s: DG type mismatch (config=%v, request=%v)",
				ilhLineID, r.DGType, rule.DGFlag(dgType))
			continue
		}

		// Skip if TWS doesn't match
		if len(r.TWS) != 0 && !objutil.ContainStr(r.TWS, twsCode) {
			logger.CtxLogDebugf(ctx, "Skipping capacity config for ilh=%s: TWS mismatch (config=%v, request=%s)",
				ilhLineID, r.TWS, twsCode)
			continue
		}

		// Skip if destination port doesn't match
		if len(r.DestinationPort) != 0 && !objutil.HaveIntersection(r.DestinationPort, destinationPorts) {
			logger.CtxLogDebugf(ctx, "Skipping capacity config for ilh=%s: no destination port intersection", ilhLineID)
			continue
		}

		lhCapacityConfig = r
		logger.CtxLogDebugf(ctx, "Found matching capacity config for ilh=%s", ilhLineID)
		break
	}

	if lhCapacityConfig == nil {
		logger.CtxLogInfof(ctx, "No matching capacity config found for ilh=%s", ilhLineID)
		return entity.ILHCapacitySettingInfo{}, srerr.New(srerr.DataErr, "no capacity config found for ilh=%s", ilhLineID)
	}

	// Get capacity setting with the highest priority
	capacitySetting, exist := entity.GetHighestPriorityCapacitySetting(ctx, lhCapacityConfig.CapacitySettings, twsCode)
	if !exist {
		logger.CtxLogInfof(ctx, "No capacity settings found for ilh=%s", ilhLineID)
		return entity.ILHCapacitySettingInfo{}, srerr.New(srerr.DataErr, "no capacity settings found for ilh=%s", ilhLineID)
	}

	return entity.ILHCapacitySettingInfo{
		CapacitySetting:             capacitySetting,
		TWS:                         lhCapacityConfig.TWS,
		DestPorts:                   lhCapacityConfig.DestinationPort,
		InheritanceTimeSlotCapacity: entity.GetInheritanceTimeSlotCapacity(ctx, lhCapacityConfig.CapacitySettings, twsCode),
	}, nil
}

// UpdatePendingLHCapacityStatusActive 更新Pending状态的LHCapacity为Active
func (s *LHCapacityServiceImpl) UpdatePendingLHCapacityStatusActive(ctx context.Context) {
	// 使用专用方法获取所有Queuing状态的配置
	configs, err := s.lhCapacityRepo.GetLHCapacitiesByStatus(ctx, rule.RuleStatusQueuing)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to query Queuing LH capacity records: %v", err)
		return
	}

	// 当前时间戳
	currentTime := timeutil.GetCurrentUnixTimeStamp(ctx)

	// 遍历所有Queuing状态的记录，检查是否需要激活
	for _, config := range configs {
		// 检查是否已到生效时间
		if config.EffectiveStartTime > 0 && config.EffectiveStartTime <= currentTime {
			// 更新状态为Active
			if err := s.lhCapacityRepo.UpdateLHCapacityStatus(ctx, config.ID, rule.RuleStatusActive, config.Operator, config.EffectiveStartTime); err != nil {
				logger.CtxLogErrorf(ctx, "Failed to update LH capacity status to Active: id=%d, error=%v", config.ID, err)
				continue
			}

			logger.CtxLogInfof(ctx, "Successfully updated LH capacity status from Queuing to Active: id=%d", config.ID)
		}
	}
}

func (s *LHCapacityServiceImpl) UpdateActiveLHCapacityStatusExpired(ctx context.Context) {
	// 获取所有Active状态的配置
	configs, err := s.lhCapacityRepo.GetLHCapacitiesByStatus(ctx, rule.RuleStatusActive)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to query Active LH capacity records: %v", err)
		return
	}

	// 按时间排序并找出需要过期的配置ID
	expiredConfigIDs := s.findConfigsToExpire(ctx, configs)

	// 将标记为过期的配置更新为Expired状态
	s.updateExpiredConfigs(ctx, configs, expiredConfigIDs)
}

// findConfigsToExpire 返回需要过期的配置ID集合
func (s *LHCapacityServiceImpl) findConfigsToExpire(ctx context.Context, configs []*entity.LHCapacityConfig) map[int]bool {
	// 按EffectiveStartTime排序，最新的在前面
	sort.Slice(configs, func(i, j int) bool {
		return configs[i].EffectiveStartTime > configs[j].EffectiveStartTime
	})

	// 用来记录哪些配置需要被更新为Expired状态
	expiredConfigIDs := make(map[int]bool)

	// 遍历所有配置，从最新的开始
	for i, latestConfig := range configs {
		// 如果当前配置已被标记为过期，跳过
		if expiredConfigIDs[latestConfig.ID] {
			continue
		}

		// 搜索有冲突的较旧配置
		for j := i + 1; j < len(configs); j++ {
			olderConfig := configs[j]

			// 如果已被标记为过期，跳过
			if expiredConfigIDs[olderConfig.ID] {
				continue
			}

			// 检查两个配置是否冲突
			if s.isConfigsConflicting(latestConfig, olderConfig) {
				// 标记旧配置为过期
				expiredConfigIDs[olderConfig.ID] = true

				logger.CtxLogInfof(ctx, "Marking config as expired: id=%d, ilhLineID=%s, effectiveTime=%d. Replaced by id=%d with effectiveTime=%d",
					olderConfig.ID, olderConfig.ILHLineID, olderConfig.EffectiveStartTime, latestConfig.ID, latestConfig.EffectiveStartTime)
			}
		}
	}

	return expiredConfigIDs
}

// isConfigsConflicting 判断两个配置是否冲突
func (s *LHCapacityServiceImpl) isConfigsConflicting(newConfig, oldConfig *entity.LHCapacityConfig) bool {
	// 条件1: ILHLineID必须相同
	if newConfig.ILHLineID != oldConfig.ILHLineID {
		return false
	}

	// 条件2: 检查TWS是否有交集
	if !objutil.HaveIntersection(newConfig.TWS, oldConfig.TWS) {
		return false
	}

	// 条件3: 检查DestinationPort是否有交集
	if !objutil.HaveIntersection(newConfig.DestinationPort, oldConfig.DestinationPort) {
		return false
	}

	// 条件4: 检查DGType
	// a. 如果旧配置是Undefined类型，新配置只能是Undefined类型才可以替代它
	// b. 如果旧配置是具体类型(DG/NonDG)，新配置必须是相同类型或Undefined才能替代它
	if (oldConfig.DGType == rule.UndefinedDGFlag && newConfig.DGType != rule.UndefinedDGFlag) ||
		(oldConfig.DGType != rule.UndefinedDGFlag &&
			newConfig.DGType != rule.UndefinedDGFlag &&
			newConfig.DGType != oldConfig.DGType) {
		return false
	}

	// 所有条件都满足，两个配置冲突
	return true
}

// updateExpiredConfigs 将需要过期的配置更新为Expired状态
func (s *LHCapacityServiceImpl) updateExpiredConfigs(ctx context.Context, configs []*entity.LHCapacityConfig, expiredConfigIDs map[int]bool) {
	for configID := range expiredConfigIDs {
		// 找到对应的配置对象
		var configToUpdate *entity.LHCapacityConfig
		for _, config := range configs {
			if config.ID == configID {
				configToUpdate = config
				break
			}
		}

		if configToUpdate == nil {
			logger.CtxLogErrorf(ctx, "Cannot find config with id=%d to update to Expired", configID)
			continue
		}

		// 更新状态为Expired
		if err := s.lhCapacityRepo.UpdateLHCapacityStatus(
			ctx, configID, rule.RuleStatusExpired, configToUpdate.Operator, configToUpdate.EffectiveStartTime,
		); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to update LH capacity status to Expired: id=%d, error=%v", configID, err)
			continue
		}

		logger.CtxLogInfof(ctx, "Updated LH capacity status from Active to Expired: id=%d", configID)
	}
}
