package cc_routing

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func TestParseCategoryRules(t *testing.T) {
	tests := []struct {
		name           string
		rows           []map[string]string
		expectedRules  []cc_routing_rule.CategoryRuleItem
		expectedCC     string
		expectedError  *srerr.Error
		mockCCResponse []*ccclient.CustomsAgent
		mockCCErr      error
		setupMock      func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error)
	}{
		{
			name: "正常解析带默认行的数据",
			rows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			expectedRules: []cc_routing_rule.CategoryRuleItem{
				{CategoryId: 1001, CustomsClearance: "CC1"},
				{CategoryId: 1002, CustomsClearance: "CC2"},
			},
			expectedCC:     "CC3",
			expectedError:  nil,
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}, {AgentId: "CC3"}},
			mockCCErr:      nil,
			setupMock: func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(response, nil).Once()
			},
		},
		{
			name: "缺少默认行",
			rows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
			},
			expectedRules: nil,
			expectedCC:    "",
			expectedError: srerr.New(srerr.ParamErr, nil, "missing required 'default' row"),
			setupMock:     func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {},
		},
		{
			name: "包含重复的类目ID",
			rows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1001", "Allocate to": "CC2"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			expectedRules: nil,
			expectedCC:    "",
			expectedError: srerr.New(srerr.ParamErr, nil, "duplicate Global Category ID: 1001"),
			setupMock:     func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {},
		},
		{
			name: "包含无效的类目ID格式",
			rows: []map[string]string{
				{"Global Category ID": "abc", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			expectedRules: nil,
			expectedCC:    "",
			expectedError: srerr.New(srerr.ParamErr, nil, "row 1: invalid Global Category ID 'abc'"),
			setupMock:     func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {},
		},
		{
			name: "包含空值",
			rows: []map[string]string{
				{"Global Category ID": "", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			expectedRules: nil,
			expectedCC:    "",
			expectedError: srerr.New(srerr.ParamErr, nil, "row 1: Global Category ID='' and Allocate to='CC1' cannot be empty"),
			setupMock:     func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {},
		},
		{
			name: "包含无效的CC值",
			rows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "INVALID_CC"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			expectedRules:  nil,
			expectedCC:     "",
			expectedError:  srerr.New(srerr.ParamErr, nil, "invalid CC values not in available list: [INVALID_CC]"),
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			setupMock: func(mockCCApi *MockCCApi, response []*ccclient.CustomsAgent, err error) {
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(response, nil).Once()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCCApi := new(MockCCApi)
			if tt.setupMock != nil {
				tt.setupMock(mockCCApi, tt.mockCCResponse, tt.mockCCErr)
			}

			service := &CCRoutingServiceImpl{
				CCApi: mockCCApi,
			}

			rules, defaultCC, err := service.parseCategoryRules(context.Background(), tt.rows)

			if tt.expectedError != nil {
				assert.Error(t, err)
				if err != nil {
					assert.Equal(t, tt.expectedError.Error(), err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
				assert.Equal(t, tt.expectedCC, defaultCC)
				assert.ElementsMatch(t, tt.expectedRules, rules)
			}

			mockCCApi.AssertExpectations(t)
		})
	}
}

func TestImportCategoryRules(t *testing.T) {
	tests := []struct {
		name             string
		productId        int
		validateOnly     bool
		mockRows         []map[string]string
		mockCCResponse   []*ccclient.CustomsAgent
		mockGetRuleErr   *srerr.Error
		mockCreateRuleId int
		mockCreateErr    *srerr.Error
		mockUpdateErr    *srerr.Error
		expectError      bool
		expectedErrMsg   string
	}{
		{
			name:      "成功导入规则",
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse:   []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}, {AgentId: "CC3"}},
			mockGetRuleErr:   srerr.New(srerr.CCRoutingRuleNotFound, nil, "not found"),
			mockCreateRuleId: 1,
			expectError:      false,
		},
		{
			name:           "空文件数据",
			productId:      100,
			mockRows:       []map[string]string{},
			expectError:    true,
			expectedErrMsg: "empty file or no data rows",
		},
		{
			name:      "缺少默认行",
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
			},
			expectError:    true,
			expectedErrMsg: "missing required 'default' row",
		},
		{
			name:      "包含无效的CC值",
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "INVALID_CC"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			expectError:    true,
			expectedErrMsg: "invalid CC values not in available list: [INVALID_CC]",
		},
		{
			name:         "仅验证模式成功",
			productId:    100,
			validateOnly: true,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			expectError:    false,
		},
		{
			name:      "规则已存在时更新",
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			mockGetRuleErr: nil, // 规则已存在
			expectError:    false,
		},
		{
			name:      "创建规则失败",
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			mockGetRuleErr: srerr.New(srerr.CCRoutingRuleNotFound, nil, "not found"),
			mockCreateErr:  srerr.New(srerr.DataErr, nil, "create failed"),
			expectError:    true,
			expectedErrMsg: "create failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCCApi := new(MockCCApi)
			mockRepo := new(MockCCRoutingRuleRepo)

			// 设置CC API mock
			if len(tt.mockCCResponse) > 0 {
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(tt.mockCCResponse, nil)
			}

			// 设置Repository mock
			if !tt.validateOnly && len(tt.mockRows) > 0 && tt.expectedErrMsg != "empty file or no data rows" && tt.expectedErrMsg != "missing required 'default' row" && !strings.Contains(tt.expectedErrMsg, "invalid CC") {
				// Mock GetCCRoutingRuleByProductId to check if rule exists
				if tt.mockGetRuleErr != nil {
					mockRepo.On("GetCCRoutingRuleByProductId", mock.Anything, tt.productId).Return((*cc_routing_rule.CCRoutingRuleTab)(nil), tt.mockGetRuleErr)
				} else {
					// Rule exists, should update
					existingRule := &cc_routing_rule.CCRoutingRuleTab{
						Id:          1,
						ProductId:   tt.productId,
						RoutingType: cc_routing_rule.CCRoutingTypeCategory,
						RuleDetail:  `{}`,
					}
					mockRepo.On("GetCCRoutingRuleByProductId", mock.Anything, tt.productId).Return(existingRule, nil)
					mockRepo.On("UpdateCCRoutingRuleByID", mock.Anything, 1, mock.Anything).Return(tt.mockUpdateErr)
				}

				// Mock CreateRoutingRule if it's a new rule
				if tt.mockGetRuleErr != nil && tt.mockGetRuleErr.GetCode() == srerr.CCRoutingRuleNotFound.Code() {
					mockRepo.On("CreateRoutingRule", mock.Anything, mock.Anything).Return(tt.mockCreateRuleId, tt.mockCreateErr)
				}
			}

			service := &CCRoutingServiceImpl{
				CCApi:         mockCCApi,
				CCRoutingRepo: mockRepo,
			}

			// Create a wrapper to test with mock data
			testService := &CategoryRuleTestService{
				CCRoutingServiceImpl: service,
				mockRows:             tt.mockRows,
			}

			err := testService.ImportCategoryRules(context.Background(), tt.productId, []byte("test"), "test_operator", tt.validateOnly)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedErrMsg != "" && err != nil {
					assert.Contains(t, err.Error(), tt.expectedErrMsg)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error for case %s, but got: %v", tt.name, err)
				}
			}

			mockCCApi.AssertExpectations(t)
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestUpdateCategoryRules(t *testing.T) {
	tests := []struct {
		name           string
		id             int
		productId      int
		validateOnly   bool
		mockRows       []map[string]string
		mockRule       *cc_routing_rule.CCRoutingRuleTab
		mockGetRuleErr *srerr.Error
		mockCCResponse []*ccclient.CustomsAgent
		mockUpdateErr  *srerr.Error
		expectError    bool
		expectedErrMsg string
	}{
		{
			name:      "成功更新规则",
			id:        1,
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeCategory,
				RuleDetail:  `{}`,
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}, {AgentId: "CC3"}},
			expectError:    false,
		},
		{
			name:           "规则不存在",
			id:             999,
			productId:      100,
			mockGetRuleErr: srerr.New(srerr.CCRoutingRuleNotFound, nil, "rule not found"),
			expectError:    true,
			expectedErrMsg: "rule not found",
		},
		{
			name:      "成功更新不同类型的规则",
			id:        1,
			productId: 100,
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "1002", "Allocate to": "CC2"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeShopGroup, // 不同的类型，但应该成功更新
				RuleDetail:  `{}`,
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}, {AgentId: "CC3"}},
			expectError:    false,
		},
		{
			name:      "成功更新权重类别规则为类目规则",
			id:        2,
			productId: 200,
			mockRows: []map[string]string{
				{"Global Category ID": "2001", "Allocate to": "CC4"},
				{"Global Category ID": "default", "Allocate to": "CC5"},
			},
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          2,
				ProductId:   200,
				RoutingType: cc_routing_rule.CCRoutingTypeWeightCategory, // 权重类别类型，但应该成功更新为类目规则
				RuleDetail:  `{}`,
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC4"}, {AgentId: "CC5"}},
			expectError:    false,
		},
		{
			name:      "空文件数据",
			id:        1,
			productId: 100,
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeCategory,
				RuleDetail:  `{}`,
			},
			mockRows:       []map[string]string{},
			expectError:    true,
			expectedErrMsg: "empty file or no data rows",
		},
		{
			name:         "仅验证模式成功",
			id:           1,
			productId:    100,
			validateOnly: true,
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeCategory,
				RuleDetail:  `{}`,
			},
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			expectError:    false,
		},
		{
			name:      "更新规则失败",
			id:        1,
			productId: 100,
			mockRule: &cc_routing_rule.CCRoutingRuleTab{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeCategory,
				RuleDetail:  `{}`,
			},
			mockRows: []map[string]string{
				{"Global Category ID": "1001", "Allocate to": "CC1"},
				{"Global Category ID": "default", "Allocate to": "CC3"},
			},
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
			mockUpdateErr:  srerr.New(srerr.DataErr, nil, "update failed"),
			expectError:    true,
			expectedErrMsg: "update failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCCApi := new(MockCCApi)
			mockRepo := new(MockCCRoutingRuleRepo)

			// 设置Repository mock for GetCCRoutingRuleByID
			if tt.mockGetRuleErr != nil {
				mockRepo.On("GetCCRoutingRuleByID", mock.Anything, tt.id).Return((*cc_routing_rule.CCRoutingRuleTab)(nil), tt.mockGetRuleErr)
			} else if tt.mockRule != nil {
				mockRepo.On("GetCCRoutingRuleByID", mock.Anything, tt.id).Return(tt.mockRule, nil)
			}

			// 设置CC API mock
			if len(tt.mockCCResponse) > 0 {
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(tt.mockCCResponse, nil)
			}

			// 设置UpdateCCRoutingRuleByID mock
			if !tt.validateOnly && tt.mockRule != nil && len(tt.mockRows) > 0 {
				mockRepo.On("UpdateCCRoutingRuleByID", mock.Anything, tt.id, mock.Anything).Return(tt.mockUpdateErr)
			}

			service := &CCRoutingServiceImpl{
				CCApi:         mockCCApi,
				CCRoutingRepo: mockRepo,
			}

			// Create a wrapper to test with mock data
			testService := &CategoryRuleTestService{
				CCRoutingServiceImpl: service,
				mockRows:             tt.mockRows,
			}

			err := testService.UpdateCategoryRules(context.Background(), tt.id, tt.productId, []byte("test"), "test_operator", tt.validateOnly)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedErrMsg != "" && err != nil {
					assert.Contains(t, err.Error(), tt.expectedErrMsg)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error for case %s, but got: %v", tt.name, err)
				}
			}

			mockCCApi.AssertExpectations(t)
			mockRepo.AssertExpectations(t)
		})
	}
}

// CategoryRuleTestService is a wrapper around CCRoutingServiceImpl for testing
type CategoryRuleTestService struct {
	*CCRoutingServiceImpl
	mockRows []map[string]string
}

// ImportCategoryRules overrides the original method to use mock data instead of parsing files
func (s *CategoryRuleTestService) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// Skip file parsing and use mock data
	if len(s.mockRows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// Call parseCategoryRules with mock data
	rules, defaultCC, parseErr := s.parseCategoryRules(ctx, s.mockRows)
	if parseErr != nil {
		return parseErr
	}

	// Build rule detail
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		CategoryRuleDetail: cc_routing_rule.CategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// If validate only mode, stop here
	if validateOnly {
		return nil
	}

	// Create or update rule
	return s.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeCategory, ruleDetail, operator)
}

// UpdateCategoryRules overrides the original method to use mock data instead of parsing files
func (s *CategoryRuleTestService) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// Check if rule exists
	_, err := s.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// Skip file parsing and use mock data
	if len(s.mockRows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// Call parseCategoryRules with mock data
	rules, defaultCC, parseErr := s.parseCategoryRules(ctx, s.mockRows)
	if parseErr != nil {
		return parseErr
	}

	// If validate only mode, stop here
	if validateOnly {
		return nil
	}

	// Update rule
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		CategoryRuleDetail: cc_routing_rule.CategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// Update rule
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeCategory,
		RuleDetail:  ruleDetail,
	}
	return s.UpdateCCRoutingRule(ctx, ruleObj, operator)
}
