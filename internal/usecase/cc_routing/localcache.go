package cc_routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

func DumpCCRoutingRule() (map[string]interface{}, error) {
	ret := make(map[string]interface{})
	ctx := context.Background()

	var records []*cc_routing_rule.CCRoutingRuleTab
	if err := dbutil.Select(ctx, cc_routing_rule.CCRoutingRuleTabHook, nil, &records); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	for _, record := range records {
		rule := &cc_routing_rule.CCRoutingRule{
			Id:          record.Id,
			ProductId:   record.ProductId,
			RoutingType: record.RoutingType,
		}
		if err := jsoniter.UnmarshalFromString(record.RuleDetail, &rule.RuleDetail); err != nil {
			logger.CtxLogErrorf(ctx, "UnmarshalFromString fail|rule detail=%s|err=%v", record.RuleDetail, err)
			continue
		}
		ret[strconv.Itoa(record.ProductId)] = rule
	}

	return ret, nil
}
