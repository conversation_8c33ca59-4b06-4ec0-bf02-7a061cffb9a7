package cc_routing

import (
	"context"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestParseWeightCategoryRules(t *testing.T) {
	// 由于validateWeightRanges是方法不能直接mock，所以我们这里只测试parseWeightCategoryRules中的部分逻辑
	// 比如解析、默认规则校验等，不测试validateWeightRanges调用部分
	tests := []struct {
		name           string
		rows           []map[string]string
		expectedError  bool
		errorContains  string
		mockCCResponse []*ccclient.CustomsAgent
		mockCCErr      error
	}{
		{
			name: "缺少默认行",
			rows: []map[string]string{
				{
					"Category ID":               "1001",
					"Weight Min (Not included)": "0",
					"Weight Max (Included)":     "1000",
					"Allocate to":               "CC1",
				},
			},
			expectedError:  true,
			errorContains:  "missing required 'default' row",
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "无效的重量值",
			rows: []map[string]string{
				{
					"Category ID":               "1001",
					"Weight Min (Not included)": "abc",
					"Weight Max (Included)":     "1000",
					"Allocate to":               "CC1",
				},
				{
					"Category ID": "default",
					"Allocate to": "CC3",
				},
			},
			expectedError:  true,
			errorContains:  "invalid Weight Min",
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "最小值大于等于最大值",
			rows: []map[string]string{
				{
					"Category ID":               "1001",
					"Weight Min (Not included)": "1000",
					"Weight Max (Included)":     "1000",
					"Allocate to":               "CC1",
				},
				{
					"Category ID": "default",
					"Allocate to": "CC3",
				},
			},
			expectedError:  true,
			errorContains:  "must be less than Weight Max",
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "非默认行缺少重量区间",
			rows: []map[string]string{
				{
					"Category ID": "1001",
					"Allocate to": "CC1",
				},
				{
					"Category ID": "default",
					"Allocate to": "CC3",
				},
			},
			expectedError:  true,
			errorContains:  "Weight Min",
			mockCCResponse: nil, // 不会调用到 CC API
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock对象
			mockCCApi := new(MockCCApi)

			// 设置mock行为（只在需要时）
			if tt.mockCCResponse != nil || tt.mockCCErr != nil {
				var srErr *srerr.Error
				if tt.mockCCErr != nil {
					srErr = srerr.New(srerr.CustomsServiceErr, nil, tt.mockCCErr.Error())
				}
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(tt.mockCCResponse, srErr)
			}

			// 创建用于测试的服务实现
			mockSvc := new(WeightCategoryRuleMockService)
			mockSvc.mockCCApi = mockCCApi

			// 执行测试
			_, _, err := mockSvc.parseWeightCategoryRules(context.Background(), tt.rows)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}

			// 验证mock调用
			mockCCApi.AssertExpectations(t)
		})
	}
}

// WeightCategoryRuleMockService 是用于测试parseWeightCategoryRules的简单实现
type WeightCategoryRuleMockService struct {
	CCRoutingServiceImpl
	mockCCApi *MockCCApi
}

func (s *WeightCategoryRuleMockService) validateWeightRanges(rules []cc_routing_rule.WeightCategoryRuleItem) *srerr.Error {
	// 这个函数在单独的测试中已经测试过了，在这里我们只需要返回nil
	return nil
}

func TestImportWeightCategoryRules(t *testing.T) {
	tests := []struct {
		name           string
		productId      int
		fileContent    []byte
		validateOnly   bool
		mockParseError *srerr.Error
		mockRules      []cc_routing_rule.WeightCategoryRuleItem
		mockDefaultCC  string
		mockCreateErr  *srerr.Error
		expectError    bool
	}{
		{
			name:         "成功导入规则",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockCreateErr: nil,
			expectError:   false,
		},
		{
			name:           "解析文件失败",
			productId:      100,
			fileContent:    []byte("invalid content"),
			validateOnly:   false,
			mockParseError: srerr.New(srerr.FormatErr, nil, "parse error"),
			expectError:    true,
		},
		{
			name:         "仅验证模式成功",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: true,
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			expectError:   false,
		},
		{
			name:         "创建规则失败",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockCreateErr: srerr.New(srerr.DataErr, nil, "create error"),
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试service
			svc := new(WeightCategoryRuleTestService)
			svc.t = t
			svc.mockRules = tt.mockRules
			svc.mockDefaultCC = tt.mockDefaultCC
			svc.mockParseError = tt.mockParseError
			svc.mockCreateErr = tt.mockCreateErr
			svc.expectedProductId = tt.productId
			svc.expectedRoutingType = cc_routing_rule.CCRoutingTypeWeightCategory

			// 执行测试
			err := svc.ImportWeightCategoryRules(context.Background(), tt.productId, tt.fileContent, "test_operator", tt.validateOnly)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}

func TestUpdateWeightCategoryRules(t *testing.T) {
	tests := []struct {
		name           string
		id             int
		productId      int
		fileContent    []byte
		validateOnly   bool
		mockRule       *cc_routing_rule.CCRoutingRule
		mockGetRuleErr *srerr.Error
		mockRules      []cc_routing_rule.WeightCategoryRuleItem
		mockDefaultCC  string
		mockUpdateErr  *srerr.Error
		expectError    bool
	}{
		{
			name:         "成功更新规则",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeWeightCategory,
			},
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:           "获取规则失败",
			id:             1,
			productId:      100,
			fileContent:    []byte("test file content"),
			validateOnly:   false,
			mockGetRuleErr: srerr.New(srerr.DataErr, nil, "not found"),
			expectError:    true,
		},
		{
			name:         "更新不同类型的规则成功",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeFixed, // 不同的类型，但应该成功更新
			},
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:         "更新ShopGroup类型规则为WeightCategory成功",
			id:           2,
			productId:    200,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          2,
				ProductId:   200,
				RoutingType: cc_routing_rule.CCRoutingTypeShopGroup, // ShopGroup类型
			},
			mockRules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 2001, MinWeight: 0, MaxWeight: 2000, CustomsClearance: "CC2"},
			},
			mockDefaultCC: "CC_DEFAULT_2",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:         "仅验证模式成功",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: true,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeWeightCategory,
			},
			mockRules:     []cc_routing_rule.WeightCategoryRuleItem{{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"}},
			mockDefaultCC: "CC_DEFAULT",
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试service
			svc := new(WeightCategoryRuleUpdateTestService)
			svc.t = t
			svc.mockRule = tt.mockRule
			svc.mockGetRuleErr = tt.mockGetRuleErr
			svc.mockRules = tt.mockRules
			svc.mockDefaultCC = tt.mockDefaultCC
			svc.mockUpdateErr = tt.mockUpdateErr
			svc.expectedId = tt.id
			svc.expectedProductId = tt.productId
			svc.expectedRoutingType = cc_routing_rule.CCRoutingTypeWeightCategory

			// 执行测试
			err := svc.UpdateWeightCategoryRules(context.Background(), tt.id, tt.productId, tt.fileContent, "test_operator", tt.validateOnly)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}

// WeightCategoryRuleTestService 是用于测试的CCRoutingService实现
type WeightCategoryRuleTestService struct {
	t *testing.T

	// 模拟数据
	mockRules      []cc_routing_rule.WeightCategoryRuleItem
	mockDefaultCC  string
	mockParseError *srerr.Error
	mockCreateErr  *srerr.Error

	// 期望值
	expectedProductId   int
	expectedRoutingType cc_routing_rule.CCRoutingType
}

func (s *WeightCategoryRuleTestService) parseWeightCategoryRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.WeightCategoryRuleItem, string, *srerr.Error) {
	if s.mockParseError != nil {
		return nil, "", s.mockParseError
	}
	return s.mockRules, s.mockDefaultCC, nil
}

func (s *WeightCategoryRuleTestService) createOrUpdateRule(ctx context.Context, productId int, routingType cc_routing_rule.CCRoutingType, ruleDetail cc_routing_rule.CCRoutingRuleDetail, operator string) *srerr.Error {
	assert.Equal(s.t, s.expectedProductId, productId)
	assert.Equal(s.t, s.expectedRoutingType, routingType)
	return s.mockCreateErr
}

// 实现CCRoutingService必要的方法
func (s *WeightCategoryRuleTestService) ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 解析Excel/CSV文件
	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := s.parseWeightCategoryRules(ctx, nil) // 只用于测试，实际解析交给mock
	if parseErr != nil {
		return parseErr
	}

	// 构建规则详情
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		WeightCategoryRuleDetail: cc_routing_rule.WeightCategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 如果只是验证模式，到此为止，不实际保存
	if validateOnly {
		return nil
	}

	// 创建或更新规则
	return s.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeWeightCategory, ruleDetail, operator)
}

// 未使用的方法，但需要实现接口
func (s *WeightCategoryRuleTestService) CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryId int) (string, *srerr.Error) {
	return "", nil
}
func (s *WeightCategoryRuleTestService) CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error) {
	return 0, nil
}
func (s *WeightCategoryRuleTestService) UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *WeightCategoryRuleTestService) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *WeightCategoryRuleTestService) DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error) {
	return nil, 0, nil
}
func (s *WeightCategoryRuleTestService) CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return nil, nil
}
func (s *WeightCategoryRuleTestService) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleTestService) GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error) {
	return nil, "", nil
}
func (s *WeightCategoryRuleTestService) ListCCs(ctx context.Context, productId int, category int64, regionId string) ([]*CCListItem, *srerr.Error) {
	return nil, nil
}

// WeightCategoryRuleUpdateTestService 是用于测试UpdateWeightCategoryRules的CCRoutingService实现
type WeightCategoryRuleUpdateTestService struct {
	t *testing.T

	// 模拟数据
	mockRule       *cc_routing_rule.CCRoutingRule
	mockGetRuleErr *srerr.Error
	mockRules      []cc_routing_rule.WeightCategoryRuleItem
	mockDefaultCC  string
	mockUpdateErr  *srerr.Error

	// 期望值
	expectedId          int
	expectedProductId   int
	expectedRoutingType cc_routing_rule.CCRoutingType
}

func (s *WeightCategoryRuleUpdateTestService) GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	assert.Equal(s.t, s.expectedId, id)
	if s.mockGetRuleErr != nil {
		return nil, s.mockGetRuleErr
	}
	return s.mockRule, nil
}

func (s *WeightCategoryRuleUpdateTestService) parseWeightCategoryRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.WeightCategoryRuleItem, string, *srerr.Error) {
	return s.mockRules, s.mockDefaultCC, nil
}

func (s *WeightCategoryRuleUpdateTestService) UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error {
	assert.Equal(s.t, s.expectedId, rule.Id)
	assert.Equal(s.t, s.expectedProductId, rule.ProductId)
	assert.Equal(s.t, s.expectedRoutingType, rule.RoutingType)
	return s.mockUpdateErr
}

// 实现CCRoutingService必要的方法
func (s *WeightCategoryRuleUpdateTestService) UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 先检查规则是否存在
	_, err := s.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// 注意：不再检查规则类型是否匹配，允许更新任何类型的规则

	// 调用共用的解析逻辑
	rules, defaultCC, _ := s.parseWeightCategoryRules(ctx, nil)

	// 如果只是验证，到这里就返回
	if validateOnly {
		return nil
	}

	// 更新规则
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		WeightCategoryRuleDetail: cc_routing_rule.WeightCategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 更新规则
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeWeightCategory,
		RuleDetail:  ruleDetail,
	}
	return s.UpdateCCRoutingRule(ctx, ruleObj, operator)
}

// 未使用的方法，但需要实现接口
func (s *WeightCategoryRuleUpdateTestService) CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryId int) (string, *srerr.Error) {
	return "", nil
}
func (s *WeightCategoryRuleUpdateTestService) CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error) {
	return 0, nil
}
func (s *WeightCategoryRuleUpdateTestService) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *WeightCategoryRuleUpdateTestService) DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error) {
	return nil, 0, nil
}
func (s *WeightCategoryRuleUpdateTestService) CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return nil, nil
}
func (s *WeightCategoryRuleUpdateTestService) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *WeightCategoryRuleUpdateTestService) GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error) {
	return nil, "", nil
}
func (s *WeightCategoryRuleUpdateTestService) ListCCs(ctx context.Context, productId int, category int64, regionId string) ([]*CCListItem, *srerr.Error) {
	return nil, nil
}
