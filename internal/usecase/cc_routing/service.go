package cc_routing

import (
	"bytes"
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type CCRoutingService interface {
	CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryId int) (string, *srerr.Error)
	CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error)
	UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error
	GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error)
	GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error)
	DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error
	ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error)
	CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error)

	// 批量导入相关方法
	ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error
	ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error
	ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error

	// 批量更新相关方法
	UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error
	UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error
	UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error
	GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error)
}

// CCListItem CC列表项
type CCListItem struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type CCRoutingServiceImpl struct {
	CCRoutingRepo cc_routing_rule.CCRoutingRuleRepo
	CCApi         ccclient.CCApi
	LPSApi        lpsclient.LpsApi
}

func NewCCRoutingServiceImpl(
	ccRoutingRuleRepo cc_routing_rule.CCRoutingRuleRepo,
	ccApi ccclient.CCApi,
	lpsApi lpsclient.LpsApi) *CCRoutingServiceImpl {
	return &CCRoutingServiceImpl{
		CCRoutingRepo: ccRoutingRuleRepo,
		CCApi:         ccApi,
		LPSApi:        lpsApi,
	}
}

func (c *CCRoutingServiceImpl) CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryId int) (string, *srerr.Error) {
	ruleVal, err := localcache.Get(ctx, constant.CCRoutingRule, strconv.Itoa(productId))
	if err != nil {
		return "", srerr.With(srerr.CCRoutingRuleNotFound, productId, err)
	}

	routingRule, ok := ruleVal.(*cc_routing_rule.CCRoutingRule)
	if !ok {
		return "", srerr.New(srerr.CCRoutingRuleNotFound, productId, "convert local cache interface to rule fail")
	}

	logger.CtxLogDebugf(ctx, "rule %s", objutil.JsonString(routingRule))

	var result string

	switch routingRule.RoutingType {
	case cc_routing_rule.CCRoutingTypeFixed:
		if objutil.ContainsString(ccList, routingRule.RuleDetail.FixedRuleDetail.FixedCustomsClearance) {
			result = routingRule.RuleDetail.FixedRuleDetail.FixedCustomsClearance
		} else {
			return "", srerr.New(srerr.CCRoutingFailed, productId, "fixed cc not in available cc list")
		}
	case cc_routing_rule.CCRoutingTypeWeight:
		for _, ruleItem := range routingRule.RuleDetail.WeightRuleDetail.RuleList {
			if weight > ruleItem.MinWeight && weight <= ruleItem.MaxWeight {
				result = ruleItem.CustomsClearance
				logger.CtxLogInfof(ctx, "min weight: %d, max weight: %d, order weight: %d", ruleItem.MinWeight, ruleItem.MaxWeight, weight)
				break
			}
		}
		if result == "" || !objutil.ContainsString(ccList, result) {
			return "", srerr.New(srerr.CCRoutingFailed, productId, "cc not in available cc list")
		}
	case cc_routing_rule.CCRoutingTypeShopGroup:
		// 店铺分组路由
		r, err := c.routeByShopGroup(ctx, routingRule, shopId, ccList, productId)
		if err != nil {
			return "", err
		}
		result = r
	case cc_routing_rule.CCRoutingTypeCategory:
		// 类目路由
		r, err := c.routeByCategory(ctx, routingRule, categoryId, ccList, productId)
		if err != nil {
			return "", err
		}
		result = r
	case cc_routing_rule.CCRoutingTypeWeightCategory:
		// 重量+类目路由
		r, err := c.routeByWeightCategory(ctx, routingRule, weight, categoryId, ccList, productId)
		if err != nil {
			return "", err
		}
		result = r
	default:
		return "", srerr.New(srerr.CCRoutingFailed, productId, "unsupported routing type")
	}

	return result, nil
}

func (c *CCRoutingServiceImpl) GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	ruleTab, err := c.CCRoutingRepo.GetCCRoutingRuleByID(ctx, id)
	if err != nil {
		return nil, err
	}

	routingRule := cc_routing_rule.CCRoutingRule{
		Id:          ruleTab.Id,
		ProductId:   ruleTab.ProductId,
		RoutingType: ruleTab.RoutingType,
	}

	if err := jsoniter.UnmarshalFromString(ruleTab.RuleDetail, &routingRule.RuleDetail); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal rule detail failed|err=%v", err)
		return nil, srerr.With(srerr.DataErr, ruleTab.RuleDetail, err)
	}

	return &routingRule, nil
}

func (c *CCRoutingServiceImpl) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	ruleTab, err := c.CCRoutingRepo.GetCCRoutingRuleByProductId(ctx, productId)
	if err != nil {
		return nil, err
	}

	routingRule := cc_routing_rule.CCRoutingRule{
		Id:          ruleTab.Id,
		ProductId:   ruleTab.ProductId,
		RoutingType: ruleTab.RoutingType,
	}

	if err := jsoniter.UnmarshalFromString(ruleTab.RuleDetail, &routingRule.RuleDetail); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal rule detail failed|err=%v", err)
		return nil, srerr.With(srerr.DataErr, ruleTab.RuleDetail, err)
	}

	return &routingRule, nil
}

func (c *CCRoutingServiceImpl) DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error {
	err := c.CCRoutingRepo.DeleteRoutingRule(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (c *CCRoutingServiceImpl) CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error) {
	existRule, err := c.CCRoutingRepo.GetCCRoutingRuleByProductId(ctx, rule.ProductId)
	if err == nil {
		return 0, srerr.New(srerr.CCRoutingRuleExist, rule.ProductId, "exist cc routing rule id: %d", existRule.Id)
	}

	ruleTab := &cc_routing_rule.CCRoutingRuleTab{
		ProductId:   rule.ProductId,
		RoutingType: rule.RoutingType,
		OperatedBy:  operator,
	}
	ruleDetail, jErr := jsoniter.MarshalToString(rule.RuleDetail)
	if jErr != nil {
		return 0, srerr.With(srerr.JsonErr, rule.RuleDetail, jErr)
	}
	ruleTab.RuleDetail = ruleDetail
	ruleId, err := c.CCRoutingRepo.CreateRoutingRule(ctx, ruleTab)
	if err != nil {
		return 0, err
	}

	return ruleId, nil
}

func (c *CCRoutingServiceImpl) UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error {
	ruleDetail, jErr := jsoniter.MarshalToString(rule.RuleDetail)
	if jErr != nil {
		return srerr.With(srerr.JsonErr, rule.RuleDetail, jErr)
	}
	updateData := map[string]interface{}{
		"routing_type": rule.RoutingType,
		"rule_detail":  ruleDetail,
		"operated_by":  operator,
	}
	if err := c.CCRoutingRepo.UpdateCCRoutingRuleByID(ctx, rule.Id, updateData); err != nil {
		return err
	}

	return nil
}

func (c *CCRoutingServiceImpl) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error) {
	list, count, err := c.CCRoutingRepo.ListCCRoutingRule(ctx, condition, offset, size)
	if err != nil {
		return nil, 0, err
	}
	ruleList := make([]cc_routing_rule.CCRoutingRule, 0, len(list))
	for _, item := range list {
		ruleList = append(ruleList, cc_routing_rule.CCRoutingRule{
			Id:          item.Id,
			ProductId:   item.ProductId,
			RoutingType: item.RoutingType,
		})
	}

	return ruleList, count, nil
}

// @core
func (c *CCRoutingServiceImpl) CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	var needCCFilter bool
	for _, availableLane := range availableLanes {
		if len(availableLane.CustomsClearanceList) > 0 {
			needCCFilter = true
			break
		}
	}

	if !needCCFilter {
		return availableLanes, nil
	}

	logger.CtxLogInfof(ctx, "lane cc list no empty, need cc filter")

	targetCC, hasPreAuth, err := c.CCApi.GetDeclarationResult(ctx, orderSn)
	if err != nil {
		return nil, srerr.With(srerr.GetCCResultFailed, orderSn, err)
	}

	// if order has not pre auth before, needn't cc filter, all lanes is available
	if !hasPreAuth {
		return availableLanes, nil
	}

	var ret []*rule.RoutingLaneInfo
	for _, availableLane := range availableLanes {
		if objutil.ContainsString(availableLane.CustomsClearanceList, targetCC) {
			ret = append(ret, availableLane)
		}
	}

	if len(ret) == 0 {
		return nil, srerr.New(srerr.NoAvailableLaneAfterCCFilter, targetCC, "no available lane after cc filter, target cc: %s", targetCC)
	}

	return ret, nil
}

// routeByShopGroup 按店铺分组路由
func (c *CCRoutingServiceImpl) routeByShopGroup(ctx context.Context, routingRule *cc_routing_rule.CCRoutingRule, shopId int64, ccList []string, productId int) (string, *srerr.Error) {
	if shopId == 0 {
		return "", srerr.New(srerr.CCRoutingFailed, productId, "shop_id is required for shop group routing")
	}

	shopGroupDetail := routingRule.RuleDetail.ShopGroupRuleDetail

	// 获取店铺所属的分组
	shopGroupIds, err := c.LPSApi.GetShopGroupIdsByShopIdsAndTag(ctx, []int64{shopId}, lpsclient.ClientTagCCAllocation)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get shop group for shop_id=%d, err=%v", shopId, err)
		// 降级使用默认CC
		if shopGroupDetail.DefaultCustomsClearance != "" && objutil.ContainsString(ccList, shopGroupDetail.DefaultCustomsClearance) {
			return shopGroupDetail.DefaultCustomsClearance, nil
		}
		return "", srerr.New(srerr.CCRoutingFailed, productId, "failed to get shop group and no valid default cc")
	}

	if len(shopGroupIds) == 0 {
		// 使用默认CC
		if shopGroupDetail.DefaultCustomsClearance != "" && objutil.ContainsString(ccList, shopGroupDetail.DefaultCustomsClearance) {
			return shopGroupDetail.DefaultCustomsClearance, nil
		}
		return "", srerr.New(srerr.CCRoutingFailed, productId, "shop has no group and no valid default cc")
	}

	// 查找匹配的规则
	shopGroupId := shopGroupIds[0]
	for _, rule := range shopGroupDetail.Rules {
		if rule.ClientTagId == uint8(lpsclient.ClientTagCCAllocation) && rule.ClientGroupId == shopGroupId {
			if objutil.ContainsString(ccList, rule.CustomsClearance) {
				return rule.CustomsClearance, nil
			}
			logger.CtxLogErrorf(ctx, "Matched CC %s not in available list %v", rule.CustomsClearance, ccList)
			break
		}
	}

	// 未匹配到规则，使用默认CC
	if shopGroupDetail.DefaultCustomsClearance != "" && objutil.ContainsString(ccList, shopGroupDetail.DefaultCustomsClearance) {
		return shopGroupDetail.DefaultCustomsClearance, nil
	}

	return "", srerr.New(srerr.CCRoutingFailed, productId, "no matching shop group rule and no valid default cc")
}

// routeByCategory 按类目路由
func (c *CCRoutingServiceImpl) routeByCategory(ctx context.Context, routingRule *cc_routing_rule.CCRoutingRule, categoryId int, ccList []string, productId int) (string, *srerr.Error) {
	if categoryId == 0 {
		return "", srerr.New(srerr.CCRoutingFailed, productId, "category_id is required for category routing")
	}

	categoryDetail := routingRule.RuleDetail.CategoryRuleDetail

	// 查找匹配的类目规则
	for _, rule := range categoryDetail.Rules {
		if rule.CategoryId == categoryId {
			if objutil.ContainsString(ccList, rule.CustomsClearance) {
				return rule.CustomsClearance, nil
			}
			logger.CtxLogErrorf(ctx, "Matched CC %s not in available list %v", rule.CustomsClearance, ccList)
			break
		}
	}

	// 未匹配到规则，使用默认CC
	if categoryDetail.DefaultCustomsClearance != "" && objutil.ContainsString(ccList, categoryDetail.DefaultCustomsClearance) {
		return categoryDetail.DefaultCustomsClearance, nil
	}

	return "", srerr.New(srerr.CCRoutingFailed, productId, "no matching category rule and no valid default cc")
}

// routeByWeightCategory 按重量+类目路由
func (c *CCRoutingServiceImpl) routeByWeightCategory(ctx context.Context, routingRule *cc_routing_rule.CCRoutingRule, weight int, categoryId int, ccList []string, productId int) (string, *srerr.Error) {
	if categoryId == 0 {
		return "", srerr.New(srerr.CCRoutingFailed, productId, "category_id is required for weight+category routing")
	}

	weightCategoryDetail := routingRule.RuleDetail.WeightCategoryRuleDetail

	// 查找匹配的重量+类目规则
	for _, rule := range weightCategoryDetail.Rules {
		if rule.CategoryId == categoryId && weight > rule.MinWeight && weight <= rule.MaxWeight {
			if objutil.ContainsString(ccList, rule.CustomsClearance) {
				logger.CtxLogInfof(ctx, "Matched weight+category rule: category=%d, min=%d, max=%d, weight=%d, cc=%s",
					rule.CategoryId, rule.MinWeight, rule.MaxWeight, weight, rule.CustomsClearance)
				return rule.CustomsClearance, nil
			}
			logger.CtxLogErrorf(ctx, "Matched CC %s not in available list %v", rule.CustomsClearance, ccList)
			break
		}
	}

	// 未匹配到规则，使用默认CC
	if weightCategoryDetail.DefaultCustomsClearance != "" && objutil.ContainsString(ccList, weightCategoryDetail.DefaultCustomsClearance) {
		return weightCategoryDetail.DefaultCustomsClearance, nil
	}

	return "", srerr.New(srerr.CCRoutingFailed, productId, "no matching weight+category rule and no valid default cc")
}

// GenerateTemplate 生成指定路由类型的模板内容
func (c *CCRoutingServiceImpl) GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error) {
	// 获取真实的CC列表作为模板示例
	ccAgents, err := c.CCApi.ListCustomsAgents(ctx)
	if err != nil {
		return nil, "", srerr.With(srerr.CCRoutingFailed, nil, err)
	}
	ccList := make([]*CCListItem, 0, len(ccAgents))
	for _, agent := range ccAgents {
		ccList = append(ccList, &CCListItem{
			Id:   agent.AgentId,
			Name: agent.AgentName,
		})
	}

	switch routingType {
	case cc_routing_rule.CCRoutingTypeShopGroup:
		return c.generateShopGroupTemplate(ccList)
	case cc_routing_rule.CCRoutingTypeCategory:
		return c.generateCategoryTemplate(ccList)
	case cc_routing_rule.CCRoutingTypeWeightCategory:
		return c.generateWeightCategoryTemplate(ccList)
	default:
		return nil, "", srerr.New(srerr.ParamErr, nil, "unsupported template routing type: %d", routingType)
	}
}

func (c *CCRoutingServiceImpl) generateShopGroupTemplate(ccList []*CCListItem) ([]byte, string, *srerr.Error) {
	return c.generateExcelTemplateWithValidation(
		"shop_group_template.xlsx",
		[]string{"Client Group", "Allocate to"},
		[][]string{
			{"Default", ""},
		},
		1, // Allocate to列索引 (0-based)
		ccList,
	)
}

func (c *CCRoutingServiceImpl) generateCategoryTemplate(ccList []*CCListItem) ([]byte, string, *srerr.Error) {
	return c.generateExcelTemplateWithValidation(
		"category_template.xlsx",
		[]string{"Global Category ID", "Allocate to"},
		[][]string{
			{"Default", ""},
		},
		1, // Allocate to列索引 (0-based)
		ccList,
	)
}

func (c *CCRoutingServiceImpl) generateWeightCategoryTemplate(ccList []*CCListItem) ([]byte, string, *srerr.Error) {
	return c.generateExcelTemplateWithValidation(
		"weight_category_template.xlsx",
		[]string{"Category ID", "Weight Min (Not included)", "Weight Max (Included)", "Allocate to"},
		[][]string{
			{"Default", "", "", ""},
		},
		3, // Allocate to列索引 (0-based)
		ccList,
	)
}

// generateExcelTemplateWithValidation 生成带数据验证下拉框的Excel模板
func (c *CCRoutingServiceImpl) generateExcelTemplateWithValidation(filename string, headers []string, rows [][]string, validateColumnIndex int, ccList []*CCListItem) ([]byte, string, *srerr.Error) {
	file := excelize.NewFile()
	sheetName := "Template"

	// 删除默认的Sheet1，创建新的模板表
	file.DeleteSheet("Sheet1")
	file.NewSheet(sheetName)
	file.SetActiveSheet(file.GetSheetIndex(sheetName))

	// 写入表头
	for i, header := range headers {
		cellName, _ := excelize.CoordinatesToCellName(i+1, 1)
		file.SetCellValue(sheetName, cellName, header)

		// 设置表头样式
		style, _ := file.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true, Size: 12},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"#E0E0E0"}, Pattern: 1},
		})
		file.SetCellStyle(sheetName, cellName, cellName, style)
	}

	// 写入示例数据
	for rowIndex, row := range rows {
		for colIndex, value := range row {
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			file.SetCellValue(sheetName, cellName, value)

			// 为Default行设置红色字体样式
			if value == "Default" {
				defaultStyle, _ := file.NewStyle(&excelize.Style{
					Font: &excelize.Font{Color: "#FF0000", Bold: true},
				})
				file.SetCellStyle(sheetName, cellName, cellName, defaultStyle)
			}
		}
	}

	// 为AllocateTo列添加数据验证下拉框
	if len(ccList) > 0 {
		// 创建CC选项列表
		ccOptions := make([]string, 0, len(ccList))
		for _, cc := range ccList {
			ccOptions = append(ccOptions, cc.Id)
		}

		// 设置数据验证范围（从第2行开始到第1000行，覆盖足够的范围）
		validateColLetter := string(rune('A' + validateColumnIndex))
		validateRange := fmt.Sprintf("%s2:%s1000", validateColLetter, validateColLetter)

		// 创建数据验证规则
		dvRange := excelize.NewDataValidation(true)
		dvRange.Sqref = validateRange
		dvRange.SetDropList(ccOptions)
		dvRange.SetError(excelize.DataValidationErrorStyleStop, "Invalid CC", "Please select a valid CC from the dropdown list")

		// 应用数据验证
		file.AddDataValidation(sheetName, dvRange)
	}

	// 创建说明表
	instructionSheet := "Instructions"
	file.NewSheet(instructionSheet)

	// 写入说明内容
	instructions := [][]string{
		{"CC Routing Import Template Instructions", ""},
		{"", ""},
		{"Available CC Options:", "Description"},
	}

	for _, cc := range ccList {
		instructions = append(instructions, []string{cc.Id, cc.Name})
	}

	instructions = append(instructions, []string{""}, []string{"Notes:", ""})
	instructions = append(instructions, []string{"1. Please select CC values from the dropdown in 'Allocate to' column", ""})
	instructions = append(instructions, []string{"2. The 'Default' row (in red) is mandatory and cannot be deleted", ""})
	instructions = append(instructions, []string{"3. Add additional rows as needed for specific rules", ""})
	instructions = append(instructions, []string{"4. All fields are required except weight fields for Default row", ""})

	for rowIndex, instruction := range instructions {
		for colIndex, text := range instruction {
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+1)
			file.SetCellValue(instructionSheet, cellName, text)
		}
	}

	// 设置列宽
	file.SetColWidth(sheetName, "A", "Z", 20)
	file.SetColWidth(instructionSheet, "A", "B", 50)

	// 导出为字节数组
	var buf bytes.Buffer
	if err := file.Write(&buf); err != nil {
		return nil, "", srerr.With(srerr.FormatErr, nil, err)
	}

	return buf.Bytes(), filename, nil
}

// createOrUpdateRule 创建或更新规则
func (c *CCRoutingServiceImpl) createOrUpdateRule(ctx context.Context, productId int, routingType cc_routing_rule.CCRoutingType, ruleDetail cc_routing_rule.CCRoutingRuleDetail, operator string) *srerr.Error {
	// 检查是否已存在规则
	existingRule, err := c.GetCCRoutingRuleByProductId(ctx, productId)
	if err != nil && err.GetCode() != srerr.CCRoutingRuleNotFound.Code() {
		return err
	}

	rule := &cc_routing_rule.CCRoutingRule{
		ProductId:   productId,
		RoutingType: routingType,
		RuleDetail:  ruleDetail,
	}

	if existingRule != nil {
		// 更新现有规则
		rule.Id = existingRule.Id
		logger.CtxLogInfof(ctx, "Updating existing CC routing rule for productId=%d, ruleId=%d", productId, rule.Id)
		return c.UpdateCCRoutingRule(ctx, rule, operator)
	} else {
		// 创建新规则
		logger.CtxLogInfof(ctx, "Creating new CC routing rule for productId=%d", productId)
		_, err := c.CreateCCRoutingRule(ctx, rule, operator)
		return err
	}
}

// validateWeightRanges 验证重量区间的连续性
func (c *CCRoutingServiceImpl) validateWeightRanges(rules []cc_routing_rule.WeightCategoryRuleItem) *srerr.Error {
	// 按CategoryId分组
	categoryGroups := make(map[int][]cc_routing_rule.WeightCategoryRuleItem)
	for _, rule := range rules {
		categoryGroups[rule.CategoryId] = append(categoryGroups[rule.CategoryId], rule)
	}

	// 验证每个类目的区间连续性
	for categoryId, categoryRules := range categoryGroups {
		if len(categoryRules) <= 1 {
			continue
		}

		// 按MinWeight排序
		for i := 0; i < len(categoryRules)-1; i++ {
			for j := i + 1; j < len(categoryRules); j++ {
				if categoryRules[i].MinWeight > categoryRules[j].MinWeight {
					categoryRules[i], categoryRules[j] = categoryRules[j], categoryRules[i]
				}
			}
		}

		// 检查相邻区间
		for i := 0; i < len(categoryRules)-1; i++ {
			curr := categoryRules[i]
			next := categoryRules[i+1]

			if curr.MaxWeight > next.MinWeight {
				return srerr.New(srerr.ParamErr, nil,
					"category %d: weight range overlapped - range [%d,%d] (CC: %s) overlaps with [%d,%d] (CC: %s). Please ensure weight ranges are non-overlapping within the same category",
					categoryId, curr.MinWeight, curr.MaxWeight, curr.CustomsClearance,
					next.MinWeight, next.MaxWeight, next.CustomsClearance)
			}
			if curr.MaxWeight < next.MinWeight {
				return srerr.New(srerr.ParamErr, nil,
					"category %d: weight range incomplete - gap between [%d,%d] (CC: %s) and [%d,%d] (CC: %s). Missing range: (%d,%d). Please ensure weight ranges are continuous within the same category",
					categoryId, curr.MinWeight, curr.MaxWeight, curr.CustomsClearance,
					next.MinWeight, next.MaxWeight, next.CustomsClearance,
					curr.MaxWeight, next.MinWeight)
			}
		}
	}

	return nil
}

// validateCCValues 校验 CC 值是否在可用列表中（可选校验）
func (c *CCRoutingServiceImpl) validateCCValues(ctx context.Context, ccValues []string, enableValidation bool) *srerr.Error {
	if !enableValidation || len(ccValues) == 0 {
		return nil
	}

	// 获取所有可用的CC列表
	availableCCs, err := c.CCApi.ListCustomsAgents(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to get available CC list for validation, err=%v", err)
		// 如果获取CC列表失败，可以选择跳过验证（降级策略）或返回错误
		// 这里选择跳过验证，避免因CC服务异常导致导入失败
		logger.CtxLogInfof(ctx, "Skipping CC validation due to CC service unavailable")
		return nil
	}

	// 构建可用CC的映射，便于快速查找
	availableCCMap := make(map[string]bool)
	for _, cc := range availableCCs {
		availableCCMap[cc.AgentId] = true
	}

	// 校验每个CC值
	var invalidCCs []string
	for _, ccValue := range ccValues {
		if ccValue != "" && !availableCCMap[ccValue] {
			invalidCCs = append(invalidCCs, ccValue)
		}
	}

	if len(invalidCCs) > 0 {
		return srerr.New(srerr.ParamErr, nil, "invalid CC values not in available list: %v", invalidCCs)
	}

	return nil
}
