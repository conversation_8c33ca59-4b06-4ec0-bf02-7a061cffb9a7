package cc_routing

import (
	"context"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestValidateCCValues 测试CC值验证函数
func TestValidateCCValues(t *testing.T) {
	tests := []struct {
		name             string
		ccValues         []string
		enableValidation bool
		mockCCResponse   []*ccclient.CustomsAgent
		mockCCErr        error
		expectError      bool
	}{
		{
			name:             "验证禁用",
			ccValues:         []string{"CC1", "CC2"},
			enableValidation: false,
			mockCCResponse:   nil,
			mockCCErr:        nil,
			expectError:      false,
		},
		{
			name:             "空CC值列表",
			ccValues:         []string{},
			enableValidation: true,
			mockCCResponse:   nil,
			mockCCErr:        nil,
			expectError:      false,
		},
		{
			name:             "CC API返回错误",
			ccValues:         []string{"CC1", "CC2"},
			enableValidation: true,
			mockCCResponse:   nil,
			mockCCErr:        assert.AnError,
			expectError:      false, // 服务降级，不返回错误
		},
		{
			name:             "所有CC值有效",
			ccValues:         []string{"CC1", "CC2"},
			enableValidation: true,
			mockCCResponse:   []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}},
			mockCCErr:        nil,
			expectError:      false,
		},
		{
			name:             "包含无效的CC值",
			ccValues:         []string{"CC1", "INVALID_CC"},
			enableValidation: true,
			mockCCResponse:   []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}},
			mockCCErr:        nil,
			expectError:      true,
		},
		{
			name:             "包含空字符串",
			ccValues:         []string{"CC1", ""},
			enableValidation: true,
			mockCCResponse:   []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}},
			mockCCErr:        nil,
			expectError:      false, // 空字符串被忽略
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock对象
			mockCCApi := new(MockCCApi)

			// 设置mock行为
			if tt.enableValidation && len(tt.ccValues) > 0 {
				var srErr *srerr.Error
				if tt.mockCCErr != nil {
					srErr = srerr.New(srerr.CustomsServiceErr, nil, tt.mockCCErr.Error())
				}
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(tt.mockCCResponse, srErr)
			}

			// 创建服务实例
			service := &CCRoutingServiceImpl{
				CCApi: mockCCApi,
			}

			// 执行测试
			err := service.validateCCValues(context.Background(), tt.ccValues, tt.enableValidation)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}

			// 验证mock调用
			mockCCApi.AssertExpectations(t)
		})
	}
}

func TestValidateWeightRanges(t *testing.T) {
	tests := []struct {
		name        string
		rules       []cc_routing_rule.WeightCategoryRuleItem
		expectError bool
	}{
		{
			name:        "空规则列表",
			rules:       []cc_routing_rule.WeightCategoryRuleItem{},
			expectError: false,
		},
		{
			name: "单个有效规则",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
			},
			expectError: false,
		},
		{
			name: "连续无间隙的多个规则",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1001, MinWeight: 1000, MaxWeight: 2000, CustomsClearance: "CC2"},
				{CategoryId: 1001, MinWeight: 2000, MaxWeight: 3000, CustomsClearance: "CC3"},
			},
			expectError: false,
		},
		{
			name: "规则中存在间隙",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1001, MinWeight: 1500, MaxWeight: 2000, CustomsClearance: "CC2"}, // 间隙
			},
			expectError: true,
		},
		{
			name: "规则中存在重叠",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1500, CustomsClearance: "CC1"},
				{CategoryId: 1001, MinWeight: 1000, MaxWeight: 2000, CustomsClearance: "CC2"}, // 重叠
			},
			expectError: true,
		},
		{
			name: "最小值大于等于最大值",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 1000, MaxWeight: 1000, CustomsClearance: "CC1"}, // 无效区间
			},
			expectError: true,
		},
		{
			name: "多个类目混合 - 有效",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1001, MinWeight: 1000, MaxWeight: 2000, CustomsClearance: "CC2"},
				{CategoryId: 1002, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1002, MinWeight: 1000, MaxWeight: 2000, CustomsClearance: "CC2"},
			},
			expectError: false,
		},
		{
			name: "多个类目混合 - 一个类目有间隙",
			rules: []cc_routing_rule.WeightCategoryRuleItem{
				{CategoryId: 1001, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1001, MinWeight: 1000, MaxWeight: 2000, CustomsClearance: "CC2"},
				{CategoryId: 1002, MinWeight: 0, MaxWeight: 1000, CustomsClearance: "CC1"},
				{CategoryId: 1002, MinWeight: 1500, MaxWeight: 2000, CustomsClearance: "CC2"}, // 间隙
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试
			service := &CCRoutingServiceImpl{}

			// 执行测试
			err := service.validateWeightRanges(tt.rules)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}
