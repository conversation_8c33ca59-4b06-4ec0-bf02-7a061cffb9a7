package cc_routing

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// parseShopGroupRules 解析店铺分组规则数据，从导入和更新方法中抽取的共用逻辑
func (c *CCRoutingServiceImpl) parseShopGroupRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.ShopGroupRuleItem, string, *srerr.Error) {
	rules := make([]cc_routing_rule.ShopGroupRuleItem, 0, len(rows))
	var defaultCC string
	hasDefault := false

	for i, row := range rows {
		// 使用新的字段名
		clientGroupId := strings.TrimSpace(row["Client Group"])
		allocateTo := strings.TrimSpace(row["Allocate to"])

		if clientGroupId == "" || allocateTo == "" {
			return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: Client Group='%s' and Allocate to='%s' cannot be empty", i+1, clientGroupId, allocateTo)
		}

		// 检查是否是默认行
		if strings.ToLower(clientGroupId) == "default" {
			hasDefault = true
			defaultCC = allocateTo
		} else {
			rules = append(rules, cc_routing_rule.ShopGroupRuleItem{
				ClientTagId:      uint8(lpsclient.ClientTagCCAllocation), // 固定值，不从模板读取
				ClientGroupId:    clientGroupId,
				CustomsClearance: allocateTo,
			})
		}
	}

	if !hasDefault {
		return nil, "", srerr.New(srerr.ParamErr, nil, "missing required 'default' row")
	}

	// 检查重复的Client Group
	seen := make(map[string]bool)
	for _, rule := range rules {
		if seen[rule.ClientGroupId] {
			return nil, "", srerr.New(srerr.ParamErr, nil, "duplicate Client Group: %s", rule.ClientGroupId)
		}
		seen[rule.ClientGroupId] = true
	}

	// 强制CC值校验（新API默认安全）
	ccValues := make([]string, 0, len(rules)+1)
	for _, rule := range rules {
		ccValues = append(ccValues, rule.CustomsClearance)
	}
	if defaultCC != "" {
		ccValues = append(ccValues, defaultCC)
	}
	if err := c.validateCCValues(ctx, ccValues, true); err != nil {
		return nil, "", err
	}

	return rules, defaultCC, nil
}

// ImportShopGroupRules 导入店铺分组规则
func (c *CCRoutingServiceImpl) ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseShopGroupRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 构建规则详情
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		ShopGroupRuleDetail: cc_routing_rule.ShopGroupRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 如果只是验证模式，到此为止，不实际保存
	if validateOnly {
		logger.CtxLogInfof(ctx, "Shop group rules validation passed for productId=%d (validate-only mode)", productId)
		return nil
	}

	// 创建或更新规则
	return c.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeShopGroup, ruleDetail, operator)
}

// UpdateShopGroupRules 更新店铺分组规则
func (c *CCRoutingServiceImpl) UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 先检查规则是否存在
	_, err := c.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseShopGroupRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 如果只是验证，到这里就返回
	if validateOnly {
		logger.CtxLogInfof(ctx, "Shop group rules validation passed for ruleId=%d, productId=%d (validate-only mode)", id, productId)
		return nil
	}

	// 更新规则
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		ShopGroupRuleDetail: cc_routing_rule.ShopGroupRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 更新规则
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeShopGroup,
		RuleDetail:  ruleDetail,
	}
	return c.UpdateCCRoutingRule(ctx, ruleObj, operator)
}
