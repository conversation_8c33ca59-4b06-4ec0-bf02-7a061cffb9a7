package cc_routing

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/stretchr/testify/mock"
)

// MockCCApi 是 CCApi 接口的mock实现

type MockCCApi struct {
	mock.Mock
}

// GetDeclarationResult 实现CCApi接口的方法
func (m *MockCCApi) GetDeclarationResult(ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
	args := m.Called(ctx, orderSn)
	return args.String(0), args.Bool(1), args.Get(2).(*srerr.Error)
}

// ListCustomsAgents 实现CCApi接口的方法
func (m *MockCCApi) ListCustomsAgents(ctx context.Context) ([]*ccclient.CustomsAgent, *srerr.Error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		if args.Get(1) == nil {
			return nil, nil
		}
		return nil, args.Get(1).(*srerr.Error)
	}
	if args.Get(1) == nil {
		return args.Get(0).([]*ccclient.CustomsAgent), nil
	}
	return args.Get(0).([]*ccclient.CustomsAgent), args.Get(1).(*srerr.Error)
}

// MockCCRoutingRuleRepo 是 CCRoutingRuleRepo 接口的mock实现

type MockCCRoutingRuleRepo struct {
	mock.Mock
}

func (m *MockCCRoutingRuleRepo) GetCCRoutingRuleByID(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRuleTab, *srerr.Error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		if args.Get(1) == nil {
			return nil, nil
		}
		return nil, args.Get(1).(*srerr.Error)
	}
	if args.Get(1) == nil {
		return args.Get(0).(*cc_routing_rule.CCRoutingRuleTab), nil
	}
	return args.Get(0).(*cc_routing_rule.CCRoutingRuleTab), args.Get(1).(*srerr.Error)
}

func (m *MockCCRoutingRuleRepo) CreateRoutingRule(ctx context.Context, data *cc_routing_rule.CCRoutingRuleTab) (int, *srerr.Error) {
	args := m.Called(ctx, data)
	if args.Get(1) == nil {
		return args.Int(0), nil
	}
	return args.Int(0), args.Get(1).(*srerr.Error)
}

func (m *MockCCRoutingRuleRepo) UpdateCCRoutingRuleByID(ctx context.Context, id int, updateData map[string]interface{}) *srerr.Error {
	args := m.Called(ctx, id, updateData)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*srerr.Error)
}

func (m *MockCCRoutingRuleRepo) DeleteRoutingRule(ctx context.Context, id int) *srerr.Error {
	args := m.Called(ctx, id)
	return args.Get(0).(*srerr.Error)
}

func (m *MockCCRoutingRuleRepo) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRuleTab, int64, *srerr.Error) {
	args := m.Called(ctx, condition, offset, size)
	return args.Get(0).([]cc_routing_rule.CCRoutingRuleTab), args.Get(1).(int64), args.Get(2).(*srerr.Error)
}

func (m *MockCCRoutingRuleRepo) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRuleTab, *srerr.Error) {
	args := m.Called(ctx, productId)
	if args.Get(0) == nil {
		if args.Get(1) == nil {
			return nil, nil
		}
		return nil, args.Get(1).(*srerr.Error)
	}
	if args.Get(1) == nil {
		return args.Get(0).(*cc_routing_rule.CCRoutingRuleTab), nil
	}
	return args.Get(0).(*cc_routing_rule.CCRoutingRuleTab), args.Get(1).(*srerr.Error)
}
