package cc_routing

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// parseCategoryRules 解析类目规则数据，从导入和更新方法中抽取的共用逻辑
func (c *CCRoutingServiceImpl) parseCategoryRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.CategoryRuleItem, string, *srerr.Error) {
	rules := make([]cc_routing_rule.CategoryRuleItem, 0, len(rows))
	var defaultCC string
	hasDefault := false

	for i, row := range rows {
		categoryIdStr := strings.TrimSpace(row["Global Category ID"])
		allocateTo := strings.TrimSpace(row["Allocate to"])

		if categoryIdStr == "" || allocateTo == "" {
			return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: Global Category ID='%s' and Allocate to='%s' cannot be empty", i+1, categoryIdStr, allocateTo)
		}

		// 检查是否是默认行
		if strings.ToLower(categoryIdStr) == "default" {
			hasDefault = true
			defaultCC = allocateTo
		} else {
			categoryId, parseErr := strconv.Atoi(categoryIdStr)
			if parseErr != nil {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: invalid Global Category ID '%s'", i+1, categoryIdStr)
			}

			rules = append(rules, cc_routing_rule.CategoryRuleItem{
				CategoryId:       categoryId,
				CustomsClearance: allocateTo,
			})
		}
	}

	if !hasDefault {
		return nil, "", srerr.New(srerr.ParamErr, nil, "missing required 'default' row")
	}

	// 检查重复的Global Category ID
	seen := make(map[int]bool)
	for _, rule := range rules {
		if seen[rule.CategoryId] {
			return nil, "", srerr.New(srerr.ParamErr, nil, "duplicate Global Category ID: %d", rule.CategoryId)
		}
		seen[rule.CategoryId] = true
	}

	// 强制CC值校验
	ccValues := make([]string, 0, len(rules)+1)
	for _, rule := range rules {
		ccValues = append(ccValues, rule.CustomsClearance)
	}
	if defaultCC != "" {
		ccValues = append(ccValues, defaultCC)
	}
	if err := c.validateCCValues(ctx, ccValues, true); err != nil {
		return nil, "", err
	}

	return rules, defaultCC, nil
}

// ImportCategoryRules 导入类目规则
func (c *CCRoutingServiceImpl) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseCategoryRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 构建规则详情
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		CategoryRuleDetail: cc_routing_rule.CategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 如果只是验证模式，到此为止，不实际保存
	if validateOnly {
		logger.CtxLogInfof(ctx, "Category rules validation passed for productId=%d (validate-only mode)", productId)
		return nil
	}

	// 创建或更新规则
	return c.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeCategory, ruleDetail, operator)
}

// UpdateCategoryRules 更新类目规则
func (c *CCRoutingServiceImpl) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 先检查规则是否存在
	_, err := c.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseCategoryRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 如果只是验证，到这里就返回
	if validateOnly {
		logger.CtxLogInfof(ctx, "Category rules validation passed for ruleId=%d, productId=%d (validate-only mode)", id, productId)
		return nil
	}

	// 更新规则
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		CategoryRuleDetail: cc_routing_rule.CategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 更新规则
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeCategory,
		RuleDetail:  ruleDetail,
	}
	return c.UpdateCCRoutingRule(ctx, ruleObj, operator)
}
