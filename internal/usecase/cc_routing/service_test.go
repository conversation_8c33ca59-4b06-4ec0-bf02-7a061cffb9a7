package cc_routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestCCRoutingServiceImpl_CCFilter(t *testing.T) {
	ctx := context.Background()
	c := &CCRoutingServiceImpl{}

	var patch *gomonkey.Patches
	type args struct {
		orderSn        string
		availableLanes []*rule.RoutingLaneInfo
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.RoutingLaneInfo
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: no needCCFilter",
			args:    args{},
			want:    []*rule.RoutingLaneInfo{},
			wantErr: nil,
			setup:   func() { c = &CCRoutingServiceImpl{} },
		},
		{
			name: "case 2: GetCCResultFailed",
			args: args{
				availableLanes: []*rule.RoutingLaneInfo{
					{
						CustomsClearanceList: []string{"customs_clearance_1"},
					},
				},
			},
			want:    nil,
			wantErr: srerr.With(srerr.GetCCResultFailed, "", srerr.New(srerr.GetCCResultFailed, "", "")),
			setup: func() {
				c = &CCRoutingServiceImpl{
					CCApi: &ccclient.CCApiImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(c.CCApi), "GetDeclarationResult", func(c *ccclient.CCApiImpl,
					ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
					return "", false, srerr.New(srerr.GetCCResultFailed, "", "")
				})
			},
		},
		{
			name: "case 3: no hasPreAuth",
			args: args{
				availableLanes: []*rule.RoutingLaneInfo{
					{
						CustomsClearanceList: []string{"customs_clearance_1"},
					},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					CustomsClearanceList: []string{"customs_clearance_1"},
				},
			},
			wantErr: nil,
			setup: func() {
				c = &CCRoutingServiceImpl{
					CCApi: &ccclient.CCApiImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(c.CCApi), "GetDeclarationResult", func(c *ccclient.CCApiImpl,
					ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
					return "", false, nil
				})
			},
		},
		{
			name: "case 4: NoAvailableLaneAfterCCFilter",
			args: args{
				availableLanes: []*rule.RoutingLaneInfo{
					{
						CustomsClearanceList: []string{"customs_clearance_1"},
					},
				},
			},
			want:    nil,
			wantErr: srerr.New(srerr.NoAvailableLaneAfterCCFilter, "", "no available lane after cc filter, target cc: %s", ""),
			setup: func() {
				c = &CCRoutingServiceImpl{
					CCApi: &ccclient.CCApiImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(c.CCApi), "GetDeclarationResult", func(c *ccclient.CCApiImpl,
					ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
					return "", true, nil
				})
			},
		},
		{
			name: "case 5: ret != 0",
			args: args{
				availableLanes: []*rule.RoutingLaneInfo{
					{
						CustomsClearanceList: []string{"customs_clearance_1"},
					},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					CustomsClearanceList: []string{"customs_clearance_1"},
				},
			},
			wantErr: nil,
			setup: func() {
				c = &CCRoutingServiceImpl{
					CCApi: &ccclient.CCApiImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(c.CCApi), "GetDeclarationResult", func(c *ccclient.CCApiImpl,
					ctx context.Context, orderSn string) (string, bool, *srerr.Error) {
					return "customs_clearance_1", true, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := c.CCFilter(ctx, tt.args.orderSn, tt.args.availableLanes)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
