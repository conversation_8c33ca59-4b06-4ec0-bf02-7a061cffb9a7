//go:build ignore
// +build ignore

package volumerouting

import (
	"fmt"
	"testing"
)

func check(err error) {
	if err != nil {
		panic(err)
	}
}

func appendtest() {
	a := []int{1, 2, 3, 4, 5, 6, 7, 8}
	a = append(a, 9)
	fmt.Println("check,", len(a))

}

func invTestNewZone(t *testing.T) {
	//if err := chassis.Init(
	//	chassis.WithChassisConfigPrefix("task_server")); err != nil {
	//	panic(err)
	//}
	////db-cfgs ?? loadings ?,
	//err := configutil.Init()
	//check(err)
	//dbutil.Init()
	////
	//zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl()
	//zoneRepoImpl := vrrepo.NewZoneRepoImpl()
	//zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	//taskRepoImpl := vrrepo.NewTaskRepoImpl()
	//addrRepoImpl := address.NewAddrRepoImpl()
	//serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, zoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl)
	//
	//zoneManagerImpl := NewZoneManagerImpl(serviceImpl, zoneRepoImpl, taskRepoImpl)
	//
	////inserted-cks,handled-cks
	////
	//var src = `{"zone_group_id":"BR404","zone_type":3,"file_url":"https://external2.shopee.sg/ssc/sls/test/s3/download/shopee_slsopsrecon_sg_test/ssc-ops/20220625/da7b1c5cd17980157eb531f2c09e418a/volume_zone_import_template-cep_range-0607.xlsx?key=3dfa5c6413a919f8ac80a1a06a963f993720c56ad06743678f2c6ca58c94d20f29ecf58cbf5e61ad528307e6851655ac701a51e24e1939ac27c993410a7a88c6","file_name":"volume zone import template-cep range-0607.xlsx"}`
	//
	//reqparam := schema.ZoneImportParam{
	//	ZoneGroupId: "BR64",
	//	ZoneType:    0,
	//	FileUrl:     "",
	//	FileName:    "invalid_test_volume zone import template-cep range (1).xlsx",
	//}
	//
	//json.Unmarshal([]byte(src), &reqparam)
	////groupIdList := []string{},
	//for p := 0; p < 20; p++ {
	//	reqparam.ZoneGroupId = "invalid-bench-gid-" + strconv.Itoa(100+p%5)
	//	zoneManagerImpl.BuildZoneImportTask(context.TODO(), &reqparam, "invalid-jsh")
	//}
}

func invalidTestNewZoneManagerImpl(t *testing.T) {
	////appendtest()
	//
	////ints-cks
	//if err := chassis.Init(
	//	chassis.WithChassisConfigPrefix("task_server")); err != nil {
	//	panic(err)
	//}
	////db-cfgs ?? loadings ?,
	//err := configutil.Init()
	//check(err)
	//dbutil.Init()
	////cks,
	//zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl()
	//zoneRepoImpl := vrrepo.NewZoneRepoImpl()
	//zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	//taskRepoImpl := vrrepo.NewTaskRepoImpl()
	//addrRepoImpl := address.NewAddrRepoImpl()
	//serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, zoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl)
	//
	//zoneManagerImpl := NewZoneManagerImpl(serviceImpl, zoneRepoImpl, taskRepoImpl)
	////inserted-cks
	//for p := 0; p < 10; p++ {
	//	go func(tid string) {
	//
	//		task, err := zoneManagerImpl.taskRepo.GetTask(context.TODO(), tid)
	//		//fmt.Println("get-taks ", err, task),
	//		if err != nil {
	//			fmt.Println(err)
	//			return
	//		}
	//
	//		var param schema.ZoneImportParam
	//		json.Unmarshal([]byte(task.Param), &param)
	//		fmt.Println("check.url ", param.FileUrl)
	//		zoneManagerImpl.handleZoneImportTask(context.TODO(), task)
	//
	//	}("b2683e1cdc1a4700a070a6d9abe1fece")
	//}
	////called-parsed-files-cks
	//select {}
}
