package volumerouting

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	jsoniter "github.com/json-iterator/go"
	"reflect"
	"testing"
	"time"
)

const lineStr = `[{"line_id":"LBR33","resource_sub_type":128,"resource_id":"LBR33","dg_related":0,"dg_flag":0},{"line_id":"LBR25","resource_sub_type":128,"resource_id":"LBR25","dg_related":0,"dg_flag":0},{"line_id":"LBR111","resource_sub_type":128,"resource_id":"LBR111","dg_related":0,"dg_flag":0},{"line_id":"LBR44","resource_sub_type":128,"resource_id":"LBR44","dg_related":0,"dg_flag":0},{"line_id":"LBR107","resource_sub_type":128,"resource_id":"LBR107","dg_related":0,"dg_flag":0},{"line_id":"LBR64","resource_sub_type":128,"resource_id":"LBR64","dg_related":0,"dg_flag":0},{"line_id":"LBR17","resource_sub_type":128,"resource_id":"LBR17","dg_related":0,"dg_flag":0}]`
const locInfos = `{"location_ids":[********],"postcode":"29014"}`

func TestZoneRuleMgrImpl_FilterByLinesV2(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	if err := localcache.Init(localcache.NewConf(constant.VolumeZoneGroup, vrrepo.DumpZoneGroup, (*vrentity.ZoneGroupInfo)(nil)).WithReloadInterval(time.Second * 20)); err != nil {
		panic(err)
		return
	}
	zoneMrg := ZoneRuleMgrImpl{zoneRuleRepo: vrrepo.NewZoneRuleRepoImpl(), zoneGroupRepo: vrrepo.NewZoneGroupRepoImpl(nil, nil), volumeRoutingSrv: vrservice.NewForecastVolumeRouting(), zoneRepo: vrrepo.NewZoneRepoImpl()}
	ctx := context.TODO()
	ctx = context.WithValue(ctx, forecast.ForecastFlag{}, forecast.TaskId(252))
	var lines []*rule.LineInfo
	var locInfo *pb.LocationInfo
	_ = jsoniter.Unmarshal([]byte(lineStr), &lines)
	_ = jsoniter.Unmarshal([]byte(locInfos), &locInfo)
	_, _, _ = zoneMrg.FilterByLinesV2(ctx, pb.RuleStep_MaxCapacity, 91003, lines, locInfo, 2,
		0, "", true, rule.OrderParcelDimension{}, 1.0)
}

func TestNeedFilterByParcelType(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		productId                   int64
		lineId                      string
		isCod                       bool
		parcelDimension             rule.OrderParcelDimension
		cogs                        float64
		isDgRelated                 bool
		dgType                      rule.DGFlag
		parcelType                  string
		parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: ParcelTypeNone",
			args: args{
				parcelType: "",
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{}
				})
			},
		},
		{
			name: "case 2: ParcelType Cod",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeCod.String(),
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{
						IsCod: true,
					}
				})
			},
		},
		{
			name: "case 3: ParcelType Dg",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeDg.String(),
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{
						IsDg: true,
					}
				})
			},
		},
		{
			name: "case 4: ParcelType Bulky",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeBulky.String(),
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{
						IsBulky: true,
					}
				})
			},
		},
		{
			name: "case 5: ParcelTypeHighValue",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeHighValue.String(),
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{
						IsHighValue: true,
					}
				})
			},
		},
		{
			name: "case 6: ParcelTypeNotMatch",
			args: args{
				parcelType: "11",
			},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := NeedFilterByParcelType(ctx, tt.args.productId, tt.args.lineId, tt.args.isCod, tt.args.parcelDimension, tt.args.cogs, tt.args.dgType, tt.args.parcelType, tt.args.parcelTypeDefinitionService, 100); got != tt.want {
				t.Errorf("NeedFilterByParcelType() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestZoneRuleMgrImpl_GetGroupInfoAndZoneNameList(t *testing.T) {
	ctx := context.Background()
	p := &ZoneRuleMgrImpl{}
	var patch, patchGetZoneGroupCacheByGroupId, patchGetZoneNameWithDeliveryAddress *gomonkey.Patches
	type args struct {
		lineInfo    *rule.LineInfo
		volumeRule  *vrentity.VolumeRule
		routingType uint8
		locInfo     *pb.LocationInfo
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 *vrentity.ZoneGroupInfo
		want2 []string
		want3 bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: groupId == ",
			args: args{
				volumeRule: &vrentity.VolumeRule{},
				lineInfo:   &rule.LineInfo{},
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneGroupRepo: vrrepo.NewZoneGroupRepoImpl(nil, nil),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					return "", nil
				})
			},
		},
		{
			name: "case 2: groupInfo == nil",
			args: args{
				volumeRule: &vrentity.VolumeRule{},
				lineInfo:   &rule.LineInfo{},
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneGroupRepo: vrrepo.NewZoneGroupRepoImpl(nil, nil),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					return "1", nil
				})
				patchGetZoneGroupCacheByGroupId = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetZoneGroupCacheByGroupId", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
					return nil, nil
				})
			},
		},
		{
			name: "case 3: zoneNameList == ",
			args: args{
				volumeRule: &vrentity.VolumeRule{},
				lineInfo:   &rule.LineInfo{},
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneGroupRepo: vrrepo.NewZoneGroupRepoImpl(nil, nil),
					zoneRepo:      vrrepo.NewZoneRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					return "1", nil
				})
				patchGetZoneGroupCacheByGroupId = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetZoneGroupCacheByGroupId", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
					return &vrentity.ZoneGroupInfo{}, nil
				})
				patchGetZoneNameWithDeliveryAddress = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRepo), "GetZoneNameWithDeliveryAddress", func(p *vrrepo.ZoneRepoImpl, ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string {
					return nil
				})
			},
		},
		{
			name: "case 4: normal result",
			args: args{
				volumeRule: &vrentity.VolumeRule{},
				lineInfo:   &rule.LineInfo{},
			},
			want:  "1",
			want1: &vrentity.ZoneGroupInfo{},
			want2: []string{"zone1"},
			want3: true,
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneGroupRepo: vrrepo.NewZoneGroupRepoImpl(nil, nil),
					zoneRepo:      vrrepo.NewZoneRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					return "1", nil
				})
				patchGetZoneGroupCacheByGroupId = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetZoneGroupCacheByGroupId", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
					return &vrentity.ZoneGroupInfo{}, nil
				})
				patchGetZoneNameWithDeliveryAddress = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRepo), "GetZoneNameWithDeliveryAddress", func(p *vrrepo.ZoneRepoImpl, ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string {
					return []string{"zone1"}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, got1, got2, got3 := p.GetGroupInfoAndZoneNameList(ctx, tt.args.lineInfo, tt.args.volumeRule, tt.args.routingType, tt.args.locInfo)
			common.AssertResult(t, got, tt.want, nil, nil)
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetGroupInfoAndZoneNameList() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("GetGroupInfoAndZoneNameList() got2 = %v, want %v", got2, tt.want2)
			}
			if got3 != tt.want3 {
				t.Errorf("GetGroupInfoAndZoneNameList() got3 = %v, want %v", got3, tt.want3)
			}
			if patch != nil {
				patch.Reset()
			}
			if patchGetZoneGroupCacheByGroupId != nil {
				patchGetZoneGroupCacheByGroupId.Reset()
			}
			if patchGetZoneNameWithDeliveryAddress != nil {
				patchGetZoneNameWithDeliveryAddress.Reset()
			}
		})
	}
}

func TestZoneRuleMgrImpl_FilterByLinesV21(t *testing.T) {
	ctx := context.WithValue(context.TODO(), forecast.ForecastFlag{}, forecast.TaskId(233))
	p := &ZoneRuleMgrImpl{}
	var patch, patchCurrentVolumeLineDimension, patchGetGroupIdByLineWithCache, patchGetZoneGroupCacheByGroupId, patchGetZoneNameWithDeliveryAddress, patchGetRuleZoneLimits *gomonkey.Patches
	type args struct {
		ruleStep     pb.RuleStep
		productId    int64
		input        []*rule.LineInfo
		locInfo      *pb.LocationInfo
		routingType  uint8
		VolumeRuleId int64
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.LineInfo
		wantLog map[string]string
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: GetRuleWithZoneLimitByProduct err",
			wantLog: map[string]string{},
			wantErr: srerr.New(srerr.ParamErr, nil, "mock err"),
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo: vrrepo.NewZoneRuleRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return nil, srerr.New(srerr.ParamErr, nil, "mock err")
				})

			},
		},
		{
			name:    "case 2: volumeRule == nil",
			wantLog: map[string]string{},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo: vrrepo.NewZoneRuleRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return nil, nil
				})

			},
		},
		{
			name: "case 3: volumeRule.RuleType == enum.VolumeRuleTypeProduct, pb.RuleStep_MaxCapacity",
			args: args{
				ruleStep: pb.RuleStep_MaxCapacity,
				input: []*rule.LineInfo{
					{},
					{
						ResourceId: "line1",
					},
				},
			},
			want: []*rule.LineInfo{
				{},
				{
					ResourceId: "line1",
				},
			},
			wantLog: map[string]string{
				"BRVolumeRule":    "0",
				"0:line1:max":     "1000",
				"0:line1:current": "0",
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo: vrrepo.NewZoneRuleRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return &vrentity.VolumeRule{
						RuleType: enum.VolumeRuleTypeProduct,
						LineLimits: []vrentity.VolumeRuleDetail{
							{
								LineId: "line1",
								Max:    1000,
							},
						},
					}, nil
				})
			},
		},
		{
			name: "case 4: volumeRule.RuleType == enum.VolumeRuleTypeProduct, ruleStep == pb.RuleStep_MinVolume",
			args: args{
				ruleStep: pb.RuleStep_MinVolume,
				input: []*rule.LineInfo{
					{},
					{
						ResourceId: "line1",
					},
				},
			},
			want: []*rule.LineInfo{
				{},
				{
					ResourceId: "line1",
				},
			},
			wantLog: map[string]string{
				"BRVolumeRule":    "0",
				"0:line1:min":     "1000",
				"0:line1:current": "0",
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo: vrrepo.NewZoneRuleRepoImpl(),
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return &vrentity.VolumeRule{
						RuleType: enum.VolumeRuleTypeProduct,
						LineLimits: []vrentity.VolumeRuleDetail{
							{
								LineId: "line1",
								Min:    1000,
							},
						},
					}, nil
				})
			},
		},
		{
			//case 5: volumeRule.RuleType == enum.VolumeRuleTypeLineVolumeZone, ruleStep == pb.RuleStep_MinVolume
			name: "case 5",
			args: args{
				ruleStep: pb.RuleStep_MinVolume,
				input: []*rule.LineInfo{
					{ResourceId: "line1"},
					{ResourceId: "line2"},
					{ResourceId: "line3"},
					{ResourceId: "line4"},
					{ResourceId: "line5"},
					{ResourceId: "line6"},
				},
				routingType: rule.LocalRoutingType,
			},
			want: []*rule.LineInfo{
				{ResourceId: "line2"},
				{ResourceId: "line3"},
				{ResourceId: "line4"},
				{ResourceId: "line5"},
			},
			wantLog: map[string]string{
				"0::zoneVolume:min": "0",
				"0:line1:current":   "1000",
				"0:line1:min":       "1000",
				"BRVolumeRule":      "0",
				"line6:zoneVolume":  "zone4:0",
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo:     vrrepo.NewZoneRuleRepoImpl(),
					volumeRoutingSrv: &vrservice.ServiceImpl{},
					zoneGroupRepo:    &vrrepo.ZoneGroupRepoImpl{},
					zoneRepo:         &vrrepo.ZoneRepoImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return &vrentity.VolumeRule{
						RuleType: enum.VolumeRuleTypeLineVolumeZone,
						LineLimits: []vrentity.VolumeRuleDetail{
							{
								ProductIdList: []int64{1},
								LineId:        "line1",
								Min:           1000,
							},
						},
					}, nil
				})
				patchCurrentVolumeLineDimension = gomonkey.ApplyMethod(reflect.TypeOf(p.volumeRoutingSrv), "CurrentVolumeLineDimension", func(p *vrservice.ServiceImpl, ctx context.Context, productId int64, lineId, date string) (int64, *srerr.Error) {
					return 1000, nil
				})
				patchGetGroupIdByLineWithCache = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					if lineId == "line2" {
						return "", nil
					} else if lineId == "line3" {
						return "group3", nil
					} else if lineId == "line4" {
						return "group4", nil
					} else if lineId == "line5" {
						return "group5", nil
					}
					return "group6", nil
				})
				patchGetZoneGroupCacheByGroupId = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetZoneGroupCacheByGroupId", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
					if groupId == "group3" {
						return nil, nil
					} else if groupId == "group4" {
						return &vrentity.ZoneGroupInfo{ZoneType: enum.ZoneTypeLocation}, nil
					} else if groupId == "group5" {
						return &vrentity.ZoneGroupInfo{ZoneType: enum.ZoneTypePostcode}, nil
					}
					return &vrentity.ZoneGroupInfo{}, nil
				})
				patchGetZoneNameWithDeliveryAddress = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRepo), "GetZoneNameWithDeliveryAddress", func(p *vrrepo.ZoneRepoImpl, ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string {
					if groupInfo.ZoneType == enum.ZoneTypeLocation {
						return nil
					} else if groupInfo.ZoneType == enum.ZoneTypePostcode {
						return []string{"zone1"}
					}
					return []string{"zone2", "zone3", "zone4"}
				})
				patchGetRuleZoneLimits = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleZoneLimits", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, ruleId uint64, dataVersion int64, groupId string, lineId, zoneName string, routingType int, isForecastType bool) (*vrentity.VolumeRuleDetail, *srerr.Error) {
					if zoneName == "zone1" {
						return nil, srerr.New(srerr.ParamErr, nil, "mock err")
					} else if zoneName == "zone2" {
						return nil, nil
					} else if zoneName == "zone3" {
						return &vrentity.VolumeRuleDetail{Min: 1000}, nil
					}
					return &vrentity.VolumeRuleDetail{Min: 0}, nil
				})
			},
		},
		{
			//case 6: volumeRule.RuleType == enum.VolumeRuleTypeLineVolumeZone, ruleStep == pb.RuleStep_MaxCapacity
			name: "case 6",
			args: args{
				ruleStep: pb.RuleStep_MaxCapacity,
				input: []*rule.LineInfo{
					{ResourceId: "line5"},
				},
			},
			want: nil,
			wantLog: map[string]string{
				"0::zoneVolume:max": "0",
				"0:line1:max":       "1000",
				"BRVolumeRule":      "0",
				"line5:zoneVolume":  "zone4:0",
			},
			setup: func() {
				p = &ZoneRuleMgrImpl{
					zoneRuleRepo:     vrrepo.NewZoneRuleRepoImpl(),
					volumeRoutingSrv: &vrservice.ServiceImpl{},
					zoneGroupRepo:    &vrrepo.ZoneGroupRepoImpl{},
					zoneRepo:         &vrrepo.ZoneRepoImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleWithZoneLimitByProduct", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
					return &vrentity.VolumeRule{
						RuleType: enum.VolumeRuleTypeLineVolumeZone,
						LineLimits: []vrentity.VolumeRuleDetail{
							{
								ProductIdList: []int64{1},
								LineId:        "line1",
								Max:           1000,
							},
						},
					}, nil
				})
				patchCurrentVolumeLineDimension = gomonkey.ApplyMethod(reflect.TypeOf(p.volumeRoutingSrv), "CurrentVolumeLineDimension", func(p *vrservice.ServiceImpl, ctx context.Context, productId int64, lineId, date string) (int64, *srerr.Error) {
					return 1000, nil
				})
				patchGetGroupIdByLineWithCache = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetGroupIdByLineWithCache", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
					return "group6", nil
				})
				patchGetZoneGroupCacheByGroupId = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneGroupRepo), "GetZoneGroupCacheByGroupId", func(p *vrrepo.ZoneGroupRepoImpl, ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
					return &vrentity.ZoneGroupInfo{}, nil
				})
				patchGetZoneNameWithDeliveryAddress = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRepo), "GetZoneNameWithDeliveryAddress", func(p *vrrepo.ZoneRepoImpl, ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string {
					return []string{"zone3", "zone4"}
				})
				patchGetRuleZoneLimits = gomonkey.ApplyMethod(reflect.TypeOf(p.zoneRuleRepo), "GetRuleZoneLimits", func(p *vrrepo.ZoneRuleRepoImpl, ctx context.Context, productId int64, ruleId uint64, dataVersion int64, groupId string, lineId, zoneName string, routingType int, isForecastType bool) (*vrentity.VolumeRuleDetail, *srerr.Error) {
					if zoneName == "zone3" {
						return &vrentity.VolumeRuleDetail{Max: 1000}, nil
					}
					return &vrentity.VolumeRuleDetail{Max: 0}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotLog, gotErr := p.FilterByLinesV2(ctx, tt.args.ruleStep, tt.args.productId, tt.args.input, tt.args.locInfo, tt.args.routingType, tt.args.VolumeRuleId, "", true, rule.OrderParcelDimension{}, 0)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			common.AssertResult(t, gotLog, tt.wantLog, nil, nil)
			if patch != nil {
				patch.Reset()
			}
			if patchCurrentVolumeLineDimension != nil {
				patchCurrentVolumeLineDimension.Reset()
			}
			if patchGetGroupIdByLineWithCache != nil {
				patchGetGroupIdByLineWithCache.Reset()
			}
			if patchGetZoneGroupCacheByGroupId != nil {
				patchGetZoneGroupCacheByGroupId.Reset()
			}
			if patchGetZoneNameWithDeliveryAddress != nil {
				patchGetZoneNameWithDeliveryAddress.Reset()
			}
			if patchGetRuleZoneLimits != nil {
				patchGetRuleZoneLimits.Reset()
			}
		})
	}
}
