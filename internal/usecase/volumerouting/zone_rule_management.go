package volumerouting

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	jsoniter "github.com/json-iterator/go"
)

var importZoneLimitMsgHeader = []string{"Product", "Line", "Volume Zone Group ID", "Zone Name", "Use case"}

type ZoneRuleMgr interface {
	GetZoneRuleListPage(ctx context.Context, param *schema.ZoneRulePageParam) (*schema.ZoneRulePageResult, *srerr.Error)
	CreateZoneRuleDraft(ctx context.Context, param *schema.ZoneRuleCreateParam, operator string) (*schema.ZoneRuleCreateResult, *srerr.Error)
	EditZoneRuleSaveDraft(ctx context.Context, param *schema.ZoneRuleEditParam, operator string) *srerr.Error
	EditZoneRuleSubmit(ctx context.Context, param *schema.ZoneRuleEditParam, operator string) *srerr.Error
	//SaveForecastVolumeRule(ctx context.Context, volumeRule *persistent2.ForecastVolumeRuleTab, request *forecast.DeployTaskRequest, operator string) *srerr.Error
	DeleteZoneRule(ctx context.Context, param *schema.ZoneRuleIdParam) *srerr.Error
	CheckDisableZoneRule(ctx context.Context, param *schema.ZoneRuleDisableCheckParam) (*schema.ZoneRuleDisableCheckResult, *srerr.Error)
	DisableZoneRule(ctx context.Context, param *schema.ZoneRuleIdParam, operator string) *srerr.Error
	GetZoneRuleLimitListPage(ctx context.Context, param *schema.ZoneRuleLimitPageParam) (*schema.ZoneRuleLimitPageResult, *srerr.Error)
	HandleZoneRuleLimitImportTask(ctx context.Context, taskId string) *srerr.Error
	ImportZoneRuleLimit(ctx context.Context, param *schema.ZoneRuleLimitImportParam, operator string) (*schema.ZoneRuleLimitImportResult, *srerr.Error)
	ForecastZoneRuleLimitImport(ctx context.Context, param *schema.ZoneRuleLimitImportParam, operator string) ([]*forecast.ZoneLimit, *srerr.Error)
	GetLineInfo(ctx context.Context, param *schema.LineInfoRequest) (map[string][]*schema.LineInfoResponse, *srerr.Error)
	ExportZoneRuleLimit(ctx context.Context, param *schema.ZoneRuleLimitExportParam) ([]byte, *srerr.Error)
	GetZoneRuleDetail(ctx context.Context, param *schema.ZoneRuleIdParam) (*schema.ZoneRuleInfo, *srerr.Error)
	GetRuleListByStatus(ctx context.Context, param *schema.RuleListRequest) ([]*schema.RuleListResponse, *srerr.Error)
	HandleRuleEffective(ctx context.Context) *srerr.Error
	StatisticVolumeByOrder(ctx context.Context, forderId string) *srerr.Error

	VolumeCounter(ctx context.Context, productId int64, lanecode, postcode string, lineidList []string, deliveryLocIdlist []uint64) *srerr.Error
	FilterByLinesV2(ctx context.Context, ruleStep pb.RuleStep, productId int64, input []*rule.LineInfo, locInfo *pb.LocationInfo, routingType uint8, volumeRuleId int64, specificName string, isCod bool, parcelDimension rule.OrderParcelDimension, cogs float64) ([]*rule.LineInfo, map[string]string, *srerr.Error)
	DeployForecastVolumeRule(ctx context.Context, tx scormv2.SQLCommon, volumeRule persistent.VolumeRoutingRuleTab, groupIdMap map[string]struct{}) *srerr.Error
	CopyVolumeRuleWithNewStatus(ctx context.Context, ruleId int64, status enum.VolumeRuleStatus, EffectiveStartTime int64, isForecastType bool) (uint64, *srerr.Error)
	CheckCanDeployForecastRule(ctx context.Context, ruleId int64) *srerr.Error
	GetAllGroup(ctx context.Context, routingType int, isForecastType bool) ([]schema.GroupDetail, *srerr.Error)
	ExportRuleLimitTemplate(ctx context.Context, productIdList []int64, routingType int) ([]byte, *srerr.Error)
}

type ZoneRuleMgrImpl struct {
	volumeRoutingSrv            vrservice.Service
	zoneRuleRepo                vrrepo.ZoneRuleRepo
	zoneGroupRepo               vrrepo.ZoneGroupRepo
	zoneRepo                    vrrepo.ZoneRepo
	taskRepo                    vrrepo.TaskRepo
	lpsApi                      lpsclient.LpsApi
	routingConfigRepo           ruledata.RoutingConfigRepo
	lnpApi                      lnpclient.LnpApi
	parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
}

func NewZoneRuleMgrImpl(
	volumeRoutingSrv vrservice.Service,
	zoneRuleRepo vrrepo.ZoneRuleRepo,
	zoneGroupRepo vrrepo.ZoneGroupRepo,
	zoneRepo vrrepo.ZoneRepo,
	taskRepo vrrepo.TaskRepo,
	lpsApi lpsclient.LpsApi,
	routingConfigRepo ruledata.RoutingConfigRepo,
	lnpApi lnpclient.LnpApi,
	parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService,
) *ZoneRuleMgrImpl {
	return &ZoneRuleMgrImpl{
		volumeRoutingSrv:            volumeRoutingSrv,
		zoneRuleRepo:                zoneRuleRepo,
		zoneGroupRepo:               zoneGroupRepo,
		zoneRepo:                    zoneRepo,
		taskRepo:                    taskRepo,
		lpsApi:                      lpsApi,
		routingConfigRepo:           routingConfigRepo,
		lnpApi:                      lnpApi,
		parcelTypeDefinitionService: parcelTypeDefinitionService,
	}
}

func (p *ZoneRuleMgrImpl) VolumeCounter(ctx context.Context, productId int64, laneCode, postcode string, lineIdList []string, deliveryLocIdList []uint64) *srerr.Error {
	////1.counter-lines todo 接口下线？？？
	return nil
}

func (p *ZoneRuleMgrImpl) GetLineInfo(ctx context.Context, param *schema.LineInfoRequest) (map[string][]*schema.LineInfoResponse, *srerr.Error) {

	lineRet, err := p.lpsApi.GetLineDictList(ctx, param.ProductId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get volume info failed: %+v", err)
		return nil, err
	}

	response := make(map[string][]*schema.LineInfoResponse)
	for _, line := range lineRet.LineDictList {
		lineType := line.LineType
		if routingType, ok := lineRet.RoleMap[lineType]; ok {
			lineType = routingType
		}

		lineTypeDetail := lfslib.LineSubTypeMap[int32(lineType)]
		if v := response[lineTypeDetail]; v == nil {
			response[lineTypeDetail] = []*schema.LineInfoResponse{}
		}
		response[lineTypeDetail] = append(response[lineTypeDetail], &schema.LineInfoResponse{
			LineId:        line.LineId,
			LineType:      lineType,
			LineName:      line.LineName,
			MaxDailyLimit: 1,
			MinDailyLimit: 1,
		})
	}

	return response, nil
}

func (p *ZoneRuleMgrImpl) GetZoneRuleListPage(ctx context.Context, param *schema.ZoneRulePageParam) (*schema.ZoneRulePageResult, *srerr.Error) {
	list, total, err := p.zoneRuleRepo.RulePage(ctx, param.FormatQueryCondition(), param.Offset, param.Size)
	if err != nil {
		return nil, err
	}
	ret := schema.ZoneRulePageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   make([]schema.ZoneRulePageModel, 0),
	}
	for _, data := range list {
		ret.List = append(ret.List, schema.ZoneRulePageModel{
			RuleId:             data.Id,
			RuleName:           data.RuleName,
			ProductId:          data.ProductId,
			ProductIdList:      data.ProductIdList,
			ShareVolume:        data.ShareVolume,
			Priority:           data.Priority,
			RuleType:           data.RuleType,
			Status:             data.RuleStatus,
			EffectiveStartTime: data.EffectiveStartTime,
			Operator:           data.Operator,
			UpdateTime:         timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(data.Mtime)),
		})
	}
	return &ret, nil
}

func (p *ZoneRuleMgrImpl) getRuleLineDictList(ctx context.Context, param *schema.ZoneRuleCreateParam) (map[string]*ruleLineInfo, *srerr.Error) {
	productIdList := []int64{param.ProductId}
	if param.ShareVolume {
		productIdList = param.ProductIdList
	}

	ret := make(map[string]*ruleLineInfo)
	for _, productId := range productIdList {
		lineRet, err := p.lpsApi.GetLineDictList(ctx, productId)
		if err != nil {
			return nil, err
		}
		for _, line := range lineRet.LineDictList {
			lineType := line.LineType
			if routingType, ok := lineRet.RoleMap[lineType]; ok {
				lineType = routingType
			}
			if lineInfo, exist := ret[line.LineId]; exist {
				lineInfo.ProductIdList = append(lineInfo.ProductIdList, productId)
			} else {
				ret[line.LineId] = &ruleLineInfo{
					LineId:        line.LineId,
					LineType:      lineType,
					ProductIdList: []int64{productId},
				}
			}
		}
	}

	return ret, nil
}

// checkShareVolumeRoleConsistent 校验Share Volume模式下Product间Routing Role的一致性
func (p *ZoneRuleMgrImpl) checkShareVolumeRoleConsistent(ctx context.Context, productIdList []int64, routingType uint8) *srerr.Error {
	commonRoleMap := make(map[int]int)
	for _, productId := range productIdList {
		// share volume的是Local/SPX场景，一定是Non Multi
		for lineType, roleType := range routing_role.GetRoleMapByProductAndType(ctx, productId, routingType, false) {
			commonRoleType, exist := commonRoleMap[lineType]
			if !exist {
				commonRoleMap[lineType] = roleType
				continue
			}
			if roleType != commonRoleType {
				return srerr.New(srerr.RoleDiffInShareVolumeProduct, productId, "Line %s has the different routing role in Product Config", lfslib.LineSubTypeMap[int32(lineType)])
			}
		}
	}

	return nil
}

func (p *ZoneRuleMgrImpl) CreateZoneRuleDraft(ctx context.Context, param *schema.ZoneRuleCreateParam, operator string) (*schema.ZoneRuleCreateResult, *srerr.Error) {
	if param.ShareVolume {
		// 对于Share Volume模式，要多一个Routing Role校验
		if err := p.checkShareVolumeRoleConsistent(ctx, param.ProductIdList, uint8(param.RoutingType)); err != nil {
			return nil, err
		}
	}

	ruleTab := persistent.VolumeRoutingRuleTab{
		RuleName:      param.RuleName,
		ProductId:     param.ProductId,
		ProductIdList: param.ProductIdList,
		ShareVolume:   param.ShareVolume,
		RuleStatus:    enum.VolumeRuleStatusDraft,
		Operator:      operator,
		RoutingType:   param.RoutingType,
	}
	// cb创建volume rule只有Forecast
	if param.RoutingType == rule.CBRoutingType {
		ruleTab.IsForecastType = constant.ForecastType
	}

	ruleId, err := p.zoneRuleRepo.CreateZoneRule(ctx, &ruleTab)
	if err != nil {
		return nil, err
	}
	if err := vrentity.InitVolumeRuleDataVersion(ctx, ruleId); err != nil {
		logger.CtxLogErrorf(ctx, "initialize rule data version fail, ruleId:%d, err:%+v", ruleId, err)
	}
	lineRet, err := p.getRuleLineDictList(ctx, param)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get line dict list fail, param:%s, operator:%s, err:%+v", objutil.JsonString(param), operator, err)
	}
	if lineRet == nil {
		return &schema.ZoneRuleCreateResult{
			RuleId: ruleId,
		}, nil
	}
	var details []persistent.VolumeRoutingRuleDetailTab
	for _, line := range lineRet {
		details = append(details, persistent.VolumeRoutingRuleDetailTab{
			RuleId:         ruleId,
			LineId:         line.LineId,
			LineType:       line.LineType,
			Min:            constant.DefaultVolumeRuleLimit,
			Max:            constant.DefaultVolumeRuleLimit,
			MaxCod:         constant.DefaultVolumeRuleLimit,
			MaxBulky:       constant.DefaultVolumeRuleLimit,
			MaxHighValue:   constant.DefaultVolumeRuleLimit,
			MaxDg:          constant.DefaultVolumeRuleLimit,
			Operator:       operator,
			RoutingType:    ruleTab.RoutingType,
			IsForecastType: ruleTab.IsForecastType,
			ProductIdList:  line.ProductIdList,
		})
	}

	if len(details) > 0 {
		if err := p.zoneRuleRepo.CreateZoneRuleDetails(ctx, details); err != nil {
			logger.CtxLogErrorf(ctx, "create zone rule details fail, err:%+v", err)
		}
	}

	return &schema.ZoneRuleCreateResult{
		RuleId: ruleId,
	}, nil
}

func (p *ZoneRuleMgrImpl) EditZoneRuleSaveDraft(ctx context.Context, param *schema.ZoneRuleEditParam, operator string) *srerr.Error {
	r := vrentity.VolumeRule{
		RuleId:               param.RuleId,
		RuleName:             param.RuleName,
		ProductId:            param.ProductId,
		ProductIdList:        param.ProductIdList,
		ShareVolume:          param.ShareVolume,
		Priority:             param.Priority,
		RuleType:             param.RuleType,
		Status:               enum.VolumeRuleStatusDraft,
		EffectiveStartTime:   param.EffectiveStartTime,
		EffectiveImmediately: param.EffectiveImmediately,
		LineLimits:           nil,
		DataVersion:          param.DataVersion,
		RoutingType:          param.RoutingType,
	}
	if param.RoutingType == rule.CBRoutingType {
		r.IsForecastType = constant.ForecastType
	}
	if param.EffectiveImmediately {
		r.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	var details []vrentity.VolumeRuleDetail

	for lineTypeName, limits := range param.LineLimit {
		lineType := lfslib.LineSubTypeNameReveseMap[lineTypeName]
		for _, limit := range limits {
			groupId, _ := p.zoneGroupRepo.GetGroupIdByLine(ctx, limit.LineId, param.RoutingType, param.IsForecastType)
			if limit.MinDailyLimit < 0 || limit.MaxDailyLimit < 0 || limit.MaxCodDailyLimit < 0 || limit.MaxBulkyDailyLimit < 0 ||
				limit.MaxHighValueDailyLimit < 0 || limit.MaxDgDailyLimit < 0 {
				return srerr.New(srerr.ParamErr, nil, "min or max daily limit must be positive")
			}
			details = append(details, vrentity.VolumeRuleDetail{
				LineId:        limit.LineId,
				LineType:      int(lineType),
				ZoneGroupId:   groupId,
				Min:           limit.MinDailyLimit,
				Max:           limit.MaxDailyLimit,
				MaxCod:        limit.MaxCodDailyLimit,
				MaxBulky:      limit.MaxBulkyDailyLimit,
				MaxHighValue:  limit.MaxHighValueDailyLimit,
				MaxDg:         limit.MaxDgDailyLimit,
				ProductIdList: limit.ProductIdList,
			})
		}
	}
	r.LineLimits = details
	productIdList := []int64{r.ProductId}
	if r.ShareVolume {
		productIdList = r.ProductIdList
	}
	for _, productId := range productIdList {
		if err := p.volumeRoutingSrv.CheckVolumeRuleCanSaveDraft(ctx, &r, productId); err != nil {
			return err
		}
	}

	return p.zoneRuleRepo.SaveZoneRule(ctx, &r, operator)
}

func (p *ZoneRuleMgrImpl) EditZoneRuleSubmit(ctx context.Context, param *schema.ZoneRuleEditParam, operator string) *srerr.Error {
	r := vrentity.VolumeRule{
		RuleId:               param.RuleId,
		RuleName:             param.RuleName,
		ProductId:            param.ProductId,
		ProductIdList:        param.ProductIdList,
		ShareVolume:          param.ShareVolume,
		Priority:             param.Priority,
		RuleType:             param.RuleType,
		Status:               enum.VolumeRuleStatusQueuing,
		EffectiveStartTime:   param.EffectiveStartTime,
		EffectiveImmediately: param.EffectiveImmediately,
		LineLimits:           nil,
		DataVersion:          param.DataVersion,
		RoutingType:          param.RoutingType,
		IsForecastType:       param.IsForecastType,
	}
	if param.RoutingType == rule.CBRoutingType {
		r.IsForecastType = constant.ForecastType
		r.Status = enum.VolumeRuleStatusSubmit
	}
	if param.EffectiveImmediately {
		r.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	var details []vrentity.VolumeRuleDetail
	for lineTypeName, limits := range param.LineLimit {
		lineType := lfslib.LineSubTypeNameReveseMap[lineTypeName]
		for _, limit := range limits {
			groupId, _ := p.zoneGroupRepo.GetGroupIdByLine(ctx, limit.LineId, r.RoutingType, r.IsForecastType)
			if limit.MinDailyLimit < 0 || limit.MaxDailyLimit < 0 || limit.MaxCodDailyLimit < 0 || limit.MaxBulkyDailyLimit < 0 ||
				limit.MaxHighValueDailyLimit < 0 || limit.MaxDgDailyLimit < 0 {
				return srerr.New(srerr.ParamErr, nil, "min or max daily limit must be positive")
			}
			details = append(details, vrentity.VolumeRuleDetail{
				LineId:        limit.LineId,
				LineType:      int(lineType),
				Min:           limit.MinDailyLimit,
				Max:           limit.MaxDailyLimit,
				MaxCod:        limit.MaxCodDailyLimit,
				MaxBulky:      limit.MaxBulkyDailyLimit,
				MaxHighValue:  limit.MaxHighValueDailyLimit,
				MaxDg:         limit.MaxDgDailyLimit,
				ZoneGroupId:   groupId,
				ProductIdList: limit.ProductIdList,
			})
		}
	}
	r.LineLimits = details
	productIdList := []int64{r.ProductId}
	if r.ShareVolume {
		productIdList = r.ProductIdList
	}
	for _, productId := range productIdList {
		if err := p.volumeRoutingSrv.CheckVolumeRuleCanSubmit(ctx, &r, productId); err != nil {
			return err
		}
	}

	return p.zoneRuleRepo.SaveZoneRule(ctx, &r, operator)
}

func (p *ZoneRuleMgrImpl) DeleteZoneRule(ctx context.Context, param *schema.ZoneRuleIdParam) *srerr.Error {
	return p.zoneRuleRepo.DeleteRule(ctx, param.RuleId)
}

func (p *ZoneRuleMgrImpl) CheckDisableZoneRule(ctx context.Context, param *schema.ZoneRuleDisableCheckParam) (*schema.ZoneRuleDisableCheckResult, *srerr.Error) {
	productIdList := []int64{param.ProductId}
	if param.ShareVolume {
		productIdList = param.ProductIdList
	}

	for _, productId := range productIdList {
		// List里的Product都要有其他Active的Rule
		condition := map[string]interface{}{
			fmt.Sprintf("(product_id = ? OR JSON_CONTAINS(product_id_list,'%d','$'))", productId): productId,
			"rule_status = ?": enum.VolumeRuleStatusActive,
			"id != ?":         param.RuleId,
		}
		list, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, condition)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get product active rules fail | product=%d,err=%v", productId, err)
			return nil, err
		}
		if len(list) == 0 {
			return &schema.ZoneRuleDisableCheckResult{ProductActive: false}, nil
		}
	}

	return &schema.ZoneRuleDisableCheckResult{ProductActive: true}, nil
}

func (p *ZoneRuleMgrImpl) DisableZoneRule(ctx context.Context, param *schema.ZoneRuleIdParam, operator string) *srerr.Error {
	return p.zoneRuleRepo.DisableRule(ctx, param.RuleId, operator)
}

func (p *ZoneRuleMgrImpl) GetZoneRuleLimitListPage(ctx context.Context, param *schema.ZoneRuleLimitPageParam) (*schema.ZoneRuleLimitPageResult, *srerr.Error) {
	list, total, err := p.zoneRuleRepo.RuleDetailPage(ctx, param.FormatQueryCondition(), param.Offset, param.Size)
	if err != nil {
		return nil, err
	}
	ret := schema.ZoneRuleLimitPageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   make([]schema.ZoneRuleLimitPageModel, 0),
	}
	for _, data := range list {
		ret.List = append(ret.List, schema.ZoneRuleLimitPageModel{
			LineId:                 data.LineId,
			LineType:               data.LineType,
			ZoneGroupId:            data.GroupId,
			ZoneName:               data.ZoneName,
			MinDailyLimit:          data.Min,
			MaxDailyLimit:          data.Max,
			MaxCodDailyLimit:       data.MaxCod,
			MaxBulkyDailyLimit:     data.MaxBulky,
			MaxHighValueDailyLimit: data.MaxHighValue,
			MaxDgDailyLimit:        data.MaxDg,
			ProductIdList:          data.ProductIdList,
		})
	}
	return &ret, nil
}

func (p *ZoneRuleMgrImpl) HandleZoneRuleLimitImportTask(ctx context.Context, taskId string) *srerr.Error {
	task, err := p.taskRepo.GetTask(ctx, taskId)
	if err != nil {
		return err
	}
	if task.TaskStatus != enum.TaskStatusInProcess {
		return nil
	}
	param := new(schema.VolumeRuleDetailImportParam)
	if err := jsoniter.Unmarshal(task.Param, param); err != nil {
		//todo: consider update
		return srerr.With(srerr.FormatErr, nil, err)
	}
	if err := p.volumeRoutingSrv.ImportRuleDetails(ctx, param.RuleId, param.FilePath, task.Operator); err != nil {
		return p.taskRepo.UpdateByTaskId(ctx, taskId, map[string]interface{}{
			"fail_reason": err.Error()[:128],
			"task_status": enum.TaskStatusFailed,
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		})
	}
	return p.taskRepo.UpdateByTaskId(ctx, taskId, map[string]interface{}{
		"task_status": enum.TaskStatusDone,
		"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
	})
}

func (p *ZoneRuleMgrImpl) ImportZoneRuleLimit(ctx context.Context, param *schema.ZoneRuleLimitImportParam, operator string) (*schema.ZoneRuleLimitImportResult, *srerr.Error) {
	// 1. 获取product导入基准值
	benchMarkValue, err := p.initRuleLimitImportTemplateData(ctx, param.ProductIdList, param.RoutingType)
	if err != nil {
		return nil, err
	}
	zoneLimits, checkResultList, err := p.volumeRoutingSrv.CheckAndGetRuleLimitData(ctx, param.RuleId, param.FileUrl, operator, benchMarkValue)
	if err != nil {
		return nil, err
	}
	dataV, vErr := vrentity.GetVolumeRuleDataVersion(ctx, param.RuleId)
	if vErr != nil {
		return nil, err
	}
	for i := 0; i < len(zoneLimits); i++ {
		zoneLimits[i].DataVersion = dataV
	}
	if err := p.zoneRuleRepo.CreateZoneRuleDetails(ctx, zoneLimits); err != nil {
		return nil, err
	}
	// 调pis发送邮件
	p.SendEmailByPis(ctx, checkResultList, operator)
	return &schema.ZoneRuleLimitImportResult{DataVersion: dataV, CheckResultList: vrentity.CheckZoneLimitInfoMsgListToString(checkResultList)}, nil
}

func (p *ZoneRuleMgrImpl) ForecastZoneRuleLimitImport(ctx context.Context, param *schema.ZoneRuleLimitImportParam, operator string) ([]*forecast.ZoneLimit, *srerr.Error) {
	zoneLimits, err := p.volumeRoutingSrv.CheckAndGetRuleLimitDataForLocalForecast(ctx, param.RuleId, param.FileUrl, operator)
	if err != nil {
		return nil, err
	}
	res := []*forecast.ZoneLimit{}
	for _, zoneLimit := range zoneLimits {
		res = append(res, &forecast.ZoneLimit{
			LineId:                 zoneLimit.LineId,
			ZoneName:               zoneLimit.ZoneName,
			LineType:               zoneLimit.LineType,
			ZoneGroupId:            zoneLimit.GroupId,
			MaxDailyLimit:          zoneLimit.Max,
			MinDailyLimit:          zoneLimit.Min,
			MaxCodDailyLimit:       zoneLimit.MaxCod,
			MaxBulkyDailyLimit:     zoneLimit.MaxBulky,
			MaxHighValueDailyLimit: zoneLimit.MaxHighValue,
			MaxDgDailyLimit:        zoneLimit.MaxDg,
		})
	}

	return res, nil
}

func (p *ZoneRuleMgrImpl) ExportZoneRuleLimit(ctx context.Context, param *schema.ZoneRuleLimitExportParam) ([]byte, *srerr.Error) {
	list, err := p.zoneRuleRepo.QueryRuleDetailByCondition(ctx, param.FormatQueryCondition())
	if err != nil {
		return nil, err
	}
	buffer, err := p.volumeRoutingSrv.ExportRuleDetails(ctx, list)
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}

func (p *ZoneRuleMgrImpl) GetRuleListByStatus(ctx context.Context, param *schema.RuleListRequest) ([]*schema.RuleListResponse, *srerr.Error) {
	ruleList := []*persistent.VolumeRoutingRuleTab{}
	condition := map[string]interface{}{
		"product_id = ?":        param.ProductId,
		"rule_status in (?)":    param.RuleStatusList,
		constant.RoutingTypeSql: param.RoutingType,
	}
	if volumeutil.IsLocalSpxRouting(param.RoutingType) {
		condition[constant.IsForecastTypeSql] = constant.NonForecastType
	}
	err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, condition, &ruleList, dbutil.WithLimit(int64(param.PageSize)), dbutil.WithOrder("rule_status asc,ctime desc"))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, param, err)
	}

	response := make([]*schema.RuleListResponse, len(ruleList))

	for idx, r := range ruleList {
		res := &schema.RuleListResponse{
			ProductId:     r.ProductId,
			ProductIdList: r.ProductIdList,
			ShareVolume:   r.ShareVolume,
			RuleId:        r.Id,
			RuleName:      r.RuleName,
			RuleStatus:    r.RuleStatus,
		}

		response[idx] = res
	}

	return response, nil
}

// ok,empty,
func (p *ZoneRuleMgrImpl) GetZoneRuleDetail(ctx context.Context, param *schema.ZoneRuleIdParam) (*schema.ZoneRuleInfo, *srerr.Error) {
	rules, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{"id = ?": param.RuleId})
	if err != nil {
		return nil, err
	}
	if len(rules) == 0 {
		return nil, srerr.New(srerr.ZoneRuleNotFound, nil, "rule not found, ruleId:%d", param.RuleId)
	}

	groupId, _ := p.zoneGroupRepo.GetGroupIdByProduct(ctx, rules[0].ProductId)
	ret := schema.ZoneRuleInfo{
		RuleId:               rules[0].Id,
		RuleName:             rules[0].RuleName,
		ZoneGroupId:          groupId,
		ProductId:            rules[0].ProductId,
		ProductIdList:        rules[0].ProductIdList,
		ShareVolume:          rules[0].ShareVolume,
		RuleType:             rules[0].RuleType,
		Priority:             rules[0].Priority,
		EffectiveStartTime:   rules[0].EffectiveStartTime,
		EffectiveImmediately: rules[0].EffectiveImmediately,
		LineLimit:            make(map[string][]schema.ZoneRuleLineLimit, 0),
		DataVersion:          rules[0].DataVersion,
	}
	if ret.EffectiveImmediately {
		ret.EffectiveStartTime = 0
	}
	if ruleDetails, _ := p.zoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{
		"rule_id = ?": param.RuleId, "zone_name = ?": "", "data_version = ?": rules[0].DataVersion}); len(ruleDetails) > 0 {
		lineLimits := make(map[string][]schema.ZoneRuleLineLimit)
		for _, detail := range ruleDetails {
			lineName := lfslib.LineSubTypeMap[int32(detail.LineType)]
			if _, ok := lineLimits[lineName]; ok {
				lineLimits[lineName] = append(lineLimits[lineName], schema.ZoneRuleLineLimit{
					LineId:                 detail.LineId,
					MinDailyLimit:          detail.Min,
					MaxDailyLimit:          detail.Max,
					MaxCodDailyLimit:       detail.MaxCod,
					MaxBulkyDailyLimit:     detail.MaxBulky,
					MaxHighValueDailyLimit: detail.MaxHighValue,
					MaxDgDailyLimit:        detail.MaxDg,
					ProductIdList:          detail.ProductIdList,
				})
				continue
			}
			lineLimits[lineName] = []schema.ZoneRuleLineLimit{
				{
					LineId:                 detail.LineId,
					MinDailyLimit:          detail.Min,
					MaxDailyLimit:          detail.Max,
					MaxCodDailyLimit:       detail.MaxCod,
					MaxBulkyDailyLimit:     detail.MaxBulky,
					MaxHighValueDailyLimit: detail.MaxHighValue,
					MaxDgDailyLimit:        detail.MaxDg,
					ProductIdList:          detail.ProductIdList,
				},
			}
		}
		ret.LineLimit = lineLimits
	}
	return &ret, nil
}

func (p *ZoneRuleMgrImpl) HandleRuleEffective(ctx context.Context) *srerr.Error {
	startTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	rules, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
		"rule_status = ?":           enum.VolumeRuleStatusQueuing,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx)},
	)
	if err != nil {
		logger.CtxLogErrorf(ctx, "active rule status fail, obtain available queueing rule fail, err:%+v", err)
		return err
	}
	if len(rules) == 0 {
		logger.CtxLogInfof(ctx, "active rule status, available rule active not found")
		return nil
	}
	var cbForecastRule, localRule []persistent.VolumeRoutingRuleTab
	for _, r := range rules {
		if r.RoutingType == rule.CBRoutingType {
			cbForecastRule = append(cbForecastRule, r)
		}
		if volumeutil.IsLocalSpxRouting(r.RoutingType) && !r.IsForecastType {
			localRule = append(localRule, r)
		}
	}

	//记录修改的rule id
	ruleIds := []string{}
	for _, r := range localRule {
		if err := p.zoneRuleRepo.UpdateRuleStatusByID(ctx, r.Id, enum.VolumeRuleStatusActive); err != nil {
			logger.CtxLogErrorf(ctx, "active rule status fail, ruleId:%d, err:%+v", r.Id, err)
			continue
		}

		ruleIds = append(ruleIds, strconv.FormatUint(r.Id, 10))
	}

	db, merr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
	if merr != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	groupIdMap := make(map[string]struct{})
	ctx = scormv2.BindContext(ctx, db)
	if err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		for _, r := range cbForecastRule {
			// 1.找到处于Active状态的rule
			activeRules, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
				"product_id = ?":   r.ProductId,
				"rule_status = ?":  enum.VolumeRuleStatusActive,
				"routing_type = ?": r.RoutingType},
			)
			if err != nil {
				return srerr.With(srerr.DatabaseErr, nil, err)
			}
			// 2.将active rule -> expire
			for _, activeRule := range activeRules {
				if err := p.zoneRuleRepo.UpdateRuleStatusByIDWithTx(ctx, tx, activeRule.Id, enum.VolumeRuleStatusExpired); err != nil {
					logger.CtxLogErrorf(ctx, "active rule status to expire fail, ruleId:%d, err:%+v", r.Id, err)
					continue
				}
			}
			// 3.将queue -> active
			if err := p.DeployForecastVolumeRule(ctx, tx, r, groupIdMap); err != nil {
				logger.CtxLogErrorf(ctx, "active rule status fail, ruleId:%d, err:%v", r.Id, err)
				continue
			}
			// 修改rule的状态
			if err := p.zoneRuleRepo.UpdateRuleStatusByIDWithTx(ctx, tx, r.Id, enum.VolumeRuleStatusActive); err != nil {
				logger.CtxLogErrorf(ctx, "active rule status fail, ruleId:%d, err:%+v", r.Id, err)
				continue
			}
			if err := p.zoneRuleRepo.DeployVolumeRuleDetail(ctx, tx, r); err != nil {
				logger.CtxLogErrorf(ctx, "deploy volume rule detail failed, ruleId:%d, err:%+v", r.Id, err)
				continue
			}
			ruleIds = append(ruleIds, strconv.FormatUint(r.Id, 10))
		}
		return nil
	}); err != nil {
		logger.CtxLogErrorf(ctx, "deploy volume rule failed=%v", err)
	}
	//上报配置变更系统
	ruleIdStr := strings.Join(ruleIds, "_")
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "VolumeRuleEffective", "Queue change to active", ruleIdStr, report_constant.RiskLevelMid)

	// 上报变更数量
	prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameRuleEffective, int64(len(ruleIds)))
	return nil
}

func (p *ZoneRuleMgrImpl) StatisticVolumeByOrder(ctx context.Context, forderId string) *srerr.Error {
	if err := p.volumeRoutingSrv.IncreaseVolumeLineDimension(ctx, forderId); err != nil {
		return err
	}
	if err := p.volumeRoutingSrv.IncreaseVolumeZoneDimension(ctx, forderId); err != nil {
		return err
	}
	return nil
}

func (z *ZoneRuleMgrImpl) DeployForecastVolumeRule(ctx context.Context, tx scormv2.SQLCommon, volumeRule persistent.VolumeRoutingRuleTab, groupIdMap map[string]struct{}) *srerr.Error {
	// 1.查找出volume rule detail对应的groupIds
	var groupIds []string
	if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).
		Where("rule_id = ?", volumeRule.Id).
		Select("distinct(group_id)").Find(&groupIds).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	// 2.把相关的zone Group都需要复制，新数据覆盖旧数据 todo 重复deploy的Group后面的覆盖前面的数据
	for _, groupId := range groupIds {
		if _, ok := groupIdMap[groupId]; ok || groupId == "" {
			continue // 如果重复就跳过，避免重复创建volume group
		} else {
			groupIdMap[groupId] = struct{}{}
		}
		// 按照条件筛选出Location postcode cep三张表Forecast数据
		condition := map[string]interface{}{
			constant.GroupIdSql:        groupId,
			constant.RoutingTypeSql:    volumeRule.RoutingType,
			constant.IsForecastTypeSql: constant.ForecastType,
		}
		// 3.1 根据GroupId找到预测对应的Group配置
		var data persistent.VolumeZoneGroupTab
		if err := dbutil.Take(ctx, persistent.VolumeZoneGroupHook, condition, &data); err != nil {
			return srerr.With(srerr.ZoneGroupNotFound, nil, err)
		}
		if data.Id == 0 {
			return srerr.New(srerr.ZoneGroupNotFound, groupId, fmt.Sprintf("Not found forecast group by groupId=%s", groupId))
		}
		// 3.2 复制对应的预测Group
		data.Id = 0
		data.IsForecastType = false
		data.Operator = volumeRule.Operator
		data.Ctime = timeutil.GetCurrentUnixTimeStamp(ctx)
		data.Mtime = timeutil.GetCurrentUnixTimeStamp(ctx)

		if err := z.createOrUpdateGroup(ctx, tx, data); err != nil {
			return err
		}

		// 3.4 先删除旧的Location数据，然后创建新的
		if err := z.zoneRepo.CreateZoneWithTx(ctx, tx, condition, enum.ZoneTypeLocation, volumeRule.RoutingType); err != nil {
			return err
		}
		// 3.5 先删除旧的postcode数据，然后创建新的
		if err := z.zoneRepo.CreateZoneWithTx(ctx, tx, condition, enum.ZoneTypePostcode, volumeRule.RoutingType); err != nil {
			return err
		}
		// 3.6 先删除旧的cepRange数据，然后创建新的
		if err := z.zoneRepo.CreateZoneWithTx(ctx, tx, condition, enum.ZoneTypeCEPRange, volumeRule.RoutingType); err != nil {
			return err
		}

		if err := z.zoneRepo.CreateProductLineWithTx(ctx, tx, condition); err != nil {
			return err
		}
	}

	return nil
}

func (z *ZoneRuleMgrImpl) CopyVolumeRuleWithNewStatus(ctx context.Context, ruleId int64, status enum.VolumeRuleStatus, EffectiveStartTime int64, isForecastType bool) (uint64, *srerr.Error) {
	// 1.根据id找到对应的rule
	ruleList, err := z.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{"id = ?": ruleId})
	if err != nil || len(ruleList) == 0 {
		logger.CtxLogErrorf(ctx, "Query volume rule err=%v or not found", err)
		return 0, err
	}
	volumeRule := ruleList[0]
	//if !isCloneStatus(volumeRule.RuleStatus) {
	//	logger.CtxLogErrorf(ctx, "rule status is not submit、queue、active、expire", err)
	//	return 0, srerr.New(srerr.VolumeRuleError, ruleId, "rule status is not submit、queue、active、expire", err)
	//}
	// 2.查找对应的volume rule detail
	volumeDetailList, derr := z.zoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{"rule_id = ?": ruleId, "data_version = ?": volumeRule.DataVersion})
	if derr != nil || len(volumeDetailList) == 0 {
		logger.CtxLogErrorf(ctx, "Query volume rule detail err=%v or not found volume rule detail,len=%v", derr, len(volumeDetailList))
		return 0, derr
	}
	operator, _ := apiutil.GetUserInfo(ctx)
	nowTs := timeutil.GetCurrentUnixTimeStamp(ctx)
	// 3.清空对应的数据
	volumeRule.Id = 0
	volumeRule.RuleStatus = status
	volumeRule.DataVersion = 0
	volumeRule.EffectiveStartTime = EffectiveStartTime
	volumeRule.IsForecastType = isForecastType
	volumeRule.Operator = operator
	volumeRule.Ctime = nowTs
	volumeRule.Mtime = nowTs
	// 4.创建新rule
	newRuleId, nErr := z.zoneRuleRepo.CreateZoneRule(ctx, &volumeRule)
	if nErr != nil {
		logger.CtxLogErrorf(ctx, "create new volume rule err=%v", err)
		return 0, err
	}

	for i := 0; i < len(volumeDetailList); i++ {
		volumeDetailList[i].Id = 0
		volumeDetailList[i].RuleId = newRuleId
		volumeDetailList[i].DataVersion = 0
		volumeDetailList[i].IsForecastType = isForecastType
		volumeDetailList[i].Operator = operator
		volumeDetailList[i].Ctime = nowTs
		volumeDetailList[i].Mtime = nowTs
	}
	// 5.创建新的rule detail
	if err := z.zoneRuleRepo.CreateZoneRuleDetails(ctx, volumeDetailList); err != nil {
		logger.CtxLogErrorf(ctx, "create new volume rule detail err=%v", err)
		return 0, err
	}

	return newRuleId, nil
}

//func isCloneStatus(status enum.VolumeRuleStatus) bool {
//	return status == enum.VolumeRuleStatusActive || status == enum.VolumeRuleStatusQueuing ||
//		status == enum.VolumeRuleStatusSubmit || status == enum.VolumeRuleStatusExpired
//}

func (z *ZoneRuleMgrImpl) CheckCanDeployForecastRule(ctx context.Context, ruleId int64) *srerr.Error {
	ruleList, err := z.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
		"id = ?": ruleId,
	})
	if err != nil {
		return err
	}
	if len(ruleList) == 0 { // 这里不需要强校验volume rule不存在，因为有些可以不用开运力因子，所以打印一条日志
		logger.CtxLogErrorf(ctx, "Volume rule not found by id=%v", ruleId)
		return nil
	}

	// 只check submit状态的rule,如果是非cb的预测就直接跳过
	volumeRule := ruleList[0]
	if volumeRule.RuleStatus != enum.VolumeRuleStatusSubmit || !volumeutil.IsCBForecast(volumeRule.RoutingType, volumeRule.IsForecastType) {
		return nil
	}

	volumeRuleDetails, derr := z.zoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{
		"rule_id = ?": volumeRule.Id,
	})
	if derr != nil {
		return derr
	}
	groupIdMap := make(map[string]struct{})
	for _, detail := range volumeRuleDetails {
		if detail.GroupId == "" {
			continue
		}
		if _, ok := groupIdMap[detail.GroupId]; ok {
			continue
		}

		groupInfo, err := z.zoneGroupRepo.GetGroupBaseByGroupId(ctx, detail.GroupId, volumeRule.RoutingType, volumeRule.IsForecastType)
		if err != nil {
			return err
		}

		if err := z.checkZoneUpdate(ctx, groupInfo, volumeRule.Mtime); err != nil {
			return err
		}
		groupIdMap[detail.GroupId] = struct{}{}
	}

	return nil
}

// 用volume rule最后的修改时间，如果有对应的地址信息的修改时间在volume rule修改时间之后则报错返回前端提示错误
func (z *ZoneRuleMgrImpl) checkZoneUpdate(ctx context.Context, group *persistent.VolumeZoneGroupTab, submitTime int64) *srerr.Error {
	// 1.筛选条件为提交volume rule后有变更的地址信息
	condition := map[string]interface{}{
		constant.GroupIdSql:        group.GroupId,
		constant.RoutingTypeSql:    group.RoutingType,
		constant.IsForecastTypeSql: group.IsForecastType,
		"mtime > ?":                submitTime,
	}
	//2. 一个group只能对应一种地址类型，按照类别查询对应group的地址信息是否变更
	switch group.ZoneType {
	case enum.ZoneTypeLocation:
		locList, err := z.zoneRepo.GetLocationZoneList(ctx, condition)
		if err != nil || len(locList) != 0 {
			return srerr.New(srerr.VolumeRuleError, nil, "Query location type err=%v or groupId=%v,routingType=%d has changed", err, group.GroupId, group.RoutingType)
		}
	case enum.ZoneTypePostcode:
		postList, err := z.zoneRepo.GetPostcodeZoneList(ctx, condition)
		if err != nil || len(postList) != 0 {
			return srerr.New(srerr.VolumeRuleError, nil, "Query postcode type err=%v or groupId=%v,routingType=%d has changed", err, group.GroupId, group.RoutingType)
		}
	case enum.ZoneTypeCEPRange:
		cepList, err := z.zoneRepo.GetPostcodeZoneList(ctx, condition)
		if err != nil || len(cepList) != 0 {
			return srerr.New(srerr.VolumeRuleError, nil, "Query cep type err=%v or groupId=%v,routingType=%d has changed", err, group.GroupId, group.RoutingType)
		}
	}

	return nil
}

func (z *ZoneRuleMgrImpl) createOrUpdateGroup(ctx context.Context, tx scormv2.SQLCommon, groupInfo persistent.VolumeZoneGroupTab) *srerr.Error {
	var existed persistent.VolumeZoneGroupTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneGroupHook, map[string]interface{}{
		constant.GroupIdSql:        groupInfo.GroupId,
		constant.IsForecastTypeSql: groupInfo.IsForecastType,
		constant.RoutingTypeSql:    groupInfo.RoutingType,
	}, &existed); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if existed.Id != 0 {
		groupInfo.Id = existed.Id
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Updates(&groupInfo).GetError(); err != nil {
			return srerr.With(srerr.DatabaseErr, nil, err)
		}
	} else {
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Create(&groupInfo).GetError(); err != nil {
			return srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	return nil
}

func (z *ZoneRuleMgrImpl) GetAllGroup(ctx context.Context, routingType int, isForecastType bool) ([]schema.GroupDetail, *srerr.Error) {
	groupList, err := z.zoneGroupRepo.QueryByCondition(ctx, map[string]interface{}{
		constant.RoutingTypeSql:    routingType,
		constant.IsForecastTypeSql: isForecastType,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "query all zone group err=%v", err)
		return nil, err
	}
	resp := make([]schema.GroupDetail, 0, len(groupList))
	for _, group := range groupList {
		resp = append(resp, schema.GroupDetail{
			GroupId:   group.GroupId,
			GroupName: group.GroupName,
		})
	}

	return resp, nil
}

func (p *ZoneRuleMgrImpl) GetLineRelateProductInfo(ctx context.Context, productIdList []int64) (map[string]lpsclient.LineDict, map[string][]int64, *srerr.Error) {
	// 1. 查询多个product的line信息
	// 2. 构建line map及line关联的product map
	lineInfoMap := make(map[string]lpsclient.LineDict)
	lineRelateProductMap := make(map[string][]int64)
	for _, productId := range productIdList {
		lineRet, err := p.lpsApi.GetLineDictList(ctx, productId)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get line dict list fail, productId:%d, err:%+v", productId, err)
			return nil, nil, err
		}
		if lineRet == nil {
			continue
		}
		for _, lineInfo := range lineRet.LineDictList {
			if _, ok := lineInfoMap[lineInfo.LineId]; !ok {
				lineInfoMap[lineInfo.LineId] = lineInfo
			}
			lineRelateProductMap[lineInfo.LineId] = append(lineRelateProductMap[lineInfo.LineId], productId)
		}
	}
	return lineInfoMap, lineRelateProductMap, nil
}

func (p *ZoneRuleMgrImpl) initRuleLimitImportTemplateData(ctx context.Context, productIdList []int64, routingType int) ([][]string, *srerr.Error) {
	dataArr := make([][]string, 0)
	lineInfoMap, lineRelateProductMap, err := p.GetLineRelateProductInfo(ctx, productIdList)
	if err != nil {
		return nil, err
	}
	forecastType := constant.NonForecastType
	if routingType == rule.CBRoutingType {
		forecastType = constant.ForecastType
	}
	for _, line := range lineInfoMap {
		groupId, gErr := p.zoneGroupRepo.GetGroupIdByLine(ctx, line.LineId, routingType, forecastType)
		logger.CtxLogInfof(ctx, "GetGroupIdByLine err=%v lineid=%v gid=%v ", gErr, line.LineId, groupId)
		if gErr != nil {
			continue
		}
		if groupId == "" {
			continue
		}
		zoneType, zErr := p.zoneGroupRepo.GetZoneTypeByGroupId(ctx, groupId, routingType, forecastType)
		if zErr != nil {
			logger.CtxLogErrorf(ctx, "GetZoneTypeByGroupId err=%v groupid=%v", zErr, groupId)
			continue
		}
		zoneNameSet, zzErr := p.zoneRepo.GetZoneNameListByGroupId(ctx, groupId, routingType, forecastType, zoneType)
		if zzErr != nil {
			logger.CtxLogErrorf(ctx, "GetZoneNameListByGroupId err=%v gid=%v ztype=%v ", zzErr, groupId, zoneType)
			continue
		}
		lineStr := objutil.Join("-", line.LineId, line.LineName, lfslib.LineSubTypeMap[int32(line.LineType)])
		logger.CtxLogInfof(ctx, "zone-name-len %v", len(zoneNameSet))
		// 获取product id
		if _, ok := lineRelateProductMap[line.LineId]; !ok {
			return nil, srerr.New(srerr.ParamErr, nil, "line relate product is nil, lineId=%s", line.LineId)
		}
		productIdListStr := strings.Join(objutil.Int64sToStrings(lineRelateProductMap[line.LineId]), vrservice.ProductRelateSymbol)
		for zoneName := range zoneNameSet {
			dataArr = append(dataArr, []string{lineStr, groupId, zoneName, productIdListStr, "", "", "", "", "", ""})
		}
	}
	return dataArr, nil
}

func (p *ZoneRuleMgrImpl) ExportRuleLimitTemplate(ctx context.Context, productIdList []int64, routingType int) ([]byte, *srerr.Error) {
	dataArr, err := p.initRuleLimitImportTemplateData(ctx, productIdList, routingType)
	if err != nil {
		return nil, err
	}

	emptySheetName := ""
	f, fErr := fileutil.MakeExcel(ctx, getTemplateHeader(), dataArr, emptySheetName)
	if fErr != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailDownloadFail, nil, fErr)
	}
	b, bErr := f.WriteToBuffer()
	if bErr != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailDownloadFail, nil, bErr)
	}
	return b.Bytes(), nil
}

func getTemplateHeader() []string {
	return []string{"Line", "*Volume Zone Group ID", "*Zone Name", "Product", "Zone Min Daily Limit", "Zone Max Daily Limit",
		"Max COD Limit", "Max Bulky Limit", "Max High-Value Limit", "Max DG Limit"}
}

func (p *ZoneRuleMgrImpl) IsLocalSmartRouting(ctx context.Context, productId int64) bool {
	conf, err := p.routingConfigRepo.GetConfigByProductID(ctx, productId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get routing config failed=%v", err)
		return false
	}
	return conf.LocalSmartRoutingEnabled
}

func (p *ZoneRuleMgrImpl) SendEmailByPis(ctx context.Context, checkResultList []vrentity.CheckZoneLimitInfoMsg, operator string) {
	if len(checkResultList) == 0 {
		return
	}
	// 1. 获取s3文件
	s3FileUrl, err := p.GenerateS3File(ctx, checkResultList)
	if err != nil {
		logger.CtxLogInfof(ctx, "generate zone limit check message file error, err=%v", err)
		return
	}
	// 2. 发送邮件
	err = p.lnpApi.SendEmail(ctx, s3FileUrl, []string{operator})
	if err != nil {
		logger.CtxLogErrorf(ctx, "send zone limit check message file email error, err=%v", err)
	}
}

func (p *ZoneRuleMgrImpl) GenerateS3File(ctx context.Context, checkResultList []vrentity.CheckZoneLimitInfoMsg) (string, *srerr.Error) {
	//1. 生成导出数据
	var dataList [][]string
	for _, checkResult := range checkResultList {
		dataList = append(dataList, []string{checkResult.ProductId, checkResult.LineId, checkResult.ZoneGroup, checkResult.ZoneName, checkResult.Message})
	}
	//2. 生成excel文件
	emptySheetName := ""
	file, err := fileutil.MakeExcel(ctx, importZoneLimitMsgHeader, dataList, emptySheetName)
	if err != nil {
		return "", srerr.With(srerr.ZoneRuleDetailExportFail, nil, err)
	}
	b, bErr := file.WriteToBuffer()
	if bErr != nil {
		return "", srerr.With(srerr.ZoneRuleDetailExportFail, nil, bErr)
	}
	//3. 生成s3文件
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Zone Limit Import Msg-%v%s", timeutil.GetCurrentTime(ctx).Unix(), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil
}
