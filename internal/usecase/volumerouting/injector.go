package volumerouting

import "github.com/google/wire"

var ZoneGroupMgrProviderSet = wire.NewSet(
	NewZoneGroupManagerImpl,
	wire.Bind(new(ZoneGroupManager), new(*ZoneGroupManagerImpl)),
)

var ZoneMgrProviderSet = wire.NewSet(
	NewZoneManagerImpl,
	wire.Bind(new(ZoneManager), new(*ZoneManagerImpl)),
)

var ZoneRuleMgrProviderSet = wire.NewSet(
	NewZoneRuleMgrImpl,
	wire.Bind(new(ZoneRuleMgr), new(*ZoneRuleMgrImpl)),
)
