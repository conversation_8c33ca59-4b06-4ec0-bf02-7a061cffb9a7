package volumerouting

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/db_lock"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/taskschema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"hash/crc32"
	"strconv"
	"strings"
)

type ZoneManager interface {
	GetLocationZoneListPage(ctx context.Context, param *schema.LocationZonePageParam) (*schema.LocationZonePageResult, *srerr.Error)
	GetPostcodeZoneListPage(ctx context.Context, param *schema.PostcodeZonePageParam) (*schema.PostcodeZonePageResult, *srerr.Error)
	GetCepRangeZoneListPage(ctx context.Context, param *schema.CepRangeZonePageParam) (*schema.CepRangeZonePageResult, *srerr.Error)
	DeleteLocationZone(ctx context.Context, param *schema.ZoneLocationDeleteParam) *srerr.Error
	DeletePostcodeZone(ctx context.Context, param *schema.ZonePostcodeDeleteParam) *srerr.Error
	DeleteCepRangeZone(ctx context.Context, param *schema.ZoneCepRangeDeleteParam) *srerr.Error
	BuildZoneExportTask(ctx context.Context, param *schema.ZoneExportParam, operator string) *srerr.Error
	BuildZoneImportTask(ctx context.Context, param *schema.ZoneImportParam, operator string) *srerr.Error
	HandleExecutableZoneImportTask(ctx context.Context, args saturn.JobArgs) *srerr.Error
	HandleZoneExportTask(ctx context.Context, taskId string) *srerr.Error
	GetTaskHistoryListPage(ctx context.Context, param *schema.VolumeRoutingHistoryQueryParam) (*schema.VolumeRoutingHistoryPageResult, *srerr.Error)
	Download(ctx context.Context, taskId string) (string, []byte, *srerr.Error)
	// for async task test func
	TestZoneExport(ctx context.Context, param *schema.ZoneExportParam, operator string) *srerr.Error
	TestZoneImport(ctx context.Context, param *schema.ZoneImportParam, operator string) *srerr.Error
}

type ZoneManagerImpl struct {
	volumeRoutingSrv vrservice.Service
	zoneRepo         vrrepo.ZoneRepo
	taskRepo         vrrepo.TaskRepo
	zoneRule         vrrepo.ZoneRuleRepo
}

func NewZoneManagerImpl(
	volumeRoutingSrv vrservice.Service,
	zoneRepo vrrepo.ZoneRepo,
	taskRepo vrrepo.TaskRepo,
	zoneRule vrrepo.ZoneRuleRepo,
) *ZoneManagerImpl {
	return &ZoneManagerImpl{
		volumeRoutingSrv: volumeRoutingSrv,
		zoneRepo:         zoneRepo,
		taskRepo:         taskRepo,
		zoneRule:         zoneRule,
	}
}

func (p *ZoneManagerImpl) GetLocationZoneListPage(ctx context.Context, param *schema.LocationZonePageParam) (*schema.LocationZonePageResult, *srerr.Error) {
	list, total, err := p.zoneRepo.LocationZonePage(ctx, param.FormatQueryCondition(), param.Offset, param.Size)
	if err != nil {
		return nil, err
	}
	result := schema.LocationZonePageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   nil,
	}
	models := make([]schema.LocationZonePageModel, 0)
	for _, data := range list {
		models = append(models, schema.LocationZonePageModel{
			Id:          data.Id,
			ZoneGroupId: data.GroupId,
			ZoneName:    data.ZoneName,
			Region:      envvar.GetCID(),
			LocationId:  data.LocationId,
			State:       data.State,
			City:        data.City,
			District:    data.District,
			Street:      data.Street,
			Operator:    data.Operator,
			UpdateTime:  timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(data.Mtime)),
		})
	}
	result.List = models
	return &result, nil
}

func (p *ZoneManagerImpl) GetPostcodeZoneListPage(ctx context.Context, param *schema.PostcodeZonePageParam) (*schema.PostcodeZonePageResult, *srerr.Error) {
	list, total, err := p.zoneRepo.PostcodeZonePage(ctx, param.FormatQueryCondition(), param.Offset, param.Size)
	if err != nil {
		return nil, err
	}
	result := schema.PostcodeZonePageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   nil,
	}
	models := make([]schema.PostcodeZonePageModel, 0)
	for _, data := range list {
		models = append(models, schema.PostcodeZonePageModel{
			Id:          data.Id,
			ZoneGroupId: data.GroupId,
			ZoneName:    data.ZoneName,
			Region:      envvar.GetCID(),
			Postcode:    data.Postcode,
			Operator:    data.Operator,
			UpdateTime:  timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(data.Mtime)),
		})
	}
	result.List = models
	return &result, nil
}

func (p *ZoneManagerImpl) GetCepRangeZoneListPage(ctx context.Context, param *schema.CepRangeZonePageParam) (*schema.CepRangeZonePageResult, *srerr.Error) {
	list, total, err := p.zoneRepo.CepRangeZonePage(ctx, param.FormatQueryCondition(), param.Offset, param.Size)
	if err != nil {
		return nil, err
	}
	result := schema.CepRangeZonePageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   nil,
	}
	models := make([]schema.CepRangeZonePageModel, 0)
	for _, data := range list {
		models = append(models, schema.CepRangeZonePageModel{
			Id:          data.Id,
			ZoneGroupId: data.GroupId,
			ZoneName:    data.ZoneName,
			Region:      envvar.GetCID(),
			CepInitial:  data.CepInitial,
			CepFinal:    data.CepFinal,
			Operator:    data.Operator,
			UpdateTime:  timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(data.Mtime)),
		})
	}
	result.List = models
	return &result, nil
}

func (p *ZoneManagerImpl) DeleteLocationZone(ctx context.Context, param *schema.ZoneLocationDeleteParam) *srerr.Error {
	if err := p.checkCanDelete(ctx, param.Id, enum.ZoneTypeLocation); err != nil {
		return err
	}
	return p.zoneRepo.DeleteLocationZone(ctx, param.FormatQueryCondition())
}

func (p *ZoneManagerImpl) DeletePostcodeZone(ctx context.Context, param *schema.ZonePostcodeDeleteParam) *srerr.Error {
	if err := p.checkCanDelete(ctx, param.Id, enum.ZoneTypePostcode); err != nil {
		return err
	}
	return p.zoneRepo.DeletePostcodeZone(ctx, param.FormatQueryCondition())
}

func (p *ZoneManagerImpl) DeleteCepRangeZone(ctx context.Context, param *schema.ZoneCepRangeDeleteParam) *srerr.Error {
	if err := p.checkCanDelete(ctx, param.Id, enum.ZoneTypeCEPRange); err != nil {
		return err
	}
	return p.zoneRepo.DeleteCepRangeZone(ctx, param.FormatQueryCondition())
}

func (p *ZoneManagerImpl) BuildZoneExportTask(ctx context.Context, param *schema.ZoneExportParam, operator string) *srerr.Error {
	taskRecord := persistent.VolumeTaskRecordTab{
		TaskId:        strings.ReplaceAll(uuid.NewV4().String(), "-", ""), // nolint
		TaskName:      constant.TaskNameZoneExport,
		OperationType: enum.TaskOperationTypeExport,
		ZoneGroupId:   param.ZoneGroupId,
		FileName:      "",
		RemotePath:    "",
		TaskStatus:    enum.TaskStatusInProcess,
		Operator:      operator,
		Ctime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Mtime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Param:         objutil.JsonBytes(param),
		RoutingType:   param.RoutingType,
	}
	if err := p.taskRepo.CreateNewTask(ctx, &taskRecord); err != nil {
		return err
	}
	msgBody := objutil.JsonBytes(taskschema.VolumeTaskParam{TaskId: taskRecord.TaskId})
	//todo: shardId reset
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(ctx, namespace, taskRecord.TaskName, msgBody, nil, ""); err != nil {
		return srerr.With(srerr.SendAsyncJobMsgFail, nil, err)
	}
	return nil
}

func (p *ZoneManagerImpl) HandleZoneExportTask(ctx context.Context, taskId string) *srerr.Error {
	//taskId ??,setyologid ??,handled ?,
	task, err := p.taskRepo.GetTask(ctx, taskId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "handleZoneExportTask fail, task not found, taskId:%s, error:%+v", taskId, err)
		return err
	}
	if task.TaskStatus != enum.TaskStatusInProcess {
		logger.CtxLogErrorf(ctx, "handleZoneExportTask fail, invalid task status, taskId:%s, taskStatus:%s", taskId, enum.TaskStatusNameMap[task.TaskStatus])
		return srerr.New(srerr.ZoneExportFail, nil, "invalid status for zone export, status: %s",
			enum.TaskStatusNameMap[task.TaskStatus])
	}
	var param schema.ZoneExportParam
	if err := jsoniter.Unmarshal(task.Param, &param); err != nil {
		logger.CtxLogErrorf(ctx, "handleZoneExportTask fail, zone export param unmarshal fail, taskId:%s, error:%+v", taskId, err)
		return p.taskRepo.UpdateByTaskId(ctx, task.TaskId, map[string]interface{}{
			"task_status": enum.TaskStatusFailed,
			"fail_reason": err.Error()[:120],
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		})
	}
	zoneType, condition := param.GetExportInfo()
	remotePath, err := p.volumeRoutingSrv.ExportZoneListByTask(ctx, zoneType, condition, task.TaskId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "handleZoneExportTask fail, handle export operation fail, taskId:%s, error:%+v", taskId, err)
		return p.taskRepo.UpdateByTaskId(ctx, task.TaskId, map[string]interface{}{
			"task_status": enum.TaskStatusFailed,
			"fail_reason": err.Error()[:120],
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		})
	}
	filename := objutil.Merge("volume zone export-",
		strings.ToLower(enum.ZoneTypeNameMap[zoneType]),
		timeutil.FormatDate(timeutil.GetCurrentTime(ctx)),
		".xlsx",
	)
	logger.CtxLogInfof(ctx, "handleZoneExportTask success, taskId:%s, filename:%s", taskId, filename)
	return p.taskRepo.UpdateByTaskId(ctx, task.TaskId, map[string]interface{}{
		"file_name":   filename,
		"remote_path": remotePath,
		"task_status": enum.TaskStatusDone,
		"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
	})

}

func (p *ZoneManagerImpl) BuildZoneImportTask(ctx context.Context, param *schema.ZoneImportParam, operator string) *srerr.Error {
	taskRecord := persistent.VolumeTaskRecordTab{
		TaskId:        strings.ReplaceAll(uuid.NewV4().String(), "-", ""), // nolint
		TaskName:      constant.TaskNameZoneImport,
		OperationType: enum.TaskOperationTypeImport,
		ZoneGroupId:   param.ZoneGroupId,
		FileName:      param.FileName,
		RemotePath:    param.FileUrl,
		TaskStatus:    enum.TaskStatusInProcess,
		Operator:      operator,
		Ctime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Mtime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Param:         objutil.JsonBytes(param),
		RoutingType:   param.RoutingType,
	}
	//if list, _ := p.taskRepo.QueryCondition(ctx, map[string]interface{}{
	//	"zone_group_id = ?": param.ZoneGroupId,
	//	"task_name = ?":     constant.TaskNameZoneImport,
	//	"task_status = ?":   enum.TaskStatusInProcess,
	//}); len(list) > 0 {
	//	taskRecord.TaskStatus = enum.TaskStatusInitial
	//}
	if err := p.taskRepo.CreateNewTask(ctx, &taskRecord); err != nil {
		return err
	}
	return nil
}

// job 会遗漏吗？ offsets ??,wait for next-rounds,
func shardJobList(tList []*persistent.VolumeTaskRecordTab, arg saturn.JobArgs) []*persistent.VolumeTaskRecordTab {
	//crc32.ChecksumIEEE,
	retList := []*persistent.VolumeTaskRecordTab{}

	for _, task := range tList {
		hashV := crc32.ChecksumIEEE([]byte(task.ZoneGroupId))
		if hashV%uint32(arg.TotalShardings) == uint32(arg.ShardingNo) {
			retList = append(retList, task)
		}
	}
	return retList
}

func formatTaskList(tlist []*persistent.VolumeTaskRecordTab) []string {
	ret := []string{}
	for _, val := range tlist {
		ret = append(ret, strings.Join([]string{val.TaskId, val.ZoneGroupId}, "_"))
	}
	return ret
}

func (p *ZoneManagerImpl) HandleExecutableZoneImportTask(ctx context.Context, arg saturn.JobArgs) *srerr.Error {
	startTime := recorder.Now(ctx).Unix()
	list, err := p.taskRepo.QueryCondition(ctx, map[string]interface{}{
		"task_status = ?": enum.TaskStatusInProcess,
		"task_name = ?":   constant.TaskNameZoneImport,
	})
	if err != nil {
		return err
	}
	if len(list) == 0 {
		logger.CtxLogInfof(ctx, "shardNo=%v | no zone import task executed currently", arg.ShardingNo)
		return nil
	}
	var results []*persistent.VolumeTaskRecordTab
	results = shardJobList(list, arg)
	if len(results) == 0 {
		logger.CtxLogInfof(ctx, "shardNo=%v | no zone import task executed on this fragment currently", arg.ShardingNo)
		return nil
	}
	logger.CtxLogInfof(ctx, "total.shards=%v shardNo=%v task.list %v", arg.TotalShardings, arg.ShardingNo, formatTaskList(results))
	taskIds := []string{}
	for _, task := range results {
		//add-lock ,groupid-levels ,
		if err := p.handleZoneImportTask(ctx, task); err != nil {
			logger.CtxLogErrorf(ctx, "handle zone import task fail, taskId:%s, err:%s", task.TaskId, err)
		}
		taskIds = append(taskIds, strconv.FormatUint(task.Id, 10))
		//if err := p.enableNextZoneImportTask(ctx, task.ZoneGroupId); err != nil {
		//	logger.CtxLogErrorf(ctx, "enable next zone import task fail, zoneGroup:%s, err:%s", task.ZoneGroupId, err)
		//}
	}

	taskIdStr := strings.Join(taskIds, ",")
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "VolumeZoneImport", "volume zone location、cep、postcode import", taskIdStr, report_constant.RiskLevelMid)

	// 上报变更数量
	prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameZoneImport, int64(len(taskIds)))
	return nil
}

func (p *ZoneManagerImpl) enableNextZoneImportTask(ctx context.Context, groupId string) *srerr.Error {
	list, _ := p.taskRepo.QueryCondition(ctx, map[string]interface{}{
		"zone_group_id = ?": groupId,
		"task_name = ?":     constant.TaskNameZoneImport,
		"task_status = ?":   enum.TaskStatusInitial,
	})
	if len(list) > 0 {
		return p.taskRepo.UpdateByTaskId(ctx, list[0].TaskId, map[string]interface{}{
			"task_status": enum.TaskStatusInProcess,
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		})
	}
	return nil
}

func (p *ZoneManagerImpl) handleZoneImportTask(ctx context.Context, task *persistent.VolumeTaskRecordTab) *srerr.Error {
	var param schema.ZoneImportParam
	ctx = logger.NewLogContext(ctx, "taskid_"+task.TaskId)
	if err := jsoniter.Unmarshal(task.Param, &param); err != nil {
		logger.CtxLogErrorf(ctx, "handle zone import fail, invalid task param, param:%s, error:%+v", objutil.Bytes2Str(task.Param), err)
		return srerr.With(srerr.ZoneImportGetFileFail, nil, err)
	}

	//add-groupid-level-LOCK,
	//Put task_status_update in same  TX with zone_info updates,
	locked, lockerPk := db_lock.TryGetLock(ctx, db_lock.ZoneImportDeadLockPeriod, db_lock.VolumeZoneImportBizmode, task.ZoneGroupId, task.TaskId)
	if !locked {
		//getLock-failed ,wait-next-round
		logger.CtxLogInfof(ctx, "getlock-failed %v", task.ZoneGroupId)
		return nil
	}
	logger.CtxLogInfof(ctx, "getlock-ok %v", task.ZoneGroupId)
	var updateData map[string]interface{}
	// 这里如果有volume rule状态处于queue则对应的Group不允许编辑
	queueStatusRuleIds, err := checkCanImport(ctx, param)
	if err != nil || len(queueStatusRuleIds) != 0 {
		updateData = map[string]interface{}{
			"task_status": enum.TaskStatusFailed,
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		}
		updateData["fail_reason"] = fmt.Sprintf("GroupId check can import err=%v or The volume rule status is queue with id=%+v", err, queueStatusRuleIds)
	} else {
		uploadData, hErr := p.volumeRoutingSrv.ImportZoneByTask(ctx, task, param)
		updateData = map[string]interface{}{
			"task_status": enum.TaskStatusDone,
			"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		}
		if hErr != nil {
			logger.CtxLogErrorf(ctx, "zone-import err=%v", hErr)
			updateData["task_status"] = enum.TaskStatusFailed
			//truncated,
			fr := hErr.Error()
			if len(fr) > 256 {
				fr = fr[:256]
			}
			updateData["fail_reason"] = fr
		}
		if len(uploadData) > 0 {
			remotePath, _ := p.volumeRoutingSrv.UploadResultFile(ctx, param.ZoneType, task.TaskId, uploadData)
			updateData["remote_path"] = remotePath
		}
	}

	//Unlock groupid-level lock
	db_lock.UnLock(ctx, lockerPk, task.ZoneGroupId)
	return p.taskRepo.UpdateByTaskId(ctx, task.TaskId, updateData)
}

func (p *ZoneManagerImpl) GetTaskHistoryListPage(ctx context.Context, param *schema.VolumeRoutingHistoryQueryParam) (*schema.VolumeRoutingHistoryPageResult, *srerr.Error) {
	list, total, err := p.taskRepo.Page(ctx, param.Offset, param.Size, param.RoutingType)
	if err != nil {
		return nil, err
	}
	ret := schema.VolumeRoutingHistoryPageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
		List:   nil,
	}
	var models []schema.VolumeRoutingHistoryPageModel
	for _, data := range list {
		models = append(models, schema.VolumeRoutingHistoryPageModel{
			TaskId:        data.TaskId,
			OperationType: data.OperationType,
			ZoneGroupId:   data.ZoneGroupId,
			FileName:      data.FileName,
			Status:        data.TaskStatus,
			Operator:      data.Operator,
			ErrMsg:        data.FailReason,
			OperationTime: timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(data.Mtime)),
		})
	}
	ret.List = models
	return &ret, nil
}

func (p *ZoneManagerImpl) Download(ctx context.Context, taskId string) (string, []byte, *srerr.Error) {
	task, err := p.taskRepo.GetTask(ctx, taskId)
	if err != nil {
		return "", nil, err
	}
	if task.RemotePath == "" {
		return "", nil, srerr.New(srerr.ParamErr, nil, "empty remove path, waiting task ending, taskId:%s", taskId)
	}
	if strings.Contains(task.RemotePath, "*@*") {
		fileLocArrs := strings.Split(task.RemotePath, "*@*")
		if len(fileLocArrs) < 2 {
			return "", nil, srerr.New(srerr.FormatErr, nil, "file remote path not found, remote path:%s", task.RemotePath)
		}
		data, fErr := fileutil.Download(ctx, fileLocArrs[0], fileLocArrs[1])
		if fErr != nil {
			return "", nil, srerr.New(srerr.S3DownloadFail, nil, "file download fail from s3 file server, err:%s", fErr.Error())
		}
		return renameFileName(task.FileName), data, nil
	}
	timeout := 30
	if configutil.GetVolumeRuleConf(ctx).HistoryDownloadTimeout != 0 {
		timeout = configutil.GetVolumeRuleConf(ctx).HistoryDownloadTimeout
	}
	data, gErr := httputil.Get(ctx, task.RemotePath, nil, timeout, nil)
	if gErr != nil {
		return "", nil, srerr.With(srerr.S3DownloadFail, nil, gErr)
	}

	return renameFileName(task.FileName), data, nil
}

func (p *ZoneManagerImpl) TestZoneExport(ctx context.Context, param *schema.ZoneExportParam, operator string) *srerr.Error {
	taskRecord := persistent.VolumeTaskRecordTab{
		TaskId:        strings.ReplaceAll(uuid.NewV4().String(), "-", ""), // nolint
		TaskName:      constant.TaskNameZoneExport,
		OperationType: enum.TaskOperationTypeExport,
		ZoneGroupId:   param.ZoneGroupId,
		FileName:      "",
		RemotePath:    "",
		TaskStatus:    enum.TaskStatusInProcess,
		Operator:      operator,
		Ctime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Mtime:         timeutil.GetCurrentUnixTimeStamp(ctx),
		Param:         objutil.JsonBytes(param),
	}
	if err := p.taskRepo.CreateNewTask(ctx, &taskRecord); err != nil {
		return err
	}
	return p.HandleZoneExportTask(ctx, taskRecord.TaskId)
}

func (p *ZoneManagerImpl) TestZoneImport(ctx context.Context, param *schema.ZoneImportParam, operator string) *srerr.Error {
	//task := persistent.VolumeTaskRecordTab{
	//	TaskId:        strings.ReplaceAll(uuid.NewV4().String(), "-", ""),
	//	TaskName:      constant.TaskNameZoneImport,
	//	OperationType: enum.TaskOperationTypeImport,
	//	ZoneGroupId:   param.ZoneGroupId,
	//	FileName:      param.FileName,
	//	FailReason:    "",
	//	RemotePath:    param.FileUrl,
	//	TaskStatus:    enum.TaskStatusInProcess,
	//	Operator:      operator,
	//	Ctime:         timeutil.GetCurrentUnixTimeStamp(ctx),
	//	Mtime:         timeutil.GetCurrentUnixTimeStamp(ctx),
	//	Param:         objutil.JsonBytes(param),
	//}
	//if err := p.taskRepo.CreateNewTask(ctx, &task); err != nil {
	//	return err
	//}
	//return p.HandleExecutableZoneImportTask(ctx, []uint64{0, 1, 2, 3, 4, 5, 6, 7, 8, 9})
	return nil
}

func checkCanImport(ctx context.Context, param schema.ZoneImportParam) ([]int64, error) {
	if !param.IsForecastType {
		return nil, nil
	}
	db, err := dbutil.SlaveDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport get db err=%v", err)
		return nil, err
	}
	var ruleIdList []int64
	if err := db.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).
		Where(constant.VolumeGroupSqlTemplate, param.ZoneGroupId, param.RoutingType, constant.NonForecastType).
		Select("distinct(rule_id)").Find(&ruleIdList).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport query VolumeRoutingRuleDetail err=%v", err)
		return nil, err
	}
	var queueStatusRuleId []int64
	if err := db.Table(persistent.VolumeRoutingRuleHook.TableName()).
		Where("id in (?) and rule_status = ?", ruleIdList, enum.VolumeRuleStatusQueuing).
		Select("id").Find(&queueStatusRuleId).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport query VolumeRoutingRuleHook err=%v", err)
		return nil, err
	}
	return queueStatusRuleId, nil
}

func (p *ZoneManagerImpl) checkCanDelete(ctx context.Context, id int64, zoneType enum.ZoneType) *srerr.Error {
	switch zoneType {
	case enum.ZoneTypeLocation:
		var location persistent.VolumeZoneLocationTab
		if err := p.zoneRepo.GetZoneInfoById(ctx, id, persistent.VolumeZoneLocationHook, &location); err != nil {
			return err
		}
		return p.doCheckCanDelete(ctx, location.GroupId, location.RoutingType, constant.NonForecastType)
	case enum.ZoneTypePostcode:
		var postcode persistent.VolumeZonePostcodeTab
		if err := p.zoneRepo.GetZoneInfoById(ctx, id, persistent.VolumeZonePostcodeHook, &postcode); err != nil {
			return err
		}
		return p.doCheckCanDelete(ctx, postcode.GroupId, postcode.RoutingType, constant.NonForecastType)
	case enum.ZoneTypeCEPRange:
		var cepRange persistent.VolumeZoneCepRangeTab
		if err := p.zoneRepo.GetZoneInfoById(ctx, id, persistent.VolumeZoneCepRangeHook, &cepRange); err != nil {
			return err
		}
		return p.doCheckCanDelete(ctx, cepRange.GroupId, cepRange.RoutingType, constant.NonForecastType)
	}

	return nil
}

func (p *ZoneManagerImpl) doCheckCanDelete(ctx context.Context, groupId string, routingType int, isForecastType bool) *srerr.Error {
	db, derr := dbutil.SlaveDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if derr != nil {
		logger.CtxLogErrorf(ctx, "Get db err=%v", derr)
		return srerr.With(srerr.DatabaseErr, nil, derr)
	}
	var ruleIds []int64
	if err := db.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).
		Where(constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).Select("rule_id").Find(&ruleIds).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "query rule detail err=%v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	ruleList, rerr := p.zoneRule.QueryRuleByCondition(ctx, map[string]interface{}{
		"id in (?)":       ruleIds,
		"rule_status = ?": enum.VolumeRuleStatusQueuing,
	})
	if rerr != nil || len(ruleList) != 0 {
		return srerr.New(srerr.DatabaseErr, nil, "zone management can't edit when the used routing rule is in queueing status")
	}

	return nil
}

func renameFileName(name string) string {
	index := strings.LastIndex(name, ".")
	newName := name[0:index] + "_result" + name[index:]
	return newName
}
