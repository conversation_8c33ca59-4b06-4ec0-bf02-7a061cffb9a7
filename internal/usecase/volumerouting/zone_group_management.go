package volumerouting

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"io"
	"strconv"
	"strings"
)

type ZoneGroupManager interface {
	GetZoneGroupListPage(ctx context.Context, param *schema.ZoneGroupPageParam) (*schema.ZoneGroupPageResult, *srerr.Error)
	CreateZoneGroup(ctx context.Context, param *schema.ZoneGroupCreateOrUpdateParam, operator string) *srerr.Error
	UpdateZoneGroup(ctx context.Context, param *schema.ZoneGroupCreateOrUpdateParam, operator string) *srerr.Error
	GetZoneGroupDetail(ctx context.Context, param *schema.ZoneGroupIdParam) (*schema.ZoneGroupDetailResult, *srerr.Error)
	SaveZoneEstimatedCapacity(ctx context.Context, param *schema.ZoneGroupZoneCapacityParam, operator string) *srerr.Error
	GetZoneCapacityListPage(ctx context.Context, param *schema.ZoneEstimatedCapacityPageParam) (*schema.ZoneEstimatedCapacityPageResult, *srerr.Error)
	ExportZoneCapacityList(ctx context.Context, param *schema.ZoneEstimatedCapacityExportParam) ([]byte, *srerr.Error)
	UploadZoneCapacity(ctx context.Context, groupId string, reader io.Reader, routingType int, operator string) *srerr.Error
	Dict(ctx context.Context) *schema.VolumeZoneDictResult
}

type ZoneGroupManagerImpl struct {
	zoneGroupRepo vrrepo.ZoneGroupRepo
	zoneRepo      vrrepo.ZoneRepo
	prodRepo      product.ProdRepo
}

func NewZoneGroupManagerImpl(
	zoneGroupRepo vrrepo.ZoneGroupRepo,
	zoneRepo vrrepo.ZoneRepo,
	prodRepo product.ProdRepo,
) *ZoneGroupManagerImpl {
	return &ZoneGroupManagerImpl{
		zoneGroupRepo: zoneGroupRepo,
		zoneRepo:      zoneRepo,
		prodRepo:      prodRepo,
	}
}

func (p *ZoneGroupManagerImpl) GetZoneGroupListPage(ctx context.Context, param *schema.ZoneGroupPageParam) (*schema.ZoneGroupPageResult, *srerr.Error) {
	condition, ok := p.getZoneGroupListPageQuery(ctx, param)
	if !ok {
		return &schema.ZoneGroupPageResult{
			Offset: param.Offset,
			Total:  0,
			Size:   param.Size,
			List:   make([]schema.ZoneGroupPageModel, 0),
		}, nil
	}
	zoneGroupList, total, pErr := p.zoneGroupRepo.Page(ctx, condition, param.Offset, param.Size)
	if pErr != nil {
		return nil, pErr
	}
	result := schema.ZoneGroupPageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
	}
	list := make([]schema.ZoneGroupPageModel, 0)
	if len(zoneGroupList) > 0 {
		groupIdSet := make(map[string]struct{})
		for _, zoneGroup := range zoneGroupList {
			groupIdSet[zoneGroup.GroupId] = struct{}{}
		}
		var groupIds []string
		for groupId := range groupIdSet {
			groupIds = append(groupIds, groupId)
		}
		groupRef, gErr := p.zoneGroupRepo.QueryProductLineRef(ctx, map[string]interface{}{"group_id in (?)": groupIds,
			constant.RoutingTypeSql:    param.RoutingType,
			constant.IsForecastTypeSql: param.IsForecastType})
		if gErr != nil {
			return nil, gErr
		}
		for _, zoneGroup := range zoneGroupList {
			model := schema.ZoneGroupPageModel{
				Id:         zoneGroup.Id,
				GroupId:    zoneGroup.GroupId,
				GroupName:  zoneGroup.GroupName,
				ZoneType:   enum.ZoneTypeNameMap[zoneGroup.ZoneType],
				Operator:   zoneGroup.Operator,
				UpdateTime: timeutil.FormatDateTime(timeutil.TransferTimeStampToTime(zoneGroup.Mtime)),
			}
			if refList, ok := groupRef[zoneGroup.GroupId]; ok {
				maskProductIdSet := make(map[int64]struct{})
				productIdSet := make(map[int64]struct{})
				lineIdSet := make(map[string]struct{})
				for _, ref := range refList {
					if ref.MaskProductId > 0 {
						maskProductIdSet[ref.MaskProductId] = struct{}{}
					}
					if ref.ProductId > 0 {
						productIdSet[ref.ProductId] = struct{}{}
					}
					if ref.LineId != "" {
						lineIdSet[ref.LineId] = struct{}{}
					}
				}
				maskProductIds, productIds := make([]int64, 0), make([]int64, 0)
				lineIds := make([]string, 0)
				for maskProductId := range maskProductIdSet {
					maskProductIds = append(maskProductIds, maskProductId)
				}
				for productId := range productIdSet {
					productIds = append(productIds, productId)
				}
				for lineId := range lineIdSet {
					lineIds = append(lineIds, lineId)
				}
				model.MaskProductIds = maskProductIds
				model.ProductIds = productIds
				model.LineIds = lineIds
			}
			list = append(list, model)
		}
	}
	result.List = list
	return &result, nil
}

func (p *ZoneGroupManagerImpl) getZoneGroupListPageQuery(ctx context.Context, param *schema.ZoneGroupPageParam) (map[string]interface{}, bool) {
	groupIdSet := make(map[string]struct{})
	var existProductLineCondition bool
	if param.MaskingProductId > 0 {
		if ret, _ := p.zoneGroupRepo.QueryProductLineRef(ctx, map[string]interface{}{"mask_product_id = ?": param.MaskingProductId}); len(ret) > 0 {
			for groupId := range ret {
				groupIdSet[groupId] = struct{}{}
			}
		}
		existProductLineCondition = true
	}
	if param.ProductId > 0 {
		if ret, _ := p.zoneGroupRepo.QueryProductLineRef(ctx, map[string]interface{}{"product_id = ?": param.ProductId}); len(ret) > 0 {
			for groupId := range ret {
				groupIdSet[groupId] = struct{}{}
			}
		}
		existProductLineCondition = true
	}
	if param.LineId != "" {
		if ret, _ := p.zoneGroupRepo.QueryProductLineRef(ctx, map[string]interface{}{"line_id = ?": param.LineId}); len(ret) > 0 {
			for groupId := range ret {
				groupIdSet[groupId] = struct{}{}
			}
		}
		existProductLineCondition = true
	}
	if existProductLineCondition && len(groupIdSet) == 0 {
		return nil, false
	}
	query := param.FormatQueryCondition()
	if len(groupIdSet) > 0 {
		var groupIds []string
		for groupId := range groupIdSet {
			groupIds = append(groupIds, groupId)
		}
		query["group_id in (?)"] = groupIds
	}
	return query, true
}

func (p *ZoneGroupManagerImpl) CreateZoneGroup(ctx context.Context, param *schema.ZoneGroupCreateOrUpdateParam, operator string) *srerr.Error {
	tab := persistent.VolumeZoneGroupTab{
		GroupId:        param.ZoneGroupId,
		GroupName:      param.ZoneGroupName,
		ZoneType:       param.ZoneType,
		GroupCapacity:  param.ZoneGroupCapacity,
		Operator:       operator,
		RoutingType:    param.RoutingType,
		IsForecastType: param.IsForecastType,
		Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
		Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	var refs []persistent.GroupProductLineRefTab
	if len(param.MaskProductIds) > 0 {
		for _, maskProductId := range param.MaskProductIds {
			refs = append(refs, persistent.GroupProductLineRefTab{
				GroupId:        param.ZoneGroupId,
				MaskProductId:  maskProductId,
				RoutingType:    param.RoutingType,
				IsForecastType: param.IsForecastType,
				Operator:       operator,
				Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			})
		}
	}
	if len(param.ProductIds) > 0 {
		for _, productId := range param.ProductIds {
			refs = append(refs, persistent.GroupProductLineRefTab{
				GroupId:        param.ZoneGroupId,
				ProductId:      productId,
				Operator:       operator,
				RoutingType:    param.RoutingType,
				IsForecastType: param.IsForecastType,
				Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			})
		}
	}
	if len(param.LineIds) > 0 {
		for _, lineId := range param.LineIds {
			refs = append(refs, persistent.GroupProductLineRefTab{
				GroupId:        param.ZoneGroupId,
				LineId:         lineId,
				Operator:       operator,
				RoutingType:    param.RoutingType,
				IsForecastType: param.IsForecastType,
				Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			})
		}
	}

	return p.zoneGroupRepo.Create(ctx, &tab, refs)
}

func (p *ZoneGroupManagerImpl) UpdateZoneGroup(ctx context.Context, param *schema.ZoneGroupCreateOrUpdateParam, operator string) *srerr.Error {
	updateData := param.FormatUpdateData(ctx, operator)
	return p.zoneGroupRepo.UpdateByGroupId(ctx, param.ZoneGroupId, updateData, param)
}

func (p *ZoneGroupManagerImpl) GetZoneGroupDetail(ctx context.Context, param *schema.ZoneGroupIdParam) (
	*schema.ZoneGroupDetailResult, *srerr.Error) {
	info, err := p.zoneGroupRepo.Get(ctx, param.Id)
	if err != nil {
		return nil, err
	}
	return &schema.ZoneGroupDetailResult{
		ZoneGroupId:       info.GroupId,
		ZoneGroupName:     info.GroupName,
		ZoneType:          info.ZoneType,
		ZoneGroupCapacity: info.GroupCapacity,
		MaskProductIds:    info.MaskProductIds,
		ProductIds:        info.ProductIds,
		LineIds:           info.LineIds,
	}, nil
}

func (p *ZoneGroupManagerImpl) SaveZoneEstimatedCapacity(ctx context.Context, param *schema.ZoneGroupZoneCapacityParam, operator string) *srerr.Error {
	zoneType, err := p.zoneGroupRepo.GetZoneTypeByGroupId(ctx, param.ZoneGroupId, param.RoutingType, constant.NonForecastType)
	if err != nil {
		return srerr.New(srerr.ZoneGroupNotFound, nil, "zone group not found, group:%s", param.ZoneGroupId)
	}
	if !p.zoneRepo.ExistZoneName(ctx, param.ZoneGroupId, zoneType, param.ZoneName, param.RoutingType, constant.NonForecastType) {
		return srerr.New(srerr.ZoneNameNotFound, nil, "zone name not found, group:%s, zone%s", param.ZoneGroupId, param.ZoneName)
	}
	return p.zoneGroupRepo.SaveZoneEstimatedCapacity(ctx, param.ZoneGroupId, param.ZoneName, param.Capacity, operator, param.RoutingType, constant.NonForecastType)
}

func (p *ZoneGroupManagerImpl) GetZoneCapacityListPage(ctx context.Context, param *schema.ZoneEstimatedCapacityPageParam) (*schema.ZoneEstimatedCapacityPageResult, *srerr.Error) {
	zoneCapacityList, total, err := p.zoneGroupRepo.GetZoneEstimateCapacityPage(ctx, param.ZoneGroupId, param.ZoneName, param.Offset, param.Size, param.RoutingType)
	if err != nil {
		return nil, err
	}
	result := schema.ZoneEstimatedCapacityPageResult{
		Offset: param.Offset,
		Total:  total,
		Size:   param.Size,
	}
	list := make([]schema.ZoneEstimatedCapacityPageModel, 0)
	for _, zoneCapacity := range zoneCapacityList {
		list = append(list, schema.ZoneEstimatedCapacityPageModel{
			ZoneName: zoneCapacity.ZoneName,
			Capacity: zoneCapacity.Capacity,
		})
	}
	result.List = list
	return &result, nil
}

var zoneCapacityHeader = []string{"*Zone Name", "Capacity"}

func (p *ZoneGroupManagerImpl) ExportZoneCapacityList(ctx context.Context, param *schema.ZoneEstimatedCapacityExportParam) ([]byte, *srerr.Error) {
	list, err := p.zoneGroupRepo.GetZoneEstimateCapacityList(ctx, param.ZoneGroupId, param.ZoneName, param.RoutingType)
	if err != nil {
		return nil, err
	}
	data := make([][]string, 0, len(list))
	for _, zc := range list {
		data = append(data, []string{zc.ZoneName, strconv.FormatInt(zc.Capacity, 10)})
	}
	emptySheetName := ""
	f, fErr := fileutil.MakeExcel(ctx, zoneCapacityHeader, data, emptySheetName)
	if fErr != nil {
		return nil, srerr.With(srerr.GroupCapacityParseExcelFail, nil, fErr)
	}
	b, wErr := f.WriteToBuffer()
	if wErr != nil {
		return nil, srerr.With(srerr.GroupCapacityParseExcelFail, nil, fErr)
	}
	return b.Bytes(), nil
}

func (p *ZoneGroupManagerImpl) UploadZoneCapacity(ctx context.Context, groupId string, reader io.Reader, routingType int, operator string) *srerr.Error {
	rows, _, err := fileutil.ParseExcel(ctx, reader, true)
	if err != nil {
		return srerr.With(srerr.GroupCapacityParseExcelFail, nil, err)
	}
	zoneType, _ := p.zoneGroupRepo.GetZoneTypeByGroupId(ctx, groupId, routingType, constant.NonForecastType)
	var list []persistent.GroupZoneCapacityRefTab
	uniqueZone := make(map[string]struct{}, len(rows))
	for i := 0; i < len(rows); i++ {
		var zoneName string
		var capacity int64
		if len(rows[i]) > 0 {
			zoneName = strings.TrimSpace(rows[i][0])
		}
		if !p.zoneRepo.ExistZoneName(ctx, groupId, zoneType, zoneName, routingType, constant.NonForecastType) {
			return srerr.New(srerr.GroupCapacityParseExcelFail, nil, "zone not found: %s", zoneName)
		}
		if len(rows[i]) > 1 && strings.TrimSpace(rows[i][1]) != "" {
			v, err := strconv.ParseInt(strings.TrimSpace(rows[i][1]), 10, 64)
			if err != nil {
				return srerr.New(srerr.GroupCapacityParseExcelFail, nil, "capacity format error: %s", rows[i][1])
			}
			if v < 0 {
				return srerr.New(srerr.GroupCapacityParseExcelFail, nil, "capacity requires positive number: %s", rows[i][1])
			}
			capacity = v
		}
		if _, ok := uniqueZone[zoneName]; ok {
			return srerr.New(srerr.GroupCapacityParseExcelFail, nil, "zone name duplicate, zoneName:%s", zoneName)
		}
		uniqueZone[zoneName] = struct{}{}
		list = append(list, persistent.GroupZoneCapacityRefTab{
			GroupId:     groupId,
			ZoneName:    zoneName,
			Capacity:    capacity,
			Operator:    operator,
			Ctime:       timeutil.GetCurrentUnixTimeStamp(ctx),
			Mtime:       timeutil.GetCurrentUnixTimeStamp(ctx),
			RoutingType: routingType,
		})
	}
	if err := p.zoneGroupRepo.BatchCreateGroupZoneCapacity(ctx, groupId, routingType, list); err != nil {
		return err
	}
	return nil
}

func (p *ZoneGroupManagerImpl) Dict(ctx context.Context) *schema.VolumeZoneDictResult {
	dictResult := schema.VolumeZoneDictResult{
		ZoneTypeEnum:   make([]schema.IntDictEnum, 0),
		RuleTypeEnum:   make([]schema.IntDictEnum, 0),
		RuleStatusEnum: make([]schema.IntDictEnum, 0),
		TaskStatusEnum: make([]schema.IntDictEnum, 0),
	}
	for k, v := range enum.ZoneTypeNameMap {
		dictResult.ZoneTypeEnum = append(dictResult.ZoneTypeEnum, schema.IntDictEnum{
			Id:   int(k),
			Name: v,
		})
	}
	for k, v := range enum.VolumeRuleTypeNameMap {
		dictResult.RuleTypeEnum = append(dictResult.RuleTypeEnum, schema.IntDictEnum{
			Id:   int(k),
			Name: v,
		})
	}
	for k, v := range enum.VolumeRuleStatusNameMap {
		dictResult.RuleStatusEnum = append(dictResult.RuleStatusEnum, schema.IntDictEnum{
			Id:   int(k),
			Name: v,
		})
	}
	for k, v := range enum.TaskStatusNameMap {
		dictResult.TaskStatusEnum = append(dictResult.TaskStatusEnum, schema.IntDictEnum{
			Id:   int(k),
			Name: v,
		})
	}
	return &dictResult
}
