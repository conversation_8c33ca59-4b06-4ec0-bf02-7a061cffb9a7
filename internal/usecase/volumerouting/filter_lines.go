package volumerouting

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const (
	BRVolumeRule = "BRVolumeRule"

	//MinVolume               = "MinVolume"
	MaxCapacity          = "MaxCapacity"
	MaxCodCapacity       = "MaxCodCapacity"
	MaxBulkyCapacity     = "MaxBulkyCapacity"
	MaxHighValueCapacity = "MaxHighValueCapacity"
	MaxDgCapacity        = "MaxDgCapacity"

	MinVolumeV2Name        = "MinVolumeV2"
	MaxCapacityV2Name      = "MaxCapacityV2"
	MaxCodCapacityV2       = "MaxCodCapacityV2"
	MaxBulkyCapacityV2     = "MaxBulkyCapacityV2"
	MaxHighValueCapacityV2 = "MaxHighValueCapacityV2"
	MaxDgCapacityV2        = "MaxDgCapacityV2"
)

func GetParcelCapacity(factorDetailName string, volumeRuleDetail vrentity.VolumeRuleDetail) int64 {
	switch factorDetailName {
	case MinVolumeV2Name:
		return volumeRuleDetail.Min
	case MaxCapacityV2Name:
		return volumeRuleDetail.Max
	case MaxCodCapacityV2:
		return volumeRuleDetail.MaxCod
	case MaxBulkyCapacityV2:
		return volumeRuleDetail.MaxBulky
	case MaxHighValueCapacityV2:
		return volumeRuleDetail.MaxHighValue
	case MaxDgCapacityV2:
		return volumeRuleDetail.MaxDg
	}
	return 0
}

func NeedFilterByParcelType(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
	cogs float64, dgType rule.DGFlag, parcelType string, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) bool {
	if parcelType == parcel_type_definition.ParcelTypeNone.String() {
		// 如果filter不属于三者(Cod/Bulky/HighValue)之一，则不用看Attribute了，需要进行Filter
		return true
	}
	// 区分SPXRoutingScenario, LocalRoutingScenario, CbMultiRoutingScenario
	parcelTypeAttr := GetParcelTypeAttr(ctx, productId, lineId, isCod, parcelDimension, cogs, dgType, parcelTypeDefinitionService, routingType)
	// 属于Cod/Bulky/HighValue/DG的Filter Type情况下
	if parcelType == parcel_type_definition.ParcelTypeCod.String() && parcelTypeAttr.IsCod {
		return true
	}

	if parcelType == parcel_type_definition.ParcelTypeDg.String() && parcelTypeAttr.IsDg {
		return true
	}

	if parcelType == parcel_type_definition.ParcelTypeBulky.String() && parcelTypeAttr.IsBulky {
		return true
	}

	if parcelType == parcel_type_definition.ParcelTypeHighValue.String() && parcelTypeAttr.IsHighValue {
		return true
	}

	return false
}

func GetParcelType(factorDetailName string) string {
	switch factorDetailName {
	case MaxCapacity:
		return parcel_type_definition.ParcelTypeNone.String()
	case MaxCodCapacity:
		return parcel_type_definition.ParcelTypeCod.String()
	case MaxBulkyCapacity:
		return parcel_type_definition.ParcelTypeBulky.String()
	case MaxHighValueCapacity:
		return parcel_type_definition.ParcelTypeHighValue.String()
	case MaxDgCapacity:
		return parcel_type_definition.ParcelTypeDg.String()
	case MinVolumeV2Name:
		return parcel_type_definition.ParcelTypeNone.String()
	case MaxCapacityV2Name:
		return parcel_type_definition.ParcelTypeNone.String()
	case MaxCodCapacityV2:
		return parcel_type_definition.ParcelTypeCod.String()
	case MaxBulkyCapacityV2:
		return parcel_type_definition.ParcelTypeBulky.String()
	case MaxHighValueCapacityV2:
		return parcel_type_definition.ParcelTypeHighValue.String()
	case MaxDgCapacityV2:
		return parcel_type_definition.ParcelTypeDg.String()
	}
	return ""
}

func GetParcelTypeAttr(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
	cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService,
	routingType int) *parcel_type_definition.ParcelTypeAttr {
	return parcelTypeDefinitionService.GetParcelTypeAttr(ctx, 0, int(productId), lineId, getScenarioTypeFromRoutingType(routingType),
		isCod, parcelDimension.Length, parcelDimension.Width, parcelDimension.Height, parcelDimension.Weight,
		cogs, true, pb.DgType(dgFlag))
}

func getScenarioTypeFromRoutingType(routingType int) int {
	switch routingType {
	case rule.SPXRoutingType:
		return parcel_type_definition2.SPXRoutingScenario
	case rule.LocalRoutingType:
		return parcel_type_definition2.LocalRoutingScenario
	case rule.CBRoutingType:
		return parcel_type_definition2.CbMultiRoutingScenario
	case rule.CbMultiRoutingType:
		return parcel_type_definition2.CbMultiRoutingScenario
	}
	return -1
}

func (p *ZoneRuleMgrImpl) FilterByLinesV2(ctx context.Context, ruleStep pb.RuleStep, productId int64, input []*rule.LineInfo,
	locInfo *pb.LocationInfo, routingType uint8, VolumeRuleId int64, factorDetailName string, isCod bool,
	parcelDimension rule.OrderParcelDimension, cogs float64) ([]*rule.LineInfo, map[string]string, *srerr.Error) {
	volumeRecordMap := make(map[string]string)
	volumeRule, err := p.zoneRuleRepo.GetRuleWithZoneLimitByProduct(ctx, productId, routingType, VolumeRuleId)
	if err != nil {
		return nil, volumeRecordMap, err
	}
	if volumeRule == nil {
		return input, volumeRecordMap, nil
	}
	//记录一下br命中的volume rule id
	volumeRecordMap[BRVolumeRule] = strconv.FormatUint(volumeRule.RuleId, 10)
	recordLineMaxOrMinVolume(volumeRecordMap, productId, volumeRule.LineLimits, factorDetailName)
	lineLimitMap := make(map[string]vrentity.VolumeRuleDetail)
	for i := 0; i < len(volumeRule.LineLimits); i++ {
		lineLimitMap[volumeRule.LineLimits[i].LineId] = volumeRule.LineLimits[i]
	}
	// 注意：Line Limit是可配置为0的，为0的时候代表业务不想优先给该3PL分单

	logger.CtxLogInfof(ctx, "rule.id=%v|type=%s ", volumeRule.RuleId, volumeRule.RuleType)

	var (
		availableLineList []*rule.LineInfo
		date              = timeutil.FormatDate(timeutil.GetLocalTime(ctx))
	)

	parcelType := GetParcelType(factorDetailName)
	if volumeRule.RuleType == enum.VolumeRuleTypeProduct {
		for _, lineInfo := range input {
			lineLimit, ok := lineLimitMap[lineInfo.ResourceId]
			if !ok {
				availableLineList = append(availableLineList, lineInfo)
				logger.CtxLogErrorf(ctx, "line limit not found | line=%s", lineInfo.ResourceId)
				continue
			}
			if !NeedFilterByParcelType(ctx, productId, lineLimit.LineId, isCod, parcelDimension, cogs, lineInfo.DGFlag,
				parcelType, p.parcelTypeDefinitionService, int(routingType)) {
				availableLineList = append(availableLineList, lineInfo)
				logger.CtxLogErrorf(ctx, "no needFilterByParcelType | lineId=%s, parcelType=%s", lineLimit.LineId, parcelType)
				continue
			}
			limit := GetParcelCapacity(factorDetailName, lineLimit)
			current, _ := p.getCurrentVolumeLineDimension(ctx, lineLimit.ProductIdList, lineInfo.ResourceId, date, parcelType)
			recordVolumeRecord(volumeRecordMap, current, productId, lineInfo.ResourceId, "", "")
			logger.CtxLogInfof(ctx, "product mode use %v, get volume by line dimension, product:%d, line:%s, date:%s, volume:%d, limit:%d",
				factorDetailName, productId, lineInfo.ResourceId, date, current, limit)
			if current < limit {
				availableLineList = append(availableLineList, lineInfo)
			}
		}
		return availableLineList, volumeRecordMap, nil
	}

	if volumeRule.RuleType == enum.VolumeRuleTypeLineVolumeZone {
		for _, lineInfo := range input {
			lineLimit, exist := lineLimitMap[lineInfo.ResourceId]
			if exist {
				if !NeedFilterByParcelType(ctx, productId, lineLimit.LineId, isCod, parcelDimension, cogs,
					lineInfo.DGFlag, parcelType, p.parcelTypeDefinitionService, int(routingType)) {
					availableLineList = append(availableLineList, lineInfo)
					logger.CtxLogErrorf(ctx, "no needFilterByParcelType | lineId=%s, parcelType=%s", lineLimit.LineId, parcelType)
					continue
				}
				current, _ := p.getCurrentVolumeLineDimension(ctx, lineLimit.ProductIdList, lineInfo.ResourceId, date, parcelType)
				recordVolumeRecord(volumeRecordMap, current, productId, lineInfo.ResourceId, "", "")
				// 先检查一下是否满足线维度运力要求，不满足则直接过滤这条line
				limit := GetParcelCapacity(factorDetailName, lineLimit)
				if current >= limit {
					logger.CtxLogInfof(ctx, "current line is %s,factor=%s,currentVolume=%d,limit=%d", lineInfo.LineId, pb.RuleStep_name[int32(ruleStep)], current, lineLimitMap[lineInfo.ResourceId])
					continue
				}
			} else {
				logger.CtxLogErrorf(ctx, "line limit not found | line=%s", lineInfo.ResourceId)
			}

			groupId, groupInfo, zoneNameList, ok := p.GetGroupInfoAndZoneNameList(ctx, lineInfo, volumeRule, routingType, locInfo)
			if !ok {
				availableLineList = append(availableLineList, lineInfo)
				continue
			}
			passLimit := false
			for _, zoneName := range zoneNameList {
				zoneLimit, err2 := p.zoneRuleRepo.GetRuleZoneLimits(ctx, productId, volumeRule.RuleId, volumeRule.DataVersion, groupInfo.GroupId, lineInfo.ResourceId, zoneName, volumeRule.RoutingType, volumeRule.IsForecastType)
				if err2 != nil {
					//可能存在zonename not configured ,比如新增了 zone ,但是未修改rules 配置limit;
					passLimit = true
					logger.CtxLogErrorf(ctx, "get rule zone limits failed | line=%s,zoneName=%s", lineInfo.ResourceId, zoneName)
					break
				}
				// 如果没有找到zone维度对应的运力限制则跳过zone维度校验，同时认为这条line通过校验
				if zoneLimit == nil {
					// 非异常场景的统计上报
					monitoring.ReportSuccess(ctx, monitoring.VolumeManagementMonitor, monitoring.ZoneLimitNotSet, "")
					passLimit = true
					continue
				}
				//step3,
				currentZoneCounter, _ := p.getCurrentVolumeZoneDimension(ctx, zoneLimit.ProductIdList, lineInfo.ResourceId, groupInfo.GroupId, zoneName, date, parcelType)
				recordVolumeRecord(volumeRecordMap, currentZoneCounter, productId, lineInfo.ResourceId, groupId, zoneName)
				recordZoneMaxOrMinVolume(volumeRecordMap, productId, lineInfo.LineId, zoneLimit, factorDetailName, zoneName)
				limit := GetParcelCapacity(factorDetailName, *zoneLimit)
				logger.CtxLogInfof(ctx, "zone mode use %v, product:%d, line:%s, group:%s, zonename:%s, date:%s, todayvolume:%d, limit:%d",
					factorDetailName, productId, lineInfo.ResourceId, groupInfo.GroupId, zoneName, date, currentZoneCounter, limit)
				if currentZoneCounter < limit {
					passLimit = true
				} else { // 只要有zone一个不满足就break
					passLimit = false
					break
				}
			}
			// 前面已经做过line运力维度的校验，这里如果zone维度校验全部跳过则认为通过校验
			if passLimit {
				availableLineList = append(availableLineList, lineInfo)
			}
		}
	}
	return availableLineList, volumeRecordMap, nil
}

func (p *ZoneRuleMgrImpl) GetGroupInfoAndZoneNameList(ctx context.Context, lineInfo *rule.LineInfo, volumeRule *vrentity.VolumeRule,
	routingType uint8, locInfo *pb.LocationInfo) (string, *vrentity.ZoneGroupInfo, []string, bool) {
	useForecastGroup := volumeRule.IsForecastType
	if forecast.IsLocalForecast(ctx, routingType) { // 如果是local Forecast预测用的是线上的group映射，其他的使用的和volume rule一样的类型，local Forecast只有 group是用的线上的
		useForecastGroup = false
	}
	//step1 ,
	groupId, _ := p.zoneGroupRepo.GetGroupIdByLineWithCache(ctx, lineInfo.ResourceId, volumeRule.RoutingType, useForecastGroup)
	if groupId == "" {
		logger.CtxLogErrorf(ctx, "group id not found | groupId=%s", groupId)
		return "", nil, nil, false
	}
	groupInfo, _ := p.zoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, groupId, int(routingType), useForecastGroup)
	if groupInfo == nil {
		//可能存在，当前 delete zone-group 做了 联动删除？ todo ,
		logger.CtxLogErrorf(ctx, "group info not found | groupId=%s", groupId)
		return "", nil, nil, false
	}
	//step2 ,
	zoneNameList := p.zoneRepo.GetZoneNameWithDeliveryAddress(ctx, groupInfo, locInfo)
	if len(zoneNameList) == 0 {
		logger.CtxLogErrorf(ctx, "zone not found | line=%s", lineInfo.ResourceId)
		return "", nil, nil, false
	}
	reportFmUse(ctx, lineInfo, zoneNameList)
	return groupId, groupInfo, zoneNameList, true
}

func recordLineMaxOrMinVolume(volumeRecordMap map[string]string, productId int64, lineLimits []vrentity.VolumeRuleDetail, factorDetailName string) {
	for _, limit := range lineLimits {
		//记录每个line的最小运力
		var key string
		if factorDetailName == MinVolumeV2Name {
			key = fmt.Sprintf("%v:%v:%v", productId, limit.LineId, "min")
		} else if factorDetailName == MaxCapacityV2Name || factorDetailName == MaxCodCapacityV2 || factorDetailName == MaxBulkyCapacityV2 ||
			factorDetailName == MaxHighValueCapacityV2 || factorDetailName == MaxDgCapacityV2 {
			key = fmt.Sprintf("%v:%v:%v", productId, limit.LineId, "max")
		}
		limitCapacity := GetParcelCapacity(factorDetailName, limit)
		volumeRecordMap[key] = strconv.FormatInt(limitCapacity, 10)
	}
}

func recordZoneMaxOrMinVolume(volumeRecordMap map[string]string, productId int64, lineId string, zoneLimit *vrentity.VolumeRuleDetail, factorDetailName, zoneCode string) {
	//记录每个line的最小运力
	var key string
	if factorDetailName == MinVolumeV2Name {
		key = fmt.Sprintf("%v:%v:%v", productId, lineId, "zoneVolume:min")
	} else if factorDetailName == MaxCapacityV2Name || factorDetailName == MaxCodCapacityV2 || factorDetailName == MaxBulkyCapacityV2 ||
		factorDetailName == MaxHighValueCapacityV2 || factorDetailName == MaxDgCapacityV2 {
		key = fmt.Sprintf("%v:%v:%v", productId, lineId, "zoneVolume:max")
	}
	limitCapacity := GetParcelCapacity(factorDetailName, *zoneLimit)
	volumeRecordMap[key] = volumeRecordMap[key] + zoneCode + ":" + strconv.FormatInt(limitCapacity, 10) + ";"
}

func recordVolumeRecord(volumeRecordMap map[string]string, current int64, productId int64, lineId string, groupId string, zoneCode string) {
	recordKey := fmt.Sprintf("%v:%v", productId, lineId)
	if zoneCode != "" {
		recordKey = fmt.Sprintf("%v:%v", lineId, "zoneVolume")
		volumeRecordMap[recordKey] = volumeRecordMap[recordKey] + fmt.Sprintf("%v:%v", zoneCode, current) + ";"
		return
	}
	volumeRecordMap[recordKey+":current"] = strconv.FormatInt(current, 10)
}

func reportFmUse(ctx context.Context, lineInfo *rule.LineInfo, zoneList []string) {
	if len(zoneList) == 0 {
		return
	}
	if lineInfo != nil {
		if lineInfo.ResourceSubType == lfslib.C_FM || lineInfo.ResourceSubType == lfslib.L_FM || lineInfo.ResourceSubType == lfslib.C_DFM {
			msg := fmt.Sprintf(" lineId=%s,lineType=%s,", lineInfo.LineId, lfslib.LineSubTypeMap[lineInfo.ResourceSubType])
			monitoring.ReportError(ctx, monitoring.VolumeManagementMonitor, monitoring.FmUseZoneLimitErr, msg)
			logger.CtxLogErrorf(ctx, msg)
		}
	}
}

func (p *ZoneRuleMgrImpl) getCurrentVolumeLineDimension(ctx context.Context, productIdList []int64, lineId, date, parcelType string) (int64, *srerr.Error) {
	var total int64
	for _, productId := range productIdList {
		current, err := p.volumeRoutingSrv.CurrentVolumeLineDimension(ctx, productId, lineId, date, parcelType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get current line volume failed | product=%d,line=%s,err=%v", productId, lineId, err)
			continue
		}
		total += current
	}

	if total == 0 {
		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.RoutingLineVolumeZeroNum, "")
	}

	return total, nil
}

func (p *ZoneRuleMgrImpl) getCurrentVolumeZoneDimension(ctx context.Context, productIdList []int64, lineId, groupId, zoneName, date, parcelType string) (int64, *srerr.Error) {
	var total int64
	for _, productId := range productIdList {
		current, err := p.volumeRoutingSrv.CurrentVolumeZoneDimension(ctx, productId, lineId, groupId, zoneName, date, parcelType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get current line zone volume failed | product=%d,line=%s,zone=%s,err=%v", productId, lineId, zoneName, err)
			continue
		}
		total += current
	}

	// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
	if total == 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.RoutingLineZoneRouteVolumeZeroNum, "")
	}

	return total, nil
}
