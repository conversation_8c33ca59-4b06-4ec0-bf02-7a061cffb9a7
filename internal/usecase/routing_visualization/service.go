package routing_visualization

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strconv"
	"strings"
)

type RoutingVisualService interface {
	GetList(ctx context.Context, requestId, fOrderId, slsTn string, pageNo, pageSize int) (*routing.RoutingVisualListResp, *srerr.Error)
	GetDetail(ctx context.Context, requestId string) (*routing.RoutingVisualDeatilResp, *srerr.Error)
	LogToResponse(ctx context.Context, log *routing_log.RoutingLog, slsId string) *routing.RoutingVisualDeatilResp
}

type RoutingVisualServiceImpl struct {
	dataApi       *dataclient.DataApi
	wbcApi        wbcclient.WbcApi
	routingConfig routing_config.RoutingConfigService
}

func NewRoutingVisualServiceImpl(
	dataApi *dataclient.DataApi,
	wbcApi wbcclient.WbcApi,
	routingConfig routing_config.RoutingConfigService,
) *RoutingVisualServiceImpl {
	return &RoutingVisualServiceImpl{
		dataApi:       dataApi,
		wbcApi:        wbcApi,
		routingConfig: routingConfig,
	}
}

func (r *RoutingVisualServiceImpl) GetList(ctx context.Context, requestId, fOrderId, slsTn string, pageNo, pageSize int) (*routing.RoutingVisualListResp, *srerr.Error) {

	//查询
	if slsTn != "" && fOrderId == "" {
		waybill, _, err := r.wbcApi.GetForderIdBySlsTn(ctx, slsTn)
		if err != nil {
			logger.CtxLogInfof(ctx, "GetList get forderId error %+v", err)
			return nil, srerr.With(srerr.DataApiErr, slsTn, err)
		}
		fOrderId = waybill
	}
	//todo请求data
	resp, err := r.dataApi.GetRoutingLogList(ctx, requestId, fOrderId, envvar.GetCID(), pageNo, pageSize)
	if err != nil {
		logger.CtxLogInfof(ctx, "GetList data error %+v", err)
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "data list response %v", resp)
	//查询对应的sls_tn和sls_id
	r.BatchGetSlsTnAndSlsId(ctx, resp)

	return resp, nil
}

func (r *RoutingVisualServiceImpl) GetDetail(ctx context.Context, rowKey string) (*routing.RoutingVisualDeatilResp, *srerr.Error) {
	resp, dataErr := r.dataApi.GetRoutingLogDetail(ctx, rowKey)
	if dataErr != nil || resp == nil {
		logger.CtxLogInfof(ctx, "GetDetail data error %+v,%+v", dataErr)
		return nil, srerr.With(srerr.DataApiErr, rowKey, dataErr)
	}
	if resp != nil && resp.Retcode != 0 {
		logger.CtxLogInfof(ctx, "GetDetail data response error %s", resp.Message)
		return nil, srerr.With(srerr.DataApiErr, rowKey, errors.New(resp.Message))
	}

	logEntry := &resp.Data.LogEntry
	slsTn, slsId, err := r.wbcApi.GetSlsTnByForderId(ctx, logEntry.FOrderId)
	if err != nil {
		logger.CtxLogInfof(ctx, "GetDetail get slstn error %+v", err)
	}
	logger.CtxLogInfof(ctx, "data detail response %v", logEntry)
	logEntry.SlsTn = slsTn
	return r.LogToResponse(ctx, logEntry, slsId), nil
}

func (r *RoutingVisualServiceImpl) BatchGetSlsTnAndSlsId(ctx context.Context, resp *routing.RoutingVisualListResp) {
	if resp == nil || len(resp.List) == 0 {
		return
	}
	for i := 0; i < len(resp.List); i++ {
		tn, id, err := r.wbcApi.GetSlsTnByForderId(ctx, resp.List[i].ForderId)
		if err != nil {
			logger.CtxLogErrorf(ctx, "BatchGetSlsTnAndSlsId error %+v", err)
			continue
		}
		resp.List[i].SlsTn = tn
		resp.List[i].SlsId = id
	}
}

var laneToLine = make(map[string][]string)
var lineToLaneMap = make(map[string][]string)
var lineInfos = make(map[string]*smart_routing_protobuf.ResourceServiceable)

func (r *RoutingVisualServiceImpl) LogToResponse(ctx context.Context, log *routing_log.RoutingLog, slsId string) *routing.RoutingVisualDeatilResp {
	if log == nil {
		return nil
	}

	resp := &routing.RoutingVisualDeatilResp{}
	for _, lane := range log.HardCriteriaResult {
		for _, line := range lane.ResourceServiceableCollection {
			laneToLine[*lane.LaneCode] = append(laneToLine[*lane.LaneCode], line.GetResourceId())
			lineInfos[line.GetResourceId()] = line
		}
	}

	for _, lane := range log.HardCriteriaResult {
		for _, line := range lane.ResourceServiceableCollection {
			lineToLaneMap[line.GetResourceId()] = append(lineToLaneMap[line.GetResourceId()], lane.GetLaneCode())
		}
	}

	productIdV1, productIdV2 := log.ProductId, log.ProductId
	conf, err := r.routingConfig.GetRoutingConfigCacheByProductID(ctx, log.ProductId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "search routing config err=%v", err)
	}
	// 如果是spx SmartRouting,运力记录的key中ProductId=0
	if conf.IsSpxSmartRouting() {
		productIdV1 = 0
	}

	resp.BaseInfo.ForderId = log.FOrderId
	resp.BaseInfo.SlsTn = log.SlsTn
	resp.BaseInfo.RoutingStatus = log.RoutingStatus
	resp.BaseInfo.RoutingResult = log.FinalResult.Result
	resp.BaseInfo.RequestTime = log.RequestTime
	resp.BaseInfo.VolumeRuleId = log.VolumeRuleID
	resp.BaseInfo.RequestId = log.RequestId
	resp.BaseInfo.ProductId = log.ProductId
	resp.BaseInfo.SlsId = slsId
	resp.BaseInfo.RuleId = log.RuleId
	resp.BaseInfo.RoutingToggle = getRoutingToggle(log)

	resp.RoutingDetail.HardCriteria.BeforeHardCriteriaLanes = log.BeforeHardCriteriaLaneList
	resp.RoutingDetail.HardCriteria.AfterHardCriteriaLanes = getHardCriteriaResult(log)
	resp.RoutingDetail.HardCriteria.LineToggle = getLineToggle(log)
	resp.RoutingDetail.HardCriteria.InputLaneCode = getLane(log.RoutingResult.AvailableFilterProcess.Before)
	resp.RoutingDetail.HardCriteria.OutputLaneCode = getLane(log.RoutingResult.AvailableFilterProcess.After)

	resp.RoutingDetail.SoftCriteria.RoutingSeq = log.RoutingSeq
	resp.RoutingDetail.SoftCriteria.InputLaneCode = log.RoutingResult.SoftCriteriaFilterProcess.Before
	resp.RoutingDetail.SoftCriteria.OutputLaneCode = []string{log.FinalResult.LaneCode}
	factoryNameMap, zoneCodeList, productId := getFactoryNameMap(ctx, productIdV1, productIdV2, log.RoutingResult.SoftCriteriaFilterProcess.StageList)
	resp.RoutingDetail.SoftCriteria.TableTitleList = getTableTitles(factoryNameMap, zoneCodeList)
	resp.RoutingDetail.SoftCriteria.Detail = r.getSoftCriteriaDetail(ctx, productId, log, factoryNameMap, zoneCodeList)

	return resp
}

func getFactoryNameMap(ctx context.Context, productIdV1, productIdV2 int, StageList []routing_log.SoftCriteriaRuleStage) (map[string]bool, []string, int) {
	factoryNameMap := make(map[string]bool)
	for _, stage := range StageList {
		for _, factory := range stage.FactorCombination {
			factoryNameMap[factory.FactorName] = true
		}
	}

	for _, stage := range StageList {
		for _, factory := range stage.FactorCombination {
			if factory.FactorName == schedule_factor.MinVolume || factory.FactorName == schedule_factor.MaxCapacity ||
				factory.FactorName == schedule_factor.MaxCodCapacity || factory.FactorName == schedule_factor.MaxBulkyCapacity ||
				factory.FactorName == schedule_factor.MaxHighValueCapacity || factory.FactorName == schedule_factor.MaxDgCapacity {
				m := factory.ProcessData.(map[string]interface{})
				if len(factory.Before) <= 0 {
					logger.CtxLogErrorf(ctx, "before is empty")
					continue
				}
				lineId := factory.Before[0]
				lineKey := fmt.Sprintf("%v:%v", productIdV1, lineId)
				if interfaceToStringSlice(m[lineKey+":zoneVolume:min"]) != nil {
					return factoryNameMap, interfaceToStringSlice(m[lineKey+":zoneVolume:min"]), productIdV1
				} else if interfaceToStringSlice(m[lineKey+":zoneVolume:max"]) != nil {
					return factoryNameMap, interfaceToStringSlice(m[lineKey+":zoneVolume:max"]), productIdV1
				}
				return factoryNameMap, nil, productIdV1
			} else if factory.FactorName == schedule_factor.MinVolumeV2Name || factory.FactorName == schedule_factor.MaxCapacityV2Name ||
				factory.FactorName == schedule_factor.MaxCodCapacityV2Name || factory.FactorName == schedule_factor.MaxBulkyCapacityV2Name ||
				factory.FactorName == schedule_factor.MaxHighValueCapacityV2Name || factory.FactorName == schedule_factor.MaxDgCapacityV2Name {
				m := factory.ProcessData.(map[string]interface{})
				var zoneCodeList []string
				for _, lineId := range factory.Before {
					lineKey := fmt.Sprintf("%v:%v", productIdV2, lineId)
					if interfaceToStringSlice(m[lineKey+":zoneVolume:min"]) != nil {
						zoneCodeList = append(zoneCodeList, interfaceToStringSlice(m[lineKey+":zoneVolume:min"])...)
					} else if interfaceToStringSlice(m[lineKey+":zoneVolume:max"]) != nil {
						zoneCodeList = append(zoneCodeList, interfaceToStringSlice(m[lineKey+":zoneVolume:max"])...)
					}
				}
				return factoryNameMap, objutil.RemoveDuplicatedStrings(zoneCodeList), productIdV2
			}
		}
	}
	return factoryNameMap, nil, productIdV2
}

func getTableTitles(factoryNameMap map[string]bool, zoneCodeList []string) []string {
	tableTitles := []string{"Input Lane Code", "Output Lane Code", "Line ID"}
	isMin, isMax := IsMin(factoryNameMap), IsMax(factoryNameMap)
	if isMin {
		tableTitles = append(tableTitles, "Line Min Limit")
	}
	if isMax {
		tableTitles = append(tableTitles, "Line Max Limit")
	}
	if isMin || isMax {
		tableTitles = append(tableTitles, "Current Line Volume")
		for _, zoneCode := range zoneCodeList {
			if factoryNameMap[schedule_factor.MinVolumeV2Name] {
				tableTitles = append(tableTitles, "Zone Min Limit of "+zoneCode)
			}
			if factoryNameMap[schedule_factor.MaxCapacityV2Name] {
				tableTitles = append(tableTitles, "Zone Max Limit of "+zoneCode)
			}
			tableTitles = append(tableTitles, "Current Zone Volume of "+zoneCode)
		}
	}

	if IsCod(factoryNameMap) {
		tableTitles = append(tableTitles, "Line Max Cod Limit")
		tableTitles = append(tableTitles, "Current Line Cod Volume")
		for _, zoneCode := range zoneCodeList {
			if factoryNameMap[schedule_factor.MaxCodCapacityV2Name] {
				tableTitles = append(tableTitles, "Zone Max Cod Limit of "+zoneCode)
			}
			tableTitles = append(tableTitles, "Current Zone Cod Volume of "+zoneCode)
		}
	}
	if IsBulky(factoryNameMap) {
		tableTitles = append(tableTitles, "Line Max Bulky Limit")
		tableTitles = append(tableTitles, "Current Line Bulky Volume")
		for _, zoneCode := range zoneCodeList {
			if factoryNameMap[schedule_factor.MaxBulkyCapacityV2Name] {
				tableTitles = append(tableTitles, "Zone Max Bulky Limit of "+zoneCode)
			}
			tableTitles = append(tableTitles, "Current Zone Bulky Volume of "+zoneCode)
		}
	}
	if IsHighValue(factoryNameMap) {
		tableTitles = append(tableTitles, "Line Max HighValue Limit")
		tableTitles = append(tableTitles, "Current Line HighValue Volume")
		for _, zoneCode := range zoneCodeList {
			if factoryNameMap[schedule_factor.MaxHighValueCapacityV2Name] {
				tableTitles = append(tableTitles, "Zone Max HighValue Limit of "+zoneCode)
			}
			tableTitles = append(tableTitles, "Current Zone HighValue Volume of "+zoneCode)
		}
	}
	if IsDg(factoryNameMap) {
		tableTitles = append(tableTitles, "Line Max DG Limit")
		tableTitles = append(tableTitles, "Current Line DG Volume")
		for _, zoneCode := range zoneCodeList {
			if factoryNameMap[schedule_factor.MaxDgCapacityV2Name] {
				tableTitles = append(tableTitles, "Zone Max DG Limit of "+zoneCode)
			}
			tableTitles = append(tableTitles, "Current Zone DG Volume of "+zoneCode)
		}
	}
	if factoryNameMap[schedule_factor.LineCheapestShippingFee] {
		tableTitles = append(tableTitles, "Shipping Fee")
	}
	tableTitles = append(tableTitles, "Weightage/Priority")
	return tableTitles
}

func IsMin(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MinVolume] || factoryNameMap[schedule_factor.MinVolumeV2Name]
}

func IsMax(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MaxCapacity] || factoryNameMap[schedule_factor.MaxCapacityV2Name]
}

func IsCod(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MaxCodCapacity] || factoryNameMap[schedule_factor.MaxCodCapacityV2Name]
}

func IsBulky(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MaxBulkyCapacity] || factoryNameMap[schedule_factor.MaxBulkyCapacityV2Name]
}

func IsHighValue(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MaxHighValueCapacity] || factoryNameMap[schedule_factor.MaxHighValueCapacityV2Name]
}

func IsDg(factoryNameMap map[string]bool) bool {
	return factoryNameMap[schedule_factor.MaxDgCapacity] || factoryNameMap[schedule_factor.MaxDgCapacityV2Name]
}

func ExistInFactoryNameMap(factoryNameMap map[string]bool, v1, v2 string) bool {
	return factoryNameMap[v1] || factoryNameMap[v2]
}

func IsV2(f routing_log.SchedulingFactorCombination) bool {
	return f.FactorName == schedule_factor.MinVolumeV2Name || f.FactorName == schedule_factor.MaxCapacityV2Name ||
		f.FactorName == schedule_factor.MaxCodCapacityV2Name || f.FactorName == schedule_factor.MaxBulkyCapacityV2Name ||
		f.FactorName == schedule_factor.MaxHighValueCapacityV2Name || f.FactorName == schedule_factor.MaxDgCapacityV2Name
}

func getRoutingToggle(log *routing_log.RoutingLog) routing.RoutingToggle {
	if log == nil {
		return routing.RoutingToggle{}
	}
	res := routing.RoutingToggle{}
	res.Local = log.RoutingToggle&1 > 0
	res.CB = log.RoutingToggle&(1<<1) > 0
	res.Spx = log.RoutingToggle&(1<<2) > 0
	res.ILH = log.RoutingToggle&(1<<3) > 0

	return res
}

func getHardCriteriaResult(log *routing_log.RoutingLog) []string {
	res := []string{}
	if log == nil {
		return res
	}
	for _, lane := range log.HardCriteriaResult {
		res = append(res, *lane.LaneCode)
	}

	return res
}

func getLineToggle(log *routing_log.RoutingLog) map[string]map[string]string {
	res := map[string]map[string]string{}
	if log == nil {
		return res
	}
	for k, v := range log.LineToggle {
		if res[k] == nil {
			res[k] = map[string]string{}
		}
		res[k]["on"] = strings.Join(v.On, "_")
		res[k]["off"] = strings.Join(v.Off, "_")
	}

	return res
}

func getLane(data map[string]map[string][]string) []string {
	res := []string{}
	if data == nil {
		return res
	}
	for laneCode, lineMap := range data {
		temp := laneCode + ":"
		for lineType, lins := range lineMap {
			temp += lineType + "-" + strings.Join(lins, "_") + ","
		}
		res = append(res, temp)
	}
	return res
}

func (r *RoutingVisualServiceImpl) getSoftCriteriaDetail(ctx context.Context, product int, log *routing_log.RoutingLog, factoryNameMap map[string]bool, zoneCodeList []string) []routing.Detail {
	details := make([]routing.Detail, 0)
	if log == nil {
		return details
	}

	for _, stage := range log.RoutingResult.SoftCriteriaFilterProcess.StageList {
		for _, factory := range stage.FactorCombination {
			detail := routing.Detail{}
			detail.SoftCriteria = stage.DisplayResourceSubType + ":" + factory.FactorName
			detail.Lanes = getFactoryLanes(factory, product, stage.Before, stage.After)
			detail.TableDataList = getTableData(factory, product, stage.Before, factoryNameMap, zoneCodeList)
			detail.Priority = factory.Priority
			details = append(details, detail)
		}
	}

	defaultCriteria := routing.Detail{}
	defaultCriteria.SoftCriteria = log.RoutingResult.DefaultCriteriaFilterProcess.FactorName
	defaultStage := getDefaultCriteriaFactoryLanes(log.RoutingResult.DefaultCriteriaFilterProcess, factoryNameMap, len(zoneCodeList))
	details = append(details, defaultStage...)

	return details
}

func getDefaultCriteriaFactoryLanes(defaultProcess routing_log.DefaultCriteriaFilterProcess, factoryNameMap map[string]bool, zoneCodeListLen int) []routing.Detail {
	details := []routing.Detail{}

	for _, stage := range defaultProcess.StageList {
		detail := routing.Detail{}
		detail.SoftCriteria = stage.FactorName
		detail.Lanes, detail.TableDataList = getDefaultLanes(stage.Before, stage.After, stage.ProcessData, stage.BeforeLanes, stage.AfterLanes,
			stage.ResourceSubType, factoryNameMap, zoneCodeListLen)
		details = append(details, detail)
	}

	return details
}

func getDefaultLanes(before, after []string, processData interface{}, beforeLanes, afterLanes []string, resourceSubType int32,
	factoryNameMap map[string]bool, zoneCodeListLen int) ([]routing.LaneDetail, [][]string) {
	lanes := []routing.LaneDetail{}
	tableDataList := make([][]string, 0)
	tempMap := map[string]string{}
	data, ok := processData.(map[string]interface{})

	for _, lineId := range before {
		laneCodes := lineToLaneMap[lineId]
		for _, laneCode := range laneCodes {
			if !objutil.ContainStr(beforeLanes, laneCode) {
				continue
			}
			lane := routing.LaneDetail{}
			if tempMap[laneCode] != "" {
				continue
			}
			blankNum := GetSoftCriteriaTableDataNum(factoryNameMap, zoneCodeListLen)
			tableData := make([]string, 0, 3+blankNum+1)
			tableData = append(tableData, laneCode)

			lane.InputLaneCode = laneCode
			if objutil.ContainStr(after, lineId) {
				lane.OutputLaneCode = laneCode
				tableData = append(tableData, laneCode)
			} else {
				tableData = append(tableData, "")
			}
			tableData = append(tableData, lineId)
			tableData = append(tableData, make([]string, blankNum)...)
			var defaultCriteriaParamStr string
			lane.LineId = lineId
			if ok {
				lineInfo := lineInfos[lineId]
				if lineInfo.GetDgServiceable() != nil && lineInfo.GetDgServiceable().GetAbility() != nil {
					var key string
					dgFlag := lineInfo.GetDgServiceable().GetAbility().GetDgType().String()
					if resourceSubType == lfslib.C_FL || objutil.ContainsInt(lfslib.NeedRoutingILH, int(resourceSubType)) {
						key = helper.FormatLineAndDgKey(lineInfo.GetResourceId(), int(smart_routing_protobuf.DgType_value[dgFlag]), rule.DgRelated)
					} else {
						key = helper.FormatLineAndDgKey(lineInfo.GetResourceId(), int(smart_routing_protobuf.DgType_value[dgFlag]), 0)
					}
					value, isInt := data[key].(float64)
					if isInt {
						lane.DefaultCriteriaParam = int(value)
						defaultCriteriaParamStr = strconv.Itoa(int(value))
					}
				}
			}
			tableData = append(tableData, defaultCriteriaParamStr)
			tableDataList = append(tableDataList, tableData)
			tempMap[laneCode] = laneCode
			lanes = append(lanes, lane)
		}
	}

	return lanes, tableDataList
}

func GetSoftCriteriaTableDataNum(factoryNameMap map[string]bool, len int) int {
	var blankNum int
	isMin, isMax := IsMin(factoryNameMap), IsMax(factoryNameMap)
	if isMin {
		blankNum++
	}
	if isMax {
		blankNum++
	}
	if isMin || isMax {
		blankNum++
		if factoryNameMap[schedule_factor.MinVolumeV2Name] {
			blankNum += len
		}
		if factoryNameMap[schedule_factor.MaxCapacityV2Name] {
			blankNum += len
		}
		blankNum += len
	}
	if IsCod(factoryNameMap) {
		if factoryNameMap[schedule_factor.MaxCodCapacityV2Name] {
			blankNum += 2 + len*2
		} else {
			blankNum += 2 + len
		}
	}
	if IsBulky(factoryNameMap) {
		if factoryNameMap[schedule_factor.MaxBulkyCapacityV2Name] {
			blankNum += 2 + len*2
		} else {
			blankNum += 2 + len
		}
	}
	if IsHighValue(factoryNameMap) {
		if factoryNameMap[schedule_factor.MaxHighValueCapacityV2Name] {
			blankNum += 2 + len*2
		} else {
			blankNum += 2 + len
		}
	}
	if IsDg(factoryNameMap) {
		if factoryNameMap[schedule_factor.MaxDgCapacityV2Name] {
			blankNum += 2 + len*2
		} else {
			blankNum += 2 + len
		}
	}
	if factoryNameMap[schedule_factor.LineCheapestShippingFee] {
		blankNum++
	}
	return blankNum
}

func getTableData(f routing_log.SchedulingFactorCombination, productId int, beforeLane []string,
	factoryNameMap map[string]bool, zoneCodeList []string) [][]string {
	tableDataList := make([][]string, 0)
	tempLineMap := map[string]string{}
	length := len(zoneCodeList)
	blankNum := GetSoftCriteriaTableDataNum(factoryNameMap, length)
	for _, lineId := range f.Before {
		laneCodes := lineToLaneMap[lineId]
		for _, laneCode := range laneCodes {
			tableData := make([]string, 0, 3+blankNum+1)
			if !objutil.ContainStr(beforeLane, laneCode) {
				continue
			}
			if tempLineMap[laneCode] != "" {
				continue
			}
			tableData = append(tableData, laneCode)

			if len(f.After) == 0 || objutil.ContainStr(f.After, lineId) {
				tableData = append(tableData, laneCode)
			} else {
				tableData = append(tableData, "")
			}
			tableData = append(tableData, lineId)

			var m map[string]interface{}
			if f.ProcessData != nil {
				m = f.ProcessData.(map[string]interface{})
			}
			// productId在之前已经判断过了(使用V1还是V2)，V1和V2使用的productId可能不一样(SPX)
			lineKey := fmt.Sprintf("%v:%v", productId, lineId)

			isV2, isMin, isMax := IsV2(f), IsMin(factoryNameMap), IsMax(factoryNameMap)
			tableData = append(tableData, getMinMaxTableData(f, isMin, m, lineKey, isMax, zoneCodeList, lineId, isV2, factoryNameMap)...)

			zoneCodeLimitList, currentZoneVolumeList := GetZoneDisplayData(zoneCodeList, interfaceToStringMap(m[lineKey+":zoneVolume:max"]), interfaceToStringMap(m[lineId+":zoneVolume"]), isV2)
			tableData = append(tableData, getParcelTypeTableData(f, factoryNameMap, m, lineKey, zoneCodeList, zoneCodeLimitList, currentZoneVolumeList, schedule_factor.MaxCodCapacity, schedule_factor.MaxCodCapacityV2Name)...)
			tableData = append(tableData, getParcelTypeTableData(f, factoryNameMap, m, lineKey, zoneCodeList, zoneCodeLimitList, currentZoneVolumeList, schedule_factor.MaxBulkyCapacity, schedule_factor.MaxBulkyCapacityV2Name)...)
			tableData = append(tableData, getParcelTypeTableData(f, factoryNameMap, m, lineKey, zoneCodeList, zoneCodeLimitList, currentZoneVolumeList, schedule_factor.MaxHighValueCapacity, schedule_factor.MaxHighValueCapacityV2Name)...)
			tableData = append(tableData, getParcelTypeTableData(f, factoryNameMap, m, lineKey, zoneCodeList, zoneCodeLimitList, currentZoneVolumeList, schedule_factor.MaxDgCapacity, schedule_factor.MaxDgCapacityV2Name)...)

			tableData = append(tableData, getLineCheapestShippingFeeTableData(f.FactorName == schedule_factor.LineCheapestShippingFee, factoryNameMap[schedule_factor.LineCheapestShippingFee], m[lineId])...)

			// default criteria (Weightage/Priority)
			tableData = append(tableData, "")
			tableDataList = append(tableDataList, tableData)
			tempLineMap[laneCode] = laneCode
		}
	}
	return tableDataList
}

func getLineCheapestShippingFeeTableData(isLineCheapestShippingFee bool, existLineCheapestShippingFee bool, value interface{}) []string {
	if !existLineCheapestShippingFee {
		return nil
	}
	if !isLineCheapestShippingFee {
		return []string{""}
	}
	if fee, ok := value.(float64); ok {
		return []string{fmt.Sprintf("%f", fee)}
	}
	logger.CtxLogErrorf(context.TODO(), "LineCheapestShippingFee data is not float64: %v", value)
	return []string{""}
}

func getParcelTypeTableData(f routing_log.SchedulingFactorCombination, factoryNameMap map[string]bool, m map[string]interface{},
	lineKey string, zoneCodeList []string, zoneCodeLimitList []string, currentZoneVolumeList []string, v1, v2 string) []string {
	var tableData []string
	if !ExistInFactoryNameMap(factoryNameMap, v1, v2) {
		return tableData
	}
	if f.FactorName == v1 || f.FactorName == v2 {
		tableData = append(tableData, interfaceToString(m[lineKey+":max"]), interfaceToString(m[lineKey+":current"]))
		for i := range zoneCodeList {
			if factoryNameMap[v2] {
				tableData = append(tableData, zoneCodeLimitList[i])
			}
			tableData = append(tableData, currentZoneVolumeList[i])
		}
	} else {
		if factoryNameMap[v2] {
			tableData = append(tableData, make([]string, 2+len(zoneCodeList)*2)...)
		} else {
			tableData = append(tableData, make([]string, 2+len(zoneCodeList))...)
		}
	}
	return tableData
}

func getMinMaxTableData(f routing_log.SchedulingFactorCombination, isMin bool, m map[string]interface{}, lineKey string, isMax bool, zoneCodeList []string, lineId string, isV2 bool, factoryNameMap map[string]bool) []string {
	var tableData []string
	length := len(zoneCodeList)
	if isMin {
		if f.FactorName == schedule_factor.MinVolume || f.FactorName == schedule_factor.MinVolumeV2Name {
			tableData = append(tableData, interfaceToString(m[lineKey+":min"]))
		} else {
			tableData = append(tableData, "")
		}
	}
	if isMax {
		if f.FactorName == schedule_factor.MaxCapacity || f.FactorName == schedule_factor.MaxCapacityV2Name {
			tableData = append(tableData, interfaceToString(m[lineKey+":max"]))
		} else {
			tableData = append(tableData, "")
		}
	}
	if !isMin && !isMax {
		return tableData
	}
	if f.FactorName == schedule_factor.MinVolume || f.FactorName == schedule_factor.MinVolumeV2Name {
		tableData = append(tableData, interfaceToString(m[lineKey+":current"]))
		zoneCodeLimitList, currentZoneVolumeList := GetZoneDisplayData(zoneCodeList, interfaceToStringMap(m[lineKey+":zoneVolume:min"]), interfaceToStringMap(m[lineId+":zoneVolume"]), isV2)
		for i := range zoneCodeList {
			if factoryNameMap[schedule_factor.MinVolumeV2Name] {
				tableData = append(tableData, zoneCodeLimitList[i])
			}
			if factoryNameMap[schedule_factor.MaxCapacityV2Name] {
				tableData = append(tableData, "")
			}
			tableData = append(tableData, currentZoneVolumeList[i])
		}
	} else if f.FactorName == schedule_factor.MaxCapacity || f.FactorName == schedule_factor.MaxCapacityV2Name {
		tableData = append(tableData, interfaceToString(m[lineKey+":current"]))
		zoneCodeLimitList, currentZoneVolumeList := GetZoneDisplayData(zoneCodeList, interfaceToStringMap(m[lineKey+":zoneVolume:max"]), interfaceToStringMap(m[lineId+":zoneVolume"]), isV2)
		for i := range zoneCodeList {
			if factoryNameMap[schedule_factor.MinVolumeV2Name] {
				tableData = append(tableData, "")
			}
			if factoryNameMap[schedule_factor.MaxCapacityV2Name] {
				tableData = append(tableData, zoneCodeLimitList[i])
			}
			tableData = append(tableData, currentZoneVolumeList[i])
		}
	} else {
		blankNumber := 1 + length
		if factoryNameMap[schedule_factor.MinVolumeV2Name] {
			blankNumber += length
		}
		if factoryNameMap[schedule_factor.MaxCapacityV2Name] {
			blankNumber += length
		}
		tableData = append(tableData, make([]string, blankNumber)...)
	}
	return tableData
}

func getFactoryLanes(f routing_log.SchedulingFactorCombination, productId int, beforeLane, afterLane []string) []routing.LaneDetail {
	lanes := make([]routing.LaneDetail, 0)
	tempLineMap := map[string]string{}
	for _, lineId := range f.Before {
		laneCodes := lineToLaneMap[lineId]
		for _, laneCode := range laneCodes {
			if !objutil.ContainStr(beforeLane, laneCode) {
				continue
			}
			lane := routing.LaneDetail{}
			lane.InputLaneCode = laneCode

			if len(f.After) == 0 || objutil.ContainStr(f.After, lineId) {
				lane.OutputLaneCode = laneCode
			}

			var m map[string]interface{}
			if f.ProcessData != nil {
				m = f.ProcessData.(map[string]interface{})
			}
			lineKey := fmt.Sprintf("%v:%v", productId, lineId)
			if f.FactorName == schedule_factor.MinVolume || f.FactorName == schedule_factor.MinVolumeV2Name {
				lane.LineLimit = interfaceToString(m[lineKey+":min"])
				lane.CurrentLineVolume = interfaceToString(m[lineKey+":current"])
				lane.CurrentZoneVolume = interfaceToString(m[lineId+":zoneVolume"])
				lane.ZoneCodeLimit = interfaceToString(m[lineKey+":zoneVolume:min"]) // V1存量没有zone的min、max数据可以保存
			} else if f.FactorName == schedule_factor.MaxCapacity || f.FactorName == schedule_factor.MaxCodCapacity ||
				f.FactorName == schedule_factor.MaxBulkyCapacity || f.FactorName == schedule_factor.MaxHighValueCapacity ||
				f.FactorName == schedule_factor.MaxDgCapacity {
				lane.LineLimit = interfaceToString(m[lineKey+":max"])
				lane.CurrentLineVolume = interfaceToString(m[lineKey+":current"])
				lane.CurrentZoneVolume = interfaceToString(m[lineId+":zoneVolume"])
				lane.ZoneCodeLimit = interfaceToString(m[lineKey+":zoneVolume:max"]) // V1存量没有zone的min、max数据可以保存
			} else if f.FactorName == schedule_factor.LineCheapestShippingFee {
				lane.ShippingFee = interfaceToFloat64(m[lineId])
			}

			lane.LineId = lineId
			if tempLineMap[laneCode] == "" {
				lanes = append(lanes, lane)
			}
			tempLineMap[laneCode] = laneCode
		}
	}
	return lanes
}

func GetZoneDisplayData(zoneCodeList []string, limitMap, volumeMap map[string]string, isV2 bool) ([]string, []string) {
	var zoneCodeLimitList []string
	var currentZoneVolumeList []string
	for _, zoneCode := range zoneCodeList {
		if isV2 {
			zoneCodeLimitList = append(zoneCodeLimitList, limitMap[zoneCode])
			currentZoneVolumeList = append(currentZoneVolumeList, volumeMap[zoneCode])
		} else {
			zoneCodeLimitList = append(zoneCodeLimitList, "")
			currentZoneVolumeList = append(currentZoneVolumeList, limitMap[zoneCode])
		}
	}
	return zoneCodeLimitList, currentZoneVolumeList
}

func interfaceToString(in interface{}) string {
	if in == nil {
		return ""
	}
	return in.(string)
}

func interfaceToStringSlice(in interface{}) []string {
	if in == nil {
		return nil
	}
	s := in.(string)
	slice := strings.Split(s, ";")
	if len(slice) <= 0 {
		return nil
	}
	// 特殊处理，"a:1;b:2;c:3;" 处理后最后一个元素为空字符串，需要去掉，返回[]string{"a", "b", "c"}
	// v2的zone存量逻辑只存最后一个zone，"a:1",直接返回slice
	if slice[len(slice)-1] == "" {
		slice = slice[:len(slice)-1]
	}
	result := make([]string, 0, len(slice))
	for _, str := range slice {
		volume := strings.Split(str, ":")
		if len(volume) <= 0 {
			logger.CtxLogErrorf(context.Background(), "len(volume) <= 0 error: %v", str)
			continue
		}
		result = append(result, volume[0])
	}
	return result
}

func interfaceToStringMap(in interface{}) map[string]string {
	if in == nil {
		return nil
	}
	s := in.(string)
	slice := strings.Split(s, ";")
	if len(slice) <= 0 {
		return nil
	}
	// 特殊处理，"a:1;b:2;c:3;" 处理后最后一个元素为空字符串，需要去掉，返回[]string{"a", "b", "c"}
	// v2的zone存量逻辑只存最后一个zone，"a:1",直接返回slice
	if slice[len(slice)-1] == "" {
		slice = slice[:len(slice)-1]
	}
	volumeMap := make(map[string]string)
	for _, str := range slice {
		volume := strings.Split(str, ":")
		if len(volume) <= 1 {
			logger.CtxLogErrorf(context.Background(), "len(volume) <= 1 error: %v", str)
			continue
		}
		volumeMap[volume[0]] = volume[1]
	}
	return volumeMap
}

func interfaceToFloat64(in interface{}) float64 {
	if in == nil {
		return 0.0
	}
	return in.(float64)
}
