package routing_visualization

type (
	WaybillReq struct {
		ForderId int64  `json:"parcel_id"`
		SlsTn    string `json:"slo_tn"`
		PageNo   int    `json:"pageno"`
		Count    int    `json:"count"`
	}

	OrderInfo struct {
		ForderId string `json:"parcel_id"`
		SlsTn    string `json:"slo_tn"`
	}

	WaybillResp struct {
		Retcode int    `json:"retcode"`
		Message string `json:"message"`
		Data    []OrderInfo
	}
)
