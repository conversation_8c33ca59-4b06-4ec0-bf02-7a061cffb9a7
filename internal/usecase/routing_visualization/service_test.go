package routing_visualization

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"reflect"
	"testing"
)

func Test_getFactoryNameMap(t *testing.T) {
	ctx := context.Background()
	type args struct {
		productIdV1 int
		productIdV2 int
		StageList   []routing_log.SoftCriteriaRuleStage
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]bool
		want1 []string
		want2 int
	}{
		// TODO: Add test cases.
		{
			name: "case 1: v1 and interfaceToStringSlice(m[lineKey+\":zoneVolume:min\"]) != nil",
			args: args{
				StageList: []routing_log.SoftCriteriaRuleStage{
					{
						FactorCombination: []routing_log.SchedulingFactorCombination{
							{
								FactorName:  schedule_factor.MinVolume,
								ProcessData: map[string]interface{}{},
							},
							{
								FactorName: schedule_factor.MinVolume,
								Before:     []string{"line1"},
								ProcessData: map[string]interface{}{
									fmt.Sprintf("%v:%v", 0, "line1") + ":zoneVolume:min": "z1:100;",
								},
							},
						},
					},
				},
			},
			want: map[string]bool{
				schedule_factor.MinVolume: true,
			},
			want1: []string{"z1"},
			want2: 0,
		},
		{
			name: "case 2: v1 and interfaceToStringSlice(m[lineKey+\":zoneVolume:max\"]) != nil",
			args: args{
				StageList: []routing_log.SoftCriteriaRuleStage{
					{
						FactorCombination: []routing_log.SchedulingFactorCombination{
							{
								FactorName:  schedule_factor.MinVolume,
								ProcessData: map[string]interface{}{},
							},
							{
								FactorName: schedule_factor.MinVolume,
								Before:     []string{"line1"},
								ProcessData: map[string]interface{}{
									fmt.Sprintf("%v:%v", 0, "line1") + ":zoneVolume:max": "z1:100;",
								},
							},
						},
					},
				},
			},
			want: map[string]bool{
				schedule_factor.MinVolume: true,
			},
			want1: []string{"z1"},
			want2: 0,
		},
		{
			name: "case 3: v1 and zoneCodeList is nil",
			args: args{
				StageList: []routing_log.SoftCriteriaRuleStage{
					{
						FactorCombination: []routing_log.SchedulingFactorCombination{
							{
								FactorName:  schedule_factor.MinVolume,
								ProcessData: map[string]interface{}{},
							},
							{
								FactorName:  schedule_factor.MinVolume,
								Before:      []string{"line1"},
								ProcessData: map[string]interface{}{},
							},
						},
					},
				},
			},
			want: map[string]bool{
				schedule_factor.MinVolume: true,
			},
			want1: nil,
			want2: 0,
		},
		{
			name: "case 4: v2",
			args: args{
				StageList: []routing_log.SoftCriteriaRuleStage{
					{
						FactorCombination: []routing_log.SchedulingFactorCombination{
							{
								FactorName: schedule_factor.MinVolumeV2Name,
								Before:     []string{"line1", "line2"},
								ProcessData: map[string]interface{}{
									fmt.Sprintf("%v:%v", 0, "line1") + ":zoneVolume:min": "z1:100;",
									fmt.Sprintf("%v:%v", 0, "line2") + ":zoneVolume:max": "z2:100;",
								},
							},
						},
					},
				},
			},
			want: map[string]bool{
				schedule_factor.MinVolumeV2Name: true,
			},
			want1: []string{"z1", "z2"},
			want2: 0,
		},
		{
			name: "case 5: default return",
			args: args{
				StageList: []routing_log.SoftCriteriaRuleStage{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := getFactoryNameMap(ctx, tt.args.productIdV1, tt.args.productIdV2, tt.args.StageList)
			common.AssertResult(t, got, tt.want, nil, nil)
			common.AssertResult(t, got1, tt.want1, nil, nil)
			if got2 != tt.want2 {
				t.Errorf("getFactoryNameMap() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func Test_getTableTitles(t *testing.T) {
	type args struct {
		factoryNameMap map[string]bool
		zoneCodeList   []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolumeV2Name:            true,
					schedule_factor.MaxCapacityV2Name:          true,
					schedule_factor.MaxCodCapacityV2Name:       true,
					schedule_factor.MaxBulkyCapacityV2Name:     true,
					schedule_factor.MaxHighValueCapacityV2Name: true,
					schedule_factor.MaxDgCapacityV2Name:        true,
					schedule_factor.LineCheapestShippingFee:    true,
				},
				zoneCodeList: []string{"z1"},
			},
			want: []string{"Input Lane Code", "Output Lane Code", "Line ID", "Line Min Limit", "Line Max Limit", "Current Line Volume",
				"Zone Min Limit of z1", "Zone Max Limit of z1", "Current Zone Volume of z1",
				"Line Max Cod Limit", "Current Line Cod Volume", "Zone Max Cod Limit of z1", "Current Zone Cod Volume of z1",
				"Line Max Bulky Limit", "Current Line Bulky Volume", "Zone Max Bulky Limit of z1", "Current Zone Bulky Volume of z1",
				"Line Max HighValue Limit", "Current Line HighValue Volume", "Zone Max HighValue Limit of z1", "Current Zone HighValue Volume of z1",
				"Line Max DG Limit", "Current Line DG Volume", "Zone Max DG Limit of z1", "Current Zone DG Volume of z1",
				"Shipping Fee", "Weightage/Priority"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTableTitles(tt.args.factoryNameMap, tt.args.zoneCodeList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getTableTitles() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetSoftCriteriaTableDataNum(t *testing.T) {
	type args struct {
		factoryNameMap map[string]bool
		len            int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{
			name: "case 1: v1 normal result",
			args: args{
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolume:               true,
					schedule_factor.MaxCapacity:             true,
					schedule_factor.MaxCodCapacity:          true,
					schedule_factor.MaxBulkyCapacity:        true,
					schedule_factor.MaxHighValueCapacity:    true,
					schedule_factor.MaxDgCapacity:           true,
					schedule_factor.LineCheapestShippingFee: true,
				},
				len: 1,
			},
			want: 17,
		},
		{
			name: "case 2: v2 normal result",
			args: args{
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolumeV2Name:            true,
					schedule_factor.MaxCapacityV2Name:          true,
					schedule_factor.MaxCodCapacityV2Name:       true,
					schedule_factor.MaxBulkyCapacityV2Name:     true,
					schedule_factor.MaxHighValueCapacityV2Name: true,
					schedule_factor.MaxDgCapacityV2Name:        true,
					schedule_factor.LineCheapestShippingFee:    true,
				},
				len: 1,
			},
			want: 23,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetSoftCriteriaTableDataNum(tt.args.factoryNameMap, tt.args.len); got != tt.want {
				t.Errorf("GetSoftCriteriaTableDataNum() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getTableData(t *testing.T) {
	type args struct {
		f              routing_log.SchedulingFactorCombination
		productId      int
		beforeLane     []string
		factoryNameMap map[string]bool
		zoneCodeList   []string
	}
	tests := []struct {
		name  string
		args  args
		want  [][]string
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				f: routing_log.SchedulingFactorCombination{
					Before:      []string{"line1", "line2"},
					ProcessData: map[string]interface{}{},
					After:       []string{"line1"},
				},
				beforeLane: []string{"lane2"},
			},
			want: [][]string{{"lane2", "lane2", "line1", ""}},
			setup: func() {
				lineToLaneMap = map[string][]string{
					"line1": {"lane1", "lane2", "lane2", "lane3"},
					"line2": {"lane1", "lane2", "lane2", "lane3"},
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := getTableData(tt.args.f, tt.args.productId, tt.args.beforeLane, tt.args.factoryNameMap, tt.args.zoneCodeList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getTableData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetZoneDisplayData(t *testing.T) {
	type args struct {
		zoneCodeList []string
		limitMap     map[string]string
		volumeMap    map[string]string
		isV2         bool
	}
	tests := []struct {
		name              string
		args              args
		want              []string
		wantCurrentVolume []string
	}{
		// TODO: Add test cases.
		{
			name: "case 1: v1 normal result",
			args: args{
				zoneCodeList: []string{"z1"},
				limitMap: map[string]string{
					"z1": "1",
				},
			},
			want:              []string{""},
			wantCurrentVolume: []string{"1"},
		},
		{
			name: "case 2: v2 normal result",
			args: args{
				zoneCodeList: []string{"z1"},
				limitMap: map[string]string{
					"z1": "1",
				},
				volumeMap: map[string]string{
					"z1": "2",
				},
				isV2: true,
			},
			want:              []string{"1"},
			wantCurrentVolume: []string{"2"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotCurrentVolume := GetZoneDisplayData(tt.args.zoneCodeList, tt.args.limitMap, tt.args.volumeMap, tt.args.isV2)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetZoneDisplayData() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(gotCurrentVolume, tt.wantCurrentVolume) {
				t.Errorf("GetZoneDisplayData() gotCurrentVolume = %v, want %v", gotCurrentVolume, tt.wantCurrentVolume)
			}
		})
	}
}

func Test_getMinMaxTableData(t *testing.T) {
	type args struct {
		f              routing_log.SchedulingFactorCombination
		isMin          bool
		m              map[string]interface{}
		lineKey        string
		isMax          bool
		zoneCodeList   []string
		lineId         string
		isV2           bool
		factoryNameMap map[string]bool
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal minV2 result",
			args: args{
				isMax: true,
				isMin: true,
				f: routing_log.SchedulingFactorCombination{
					FactorName: schedule_factor.MinVolumeV2Name,
					Before:     []string{"line1"},
				},
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolumeV2Name:   true,
					schedule_factor.MaxCapacityV2Name: true,
				},
				zoneCodeList: []string{"z1"},
			},
			want: []string{"", "", "", "", "", ""},
		},
		{
			name: "case 2: normal maxV2 result",
			args: args{
				isMax: true,
				isMin: true,
				f: routing_log.SchedulingFactorCombination{
					FactorName: schedule_factor.MaxCapacityV2Name,
					Before:     []string{"line1"},
				},
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolumeV2Name:   true,
					schedule_factor.MaxCapacityV2Name: true,
				},
				zoneCodeList: []string{"z1"},
			},
			want: []string{"", "", "", "", "", ""},
		},
		{
			name: "case 3: normal non-min non-max result",
			args: args{
				isMax: true,
				isMin: true,
				f: routing_log.SchedulingFactorCombination{
					FactorName: schedule_factor.LineCheapestShippingFee,
					Before:     []string{"line1"},
				},
				factoryNameMap: map[string]bool{
					schedule_factor.MinVolumeV2Name:   true,
					schedule_factor.MaxCapacityV2Name: true,
				},
				zoneCodeList: []string{"z1"},
			},
			want: []string{"", "", "", "", "", ""},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMinMaxTableData(tt.args.f, tt.args.isMin, tt.args.m, tt.args.lineKey, tt.args.isMax, tt.args.zoneCodeList, tt.args.lineId, tt.args.isV2, tt.args.factoryNameMap); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getMinMaxTableData() = %v, want %v", got, tt.want)
			}
		})
	}
}
