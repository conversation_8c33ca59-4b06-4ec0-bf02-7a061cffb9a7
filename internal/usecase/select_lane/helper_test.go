package select_lane

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"reflect"
	"testing"
)

func TestSmartRoutingServiceImpl_composeSmartRoutingOrderData(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	var patch *gomonkey.Patches
	type args struct {
		in       *pb.SelectLaneReq
		logEntry *routing_log.RoutingLog
	}
	tests := []struct {
		name  string
		args  args
		want  *rule.SmartRoutingOrderData
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: shipmentType == constant.PickupShipmentType",
			args: args{
				in: &pb.SelectLaneReq{
					Order: &pb.OrderInfo{
						OrderInfoFromOms: &pb.CreateOrderRequestData{
							BaseInfo: &pb.CreateOrderBaseInfo{
								PickupTime: proto.Int32(*********),
								IsReturn:   proto.Int32(1),
								WhsId:      proto.String("1"),
							},
							ForderInfo: &pb.CreateOrderForderInfo{
								PaymentMethod: proto.String("cod"),
							},
							DeliverInfo: &pb.CreateOrderDeliverInfo{},
							PickupInfo:  &pb.CreateOrderPickupInfo{},
							LaneInfo:    &pb.CreateOrderLaneInfo{},
							Skus: []*pb.CreateOrderSKU{
								{},
							},
						},
					},
				},
			},
			want: &rule.SmartRoutingOrderData{
				WmsFlag:      1,
				ShipmentType: constant.PickupShipmentType,
				IsReturn:     1,
				Items: []*rule.SmartRoutingOrderDataItem{
					{},
				},
				IsCod: true,
			},
			setup: func() {
				s = &SmartRoutingServiceImpl{
					AddrRepo: &address.AddrRepoImpl{},
				}
			},
		},
		{
			name: "case 2: shipmentType == constant.DropOffShipmentType",
			args: args{
				in: &pb.SelectLaneReq{
					Order: &pb.OrderInfo{
						OrderInfoFromOms: &pb.CreateOrderRequestData{
							BaseInfo: &pb.CreateOrderBaseInfo{
								PickupTime: proto.Int32(-1),
								IsReturn:   proto.Int32(1),
								WhsId:      proto.String("1"),
							},
							ForderInfo: &pb.CreateOrderForderInfo{
								PaymentMethod: proto.String("cod"),
							},
							DeliverInfo: &pb.CreateOrderDeliverInfo{},
							PickupInfo:  &pb.CreateOrderPickupInfo{},
							LaneInfo:    &pb.CreateOrderLaneInfo{},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{
					ExtraInfo: routing_log.ExtraInfo{},
				},
			},
			want: &rule.SmartRoutingOrderData{
				WmsFlag:      1,
				ShipmentType: constant.DropOffShipmentType,
				IsReturn:     1,
				IsCod:        true,
			},
			setup: func() {
				addrRepoImpl := &address.AddrRepoImpl{}
				s = &SmartRoutingServiceImpl{
					AddrRepo: addrRepoImpl,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(addrRepoImpl), "GetLocationByLocFullPathName", func(p *address.AddrRepoImpl, ctx context.Context, cid, state, city, district, street string) (*address.Location, *srerr.Error) {
					return &address.Location{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			common.AssertResult(t, s.composeSmartRoutingOrderData(ctx, tt.args.in, tt.args.logEntry), tt.want, nil, nil)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
