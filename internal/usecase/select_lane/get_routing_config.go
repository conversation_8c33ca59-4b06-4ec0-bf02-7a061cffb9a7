package select_lane

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"github.com/gogo/protobuf/proto"
)

func (s *SmartRoutingServiceImpl) GetRoutingConfig(ctx context.Context, req *pb.GetRoutingConfigReq) (*pb.GetRoutingConfigRsp, error) {
	rsp := &pb.GetRoutingConfigRsp{}
	routingConfig, err := s.RoutingConfig.GetRoutingConfigCacheByProductID(ctx, int(req.GetProductId()))
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, err
	}

	rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)
	rsp.RoutingConfig = &pb.RoutingConfig{
		CBSmartRoutingEnable:      proto.Bool(routingConfig.SmartRoutingEnabled),
		CBMultiSmartRoutingEnable: proto.Bool(routingConfig.CBMultiSmartRoutingEnabled),
		SPXSmartRoutingEnable:     proto.Bool(routingConfig.SpxSmartRoutingEnabled),
		LocalSmartRoutingEnable:   proto.Bool(routingConfig.LocalSmartRoutingEnabled),
	}

	return rsp, nil
}
