package select_lane

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strings"
)

const (
	DisableRoutingLog = "config.disable_routing_log" // downgrade, do not send routing log message to saturn
)

func setupRoutingLogBasicInfo(ctx context.Context, log *routing_log.RoutingLog, req *pb.SelectLaneReq, rule *rule.RoutingRuleParsed, roleMap map[int]int, availableLaneList []*pb.LaneServiceable, dgType int) {
	log.ProductId = int(req.GetProductInfo().GetProductId())
	log.FOrderId = req.GetOrder().GetOrderInfoFromOms().GetBaseInfo().GetForderid()
	//设置lps硬性校验的入参
	log.BeforeHardCriteriaLaneList = req.GetExtraMsg().GetBeforeHardCriteriaLaneList()
	log.HardCriteriaResult = availableLaneList
	log.RequestId = req.GetHeader().GetRequestId()
	log.CacheOrderType = req.GetCacheOrderType()
	if rule != nil {
		log.RuleId = int(rule.ID)
		log.RoutingType = int(rule.RoutingType)
		//记录一下开和关闭的rule
		log.LineToggle = make(map[string]*routing_log.Toggle)
		for k, v := range rule.LineToggle {
			log.LineToggle[k] = &routing_log.Toggle{On: v.On, Off: v.Off}
		}
	}
	log.RoutingResult.RoutingRole = roleMap
	log.RequestTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	log.ExtraInfo.ValidationWeight = req.GetOrder().GetValidationWeight()
	log.ExtraInfo.DgType = dgType
	log.ExtraInfo.DeliveryLocationIdList = make([]uint, len(req.GetOrder().GetDeliveryLocationIdList()))
	//这里参数类型需要转化一下，cb预测那里之前用的是uint，单pb文件里面是uint64，这个字段只有在预测的时候用到。
	for i := 0; i < len(req.GetOrder().GetDeliveryLocationIdList()); i++ {
		log.ExtraInfo.DeliveryLocationIdList[i] = uint(req.GetOrder().GetDeliveryLocationIdList()[i])
	}
}
func setRoutingToggle(log *routing_log.RoutingLog, conf *rule.RoutingConfig) {
	/*
		用routing_toggle的二进制位表示是否开启对应的routing
		Local 表示第一位，CB表示第二位，SPX表示第三位，ILH表示第四位，MultiCB标记第五位(从低位数)
		例如 routing_toggle表示三3，对应的二进制为11，表示开启了local和CB routing
	*/
	if log != nil && conf != nil {
		res := 0
		if conf.LocalSmartRoutingEnabled {
			res = 1
		}
		if conf.SmartRoutingEnabled {
			res = res ^ (1 << 1)
		}
		if conf.SpxSmartRoutingEnabled {
			res = res ^ (1 << 2)
		}
		if conf.IlhSmartRoutingEnabled {
			res = res ^ (1 << 3)
		}
		if conf.CBMultiSmartRoutingEnabled {
			res = res ^ (1 << 4)
		}
		log.RoutingToggle = res
	}
}
func getDisableRoutingLog(ctx context.Context) bool {
	return config.GetBoolWithContext(ctx, DisableRoutingLog, false)
}

func setupFinalRes(logEntry *routing_log.RoutingLog, res []*rule.RoutingLaneInfo) {
	if res == nil || len(res) < 1 {
		return
	}
	logEntry.RoutingStatus = true
	logEntry.FinalResult.LaneCode = res[0].LaneCode
	logEntry.FinalResult.LaneCodeGroup = res[0].LaneCodeGroup

	lineMap := make(map[string][]string)
	lines := res[0].LineList
	for _, line := range lines {
		key := lfslib.LineSubTypeMap[line.ResourceSubType]
		lineMap[key] = append(lineMap[key], line.LineId)
	}
	routingResult := ""
	for k, v := range lineMap {
		lines := strings.Join(v, "_")
		routingResult += k + ":" + lines + ";"
	}
	logEntry.FinalResult.Result = res[0].LaneCode + "：" + routingResult
	//调度失败直接展示调度失败原因
	if logEntry.ExtraMsg != "" {
		logEntry.FinalResult.Result = logEntry.ExtraMsg
	}
	logEntry.FinalResult.ActualPointList = getActualPoint(res[0])
	logEntry.ExtraInfo.ShippingFee = GetShippingFee(logEntry, res[0])
}

func getActualPoint(laneInfo *rule.RoutingLaneInfo) []routing_log.ActualPoint {
	actualPoints := make([]routing_log.ActualPoint, 0, len(laneInfo.ActualPointList))
	for _, point := range laneInfo.ActualPointList {
		actualPoints = append(actualPoints, routing_log.ActualPoint{
			PointId:     point.PointId,
			PointType:   point.PointType,
			SiteSubType: point.SiteSubType,
			SiteId:      point.SiteId,
		})
	}

	return actualPoints
}

func SetupILHRoutingLogBasicInfo(log *routing_log.RoutingLog, rule *rule.RoutingRuleParsed, roleMap map[int]int) {
	if rule != nil {
		log.RuleId = int(rule.ID)
	}
	log.RoutingResult.RoutingRole = roleMap
}

// GetScheduleResultFactor 从routingLog解析调度日志
func GetScheduleResultFactor(routingLog *routing_log.RoutingLog) string {
	if routingLog == nil {
		return ""
	}
	// 倒叙从routing_log解析调度因子
	if len(routingLog.RoutingResult.SpxSelfBuildProcess.After) > 1 {
		return "FinalFirstResult"
	}
	if len(routingLog.RoutingResult.SpxSelfBuildProcess.After) == 1 {
		return "SpxSelfBuildProcess"
	}
	if routingLog.RoutingResult.DefaultCriteriaFilterProcess.FactorName != "" {
		return routingLog.RoutingResult.DefaultCriteriaFilterProcess.FactorName
	}
	if len(routingLog.RoutingResult.SoftCriteriaFilterProcess.StageList) > 0 {
		stageListLen := len(routingLog.RoutingResult.SoftCriteriaFilterProcess.StageList)
		if len(routingLog.RoutingResult.SoftCriteriaFilterProcess.StageList[stageListLen-1].FactorCombination) > 0 {
			factorLen := len(routingLog.RoutingResult.SoftCriteriaFilterProcess.StageList[stageListLen-1].FactorCombination)
			return routingLog.RoutingResult.SoftCriteriaFilterProcess.StageList[stageListLen-1].FactorCombination[factorLen-1].FactorName
		}
	}
	if len(routingLog.RoutingResult.AvailableFilterProcess.After) == 1 {
		return "AvailableFilterProcess"
	}
	return "HardCriteriaProcess"
}

func GetShippingFee(logEntry *routing_log.RoutingLog, finResultLane *rule.RoutingLaneInfo) float64 {
	shippingFee := 0.0
	if logEntry == nil {
		return shippingFee
	}
	for _, stage := range logEntry.RoutingResult.SoftCriteriaFilterProcess.StageList {
		for _, factor := range stage.FactorCombination {
			if factor.FactorName == schedule_factor.LineCheapestShippingFee {
				m, ok := factor.ProcessData.(map[string]float64)
				if !ok {
					continue
				}
				for _, line := range finResultLane.LineList {
					if fee := m[line.LineId]; fee > 0 {
						shippingFee += fee
					}
				}
			}
		}
	}

	return shippingFee
}
