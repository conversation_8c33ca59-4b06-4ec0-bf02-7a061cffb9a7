package select_lane

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
)

func nonLiveMock(ctx context.Context, orderSN *string) *rule.RoutingLaneInfo {
	if envvar.GetEnvWithCtx(ctx) == enum.LIVE {
		return nil
	}
	if orderSN == nil || *orderSN == "" {
		return nil
	}
	parts := strings.Split(*orderSN, "_")
	if len(parts) < 2 {
		return nil
	}

	return &rule.RoutingLaneInfo{
		LaneCode: parts[1],
	}
}

func checkExistInLaneList(lanecode string, srcLaneList []*pb.LaneServiceable) bool {
	for _, srcLane := range srcLaneList {
		if srcLane.GetLaneCode() == lanecode {
			return true
		}
	}
	return false
}
