package select_lane

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"reflect"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
	"time"
)

func TestSmartRoutingServiceImpl_isResourceNeedDgWithLru(t *testing.T) {
	ctx := context.Background()
	lanesrv := &lane.LaneServiceImpl{}
	s := &SmartRoutingServiceImpl{}

	type args struct {
		laneCode        string
		laneCodeGroup   []string
		resourceSubType uint32
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: resourceDgLruCache.Get success",
			args: args{},
			want: true,
			setup: func() {
				resourceDgLruCache.Add(ctx, ":0", true)
			},
		},
		{
			name: "case 2: resourceDgLruCache.Get fail",
			args: args{
				resourceSubType: lfslib.C_FL,
				laneCodeGroup:   []string{"1", "2", "3"},
			},
			want: true,
			setup: func() {
				s = &SmartRoutingServiceImpl{
					LaneSrv: lanesrv,
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := s.isResourceNeedDgWithLru(ctx, tt.args.laneCode, tt.args.laneCodeGroup, tt.args.resourceSubType); got != tt.want {
				t.Errorf("isResourceNeedDgWithLru() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSmartRoutingServiceImpl_LoadRoutingLanes(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	type args struct {
		availableLanes []*pb.LaneServiceable
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.RoutingLaneInfo
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				availableLanes: []*pb.LaneServiceable{
					{
						ResourceServiceableCollection: []*pb.ResourceServiceable{
							{
								ResourceType: proto.Uint32(lfslib.ResourceTypeSite),
								ActualPoint:  []*pb.ActualPoint{{}},
							},
							{
								ResourceType:    proto.Uint32(lfslib.ResourceTypeLine),
								ResourceSubType: proto.Uint32(lfslib.C_FL),
							},
						},
						LaneCodeGroup: []string{"1", "2", "3"},
					},
					{
						ResourceServiceableCollection: []*pb.ResourceServiceable{
							{
								ResourceType: proto.Uint32(lfslib.ResourceTypeSite),
								ActualPoint:  []*pb.ActualPoint{{}},
							},
							{
								ResourceType:    proto.Uint32(lfslib.ResourceTypeLine),
								ResourceSubType: proto.Uint32(lfslib.C_FL),
							},
						},
						LaneCodeGroup: []string{"1", "2", "3"},
					},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType: lfslib.C_FL,
							DgRelated:       1,
							DGFlag:          rule.NonDG,
						},
					},
					ActualPointList: []rule.ActualPoint{{}},
					LaneCodeGroup:   []string{"1", "2", "3"},
				},
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType: lfslib.C_FL,
							DgRelated:       1,
							DGFlag:          rule.NonDG,
						},
					},
					ActualPointList: []rule.ActualPoint{{}},
					LaneCodeGroup:   []string{"1", "2", "3"},
				},
			},
			setup: func() {
				s = &SmartRoutingServiceImpl{
					LaneSrv: &lane.LaneServiceImpl{},
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			common.AssertResult(t, s.LoadRoutingLanes(ctx, tt.args.availableLanes), tt.want, nil, nil)
		})
	}
}

func TestSmartRoutingServiceImpl_sendLogMessage(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	var patch, patchWhiteList, patchDisableLog *gomonkey.Patches
	type args struct {
		log             *routing_log.RoutingLog
		routingLaneInfo []*rule.RoutingLaneInfo
		in              *pb.SelectLaneReq
	}
	tests := []struct {
		name  string
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: en(whiteList) == 0",
			args: args{
				log: &routing_log.RoutingLog{},
				routingLaneInfo: []*rule.RoutingLaneInfo{
					{},
				},
			},
			setup: func() {
				s = &SmartRoutingServiceImpl{
					routingLog: &routing_log.RoutingLogServiceImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(s.routingLog), "SendRoutingLogToTask", func(r *routing_log.RoutingLogServiceImpl,
					ctx context.Context, orderData *pb.CreateOrderRequestData, routingLog *routing_log.RoutingLog) *srerr.Error {
					return nil
				})
				patchWhiteList = gomonkey.ApplyFunc(configutil.GetWriteList, func(ctx context.Context) configutil.RoutingLogWriteListConfig {
					return configutil.RoutingLogWriteListConfig{}
				})
				patchDisableLog = gomonkey.ApplyFunc(config.GetBoolWithContext, func(ctx context.Context) bool {
					return false
				})
			},
		},
		{
			name: "case 2: normal result",
			args: args{
				log: &routing_log.RoutingLog{},
				routingLaneInfo: []*rule.RoutingLaneInfo{
					{},
				},
			},
			setup: func() {
				s = &SmartRoutingServiceImpl{
					routingLog: &routing_log.RoutingLogServiceImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(s.routingLog), "SendRoutingLogToTask", func(r *routing_log.RoutingLogServiceImpl,
					ctx context.Context, orderData *pb.CreateOrderRequestData, routingLog *routing_log.RoutingLog) *srerr.Error {
					return srerr.New(srerr.ParamErr, nil, "mock error")
				})
				patchWhiteList = gomonkey.ApplyFunc(configutil.GetWriteList, func(ctx context.Context) configutil.RoutingLogWriteListConfig {
					return configutil.RoutingLogWriteListConfig{
						List: []int{0},
					}
				})
				patchDisableLog = gomonkey.ApplyFunc(config.GetBoolWithContext, func(ctx context.Context) bool {
					return false
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			s.sendLogMessage(ctx, tt.args.log, tt.args.routingLaneInfo, tt.args.in)
			if patch != nil {
				patch.Reset()
			}
			if patchWhiteList != nil {
				patchWhiteList.Reset()
			}
			if patchDisableLog != nil {
				patchDisableLog.Reset()
			}
		})
	}
}

func TestSmartRoutingServiceImpl_SetLineParcelSnapshot(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	var patch, patchSet *gomonkey.Patches
	type args struct {
		selectLaneReq    *pb.SelectLaneReq
		reportModule     string
		routingLaneInfo  []*rule.RoutingLaneInfo
		rsp              pb.SelectLaneRsp
		routingOrderData *rule.SmartRoutingOrderData
	}
	tests := []struct {
		name  string
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: redis set error",
			args: args{
				routingOrderData: &rule.SmartRoutingOrderData{},
				routingLaneInfo: []*rule.RoutingLaneInfo{
					{},
					{
						LaneCode: "lane1",
						LineList: []*rule.LineInfo{
							{},
						},
					},
				},
				rsp: pb.SelectLaneRsp{
					SelectedLane: &pb.RoutingLaneInfo{
						LaneCode: proto.String("lane1"),
					},
				},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(volumerouting.GetParcelTypeAttr, func(ctx context.Context, productId int64, lineId string, isCod bool, parcelDimension rule.OrderParcelDimension,
					cogs float64, dgFlag rule.DGFlag, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService, routingType int) *parcel_type_definition.ParcelTypeAttr {
					return &parcel_type_definition.ParcelTypeAttr{}
				})
				patchSet = gomonkey.ApplyFunc(redisutil.Set, func(ctx context.Context, key string, v interface{}, exp time.Duration) error {
					return errors.New("redis set error")
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			s.SetLineParcelSnapshot(ctx, tt.args.selectLaneReq, tt.args.reportModule, tt.args.routingLaneInfo, tt.args.rsp, tt.args.routingOrderData, 100)
			if patch != nil {
				patch.Reset()
			}
			if patchSet != nil {
				patchSet.Reset()
			}
		})
	}
}
