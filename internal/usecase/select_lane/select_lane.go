package select_lane

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/lrucache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

func NewSmartRoutingServiceImpl(
	routingSrv routing.RoutingService,
	routingRuleRepo routing.RoutingRuleRepo,
	lfsCli lfsclient.LfsApi,
	routingConfig routing_config.RoutingConfigService,
	addrRepo address.AddrRepo,
	factorSet *schedule_factor.FactorSet,
	routingLog routing_log.RoutingLogService,
	laneSrv lane.LaneService,
	ccRoutingSrv cc_routing.CCRoutingService,
	availableLhService available_lh.AvailableLHService,
	lhCapacityService lh_capacity.LHCapacityService,
	ilhRoutingService routing.ILHRoutingService,
	parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService,
) *SmartRoutingServiceImpl {
	return &SmartRoutingServiceImpl{
		RoutingService:              routingSrv,
		RoutingRule:                 routingRuleRepo,
		LfsCli:                      lfsCli,
		RoutingConfig:               routingConfig,
		AddrRepo:                    addrRepo,
		FactorSet:                   factorSet,
		routingLog:                  routingLog,
		LaneSrv:                     laneSrv,
		CCRoutingSrv:                ccRoutingSrv,
		availableLhService:          availableLhService,
		lhCapacityService:           lhCapacityService,
		ilhRoutingService:           ilhRoutingService,
		parcelTypeDefinitionService: parcelTypeDefinitionService,
	}
}

type SmartRoutingServiceImpl struct {
	pb.UnimplementedSmartRoutingServer

	RoutingService              routing.RoutingService
	RoutingRule                 routing.RoutingRuleRepo
	LfsCli                      lfsclient.LfsApi
	RoutingConfig               routing_config.RoutingConfigService
	AddrRepo                    address.AddrRepo
	FactorSet                   *schedule_factor.FactorSet
	routingLog                  routing_log.RoutingLogService
	LaneSrv                     lane.LaneService
	CCRoutingSrv                cc_routing.CCRoutingService
	availableLhService          available_lh.AvailableLHService
	lhCapacityService           lh_capacity.LHCapacityService
	ilhRoutingService           routing.ILHRoutingService
	parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
}

const (
	RerouteSelectLane = iota + 1
	CreateOrderSelectLane
)

const (
	NormalOrder           = 0
	CacheBookingOrder     = 1
	CacheFulfillmentOrder = 2
)

var (
	resourceDgLruCache, _ = cache.NewLruCache(cache.ResourceNeedDGLruName)
)

func (s *SmartRoutingServiceImpl) RerouteSelectLane(ctx context.Context, in *pb.SelectLaneReq) (*pb.SelectLaneRsp, error) {
	return s.selectLane(ctx, in, RerouteSelectLane)
}

func (s *SmartRoutingServiceImpl) SelectLane(ctx context.Context, in *pb.SelectLaneReq) (*pb.SelectLaneRsp, error) {
	return s.selectLane(ctx, in, CreateOrderSelectLane)
}

func (s *SmartRoutingServiceImpl) selectLane(ctx context.Context, selectLaneReq *pb.SelectLaneReq, reqType int) (*pb.SelectLaneRsp, error) {
	// 区分正常下单和Reroute的场景
	reportModule := monitoring.CatSelectLaneApi
	if reqType == RerouteSelectLane {
		reportModule = monitoring.CatRerouteSelectLaneApi
	}

	rsp := pb.SelectLaneRsp{}
	rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), nil)
	//todo,move into RoutingService.Routing
	logEntry := new(routing_log.RoutingLog)
	//required-params-validate
	paramValidErr := validateSchema(ctx, selectLaneReq)
	// cat上报记录request_id，方便查找日志
	requestId := selectLaneReq.GetHeader().GetRequestId()
	if paramValidErr != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneParamValid, fmt.Sprintf("requestId: %v, paramValidErr: %v", requestId, paramValidErr))
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), paramValidErr)
		return &rsp, nil
	}
	availableLaneList := selectLaneReq.GetAvailableLaneList()
	if len(availableLaneList) < 1 {
		monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneAvailableListCheck, fmt.Sprintf("requestId: %v, no availableLaneList", requestId))
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), srerr.New(srerr.NoAvailableLaneList, selectLaneReq.GetProductInfo().GetProductId(), ""))
		return &rsp, nil
	}

	conf, lErr := s.RoutingConfig.GetRoutingConfigCacheByProductID(ctx, int(selectLaneReq.GetProductInfo().GetProductId()))
	if lErr != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneRoutingConfigError, fmt.Sprintf("requestId: %v, get routing config error: %v", requestId, lErr))
		logger.CtxLogErrorf(ctx, "get routing config %v", lErr)
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), srerr.With(srerr.RoutingConfigNotFound, selectLaneReq.GetProductInfo().GetProductId(), lErr))
		return &rsp, nil
	}
	setRoutingToggle(logEntry, conf)
	laneList := s.LoadRoutingLanes(ctx, availableLaneList)
	dgType := s.getParcelDgType(laneList)

	if !conf.IsSupportSmartRouting() {
		if len(availableLaneList) > 1 {
			monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneTooManyAvailableLane, fmt.Sprintf("requestId: %v, not support routing and too many lane error", requestId))
			rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), srerr.New(srerr.TooManyAvailableLanes, selectLaneReq.GetProductInfo().GetProductId(), ""))
			return &rsp, nil
		}
		monitoring.ReportSuccess(ctx, reportModule, monitoring.SelectLaneNoSupportRoutingSuccess, fmt.Sprintf("requestId: %v, not support routing and success", requestId))

		// 日志设置
		setupRoutingLogBasicInfo(ctx, logEntry, selectLaneReq, nil, nil, availableLaneList, dgType)
		setupFinalRes(logEntry, laneList)
		//暂时测试用的
		rsp.SelectedLane = generatePbRsp(laneList[0])

		return &rsp, nil
	}

	if envvar.GetCID() == constant.TW && conf.IsCBSmartRouting() {
		var fErr *srerr.Error
		laneList, fErr = s.CCRoutingSrv.CCFilter(ctx, selectLaneReq.GetOrder().GetOrderInfoFromOms().GetBaseInfo().GetOrdersn(), laneList)
		if fErr != nil {
			monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneTwCCError, fmt.Sprintf("requestId: %v, tw cc err=%v", requestId, fErr))
			rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), fErr)
			return &rsp, nil
		}
	}

	routingType := conf.GetRoutingType()
	if selectLaneReq.GetProductInfo().GetSpecifySpxRouting() {
		routingType = rule.SPXRoutingType
	}

	ruleParam := generateRuleMatchParam(selectLaneReq, routingType, dgType)
	matchedRule, findRuleErr := s.RoutingRule.MatchFirstPriorityRoutingRule(ctx, ruleParam)
	if findRuleErr != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneMatchRuleError, fmt.Sprintf("requestId: %v, match rule error: %v", requestId, findRuleErr))
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), findRuleErr)
		return &rsp, nil
	}

	// reroute场景不需要mock
	if reqType != RerouteSelectLane {
		if mockedLane := nonLiveMock(ctx, selectLaneReq.GetOrder().GetOrderInfoFromOms().GetBaseInfo().Ordersn); mockedLane != nil {
			if !checkExistInLaneList(mockedLane.LaneCode, availableLaneList) {
				monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneMockError, fmt.Sprintf("requestId: %v, mock routing error", requestId))
				rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), srerr.New(srerr.NoAvailableLane, "", "Mock lane not in available lane list"))
				return &rsp, nil
			}
			monitoring.ReportSuccess(ctx, reportModule, monitoring.SelectLaneMockSuccess, fmt.Sprintf("requestId: %v, mock routing success", requestId))
			rsp.SelectedLane = generatePbRsp(mockedLane)
			return &rsp, nil
		}
	}

	ctx = context.WithValue(ctx, routing_log.RoutingLogContextKey, logEntry)

	roleMap := routing_role.UpdateLineResourceType(ctx, int(selectLaneReq.GetProductInfo().GetProductId()), routingType, laneList, selectLaneReq.GetProductInfo().GetIsMultiProduct(), logEntry)
	// 日志设置
	setupRoutingLogBasicInfo(ctx, logEntry, selectLaneReq, matchedRule, roleMap, availableLaneList, dgType)

	//SSCSMR-3797: 提前判断是否需要VbCB，若需要的话，需要提前获取SPX rule，然后一起判断toggle
	// checkMatchVnCb需要用到realResourceType，需要放到转换resourceType后面
	var matchedSPXRule *rule.RoutingRuleParsed
	if !configutil.BypassVncb(ctx) && checkMatchVnCb(laneList) {
		ruleParam.IsMultiProduct = false
		ruleParam.RoutingType = rule.SPXRoutingType

		matchedSPXRule, findRuleErr = s.RoutingRule.MatchFirstPriorityRoutingRule(ctx, ruleParam)
		if findRuleErr != nil {
			// 二段式3pl toggle校验，没有匹配到规则不能拦截主流程，故这里只做上报监控告警
			monitoring.ReportError(ctx, monitoring.CatVnCbPreToggleCheck, monitoring.SelectLaneMatchSpxRuleError, fmt.Sprintf("requestId: %v, match SPX rule error: %v", requestId, findRuleErr))
			logger.CtxLogErrorf(ctx, "requestId: %s, match SPX rule error: %v", requestId, findRuleErr)
		}
	}

	routingOrderData := s.composeSmartRoutingOrderData(ctx, selectLaneReq, logEntry)
	notOnlySPX := false
	// 加上业务场景，只有select_lane场景需要提前计算运费
	extraInfo := routing.ExtraInfo{
		BusinessType: constant.SelectLaneRouting,
	}
	routingLaneInfo, err := s.RoutingService.RoutingWithExtraInfo(ctx, int(selectLaneReq.GetProductInfo().GetProductId()), selectLaneReq.GetProductInfo().GetIsMultiProduct(), laneList, matchedRule, matchedSPXRule, routingOrderData, logEntry, notOnlySPX, &extraInfo)
	if err != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.SelectLaneSmartRoutingError, fmt.Sprintf("requestId: %v, smart routing error: %v", requestId, err))
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), err)
		return &rsp, nil
	}
	// reroute接口需要返回运费和3pl toggle校验之后的lane
	if reqType == RerouteSelectLane {
		AddRerouteResponse(ctx, &rsp, logEntry, laneList)
	}
	//常规调度流程，
	if selectLaneReq.GetCacheOrderType() != CacheBookingOrder {
		s.sendLogMessage(ctx, logEntry, routingLaneInfo, selectLaneReq) //非cache booking order才需要落库
	}
	prometheusutil.UnifiedSelectLaneReport(logEntry.ProductId, logEntry.RuleId, logEntry.FinalResult.LaneCode, GetScheduleResultFactor(logEntry), rule.GetRoutingTypeName(routingType))
	//2次调度逻辑处理,确保未改写lanecode 信息, 只读,
	vncbLog := new(routing_log.RoutingLog)
	setupRoutingLogBasicInfo(ctx, vncbLog, selectLaneReq, matchedSPXRule, roleMap, availableLaneList, dgType)
	s.VnCbStage(ctx, &rsp, conf, routingLaneInfo, ruleParam, routingOrderData, vncbLog, selectLaneReq, matchedSPXRule)

	s.SetLineParcelSnapshot(ctx, selectLaneReq, reportModule, routingLaneInfo, rsp, routingOrderData, int(routingType))
	monitoring.ReportSuccess(ctx, reportModule, monitoring.SelectLaneSmartRoutingSuccess, fmt.Sprintf("requestId: %v, smart routing success", requestId))
	return &rsp, nil
}

func (s *SmartRoutingServiceImpl) SetLineParcelSnapshot(ctx context.Context, selectLaneReq *pb.SelectLaneReq, reportModule string,
	routingLaneInfo []*rule.RoutingLaneInfo, rsp pb.SelectLaneRsp, routingOrderData *rule.SmartRoutingOrderData, routingType int) {
	lineParcelSnapshot := make(map[string]parcel_type_definition.ParcelTypeAttr)
	forderId := selectLaneReq.GetOrder().GetOrderInfoFromOms().GetBaseInfo().GetForderid()
	for _, routingLane := range routingLaneInfo {
		if routingLane.LaneCode != rsp.GetSelectedLane().GetLaneCode() {
			continue
		}
		for _, lineInfo := range routingLane.LineList {
			parcelTypeAttr := volumerouting.GetParcelTypeAttr(ctx, int64(selectLaneReq.GetProductInfo().GetProductId()), lineInfo.LineId,
				routingOrderData.IsCod, routingOrderData.OrderParcelDimension, routingOrderData.Cogs, lineInfo.DGFlag,
				s.parcelTypeDefinitionService, routingType)
			lineParcelSnapshot[lineInfo.LineId] = *parcelTypeAttr
		}
		break
	}
	unmarshal, err := json.Marshal(lineParcelSnapshot)
	if err != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.LineParcelSetError, fmt.Sprintf("unmarshalErr|requestId: %v", selectLaneReq.GetHeader().GetRequestId()))
		return
	}
	if redisErr := redisutil.Set(ctx, forderId, unmarshal, time.Hour*24); redisErr != nil {
		monitoring.ReportError(ctx, reportModule, monitoring.LineParcelSetError, fmt.Sprintf("requestId: %v", selectLaneReq.GetHeader().GetRequestId()))
	}
}

func (s *SmartRoutingServiceImpl) PreFulfillmentCheck(ctx context.Context, request *pb.PreFulfillmentCheckReq) (*pb.PreFulfillmentCheckRsp, error) {
	rsp := &pb.PreFulfillmentCheckRsp{}
	availableLaneList := request.GetAvailableLaneList()

	parcelDimensions := request.GetOrder().GetParcelDimension()
	if err := validateParcelDimensions(parcelDimensions.GetLength(), parcelDimensions.GetWidth(), parcelDimensions.GetHeight()); err != nil {
		monitoring.ReportError(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckParcelDimensionError, fmt.Sprintf("validate parcel dimension err:%v", err))
		rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	conf, err := s.RoutingConfig.GetRoutingConfigCacheByProductID(ctx, int(request.GetProductInfo().GetProductId()))
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckConfigError, fmt.Sprintf("get config err:%v", err))
		rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	// 在正式校验之前，再做一遍检查是否开了Smart Routing的逻辑，没开则直接返回成功，但这部分属于异常流量，需要上报错误
	if !conf.IsSupportSmartRouting() {
		monitoring.ReportError(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckNotSupportError, "Product not support smart routing")
		rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), nil)
		for _, availableLane := range availableLaneList {
			// 返回的Lane里不需要对Service Code赋值，无法取值，接口里是冗余无用的
			rsp.AvailableLaneList = append(rsp.AvailableLaneList, &pb.RoutingLaneInfo{
				LaneCode:      availableLane.LaneCode,
				LaneCodeGroup: availableLane.LaneCodeGroup,
				DgGroupId:     availableLane.DgGroupId,
			})
		}
		return rsp, nil
	}

	// 类型转换
	laneList := s.LoadRoutingLanes(ctx, availableLaneList)
	routing_role.UpdateLineResourceType(ctx, int(request.GetProductInfo().GetProductId()), conf.GetRoutingType(), laneList, request.GetProductInfo().GetIsMultiProduct(), nil)

	ruleParam := preFulfillmentCheckGenerateRuleMatchParam(request, conf, s.getParcelDgType(laneList))
	// find rule
	matchedRule, findRuleErr := s.RoutingRule.MatchFirstPriorityRoutingRule(ctx, ruleParam)
	if findRuleErr != nil {
		monitoring.ReportError(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckFindRuleError, fmt.Sprintf("find rule err:%v", err))
		rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), findRuleErr)
		return rsp, nil
	}

	orderData := &rule.SmartRoutingOrderData{IsWms: request.GetOrder().GetIsWms()}
	filterLanes, err := s.RoutingService.DisableCheck(ctx, int(request.GetProductInfo().GetProductId()), request.GetProductInfo().GetIsMultiProduct(), laneList, matchedRule, orderData)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckDisableCheckError, fmt.Sprintf("filter lane err:%v", err))
		rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	rsp.Header = grpc_util.GenerateRespHeader(request.GetHeader().GetRequestId(), nil)
	for _, filterLane := range filterLanes {
		rsp.AvailableLaneList = append(rsp.AvailableLaneList, &pb.RoutingLaneInfo{
			LaneCode:      proto.String(filterLane.LaneCode),
			LaneCodeGroup: filterLane.LaneCodeGroup,
			ServiceCode:   proto.String(filterLane.ServiceCode),
			DgGroupId:     proto.String(filterLane.DgGroupId),
		})
	}
	monitoring.ReportSuccess(ctx, monitoring.CatPreFulfillmentCheckApi, monitoring.PreFulfillmentCheckSuccess, "")
	return rsp, nil
}

func AddRerouteResponse(ctx context.Context, rsp *pb.SelectLaneRsp, logEntry *routing_log.RoutingLog, laneList []*rule.RoutingLaneInfo) {
	if logEntry == nil {
		return
	}
	laneCodeMap := make(map[string]bool)
	for laneCode := range logEntry.RoutingResult.AvailableFilterProcess.After {
		laneCodeMap[laneCode] = true
	}
	var availLanes []*pb.AvailableLanes
	for _, laneInfo := range laneList {
		if _, ok := laneCodeMap[laneInfo.LaneCode]; ok {
			availLanes = append(availLanes, &pb.AvailableLanes{
				LaneCode:      proto.String(laneInfo.LaneCode),
				LaneCodeGroup: laneInfo.LaneCodeGroup,
				ServiceCode:   proto.String(laneInfo.ServiceCode),
			})
		}
	}
	rsp.AvailableLanes = availLanes
	var allocationShippingFee []*pb.AllocationShippingFee
	for line, fee := range logEntry.LineFeeMap {
		allocationShippingFee = append(allocationShippingFee, &pb.AllocationShippingFee{
			LineId:       proto.String(line),
			AllocationSf: proto.Float64(fee),
		})
	}
	rsp.AllocationShippingFee = allocationShippingFee
	logger.CtxLogInfof(ctx, "AddRerouteResponse add extra shipping fee, rsp is %v", rsp)
}

func (s *SmartRoutingServiceImpl) sendLogMessage(ctx context.Context, log *routing_log.RoutingLog, routingLaneInfo []*rule.RoutingLaneInfo, in *pb.SelectLaneReq) {
	if log != nil {
		setupFinalRes(log, routingLaneInfo)
	}

	if log != nil && !getDisableRoutingLog(ctx) {
		whiteList := configutil.GetWriteList(ctx).List
		var logErr *srerr.Error
		if len(whiteList) == 0 {
			logErr = s.routingLog.SendRoutingLogToTask(ctx, in.GetOrder().GetOrderInfoFromOms(), log)
		} else if objutil.ContainsInt(whiteList, log.ProductId) {
			logErr = s.routingLog.SendRoutingLogToTask(ctx, in.GetOrder().GetOrderInfoFromOms(), log)
		}
		if logErr != nil {
			logger.CtxLogErrorf(ctx, "push2kafka err=%v", logErr)
		}
	}
}

// @core LoadRoutingLanes 装载请求参数的Lane到调度的Lane，做类型转换以及DG信息填充
func (s *SmartRoutingServiceImpl) LoadRoutingLanes(ctx context.Context, availableLanes []*pb.LaneServiceable) []*rule.RoutingLaneInfo {
	var laneInfos []*rule.RoutingLaneInfo

	for _, availableLane := range availableLanes {
		var lineInfos []*rule.LineInfo
		var actualPoints []rule.ActualPoint
		for _, resource := range availableLane.GetResourceServiceableCollection() {
			if resource.GetResourceType() == lfslib.ResourceTypeSite {
				for _, point := range resource.GetActualPoint() {
					actualPoints = append(actualPoints, rule.ActualPoint{
						PointId:     point.GetPointId(),
						PointType:   point.GetPointType(),
						SiteSubType: point.GetSiteSubType(),
						SiteId:      point.GetSiteId(),
					})
				}

			}
			if resource.GetResourceType() != lfslib.ResourceTypeLine {
				continue
			}
			lineInfo := &rule.LineInfo{
				LineId:          resource.GetResourceId(),
				ResourceSubType: int32(resource.GetResourceSubType()),
				ResourceId:      resource.GetResourceId(),
				DGFlag:          rule.DGFlag(resource.GetDgServiceable().GetAbility().GetDgType()),
			}
			if s.isResourceNeedDgWithLru(ctx, availableLane.GetLaneCode(), availableLane.GetLaneCodeGroup(), resource.GetResourceSubType()) {
				lineInfo.DgRelated = rule.DgRelated
			}
			lineInfos = append(lineInfos, lineInfo)
		}
		laneInfo := &rule.RoutingLaneInfo{
			LaneCode:             availableLane.GetLaneCode(),
			LaneCodeGroup:        availableLane.GetLaneCodeGroup(),
			LineList:             lineInfos,
			DgGroupId:            availableLane.GetDgGroupId(),
			CustomsClearanceList: availableLane.GetCustomsClearanceList(),
			ActualPointList:      actualPoints,
		}
		logger.CtxLogDebugf(ctx, "Load lane info, laneCode=%v", availableLane.GetLaneCode())
		laneInfos = append(laneInfos, laneInfo)
	}

	return laneInfos
}

func (s *SmartRoutingServiceImpl) isResourceNeedDgWithLru(ctx context.Context, laneCode string, laneCodeGroup []string, resourceSubType uint32) bool {
	// 这里用+做字符串拼接性能会更好
	key := laneCode + ":"
	for _, l := range laneCodeGroup {
		key += l + ":"
	}
	key += strconv.Itoa(int(resourceSubType))

	if cacheVal, exist := resourceDgLruCache.Get(ctx, key); exist {
		if val, ok := cacheVal.(bool); ok {
			return val
		}
	}

	need := s.LaneSrv.IsResourceNeedDg(ctx, laneCode, laneCodeGroup, resourceSubType)
	resourceDgLruCache.Add(ctx, key, need)

	return need
}

func (s *SmartRoutingServiceImpl) getParcelDgType(res []*rule.RoutingLaneInfo) int {
	dgType := int(rule.UndefinedDGFlag)
	for _, lane := range res {
		for _, line := range lane.LineList {
			if line.DgRelated == rule.DgRelated {
				if line.DGFlag == rule.DG {
					return int(rule.DG)
				} else if line.DGFlag == rule.NonDG {
					dgType = int(rule.NonDG)
				}
			}
		}
	}
	return dgType
}
