package select_lane

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func validateSchema(ctx context.Context, in *pb.SelectLaneReq) *srerr.Error {
	//not-required,
	if in.GetHeader() == nil {
		return srerr.New(srerr.ParamInvalid, "", "%v", "header")
	}

	//|| in.GetProductInfo().GetValidationWeight() == nil my is nil,
	if in.GetProductInfo() == nil {
		return srerr.New(srerr.ParamInvalid, "", "%v", "ProductInfo ProductInfo.validation-weight")
	}

	if in.GetAvailableLaneList() == nil {
		return srerr.New(srerr.ParamInvalid, "", "%v", "avaliablelanelist")
	}

	if in.GetOrder() == nil || in.GetOrder().GetOrderInfoFromOms() == nil ||
		in.GetOrder().GetOrderInfoFromOms().GetBaseInfo() == nil ||
		in.GetOrder().GetOrderInfoFromOms().GetForderInfo() == nil ||
		in.GetOrder().GetOrderInfoFromOms().GetPickupInfo() == nil || in.GetOrder().GetOrderInfoFromOms().GetDeliverInfo() == nil {
		return srerr.New(srerr.ParamInvalid, "", "%v", "orderinfoFromOms")
	}

	parcelDimensions := in.GetOrder().GetParcelDimension()
	return validateParcelDimensions(parcelDimensions.GetLength(), parcelDimensions.GetWidth(), parcelDimensions.GetHeight())
}

func validateParcelDimensions(length, width, height float64) *srerr.Error {
	if length < rule.MinParcelSize || width < rule.MinParcelSize || height < rule.MinParcelSize || length > rule.MaxParcelSize || width > rule.MaxParcelSize || height > rule.MaxParcelSize {
		return srerr.New(srerr.ParamInvalid, "", "%v", "parcel dimensions")
	}
	return nil
}
