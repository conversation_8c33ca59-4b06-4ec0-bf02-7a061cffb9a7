package select_lane

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func Test_setRoutingToggle(t *testing.T) {
	type args struct {
		log  *routing_log.RoutingLog
		conf *rule.RoutingConfig
	}
	tests := []struct {
		name string
		args args
		want *routing_log.RoutingLog
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				log: &routing_log.RoutingLog{},
				conf: &rule.RoutingConfig{
					LocalSmartRoutingEnabled:   true,
					SmartRoutingEnabled:        true,
					SpxSmartRoutingEnabled:     true,
					IlhSmartRoutingEnabled:     true,
					CBMultiSmartRoutingEnabled: true,
				},
			},
			want: &routing_log.RoutingLog{
				RoutingToggle: 1 ^ (1 << 1) ^ (1 << 2) ^ (1 << 3) ^ (1 << 4),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			setRoutingToggle(tt.args.log, tt.args.conf)
			common.AssertResult(t, tt.args.log, tt.want, nil, nil)
		})
	}
}
