package select_lane

import (
	"context"
	"fmt"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/gogo/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"testing"
)

func TestSchemas(t *testing.T) {
	req := new(pb.SelectLaneReq)
	req.ProductInfo = &pb.ProductInfo{
		ProductId: proto.Uint32(0),
	}

	fmt.Println(validateSchema(context.Background(), req))

}

type Test123 struct {
	TestMap map[string]float64 `json:"test_map"`
}

func TestDebug1(t *testing.T) {
	str := "{\"test_map\":{\"123\":12.3, \"456\":45.6}}"
	var testMap Test123
	err := jsoniter.UnmarshalFromString(str, &testMap)
	fmt.Println(err)
}

func TestDebug2(t *testing.T) {
	var test1 int = 12
	str := strconv.Itoa(test1)
	fmt.Println(str)
}
