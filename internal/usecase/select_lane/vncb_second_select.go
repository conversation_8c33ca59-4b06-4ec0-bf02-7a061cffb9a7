package select_lane

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

func (s *SmartRoutingServiceImpl) vncbSecondSelect(ctx context.Context,
	candidateLaneList []*rule.RoutingLaneInfo, ruleMatchParam routing.RuleMatchParam,
	orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, matchedRule *rule.RoutingRuleParsed) (*rule.RoutingLaneInfo, *srerr.Error) {

	// 匹配二次调度的规则提前到第一步，在前面未匹配到不会报错，但如果走到这里则表示需要做二次调度，没有调度规则则需要报错
	if matchedRule == nil {
		return nil, srerr.New(srerr.RuleNotFound, nil, "vncb secondary scheduling|spx rule is nil")
	}

	logEntry.RuleId = int(matchedRule.ID)
	notOnlySPX := true
	laneList, err := s.RoutingService.Routing(ctx, int(ruleMatchParam.ProductId), ruleMatchParam.IsMultiProduct, routing.RewriteLaneInfo(candidateLaneList), matchedRule, orderData, logEntry, notOnlySPX)
	if err != nil {
		return nil, err
	}

	if len(laneList) >= 1 {
		return laneList[0], nil
	}

	//return nil, srerr.New(srerr.RoutingVnCbSpxPhaseErr, nil, "there are still lanes after second spx routing")
	return nil, srerr.New(srerr.RoutingVnCbSpxPhaseErr, nil, "no available lane after second spx routing")
}

// 外层执行到这里,len(routingLaneInfo) >= 1 ;
// add 开关，vncb 后上线，后续vncb 的迭代丢到新的分支。livetest 对比 ｜ test uat,
// VNCB 验收通过后，则可以打开开关。
// 时序 应该是,
//
//	1.selectLane 流量其他regions 先切换 	2.VNCB lps-api golive , VNCB-test pass ;  3.VN 开始切换，需要提前配置 Bypass 开关。
//
// @core
func (s *SmartRoutingServiceImpl) VnCbStage(ctx context.Context, rsp *pb.SelectLaneRsp,
	conf *rule.RoutingConfig,
	routingLaneInfo []*rule.RoutingLaneInfo, ruleMatchParam routing.RuleMatchParam,
	routingOrderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, selectLaneReq *pb.SelectLaneReq,
	matchedSPXRule *rule.RoutingRuleParsed) {
	// return-rsp && nil checked,
	logEntry.RequestId = selectLaneReq.GetHeader().GetRequestId() + "_vncb"
	logEntry.FOrderId = ruleMatchParam.Forderid
	logEntry.ExtraMsg = "vncb_not_matched"
	//	todo ,这里最好 加product-level 开关，否则 操作间隙 容易 出问题.

	if len(routingLaneInfo) == 1 {
		rsp.SelectedLane = generatePbRsp(routingLaneInfo[0])
		return
	}
	//todo, configutil.BypassVncb 拆出来,

	if configutil.BypassVncb(ctx) || !checkMatchVnCb(routingLaneInfo) {
		rsp.SelectedLane = generatePbRsp(routingLaneInfo[0])
		return
	}

	logEntry.ExtraMsg = "vncb_matched"
	if !conf.IsSpxSmartRouting() {
		sErr := srerr.New(srerr.RoutingCfgErr, nil, " plz check routing config")
		monitoring.ReportError(ctx, monitoring.VnCbRouting, monitoring.SelectLaneVnCbRoutingError, fmt.Sprintf("request id = %s plz check rouitng config %v", selectLaneReq.GetHeader().GetRequestId(), sErr))
		logger.CtxLogErrorf(ctx, "Routing| got multi lanes but SPX smart routing unable, plz check routing config")
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), sErr)
		return
	}

	vncbRoutingLane, vncbRoutingErr := s.vncbSecondSelect(ctx, routingLaneInfo, ruleMatchParam, routingOrderData, logEntry, matchedSPXRule)
	if vncbRoutingErr != nil {
		monitoring.ReportError(ctx, monitoring.VnCbRouting, monitoring.SelectLaneVnCbRoutingError, fmt.Sprintf("request id = %s vn cb routing error %v", selectLaneReq.GetHeader().GetRequestId(), vncbRoutingErr))
		rsp.Header = grpc_util.GenerateRespHeader(selectLaneReq.GetHeader().GetRequestId(), vncbRoutingErr)
		return
	}
	rsp.SelectedLane = generatePbRsp(vncbRoutingLane)

	//produce log data to kafka
	if !getDisableRoutingLog(ctx) && !configutil.BypassVncb(ctx) && selectLaneReq.GetCacheOrderType() != CacheBookingOrder {
		s.sendLogMessage(ctx, logEntry, routingLaneInfo, selectLaneReq)
	}

	monitoring.ReportSuccess(ctx, monitoring.VnCbRouting, monitoring.SelectLaneVnCbRoutingSuccess, fmt.Sprintf("requestId: %v, vn_cb routing success", selectLaneReq.GetHeader().GetRequestId()))
}

func checkMatchVnCb(routingLanes []*rule.RoutingLaneInfo) bool {
	//4.first check if C_LM is C_D_FM
	needSpxRouting := false
	skipLoop := false
	for _, lane := range routingLanes {
		if skipLoop {
			break
		}
		lines := lane.LineList
		for _, line := range lines {
			if line.ResourceSubType == lfslib.C_LM && line.RealResourceSubType == lfslib.C_DFM {
				needSpxRouting = true
				skipLoop = true
				break
			}
		}
	}
	return needSpxRouting
}
