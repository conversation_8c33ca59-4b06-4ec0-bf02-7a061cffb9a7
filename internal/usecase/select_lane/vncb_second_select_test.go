package select_lane

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestSmartRoutingServiceImpl_vncbSecondSelect(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	var patch *gomonkey.Patches
	type args struct {
		candidateLaneList []*rule.RoutingLaneInfo
		ruleMatchParam    routing.RuleMatchParam
		orderData         *rule.SmartRoutingOrderData
		logEntry          *routing_log.RoutingLog
		matchedRule       *rule.RoutingRuleParsed
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingLaneInfo
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case1: matchedRule == nil",
			wantErr: srerr.New(srerr.RuleNotFound, nil, "vncb secondary scheduling|spx rule is nil"),
			setup:   func() {},
		},
		{
			name: "case2: Routing return error",
			args: args{
				logEntry:    &routing_log.RoutingLog{},
				matchedRule: &rule.RoutingRuleParsed{}},
			wantErr: srerr.New(srerr.RuleNotFound, nil, "Routing error"),
			setup: func() {
				s = &SmartRoutingServiceImpl{
					RoutingService: &routing.RoutingServiceImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(s.RoutingService), "Routing", func(rs *routing.RoutingServiceImpl, ctx context.Context,
					productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed,
					orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error) {
					return nil, srerr.New(srerr.RuleNotFound, nil, "Routing error")
				})
			},
		},
		{
			name: "case3: len(laneList) >= 1",
			args: args{
				logEntry:    &routing_log.RoutingLog{},
				matchedRule: &rule.RoutingRuleParsed{}},
			want:    &rule.RoutingLaneInfo{},
			wantErr: nil,
			setup: func() {
				s = &SmartRoutingServiceImpl{
					RoutingService: &routing.RoutingServiceImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(s.RoutingService), "Routing", func(rs *routing.RoutingServiceImpl, ctx context.Context,
					productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed,
					orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error) {
					return []*rule.RoutingLaneInfo{{}}, nil
				})
			},
		},
		{
			name: "case4: len(laneList) >= 1",
			args: args{
				logEntry:    &routing_log.RoutingLog{},
				matchedRule: &rule.RoutingRuleParsed{}},
			want:    nil,
			wantErr: srerr.New(srerr.RoutingVnCbSpxPhaseErr, nil, "no available lane after second spx routing"),
			setup: func() {
				s = &SmartRoutingServiceImpl{
					RoutingService: &routing.RoutingServiceImpl{},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(s.RoutingService), "Routing", func(rs *routing.RoutingServiceImpl, ctx context.Context,
					productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed,
					orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error) {
					return []*rule.RoutingLaneInfo{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := s.vncbSecondSelect(ctx, tt.args.candidateLaneList, tt.args.ruleMatchParam, tt.args.orderData, tt.args.logEntry, tt.args.matchedRule)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func Test_checkMatchVnCb(t *testing.T) {
	type args struct {
		routingLanes []*rule.RoutingLaneInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case1: normal result",
			args: args{
				routingLanes: []*rule.RoutingLaneInfo{
					{
						LineList: []*rule.LineInfo{
							{
								ResourceSubType:     lfslib.C_LM,
								RealResourceSubType: lfslib.C_DFM,
							},
						},
					},
					{},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkMatchVnCb(tt.args.routingLanes); got != tt.want {
				t.Errorf("checkMatchVnCb() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSmartRoutingServiceImpl_VnCbStage(t *testing.T) {
	ctx := context.Background()
	s := &SmartRoutingServiceImpl{}
	var patch, patchRouting, patchSendRoutingLogToTask, patchWhiteList, patchDisableLog *gomonkey.Patches
	type args struct {
		rsp              *pb.SelectLaneRsp
		conf             *rule.RoutingConfig
		routingLaneInfo  []*rule.RoutingLaneInfo
		ruleMatchParam   routing.RuleMatchParam
		routingOrderData *rule.SmartRoutingOrderData
		logEntry         *routing_log.RoutingLog
		selectLaneReq    *pb.SelectLaneReq
		matchedSPXRule   *rule.RoutingRuleParsed
	}
	tests := []struct {
		name  string
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case1: len(routingLaneInfo) == 1",
			args: args{
				routingLaneInfo: []*rule.RoutingLaneInfo{{}},
				logEntry:        &routing_log.RoutingLog{},
				rsp:             &pb.SelectLaneRsp{},
			},
			setup: func() {},
		},
		{
			name: "case2: !checkMatchVnCb(routingLaneInfo)",
			args: args{
				routingLaneInfo: []*rule.RoutingLaneInfo{{}, {}},
				logEntry:        &routing_log.RoutingLog{},
				rsp:             &pb.SelectLaneRsp{},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(config.GetIntWithContext, func(ctx context.Context, key string, defaultValue int) int {
					return 1
				})
			},
		},
		{
			name: "case3: !conf.IsSpxSmartRouting()",
			args: args{
				routingLaneInfo: []*rule.RoutingLaneInfo{{},
					{
						LineList: []*rule.LineInfo{
							{
								ResourceSubType:     lfslib.C_LM,
								RealResourceSubType: lfslib.C_DFM,
							},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{},
				rsp:      &pb.SelectLaneRsp{},
				conf:     &rule.RoutingConfig{SpxSmartRoutingEnabled: false},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(config.GetIntWithContext, func(ctx context.Context, key string, defaultValue int) int {
					return 0
				})
				chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
			},
		},
		{
			name: "case4: vncbRoutingErr != nil",
			args: args{
				routingLaneInfo: []*rule.RoutingLaneInfo{{},
					{
						LineList: []*rule.LineInfo{
							{
								ResourceSubType:     lfslib.C_LM,
								RealResourceSubType: lfslib.C_DFM,
							},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{},
				rsp:      &pb.SelectLaneRsp{},
				conf:     &rule.RoutingConfig{SpxSmartRoutingEnabled: true},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(config.GetIntWithContext, func(ctx context.Context, key string, defaultValue int) int {
					return 0
				})
			},
		},
		{
			name: "case5: normal result",
			args: args{
				routingLaneInfo: []*rule.RoutingLaneInfo{{},
					{
						LineList: []*rule.LineInfo{
							{
								ResourceSubType:     lfslib.C_LM,
								RealResourceSubType: lfslib.C_DFM,
							},
						},
					},
				},
				logEntry:       &routing_log.RoutingLog{},
				rsp:            &pb.SelectLaneRsp{},
				conf:           &rule.RoutingConfig{SpxSmartRoutingEnabled: true},
				matchedSPXRule: &rule.RoutingRuleParsed{},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(config.GetIntWithContext, func(ctx context.Context, key string, defaultValue int) int {
					return 0
				})
				s = &SmartRoutingServiceImpl{
					RoutingService: &routing.RoutingServiceImpl{},
					routingLog:     &routing_log.RoutingLogServiceImpl{},
				}
				patchRouting = gomonkey.ApplyMethod(reflect.TypeOf(s.RoutingService), "Routing", func(rs *routing.RoutingServiceImpl, ctx context.Context,
					productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed,
					orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error) {
					return []*rule.RoutingLaneInfo{{}}, nil
				})
				patchSendRoutingLogToTask = gomonkey.ApplyMethod(reflect.TypeOf(s.routingLog), "SendRoutingLogToTask", func(r *routing_log.RoutingLogServiceImpl,
					ctx context.Context, orderData *pb.CreateOrderRequestData, routingLog *routing_log.RoutingLog) *srerr.Error {
					return nil
				})
				patchWhiteList = gomonkey.ApplyFunc(configutil.GetWriteList, func(ctx context.Context) configutil.RoutingLogWriteListConfig {
					return configutil.RoutingLogWriteListConfig{}
				})
				patchDisableLog = gomonkey.ApplyFunc(config.GetBoolWithContext, func(ctx context.Context) bool {
					return false
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			s.VnCbStage(ctx, tt.args.rsp, tt.args.conf, tt.args.routingLaneInfo, tt.args.ruleMatchParam, tt.args.routingOrderData, tt.args.logEntry, tt.args.selectLaneReq, tt.args.matchedSPXRule)
			if patch != nil {
				patch.Reset()
			}
			if patchRouting != nil {
				patchRouting.Reset()
			}
			if patchSendRoutingLogToTask != nil {
				patchSendRoutingLogToTask.Reset()
			}
			if patchWhiteList != nil {
				patchWhiteList.Reset()
			}
			if patchDisableLog != nil {
				patchDisableLog.Reset()
			}
		})
	}
}
