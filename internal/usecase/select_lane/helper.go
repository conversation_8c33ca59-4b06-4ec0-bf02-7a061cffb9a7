package select_lane

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"github.com/gogo/protobuf/proto"
	"strconv"
)

func convertOriginalOrderReq(in *pb.SelectLaneReq) *ordentity.OrderInfo {
	order := in.GetOrder().GetOrderInfoFromOms()
	orderInfo := &ordentity.OrderInfo{}

	shopId := common.AtoiByPoint(order.ForderInfo.ShopId)
	orderInfo.ShopId = &shopId

	checkoutItem := &ordentity.CheckoutItem{}
	checkoutItem.ShopId = shopId
	for i := 0; i < len(order.Skus); i++ {
		itemInfo := &ordentity.ItemInfo{}
		itemInfo.ItemId = uint64(order.Skus[i].GetItemId())
		//common.U64toU64ByPoint(proto.Uint64(uint64(*order.Skus[i].ItemId)))
		itemInfo.ModelId = uint64(order.Skus[i].GetModelId())
		itemInfo.Quantity = common.U32toIByPoint(order.Skus[i].Quantity)

		weight := common.F32toF64ByPoint(order.Skus[i].Weight)
		itemInfo.Weight = &weight

		length := common.F32toF64ByPoint(order.Skus[i].Length)
		itemInfo.Length = &length

		width := common.F32toF64ByPoint(order.Skus[i].Width)
		itemInfo.Width = &width

		height := common.F32toF64ByPoint(order.Skus[i].Height)
		itemInfo.Height = &height

		categoryId := common.U32toU64ByPoint(order.Skus[i].CategoryId)
		itemInfo.CategoryId = &categoryId

		checkoutItem.Items = append(checkoutItem.Items, itemInfo)
	}
	orderInfo.CheckoutItems = append(orderInfo.CheckoutItems, checkoutItem)

	orderInfo.PickupAddress = &ordentity.AddressInfo{
		Country:    order.PickupInfo.PickupCountry,
		State:      order.PickupInfo.PickupState,
		City:       order.PickupInfo.PickupCity,
		District:   order.PickupInfo.PickupDistrict,
		Street:     order.PickupInfo.PickupStreet,
		PostalCode: order.PickupInfo.PickupPostalCode,
		Longitude:  order.PickupInfo.PickupLongitude,
		Latitude:   order.PickupInfo.PickupLatitude,
		Phone:      order.PickupInfo.PickupPhone,
	}
	orderInfo.PickupAddress.AddrLine = str.Ptr(orderInfo.PickupAddress.CombineAddressLine(order.PickupInfo.PickupAddress))

	orderInfo.DeliveryAddress = &ordentity.AddressInfo{
		Country:    order.DeliverInfo.DeliverCountry,
		State:      order.DeliverInfo.DeliverState,
		City:       order.DeliverInfo.DeliverCity,
		District:   order.DeliverInfo.DeliverDistrict,
		Street:     order.DeliverInfo.DeliverStreet,
		PostalCode: order.DeliverInfo.DeliverPostalCode,
		Longitude:  order.DeliverInfo.DeliverLongitude,
		Latitude:   order.DeliverInfo.DeliverLatitude,
		Phone:      order.DeliverInfo.DeliverPhone,
	}
	orderInfo.DeliveryAddress.AddrLine = str.Ptr(orderInfo.DeliveryAddress.CombineAddressLine(order.DeliverInfo.DeliverAddress))

	orderInfo.CodAmount = order.ForderInfo.CodAmount

	totalPrice := common.F32toF64ByPoint(order.ForderInfo.TotalPrice)
	orderInfo.TotalPrice = &totalPrice

	cogs := common.F32toF64ByPoint(order.ForderInfo.Cogs)
	orderInfo.COGS = &cogs

	orderInfo.WhsId = common.Op3s(order.BaseInfo.WhsId == nil, "", *order.BaseInfo.WhsId)

	//isShopSbs := common.I32toIByPoint(order.ForderInfo.IsShopSbs)
	//orderInfo.FulfillmentType = &isShopSbs

	buyerPaidShippingFee := common.F32toF64ByPoint(order.ForderInfo.BuyerPaidShippingFee)
	orderInfo.BuyerPaidShippingFee = &buyerPaidShippingFee

	orderInfo.PaymentMethod = common.Op3s(order.ForderInfo.PaymentMethod == nil, "", *order.ForderInfo.PaymentMethod)

	orderInfo.IsReturn = common.Int32toBoolByPoint(order.BaseInfo.IsReturn)

	//orderInfo.TaxNumber = order.ForderInfo.TaxNumber

	//orderInfo.SellerTaxNumber = order.ForderInfo.SellerTaxNumber

	//orderInfo.ShippingTraceno = order.ForderInfo.ShippingTraceno
	orderInfo.PickupTime = int(order.BaseInfo.GetPickupTime())

	orderInfo.CreateOrderTime = int(order.BaseInfo.GetOrderCreateTime())

	if order.LaneInfo != nil && order.LaneInfo.DgFlag != nil {
		orderInfo.LaneDgFlag = int32(common.I32toIByPoint(order.LaneInfo.DgFlag))
	}

	return orderInfo

}

func (s *SmartRoutingServiceImpl) composeSmartRoutingOrderData(ctx context.Context, in *pb.SelectLaneReq, logEntry *routing_log.RoutingLog) *rule.SmartRoutingOrderData {
	var shipmentType, wmsFlag uint8
	var pickupLocationIDs, deliverLocationIDs []int
	isReturn := constant.IsForwardOrder
	// 重新定义个FetchOrder 方法 On
	orderInfo := convertOriginalOrderReq(in)
	if orderInfo.PickupTime > 0 {
		shipmentType = constant.PickupShipmentType
	} else {
		shipmentType = constant.DropOffShipmentType
	}
	if orderInfo.WhsId != "" {
		wmsFlag = 1
	}
	if orderInfo.IsReturn {
		isReturn = constant.IsReturnOrder
	}
	pickupLoc, err := s.AddrRepo.GetLocationByLocFullPathName(ctx, orderInfo.PickupAddress.GetCountry(), orderInfo.PickupAddress.GetState(), orderInfo.PickupAddress.GetCity(), orderInfo.PickupAddress.GetDistrict(), orderInfo.PickupAddress.GetStreet())
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get pickup location fail|PickupInfo=%s,err=%s", str.JsonString(orderInfo.PickupAddress), err.Error())
	}
	if pickupLoc != nil {
		pickupLocationIDs = pickupLoc.FormatLocationIdList()
	}
	deliverLoc, err := s.AddrRepo.GetLocationByLocFullPathName(ctx, orderInfo.DeliveryAddress.GetCountry(), orderInfo.DeliveryAddress.GetState(), orderInfo.DeliveryAddress.GetCity(), orderInfo.DeliveryAddress.GetDistrict(), orderInfo.DeliveryAddress.GetStreet())
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get deliver location fail|DeliverInfo=%s,err=%s", str.JsonString(orderInfo.DeliveryAddress), err.Error())
	}
	if deliverLoc != nil {
		deliverLocationIDs = deliverLoc.FormatLocationIdList()
		logEntry.ExtraInfo.BuyerCityId = deliverLoc.GetCityLocId()
		logEntry.ExtraInfo.BuyerStateId = deliverLoc.GetStateLocId()
	}
	items := make([]*rule.SmartRoutingOrderDataItem, 0)
	for _, item := range orderInfo.CheckoutItems {
		for _, s := range item.Items {
			items = append(items, &rule.SmartRoutingOrderDataItem{
				ItemID:     s.GetItemId(),
				ModelID:    s.GetModelId(),
				CategoryID: uint32(s.GetCategoryId()),
				Weight:     float32(s.GetWeight()),
				Height:     float32(s.GetHeight()),
				Length:     float32(s.GetLength()),
				Width:      float32(s.GetWidth()),
				Quantity:   uint32(s.GetQuantity()),
			})
		}
	}

	res := &rule.SmartRoutingOrderData{
		CodAmount:           orderInfo.GetCodAmount(),
		Cogs:                orderInfo.GetCogs(),
		CreateOrderTime:     uint32(orderInfo.CreateOrderTime),
		IsReturn:            uint8(isReturn),
		WmsFlag:             wmsFlag,
		DgFlag:              uint8(orderInfo.LaneDgFlag),
		ShipmentType:        shipmentType,
		PickupLocationIds:   pickupLocationIDs,
		PickupPostCode:      orderInfo.PickupAddress.GetPostalCode(),
		DeliveryLocationIds: deliverLocationIDs,
		DeliveryPostCode:    orderInfo.DeliveryAddress.GetPostalCode(),
		Items:               items,
		ActualPointMap:      convertActualPointmap(in),
		IsCod:               orderInfo.IsCodPayment(),
		IsWms:               in.GetOrder().GetIsWms(),
		OrderParcelDimension: rule.OrderParcelDimension{
			Length: in.GetOrder().GetParcelDimension().GetLength(),
			Width:  in.GetOrder().GetParcelDimension().GetWidth(),
			Height: in.GetOrder().GetParcelDimension().GetHeight(),
		},
	}

	if logEntry != nil {
		//此处记录以下SmartRoutingData信息，预测在Cheapest fee和max volume min volume中有用到该信息
		logEntry.ExtraInfo.SmartRoutingData = *res
	}

	return res
}

// shallow-copy
func convertActualPointmap(in *pb.SelectLaneReq) map[string][]*pb.ActualPoint {
	ret := make(map[string][]*pb.ActualPoint)
	src := in.GetActualPointMap()

	for lanecode, srcPointList := range src {
		//used,
		dstPointList := srcPointList.GetLaneActualPointList()
		ret[lanecode] = dstPointList
	}

	return ret
}

func formatRuleItemCategoryInfo(in *pb.SelectLaneReq) []*routing.ItemCategoryInfo {
	infos := make([]*routing.ItemCategoryInfo, 0, len(in.GetOrder().GetOrderInfoFromOms().GetSkus()))
	for _, sku := range in.GetOrder().GetOrderInfoFromOms().GetSkus() {
		infos = append(infos, &routing.ItemCategoryInfo{
			GlobalCategoryIdL1: int(sku.GetGlobalCategoryId_L1()),
			GlobalCategoryIdL2: int(sku.GetGlobalCategoryId_L2()),
			GlobalCategoryIdL3: int(sku.GetGlobalCategoryId_L3()),
			GlobalCategoryIdL4: int(sku.GetGlobalCategoryId_L4()),
			GlobalCategoryIdL5: int(sku.GetGlobalCategoryId_L5()),
		})
	}

	return infos
}

func formatPreFulfillmentCheckRuleItemCategoryInfo(in *pb.PreFulfillmentCheckReq) []*routing.ItemCategoryInfo {
	infos := make([]*routing.ItemCategoryInfo, 0, len(in.GetOrder().GetItemCategoryInfoList()))
	for _, item := range in.GetOrder().GetItemCategoryInfoList() {
		infos = append(infos, &routing.ItemCategoryInfo{
			GlobalCategoryIdL1: int(item.GetGlobalCategoryIdL1()),
			GlobalCategoryIdL2: int(item.GetGlobalCategoryIdL2()),
			GlobalCategoryIdL3: int(item.GetGlobalCategoryIdL3()),
			GlobalCategoryIdL4: int(item.GetGlobalCategoryIdL4()),
			GlobalCategoryIdL5: int(item.GetGlobalCategoryIdL5()),
		})
	}

	return infos
}

func generatePbRsp(src *rule.RoutingLaneInfo) *pb.RoutingLaneInfo {
	laneRsp := new(pb.RoutingLaneInfo)
	laneRsp.LaneCode = proto.String(src.LaneCode)
	laneRsp.LaneCodeGroup = src.LaneCodeGroup
	laneRsp.ServiceCode = proto.String(src.ServiceCode)
	laneRsp.DgGroupId = proto.String(src.DgGroupId)
	return laneRsp
}

func generateRuleMatchParam(in *pb.SelectLaneReq, routingType uint8, dgType int) routing.RuleMatchParam {
	param := routing.RuleMatchParam{}
	param.ProductId = int64(in.GetProductInfo().GetProductId())
	param.WhsID = in.GetOrder().GetOrderInfoFromOms().GetBaseInfo().GetWhsId()
	param.Cogs = in.GetOrder().GetOrderInfoFromOms().GetForderInfo().GetCogs()
	//ValidationWeight 需要改协议，从 checker 取的，下游计算得到。
	param.ValidationWeight = int(in.GetOrder().GetValidationWeight())
	param.DgType = dgType
	param.ParcelLength = in.GetOrder().GetParcelDimension().GetLength()
	param.ParcelWidth = in.GetOrder().GetParcelDimension().GetWidth()
	param.ParcelHeight = in.GetOrder().GetParcelDimension().GetHeight()
	param.ItemCategoryInfos = formatRuleItemCategoryInfo(in)
	param.RoutingType = routingType
	param.LocationIDList = in.GetOrder().GetDeliveryLocationIdList()
	param.IsMultiProduct = in.GetProductInfo().GetIsMultiProduct()
	param.Forderid = in.GetOrder().GetOrderInfoFromOms().GetBaseInfo().GetForderid()
	param.ZoneType = locationzone.RunningZoneType
	param.DeliverPostCode = in.GetOrder().GetOrderInfoFromOms().GetDeliverInfo().GetDeliverPostalCode()
	param.ShopId = in.GetOrder().GetOrderInfoFromOms().GetForderInfo().GetShopId()
	return param
}

// @core
func preFulfillmentCheckGenerateRuleMatchParam(in *pb.PreFulfillmentCheckReq, cfg *rule.RoutingConfig, dgType int) routing.RuleMatchParam {
	param := routing.RuleMatchParam{}
	param.ProductId = int64(in.GetProductInfo().GetProductId())
	param.WhsID = in.GetOrder().GetWhsId()
	param.Cogs = in.GetOrder().GetCogs()
	//ValidationWeight 需要改协议，从 checker 取的，下游计算得到。
	param.ValidationWeight = int(in.GetOrder().GetValidationWeight())
	param.DgType = dgType
	param.ParcelLength = in.GetOrder().GetParcelDimension().GetLength()
	param.ParcelWidth = in.GetOrder().GetParcelDimension().GetWidth()
	param.ParcelHeight = in.GetOrder().GetParcelDimension().GetHeight()
	param.ItemCategoryInfos = formatPreFulfillmentCheckRuleItemCategoryInfo(in)
	param.RoutingType = cfg.GetRoutingType()
	param.LocationIDList = in.GetOrder().GetDeliverLocationIdList()
	param.IsMultiProduct = in.GetProductInfo().GetIsMultiProduct()
	param.ZoneType = locationzone.RunningZoneType
	param.DeliverPostCode = in.GetOrder().GetPostalCode()
	param.ShopId = strconv.FormatInt(in.GetOrder().GetShopId(), 10)
	return param
}

// @core
func FilterLanesByDestPort(routingLaneInfos []*rule.RoutingLaneInfo, destPorts []string) []*rule.RoutingLaneInfo {
	if len(destPorts) == 0 {
		return routingLaneInfos
	}
	var ret []*rule.RoutingLaneInfo
	for _, routingLaneInfo := range routingLaneInfos {
		if objutil.ContainStr(destPorts, routingLaneInfo.DestinationPort) {
			ret = append(ret, routingLaneInfo)
		}
	}

	return ret
}
