package masking_result_panel

import (
	"context"
	"fmt"
	"sort"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/gogo/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
)

type KeyDataManager interface {
	GetKeyDataList(ctx context.Context, request masking_panel.GetKeyDataRequest) (*masking_panel.GetKeyDataResponse, *srerr.Error)
	ExportData(ctx context.Context, request masking_panel.GetKeyDataRequest) *srerr.Error
	GetExportTasks(ctx context.Context, request schema.GetExportTasksRequest) (schema.GetExportTasksResponse, *srerr.Error)
	GetAndUploadMaskingData(ctx context.Context, request masking_panel.GetKeyDataRequest, taskID uint64) *srerr.Error
	CreateExportTaskRecord(ctx context.Context, req masking_panel.CreateExportTaskReq) (uint64, *srerr.Error)
	UpdateExportTaskRecord(ctx context.Context, taskID uint64, updateData map[string]interface{}) *srerr.Error
}

type KeyDataImpl struct {
	dataApi        *dataclient.DataApi
	exportTaskRepo export_task.ExportTaskRepo
	prodRepo       product.ProdRepo
	convertorMap   *dataclient.MaskingConvertorMap
	lpsApi         lpsclient.LpsApi
}

func NewKeyDataImpl(dataApi *dataclient.DataApi,
	exportTaskRepo export_task.ExportTaskRepo,
	prodRepo product.ProdRepo,
	convertorMap *dataclient.MaskingConvertorMap,
	lpsApi lpsclient.LpsApi) *KeyDataImpl {
	return &KeyDataImpl{
		dataApi:        dataApi,
		exportTaskRepo: exportTaskRepo,
		prodRepo:       prodRepo,
		convertorMap:   convertorMap,
		lpsApi:         lpsApi,
	}
}

var exportMaskingDataExcelHeader = []string{"Time", "Masking or Fulfillment Product", "Orders"}

func (receiver *KeyDataImpl) GetKeyDataList(ctx context.Context, request masking_panel.GetKeyDataRequest) (*masking_panel.GetKeyDataResponse, *srerr.Error) {
	response := &masking_panel.GetKeyDataResponse{}
	//1.check time range
	timeRange := request.GetEndTime() - request.GetStartTime()
	if timeRange <= 0 {
		tErr := srerr.New(srerr.ParamErr, nil, "start time is bigger than end time")
		logger.CtxLogErrorf(ctx, "GetKeyDataList|request start time:%v, end time:%v, err:%v", request.GetStartTime(), request.GetEndTime(), tErr)
		return nil, tErr
	}
	var (
		dataResp        dataclient.GetPanelFromDataResp
		timeGranularity string
		gErr            *srerr.Error
	)

	// 当请求带有Mask Channel时，需要将Mask Channel下的F Channel同时查出
	// Map作缓存用，函数内需要使用到多次
	maskProductDetailMap := make(map[int64]*lpsclient.ProductDetailInfo)
	for _, maskProductId := range request.MaskProductIDs {
		detail, err := receiver.lpsApi.GetProductDetail(ctx, int(maskProductId))
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetProductDetail fail | productId = %v, err = %v", maskProductId, err)
			return nil, srerr.Wrap(err, maskProductId, srerr.ProductInfoNotFound)
		}
		for _, componentProductId := range detail.GetComponentProduct().ComponentProducts {
			request.ProductIDs = append(request.ProductIDs, int64(componentProductId))
		}
		maskProductDetailMap[maskProductId] = detail
	}
	// 填充完F Channel后可能会与原本请求中的F Channel列表有重复的，需要做去重
	request.ProductIDs = objutil.RemoveDuplicateInt64(request.ProductIDs)

	//3.call data's api to get response
	//fill request and get time granularity
	dataRequest, tempGranularity := fillAndConvertTime(request)
	timeGranularity = tempGranularity
	dataResp, gErr = receiver.getKeyDataFromDataTeam(ctx, dataRequest, timeGranularity)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetKeyDataList, get data fail, err:%v", gErr)
		return nil, gErr
	}
	if len(dataResp.Data.List) == 0 {
		logger.CtxLogInfof(ctx, "convertToMaskingKeyDataList| aggregated info list is empty, will return empty list")
		aggregatedKeyDataList := make([]masking_panel.AggregatedKeyData, 0)
		response.List = aggregatedKeyDataList
		return response, nil
	}

	//4.get product id and name map
	productIDNameMap := make(map[string]string, 0)
	productBaseInfoList := receiver.prodRepo.GetProductBaseInfoList(ctx)
	if len(productBaseInfoList) == 0 {
		logger.CtxLogErrorf(ctx, "convertToMaskingKeyDataList|get empty product base info list")
	} else {
		for productID, productInterface := range productBaseInfoList {
			//convert product base info
			productBaseInfo, ok := productInterface.(*lpsclient.LogisticProductTab)
			if !ok {
				logger.CtxLogErrorf(ctx, "product info:%+v, convert product base info err", productInterface)
			} else {
				//set product id and seller display name
				productIDNameMap[productID] = productBaseInfo.SellerDisplayName
			}
		}
	}

	// 请求参数为空的情况，需要从data的response中获取product列表
	if len(maskProductDetailMap) == 0 {
		for _, info := range dataResp.Data.List {
			productId := int(info.ProductID)
			detail, err := receiver.lpsApi.GetProductDetail(ctx, productId)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetProductDetail fail | productId = %v, err = %v", productId, err)
				return nil, srerr.Wrap(err, productId, srerr.ProductInfoNotFound)
			}
			if detail.IsMaskingProduct {
				maskProductDetailMap[int64(productId)] = detail
			}
		}
	}

	//5.get convertor by timeGranularity, and then convert data list
	convertor := receiver.convertorMap.ConvertorMap[timeGranularity]
	response.List = convertor.ConvertToMaskingKeyDataList(ctx, dataResp.Data.List, productIDNameMap, maskProductDetailMap)

	return response, nil
}

func (receiver *KeyDataImpl) ExportData(ctx context.Context, request masking_panel.GetKeyDataRequest) *srerr.Error {
	// 1.marshal request into msgData
	operator, _ := apiutil.GetUserInfo(ctx)
	//create task record
	createReq := masking_panel.CreateExportTaskReq{
		TaskStatus:        enum.Created,
		TaskType:          enum.Download,
		TaskBusinessScene: enum.MaskingPanel,
		LastOperateTime:   timeutil.GetCurrentTime(ctx).Unix(),
		LastOperator:      operator,
	}
	taskID, cErr := receiver.CreateExportTaskRecord(ctx, createReq)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "ExportData| create task record err:%v", cErr)
		return cErr
	}

	request.TaskID = taskID
	msgData, mErr := jsoniter.Marshal(&request)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ExportData| marshal err:%v", mErr)
		return srerr.With(srerr.JsonErr, nil, mErr)
	}
	logger.CtxLogDebugf(ctx, "ExportData| data after marshal:%v", string(msgData))
	// 2.send msg to saturn
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameExportMaskingData, msgData, nil, ""); err != nil {
		return srerr.With(srerr.SendAsyncJobMsgFail, nil, err)
	}
	return nil
}

func (receiver *KeyDataImpl) GetExportTasks(ctx context.Context, request schema.GetExportTasksRequest) (schema.GetExportTasksResponse, *srerr.Error) {
	response := schema.GetExportTasksResponse{
		PageNo:   request.PageNo,
		PageSize: request.PageSize,
		List:     make([]schema.ExportTask, 0),
	}
	// 此处增加masking volume和routing volume两个场景，默认返回masking panel业务记录
	businessScene := enum.MaskingPanel
	if request.BusinessScene != "" {
		businessScene = request.BusinessScene
	}
	exportTaskList, gErr := receiver.exportTaskRepo.GetExportTaskList(ctx, request, businessScene)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetExportTasks| Get export tasks err:%v", gErr)
		return response, gErr
	}
	//get empty list
	if len(exportTaskList) == 0 {
		logger.CtxLogInfof(ctx, "GetExportTasks| Get empty export task list")
		return response, nil
	}
	//fill total into response
	total, cErr := receiver.exportTaskRepo.CountExportTask(ctx, businessScene)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "GetExportTasks| Count export tasks err:%v", cErr)
		return response, gErr
	}
	response.Total = total
	//fill export task list into response
	var exportTaskEntities []schema.ExportTask
	for _, exportTaskPersistent := range exportTaskList {
		tempExportTaskEntity := schema.ExportTask{
			TaskID:        exportTaskPersistent.ID,
			TaskStatus:    exportTaskPersistent.TaskStatus,
			Operator:      exportTaskPersistent.LastOperator,
			OperationTime: exportTaskPersistent.LastOperateTime,
			ErrorMessage:  exportTaskPersistent.ErrorMessage,
			S3Url:         exportTaskPersistent.DownloadUrl,
		}
		exportTaskEntities = append(exportTaskEntities, tempExportTaskEntity)
	}
	response.List = exportTaskEntities

	return response, nil
}

func (receiver *KeyDataImpl) CreateExportTaskRecord(ctx context.Context, req masking_panel.CreateExportTaskReq) (uint64, *srerr.Error) {
	taskID, cErr := receiver.exportTaskRepo.CreateTaskRecord(ctx, req)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateExportTaskRecord| create task record err:%v", cErr)
		return 0, cErr
	}
	return taskID, nil
}

func (receiver *KeyDataImpl) GetAndUploadMaskingData(ctx context.Context, request masking_panel.GetKeyDataRequest, taskID uint64) *srerr.Error {
	updateData := map[string]interface{}{
		"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
		"task_status": int(enum.Doing),
	}
	//update record to 'doing'
	if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'doing', err:%v", uErr)
		return uErr
	}
	//check time range
	timeRange := request.GetEndTime() - request.GetStartTime()
	if timeRange < 0 {
		tErr := srerr.New(srerr.ParamErr, nil, "start time is bigger or equal than end time")
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|request start time:%v, end time:%v, err:%v", request.GetStartTime(), request.GetEndTime(), tErr)
		updateData["error_message"] = tErr.Error()
		updateData["task_status"] = int(enum.Failed)
		if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
			logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'failed' after judging time range, err:%v", uErr)
			return uErr
		}
		return tErr
	}
	//call data's api to get response
	request, timeGranularity := fillAndConvertTime(request)
	reqForData := dataclient.GetPanelFromDataRequest{
		StartTime:             request.StartTime,
		EndTime:               request.EndTime,
		MaskProductIDs:        request.MaskProductIDs,
		FulfillmentProductIDs: request.ProductIDs,
		ShopGroupID:           request.ShopGroupID,
		SoftCriteriaRuleID:    request.SoftCriteriaRuleID,
		VolumeRuleID:          request.VolumeRuleID,
		ZoneCode:              request.ZoneCode,
		RouteCode:             request.RouteCode,
		TimeGranularity:       proto.String(timeGranularity),
		Country:               proto.String(envvar.GetCIDLower()),
		AllocationStatus:      proto.Bool(true), //now will only select data of allocation_status==true
		AggregateKey:          request.GetAggregatedKeyList(),
		Region:                envvar.GetCIDLower(),
	}

	resp, gErr := receiver.dataApi.GetMaskingResultListV2(ctx, reqForData, request.RequestID)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|req:%v, call data's api err:%v", reqForData, gErr)
		updateData["error_message"] = objutil.Merge("call data's api err:", gErr.Error())
		updateData["task_status"] = int(enum.Failed)
		if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
			logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'failed' after calling data's api, err:%v", uErr)
			return uErr
		}
		return gErr
	}

	//convert response list to excel
	tabs := convertAggregateInfosToExcel(ctx, resp)
	if len(tabs) > constant.MaxExcelRows {
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData| rows of excel exceed 1,000,000")
		return srerr.New(srerr.MaskingPanelExportFail, nil, "rows of excel exceed 1,000,000")
	}
	sheetName := ""
	f, eErr := fileutil.MakeExcel(ctx, exportMaskingDataExcelHeader, tabs, sheetName)
	if eErr != nil {
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData| make excel err:%v", eErr)
		updateData["error_message"] = objutil.Merge("make excel err:", eErr.Error())
		updateData["task_status"] = int(enum.Failed)
		if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
			logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'failed' after making excel, err:%v", uErr)
			return uErr
		}
		return srerr.With(srerr.MaskingPanelExportFail, nil, eErr)
	}

	//upload excel to s3
	b, wErr := f.WriteToBuffer()
	if wErr != nil {
		return srerr.With(srerr.MaskingPanelExportFail, nil, wErr)
	}
	exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
	s3Key := fmt.Sprintf("Masking allocation result:%v.xlsx", exportTime)
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		updateData["error_message"] = objutil.Merge("upload s3 err:", err.Error())
		updateData["task_status"] = int(enum.Failed)
		if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
			logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'failed' after uploading to s3, err:%v", uErr)
			return uErr
		}
		return srerr.With(srerr.S3UploadFail, nil, err)
	}

	//update task record to success
	s3Url := fileutil.GetS3Url(ctx, bucket, s3Key)
	updateData["download_url"] = s3Url
	updateData["task_status"] = int(enum.Success)
	if uErr := receiver.UpdateExportTaskRecord(ctx, taskID, updateData); uErr != nil {
		logger.CtxLogErrorf(ctx, "GetAndUploadMaskingData|fail to update task to 'success', err:%v", uErr)
		return uErr
	}

	return nil
}

func (receiver *KeyDataImpl) UpdateExportTaskRecord(ctx context.Context, taskID uint64, updateData map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, export_task.ExportTaskTabHook, map[string]interface{}{"id = ?": taskID}, updateData, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.UpdateTaskRecordFail, nil, err)
	}
	return nil
}

// getKeyDataFromDataTeam :if time ranges within 2 days, will call data's api by minute dimension; else will call api by day dimension
func (receiver *KeyDataImpl) getKeyDataFromDataTeam(ctx context.Context, request masking_panel.GetKeyDataRequest, timeGranularity string) (dataclient.GetPanelFromDataResp, *srerr.Error) {
	var (
		response dataclient.GetPanelFromDataResp
		resp     dataclient.ResultPanel
		cErr     *srerr.Error
	)
	reqForData := dataclient.GetPanelFromDataRequest{
		StartTime:             request.StartTime,
		EndTime:               request.EndTime,
		MaskProductIDs:        request.MaskProductIDs,
		FulfillmentProductIDs: request.ProductIDs,
		ShopGroupID:           request.ShopGroupID,
		SoftCriteriaRuleID:    request.SoftCriteriaRuleID,
		VolumeRuleID:          request.VolumeRuleID,
		ZoneCode:              request.ZoneCode,
		RouteCode:             request.RouteCode,
		TimeGranularity:       proto.String(timeGranularity),
		Country:               proto.String(envvar.GetCIDLower()),
		AllocationStatus:      proto.Bool(true), //now will only select data of allocation_status==true
		AggregateKey:          request.GetAggregatedKeyList(),
		Region:                envvar.GetCIDLower(),
	}
	requestID := request.RequestID
	//根据开关决定是否使用v2接口
	resp, cErr = receiver.dataApi.GetMaskingResultListV2(ctx, reqForData, requestID)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "getKeyDataFromDataTeam| call data's api err:%v", cErr)
		return response, cErr
	}

	response.Data.List = resp.List

	return response, nil
}

// 先生成二维数组再排序
func convertAggregateInfosToExcel(ctx context.Context, dataResp dataclient.ResultPanel) [][]string {
	var rows [][]string
	aggregateInfoList := dataResp.List
	for _, aggregateInfo := range aggregateInfoList {
		productID := strconv.FormatUint(aggregateInfo.ProductID, 10)
		for _, aggregateData := range aggregateInfo.AggregateDataList {
			dayTime, hourTime := timeutil.SplitDateToDayAndHour(ctx, aggregateData.Datetime)
			time := dayTime
			//daily,
			if dataResp.TimeGranularity == client.HourlyGranularity {
				time = objutil.Merge(time, fmt.Sprintf(" 00:00~%v:59", hourTime))
			}
			orderCount := strconv.Itoa(aggregateData.OrderCount)
			rows = append(rows, []string{
				time,
				productID,
				orderCount,
			})
		}
	}
	//first order by product id, second by time
	sort.Slice(rows, func(i, j int) bool {
		if rows[i][1] < rows[j][1] {
			return true
		} else if (rows[i][1] == rows[j][1]) && (rows[i][0] > rows[j][0]) {
			return true
		}
		return false
	})
	return rows
}

// fillAndConvertTime :will convert time from second to millisecond, and get time granularity
func fillAndConvertTime(request masking_panel.GetKeyDataRequest) (masking_panel.GetKeyDataRequest, string) {
	startTime := request.GetStartTime()
	endTime := request.GetEndTime()
	timeRange := endTime - startTime
	//1.set time granularity by judging whether time ranges within 2 days
	timeGranularity := client.DailyGranularity
	if timeRange <= timeutil.OneDayUnix*2 { //time ranges within 2 days
		timeGranularity = client.HourlyGranularity
	}

	//2.convert start and end time from second to millisecond
	request.StartTime = proto.Int64(startTime * 1000)
	request.EndTime = proto.Int64(endTime * 1000)

	return request, timeGranularity
}
