package masking_result_panel

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"github.com/google/wire"
)

var KeyDataMgrProviderSet = wire.NewSet(
	NewKeyDataImpl,
	wire.Bind(new(KeyDataManager), new(*KeyDataImpl)),
	dataclient.DataClientProviderSet,
	export_task.ExportTaskProviderSet,
	dataclient.NewMaskingConvertorMap,
)
