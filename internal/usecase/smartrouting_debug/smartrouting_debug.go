package smartrouting_debug

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
)

type SmartRoutingDebugInterface interface {
	RefreshLocalCacheImpl(ctx context.Context, req *pb.RefreshLocalCacheReq) (*pb.RefreshLocalCacheResp, error)
}

type SmartRoutingDebug struct {
}

func NewSmartRoutingDebug() *SmartRoutingDebug {
	return &SmartRoutingDebug{}
}

func (s *SmartRoutingDebug) RefreshLocalCacheImpl(ctx context.Context, req *pb.RefreshLocalCacheReq) (*pb.RefreshLocalCacheResp, error) {
	resp := &pb.RefreshLocalCacheResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	// live环境不执行刷新缓存
	if envvar.GetEnv() == enum.LIVE {
		return resp, nil
	}
	if req.GetAll() {
		err := localcache.ReloadAllCache()
		if err != nil {
			resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.LocalCacheErr, nil, "refresh local cache error:%v", err))
		}
		return resp, nil
	}
	if req.GetNamespaces() != nil {
		for _, namespace := range req.GetNamespaces() {
			err := localcache.ReloadCache(namespace)
			if err != nil {
				resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.LocalCacheErr, nil, "refresh local cache error:%v", err))
			}
			return resp, nil
		}
	}
	return nil, nil
}
