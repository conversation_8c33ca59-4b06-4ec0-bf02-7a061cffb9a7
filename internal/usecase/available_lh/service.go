package available_lh

import (
	"context"
	"sort"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"

	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

// AvailableLHService 定义Available LH Service接口
type AvailableLHService interface {
	// CreateAvailableLH 创建可用LH配置
	CreateAvailableLH(ctx context.Context, req *ilh_smart_routing.CreateAvailableLHConfigReq) (int, *srerr.Error)

	// UpdateAvailableLH 更新可用LH配置
	UpdateAvailableLH(ctx context.Context, req *ilh_smart_routing.UpdateAvailableLHConfigReq) *srerr.Error

	// GetAvailableLH 获取指定ID的可用LH配置
	GetAvailableLH(ctx context.Context, req *ilh_smart_routing.GetAvailableLHConfigReq) (*ilh_smart_routing.GetAvailableLHConfigResp, *srerr.Error)

	// ListAvailableLH 分页查询可用LH配置列表
	ListAvailableLH(ctx context.Context, req *ilh_smart_routing.ListAvailableLHConfigReq) (*ilh_smart_routing.ListAvailableLHConfigResp, *srerr.Error)

	// DeleteAvailableLH 删除可用LH配置
	DeleteAvailableLH(ctx context.Context, req *ilh_smart_routing.DeleteAvailableLHConfigReq) *srerr.Error

	// CopyAvailableLH 复制可用LH配置
	CopyAvailableLH(ctx context.Context, req *ilh_smart_routing.CopyAvailableLHConfigReq) (int, *srerr.Error)

	// CheckActiveAvailableLH 检查是否存在激活状态的可用LH配置
	CheckActiveAvailableLH(ctx context.Context, req *ilh_smart_routing.CheckAvailableLHConfigReq) (*ilh_smart_routing.CheckAvailableLHConfigResp, *srerr.Error)

	// UpdateAvailableLHStatus 更新可用LH配置的状态
	UpdateAvailableLHStatus(ctx context.Context, req *ilh_smart_routing.UpdateAvailableLHStatusReq) *srerr.Error

	// GetAvailableLHRule 获取指定ProductID的缓存配置
	GetAvailableLHRule(ctx context.Context, multiProductID int, dgType int, twsCode string, destinationPorts []string) (entity.AvailableLHRule, *srerr.Error)

	// UpdatePendingAvailableLHStatusActive 更新Pending状态的AvailableLH为Active
	UpdatePendingAvailableLHStatusActive(ctx context.Context)

	// UpdateActiveAvailableLHStatusExpired 更新Active状态的AvailableLH为Expired
	UpdateActiveAvailableLHStatusExpired(ctx context.Context)
}

type AvailableLHServiceImpl struct {
	AvailableLhRepo repo.AvailableLHRepo
}

func NewAvailableLHServiceImpl(availableLhRepo repo.AvailableLHRepo) *AvailableLHServiceImpl {
	return &AvailableLHServiceImpl{
		AvailableLhRepo: availableLhRepo,
	}
}

func (a *AvailableLHServiceImpl) CreateAvailableLH(ctx context.Context, req *ilh_smart_routing.CreateAvailableLHConfigReq) (int, *srerr.Error) {
	// 将请求转换为领域模型
	availableLH := &entity.AvailableLH{
		MultiProductID: req.MultiProductID,
		RuleStatus:     req.RuleStatus,
		Rules:          req.Rules,
		Operator:       req.Operator,
	}
	if req.RuleStatus == rule.RuleStatusActive {
		availableLH.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	if req.RuleStatus == rule.RuleStatusDraft {
		// 只有Active/Expired状态
		req.RuleStatus = rule.RuleStatusExpired
	}

	return a.AvailableLhRepo.CreateAvailableLH(ctx, availableLH)
}

func (a *AvailableLHServiceImpl) UpdateAvailableLH(ctx context.Context, req *ilh_smart_routing.UpdateAvailableLHConfigReq) *srerr.Error {
	// 将请求转换为领域模型
	availableLH := &entity.AvailableLH{
		ID:             req.ID,
		MultiProductID: req.MultiProductID,
		RuleStatus:     req.RuleStatus,
		Rules:          req.Rules,
		Operator:       req.Operator,
	}
	if req.RuleStatus == rule.RuleStatusActive {
		availableLH.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	if req.RuleStatus == rule.RuleStatusDraft {
		// 只有Active/Expired状态
		req.RuleStatus = rule.RuleStatusExpired
	}

	return a.AvailableLhRepo.UpdateAvailableLH(ctx, availableLH)
}

func (a *AvailableLHServiceImpl) GetAvailableLH(ctx context.Context, req *ilh_smart_routing.GetAvailableLHConfigReq) (*ilh_smart_routing.GetAvailableLHConfigResp, *srerr.Error) {
	// 调用仓库层获取记录
	availableLH, err := a.AvailableLhRepo.GetAvailableLH(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 将领域模型转换为响应
	resp := &ilh_smart_routing.GetAvailableLHConfigResp{
		ID:             availableLH.ID,
		MultiProductID: availableLH.MultiProductID,
		RuleStatus:     availableLH.RuleStatus,
		Rules:          availableLH.Rules,
	}

	return resp, nil
}

func (a *AvailableLHServiceImpl) ListAvailableLH(ctx context.Context, req *ilh_smart_routing.ListAvailableLHConfigReq) (*ilh_smart_routing.ListAvailableLHConfigResp, *srerr.Error) {
	// 获取列表数据
	availableLHs, total, err := a.AvailableLhRepo.ListAvailableLH(ctx, req.MultiProductID, req.RuleStatus, req.Pageno, req.Limit)
	if err != nil {
		return nil, err
	}

	// 构建响应列表
	respItems := make([]ilh_smart_routing.ListAvailableLHConfigRespItem, 0, len(availableLHs))
	for _, availableLH := range availableLHs {
		// 获取默认规则的可用线路信息
		defaultRuleAvailableLH, defaultRuleAvailableCC := extractDefaultRuleLines(availableLH.Rules)

		respItems = append(respItems, ilh_smart_routing.ListAvailableLHConfigRespItem{
			ID:                     availableLH.ID,
			MultiProductID:         availableLH.MultiProductID,
			MultiRules:             len(availableLH.Rules) > 1,
			RuleStatus:             rule.RuleStatus(availableLH.RuleStatus),
			EffectiveStartTime:     availableLH.EffectiveStartTime,
			DefaultRuleAvailableLH: defaultRuleAvailableLH,
			DefaultRuleAvailableCC: defaultRuleAvailableCC,
		})
	}

	return &ilh_smart_routing.ListAvailableLHConfigResp{
		Count:  len(respItems),
		Total:  total,
		Pageno: req.Pageno,
		List:   respItems,
	}, nil
}

// extractDefaultRuleLines 提取默认规则中的可用线路信息
func extractDefaultRuleLines(rules []entity.AvailableLHRule) ([]rule.BaseLineInfo, []rule.BaseLineInfo) {
	var defaultRuleAvailableLH, defaultRuleAvailableCC []rule.BaseLineInfo

	if len(rules) == 0 {
		return defaultRuleAvailableLH, defaultRuleAvailableCC
	}

	// 查找优先级为1000的默认规则
	var (
		foundDefaultRule bool
		defaultRule      entity.AvailableLHRule
	)

	for _, r := range rules {
		if r.Priority == 1000 {
			defaultRule = r
			foundDefaultRule = true
			break
		}
	}

	if !foundDefaultRule {
		return defaultRuleAvailableLH, defaultRuleAvailableCC
	}

	// 提取默认规则中的线路信息
	for _, availableLine := range defaultRule.AvailableLine {
		lineInfos := make([]rule.BaseLineInfo, 0, len(availableLine.LineList))
		for _, l := range availableLine.LineList {
			lineInfos = append(lineInfos, rule.BaseLineInfo{
				LineId:   l.LineId,
				LineName: l.LineName,
			})
		}

		if objutil.ContainInt(lfslib.ILHLine, availableLine.LineSubType) {
			defaultRuleAvailableLH = append(defaultRuleAvailableLH, lineInfos...)
		}
		if objutil.ContainInt(lfslib.CCLine, availableLine.LineSubType) {
			defaultRuleAvailableCC = append(defaultRuleAvailableCC, lineInfos...)
		}
	}

	return defaultRuleAvailableLH, defaultRuleAvailableCC
}

func (a *AvailableLHServiceImpl) GetAvailableLHRule(
	ctx context.Context, multiProductID int, dgType int, twsCode string, destinationPorts []string,
) (entity.AvailableLHRule, *srerr.Error) {
	cacheVal, err := localcache.Get(ctx, constant.AvailableLHConfig, strconv.Itoa(multiProductID))
	if err != nil {
		return entity.AvailableLHRule{}, srerr.With(srerr.LocalCacheErr, multiProductID, err)
	}

	availableLHConfig, ok := cacheVal.(*entity.AvailableLH)
	if !ok {
		return entity.AvailableLHRule{}, srerr.New(srerr.LocalCacheErr, multiProductID, "convert cache val to available lh failed")
	}

	// Find applicable rule
	var availableLHRule entity.AvailableLHRule
	for _, r := range availableLHConfig.Rules {
		// Skip if DG type doesn't match
		if r.DGType != rule.UndefinedDGFlag && r.DGType != rule.DGFlag(dgType) {
			continue
		}

		// Skip if TWS doesn't match
		if len(r.TWS) != 0 && !objutil.ContainStr(r.TWS, twsCode) {
			continue
		}

		// Skip if destination port doesn't match
		if len(r.DestinationPort) != 0 && !objutil.HaveIntersection(r.DestinationPort, destinationPorts) {
			continue
		}

		availableLHRule = r
		break
	}

	logger.CtxLogInfof(ctx, "Get Available LH Rule success, RuleName=%s, Priority=%d, MultiProductID=%d, DGType=%d, TWSCode=%s, DestinationPorts=%v",
		availableLHRule.RuleName, availableLHRule.Priority, multiProductID, dgType, twsCode, destinationPorts)

	return availableLHRule, nil
}

// DeleteAvailableLH 删除可用线路配置
func (a *AvailableLHServiceImpl) DeleteAvailableLH(ctx context.Context, req *ilh_smart_routing.DeleteAvailableLHConfigReq) *srerr.Error {
	// 调用仓库层删除记录
	return a.AvailableLhRepo.DeleteAvailableLH(ctx, req.ID)
}

func (a *AvailableLHServiceImpl) CopyAvailableLH(ctx context.Context, req *ilh_smart_routing.CopyAvailableLHConfigReq) (int, *srerr.Error) {
	// 获取源配置记录
	sourceAvailableLH, err := a.AvailableLhRepo.GetAvailableLH(ctx, req.ID)
	if err != nil {
		return 0, err
	}

	// 创建新的配置记录，使用源配置的产品ID
	newAvailableLH := &entity.AvailableLH{
		MultiProductID: sourceAvailableLH.MultiProductID,
		RuleStatus:     rule.RuleStatusExpired,
		Rules:          sourceAvailableLH.Rules, // 复制规则
		Operator:       req.Operator,
	}

	// 调用仓库层创建新记录
	return a.AvailableLhRepo.CreateAvailableLH(ctx, newAvailableLH)
}

// CheckActiveAvailableLH 检查是否存在相同MultiProductID且状态为Active的配置
func (a *AvailableLHServiceImpl) CheckActiveAvailableLH(ctx context.Context, req *ilh_smart_routing.CheckAvailableLHConfigReq) (*ilh_smart_routing.CheckAvailableLHConfigResp, *srerr.Error) {
	// 设置查询条件，只查询Active状态的配置
	activeStatus := rule.RuleStatusActive

	// 调用仓库层查询
	availableLHs, _, err := a.AvailableLhRepo.ListAvailableLH(ctx, req.MultiProductID, &activeStatus, 1, 1)
	if err != nil {
		return nil, err
	}

	// 构建响应，只返回是否存在
	resp := &ilh_smart_routing.CheckAvailableLHConfigResp{
		Exists: len(availableLHs) > 0,
	}

	return resp, nil
}

// UpdateAvailableLHStatus 仅更新可用线路配置的状态
func (a *AvailableLHServiceImpl) UpdateAvailableLHStatus(ctx context.Context, req *ilh_smart_routing.UpdateAvailableLHStatusReq) *srerr.Error {
	// 首先获取现有配置
	availableLH, err := a.AvailableLhRepo.GetAvailableLH(ctx, req.ID)
	if err != nil {
		return err
	}

	// 仅更新状态字段
	availableLH.RuleStatus = req.Status
	availableLH.Operator = req.Operator
	if req.Status == rule.RuleStatusActive {
		availableLH.EffectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	if err := a.AvailableLhRepo.UpdateAvailableLHStatus(ctx, availableLH); err != nil {
		logger.CtxLogErrorf(ctx, "Update available LH status failed, err=%v", err)
		return err
	}

	// 主动触发一次轮转逻辑
	a.UpdateActiveAvailableLHStatusExpired(ctx)

	return nil
}

// UpdatePendingAvailableLHStatusActive 更新Pending状态的AvailableLH为Active
func (a *AvailableLHServiceImpl) UpdatePendingAvailableLHStatusActive(ctx context.Context) {
	// 获取所有Queuing状态的配置
	configs, err := a.AvailableLhRepo.GetAvailableLHsByStatus(ctx, rule.RuleStatusQueuing)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to query Queuing Available LH records: %v", err)
		return
	}

	// 当前时间戳
	currentTime := timeutil.GetCurrentUnixTimeStamp(ctx)

	// 遍历所有Queuing状态的记录，检查是否需要激活
	for _, config := range configs {
		// 检查是否已到生效时间
		if config.EffectiveStartTime > 0 && config.EffectiveStartTime <= currentTime {
			// 更新状态为Active
			config.RuleStatus = rule.RuleStatusActive
			if err := a.AvailableLhRepo.UpdateAvailableLHStatus(ctx, config); err != nil {
				logger.CtxLogErrorf(ctx, "Failed to update AvailableLH status to Active: id=%d, error=%v", config.ID, err)
				continue
			}

			logger.CtxLogInfof(ctx, "Successfully updated AvailableLH status from Queuing to Active: id=%d", config.ID)
		}
	}
}

// UpdateActiveAvailableLHStatusExpired 更新Active状态的AvailableLH为Expired
func (a *AvailableLHServiceImpl) UpdateActiveAvailableLHStatusExpired(ctx context.Context) {
	// 获取所有Active状态的配置
	configs, err := a.AvailableLhRepo.GetAvailableLHsByStatus(ctx, rule.RuleStatusActive)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Failed to query Active Available LH records: %v", err)
		return
	}

	// 按唯一键(MultiProductID)分组
	groupedConfigs := make(map[int][]*entity.AvailableLH)

	for _, config := range configs {
		// 以MultiProductID为键进行分组
		key := config.MultiProductID
		groupedConfigs[key] = append(groupedConfigs[key], config)
	}

	// 对每组配置，只保留EffectiveStartTime最新的为Active
	for multiProductID, configGroup := range groupedConfigs {
		// 如果只有一个配置，无需处理
		if len(configGroup) <= 1 {
			continue
		}

		// 按EffectiveStartTime排序，最新的在前面
		sort.Slice(configGroup, func(i, j int) bool {
			return configGroup[i].EffectiveStartTime > configGroup[j].EffectiveStartTime
		})

		// 保留EffectiveStartTime最新的配置，将其他配置改为Expired
		latestConfig := configGroup[0]
		logger.CtxLogInfof(ctx, "Keeping active config with MultiProductID %d: id=%d", multiProductID, latestConfig.ID)

		// 将其他配置设为Expired
		for i := 1; i < len(configGroup); i++ {
			config := configGroup[i]
			config.RuleStatus = rule.RuleStatusExpired

			if err := a.AvailableLhRepo.UpdateAvailableLHStatus(ctx, config); err != nil {
				logger.CtxLogErrorf(ctx, "Failed to update AvailableLH status to Expired: id=%d, error=%v", config.ID, err)
				continue
			}

			logger.CtxLogInfof(ctx, "Updated AvailableLH status from Active to Expired: id=%d, MultiProductID=%d", config.ID, multiProductID)
		}
	}
}
