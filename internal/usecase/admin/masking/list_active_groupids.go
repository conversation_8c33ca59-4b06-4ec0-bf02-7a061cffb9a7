package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// func (p *ProductPriorityService)
func (p *ProductPriorityService) ListActiveGroups(ctx context.Context, req *admin_protocol.ListActiveGroupId) (*admin_protocol.ListActiveGroupIdRsp, *srerr.Error) {
	//gene-apis-reads-checks,
	condition := map[string]interface{}{}
	condition["mask_product_id = ?"] = req.MaskingProductId
	condition["config_status = ?"] = entity.Active
	res, err := p.PriorityRepo.GetProductPriorityList(ctx, condition)
	if err != nil {
		//
		return nil, err
	}
	//
	rsp := new(admin_protocol.ListActiveGroupIdRsp)
	rsp.GroupIdList = make([]int64, len(res))
	for i, v := range res {
		rsp.GroupIdList[i] = v.ShopGroupID
	}
	return rsp, nil
}
