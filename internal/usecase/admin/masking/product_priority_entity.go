package masking

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

const DateLength = 10

type ProductPriorityImport struct {
	MaskingProductId     string `json:"masking_product_id"`
	ShopGroupId          string `json:"shop_group_id"`
	RuleType             string `json:"rule_type"`
	EffectiveTime        string `json:"effective_time"`
	FulfillmentProductId string `json:"fulfillment_product_id"`
	Value                string `json:"value"`
	ToggleStatus         string `json:"toggle_status"`
	ActionCode           string `json:"action_code"`
	ValidateMessage      string `json:"validate_message"`
}

type ProductPriorityExport struct {
	MaskingProduct     string `json:"masking_product"`
	ShopGroupId        string `json:"shop_group_id"`
	ForecastRuleId     string `json:"forecast_rule_id"`
	RuleType           string `json:"rule_type"`
	Status             string `json:"status"`
	EffectiveTime      string `json:"effective_time"`
	LastOperator       string `json:"last_operator"`
	LastUpdated        string `json:"last_updated"`
	FulfillmentProduct string `json:"fulfillment_product"`
	Value              string `json:"value"`
	ToggleStatus       string `json:"toggle_status"`
}

type CreateRequest struct {
	MaskProductId   int32             `json:"mask_product_id"`
	ShopGroupId     int32             `json:"shop_group_id,omitempty"`
	RuleType        uint32            `json:"rule_type,omitempty"`
	PriorityDetails []*DetailOfCreate `json:"priority_details,omitempty"`
}

type DetailOfCreate struct {
	ProductId int32  `json:"product_id,omitempty"`
	Priority  uint32 `json:"priority,omitempty"`
	Weightage uint32 `json:"weightage,omitempty"`
	Status    uint32 `json:"status,omitempty"`
}

type ProductPriorityExportTemplate struct {
	MaskingProduct     string   `json:"masking_product"`
	ShopGroupId        []string `json:"shop_group_id"`
	RuleType           []string `json:"rule_type"`
	EffectiveTime      string   `json:"effective_time"`
	FulfillmentProduct string   `json:"fulfillment_product"`
	Value              string   `json:"value"`
	ToggleStatus       []string `json:"toggle_status"`
	ActionMode         []string `json:"action_mode"`
}

func (p *ProductPriorityImport) formatProductPriorityImport() *ProductPriorityImport {
	if strings.Contains(p.MaskingProductId, ".") {
		p.MaskingProductId = p.MaskingProductId[:strings.Index(p.MaskingProductId, ".")]
	}
	if strings.Contains(p.ShopGroupId, ".") {
		p.ShopGroupId = p.ShopGroupId[:strings.Index(p.ShopGroupId, ".")]
	}
	// shop group如果传了group name, 则需要取前面一部分作为shop group id
	shopGroupInfoList := strings.Split(p.ShopGroupId, ShopGroupSeparator)
	if len(shopGroupInfoList) > 0 {
		p.ShopGroupId = shopGroupInfoList[0]
	}
	if strings.Contains(p.FulfillmentProductId, ".") {
		p.FulfillmentProductId = p.FulfillmentProductId[:strings.Index(p.FulfillmentProductId, ".")]
	}
	if strings.Contains(p.Value, ".") {
		p.Value = p.Value[:strings.Index(p.Value, ".")]
	}
	if strings.Contains(p.ActionCode, ".") {
		p.ActionCode = p.ActionCode[:strings.Index(p.ActionCode, ".")]
	}
	//如果传入的是数字型日期格式，则需要转换
	_, err := strconv.ParseFloat(p.EffectiveTime, 64)
	if err == nil {
		p.EffectiveTime = timeutil.FormatDateTime(fileutil.ExcelDateStringToDate(p.EffectiveTime))
	}
	//如果转换后的effective_time只有日期，则加上00:00:00
	if len(p.EffectiveTime) == DateLength {
		p.EffectiveTime = p.EffectiveTime + " 00:00:00"
	}

	return p
}
