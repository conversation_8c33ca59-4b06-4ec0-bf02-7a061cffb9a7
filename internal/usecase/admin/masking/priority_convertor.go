package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

const (
	UpdateCheckType = "update"
	CreateCheckType = "create"
)

var PriorityConvertorMap map[string]PriorityConvertor

func NewPriorityConvertorMap(lpsApi lpsclient.LpsApi) map[string]PriorityConvertor {
	if PriorityConvertorMap != nil {
		return PriorityConvertorMap
	}

	PriorityConvertorMap = make(map[string]PriorityConvertor, 0)
	PriorityConvertorMap[UpdateCheckType] = NewUpdatePriorityConvertor(lpsApi)
	PriorityConvertorMap[CreateCheckType] = NewCreatePriorityConvertor(lpsApi)

	return PriorityConvertorMap
}

type PriorityConvertor interface {
	CheckComponentProduct(ctx context.Context, maskProduct *lpsclient.ProductDetailInfo, componentProduct entity.ComponentPriority) (string, bool)
}

type UpdatePriorityConvertorImpl struct {
	lpsApi lpsclient.LpsApi
}

func NewUpdatePriorityConvertor(lpsApi lpsclient.LpsApi) PriorityConvertor {
	return &UpdatePriorityConvertorImpl{
		lpsApi: lpsApi,
	}
}

func (p *UpdatePriorityConvertorImpl) CheckComponentProduct(ctx context.Context, maskProduct *lpsclient.ProductDetailInfo, componentPriority entity.ComponentPriority) (string, bool) {
	//入参的component product不属于masking product，且该component product的status为1，报错
	if !objutil.ContainInt(maskProduct.GetComponentProduct().ComponentProducts, int(componentPriority.ProductID)) && componentPriority.Status != entity.Closed {
		return fmt.Sprintf("unavailable component product %d", componentPriority.ProductID), false
	}

	//入参的component product属于masking product，但是不存在db中， 且该component product的status为1，报错
	notExisted := false
	productInfo, err := p.lpsApi.GetProductDetail(ctx, int(componentPriority.ProductID))
	if err != nil || productInfo == nil || productInfo.ProductId == 0 {
		notExisted = true
	}

	if notExisted && componentPriority.Status != entity.Closed {
		errMsg := fmt.Sprintf("component product:%v not existed but is open, plz check it.", componentPriority.ProductID)
		return errMsg, false
	}

	return "", true
}

type CreatePriorityConvertorImpl struct {
	lpsApi lpsclient.LpsApi
}

func NewCreatePriorityConvertor(lpsApi lpsclient.LpsApi) PriorityConvertor {
	return &CreatePriorityConvertorImpl{
		lpsApi: lpsApi,
	}
}

func (p *CreatePriorityConvertorImpl) CheckComponentProduct(ctx context.Context, maskProduct *lpsclient.ProductDetailInfo, componentPriority entity.ComponentPriority) (string, bool) {
	//入参的component product不属于masking product，报错
	if !objutil.ContainInt(maskProduct.GetComponentProduct().ComponentProducts, int(componentPriority.ProductID)) {
		return fmt.Sprintf("unavailable component product %v", componentPriority.ProductID), false
	}

	//入参的component product属于masking product，但component product不存在db中，报错
	productInfo, err := p.lpsApi.GetProductDetail(ctx, int(componentPriority.ProductID))
	if err != nil || productInfo == nil || productInfo.ProductId == 0 {
		return fmt.Sprintf("component product:%v not existed, plz check it.", componentPriority.ProductID), false
	}

	return "", true
}
