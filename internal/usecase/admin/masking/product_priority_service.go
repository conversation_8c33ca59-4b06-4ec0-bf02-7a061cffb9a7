package masking

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"math"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/redislock"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/rlock"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

//type ProductPriorityServiceInterface interface {
//	List(ctx context.Context, page uint32, count uint32, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, statusList []uint32) (
//		priorities []entity.ProductPriority, total uint32, srErr *srerr.Error)
//	Detail(ctx context.Context, id uint64) (ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority, smrErr *srerr.Error)
//	Create(ctx context.Context, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority) *srerr.Error
//	Update(ctx context.Context, id uint64, ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority) *srerr.Error
//}

//type ProductPriorityService struct {
//	LpsApi               lpsclient.LpsApi
//	PriorityRepo         productpriority.PriorityRepo
//	PriorityConvertorMap map[string]PriorityConvertor
//}

const S3TimeOut = 10
const EffectiveTimeInterval = 15 * 60
const ProductPriorityEditLockPrefix = "PRODUCT_PRIORITY_EDIT_LOCK:%v"
const ProductPriorityEditLockTimeOUt = 300 * time.Second
const RecordKey = "ssc_lps_cid_read.logistic_product_priority_tab"
const ImportColumnNum = 8
const DefaultShopGroup = "-1"
const DefaultShopGroupId = -1
const DefaultShopGroupName = "default group"
const ShopGroupSeparator = "---"
const TimeInterval = 2
const ProductPriorityImportRestrict = 60000
const DefaultHiddenSheetName = "ShopGroupId"

const (
	ActionModeAdd            = 1
	ActionModeUpdate         = 2
	RuleTypePriority         = "Product Priority"
	RuleTypeWeightage        = "Weightage"
	ToggleStatusOpen         = "on"
	ToggleStatusClose        = "off"
	ProductPrioritySheetName = "Product Priority/Weightage"
)

var (
	ProductPriorityImportHeader = []string{"*mask product id", "*shop group id-name", "*rule type", "*effective time", "*fulfillment product id", "priority/weightage", "*toggle status", "*action code", "Reason"}
	ProductPriorityExportHeader = []string{"mask product id-name", "shop group id-name", "forecast rule id", "rule type", "status", "effective time", "last operator", "last updated", "fulfillment product", "priority/weightage", "toggle status"}
)

type ProductPriorityServiceInterface interface {
	List(ctx context.Context, page uint32, count uint32, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, statusList []uint32) (
		priorities []entity.ProductPriority, total uint32, srErr *srerr.Error)
	Detail(ctx context.Context, id uint64) (ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority, effectTs int, smrErr *srerr.Error)
	Create(ctx context.Context, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority, effectNow bool, effectTs int) *srerr.Error
	Update(ctx context.Context, id uint64, ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority, effectNow bool, effectTs int) *srerr.Error

	ImportValidate(ctx context.Context, req *admin_protocol.ImportValidateRequest) (*admin_protocol.ImportValidateResponse, *srerr.Error)
	Import(ctx context.Context, req *admin_protocol.ImportRequest) *srerr.Error
	Export(ctx context.Context, req *admin_protocol.ExportRequest) (*admin_protocol.ExportResponse, *srerr.Error)
	ExportTemplate(ctx context.Context, req *admin_protocol.ExportTemplateRequest) (*admin_protocol.ExportTemplateResponse, *srerr.Error)
	ListActiveGroups(ctx context.Context, req *admin_protocol.ListActiveGroupId) (*admin_protocol.ListActiveGroupIdRsp, *srerr.Error)
}

type ProductPriorityService struct {
	LpsApi               lpsclient.LpsApi
	PriorityRepo         productpriority.PriorityRepo
	RedisLock            rlock.RedisLockClient
	PriorityConvertorMap map[string]PriorityConvertor
}

func NewProductPriorityService(lpsApi lpsclient.LpsApi, priorityRepo productpriority.PriorityRepo) *ProductPriorityService {
	return &ProductPriorityService{
		LpsApi:               lpsApi,
		PriorityRepo:         priorityRepo,
		PriorityConvertorMap: NewPriorityConvertorMap(lpsApi),
		RedisLock:            rlock.New(redisutil.GetDefaultInstance()),
	}
}

func (p *ProductPriorityService) List(ctx context.Context, page uint32, count uint32, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, statusList []uint32) (
	priorities []entity.ProductPriority, total uint32, srErr *srerr.Error) {

	var priorityTabItems []productpriority.LogisticProductPriorityTab
	// TODO use cache
	priorityTabItems, total, srErr = p.PriorityRepo.List(ctx, maskProductID, shopGroupID, ruleType, statusList, page, count)
	if srErr != nil {
		return nil, 0, srErr
	}

	itemLen := len(priorityTabItems)
	priorities = make([]entity.ProductPriority, itemLen)
	groupArray, gErr := p.LpsApi.GetShopGroupListByTag(ctx, uint64(lpsclient.ClientTag3PLMasking))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "List|get shop group list by tag err:%v", gErr)
		return nil, 0, srErr
	}
	groupMap := convertShopArrayToMap(groupArray)

	productMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	for i := 0; i < itemLen; i++ {
		priority, _ := productpriority.ConvertTabToProductPriority(ctx, priorityTabItems[i], false)
		// todo update status
		if productInterface, existed := productMap[strconv.Itoa(maskProductID)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				priority.MaskProductName = product.SellerDisplayName
			} else {
				priority.MaskProductName = "not enabled now"
				logger.CtxLogInfof(ctx, "List|mask product id:%v, not exist, will set default name", maskProductID)
			}
		}
		// 根据区域判断是否需要从client 模块获取shop group 信息
		shopGroup, ok := groupMap[priority.ShopGroupID]
		if !ok {
			priority.ShopGroupName = "not enable now"
		} else {
			priority.ShopGroupName = shopGroup.GroupName
		}
		priorities[i] = *priority
	}

	return priorities, total, srErr
}

func (p *ProductPriorityService) Detail(ctx context.Context, id uint64) (ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority, effectTs int, smrErr *srerr.Error) {
	priorityTab, err := p.PriorityRepo.GetById(ctx, id)
	if err != nil {
		return 0, nil, 0, err
	}
	priority := &entity.ProductPriority{
		MaskProductID: priorityTab.MaskProductID,
		ShopGroupID:   priorityTab.ShopGroupID,
	}

	maskProduct, err := p.LpsApi.GetProductDetail(ctx, int(priorityTab.MaskProductID))
	if err == nil {
		priority.MaskProductName = maskProduct.SellerDisplayName
	}

	shopGroupList, err := p.LpsApi.GetShopGroupListByTag(ctx, uint64(lpsclient.ClientTag3PLMasking))
	if err == nil {
		for _, shopGroupUnit := range shopGroupList {
			if shopGroupUnit.ShopGroupId == priority.ShopGroupID {
				priority.ShopGroupName = shopGroupUnit.PriorityShopGroup.GroupName
				break
			}
		}
	}

	priority, smrErr = productpriority.ConvertTabToProductPriority(ctx, *priorityTab, true)
	if smrErr != nil {
		return 0, nil, 0, smrErr
	}

	componentPriorities = p.enrichComponentPriorities(ctx, maskProduct, priority.ComponentPriorities)
	return priority.RuleType, componentPriorities, priorityTab.EffectiveStartTime, nil
}

func (p *ProductPriorityService) Create(ctx context.Context, maskProductID int, shopGroupID int64,
	ruleType entity.DefaultRuleType, componentPriorities []entity.ComponentPriority,
	effectNow bool, effectTs int) *srerr.Error {
	userEmail, _ := apiutil.GetUserInfo(ctx)
	priority := &entity.ProductPriority{
		MaskProductID:       int64(maskProductID),
		ShopGroupID:         shopGroupID,
		RuleType:            ruleType,
		ComponentPriorities: componentPriorities,
		Operator:            userEmail,
		Ctime:               recorder.Now(ctx).Unix(),
		Mtime:               recorder.Now(ctx).Unix(),
	}

	maskProduct, err := p.LpsApi.GetProductDetail(ctx, maskProductID)
	if err != nil {
		return err
	}

	shopGroupList, err := p.LpsApi.GetShopGroupListByTag(ctx, uint64(lpsclient.ClientTag3PLMasking))
	if err != nil {
		return err
	}
	shopGroupExisted := false
	for _, shopGroupUnit := range shopGroupList {
		if shopGroupUnit.ShopGroupId == shopGroupID {
			shopGroupExisted = true
			break
		}
	}
	if !shopGroupExisted {
		errMsg := fmt.Sprintf("shop group:%v not existed", shopGroupID)
		return srerr.New(srerr.ShopGroupNotExisted, nil, errMsg)
	}

	for _, componentPriority := range priority.ComponentPriorities {
		if errMsg, pass := p.PriorityConvertorMap[CreateCheckType].CheckComponentProduct(ctx, maskProduct, componentPriority); !pass {
			return srerr.New(srerr.ProductPriorityUpdateFail, nil, errMsg)
		}
	}

	item, smrErr := productpriority.ConvertProductPriorityToTab(ctx, *priority, true)
	if smrErr != nil {
		return smrErr
	}
	item.ConfigStatus = int32(entity.Active)
	item.EffectiveStartTime = int(timeutil.GetCurrentUnixTimeStamp(ctx))
	if !effectNow {
		item.ConfigStatus = int32(entity.Upcoming)
		item.EffectiveStartTime = effectTs
	}
	locker, lerr := p.RedisLock.Obtain(ctx, fmt.Sprintf(ProductPriorityEditLockPrefix, maskProductID), ProductPriorityEditLockTimeOUt, nil)
	if lerr != nil {
		logger.CtxLogErrorf(ctx, "getLockFailed %v", lerr)
		return srerr.New(srerr.ServerErr, nil, fmt.Sprintf("cant edit concurrently mask_product_id=%v, shop_group_id=%v", item.MaskProductID, item.ShopGroupID))
	}
	defer func() {
		if err := locker.Release(ctx); err != nil {
			logger.CtxLogErrorf(ctx, "release lock fail|err=%+v ", err)
		}
	}()
	// create 接口仅能创建active 状态的记录
	smrErr = p.PriorityRepo.Create(ctx, item)

	return smrErr
}
func (p *ProductPriorityService) Update(ctx context.Context, id uint64, ruleType entity.DefaultRuleType,
	componentPriorities []entity.ComponentPriority,
	effectNow bool, effectTs int) *srerr.Error {
	userEmail, _ := apiutil.GetUserInfo(ctx)
	priorityTab, err := p.PriorityRepo.GetById(ctx, id)
	if err != nil {
		return err
	}
	priority := &entity.ProductPriority{
		ID:                  priorityTab.ID,
		MaskProductID:       priorityTab.MaskProductID,
		ShopGroupID:         priorityTab.ShopGroupID,
		RuleType:            ruleType,
		ComponentPriorities: componentPriorities,
		Operator:            userEmail,
		Mtime:               recorder.Now(ctx).Unix(),
	}

	maskProduct, err := p.LpsApi.GetProductDetail(ctx, int(priorityTab.MaskProductID))
	if err != nil {
		errMsg := fmt.Sprintf("product not found for product_id(%v)", priorityTab.MaskProductID)
		return srerr.New(srerr.LpsError, nil, errMsg)
	}

	shopGroupList, err := p.LpsApi.GetShopGroupListByTag(ctx, uint64(lpsclient.ClientTag3PLMasking))
	if err != nil {
		return err
	}
	shopGroupExisted := false
	for _, shopGroupUnit := range shopGroupList {
		if shopGroupUnit.ShopGroupId == priorityTab.ShopGroupID {
			shopGroupExisted = true
			break
		}
	}
	if !shopGroupExisted {
		errMsg := fmt.Sprintf("can not find client group id %v", priorityTab.ShopGroupID)
		return srerr.New(srerr.ShopGroupNotExisted, priorityTab.ShopGroupID, errMsg)
	}

	for _, componentPriority := range priority.ComponentPriorities {
		if errMsg, pass := p.PriorityConvertorMap[UpdateCheckType].CheckComponentProduct(ctx, maskProduct, componentPriority); !pass {
			return srerr.New(srerr.ProductPriorityUpdateFail, nil, errMsg)
		}
	}

	item, cErr := productpriority.ConvertProductPriorityToTab(ctx, *priority, true)
	if cErr != nil {
		return cErr
	}
	// update 不能修改记录状态
	item.ConfigStatus = priorityTab.ConfigStatus
	//0 值不更新，be 先发布无问题。
	item.EffectiveStartTime = priorityTab.EffectiveStartTime
	locker, lerr := p.RedisLock.Obtain(ctx, fmt.Sprintf(ProductPriorityEditLockPrefix, priorityTab.MaskProductID), ProductPriorityEditLockTimeOUt, nil)
	if lerr != nil {
		logger.CtxLogErrorf(ctx, "getLockFailed %v", lerr)
		return srerr.New(srerr.ServerErr, nil, fmt.Sprintf("cant edit concurrently mask_product_id=%v, shop_group_id=%v, can't create another active configure", item.MaskProductID, item.ShopGroupID))
	}
	defer func() {
		if err := locker.Release(ctx); err != nil {
			logger.CtxLogErrorf(ctx, "release lock fail|err=%+v ", err)
		}
	}()
	if priorityTab.ConfigStatus == int32(entity.Upcoming) && effectNow {
		//GetActiveConfigByProductIdAndGroupId
		record, errq := p.PriorityRepo.GetActiveConfigByProductIdAndGroupIdComplyRecordInexist(ctx, int(priorityTab.MaskProductID), priorityTab.ShopGroupID)
		if errq != nil {
			//not include inexist ,
			logger.CtxLogErrorf(ctx, "GetActiveConfigByProductIdAndGroupId %v", errq)
			return errq
		}
		if record != nil && record.ID != 0 {
			//a.不允许 多条active rows,
			return srerr.New(srerr.ServerErr, nil, fmt.Sprintf("there is an active configure about mask_product_id=%v, shop_group_id=%v, can't create another active configure", item.MaskProductID, item.ShopGroupID))
		}
	}
	if priorityTab.ConfigStatus == int32(entity.Upcoming) {
		if effectNow {
			item.ConfigStatus = int32(entity.Active)
			//需要校验下 是否存在active 的，
			item.EffectiveStartTime = int(timeutil.GetCurrentUnixTimeStamp(ctx))
		} else {
			item.EffectiveStartTime = effectTs
		}
	}
	cErr = p.PriorityRepo.Update(ctx, item)

	return cErr
}

func convertShopArrayToMap(ShopGroupArray []lpsclient.ShopGroupUnit) map[int64]lpsclient.PriorityShopGroup {
	groupMap := make(map[int64]lpsclient.PriorityShopGroup)
	for _, groupUnit := range ShopGroupArray {
		groupMap[groupUnit.ShopGroupId] = groupUnit.PriorityShopGroup
	}
	return groupMap
}

func (p *ProductPriorityService) enrichComponentPriorities(ctx context.Context, maskProduct *lpsclient.ProductDetailInfo, componentPriorities []entity.ComponentPriority) []entity.ComponentPriority {
	productMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	returnComponentPriorities := make([]entity.ComponentPriority, 0)
	componentProductMap := mathutil.SetIntSliceToMap(maskProduct.GetComponentProduct().ComponentProducts)
	for _, componentPriority := range componentPriorities {
		productExisted := false
		if productInterface, existed := productMap[strconv.FormatInt(componentPriority.ProductID, 10)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				componentPriority.Name = product.SellerDisplayName
				productExisted = true
			} else {
				logger.CtxLogErrorf(ctx, "enrichComponentPriorities| product id:%v, convert fail")
			}
		}
		if !productExisted {
			// 如果 component 不存在，就置为关闭
			componentPriority.Name = "not enabled now"
			componentPriority.Status = entity.Closed
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			delete(componentProductMap, int(componentPriority.ProductID))
			continue
		}
		if _, ok := componentProductMap[int(componentPriority.ProductID)]; !ok {
			componentPriority.Name = componentPriority.Name + "( not under mask product )"
			componentPriority.Status = entity.Closed
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			continue
		}
		delete(componentProductMap, int(componentPriority.ProductID))
		returnComponentPriorities = append(returnComponentPriorities, componentPriority)
	}
	for componentProductId := range componentProductMap {
		// 对于不在priority列表下的component product需要补齐
		componentPriority := entity.ComponentPriority{
			ProductID: int64(componentProductId),
			Status:    entity.Closed,
		}

		productExisted := false
		if productInterface, existed := productMap[strconv.FormatInt(componentPriority.ProductID, 10)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				componentPriority.Name = product.SellerDisplayName
				productExisted = true
			} else {
				logger.CtxLogErrorf(ctx, "enrichComponentPriorities| product id:%v, convert fail")
			}
		}

		if !productExisted {
			componentPriority.Name = "not enabled now"
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			continue
		}
		returnComponentPriorities = append(returnComponentPriorities, componentPriority)
	}
	sort.Sort(entity.ComponentPriorities(returnComponentPriorities))
	for i := range returnComponentPriorities {
		returnComponentPriorities[i].Priority = uint32(i + 1)
	}
	return returnComponentPriorities
}

func (p *ProductPriorityService) ImportValidate(ctx context.Context, req *admin_protocol.ImportValidateRequest) (*admin_protocol.ImportValidateResponse, *srerr.Error) {
	importValidateResponse := admin_protocol.ImportValidateResponse{
		Url:           req.Url,
		IsPartSuccess: false,
	}
	//1. 下载并解析数据
	productPriorityImportList, header, err := p.DownloadProductPriorityFile(ctx, req.Url)
	if err != nil {
		return nil, err
	}
	//2. 校验导入的数据
	validResult, vErr := p.CheckImportProductPriorityData(ctx, req.MaskingProductId, req.ActionMode, productPriorityImportList, header)
	if vErr != nil {
		return nil, vErr
	}
	//3. 部分成功，写入错误信息到excel文件，上传到s3
	if !validResult {
		partSuccessFileUrl, uErr := p.UploadProductPriorityFile(ctx, ProductPriorityImportHeader, convertProductPriorityImportToString(productPriorityImportList), ProductPrioritySheetName)
		if uErr != nil {
			return nil, uErr
		}
		importValidateResponse.Url = partSuccessFileUrl
		importValidateResponse.IsPartSuccess = true
	}
	//4. 构建返回结果
	return &importValidateResponse, nil
}

func (p *ProductPriorityService) Import(ctx context.Context, req *admin_protocol.ImportRequest) *srerr.Error {
	//1. 下载并解析数据
	productPriorityImportList, header, err := p.DownloadProductPriorityFile(ctx, req.Url)
	if err != nil {
		return err
	}
	//2. 加分布式锁
	lock, err1 := p.RedisLock.Obtain(ctx, formatRedisLockKey(req.MaskingProductId), ProductPriorityEditLockTimeOUt, &redislock.Options{
		RetryStrategy: redislock.NoRetry(),
	})
	if err1 != nil {
		return srerr.With(srerr.RedisLockFail, "", err1)
	}
	defer func() {
		if err2 := lock.Release(ctx); err2 != nil {
			logger.CtxLogErrorf(ctx, "release lock fail|err=%+v", err2)
		}
	}()
	//3. 校验导入的数据
	validResult, vErr := p.CheckImportProductPriorityData(ctx, req.MaskingProductId, req.ActionMode, productPriorityImportList, header)
	if vErr != nil {
		return vErr
	}
	//4. 部分成功，写入错误信息到excel文件，上传到s3
	if !validResult {
		partSuccessFileUrl, uErr := p.UploadProductPriorityFile(ctx, ProductPriorityImportHeader, convertProductPriorityImportToString(productPriorityImportList), ProductPrioritySheetName)
		if uErr != nil {
			return uErr
		}
		// 保存操作日志（不要影响主流程）
		_ = p.LpsApi.AddHistoryOperation(ctx, RecordKey, fmt.Sprintf("import fail-%v#&#&#%v", req.MaskingProductId, partSuccessFileUrl), lpsclient.ImportType, envvar.GetRequestEmail(ctx), "batch import product priority")
		return srerr.New(srerr.ProductPriorityPartSuccess, partSuccessFileUrl, "import product priority part success")
	}
	//5. 校验成功，保存数据，大事务，全部成功或全部失败、masking_product_id分布式锁、生效时间处理
	insertTabList, updateTabList, err := p.ConvertToProductPriorityTab(ctx, req.MaskingProductId, productPriorityImportList, req.ActionMode)
	if err != nil {
		return err
	}
	err = p.PriorityRepo.ImportSaveProductPriority(ctx, insertTabList, updateTabList)
	if err != nil {
		return err
	}
	//6. 保存操作日志（不要影响主流程）
	_ = p.LpsApi.AddHistoryOperation(ctx, RecordKey, fmt.Sprintf("import success-%v#&#&#%v", req.MaskingProductId, req.Url), lpsclient.ImportType, envvar.GetRequestEmail(ctx), "batch import product priority")
	return nil
}

func (p *ProductPriorityService) Export(ctx context.Context, req *admin_protocol.ExportRequest) (*admin_protocol.ExportResponse, *srerr.Error) {
	//1. 查询数据
	if !req.ExportAllData && len(req.Ids) < 1 {
		return nil, srerr.New(srerr.ExportParamCanNotNull, nil, "")
	}
	condition := make(map[string]interface{})
	if !req.ExportAllData {
		condition["id in ?"] = req.Ids
	} else {
		if req.MaskingProductId != 0 {
			condition["mask_product_id = ?"] = req.MaskingProductId
		}
		if req.ShopGroupId != 0 {
			condition["shop_group_id = ?"] = req.ShopGroupId
		}
		if req.RuleType != 0 {
			condition["rule_type = ?"] = req.RuleType
		}
		if len(req.StatusList) > 0 {
			condition["id in ?"] = req.StatusList
		}
	}
	productPriorityTabList, err := p.PriorityRepo.GetProductPriorityList(ctx, condition)
	if err != nil {
		return nil, err
	}

	//2. 查询client group信息
	var shopGroupIdList []int64
	shopGroupIdMap := make(map[int64]bool)
	for _, productPriorityTab := range productPriorityTabList {
		if _, ok := shopGroupIdMap[productPriorityTab.ShopGroupID]; !ok {
			shopGroupIdList = append(shopGroupIdList, productPriorityTab.ShopGroupID)
			shopGroupIdMap[productPriorityTab.ShopGroupID] = true
		}
	}
	shopGroupInfoMap, _ := p.GetClientShopInfoMap(ctx, shopGroupIdList)

	productPriorityExportList, err := convertToProductPriorityExport(productPriorityTabList, shopGroupInfoMap)
	if err != nil {
		return nil, err
	}
	//3. 生成excel文件并上传s3
	fileUrl, uErr := p.UploadProductPriorityFile(ctx, ProductPriorityExportHeader, convertProductPriorityExportToString(productPriorityExportList), ProductPrioritySheetName)
	if uErr != nil {
		return nil, uErr
	}
	//4. 构建返回结果
	exportResponse := admin_protocol.ExportResponse{
		Url: fileUrl,
	}
	_ = p.LpsApi.AddHistoryOperation(ctx, RecordKey, fmt.Sprintf("export success#&#&#%v", fileUrl), lpsclient.ExportType, envvar.GetRequestEmail(ctx), "batch export product priority")
	return &exportResponse, nil
}

func (p *ProductPriorityService) ExportTemplate(ctx context.Context, req *admin_protocol.ExportTemplateRequest) (*admin_protocol.ExportTemplateResponse, *srerr.Error) {
	//1. 获取masking_product_id信息
	maskingProduct, err := p.LpsApi.GetProductDetail(ctx, int(req.MaskingProductId))
	if err != nil {
		return nil, err
	}
	if maskingProduct == nil {
		return nil, srerr.New(srerr.GetProductBaseFail, nil, "product detail is nil")
	}
	//2. 获取关联的shopGroup列表
	shopGroupInfoList, err := p.LpsApi.GetProductRelateShopGroup(ctx, lpsclient.ClientTag3PLMasking, req.MaskingProductId)
	if err != nil {
		return nil, err
	}
	exportTemplateList, err := p.convertToExportTemplate(ctx, maskingProduct, shopGroupInfoList)
	if err != nil {
		return nil, err
	}
	//3. 上传模版文件
	fileUrl, err := p.UploadProductPriorityExportTemplateFile(ctx, exportTemplateList)
	if err != nil {
		return nil, err
	}
	exportTemplateResponse := admin_protocol.ExportTemplateResponse{
		Url: fileUrl,
	}
	return &exportTemplateResponse, nil
}

func (p *ProductPriorityService) DownloadProductPriorityFile(ctx context.Context, url string) ([]*ProductPriorityImport, []string, *srerr.Error) {
	//1. 下载s3文件
	fileData, err := httputil.Get(ctx, url, nil, S3TimeOut, nil)
	if err != nil {
		return nil, nil, srerr.With(srerr.S3DownloadFail, url, err)
	}
	//2. 解析excel文件，得到文件数据
	rows, header, fErr := getFileData(ctx, url, fileData)
	if fErr != nil {
		return nil, nil, srerr.With(srerr.ParseExcelError, url, err)
	}
	if len(rows) > ProductPriorityImportRestrict {
		return nil, nil, srerr.With(srerr.ImportDataExtendRestrict, url, nil)
	}
	var productPriorityImportList []*ProductPriorityImport
	for _, row := range rows {
		var tag bool
		for k := 0; k < ImportColumnNum; k++ {
			if row[k] != "" {
				tag = true
				break
			}
		}
		if !tag {
			continue
		}
		productPriorityImport := new(ProductPriorityImport)
		fileutil.ExcelRowToInterface(row, productPriorityImport, ImportColumnNum)
		productPriorityImportList = append(productPriorityImportList, productPriorityImport.formatProductPriorityImport())
	}
	return productPriorityImportList, header, nil
}

func getFileData(ctx context.Context, url string, fileData []byte) ([][]string, []string, *srerr.Error) {
	rows, header, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(fileData), true)
	if fErr == nil {
		return rows, header, nil
	}
	if strings.Contains(url, ".csv?") {
		rows, header = fileutil.ReadCsv(fileData)
	}
	if strings.Contains(url, ".xls?") {
		rows, header, fErr = fileutil.ParseXlsFile(ctx, fileData)
	}
	if fErr != nil && len(rows) < 1 {
		return nil, nil, srerr.With(srerr.ParseExcelError, nil, fErr)
	}
	return rows, header, nil
}

func (p *ProductPriorityService) CheckImportProductPriorityData(ctx context.Context, maskingProductId int64, actionMode uint8, productPriorityImportList []*ProductPriorityImport, header []string) (bool, *srerr.Error) {
	//0. 校验表头是否正确
	for i := 0; i < ImportColumnNum; i++ {
		if i > len(header)-1 || i > len(ProductPriorityImportHeader)-1 {
			return false, srerr.New(srerr.ImportHeaderError, nil, "import excel header wrong format")
		}
		if header[i] != ProductPriorityImportHeader[i] {
			return false, srerr.New(srerr.ImportHeaderError, nil, "import excel header wrong format, correct is %v, wrong is %v", ProductPriorityImportHeader[i], header[i])
		}
	}
	//1. 校验maskingProductId是否正确
	maskingProductInfo, err := p.LpsApi.GetProductDetail(ctx, int(maskingProductId))
	if err != nil {
		return false, err
	}
	if maskingProductInfo == nil {
		return false, srerr.New(srerr.ProductInfoNotFound, maskingProductId, "")
	}
	if maskingProductInfo.ComponentProduct == nil {
		return false, srerr.New(srerr.ProductInfoNotFound, maskingProductId, "")
	}
	//2. 查询shop group信息
	var shopGroupIdList []int64
	shopGroupIdMap := make(map[string]bool)
	for _, productPriorityImport := range productPriorityImportList {
		if _, ok := shopGroupIdMap[productPriorityImport.ShopGroupId]; !ok {
			shopGroupIdMap[productPriorityImport.ShopGroupId] = true
			value, pErr := strconv.ParseInt(productPriorityImport.ShopGroupId, 10, 64)
			// 忽略转换错误的shop_group_id
			if pErr == nil {
				shopGroupIdList = append(shopGroupIdList, value)
			}
		}
	}
	shopGroupInfoMap, _ := p.GetClientShopInfoMap(ctx, shopGroupIdList)
	// 查询product关联的shop_group
	relateShopGroupMap := make(map[string]bool)
	// default shop group不在关联表中，是兜底逻辑-1
	relateShopGroupMap[DefaultShopGroup] = true
	productRelateShopGroupList, err := p.LpsApi.GetProductRelateShopGroup(ctx, lpsclient.ClientTag3PLMasking, maskingProductId)
	if err != nil {
		return false, err
	}
	for _, productRelateShopGroup := range productRelateShopGroupList {
		if _, ok := relateShopGroupMap[productRelateShopGroup.ClientGroupId]; !ok {
			relateShopGroupMap[productRelateShopGroup.ClientGroupId] = true
		}
	}

	//3. 校验导入数据
	// false: 校验失败，true：校验成功
	finalValidResult := true
	ruleTypeMap := make(map[string]string)
	effectiveTimeMap := make(map[string]string)
	shopGroupMap := make(map[string]string)
	for _, productPriorityImport := range productPriorityImportList {
		//3.0 必填校验
		if productPriorityImport.MaskingProductId == "" {
			productPriorityImport.ValidateMessage += "masking_product_id required;"
		}
		if productPriorityImport.ShopGroupId == "" {
			productPriorityImport.ValidateMessage += "shop group id required;"
		}
		if _, ok := relateShopGroupMap[productPriorityImport.ShopGroupId]; !ok {
			productPriorityImport.ValidateMessage += "The current shop group id is not bound to the mask channel, please check in the client module.;"
		}
		if !(productPriorityImport.RuleType == RuleTypePriority || productPriorityImport.RuleType == RuleTypeWeightage) {
			productPriorityImport.ValidateMessage += "Rule type should be Product Priority or Weightage.;"
		}
		if productPriorityImport.FulfillmentProductId == "" {
			productPriorityImport.ValidateMessage += "fulfillment_product_id required;"
		}
		if productPriorityImport.ToggleStatus == "" {
			productPriorityImport.ValidateMessage += "toggle status required;"
		}
		if productPriorityImport.ActionCode == "" {
			productPriorityImport.ValidateMessage += "action code required;"
		}
		if !(productPriorityImport.ToggleStatus == ToggleStatusOpen || productPriorityImport.ToggleStatus == ToggleStatusClose) {
			productPriorityImport.ValidateMessage += "toggle status should be on/off;"
		}
		if !(productPriorityImport.ActionCode == common.InterfaceToStr(ActionModeAdd) || productPriorityImport.ActionCode == common.InterfaceToStr(ActionModeUpdate)) {
			productPriorityImport.ValidateMessage += "action mode should be 1 or 2;"
		}
		if productPriorityImport.ValidateMessage != "" {
			finalValidResult = false
			continue
		}
		//3.1 校验masking_product_id是否和选择的masking_product_id相同
		if common.InterfaceToStr(maskingProductId) != productPriorityImport.MaskingProductId {
			productPriorityImport.ValidateMessage += fmt.Sprintf("masking_product_id wrong, correct is %v;", maskingProductId)
		}
		//3.2 校验action_mode是否一致
		if common.InterfaceToStr(actionMode) != productPriorityImport.ActionCode {
			if actionMode == ActionModeAdd {
				productPriorityImport.ValidateMessage += "please use import update files button for action code = 2;"
			} else {
				productPriorityImport.ValidateMessage += "please use import create files button for action code = 1;"
			}
		}
		//3.3 校验effective_time、rule_type是否一致
		key := fmt.Sprintf("%v:%v", productPriorityImport.MaskingProductId, productPriorityImport.ShopGroupId)
		if _, ok := ruleTypeMap[key]; !ok {
			ruleTypeMap[key] = productPriorityImport.RuleType
			effectiveTimeMap[key] = productPriorityImport.EffectiveTime
		} else {
			if ruleTypeMap[key] != productPriorityImport.RuleType {
				productPriorityImport.ValidateMessage += "rule type inconformity;"
			}
			if !compareEffectiveTime(effectiveTimeMap[key], productPriorityImport) {
				productPriorityImport.ValidateMessage += "the effective time not consistent in one group;"
			}
		}
		//3.4 校验effective_time生效时间是否小于15分钟
		effectiveTimeStamp, err1 := timeutil.ParseDatetimeStr(productPriorityImport.EffectiveTime)
		if err1 != nil {
			productPriorityImport.ValidateMessage += fmt.Sprintf("effective time format invid, must be: 2006-01-02 15:04:05, err=%v;", err1)
		}
		if !timeutil.CompareTimeInterval(ctx, effectiveTimeStamp, int64(EffectiveTimeInterval)) {
			productPriorityImport.ValidateMessage += "effective time interval less than 15 minutes;"
		}
		//3.5 判断shop_group_id是否存在
		if _, ok := shopGroupMap[productPriorityImport.ShopGroupId]; !ok {
			if _, ok1 := shopGroupInfoMap[productPriorityImport.ShopGroupId]; !ok1 && productPriorityImport.ShopGroupId != DefaultShopGroup {
				productPriorityImport.ValidateMessage += "shop group id not found;"
			}
			shopGroupMap[productPriorityImport.ShopGroupId] = productPriorityImport.ShopGroupId
		}
		//3.6 判断最终的校验结果
		if productPriorityImport.ValidateMessage != "" {
			finalValidResult = false
		}
	}

	//4. 校验fulfillment product id、priority/weightage
	priorityMap := make(map[string][]*ProductPriorityImport)
	for _, productPriorityImport := range productPriorityImportList {
		key := fmt.Sprintf("%v:%v", productPriorityImport.MaskingProductId, productPriorityImport.ShopGroupId)
		if _, ok := priorityMap[key]; !ok {
			var priorityList []*ProductPriorityImport
			priorityList = append(priorityList, productPriorityImport)
			priorityMap[key] = priorityList
		} else {
			priorityMap[key] = append(priorityMap[key], productPriorityImport)
		}
	}
	for _, productPriorityList := range priorityMap {
		//4.1 校验shop group id
		shopGroupId, err4 := strconv.ParseInt(productPriorityList[0].ShopGroupId, 10, 64)
		if err4 != nil {
			productPriorityList[0].ValidateMessage += fmt.Sprintf("shop group id is invalid, err=%v;", err4)
		}
		// 查询masking_product_id、shop_group_id确定的product_priority
		priorityList, err5 := p.PriorityRepo.GetProductPriority(ctx, maskingProductId, shopGroupId, []int32{int32(entity.Active), int32(entity.Upcoming)})
		if err5 != nil {
			productPriorityList[0].ValidateMessage += fmt.Sprintf("get product priority error, err=%v;", err5)
		}
		if actionMode == ActionModeAdd && len(priorityList) != 0 {
			productPriorityList[0].ValidateMessage += "already have product priority, can't create;"
		}
		if actionMode == ActionModeUpdate && len(priorityList) < 1 {
			productPriorityList[0].ValidateMessage += "product priority not exists, can't update;"
		}
		if actionMode == ActionModeUpdate && len(priorityList) > 2 {
			productPriorityList[0].ValidateMessage += "product priority have multiple upcoming record;"
		}
		//4.2 校验fulfillment_product_id是否有效
		fulfillmentProductIdMap := make(map[string]bool)
		for _, productPriority := range productPriorityList {
			if _, ok := fulfillmentProductIdMap[productPriority.FulfillmentProductId]; ok {
				productPriority.ValidateMessage += "shop group can not have duplicate fulfillment channel;"
			}
			fulfillmentProductIdMap[productPriority.FulfillmentProductId] = true
			fulfillmentProductId, err6 := strconv.ParseInt(productPriority.FulfillmentProductId, 10, 64)
			if err6 != nil {
				productPriority.ValidateMessage += fmt.Sprintf("fulfillment product id is invalid, err=%v;", err6)
				continue
			}
			if !objutil.ContainsInt(maskingProductInfo.ComponentProduct.ComponentProducts, int(fulfillmentProductId)) {
				productPriority.ValidateMessage += fmt.Sprintf("fulfillment product id:%v is not belong to mask product id：%v;", fulfillmentProductId, maskingProductId)
			}
		}
		if len(fulfillmentProductIdMap) != len(maskingProductInfo.ComponentProduct.ComponentProducts) {
			productPriorityList[0].ValidateMessage += "shop group should config all fulfillment channel in excel;"
		}
		//4.3 校验priority、weightage值是否有效
		ruleType := productPriorityList[0].RuleType
		sort.Slice(productPriorityList, func(i, j int) bool {
			return productPriorityList[i].Value < productPriorityList[j].Value
		})
		var initPriority int64 = 1
		for _, productPriority := range productPriorityList {
			if productPriority.ToggleStatus == ToggleStatusClose {
				if productPriority.Value != "" {
					productPriority.ValidateMessage += "this channel status is off, should not insert value in priority;"
				}
				continue
			}
			value, err7 := strconv.ParseInt(productPriority.Value, 10, 64)
			if err7 != nil || value < 1 {
				productPriority.ValidateMessage += "Product priority/weightage should be int;"
				continue
			}
			if ruleType == RuleTypePriority {
				if initPriority != value {
					productPriority.ValidateMessage += "the priority need to be in order, like 1,2,3;"
				}
				initPriority++
				if int(value) > len(fulfillmentProductIdMap) {
					productPriority.ValidateMessage += "priority value should not exceed the number of channels;"
				}
			}
			if ruleType == RuleTypeWeightage && productPriority.Value == "0" {
				productPriority.ValidateMessage += "opened product weightage value can not be 0;"
			}
		}
		//4.4 判断最终的校验结果
		for _, productPriority := range productPriorityList {
			if productPriority.ValidateMessage != "" {
				finalValidResult = false
				break
			}
		}
	}

	return finalValidResult, nil
}

func (p *ProductPriorityService) UploadProductPriorityFile(ctx context.Context, header []string, data [][]string, sheetName string) (string, *srerr.Error) {
	// 生成excel文件
	newFile, fErr := fileutil.MakeExcelWithSheetName(ctx, header, data, sheetName)
	if fErr != nil {
		return "", srerr.With(srerr.MakeExcelError, nil, fErr)
	}
	b, wErr := newFile.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.MakeExcelError, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Product Priority-%v%s", timeutil.GetCurrentTime(ctx).Unix(), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil
}

// compareEffectiveTime excel导入时存在精度丢失，对比时间时，误差2秒算相等
func compareEffectiveTime(effectiveTime string, productPriorityImport *ProductPriorityImport) bool {
	effectiveTimeSecond, _ := timeutil.ParseDatetimeStr(effectiveTime)
	newEffectiveTimeSecond, _ := timeutil.ParseDatetimeStr(productPriorityImport.EffectiveTime)
	if math.Abs(float64(effectiveTimeSecond.Unix()-newEffectiveTimeSecond.Unix())) <= float64(TimeInterval) {
		// 修正误差
		productPriorityImport.EffectiveTime = effectiveTime
		return true
	}
	return false
}

func convertProductPriorityImportToString(productPriorityImportList []*ProductPriorityImport) [][]string {
	var productPriorityStringList [][]string
	for _, productPriorityImport := range productPriorityImportList {
		var productPriorityString []string
		priorityType := reflect.ValueOf(productPriorityImport).Elem()
		for k := 0; k < priorityType.NumField(); k++ {
			productPriorityString = append(productPriorityString, common.InterfaceToStr(priorityType.Field(k).Interface()))
		}
		productPriorityStringList = append(productPriorityStringList, productPriorityString)
	}
	return productPriorityStringList
}

func convertProductPriorityExportToString(productPriorityExportList []*ProductPriorityExport) [][]string {
	var productPriorityStringList [][]string
	for _, productPriorityExport := range productPriorityExportList {
		var productPriorityString []string
		priorityType := reflect.ValueOf(productPriorityExport).Elem()
		for k := 0; k < priorityType.NumField(); k++ {
			productPriorityString = append(productPriorityString, common.InterfaceToStr(priorityType.Field(k).Interface()))
		}
		productPriorityStringList = append(productPriorityStringList, productPriorityString)
	}
	return productPriorityStringList
}

func (p *ProductPriorityService) ConvertToProductPriorityTab(ctx context.Context, maskingProductId int64, productPriorityImportList []*ProductPriorityImport, actionMode uint8) ([]*productpriority.LogisticProductPriorityTab, []*productpriority.LogisticProductPriorityTab, *srerr.Error) {
	//1. 构建product_priority数据
	priorityMap := make(map[string][]*ProductPriorityImport)
	for _, productPriorityImport := range productPriorityImportList {
		key := fmt.Sprintf("%v:%v", productPriorityImport.MaskingProductId, productPriorityImport.ShopGroupId)
		if _, ok := priorityMap[key]; !ok {
			var priorityList []*ProductPriorityImport
			priorityList = append(priorityList, productPriorityImport)
			priorityMap[key] = priorityList
		} else {
			priorityMap[key] = append(priorityMap[key], productPriorityImport)
		}
	}
	//2. 构建插入数据
	operator := envvar.GetRequestEmail(ctx)
	currentTime := timeutil.GetCurrentTime(ctx).Unix()
	var insertPriorityTabList []*productpriority.LogisticProductPriorityTab
	var updatePriorityTabList []*productpriority.LogisticProductPriorityTab
	for _, productPriorityList := range priorityMap {
		switch actionMode {
		case uint8(ActionModeAdd):
			insertPriorityTab, err := GenerateProductPriorityTab(productPriorityList, currentTime, operator)
			if err != nil {
				return nil, nil, err
			}
			insertPriorityTabList = append(insertPriorityTabList, insertPriorityTab)
		case uint8(ActionModeUpdate):
			shopGroupId, _ := strconv.ParseInt(productPriorityList[0].ShopGroupId, 10, 64)
			priorityList, err := p.PriorityRepo.GetProductPriority(ctx, maskingProductId, shopGroupId, []int32{int32(entity.Upcoming)})
			if err != nil {
				return nil, nil, err
			}
			if len(priorityList) > 1 {
				return nil, nil, srerr.New(srerr.HaveMultiUpcomingPriority, shopGroupId, "")
			}
			insertPriorityTab, err := GenerateProductPriorityTab(productPriorityList, currentTime, operator)
			if err != nil {
				return nil, nil, err
			}
			if len(priorityList) < 1 {
				insertPriorityTabList = append(insertPriorityTabList, insertPriorityTab)
			} else {
				insertPriorityTab.ID = priorityList[0].ID
				insertPriorityTab.Ctime = priorityList[0].Ctime
				insertPriorityTab.Validity = priorityList[0].Validity
				insertPriorityTab.ExpiredTime = priorityList[0].ExpiredTime
				insertPriorityTab.ForecastTaskId = priorityList[0].ForecastTaskId
				updatePriorityTabList = append(updatePriorityTabList, insertPriorityTab)
			}
		}
	}
	return insertPriorityTabList, updatePriorityTabList, nil
}

func GenerateProductPriorityTab(productPriorityImportList []*ProductPriorityImport, currentTime int64, operator string) (*productpriority.LogisticProductPriorityTab, *srerr.Error) {
	//1. 构建参数
	maskingProductId, _ := strconv.ParseInt(productPriorityImportList[0].MaskingProductId, 10, 64)
	shopGroupId, _ := strconv.ParseInt(productPriorityImportList[0].ShopGroupId, 10, 64)
	var ruleType entity.DefaultRuleType
	switch productPriorityImportList[0].RuleType {
	case RuleTypePriority:
		ruleType = entity.Priority
	case RuleTypeWeightage:
		ruleType = entity.Weightage
	}
	effectiveTime, _ := timeutil.ParseDatetimeStr(productPriorityImportList[0].EffectiveTime)
	var componentPrioritiesList []*entity.ComponentPriority
	var priority uint32 = 1
	for _, productPriority := range productPriorityImportList {
		fulfillmentProductId, _ := strconv.ParseInt(productPriority.FulfillmentProductId, 10, 64)
		var status entity.ComponentPriorityStatus
		switch productPriority.ToggleStatus {
		case ToggleStatusClose:
			status = entity.Closed
		case ToggleStatusOpen:
			status = entity.Open
		}
		componentPriorities := entity.ComponentPriority{
			ProductID: fulfillmentProductId,
			Name:      "",
			Status:    status,
		}
		if status == entity.Closed {
			componentPriorities.Priority = 0
			componentPriorities.Weightage = 0
		} else {
			value, _ := strconv.ParseInt(productPriority.Value, 10, 64)
			switch productPriority.RuleType {
			case RuleTypePriority:
				componentPriorities.Priority = uint32(value)
				componentPriorities.Weightage = 1
			case RuleTypeWeightage:
				componentPriorities.Weightage = uint32(value)
				componentPriorities.Priority = priority
				priority++
			}
		}
		componentPrioritiesList = append(componentPrioritiesList, &componentPriorities)
	}
	componentPrioritiesBytes, err := json.Marshal(componentPrioritiesList)
	if err != nil {
		return nil, srerr.With(srerr.JsonErr, nil, err)
	}
	productPriorityTab := productpriority.LogisticProductPriorityTab{
		MaskProductID:       maskingProductId,
		ShopGroupID:         shopGroupId,
		RuleType:            ruleType,
		ComponentPriorities: componentPrioritiesBytes,
		Operator:            operator,
		Ctime:               currentTime,
		Mtime:               currentTime,
		ConfigStatus:        int32(entity.Upcoming),
		EffectiveStartTime:  int(effectiveTime.Unix()),
	}
	return &productPriorityTab, nil
}

func formatRedisLockKey(maskingProductId int64) string {
	return fmt.Sprintf(ProductPriorityEditLockPrefix, maskingProductId)
}

func convertToProductPriorityExport(productPriorityTabList []*productpriority.LogisticProductPriorityTab, shopGroupInfoMap map[string]*lpsclient.GetClientGroupInfo) ([]*ProductPriorityExport, *srerr.Error) {
	var productPriorityExportList []*ProductPriorityExport
	for _, productPriorityTab := range productPriorityTabList {
		var ruleType string
		if productPriorityTab.RuleType == entity.Priority {
			ruleType = RuleTypePriority
		}
		if productPriorityTab.RuleType == entity.Weightage {
			ruleType = RuleTypeWeightage
		}
		// 当没有fulfillment_product时赋值基础数据就就不用往下走
		if len(productPriorityTab.ComponentPriorities) < 1 {
			productPriorityExport := ProductPriorityExport{
				MaskingProduct: buildExportProductInfo(productPriorityTab.MaskProductID),
				ShopGroupId:    buildExportShopGroupInfo(common.InterfaceToStr(productPriorityTab.ShopGroupID), shopGroupInfoMap),
				ForecastRuleId: common.InterfaceToStr(productPriorityTab.ForecastTaskId),
				RuleType:       ruleType,
				Status:         entity.PriorityConfigStatusType(productPriorityTab.ConfigStatus).String(),
				EffectiveTime:  buildEffectiveTime(productPriorityTab),
				LastOperator:   productPriorityTab.Operator,
				LastUpdated:    timeutil.FormatLocalTimestamp(productPriorityTab.Mtime),
			}
			productPriorityExportList = append(productPriorityExportList, &productPriorityExport)
			continue
		}
		var componentPrioritiesList []*entity.ComponentPriority
		err := json.Unmarshal(productPriorityTab.ComponentPriorities, &componentPrioritiesList)
		if err != nil {
			return nil, srerr.With(srerr.JsonErr, "", err)
		}
		for _, componentPriorities := range componentPrioritiesList {
			var toggleStatus string
			if componentPriorities.Status == entity.Closed {
				toggleStatus = ToggleStatusClose
			}
			if componentPriorities.Status == entity.Open {
				toggleStatus = ToggleStatusOpen
			}
			var value string
			if productPriorityTab.RuleType == entity.Priority {
				value = common.InterfaceToStr(componentPriorities.Priority)
			}
			if productPriorityTab.RuleType == entity.Weightage {
				value = common.InterfaceToStr(componentPriorities.Weightage)
			}
			productPriorityExport := ProductPriorityExport{
				MaskingProduct:     buildExportProductInfo(productPriorityTab.MaskProductID),
				ShopGroupId:        buildExportShopGroupInfo(common.InterfaceToStr(productPriorityTab.ShopGroupID), shopGroupInfoMap),
				ForecastRuleId:     common.InterfaceToStr(productPriorityTab.ForecastTaskId),
				RuleType:           ruleType,
				Status:             entity.PriorityConfigStatusType(productPriorityTab.ConfigStatus).String(),
				EffectiveTime:      buildEffectiveTime(productPriorityTab),
				LastOperator:       productPriorityTab.Operator,
				LastUpdated:        timeutil.FormatLocalTimestamp(productPriorityTab.Mtime),
				FulfillmentProduct: buildExportProductInfo(componentPriorities.ProductID),
				Value:              value,
				ToggleStatus:       toggleStatus,
			}
			productPriorityExportList = append(productPriorityExportList, &productPriorityExport)
		}
	}
	return productPriorityExportList, nil
}

func buildExportProductInfo(productId int64) string {
	value, _ := localcache.Get(context.Background(), constant.ProductBaseInfoList, common.InterfaceToStr(productId))
	product, _ := value.(*lpsclient.LogisticProductTab)
	if product == nil {
		return fmt.Sprintf("%v", productId)
	}
	return fmt.Sprintf("%v - %v", productId, product.SellerDisplayName)
}

func buildExportShopGroupInfo(shopGroupId string, shopGroupInfoMap map[string]*lpsclient.GetClientGroupInfo) string {
	if _, ok := shopGroupInfoMap[shopGroupId]; ok && shopGroupInfoMap[shopGroupId] != nil {
		return fmt.Sprintf("%v-%v", shopGroupId, shopGroupInfoMap[shopGroupId].ClientGroupName)
	}
	if shopGroupId == DefaultShopGroup {
		return fmt.Sprintf("%v-%v", shopGroupId, DefaultShopGroupName)
	}
	return shopGroupId
}

func buildEffectiveTime(logisticProductPriorityTab *productpriority.LogisticProductPriorityTab) string {
	effectiveTime := logisticProductPriorityTab.EffectiveStartTime
	if effectiveTime == 0 {
		effectiveTime = int(logisticProductPriorityTab.Ctime)
	}
	if effectiveTime == 0 {
		return ""
	}
	return timeutil.FormatLocalTimestamp(int64(effectiveTime))
}

// GetClientShopInfoMap 批量获取client shop
func (p *ProductPriorityService) GetClientShopInfoMap(ctx context.Context, shopGroupIdList []int64) (map[string]*lpsclient.GetClientGroupInfo, *srerr.Error) {
	clientGroupInfoMap := map[string]*lpsclient.GetClientGroupInfo{}
	if len(shopGroupIdList) < 1 {
		return clientGroupInfoMap, nil
	}
	getClientGroupInfoList, err := p.LpsApi.GetClientGroupInfoList(ctx, lpsclient.ClientTag3PLMasking, shopGroupIdList)
	if err != nil {
		return clientGroupInfoMap, err
	}
	for _, getClientGroupInfo := range getClientGroupInfoList {
		clientGroupInfoMap[getClientGroupInfo.ClientGroupID] = getClientGroupInfo
	}
	return clientGroupInfoMap, nil
}

func (p *ProductPriorityService) UploadProductPriorityExportTemplateFile(ctx context.Context, data []*ProductPriorityExportTemplate) (string, *srerr.Error) {
	// 生成excel文件
	newFile, fErr := makeProductPriorityExportTemplate(ctx, data)
	if fErr != nil {
		return "", srerr.With(srerr.MakeExcelError, nil, fErr)
	}
	b, wErr := newFile.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.MakeExcelError, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Product_Priority_Toggle_Template-%v%s", timeutil.GetCurrentTime(ctx).Unix(), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil
}

func makeProductPriorityExportTemplate(ctx context.Context, data []*ProductPriorityExportTemplate) (*excelize.File, *srerr.Error) {
	path, _ := os.Getwd()
	file, err := excelize.OpenFile(filepath.Join(path, "/resource/Product_PriorityToggle_Standard_Template.xlsx"))
	if err != nil {
		return nil, srerr.With(srerr.ParseExcelError, nil, err)
	}
	if len(file.GetSheetList()) < 1 {
		return nil, srerr.New(srerr.ParseExcelError, nil, "open excel file fail")
	}
	sheetName := file.GetSheetName(0)
	begin := 1
	for i := 0; i < len(data); i++ {
		if i == 0 {
			// shop group下拉框
			shopGroupRange := excelize.NewDataValidation(true)
			shopGroupRange.Sqref = "B2:B65535"
			err1 := setShopGroupDropList(file, shopGroupRange, data[i].ShopGroupId)
			if err != nil {
				return nil, err1
			}
			err = file.AddDataValidation(sheetName, shopGroupRange)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}

			// rule type下拉框
			ruleTypeRange := excelize.NewDataValidation(true)
			ruleTypeRange.Sqref = "C2:C65535"
			err = ruleTypeRange.SetDropList(data[i].RuleType)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}
			err = file.AddDataValidation(sheetName, ruleTypeRange)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}
			// toggle status下拉框
			toggleStatusRange := excelize.NewDataValidation(true)
			toggleStatusRange.Sqref = "G2:G65535"
			err = toggleStatusRange.SetDropList(data[i].ToggleStatus)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}
			err = file.AddDataValidation(sheetName, toggleStatusRange)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}
			// action mode下拉框
			actionModeRange := excelize.NewDataValidation(true)
			actionModeRange.Sqref = "H2:H65535"
			err = actionModeRange.SetDropList(data[i].ActionMode)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}
			err = file.AddDataValidation(sheetName, actionModeRange)
			if err != nil {
				return nil, srerr.With(srerr.ParseExcelError, "generate excel fail", err)
			}

		}
		_ = file.SetCellStr(sheetName, fileutil.GetAxis(begin+i, 0), data[i].MaskingProduct)
		_ = file.SetCellStr(sheetName, fileutil.GetAxis(begin+i, 3), data[i].EffectiveTime)
		_ = file.SetCellStr(sheetName, fileutil.GetAxis(begin+i, 4), data[i].FulfillmentProduct)
	}
	return file, nil
}

func setShopGroupDropList(file *excelize.File, shopGroupRange *excelize.DataValidation, shopGroupIds []string) *srerr.Error {
	for idx, shopGroupId := range shopGroupIds {
		err := file.SetCellStr(DefaultHiddenSheetName, fileutil.GetAxis(idx, 0), shopGroupId)
		if err != nil {
			return srerr.With(srerr.ParseExcelError, "generate excel fail", err)
		}
	}
	err := file.SetSheetVisible(DefaultHiddenSheetName, false)
	if err != nil {
		return srerr.With(srerr.ParseExcelError, "generate excel fail", err)
	}
	shopGroupRange.Formula1 = fmt.Sprintf("<formula1>=%s!$%s$1:$%s$%d</formula1>", DefaultHiddenSheetName, "A", "A", len(shopGroupIds))
	shopGroupRange.Type = "list"
	return nil
}

func (p *ProductPriorityService) convertToExportTemplate(ctx context.Context, maskingProduct *lpsclient.ProductDetailInfo, shopGroupInfoList []*lpsclient.ProductRelateShopGroup) ([]*ProductPriorityExportTemplate, *srerr.Error) {
	var exportTemplateList []*ProductPriorityExportTemplate
	//1. 查询shop group信息
	var shopGroupIdList []int64
	shopGroupIdMap := make(map[string]bool)
	for _, shopGroupInfo := range shopGroupInfoList {
		if _, ok := shopGroupIdMap[shopGroupInfo.ClientGroupId]; !ok {
			shopGroupId, err := strconv.ParseInt(shopGroupInfo.ClientGroupId, 10, 64)
			if err != nil {
				return nil, srerr.With(srerr.TypeConvertErr, "shop group convert fail", err)
			}
			shopGroupIdList = append(shopGroupIdList, shopGroupId)
			shopGroupIdMap[shopGroupInfo.ClientGroupId] = true
		}
	}
	shopGroupInfoMap, _ := p.GetClientShopInfoMap(ctx, shopGroupIdList)
	// 导出时默认加上default group
	if _, ok := shopGroupIdMap[DefaultShopGroup]; !ok {
		shopGroupIdList = append(shopGroupIdList, DefaultShopGroupId)
	}
	for _, fulfillmentProductId := range maskingProduct.GetComponentProduct().ComponentProducts {
		exportTemplate := ProductPriorityExportTemplate{
			MaskingProduct:     strconv.FormatInt(maskingProduct.ProductId, 10),
			ShopGroupId:        buildExportTemplateShopGroupInfo(shopGroupIdList, shopGroupInfoMap),
			RuleType:           []string{RuleTypeWeightage, RuleTypePriority},
			EffectiveTime:      timeutil.FormatDateTime(timeutil.GetLocalTime(ctx)),
			FulfillmentProduct: strconv.FormatInt(int64(fulfillmentProductId), 10),
			ToggleStatus:       []string{ToggleStatusOpen, ToggleStatusClose},
			ActionMode:         []string{strconv.FormatInt(ActionModeAdd, 10), strconv.FormatInt(ActionModeUpdate, 10)},
		}
		exportTemplateList = append(exportTemplateList, &exportTemplate)
	}
	return exportTemplateList, nil
}

func buildExportTemplateShopGroupInfo(shopGroupIdList []int64, shopGroupInfoMap map[string]*lpsclient.GetClientGroupInfo) []string {
	var shopGroupInfoList []string
	var shopGroupName string
	for _, shopGroupId := range shopGroupIdList {
		shopGroupIdStr := strconv.FormatInt(shopGroupId, 10)
		if _, ok := shopGroupInfoMap[shopGroupIdStr]; ok {
			shopGroupName = shopGroupInfoMap[shopGroupIdStr].ClientGroupName
		} else if shopGroupIdStr == DefaultShopGroup {
			shopGroupName = DefaultShopGroupName
		}
		shopGroupInfoList = append(shopGroupInfoList, fmt.Sprintf("%v%s%v", shopGroupId, ShopGroupSeparator, shopGroupName))
	}
	return shopGroupInfoList
}
