package ilh_forecast_task

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	ruleEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
)

// MockLaneService 模拟LaneService
type MockLaneService struct {
	mock.Mock
}

func (m *MockLaneService) GetLaneInfoByLaneCode(ctx context.Context, laneCode string) (*laneInfo, error) {
	args := m.Called(ctx, laneCode)
	return args.Get(0).(*laneInfo), args.Error(1)
}

// mockLaneInfo 模拟laneInfo结构
type laneInfo struct {
	cilhLineInfo      *lineInfo
	importILHLineInfo *lineInfo
}

func (l *laneInfo) GetCILHLineInfo() *lineInfo {
	return l.cilhLineInfo
}

func (l *laneInfo) GetImportILHLineInfo() *lineInfo {
	return l.importILHLineInfo
}

// mockLineInfo 模拟lineInfo结构
type lineInfo struct {
	LineID   string
	LineName string
}

// TestProcessLHResult 测试 processLHResult 方法
func TestProcessLHResult(t *testing.T) {
	tests := []struct {
		name             string
		results          []*persistent.ILHForecastTaskResultTab
		ilhLine          ilh_smart_routing.ILHLineInfo
		productNameMap   map[int64]string
		expectedProducts int               // 期望的产品数量
		expectedWeights  map[int][]float64 // productID -> [ReservedBSA, NonReservedBSA, AdhocWeight]
	}{
		{
			name: "测试新建LH结果",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					ReversedBSAWeight:    100.0,
					NonReversedBSAWeight: 200.0,
					AdhocWeight:          300.0,
				},
			},
			ilhLine: ilh_smart_routing.ILHLineInfo{
				LineID:   "LPH1102-MCT-(EX)PH(SLS)",
				LineName: "LPH1102-MCT-(EX)PH(SLS)",
			},
			productNameMap: map[int64]string{
				48021: "Express International",
			},
			expectedProducts: 1,
			expectedWeights: map[int][]float64{
				48021: {100.0, 200.0, 300.0},
			},
		},
		{
			name: "测试相同Product数据合并",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					ReversedBSAWeight:    100.0,
					NonReversedBSAWeight: 200.0,
					AdhocWeight:          300.0,
				},
				{
					MultiProductID:       48021, // 相同ProductID
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					ReversedBSAWeight:    50.0,
					NonReversedBSAWeight: 75.0,
					AdhocWeight:          125.0,
				},
			},
			ilhLine: ilh_smart_routing.ILHLineInfo{
				LineID:   "LPH1102-MCT-(EX)PH(SLS)",
				LineName: "LPH1102-MCT-(EX)PH(SLS)",
			},
			productNameMap: map[int64]string{
				48021: "Express International",
			},
			expectedProducts: 1, // 应该合并为1个产品
			expectedWeights: map[int][]float64{
				48021: {150.0, 275.0, 425.0}, // 合并后的重量
			},
		},
		{
			name: "测试不同Product数据不合并",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					ReversedBSAWeight:    100.0,
					NonReversedBSAWeight: 200.0,
					AdhocWeight:          300.0,
				},
				{
					MultiProductID:       48002, // 不同ProductID
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					ReversedBSAWeight:    50.0,
					NonReversedBSAWeight: 75.0,
					AdhocWeight:          125.0,
				},
			},
			ilhLine: ilh_smart_routing.ILHLineInfo{
				LineID:   "LPH1102-MCT-(EX)PH(SLS)",
				LineName: "LPH1102-MCT-(EX)PH(SLS)",
			},
			productNameMap: map[int64]string{
				48021: "Express International",
				48002: "Standard International",
			},
			expectedProducts: 2, // 应该有2个不同的产品
			expectedWeights: map[int][]float64{
				48021: {100.0, 200.0, 300.0},
				48002: {50.0, 75.0, 125.0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service := &ILHForecastTaskServiceImpl{}

			// 初始化测试数据
			resultData := &forecastResultData{
				lhResultMap: make(map[string]ilh_smart_routing.ForecastLHResult),
			}

			// 执行测试
			for _, result := range tt.results {
				service.processLHResult(result, tt.ilhLine, tt.productNameMap, resultData)
			}

			// 验证结果
			lhResultKey := generateLhResultKey(tt.ilhLine.LineID, tt.results[0].DestinationPort, tt.results[0].DgType)
			lhResult, exists := resultData.lhResultMap[lhResultKey]

			assert.True(t, exists, "LH结果应该存在")
			assert.Equal(t, tt.expectedProducts, len(lhResult.ProductInfos), "产品数量应该匹配")

			// 验证每个产品的重量
			for _, productInfo := range lhResult.ProductInfos {
				expectedWeight, ok := tt.expectedWeights[productInfo.ProductID]
				assert.True(t, ok, "产品ID %d 应该在期望结果中", productInfo.ProductID)

				assert.Equal(t, expectedWeight[0], productInfo.ReservedBSA, "ReservedBSA应该匹配")
				assert.Equal(t, expectedWeight[1], productInfo.NonReservedBSA, "NonReservedBSA应该匹配")
				assert.Equal(t, expectedWeight[2], productInfo.AdhocWeight, "AdhocWeight应该匹配")

				// 占比在processLHResult中应该为0，后续由calculateLHResultProportions计算
				assert.Equal(t, 0, productInfo.ReservedBSAProportion, "比例初始应该为0")
				assert.Equal(t, 0, productInfo.NonReservedBSAProportion, "比例初始应该为0")
				assert.Equal(t, 0, productInfo.AdhocWeightProportion, "比例初始应该为0")
			}
		})
	}
}

// TestCalculateLHResultProportions 测试 calculateLHResultProportions 方法
func TestCalculateLHResultProportions(t *testing.T) {
	tests := []struct {
		name                string
		lhResult            ilh_smart_routing.ForecastLHResult
		expectedProportions map[int][]int // productID -> [ReservedBSAProportion, NonReservedBSAProportion, AdhocWeightProportion]
	}{
		{
			name: "测试单个产品占比计算",
			lhResult: ilh_smart_routing.ForecastLHResult{
				ILHLine: ilh_smart_routing.ILHLineInfo{
					LineID:   "LPH1102-MCT-(EX)PH(SLS)",
					LineName: "LPH1102-MCT-(EX)PH(SLS)",
				},
				DestinationPort: "SPH1105",
				DGType:          ruleEntity.DGFlag(0),
				ProductInfos: []ilh_smart_routing.ForecastLHProductInfo{
					{
						ProductID:      48021,
						ProductName:    "Express International",
						ReservedBSA:    100.0,
						NonReservedBSA: 200.0,
						AdhocWeight:    300.0,
						// 总重量: 600.0
					},
				},
			},
			expectedProportions: map[int][]int{
				48021: {16, 33, 50}, // 100/600=16%, 200/600=33%, 300/600=50%
			},
		},
		{
			name: "测试多个产品占比计算",
			lhResult: ilh_smart_routing.ForecastLHResult{
				ILHLine: ilh_smart_routing.ILHLineInfo{
					LineID:   "LPH1102-MCT-(EX)PH(SLS)",
					LineName: "LPH1102-MCT-(EX)PH(SLS)",
				},
				DestinationPort: "SPH1105",
				DGType:          ruleEntity.DGFlag(0),
				ProductInfos: []ilh_smart_routing.ForecastLHProductInfo{
					{
						ProductID:      48021,
						ProductName:    "Express International",
						ReservedBSA:    200.0,
						NonReservedBSA: 0.0,
						AdhocWeight:    300.0,
						// 产品重量: 500.0
					},
					{
						ProductID:      48002,
						ProductName:    "Standard International",
						ReservedBSA:    0.0,
						NonReservedBSA: 300.0,
						AdhocWeight:    200.0,
						// 产品重量: 500.0
					},
					// 总重量: 1000.0
				},
			},
			expectedProportions: map[int][]int{
				48021: {20, 0, 30}, // 200/1000=20%, 0/1000=0%, 300/1000=30%
				48002: {0, 30, 20}, // 0/1000=0%, 300/1000=30%, 200/1000=20%
			},
		},
		{
			name: "测试零重量情况",
			lhResult: ilh_smart_routing.ForecastLHResult{
				ILHLine: ilh_smart_routing.ILHLineInfo{
					LineID:   "LPH1102-MCT-(EX)PH(SLS)",
					LineName: "LPH1102-MCT-(EX)PH(SLS)",
				},
				DestinationPort: "SPH1105",
				DGType:          ruleEntity.DGFlag(0),
				ProductInfos: []ilh_smart_routing.ForecastLHProductInfo{
					{
						ProductID:      48021,
						ProductName:    "Express International",
						ReservedBSA:    0.0,
						NonReservedBSA: 0.0,
						AdhocWeight:    0.0,
					},
				},
			},
			expectedProportions: map[int][]int{
				48021: {0, 0, 0}, // 总重量为0时，占比应该为0
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service := &ILHForecastTaskServiceImpl{}

			// 初始化测试数据
			resultData := &forecastResultData{
				lhResultMap: make(map[string]ilh_smart_routing.ForecastLHResult),
			}

			// 设置初始数据
			lhResultKey := generateLhResultKey(tt.lhResult.ILHLine.LineID, tt.lhResult.DestinationPort, tt.lhResult.DGType)
			resultData.lhResultMap[lhResultKey] = tt.lhResult

			// 执行测试
			service.calculateLHResultProportions(resultData)

			// 验证结果
			updatedLhResult := resultData.lhResultMap[lhResultKey]

			for _, productInfo := range updatedLhResult.ProductInfos {
				expectedProportion, ok := tt.expectedProportions[productInfo.ProductID]
				assert.True(t, ok, "产品ID %d 应该在期望结果中", productInfo.ProductID)

				assert.Equal(t, expectedProportion[0], productInfo.ReservedBSAProportion,
					"产品 %d 的 ReservedBSAProportion 应该匹配", productInfo.ProductID)
				assert.Equal(t, expectedProportion[1], productInfo.NonReservedBSAProportion,
					"产品 %d 的 NonReservedBSAProportion 应该匹配", productInfo.ProductID)
				assert.Equal(t, expectedProportion[2], productInfo.AdhocWeightProportion,
					"产品 %d 的 AdhocWeightProportion 应该匹配", productInfo.ProductID)
			}
		})
	}
}

// TestProcessBlockedILHResult 测试 processBlockedILHResult 方法
func TestProcessBlockedILHResult(t *testing.T) {
	tests := []struct {
		name             string
		results          []*persistent.ILHForecastTaskResultTab
		productNameMap   map[int64]string
		expectedProducts int
		expectedWeights  map[int]float64 // productID -> total AdhocWeight
	}{
		{
			name: "测试单个Block记录",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					LaneCode:             "BLOCKED",
					ReversedBSAWeight:    0.0,
					NonReversedBSAWeight: 0.0,
					AdhocWeight:          500.0,
				},
			},
			productNameMap: map[int64]string{
				48021: "Express International",
			},
			expectedProducts: 1,
			expectedWeights: map[int]float64{
				48021: 500.0,
			},
		},
		{
			name: "测试相同Product的Block记录合并",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					LaneCode:             "BLOCKED",
					ReversedBSAWeight:    0.0,
					NonReversedBSAWeight: 0.0,
					AdhocWeight:          300.0,
				},
				{
					MultiProductID:       48021, // 相同ProductID
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					LaneCode:             "BLOCKED",
					ReversedBSAWeight:    0.0,
					NonReversedBSAWeight: 0.0,
					AdhocWeight:          200.0,
				},
			},
			productNameMap: map[int64]string{
				48021: "Express International",
			},
			expectedProducts: 1, // 应该合并为1个产品
			expectedWeights: map[int]float64{
				48021: 500.0, // 300.0 + 200.0
			},
		},
		{
			name: "测试不同Product的Block记录",
			results: []*persistent.ILHForecastTaskResultTab{
				{
					MultiProductID:       48021,
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					LaneCode:             "BLOCKED",
					ReversedBSAWeight:    0.0,
					NonReversedBSAWeight: 0.0,
					AdhocWeight:          300.0,
				},
				{
					MultiProductID:       48002, // 不同ProductID
					DestinationPort:      "SPH1105",
					DgType:               ruleEntity.DGFlag(0),
					LaneCode:             "BLOCKED",
					ReversedBSAWeight:    0.0,
					NonReversedBSAWeight: 0.0,
					AdhocWeight:          200.0,
				},
			},
			productNameMap: map[int64]string{
				48021: "Express International",
				48002: "Standard International",
			},
			expectedProducts: 2, // 应该有2个产品
			expectedWeights: map[int]float64{
				48021: 300.0,
				48002: 200.0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			service := &ILHForecastTaskServiceImpl{}

			// 初始化测试数据
			resultData := &forecastResultData{
				lhResultMap: make(map[string]ilh_smart_routing.ForecastLHResult),
			}

			// 执行测试
			for _, result := range tt.results {
				service.processBlockedILHResult(result, tt.productNameMap, resultData)
			}

			// 验证结果 - Block记录使用LH结果的key格式
			blockResultKey := generateLhResultKey(BlockedLineID, tt.results[0].DestinationPort, tt.results[0].DgType)
			blockResult, exists := resultData.lhResultMap[blockResultKey]

			assert.True(t, exists, "Block结果应该存在")
			assert.Equal(t, tt.expectedProducts, len(blockResult.ProductInfos), "产品数量应该匹配")

			// 验证Block结果的基本信息
			assert.Equal(t, BlockedLineID, blockResult.ILHLine.LineID, "LineID应该为BLOCKED")
			assert.Equal(t, BlockedLineID, blockResult.ILHLine.LineName, "LineName应该为BLOCKED")

			// 验证每个产品的重量
			for _, productInfo := range blockResult.ProductInfos {
				expectedWeight, ok := tt.expectedWeights[productInfo.ProductID]
				assert.True(t, ok, "产品ID %d 应该在期望结果中", productInfo.ProductID)

				assert.Equal(t, 0.0, productInfo.ReservedBSA, "Block记录的ReservedBSA应该为0")
				assert.Equal(t, 0.0, productInfo.NonReservedBSA, "Block记录的NonReservedBSA应该为0")
				assert.Equal(t, expectedWeight, productInfo.AdhocWeight, "AdhocWeight应该匹配")

				// 占比在processBlockedILHResult中应该为0，后续由calculateLHResultProportions计算
				assert.Equal(t, 0, productInfo.ReservedBSAProportion, "比例初始应该为0")
				assert.Equal(t, 0, productInfo.NonReservedBSAProportion, "比例初始应该为0")
				assert.Equal(t, 0, productInfo.AdhocWeightProportion, "比例初始应该为0")
			}
		})
	}
}

// TestIntegrationLHResultProcessing 测试整合流程
func TestIntegrationLHResultProcessing(t *testing.T) {
	// 创建服务实例
	service := &ILHForecastTaskServiceImpl{}

	// 准备测试数据：包含正常记录和Block记录
	normalResults := []*persistent.ILHForecastTaskResultTab{
		{
			MultiProductID:       48021,
			DestinationPort:      "SPH1105",
			DgType:               ruleEntity.DGFlag(0),
			ReversedBSAWeight:    100.0,
			NonReversedBSAWeight: 200.0,
			AdhocWeight:          300.0,
		},
		{
			MultiProductID:       48021, // 相同Product，应该合并
			DestinationPort:      "SPH1105",
			DgType:               ruleEntity.DGFlag(0),
			ReversedBSAWeight:    50.0,
			NonReversedBSAWeight: 100.0,
			AdhocWeight:          150.0,
		},
	}

	blockedResults := []*persistent.ILHForecastTaskResultTab{
		{
			MultiProductID:       48021,
			DestinationPort:      "SPH1105",
			DgType:               ruleEntity.DGFlag(0),
			LaneCode:             "BLOCKED",
			ReversedBSAWeight:    0.0,
			NonReversedBSAWeight: 0.0,
			AdhocWeight:          400.0,
		},
	}

	ilhLine := ilh_smart_routing.ILHLineInfo{
		LineID:   "LPH1102-MCT-(EX)PH(SLS)",
		LineName: "LPH1102-MCT-(EX)PH(SLS)",
	}

	productNameMap := map[int64]string{
		48021: "Express International",
	}

	// 初始化测试数据
	resultData := &forecastResultData{
		lhResultMap: make(map[string]ilh_smart_routing.ForecastLHResult),
	}

	// 步骤1: 处理正常记录
	for _, result := range normalResults {
		service.processLHResult(result, ilhLine, productNameMap, resultData)
	}

	// 步骤2: 处理Block记录
	for _, result := range blockedResults {
		service.processBlockedILHResult(result, productNameMap, resultData)
	}

	// 步骤3: 计算占比
	service.calculateLHResultProportions(resultData)

	// 验证正常记录结果
	normalResultKey := generateLhResultKey(ilhLine.LineID, "SPH1105", ruleEntity.DGFlag(0))
	normalResult, exists := resultData.lhResultMap[normalResultKey]
	assert.True(t, exists, "正常记录应该存在")
	assert.Equal(t, 1, len(normalResult.ProductInfos), "应该只有1个产品（合并后）")

	normalProductInfo := normalResult.ProductInfos[0]
	assert.Equal(t, 48021, normalProductInfo.ProductID)
	assert.Equal(t, 150.0, normalProductInfo.ReservedBSA, "ReservedBSA应该合并")       // 100 + 50
	assert.Equal(t, 300.0, normalProductInfo.NonReservedBSA, "NonReservedBSA应该合并") // 200 + 100
	assert.Equal(t, 450.0, normalProductInfo.AdhocWeight, "AdhocWeight应该合并")       // 300 + 150

	// 验证占比计算：总重量 = 150 + 300 + 450 = 900
	assert.Equal(t, 16, normalProductInfo.ReservedBSAProportion, "ReservedBSA占比")       // 150/900 = 16%
	assert.Equal(t, 33, normalProductInfo.NonReservedBSAProportion, "NonReservedBSA占比") // 300/900 = 33%
	assert.Equal(t, 50, normalProductInfo.AdhocWeightProportion, "AdhocWeight占比")       // 450/900 = 50%

	// 验证Block记录结果
	blockResultKey := generateLhResultKey(BlockedLineID, "SPH1105", ruleEntity.DGFlag(0))
	blockResult, exists := resultData.lhResultMap[blockResultKey]
	assert.True(t, exists, "Block记录应该存在")
	assert.Equal(t, 1, len(blockResult.ProductInfos), "应该只有1个产品")

	blockProductInfo := blockResult.ProductInfos[0]
	assert.Equal(t, 48021, blockProductInfo.ProductID)
	assert.Equal(t, 0.0, blockProductInfo.ReservedBSA)
	assert.Equal(t, 0.0, blockProductInfo.NonReservedBSA)
	assert.Equal(t, 400.0, blockProductInfo.AdhocWeight)

	// Block记录的占比计算：只有一个产品，AdhocWeight占比应该是100%
	assert.Equal(t, 0, blockProductInfo.ReservedBSAProportion)
	assert.Equal(t, 0, blockProductInfo.NonReservedBSAProportion)
	assert.Equal(t, 100, blockProductInfo.AdhocWeightProportion) // 400/400 = 100%
}

// Mock结构，如果需要的话可以用于更复杂的测试
type MockLHForecastTaskService struct {
	mock.Mock
}

// 辅助函数：验证占比计算的精度
func assertProportionWithTolerance(t *testing.T, expected, actual int, tolerance int, msgAndArgs ...interface{}) {
	diff := expected - actual
	if diff < 0 {
		diff = -diff
	}
	assert.LessOrEqual(t, diff, tolerance, msgAndArgs...)
}
