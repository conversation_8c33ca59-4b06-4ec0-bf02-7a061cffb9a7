package sync_data

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/sync_data_schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/google/uuid"
	"time"
)

type SyncDataService interface {
	SyncMaskingForecastOrders(ctx context.Context, req sync_data_schema.SyncMfOrdersReq) *srerr.Error
	GetSyncData(ctx context.Context, req sync_data_schema.GetSyncDataReq) *srerr.Error
	TestBalance(ctx context.Context, req sync_data_schema.TestBalanceReq) *srerr.Error
}

type SyncDataImpl struct {
	AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo
}

func NewSyncDataImpl(AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo) *SyncDataImpl {
	return &SyncDataImpl{
		AllocateOrderDataRepo: AllocateOrderDataRepo,
	}
}

func (s *SyncDataImpl) SyncMaskingForecastOrders(ctx context.Context, req sync_data_schema.SyncMfOrdersReq) *srerr.Error {
	//1.校验入参
	if vErr := validateTime(req.StartDate, req.EndDate); vErr != nil {
		logger.CtxLogErrorf(ctx, "SyncMaskingForecastOrders|validate time err:%v", vErr)
		return vErr
	}
	//2.获取起止时间：左区间为start date，右区间为end date多一天，比如2023-04-01，2023-04-02，
	//我们希望检索04-01和04-02两天的数据，所以end row key需要是04-03的0点0分0秒
	startTime := timeutil.GetStartTimeByString(req.StartDate)
	tempTime := timeutil.GetStartTimeByString(req.EndDate)
	endTime := timeutil.GetNextDayStartTimeByTimeStamp(tempTime, nextOneDay)
	//3.获取所有的mask product id
	//从缓存获取所有product 信息
	productInfoMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	//获取所有 mask product id
	if len(productInfoMap) == 0 {
		return srerr.New(srerr.LpsError, nil, "get empty product list")
	}
	maskProductIds := make([]uint64, 0)

	for _, productInterface := range productInfoMap {
		//convert product base info
		productBaseInfo, ok := productInterface.(*lpsclient.LogisticProductTab)
		if !ok {
			logger.CtxLogErrorf(ctx, "product info:%+v, convert product base info err", productInterface)
		} else {
			//add mask product id
			if productBaseInfo.IsMaskingProduct {
				maskProductIds = append(maskProductIds, uint64(productBaseInfo.ProductId))
			}
		}
	}
	//4.初始化client
	client := gohbase.NewClient(req.HostUrl, gohbase.EffectiveUser(req.UserName), gohbase.ZookeeperRoot(req.ZkRoot), gohbase.Auth(req.AuthMethod), gohbase.Password(req.PassWord))
	//5.根据start time, end time获取hbase数据
	for _, maskProductId := range maskProductIds {
		hbResults, _ := s.AllocateOrderDataRepo.GetAllocateOrderDataFromHbaseByKey(ctx, maskProductId, startTime, endTime, 1000, false, false)
		//5.将获取到的数据写入uat hbase中（支持选择写入uat或test，在request中指定对应的table等hbase连接信息
		for result := range hbResults {
			cell := result.Cells[0]
			rowKey := cell.Row //从result里获取row key
			//todo:local test,打印row key，方便验证
			println(string(rowKey))
			//准备数据
			value := prepare(string(cell.Qualifier[:]), cell.Value)
			//写入non-live hbase中
			putRequest, err := gohbase.NewPutStr(ctx, req.TableName, string(rowKey), value)
			if err != nil {
				logger.CtxLogErrorf(ctx, "SyncMaskingForecastOrders|hrpc.NewPutStr: %s", err.Error())
				return srerr.With(srerr.DataErr, nil, err)
			}

			_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

			tableName := str.GenKey(":", string(putRequest.Table()), "create")
			_, err = client.Put(putRequest)
			if err != nil {
				logger.CtxLogErrorf(ctx, "SyncMaskingForecastOrders| put request err:%v", err)
				endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
				continue
			}
			endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
		}
	}
	return nil
}

func (s *SyncDataImpl) GetSyncData(ctx context.Context, req sync_data_schema.GetSyncDataReq) *srerr.Error {
	//4.初始化client
	client := gohbase.NewClient(req.HostUrl, gohbase.EffectiveUser(req.UserName), gohbase.ZookeeperRoot(req.ZkRoot), gohbase.Auth(req.AuthMethod), gohbase.Password(req.PassWord))
	var err error
	defer func() {
		if errs := recover(); errs != nil {
			switch fmt.Sprintf("%v", errs) {
			case "runtime error: index out of range":
				err = errors.New("NoSuchRowKeyOrQualifierException")
			case "runtime error: invalid memory address or nil pointer dereference":
				err = errors.New("NoSuchColFamilyException")
			default:
				err = fmt.Errorf("%v", errs)
			}
		}
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetSyncData| defer err:%v", err)
		}
	}()
	//遍历所有的row key，根据row key从hbase获取数据
	for _, rowKey := range req.RowKeyList {
		//生成请求
		getRequest, err := gohbase.NewGetStr(ctx, req.TableName, rowKey)
		if err != nil {
			logger.CtxLogErrorf(ctx, "hrpc.NewGetStr: %s", err.Error())
			continue
		}
		//初始化cat上报
		_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

		//指定table，并使用hbase client根据row key获取数据
		tableName := str.GenKey(":", string(getRequest.Table()), "get")
		result, err := client.Get(getRequest)
		if err != nil {
			endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
			continue
		}
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
		//验证获取到的数据
		for _, cell := range result.Cells {
			println(string(cell.Qualifier))
			println(string(cell.Row))
			logger.CtxLogInfof(ctx, "GetSyncData| req row key:%v, result row key:%v, result qualifier:%v", rowKey, string(cell.Row), string(cell.Qualifier))
			myBytes, _ := zip.ZSTDDecompress(cell.Value)
			println(string(myBytes))
		}
	}
	return nil
}

// masking forecast测试平衡性，按照规则设定row key，并插入non-live表中，查看数据分布是否均匀
func (s *SyncDataImpl) TestBalance(ctx context.Context, req sync_data_schema.TestBalanceReq) *srerr.Error {
	//1.校验入参
	if vErr := validateTime(req.StartDate, req.EndDate); vErr != nil {
		logger.CtxLogErrorf(ctx, "TestBalance|validate time err:%v", vErr)
		return vErr
	}
	//2.获取起止时间：左区间为start date，右区间为end date多一天，比如2023-04-01，2023-04-02，
	//我们希望检索04-01和04-02两天的数据，所以end row key需要是04-03的0点0分0秒
	startTime := timeutil.GetStartTimeByString(req.StartDate)
	tempTime := timeutil.GetStartTimeByString(req.EndDate)
	endTime := timeutil.GetNextDayStartTimeByTimeStamp(tempTime, nextOneDay)

	//3.初始化client
	client := gohbase.NewClient(req.HostUrl, gohbase.EffectiveUser(req.UserName), gohbase.ZookeeperRoot(req.ZkRoot), gohbase.Auth(req.AuthMethod), gohbase.Password(req.PassWord))

	//4.检索hbase数据, 并获取其中一个
	hbResults, _ := s.AllocateOrderDataRepo.GetAllocateOrderDataFromHbaseByKey(ctx, req.MaskingProductId, startTime, endTime, 1000, false, false)
	var sameResult *gohbase.Result
	for result := range hbResults {
		sameResult = result
		break
	}
	//5.循环写入，通过request控制结束条件
	loopStartTime := time.Now().Unix() // nolint
	for {
		if time.Now().Unix()-loopStartTime >= req.LoopSeconds { // nolint
			break
		}
		for _, cell := range sameResult.Cells {
			dataMap := prepare(string(cell.Qualifier), cell.Value)
			//重组row key
			timeMill := time.Now().UnixNano() / 1000000 // nolint
			salt := (timeMill / 1000) % 1000            //salt规则：salt = intDiv(request_time_mill, 1000) % 1000  ps:intDiv为整除
			uniqId := uuid.New().String()               // nolint
			newRow := fmt.Sprintf("%03d_%d_%s", salt, req.MaskingProductId, uniqId)
			//test
			println(newRow)
			//写入non-live hbase中
			putRequest, err := gohbase.NewPutStr(ctx, req.TableName, newRow, dataMap)
			if err != nil {
				logger.CtxLogErrorf(ctx, "SyncMaskingForecastOrders|hrpc.NewPutStr: %s", err.Error())
				return srerr.With(srerr.DataErr, nil, err)
			}

			_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

			tableName := str.GenKey(":", string(putRequest.Table()), "create")
			_, err = client.Put(putRequest)
			if err != nil {
				logger.CtxLogErrorf(ctx, "SyncMaskingForecastOrders| put request err:%v", err)
				endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
				continue
			}
			endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
		}
	}

	return nil
}

func validateTime(startDate, endDate string) *srerr.Error {
	start, err := timeutil.ParseLocalTime(timeutil.DateFormat, startDate)
	if err != nil {
		return srerr.New(srerr.ParamErr, nil, "invalid start_date: %s, layout: %s", startDate, timeutil.DateFormat)
	}
	end, err := timeutil.ParseLocalTime(timeutil.DateFormat, startDate)
	if err != nil {
		return srerr.New(srerr.ParamErr, nil, "invalid end_date: %s, layout: %s", endDate, timeutil.DateFormat)
	}
	if end.Sub(start) < 0 {
		return srerr.New(srerr.ParamErr, nil, "The end_date must be later than the start_date.")
	}
	return nil
}

func prepare(key string, value []byte) map[string]map[string][]byte {
	tempResult := map[string][]byte{
		key: value,
	}
	return map[string]map[string][]byte{
		"c": tempResult,
	}
}
