package allocation

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type SoftRuleService struct {
	VolumeCounter        volumecounter.MaskVolumeCounter
	RateClient           chargeclient.ChargeApi
	ShopWhitelistService whitelist.ShopWhitelistService
}

func NewSoftRuleService(
	counter volumecounter.MaskVolumeCounter,
	rateClient chargeclient.ChargeApi,
	shopWhitelistService whitelist.ShopWhitelistService,
) *SoftRuleService {
	return &SoftRuleService{
		VolumeCounter:        counter,
		RateClient:           rateClient,
		ShopWhitelistService: shopWhitelistService,
	}
}

// @core
func (s *SoftRuleService) Allocate(
	ctx context.Context, maskProductID int64, productIDs []int64, r *rule.MaskRule, locationVolumes map[int64]rulevolume.ProductLocVolumes,
	defaultRuleType entity.DefaultRuleType, productPriorities, productWeightages map[int64]int32, orderInfo *pb.MaskingOrderInfo,
	shippingFeeConfigs []*allocation.ShippingFeeConfig, businessType int8, productGroupCodeMapping map[int64]string,
	attrMap map[int64]*parcel_type_definition.ParcelTypeAttr,
) (int64, *srerr.Error) {
	productID, _, err := s.SoftRuleCriteria(
		ctx, maskProductID, productIDs, r, locationVolumes, defaultRuleType, productPriorities, productWeightages,
		orderInfo, shippingFeeConfigs, rule_mode.MplOrderRule, AllocateRequest, businessType, productGroupCodeMapping, attrMap)
	if err == nil {
		// 更新规则内的batch volume 计数
		// 注意：在应用层要更新当天的总计数，因为如果硬性校验得到了唯一的fulfillment product，不会走这里的allocate
		if iErr := s.VolumeCounter.IncrVolumeForRule(ctx, productID, r.Id, r.GetBatchVolume()); iErr != nil {
			logger.CtxLogErrorf(ctx, "masking allocate IncrVolumeForRule error: %s", iErr.Error())
		}
	}
	return productID, err
}

func (s *SoftRuleService) SoftRuleCriteria(
	ctx context.Context, maskProductID int64, productIDs []int64, r *rule.MaskRule, locationVolumes map[int64]rulevolume.ProductLocVolumes,
	defaultRuleType entity.DefaultRuleType, productPriorities, productWeightages map[int64]int32, orderInfo *pb.MaskingOrderInfo,
	shippingFeeConfigs []*allocation.ShippingFeeConfig, rm rule_mode.RuleMode, reqType int, businessType int8,
	productGroupCodeMapping map[int64]string, attrMap map[int64]*parcel_type_definition.ParcelTypeAttr,
) (int64, []*pb.SortedResult, *srerr.Error) {
	// 预测任务不需要执行提前计算运费，1. forecast task不需要保存运费，2. forecast task qps较高，会影响finance服务性能，故排除forecast task
	preCalcFee := s.preCalculateShippingFee(ctx, maskProductID, productIDs, orderInfo, businessType)

	return s.SoftRule(ctx, maskProductID, productIDs, r, locationVolumes, defaultRuleType, productPriorities,
		productWeightages, orderInfo, shippingFeeConfigs, rm, reqType, preCalcFee, productGroupCodeMapping, businessType, attrMap)
}

// @core
func (s *SoftRuleService) SoftRule(
	ctx context.Context, maskProductID int64, productIDs []int64, r *rule.MaskRule, locationVolumes map[int64]rulevolume.ProductLocVolumes,
	defaultRuleType entity.DefaultRuleType, productPriorities, productWeightages map[int64]int32, orderInfo *pb.MaskingOrderInfo,
	shippingFeeConfigs []*allocation.ShippingFeeConfig, rm rule_mode.RuleMode, reqType int, preCalcFee *chargeentity.BatchAllocationESFResp,
	productGroupCodeMapping map[int64]string, businessType int8, attrMap map[int64]*parcel_type_definition.ParcelTypeAttr,
) (int64, []*pb.SortedResult, *srerr.Error) {
	task := newTask(
		maskProductID, r, locationVolumes, s.VolumeCounter, defaultRuleType, productPriorities, productWeightages, s, rm,
		reqType, preCalcFee, productGroupCodeMapping, businessType, attrMap, s.ShopWhitelistService,
	)
	productID, err := task.GetResult(ctx, productIDs, orderInfo, maskProductID, shippingFeeConfigs)
	if err != nil {
		return productID, task.SortedRes, err
	}
	return productID, task.SortedRes, nil
}

/*
判断是否提前计算运费，两个开关一个总开关，一个mask product维度开关
*/
func (s *SoftRuleService) preCalculateShippingFee(ctx context.Context, maskProductId int64, productIDs []int64, info *pb.MaskingOrderInfo, businessType int8) *chargeentity.BatchAllocationESFResp {
	// 对返回结果做个初始化，防止外部调用空指针异常
	initCalcFeeResult := chargeentity.BatchAllocationESFResp{}
	//1. 只有allocate、estimate两个业务场景需要提前计算运费
	if businessType != constant.AllocateMasking && businessType != constant.EstimateMasking {
		monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.NonAllocateScene, "not live masking, no need to pre calc fee")
		return &initCalcFeeResult
	}

	//2. 判断总开关和mask维度的提前计算运费开关是否发打开
	if !configutil.IsOpenMaskingPreCalcFeeSwitch(ctx, maskProductId) {
		monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingGlobalSwitchClose, "global switch is close, no need to pre calc fee")
		return &initCalcFeeResult
	}

	//3. 提前计算运费
	preCalcFeeResult, err := s.calculateShippingFee(ctx, productIDs, info)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingCalcFeeError, fmt.Sprintf("pre calc masking fee error, err=%v", err))
		logger.CtxLogErrorf(ctx, "pre calc masking fee error | err: %v", err)
		return &initCalcFeeResult
	}

	//4. 保存运费信息到log
	allocationLog := GetAllocationLogFromContext(ctx)
	if allocationLog != nil {
		allocationLog.SetPreCalculate(preCalcFeeResult)
	}
	logger.CtxLogInfof(ctx, "pre calc masking fee success, masking_product=%v, productIds = %v, pre fee result=%v", maskProductId, productIDs, preCalcFeeResult)
	monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingPreCalcFeeSuccess, fmt.Sprintf("pre calc masking fee success, masking_product=%v, productIds = %v, pre fee result=%v", maskProductId, productIDs, preCalcFeeResult))
	//5. 解析提前计算的运费结果
	return preCalcFeeResult
}

func (s *SoftRuleService) calculateShippingFee(ctx context.Context, productIDs []int64, info *pb.MaskingOrderInfo) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
	rateReq := chargeentity.BatchAllocationESFReq{
		Token: constant.FreightApiTokens[strings.ToLower(envvar.GetEnvWithCtx(ctx))],
	}
	var rateResp *chargeentity.BatchAllocationESFResp
	var err *srerr.Error
	commonReqDataItem := GetCommonReqDataItemForOrder(info)
	//计算mask product下面子product运费
	for _, productID := range productIDs {
		reqDataItem := commonReqDataItem
		reqDataItem.ProductID = productID
		reqDataItem.UniqueId = fmt.Sprintf(UniqPattern, productID)
		reqDataItem.Timestamp = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
		rateReq.Data = append(rateReq.Data, &reqDataItem)
	}

	rateResp, err = s.RateClient.BatchAllocatingESFWithGrpc(ctx, &rateReq, chargeentity.SceneAllocating)
	if err != nil {
		return nil, err
	}
	return rateResp, nil
}

func GetCommonReqDataItemForOrder(info *pb.MaskingOrderInfo) chargeentity.BatchAllocationESFReqDataItem {
	commonReqDataItem := chargeentity.BatchAllocationESFReqDataItem{
		CODAmount:           info.CodAmount,
		COGS:                info.Cogs,
		WMSFlag:             bool2int(info.GetIsWms()),
		PickupLocationIDs:   nil,
		DeliveryLocationIDs: nil,
		SellerTaxNumber:     info.GetSellerTaxNumber(),
		StateRegistration:   info.GetStateRegistration(),
	}

	if info.PickupAddress != nil {
		commonReqDataItem.PickupPostcode = info.PickupAddress.PostalCode
		commonReqDataItem.PickupLongitude = info.PickupAddress.Longitude
		commonReqDataItem.PickupLatitude = info.PickupAddress.Latitude
		commonReqDataItem.PickupLocationIDs = sliceInt64ToInt(info.PickupAddress.GetLocationIds())
	}

	if info.DeliveryAddress != nil {
		commonReqDataItem.DeliveryPostcode = info.DeliveryAddress.PostalCode
		commonReqDataItem.DeliverLongitude = info.DeliveryAddress.Longitude
		commonReqDataItem.DeliverLatitude = info.DeliveryAddress.Latitude
		commonReqDataItem.DeliveryLocationIDs = sliceInt64ToInt(info.DeliveryAddress.GetLocationIds())
	}

	for _, item := range info.CheckoutItems {
		commonReqDataItem.SkuInfo = append(commonReqDataItem.SkuInfo, chargeentity.SkuInfo{
			ItemID:     item.GetItemId(),
			ModelID:    item.GetModelId(),
			CategoryID: item.CategoryId,
			Weight:     item.Weight,
			Quantity:   int(item.GetQuantity()),
			Length:     item.Length,
			Width:      item.Width,
			Height:     item.Height,
		})
	}
	// if order is cod order, get allocating shipping fee must assign add_cod_fee to 1
	if IsCodPayment(info) {
		commonReqDataItem.AddCodFee = 1
	}

	return commonReqDataItem
}

func IsCodPayment(p *pb.MaskingOrderInfo) bool {
	return strings.ToUpper(p.GetPaymentMethod()) == constant.PaymentMethodCod
}

func bool2int(x bool) int {
	if x {
		return 1
	} else {
		return 0
	}
}

func GetForecastCommonReqDataItemForOrder(info *pb.MaskingOrderInfo) chargeentity.BatchForecastAllocationESFReqDataItem {
	commonReqDataItem := chargeentity.BatchForecastAllocationESFReqDataItem{
		CODAmount:           info.CodAmount,
		COGS:                info.Cogs,
		WMSFlag:             bool2int(info.GetIsWms()),
		PickupLocationIDs:   nil,
		DeliveryLocationIDs: nil,
		SellerTaxNumber:     info.GetSellerTaxNumber(),
		StateRegistration:   info.GetStateRegistration(),
	}
	if info.PickupAddress != nil {
		commonReqDataItem.PickupPostcode = info.PickupAddress.PostalCode
		commonReqDataItem.PickupLongitude = info.PickupAddress.Longitude
		commonReqDataItem.PickupLatitude = info.PickupAddress.Latitude
		commonReqDataItem.PickupLocationIDs = sliceInt64ToInt(info.PickupAddress.GetLocationIds())
	}

	if info.DeliveryAddress != nil {
		commonReqDataItem.DeliveryPostcode = info.DeliveryAddress.PostalCode
		commonReqDataItem.DeliverLongitude = info.DeliveryAddress.Longitude
		commonReqDataItem.DeliverLatitude = info.DeliveryAddress.Latitude
		commonReqDataItem.DeliveryLocationIDs = sliceInt64ToInt(info.DeliveryAddress.GetLocationIds())
	}

	for _, item := range info.CheckoutItems {
		commonReqDataItem.SkuInfo = append(commonReqDataItem.SkuInfo, chargeentity.SkuInfo{
			ItemID:     item.GetItemId(),
			ModelID:    item.GetModelId(),
			CategoryID: item.CategoryId,
			Weight:     item.Weight,
			Quantity:   int(item.GetQuantity()),
			Length:     item.Length,
			Width:      item.Width,
			Height:     item.Height,
		})
	}

	return commonReqDataItem
}

func sliceInt64ToInt(data []int64) []int {
	res := make([]int, 0, len(data))
	for i := range data {
		res = append(res, int(data[i]))
	}
	return res
}
