package allocation

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_initSortedRes(t *testing.T) {
	type args struct {
		pl []int64
	}
	tests := []struct {
		name string
		args args
		want map[int64]int
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal case",
			args: args{
				pl: []int64{1, 2, 3, 4, 5},
			},
			want: map[int64]int{
				1: 0,
				2: 0,
				3: 0,
				4: 0,
				5: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, initSortedRes(tt.args.pl), "initSortedRes(%v)", tt.args.pl)
		})
	}
}

func Test_mapToSortedRes(t *testing.T) {
	type args struct {
		cntMap map[int64]int
	}
	tests := []struct {
		name string
		args args
		want []*pb.SortedResult
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal case",
			args: args{
				map[int64]int{
					1: 1,
					2: 2,
					3: 3,
					4: 4,
					5: 5,
					6: 5,
				},
			},
			want: []*pb.SortedResult{
				{
					Priority: proto.Int32(1),
					DetailList: []*pb.Detail{
						{
							ShippingChannelId: proto.Int64(5),
						},
						{
							ShippingChannelId: proto.Int64(6),
						},
					},
				},
				{
					Priority: proto.Int32(2),
					DetailList: []*pb.Detail{
						{
							ShippingChannelId: proto.Int64(4),
						},
					},
				},
				{
					Priority: proto.Int32(3),
					DetailList: []*pb.Detail{
						{
							ShippingChannelId: proto.Int64(3),
						},
					},
				},
				{
					Priority: proto.Int32(4),
					DetailList: []*pb.Detail{
						{
							ShippingChannelId: proto.Int64(2),
						},
					},
				},
				{
					Priority: proto.Int32(5),
					DetailList: []*pb.Detail{
						{
							ShippingChannelId: proto.Int64(1),
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common.AssertResult(t, tt.want, mapToSortedRes(tt.args.cntMap), nil, nil)
		})
	}
}
