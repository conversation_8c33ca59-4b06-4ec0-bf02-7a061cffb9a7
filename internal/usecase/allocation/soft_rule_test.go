package allocation

import (
	"context"
	"reflect"
	"testing"

	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
)

func TestSoftRuleService_preCalculateShippingFee(t *testing.T) {
	ctx := context.Background()
	testGlobalConf := configutil.InitTest()
	mockChargeApi := &chargeclient.ChargeApiImpl{}
	impl := &SoftRuleService{}

	var patchGetAllocationLogFromContext, patchBatchAllocatingESFWithGrpc *gomonkey.Patches
	type args struct {
		maskProductId int64
		productIDs    []int64
		info          *pb.MaskingOrderInfo
		businessType  int8
	}
	tests := []struct {
		name  string
		args  args
		want  *chargeentity.BatchAllocationESFResp
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: not live masking, no need to pre calc fee",
			args: args{
				maskProductId: 1,
				productIDs:    []int64{1, 2, 3},
				info:          nil,
				businessType:  3,
			},
			want: &chargeentity.BatchAllocationESFResp{},
			setup: func() {
				impl = &SoftRuleService{}
			},
		},
		{
			name: "case 2: global switch is close, no need to pre calc fee",
			args: args{
				maskProductId: 1,
				productIDs:    []int64{1, 2, 3},
				info:          nil,
				businessType:  1,
			},
			want: &chargeentity.BatchAllocationESFResp{},
			setup: func() {
				impl = &SoftRuleService{}
			},
		},
		{
			name: "case 3: pre calc masking fee error",
			args: args{
				maskProductId: 1,
				productIDs:    []int64{1},
				info: &pb.MaskingOrderInfo{
					CodAmount: proto.Float64(0.1),
					Cogs:      proto.Float64(0.1),
				},
				businessType: 1,
			},
			want: &chargeentity.BatchAllocationESFResp{},
			setup: func() {
				impl = &SoftRuleService{RateClient: mockChargeApi}
				testGlobalConf.MaskingPreCalcFeeSwitch = configutil.MaskingPreCalcFeeSwitch{
					GlobalSwitch:  true,
					MaskingSwitch: []int64{1},
				}
				patchBatchAllocatingESFWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{},
						srerr.New(srerr.ChargeApiError, nil, "BatchAllocatingESFWithGrpc error")
				})
			},
		},
		{
			name: "case 4: allocationLog != nil",
			args: args{
				maskProductId: 1,
				productIDs:    []int64{1},
				info: &pb.MaskingOrderInfo{
					CodAmount: proto.Float64(0.1),
					Cogs:      proto.Float64(0.1),
				},
				businessType: 1,
			},
			want: &chargeentity.BatchAllocationESFResp{},
			setup: func() {
				impl = &SoftRuleService{RateClient: mockChargeApi}
				testGlobalConf.MaskingPreCalcFeeSwitch = configutil.MaskingPreCalcFeeSwitch{
					GlobalSwitch:  true,
					MaskingSwitch: []int64{1},
				}
				patchBatchAllocatingESFWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{}, nil
				})
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return &Log{}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := impl.preCalculateShippingFee(ctx, tt.args.maskProductId, tt.args.productIDs, tt.args.info, tt.args.businessType); !assert.Equal(t, got, tt.want) {
				t.Errorf("preCalculateShippingFee() = %v, want %v", got, tt.want)
			}
		})
		if patchBatchAllocatingESFWithGrpc != nil {
			patchBatchAllocatingESFWithGrpc.Reset()
		}
		if patchGetAllocationLogFromContext != nil {
			patchGetAllocationLogFromContext.Reset()
		}
	}
}

func TestGetCommonReqDataItemForOrder(t *testing.T) {
	type args struct {
		info *pb.MaskingOrderInfo
	}
	tests := []struct {
		name string
		args args
		want chargeentity.BatchAllocationESFReqDataItem
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				info: &pb.MaskingOrderInfo{
					PickupAddress:   &pb.Address{},
					DeliveryAddress: &pb.Address{},
					CheckoutItems:   []*pb.CheckOutItem{{}},
					PaymentMethod:   proto.String(constant.PaymentMethodCod),
				},
			},
			want: chargeentity.BatchAllocationESFReqDataItem{
				AddCodFee: 1,
				SkuInfo:   []chargeentity.SkuInfo{{}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common.AssertResult(t, GetCommonReqDataItemForOrder(tt.args.info), tt.want, nil, nil)
		})
	}
}

func TestSoftRuleService_calculateShippingFee(t *testing.T) {
	ctx := context.Background()
	var s SoftRuleService
	var chargeApiImpl *chargeclient.ChargeApiImpl
	var patch *gomonkey.Patches
	type args struct {
		productIDs []int64
		info       *pb.MaskingOrderInfo
	}
	tests := []struct {
		name    string
		args    args
		want    *chargeentity.BatchAllocationESFResp
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: BatchAllocatingESFWithGrpc failed",
			args: args{
				info: &pb.MaskingOrderInfo{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.ChargeApiError, nil, "mock BatchAllocatingESFWithGrpc error"),
			setup: func() {
				s = SoftRuleService{RateClient: chargeApiImpl}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(chargeApiImpl), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return nil, srerr.New(srerr.ChargeApiError, nil, "mock BatchAllocatingESFWithGrpc error")
				})
			},
		},
		{
			name: "case 2: normal result",
			args: args{
				productIDs: []int64{1},
				info:       &pb.MaskingOrderInfo{},
			},
			want:    &chargeentity.BatchAllocationESFResp{},
			wantErr: nil,
			setup: func() {
				s = SoftRuleService{RateClient: chargeApiImpl}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(chargeApiImpl), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := s.calculateShippingFee(ctx, tt.args.productIDs, tt.args.info)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
