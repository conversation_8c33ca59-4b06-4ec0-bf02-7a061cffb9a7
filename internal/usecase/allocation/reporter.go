package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"strconv"
)

type Scene string
type Field string
type FieldValue string

// 通用value
const (
	SuccessValue FieldValue = "0"
	FailValue    FieldValue = "1"
)
const (
	// HoldOrderScene hold单场景
	HoldOrderScene Scene = "hold_order"
	// GreySwitchField hold灰度切换
	GreySwitchField Field      = "grey_switch"
	SingleValue     FieldValue = "single"
	BatchValue      FieldValue = "batch"
	// PreCalcShippingFee 提前运费计算
	PreCalcShippingFee Field = "pre_calc_shipping_fee"
	// InitHoldOrderData 初始化hold单数据
	InitHoldOrderData Field = "init_hold_order_data"
	// AddFulfillmentShippingFee 填充esf运费信息
	AddFulfillmentShippingFee Field = "add_fulfillment_shipping_fee"
	// MarshalHoldData 编码hold单数据
	MarshalHoldData Field = "marshal_hold_data"
	// SendHoldDataToKafka hold单信息发送至Kafka
	SendHoldDataToKafka Field = "send_hold_data_to_kafka"
	// AddHoldOrderData 新增hold单数据
	AddHoldOrderData Field = "add_hold_order_data"
	// UpdateHoldOrderData 更新hold单数据
	UpdateHoldOrderData Field = "update_hold_order_data"
	// MatchLocationVolumes 匹配运力配置
	MatchLocationVolumes Field = "match_location_volumes"
	// IncrRouteVolume Route Volume计数
	IncrRouteVolume Field = "incr_route_volume"
	// IncrZoneVolume Zone Volume计数
	IncrZoneVolume Field = "incr_zone_volume"

	// ExecuteBatchAllocation 执行Batch Allocation场景
	ExecuteBatchAllocationScene Scene = "execute_batch_allocation"
	// GetOrder 根据Batch Info获取订单信息
	GetOrderField Field = "get_order"
	// ParamAssemble 组装SDK参数
	ParamAssembleField Field = "param_assemble"
	// SDKCalc SDK计算
	SDKCalcField Field = "sdk_calc"
	// CreateResult 记录结果（DB+运力更新）
	CreateResultField Field = "create_result"
	// WholeProcess 全流程
	WholeProcessField Field = "whole_process"

	OrderSlaScene Scene = "order_sla_scene"
	OrderSlaField Field = "order_sla"
)

func ReportBatchAllocate(ctx context.Context, maskProductId string, scene Scene, field Field, fieldValue FieldValue) {
	prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
		MaskProductID: maskProductId,
		Scene:         string(scene),
		Field:         string(field),
		FieldValue:    string(fieldValue),
	})
}

func ReportBatchAllocateTimeConsume(maskProductId int, scene Scene, field Field, fieldValue FieldValue, value float64) {
	info := prometheusutil.CounterInfo{
		MaskProductID: strconv.Itoa(maskProductId),
		Scene:         string(scene),
		Field:         string(field),
		FieldValue:    string(fieldValue),
	}

	prometheusutil.ObserveBatchAllocateHistogram(info, value)
}

func ReportBatchAllocateWithInfoLog(ctx context.Context, maskProductId string, scene Scene, field Field, fieldValue FieldValue) {
	ReportBatchAllocate(ctx, maskProductId, scene, field, fieldValue)
	logger.CtxLogInfof(ctx, "mask product id=%s, scene=%s, field=%s, field_value=%s", maskProductId, scene, field, fieldValue)
}

func ReportBatchAllocateWithErrorLog(ctx context.Context, maskProductId string, scene Scene, field Field, fieldValue FieldValue) {
	ReportBatchAllocate(ctx, maskProductId, scene, field, fieldValue)
	logger.CtxLogErrorf(ctx, "mask product id=%s, scene=%s, field=%s, field_value=%s", maskProductId, scene, field, fieldValue)
}
