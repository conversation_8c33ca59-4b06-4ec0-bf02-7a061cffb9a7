package whitelist

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type ShopWhitelistService interface {
	ImportShopWhitelist(ctx context.Context, req *allocation.UploadWhitelistReq) *srerr.Error
	ListShopWhitelist(ctx context.Context, req *allocation.ListWhitelistReq, needPage bool) (*allocation.ListWhitelistResp, *srerr.Error)
	ExportShopWhitelist(ctx context.Context, req *allocation.ListWhitelistReq) (*allocation.ExportWhitelistResp, *srerr.Error)
	CheckShopWhitelistAndFulfillmentType(ctx context.Context, shopId int64, orderFulfillmentType whitelist.FulfillmentType) bool
	GetPickupPriorityByMaskProductIDWithCache(ctx context.Context, maskProductID int64) (map[int64]int64, *srerr.Error)
}

type ShopWhitelistServiceImpl struct {
	ShopWhitelistRepo whitelist.ShopWhitelistRepo
}

func NewShopWhitelistServiceImpl(ShopWhitelistRepo whitelist.ShopWhitelistRepo) *ShopWhitelistServiceImpl {
	return &ShopWhitelistServiceImpl{
		ShopWhitelistRepo: ShopWhitelistRepo,
	}
}

func (s *ShopWhitelistServiceImpl) ImportShopWhitelist(ctx context.Context, req *allocation.UploadWhitelistReq) *srerr.Error {
	url := req.Url
	// 1.Parse Excel
	defFile, err := client.SendRequest(ctx, client.GetMethod, url, nil, nil, nil, client.TextContent)
	if err != nil {
		return srerr.With(srerr.ParamErr, "can not download shop whitelist file", err)
	}

	rows, rErr := fileutil.ReadOriginalData(defFile.Body, fileutil.DefaultSheet)
	if rErr != nil {
		return srerr.New(srerr.ParamErr, nil, "read data from excel err:%v", err)
	}
	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "get empty excel rows")
	}
	if len(rows[0]) != len(shopWhitelistHeader) {
		return srerr.New(srerr.ParamErr, nil, "excel headers invalid, headers length is invalid, headers length %v", len(rows[0]))
	}
	var (
		insertTabs    []*whitelist.WhitelistTab
		deleteShopIds []string
		insertShopIds []string
		duplicateShop = make(map[string]struct{}, 0)
		nowTime       = timeutil.GetCurrentUnixTimeStamp(ctx)
	)

	dataList, rErr := fileutil.RowsToData(rows, len(shopWhitelistHeader))
	if rErr != nil {
		return rErr
	}
	for idx, data := range dataList {
		if fileutil.IsEmptyRow(data) {
			continue
		}

		shopIdStr := data[0]
		fulfillmentType := convertFulfillmentType(data[1])
		action := data[2]
		operator, _ := apiutil.GetUserInfo(ctx)

		if fulfillmentType == whitelist.IllegalType {
			return srerr.New(srerr.ParamErr, nil, "row=%d, illegal fulfillment type, type=%s", idx+1, data[1])
		}

		// 判重
		if _, ok := duplicateShop[shopIdStr]; ok {
			return srerr.New(srerr.ParamErr, nil, "duplicate shop_id = %s", shopIdStr)
		}
		duplicateShop[shopIdStr] = struct{}{}

		// delete action
		if action == whitelist.ActionDelete {
			deleteShopIds = append(deleteShopIds, shopIdStr)
		} else if action == whitelist.ActionAddOrUpdate { //add or update
			tab := &whitelist.WhitelistTab{
				WhitelistScenario: whitelist.MaskingShopWhitelist,
				WhitelistId:       shopIdStr,
				Operator:          operator,
				FulfillmentType:   fulfillmentType,
				WhitelistStatus:   whitelist.ActiveWhitelist,
				Ctime:             nowTime,
				Mtime:             nowTime,
			}
			insertTabs = append(insertTabs, tab)
			insertShopIds = append(insertShopIds, shopIdStr)
		} else {
			return srerr.New(srerr.ParamErr, nil, "row=%d, illegal action, action=%s", idx+1, action)
		}
	}
	db, dbErr := dbutil.MasterDB(ctx, whitelist.WhitelistTabHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		if len(insertTabs) != 0 {
			if err1 := s.ShopWhitelistRepo.BatchUpdateOrInsert(ctx, insertTabs, insertShopIds); err1 != nil {
				return err1
			}
		}
		if len(deleteShopIds) != 0 {
			if err1 := s.ShopWhitelistRepo.SetShopWhiteListExpired(ctx, deleteShopIds); err1 != nil {
				return err1
			}
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}

	// 2.repo batch insert
	return nil
}

func (s *ShopWhitelistServiceImpl) ListShopWhitelist(ctx context.Context, req *allocation.ListWhitelistReq, needPage bool) (*allocation.ListWhitelistResp, *srerr.Error) {
	condition := map[string]interface{}{
		"whitelist_status = ?":   whitelist.ActiveWhitelist,
		"whitelist_scenario = ?": whitelist.MaskingShopWhitelist,
	}
	if req.ShopId != "" {
		condition["whitelist_id = ?"] = req.ShopId
	}
	if req.FulfillmentType != whitelist.IllegalType {
		condition["fulfillment_type = ?"] = req.FulfillmentType
	}

	if req.Page < defaultPage {
		req.Page = defaultPage
	}
	if req.Size <= 0 {
		req.Size = defaultSize
	}
	offset := (req.Page - 1) * req.Size
	size := req.Size

	tabs, total, lErr := s.ShopWhitelistRepo.ListShopWhitelist(ctx, condition, offset, size, needPage)
	if lErr != nil {
		return nil, lErr
	}

	resp := &allocation.ListWhitelistResp{
		Page:  req.Page,
		Size:  req.Size,
		Total: total,
	}
	list := make([]allocation.WhitelistItem, 0)
	for _, tempTab := range tabs {
		tab := tempTab
		shopId, err := strconv.ParseInt(tab.WhitelistId, 10, 64)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "illegal shop id:%v", tab.WhitelistId)
		}
		item := allocation.WhitelistItem{
			ShopId:          shopId,
			FulfillmentType: tab.FulfillmentType,
			ModifyTime:      tab.Mtime,
			LastUpdatedBy:   tab.Operator,
		}
		list = append(list, item)
	}
	resp.List = list

	return resp, nil
}

func (s *ShopWhitelistServiceImpl) ExportShopWhitelist(ctx context.Context, req *allocation.ListWhitelistReq) (*allocation.ExportWhitelistResp, *srerr.Error) {
	// 1.检索数据
	resp, lErr := s.ListShopWhitelist(ctx, req, false)
	if lErr != nil {
		return nil, lErr
	}
	// 2.生成Excel
	data := make([][]string, 0)
	for _, item := range resp.List {
		data = append(data, []string{
			strconv.FormatInt(item.ShopId, 10),
			item.FulfillmentType.Name(),
		})
	}

	sheetName := ""
	f, mErr := fileutil.MakeExcel(ctx, exportHeader, data, sheetName)
	if mErr != nil {
		return nil, srerr.With(srerr.ParseExcelError, nil, mErr)
	}

	//upload excel to s3
	b, wErr := f.WriteToBuffer()
	if wErr != nil {
		return nil, srerr.With(srerr.ParseExcelError, nil, wErr)
	}
	exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
	s3Key := fmt.Sprintf("Shop White List:%v.xlsx", exportTime)
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return nil, srerr.With(srerr.S3UploadFail, nil, err)
	}

	//update task record to success
	s3Url := fileutil.GetS3Url(ctx, bucket, s3Key)

	return &allocation.ExportWhitelistResp{
		Url: s3Url,
	}, nil
}

func (s *ShopWhitelistServiceImpl) getShopFulfillmentTypeWithCache(ctx context.Context, shopId int64) (whitelist.FulfillmentType, *srerr.Error) {
	cacheKey := strconv.Itoa(int(shopId))
	cacheVal, err := localcache.Get(ctx, constant.MaskingWhitelist, cacheKey)
	if err != nil {
		return whitelist.IllegalType, srerr.With(srerr.LocalCacheErr, cacheKey, err)
	}

	whitelistType, ok := cacheVal.(whitelist.FulfillmentType)
	if !ok {
		return whitelist.IllegalType, srerr.New(srerr.LocalCacheErr, cacheKey, "invalid value type")
	}

	return whitelistType, nil
}

func (s *ShopWhitelistServiceImpl) CheckShopWhitelistAndFulfillmentType(
	ctx context.Context, shopId int64, orderFulfillmentType whitelist.FulfillmentType) bool {

	// 1.检查Shop是否在白名单中
	whitelistFulfillmentType, err := s.getShopFulfillmentTypeWithCache(ctx, shopId)
	if err != nil {
		// 不在白名单中，直接返回false
		return false
	}

	// 2.检查FulfillmentType是否能匹配
	if whitelistFulfillmentType == whitelist.BothFulfillment || whitelistFulfillmentType == orderFulfillmentType {
		return true
	}

	return false
}

func (s *ShopWhitelistServiceImpl) GetPickupPriorityByMaskProductIDWithCache(ctx context.Context, maskProductID int64) (map[int64]int64, *srerr.Error) {
	cacheKey := strconv.Itoa(int(maskProductID))
	cacheVal, err := localcache.Get(ctx, constant.PickupPriority, cacheKey)
	if err != nil {
		return nil, srerr.With(srerr.LocalCacheErr, cacheKey, err)
	}

	pickupPriority, ok := cacheVal.(map[int64]int64)
	if !ok {
		return nil, srerr.New(srerr.LocalCacheErr, cacheKey, "invalid value type")
	}

	return pickupPriority, nil
}
