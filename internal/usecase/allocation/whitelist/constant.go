package whitelist

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"

const (
	defaultPage = 1
	defaultSize = 10
)

var (
	shopWhitelistHeader = []string{"Shop ID", "Fulfillment Type", "Action"} // action:-1 -> delete; 0 -> add/update
	exportHeader        = []string{"Shop ID", "Fulfillment Type"}
)

func convertFulfillmentType(typeStr string) whitelist.FulfillmentType {
	if typeStr == whitelist.BothType {
		return whitelist.BothFulfillment
	} else if typeStr == whitelist.WMSType {
		return whitelist.WMSFulfillment
	} else if typeStr == whitelist.MPLType {
		return whitelist.MPLFulfillment
	}

	return whitelist.IllegalType
}
