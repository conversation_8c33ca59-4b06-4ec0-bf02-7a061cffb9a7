package allocation

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

func NewSoftCriteriaDetail(ctx context.Context) *allocation.SoftCriteriaDetail {
	if !configutil.GetAllocationLogConf(ctx) {
		return nil
	}
	return &allocation.SoftCriteriaDetail{
		Input:                          -1,
		Output:                         -1,
		MaxDailyLimitOfCountry:         -1,
		MaxDailyLimitOfRoutes:          make([]allocation.RouteLimit, 0),
		MaxDailyLimitOfZoneDestination: make([]allocation.ZoneLimit, 0),
		MaxDailyLimitOfZoneOrigin:      make([]allocation.ZoneLimit, 0),
		MinDailyLimitOfCountry:         -1,
		MinDailyLimitOfRoutes:          make([]allocation.RouteLimit, 0),
		MinDailyLimitOfZoneDestination: make([]allocation.ZoneLimit, 0),
		MinDailyLimitOfZoneOrigin:      make([]allocation.ZoneLimit, 0),
		ShippingFee:                    -1,
		SystemVolumeOfProduct:          -1,
		SystemVolumeOfRoutes:           make([]allocation.RouteLimit, 0),
		SystemVolumeOfZoneDestination:  make([]allocation.ZoneLimit, 0),
		SystemVolumeOfZoneOrigin:       make([]allocation.ZoneLimit, 0),
		MaxVolumeInBatch:               -1,
		BatchSize:                      -1,
		SystemVolumeInBatch:            -1,
		ProductWeightage:               -1,
		ProductStatus:                  false,
		ProductPriority:                -1,
		WhitelistPriority:              -1,
	}
}

type (
	AllocationLog struct {
		List []*LogDetail
	}

	LogDetail struct {
		RequestId                   string                `json:"request_id"`
		FOrderId                    uint64                `json:"forder_id"`
		OrderId                     uint64                `json:"order_id"`
		Status                      bool                  `json:"status"` // 请求结果状态
		UniqueId                    string                `json:"unique_id"`
		FulfillmentProductId        int                   `json:"fulfillment_product_id"`
		AllocationScenario          pb.AllocationScenario `json:"allocation_scenario"`
		ReallocationPreviousProduct int                   `json:"reallocation_previous_product"`
		ReselectPreviousProduct     *int64                `json:"reselect_previous_product"`
		MaskProductId               int                   `json:"mask_product_id"`
		RequestTime                 int64                 `json:"request_time"`
		AllocateType                string                `json:"allocate_type"`

		RequestDataStr  string      `json:"request_data"`
		RequestData     interface{} `json:"-"`
		ResponseDataStr string      `json:"response_data"`
		ResponseData    interface{} `json:"-"`

		HardCriteriaList    map[int64][]ProductToggle `json:"-"`
		HardCriteriaListStr string                    `json:"hard_criteria_list"`
		HardInput           []int                     `json:"hard_input"`
		HardOutput          []int                     `json:"hard_output"`

		SoftCriteriaList    []allocation.SoftCriteria `json:"-"`
		ShippingFeeList     []PreShippingFee          `json:"shipping_fee_list"`
		SoftCriteriaListStr string                    `json:"soft_criteria_list"`

		SoftInput             []int64                 `json:"soft_input"`
		SoftOutput            []int64                 `json:"soft_output"`
		SoftRuleId            int64                   `json:"soft_rule_id"`
		VolumeRuleId          uint64                  `json:"volume_rule_id"`
		ZoneDestinationCode   string                  `json:"zone_destination_code"`
		ZoneOriginCode        string                  `json:"zone_origin_code"`
		RouteCodes            []string                `json:"route_codes"`
		ShopGroupId           int64                   `json:"shop_group_id"`
		ProductParcelInfoList []*pb.ProductParcelInfo `json:"product_parcel_info_list"`

		BatchAllocationLog    *BatchAllocationLog `json:"-"`
		BatchAllocationLogStr string              `json:"batch_allocation_log"`
	}

	ProductToggle struct {
		Available int `json:"available"`
		Priority  int `json:"priority"`
		ProductId int `json:"product_id"`
	}
)

type Log struct {
	List                  []allocation.SoftCriteria
	ShippingFee           []PreShippingFee        `json:"pre_shipping_fee"`
	SoftInput             []int64                 `json:"soft_input"`
	SoftOutput            []int64                 `json:"soft_output"`
	SoftRuleId            int64                   `json:"soft_rule_id"`
	VolumeRuleId          uint64                  `json:"volume_rule_id"`
	ZoneDestinationCode   string                  `json:"zone_destination_code"`
	ZoneOriginCode        string                  `json:"zone_origin_code"`
	RouteCodes            []string                `json:"route_codes"`
	ShopGroupId           int64                   `json:"shop_group_id"`
	MaskingProductId      int64                   `json:"masking_product_id"`
	FulfillmentProductId  int64                   `json:"fulfillment_product_id"`
	ProductParcelInfoList []*pb.ProductParcelInfo `json:"product_parcel_info_list"`

	// batch allocation log
	// grpc 埋点数据
	OrderListBytes        []byte         `json:"order_list_bytes"`
	Distributions         []Distribution `json:"distributions"`
	OrderInfoMapBytes     []byte         `json:"order_info_map_bytes"`
	BatchInfoBytes        []byte         `json:"batch_info_bytes"`
	LastSecond            int64          `json:"last_second"`
	CountryVolumeMapBytes []byte         `json:"country_volume_map_bytes"`
	// task 装填数据
	BatchAllocationLog *BatchAllocationLog `json:"batch_allocation_log"`
}

type AllocateOrderInfoForLog struct {
	OrderID                uint64                           `json:"order_id"`
	ZoneCodeList           []string                         `json:"zone_code_list"`
	RouteCodeList          []string                         `json:"route_code_list"`
	Inputs                 []int                            `json:"inputs"`
	ZoneVolumeMap          map[int64]map[string]*VolumeInfo `json:"zone_volume_map"`
	RouteVolumeMap         map[int64]map[string]*VolumeInfo `json:"route_volume_map"`
	PickupEffWhitelistInfo PickupEffWhitelistInfo           `json:"pickup_eff_whitelist_info"`
}

type VolumeInfo struct {
	MaxDailyLimit          int64 `json:"max_daily_limit,omitempty"`
	MaxCodDailyLimit       int64 `json:"max_cod_daily_limit,omitempty"`
	MaxBulkyDailyLimit     int64 `json:"max_bulky_daily_limit,omitempty"`
	MaxHighValueDailyLimit int64 `json:"max_high_value_daily_limit,omitempty"`
	MaxDgDailyLimit        int64 `json:"max_dg_daily_limit,omitempty"`
	MinBatchLimit          int64 `json:"min_batch_limit,omitempty"`
	SystemVolume           int64 `json:"system_volume,omitempty"`
	SystemCodVolume        int64 `json:"system_cod_volume,omitempty"`
	SystemBulkyVolume      int64 `json:"system_bulky_volume,omitempty"`
	SystemHighValueVolume  int64 `json:"system_high_value_volume,omitempty"`
	SystemDgVolume         int64 `json:"system_dg_volume,omitempty"`
}

type PickupEffWhitelistInfo struct {
	HitWhiteList      bool  `json:"hit_pickup_eff_whitelist"`
	WhitelistProducts []int `json:"whitelist_products"`
}

type PreShippingFee struct {
	ProductId           int64   `json:"product_id"`
	AllocateShippingFee float64 `json:"allocate_shipping_fee"`
}

type (
	// BatchAllocationLog 一个BatchAllocationLog，对应一整个batch
	BatchAllocationLog struct {
		List []BatchAllocationDetail `json:"list"` // 每个元素对应一条订单
	}

	BatchAllocationDetail struct {
		// basic info
		RequestId            string `json:"request_id"`
		FOrderId             uint64 `json:"forder_id"`
		OrderId              uint64 `json:"order_id"`
		Status               bool   `json:"status"` // 请求结果状态
		UniqueId             string `json:"unique_id"`
		FulfillmentProductId int    `json:"fulfillment_product_id"`
		MaskProductId        int    `json:"mask_product_id"`
		RequestTime          int64  `json:"request_time"`
		IsWms                bool   `json:"is_wms"`
		AllocationMethod     string `json:"allocation_method"`
		BatchId              int    `json:"batch_id"`
		BatchName            string `json:"batch_name"`
		BatchTime            string `json:"batch_time"` //e.g. 15:10 - 15:13
		BatchSize            int    `json:"batch_size"`
		RequestData          string `json:"request_data"`
		AllocateType         string `json:"allocate_type"`

		// hard check
		HardCriteriaList    map[int64][]ProductToggle `json:"-"`
		HardCriteriaListStr string                    `json:"hard_criteria_list"`
		HardInput           []int                     `json:"hard_input"`
		HardOutput          []int                     `json:"hard_output"`
		IgnoreSnapShot      bool                      `json:"ignore_snap_shot"`

		// soft check
		Input                       []int                   `json:"input"`  // products will go through soft check
		Output                      []int                   `json:"output"` // final allocated results
		SoftRuleId                  int                     `json:"soft_rule_id"`
		ShopGroupId                 int                     `json:"shop_group_id"`
		VolumeRuleId                int                     `json:"volume_rule_id"`
		RuleType                    string                  `json:"rule_type"` // e.g. Zone
		DestZoneCodeList            []string                `json:"dest_zone_code_list"`
		RouteCodeList               []string                `json:"route_code_list"`
		Steps                       []Step                  `json:"steps"`
		SoftCriteriaListStr         string                  `json:"soft_criteria_list"`
		BatchAllocationDistribution []Distribution          `json:"batch_level_allocation_distribution"`
		ProductParcelInfoList       []*pb.ProductParcelInfo `json:"product_parcel_info_list"`
	}

	Step struct {
		SoftCriteriaDetailList []SoftCriteriaDetail `json:"soft_criteria_detail_list"`
	}

	SoftCriteriaDetail struct {
		Step             string   `json:"step"`
		SoftCriteriaName string   `json:"soft_criteria_name"`
		InputProduct     string   `json:"input_product"`
		OutputProduct    string   `json:"output_product"`
		Titles           []string `json:"titles"` //涉及的zone
		Values           []string `json:"values"`
	}

	Distribution struct {
		InputProduct                  string `json:"input_product"`
		ProductName                   string `json:"product_name"`
		MaxDailyLimitCountry          int64  `json:"max_daily_limit_country"`
		MinBatchLimitCountry          int64  `json:"min_batch_limit_country"`
		MaxDailyCodLimitCountry       int64  `json:"max_daily_cod_limit_country"`
		MaxDailyBulkyLimitCountry     int64  `json:"max_daily_bulky_limit_country"`
		MaxDailyHighValueLimitCountry int64  `json:"max_daily_high_value_limit_country"`
		MaxDailyDgLimitCountry        int64  `json:"max_daily_dg_limit_country"`
		SystemVolumeProduct           int64  `json:"system_volume_product"`
		SystemVolumeCod               int64  `json:"system_volume_cod"`
		SystemVolumeBulky             int64  `json:"system_volume_bulky"`
		SystemVolumeHighValue         int64  `json:"system_volume_high_value"`
		SystemVolumeDg                int64  `json:"system_volume_dg"`
		AllocationShippingFee         string `json:"allocation_shipping_fee"`
	}
)

func NewLog() *Log {
	return &Log{
		List:        make([]allocation.SoftCriteria, 0),
		ShippingFee: make([]PreShippingFee, 0),
	}
}

func GetAllocationLogFromContext(ctx context.Context) *Log {
	if !configutil.GetAllocationLogConf(ctx) {
		return nil
	}
	allocationLog := ctx.Value(constant.AllocationLogCtxKey)
	if allocationLog == nil {
		return nil
	}
	softCriteriaList, dumpLog := allocationLog.(*Log)
	if dumpLog {
		return softCriteriaList
	}
	return nil
}

func (al *Log) AppendSoftCriteriaList(ctx context.Context, id int, name string) {
	if !al.isValidate(ctx, FirstLevel) || al.List == nil {
		return
	}
	al.List = append(al.List, allocation.SoftCriteria{
		Id:   id,
		Name: name,
	})
}

func GetAllocationLogString(ctx context.Context) string {
	al := GetAllocationLogFromContext(ctx)
	if !al.isValidate(ctx, FirstLevel) {
		return ""
	}
	b, e := jsoniter.Marshal(al.List)
	if e != nil {
		logger.CtxLogErrorf(ctx, "SoftCriteriaList to String error:%s", e.Error())
		return ""
	}
	return string(b)
}

func GetAllocationLogShippingFee(ctx context.Context) string {
	al := GetAllocationLogFromContext(ctx)
	if !al.isValidate(ctx, FirstLevel) {
		return ""
	}
	b, e := jsoniter.Marshal(al.ShippingFee)
	if e != nil {
		logger.CtxLogErrorf(ctx, "allocate shippingFee to String error:%s", e.Error())
		return ""
	}
	return string(b)
}

func (al *Log) AddDefaultSoftCriteriaItem(ctx context.Context, productIds []int64, selectedProduct int64,
	defaultRuleType entity.DefaultRuleType,
	productPriorities, productWeightages map[int64]int32) {
	if !al.isValidate(ctx, FirstLevel) {
		return
	}
	softCriteriaItem := allocation.SoftCriteria{
		Id:     len(al.List) + 1,
		Name:   defaultRuleType.String(),
		Detail: make(map[int64]*allocation.SoftCriteriaDetail),
	}
	for _, prod := range productIds {
		softCriteriaItem.Detail[prod] = NewSoftCriteriaDetail(ctx)
		softCriteriaItem.Detail[prod].Input = prod

		switch defaultRuleType {
		case entity.Priority:
			if p, ok := productPriorities[prod]; ok {
				softCriteriaItem.Detail[prod].ProductPriority = p
				softCriteriaItem.Detail[prod].ProductStatus = true
			}
		case entity.Weightage:
			if w, ok := productWeightages[prod]; ok {
				softCriteriaItem.Detail[prod].ProductWeightage = w
				softCriteriaItem.Detail[prod].ProductStatus = true
			}
		}
	}
	softCriteriaItem.Detail[selectedProduct].Output = selectedProduct
	al.List = append(al.List, softCriteriaItem)
}

func (al *Log) AppendSoftCriteriaDetail(ctx context.Context) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaIdx := len(al.List) - 1
	al.List[softCriteriaIdx].Detail = make(map[int64]*allocation.SoftCriteriaDetail)
}

func (al *Log) UpdateSoftCriteriaDetailCountryVolume(ctx context.Context, prod int64,
	currentVolume, threshold int32, volType volumeFilterType) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	softCriteriaMap[prod].SystemVolumeOfProduct = currentVolume
	switch volType {
	case volumeFilterTypeMinVolume:
		softCriteriaMap[prod].MinDailyLimitOfCountry = threshold
	case volumeFilterTypeMaxCapacity:
		softCriteriaMap[prod].MaxDailyLimitOfCountry = threshold
	}
}

func (al *Log) UpdateSoftCriteriaDetailBatchSize(ctx context.Context, prod int64, maxVolumeInBatch, systemVolumeInBatch, batchSize int32) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	softCriteriaMap[prod].MaxVolumeInBatch = maxVolumeInBatch
	softCriteriaMap[prod].SystemVolumeInBatch = systemVolumeInBatch
	softCriteriaMap[prod].BatchSize = batchSize
}

func (al *Log) UpdateSoftCriteriaDetailFee(ctx context.Context, prod int64, fee float64) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	softCriteriaMap[prod].ShippingFee = fee
}

func (al *Log) UpdateSoftCriteriaDetailInput(ctx context.Context, prod int64) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		softCriteriaMap[prod] = NewSoftCriteriaDetail(ctx)
	}
	softCriteriaMap[prod].Input = prod
}

func (al *Log) UpdateSoftCriteriaDetailOutput(ctx context.Context, prod int64) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	softCriteriaMap[prod].Output = prod
}

func (al *Log) SortCriteriaBottomLogic(ctx context.Context, products []int64) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	for _, prod := range products {
		if _, ok := softCriteriaMap[prod]; !ok {
			continue
		}
		softCriteriaMap[prod].Output = prod
	}
}

func (al *Log) UpdateSortCriteriaDetailLocVolume(ctx context.Context, prod int64, currentVolume int32, locVol *rulevolume.MaskLocVolume, volType volumeFilterType, limit int32) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	switch locVol.LocType {
	case rulevolume.MaskLocTypeRoute:
		switch volType {
		case volumeFilterTypeMinVolume:
			softCriteriaMap[prod].MinDailyLimitOfRoutes = append(
				softCriteriaMap[prod].MinDailyLimitOfRoutes,
				allocation.RouteLimit{
					RouteCode: locVol.RouteVolume.RouteCode,
					Limit:     limit,
				})
		case volumeFilterTypeMaxCapacity:
			softCriteriaMap[prod].MaxDailyLimitOfRoutes = append(
				softCriteriaMap[prod].MaxDailyLimitOfRoutes,
				allocation.RouteLimit{
					RouteCode: locVol.RouteVolume.RouteCode,
					Limit:     limit,
				})

		}
		softCriteriaMap[prod].SystemVolumeOfRoutes = append(
			softCriteriaMap[prod].SystemVolumeOfRoutes,
			allocation.RouteLimit{
				RouteCode: locVol.RouteVolume.RouteCode,
				Limit:     currentVolume,
			})
	case rulevolume.MaskLocTypeZone:
		switch locVol.ZoneDirection {
		case rulevolume.MaskZoneDirectionOrigin:
			switch volType {
			case volumeFilterTypeMinVolume:
				softCriteriaMap[prod].MinDailyLimitOfZoneOrigin = append(
					softCriteriaMap[prod].MinDailyLimitOfZoneOrigin,
					allocation.ZoneLimit{
						ZoneCode: locVol.ZoneVolume.ZoneCode,
						Limit:    limit,
					})
			case volumeFilterTypeMaxCapacity:
				softCriteriaMap[prod].MaxDailyLimitOfZoneOrigin = append(
					softCriteriaMap[prod].MaxDailyLimitOfZoneOrigin,
					allocation.ZoneLimit{
						ZoneCode: locVol.ZoneVolume.ZoneCode,
						Limit:    limit,
					})
			}
			softCriteriaMap[prod].SystemVolumeOfZoneOrigin = append(
				softCriteriaMap[prod].SystemVolumeOfZoneOrigin,
				allocation.ZoneLimit{
					ZoneCode: locVol.ZoneVolume.ZoneCode,
					Limit:    currentVolume,
				})
		case rulevolume.MaskZoneDirectionDest:
			switch volType {
			case volumeFilterTypeMinVolume:
				softCriteriaMap[prod].MinDailyLimitOfZoneDestination = append(
					softCriteriaMap[prod].MinDailyLimitOfZoneDestination,
					allocation.ZoneLimit{
						ZoneCode: locVol.ZoneVolume.ZoneCode,
						Limit:    limit,
					})
			case volumeFilterTypeMaxCapacity:
				softCriteriaMap[prod].MaxDailyLimitOfZoneDestination = append(
					softCriteriaMap[prod].MaxDailyLimitOfZoneDestination,
					allocation.ZoneLimit{
						ZoneCode: locVol.ZoneVolume.ZoneCode,
						Limit:    limit,
					})
			}
			softCriteriaMap[prod].SystemVolumeOfZoneDestination = append(
				softCriteriaMap[prod].SystemVolumeOfZoneDestination,
				allocation.ZoneLimit{
					ZoneCode: locVol.ZoneVolume.ZoneCode,
					Limit:    currentVolume,
				})
		}
	}
}

func (al *Log) isValidate(ctx context.Context, level int) bool {
	switch level {
	case FirstLevel:
		if !configutil.GetAllocationLogConf(ctx) || al == nil {
			return false
		}
	case SecondLevel:
		if !configutil.GetAllocationLogConf(ctx) || al == nil || len(al.List) == 0 {
			return false
		}
	default:
		return false
	}
	return true
}

const (
	FirstLevel = iota + 1
	SecondLevel
)

// GetScheduleResultFactor 获取调度结果的调度因子
func GetScheduleResultFactor(softCriteria string) string {
	var log []allocation.SoftCriteria
	err := jsoniter.UnmarshalFromString(softCriteria, &log)
	if err != nil {
		return ""
	}
	if len(log) < 1 {
		return ""
	}
	return log[len(log)-1].Name
}

func (al *Log) SetPreCalculate(shippingFee *chargeentity.BatchAllocationESFResp) {
	if shippingFee == nil || shippingFee.Data == nil {
		return
	}
	var shippingFees []PreShippingFee
	for _, item := range shippingFee.Data.AllocatingShippingFeeResult {
		if item.RetCode == 0 {
			shippingFees = append(shippingFees, PreShippingFee{
				item.ProductId,
				item.AllocatingShippingFee})
		}
	}
	al.ShippingFee = shippingFees
}

func (al *Log) SetSoftInput(input []int64) {
	if al == nil {
		return
	}
	al.SoftInput = input
}

func (al *Log) SetSoftOutput(output []int64) {
	if al == nil {
		return
	}
	al.SoftOutput = output
}

func (al *Log) SetRuleId(RuleId int64) {
	if al == nil {
		return
	}
	al.SoftRuleId = RuleId
}

func (al *Log) SetVolumeRuleId(ruleId uint64) {
	if al == nil {
		return
	}
	al.VolumeRuleId = ruleId
}

func (al *Log) SetDestZoneCode(code string) {
	if al == nil {
		return
	}
	if al.ZoneDestinationCode == "" {
		al.ZoneDestinationCode = code
	} else {
		al.ZoneDestinationCode = fmt.Sprintf("%s||%s", al.ZoneDestinationCode, code)
	}
}

func (al *Log) SetOriginZoneCode(code string) {
	if al == nil {
		return
	}
	if al.ZoneOriginCode == "" {
		al.ZoneOriginCode = code
	} else {
		al.ZoneOriginCode = fmt.Sprintf("%s||%s", al.ZoneOriginCode, code)
	}
}

func (al *Log) SetRouteCode(code string) {
	if al == nil {
		return
	}
	al.RouteCodes = append(al.RouteCodes, code)
}

func (al *Log) SetShopGroupId(shopGroupId int64) {
	if al == nil {
		return
	}
	al.ShopGroupId = shopGroupId
}

func (al *Log) SetMaskingProductId(maskingProductId int64) {
	if al == nil {
		return
	}
	al.MaskingProductId = maskingProductId
}

func (al *Log) SetFulfillmentProductId(fulfillmentProductId int64) {
	if al == nil {
		return
	}
	al.FulfillmentProductId = fulfillmentProductId
}

func (al *Log) SetSoftOut(productId int64) {
	if al == nil {
		return
	}
	al.SoftOutput = []int64{productId}
}

func (al *Log) RemoveDuplicateZones() {
	if al == nil {
		return
	}
	if strings.Contains(al.ZoneOriginCode, "||") {
		orgCodes := strings.Split(al.ZoneOriginCode, "||")
		orgCodes = objutil.RemoveDuplicatedStrings(orgCodes)
		al.ZoneOriginCode = strings.Join(orgCodes, "||")
	}
	if strings.Contains(al.ZoneDestinationCode, "||") {
		destCodes := strings.Split(al.ZoneDestinationCode, "||")
		destCodes = objutil.RemoveDuplicatedStrings(destCodes)
		al.ZoneDestinationCode = strings.Join(destCodes, "||")
	}
}

func (al *Log) RemoveDuplicateRoutes() {
	if al == nil {
		return
	}
	if len(al.RouteCodes) > 1 {
		al.RouteCodes = objutil.RemoveDuplicatedStrings(al.RouteCodes)
	}
}

func (al *Log) UpdateSoftCriteriaDetailWhitelistPriority(ctx context.Context, prod int64, priority int32) {
	if !al.isValidate(ctx, SecondLevel) {
		return
	}
	softCriteriaMap := al.List[len(al.List)-1].Detail
	if softCriteriaMap == nil {
		return
	}
	if _, ok := softCriteriaMap[prod]; !ok {
		return
	}
	softCriteriaMap[prod].WhitelistPriority = priority
}

func (al *Log) SetProductParcelInfoList(productParcelInfoList []*pb.ProductParcelInfo) {
	if al == nil {
		return
	}
	al.ProductParcelInfoList = productParcelInfoList
}
