package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetAllocationLogFromContext(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	tests := []struct {
		name  string
		want  *Log
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: configutil.GetAllocationLogConf return false",
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return false
				})
			},
		},
		{
			name: "case 2: ctx.Value(constant.AllocationLogCtxKey) is nil",
			want: nil,
			setup: func() {
				ctx = context.WithValue(ctx, constant.AllocationLogCtxKey, nil)
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 3: allocationLog.(*Log) failed",
			want: nil,
			setup: func() {
				ctx = context.WithValue(ctx, constant.AllocationLogCtxKey, 1)
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 4: normal result",
			want: &Log{},
			setup: func() {
				ctx = context.WithValue(ctx, constant.AllocationLogCtxKey, &Log{})
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			common.AssertResult(t, tt.want, GetAllocationLogFromContext(ctx), nil, nil)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestLog_isValidate(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	var al *Log
	type args struct {
		level int
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: level FirstLevel return false",
			args: args{
				level: FirstLevel,
			},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return false
				})
			},
		},
		{
			name: "case 2: level LastLevel return false",
			args: args{
				level: SecondLevel,
			},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 3: unknown level return false",
			args: args{
				level: 3,
			},
			want:  false,
			setup: func() {},
		},
		{
			name: "case 4: normal result return true",
			args: args{
				level: SecondLevel,
			},
			want: true,
			setup: func() {
				al = &Log{
					List: []allocation.SoftCriteria{
						{},
					},
				}
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			assert.Equalf(t, tt.want, al.isValidate(ctx, tt.args.level), "isValidate(%v, %v)", ctx, tt.args.level)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestLog_AddDefaultSoftCriteriaItem(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		productIds        []int64
		selectedProduct   int64
		defaultRuleType   entity.DefaultRuleType
		productPriorities map[int64]int32
		productWeightages map[int64]int32
	}
	tests := []struct {
		name  string
		al    *Log
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: not Validate ",
			setup: func() {},
		},
		{
			name: "case 2: softCriteriaItem.Detail[selectedProduct] == nil",
			al:   &Log{},
			args: args{
				productIds: []int64{1, 2},
				productPriorities: map[int64]int32{
					1: 1,
				},
				defaultRuleType: entity.Priority,
				selectedProduct: 1,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 2: softCriteriaItem.Detail[selectedProduct] == nil",
			al:   &Log{},
			args: args{
				productIds: []int64{1, 2},
				productWeightages: map[int64]int32{
					2: 2,
				},
				defaultRuleType: entity.Weightage,
				selectedProduct: 2,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			tt.al.AddDefaultSoftCriteriaItem(ctx, tt.args.productIds, tt.args.selectedProduct, tt.args.defaultRuleType, tt.args.productPriorities, tt.args.productWeightages)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestLog_UpdateSortCriteriaDetailLocVolume(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		prod          int64
		currentVolume int32
		locVol        *rulevolume.MaskLocVolume
		volType       volumeFilterType
		limit         int32
	}
	tests := []struct {
		name  string
		al    *Log
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: isValidate return false",
			setup: func() {},
		},
		{
			name: "case 2: softCriteriaMap == nil",
			al: &Log{
				List: []allocation.SoftCriteria{{}},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 3: softCriteriaMap[prod] ok == false",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 4: LocType == MaskLocTypeRoute, volType == volumeFilterTypeMinVolume",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:     rulevolume.MaskLocTypeRoute,
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
				volType: volumeFilterTypeMinVolume,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 5: LocType == MaskLocTypeRoute, volType == volumeFilterTypeMaxCapacity",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:     rulevolume.MaskLocTypeRoute,
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
				volType: volumeFilterTypeMaxCapacity,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 6: LocType == MaskLocTypeZone, ZoneDirection == MaskZoneDirectionOrigin, volType == volumeFilterTypeMinVolume",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:       rulevolume.MaskLocTypeZone,
					ZoneVolume:    &rulevolume.MaskZoneVolume{},
					ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
				},
				volType: volumeFilterTypeMinVolume,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 7: LocType == MaskLocTypeZone, ZoneDirection == MaskZoneDirectionOrigin, volType == volumeFilterTypeMaxCapacity",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:       rulevolume.MaskLocTypeZone,
					ZoneVolume:    &rulevolume.MaskZoneVolume{},
					ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
				},
				volType: volumeFilterTypeMaxCapacity,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 8: LocType == MaskLocTypeZone, ZoneDirection == MaskZoneDirectionDest, volType == volumeFilterTypeMinVolume",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:       rulevolume.MaskLocTypeZone,
					ZoneVolume:    &rulevolume.MaskZoneVolume{},
					ZoneDirection: rulevolume.MaskZoneDirectionDest,
				},
				volType: volumeFilterTypeMinVolume,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 9: LocType == MaskLocTypeZone, ZoneDirection == MaskZoneDirectionDest, volType == volumeFilterTypeMaxCapacity",
			al: &Log{
				List: []allocation.SoftCriteria{{Detail: map[int64]*allocation.SoftCriteriaDetail{1: {}}}},
			},
			args: args{
				prod: 1,
				locVol: &rulevolume.MaskLocVolume{
					LocType:       rulevolume.MaskLocTypeZone,
					ZoneVolume:    &rulevolume.MaskZoneVolume{},
					ZoneDirection: rulevolume.MaskZoneDirectionDest,
				},
				volType: volumeFilterTypeMaxCapacity,
			},
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetAllocationLogConf, func(ctx context.Context) bool {
					return true
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			tt.al.UpdateSortCriteriaDetailLocVolume(ctx, tt.args.prod, tt.args.currentVolume, tt.args.locVol, tt.args.volType, tt.args.limit)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
