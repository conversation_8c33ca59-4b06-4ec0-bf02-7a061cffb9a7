package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

type TempAllocationLog struct {
	RequestId string
	Log       *Log
}

func FillLog(ctx context.Context, log *Log, allocationPath *allocpath.DetailData) (*AllocationLog, *srerr.Error) {
	logDetail := &LogDetail{
		MaskProductId:         int(log.MaskingProductId),
		FulfillmentProductId:  int(log.FulfillmentProductId),
		RequestId:             allocationPath.BasicInfo.RequestId,
		Status:                allocationPath.BasicInfo.AllocationStatus,
		UniqueId:              allocationPath.BasicInfo.UniqueId,
		RequestTime:           int64((allocationPath.BasicInfo.RequestTime+1)*1000 - 1), //原始数据需要传million second给data; 将误差控制在毫秒级别，这样才不会影响row key等
		RequestDataStr:        allocationPath.BasicInfo.RequestData,
		ResponseDataStr:       allocationPath.BasicInfo.ResponseData,
		HardCriteriaListStr:   ConvertToHardCriteria(ctx, allocationPath.HardCriteriaResp.ProductToggle),
		HardInput:             convertProductNameSlice(ctx, allocationPath.HardCriteriaResp.Input),
		HardOutput:            convertProductNameSlice(ctx, allocationPath.HardCriteriaResp.Output),
		AllocationScenario:    smart_routing_protobuf.AllocationScenario_NormalAllocation,
		AllocateType:          string(constant.AsyncSingleAllocate),
		ProductParcelInfoList: log.ProductParcelInfoList,

		ShippingFeeList:     log.ShippingFee,
		SoftInput:           log.SoftInput,
		SoftOutput:          log.SoftOutput,
		SoftRuleId:          log.SoftRuleId,
		VolumeRuleId:        log.VolumeRuleId,
		ZoneDestinationCode: log.ZoneDestinationCode,
		ZoneOriginCode:      log.ZoneOriginCode,
		RouteCodes:          log.RouteCodes,
		ShopGroupId:         log.ShopGroupId,
	}
	logger.CtxLogInfof(ctx, "request_id:%s, origin request time:%d, new request time:%d", logDetail.RequestId, allocationPath.BasicInfo.RequestTime, logDetail.RequestTime)
	forderId, _ := strconv.ParseUint(allocationPath.BasicInfo.FOrderId, 10, 64)
	logDetail.FOrderId = forderId

	orderId, _ := strconv.ParseUint(allocationPath.BasicInfo.OrderId, 10, 64)
	logDetail.OrderId = orderId

	softStr, _ := jsoniter.MarshalToString(log.List)
	logDetail.SoftCriteriaListStr = softStr

	resultLog := &AllocationLog{
		List: []*LogDetail{logDetail},
	}

	return resultLog, nil
}

func ConvertToHardCriteria(ctx context.Context, shopToggleMap map[int64][]string) string {
	result := make(map[int64][]ProductToggle, 0)
	for shop, toggleStrList := range shopToggleMap {
		for _, toggleStr := range toggleStrList {
			logger.CtxLogInfof(ctx, "toggle string:%s", toggleStr)
			// example:  "80014-J&T Express: On"
			tempToggle := ProductToggle{}
			tempSlice := strings.Split(toggleStr, ":")
			if len(tempSlice) != 2 {
				logger.CtxLogErrorf(ctx, "convert toggle string err, length is not 2|%v", tempSlice)
				continue
			}
			// available
			if strings.Contains(tempSlice[1], "On") {
				tempToggle.Available = Available
			} else if strings.Contains(tempSlice[1], "Off") {
				tempToggle.Available = UnAvailable
			} else {
				tempToggle.Available = NotExisted
			}
			// product
			productNameSlice := strings.Split(tempSlice[0], "-")
			if len(productNameSlice) != 2 {
				logger.CtxLogErrorf(ctx, "convert product name string err, length is not 2|%v", productNameSlice)
				continue
			}
			productId, _ := strconv.Atoi(productNameSlice[0])
			tempToggle.ProductId = productId

			result[shop] = append(result[shop], tempToggle)
		}

	}

	if len(result) == 0 {
		logger.CtxLogInfof(ctx, "empty result")
		return ""
	}

	str, _ := jsoniter.MarshalToString(result)
	return str
}

func convertProductNameSlice(ctx context.Context, data []string) []int {
	var result []int
	for _, productName := range data {
		tempSlice := strings.Split(productName, "-")
		if len(tempSlice) != 2 {
			logger.CtxLogErrorf(ctx, "product name:%s|temp slice not equal to 2", productName)
			continue
		}
		productId, err := strconv.Atoi(tempSlice[0])
		if err != nil {
			logger.CtxLogErrorf(ctx, "convert to int err:%v", err)
			continue
		}
		result = append(result, productId)
	}

	return result
}
