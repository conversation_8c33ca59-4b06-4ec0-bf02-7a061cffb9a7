package batch_allocate

import (
	"bytes"
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
	"strings"
)

const (
	timeout   = 10
	illegalId = 0
)

var (
	batchConfHeader = []string{"minute", "ratio"}
)

type BatchMinuteOrderConfService interface {
	GetBatchMinuteOrderConf(ctx context.Context, req allocation.GetMinuteOrderConfReq) (allocation.GetMinuteOrderConfResp, *srerr.Error)
	GetBatchMinuteOrderConfList(ctx context.Context, req allocation.GetMinuteOrderConfListReq) (allocation.GetMinuteOrderConfListResp, *srerr.Error)
	CreateBatchMinuteOrderConf(ctx context.Context, req allocation.CreateMinuteOrderConfReq) *srerr.Error
	//UpdateBatchMinuteOrderConf(ctx context.Context, condition map[string]interface{}, tab *BatchMinuteOrderConfigTab) *srerr.Error
}

type BatchMinuteOrderConfServiceImpl struct {
	BatchMinuteOrderConfRepo batch_minute_order_conf.BatchMinuteOrderConfRepo
}

func NewBatchMinuteOrderConfServiceImpl(BatchMinuteOrderConfRepo batch_minute_order_conf.BatchMinuteOrderConfRepo) *BatchMinuteOrderConfServiceImpl {
	return &BatchMinuteOrderConfServiceImpl{
		BatchMinuteOrderConfRepo: BatchMinuteOrderConfRepo,
	}
}

func (b *BatchMinuteOrderConfServiceImpl) GetBatchMinuteOrderConf(ctx context.Context, req allocation.GetMinuteOrderConfReq) (allocation.GetMinuteOrderConfResp, *srerr.Error) {
	//1. first get by conf type, if conf type != default type
	//2. if not found, then get by default type

	var (
		resp           allocation.GetMinuteOrderConfResp
		tab            batch_minute_order_conf.BatchMinuteOrderConfigTab
		gErr           *srerr.Error
		minuteRatioMap = make(map[int64]float64, 0)
	)
	condition := convertCondition(req.Id, req.MaskProductId, req.ConfType, req.ConfStatus)
	//1.get tab by conf type (normal or campaign)
	tab, gErr = b.BatchMinuteOrderConfRepo.GetTab(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get tab by condition:%v, err:%v", condition, gErr)
		return resp, gErr
	}
	if tab.Id == illegalId {
		//get by default type
		condition = convertCondition(illegalId, illegalId, batch_minute_order_conf.DefaultType, req.ConfStatus)
		tab, gErr = b.BatchMinuteOrderConfRepo.GetTab(ctx, condition)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "get tab by condition:%v, err:%v", condition, gErr)
			return resp, gErr
		}
	}

	//2.empty ratio map, return
	if len(tab.MinuteOrderRatio) == 0 {
		logger.CtxLogInfof(ctx, "tab's minute order ratio is empty")
		return resp, nil
	}
	//3.build result from tab
	if err := jsoniter.Unmarshal(tab.MinuteOrderRatio, &minuteRatioMap); err != nil {
		logger.CtxLogErrorf(ctx, "unmarshal ratio map err:%v", err)
		return resp, srerr.New(srerr.TypeConvertErr, nil, "unmarshal ratio map err")
	}
	for minute, ratio := range minuteRatioMap {
		resp.List = append(resp.List, allocation.BatchMinuteOrderConfUnit{
			Minute: minute,
			Ratio:  decimal.NewFromFloat(ratio).String(),
		})
	}
	sort.Slice(resp.List, func(i, j int) bool {
		return resp.List[i].Minute < resp.List[j].Minute
	})
	return resp, nil
}

func (b *BatchMinuteOrderConfServiceImpl) GetBatchMinuteOrderConfList(ctx context.Context, req allocation.GetMinuteOrderConfListReq) (allocation.GetMinuteOrderConfListResp, *srerr.Error) {
	return allocation.GetMinuteOrderConfListResp{}, nil
}

func (b *BatchMinuteOrderConfServiceImpl) CreateBatchMinuteOrderConf(ctx context.Context, req allocation.CreateMinuteOrderConfReq) *srerr.Error {
	//1.get excel from url
	//1. 下载s3文件
	defFile, err := httputil.Get(ctx, req.Url, nil, timeout, nil)
	if err != nil {
		return srerr.With(srerr.S3DownloadFail, req.Url, err)
	}
	//2. 解析excel文件，得到zone definition数据
	rows, tempHeaders, fErr := getFileData(ctx, req.Url, defFile)
	if fErr != nil {
		return fErr
	}
	//3.validate data
	if !checkDefHeaders(tempHeaders, batchConfHeader) {
		return srerr.New(srerr.ExcelValidateError, nil, "check header fail")
	}
	//4.convert to dto
	minuteRatioMap := make(map[int64]float64, 0)
	for _, row := range rows {
		minute, err := strconv.ParseInt(row[0], 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "parse row-minute:%v, err:%v", row, err)
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
		ratio, err := strconv.ParseFloat(row[1], 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "parse row-ratio:%v, err:%v", row, err)
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
		minuteRatioMap[minute] = ratio
	}
	confBytes, mErr := jsoniter.Marshal(minuteRatioMap)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "marshal err:%v", mErr)
		return srerr.With(srerr.TypeConvertErr, nil, mErr)
	}
	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	tab := &batch_minute_order_conf.BatchMinuteOrderConfigTab{
		MaskProductId:    req.MaskProductId,
		ConfType:         req.ConfType,
		MinuteOrderRatio: confBytes,
		ConfStatus:       batch_minute_order_conf.ConfActive,
		CTime:            nowTime,
		MTime:            nowTime,
	}
	// expire old conf while inserting a new one
	db, err := dbutil.MasterDB(ctx, batch_minute_order_conf.BatchMinuteOrderConfigTabHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "CreateBatchMinuteOrderConf|get master db err:%v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	ctx = scormv2.BindContext(ctx, db)
	txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		// search with same mask product, same conf type
		existedConf := batch_minute_order_conf.BatchMinuteOrderConfigTab{}
		if gErr := tx.Table(batch_minute_order_conf.BatchMinuteOrderConfigTabHook.TableName()).Debug().Where("mask_product_id = ? and conf_type = ? and conf_status = ?", req.MaskProductId, req.ConfType, batch_minute_order_conf.ConfActive).
			Find(&existedConf).GetError(); gErr != nil {

			if gErr != scormv2.ErrRecordNotFound {
				logger.CtxLogErrorf(ctx, "get tab err:%v", gErr)
				return srerr.With(srerr.DatabaseErr, nil, gErr)
			}
		}
		if existedConf.Id != illegalId {
			// expire existed conf
			existedConf.ConfStatus = batch_minute_order_conf.ConfExpired
			if uErr := tx.Table(batch_minute_order_conf.BatchMinuteOrderConfigTabHook.TableName()).Debug().
				Where("id = ?", existedConf.Id).Updates(&existedConf).GetError(); uErr != nil {

				logger.CtxLogErrorf(ctx, "update batch minute order conf to expired err:%v", uErr)
				return uErr
			}
		}
		//5.store into db
		if cErr := tx.Table(batch_minute_order_conf.BatchMinuteOrderConfigTabHook.TableName()).Debug().Create(&tab).GetError(); cErr != nil {
			logger.CtxLogErrorf(ctx, "create batch minute order conf err:%v", cErr)
			return cErr
		}

		if tx.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, nil, tx.GetError())
		}
		return nil
	})
	if txErr != nil {
		logger.CtxLogErrorf(ctx, "ParseBatchVolume| transaction err:%v", txErr)
		return srerr.With(srerr.DatabaseErr, nil, txErr)
	}

	return nil
}

func getFileData(ctx context.Context, url string, fileData []byte) ([][]string, []string, *srerr.Error) {
	rows, header, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(fileData), true)
	if fErr == nil {
		return rows, header, nil
	}
	if strings.Contains(url, ".csv?") {
		rows, header = fileutil.ReadCsv(fileData)
	}
	if strings.Contains(url, ".xls?") {
		rows, header, fErr = fileutil.ParseXlsFile(ctx, fileData)
	}
	if fErr != nil && len(rows) < 1 {
		return nil, nil, srerr.With(srerr.ParseExcelError, nil, fErr)
	}
	return rows, header, nil
}

func checkDefHeaders(headers1, headers2 []string) bool {
	if len(headers1) == 0 || len(headers2) == 0 {
		return false
	}
	if len(headers1) != len(headers2) {
		return false
	}
	for i := 0; i < len(headers1); i++ {
		if headers1[i] != headers2[i] {
			return false
		}
	}
	return true
}

func convertCondition(id, maskProductId uint64, confType, confStatus int) map[string]interface{} {
	condition := map[string]interface{}{}

	if id != 0 {
		condition["id = ?"] = id
	} else {
		condition["conf_type = ?"] = confType
		condition["conf_status = ?"] = confStatus
		if maskProductId != 0 {
			condition["mask_product_id = ?"] = maskProductId
		}
	}

	return condition
}

func ConvertUseForCampaign(useForCampaign bool) int {
	if useForCampaign {
		return batch_minute_order_conf.CampaignType
	}

	return batch_minute_order_conf.NormalType
}
