package service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

const (
	GeryByCount           = 0
	GeryByPercentage      = 1
	MaxGreyPercentage int = 10000
)

type GreyService interface {
	PassGrey(ctx context.Context, maskProduct int, randNum int) bool
}

type GreyServiceImpl struct {
}

func NewGreyServiceImpl() *GreyServiceImpl {
	return &GreyServiceImpl{}
}

func (i *GreyServiceImpl) PassGrey(ctx context.Context, maskProduct int, randNum int) bool {
	conf := configutil.GetBatchAllocateConf()
	greySwitch, exist := conf.GreySwitchMap[maskProduct]
	if !exist {
		logger.CtxLogErrorf(ctx, "PassGrey|mask product:%v, got no grey switch conf, return false", maskProduct)
		return false
	}

	switch greySwitch.GreyType {
	case GeryByCount:
		//todo
	case GeryByPercentage:
		if randNum < greySwitch.Percentage {
			return true
		}
	}

	return false
}
