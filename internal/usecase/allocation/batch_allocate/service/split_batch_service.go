package service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service/split_batch_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type SplitBatchServer interface {
	SplitOrdersIntoBatch(ctx context.Context, maskProductId uint64) *srerr.Error
}

type SplitBatchServerImpl struct {
	Executor split_batch_chain.Executor
}

func NewSplitBatchServerImpl(Executor split_batch_chain.Executor) *SplitBatchServerImpl {
	return &SplitBatchServerImpl{
		Executor: Executor,
	}
}

func (i *SplitBatchServerImpl) SplitOrdersIntoBatch(ctx context.Context, maskProductId uint64) *srerr.Error {
	//1.mask product id来源于saturn分片参数
	//2.将mask product id传进责任链，唤起责任链
	return i.Executor.ExecuteChain(ctx, maskProductId)
}
