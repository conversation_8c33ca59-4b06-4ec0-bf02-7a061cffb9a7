package split_batch_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"testing"
)

func Test_isSameDay(t *testing.T) {
	type args struct {
		lastOrderTable string
		timestamp      int64
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "hah",
			want: false,
			args: args{lastOrderTable: "batch_allocate_hold_order_tab_31_123123", timestamp: timeutil.GetCurrentUnixTimeStamp(context.Background())},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isNotSameDay(context.Background(), tt.args.lastOrderTable, tt.args.timestamp); got != tt.want {
				t.Errorf("isSameDay() = %v, want %v", got, tt.want)
			}
		})
	}
}
