package split_batch_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type ExecutorImpl struct {
	SplitBatchRepo         batch_allocate.SplitBatchRepo
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
	maskRuleRepo           rule.IMaskRuleRepo
}

func NewExecutorImpl(SplitBatchRepo batch_allocate.SplitBatchRepo,
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo,
	maskRuleRepo rule.IMaskRuleRepo) *ExecutorImpl {
	return &ExecutorImpl{
		SplitBatchRepo:         SplitBatchRepo,
		BatchAllocateOrderRepo: BatchAllocateOrderRepo,
		maskRuleRepo:           maskRuleRepo,
	}
}

func (e *ExecutorImpl) ExecuteChain(ctx context.Context, maskProductId uint64) *srerr.Error {
	//定义condition manager
	conditionManager := NewConditionManagerImpl(e.maskRuleRepo)
	//定义index Finder
	indexFinder := NewIndexFinderImpl(e.SplitBatchRepo, e.BatchAllocateOrderRepo)
	//定义batch manager
	batchManager := NewBatchManagerImpl(e.SplitBatchRepo)
	//组装责任链，condition manager -> index finder -> batch manager
	conditionManager.SetNext(indexFinder)
	indexFinder.SetNext(batchManager)
	//执行condition manager
	p := &JobParam{
		MaskProductID:       maskProductId,
		SysCurrentTimeStamp: timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	return conditionManager.ExecuteJob(ctx, p)
}
