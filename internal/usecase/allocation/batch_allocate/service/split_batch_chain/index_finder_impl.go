package split_batch_chain

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
	"time"
)

const (
	indexFinderName = "Index Finder"
	oneStep         = 1
	zero            = 0
	illegalID       = 0
	firstID         = 1
	totalLength     = 7
	dayIndex        = 6
)

type TempOrder struct {
	Ctime int64
}

//IndexFinder 探索切分index
type IndexFinderImpl struct {
	nextJob                Job
	SplitBatchRepo         batch_allocate.SplitBatchRepo
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
}

func NewIndexFinderImpl(SplitBatchRepo batch_allocate.SplitBatchRepo,
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo) *IndexFinderImpl {
	return &IndexFinderImpl{
		SplitBatchRepo:         SplitBatchRepo,
		BatchAllocateOrderRepo: BatchAllocateOrderRepo,
	}
}

/*
	跨天边界场景逻辑：
	1. last batch的ctime与current sys time是否同一天，否则按正常逻辑走
	2. 跨天：
		2.1 检查last batch后是否残留订单未分批，是则自成一批
		2.2 从当天（即跨天）的订单表开始，走常规分批逻辑
*/
func (i *IndexFinderImpl) ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error {
	logger.CtxLogInfof(ctx, "index finder start")
	//1.根据mask product id获取最后一个批次，得到last order id, last order ctime
	//SSCSMR-1695:找不到的话，last会返回错误 =》规避ErrRecordNotFound，用id进行判断获取不到
	lastBatch, gErr := i.SplitBatchRepo.GetLastBatchByConditionFromMaster(ctx, map[string]interface{}{
		"mask_product_id = ?": p.MaskProductID,
	})
	if gErr != nil {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.LastBatchNotExist,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "index finder| get last batch by mask product:%v, err:%v", p.MaskProductID, gErr)
		return srerr.With(srerr.GetLastBatchError, nil, gErr)
	}

	var (
		isFinish bool
		sErr     *srerr.Error
	)
	if isNotSameDay(ctx, lastBatch.LastOrderTableName, p.SysCurrentTimeStamp) {
		isFinish, sErr = i.splitYesterday(ctx, p, lastBatch)
		if sErr != nil {
			logger.CtxLogErrorf(ctx, "index finder| split yesterday batch err:%v", sErr)
			return sErr
		}
		// 如果昨天的订单仍有剩余（截断），直接进入下一个环节，不拆分今日订单
		if !isFinish {
			return i.nextJob.ExecuteJob(ctx, p)
		}
	}
	// 如果昨天的订单全分完了（没有截断），需要拆分今天的订单
	sErr = i.splitToday(ctx, p, lastBatch)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "index finder| split today batch err:%v", sErr)
		return sErr
	}

	return i.nextJob.ExecuteJob(ctx, p)
}

func (i *IndexFinderImpl) SetNext(job Job) {
	i.nextJob = job
}

func (i *IndexFinderImpl) IndexFinderName() string {
	return indexFinderName
}

/*
	常规逻辑：
	1. 根据last batch获取last order id
	2. 检索next order
		2.1 last batch为空，则初始化next order id = 1
	3. 根据匹配到的batch size time range进行等待，即sleep，确保能正确切分批次
	4. 获取当前系统时间，按hold单时间+next order id+next order table name检索订单，查看订单数，订单超过default quantity，直接截断并完成拆分
	5. 等待时间达到切分批次，并再次判断是否需要截断
*/
func (i *IndexFinderImpl) splitToday(ctx context.Context, p *JobParam, lastBatch batch_allocate.BAOnlineBatchTab) *srerr.Error {
	var (
		nextOrderID        uint64
		today              = timeutil.GetDayByTimeStamp(timeutil.GetCurrentUnixTimeStamp(ctx))
		nextOrderTableName = order.BatchAllocateHoldOrderTabHook.TableNameByPartition(int(p.MaskProductID), today)
		timeRange          int64
		sysCurrentMinute   = timeutil.GetMinuteByTimeStamp(p.SysCurrentTimeStamp)
	)
	//2. next order
	nextOrderID, gErr := i.getNextOrderID(ctx, lastBatch, nextOrderTableName)
	if gErr != nil {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.NextOrderNotExist,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "index finder|split today, get next order id err:%v", gErr)
		return gErr
	}
	if nextOrderID == illegalID {
		logger.CtxLogInfof(ctx, "index finder| get no next order, return")
		return srerr.New(srerr.GetNoOrderError, nil, "get no next order, return")
	}

	//3.遍历batch size，next order 的ctime在哪个fix_by_time_unit切片的区间内
	for _, fixByTimeUnit := range p.SplitCondition.FixByTimeList {
		if int64(sysCurrentMinute) >= fixByTimeUnit.StartTime && int64(sysCurrentMinute) <= fixByTimeUnit.EndTime {
			timeRange = fixByTimeUnit.TimeRange
			break
		}
	}
	logger.CtxLogInfof(ctx, "index finder| sys current minute:%v, time range:%v", sysCurrentMinute, timeRange)

	var lastOrderID uint64
	//4.检索id >= next order id, ctime < fix_by_time_unit的右区间，
	//且ctime < next order ctime + fix_by_time_unit 的time range；
	//得到新的last order id
	condition := map[string]interface{}{
		"id >= ?":    nextOrderID,
		"ctime <= ?": p.SysCurrentTimeStamp + timeRange,
	}
	lastOrderID, gErr = i.BatchAllocateOrderRepo.GetLastOrderIDFromMaster(ctx, condition, nextOrderTableName)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "index finder|condition:%v, next order table:%v, get last order id err:%v", condition, nextOrderTableName, gErr)
		return gErr
	}

	//5.判断[next order id, new last order id]是否超过default max quantity
	//5.1 超过则截断；
	conf := configutil.GetSplitBatchConf(ctx)
	if conf.DefaultMaxQuantity != zero && (lastOrderID-nextOrderID > uint64(conf.DefaultMaxQuantity)) {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.ExceedMaxQuantity,
			FieldValue:    "1",
		})
		logger.CtxLogInfof(ctx, "index finder|split today, next start id:%v, next end id:%v, will fill and go next stage", nextOrderID, lastOrderID)

		lastOrderID = nextOrderID + uint64(conf.DefaultMaxQuantity) - oneStep
		fillJobParam(p, nextOrderID, lastOrderID, nextOrderTableName, false)
		return nil
	}

	//5.2 未超过，等待
	sleepTime := timeutil.GetCurrentUnixTimeStamp(ctx) - p.SysCurrentTimeStamp
	if sleepTime < timeRange {
		logger.CtxLogInfof(ctx, "index finder|need to sleep %v(s)", timeRange-sleepTime)
		time.Sleep(time.Duration(timeRange-sleepTime) * time.Second)
	}

	//5.3 按照时间重新检索订单
	lastOrderID, gErr = i.BatchAllocateOrderRepo.GetLastOrderIDFromMaster(ctx, condition, nextOrderTableName)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "index finder|condition2:%v, next order table:%v, get last order id err:%v", condition, nextOrderTableName, gErr)
		return gErr
	}
	//5.4 超过则截断；
	if conf.DefaultMaxQuantity != zero && (lastOrderID-nextOrderID > uint64(conf.DefaultMaxQuantity)) {
		logger.CtxLogInfof(ctx, "index finder| next start id:%v, next end id:%v, finally will go next stage", nextOrderID, lastOrderID)
		lastOrderID = nextOrderID + uint64(conf.DefaultMaxQuantity) - oneStep
	}
	//6.将批次起止位置记录下来，传给下一块责任链
	fillJobParam(p, nextOrderID, lastOrderID, nextOrderTableName, false)

	return nil
}

/*
	边界逻辑：
	1. 根据last batch及table name获取next order id
	2. 若next order id不为0，则检索所有剩余订单，超过最大值则截断
	3. 记录该批次起止位置
*/
func (i *IndexFinderImpl) splitYesterday(ctx context.Context, p *JobParam, lastBatch batch_allocate.BAOnlineBatchTab) (bool, *srerr.Error) {
	var (
		nextOrderID, lastOrderID uint64
		nextOrderTableName       = lastBatch.LastOrderTableName
	)
	// 2. next order
	nextOrderID, gErr := i.getNextOrderID(ctx, lastBatch, nextOrderTableName)
	if gErr != nil {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.NextOrderNotExist,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "index finder| yesterday get next order id err:%v", gErr)
		return false, gErr
	}

	if nextOrderID == illegalID {
		logger.CtxLogInfof(ctx, "index finder| get no yesterday order")
		return true, nil
	}

	// 4.检索订单
	condition := map[string]interface{}{
		"id >= ?": nextOrderID,
	}
	lastOrderID, gErr = i.BatchAllocateOrderRepo.GetLastOrderIDFromMaster(ctx, condition, nextOrderTableName)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "index finder|condition:%v, next order table:%v, get last order id err:%v", condition, nextOrderTableName, gErr)
		return false, gErr
	}

	conf := configutil.GetSplitBatchConf(ctx)
	if conf.DefaultMaxQuantity != zero && (lastOrderID-nextOrderID > uint64(conf.DefaultMaxQuantity)) {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.ExceedMaxQuantity,
			FieldValue:    "1",
		})
		logger.CtxLogInfof(ctx, "index finder|split yesterday, next start id:%v, next end id:%v, will fill and go next stage", nextOrderID, lastOrderID)

		lastOrderID = nextOrderID + uint64(conf.DefaultMaxQuantity) - oneStep
		fillJobParam(p, nextOrderID, lastOrderID, nextOrderTableName, true)
		return false, nil
	}

	fillJobParam(p, nextOrderID, lastOrderID, nextOrderTableName, true)
	return true, nil
}

func (i *IndexFinderImpl) getNextOrderID(ctx context.Context, lastBatch batch_allocate.BAOnlineBatchTab, nextOrderTableName string) (uint64, *srerr.Error) {
	var tempNextOrderID uint64
	// batch流程启动后的第一批，或跨天后的第一批，需要从头开始检索
	if lastBatch.ID == illegalID || lastBatch.LastOrderTableName != nextOrderTableName {
		tempNextOrderID = firstID
	} else {
		//2.获取next order
		tempNextOrderID = lastBatch.LastOrderDbID + oneStep
	}
	logger.CtxLogInfof(ctx, "index finder| next order id:%v, next order table name:%v", tempNextOrderID, nextOrderTableName)
	//获取该order, 检索不到不会报错
	nextOrder, gErr := i.BatchAllocateOrderRepo.GetOrderByIDAndTableFromMaster(ctx, tempNextOrderID, nextOrderTableName)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "index finder| get next order info err:%v", gErr)
		return 0, gErr
	}

	return nextOrder.ID, nil
}

func fillJobParam(p *JobParam, firstOrderID, lastOrderID uint64, tableName string, isYesterday bool) {
	if isYesterday {
		p.YesterdayIndex = &Index{
			FirstOrderID:       firstOrderID,
			LastOrderID:        lastOrderID,
			LastOrderTableName: tableName,
		}
	} else {
		p.TodayIndex = &Index{
			FirstOrderID:       firstOrderID,
			LastOrderID:        lastOrderID,
			LastOrderTableName: tableName,
		}
	}
}

func isNotSameDay(ctx context.Context, lastOrderTable string, timestamp int64) bool {
	// last batch为空，不需要检索昨天的订单，直接返回
	if lastOrderTable == "" {
		return false
	}

	//batch_allocate_hold_order_tab_%02d_%08d
	strs := strings.Split(lastOrderTable, "_")
	if len(strs) != totalLength {
		logger.CtxLogInfof(ctx, "isSameDay|string:%v, timestamp:%v, got wrong string", lastOrderTable, timestamp)
		return true
	}
	day := timeutil.GetDayByTimeStamp(timestamp)

	return strs[dayIndex] != fmt.Sprintf("%08d", day)
}
