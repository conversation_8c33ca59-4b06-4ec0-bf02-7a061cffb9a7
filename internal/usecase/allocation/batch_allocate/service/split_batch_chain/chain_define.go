package split_batch_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type JobParam struct {
	MaskProductID       uint64 `json:"mask_product_id"`
	SplitCondition      Condition
	YesterdayIndex      *Index
	TodayIndex          *Index
	MaskRule            *rule.MaskRule
	SysCurrentTimeStamp int64
}

type (
	//拆分规则，包括hold单时长、默认最大数量等
	Condition struct {
		DefaultMaxQuantity int64
		FixByTimeList      []FixByTimeUnit
	}

	FixByTimeUnit struct {
		StartTime int64 `json:"start_time"` //时间区间的左区间，记录的是秒数
		EndTime   int64 `json:"end_time"`   //时间区间的右区间，记录的是秒数
		TimeRange int64 `json:"time_range"` //时间分片单元内的切片范围, 单位：秒
	}

	Index struct {
		FirstOrderID       uint64
		LastOrderID        uint64
		LastOrderTableName string
	}
)

//Job 定义责任链单元
type Job interface {
	ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error
	SetNext(job Job)
}

//ConditionManager 获取切分条件
type ConditionManager interface {
	ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error
	SetNext(job Job)
	ConditionManagerName() string
}

//IndexFinder 探索切分index
type IndexFinder interface {
	ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error
	SetNext(job Job)
	IndexFinderName() string
}

//BatchManager 管理批次
type BatchManager interface {
	ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error
	SetNext(job Job)
	BatchManagerName() string
}

//定义责任链唤起入口
type Executor interface {
	ExecuteChain(ctx context.Context, maskProductId uint64) *srerr.Error
}
