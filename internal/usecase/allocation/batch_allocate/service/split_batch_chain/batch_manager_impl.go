package split_batch_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const batchManagerName = "Batch Manager"

//BatchManager 管理批次
type BatchManagerImpl struct {
	nextJob        Job
	SplitBatchRepo batch_allocate.SplitBatchRepo
}

func NewBatchManagerImpl(SplitBatchRepo batch_allocate.SplitBatchRepo) *BatchManagerImpl {
	return &BatchManagerImpl{
		SplitBatchRepo: SplitBatchRepo,
	}
}

func (i *BatchManagerImpl) ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error {
	logger.CtxLogInfof(ctx, "batch manager start")
	//1.生成batch tab
	nowStamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	tabs := make([]batch_allocate.BAOnlineBatchTab, 0)
	if p.YesterdayIndex != nil {
		tabs = append(tabs, batch_allocate.BAOnlineBatchTab{
			MaskProductID:      p.MaskProductID,
			FirstOrderDbID:     p.YesterdayIndex.FirstOrderID,
			LastOrderDbID:      p.YesterdayIndex.LastOrderID,
			LastOrderTableName: p.YesterdayIndex.LastOrderTableName,
			BatchStatus:        batch_allocate.BatchPending,
			Ctime:              nowStamp,
			Mtime:              nowStamp,
		})
	}
	if p.TodayIndex != nil {
		tabs = append(tabs, batch_allocate.BAOnlineBatchTab{
			MaskProductID:      p.MaskProductID,
			FirstOrderDbID:     p.TodayIndex.FirstOrderID,
			LastOrderDbID:      p.TodayIndex.LastOrderID,
			LastOrderTableName: p.TodayIndex.LastOrderTableName,
			BatchStatus:        batch_allocate.BatchPending,
			Ctime:              nowStamp,
			Mtime:              nowStamp,
		})
	}

	if len(tabs) == 0 {
		logger.CtxLogErrorf(ctx, "got empty batch to insert")
		return srerr.New(srerr.ParamErr, nil, "got empty batch to insert")
	}
	//2.insert tab
	if cErr := i.SplitBatchRepo.CreateBatches(ctx, tabs); cErr != nil {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.InsertBatchError,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "batch manager|insert batch err:%v", cErr)
		return cErr
	}

	return nil
}
func (i *BatchManagerImpl) SetNext(job Job) {
	i.nextJob = job
}

func (i *BatchManagerImpl) BatchManagerName() string {
	return batchManagerName
}
