package split_batch_chain

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const (
	conditionManagerName = "Condition Manager"
)

//ConditionManagerImpl 获取切分条件
type ConditionManagerImpl struct {
	nextJob      Job
	maskRuleRepo rule.IMaskRuleRepo
}

func NewConditionManagerImpl(maskRuleRepo rule.IMaskRuleRepo) *ConditionManagerImpl {
	return &ConditionManagerImpl{
		maskRuleRepo: maskRuleRepo,
	}
}

func (i *ConditionManagerImpl) ExecuteJob(ctx context.Context, p *JobParam) *srerr.Error {
	//1.mask product id来源于saturn分片参数
	//2.获取mask对应的batch size规则, fix by time 为空报错
	//3.获取batch size, default max quantity, 组装condition，唤起责任链

	logger.CtxLogInfof(ctx, "condition manager start")

	condition := map[string]interface{}{
		"mask_product_id = ?":       p.MaskProductID,
		"rule_status = ?":           rule.MaskRuleStatusActive,
		"allocation_method = ?":     allocation.BatchAllocate,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStampUnWrapped(),
	}
	maskRule, gErr := i.maskRuleRepo.GetRuleByCondition(ctx, condition)
	if gErr != nil {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.RuleNotExist,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "condition manager|mask product id:%v, get mask rule err:%v", p.MaskProductID, gErr)
		return gErr
	}
	p.MaskRule = maskRule

	splitBatchConf := configutil.GetSplitBatchConf(ctx)
	if splitBatchConf.DefaultMaxQuantity == 0 {
		logger.CtxLogInfof(ctx, "condition manager|default max quantity is 0")
	}
	p.SplitCondition.DefaultMaxQuantity = splitBatchConf.DefaultMaxQuantity
	p.SplitCondition.FixByTimeList = make([]FixByTimeUnit, len(maskRule.BatchRuleDetail.BatchSize.FixedTime.FixedTimeUnitList))
	for i := 0; i < len(maskRule.BatchRuleDetail.BatchSize.FixedTime.FixedTimeUnitList); i++ {
		tempUnit := maskRule.BatchRuleDetail.BatchSize.FixedTime.FixedTimeUnitList[i]
		p.SplitCondition.FixByTimeList[i] = FixByTimeUnit{
			StartTime: tempUnit.StartTime,
			EndTime:   tempUnit.EndTime,
			TimeRange: tempUnit.TimeRange,
		}
	}
	if len(p.SplitCondition.FixByTimeList) == 0 {
		prometheusutil.IncrBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(p.MaskProductID, 10),
			Scene:         prometheusutil.SplitBatch,
			Field:         prometheusutil.FixByTimeNotExist,
			FieldValue:    "1",
		})
		logger.CtxLogErrorf(ctx, "condition manager|empty batch size")
		return srerr.New(srerr.DataErr, nil, "empty batch size")
	}

	//执行下一part
	return i.nextJob.ExecuteJob(ctx, p)
}

func (i *ConditionManagerImpl) SetNext(job Job) {
	i.nextJob = job
}

func (i *ConditionManagerImpl) ConditionManagerName() string {
	return conditionManagerName
}
