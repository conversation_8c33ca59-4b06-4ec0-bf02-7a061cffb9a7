package batch_allocate

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"github.com/google/wire"
)

var BatchMinuteOrderConfServiceProviderSet = wire.NewSet(
	NewBatchMinuteOrderConfServiceImpl,
	wire.Bind(new(BatchMinuteOrderConfService), new(*BatchMinuteOrderConfServiceImpl)))

var SplitBatchServerProvider = wire.NewSet(
	service.NewSplitBatchServerImpl,
	wire.Bind(new(service.SplitBatchServer), new(*service.SplitBatchServerImpl)),
)

var GreyServiceProviderSet = wire.NewSet(
	service.NewGreyServiceImpl,
	wire.Bind(new(service.GreyService), new(*service.GreyServiceImpl)),
)
