package allocation

import (
	_ "fmt"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/gogo/protobuf/proto"
	"sort"
)

/*

功能保证
	1.无交集 cntMap , cntMap key 为fullfillmetn-id ，for A-f-id ,only exist one Priority ✅
	2.priority 从1 开始连续 ✅
	3.无遗漏，包含全部的f-id-pass hard-cretiera, ✅ ，通过 initMap 保证

非功能保证
	1.性能
	2.并发 ✅
	3.panic ?

*/

func initSortedRes(pl []int64) map[int64]int {
	resMap := map[int64]int{}
	for _, productId := range pl {
		resMap[productId] = 0
	}
	return resMap
}

func incrCntMap(rm int, cntMap map[int64]int, productList []int64) {
	if rm != EstimateMaskingChannelRequest {
		return
	}
	for _, productId := range productList {
		cntMap[productId]++
	}
}

func mapToSortedRes(cntMap map[int64]int) []*pb.SortedResult {
	var resMap = map[int][]int64{}
	//sorted??,INCR ?? 0 ,保证priority INCR 连续,
	priorityList := []int{}
	for fulfillmentId, stepsPassed := range cntMap {
		if _, exist := resMap[stepsPassed]; !exist {
			resMap[stepsPassed] = []int64{fulfillmentId}
			priorityList = append(priorityList, stepsPassed)
		} else {
			resMap[stepsPassed] = append(resMap[stepsPassed], fulfillmentId)
		}
	}
	//done,
	sort.Slice(priorityList, func(i, j int) bool {
		return priorityList[i] > priorityList[j]
	})
	//fmt.Println("de1 ", resMap, priorityList)
	ret := make([]*pb.SortedResult, len(resMap))
	for k, originalPriotiry := range priorityList {
		temp := pb.SortedResult{}
		temp.Priority = proto.Int32(int32(k + 1))
		temp.DetailList = make([]*pb.Detail, len(resMap[originalPriotiry]))
		//fmt.Println("de2 ,", resMap[originalPriotiry])
		for lindex, fid := range resMap[originalPriotiry] {
			temp.DetailList[lindex] = new(pb.Detail)
			temp.DetailList[lindex].ShippingChannelId = proto.Int64(fid)
		}
		ret[k] = &temp
	}
	return ret
}
