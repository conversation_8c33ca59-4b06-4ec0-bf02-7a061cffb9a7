package allocation

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
)

func TestTask_GetResult(t *testing.T) {
	ctx := context.TODO()
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))

	mockChargeApi := &chargeclient.ChargeApiImpl{}
	task := &Task{
		service: &SoftRuleService{
			RateClient: mockChargeApi,
		},
	}
	var patch *gomonkey.Patches
	type args struct {
		productIDs         []int64
		orderInfo          *pb.MaskingOrderInfo
		maskProductID      int64
		shippingFeeConfigs []*allocation.ShippingFeeConfig
	}
	tests := []struct {
		name      string
		args      args
		setup     func()
		want      int64
		wantError *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: no available product",
			args: args{
				productIDs:         []int64{},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
			},
			want:      0,
			wantError: srerr.New(srerr.NoAvailableProduct, []int64{}, ""),
		},
		{
			name: "case 2: len(productIDs) == 1",
			args: args{
				productIDs:         []int64{2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}
			},
			want:      2,
			wantError: nil,
		},
		{
			name: "case 3: after filtered len(productIDs) == 1",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id: 1,
					MaskRuleSteps: []rule.MaskRuleStep{
						{MaskStepType: rule.MaskStepMaxCodCapacityCountry, MaxCodCapacityData: &rule.MaxCapacityData{}, IsCodHardCapacityData: &rule.IsHardCapacityData{}},
					},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 4: t.Rule.MaskRuleSteps = rule.MaskStepMinVolumeCountry",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id: 1,
					MaskRuleSteps: []rule.MaskRuleStep{
						{MaskStepType: rule.MaskStepMinVolumeCountry, MinVolumeData: &rule.MinVolumeData{}},
					},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 5: t.Rule.MaskRuleSteps = rule.MaskStepMaxCapacityCountry",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxCapacityCountry, MaxCapacityData: &rule.MaxCapacityData{}, IsHardCapacityData: &rule.IsHardCapacityData{}},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 6: t.Rule.MaskRuleSteps = rule.MaskStepMinVolumeZoneRoute",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMinVolumeZoneRoute, MaxCapacityData: &rule.MaxCapacityData{}},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 7: t.Rule.MaskRuleSteps = rule.MaskStepMaxCapacityZoneRoute",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxCapacityZoneRoute},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 8: t.Rule.MaskRuleSteps = rule.MaskStepMaxBatchVolume",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxBatchVolume, BatchVolumeData: &rule.BatchVolumeData{}},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 9: t.Rule.MaskRuleSteps = rule.MaskStepCheapestFee",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepCheapestFee, BatchVolumeData: &rule.BatchVolumeData{}},
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{}, nil
				})
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 10: t.Rule.MaskRuleSteps = rule.MaskStepPickupEfficiencyWhitelist",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepPickupEfficiencyWhitelist},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 11: t.Rule.MaskRuleSteps = rule.MaskStepMaxCodCapacityZoneRoute",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxCodCapacityZoneRoute},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 12: t.Rule.MaskRuleSteps = rule.MaskStepMaxBulkyCapacityCountry",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxBulkyCapacityCountry, MaxBulkyCapacityData: &rule.MaxCapacityData{},
						IsBulkyHardCapacityData: &rule.IsHardCapacityData{}},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 13: t.Rule.MaskRuleSteps = rule.MaskStepMaxBulkyCapacityZoneRoute",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxBulkyCapacityZoneRoute},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 14: t.Rule.MaskRuleSteps = rule.MaskStepMaxHighValueCapacityCountry",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxHighValueCapacityCountry, MaxHighValueCapacityData: &rule.MaxCapacityData{},
						IsHighValueHardCapacityData: &rule.IsHardCapacityData{}},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 15: t.Rule.MaskRuleSteps = rule.MaskStepMaxHighValueCapacityZoneRoute",
			args: args{
				productIDs:         []int64{3, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{
					{MaskStepType: rule.MaskStepMaxHighValueCapacityZoneRoute},
				}
			},
			want:      3,
			wantError: nil,
		},
		{
			name: "case 16: Product priority not enabled, returning first channel",
			args: args{
				productIDs:         []int64{1, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{}
			},
			want:      1,
			wantError: nil,
		},
		{
			name: "case 17: t.DefaultRuleType = entity.Priority",
			args: args{
				productIDs:         []int64{1, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{}
				task.Rule.EnableProductPriority = true
				task.DefaultRuleType = entity.Priority
			},
			want:      1,
			wantError: nil,
		},
		{
			name: "case 18: t.DefaultRuleType = entity.Weightage",
			args: args{
				productIDs:         []int64{1, 2},
				orderInfo:          &pb.MaskingOrderInfo{},
				maskProductID:      1,
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{},
			},
			setup: func() {
				task.Rule = &rule.MaskRule{
					Id:                    1,
					MaskRuleSteps:         []rule.MaskRuleStep{},
					EnableProductPriority: false,
				}
				task.attrMap = map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				}

				task.Rule.MaskRuleSteps = []rule.MaskRuleStep{}
				task.Rule.EnableProductPriority = true
				task.DefaultRuleType = entity.Weightage
				task.reqType = EstimateMaskingChannelRequest
			},
			wantError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := task.GetResult(ctx, tt.args.productIDs, tt.args.orderInfo, tt.args.maskProductID, tt.args.shippingFeeConfigs)
			if tt.name == "case 18: t.DefaultRuleType = entity.Weightage" {
				t.Logf("GetResult() = %v", got)
				common.AssertResult(t, "", "", gotErr, tt.wantError)
			} else {
				common.AssertResult(t, got, tt.want, gotErr, tt.wantError)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestTask_filterByPriority(t *testing.T) {
	task := &Task{
		productPriorities: map[int64]int32{
			2: 2,
			3: 3,
		},
	}

	type args struct {
		productIDs []int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		// TODO: Add test cases.
		{
			name: "case 1: filter by priority",
			args: args{
				productIDs: []int64{1, 2, 3},
			},
			want: 2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, task.filterByPriority(tt.args.productIDs), "filterByPriority(%v)", tt.args.productIDs)
		})
	}
}

func TestTask_filterByWeightage(t *testing.T) {
	ctx := context.TODO()

	task := &Task{
		productWeightages: map[int64]int32{
			1: 1,
		},
	}

	type args struct {
		productIDs []int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		// TODO: Add test cases.
		{
			name: "case 1: filter by weightage",
			args: args{
				productIDs: []int64{1, 2},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, task.filterByWeightage(ctx, tt.args.productIDs), "filterByWeightage()")
		})
	}
}

func TestTask_filterByCountryVolume(t *testing.T) {
	ctx := context.Background()
	var task *Task
	var patchGetAllocationLogFromContext, patchGetProductVolume *gomonkey.Patches
	type args struct {
		maskProductID           int64
		products                []int64
		volumeThreshold         map[rule.VolumeKey]int32
		groupVolumeThreshold    map[string]int32
		isHardCap               map[rule.VolumeKey]bool
		groupIsHardCap          map[string]bool
		volType                 volumeFilterType
		productGroupCodeMapping map[int64]string
		attrMap                 map[int64]*parcel_type_definition.ParcelTypeAttr
		filterParcelType        parcel_type_definition.ParcelType
	}
	tests := []struct {
		name  string
		args  args
		want  []int64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: ProductVolume filtered != nil",
			args: args{
				products:         []int64{1, 2, 3, 4, 5},
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
					3: {
						IsCod: true,
					},
					4: {
						IsCod: true,
					},
					5: {
						IsCod: true,
					},
				},

				productGroupCodeMapping: map[int64]string{
					2: "group2",
					4: "group4",
					5: "group5",
				},
				groupVolumeThreshold: map[string]int32{
					"group4": 0,
					"group5": 10,
				},
			},
			want: []int64{1, 5},
			setup: func() {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
				patchGetProductVolume = gomonkey.ApplyMethod(reflect.TypeOf(task), "GetProductVolume",
					func(a *Task, ctx context.Context, maskProductID, id int64, groupCode string, parcelType parcel_type_definition.ParcelType) int32 {
						return 0
					})
			},
		},
		{
			name: "case 2: hard cap filtered != nil",
			args: args{
				products:         []int64{2, 3, 4},
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
					3: {
						IsCod: true,
					},
					4: {
						IsCod: true,
					},
				},

				productGroupCodeMapping: map[int64]string{
					2: "group2",
					4: "group4",
				},
				volType: volumeFilterTypeMaxCapacity,
				groupIsHardCap: map[string]bool{
					"group2": true,
				},
				isHardCap: map[rule.VolumeKey]bool{
					rule.VolumeKey{MaskProductID: 0, FulfillmentProductID: 3}: true,
				},
			},
			want: []int64{4},
			setup: func() {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 3: hard cap filtered == nil",
			args: args{
				products:         []int64{2},
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				},
				productGroupCodeMapping: map[int64]string{
					2: "group2",
				},
				volType: volumeFilterTypeMinVolume,
			},
			want: []int64{2},
			setup: func() {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			assert.Equalf(t, tt.want, task.filterByCountryVolume(ctx, tt.args.maskProductID, tt.args.products, tt.args.volumeThreshold, tt.args.groupVolumeThreshold, tt.args.isHardCap, tt.args.groupIsHardCap, tt.args.volType, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType), "filterByCountryVolume(%v, %v, %v, %v, %v, %v, %v, %v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.products, tt.args.volumeThreshold, tt.args.groupVolumeThreshold, tt.args.isHardCap, tt.args.groupIsHardCap, tt.args.volType, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType)
			if patchGetAllocationLogFromContext != nil {
				patchGetAllocationLogFromContext.Reset()
			}
			if patchGetProductVolume != nil {
				patchGetProductVolume.Reset()
			}
		})
	}
}

func TestTask_needFilterByParcelType(t *testing.T) {
	var task *Task
	type args struct {
		productId        int64
		attrMap          map[int64]*parcel_type_definition.ParcelTypeAttr
		filterParcelType parcel_type_definition.ParcelType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: filterParcelType == parcel_type_definition.ParcelTypeNone, need filter by parcel type",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeNone,
			},
			want: true,
		},
		{
			name: "case 2: attrMap[productId] not exist",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeCod,
			},
			want: false,
		},
		{
			name: "case 3: filterParcelType == parcel_type_definition.ParcelTypeCod, need filter by parcel type",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					0: {
						IsCod: true,
					},
				},
			},
			want: true,
		},
		{
			name: "case 4: filterParcelType == parcel_type_definition.ParcelTypeBulky, need filter by parcel type",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeBulky,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					0: {
						IsBulky: true,
					},
				},
			},
			want: true,
		},
		{
			name: "case 5: filterParcelType == parcel_type_definition.ParcelTypeHighValue, need filter by parcel type",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeHighValue,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					0: {
						IsHighValue: true,
					},
				},
			},
			want: true,
		},
		{
			name: "case 6: other filterParcelType",
			args: args{
				filterParcelType: parcel_type_definition.ParcelTypeHighValue,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					0: {},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, task.needFilterByParcelType(tt.args.productId, tt.args.attrMap, tt.args.filterParcelType), "needFilterByParcelType(%v, %v, %v)", tt.args.productId, tt.args.attrMap, tt.args.filterParcelType)
		})
	}
}

func TestTask_filterByZoneRouteVolume(t *testing.T) {
	ctx := context.Background()
	var patchGetAllocationLogFromContext, patchZoneRouteIndependentFilter *gomonkey.Patches
	type args struct {
		maskProductID           int64
		products                []int64
		volType                 volumeFilterType
		productGroupCodeMapping map[int64]string
		attrMap                 map[int64]*parcel_type_definition.ParcelTypeAttr
		filterParcelType        parcel_type_definition.ParcelType
	}
	tests := []struct {
		name  string
		task  *Task
		args  args
		want  []int64
		setup func(task *Task)
	}{
		// TODO: Add test cases.
		{
			name: "case 1: zone volume len(filtered) == 1",
			task: &Task{},
			args: args{
				products: []int64{0},
			},
			want: []int64{0},
			setup: func(task *Task) {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 2: route volume len(filtered) != 0",
			task: &Task{},
			args: args{
				products: []int64{0, 1},
			},
			want: []int64{0, 1},
			setup: func(task *Task) {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 3: all products were filtered out",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					2: {RouteVolumes: []*rulevolume.MaskLocVolume{
						{
							RouteVolume: &rulevolume.MaskRouteVolume{},
						},
					}},
					3: {RouteVolumes: []*rulevolume.MaskLocVolume{
						{
							RouteVolume: &rulevolume.MaskRouteVolume{},
						},
					}},
				},
			},
			args: args{
				products:         []int64{2, 3},
				volType:          3,
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
					3: {
						IsCod: true,
					},
				},
			},
			want: []int64{2, 3},
			setup: func(task *Task) {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
				patchZoneRouteIndependentFilter = gomonkey.ApplyMethod(reflect.TypeOf(task), "ZoneRouteIndependentFilter",
					func(t *Task, ctx context.Context, maskProductID int64, products []int64, volType volumeFilterType, locType rulevolume.MaskLocType,
						currentVolumes map[string]int32, productGroupCodeMapping map[int64]string,
						attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, filterParcelType parcel_type_definition.ParcelType,
					) ([]int64, bool) {
						return []int64{}, false
					})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(tt.task)
			assert.Equalf(t, tt.want, tt.task.filterByZoneRouteVolume(ctx, tt.args.maskProductID, tt.args.products, tt.args.volType, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType), "filterByZoneRouteVolume(%v, %v, %v, %v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.products, tt.args.volType, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType)
			if patchGetAllocationLogFromContext != nil {
				patchGetAllocationLogFromContext.Reset()
			}
			if patchZoneRouteIndependentFilter != nil {
				patchZoneRouteIndependentFilter.Reset()
			}
		})
	}
}

func TestTask_zoneRouteIndependentFilter(t *testing.T) {
	ctx := context.Background()

	mockMaskVolumeCounter := &volumecounter.MaskVolumeCounterImpl{}
	var patchGetProductZoneVolume *gomonkey.Patches
	type args struct {
		maskProductID           int64
		products                []int64
		volType                 volumeFilterType
		locType                 rulevolume.MaskLocType
		currentVolumes          map[string]int32
		productGroupCodeMapping map[int64]string
		attrMap                 map[int64]*parcel_type_definition.ParcelTypeAttr
		filterParcelType        parcel_type_definition.ParcelType
	}
	tests := []struct {
		name        string
		task        *Task
		args        args
		want        []int64
		wantAllFail bool
		setup       func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: filterByLocVolume filtered != nil",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					2: {ZoneVolumes: []*rulevolume.MaskLocVolume{{}}},
				},
			},
			args: args{
				products:         []int64{1, 2, 3},
				locType:          rulevolume.MaskLocTypeZone,
				volType:          3,
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
					3: {
						IsCod: true,
					},
				},
			},
			want:        []int64{1, 3},
			wantAllFail: false,
			setup:       func() {},
		},
		{
			name: "case 2: hard cap filtered != nil",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					2: {ZoneVolumes: []*rulevolume.MaskLocVolume{{
						LocType:       rulevolume.MaskLocTypeZone,
						ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
						ZoneVolume: &rulevolume.MaskZoneVolume{
							IsHardCap: true,
						},
					}}},
					3: {ZoneVolumes: []*rulevolume.MaskLocVolume{{}}},
				},
				VolumeCounter: mockMaskVolumeCounter,
			},
			args: args{
				products:         []int64{2, 3},
				locType:          rulevolume.MaskLocTypeZone,
				volType:          volumeFilterTypeMaxCapacity,
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				currentVolumes:   map[string]int32{},
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
					3: {
						IsCod: true,
					},
				},
			},
			want:        []int64{3},
			wantAllFail: false,
			setup: func() {
				patchGetProductZoneVolume = gomonkey.ApplyMethod(reflect.TypeOf(mockMaskVolumeCounter), "GetProductZoneVolume",
					func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskProductID, id int64,
						zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode,
						parcelType parcel_type_definition.ParcelType) (int32, error) {
						return 0, nil
					})
			},
		},
		{
			name: "case 3: hard cap filtered == nil",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					2: {ZoneVolumes: []*rulevolume.MaskLocVolume{{}}},
				},
			},
			args: args{
				products:         []int64{2},
				locType:          rulevolume.MaskLocTypeZone,
				volType:          3,
				filterParcelType: parcel_type_definition.ParcelTypeCod,
				attrMap: map[int64]*parcel_type_definition.ParcelTypeAttr{
					2: {
						IsCod: true,
					},
				},
			},
			want:        []int64{2},
			wantAllFail: true,
			setup:       func() {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, wantAllFail := tt.task.ZoneRouteIndependentFilter(ctx, tt.args.maskProductID, tt.args.products, tt.args.volType, tt.args.locType, tt.args.currentVolumes, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType)
			assert.Equalf(t, tt.want, got, "zoneRouteIndependentFilter(%v, %v, %v, %v, %v, %v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.products, tt.args.volType, tt.args.locType, tt.args.currentVolumes, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType)
			assert.Equalf(t, tt.wantAllFail, wantAllFail, "zoneRouteIndependentFilter(%v, %v, %v, %v, %v, %v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.products, tt.args.volType, tt.args.locType, tt.args.currentVolumes, tt.args.productGroupCodeMapping, tt.args.attrMap, tt.args.filterParcelType)
			if patchGetProductZoneVolume != nil {
				patchGetProductZoneVolume.Reset()
			}
		})
	}
}

func TestTask_filterByLocVolume(t *testing.T) {
	ctx := context.Background()
	mockMaskVolumeCounter := &volumecounter.MaskVolumeCounterImpl{}
	task := &Task{}

	var patchGetGroupRouteVolume, patchGetGroupZoneVolume, patchGetProductRouteVolume, patchGetProductZoneVolume *gomonkey.Patches
	type args struct {
		maskProductID    int64
		productId        int64
		volType          volumeFilterType
		locType          rulevolume.MaskLocType
		counterForLog    map[string]int32
		groupCode        string
		filterParcelType parcel_type_definition.ParcelType
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: t.LocationVolumes[productId] not exist",
			args: args{},
			want: true,
			setup: func() {
				task = &Task{}
			},
		},
		{
			name: "case 2: len(locVols) == 0",
			args: args{},
			want: true,
			setup: func() {
				task = &Task{}
				task.LocationVolumes = map[int64]rulevolume.ProductLocVolumes{
					0: {},
				}
			},
		},
		{
			name: "case 3: groupCode is not blank",
			args: args{
				locType:       rulevolume.MaskLocTypeZone,
				volType:       volumeFilterTypeMinVolume,
				groupCode:     "group1",
				counterForLog: map[string]int32{},
			},
			want: false,
			setup: func() {
				task = &Task{}
				task.LocationVolumes = map[int64]rulevolume.ProductLocVolumes{
					0: {ZoneVolumes: []*rulevolume.MaskLocVolume{
						{
							LocType: rulevolume.MaskLocTypeRoute,
							RouteVolume: &rulevolume.MaskRouteVolume{
								MinVolume: 1,
							},
						},
						{
							LocType: rulevolume.MaskLocTypeZone,
							ZoneVolume: &rulevolume.MaskZoneVolume{
								OriginMinVolume: 1,
							},
							ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
						},
						{
							LocType: rulevolume.MaskLocTypeZone,
							ZoneVolume: &rulevolume.MaskZoneVolume{
								DestMinVolume: -1,
							},
							ZoneDirection: rulevolume.MaskZoneDirectionDest,
						},
					}},
				}
				task.VolumeCounter = mockMaskVolumeCounter
				patchGetGroupRouteVolume = gomonkey.ApplyMethod(reflect.TypeOf(mockMaskVolumeCounter), "GetGroupRouteVolume",
					func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, groupCode, routeCode string, rm rule_mode.RuleMode,
						parcelType parcel_type_definition.ParcelType) (int32, error) {
						return 0, nil
					})
				patchGetGroupZoneVolume = gomonkey.ApplyMethod(reflect.TypeOf(mockMaskVolumeCounter), "GetGroupZoneVolume",
					func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, groupCode, zoneCode string, direction rulevolume.MaskZoneDirection,
						rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
						return 0, nil
					})
			},
		},
		{
			name: "case 4: groupCode is blank",
			args: args{
				locType:          rulevolume.MaskLocTypeRoute,
				volType:          volumeFilterTypeMaxCapacity,
				filterParcelType: 4,
				counterForLog:    map[string]int32{},
			},
			want: true,
			setup: func() {
				task = &Task{}
				task.LocationVolumes = map[int64]rulevolume.ProductLocVolumes{
					0: {RouteVolumes: []*rulevolume.MaskLocVolume{
						{
							LocType:     rulevolume.MaskLocTypeRoute,
							RouteVolume: &rulevolume.MaskRouteVolume{},
						},
						{
							LocType:       rulevolume.MaskLocTypeZone,
							ZoneVolume:    &rulevolume.MaskZoneVolume{},
							ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
						},
						{
							LocType:       rulevolume.MaskLocTypeZone,
							ZoneVolume:    &rulevolume.MaskZoneVolume{},
							ZoneDirection: rulevolume.MaskZoneDirectionDest,
						},
					}},
				}
				task.VolumeCounter = mockMaskVolumeCounter
				patchGetProductRouteVolume = gomonkey.ApplyMethod(reflect.TypeOf(mockMaskVolumeCounter), "GetProductRouteVolume",
					func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskProductID, id int64, routeCode string,
						rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
						return -1, nil
					})
				patchGetProductZoneVolume = gomonkey.ApplyMethod(reflect.TypeOf(mockMaskVolumeCounter), "GetProductZoneVolume",
					func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskProductID, id int64, zoneCode string,
						direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
						return -1, nil
					})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			assert.Equalf(t, tt.want, task.filterByLocVolume(ctx, tt.args.maskProductID, tt.args.productId, tt.args.volType, tt.args.locType, tt.args.counterForLog, tt.args.groupCode, tt.args.filterParcelType), "filterByLocVolume(%v, %v, %v, %v, %v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.productId, tt.args.volType, tt.args.locType, tt.args.counterForLog, tt.args.groupCode, tt.args.filterParcelType)

			if patchGetGroupRouteVolume != nil {
				patchGetGroupRouteVolume.Reset()
			}
			if patchGetGroupZoneVolume != nil {
				patchGetGroupZoneVolume.Reset()
			}
			if patchGetProductRouteVolume != nil {
				patchGetProductRouteVolume.Reset()
			}
			if patchGetProductZoneVolume != nil {
				patchGetProductZoneVolume.Reset()
			}
		})
	}
}

func TestTask_filterByLocHardCap(t *testing.T) {
	type args struct {
		productId        int64
		locType          rulevolume.MaskLocType
		filterParcelType parcel_type_definition.ParcelType
		setup            func()
	}
	tests := []struct {
		name string
		task *Task
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: LocationVolumes[productId] not exist",
			task: &Task{},
			args: args{},
			want: true,
		},
		{
			name: "case 2: len(locVols) == 0",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					0: {ZoneVolumes: []*rulevolume.MaskLocVolume{}},
				},
			},
			args: args{
				locType: rulevolume.MaskLocTypeZone,
			},
			want: true,
		},
		{
			name: "case 3: locVol.ZoneDirection == rulevolume.MaskZoneDirectionOrigin && locVol.ZoneVolume.IsHardCap == true",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					0: {ZoneVolumes: []*rulevolume.MaskLocVolume{
						{
							LocType:       rulevolume.MaskLocTypeZone,
							ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
							ZoneVolume: &rulevolume.MaskZoneVolume{
								IsHardCap: true,
							},
						},
					}},
				},
			},
			args: args{
				locType: rulevolume.MaskLocTypeZone,
			},
			want: false,
		},
		{
			name: "case 4: locVol.ZoneDirection == rulevolume.MaskZoneDirectionDest && isHardCap == false",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					0: {ZoneVolumes: []*rulevolume.MaskLocVolume{
						{
							LocType:       rulevolume.MaskLocTypeZone,
							ZoneDirection: rulevolume.MaskZoneDirectionDest,
							ZoneVolume:    &rulevolume.MaskZoneVolume{},
						},
					}},
				},
			},
			args: args{
				locType: rulevolume.MaskLocTypeZone,
			},
			want: true,
		},
		{
			name: "case 5: MaskLocTypeRoute",
			task: &Task{
				LocationVolumes: map[int64]rulevolume.ProductLocVolumes{
					0: {RouteVolumes: []*rulevolume.MaskLocVolume{
						{
							LocType:     rulevolume.MaskLocTypeRoute,
							RouteVolume: &rulevolume.MaskRouteVolume{},
						},
					}},
				},
			},
			args: args{
				locType: rulevolume.MaskLocTypeRoute,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.task.filterByLocHardCap(tt.args.productId, tt.args.locType, tt.args.filterParcelType), "filterByLocHardCap(%v, %v, %v)", tt.args.productId, tt.args.locType, tt.args.filterParcelType)
		})
	}
}

func TestTask_filterByVolumeInBatch(t *testing.T) {
	ctx := context.Background()
	var patchGetAllocationLogFromContext, patchGetProductVolumeInBatch *gomonkey.Patches
	type args struct {
		products        []int64
		batchSize       int32
		volumeThreshold map[int64]int32
		maskProductID   int64
	}
	tests := []struct {
		name  string
		task  *Task
		args  args
		want  []int64
		setup func(task *Task)
	}{
		// TODO: Add test cases.
		{
			name: "case 1: filtered != nil",
			task: &Task{},
			args: args{
				products:        []int64{1, 2},
				volumeThreshold: map[int64]int32{2: 2},
			},
			want: []int64{2},
			setup: func(task *Task) {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
				patchGetProductVolumeInBatch = gomonkey.ApplyMethod(reflect.TypeOf(task), "GetProductVolumeInBatch", func(t *Task, ctx context.Context, id int64, maskProductID int64) int32 {
					return 1
				})
			},
		},
		{
			name: "case 2: filtered == nil",
			task: &Task{},
			args: args{},
			want: nil,
			setup: func(task *Task) {
				patchGetAllocationLogFromContext = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(tt.task)
			assert.Equalf(t, tt.want, tt.task.filterByVolumeInBatch(ctx, tt.args.products, tt.args.batchSize, tt.args.volumeThreshold, tt.args.maskProductID), "filterByVolumeInBatch(%v, %v, %v, %v, %v)", ctx, tt.args.products, tt.args.batchSize, tt.args.volumeThreshold, tt.args.maskProductID)
			if patchGetAllocationLogFromContext != nil {
				patchGetAllocationLogFromContext.Reset()
			}
			if patchGetProductVolumeInBatch != nil {
				patchGetProductVolumeInBatch.Reset()
			}
		})
	}
}

func TestTask_filterByCheapestFee(t *testing.T) {
	ctx := context.Background()
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	mockChargeApi := &chargeclient.ChargeApiImpl{}
	mockMaskVolumeCounter := &volumecounter.MaskVolumeCounterImpl{}

	var patch, patchBatchAllocatingESFWithGrpc *gomonkey.Patches
	type args struct {
		products           []int64
		info               *pb.MaskingOrderInfo
		shippingFeeConfigs []*allocation.ShippingFeeConfig
	}
	tests := []struct {
		name  string
		task  *Task
		args  args
		want  []int64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: len(products) <= 1",
			args:  args{},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 2: err != nil || rateResp == nil",
			task: &Task{},
			args: args{
				products:           []int64{1, 2},
				shippingFeeConfigs: []*allocation.ShippingFeeConfig{{}},
			},
			want: []int64{1, 2},
			setup: func() {
				patch = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 3: Cheapest fee get esf from rateapi not success",
			task: &Task{
				service: NewSoftRuleService(mockMaskVolumeCounter, mockChargeApi, &whitelist2.ShopWhitelistServiceImpl{}),
			},
			args: args{
				products: []int64{1, 2},
				info:     &pb.MaskingOrderInfo{},
			},
			want: []int64{1, 2},
			setup: func() {
				patchBatchAllocatingESFWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{}, nil
				})
				patch = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 4: len(minFeeProducts) != 0",
			task: &Task{
				service: NewSoftRuleService(mockMaskVolumeCounter, mockChargeApi, &whitelist2.ShopWhitelistServiceImpl{}),
			},
			args: args{
				products: []int64{1, 2},
				info:     &pb.MaskingOrderInfo{},
			},
			want: []int64{1, 2},
			setup: func() {
				patchBatchAllocatingESFWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{
						Data: &chargeentity.BatchAllocationESFRespData{
							AllocatingShippingFeeResult: []*chargeentity.AllocatingShippingFeeResultItem{
								{
									RetCode: -1,
								},
								{
									ProductId: 1,
								},
								{
									ProductId: 2,
								},
							},
						},
					}, nil
				})
				patch = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
		{
			name: "case 5: len(minFeeProducts) == 0",
			task: &Task{
				service: NewSoftRuleService(mockMaskVolumeCounter, mockChargeApi, &whitelist2.ShopWhitelistServiceImpl{}),
			},
			args: args{
				products: []int64{1, 2},
				info:     &pb.MaskingOrderInfo{},
			},
			want: []int64{1, 2},
			setup: func() {
				patchBatchAllocatingESFWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(mockChargeApi), "BatchAllocatingESFWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, req *chargeentity.BatchAllocationESFReq) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
					return &chargeentity.BatchAllocationESFResp{
						Data: &chargeentity.BatchAllocationESFRespData{
							AllocatingShippingFeeResult: []*chargeentity.AllocatingShippingFeeResultItem{
								{
									RetCode: -1,
								},
							},
						},
					}, nil
				})
				patch = gomonkey.ApplyFunc(GetAllocationLogFromContext, func(ctx context.Context) *Log {
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		tt.setup()
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.task.filterByCheapestFee(ctx, tt.args.products, tt.args.info, tt.args.shippingFeeConfigs), "filterByCheapestFee(%v, %v, %v, %v)", ctx, tt.args.products, tt.args.info, tt.args.shippingFeeConfigs)
		})
		if patch != nil {
			patch.Reset()
		}
		if patchBatchAllocatingESFWithGrpc != nil {
			patchBatchAllocatingESFWithGrpc.Reset()
		}
	}
}

func TestTask_filterByPickupEfficiencyWhitelist(t *testing.T) {
	ctx := context.Background()
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))

	var patch *gomonkey.Patches
	type args struct {
		maskProductID int64
		products      []int64
		info          *pb.MaskingOrderInfo
	}
	tests := []struct {
		name  string
		task  *Task
		args  args
		want  []int64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: len(checkoutItems) == 0",
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 2: no_meet_whitelist",
			task: &Task{},
			args: args{
				info: &pb.MaskingOrderInfo{
					CheckoutItems: []*pb.CheckOutItem{
						{},
					},
				},
			},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 3: get_priority_err",
			task: &Task{},
			args: args{
				info: &pb.MaskingOrderInfo{
					CheckoutItems: []*pb.CheckOutItem{
						{},
					},
				},
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					if namespace == constant.MaskingWhitelist {
						return whitelist.BothFulfillment, nil
					} else if namespace == constant.PickupPriority {
						return 1, errors.New("mock localcache error")
					}
					return nil, nil
				})
			},
		},
		{
			name: "case 4: product no_priority",
			task: &Task{},
			args: args{
				info: &pb.MaskingOrderInfo{
					CheckoutItems: []*pb.CheckOutItem{
						{},
					},
				},
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					if namespace == constant.MaskingWhitelist {
						return whitelist.BothFulfillment, nil
					} else if namespace == constant.PickupPriority {
						return pickup_priority.PickupPriorityTab{}, nil
					}
					return nil, nil
				})
			},
		},
		{
			name: "case 5: product no_priority",
			task: &Task{},
			args: args{
				products: []int64{1, 2},
				info: &pb.MaskingOrderInfo{
					CheckoutItems: []*pb.CheckOutItem{
						{},
					},
				},
			},
			want: []int64{1},
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					if namespace == constant.MaskingWhitelist {
						return whitelist.BothFulfillment, nil
					} else if namespace == constant.PickupPriority {
						return pickup_priority.PickupPriorityTab{
							ProductPriorityDto: pickup_priority.ProductPriority{
								List: []pickup_priority.Priority{
									{
										ProductId:        1,
										PrioritySequence: 1,
									},
									{
										ProductId:        2,
										PrioritySequence: 2,
									},
								},
							},
						}, nil
					}
					return nil, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			assert.Equalf(t, tt.want, tt.task.filterByPickupEfficiencyWhitelist(ctx, tt.args.maskProductID, tt.args.products, tt.args.info), "filterByPickupEfficiencyWhitelist(%v, %v, %v, %v)", ctx, tt.args.maskProductID, tt.args.products, tt.args.info)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestTask_GetOrderType(t *testing.T) {

	tests := []struct {
		name string
		task *Task
		want whitelist.FulfillmentType
	}{
		// TODO: Add test cases.
		{
			name: "case 1: whitelist.IllegalType",
			task: &Task{},
			want: whitelist.IllegalType,
		},
		{
			name: "case 1: whitelist.MPLFulfillment",
			task: &Task{businessType: constant.AllocateMasking},
			want: whitelist.MPLFulfillment,
		},
		{
			name: "case 1: whitelist.WMSFulfillment",
			task: &Task{businessType: constant.EstimateMasking},
			want: whitelist.WMSFulfillment,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.task.GetOrderType(), "GetOrderType()")
		})
	}
}

func TestTask_getRouteHardCapByParcelType(t *testing.T) {

	type args struct {
		locVolume  *rulevolume.MaskLocVolume
		parcelType parcel_type_definition.ParcelType
	}
	tests := []struct {
		name string
		task *Task
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: getRouteHardCapByParcelType false",
			args: args{
				parcelType: -1,
			},
			want: false,
		},
		{
			name: "case 2: parcel_type_definition.ParcelTypeNone",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeNone,
				locVolume: &rulevolume.MaskLocVolume{
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
			},
			want: false,
		},
		{
			name: "case 3: parcel_type_definition.ParcelTypeCod",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeCod,
				locVolume: &rulevolume.MaskLocVolume{
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
			},
			want: false,
		},
		{
			name: "case 4: parcel_type_definition.ParcelTypeBulky",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeBulky,
				locVolume: &rulevolume.MaskLocVolume{
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
			},
			want: false,
		},
		{
			name: "case 5: parcel_type_definition.ParcelTypeHighValue",
			args: args{
				parcelType: parcel_type_definition.ParcelTypeHighValue,
				locVolume: &rulevolume.MaskLocVolume{
					RouteVolume: &rulevolume.MaskRouteVolume{},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, tt.task.getRouteHardCapByParcelType(tt.args.locVolume, tt.args.parcelType), "getRouteHardCapByParcelType(%v, %v)", tt.args.locVolume, tt.args.parcelType)
		})
	}
}

func TestTask_getPreCalculateShippingFee(t *testing.T) {
	ctx := context.Background()
	type args struct {
		productIds []int64
	}
	tests := []struct {
		name                    string
		task                    *Task
		args                    args
		wantPreRateResp         *chargeentity.BatchAllocationESFResp
		wantRemainingProductIds []int64
	}{
		// TODO: Add test cases.
		{
			name:                    "case 1: get masking pre calc fee is nil",
			task:                    &Task{},
			args:                    args{},
			wantPreRateResp:         nil,
			wantRemainingProductIds: nil,
		},
		{
			name: "case 1: get masking pre calc fee is nil",
			task: &Task{
				PreCalcFee: &chargeentity.BatchAllocationESFResp{
					Retcode: 0,
					Data: &chargeentity.BatchAllocationESFRespData{
						AllocatingShippingFeeResult: []*chargeentity.AllocatingShippingFeeResultItem{
							{
								UniqueId: fmt.Sprintf(UniqPattern, 1),
							},
						},
					},
				},
			},
			args: args{
				productIds: []int64{1, 2},
			},
			wantPreRateResp: &chargeentity.BatchAllocationESFResp{
				Data: &chargeentity.BatchAllocationESFRespData{
					AllocatingShippingFeeResult: []*chargeentity.AllocatingShippingFeeResultItem{
						{
							UniqueId: fmt.Sprintf(UniqPattern, 1),
						},
					},
				},
			},
			wantRemainingProductIds: []int64{2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotRemainingProductIds := tt.task.getPreCalculateShippingFee(ctx, tt.args.productIds)
			common.AssertResult(t, tt.wantPreRateResp, got, nil, nil)
			common.AssertResult(t, tt.wantRemainingProductIds, gotRemainingProductIds, nil, nil)
		})
	}
}

func Test_mergeRateResp(t *testing.T) {
	type args struct {
		preShippingFee      *chargeentity.BatchAllocationESFResp
		realTimeShippingFee *chargeentity.BatchAllocationESFResp
	}
	tests := []struct {
		name string
		args args
		want *chargeentity.BatchAllocationESFResp
	}{
		// TODO: Add test cases.
		{
			name: "case 1: preShippingFee == nil",
			args: args{
				realTimeShippingFee: &chargeentity.BatchAllocationESFResp{},
			},
			want: &chargeentity.BatchAllocationESFResp{},
		},
		{
			name: "case 2: realTimeShippingFee == nil",
			args: args{
				preShippingFee: &chargeentity.BatchAllocationESFResp{},
			},
			want: &chargeentity.BatchAllocationESFResp{},
		},
		{
			name: "case 3: realTimeShippingFee == nil",
			args: args{
				realTimeShippingFee: &chargeentity.BatchAllocationESFResp{
					Data: &chargeentity.BatchAllocationESFRespData{},
				},
				preShippingFee: &chargeentity.BatchAllocationESFResp{
					Data: &chargeentity.BatchAllocationESFRespData{},
				},
			},
			want: &chargeentity.BatchAllocationESFResp{
				Data: &chargeentity.BatchAllocationESFRespData{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common.AssertResult(t, tt.want, mergeRateResp(tt.args.preShippingFee, tt.args.realTimeShippingFee), nil, nil)
		})
	}
}
