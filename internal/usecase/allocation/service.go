package allocation

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	lcospb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	itemPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
	"github.com/gogo/protobuf/proto"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	mask_rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type PathKey string

const (
	needAsyncAllocationKey PathKey = "need_async_allocation"
	requestIDLength                = 3

	CheckoutFulfillmentIdempotenceKey    = "checkout_fulfillment_%d"
	CheckoutFulfillmentIdempotenceValue  = 1
	CheckoutFulfillmentIdempotenceExpire = 2 * time.Hour

	DeductVolumeCountKey    = "deduct_volume_count_%d"
	DeductVolumeCountValue  = 1
	DeductVolumeCountExpire = 2 * time.Hour
)

const (
	NormalOrder           = 0
	CacheBookingOrder     = 1
	CacheFulfillmentOrder = 2
)

var (
	goroutinePool     *ants.Pool
	goroutinePoolSize = 64

	//for async allocation log
	asyncGoroutinePool     *ants.Pool
	asyncGoroutinePoolSize = 16
)

func init() {
	// 用于做异步调度数据统计，非核心关键数据，使用非阻塞式
	p, err := ants.NewPool(goroutinePoolSize, ants.WithNonblocking(true))
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}

	goroutinePool = p
}

type MaskingProduct struct {
	ID int64
}

type volumeFilterType int

const (
	volumeFilterTypeMinVolume   volumeFilterType = 1
	volumeFilterTypeMaxCapacity volumeFilterType = 2

	AllocateRequest = iota + 1
	EstimateMaskingChannelRequest

	AllocateRequestName               = "AllocateRequest"
	EstimateMaskingChannelRequestName = "EstimateMaskingChannelRequest"
)

func GetAllocateReqTypeName(reqType int) string {
	switch reqType {
	case AllocateRequest:
		return AllocateRequestName
	case EstimateMaskingChannelRequest:
		return EstimateMaskingChannelRequestName
	}
	return ""
}

type AllocationServiceImpl struct {
	pb.UnimplementedMaskingServer

	configRepo             config.IMaskConfigRepo
	RuleRepo               rule.IMaskRuleRepo
	softRuleService        *SoftRuleService
	maskRuleVolumeSrv      rulevolume.MaskRuleVolumeService
	outerCheck             outercheck.AllOuterCheckService
	maskVolumeCounter      volumecounter.MaskVolumeCounter
	VolumeCounter          volume_counter.VolumeCounter
	scheduleCountStat      schedule_visual.ScheduleCountStatInterface
	maskRuleVolume         mask_rule.IMaskRuleVolumeRepo
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
	GreyService            service.GreyService
	ParcelTypeService      parcel_type_definition.ParcelTypeDefinitionService
	PickupEffCounter       pickup_efficiency_counter.PickupEffCounter
	SpexService            spex_service.SpexService
	LcosApi                lcosclient.LcosApi
	LpsApi                 lpsclient.LpsApi
	LlsApi                 llsclient.LlsApi
	AddrRepo               address.AddrRepo
}

func NewAllocationService(
	softRuleService *SoftRuleService,
	maskRuleVolumeSrv rulevolume.MaskRuleVolumeService,
	confRepo config.IMaskConfigRepo,
	ruleRepo rule.IMaskRuleRepo,
	outerCheck outercheck.AllOuterCheckService,
	maskVolumeCounter volumecounter.MaskVolumeCounter,
	volumeCounter volume_counter.VolumeCounter,
	scheduleCountStat schedule_visual.ScheduleCountStatInterface,
	maskRuleVolume mask_rule.IMaskRuleVolumeRepo,
	batchAllocateOrderRepo order.BatchAllocateOrderRepo,
	GreyService service.GreyService,
	parcelTypeService parcel_type_definition.ParcelTypeDefinitionService,
	pickupEffCounter pickup_efficiency_counter.PickupEffCounter,
	spexService spex_service.SpexService,
	lcosApi lcosclient.LcosApi,
	lpsApi lpsclient.LpsApi,
	llsApi llsclient.LlsApi,
	addrRepo address.AddrRepo) *AllocationServiceImpl {
	// 放在这里是因为configutil.get需要先初始化global conf，没办法放在init函数里
	if configutil.GetAllocationPathConf(context.Background()).AsyncGoRoutineNum != 0 {
		asyncGoroutinePoolSize = configutil.GetAllocationPathConf(context.Background()).AsyncGoRoutineNum
	}
	// 核心链路，非阻塞式，当消费不过来时丢弃消息
	p2, err := ants.NewPool(asyncGoroutinePoolSize, ants.WithNonblocking(true))
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}
	asyncGoroutinePool = p2

	return &AllocationServiceImpl{
		softRuleService:        softRuleService,
		maskRuleVolumeSrv:      maskRuleVolumeSrv,
		configRepo:             confRepo,
		RuleRepo:               ruleRepo,
		outerCheck:             outerCheck,
		maskVolumeCounter:      maskVolumeCounter,
		VolumeCounter:          volumeCounter,
		scheduleCountStat:      scheduleCountStat,
		maskRuleVolume:         maskRuleVolume,
		BatchAllocateOrderRepo: batchAllocateOrderRepo,
		GreyService:            GreyService,
		ParcelTypeService:      parcelTypeService,
		PickupEffCounter:       pickupEffCounter,
		SpexService:            spexService,
		LcosApi:                lcosApi,
		LpsApi:                 lpsApi,
		LlsApi:                 llsApi,
		AddrRepo:               addrRepo,
	}
}

func (a *AllocationServiceImpl) Allocate(ctx context.Context, req *pb.MaskingReq) (*pb.MaskingResp, error) {
	//1. fulfillment allocate
	rsp := a.allocate(ctx, req, AllocateRequest)
	if rsp.GetHeader().GetRetcode() != 0 {
		monitoring.ReportError(ctx, monitoring.CatAllocateApi, monitoring.AllocateError, fmt.Sprintf("request_id: %s, error: %s", rsp.GetHeader().GetRequestId(), rsp.GetHeader().GetMessage()))
		// 如果fulfillment allocate调度失败则直接返回，不需要再执行install allocate
		return rsp, nil
	}
	a.SendScheduleCountStat(ctx, req, rsp)
	monitoring.ReportSuccess(ctx, monitoring.CatAllocateApi, monitoring.AllocateSuccess, "")
	//2. install allocate
	installAllocateList, iErr := a.maskingInstallAllocate(ctx, req, rsp.GetAllocate().GetProductId(), AllocateRequest)
	if iErr != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), iErr)
		return rsp, nil
	}
	rsp.InstallAllocateList = installAllocateList
	return rsp, nil
}

func (a *AllocationServiceImpl) InstallAllocate(ctx context.Context, req *pb.InstallAllocateReq) (*pb.InstallAllocateResp, error) {
	//1. install channel allocate
	rsp, err := a.installAllocate(ctx, req, AllocateRequest)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, monitoring.SingleInstallAllocateError, fmt.Sprintf("single install allocate error=%v", err))
		return rsp, err
	}
	if rsp.GetHeader().GetRetcode() != 0 {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, monitoring.SingleInstallAllocateRetcodeError, fmt.Sprintf("single install allocate retcode error, retcode=%d", rsp.GetHeader().GetRetcode()))
		return rsp, nil
	}
	return rsp, nil
}

func (a *AllocationServiceImpl) IncrInstallAllocateVolume(ctx context.Context, req *pb.IncrInstallAllocateVolumeReq) (*pb.IncrInstallAllocateVolumeResp, error) {
	rsp := &pb.IncrInstallAllocateVolumeResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	for _, installLocVolumeInfo := range req.GetInstallLocVolumeInfoList() {
		installAdjustLocVolumeReq := &pb.AdjustLocVolumeReq{
			Header:              req.Header,
			MaskingProductId:    proto.Int64(installLocVolumeInfo.GetMaskingProductId()),
			ProductId:           proto.Int64(installLocVolumeInfo.GetProductId()),
			PickupLocationIds:   req.GetPickupLocationIds(),
			DeliveryLocationIds: req.GetDeliveryLocationIds(),
			HardProductIds:      []int64{installLocVolumeInfo.GetProductId()},
			PickupPostcode:      req.PickupPostcode,
			DeliveryPostcode:    req.DeliveryPostcode,
		}
		installAdjustLocVolumeRsp, _ := a.adjustLocVolume(ctx, installAdjustLocVolumeReq)
		if installAdjustLocVolumeRsp.GetHeader().GetRetcode() != 0 {
			monitoring.ReportError(ctx, monitoring.CatInstallAllocate, monitoring.SingleInstallAllocateUpdateVolumeError, fmt.Sprintf("single install allocate retcode error, retcode=%d", installAdjustLocVolumeRsp.GetHeader().GetRetcode()))
			rsp.Header = installAdjustLocVolumeRsp.Header
			return rsp, nil
		}
	}
	return rsp, nil
}

// maskingInstallAllocate 物流单仓单同时调度时的安装单调度抽象方法
func (a *AllocationServiceImpl) maskingInstallAllocate(ctx context.Context, req *pb.MaskingReq, fulfillmentProductId int64, reqType int) ([]*pb.InstallAllocate, *srerr.Error) {
	// 是否需要进行install allocate，由上游传参确定是否需要做install allocate
	if !req.GetInstallMaskingInfo().GetNeedInstallAllocate() {
		monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.NoNeedInstallAllocate, "")
		return nil, nil
	}
	monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateTotalNum, "")
	installAllocateReq := &pb.InstallAllocateReq{
		Header:                  req.Header,
		FulfillmentProductId:    proto.Int64(fulfillmentProductId),
		OrderInfo:               req.GetOrderInfo(),
		InstallAllocateInfoList: req.GetInstallMaskingInfo().GetInstallAllocateInfoList(),
		RequestTimeStamp:        proto.Int64(req.GetRequestTimeStamp()),
	}
	installAllocateResp, err := a.installAllocate(ctx, installAllocateReq, reqType)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateError, fmt.Sprintf("install allocate error, err=%v", err))
		return nil, srerr.With(srerr.InstallAllocateError, nil, err)
	}
	// install allocate失败则全都失败
	if installAllocateResp.GetHeader().GetRetcode() != 0 {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateHeaderError, fmt.Sprintf("install allocate error|retcode=%d, message=%s, cause=%s", installAllocateResp.GetHeader().GetRetcode(), installAllocateResp.GetHeader().GetMessage(), installAllocateResp.GetHeader().GetCause()))
		return nil, srerr.New(srerr.InstallAllocateError, nil, "install allocate error|retcode=%d, message=%s, cause=%s", installAllocateResp.GetHeader().GetRetcode(), installAllocateResp.GetHeader().GetMessage(), installAllocateResp.GetHeader().GetCause())
	}
	monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateSuccess, "")
	// 返回install allocate的结果
	return installAllocateResp.InstallAllocateList, nil
}

func (a *AllocationServiceImpl) installAllocate(ctx context.Context, req *pb.InstallAllocateReq, reqType int) (*pb.InstallAllocateResp, *srerr.Error) {
	// 不管是同步还是异步，都不需要处理异步allocate日志
	ctx = context.WithValue(ctx, needAsyncAllocationKey, false)
	rsp := &pb.InstallAllocateResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	//1. 构建成allocate请求参数
	installAllocateReqMap, installProductTypeMap, err := convertToInstallAllocateReq(ctx, req)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateValidParamError, fmt.Sprintf("install allocate valid param error=%v", err))
		logger.CtxLogErrorf(ctx, "convert install allocate request error|err=%v", err)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}
	//2. 根据item分批进行调度
	installAllocateRespMap := make(map[InstallAllocateItemInfo]*pb.InstallAllocate)
	for installAllocateItemInfo, installAllocateReq := range installAllocateReqMap {
		installRsp := a.allocate(ctx, installAllocateReq, reqType)
		if installRsp.GetHeader().GetRetcode() != 0 {
			monitoring.ReportError(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateAllocateError, fmt.Sprintf("install allocate allocate error|retcode=%d, message=%s, cause=%s", installRsp.GetHeader().GetRetcode(), installRsp.GetHeader().GetMessage(), installRsp.GetHeader().GetCause()))
			rsp.Header = installRsp.Header
			return rsp, nil
		}
		installAllocate := &pb.InstallAllocate{
			ItemUniqueId:     proto.String(installAllocateItemInfo.ItemUniqueId),
			ItemId:           proto.Uint64(installAllocateItemInfo.ItemId),
			MaskingProductId: installAllocateReq.MaskingProductId,
			ProductId:        proto.Int64(installRsp.GetAllocate().GetProductId()),
			ShopGroupId:      proto.Int64(installRsp.GetAllocate().GetShopGroupId()),
		}
		installAllocateRespMap[installAllocateItemInfo] = installAllocate
	}
	//3. 根据install product type对install product调度结果做处理
	installAllocateList, cErr := convertInstallAllocateByType(ctx, installAllocateReqMap, installAllocateRespMap, installProductTypeMap)
	if cErr != nil {
		monitoring.ReportError(ctx, monitoring.CatInstallAllocate, GetAllocateReqTypeName(reqType)+monitoring.InstallAllocateConvertError, fmt.Sprintf("install allocate convert error|err=%v", cErr))
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), cErr)
		return rsp, nil
	}
	rsp.InstallAllocateList = installAllocateList
	return rsp, nil
}

// convertToInstallAllocateReq 构建install allocate请求参数
func convertToInstallAllocateReq(ctx context.Context, installAllocateReq *pb.InstallAllocateReq) (map[InstallAllocateItemInfo]*pb.MaskingReq, map[int64]pb.InstallProductType, *srerr.Error) {
	//1. 校验参数
	// install info不能为空
	if len(installAllocateReq.GetInstallAllocateInfoList()) == 0 {
		return nil, nil, srerr.New(srerr.ParamErr, nil, "param error|install allocate info is empty")
	}
	// 校验item unique id是否重复
	duplicateItemUniqueIdMap := make(map[string]struct{})
	for _, installAllocateInfo := range installAllocateReq.GetInstallAllocateInfoList() {
		if _, ok := duplicateItemUniqueIdMap[installAllocateInfo.GetItemUniqueId()]; ok {
			return nil, nil, srerr.New(srerr.DuplicateItemUniqueIdError, nil, "duplicate item unique id|itemUniqueId=%s", installAllocateInfo.GetItemUniqueId())
		}
		duplicateItemUniqueIdMap[installAllocateInfo.GetItemUniqueId()] = struct{}{}
	}

	installRequestMap := make(map[InstallAllocateItemInfo]*pb.MaskingReq)
	installProductTypeMap := make(map[int64]pb.InstallProductType)
	for _, installAllocateInfo := range installAllocateReq.GetInstallAllocateInfoList() {
		// 物流安装渠道连接性关系不能为空
		if len(installAllocateInfo.GetLogisticsInstallJointInfoList()) == 0 {
			return nil, nil, srerr.New(srerr.ParamErr, nil, "param error|logistics install joint info is empty")
		}
		//2. 转换install allocate请求参数
		// 获取fulfillment product对应的install product列表
		var jointInstallProductIdList []int64
		for _, logisticsInstallJointInfo := range installAllocateInfo.GetLogisticsInstallJointInfoList() {
			if logisticsInstallJointInfo.GetFulfillmentProductId() == installAllocateReq.GetFulfillmentProductId() {
				jointInstallProductIdList = getInstallProductIdList(logisticsInstallJointInfo.GetInstallProductAttrList())
				installProductTypeMap = getInstallProductTypeMap(logisticsInstallJointInfo.GetInstallProductAttrList(), installProductTypeMap)
				break
			}
		}
		jointInstallProductIdMap := make(map[int64]struct{})
		for _, jointInstallProductId := range jointInstallProductIdList {
			jointInstallProductIdMap[jointInstallProductId] = struct{}{}
		}

		maskingReq := &pb.MaskingReq{
			Header:            installAllocateReq.GetHeader(),
			MaskingProductId:  installAllocateInfo.MaskingProductId,
			MaskType:          installAllocateInfo.MaskType,
			ProductComponents: installAllocateInfo.ProductComponents,
			//ProductIds: installAllocateInfo.ProductIds,
			ShopGroupIds:     installAllocateInfo.ShopGroupIds,
			OrderInfo:        installAllocateReq.GetOrderInfo(),
			HardProductIds:   installAllocateInfo.HardProductIds,
			RequestTimeStamp: proto.Int64(installAllocateReq.GetRequestTimeStamp()),
		}
		// 取fulfillment product、install product的连接性关系和install allocate硬性校验结果的交集作为install allocate的输入
		var productIds []int64
		// 使用productIds作为硬性校验结果
		for _, hardProductId := range installAllocateInfo.ProductIds {
			if _, ok := jointInstallProductIdMap[hardProductId]; ok {
				productIds = append(productIds, hardProductId)
			}
		}
		maskingReq.ProductIds = productIds
		installAllocateItemInfo := InstallAllocateItemInfo{
			ItemId:       installAllocateInfo.GetItemId(),
			ItemUniqueId: installAllocateInfo.GetItemUniqueId(),
		}
		installRequestMap[installAllocateItemInfo] = maskingReq
	}
	return installRequestMap, installProductTypeMap, nil
}

func getInstallProductIdList(installProductAttrList []*pb.InstallProductAttr) []int64 {
	installProductIdList := make([]int64, 0, len(installProductAttrList))
	for _, installProductAttr := range installProductAttrList {
		installProductIdList = append(installProductIdList, installProductAttr.GetInstallProductId())
	}
	return installProductIdList
}

func getInstallProductTypeMap(installProductAttrList []*pb.InstallProductAttr, installProductTypeMap map[int64]pb.InstallProductType) map[int64]pb.InstallProductType {
	for _, installProductAttr := range installProductAttrList {
		if _, ok := installProductTypeMap[installProductAttr.GetInstallProductId()]; !ok {
			installProductTypeMap[installProductAttr.GetInstallProductId()] = installProductAttr.GetInstallProductType()
		}
	}
	return installProductTypeMap
}

// convertInstallAllocateByType 对多个item的调度结果，如果install product type是isp则不用处理，如果是lm类型，则需要保持所有lm类型的install product一样，isp类型的product可以与所有lm类型的product匹配
func convertInstallAllocateByType(ctx context.Context, installAllocateReqMap map[InstallAllocateItemInfo]*pb.MaskingReq, installAllocateRespMap map[InstallAllocateItemInfo]*pb.InstallAllocate, installProductTypeMap map[int64]pb.InstallProductType) ([]*pb.InstallAllocate, *srerr.Error) {
	// 提前检查输入参数
	if len(installAllocateRespMap) == 0 {
		// 如果没有分配结果，直接返回空列表
		return []*pb.InstallAllocate{}, nil
	}

	// 快速路径：如果只有一个分配结果，无需计算交集
	if len(installAllocateRespMap) == 1 {
		for _, resp := range installAllocateRespMap {
			return []*pb.InstallAllocate{resp}, nil
		}
	}

	// 预分配结果列表容量，避免多次扩容
	installAllocateList := make([]*pb.InstallAllocate, 0, len(installAllocateRespMap))

	// 获取硬性校验结果的交集
	interSectionProductMap, isErr := getInterSectionProduct(ctx, installAllocateReqMap, installAllocateRespMap, installProductTypeMap)
	if isErr != nil {
		return nil, isErr
	}

	// 选择一个交集产品ID作为所有LM类型产品的共同结果
	// 选择最小的ID作为共同结果，保证同一请求每次请求结果一致
	var commonLMProductID int64
	for productID := range interSectionProductMap {
		if commonLMProductID == 0 {
			commonLMProductID = productID
		}
		if commonLMProductID > productID {
			commonLMProductID = productID
		}
	}

	// 处理每个分配结果
	for itemInfo, allocateResp := range installAllocateRespMap {
		productID := allocateResp.GetProductId()

		// 验证产品类型是否存在
		productType, exists := installProductTypeMap[productID]
		if !exists {
			return nil, srerr.New(srerr.InstallAllocateError, nil,
				"can not found install product type，productId=%d", productID)
		}

		// 处理ISP类型产品 - 直接添加到结果
		if productType == pb.InstallProductType_ISPInstallation {
			installAllocateList = append(installAllocateList, allocateResp)
			continue
		}

		// 获取该项的请求信息
		allocateReq, exists := installAllocateReqMap[itemInfo]
		if !exists {
			return nil, srerr.New(srerr.InstallAllocateError, nil,
				"can not found allocate request info，itemInfo=%v", itemInfo)
		}

		// 尝试使用共同的LM产品ID
		if commonLMProductID != 0 && objutil.ContainInt64(allocateReq.GetProductIds(), commonLMProductID) {
			// 创建新的响应对象，避免修改原始对象
			allocateResp.ProductId = proto.Int64(commonLMProductID)
			installAllocateList = append(installAllocateList, allocateResp)
			continue
		}

		// 尝试找到ISP类型产品
		ispProductID, err := findISPProduct(allocateReq.GetProductIds(), installProductTypeMap)
		if err != nil {
			return nil, err
		}

		if ispProductID != 0 {
			// 创建新的响应对象，避免修改原始对象
			allocateResp.ProductId = proto.Int64(ispProductID)
			installAllocateList = append(installAllocateList, allocateResp)
			continue
		}

		// 没有找到匹配的产品
		return nil, srerr.New(srerr.InstallAllocateError, nil,
			"%v can not found valid allocate result", itemInfo)
	}

	return installAllocateList, nil
}

// findISPProduct 在产品ID列表中查找ISP类型产品
func findISPProduct(productIDs []int64, typeMap map[int64]pb.InstallProductType) (int64, *srerr.Error) {
	for _, id := range productIDs {
		productType, exists := typeMap[id]
		if !exists {
			return 0, srerr.New(srerr.InstallAllocateError, nil,
				"can not found install product type，productId=%d", id)
		}

		if productType == pb.InstallProductType_ISPInstallation {
			return id, nil
		}
	}
	return 0, nil
}

// getInterSectionProduct 获取硬性校验结果的交集
func getInterSectionProduct(ctx context.Context, installAllocateReqMap map[InstallAllocateItemInfo]*pb.MaskingReq, installAllocateRespMap map[InstallAllocateItemInfo]*pb.InstallAllocate, installProductTypeMap map[int64]pb.InstallProductType) (map[int64]struct{}, *srerr.Error) {
	// 用于存储所有有效产品ID的交集结果
	interSectionProductMap := make(map[int64]struct{})

	// 预先收集所有非ISP类型的项目及其请求
	var lmItems []struct {
		itemInfo InstallAllocateItemInfo
		req      *pb.MaskingReq
		resp     *pb.InstallAllocate
	}

	// 首先遍历所有分配结果，收集非ISP类型的项目
	for itemInfo, allocateResp := range installAllocateRespMap {
		productID := allocateResp.GetProductId()

		// 验证产品类型是否存在
		productType, exists := installProductTypeMap[productID]
		if !exists {
			return nil, srerr.New(srerr.InstallAllocateError, nil,
				"can not found install allocate type，productId=%d", productID)
		}

		// 跳过ISP类型的产品
		if productType == pb.InstallProductType_ISPInstallation {
			continue
		}

		// 获取对应的请求信息
		allocateReq, exists := installAllocateReqMap[itemInfo]
		if !exists {
			return nil, srerr.New(srerr.InstallAllocateError, nil,
				"can not found install allocate request info，itemInfo=%v", itemInfo)
		}

		// 添加到LM项目列表
		lmItems = append(lmItems, struct {
			itemInfo InstallAllocateItemInfo
			req      *pb.MaskingReq
			resp     *pb.InstallAllocate
		}{itemInfo, allocateReq, allocateResp})
	}

	// 如果没有LM类型的项目，直接返回空交集
	if len(lmItems) == 0 {
		return interSectionProductMap, nil
	}

	// 如果LM项目的调度结果一致，则不需要变更，直接以调度结果为交集
	var commonAllocateResult int64
	for _, item := range lmItems {
		// 初始取第一个item的调度结果
		if commonAllocateResult == 0 {
			commonAllocateResult = item.resp.GetProductId()
		}
		// 当item的调度结果不相同时，，则直接退出循环，标记没有共同调度结果
		if commonAllocateResult != item.resp.GetProductId() {
			commonAllocateResult = 0
			break
		}
	}
	// 如果所有item的调度结果都相同，则以其为交集
	if commonAllocateResult != 0 {
		interSectionProductMap[commonAllocateResult] = struct{}{}
		return interSectionProductMap, nil
	}

	// 多个LM项目情况：计算交集
	// 使用第一个项目的产品ID列表初始化交集
	firstItem := lmItems[0]

	// 初始化交集为第一个项目的非ISP产品
	for _, productID := range firstItem.req.GetProductIds() {
		productType, exists := installProductTypeMap[productID]
		if !exists {
			return nil, srerr.New(srerr.InstallAllocateError, nil,
				"can not found install product type，productId=%d", productID)
		}

		// 只添加非ISP类型的产品
		if productType != pb.InstallProductType_ISPInstallation {
			interSectionProductMap[productID] = struct{}{}
		}
	}

	// 与其余项目计算交集
	for i := 1; i < len(lmItems); i++ {
		// 如果交集已经为空，则无需继续计算
		if len(interSectionProductMap) == 0 {
			break
		}

		// 计算当前项目与已有交集的交集
		tempIntersection := make(map[int64]struct{})
		hasISP := false

		for _, productID := range lmItems[i].req.GetProductIds() {
			productType, exists := installProductTypeMap[productID]
			if !exists {
				return nil, srerr.New(srerr.InstallAllocateError, nil,
					"can not found install product type，productId=%d", productID)
			}

			// 标记ISP类型产品
			if productType == pb.InstallProductType_ISPInstallation {
				hasISP = true
				continue
			}

			// 如果当前产品ID存在于之前的交集中，添加到新交集
			if _, exists1 := interSectionProductMap[productID]; exists1 {
				tempIntersection[productID] = struct{}{}
			}
		}

		// 更新交集结果
		// 有交集或交集为空且不存在ISP的候选项则以交集为准
		if len(tempIntersection) > 0 || !hasISP {
			// 清空原交集并替换为新交集
			interSectionProductMap = tempIntersection
		}
	}

	return interSectionProductMap, nil
}

func (a *AllocationServiceImpl) singleAllocate(ctx context.Context, req *pb.MaskingReq) *pb.ASyncAllocateResp {
	ctx = context.WithValue(ctx, needAsyncAllocationKey, true)
	// 初始化Resp
	rsp := &pb.ASyncAllocateResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	// 获取allocated渠道
	var allocatedProductId int64
	var maskingInstallAllocateList []*pb.InstallAllocate
	if len(req.GetProductIds()) == 1 {
		// 如果channel == 1
		// 获取最终allocated渠道
		allocatedProductId = req.GetProductIds()[0]
		//2. install allocate
		installAllocateList, iErr := a.maskingInstallAllocate(ctx, req, allocatedProductId, AllocateRequest)
		if iErr != nil {
			rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), iErr)
			return rsp
		}
		maskingInstallAllocateList = installAllocateList
	} else {
		// 如果channel > 1
		allocatedRsp, _ := a.Allocate(ctx, req)

		// allocate报错提前返回结果
		if allocatedRsp.GetHeader().GetRetcode() != 0 {
			return &pb.ASyncAllocateResp{Header: allocatedRsp.GetHeader()}
		}
		// 获取最终allocated渠道
		allocatedProductId = allocatedRsp.GetAllocate().GetProductId()
		maskingInstallAllocateList = allocatedRsp.GetInstallAllocateList()
	}
	// 转换install allocate结果
	var installAllocateResultList []order.InstallAllocateResult
	for _, installAllocate := range maskingInstallAllocateList {
		installAllocateResultList = append(installAllocateResultList, order.InstallAllocateResult{
			ItemUniqueId:     installAllocate.GetItemUniqueId(),
			ItemId:           installAllocate.GetItemId(),
			MaskingProductId: installAllocate.GetMaskingProductId(),
			ProductId:        installAllocate.GetProductId(),
		})
	}
	// 获取allocated渠道对应的运费
	shippingFee := a.getShippingFeeForAllocatedProduct(ctx, req.GetFulfillmentShippingFeeInfoList(), allocatedProductId)

	// 存储至结果表
	// 数据转换
	orderId, convErr := strconv.Atoi(req.GetOrderInfo().GetOrderId())
	if convErr != nil {
		logger.CtxLogErrorf(ctx, "convert orderId to int type failed|err=%v", convErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, convErr.Error()))
		return rsp
	}
	result := &order.OrderResultTab{
		OrderID:                    uint64(orderId),
		OrderStatus:                batch_allocate.OrderStatusTypeAsyncAllocated,
		MaskProductID:              int(req.GetMaskingProductId()),
		AllocateResult:             int(allocatedProductId),
		FulfillmentShippingFeeInfo: shippingFee,
		InstallAllocateResult:      installAllocateResultList,
		RequestTimeStamp:           req.GetRequestTimeStamp(),
	}
	// 查询结果表是否存在此订单
	allowInsert := true
	if envvar.IsLivetest() {
		allowInsert = configutil.GetAsyncCompressConf(ctx).AllowRequest
	}
	if allowInsert {
		allocateTime := timeutil.GetLocalTime(ctx)
		data, gErr := a.BatchAllocateOrderRepo.GetOrderResultByOrderID(ctx, uint64(orderId), allocateTime)
		if gErr != nil {
			// 不存在，新建数据
			if err := a.BatchAllocateOrderRepo.BatchCreateOrderResult(ctx, []*order.OrderResultTab{result}, allocateTime.Day()); err != nil {
				// 新建数据出错
				ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, AddHoldOrderData, FailValue)
				logger.CtxLogErrorf(ctx, "save result to db failed|result=%v|err=%v", str.JsonString(result), err)
				rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, err.Error()))
				return rsp
			}
			ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, AddHoldOrderData, SuccessValue)
			logger.CtxLogInfof(ctx, "save result to db success|result=%v", str.JsonString(result))
		} else {
			// 存在，更新数据
			if err := a.BatchAllocateOrderRepo.UpdateOrderResultByPrimaryId(ctx, data.ID, result, allocateTime); err != nil {
				// 更新数据出错
				ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, UpdateHoldOrderData, FailValue)
				logger.CtxLogErrorf(ctx, "order id already existed. update result to db failed|result=%v|err=%v", str.JsonString(result), err)
				rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, err.Error()))
				return rsp
			}
			ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, UpdateHoldOrderData, SuccessValue)
			logger.CtxLogInfof(ctx, "order id already existed. update result to db success|result=%v", str.JsonString(result))
		}
	}

	// 更新运力
	locVols, volErr := a.MatchLocationVolumesForUpdateVolume(
		ctx, req.GetMaskingProductId(), []int64{allocatedProductId},
		req.GetOrderInfo().GetPickupAddress().GetLocationIds(), req.GetOrderInfo().GetDeliveryAddress().GetLocationIds(),
		rule_mode.MplOrderRule,
		req.GetOrderInfo().GetPickupAddress().GetPostalCode(), req.GetOrderInfo().GetDeliveryAddress().GetPostalCode(),
		allocation2.SingleAllocate,
	)
	if volErr != nil {
		// 获取运力配置出错
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, MatchLocationVolumes, FailValue)
		logger.CtxLogErrorf(ctx, "match location volume config failed|err=%v", volErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocationLocVolumeError, nil, volErr.Error()))
		return rsp
	}

	dgType := pb.DgType_NONE_DG
	for _, productInfo := range req.GetProductDgInfoList() {
		if productInfo.GetProductId() == allocatedProductId {
			dgType = productInfo.GetDgType()
			break
		}
	}
	parcelAttr := a.GetAdjustVolumeParcelTypeAttr(
		ctx, req.GetMaskingProductId(), allocatedProductId, req.GetOrderInfo().GetProductParcelDimensions()[allocatedProductId],
		req.GetOrderInfo().GetCogs(), req.GetOrderInfo().GetPaymentMethod(), dgType)
	groupCode := a.GetFulfillmentProductGroupCode(ctx, req.GetMaskingProductId(), allocatedProductId, rule_mode.MplOrderRule, allocation2.SingleAllocate)
	// 更新运力需要对匹配到的（zoneCode + zoneDirection） 或 routeCode去重
	removeDupLocVols := rulevolume.RemoveDuplicationLocVols(locVols)
	_ = a.maskVolumeCounter.IncrMaskVolume(ctx, req.GetMaskingProductId(), allocatedProductId, groupCode, parcelAttr)
	// 上报route&zone模式更新运力，规则是分单的时候优先匹配route，更新运力的时候要更新匹配到的route和zone
	monitorRouteAndZoneMode(ctx, removeDupLocVols[allocatedProductId])
	for _, locVol := range removeDupLocVols[allocatedProductId] {
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			if err := a.maskVolumeCounter.IncrRouteVolume(ctx, req.GetMaskingProductId(), allocatedProductId, groupCode, locVol.LocCode, rule_mode.MplOrderRule, parcelAttr); err != nil {
				ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, IncrRouteVolume, FailValue)
				logger.CtxLogErrorf(ctx, "incr route volume failed|err=%v", err)
			}
		case rulevolume.MaskLocTypeZone:
			if err := a.maskVolumeCounter.IncrZoneVolume(ctx, req.GetMaskingProductId(), allocatedProductId, groupCode, locVol.LocCode, locVol.ZoneDirection, rule_mode.MplOrderRule, parcelAttr); err != nil {
				ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, IncrZoneVolume, FailValue)
				logger.CtxLogErrorf(ctx, "incr zone volume failed|err=%v", err)
			}
		}
	}
	//2. 更新install channel调度结果运力
	for _, installAllocateResult := range installAllocateResultList {
		monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.AsyncNonWmsUpdateVolumeTotalNum, "")
		installAdjustLocVolumeReq := &pb.AdjustLocVolumeReq{
			Header:              req.Header,
			MaskingProductId:    proto.Int64(installAllocateResult.MaskingProductId),
			ProductId:           proto.Int64(installAllocateResult.ProductId),
			PickupLocationIds:   req.GetOrderInfo().GetPickupAddress().GetLocationIds(),
			DeliveryLocationIds: req.GetOrderInfo().GetDeliveryAddress().GetLocationIds(),
			HardProductIds:      []int64{installAllocateResult.ProductId},
			PickupPostcode:      proto.String(req.GetOrderInfo().GetPickupAddress().GetPostalCode()),
			DeliveryPostcode:    proto.String(req.GetOrderInfo().GetDeliveryAddress().GetPostalCode()),
		}
		installAdjustLocVolumeRsp, _ := a.adjustLocVolume(ctx, installAdjustLocVolumeReq)
		if installAdjustLocVolumeRsp.GetHeader().GetRetcode() != 0 {
			monitoring.ReportError(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.AsyncNonWmsUpdateVolumeError, fmt.Sprintf("async non wms order update volume error|retcode=%d, message=%s, casue=%s", installAdjustLocVolumeRsp.GetHeader().GetRetcode(), installAdjustLocVolumeRsp.GetHeader().GetMessage(), installAdjustLocVolumeRsp.GetHeader().GetCause()))
			rsp.Header = installAdjustLocVolumeRsp.Header
			return rsp
		}
	}
	//3. 更新shopgroupid
	// lps传入参数已经进行去重操作
	a.updateDashboardCounter(ctx, req, allocatedProductId, groupCode)
	return rsp
}

func (a *AllocationServiceImpl) updateDashboardCounter(ctx context.Context, req *pb.MaskingReq, allocatedProductId int64, groupCode string) {
	logger.CtxLogDebugf(ctx, "dashboard:allocatedProductId:%d, groupCode%s", allocatedProductId, groupCode)
	for _, id := range req.ShopGroupIds {
		reqtime := req.GetRequestTimeStamp()
		if reqtime == 0 {
			reqtime = timeutil.GetLocalTime(ctx).Unix()
		}
		//	_ = a.VolumeCounter.UpdateShopGroupVolume(ctx, req.GetMaskingProductId(), req.GetProductId(), groupCode, req.GetShopGroupId(), nowTime, pb.UpdateVolumeType_Create, rule_mode.MplOrderRule)
		logger.CtxLogDebugf(ctx, "updating dashboard shopgroudid %d", id)
		err := a.VolumeCounter.UpdateShopGroupVolume(ctx, req.GetMaskingProductId(), allocatedProductId, groupCode, id, reqtime, pb.UpdateVolumeType_Create, rule_mode.MplOrderRule)
		if err != nil {
			logger.CtxLogErrorf(ctx, "update shop group id failed|err=%v", err)
		}
	}
}

func (a *AllocationServiceImpl) GetFulfillmentProductGroupCode(
	ctx context.Context, maskProductID, fulfillmentProductID int64, ruleMode rule_mode.RuleMode, allocationMethod int64,
) string {
	r, err := a.maskRuleVolumeSrv.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, maskProductID, ruleMode, allocationMethod)
	if err != nil {
		logger.CtxLogErrorf(ctx, "active rule volume not found|err=%v", err)
		return ""
	}

	// 非Group模式
	if !r.ShareVolume {
		return ""
	}

	for groupCode, productList := range r.GetGroupMap() {
		for _, p := range productList {
			if p == fulfillmentProductID {
				logger.CtxLogInfof(ctx, "fulfillment product %d get group code: %s", fulfillmentProductID, groupCode)
				return groupCode
			}
		}
	}

	return ""
}

// ASyncAllocate 异步allocate，服务于batch allocate功能
func (a *AllocationServiceImpl) ASyncAllocate(ctx context.Context, req *pb.MaskingReq) (*pb.ASyncAllocateResp, error) {
	var rsp *pb.ASyncAllocateResp
	// 如果需要做install allocate，则强制走single allocate
	if req.GetInstallMaskingInfo().GetNeedInstallAllocate() {
		rsp = a.singleAllocate(ctx, req)
		return rsp, nil
	}

	// 根据soft rule判断是否走batch
	//only product of allocation enabled will check rule，在里层查了mask_volume_tab
	_, err := a.RuleRepo.GetEffectiveRuleByCache(ctx, req.GetMaskingProductId(), GetRuleMode(ctx, AllocateRequest), allocation2.BatchAllocate)
	if err != nil {
		logger.CtxLogInfof(ctx, "got batch rule failed, will go single logic")
		rsp = a.singleAllocate(ctx, req)
	} else {
		// single/batch灰度切换
		if a.GreyService.PassGrey(ctx, int(req.GetMaskingProductId()), recorder.RandIntn(ctx, service.MaxGreyPercentage)) {
			// batch
			ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, GreySwitchField, SingleValue)
			rsp = a.batchAllocate(ctx, req)
		} else {
			// single
			ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, GreySwitchField, BatchValue)
			rsp = a.singleAllocate(ctx, req)
		}
	}

	return rsp, nil
}

// @core
func (a *AllocationServiceImpl) matchLocationVolumesNew(
	ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64,
	rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int,
) (map[int64]rulevolume.ProductLocVolumes, *srerr.Error) {
	// 上报postcode监控
	monitoring.ReportPostcode(ctx, pickupPostcode, deliveryPostcode)

	locVolumes, rErr := a.matchRouteVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
	if rErr != nil {
		return nil, rErr
	}

	zoneVolumes, zErr := a.matchZoneVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
	if zErr != nil {
		return nil, zErr
	}

	locVolumes = append(locVolumes, zoneVolumes...)
	result := convertToProductLocTypeMap(locVolumes)

	logger.CtxLogDebugf(ctx, "match location volumes result: %s", str.JsonStringForDebugLog(result))

	return result, nil
}

func (a *AllocationServiceImpl) MatchLocationVolumesForUpdateVolume(
	ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64,
	rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int,
) (map[int64][]*rulevolume.MaskLocVolume, *srerr.Error) {
	locVolumes, rErr := a.matchRouteVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
	if rErr != nil {
		return nil, rErr
	}

	zoneVolumes, zErr := a.matchZoneVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
	if zErr != nil {
		return nil, zErr
	}

	locVolumes = append(locVolumes, zoneVolumes...)
	result := convertToProductMap(locVolumes)

	logger.CtxLogDebugf(ctx, "match location volumes result for update volume: %s", str.JsonStringForDebugLog(result))

	return result, nil
}

func convertToProductMap(locations []*rulevolume.MaskLocVolume) map[int64][]*rulevolume.MaskLocVolume {
	result := make(map[int64][]*rulevolume.MaskLocVolume)
	for _, loc := range locations {
		if result[loc.ProductId] == nil {
			result[loc.ProductId] = make([]*rulevolume.MaskLocVolume, 0)
		}
		result[loc.ProductId] = append(result[loc.ProductId], loc)
	}

	return result
}

func convertToProductLocTypeMap(locVolumes []*rulevolume.MaskLocVolume) map[int64]rulevolume.ProductLocVolumes {
	result := make(map[int64]rulevolume.ProductLocVolumes)
	for _, locVolume := range locVolumes {
		productLocVolumes := result[locVolume.ProductId]
		if locVolume.LocType == rulevolume.MaskLocTypeRoute {
			productLocVolumes.RouteVolumes = append(productLocVolumes.RouteVolumes, locVolume)
		} else if locVolume.LocType == rulevolume.MaskLocTypeZone {
			productLocVolumes.ZoneVolumes = append(productLocVolumes.ZoneVolumes, locVolume)
		}
		result[locVolume.ProductId] = productLocVolumes
	}

	return result
}

func (a *AllocationServiceImpl) matchRouteVolume(
	ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64, rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int,
) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var routeVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		// 先匹配route运力
		routeVolumes, err := a.maskRuleVolumeSrv.MatchLocsToRoutes(ctx, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
		if err != nil {
			return nil, err
		}
		routeVolumeList = append(routeVolumeList, routeVolumes...)
	}

	return routeVolumeList, nil
}

func (a *AllocationServiceImpl) matchZoneVolume(
	ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64, rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int,
) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var zoneVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		zoneVolumes, err := a.maskRuleVolumeSrv.MatchLocsToZones(ctx, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
		if err != nil {
			return nil, err
		}
		zoneVolumeList = append(zoneVolumeList, zoneVolumes...)
	}

	return zoneVolumeList, nil
}

func (a *AllocationServiceImpl) EstimateMaskingChannel(ctx context.Context, req *pb.MaskingReq) (*pb.MaskingResp, error) {
	rsp := a.allocate(ctx, req, EstimateMaskingChannelRequest)
	if rsp.GetHeader().GetRetcode() != 0 {
		monitoring.ReportError(ctx, monitoring.CatAllocateApi, monitoring.EstimateMaskingChannelError, fmt.Sprintf("request_id: %s, error: %s", rsp.GetHeader().GetRequestId(), rsp.GetHeader().GetMessage()))
	}
	monitoring.ReportSuccess(ctx, monitoring.CatAllocateApi, monitoring.EstimateMaskingChannelSuccess, "")
	//2. install allocate
	// 对物流调度结果做安装单调度
	installAllocateList, iErr := a.maskingInstallAllocate(ctx, req, rsp.GetAllocate().GetProductId(), EstimateMaskingChannelRequest)
	if iErr != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), iErr)
		return rsp, nil
	}
	rsp.InstallAllocateList = installAllocateList
	// 对仓单的排序结果分别做安装单调度
	for _, sortedResult := range rsp.SortedResult {
		for _, detail := range sortedResult.DetailList {
			sortInstallAllocateList, sErr := a.maskingInstallAllocate(ctx, req, detail.GetShippingChannelId(), EstimateMaskingChannelRequest)
			if sErr != nil {
				rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), sErr)
				return rsp, nil
			}
			detail.InstallAllocateList = sortInstallAllocateList
		}
	}
	return rsp, nil
}

func (a *AllocationServiceImpl) allocate(ctx context.Context, req *pb.MaskingReq, reqType int) *pb.MaskingResp {
	rsp := &pb.MaskingResp{
		Header:   grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
		Allocate: &pb.Allocate{},
	}
	// get allocate config
	conf, err := a.configRepo.GetConfigByMaskingProductIDByCache(ctx, req.GetMaskingProductId())
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp
	}

	if !conf.SmartAllocationEnabled {
		logger.CtxLogInfof(ctx, "soft_criteria use default product:%v, ", conf.DefaultProduct)
		if !objutil.ContainInt64(req.GetProductIds(), conf.DefaultProduct) {
			rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.NoAvailableProduct, req.GetMaskingProductId(), "default product of %d is not in req %v", req.GetMaskingProductId(), req.GetProductIds()))
			return rsp
		}
		rsp.Allocate.ProductId = proto.Int64(conf.DefaultProduct)
		return rsp
	}

	// 获取可用的Product列表，要针对Reallocation场景下并且Buyer Reselect过，去除Buyer Reselect Previous Product
	availableProducts, err := FilterBuyerRejectedFulfillmentProduct(ctx, req)
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp
	}

	// 报错为系统异常，无配置不会报错，仅返回空数组
	var locVols map[int64]rulevolume.ProductLocVolumes
	switch reqType {
	case AllocateRequest:
		locVols, err = a.matchLocationVolumesNew(ctx, req.GetMaskingProductId(), req.GetHardProductIds(), req.GetOrderInfo().GetPickupAddress().GetLocationIds(), req.GetOrderInfo().GetDeliveryAddress().GetLocationIds(), GetRuleMode(ctx, reqType), req.GetOrderInfo().GetPickupAddress().GetPostalCode(), req.GetOrderInfo().GetDeliveryAddress().GetPostalCode(), allocation2.SingleAllocate)
	case EstimateMaskingChannelRequest:
		locVols, err = a.matchLocationVolumesNew(ctx, req.GetMaskingProductId(), req.GetProductIds(), req.GetOrderInfo().GetPickupAddress().GetLocationIds(), req.GetOrderInfo().GetDeliveryAddress().GetLocationIds(), GetRuleMode(ctx, reqType), req.GetOrderInfo().GetPickupAddress().GetPostalCode(), req.GetOrderInfo().GetDeliveryAddress().GetPostalCode(), allocation2.SingleAllocate)
	}

	if err != nil {
		logger.CtxLogErrorf(ctx, "matchLocationVolumes fail, err:%v, mask_product_id:%d, product_ids:%v, pickup_location_ids:%v, delivery_location_ids:%v", err, req.GetMaskingProductId(), req.GetProductIds(), req.GetOrderInfo().GetPickupAddress().GetLocationIds(), req.GetOrderInfo().GetDeliveryAddress().GetLocationIds())
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocationLocVolumeError, req.GetMaskingProductId(), err.Error()))
		return rsp
	}

	priority, err := a.outerCheck.GetProductFromMaskingShopGroup(ctx, req.GetMaskingProductId(), req.GetShopGroupIds(), req.GetMaskType(), req.GetProductComponents())
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetProductFromMaskingShopGroup fail, err:%v", err)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp
	}
	rsp.Allocate.ShopGroupId = proto.Int64(priority.ShopGroupID)

	//only product of allocation enabled will check rule，在里层查了mask_volume_tab
	ruleInfo, err := a.RuleRepo.GetEffectiveRuleByCache(ctx, req.GetMaskingProductId(), GetRuleMode(ctx, reqType), allocation2.SingleAllocate)
	if err != nil {
		logger.CtxLogErrorf(ctx, "soft_criteria for allocate, masking product:%v, get effective rule err:%v", req.GetMaskingProductId(), err)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocationRuleError, req.GetMaskingProductId(), err.Error()))
		return rsp
	}

	rsp.Allocate.RuleId = proto.Int64(ruleInfo.Id)
	rsp.Allocate.LocalVolumeId = proto.Uint64(ruleInfo.LocVolumeId)

	priorityMap := make(map[int64]int32)
	weightageMap := make(map[int64]int32)
	for _, item := range priority.ComponentPriorities {
		if item.Status == entity.Open {
			priorityMap[item.ProductID] = int32(item.Priority)
			weightageMap[item.ProductID] = int32(item.Weightage)
		}
	}

	var productGroupCodeMapping map[int64]string
	if ruleInfo.LocVolumeId != 0 {
		productGroupCodeMapping = a.getProductGroupCodeMapping(ctx, int64(ruleInfo.LocVolumeId))
	}

	logger.CtxLogInfof(ctx, "soft_criteria for allocate, masking product:%v, product ids:%v, rule:%v, priority:%v, orderInfo:%v",
		req.GetMaskingProductId(), availableProducts, ruleInfo, priorityMap, req.GetOrderInfo())

	if configutil.GetAllocationLogConf(ctx) {
		ctx = context.WithValue(ctx, constant.AllocationLogCtxKey, NewLog())
	}

	attrMap := a.getAllocateParcelTypeAttr(ctx, req.GetMaskingProductId(), availableProducts, req.GetOrderInfo(), req.GetProductDgInfoList())

	var productId int64 = 0
	switch reqType {
	case AllocateRequest:
		productId, err = a.softRuleService.Allocate(
			ctx, req.GetMaskingProductId(), availableProducts, ruleInfo, locVols, priority.RuleType, priorityMap, weightageMap,
			req.GetOrderInfo(), nil, constant.AllocateMasking, productGroupCodeMapping, attrMap,
		)
	case EstimateMaskingChannelRequest:
		var sortedRes []*pb.SortedResult
		productId, sortedRes, err = a.softRuleService.SoftRuleCriteria(
			ctx, req.GetMaskingProductId(), availableProducts, ruleInfo, locVols, priority.RuleType, priorityMap,
			weightageMap, req.GetOrderInfo(), nil, GetRuleMode(ctx, reqType),
			EstimateMaskingChannelRequest, constant.EstimateMasking, productGroupCodeMapping, attrMap,
		)
		rsp.SortedResult = sortedRes
	}
	logger.CtxLogInfof(ctx, "soft_criteria for allocate, masking product:%v, resp:%v", req.GetMaskingProductId(), productId)
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, req.GetMaskingProductId(), err.Error()))
		return rsp
	}

	allocationLog := GetAllocationLogFromContext(ctx)
	allocationLog.SetShopGroupId(priority.ShopGroupID)
	allocationLog.SetMaskingProductId(req.GetMaskingProductId())
	allocationLog.SetFulfillmentProductId(productId)
	allocationLog.SetSoftOut(productId)
	allocationLog.RemoveDuplicateZones()
	allocationLog.RemoveDuplicateRoutes()
	allocationLog.SetVolumeRuleId(ruleInfo.LocVolumeId)
	allocationLog.SetProductParcelInfoList(convertParcelTypeAttrMapToSlice(attrMap))

	rsp.Allocate.ProductId = proto.Int64(productId)
	rsp.Allocate.SoftCriteria = proto.String(GetAllocationLogString(ctx))
	rsp.Allocate.ShippingFee = proto.String(GetAllocationLogShippingFee(ctx))
	rsp.Allocate.ProductParcelInfoList = convertParcelTypeAttrMapToSlice(attrMap)

	// 避免堵塞主流程，使用task异步处理allocation log
	needUpdate, _ := ctx.Value(needAsyncAllocationKey).(bool)
	if needUpdate && req.GetCacheOrderType() != CacheBookingOrder {
		log := &TempAllocationLog{
			Log: allocationLog,
		}
		keys := strings.Split(req.GetHeader().GetRequestId(), "|")
		if len(keys) == requestIDLength {
			// example: "|64ca21a10cfc995ad6017244f89da300:030004e09f1ea518:020000f002c50c5f|zgo1", only use the middle part
			log.RequestId = keys[1]
		}
		if log.RequestId == "" || log.Log == nil {
			logger.CtxLogErrorf(ctx, "illegal request id:%s, or illegal log:%v", log.RequestId, log.Log)
		} else {
			msg := objutil.JsonBytes(log)
			compressMsg := zip.ZSTDCompress(msg)
			// 使用smr-task来处理旁路功能，因为后续batask主要负责batch调度，不要影响batask
			// 使用协程池发送kafka, 有损式并发 TODO：需要压一下触发"有损"状态的QPS基线
			namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
			if sErr := asyncGoroutinePool.Submit(func() {
				if err := kafkahelper.DeliveryMessage(
					ctx, namespace, constant.TaskNameMakeUpAsyncAllocationLog, compressMsg, nil, kafkahelper.MakeUpAsyncAllocationLogType,
				); err != nil {
					errMsg := fmt.Sprintf("delivery async allocation log to kafka failed | requestid=%s, err=%v", req.GetHeader().GetRequestId(), err)
					_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, "Async Allocation Log", monitoring.StatusError, errMsg)
					logger.CtxLogErrorf(ctx, errMsg)
				}
			}); sErr != nil {
				logger.CtxLogErrorf(ctx, "submit async go routine err:%v", sErr)
			}
		}
	}

	prometheusutil.UnifiedMaskingMonitorReport(rsp.Allocate, req.GetMaskingProductId(), priority, GetScheduleResultFactor(rsp.Allocate.GetSoftCriteria()))
	return rsp
}

func convertParcelTypeAttrMapToSlice(attrMap map[int64]*parcel_type_definition.ParcelTypeAttr) []*pb.ProductParcelInfo {
	productParcelInfo := make([]*pb.ProductParcelInfo, 0)
	for k, v := range attrMap {
		productParcelInfo = append(productParcelInfo, &pb.ProductParcelInfo{
			ProductId: proto.Int64(k),
			ParcelTypeAttr: &pb.ParcelTypeAttr{
				IsCod:       proto.Bool(v.IsCod),
				IsBulky:     proto.Bool(v.IsBulky),
				IsHighValue: proto.Bool(v.IsHighValue),
				IsDg:        proto.Bool(v.IsDg),
			},
		})
	}
	return productParcelInfo
}

// TODO need confirmation
// 1. 接口协议：生成新的接口协议
// 2. allocate配置：get allocate config 是否保留
func (a *AllocationServiceImpl) batchAllocate(ctx context.Context, req *pb.MaskingReq) *pb.ASyncAllocateResp {
	// 初始化Resp
	rsp := &pb.ASyncAllocateResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}

	// 提前计算运费
	preCalcFee, calErr := a.softRuleService.calculateShippingFee(ctx, req.GetProductIds(), req.GetOrderInfo())
	if calErr != nil {
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, PreCalcShippingFee, FailValue)
		logger.CtxLogErrorf(ctx, "pre calculate shipping fee failed|err=%v", calErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, calErr.Error()))
		return rsp
	}

	// 数据转换
	// 1. 初始化hold单数据
	tabMsg, convErr := a.initHoldOrderData(ctx, req, preCalcFee)
	if convErr != nil {
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, InitHoldOrderData, FailValue)
		logger.CtxLogErrorf(ctx, "convert data to table message failed|err=%v", convErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, convErr.Error()))
		return rsp
	}
	// 2. 填充esf运费信息
	addErr := a.addFulfillmentShippingFee(ctx, req, tabMsg)
	if addErr != nil {
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, AddFulfillmentShippingFee, FailValue)
		logger.CtxLogErrorf(ctx, "add fulfillment shipping fee to table data failed|err=%v", addErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, addErr.Error()))
		return rsp
	}

	// 数据发送至Kafka
	bytes, mErr := jsoniter.Marshal(tabMsg)
	if mErr != nil {
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, SendHoldDataToKafka, FailValue)
		logger.CtxLogErrorf(ctx, "marshal table message to kafka bytes failed|err=%v", mErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, mErr.Error()))
		return rsp
	}
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrBANamespace
	if kErr := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameBatchAllocateHoldOrders, bytes, nil, ""); kErr != nil {
		ReportBatchAllocate(ctx, strconv.FormatInt(req.GetMaskingProductId(), 10), HoldOrderScene, SendHoldDataToKafka, FailValue)
		logger.CtxLogErrorf(ctx, "send message to kafka failed|err=%v", kErr)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocateError, nil, kErr.Error()))
		return rsp
	}
	return rsp
}

// AdjustCapacity 参数说明：
// 买家重选后的channel_id: req.GetReselectChannelId()
// 买家重选后的channel_id对应的masking_channel_id:req.GetMaskingProductId()
// 重选前调度出来的channel_id:req.GetFulfilmentChannelId()
func (a *AllocationServiceImpl) AdjustCapacity(ctx context.Context, req *pb.AdjustCapacityReq) (*pb.AdjustCapacityResp, error) {
	rsp := &pb.AdjustCapacityResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	errMsg := ""
	if len(req.GetPickupLocationIds()) < 1 && len(req.GetDeliveryLocationIds()) < 1 {
		//增加reselectChannelID对应的volume
		e := a.maskVolumeCounter.IncrProductVolume(ctx, req.GetMaskingProductId(), req.GetReselectChannelId(), &parcel_type_definition.ParcelTypeAttr{})
		if e != nil {
			logger.CtxLogErrorf(ctx, "Increase order counter| reselectChannelID=%v, maskingChannelID=%v, err=%v", req.GetReselectChannelId(), req.GetMaskingProductId(), e)
			errMsg = errMsg + e.Error()
		}

		//减少fulfilmentChannelID对应的volume
		e = a.maskVolumeCounter.DecrProductVolume(ctx, req.GetMaskingProductId(), req.GetFulfilmentChannelId())
		if e != nil {
			logger.CtxLogErrorf(ctx, "Decrease order counter| fulfilmentChannelID=%v, maskingChannelID=%v, err=%v", req.GetFulfilmentChannelId(), req.GetMaskingProductId(), e)
			errMsg = errMsg + e.Error()
		}

		if err := a.UpdateShopFulfillmentProducts(
			ctx, req.GetOrderId(), 0, req.GetMaskingProductId(), req.GetReselectChannelId(), req.GetFulfilmentChannelId()); err != nil {
			logger.CtxLogErrorf(ctx, " update shop fulfillment products failed | err: %v", err)
			errMsg = errMsg + err.Error()
		}
	} else {
		nowTime := timeutil.GetLocalTime(ctx).Unix()
		param := AllocateCounterParam{
			OrderID:             req.GetOrderId(),
			MaskingProductId:    req.GetMaskingProductId(),
			PickupLocationIds:   req.GetPickupLocationIds(),
			DeliveryLocationIds: req.GetDeliveryLocationIds(),
			SloCreateTime:       nowTime,
			OrderType:           req.GetOrderType(),
			PickupPostcode:      req.GetPickupPostcode(),
			DeliveryPostcode:    req.GetDeliveryPostcode(),
			VolumeScene:         pb.UpdateVolumeScene_MaskingRerouteOrder.String(),
		}
		//增加reselectChannelID对应的volume
		param.ProductId = req.GetReselectChannelId()
		param.UpdateType = pb.UpdateVolumeType_Create
		e := a.AllocateVolumeCounter(ctx, param)
		if e != nil {
			logger.CtxLogErrorf(ctx, "Increase order counter| reselectChannelID=%v, maskingChannelID=%v, err=%v", req.GetReselectChannelId(), req.GetMaskingProductId(), e)
			errMsg = errMsg + e.Error()
		}
		// 上报运力监控
		prometheusutil.ReportMaskingVolumeSceneMonitor(prometheusutil.AdjustCapacityInterface, pb.UpdateVolumeScene_CreateOrder.String(), req.GetMaskingProductId(), req.GetReselectChannelId(), pb.UpdateVolumeType_Create.String())

		//减少fulfilmentChannelID对应的volume
		param.ProductId = req.GetFulfilmentChannelId()
		param.UpdateType = pb.UpdateVolumeType_Cancel
		e = a.AllocateVolumeCounter(ctx, param)
		if e != nil {
			logger.CtxLogErrorf(ctx, "Decrease order counter| fulfilmentChannelID=%v, maskingChannelID=%v, err=%v", req.GetFulfilmentChannelId(), req.GetMaskingProductId(), e)
			errMsg = errMsg + e.Error()
		}
		// 上报运力监控
		prometheusutil.ReportMaskingVolumeSceneMonitor(prometheusutil.AdjustCapacityInterface, pb.UpdateVolumeScene_CancelOrder.String(), req.GetMaskingProductId(), req.GetReselectChannelId(), pb.UpdateVolumeType_Cancel.String())
	}

	if errMsg != "" {
		monitoring.ReportError(ctx, monitoring.CatAllocateApi, monitoring.AdjustCapacityError, errMsg)
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AdjustCapacityError, nil, errMsg))
	}

	// 对于BMFC场景，需要记录订单到结果表
	if req.GetOrderId() != 0 {
		a.recordBMFCOrderResult(ctx, req.GetOrderId(), int(req.GetMaskingProductId()), int(req.GetReselectChannelId()))
	}

	return rsp, nil
}

// UpdateShopFulfillmentProducts 增加新Product统计量，扣减旧Product统计量
func (a *AllocationServiceImpl) UpdateShopFulfillmentProducts(
	ctx context.Context, orderID uint64, shopID int64, maskProductID, addProductID, remProductID int64) *srerr.Error {

	var (
		today             = timeutil.GetCurrentTime(ctx)
		batchAllocateConf = configutil.GetBatchAllocateConf()
	)

	if !batchAllocateConf.PickupEffStatsEnable {
		return nil
	}

	// 非Masking场景不需要统计
	if maskProductID == 0 {
		return nil
	}

	// 二者必须有一不为空
	if shopID == 0 && orderID == 0 {
		return nil
	}

	// 上游有Shop ID则直接用，没有则用Order ID反查Hold单表
	if shopID == 0 {
		orderInfo, err := a.BatchAllocateOrderRepo.GetHoldOrderInfoByOrderID(ctx, orderID, int(maskProductID), today.Day())
		if err != nil {
			logger.CtxLogInfof(ctx, "can not find hold order info | orderId=%d, maskingProductId=%d, day=%d", orderID, maskProductID, today.Day())
			// 找不到属于正常情况
			return nil
		}

		if orderInfo.ShopID == 0 {
			logger.CtxLogInfof(ctx, "order shop id is 0 | orderId=%d, maskingProductId=%d", orderID, maskProductID)
			return nil
		}

		shopID = orderInfo.ShopID
	}

	if addProductID != 0 {
		if err := a.PickupEffCounter.IncrShopFulfillmentProducts(ctx, shopID, maskProductID, addProductID, 1, today); err != nil {
			return err
		}
	}

	if remProductID != 0 {
		if err := a.PickupEffCounter.IncrShopFulfillmentProducts(ctx, shopID, maskProductID, remProductID, -1, today); err != nil {
			return err
		}
	}

	return nil
}

func (a *AllocationServiceImpl) recordBMFCOrderResult(ctx context.Context, orderID uint64, maskingProductID, reselectProductID int) {
	// 记录Order ID到Redis中
	redisKey := allocation2.GetBMFCOrderRecordKey(orderID)
	if err := redisutil.Set(ctx, redisKey, orderID, constant.BMFCOrderRecordExpire); err != nil {
		logger.CtxLogErrorf(ctx, "failed to record BMFC order ID to redis | orderID=%d, err=%v", orderID, err)
	}

	// 先找当天的结果表
	date := timeutil.GetLocalTime(ctx)
	orderResult, err := a.BatchAllocateOrderRepo.GetOrderResultByOrderID(ctx, orderID, date)
	if err != nil {
		logger.CtxLogInfof(ctx, "can not find order result | tablePartition=%d", date.Day())
		// 当天的找不到再找昨天的结果表
		yesterday := date.AddDate(0, 0, -1)
		orderResult, err = a.BatchAllocateOrderRepo.GetOrderResultByOrderID(ctx, orderID, yesterday)
		if err != nil {
			logger.CtxLogInfof(ctx, "can not find order result | tablePartition=%d", yesterday.Day())
		} else {
			// 找到就把日期换成昨天的
			date = yesterday
		}
	}

	if orderResult != nil {
		orderResult.AllocateResult = reselectProductID
		orderResult.OrderStatus = batch_allocate.OrderStatusTypeSyncAllocated
	} else {
		orderResult = &order.OrderResultTab{
			OrderID:        orderID,
			OrderStatus:    batch_allocate.OrderStatusTypeSyncAllocated,
			MaskProductID:  maskingProductID,
			AllocateResult: reselectProductID,
		}
	}

	if err := a.BatchAllocateOrderRepo.CreateOrUpdateOrderResult(ctx, orderResult, date.Day()); err != nil {
		logger.CtxLogErrorf(ctx, "create order result failed | orderID=%d, err=%v", orderID, err)
	}
}

// AdjustLocVolume 更新country和zone/route维度的当前单量
// AdjustLocVolume 仅lps allocate 接口使用
func (a *AllocationServiceImpl) AdjustLocVolume(ctx context.Context, req *pb.AdjustLocVolumeReq) (*pb.AdjustLocVolumeResp, error) {
	//1. fulfillment allocate运力更新
	rsp, _ := a.adjustLocVolume(ctx, req)
	if rsp.GetHeader().GetRetcode() != 0 {
		return rsp, nil
	}
	//2. install allocate运力更新
	// 构建install allocate运力更新参数
	if req.GetInstallAdjustLocVolumeInfo().GetNeedInstallAllocate() {
		for _, installLocVolumeInfo := range req.GetInstallAdjustLocVolumeInfo().GetInstallLocVolumeInfoList() {
			monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.NonWmsUpdateVolumeTotalNum, "")
			installAdjustLocVolumeReq := &pb.AdjustLocVolumeReq{
				Header:              req.Header,
				MaskingProductId:    proto.Int64(installLocVolumeInfo.GetMaskingProductId()),
				ProductId:           proto.Int64(installLocVolumeInfo.GetProductId()),
				PickupLocationIds:   req.GetPickupLocationIds(),
				DeliveryLocationIds: req.GetDeliveryLocationIds(),
				HardProductIds:      []int64{installLocVolumeInfo.GetProductId()},
				PickupPostcode:      proto.String(req.GetPickupPostcode()),
				DeliveryPostcode:    proto.String(req.GetDeliveryPostcode()),
			}
			installRsp, _ := a.adjustLocVolume(ctx, installAdjustLocVolumeReq)
			if installRsp.GetHeader().GetRetcode() != 0 {
				monitoring.ReportError(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.NonWmsUpdateVolumeError, fmt.Sprintf("non wms order update volume error|retcode=%d, message=%s, casue=%s", installRsp.GetHeader().GetRetcode(), installRsp.GetHeader().GetMessage(), installRsp.GetHeader().GetCause()))
				rsp.Header = installRsp.Header
				return rsp, nil
			}
		}
	}
	return rsp, nil
}

// adjustLocVolume 更新masking运力
func (a *AllocationServiceImpl) adjustLocVolume(ctx context.Context, req *pb.AdjustLocVolumeReq) (*pb.AdjustLocVolumeResp, error) {
	rsp := &pb.AdjustLocVolumeResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	locVols, err := a.MatchLocationVolumesForUpdateVolume(
		ctx, req.GetMaskingProductId(), req.GetHardProductIds(), req.GetPickupLocationIds(), req.GetDeliveryLocationIds(),
		rule_mode.MplOrderRule, req.GetPickupPostcode(), req.GetDeliveryPostcode(), allocation2.SingleAllocate,
	)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatAllocateApi, monitoring.AdjustLocVolumeError, fmt.Sprintf("get locvols error:%v", err))
		logger.CtxLogErrorf(ctx, "matchLocationVolumes fail, err:%v, mask_product_id:%d, product_ids:%d, pickup_location_ids:%v, delivery_location_ids:%v", err, req.GetMaskingProductId(), req.GetProductId(), req.GetPickupLocationIds(), req.GetDeliveryLocationIds())
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.AllocationLocVolumeError, req.GetMaskingProductId(), err.Error()))
		return rsp, nil
	}

	parcelAttr := a.GetAdjustVolumeParcelTypeAttr(ctx, req.GetMaskingProductId(), req.GetProductId(), req.GetParcelDimension(), req.GetCogs(), req.GetPaymentMethod(), req.GetDgType())
	// 更新运力需要对匹配到底的（zoneCode + zoneDirection） 或 routeCode去重
	removeDupLocVols := rulevolume.RemoveDuplicationLocVols(locVols)
	groupCode := a.GetFulfillmentProductGroupCode(ctx, req.GetMaskingProductId(), req.GetProductId(), rule_mode.MplOrderRule, allocation2.SingleAllocate)
	_ = a.maskVolumeCounter.IncrMaskVolume(ctx, req.GetMaskingProductId(), req.GetProductId(), groupCode, parcelAttr)
	rsp.LocCodes = make([]string, len(removeDupLocVols[req.GetProductId()]))
	// 上报route&zone模式更新运力，规则是分单的时候优先匹配route，更新运力的时候要更新匹配到的route和zone
	monitorRouteAndZoneMode(ctx, removeDupLocVols[req.GetProductId()])
	for i, locVol := range removeDupLocVols[req.GetProductId()] {
		rsp.LocType = proto.Int64(int64(locVol.LocType))
		rsp.LocCodes[i] = locVol.LocCode
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			_ = a.maskVolumeCounter.IncrRouteVolume(ctx, req.GetMaskingProductId(), req.GetProductId(), groupCode, locVol.LocCode, rule_mode.MplOrderRule, parcelAttr)
			rsp.LocCodeInfoList = append(rsp.LocCodeInfoList, &pb.LocCodeInfo{
				LocType: pb.MaskLocCodeType_Route.Enum(),
				LocCode: proto.String(locVol.LocCode),
			})
		case rulevolume.MaskLocTypeZone:
			_ = a.maskVolumeCounter.IncrZoneVolume(ctx, req.GetMaskingProductId(), req.GetProductId(), groupCode, locVol.LocCode, locVol.ZoneDirection, rule_mode.MplOrderRule, parcelAttr)
			rsp.LocCodeInfoList = append(rsp.LocCodeInfoList, &pb.LocCodeInfo{
				LocType:       pb.MaskLocCodeType_Zone.Enum(),
				LocCode:       proto.String(locVol.LocCode),
				ZoneDirection: pb.MaskZoneDirection(locVol.ZoneDirection).Enum(),
			})
		}
	}

	// 上报shop_group_id单量
	if req.GetShopGroupId() != 0 {
		nowTime := timeutil.GetLocalTime(ctx).Unix()
		_ = a.VolumeCounter.UpdateShopGroupVolume(ctx, req.GetMaskingProductId(), req.GetProductId(), groupCode, req.GetShopGroupId(), nowTime, pb.UpdateVolumeType_Create, rule_mode.MplOrderRule)
	}

	if err := a.PickupEffCounter.IncrShopFulfillmentProducts(ctx, int64(req.GetShopId()), req.GetMaskingProductId(), req.GetProductId(), 1, timeutil.GetCurrentTime(ctx)); err != nil {
		logger.CtxLogErrorf(ctx, "add shop fulfillment products failed | err: %v", err)
	}

	// 上报运力监控
	prometheusutil.ReportMaskingVolumeSceneMonitor(prometheusutil.AdjustLocVolumeInterface, pb.UpdateVolumeScene_CreateOrder.String(), req.GetMaskingProductId(), req.GetProductId(), pb.UpdateVolumeType_Create.String())

	return rsp, nil
}

func monitorRouteAndZoneMode(ctx context.Context, locVolumes []*rulevolume.MaskLocVolume) {
	routeFlag := false
	zoneFlag := false
	for _, loc := range locVolumes {
		if loc.LocType == rulevolume.MaskLocTypeRoute {
			routeFlag = true
		}

		if loc.LocType == rulevolume.MaskLocTypeZone {
			zoneFlag = true
		}
	}

	if routeFlag && zoneFlag {
		monitoring.ReportSuccess(ctx, monitoring.CatAllocateApi, monitoring.RouteAndZoneMode, fmt.Sprintf("route & zone mode %s", objutil.JsonString(locVolumes)))
	}
}

func (a *AllocationServiceImpl) GetOpenComponentProducts(ctx context.Context, req *pb.GetOpenComponentProductsReq) (*pb.GetOpenComponentProductsResp, error) {
	resp := &pb.GetOpenComponentProductsResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}

	priority, err := a.outerCheck.GetMaskingMultiProductPriority(ctx, req.GetMaskingProductId(), req.GetShopGroupIds())
	if err != nil {
		resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return resp, err
	}

	for _, c := range priority.ComponentPriorities {
		if c.Status == entity.Open {
			resp.ComponentProductIds = append(resp.ComponentProductIds, c.ProductID)
		}
	}

	return resp, nil
}

// IncrAllocateVolume 这个是仓单更新运力，由lps的/incr_volume接口调用
func (a *AllocationServiceImpl) IncrAllocateVolume(ctx context.Context, req *pb.IncrAllocateVolumeReq) (*pb.IncrAllocateVolumeResp, error) {
	resp := &pb.IncrAllocateVolumeResp{
		Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil),
	}
	//1. 物流单运力增加
	param := AllocateCounterParam{
		MaskingProductId:    req.GetMaskProductId(),
		ProductId:           req.GetProductId(),
		PickupLocationIds:   req.GetPickupLocationIds(),
		DeliveryLocationIds: req.GetDeliveryLocationIds(),
		SloCreateTime:       req.GetSloCreateTime(),
		UpdateType:          req.GetUpdateVolumeType(),
		OrderType:           req.GetOrderType(),
		PickupPostcode:      req.GetPickupPostCode(),
		DeliveryPostcode:    req.GetDeliverPostCode(),
		VolumeScene:         pb.UpdateVolumeScene_CreateOrder.String(),
	}
	if err := a.AllocateVolumeCounter(ctx, param); err != nil {
		logger.CtxLogErrorf(ctx, "update allocate volume failed %v", err)
		resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		monitoring.ReportError(ctx, monitoring.CatIncrAllocateVolumeApi, monitoring.IncrAllocateVolumeError, fmt.Sprintf("requestId: %v, incr allocate volume error: %v", req.GetHeader().GetRequestId(), err))
		return resp, nil
	}
	monitoring.ReportSuccess(ctx, monitoring.CatIncrAllocateVolumeApi, monitoring.IncrAllocateVolumeSuccess, fmt.Sprintf("requestId: %v, incr allocate volume Success", req.GetHeader().GetRequestId()))
	// 上报运力监控
	prometheusutil.ReportMaskingVolumeSceneMonitor(prometheusutil.IncrVolumeInterface, pb.UpdateVolumeScene_CreateOrder.String(), req.GetMaskProductId(), req.GetProductId(), pb.UpdateVolumeType_Create.String())
	prometheusutil.EstimateOrderRateMonitorReport(req.GetMaskProductId(), req.GetProductId())

	//2. install allocate运力更新
	// 构建install allocate运力更新参数
	if req.GetInstallAdjustLocVolumeInfo().GetNeedInstallAllocate() {
		for _, installLocVolumeInfo := range req.GetInstallAdjustLocVolumeInfo().GetInstallLocVolumeInfoList() {
			monitoring.ReportSuccess(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.WmsUpdateVolumeTotalNum, "")
			installAdjustLocVolumeReq := AllocateCounterParam{
				MaskingProductId:    installLocVolumeInfo.GetMaskingProductId(),
				ProductId:           installLocVolumeInfo.GetProductId(),
				PickupLocationIds:   req.GetPickupLocationIds(),
				DeliveryLocationIds: req.GetDeliveryLocationIds(),
				PickupPostcode:      req.GetPickupPostCode(),
				DeliveryPostcode:    req.GetDeliverPostCode(),
				UpdateType:          req.GetUpdateVolumeType(),
				OrderType:           req.GetOrderType(),
				VolumeScene:         pb.UpdateVolumeScene_CreateOrder.String(),
			}
			err := a.AllocateVolumeCounter(ctx, installAdjustLocVolumeReq)
			if err != nil {
				monitoring.ReportError(ctx, monitoring.CatInstallAllocateUpdateVolume, monitoring.WmsUpdateVolumeError, fmt.Sprintf("wms order update volume error|err=%v", err))
				logger.CtxLogErrorf(ctx, "update install allocate volume failed %v", err)
				resp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
				return resp, nil
			}
		}
	}

	return resp, nil
}

func (a *AllocationServiceImpl) AllocateVolumeCounter(ctx context.Context, param AllocateCounterParam) *srerr.Error {
	var (
		productIds          []int64
		orderId             = param.OrderID
		maskingProductId    = param.MaskingProductId
		productId           = param.ProductId
		pickupLocationIds   = param.PickupLocationIds
		deliveryLocationIds = param.DeliveryLocationIds
		sloCreateTime       = param.SloCreateTime
		updateType          = param.UpdateType
		orderType           = param.OrderType
		pickupPostcode      = param.PickupPostcode
		deliveryPostcode    = param.DeliveryPostcode
		volumeScene         = param.VolumeScene
		shopId              = param.ShopId
	)
	productIds = append(productIds, productId)
	ruleMode := OrderTypeMappingRuleMode(ctx, orderType)
	locVols, err := a.MatchLocationVolumesForUpdateVolume(
		ctx, maskingProductId, productIds, pickupLocationIds, deliveryLocationIds,
		ruleMode, pickupPostcode, deliveryPostcode, allocation2.SingleAllocate,
	)
	if err != nil {
		return srerr.With(srerr.AllocationLocVolumeError, maskingProductId, err)
	}
	// 更新运力需要对匹配到底的（zoneCode + zoneDirection） 或 routeCode去重
	removeDupLocVols := rulevolume.RemoveDuplicationLocVols(locVols)

	//only product of allocation enabled will check rule
	ruleInfo, err := a.RuleRepo.GetEffectiveRuleByCache(ctx, maskingProductId, ruleMode, allocation2.SingleAllocate)
	if err != nil {
		logger.CtxLogErrorf(ctx, "soft_criteria for allocate, masking product:%v, get effective rule err:%v", maskingProductId, err)
		return srerr.New(srerr.AllocationRuleError, maskingProductId, err.Error())
	}
	groupCode := a.GetFulfillmentProductGroupCode(ctx, maskingProductId, productId, ruleMode, allocation2.SingleAllocate)
	_ = a.VolumeCounter.UpdateMaskVolume(ctx, maskingProductId, productId, groupCode, sloCreateTime, updateType, ruleMode)
	for _, locVol := range removeDupLocVols[productId] {
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			_ = a.VolumeCounter.UpdateRouteVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, sloCreateTime, updateType, ruleMode)
		case rulevolume.MaskLocTypeZone:
			_ = a.VolumeCounter.UpdateZoneVolume(ctx, maskingProductId, productId, groupCode, locVol.LocCode, locVol.ZoneDirection, sloCreateTime, updateType, ruleMode)
		}
	}

	// 更新规则内的batch volume 计数
	// 注意：在应用层要更新当天的总计数，因为如果硬性校验得到了唯一的fulfillment product，不会走这里的allocate
	_ = a.VolumeCounter.UpdateVolumeForRule(ctx, productId, ruleInfo.Id, ruleInfo.GetBatchVolume(), sloCreateTime, updateType)

	if updateType == pb.UpdateVolumeType_Create {
		if err := a.UpdateShopFulfillmentProducts(ctx, orderId, shopId, maskingProductId, productId, 0); err != nil {
			logger.CtxLogErrorf(ctx, " update shop fulfillment products failed | err: %v", err)
		}
	} else if updateType == pb.UpdateVolumeType_Cancel {
		if err := a.UpdateShopFulfillmentProducts(ctx, orderId, shopId, maskingProductId, 0, productId); err != nil {
			logger.CtxLogErrorf(ctx, " update shop fulfillment products failed | err: %v", err)
		}
	}

	// SSCSMR-3546: 上报promethous，配置告警大盘
	prometheusutil.ReportAllocateVolumeCounter(envvar.GetRequestUrl(ctx), strconv.FormatInt(maskingProductId, 10), strconv.FormatInt(productId, 10), orderType.String(), updateType.String(), volumeScene)
	return nil
}

func generateAllocateLog(ctx context.Context, req *pb.MaskingReq, rsp *pb.MaskingResp) *allocation.LogDetail {
	var hardOutputList []int
	for _, hardProductId := range req.GetHardProductIds() {
		hardOutputList = append(hardOutputList, int(hardProductId))
	}
	logDetail := allocation.LogDetail{
		MaskProductId:        int(req.GetMaskingProductId()),
		FulfillmentProductId: int(rsp.GetAllocate().GetProductId()),
		SoftRuleId:           rsp.GetAllocate().GetRuleId(),
		ShopGroupId:          rsp.GetAllocate().GetShopGroupId(),
		HardOutput:           hardOutputList,
		SoftInput:            req.GetProductIds(),
		SoftOutput:           []int64{rsp.GetAllocate().GetProductId()},
		SoftCriteriaListStr:  rsp.GetAllocate().GetSoftCriteria(),
	}
	return &logDetail
}

func (a *AllocationServiceImpl) SendScheduleCountStat(ctx context.Context, req *pb.MaskingReq, rsp *pb.MaskingResp) {
	if !configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.Allocate)) {
		return
	}

	statCtx := ctxhelper.CloneTrace(ctx)
	sendStatFunc := func() {
		defer func() {
			if e := recover(); e != nil {
				logger.CtxLogErrorf(statCtx, "send masking schedule count stat error:%v", e)
			}
		}()

		// 发送统计数据到Saturn进行异步处理
		msg := allocation.MarshallStatMsg(
			generateAllocateLog(statCtx, req, rsp),
			schedule_stat.Allocate,
			timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), schedule_stat.MaskingScheduleStatHourTimeFormat),
		)
		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		if err := kafkahelper.DeliveryMessage(statCtx, namespace, constant.TaskNameScheduleCountStat, msg, nil, constant.TaskNameScheduleCountStat); err != nil {
			logger.CtxLogErrorf(statCtx, "SendMessage failed|err=%v", err)
		}
	}

	// 避免堵塞主流程，使用协程池异步发送Kafka
	if err := goroutinePool.Submit(sendStatFunc); err != nil {
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatGoroutinePool, "AllocateSendScheduleCountStat", monitoring.StatusError, err.Error())
		logger.CtxLogErrorf(ctx, "submit async send stat func to goroutine pool fail | err=%v", err)
	}
}

// removeReselectPreviousProduct 如果入参的Products中有Reselect Previous Product，则移除
func removeReselectPreviousProduct(ctx context.Context, reselectPreviousProductId int64, products []int64) []int64 {
	filterProduct := make([]int64, 0, len(products))
	for _, p := range products {
		if p == reselectPreviousProductId {
			logger.CtxLogInfof(ctx, "remove buyer reselect previous product: %d", reselectPreviousProductId)
			continue
		}
		filterProduct = append(filterProduct, p)
	}

	return filterProduct
}

// needRemoveReselectPreviousProduct 判断是否需要移除Buyer Reselect Previous Product, 需要满足Reallocation场景 + 有Reselect记录
func needRemoveReselectPreviousProduct(req *pb.MaskingReq) bool {
	return req.GetAllocateScenario() == pb.AllocationScenario_Reallocation && req.GetReselectPreviousProductId() != 0
}

// 业务背景：上游的Reselect只会触发一次，即只允许Buyer有一次机会进行Reselect
// filterBuyerRejectedFulfillmentProduct 判断是否需要移除Buyer Reselect Fulfillment Product, 需要则去除，否则直接返回原来的
func FilterBuyerRejectedFulfillmentProduct(ctx context.Context, req *pb.MaskingReq) ([]int64, *srerr.Error) {
	if !needRemoveReselectPreviousProduct(req) {
		return req.GetProductIds(), nil
	}

	filterProducts := removeReselectPreviousProduct(ctx, req.GetReselectPreviousProductId(), req.GetProductIds())
	// 对于去除Origin Product后无可用Product的情况下，直接报错
	if len(filterProducts) == 0 {
		return nil, srerr.New(srerr.AllocateError, req.GetMaskingProductId(), "No available products after remove buyer reselect previous product")
	}

	return filterProducts, nil
}

func (a *AllocationServiceImpl) BatchGetAllocateOrderResult(ctx context.Context, req *pb.BatchGetAllocateOrderResultReq) (*pb.BatchGetAllocateOrderResultResp, error) {
	var (
		resp = &pb.BatchGetAllocateOrderResultResp{Header: grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)}
		date = timeutil.GetCurrentTime(ctx)
		// 按天记录order id，为后续聚合时绑定day与Order id的关系
		dayOrderMap = make(map[int]map[uint64]struct{}, 0)
		// 按Order status聚合order id list，做重入操作
		dayStatusOrderIDListMap         = make(map[int]map[batch_allocate.OrderStatus][]uint64, 0)
		illegalRetcodeNum, illegalSFNum = 0, 0
		logStartTime                    = timeutil.GetCurrentUnixMilliTimeStamp(ctx)
	)

	// 从T中获取
	result, err := a.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx, req.GetOrderIdList(), date.Day())
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetOrderResultByOrderID failed: %v", err)
	}

	// 绑定T与order tab id
	for _, r := range result {
		// 先by day初始化map
		if dayOrderMap[date.Day()] == nil {
			dayOrderMap[date.Day()] = make(map[uint64]struct{}, 0)
			dayStatusOrderIDListMap[date.Day()] = make(map[batch_allocate.OrderStatus][]uint64, 0)
		}
		// 按天记录Order tab主键ID
		dayOrderMap[date.Day()][r.OrderID] = struct{}{}
	}

	// 如果从T中已经获取到的就不需要再从T-1获取了（如果两边都有就是以T为准）
	var remainOrderIDList []uint64
	if len(result) == 0 {
		remainOrderIDList = req.GetOrderIdList()
	} else {
		foundOrderIDMap := make(map[uint64]struct{}, len(result))
		for _, r := range result {
			foundOrderIDMap[r.OrderID] = struct{}{}
		}
		for _, o := range req.GetOrderIdList() {
			if _, exist := foundOrderIDMap[o]; exist {
				continue
			}
			remainOrderIDList = append(remainOrderIDList, o)
		}
	}

	// 从T-1中获取
	yesterdayResult, err := a.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx, remainOrderIDList, date.AddDate(0, 0, -1).Day())
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetOrderResultByOrderID failed: %v", err)
	}
	// 绑定T-1与order tab id
	for _, r := range yesterdayResult {
		// 先by day初始化map
		yesterday := date.AddDate(0, 0, -1).Day()
		if dayOrderMap[yesterday] == nil {
			dayOrderMap[yesterday] = make(map[uint64]struct{}, 0)
			dayStatusOrderIDListMap[yesterday] = make(map[batch_allocate.OrderStatus][]uint64, 0)
		}
		// 按天记录Order tab主键ID
		dayOrderMap[yesterday][r.OrderID] = struct{}{}
	}

	// 正常能获取到的订单
	result = append(result, yesterdayResult...)
	successOrders := make(map[uint64]struct{}, len(result))
	for _, r := range result {
		successOrders[r.OrderID] = struct{}{}

		var (
			// 模拟push流程
			retcode, orderStatusAfterPush, message = order.GetOrderStatus(ctx, r)
			fulfillmentShippingFee                 float64 //默认为0，只有正常调度完成且不报错的单，才复制shipping fee
		)
		if retcode == batch_allocate.Success {
			fulfillmentShippingFee = r.FulfillmentShippingFeeInfo.FulfillmentShippingFee
		}

		// 记录-1运费的次数
		if r.FulfillmentShippingFeeInfo.FulfillmentShippingFee == batch_allocate.IllegalEsf {
			illegalSFNum += 1
		}
		// 记录非0 retcode的次数
		if retcode != batch_allocate.Success {
			illegalRetcodeNum += 1
		}

		// status = 3的订单才需要扭转终态
		if r.OrderStatus == batch_allocate.OrderStatusTypeAsyncAllocated {
			if orderStatusAfterPush == batch_allocate.OrderStatusTypeAsyncAllocated { // 调度完毕&&没有error的订单，扭转为pushed状态
				orderStatusAfterPush = batch_allocate.OrderStatusTypeAsyncPushed
			}
			// 只针对status = 3的订单，将订单结果修改为对应的终态
			for day, orderMap := range dayOrderMap {
				if _, ok := orderMap[r.OrderID]; ok {
					dayStatusOrderIDListMap[day][orderStatusAfterPush] = append(dayStatusOrderIDListMap[day][orderStatusAfterPush], r.OrderID)
				}
			}
		}

		// install allocate结果转换
		var installAllocateList []*pb.InstallAllocate
		for _, installAllocateResult := range r.InstallAllocateResult {
			installAllocateList = append(installAllocateList, &pb.InstallAllocate{
				ItemUniqueId:     proto.String(installAllocateResult.ItemUniqueId),
				ItemId:           proto.Uint64(installAllocateResult.ItemId),
				MaskingProductId: proto.Int64(installAllocateResult.MaskingProductId),
				ProductId:        proto.Int64(installAllocateResult.ProductId),
			})
		}

		resp.OrderResultList = append(resp.OrderResultList, &pb.OrderResultUnit{
			Retcode:                proto.Int(retcode),
			Message:                proto.String(message),
			OrderId:                proto.Uint64(r.OrderID),
			FulfillmentProductId:   proto.Int(r.AllocateResult),
			FulfillmentShippingFee: proto.Float64(fulfillmentShippingFee),
			InstallAllocateList:    installAllocateList,
		})
	}

	// 重入订单状态，将fall back订单扭转到终态
	for day, statusOrderIDList := range dayStatusOrderIDListMap {
		for status, orderIDList := range statusOrderIDList {
			bErr := a.BatchAllocateOrderRepo.BatchUpdateStatusByOrderID(ctx, day, orderIDList, status)
			if bErr != nil {
				msg := fmt.Sprintf("batch update err:%v, by day:%d, status:%d, order id list:%v", bErr, day, status, orderIDList)
				logger.CtxLogErrorf(ctx, msg)
				// 上报Prometheus
				prometheusutil.AddBatchAllocateCounter(prometheusutil.CounterInfo{
					Scene:       "BatchGetAllocateOrderResult",
					Field:       "UpdateStatus",
					FieldValue:  strconv.Itoa(int(status)),
					ReportValue: float64(len(orderIDList)),
				})
			}
		}
	}

	// 无法获取的订单
	for _, o := range req.GetOrderIdList() {
		if _, exist := successOrders[o]; exist {
			continue
		}

		resp.OrderResultList = append(resp.OrderResultList, &pb.OrderResultUnit{
			Retcode: proto.Int(srerr.OrderResultNotFoundError.Code()),
			Message: proto.String(srerr.OrderResultNotFoundError.Msg()),
			OrderId: proto.Uint64(o),
		})
	}

	// 上报Prometheus，监控：
	// 1. retcode ！= 0 的数量
	// 2. shipping fee = -1的数量
	// 告警规则：用当前count数值 - 10s前count数据，如果大于0就触发告警。如果告警持续一分钟以上，需要联系上游回切sync链路
	prometheusutil.AddBatchAllocateCounter(prometheusutil.CounterInfo{
		Scene:       "BatchGetAllocateOrderResult",
		Field:       "Illegal retcode num",
		ReportValue: float64(illegalRetcodeNum),
	})
	prometheusutil.AddBatchAllocateCounter(prometheusutil.CounterInfo{
		Scene:       "BatchGetAllocateOrderResult",
		Field:       "Illegal shipping fee num",
		ReportValue: float64(illegalSFNum),
	})

	logCostTime := timeutil.GetCurrentUnixMilliTimeStamp(ctx) - logStartTime
	logger.CtxLogInfof(ctx, "cost time:%d", logCostTime)

	return resp, nil
}

func (a *AllocationServiceImpl) getProductGroupCodeMapping(ctx context.Context, volumeRuleID int64) map[int64]string {
	m := make(map[int64]string)

	volumeRule, err := a.maskRuleVolumeSrv.GetRuleVolumeByIDWithCache(ctx, uint64(volumeRuleID))
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetRuleVolumeByIDWithCache failed|id=%d,err=%v", volumeRuleID, err)
		return m
	}

	for _, groupInfo := range volumeRule.GroupInfo.FulfillmentProductGroupInfos {
		for _, fulfillmentProductInfo := range groupInfo.FulfillmentProductInfos {
			m[int64(fulfillmentProductInfo.FulfillmentProductID)] = groupInfo.GroupCode
		}
	}

	return m
}

func (a *AllocationServiceImpl) getAllocateParcelTypeAttr(
	ctx context.Context, maskProductID int64, fulfillmentProductIDs []int64, orderInfo *pb.MaskingOrderInfo, productInfos []*pb.ProductDgInfo,
) map[int64]*parcel_type_definition.ParcelTypeAttr {
	var (
		isCod            bool
		attrMap          = make(map[int64]*parcel_type_definition.ParcelTypeAttr, len(fulfillmentProductIDs))
		productDgInfoMap = make(map[int64]*pb.ProductDgInfo, len(productInfos))

		cogs = orderInfo.GetCogs()
	)
	if orderInfo.GetPaymentMethod() == constant.PaymentMethodCod {
		isCod = true
	}

	for _, productInfo := range productInfos {
		productDgInfoMap[productInfo.GetProductId()] = productInfo
	}

	for _, productId := range fulfillmentProductIDs {
		var (
			dimensionExist                bool
			length, width, height, weight float64
		)
		if parcelDimension, exist := orderInfo.GetProductParcelDimensions()[productId]; exist {
			dimensionExist = true
			length = parcelDimension.GetLength() // CM
			width = parcelDimension.GetWidth()   // CM
			height = parcelDimension.GetHeight() // CM
			weight = parcelDimension.GetWeight() // G
		}

		if info, exists := productDgInfoMap[productId]; !exists || info == nil {
			logger.CtxLogErrorf(ctx, "product info not found|product_id=%d", productId)
			monitoring.ReportError(ctx, monitoring.AllocateError, monitoring.DgNotFound, fmt.Sprintf("product info not found|product_id=%d", productId))
			productDgInfoMap[productId] = &pb.ProductDgInfo{}
		}
		attrMap[productId] = a.ParcelTypeService.GetParcelTypeAttr(
			ctx, int(maskProductID), int(productId), "", parcel_type_definition2.MaskingScenario, isCod, length, width, height, weight,
			cogs, dimensionExist, productDgInfoMap[productId].GetDgType())
	}

	logger.CtxLogInfof(ctx, "parcel type attribute: %s", objutil.JsonString(attrMap))

	return attrMap
}

func (a *AllocationServiceImpl) GetAdjustVolumeParcelTypeAttr(
	ctx context.Context, maskProductID int64, fulfillmentProductID int64, parcelDimension *pb.ParcelDimension, cogs float64,
	paymentMethod string, dgType pb.DgType) *parcel_type_definition.ParcelTypeAttr {
	var (
		isCod                         bool
		dimensionExist                bool
		length, width, height, weight float64
	)
	if paymentMethod == constant.PaymentMethodCod {
		isCod = true
	}

	if parcelDimension != nil {
		dimensionExist = true
		length = parcelDimension.GetLength()
		width = parcelDimension.GetWidth()
		height = parcelDimension.GetHeight()
		weight = parcelDimension.GetWeight()
	}

	parcelTypeAttr := a.ParcelTypeService.GetParcelTypeAttr(
		ctx, int(maskProductID), int(fulfillmentProductID), "", parcel_type_definition2.MaskingScenario, isCod,
		length, width, height, weight, cogs, dimensionExist, dgType)

	logger.CtxLogInfof(ctx, "parcel type attribute: %s", objutil.JsonString(parcelTypeAttr))

	return parcelTypeAttr
}

func (a *AllocationServiceImpl) CheckoutFulfillmentProductCounter(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest) *srerr.Error {
	logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|request=%s", objutil.JsonString(request))
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.TotalBusinessProcessNumber)
	//1. 检查是否是当天的订单
	currentDayStartTime := timeutil.GetStartTime(timeutil.GetLocalTime(ctx))
	if currentDayStartTime > int64(request.UpdateTime) {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.NonTodayOrder)
		logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|non today order, no need to update volume|orderId=%d, productId=%d", request.OrderId, request.ProductId)
		return nil
	}
	//2. 检查是否是checkout直选fulfillment product
	// 多个masking product id
	maskingProductIdList, checkResult := a.CheckCheckoutDirectFulfillmentProduct(ctx, request)
	// 不是checkout直选fulfillment product则直接返回
	if !checkResult {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.NonCheckoutFulfillmentProduct)
		logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|no direct fulfillment product|orderId=%d, productId=%d", request.OrderId, request.ProductId)
		return nil
	}
	//3. 检查是否是重复orderId
	value, err1 := redisutil.GetInt(ctx, fmt.Sprintf(CheckoutFulfillmentIdempotenceKey, request.OrderId))
	if err1 != nil && err1 != redis.Nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.GetOrderIdFromRedisError)
		// 去重校验，获取redis报错不拦截主流程
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|get redis error, err=%v", err1)
	}
	if err1 == nil && value == CheckoutFulfillmentIdempotenceValue {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.DuplicateOrder)
		logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|duplicate order, orderId=%d", request.OrderId)
		return nil
	}
	// 上报选择的fulfillment product业务指标
	prometheusutil.ReportCheckoutFulfillmentCounterBusiness(request.ProductId)
	logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|direct fulfillment product|orderId=%d, productId=%d", request.OrderId, request.ProductId)
	//4. 补全更新运力的参数
	updateVolumeReqList, err := a.CompletionCheckoutFulfillmentProductCounterRequest(ctx, request, maskingProductIdList)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.GenerateUpdateVolumeRequestError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|completion checkout fulfillment product counter request error, err=%v", err)
		return err
	}
	// 请求参数大于1表示一个fulfillment product属于多个masking product，上报个数
	if len(updateVolumeReqList) > 1 {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.MultiMaskingProductNum)
	}
	logger.CtxLogInfof(ctx, "CheckoutFulfillmentProductCounter|update volume request=%v", objutil.JsonString(updateVolumeReqList))
	//5. 更新运力
	for _, updateVolumeReq := range updateVolumeReqList {
		_, uErr := a.AdjustLocVolume(ctx, updateVolumeReq)
		if uErr != nil {
			prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.UpdateVolumeError)
			logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|update volulme error, err=%v", uErr)
			return srerr.With(srerr.CheckoutFProductUpdateVolumeError, nil, uErr)
		}
	}
	//6. 记录orderId，防止重复消费
	err2 := redisutil.Set(ctx, fmt.Sprintf(CheckoutFulfillmentIdempotenceKey, request.OrderId), CheckoutFulfillmentIdempotenceValue, CheckoutFulfillmentIdempotenceExpire)
	if err2 != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.RecordOrderIdToRedisError)
		logger.CtxLogErrorf(ctx, "CheckoutFulfillmentProductCounter|set order id error, err=%v", err2)
	}
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.BusinessProcess, prometheusutil.ExecuteBusinessSuccess)
	return nil
}

// CheckCheckoutDirectFulfillmentProduct 检查fulfillment product是否是checkout链路直选的product
func (a *AllocationServiceImpl) CheckCheckoutDirectFulfillmentProduct(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest) ([]int64, bool) {
	//1. 获取masking product配置
	maskingProductIdMap := make(map[int]struct{})
	fulfillmentProductIdMap := make(map[int][]int64)
	itemList := localcache.AllItems(ctx, constant.MaskingProductRef)
	for _, item := range itemList {
		if maskingProductRef, ok := item.(*lpsclient.GetMaskingProductRefData); ok {
			maskingProductIdMap[maskingProductRef.MaskingProductId] = struct{}{}
			for _, fulfillmentProductId := range maskingProductRef.ComponentProductId {
				if _, ok1 := fulfillmentProductIdMap[fulfillmentProductId]; ok1 {
					fulfillmentProductIdMap[fulfillmentProductId] = []int64{}
				}
				fulfillmentProductIdMap[fulfillmentProductId] = append(fulfillmentProductIdMap[fulfillmentProductId], int64(maskingProductRef.MaskingProductId))
			}
		}
	}
	//2. 检查product_id是否是masking_product_id，是则表明本次下单不是直选fulfillment product
	if _, ok := maskingProductIdMap[int(request.ProductId)]; ok {
		return []int64{request.ProductId}, false
	}
	//3. 检查product_id是否属于masking_product下的fulfillment_product
	if _, ok := fulfillmentProductIdMap[int(request.ProductId)]; ok {
		return fulfillmentProductIdMap[int(request.ProductId)], true
	}
	return nil, false
}

func (a *AllocationServiceImpl) CompletionCheckoutFulfillmentProductCounterRequest(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest, maskingProductIdList []int64) ([]*pb.AdjustLocVolumeReq, *srerr.Error) {
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.TotalCompleteParameterNumber)
	//1. 根据orderId获取order信息，包括cogs、paymentMethod、sellerAddress、buyerAddress、itemId、
	checkoutFProductOrderInfo, err := a.GetOrderInfoByOrderId(ctx, request.OrderId)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.GetOrderInfoError)
		logger.CtxLogErrorf(ctx, "get order info by order id error, err=%v", err)
		return nil, err
	}
	//2. 获取item的长宽高信息
	itemInfoList, err := a.GetItemInfo(ctx, checkoutFProductOrderInfo.OrderShopItemInfoList)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.GetItemInfoError)
		logger.CtxLogErrorf(ctx, "get item info error, err=%v", err)
		return nil, err
	}
	//3. 请求lcos获取校验后的parcel长宽高重量信息
	parcelDimension, err := a.GetParcelInfo(ctx, request, itemInfoList)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.GetParcelInfoError)
		logger.CtxLogErrorf(ctx, "get parcel info error, err=%v", err)
		return nil, err
	}
	//4. 获取location id
	pickupAddress, deliverAddress, err := a.GetLocationIds(ctx, checkoutFProductOrderInfo)
	if err != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.GetLocationIdsError)
		logger.CtxLogErrorf(ctx, "get location ids error, err=%v", err)
		return nil, err
	}

	dgType, err := a.ObtainDgType(ctx, request.FulfillmentProductId, itemInfoList, pickupAddress, deliverAddress, checkoutFProductOrderInfo.PackageAmount)
	if err != nil {
		return nil, err
	}

	//5. 构建更新运力的请求
	var updateVolumeReqList []*pb.AdjustLocVolumeReq
	for _, maskingProductId := range maskingProductIdList {
		updateVolumeReq := &pb.AdjustLocVolumeReq{
			MaskingProductId:    proto.Int64(maskingProductId),
			ProductId:           proto.Int64(request.ProductId),
			HardProductIds:      []int64{request.ProductId},
			PickupLocationIds:   objutil.IntToInt64Slice(pickupAddress.LocationIDs),
			DeliveryLocationIds: objutil.IntToInt64Slice(deliverAddress.LocationIDs),
			PickupPostcode:      pickupAddress.PostalCode,
			DeliveryPostcode:    deliverAddress.PostalCode,
			Cogs:                proto.Float64(checkoutFProductOrderInfo.Cogs),
			PaymentMethod:       proto.String(checkoutFProductOrderInfo.PaymentMethod),
			ParcelDimension:     parcelDimension,
			ShopId:              proto.Uint64(request.ShopId),
			DgType:              Uint64ToDgTypePtr(dgType),
		}
		updateVolumeReqList = append(updateVolumeReqList, updateVolumeReq)
	}
	prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.CompleteSuccess)

	return updateVolumeReqList, nil
}

func (a *AllocationServiceImpl) ObtainDgType(ctx context.Context, fulfillmentProductId int64, itemInfoList []*allocation.ItemInfo, pickupAddress *ordentity.AddressInfo, deliverAddress *ordentity.AddressInfo, packageAmount float64) (uint64, *srerr.Error) {
	lineListResult, lpsErr := a.LpsApi.GetLineDictList(ctx, fulfillmentProductId)
	if lpsErr != nil {
		logger.CtxLogErrorf(ctx, "get line dict list error, err=%v", lpsErr)
		return 0, lpsErr
	}
	lineInfo := make([]*llspb.CheckOrderLineInfo, 0, len(lineListResult.LineDictList))
	for _, linedict := range lineListResult.LineDictList {
		lineInfo = append(lineInfo, &llspb.CheckOrderLineInfo{
			LineId: proto.String(linedict.LineId),
			CheckOrderFunc: &llspb.CheckOrderFunc{
				CheckTp: proto.Bool(true),
			},
		})
	}

	var skuList []*llspb.SkuInfo
	for _, itemInfo := range itemInfoList {
		if itemInfo == nil {
			continue
		}
		var dgType uint64
		if itemInfo.GetIsDg() == 0 {
			dgType = 1
		} else {
			dgType = 2
		}
		skuList = append(skuList, &llspb.SkuInfo{
			ItemId:           proto.Uint64(itemInfo.ItemId),
			CategoryId:       proto.Uint32(uint32(itemInfo.GetCategoryId())),
			GlobalCategoryId: proto.Uint32(uint32(itemInfo.GetGlobalCategoryId())),
			ItemPrice:        proto.String(str.ToStr(itemInfo.GetPrice())),
			ModelId:          proto.Uint64(itemInfo.ModelId),
			Quantity:         proto.Uint32(uint32(itemInfo.Quantity)),
			DgType:           proto.Uint64(dgType),
			DgSpecificType:   proto.Uint32(itemInfo.DgSpecificType),
		})
	}

	//5. 获取dg flag
	checkOrderReq := &llspb.CheckOrderInfo{
		ReqNo:    proto.String(""),
		LineInfo: lineInfo,
		PickupInfo: &llspb.LocationInfoData{
			StateLocationId:    proto.Uint32(uint32(pickupAddress.GetStateLocationId())),
			CityLocationId:     proto.Uint32(uint32(pickupAddress.GetCityLocationId())),
			DistrictLocationId: proto.Uint32(uint32(pickupAddress.GetDistrictLocationId())),
			StreetLocationId:   proto.Uint32(uint32(pickupAddress.GetStreetLocationId())),
			Postcode:           proto.String(pickupAddress.GetPostalCode()),
		},
		DeliverInfo: &llspb.LocationInfoData{
			StateLocationId:    proto.Uint32(uint32(deliverAddress.GetStateLocationId())),
			CityLocationId:     proto.Uint32(uint32(deliverAddress.GetCityLocationId())),
			DistrictLocationId: proto.Uint32(uint32(deliverAddress.GetDistrictLocationId())),
			StreetLocationId:   proto.Uint32(uint32(deliverAddress.GetStreetLocationId())),
			Postcode:           proto.String(deliverAddress.GetPostalCode()),
		},
		PackageAmount: proto.String(str.ToStr(packageAmount)),
		Skus:          skuList,
	}
	checkOrderResp, llsErr := a.LlsApi.CheckOrder(ctx, checkOrderReq)
	if llsErr != nil {
		prometheusutil.ReportCheckoutFulfillmentProductEvent(prometheusutil.CompleteParameter, prometheusutil.GetDgError)
		return 0, llsErr
	}
	var dgType uint64
	for _, checkOrderReqItem := range checkOrderResp {
		for _, itemResult := range checkOrderReqItem.GetCheckResult() {
			if itemResult.GetExtraData().GetLineLocationDgFlagData().GetParcelDgFlag() > dgType {
				dgType = itemResult.GetExtraData().GetLineLocationDgFlagData().GetParcelDgFlag()
			}
		}
	}
	return dgType, nil
}

func Uint64ToDgTypePtr(source uint64) *pb.DgType {
	nilV := pb.DgType(0)
	if source == 0 {
		return &nilV
	}
	dest := pb.DgType(uint32(source))
	return &dest
}

func (a *AllocationServiceImpl) GetLocationIds(ctx context.Context, orderInfo *allocation.CheckoutFulfillmentProductOrderInfo) (*ordentity.AddressInfo, *ordentity.AddressInfo, *srerr.Error) {
	if orderInfo == nil {
		return nil, nil, nil
	}
	//1. 获取pickup地址的locationIds
	pickupAddressInfo := &ordentity.AddressInfo{
		State:      proto.String(orderInfo.SellerAddress.State),
		City:       proto.String(orderInfo.SellerAddress.City),
		District:   proto.String(orderInfo.SellerAddress.District),
		Street:     proto.String(orderInfo.SellerAddress.Town),
		PostalCode: proto.String(orderInfo.SellerAddress.Zipcode),
		Country:    proto.String(orderInfo.SellerAddress.Country),
		Address:    proto.String(orderInfo.SellerAddress.Address),
	}
	err := a.AddrRepo.FillLocationIds(ctx, pickupAddressInfo)
	if err != nil {
		logger.CtxLogErrorf(ctx, "FillLocationIds error|pickup address error, err=%v", err)
		return nil, nil, err
	}
	//2. 获取deliver address的locationIds
	deliverAddressInfo := &ordentity.AddressInfo{
		State:      proto.String(orderInfo.BuyerAddress.State),
		City:       proto.String(orderInfo.BuyerAddress.City),
		District:   proto.String(orderInfo.BuyerAddress.District),
		Street:     proto.String(orderInfo.BuyerAddress.Town),
		PostalCode: proto.String(orderInfo.BuyerAddress.Zipcode),
		Country:    proto.String(orderInfo.BuyerAddress.Country),
		Address:    proto.String(orderInfo.BuyerAddress.Address),
	}
	err = a.AddrRepo.FillLocationIds(ctx, deliverAddressInfo)
	if err != nil {
		logger.CtxLogErrorf(ctx, "FillLocationIds error|deliver address error, err=%v", err)
		return nil, nil, err
	}
	return pickupAddressInfo, deliverAddressInfo, nil
}

func (a *AllocationServiceImpl) GetOrderInfoByOrderId(ctx context.Context, orderId uint64) (*allocation.CheckoutFulfillmentProductOrderInfo, *srerr.Error) {
	//1. 构建请求参数
	var (
		orderRespType = uint32(1) // 1: 返回order item信息，2: 返回order信息
		orderExtInfo  = &orderPb.OrderExtInfo{}
	)
	orderInfoReq := &orderPb.GetOrderListByIdListRequest{
		OrderIds:      []uint64{orderId},
		QueryRespType: &orderRespType,
	}
	orderResp, err := a.SpexService.GetOrderList(ctx, orderInfoReq, envvar.GetCID())
	if err != nil {
		logger.CtxLogErrorf(ctx, "spex service|get order list from spex error, err=%v", err)
		return nil, err
	}
	// 该方法只查单个订单的信息，故只需要取返回结果列表的第一个结果
	if len(orderResp.OrderWithItems) == 0 {
		logger.CtxLogErrorf(ctx, "spex service|get order list is nil")
		return nil, srerr.New(srerr.SpexError, nil, "GetOrderList result is nil")
	}
	orderInfo := orderResp.OrderWithItems[0]
	if mErr := proto.Unmarshal(orderInfo.GetOrder().GetExtinfo(), orderExtInfo); mErr != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal orderExtInfo err=%v", mErr)
		return nil, srerr.With(srerr.JsonErr, nil, mErr)
	}
	// 构造返回结果
	checkoutFulfillmentProductOrderInfo := &allocation.CheckoutFulfillmentProductOrderInfo{
		Cogs:          float64(orderExtInfo.GetOrderPrice()) / constant.DB_PRICE_INFLATION_FACTOR,
		PackageAmount: float64(orderInfo.GetOrder().GetTotalPrice()),
	}
	// 确认判断支付方式是否是cod的逻辑是否正确
	if orderInfo.GetOrder().GetPaymentMethod() == int32(orderPb.Constant_PAYMENT_METHOD_PAY_COD) {
		checkoutFulfillmentProductOrderInfo.PaymentMethod = constant.PaymentMethodCod
	}
	// 解析shop item id
	var orderShopItemInfoList []allocation.OrderShopItemInfo
	for _, itemInfo := range orderInfo.GetOrderItems() {
		orderShopItemInfoList = append(orderShopItemInfoList, allocation.OrderShopItemInfo{
			ShopId:   itemInfo.GetShopid(),
			ItemId:   itemInfo.GetItemid(),
			ModelId:  itemInfo.GetModelid(),
			Quantity: itemInfo.GetAmount(),
		})
	}
	checkoutFulfillmentProductOrderInfo.OrderShopItemInfoList = orderShopItemInfoList
	// 解析买家和卖家地址
	if orderExtInfo.GetBuyerAddress() != nil {
		buyerAddress := allocation.AddressInfo{
			Country:  orderExtInfo.GetBuyerAddress().GetCountry(),
			State:    orderExtInfo.GetBuyerAddress().GetState(),
			City:     orderExtInfo.GetBuyerAddress().GetCity(),
			District: orderExtInfo.GetBuyerAddress().GetDistrict(),
			Town:     orderExtInfo.GetBuyerAddress().GetTown(),
			Address:  orderExtInfo.GetBuyerAddress().GetAddress(),
			Zipcode:  orderExtInfo.GetBuyerAddress().GetZipcode(),
		}
		checkoutFulfillmentProductOrderInfo.BuyerAddress = buyerAddress
	}
	if orderExtInfo.GetSellerAddress() != nil {
		sellerAddress := allocation.AddressInfo{
			Country:  orderExtInfo.GetSellerAddress().GetCountry(),
			State:    orderExtInfo.GetSellerAddress().GetState(),
			City:     orderExtInfo.GetSellerAddress().GetCity(),
			District: orderExtInfo.GetSellerAddress().GetDistrict(),
			Town:     orderExtInfo.GetSellerAddress().GetTown(),
			Address:  orderExtInfo.GetSellerAddress().GetAddress(),
			Zipcode:  orderExtInfo.GetSellerAddress().GetZipcode(),
		}
		checkoutFulfillmentProductOrderInfo.SellerAddress = sellerAddress
	}
	fulfilmentSourceSnapshot := orderExtInfo.GetLogisticsInfo().GetFulfilmentSourceSnapshot()
	if fulfilmentSourceSnapshot == "" || fulfilmentSourceSnapshot[len(fulfilmentSourceSnapshot)-1] == 'Z' {
		checkoutFulfillmentProductOrderInfo.OrderType = pb.OrderType_MplOrderType
	} else {
		checkoutFulfillmentProductOrderInfo.OrderType = pb.OrderType_WmsOrderType
	}
	return checkoutFulfillmentProductOrderInfo, nil
}

func (a *AllocationServiceImpl) GetItemInfo(ctx context.Context, shopItemIdList []allocation.OrderShopItemInfo) ([]*allocation.ItemInfo, *srerr.Error) {
	//1. 构建请求参数
	newReq := &itemPb.GetProductInfoRequest{}
	sid := make([]*itemPb.ShopItemId, 0)
	for _, shopItemId := range shopItemIdList {
		temp := itemPb.ShopItemId{
			ShopId: proto.Uint32(uint32(shopItemId.ShopId)),
			ItemId: proto.Uint64(uint64(shopItemId.ItemId)),
		}
		sid = append(sid, &temp)
	}
	newReq.NeedDeleted = proto.Bool(true)
	newReq.ShopItemIds = sid
	newReq.InfoTypes = []uint32{
		uint32(itemPb.Constant_ITEM_BASIC),
		uint32(itemPb.Constant_MODEL_BASIC),
		uint32(itemPb.Constant_CATEGORY),
		uint32(itemPb.Constant_PRICE),
		uint32(itemPb.Constant_LOGISTICS),
		uint32(itemPb.Constant_ATTRIBUTE),
	}
	//2. 请求spex
	resp, err := a.SpexService.GetItemProductInfo(ctx, newReq, envvar.GetCID())
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, srerr.New(srerr.SpexError, nil, "request spex service error|get item product info fail")
	}
	//3. 组装返回结果
	productInfoMap := make(map[string]*itemPb.ProductInfo)
	for _, productInfo := range resp.GetInfo() {
		itemProductInfoKey := fmt.Sprintf(constant.ItemProductInfoPattern, productInfo.GetShopId(), productInfo.GetItemId())
		productInfoMap[itemProductInfoKey] = productInfo
	}
	var itemInfoList []*allocation.ItemInfo
	for _, shopItemId := range shopItemIdList {
		itemProductInfoKey := fmt.Sprintf(constant.ItemProductInfoPattern, shopItemId.ShopId, shopItemId.ItemId)
		var productInfo *itemPb.ProductInfo
		if tempProductInfo, ok := productInfoMap[itemProductInfoKey]; ok {
			productInfo = tempProductInfo
		}
		if productInfo == nil {
			logger.CtxLogInfof(ctx, "item product info not exists|shopId: %d, itemId: %d", shopItemId.ShopId, shopItemId.ItemId)
			return nil, srerr.New(srerr.ItemNotFound, nil, "item product info not exists|shopId: %d, itemId: %d", shopItemId.ShopId, shopItemId.ItemId)
		}
		itemInfo := &allocation.ItemInfo{
			ItemId:         uint64(shopItemId.ItemId),
			ModelId:        uint64(shopItemId.ModelId),
			Quantity:       int(shopItemId.Quantity),
			DgSpecificType: uint32(productInfo.GetLogistics().GetDangerousGoodsCategorization()),
		}
		aErr := spex_service.AttachItemInfoV2(ctx, itemInfo, productInfo)
		if aErr != nil {
			logger.CtxLogErrorf(ctx, "attach item into error, err=%v", aErr)
			return nil, aErr
		}
		itemInfoList = append(itemInfoList, itemInfo)
	}
	return itemInfoList, nil
}

// todo 是否需要考虑子单
func (a *AllocationServiceImpl) GetParcelInfo(ctx context.Context, request *allocation.CheckoutFulfillmentProductCounterRequest, itemInfoList []*allocation.ItemInfo) (*pb.ParcelDimension, *srerr.Error) {
	//1. 组装请求参数
	var skuInfoList []*lcospb.SkuInfo
	for _, itemInfo := range itemInfoList {
		skuInfo := &lcospb.SkuInfo{
			ItemId:     proto.Uint64(itemInfo.ItemId),
			ModelId:    proto.Uint64(itemInfo.ModelId),
			Quantity:   proto.Uint32(uint32(itemInfo.Quantity)),
			Length:     itemInfo.Length,
			Width:      itemInfo.Width,
			Height:     itemInfo.Height,
			CategoryId: itemInfo.CategoryId,
			Weight:     proto.Float64(itemInfo.GetWeight() * 1000), // 重量是否需要乘以1000
		}
		skuInfoList = append(skuInfoList, skuInfo)
	}
	productRule := &lcospb.SingleProductRule{
		UniqueId:       proto.String(constant.GetParcelInfoUniqueId),
		ProductId:      []string{strconv.FormatInt(request.ProductId, 10)},
		SkuInfo:        skuInfoList,
		OrderInfo:      nil,
		SubPackageInfo: nil,
	}
	var productRuleList []*lcospb.SingleProductRule
	productRuleList = append(productRuleList, productRule)
	//2. 请求lcos获取pracel结果
	respMap, err := a.LcosApi.BatchCheckProductRule(ctx, productRuleList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchCheckProductRule error|request lcos error, err=%v", err)
		return nil, err
	}
	var resp *lcospb.CheckProductRuleResponse
	if tempResp, ok := respMap[constant.GetParcelInfoUniqueId]; ok {
		resp = tempResp
	} else {
		logger.CtxLogErrorf(ctx, "BatchCheckProductRule fail|check result is nil")
	}
	//3. 组装返回结果
	parcelDimension := &pb.ParcelDimension{}
	for _, ruleLimitInfo := range resp.GetRuleLimitInfo() {
		if ruleLimitInfo == nil || ruleLimitInfo.GetProductId() == "" {
			continue
		}
		for _, limitDetail := range ruleLimitInfo.GetLimitDetail() {
			switch limitDetail.GetRuleType() {
			case lcosclient.MaxActualVolumetricWeightRule:
				parcelDimension.Weight = limitDetail.CalculateResult
			case lcosclient.MaxLengthRule:
				parcelDimension.Length = limitDetail.CalculateResult
			case lcosclient.MaxWidthRule:
				parcelDimension.Width = limitDetail.CalculateResult
			case lcosclient.SumHeightRule:
				parcelDimension.Height = limitDetail.CalculateResult
			}
		}
	}
	return parcelDimension, nil
}

const (
	Available = iota + 1
	UnAvailable
	NotExisted
)

type AllocateCounterParam struct {
	OrderID             uint64
	MaskingProductId    int64
	ProductId           int64
	PickupLocationIds   []int64
	DeliveryLocationIds []int64
	SloCreateTime       int64
	UpdateType          pb.UpdateVolumeType
	OrderType           pb.OrderType
	PickupPostcode      string
	DeliveryPostcode    string
	VolumeScene         string
	ShopId              int64
}

type InstallAllocateItemInfo struct {
	ItemId       uint64
	ItemUniqueId string
}
