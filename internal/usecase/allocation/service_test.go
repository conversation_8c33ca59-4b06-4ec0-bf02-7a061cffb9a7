package allocation

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"reflect"
	"strings"
	"testing"
	"time"
)

func TestAllocationServiceImpl_FilterBuyerRejectedFulfillmentProduct(t *testing.T) {
	ctx := context.TODO()
	type args struct {
		ctx context.Context
		req *smart_routing_protobuf.MaskingReq
	}

	scenario := smart_routing_protobuf.AllocationScenario_Reallocation

	tests := []struct {
		name    string
		args    args
		want    []int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: ctx,
				req: &smart_routing_protobuf.MaskingReq{
					Header: &smart_routing_protobuf.ReqHeader{
						RequestId: proto.String("FilterBuyerRejectedFulfillmentProduct-test"),
					},
					ReselectPreviousProductId: proto.Int64(8001),
					ProductIds:                []int64{8001, 8003},
				},
			},
			want: []int64{8001, 8003},
		},
		{

			args: args{
				ctx: ctx,
				req: &smart_routing_protobuf.MaskingReq{
					Header: &smart_routing_protobuf.ReqHeader{
						RequestId: proto.String("FilterBuyerRejectedFulfillmentProduct-test"),
					},
					ReselectPreviousProductId: proto.Int64(8001),
					ProductIds:                []int64{8001, 8003},
					AllocateScenario:          &scenario,
				},
			},
			want: []int64{8003},
		},
		{

			args: args{
				ctx: ctx,
				req: &smart_routing_protobuf.MaskingReq{
					Header: &smart_routing_protobuf.ReqHeader{
						RequestId: proto.String("FilterBuyerRejectedFulfillmentProduct-test"),
					},
					ReselectPreviousProductId: proto.Int64(8001),
					ProductIds:                []int64{8001},
					AllocateScenario:          &scenario,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FilterBuyerRejectedFulfillmentProduct(tt.args.ctx, tt.args.req)
			if err != nil && !tt.wantErr {
				t.Errorf("FilterBuyerRejectedFulfillmentProduct() error = %v", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FilterBuyerRejectedFulfillmentProduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAllocationServiceImpl_BatchGetAllocateOrderResult(t *testing.T) {
	// 由于借助chassis，所以需要在启动环境中手动设置参数，参数如下：
	// CHASSIS_HOME=/Users/<USER>/go/src/logistics-smart-routing;CID=id;ENV=test;GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn;SP_UNIT_SOCKET=${HOME}/run/spex/spex.sock
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	ctx := context.TODO()

	type fields struct {
		UnimplementedMaskingServer smart_routing_protobuf.UnimplementedMaskingServer
		configRepo                 config.IMaskConfigRepo
		ruleRepo                   rule.IMaskRuleRepo
		softRuleService            *SoftRuleService
		maskRuleVolumeSrv          rulevolume2.MaskRuleVolumeService
		outerCheck                 outercheck.AllOuterCheckService
		maskVolumeCounter          volumecounter.MaskVolumeCounter
		volumeCounter              volume_counter.VolumeCounter
		scheduleCountStat          schedule_visual.ScheduleCountStatInterface
		maskRuleVolume             rulevolume.IMaskRuleVolumeRepo
		BatchAllocateOrderRepo     order.BatchAllocateOrderRepo
		GreyService                service.GreyService
	}
	type args struct {
		ctx context.Context
		req *smart_routing_protobuf.BatchGetAllocateOrderResultReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *smart_routing_protobuf.BatchGetAllocateOrderResultResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx: ctx,
				req: &smart_routing_protobuf.BatchGetAllocateOrderResultReq{
					Header: &smart_routing_protobuf.ReqHeader{
						RequestId: proto.String("qxl-test"),
					},
					OrderIdList: []uint64{***************, ***************, ***************, ***************},
				},
			},
			fields: fields{
				BatchAllocateOrderRepo: &order.BatchAllocateOrderRepoImpl{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &AllocationServiceImpl{
				UnimplementedMaskingServer: tt.fields.UnimplementedMaskingServer,
				configRepo:                 tt.fields.configRepo,
				RuleRepo:                   tt.fields.ruleRepo,
				softRuleService:            tt.fields.softRuleService,
				maskRuleVolumeSrv:          tt.fields.maskRuleVolumeSrv,
				outerCheck:                 tt.fields.outerCheck,
				maskVolumeCounter:          tt.fields.maskVolumeCounter,
				VolumeCounter:              tt.fields.volumeCounter,
				scheduleCountStat:          tt.fields.scheduleCountStat,
				maskRuleVolume:             tt.fields.maskRuleVolume,
				BatchAllocateOrderRepo:     tt.fields.BatchAllocateOrderRepo,
				GreyService:                tt.fields.GreyService,
			}
			got, err := a.BatchGetAllocateOrderResult(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetAllocateOrderResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetAllocateOrderResult() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAllocationServiceImpl_GetOrderInfoByOrderId(t *testing.T) {
	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("task_server"),
		chassis.WithGRPCUnaryServerInterceptor(), // grpc拦截器
		chassis.WithDefaultProviderHandlerChain(),
		chassis.WithDefaultConsumerHandlerChain(),
	); err != nil {
		log.Fatalf("Init failed with err: %s\n", err.Error())
		return
	}
	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
	}
	if err := spex_service.InitSpex(); err != nil {
		log.Fatalf("init spex service failed %+v", err)
	}
	allocationServiceImpl := &AllocationServiceImpl{
		SpexService: spex_service.NewSpexServiceImpl(),
	}
	result, err := allocationServiceImpl.GetOrderInfoByOrderId(context.Background(), 186316679201262)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(result)
}

//
//func createBatch() {
//
//	tempOrder := order.OrderResultTab{
//		OrderStatus:    3,
//		MaskProductID:  8003,
//		AllocateResult: 80088,
//	}
//	start := 182466038219794
//	totalOrders := make([][]order.OrderResultTab, 5)
//	orders := make([]order.OrderResultTab, 0)
//	for i := 0; i < 10000; i++ {
//		orderID := start + i
//		tempOrder.OrderID = uint64(orderID)
//		orders = append(orders, tempOrder)
//	}
//	totalOrders[0] = orders
//
//	orders1 := make([]order.OrderResultTab, 0)
//	for i := 10000; i < 20000; i++ {
//		orderID := start + i
//		tempOrder.OrderID = uint64(orderID)
//		orders1 = append(orders1, tempOrder)
//	}
//	totalOrders[1] = orders1
//
//	orders2 := make([]order.OrderResultTab, 0)
//	for i := 20000; i < 30000; i++ {
//		orderID := start + i
//		tempOrder.OrderID = uint64(orderID)
//		orders2 = append(orders2, tempOrder)
//	}
//	totalOrders[2] = orders2
//
//	orders3 := make([]order.OrderResultTab, 0)
//	for i := 30000; i < 40000; i++ {
//		orderID := start + i
//		tempOrder.OrderID = uint64(orderID)
//		orders3 = append(orders3, tempOrder)
//	}
//	totalOrders[3] = orders3
//
//	orders4 := make([]order.OrderResultTab, 0)
//	for i := 40000; i < 50000; i++ {
//		orderID := start + i
//		tempOrder.OrderID = uint64(orderID)
//		orders4 = append(orders4, tempOrder)
//	}
//	totalOrders[4] = orders4
//
//	pool, _ := ants.NewPool(5)
//	wg := sync.WaitGroup{}
//	wg.Add(5)
//	for i := range totalOrders {
//		idx := i
//		pool.Submit(func() {
//			orderList := totalOrders[idx]
//			err := dbutil.InsertBatch(ctx, &order.OrderResultTab{}, orderList)
//			if err != nil {
//				println("haha")
//			}
//			wg.Done()
//		})
//	}
//	wg.Wait()
//
//}

func Test_removeReselectPreviousProduct(t *testing.T) {
	ctx := context.TODO()
	type args struct {
		reselectPreviousProductId int64
		products                  []int64
	}
	tests := []struct {
		name string
		args args
		want []int64
	}{
		// TODO: Add test cases.
		{
			name: "case 1: no reselect previous product in products",
			args: args{
				reselectPreviousProductId: 0,
				products:                  []int64{1, 2, 3},
			},
			want: []int64{1, 2, 3},
		},
		{
			name: "case 2: remove reselect previous product in products",
			args: args{
				reselectPreviousProductId: 1,
				products:                  []int64{1, 2, 3},
			},
			want: []int64{2, 3},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, removeReselectPreviousProduct(ctx, tt.args.reselectPreviousProductId, tt.args.products), "removeReselectPreviousProduct(%v, %v, %v)", ctx, tt.args.reselectPreviousProductId, tt.args.products)
		})
	}
}

func Test_convertInstallAllocateByType(t *testing.T) {
	ctx := context.TODO()

	// 定义测试用例
	tests := []struct {
		name                   string
		installAllocateReqMap  map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq
		installAllocateRespMap map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate
		installProductTypeMap  map[int64]smart_routing_protobuf.InstallProductType
		want                   []*smart_routing_protobuf.InstallAllocate
		wantErr                bool
	}{
		{
			name:                   "case 1: empty response map",
			installAllocateReqMap:  make(map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq),
			installAllocateRespMap: make(map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate),
			installProductTypeMap:  make(map[int64]smart_routing_protobuf.InstallProductType),
			want:                   []*smart_routing_protobuf.InstallAllocate{},
			wantErr:                false,
		},
		{
			name: "case 2: single ISP type product",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{101},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(101),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				101: smart_routing_protobuf.InstallProductType_ISPInstallation,
			},
			want: []*smart_routing_protobuf.InstallAllocate{
				{
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(101),
				},
			},
			wantErr: false,
		},
		{
			name: "case 3: multiple LM type products with intersection",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{201, 202, 203},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{202, 203, 204},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(201),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(204),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				201: smart_routing_protobuf.InstallProductType_LMInstallation,
				202: smart_routing_protobuf.InstallProductType_LMInstallation,
				203: smart_routing_protobuf.InstallProductType_LMInstallation,
				204: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want: []*smart_routing_protobuf.InstallAllocate{
				{
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(202), // 使用交集中的第一个产品
				},
				{
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(202), // 使用交集中的第一个产品
				},
			},
			wantErr: false,
		},
		{
			name: "case 4: mixed ISP and LM type products",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{301, 302},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{303, 304},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(301),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(303),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				301: smart_routing_protobuf.InstallProductType_ISPInstallation,
				302: smart_routing_protobuf.InstallProductType_LMInstallation,
				303: smart_routing_protobuf.InstallProductType_LMInstallation,
				304: smart_routing_protobuf.InstallProductType_ISPInstallation,
			},
			want: []*smart_routing_protobuf.InstallAllocate{
				{
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(301), // ISP类型保持不变
				},
				{
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(304), // 没有交集，使用ISP类型
				},
			},
			wantErr: false,
		},
		{
			name: "case 5: product type not found error",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{401},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{401},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(401),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(401),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{},
			want:                  nil,
			wantErr:               true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用gomonkey模拟getInterSectionProduct函数
			var patch *gomonkey.Patches
			defer func() {
				if patch != nil {
					patch.Reset()
				}
			}()

			// 根据测试用例模拟不同的交集结果
			if tt.name == "case 3: multiple LM type products with intersection" {
				interSectionMap := map[int64]struct{}{
					202: {},
					203: {},
				}
				patch = gomonkey.ApplyFunc(getInterSectionProduct, func(ctx context.Context, reqMap map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq, respMap map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate, typeMap map[int64]smart_routing_protobuf.InstallProductType) (map[int64]struct{}, *srerr.Error) {
					return interSectionMap, nil
				})
			} else if tt.name == "case 4: mixed ISP and LM type products" {
				// 没有交集的情况
				patch = gomonkey.ApplyFunc(getInterSectionProduct, func(ctx context.Context, reqMap map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq, respMap map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate, typeMap map[int64]smart_routing_protobuf.InstallProductType) (map[int64]struct{}, *srerr.Error) {
					return map[int64]struct{}{}, nil
				})

				// 模拟findISPProduct函数，为item2返回ISP类型产品
				patch = gomonkey.ApplyFunc(findISPProduct, func(productIDs []int64, typeMap map[int64]smart_routing_protobuf.InstallProductType) (int64, *srerr.Error) {
					if len(productIDs) > 0 && productIDs[0] == 303 {
						return 304, nil // 为item2返回ISP类型产品
					}
					return 0, nil
				})
			}

			got, err := convertInstallAllocateByType(ctx, tt.installAllocateReqMap, tt.installAllocateRespMap, tt.installProductTypeMap)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertInstallAllocateByType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 比较结果，忽略顺序
			if !tt.wantErr {
				assert.ElementsMatch(t, tt.want, got, "convertInstallAllocateByType() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getInterSectionProduct(t *testing.T) {
	ctx := context.TODO()

	// 定义测试用例
	tests := []struct {
		name                   string
		installAllocateReqMap  map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq
		installAllocateRespMap map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate
		installProductTypeMap  map[int64]smart_routing_protobuf.InstallProductType
		want                   map[int64]struct{}
		wantErr                bool
	}{
		{
			name:                   "case 1: empty response map",
			installAllocateReqMap:  make(map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq),
			installAllocateRespMap: make(map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate),
			installProductTypeMap:  make(map[int64]smart_routing_protobuf.InstallProductType),
			want:                   map[int64]struct{}{},
			wantErr:                false,
		},
		{
			name: "case 2: only ISP type products",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{101},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(101),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				101: smart_routing_protobuf.InstallProductType_ISPInstallation,
			},
			want:    map[int64]struct{}{},
			wantErr: false,
		},
		{
			name: "case 3: single LM type product",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{201, 202},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(201),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				201: smart_routing_protobuf.InstallProductType_LMInstallation,
				202: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want: map[int64]struct{}{
				201: {},
			},
			wantErr: false,
		},
		{
			name: "case 4: multiple LM type products with intersection",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{201, 202, 203},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{202, 203, 204},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(201),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(204),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				201: smart_routing_protobuf.InstallProductType_LMInstallation,
				202: smart_routing_protobuf.InstallProductType_LMInstallation,
				203: smart_routing_protobuf.InstallProductType_LMInstallation,
				204: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want: map[int64]struct{}{
				202: {},
				203: {},
			},
			wantErr: false,
		},
		{
			name: "case 5: multiple LM type products with no intersection",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{201, 202},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{203, 204},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(201),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(203),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				201: smart_routing_protobuf.InstallProductType_LMInstallation,
				202: smart_routing_protobuf.InstallProductType_LMInstallation,
				203: smart_routing_protobuf.InstallProductType_LMInstallation,
				204: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want:    map[int64]struct{}{},
			wantErr: false,
		},
		{
			name: "case 6: mixed ISP and LM type products",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{301, 302},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{302, 303, 304},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(301),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(302),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				301: smart_routing_protobuf.InstallProductType_LMInstallation,
				302: smart_routing_protobuf.InstallProductType_LMInstallation,
				303: smart_routing_protobuf.InstallProductType_ISPInstallation,
				304: smart_routing_protobuf.InstallProductType_ISPInstallation,
			},
			want: map[int64]struct{}{
				302: {},
			},
			wantErr: false,
		},
		{
			name: "case 7: product type not found error",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{401},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(401),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{},
			want:                  nil,
			wantErr:               true,
		},
		{
			name:                  "case 8: request not found error",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(501),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				501: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "case 9: multiple LM type allocate result equal",
			installAllocateReqMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.MaskingReq{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ProductIds: []int64{201, 202, 203},
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ProductIds: []int64{202, 203, 204},
				},
			},
			installAllocateRespMap: map[InstallAllocateItemInfo]*smart_routing_protobuf.InstallAllocate{
				{ItemId: 1, ItemUniqueId: "item1"}: {
					ItemId:       proto.Uint64(1),
					ItemUniqueId: proto.String("item1"),
					ProductId:    proto.Int64(202),
				},
				{ItemId: 2, ItemUniqueId: "item2"}: {
					ItemId:       proto.Uint64(2),
					ItemUniqueId: proto.String("item2"),
					ProductId:    proto.Int64(202),
				},
			},
			installProductTypeMap: map[int64]smart_routing_protobuf.InstallProductType{
				201: smart_routing_protobuf.InstallProductType_LMInstallation,
				202: smart_routing_protobuf.InstallProductType_LMInstallation,
				203: smart_routing_protobuf.InstallProductType_LMInstallation,
				204: smart_routing_protobuf.InstallProductType_LMInstallation,
			},
			want: map[int64]struct{}{
				202: {},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getInterSectionProduct(ctx, tt.installAllocateReqMap, tt.installAllocateRespMap, tt.installProductTypeMap)
			if (err != nil) != tt.wantErr {
				t.Errorf("getInterSectionProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 比较结果
			if !tt.wantErr {
				// 检查结果集大小
				if len(got) != len(tt.want) {
					t.Errorf("getInterSectionProduct() got size = %v, want size %v", len(got), len(tt.want))
					return
				}

				// 检查每个元素
				for k := range tt.want {
					if _, exists := got[k]; !exists {
						t.Errorf("getInterSectionProduct() missing key %v in result", k)
					}
				}
			}
		})
	}
}

type mockVolumeCounter struct {
	getUpdateShopGroupVolumeFunc func(ctx context.Context, maskProductID, productID int64, groupCode string, shopGroupId int64, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error
}

func (m mockVolumeCounter) UpdateShopGroupVolume(ctx context.Context, maskProductID, productID int64, groupCode string, shopGroupId int64, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	if m.getUpdateShopGroupVolumeFunc != nil {
		return m.getUpdateShopGroupVolumeFunc(ctx, maskProductID, productID, groupCode, shopGroupId, sloCreateTime, updateType, rm)
	} else {
		return nil
	}
}

func (m mockVolumeCounter) GetProductVolume(ctx context.Context, productID int) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetLaneVolume(ctx context.Context, productID int, laneCode string) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetLaneVolume(ctx context.Context, productID int, laneCodeList []string) ([]int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetLineVolume(ctx context.Context, productID int, lineID, parcelType string) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetLineVolume(ctx context.Context, productID int, lineIDList []string) ([]int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode, parcelType string) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrLineVolume(ctx context.Context, productID int, lineID string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsDpCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsDpParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHTwsDpWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationDpCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationDpParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHCombinationDpWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) IncrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DecrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetILHProductCounter(ctx context.Context, productID int, days []time.Time) (map[volume_counter.ILHProductCounter]*volume_counter.ILHProductCounterResult, int, int, int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetMultiProductServiceCodeVolumes(ctx context.Context, productID int, days []time.Time) (map[string]int, int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetProductLaneVolumes(ctx context.Context, productID int, days []time.Time) (map[volume_counter.LaneCounter]int, int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, lineParcelTypeAttr *parcel_type_definition.ParcelTypeAttr, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateProductVolume(ctx context.Context, productId int64, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateLineVolume(ctx context.Context, productId int64, lineId string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateLineZoneVolume(ctx context.Context, productId int64, lineId string, zoneCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateLaneFieldVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateLaneFieldZoneVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, zoneCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateLaneVolume(ctx context.Context, productId int64, laneCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateMultiLaneFieldVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateMultiLaneFieldZoneVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode, zoneCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateMultiProductServiceCodeVolume(ctx context.Context, productId int64, serviceCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateActualPointVolume(ctx context.Context, productId int64, laneCode string, lineIdList []string, actualPointInfo []*smart_routing_protobuf.VolumeActualPoint, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetActualPointVolume(ctx context.Context, productId int64, laneCode string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetActualPointVolume(ctx context.Context, actualPoints []volume_counter.ReportActualPointInfo) ([]int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetLineActualPointVolume(ctx context.Context, productId int64, laneCode string, lineId string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetLineActualPointVolume(ctx context.Context, lineActualPointList []volume_counter.ReportLineActualPointInfo) ([]int, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetDashboardVolume(ctx context.Context, keyList []volume_counter.RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetZoneDashboardVolume(ctx context.Context, keyList []volume_counter.RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateMaskVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DeductProductVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateRouteVolume(ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DeductRouteVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, routeCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateZoneVolume(ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string, direction rulevolume2.MaskZoneDirection, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) DeductZoneVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, zoneCode string, direction rulevolume2.MaskZoneDirection, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) UpdateVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) *srerr.Error {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetMaskProductVolume(ctx context.Context, maskingProductId int64, timestamp int64, rm rule_mode.RuleMode) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetFulfillmentProductVolume(ctx context.Context, maskingProductId, productId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetGroupCodeVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetMaskZoneVolume(ctx context.Context, maskProductId, productId int64, zoneCode string, direction rulevolume2.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetGroupZoneVolume(ctx context.Context, groupCode string, zoneCode string, direction rulevolume2.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetMaskRouteVolume(ctx context.Context, maskProductId, productId int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetGroupRouteVolume(ctx context.Context, groupCode string, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetMaskShopGroupVolume(ctx context.Context, maskProductId, productId int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) GetGroupShopGroupVolume(ctx context.Context, groupCode string, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetMaskZoneVolume(ctx context.Context, productId int64, zoneCodeList []string, direction rulevolume2.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetMaskRouteVolume(ctx context.Context, productId int64, routeCodeList []string, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func (m mockVolumeCounter) BatchGetMaskShopGroupVolume(ctx context.Context, productId int64, shopGroupIdList []int64, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	//TODO implement me
	panic("implement me")
}

func Test_updateDashboardCounter(t *testing.T) {
	req1 := &smart_routing_protobuf.MaskingReq{
		MaskingProductId: proto.Int64(7000),
		ShopGroupIds:     []int64{741074},
		RequestTimeStamp: proto.Int64(0),
	}
	ctx := context.TODO()
	tests := []struct {
		name    string
		req     *smart_routing_protobuf.MaskingReq
		rsp     *smart_routing_protobuf.ASyncAllocateResp
		wantErr bool
	}{
		{
			name:    "single allocate update redis counter fail",
			req:     req1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockvolumncnter := &mockVolumeCounter{
				getUpdateShopGroupVolumeFunc: func(ctx context.Context, maskProductID, productID int64, groupCode string, shopGroupId int64, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
					return &srerr.Error{}
				},
			}
			allocationimpl := &AllocationServiceImpl{
				VolumeCounter: mockvolumncnter,
			}
			allocationimpl.updateDashboardCounter(ctx, req1, 70027, "")
		})
	}

}
