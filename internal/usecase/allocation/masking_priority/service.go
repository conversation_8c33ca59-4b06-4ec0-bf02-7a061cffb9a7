package masking_priority

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type PickupPriorityService interface {
	List(ctx context.Context, req *allocation.ListPriorityReq) (*allocation.ListPriorityResp, *srerr.Error)
	Create(ctx context.Context, req *allocation.CreatePriorityReq) *srerr.Error
	Delete(ctx context.Context, req *allocation.DeletePriorityReq) *srerr.Error
	Update(ctx context.Context, req *allocation.CreatePriorityReq) *srerr.Error
	Detail(ctx context.Context, req *allocation.GetPriorityReq) (*allocation.Item, *srerr.Error)
}

type PickupPriorityServiceImpl struct {
	PickupPriorityRepo pickup_priority.PickupPriorityRepo
	AllocationRuleRepo rule.AllocationRuleRepo
}

func NewPickupPriorityServiceImpl(PickupPriorityRepo pickup_priority.PickupPriorityRepo,
	AllocationRuleRepo rule.AllocationRuleRepo) *PickupPriorityServiceImpl {
	return &PickupPriorityServiceImpl{
		PickupPriorityRepo: PickupPriorityRepo,
		AllocationRuleRepo: AllocationRuleRepo,
	}
}

func (i *PickupPriorityServiceImpl) List(ctx context.Context, req *allocation.ListPriorityReq) (*allocation.ListPriorityResp, *srerr.Error) {
	if req.Page < defaultPage {
		req.Page = defaultPage
	}
	if req.Size <= 0 {
		req.Size = defaultSize
	}
	offset := (req.Page - 1) * req.Size
	size := req.Size

	condition := make(map[string]interface{}, 0)
	if req.MaskingProductId != 0 {
		condition["masking_product_id = ?"] = req.MaskingProductId
	}

	tabs, total, lErr := i.PickupPriorityRepo.List(ctx, condition, offset, size)
	if lErr != nil {
		return nil, lErr
	}
	resp := &allocation.ListPriorityResp{
		Page:  req.Page,
		Size:  req.Size,
		Total: total,
	}

	list := make([]allocation.Item, 0)
	productNameMap := localcache.AllItems(ctx, constant.ProductNameDict)
	for _, tempTab := range tabs {
		tab := tempTab
		tab.Unmarshal()

		priorityItem := allocation.Item{
			MaskingProductId: tab.MaskingProductId,
			ModifyTime:       tab.Mtime,
			LastUpdatedBy:    tab.Operator,
		}
		if value, ok := productNameMap[strconv.FormatInt(tab.MaskingProductId, 10)]; ok {
			maskProductName, _ := value.(string)
			priorityItem.MaskingProductName = maskProductName
		}
		priorityItem.PriorityList = priorityConvertDbToAdmin(ctx, tab)
		list = append(list, priorityItem)
	}
	resp.List = list

	return resp, nil
}

func (i *PickupPriorityServiceImpl) Create(ctx context.Context, req *allocation.CreatePriorityReq) *srerr.Error {
	// 1. 转换tab
	tab := convertToTab(ctx, req)
	// 2. insert tab
	if err := i.PickupPriorityRepo.Create(ctx, tab); err != nil {
		return err
	}
	return nil
}

func (i *PickupPriorityServiceImpl) Update(ctx context.Context, req *allocation.CreatePriorityReq) *srerr.Error {
	// 1. 转换tab
	tab := convertToTab(ctx, req)
	// 2. insert tab
	if err := i.PickupPriorityRepo.Update(ctx, map[string]interface{}{"masking_product_id = ?": req.MaskingProductId}, tab); err != nil {
		return err
	}
	return nil
}

func (i *PickupPriorityServiceImpl) Delete(ctx context.Context, req *allocation.DeletePriorityReq) *srerr.Error {
	// 1. 根据mask product id，获取masking soft rule, 判断是否有规则开启了新调度因子
	ruleList, err := i.AllocationRuleRepo.GetRuleListByCondition(ctx, map[string]interface{}{
		"rule_status = ?":       rule.MaskRuleStatusActive,
		"mask_product_id = ?":   req.MaskingProductId,
		"allocation_method = ?": allocation2.SingleAllocate,
	})
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	// 判断是否开启了Pickup Efficiency 调度因子，若开启则报错并return
	for _, maskRule := range ruleList {
		detail := &rule.MaskRuleDetail{}
		if err := jsoniter.Unmarshal(maskRule.Rule, &detail); err != nil {
			return srerr.With(srerr.DataErr, maskRule.Rule, err)
		}
		if detail.PickupEfficiencyWhitelistEnable {
			msg := fmt.Sprintf("rule id:%d, you can't delete priority since the rule has toggle on the factor", maskRule.Id)
			logger.CtxLogErrorf(ctx, msg)
			return srerr.New(srerr.ParamErr, nil, msg)
		}
	}

	if err := i.PickupPriorityRepo.Delete(ctx, req.MaskingProductId); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (i *PickupPriorityServiceImpl) Detail(ctx context.Context, req *allocation.GetPriorityReq) (*allocation.Item, *srerr.Error) {
	condition := map[string]interface{}{
		"masking_product_id = ?": req.MaskingProductId,
	}
	tab, err := i.PickupPriorityRepo.Get(ctx, condition)
	if err != nil {
		return nil, err
	}
	tab.Unmarshal()

	productNameMap := localcache.AllItems(ctx, constant.ProductNameDict)
	priorityItem := &allocation.Item{
		MaskingProductId: tab.MaskingProductId,
		ModifyTime:       tab.Mtime,
		LastUpdatedBy:    tab.Operator,
	}
	if value, ok := productNameMap[strconv.FormatInt(tab.MaskingProductId, 10)]; ok {
		maskProductName, _ := value.(string)
		priorityItem.MaskingProductName = maskProductName
	}
	priorityItem.PriorityList = priorityConvertDbToAdmin(ctx, tab)

	return priorityItem, nil
}

func priorityConvertDbToAdmin(ctx context.Context, tab *pickup_priority.PickupPriorityTab) []allocation.Priority {
	priorityList := make([]allocation.Priority, 0)
	productNameMap := localcache.AllItems(ctx, constant.ProductNameDict)
	for _, tempPriority := range tab.ProductPriorityDto.List {
		priority := allocation.Priority{
			Priority:             tempPriority.PrioritySequence,
			FulfillmentProductId: tempPriority.ProductId,
		}
		if value, ok := productNameMap[strconv.FormatInt(tempPriority.ProductId, 10)]; ok {
			productName, _ := value.(string)
			priority.FulfillmentProductName = productName
		}

		priorityList = append(priorityList, priority)
	}
	return priorityList
}

func priorityAdminToDb(ctx context.Context, adminList []allocation.Priority) pickup_priority.ProductPriority {
	dtoPriority := pickup_priority.ProductPriority{}

	for _, adminPriority := range adminList {
		priority := pickup_priority.Priority{
			PrioritySequence: adminPriority.Priority,
			ProductId:        adminPriority.FulfillmentProductId,
		}
		dtoPriority.List = append(dtoPriority.List, priority)
	}

	return dtoPriority
}

func convertToTab(ctx context.Context, req *allocation.CreatePriorityReq) *pickup_priority.PickupPriorityTab {
	email, _ := apiutil.GetUserInfo(ctx)
	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	tab := &pickup_priority.PickupPriorityTab{
		MaskingProductId: req.MaskingProductId,
		Operator:         email,
		Ctime:            nowTime,
		Mtime:            nowTime,
	}
	productPriority := priorityAdminToDb(ctx, req.List)
	tab.ProductPriorityDto = productPriority
	tab.Marshal()

	return tab
}
