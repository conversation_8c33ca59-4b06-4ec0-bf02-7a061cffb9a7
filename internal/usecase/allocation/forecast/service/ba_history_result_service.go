package service

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"github.com/shopspring/decimal"
	"strconv"
)

func (b *BatchAllocateForecastImpl) convertToEntityList(tabs []model.AllocateHistoryOutlineTab, resultType int, reqZoneRouteCode string) []model.OutlineEntity {
	var results []model.OutlineEntity
	for _, tab := range tabs {
		if tab.ResultType != resultType { //一级条件：result type
			continue
		}
		//zone or route code不为空，说明需要检索zone code 或route code
		zoneRouteCode := ""
		if reqZoneRouteCode != "" { //二级条件：zone route code
			if resultType == model.AllocateHistoryTypeZone {
				zoneRouteCode = tab.ZoneCode
			} else if resultType == model.AllocateHistoryTypeRoute {
				zoneRouteCode = tab.RouteCode
			}

			if zoneRouteCode != reqZoneRouteCode {
				continue
			}
		}
		result := model.OutlineEntity{
			MaskProductId:    tab.MaskProductId,
			ProductId:        tab.ProductId,
			Code:             zoneRouteCode,
			CodeType:         resultType,
			OrderQuantity:    tab.OrderQuantity,
			TotalShippingFee: tab.TotalShippingFee,
		}
		results = append(results, result)
	}
	return results
}

//get overall info and zone&route info
func (b BatchAllocateForecastImpl) GetAllAndZRHistoryList(tabs []model.AllocateHistoryOutlineTab, dayLength int64, req schema.BAHistoricalInfoReq) []schema.HistoricalInfo {
	//1. get overall info list
	overAllEntities := b.convertToEntityList(tabs, model.AllocateHistoryTypeProduct, "")
	overallInfoList := b.getOverallHistoryInfoList(overAllEntities, dayLength)
	//2. get zone & route info list
	zrInfoList := b.GetZRHistoryInfoList(tabs, dayLength, req)

	return append(overallInfoList, zrInfoList...)
}

func (b *BatchAllocateForecastImpl) GetZRHistoryInfoList(tabs []model.AllocateHistoryOutlineTab, dayLength int64, req schema.BAHistoricalInfoReq) []schema.HistoricalInfo {
	reqZoneRouteType := req.ZoneRouteType
	reqZoneRouteCode := req.ZoneRouteCode
	zoneTabs := b.convertToEntityList(tabs, model.AllocateHistoryTypeZone, reqZoneRouteCode)
	routeTabs := b.convertToEntityList(tabs, model.AllocateHistoryTypeRoute, reqZoneRouteCode)
	var entities []model.OutlineEntity
	switch reqZoneRouteType {
	case constant.ReqCodeTypeZone:
		entities = append(entities, zoneTabs...)
	case constant.ReqCodeTypeRoute:
		entities = append(entities, routeTabs...)
	case constant.ReqCodeTypeZoneAndRoute:
		entities = append(entities, zoneTabs...)
		entities = append(entities, routeTabs...)
	}
	zrInfoList := b.getZRHistoryInfoList(entities, dayLength, reqZoneRouteType)
	return zrInfoList
}

func (b *BatchAllocateForecastImpl) getOverallHistoryInfoList(entities []model.OutlineEntity, dayLength int64) []schema.HistoricalInfo {
	if len(entities) == 0 {
		return []schema.HistoricalInfo{}
	}
	var totalOrderCount int64
	channelCountMap := make(map[uint64]channelInfo, 0)
	//聚合tab结果
	for _, entity := range entities {
		totalOrderCount += entity.OrderQuantity
		if value, ok := channelCountMap[entity.ProductId]; !ok {
			//channel 第一次出现
			chInfo := channelInfo{
				FulfillmentChannel: entity.ProductId,
				OrderQuantity:      entity.OrderQuantity,
				Ado:                entity.OrderQuantity / dayLength,
				TotalShippingFee:   entity.TotalShippingFee,
			}
			channelCountMap[entity.ProductId] = chInfo
		} else {
			//channel并非第一次出现
			valueShippingFee, _ := strconv.ParseFloat(value.TotalShippingFee, 64)
			entityShippingFee, _ := strconv.ParseFloat(entity.TotalShippingFee, 64)
			//转换成decimal进行计算，避免精度丢失
			d := decimal.NewFromFloat(valueShippingFee)
			d2 := decimal.NewFromFloat(entityShippingFee)
			value.TotalShippingFee += d.Add(d2).String() //转换回string
			value.OrderQuantity += entity.OrderQuantity
			value.Ado = value.OrderQuantity / dayLength
		}
	}
	//定义over all结果信息
	allHistoricalInfo := schema.HistoricalInfo{
		ResultType: constant.ResultTypeOverAll,
		ResultName: "Overall Allocation Result",
	}
	//遍历聚合结果map，装填在over all结果中
	resultList := make([]schema.Result, len(channelCountMap))
	i := 0
	for _, countValue := range channelCountMap {
		//计算初始的比例
		originRatio, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", float64(countValue.OrderQuantity)/float64(totalOrderCount)), 64)
		//转换成decimal，再成100变成百分数
		d := decimal.NewFromFloat(originRatio)
		d = d.Mul(decimal.NewFromInt(100))
		//将百分数转换成string（以便前端处理
		orderRatio, _ := d.Float64()
		orderRatioStr := fmt.Sprintf("%.2f", orderRatio)
		//组装结果
		resultList[i] = schema.Result{
			FulfillmentChannel: countValue.FulfillmentChannel,
			OrderQuantity:      countValue.OrderQuantity,
			Ado:                countValue.Ado,
			OrderCountRatio:    orderRatioStr + "%",
		}
		totalShippingFee, _ := strconv.ParseFloat(countValue.TotalShippingFee, 64)
		resultList[i].TotalShippingFee = totalShippingFee
		i++
	}
	allHistoricalInfo.ResultList = resultList

	return []schema.HistoricalInfo{allHistoricalInfo}
}

//get zone/route info list
func (b *BatchAllocateForecastImpl) getZRHistoryInfoList(entityList []model.OutlineEntity, dayLength int64, reqZoneRouteType int) []schema.HistoricalInfo {
	var zrHistoricalList []schema.HistoricalInfo
	if len(entityList) == 0 {
		return zrHistoricalList
	}
	//zone, route类型需要按zone/route统计，统计该code下f channel对应的order quantity， ado， order ratio
	codeChannelCountMap := make(map[string]map[uint64]channelInfo, 0)
	//按zone/route code聚合
	var totalOrderCount int64
	codeTypeMap := make(map[string]string, 0)
	for _, entity := range entityList {
		totalOrderCount += entity.OrderQuantity
		//保存zone/route code的类型
		codeTypeMap[entity.Code] = model.TypePrefixMap[entity.CodeType]
		//聚合zone/route 下channel信息
		if codeChannelCountMap[entity.Code] == nil {
			//初始化空map
			codeChannelCountMap[entity.Code] = make(map[uint64]channelInfo, 0)
		}
		if value, ok := codeChannelCountMap[entity.Code][entity.ProductId]; !ok {
			//channel 第一次出现
			chInfo := channelInfo{ //zone/route 不需要统计shipping fee
				FulfillmentChannel: entity.ProductId,
				OrderQuantity:      entity.OrderQuantity,
				Ado:                entity.OrderQuantity / dayLength,
			}
			codeChannelCountMap[entity.Code][entity.ProductId] = chInfo
		} else {
			//channel 非第一次出现
			value.OrderQuantity += entity.OrderQuantity
			value.Ado = value.OrderQuantity / dayLength
			codeChannelCountMap[entity.Code][entity.ProductId] = value
		}
	}
	//遍历聚合结果map，装填结果
	i := 1 //标识result name顺序
	for code, channelCountMap := range codeChannelCountMap {
		resultName := fmt.Sprintf("%s %d -%s Allocation Result", codeTypeMap[code], i, code)
		//定义resp 结果单元
		historicalInfo := schema.HistoricalInfo{
			ResultType: constant.ReqResultTypeMap[reqZoneRouteType],
			ResultName: resultName,
			ZRCode:     code,
			ZRCodeType: codeTypeMap[code],
		}
		//遍历聚合结果map，装填在resp结果中
		resultList := make([]schema.Result, len(channelCountMap))
		j := 0
		for _, countValue := range channelCountMap {
			//计算初始的比例
			originRatio, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", float64(countValue.OrderQuantity)/float64(totalOrderCount)), 64)
			//转换成decimal，再乘以100变成百分数
			d := decimal.NewFromFloat(originRatio)
			d = d.Mul(decimal.NewFromInt(100))
			//将百分数转换成string（以便前端处理
			orderRatio, _ := d.Float64()
			orderRatioStr := fmt.Sprintf("%.2f", orderRatio)
			resultList[j] = schema.Result{
				FulfillmentChannel: countValue.FulfillmentChannel,
				OrderQuantity:      countValue.OrderQuantity,
				Ado:                countValue.Ado,
				OrderCountRatio:    orderRatioStr + "%",
			}
			j++
		}
		historicalInfo.ResultList = resultList
		zrHistoricalList = append(zrHistoricalList, historicalInfo)
		i++
	}

	return zrHistoricalList
}
