package service

type zone struct {
	ZoneCode string
	LocID    int
	PostCode string
}

type route struct {
	RouteCode      string
	OriginLocID    int
	OriginPostcode string
	DestLocID      int
	DestPostcode   string
}

type channelInfo struct {
	FulfillmentChannel uint64 `json:"fulfillment_channel"`
	OrderQuantity      int64  `json:"order_quantity"`
	Ado                int64  `json:"ado"`
	TotalShippingFee   string `json:"total_shipping_fee"`
	CostSaving         string `json:"cost_saving"`
}

type batchInfo struct {
	SubTaskId      uint64 `json:"sub_task_id"` //一个sub task 即对应一个batch size
	BatchSizeName  string `json:"batch_size_name"`
	BatchUnitCount int    `json:"batch_unit_count"`
}
