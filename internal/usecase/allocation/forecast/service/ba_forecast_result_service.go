package service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"
	"strconv"
	"strings"
)

const (
	batchSizeNameHistorical = "Historical Shipment"
)

func (b *BatchAllocateForecastImpl) GetOverAllList(
	ctx context.Context, req schema.BAForecastInfoReq, subtaskList []model.BatchAllocateSubTaskTab, dayLength int64,
	historyReq schema.BAHistoricalInfoReq) schema.BAForecastInfoResp {

	var (
		resp = schema.BAForecastInfoResp{
			OverallResultList:          make([]schema.OverallResult, 0),
			ZoneResultList:             make([]schema.ZoneRouteResult, 0),
			RouteResultList:            make([]schema.ZoneRouteResult, 0),
			PickupEfficiencyResultList: make([]schema.PickupEfficiencyResult, 0),
		}
		forecastOverallResultList   []schema.OverallResult
		forecastPickupEffResultList []schema.PickupEfficiencyResult
	)

	if strings.ToLower(req.ZoneCode) == "all" || req.ZoneCode == "" {
		forecastOverallResultList, forecastPickupEffResultList = b.getResultOverallList(ctx, subtaskList, dayLength)
	}

	//预测结果只展示over all信息
	//resp.ZoneResultList = b.getResultZRList(ctx, req, subtaskList)

	//获取history 信息
	historyInfo, gErr := b.GetHistoricalInfo(ctx, historyReq)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetOverAllList|req:%v, get history info err:%v", historyReq, gErr)
		return resp
	}
	for _, info := range historyInfo.List {
		//只需要over all信息
		if info.ResultType != constant.ResultTypeOverAll {
			continue
		}

		var (
			totalShippingFee float64
			resultDetailList = make([]schema.ResultDetail, 0, len(info.ResultList))
		)
		for _, historyResult := range info.ResultList {
			totalShippingFee += historyResult.TotalShippingFee
			resultDetailList = append(resultDetailList, schema.ResultDetail{
				FulfillmentProduct: historyResult.FulfillmentChannel,
				OrderQuantity:      historyResult.OrderQuantity,
				Ado:                historyResult.Ado,
				ShippingFee:        fmt.Sprintf("%.2f", historyResult.TotalShippingFee),
				Percentage:         historyResult.OrderCountRatio,
			})
		}
		resp.OverallResultList = append(resp.OverallResultList, schema.OverallResult{
			BatchSizeName:    batchSizeNameHistorical,
			TotalShippingFee: fmt.Sprintf("%.4f", totalShippingFee),
			ResultDetailList: resultDetailList,
		})
		resp.OverallResultList = append(resp.OverallResultList, forecastOverallResultList...)

		pickupEffHistoricalResult, err := b.GetHistoricalPickupEffInfo(ctx, historyReq)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get historical pickup efficiency info err:%v", err)
			continue
		}
		resp.PickupEfficiencyResultList = append(resp.PickupEfficiencyResultList, schema.PickupEfficiencyResult{
			BatchSizeName: batchSizeNameHistorical,
			ResultList:    pickupEffHistoricalResult,
		})
		resp.PickupEfficiencyResultList = append(resp.PickupEfficiencyResultList, forecastPickupEffResultList...)

		break
	}

	return resp
}

func (b *BatchAllocateForecastImpl) GetOverAllAndZRList(ctx context.Context, req schema.BAForecastInfoReq, subtaskList []model.BatchAllocateSubTaskTab, dayLength int64) schema.BAForecastInfoResp {
	resp := schema.BAForecastInfoResp{
		OverallResultList:          make([]schema.OverallResult, 0),
		ZoneResultList:             make([]schema.ZoneRouteResult, 0),
		RouteResultList:            make([]schema.ZoneRouteResult, 0),
		PickupEfficiencyResultList: make([]schema.PickupEfficiencyResult, 0),
	}
	if strings.ToLower(req.ZoneCode) == "all" || req.ZoneCode == "" {
		resp.OverallResultList, resp.PickupEfficiencyResultList = b.getResultOverallList(ctx, subtaskList, dayLength)
	}

	resp.ZoneResultList, resp.RouteResultList = b.getResultZRList(ctx, req, subtaskList)

	return resp
}

func (b *BatchAllocateForecastImpl) getResultOverallList(
	ctx context.Context, subtaskList []model.BatchAllocateSubTaskTab, dayLength int64) ([]schema.OverallResult, []schema.PickupEfficiencyResult) {

	var (
		overallList         = make([]schema.OverallResult, 0)
		pickupEffResultList = make([]schema.PickupEfficiencyResult, 0)
	)

	for _, subTask := range subtaskList {
		//unmarshal splitting rule
		splitRule := model.SplittingRule{}
		if err := jsoniter.Unmarshal(subTask.SplittingRule, &splitRule); err != nil {
			logger.CtxLogErrorf(ctx, "getResultOverallList|sub task id:%v, unmarshal splitting rule err:%v", subTask.Id, err)
			continue
		}
		//1. get overall result by sub_task_id
		results, gErr := b.BatchResultRepo.GetResultsByCondition(ctx, map[string]interface{}{
			"sub_task_id = ?": subTask.Id,
			"result_type = ?": allocation.ResultTypeOverall,
		})
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "getResultOverallList|sub task id:%v, get overall result err:%v", gErr)
			continue
		}
		//2.获取聚合数据
		outline, gErr := b.BatchSubtaskOutlineRepo.GetOutlineByCondition(ctx, map[string]interface{}{
			"sub_task_id = ?": subTask.Id,
		})
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "getResultOverallList|sub task id:%v, get sub task outline err:%v", gErr)
			continue
		}

		//3.聚合装填数据
		//3.1 build up overall info
		overall := schema.OverallResult{
			BatchSizeName:        splitRule.BatchSizeName,
			TotalShippingFee:     fmt.Sprintf("%.2f", outline.TotalShippingFee),
			TotalCostSaving:      fmt.Sprintf("%.2f", outline.TotalCostSaving),
			PickupEfficiencyCost: fmt.Sprintf("%.2f", outline.PickupEfficiencyCost),
			BatchQuantity:        outline.Batches,
			MaxHoldingTime:       int(outline.MaxHoldingTime),
			AvgMaxHoldingTime:    int(outline.AvgHoldingTime),
			AvgOrdersEachBatch:   int(outline.AvgOrdersPerBatch),
		}
		//3.2 装填详情
		//计算total count
		var (
			totalCount int64
		)
		for _, result := range results {
			totalCount += result.OrderQuantity
		}

		for _, result := range results {
			resultDetail := schema.ResultDetail{
				FulfillmentProduct: result.FulfillmentProductId,
				OrderQuantity:      result.OrderQuantity,
				Ado:                result.OrderQuantity / dayLength,
				ShippingFee:        fmt.Sprintf("%.2f", result.TotalCostFee),
				CostSaving:         fmt.Sprintf("%.2f", result.TotalSaveFee),
			}
			//计算初始的比例
			originRatio, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", float64(result.OrderQuantity)/float64(totalCount)), 64)
			//转换成decimal，再乘以100变成百分数
			d := decimal.NewFromFloat(originRatio)
			d = d.Mul(decimal.NewFromInt(100))
			//将百分数转换成string（以便前端处理
			orderRatio, _ := d.Float64()
			orderRatioStr := fmt.Sprintf("%.2f", orderRatio)
			resultDetail.Percentage = orderRatioStr + "%"

			overall.ResultDetailList = append(overall.ResultDetailList, resultDetail)
		}

		overallList = append(overallList, overall)

		pickupEffResult := schema.PickupEfficiencyResult{
			BatchSizeName: splitRule.BatchSizeName,
			ResultList:    make([]schema.PickupEfficiencyResultDetail, 0, len(outline.PickupEfficiencyResult)),
		}
		for _, r := range outline.PickupEfficiencyResult {
			pickupEffResult.ResultList = append(pickupEffResult.ResultList, schema.PickupEfficiencyResultDetail{
				NumberOf3pls:  r.NumberOf3pls,
				NumberOfShops: r.NumberOfShops,
			})
		}
		pickupEffResultList = append(pickupEffResultList, pickupEffResult)
	}

	return overallList, pickupEffResultList
}

func (b *BatchAllocateForecastImpl) getResultZRList(
	ctx context.Context, req schema.BAForecastInfoReq, subtaskList []model.BatchAllocateSubTaskTab,
) ([]schema.ZoneRouteResult, []schema.ZoneRouteResult) {
	// 检索sub task unit result list
	var (
		zoneResultList  []schema.ZoneRouteResult
		routeResultList []schema.ZoneRouteResult
		batchIdInfoMap  = make(map[uint64]batchInfo, 0)
		condition       = map[string]interface{}{
			"result_type IN (?)": []int{constant.ResultTypeZone, constant.ResultTypeRoute},
		}
	)
	if req.ZoneCode != "" && strings.ToLower(req.ZoneCode) != "all" {
		condition["zone_code = ?"] = req.ZoneCode
	}
	for _, subTask := range subtaskList { //每个subtask对应一个batch size
		//unmarshal splitting rule
		splitRule := model.SplittingRule{}
		if err := jsoniter.Unmarshal(subTask.SplittingRule, &splitRule); err != nil {
			logger.CtxLogErrorf(ctx, "GetForecastInfo|sub task id:%v, unmarshal splitting rule err:%v", subTask.Id, err)
			continue
		}
		//获取unit result list
		condition["sub_task_id = ?"] = subTask.Id
		unitResultList, gErr := b.BatchAllocateForecastRepo.GetSubTaskUnitResultListByCondition(ctx, condition)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetForecastInfo|sub task id:%v, condition:%v, get unit result list err:%v", subTask.Id, condition, gErr)
			continue
		}
		//获取unit 信息
		unitList, gErr := b.BatchAllocateForecastRepo.GetSubTaskUnitByCondition(ctx, map[string]interface{}{"sub_task_id = ?": subTask.Id})
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetForecastInfo|zone|sub task id:%v, get unit list err:%v", gErr)
			continue
		}
		if len(unitList) == 0 || len(unitResultList) == 0 {
			logger.CtxLogErrorf(ctx, "GetForecastInfo|zone|length of unit list:%v, length of unit result list:%v, one of them is 0, just continue", len(unitList), len(unitResultList))
			continue
		}
		//按batch name统计unit数
		batchIdInfoMap[subTask.Id] = batchInfo{
			BatchSizeName:  splitRule.BatchSizeName,
			BatchUnitCount: len(unitList),
		}

		//按zone统计订单数
		var (
			totalCount     int64
			zoneResultMap  = make(map[string]map[uint64]int64)
			routeResultMap = make(map[string]map[uint64]int64)
		)
		for _, unitResult := range unitResultList {
			totalCount += unitResult.OrderQuantity
			if unitResult.ResultType == allocation.ResultTypeZone {
				if _, ok := zoneResultMap[unitResult.ZoneCode]; !ok {
					zoneResultMap[unitResult.ZoneCode] = make(map[uint64]int64)
				}
				zoneResultMap[unitResult.ZoneCode][unitResult.FulfillmentProductId] += unitResult.OrderQuantity
			} else if unitResult.ResultType == allocation.ResultTypeRoute {
				if _, ok := routeResultMap[unitResult.RouteCode]; !ok {
					routeResultMap[unitResult.RouteCode] = make(map[uint64]int64)
				}
				routeResultMap[unitResult.RouteCode][unitResult.FulfillmentProductId] += unitResult.OrderQuantity
			}
		}

		//装填zone result
		zoneResult := schema.ZoneRouteResult{
			BatchSizeName: splitRule.BatchSizeName,
		}
		for zoneCode, productCountMap := range zoneResultMap {
			for product, count := range productCountMap {
				result := schema.BatchSizeResult{
					ZoneCode:           zoneCode,
					BatchQuantity:      count,
					ADO:                count,
					FulfillmentProduct: product,
				}
				if totalCount != 0 {
					result.Percentage = fmt.Sprintf("%.4f", float64(count)/float64(totalCount))
				}
				zoneResult.BatchSizeResultList = append(zoneResult.BatchSizeResultList, result)
			}
		}
		zoneResultList = append(zoneResultList, zoneResult)

		routeResult := schema.ZoneRouteResult{
			BatchSizeName: splitRule.BatchSizeName,
		}
		for routeCode, productCountMap := range routeResultMap {
			for product, count := range productCountMap {
				result := schema.BatchSizeResult{
					RouteCode:          routeCode,
					BatchQuantity:      count,
					ADO:                count,
					FulfillmentProduct: product,
				}
				if totalCount != 0 {
					result.Percentage = fmt.Sprintf("%.4f", float64(count)/float64(totalCount))
				}
				routeResult.BatchSizeResultList = append(routeResult.BatchSizeResultList, result)
			}
		}
		routeResultList = append(routeResultList, routeResult)
	}
	return zoneResultList, routeResultList
}

func (b *BatchAllocateForecastImpl) GetHistoricalPickupEffInfo(ctx context.Context, req schema.BAHistoricalInfoReq) ([]schema.PickupEfficiencyResultDetail, *srerr.Error) {
	days, err := req.OrderPaidTime.OrderPaidTimeToTimeList()
	if err != nil {
		logger.CtxLogErrorf(ctx, "get time list failed | err=%v", err)
		return nil, err
	}

	if len(days) == 0 {
		return nil, nil
	}

	shopIDs, err := b.PickupEffCounter.GetShopIdSet(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get shop id set failed | err=%v", err)
		return nil, err
	}

	pickupEffCountMap := make(map[int]int)
	for _, day := range days {
		shopFulfillmentProducts, err := b.PickupEffCounter.BatchGetShopFulfillmentProducts(ctx, shopIDs, int64(req.MaskProductId), day)
		if err != nil {
			logger.CtxLogErrorf(ctx, "batch get shop fulfillment products | err=%v", err)
			return nil, err
		}

		for _, fulfillmentProducts := range shopFulfillmentProducts {
			if len(fulfillmentProducts) == 0 {
				continue
			}
			pickupEffCountMap[len(fulfillmentProducts)] += 1
		}
	}

	resultList := make([]schema.PickupEfficiencyResultDetail, 0, len(pickupEffCountMap))
	for numberOf3pls, numberOfShops := range pickupEffCountMap {
		resultList = append(resultList, schema.PickupEfficiencyResultDetail{
			NumberOf3pls:  numberOf3pls,
			NumberOfShops: objutil.RoundDivideInt(numberOfShops, len(days)),
		})
	}

	return resultList, nil
}
