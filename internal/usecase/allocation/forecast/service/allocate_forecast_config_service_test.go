package service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"testing"
)

func TestAllocateForecastTaskConfigServiceImpl_CreateBASubTask(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	redisutil.InitDefaultClient()
	s := AllocateForecastTaskConfigServiceImpl{ForecastingSubTaskRepo: forecasting_sub_task.NewForecastingSubTaskRepoImpl()}
	err := s.CreateBASubTaskByTaskConfig(context.TODO())
	if err != nil {
		panic(err)
	}
}
