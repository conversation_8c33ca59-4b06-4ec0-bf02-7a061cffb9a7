package service

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"sort"
	"strconv"
	"strings"
	"sync"
)

type AllocateRankService interface {
	GetDateRankCodeList(ctx context.Context, req *schema.DateRankCodeListReq) (*schema.RankCodeListResp, *srerr.Error)
	GetResultRankCodeList(ctx context.Context, req *schema.ResultRankCodeListReq) (*schema.RankCodeListResp, *srerr.Error)
	GetDateRankList(ctx context.Context, req *schema.DateRankListReq) (*schema.RankListResp, *srerr.Error)
	GetResultRankList(ctx context.Context, req *schema.ResultRankListReq) (*schema.RankListResp, *srerr.Error)
	StatisticsDateRank(ctx context.Context, orders []*model.AllocateOrderDataTab) (map[string]int, *srerr.Error)
	IncrDateRank(ctx context.Context, recordMap map[string]int) *srerr.Error
	IncrHistoricalRank(ctx context.Context, recordMap *sync.Map) *srerr.Error
	IncrForecastRank(ctx context.Context, recordMap *sync.Map) *srerr.Error
	ExportDateRankList(ctx context.Context, req *schema.DateRankListReq) (*bytes.Buffer, *srerr.Error)
	ExportResultRankList(ctx context.Context, req *schema.ResultRankListReq) (*bytes.Buffer, *srerr.Error)
}

type AllocateRankServiceImpl struct {
	AllocateDateRankRepo              repo.AllocateDateRankRepo
	AllocateHistoricalRankRepo        repo.AllocateHistoricalRankRepo
	AllocateForecastRankRepo          repo.AllocateForecastRankRepo
	AllocateForecastTaskConfigService AllocateForecastTaskConfigService
	LpsApi                            lpsclient.LpsApi
}

func NewAllocateRankServiceImpl(LpsApi lpsclient.LpsApi, AllocateDateRankRepo repo.AllocateDateRankRepo, AllocateHistoricalRankRepo repo.AllocateHistoricalRankRepo,
	AllocateForecastRankRepo repo.AllocateForecastRankRepo, AllocateForecastTaskConfigService AllocateForecastTaskConfigService) *AllocateRankServiceImpl {
	return &AllocateRankServiceImpl{
		LpsApi:                            LpsApi,
		AllocateDateRankRepo:              AllocateDateRankRepo,
		AllocateHistoricalRankRepo:        AllocateHistoricalRankRepo,
		AllocateForecastRankRepo:          AllocateForecastRankRepo,
		AllocateForecastTaskConfigService: AllocateForecastTaskConfigService,
	}
}

func (a AllocateRankServiceImpl) GetResultRankCodeList(ctx context.Context, req *schema.ResultRankCodeListReq) (*schema.RankCodeListResp, *srerr.Error) {
	if *req.Type == allocation.HistoricalRank {
		rankCodeList, err := a.AllocateHistoricalRankRepo.GetRankCodeList(ctx, *req.TaskId, *req.MaskingProductId, *req.RankType)
		if err != nil {
			return nil, err
		}
		if len(rankCodeList) == 0 {
			rankCodeLists := make([]*schema.RankCode, 0)
			allRankCode := &schema.RankCode{
				RankType:    *req.RankType,
				RankCode:    allocation.AllRankCode,
				DisplayName: allocation.AllRankCode,
			}
			rankCodeLists = append(rankCodeLists, allRankCode)
			resp := &schema.RankCodeListResp{
				RankCodeList: rankCodeLists,
			}
			return resp, nil
		}
		resp, err := a.convertRankCodeToResp(ctx, allocation.CLientTag3PLMasking, *req.RankType, rankCodeList)
		return resp, err
	} else if *req.Type == allocation.ForecastRank {
		taskConfig, err := a.AllocateForecastTaskConfigService.GetForecastTaskConfigById(ctx, int64(*req.TaskId))
		if err != nil || taskConfig == nil {
			return nil, srerr.New(srerr.ParamErr, *req, "can't find task config by task id = %v", *req.TaskId)
		}
		rankCodeList, err := a.AllocateForecastRankRepo.GetRankCodeList(ctx, *req.TaskId, *req.MaskingProductId, *req.RankType)
		if err != nil {
			return nil, err
		}
		if len(rankCodeList) == 0 {
			rankCodeLists := make([]*schema.RankCode, 0)
			allRankCode := &schema.RankCode{
				RankType:    *req.RankType,
				RankCode:    allocation.AllRankCode,
				DisplayName: allocation.AllRankCode,
			}
			rankCodeLists = append(rankCodeLists, allRankCode)
			resp := &schema.RankCodeListResp{
				RankCodeList: rankCodeLists,
			}
			return resp, nil
		}
		clientTagId := allocation.CLientTag3PLMasking
		if taskConfig.BaseInfo.ShopGroupChannelPriorityToggle {
			clientTagId = allocation.ClientTag3PLMaskingForecast
		}
		resp, err := a.convertRankCodeToResp(ctx, uint64(clientTagId), *req.RankType, rankCodeList)
		return resp, err
	}
	return nil, nil
}

func (a AllocateRankServiceImpl) convertRankCodeToResp(ctx context.Context, clientTagId uint64, rankType int, rankCodeList []string) (*schema.RankCodeListResp, *srerr.Error) {
	resp := &schema.RankCodeListResp{
		RankCodeList: make([]*schema.RankCode, 0),
	}
	rankCodeLists := make([]*schema.RankCode, 0)
	allRankCode := &schema.RankCode{
		RankType:    rankType,
		RankCode:    allocation.AllRankCode,
		DisplayName: allocation.AllRankCode,
	}
	rankCodeLists = append(rankCodeLists, allRankCode)
	shopGroupMap := make(map[string]*schema.ShopGroup)
	var err *srerr.Error
	if constant.AllocateForecastRankTypeEnum[(constant.ShopGroup)] == rankType {
		shopGroupMap, err = a.getShopGroupsByRankCode(ctx, clientTagId, rankCodeList)
		if err != nil {
			return nil, err
		}
	}
	for _, rankCode := range rankCodeList {
		r := &schema.RankCode{
			RankCode: rankCode,
			RankType: rankType,
		}
		if rankType == constant.AllocateForecastRankTypeEnum[constant.ShopGroup] {
			shopGroup, exist := shopGroupMap[r.RankCode]
			if exist {
				r.DisplayName = fmt.Sprintf("%v-%s", shopGroup.GroupId, shopGroup.GroupName)
				rankCodeLists = append(rankCodeLists, r)
			}
		} else {
			r.DisplayName = rankCode
			rankCodeLists = append(rankCodeLists, r)
		}
	}
	resp.RankCodeList = rankCodeLists
	return resp, nil
}

func (a AllocateRankServiceImpl) GetDateRankCodeList(ctx context.Context, req *schema.DateRankCodeListReq) (*schema.RankCodeListResp, *srerr.Error) {
	rankCodeList, err := a.AllocateDateRankRepo.GetRankCodeList(ctx, *req.MaskingProductId, *req.RankType)
	if err != nil {
		return nil, err
	}
	if len(rankCodeList) == 0 {
		rankCodeLists := make([]*schema.RankCode, 0)
		allRankCode := &schema.RankCode{
			RankType:    *req.RankType,
			RankCode:    allocation.AllRankCode,
			DisplayName: allocation.AllRankCode,
		}
		rankCodeLists = append(rankCodeLists, allRankCode)
		resp := &schema.RankCodeListResp{
			RankCodeList: rankCodeLists,
		}
		return resp, nil
	}
	resp, err := a.convertRankCodeToResp(ctx, allocation.CLientTag3PLMasking, *req.RankType, rankCodeList)
	return resp, err
}

func (a AllocateRankServiceImpl) getShopGroupsByRankCode(ctx context.Context, tagId uint64, rankCodeList []string) (map[string]*schema.ShopGroup, *srerr.Error) {
	result := make(map[string]*schema.ShopGroup)
	// 根据区域判断是否需要从client 模块获取shop group 信息
	if configutil.GetUseClientModConfig(ctx).Switch {
		// 首先添加 default group
		defaultShopGroupId := strconv.FormatInt(constant2.DefaultShopGroupId, 10)
		defaultShopGroup := &schema.ShopGroup{
			GroupId:   constant2.DefaultShopGroupId,
			GroupName: constant2.DefaultShopGroupName,
		}
		result[defaultShopGroupId] = defaultShopGroup
		//SSCSMR-517:从lps获取client group
		clientGroupList, err := a.LpsApi.GetClientGroupTabsByTag(ctx, tagId)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Error())
			return nil, err
		}
		for _, clientGroup := range clientGroupList {
			groupID, pErr := strconv.ParseInt(clientGroup.ClientGroupID, 10, 64)
			if pErr != nil {
				logger.CtxLogErrorf(ctx, "Can't not convert shop group id, client group id =%v", clientGroup.ClientGroupID)
				continue
			}
			if !objutil.ContainStr(rankCodeList, clientGroup.ClientGroupID) {
				continue
			}
			result[clientGroup.ClientGroupID] = &schema.ShopGroup{
				GroupId:   groupID,
				GroupName: clientGroup.ClientGroupName,
			}
		}
		return result, nil
	}
	//tSSCSMR-517:从lps获取 shop group
	shopGroups, err := a.LpsApi.GetShopGroupList(ctx)
	if err != nil {
		return nil, err
	}
	for _, shopGroup := range shopGroups {
		result[strconv.FormatInt(shopGroup.GroupId, 10)] = &schema.ShopGroup{
			GroupId:   shopGroup.GroupId,
			GroupName: shopGroup.GroupName,
		}
	}
	return result, nil
}

func (a AllocateRankServiceImpl) GetDateRankList(ctx context.Context, req *schema.DateRankListReq) (*schema.RankListResp, *srerr.Error) {
	var overAllRankList []*model.AllocateDateRankTab
	var err *srerr.Error
	rankCode := *req.RankCode
	dateStrList, err := req.OrderPaidTime.OrderPaidTimeToStrList()
	if err != nil {
		return nil, err
	}
	if len(dateStrList) <= 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "the length of date_list must be greater than 0")
	}
	if rankCode == allocation.AllRankCode {

		overAllRankList, err = a.AllocateDateRankRepo.GetDateRankList(ctx, *req.MaskingProductId,
			constant.AllocateForecastRankTypeEnum[constant.Overall], "", dateStrList)
		if err != nil {
			return nil, err
		}
		rankCode = ""
	}
	dateRankList, err := a.AllocateDateRankRepo.GetDateRankList(ctx, *req.MaskingProductId, *req.RankType, rankCode, dateStrList)
	if err != nil {
		return nil, err
	}
	resp, err := a.convertDateRankToResp(ctx, *req.RankType, dateStrList, overAllRankList, dateRankList)
	return resp, err
}

func (a AllocateRankServiceImpl) GetProductsByProductIds(ctx context.Context, productIds []int) (map[string]*lpsclient.LogisticProductTab, *srerr.Error) {
	productsMap := make(map[string]*lpsclient.LogisticProductTab)
	productMap := localcache.AllItems(ctx, constant2.ProductBaseInfoList)
	for _, productId := range productIds {
		if productInterface, existed := productMap[strconv.Itoa(productId)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				productsMap[strconv.Itoa(product.ProductId)] = product
			} else {
				logger.CtxLogErrorf(ctx, "GetProductsByProductIds| product id:%v, convert fail")
			}
		}
	}
	return productsMap, nil
}

func (a *AllocateRankServiceImpl) StatisticsDateRank(ctx context.Context, orders []*model.AllocateOrderDataTab) (map[string]int, *srerr.Error) {
	result := make(map[string]int)
	for _, order := range orders {
		maskProductId := order.MaskingProductID
		productId := order.FulfillmentProductID
		dateStr := timeutil.ConvertTimeStampToLocalTime(int64(order.OrderTime)).Format(constant2.TimeLayout)
		overallRankType := constant.AllocateForecastRankTypeEnum[constant.Overall]
		overallRankCode := strconv.Itoa(productId)
		overallKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, overallRankType, overallRankCode)
		if value, exists := result[overallKey]; exists {
			result[overallKey] = value + 1
		} else {
			result[overallKey] = 1
		}
		shopGroupId := order.ShopGroupId
		shopGroupType := constant.AllocateForecastRankTypeEnum[constant.ShopGroup]
		shopGroupKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, shopGroupType, shopGroupId)
		if value, exists := result[shopGroupKey]; exists {
			result[shopGroupKey] = value + 1
		} else {
			result[shopGroupKey] = 1
		}
		zone := order.ZoneCode
		if zone != "" {
			zoneType := constant.AllocateForecastRankTypeEnum[constant.Zone]
			zoneKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, zoneType, zone)
			if value, exists := result[zoneKey]; exists {
				result[zoneKey] = value + 1
			} else {
				result[zoneKey] = 1
			}
		}
		originZone := order.OriginZoneCode
		if originZone != "" {
			zoneType := constant.AllocateForecastRankTypeEnum[constant.OriginZone]
			zoneKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, zoneType, originZone)
			if value, exists := result[zoneKey]; exists {
				result[zoneKey] = value + 1
			} else {
				result[zoneKey] = 1
			}
		}
		destZone := order.DestZoneCode
		if destZone != "" {
			zoneType := constant.AllocateForecastRankTypeEnum[constant.DestZone]
			zoneKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, zoneType, destZone)
			if value, exists := result[zoneKey]; exists {
				result[zoneKey] = value + 1
			} else {
				result[zoneKey] = 1
			}
		}
		route := order.RouteCode
		if route != "" {
			routeType := constant.AllocateForecastRankTypeEnum[constant.Route]
			routeKey := fmt.Sprintf("%v#%v#%v#%v#%v", maskProductId, productId, dateStr, routeType, route)
			if value, exists := result[routeKey]; exists {
				result[routeKey] = value + 1
			} else {
				result[routeKey] = 1
			}
		}
	}
	return result, nil
}

func (a *AllocateRankServiceImpl) IncrDateRank(ctx context.Context, recordMap map[string]int) *srerr.Error {
	var records []*model.AllocateDateRankTab
	for key, value := range recordMap {
		values := strings.Split(key, "#")
		record := &model.AllocateDateRankTab{}
		record.OrderQuantity = value
		maskingProductId, err := strconv.Atoi(values[0])
		if err != nil {
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
		record.MaskingProductID = maskingProductId
		productId, err := strconv.Atoi(values[1])
		if err != nil {
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
		record.ProductId = productId
		dateStr := values[2]
		record.Date = dateStr

		rankType, err := strconv.Atoi(values[3])
		if err != nil {
			return srerr.With(srerr.TypeConvertErr, nil, err)
		}
		record.RankType = rankType

		rankCode := values[4]
		record.RankCode = rankCode

		//填充shipping  fee
		records = append(records, record)
	}
	sort.SliceStable(records, func(i, j int) bool {
		if records[i].MaskingProductID < records[j].MaskingProductID {
			return true
		} else if (records[i].MaskingProductID == records[j].MaskingProductID) && (records[i].ProductId < records[j].ProductId) {
			return true
		} else if (records[i].MaskingProductID == records[j].MaskingProductID) && (records[i].ProductId == records[j].ProductId) && (records[i].Date < records[j].Date) {
			return true
		} else if (records[i].MaskingProductID == records[j].MaskingProductID) && (records[i].ProductId == records[j].ProductId) && (records[i].Date == records[j].Date) && (records[i].RankType < records[j].RankType) {
			return true
		} else if (records[i].MaskingProductID == records[j].MaskingProductID) && (records[i].ProductId == records[j].ProductId) && (records[i].Date == records[j].Date) && (records[i].RankType == records[j].RankType) && (records[i].RankCode < records[j].RankCode) {
			return true
		} else {
			return false
		}
	})
	_, err := a.AllocateDateRankRepo.InsertAllocateDateRank(ctx, records)
	return err
}

func (a AllocateRankServiceImpl) IncrHistoricalRank(ctx context.Context, recordMap *sync.Map) *srerr.Error {
	if recordMap == nil {
		return srerr.New(srerr.ServerErr, recordMap, "IncrHistoricalRank occur err. recordMap can't be nil.")
	}
	var records []*model.AllocateHistoricalRankTab
	recordMap.Range(func(key, value interface{}) bool {
		values := strings.Split(key.(string), ":")
		isTotalKey := false
		if len(values) == 2 {
			logger.CtxLogInfof(ctx, "IncrHistoricalRank|total key:%v, value:%v, skip it", key, value)
			isTotalKey = true
		}
		//total key no need to store into db, so just skip it
		if !isTotalKey {
			record := &model.AllocateHistoricalRankTab{}
			record.OrderQuantity = value.(int)
			maskingProductId, err := strconv.Atoi(values[0])
			if err != nil {
				return false
			}
			record.MaskingProductID = maskingProductId
			productId, err := strconv.Atoi(values[1])
			if err != nil {
				return false
			}
			record.ProductId = productId
			forecastTaskId, err := strconv.Atoi(values[2])
			if err != nil {
				return false
			}
			record.ForecastTaskId = int64(forecastTaskId)

			rankType, err := strconv.Atoi(values[3])
			if err != nil {
				return false
			}
			record.RankType = rankType

			rankCode := values[4]
			record.RankCode = rankCode
			records = append(records, record)
		}
		return true
	})
	_, err := a.AllocateHistoricalRankRepo.InsertAllocateHistoricalRank(ctx, records)
	return err
}

func (a AllocateRankServiceImpl) IncrForecastRank(ctx context.Context, recordMap *sync.Map) *srerr.Error {
	if recordMap == nil {
		return srerr.New(srerr.ServerErr, recordMap, "IncrForecastRank occur err. recordMap can't be nil.")
	}
	var records []*model.AllocateForecastRankTab
	recordMap.Range(func(key, value interface{}) bool {
		logger.CtxLogInfof(ctx, "IncrForecastRank| key:%v, value:%v", key, value)
		values := strings.Split(key.(string), ":")
		isTotalKey := false
		if len(values) == 2 {
			logger.CtxLogInfof(ctx, "IncrForecastRank|total key:%v, value:%v, skip it", key, value)
			isTotalKey = true
		}
		//total key no need to store into db, so just skip it
		if !isTotalKey {
			record := &model.AllocateForecastRankTab{}
			record.OrderQuantity = value.(int)
			maskingProductId, err := strconv.Atoi(values[0])
			if err != nil {
				return false
			}
			record.MaskingProductID = maskingProductId
			productId, err := strconv.Atoi(values[1])
			if err != nil {
				return false
			}
			record.ProductId = productId
			forecastTaskId, err := strconv.Atoi(values[2])
			if err != nil {
				return false
			}
			record.ForecastTaskId = int64(forecastTaskId)

			rankType, err := strconv.Atoi(values[3])
			if err != nil {
				return false
			}
			record.RankType = rankType

			rankCode := values[4]
			record.RankCode = rankCode
			records = append(records, record)
			logger.CtxLogInfof(ctx, "IncrForecastRank| record:%+v", *record)
		}
		return true
	})
	_, err := a.AllocateForecastRankRepo.InsertAllocateForecastRank(ctx, records)
	return err
}

func (a AllocateRankServiceImpl) GetResultRankList(ctx context.Context, req *schema.ResultRankListReq) (
	*schema.RankListResp, *srerr.Error) {
	taskConfig, err := a.AllocateForecastTaskConfigService.GetForecastTaskConfigById(ctx, int64(*req.TaskId))
	if err != nil || taskConfig == nil {
		return nil, srerr.New(srerr.ParamErr, *req, "can't find task config by task id = %v", *req.TaskId)
	}
	if *req.Type == allocation.HistoricalRank {
		var overAllRankList []*model.AllocateHistoricalRankTab
		dateStrList, err := taskConfig.BaseInfo.OrderPaidTime.OrderPaidTimeToStrList()
		if err != nil {
			return nil, err
		}
		if len(dateStrList) <= 0 {
			return nil, srerr.New(srerr.DataErr, *taskConfig, "order paid time of task config must be greater than 0")
		}
		queryRankCode := *req.RankCode
		if queryRankCode == allocation.AllRankCode {
			queryRankCode = ""
			overAllRankList, err = a.AllocateHistoricalRankRepo.GetHistoricalRankList(
				ctx, *req.TaskId, *req.MaskingProductId, constant.AllocateForecastRankTypeEnum[constant.Overall], queryRankCode)
			if err != nil {
				return nil, err
			}
		}
		historicalRankList, err := a.AllocateHistoricalRankRepo.GetHistoricalRankList(
			ctx, *req.TaskId, *req.MaskingProductId, *req.RankType, queryRankCode)
		if err != nil {
			return nil, err
		}
		resp, err := a.convertHistoricalRankToResp(ctx, *req.RankType, dateStrList, overAllRankList, historicalRankList)
		return resp, err
	} else if *req.Type == allocation.ForecastRank {
		var overAllRankList []*model.AllocateForecastRankTab
		dateStrList, err := taskConfig.BaseInfo.OrderPaidTime.OrderPaidTimeToStrList()
		if err != nil {
			return nil, err
		}
		if len(dateStrList) <= 0 {
			return nil, srerr.New(srerr.DataErr, *taskConfig, "order paid time of task config must be greater than 0")
		}
		queryRankCode := *req.RankCode
		if queryRankCode == allocation.AllRankCode {
			queryRankCode = ""
			overAllRankList, err = a.AllocateForecastRankRepo.GetForecastRankList(
				ctx, *req.TaskId, constant.AllocateForecastRankTypeEnum[constant.Overall], queryRankCode)
			if err != nil {
				return nil, err
			}
		}
		// 查询blocked数
		blockedRankList, err := a.AllocateForecastRankRepo.GetForecastRankList(
			ctx, *req.TaskId, constant.AllocateForecastRankTypeEnum[constant.Blocked], "")
		if err != nil {
			return nil, err
		}
		dateRankList, err := a.AllocateForecastRankRepo.GetForecastRankList(
			ctx, *req.TaskId, *req.RankType, queryRankCode)
		if err != nil {
			return nil, err
		}
		resp, err := a.convertForecastRankToResp(ctx, *req.RankType, dateStrList, taskConfig, overAllRankList, blockedRankList, dateRankList)
		return resp, err
	}
	return nil, srerr.New(srerr.DataErr, *taskConfig, "type not support. type=%v", *req.Type)
}

func (a AllocateRankServiceImpl) convertDateRankToResp(ctx context.Context, rankType int, dateList []string, overAllRankList, dateRankList []*model.AllocateDateRankTab) (*schema.RankListResp, *srerr.Error) {
	resp := &schema.RankListResp{
		List: make([]*schema.RankList, 0),
	}
	if len(dateRankList) <= 0 {
		return resp, nil
	}
	// batch query shop group info
	shopGroupMap := make(map[string]*schema.ShopGroup)
	if constant.AllocateForecastRankTypeEnum[constant.ShopGroup] == rankType {
		rankCodeList := make([]string, 0)
		for _, dateRank := range dateRankList {
			rankCodeList = append(rankCodeList, dateRank.RankCode)
		}
		tmp, err := a.getShopGroupsByRankCode(ctx, allocation.CLientTag3PLMasking, rankCodeList)
		if err != nil {
			return nil, err
		}
		for key, value := range tmp {
			shopGroupMap[key] = value
		}
	}
	// batch query product
	maskingProduct, err := a.LpsApi.GetProductDetail(ctx, dateRankList[0].MaskingProductID)
	if maskingProduct == nil || err != nil {
		return nil, srerr.New(srerr.DataErr, err, "data err. masking product not found. masking product id:%v", dateRankList[0].MaskingProductID)
	}
	productsMap, err := a.getDateRankAllProductMap(ctx, overAllRankList, dateRankList)
	if err != nil {
		return nil, err
	}
	// convert overall rank info
	if len(overAllRankList) > 0 {
		dateOverallRank, dateOverallErr := a.convertDateOverallRank(ctx, rankType, dateList, maskingProduct, productsMap, overAllRankList)
		if dateOverallErr != nil {
			return nil, dateOverallErr
		}
		resp.List = append(resp.List, dateOverallRank)
	}

	// convert code rank info
	codeTableMap, dateTypeRankErr := a.convertDateTypeRank(ctx, rankType, dateList, maskingProduct, productsMap, shopGroupMap, dateRankList)
	if dateTypeRankErr != nil {
		return nil, dateTypeRankErr
	}

	for _, value := range codeTableMap {
		resp.List = append(resp.List, value)
	}
	return resp, nil
}

func (a AllocateRankServiceImpl) convertHistoricalRankToResp(ctx context.Context, rankType int, dateList []string, overAllRankList, historicalRankList []*model.AllocateHistoricalRankTab) (*schema.RankListResp, *srerr.Error) {
	resp := &schema.RankListResp{
		List: make([]*schema.RankList, 0),
	}
	if len(historicalRankList) <= 0 {
		return resp, nil
	}
	// batch query shop group info
	shopGroupMap := make(map[string]*schema.ShopGroup)
	if constant.AllocateForecastRankTypeEnum[constant.ShopGroup] == rankType {
		rankCodeList := make([]string, 0)
		for _, dateRank := range historicalRankList {
			rankCodeList = append(rankCodeList, dateRank.RankCode)
		}
		tmp, err := a.getShopGroupsByRankCode(ctx, allocation.CLientTag3PLMasking, rankCodeList)
		if err != nil {
			return nil, err
		}
		for key, value := range tmp {
			shopGroupMap[key] = value
		}
	}
	// batch query product
	maskingProduct, err := a.LpsApi.GetProductDetail(ctx, historicalRankList[0].MaskingProductID)
	if maskingProduct == nil || err != nil {
		return nil, srerr.New(srerr.DataErr, nil, "data err. masking product not found. masking product id:%v", historicalRankList[0].MaskingProductID)
	}
	productsMap, err := a.getHistoryAllProductMap(ctx, overAllRankList, historicalRankList)
	if err != nil {
		return nil, err
	}
	// convert overall rank info
	if len(overAllRankList) > 0 {
		historyOverallRank, historyErr := a.convertHistoryOverallRank(ctx, rankType, dateList, maskingProduct, productsMap, overAllRankList)
		if historyErr != nil {
			return nil, historyErr
		}
		resp.List = append(resp.List, historyOverallRank)
	}

	codeTableMap, historyTypeRankErr := a.convertHistoryTypeRank(ctx, rankType, dateList, maskingProduct, productsMap, shopGroupMap, historicalRankList)
	if historyTypeRankErr != nil {
		return nil, historyTypeRankErr
	}
	for _, value := range codeTableMap {
		resp.List = append(resp.List, value)
	}
	return resp, nil
}

func (a AllocateRankServiceImpl) convertForecastRankToResp(ctx context.Context, rankType int,
	dateList []string, taskConfig *schema.AllocateForecastTaskConfigResp,
	overAllRankList, blockedRankList, forecastRankList []*model.AllocateForecastRankTab) (
	*schema.RankListResp, *srerr.Error) {
	resp := &schema.RankListResp{
		List: make([]*schema.RankList, 0),
	}
	if len(forecastRankList) <= 0 && len(blockedRankList) <= 0 {
		return resp, nil
	}
	// batch query shop group info
	shopGroupMap := make(map[string]*schema.ShopGroup)
	if constant.AllocateForecastRankTypeEnum[constant.ShopGroup] == rankType && len(forecastRankList) > 0 {
		rankCodeList := make([]string, 0)
		for _, rank := range forecastRankList {
			rankCodeList = append(rankCodeList, rank.RankCode)
		}
		clientTag := allocation.CLientTag3PLMasking
		if taskConfig.BaseInfo.ShopGroupChannelPriorityToggle {
			clientTag = allocation.ClientTag3PLMaskingForecast
		}
		tmp, err := a.getShopGroupsByRankCode(ctx, uint64(clientTag), rankCodeList)
		if err != nil {
			return nil, err
		}
		for key, value := range tmp {
			shopGroupMap[key] = value
		}
	}
	// batch query product
	maskProductId := 0
	if len(forecastRankList) > 0 {
		maskProductId = forecastRankList[0].MaskingProductID
	} else if len(blockedRankList) > 0 {
		maskProductId = blockedRankList[0].MaskingProductID
	}
	maskingProduct, err := a.LpsApi.GetProductDetail(ctx, maskProductId)
	if maskingProduct == nil || err != nil {
		return nil, srerr.New(srerr.TypeConvertErr, nil, "convertForecastRankToResp err. masking product not found. masking product id:%v", maskProductId)
	}
	productsMap, err := a.getAllProductMap(ctx, overAllRankList, blockedRankList, forecastRankList)
	if err != nil {
		return nil, err
	}
	// convert overall rank info
	if len(overAllRankList) > 0 || len(blockedRankList) > 0 {
		overallRank, overallRankErr := a.convertOverallRank(ctx, rankType, dateList, maskingProduct, productsMap, overAllRankList, blockedRankList)
		if overallRankErr != nil {
			return nil, overallRankErr
		}
		resp.List = append(resp.List, overallRank)
	}
	// convert code rank info
	codeTableMap, typeRankErr := a.convertTypeRank(ctx, rankType, dateList, maskingProduct, productsMap, shopGroupMap, forecastRankList)
	if typeRankErr != nil {
		return nil, typeRankErr
	}
	for _, value := range codeTableMap {
		resp.List = append(resp.List, value)
	}
	return resp, nil
}

func (a AllocateRankServiceImpl) ExportDateRankList(ctx context.Context, req *schema.DateRankListReq) (*bytes.Buffer, *srerr.Error) {
	resp, err := a.GetDateRankList(ctx, req)
	if err != nil {
		return nil, err
	}
	file, err := generateResultRankExcel(resp.List)
	return file, err
}

func (a AllocateRankServiceImpl) ExportResultRankList(ctx context.Context, req *schema.ResultRankListReq) (*bytes.Buffer, *srerr.Error) {
	resp, err := a.GetResultRankList(ctx, req)
	if err != nil {
		return nil, err
	}
	file, err := generateResultRankExcel(resp.List)
	return file, err
}

func generateResultRankExcel(tables []*schema.RankList) (*bytes.Buffer, *srerr.Error) {
	file := excelize.NewFile()

	for i, table := range tables {
		sheetName := file.GetSheetName(i)
		if sheetName == "" {
			file.NewSheet(table.TableName)
		} else {
			file.SetSheetName(sheetName, table.TableName)
		}
		_ = file.SetSheetRow(table.TableName, "A1", &[]interface{}{"Fulfillment Channel", "Order Quantity", "ADO", "% Among All Orders"})
		for rowN, row := range table.DataList {
			line := rowN + 1
			_ = file.SetCellValue(table.TableName, fileutil.GetAxis(line, 0), row.DisplayName)
			_ = file.SetCellValue(table.TableName, fileutil.GetAxis(line, 1), row.OrderQuantity)
			_ = file.SetCellValue(table.TableName, fileutil.GetAxis(line, 2), row.AverageDailyOrder)
			_ = file.SetCellValue(table.TableName, fileutil.GetAxis(line, 3), fmt.Sprintf("%v%%", row.Percentage))
		}

	}
	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, "generateResultRankExcel", fErr)
	}
	return buffer, nil
}

func (a AllocateRankServiceImpl) convertOverallRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab,
	overAllRankList, blockedRankList []*model.AllocateForecastRankTab) (*schema.RankList, *srerr.Error) {
	// convert overall rank info
	overallTotalCount := 0
	overallProductCountMap := make(map[int]int)
	dateCount := len(dateList)
	allForecastRank := &schema.RankList{
		RankType:  rankType,
		RankCode:  allocation.AllRankCode,
		TableName: allocation.AllTableName,
	}
	dataList := make([]*schema.RankItem, 0)
	for _, overallRank := range overAllRankList {
		productId := overallRank.ProductId
		// construct table
		if count, ok := overallProductCountMap[productId]; ok {
			overallProductCountMap[productId] = count + overallRank.OrderQuantity
		} else {
			overallProductCountMap[productId] = overallRank.OrderQuantity
		}
		overallTotalCount = overallTotalCount + overallRank.OrderQuantity
	}
	// 加上blocked 数
	if len(blockedRankList) > 0 {
		overallTotalCount = overallTotalCount + blockedRankList[0].OrderQuantity
	}
	for key, value := range overallProductCountMap {
		item := a.convertRankItem(ctx, rankType, allocation.AllRankCode, productsMap, key, value, maskingProduct, dateCount, overallTotalCount)
		dataList = append(dataList, item)
	}
	// 加上blocked item
	if len(blockedRankList) > 0 {
		percentage, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(blockedRankList[0].OrderQuantity*100)/float64(overallTotalCount)), 64)
		item := &schema.RankItem{
			MaskingProductId: int(maskingProduct.ProductId),
			MaskProductName:  maskingProduct.SellerDisplayName,
			ProductId:        0,
			ProductName:      "Block",
			RankCode:         "",
			RankType:         constant.AllocateForecastRankTypeEnum[constant.Blocked],
			OrderQuantity:    blockedRankList[0].OrderQuantity,
			Percentage:       percentage,
			DisplayName:      "Block",
		}
		dataList = append(dataList, item)
	}
	// 加上sum item
	item := &schema.RankItem{
		MaskingProductId:  int(maskingProduct.ProductId),
		MaskProductName:   maskingProduct.SellerDisplayName,
		ProductId:         0,
		ProductName:       "Sum",
		RankCode:          "",
		RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
		OrderQuantity:     overallTotalCount,
		AverageDailyOrder: overallTotalCount / dateCount,
		Percentage:        100,
		DisplayName:       "Sum",
	}
	dataList = append(dataList, item)
	allForecastRank.DataList = dataList
	return allForecastRank, nil
}

func (a AllocateRankServiceImpl) convertTypeRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab, shopGroupMap map[string]*schema.ShopGroup,
	forecastRankList []*model.AllocateForecastRankTab) (map[string]*schema.RankList, *srerr.Error) {
	// convert code rank info
	dateCount := len(dateList)
	rankCodeTotalCount := make(map[string]int)
	rankCodeProductCount := make(map[string]map[int]int)
	for _, forecastRank := range forecastRankList {
		rankCode := forecastRank.RankCode
		if count, ok := rankCodeTotalCount[rankCode]; ok {
			rankCodeTotalCount[rankCode] = count + forecastRank.OrderQuantity
		} else {
			rankCodeTotalCount[rankCode] = forecastRank.OrderQuantity
		}
		if productCountMap, ok := rankCodeProductCount[rankCode]; ok {
			if count, ok := productCountMap[forecastRank.ProductId]; ok {
				productCountMap[forecastRank.ProductId] = count + forecastRank.OrderQuantity
			} else {
				productCountMap[forecastRank.ProductId] = forecastRank.OrderQuantity
			}
		} else {
			tempProductMap := make(map[int]int)
			tempProductMap[forecastRank.ProductId] = forecastRank.OrderQuantity
			rankCodeProductCount[rankCode] = tempProductMap
		}
	}
	codeTableMap := make(map[string]*schema.RankList)
	for rankCode, productCountMap := range rankCodeProductCount {
		// construct table
		codeTableMap = a.constructTableName(ctx, codeTableMap, rankType, rankCode, shopGroupMap)
		// add data list
		table := codeTableMap[rankCode]
		if table == nil {
			logger.CtxLogErrorf(ctx, "can't find rank code table. rankCode = %v", rankCode)
			continue
		}
		rankCodeTotal := rankCodeTotalCount[rankCode]
		for key, value := range productCountMap {
			item := a.convertRankItem(ctx, rankType, rankCode, productsMap, key, value, maskingProduct, dateCount, rankCodeTotal)
			table.DataList = append(table.DataList, item)
		}
		// 加上sum item
		item := &schema.RankItem{
			MaskingProductId:  int(maskingProduct.ProductId),
			MaskProductName:   maskingProduct.SellerDisplayName,
			ProductId:         0,
			ProductName:       "Sum",
			RankCode:          "",
			RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
			OrderQuantity:     rankCodeTotal,
			AverageDailyOrder: rankCodeTotal / dateCount,
			Percentage:        100,
			DisplayName:       "Sum",
		}
		table.DataList = append(table.DataList, item)
	}
	return codeTableMap, nil
}

func (a AllocateRankServiceImpl) getAllProductMap(ctx context.Context, overAllRankList, blockedRankList, typeRankList []*model.AllocateForecastRankTab) (map[string]*lpsclient.LogisticProductTab, *srerr.Error) {
	productIds := make([]int, 0)
	productMap := make(map[string]*lpsclient.LogisticProductTab)
	for _, item := range overAllRankList {
		productIds = append(productIds, item.ProductId)
	}
	for _, item := range blockedRankList {
		productIds = append(productIds, item.ProductId)
	}
	for _, item := range typeRankList {
		productIds = append(productIds, item.ProductId)
	}
	if len(productIds) > 0 {
		productMap, err := a.GetProductsByProductIds(ctx, productIds)
		return productMap, err
	}
	return productMap, nil
}

func (a AllocateRankServiceImpl) getHistoryAllProductMap(ctx context.Context, overAllRankList, typeRankList []*model.AllocateHistoricalRankTab) (map[string]*lpsclient.LogisticProductTab, *srerr.Error) {
	productIds := make([]int, 0)
	productMap := make(map[string]*lpsclient.LogisticProductTab)
	for _, item := range overAllRankList {
		productIds = append(productIds, item.ProductId)
	}
	for _, item := range typeRankList {
		productIds = append(productIds, item.ProductId)
	}
	if len(productIds) > 0 {
		productMap, err := a.GetProductsByProductIds(ctx, productIds)
		return productMap, err
	}
	return productMap, nil
}

func (a AllocateRankServiceImpl) convertHistoryOverallRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab,
	overAllRankList []*model.AllocateHistoricalRankTab) (*schema.RankList, *srerr.Error) {
	// convert overall rank info
	dateCount := len(dateList)
	overallTotalCount := 0
	overallProductCountMap := make(map[int]int)
	historyOverallRank := &schema.RankList{
		RankType:  rankType,
		RankCode:  allocation.AllRankCode,
		TableName: allocation.AllTableName,
	}
	dataList := make([]*schema.RankItem, 0)
	for _, overallRank := range overAllRankList {
		productId := overallRank.ProductId
		// construct table
		if count, ok := overallProductCountMap[productId]; ok {
			overallProductCountMap[productId] = count + overallRank.OrderQuantity
		} else {
			overallProductCountMap[productId] = overallRank.OrderQuantity
		}
		overallTotalCount = overallTotalCount + overallRank.OrderQuantity
	}
	for key, value := range overallProductCountMap {
		item := a.convertRankItem(ctx, rankType, allocation.AllRankCode, productsMap, key, value, maskingProduct, dateCount, overallTotalCount)
		dataList = append(dataList, item)
	}
	// 加上sum item
	item := &schema.RankItem{
		MaskingProductId:  int(maskingProduct.ProductId),
		MaskProductName:   maskingProduct.SellerDisplayName,
		ProductId:         0,
		ProductName:       "Sum",
		RankCode:          "",
		RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
		OrderQuantity:     overallTotalCount,
		AverageDailyOrder: overallTotalCount / dateCount,
		Percentage:        100,
		DisplayName:       "Sum",
	}
	dataList = append(dataList, item)
	historyOverallRank.DataList = dataList
	return historyOverallRank, nil
}

func (a AllocateRankServiceImpl) convertHistoryTypeRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab, shopGroupMap map[string]*schema.ShopGroup,
	historicalRankList []*model.AllocateHistoricalRankTab) (map[string]*schema.RankList, *srerr.Error) {
	// convert code rank info
	dateCount := len(dateList)
	rankCodeTotalCount := make(map[string]int)
	rankCodeProductCount := make(map[string]map[int]int)
	for _, historicalRank := range historicalRankList {
		rankCode := historicalRank.RankCode
		if count, ok := rankCodeTotalCount[rankCode]; ok {
			rankCodeTotalCount[rankCode] = count + historicalRank.OrderQuantity
		} else {
			rankCodeTotalCount[rankCode] = historicalRank.OrderQuantity
		}
		if productCountMap, ok := rankCodeProductCount[rankCode]; ok {
			if count, ok := productCountMap[historicalRank.ProductId]; ok {
				productCountMap[historicalRank.ProductId] = count + historicalRank.OrderQuantity
			} else {
				productCountMap[historicalRank.ProductId] = historicalRank.OrderQuantity
			}
		} else {
			tempProductMap := make(map[int]int)
			tempProductMap[historicalRank.ProductId] = historicalRank.OrderQuantity
			rankCodeProductCount[rankCode] = tempProductMap
		}
	}
	codeTableMap := make(map[string]*schema.RankList)
	for rankCode, productCountMap := range rankCodeProductCount {
		// construct table
		codeTableMap = a.constructTableName(ctx, codeTableMap, rankType, rankCode, shopGroupMap)
		// add data list
		table := codeTableMap[rankCode]
		if table == nil {
			logger.CtxLogErrorf(ctx, "can't find rank code table. rankCode = %v", rankCode)
			continue
		}
		rankCodeTotal := rankCodeTotalCount[rankCode]
		for key, value := range productCountMap {
			item := a.convertRankItem(ctx, rankType, rankCode, productsMap, key, value, maskingProduct, dateCount, rankCodeTotal)
			table.DataList = append(table.DataList, item)
		}
		// 加上sum item
		item := &schema.RankItem{
			MaskingProductId:  int(maskingProduct.ProductId),
			MaskProductName:   maskingProduct.SellerDisplayName,
			ProductId:         0,
			ProductName:       "Sum",
			RankCode:          "",
			RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
			OrderQuantity:     rankCodeTotal,
			AverageDailyOrder: rankCodeTotal / dateCount,
			Percentage:        100,
			DisplayName:       "Sum",
		}
		table.DataList = append(table.DataList, item)
	}
	return codeTableMap, nil
}

func (a AllocateRankServiceImpl) getDateRankAllProductMap(ctx context.Context, overAllRankList, dateRankList []*model.AllocateDateRankTab) (map[string]*lpsclient.LogisticProductTab, *srerr.Error) {
	productIds := make([]int, 0)
	productMap := make(map[string]*lpsclient.LogisticProductTab)
	for _, item := range overAllRankList {
		productIds = append(productIds, item.ProductId)
	}
	for _, item := range dateRankList {
		productIds = append(productIds, item.ProductId)
	}
	if len(productIds) > 0 {
		productMap, err := a.GetProductsByProductIds(ctx, productIds)
		return productMap, err
	}
	return productMap, nil
}

func (a AllocateRankServiceImpl) convertDateOverallRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab,
	overAllRankList []*model.AllocateDateRankTab) (*schema.RankList, *srerr.Error) {
	// convert overall rank info
	dateCount := len(dateList)
	overallTotalCount := 0
	overallProductCountMap := make(map[int]int)
	dateOverallRank := &schema.RankList{
		RankType:  rankType,
		RankCode:  allocation.AllRankCode,
		TableName: allocation.AllTableName,
	}
	dataList := make([]*schema.RankItem, 0)
	for _, overallRank := range overAllRankList {
		productId := overallRank.ProductId
		// construct table
		if count, ok := overallProductCountMap[productId]; ok {
			overallProductCountMap[productId] = count + overallRank.OrderQuantity
		} else {
			overallProductCountMap[productId] = overallRank.OrderQuantity
		}
		overallTotalCount = overallTotalCount + overallRank.OrderQuantity
	}
	for key, value := range overallProductCountMap {
		item := a.convertRankItem(ctx, rankType, allocation.AllRankCode, productsMap, key, value, maskingProduct, dateCount, overallTotalCount)
		dataList = append(dataList, item)
	}
	// 加上sum item
	item := &schema.RankItem{
		MaskingProductId:  int(maskingProduct.ProductId),
		MaskProductName:   maskingProduct.SellerDisplayName,
		ProductId:         0,
		ProductName:       "Sum",
		RankCode:          "",
		RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
		OrderQuantity:     overallTotalCount,
		AverageDailyOrder: overallTotalCount / dateCount,
		Percentage:        100,
		DisplayName:       "Sum",
	}
	dataList = append(dataList, item)
	dateOverallRank.DataList = dataList
	return dateOverallRank, nil
}

func (a AllocateRankServiceImpl) convertDateTypeRank(ctx context.Context, rankType int, dateList []string,
	maskingProduct *lpsclient.ProductDetailInfo, productsMap map[string]*lpsclient.LogisticProductTab, shopGroupMap map[string]*schema.ShopGroup,
	dateRankList []*model.AllocateDateRankTab) (map[string]*schema.RankList, *srerr.Error) {
	// convert code rank info
	dateCount := len(dateList)
	rankCodeTotalCount := make(map[string]int)
	rankCodeProductCount := make(map[string]map[int]int)
	for _, dateRank := range dateRankList {
		rankCode := dateRank.RankCode
		if count, ok := rankCodeTotalCount[rankCode]; ok {
			rankCodeTotalCount[rankCode] = count + dateRank.OrderQuantity
		} else {
			rankCodeTotalCount[rankCode] = dateRank.OrderQuantity
		}
		if productCountMap, ok := rankCodeProductCount[rankCode]; ok {
			if count, ok := productCountMap[dateRank.ProductId]; ok {
				productCountMap[dateRank.ProductId] = count + dateRank.OrderQuantity
			} else {
				productCountMap[dateRank.ProductId] = dateRank.OrderQuantity
			}
		} else {
			tempProductMap := make(map[int]int)
			tempProductMap[dateRank.ProductId] = dateRank.OrderQuantity
			rankCodeProductCount[rankCode] = tempProductMap
		}
	}
	codeTableMap := make(map[string]*schema.RankList)
	for rankCode, productCountMap := range rankCodeProductCount {
		// construct table
		codeTableMap = a.constructTableName(ctx, codeTableMap, rankType, rankCode, shopGroupMap)
		// add data list
		table := codeTableMap[rankCode]
		if table == nil {
			logger.CtxLogErrorf(ctx, "can't find rank code table. rankCode = %v", rankCode)
			continue
		}
		rankCodeTotal := rankCodeTotalCount[rankCode]
		for key, value := range productCountMap {
			item := a.convertRankItem(ctx, rankType, rankCode, productsMap, key, value, maskingProduct, dateCount, rankCodeTotal)
			table.DataList = append(table.DataList, item)
		}
		// 加上sum item
		item := &schema.RankItem{
			MaskingProductId:  int(maskingProduct.ProductId),
			MaskProductName:   maskingProduct.SellerDisplayName,
			ProductId:         0,
			ProductName:       "Sum",
			RankCode:          "",
			RankType:          constant.AllocateForecastRankTypeEnum[constant.Sum],
			OrderQuantity:     rankCodeTotal,
			AverageDailyOrder: rankCodeTotal / dateCount,
			Percentage:        100,
			DisplayName:       "Sum",
		}
		table.DataList = append(table.DataList, item)
	}
	return codeTableMap, nil
}

func (a AllocateRankServiceImpl) constructTableName(ctx context.Context, codeTableMap map[string]*schema.RankList,
	rankType int, rankCode string, shopGroupMap map[string]*schema.ShopGroup) map[string]*schema.RankList {
	if _, ok := codeTableMap[rankCode]; !ok {
		tempTable := &schema.RankList{
			RankType: rankType,
			RankCode: rankCode,
		}
		if constant.AllocateForecastRankTypeEnum[constant.ShopGroup] == rankType {
			shopGroup, exist := shopGroupMap[rankCode]
			if !exist {
				logger.CtxLogErrorf(ctx, "can't find shop group by shop_group_id = %v", rankCode)
				return codeTableMap
			}
			tempTable.TableName = fmt.Sprintf("Shop Group-%v-%s Allocation Result", shopGroup.GroupId, shopGroup.GroupName)
		} else if constant.AllocateForecastRankTypeEnum[constant.Route] == rankType {
			tempTable.TableName = fmt.Sprintf("Route-%s Allocation Result", rankCode)
		} else if constant.AllocateForecastRankTypeEnum[constant.Zone] == rankType {
			tempTable.TableName = fmt.Sprintf("Zone-%s Allocation Result", rankCode)
		}
		dataList := make([]*schema.RankItem, 0)
		tempTable.DataList = dataList
		codeTableMap[rankCode] = tempTable
	}
	return codeTableMap
}

func (a AllocateRankServiceImpl) convertRankItem(ctx context.Context, rankType int, rankCode string,
	productsMap map[string]*lpsclient.LogisticProductTab, productId int, quantity int,
	maskingProduct *lpsclient.ProductDetailInfo, dateCount int, overallTotalCount int) *schema.RankItem {
	productName := ""
	product, exist := productsMap[strconv.Itoa(productId)]
	if exist {
		productName = product.SellerDisplayName
	} else {
		logger.CtxLogErrorf(ctx, "can't find product name. product id = %v", productId)
	}
	percentage, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(quantity*100)/float64(overallTotalCount)), 64)
	item := &schema.RankItem{
		MaskingProductId:  int(maskingProduct.ProductId),
		MaskProductName:   maskingProduct.SellerDisplayName,
		ProductId:         productId,
		ProductName:       productName,
		RankCode:          rankCode,
		RankType:          rankType,
		OrderQuantity:     quantity,
		AverageDailyOrder: quantity / dateCount,
		Percentage:        percentage,
		DisplayName:       fmt.Sprintf("%v-%v", productId, productName),
	}
	return item
}
