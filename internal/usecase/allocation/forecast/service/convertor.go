package service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
)

var (
	overallHeader         = []string{"Fulfillment Product", "Order quantity", "ADO", "Percentage", "Total Shipping Fee", "Cost Saving"}
	zoneRouteResultHeader = []string{"Zone Code", "Route Code", "Fulfillment Product", "Order quantity", "ADO", "Percentage"}
)

func (b *BatchAllocateForecastImpl) convertToTab(ctx context.Context, req schema.BAForecastCreateUpdateReq) (model.AllocateForecastTaskConfigTab, *srerr.Error) {
	tab := model.AllocateForecastTaskConfigTab{}
	baseInfo := req.BaseInfo
	tab.TaskName = baseInfo.TaskName
	tab.AllocationMethod = baseInfo.AllocationMethod
	tab.MaskingProductID = baseInfo.MaskProductId
	tab.LocalSoftCriteriaToggle = objutil.Bool2Int(baseInfo.LocalSoftCriteriaToggle)
	tab.RunSoftRuleOnlyToggle = objutil.Bool2Int(baseInfo.RunSoftRuleOnlyToggle)
	tab.ShopGroupChannelPriorityToggle = objutil.Bool2Int(baseInfo.ShopGroupChannelPriorityToggle)
	tab.Status = baseInfo.Status
	currentTime := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	tab.CTime = currentTime
	tab.MTime = currentTime
	tab.RecalculateShippingFeeToggle = objutil.Bool2Int(baseInfo.ReCalcFee)
	//转换batch order paid time
	orderPaidTime := req.BaseInfo.OrderPaidTime
	orderPaidTimeBytes, err := jsoniter.Marshal(orderPaidTime)
	if err != nil {
		return tab, srerr.New(srerr.ParamErr, nil, "order_paid_time convert error. err:%v", err)
	}
	tab.OrderPaidTime = orderPaidTimeBytes
	operateBy, _ := apiutil.GetUserInfo(ctx)
	tab.OperatedBy = operateBy
	var batchSizeListBytes []byte
	if len(req.BatchSizeList) != 0 {
		batchSizeListBytes, err = jsoniter.Marshal(req.BatchSizeList)
		if err != nil {
			tab.BatchSizeList = batchSizeListBytes
			logger.CtxLogErrorf(ctx, "convertDraftTab|convert batch size list err:%v, will set it empty", err)
		}
	}
	tab.BatchSizeList = batchSizeListBytes

	var batchAllocateRuleConfigBytes []byte
	//打开，由业务配置；关闭，获取线上规则
	if req.BaseInfo.LocalSoftCriteriaToggle {
		batchAllocateRuleConfigBytes, err = jsoniter.Marshal(req.BatchAllocationRuleConfig)
		if err != nil {
			batchAllocateRuleConfigBytes = make([]byte, 0)
			logger.CtxLogErrorf(ctx, "convertDraftTab|convert batch allocate rule config err:%v, will set it empty", err)
		}
	} else {
		//todo：SSCSMR-1698：等实时分单上线后补齐-》获取live配置
		batchAllocateRuleConfigBytes = make([]byte, 0)
	}
	tab.BatchAllocationRuleConfig = batchAllocateRuleConfigBytes

	//json格式不需要初始化值
	var batchProductPriorityBytes []byte
	if len(req.ProductPriorityConfigs) != 0 {
		for i := 0; i < len(req.ProductPriorityConfigs); i++ {
			req.ProductPriorityConfigs[i].MaskProductId = req.BaseInfo.MaskProductId
		}
		batchProductPriorityBytes, err = jsoniter.Marshal(req.ProductPriorityConfigs)
		if err != nil {
			batchProductPriorityBytes = make([]byte, 0)
			logger.CtxLogErrorf(ctx, "convertDraftTab|convert batch allocate product priority config err:%v, will set it empty", err)
		}
	}
	tab.ProductPriorityConfigs = batchProductPriorityBytes

	return tab, nil
}

func convertHistoryToExcelTabs(ctx context.Context, historyInfo schema.BAHistoricalInfoResp) (overAllTabs [][]string, zrTabs [][]string) {
	//按类型排序
	sort.Slice(historyInfo.List, func(i, j int) bool {
		return historyInfo.List[i].ZRCodeType > historyInfo.List[j].ZRCodeType
	})
	for _, tempInfo := range historyInfo.List {
		//装填over all tabs
		if tempInfo.ResultType == constant.ResultTypeOverAll {
			for _, tempResult := range tempInfo.ResultList {
				overAllTabs = append(overAllTabs, []string{
					strconv.FormatUint(tempResult.FulfillmentChannel, 10), //product name
					strconv.FormatInt(tempResult.OrderQuantity, 10),       //order quantity
					strconv.FormatInt(tempResult.Ado, 10),                 //ado
					tempResult.OrderCountRatio,                            //percentage
					decimal.NewFromFloat(tempResult.TotalShippingFee).String(),
				})
			}
		} else {
			//装填zone route tabs
			for _, tempResult := range tempInfo.ResultList {
				var tempTab []string
				if tempInfo.ZRCodeType == constant.UpperZone {
					tempTab = []string{tempInfo.ZRCode, ""} //第一列是zone code， 第二列是route code
				} else {
					tempTab = []string{"", tempInfo.ZRCode}
				}
				leftStrings := []string{
					strconv.FormatUint(tempResult.FulfillmentChannel, 10), //product name
					strconv.FormatInt(tempResult.OrderQuantity, 10),       //order quantity
					strconv.FormatInt(tempResult.Ado, 10),                 //ado
					tempResult.OrderCountRatio,                            //percentage
				}
				tempTab = append(tempTab, leftStrings...)
				zrTabs = append(zrTabs, tempTab)
			}
		}
	}
	return overAllTabs, zrTabs
}

func convertResultToExcel(ctx context.Context, forecastInfo schema.BAForecastInfoResp, levelType int) (*excelize.File, *srerr.Error) {
	//1.转换成Excel tab
	batchSizeTabs := convertToBatchSizeTabs(ctx, forecastInfo, levelType)
	if len(batchSizeTabs) == 0 {
		logger.CtxLogErrorf(ctx, "convert to excel tabs err, got empty excel tabs")
		return nil, srerr.New(srerr.ParseExcelError, nil, "convert to excel tabs err, got empty excel tabs")
	}
	//2.生成Excel对象
	f, gErr := generateExcel(ctx, batchSizeTabs)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "generate excel err:%v", gErr)
		return nil, gErr
	}

	return f, nil
}

func convertToBatchSizeTabs(ctx context.Context, forecastInfo schema.BAForecastInfoResp, levelType int) map[string][][]string {
	var results map[string][][]string
	if levelType == constant.LevelTypeOverall {
		overAllTabsMap := make(map[string][][]string, 0)
		for _, overallResult := range forecastInfo.OverallResultList {
			//记录batch size内每一行的细节
			overAllTabs := [][]string{
				overallHeader,
			}
			for _, resultDetail := range overallResult.ResultDetailList {
				overAllTabs = append(overAllTabs, []string{
					strconv.FormatUint(resultDetail.FulfillmentProduct, 10),
					strconv.FormatInt(resultDetail.OrderQuantity, 10),
					strconv.FormatInt(resultDetail.Ado, 10),
					resultDetail.Percentage,
					resultDetail.ShippingFee,
					resultDetail.CostSaving,
				})
			}
			//添加batch size和细节的映射
			overAllTabsMap[overallResult.BatchSizeName] = overAllTabs
		}
		results = overAllTabsMap
	} else if levelType == constant.LevelTypeZoneRoute {
		// zone route都要导出
		zoneRouteTabsMap := make(map[string][][]string, 0)
		for _, zoneResult := range forecastInfo.ZoneResultList {
			zoneTabs := [][]string{
				zoneRouteResultHeader,
			}
			for _, resultDetail := range zoneResult.BatchSizeResultList {
				zoneTabs = append(zoneTabs, []string{
					resultDetail.ZoneCode,
					"", //empty route code
					strconv.FormatUint(resultDetail.FulfillmentProduct, 10), //fulfillment product
					strconv.FormatInt(resultDetail.BatchQuantity, 10),
					strconv.FormatInt(resultDetail.ADO, 10), //ADO
					resultDetail.Percentage,                 //percentage
				})
			}
			zoneRouteTabsMap[zoneResult.BatchSizeName] = zoneTabs
		}
		for _, routeResult := range forecastInfo.RouteResultList {
			var routeTabs [][]string
			for _, resultDetail := range routeResult.BatchSizeResultList {
				routeTabs = append(routeTabs, []string{
					"", //empty zone code
					resultDetail.RouteCode,
					strconv.FormatUint(resultDetail.FulfillmentProduct, 10),
					strconv.FormatInt(resultDetail.BatchQuantity, 10),
					strconv.FormatInt(resultDetail.ADO, 10),
					resultDetail.Percentage,
				})
			}

			// 如果是空的话要添加Header
			if len(zoneRouteTabsMap[routeResult.BatchSizeName]) == 0 {
				zoneRouteTabsMap[routeResult.BatchSizeName] = append(zoneRouteTabsMap[routeResult.BatchSizeName], zoneRouteResultHeader)
			}
			zoneRouteTabsMap[routeResult.BatchSizeName] = append(zoneRouteTabsMap[routeResult.BatchSizeName], routeTabs...)
		}
		results = zoneRouteTabsMap
	}
	return results
}

func generateExcel(ctx context.Context, batchSizeTabs map[string][][]string) (*excelize.File, *srerr.Error) {
	var (
		aErr error
		file = excelize.NewFile()
	)
	for batchSizeName, tabs := range batchSizeTabs {
		if len(tabs) < 2 {
			logger.CtxLogInfof(ctx, "generateExcel|empty tabs, continue this batch size:%v", batchSizeName)
		}
		if file, aErr = fileutil.AppendExcel(ctx, file, tabs[0], tabs[1:], batchSizeName, false); aErr != nil {
			logger.CtxLogErrorf(ctx, "generateExcel| append excel err:%v", aErr)
			continue
		}
	}
	file.DeleteSheet(file.GetSheetName(0)) //删除默认页
	return file, nil
}
