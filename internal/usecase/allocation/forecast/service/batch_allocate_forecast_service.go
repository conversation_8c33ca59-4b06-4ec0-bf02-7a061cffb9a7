package service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"strings"
	"sync"
)

const (
	workerNum = 64
	S3TimeOut = 10
	typeZone  = 2
	allInfo   = "all"
)

var (
	defZoneHeaders        = []string{"State", "City", "District", "Street", "Postcode", "Zone Code"}
	targetVolZoneHeaders  = []string{"Zone Code", "Component Product ID", "Group Code", "Min Vol Dest", "Max Vol Dest", "Max COD vol dest", "Max Bulky vol dest", "Max high-value vol dest", "Max DG vol dest"}
	defRouteHeaders       = []string{"Origin state", "Origin city", "Origin district", "Origin street", "Origin postcode", "Destination state", "Destination city", "Destination district", "Destination street", "Destination postcode", "Route Code"}
	targetVolRouteHeaders = []string{"Route Code", "Component Product ID", "Group Code", "Min Vol", "Max Vol", "Max COD Vol", "Max Bulky Vol", "Max High-Value Vol", "Max DG vol"}
	historyInfoFuncMap    = map[string]func(tabs []model.AllocateHistoryOutlineTab, dayLength int64, req schema.BAHistoricalInfoReq) []schema.HistoricalInfo{}
	once                  = sync.Once{}
	historyOverallHeader  = []string{"Fulfillment Product", "Order quantity", "ADO", "Percentage", "Total Shipping Fee"}
	historyZRLevelHeader  = []string{"Zone Code", "Route Code", "Fulfillment Product", "Order quantity", "ADO", "Percentage"}
)

type BatchAllocateForecastService interface {
	CreateBatchAllocationTask(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error
	UpdateBatchAllocationTask(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error
	GetHistoricalCodeList(ctx context.Context, req schema.BAHistoricalCodeReq) ([]string, *srerr.Error)
	GetHistoricalInfo(ctx context.Context, req schema.BAHistoricalInfoReq) (schema.BAHistoricalInfoResp, *srerr.Error)
	GetForecastCodeList(ctx context.Context, req schema.BAForecastCodeReq) ([]string, *srerr.Error)
	GetForecastInfo(ctx context.Context, req schema.BAForecastInfoReq) (schema.BAForecastInfoResp, *srerr.Error)
	GetExportForecastInfo(ctx context.Context, req schema.BAForecastInfoReq) (schema.BAForecastInfoResp, *srerr.Error)
	ExportHistory(ctx context.Context, req schema.BAExportHistoryReq) (schema.BAExportHistoryResp, *srerr.Error)
	ExportForecastResult(ctx context.Context, req schema.BAExportForecastResultReq) (schema.BAExportForecastResultResp, *srerr.Error)
	ParseRouteExcel(ctx context.Context, forecastId uint64, maskProductID int, defFileUrl, volFileUrl string) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error)
	ParseZoneExcel(ctx context.Context, forecastId uint64, maskProductID int, defFileUrl, volFileUrl string, parseType int) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error)
}

type BatchAllocateForecastImpl struct {
	AllocateForecastTaskConfigRepo  repo.AllocateForecastTaskConfigRepo
	AddrRepo                        address.AddrRepo
	LpsApi                          lpsclient.LpsApi
	BatchAllocateForecastVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo
	AllocateDateRankRepo            repo.AllocateDateRankRepo
	BatchAllocateForecastRepo       repo.BatchAllocateForecastRepo
	AllocateHistoryOutlineRepo      repo.AllocateHistoryOutlineRepo
	BatchUnitFeeResultRepo          repo.BatchUnitFeeResultRepo
	BatchResultRepo                 repo.BatchAllocateForecastUnitResultRepo
	BatchSubtaskOutlineRepo         repo.BatchAllocateSubTaskOutlineRepo
	BatchAllocateOrderRepo          order.BatchAllocateOrderRepo
	PickupEffCounter                pickup_efficiency_counter.PickupEffCounter
}

func NewBatchAllocateForecastImpl(
	AllocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	AddrRepo address.AddrRepo,
	LpsApi lpsclient.LpsApi,
	BatchAllocateForecastVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo,
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo,
	AllocateDateRankRepo repo.AllocateDateRankRepo,
	AllocateHistoryOutlineRepo repo.AllocateHistoryOutlineRepo,
	BatchUnitFeeResultRepo repo.BatchUnitFeeResultRepo,
	BatchResultRepo repo.BatchAllocateForecastUnitResultRepo,
	BatchSubtaskOutlineRepo repo.BatchAllocateSubTaskOutlineRepo,
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo,
	PickupEffCounter pickup_efficiency_counter.PickupEffCounter,
) *BatchAllocateForecastImpl {
	b := &BatchAllocateForecastImpl{
		AllocateForecastTaskConfigRepo:  AllocateForecastTaskConfigRepo,
		AddrRepo:                        AddrRepo,
		LpsApi:                          LpsApi,
		BatchAllocateForecastVolumeRepo: BatchAllocateForecastVolumeRepo,
		BatchAllocateForecastRepo:       BatchAllocateForecastRepo,
		AllocateDateRankRepo:            AllocateDateRankRepo,
		AllocateHistoryOutlineRepo:      AllocateHistoryOutlineRepo,
		BatchUnitFeeResultRepo:          BatchUnitFeeResultRepo,
		BatchResultRepo:                 BatchResultRepo,
		BatchSubtaskOutlineRepo:         BatchSubtaskOutlineRepo,
		BatchAllocateOrderRepo:          BatchAllocateOrderRepo,
		PickupEffCounter:                PickupEffCounter,
	}

	//注册方法
	once.Do(func() {
		historyInfoFuncMap[constant.ZoneRouteCodeAll] = b.GetAllAndZRHistoryList
		historyInfoFuncMap[constant.ZoneRouteCodeNotAll] = b.GetZRHistoryInfoList
	})

	return b
}

func getHistoryInfoFunc(funcType string) func(tabs []model.AllocateHistoryOutlineTab, dayLength int64, req schema.BAHistoricalInfoReq) []schema.HistoricalInfo {
	if strings.ToLower(funcType) == constant.ZoneRouteCodeAll || funcType == "" {
		return historyInfoFuncMap[constant.ZoneRouteCodeAll]
	}
	return historyInfoFuncMap[constant.ZoneRouteCodeNotAll]
}

func (b *BatchAllocateForecastImpl) CreateBatchAllocationTask(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error {
	//校验
	if vErr := b.ValidateRequest(ctx, req); vErr != nil {
		return vErr
	}

	return b.CreateOrUpdateTask(ctx, req, constant.OperationCreate)
}

func (b *BatchAllocateForecastImpl) UpdateBatchAllocationTask(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error {
	//1.校验
	if vErr := b.ValidateRequest(ctx, req); vErr != nil {
		return vErr
	}
	//2.检查id是否合法
	_, gErr := b.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, int64(req.BaseInfo.Id))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateBatchAllocationTask|task id:%v, get task err:%v", req.BaseInfo.Id, gErr)
		return gErr
	}

	return b.CreateOrUpdateTask(ctx, req, constant.OperationUpdate)
}

func (b *BatchAllocateForecastImpl) CreateOrUpdateTask(ctx context.Context, req schema.BAForecastCreateUpdateReq, operationType string) *srerr.Error {
	//1.convert tab
	tab, cErr := b.convertToTab(ctx, req)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateOrUpdateTask|convert request to tab err:%v", cErr)
		return srerr.With(srerr.ParamErr, nil, cErr)
	}
	var err error
	if operationType == constant.OperationCreate {
		err = dbutil.Insert(ctx, &tab, dbutil.ModelInfo{})
	} else if operationType == constant.OperationUpdate {
		value := b.convertTabToMap(ctx, tab)
		err = dbutil.Update(ctx, model.AllocateForecastTaskConfigHook, map[string]interface{}{"id = ?": req.BaseInfo.Id}, value, dbutil.ModelInfo{})
	} else {
		logger.CtxLogErrorf(ctx, "CreateOrUpdateTask|wrong operation type:%v", operationType)
		err = fmt.Errorf("wrong operation type")
	}

	return srerr.With(srerr.DatabaseErr, nil, err)
}

func (b *BatchAllocateForecastImpl) GetHistoricalCodeList(ctx context.Context, req schema.BAHistoricalCodeReq) ([]string, *srerr.Error) {
	days, tErr := req.OrderPaidTime.OrderPaidTimeToTimeList()
	if tErr != nil {
		logger.CtxLogErrorf(ctx, "GetHistoricalCodeList| convert order paid time err:%v", tErr)
		return nil, tErr
	}
	codeList := make([]string, 0)
	for _, day := range days {
		condition := map[string]interface{}{
			"mask_product_id = ?": req.MaskProductId,
			"date_unix = ?":       day.Unix(),
		}
		switch req.ZoneRouteType {
		case constant.ReqCodeTypeRoute:
			condition["result_type = ?"] = model.AllocateHistoryTypeRoute
		case constant.ReqCodeTypeZone:
			condition["result_type = ?"] = model.AllocateHistoryTypeZone
		case constant.ReqCodeTypeZoneAndRoute:
			condition["result_type in (?)"] = []int{model.AllocateHistoryTypeZone, model.AllocateHistoryTypeRoute}
		}
		outlineTabs, gErr := b.AllocateHistoryOutlineRepo.GetHistoryTabList(ctx, condition)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetBAHistoricalCodeList|get batch allocate history outline list err:%v", gErr)
			continue
		}
		tempCodeList := b.convertToCodeList(outlineTabs)
		codeList = append(codeList, tempCodeList...)
	}
	//去重
	codeMap := make(map[string]struct{}, 0)
	for _, code := range codeList {
		if _, ok := codeMap[code]; !ok {
			codeMap[code] = struct{}{}
		}
	}
	results := []string{"All"}
	for code := range codeMap {
		results = append(results, code)
	}
	return results, nil
}

func (b *BatchAllocateForecastImpl) GetHistoricalInfo(ctx context.Context, req schema.BAHistoricalInfoReq) (schema.BAHistoricalInfoResp, *srerr.Error) {
	resp := schema.BAHistoricalInfoResp{}
	//1.检索DB
	condition := make(map[string]interface{}, 0)
	condition["mask_product_id = ?"] = req.MaskProductId
	//1.3 遍历日期，获取结果
	tabs := make([]model.AllocateHistoryOutlineTab, 0)
	days, tErr := req.OrderPaidTime.OrderPaidTimeToTimeList()
	if tErr != nil {
		logger.CtxLogErrorf(ctx, "GetHistoricalInfo| convert order paid time err:%v", tErr)
		return resp, tErr
	}
	for _, day := range days {
		condition["date_unix = ?"] = day.Unix()
		tempTabs, gErr := b.AllocateHistoryOutlineRepo.GetHistoryTabList(ctx, condition)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetHistoricalInfo|condition:%v get date rank tabs err:%v", condition, gErr)
			continue
		}
		tabs = append(tabs, tempTabs...)
	}
	//2.组装参数 -- 先全量检索，然后再在内存中按req type分数据，然后再统计
	//根据request中code类型获取对应的func
	historyInfoFunc := getHistoryInfoFunc(req.ZoneRouteCode)
	//调用func获取对应的数据
	dayLength := len(days)
	historyInfoList := historyInfoFunc(tabs, int64(dayLength), req)
	resp.List = historyInfoList
	return resp, nil
}

func (b *BatchAllocateForecastImpl) GetForecastCodeList(ctx context.Context, req schema.BAForecastCodeReq) ([]string, *srerr.Error) {
	codeList := []string{"All"}
	//todo:SSCSMR-1698:Pm沟通后说当前只展示overall信息
	////1. get sub task list
	//subTaskList, gErr := b.BatchAllocateForecastRepo.GetSubTaskList(ctx, req.BatchAllocateForecastId)
	//if gErr != nil {
	//	logger.CtxLogErrorf(ctx, "GetForecastCodeList|req:%v, get sub task list err:%v", req, gErr)
	//	return nil, gErr
	//}
	////2.get sub task unit result list
	//var unitResultList []model.BAForecastUnitResultTab
	//for _, subtask := range subTaskList {
	//	tempUnitResultList, gErr := b.BatchAllocateForecastRepo.GetSubTaskUnitResultListByCondition(ctx, map[string]interface{}{"sub_task_id = ?": subtask.Id})
	//	if gErr != nil {
	//		logger.CtxLogErrorf(ctx, "GetForecastCodeList|sub task id:%v, get unit result list err:%v", subtask.Id, gErr)
	//		continue
	//	}
	//	unitResultList = append(unitResultList, tempUnitResultList...)
	//}
	////3.get zone/route code
	//codeList := []string{"All"}
	//codeMap := make(map[string]struct{}, 0)
	//for _, unitResult := range unitResultList {
	//	if unitResult.ResultType != constant.ResultTypeZone || unitResult.ZoneCode == "" {
	//		continue
	//	}
	//	//去重并追加zone code到结果list中
	//	if _, ok := codeMap[unitResult.ZoneCode]; !ok {
	//		codeList = append(codeList, unitResult.ZoneCode)
	//		codeMap[unitResult.ZoneCode] = struct{}{}
	//	}
	//}
	return codeList, nil
}

func (b *BatchAllocateForecastImpl) GetForecastInfo(ctx context.Context, req schema.BAForecastInfoReq) (schema.BAForecastInfoResp, *srerr.Error) {
	logger.CtxLogInfof(ctx, "GetForecastInfo|req:%v", req)
	resp := schema.BAForecastInfoResp{}
	//1.检索数据
	//获取main task,从而获取时间长度
	mainTask, gErr := b.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, int64(req.BatchAllocateForecastId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|req:%v, get main task err:%v", req, gErr)
		return resp, gErr
	}

	orderPaidTime := schema.OrderPaidTime{}
	if err := jsoniter.Unmarshal(mainTask.OrderPaidTime, &orderPaidTime); err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|unmarshal order paid time err:%v", err)
		return resp, srerr.New(srerr.ParamErr, nil, "unmarshal order paid time err")
	}
	days, cErr := orderPaidTime.OrderPaidTimeToTimeList()
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|convert order paid time to time list err:%v", cErr)
		return resp, cErr
	}
	dayLength := len(days)
	//1.1 检索sub task
	subtaskList, gErr := b.BatchAllocateForecastRepo.GetSubTaskList(ctx, req.BatchAllocateForecastId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|req:%v, get sub task list err:%v", req, gErr)
		return resp, gErr
	}
	historyReq := schema.BAHistoricalInfoReq{
		MaskProductId: uint64(mainTask.MaskingProductID),
		ZoneRouteType: typeZone,
		ZoneRouteCode: allInfo,
		OrderPaidTime: orderPaidTime,
	}
	resp = b.GetOverAllList(ctx, req, subtaskList, int64(dayLength), historyReq)
	return resp, nil
}

func (b *BatchAllocateForecastImpl) GetExportForecastInfo(ctx context.Context, req schema.BAForecastInfoReq) (schema.BAForecastInfoResp, *srerr.Error) {
	logger.CtxLogInfof(ctx, "GetForecastInfo|req:%v", req)
	resp := schema.BAForecastInfoResp{}
	//1.检索数据
	//获取main task,从而获取时间长度
	mainTask, gErr := b.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, int64(req.BatchAllocateForecastId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|req:%v, get main task err:%v", req, gErr)
		return resp, gErr
	}

	orderPaidTime := schema.OrderPaidTime{}
	if err := jsoniter.Unmarshal(mainTask.OrderPaidTime, &orderPaidTime); err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|unmarshal order paid time err:%v", err)
		return resp, srerr.New(srerr.ParamErr, nil, "unmarshal order paid time err")
	}
	days, cErr := orderPaidTime.OrderPaidTimeToTimeList()
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|convert order paid time to time list err:%v", cErr)
		return resp, cErr
	}
	dayLength := len(days)
	//1.1 检索sub task
	subtaskList, gErr := b.BatchAllocateForecastRepo.GetSubTaskList(ctx, req.BatchAllocateForecastId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastInfo|req:%v, get sub task list err:%v", req, gErr)
		return resp, gErr
	}
	resp = b.GetOverAllAndZRList(ctx, req, subtaskList, int64(dayLength))
	return resp, nil
}

func (b *BatchAllocateForecastImpl) ExportHistory(ctx context.Context, req schema.BAExportHistoryReq) (schema.BAExportHistoryResp, *srerr.Error) {
	//定义resp
	resp := schema.BAExportHistoryResp{}
	//获取历史订单信息
	infoReq := schema.BAHistoricalInfoReq{
		MaskProductId: req.MaskProductId,
		OrderPaidTime: req.OrderPaidTime,
		ZoneRouteType: req.ExportCodeType,
		ZoneRouteCode: req.ExportCode,
	}
	historyInfoResp, gErr := b.GetHistoricalInfo(ctx, infoReq)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ExportHistory|get history info err:%v", gErr)
		return resp, gErr
	}
	if len(historyInfoResp.List) == 0 {
		return resp, srerr.New(srerr.HistoryInfoExportErr, nil, "empty result, no need export")
	}
	//转换历史订单信息到Excel文件
	overAllTabs, zrTabs := convertHistoryToExcelTabs(ctx, historyInfoResp)

	overAllSheetName := "Historical result: Region level export"
	f, mErr := fileutil.MakeExcel(ctx, historyOverallHeader, overAllTabs, overAllSheetName)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ExportHistory| make over all excel err:%v", mErr)
		return resp, srerr.With(srerr.HistoryInfoExportErr, "make over all excel err", mErr)
	}
	zrSheetName := "Historical Result: Zone level export"
	f, mErr = fileutil.AppendExcel(ctx, f, historyZRLevelHeader, zrTabs, zrSheetName, false)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ExportHistory| make zone route excel err:%v", mErr)
		return resp, srerr.With(srerr.HistoryInfoExportErr, "make zone route excel err", mErr)
	}
	//删掉sheet1
	f.DeleteSheet(f.GetSheetName(0))

	//upload excel to s3
	excelBuffer, wErr := f.WriteToBuffer()
	if wErr != nil {
		return resp, srerr.With(srerr.HistoryInfoExportErr, nil, wErr)
	}
	exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
	s3Key := fmt.Sprintf("Historical Result:%v.xlsx", exportTime)
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, excelBuffer); err != nil {
		return resp, srerr.With(srerr.HistoryInfoExportErr, "failed to upload s3", err)
	}
	url := fileutil.GetS3Url(ctx, bucket, s3Key)
	resp.Url = url
	return resp, nil
}

func (b *BatchAllocateForecastImpl) ExportForecastResult(ctx context.Context, req schema.BAExportForecastResultReq) (schema.BAExportForecastResultResp, *srerr.Error) {
	//定义resp
	resp := schema.BAExportForecastResultResp{}
	//获取历史订单信息
	infoReq := schema.BAForecastInfoReq{
		BatchAllocateForecastId: req.BatchAllocateForecastId,
	}
	forecastInfoResp, gErr := b.GetExportForecastInfo(ctx, infoReq)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ExportForecastResult|get forecast result info err:%v", gErr)
		return resp, gErr
	}
	if len(forecastInfoResp.OverallResultList) == 0 && len(forecastInfoResp.ZoneResultList) == 0 && len(forecastInfoResp.RouteResultList) == 0 {
		return resp, srerr.New(srerr.ForecastInfoExportErr, nil, "empty result, no need export")

	}

	//转换预测结果信息到Excel文件
	f, cErr := convertResultToExcel(ctx, forecastInfoResp, req.LevelType)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "req level:%v, result info:%v, convert to excel err:%v", req.LevelType, forecastInfoResp, cErr)
		return resp, cErr
	}

	//upload excel to s3
	excelBuffer, wErr := f.WriteToBuffer()
	if wErr != nil {
		return resp, srerr.With(srerr.HistoryInfoExportErr, nil, wErr)
	}
	exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
	s3Key := fmt.Sprintf("Forecast Result:%v.xlsx", exportTime)
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, excelBuffer); err != nil {
		return resp, srerr.With(srerr.HistoryInfoExportErr, "failed to upload s3", err)
	}
	url := fileutil.GetS3Url(ctx, bucket, s3Key)
	resp.Url = url
	return resp, nil
}

func (b *BatchAllocateForecastImpl) convertToCodeList(outlineTabs []model.AllocateHistoryOutlineTab) []string {
	var codeList []string
	for _, tab := range outlineTabs {
		if tab.ZoneCode != "" {
			codeList = append(codeList, tab.ZoneCode)
		} else if tab.RouteCode != "" {
			codeList = append(codeList, tab.RouteCode)
		}
	}

	return codeList
}

func (b BatchAllocateForecastImpl) convertTabToMap(ctx context.Context, tab model.AllocateForecastTaskConfigTab) map[string]interface{} {
	value := map[string]interface{}{
		"task_name":                          tab.TaskName,
		"mask_product_id":                    tab.MaskingProductID,
		"shop_group_channel_priority_toggle": tab.ShopGroupChannelPriorityToggle,
		"local_soft_criteria_toggle":         tab.LocalSoftCriteriaToggle,
		"run_soft_rule_only_toggle":          tab.RunSoftRuleOnlyToggle,
		"re_calc_fee":                        tab.RecalculateShippingFeeToggle,
		"order_paid_time":                    tab.OrderPaidTime,
		"task_status":                        tab.Status,
		"sync_desc":                          tab.SyncDesc,
		"product_priority_configs":           tab.ProductPriorityConfigs,
		"allocation_rule_config":             tab.AllocationRuleConfig,
		"deploy_config_detail":               tab.DeployConfigDetail,
		"config_sync_status":                 tab.ConfigSyncStatus,
		"complete_time":                      tab.CompleteTime,
		"latest_deploy_time":                 tab.LatestDeployTime,
		"effective_start_time":               tab.EffectiveStartTime,
		"operated_by":                        tab.OperatedBy,
		"ctime":                              tab.CTime,
		"mtime":                              tab.MTime,
		"deploy_failed_desc":                 tab.DeployFailedDesc,
		"allocation_method":                  tab.AllocationMethod,
		"splitting_rule_list":                tab.BatchSizeList,
		"batch_allocation_rule_config":       tab.BatchAllocationRuleConfig,
	}
	return value
}
