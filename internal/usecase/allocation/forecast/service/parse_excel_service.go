package service

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

const (
	// Route/Zone code的最大长度(DB字段的长度)
	maxRouteZoneCodeLen = 64
	exceedLengthErrMsg  = "Name can't exceed 64 character for column 'zone code'/'route code' at row %d. Please re-confirm"
	LocationJoinCode    = "-"
)

func (b *BatchAllocateForecastImpl) ParseZoneExcel(ctx context.Context, forecastId uint64, maskProductID int, defFileUrl, volFileUrl string, parseType int) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error) {
	//1. 下载s3文件
	defFile, err := httputil.Get(ctx, defFileUrl, nil, S3TimeOut, nil)
	if err != nil {
		return nil, srerr.With(srerr.S3DownloadFail, defFileUrl, err)
	}
	volFile, err := httputil.Get(ctx, volFileUrl, nil, S3TimeOut, nil)
	if err != nil {
		return nil, srerr.With(srerr.S3DownloadFail, volFileUrl, err)
	}
	//2. 解析excel文件，得到zone definition数据
	defRows, tempDefHeaders, fErr := getFileData(ctx, defFileUrl, defFile)
	if fErr != nil {
		return nil, fErr
	}
	if !checkDefHeaders(tempDefHeaders, defZoneHeaders) {
		return nil, srerr.New(srerr.ExcelValidateError, nil, "check header fail")
	}

	zoneCodes := make(map[string][]zone)
	// 用于检查全部数据的地址唯一性
	checkDulMap := make(map[string]struct{})
	//用于检查同一Zone Code下的地址不能含有覆盖关系
	checkCoverMap := make(map[zone][]string)
	for rowIndex, data := range defRows {
		//跳过空白行
		if isEmptyRow(data) {
			continue
		}
		if len(data) < len(defZoneHeaders) {
			return nil, srerr.New(srerr.ParamErr, nil, "invalid import data|columns must equal %d", len(defZoneHeaders))
		}
		// 检查Zone Code
		zoneCode := data[5]
		if zoneCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone Code can not be empty: %v", data)
		}
		if len(zoneCode) > maxRouteZoneCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}

		// 检查上级地址是否为空
		if !checkLocValid(data[:4]) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v ", zoneCode, data[0], data[1], data[2])
		}

		// 检查是否仅配置了postcode
		if !checkLocPostcode(data[:4], data[4]) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v, Street %v, Postcode %v ", zoneCode, data[0], data[1], data[2], data[3], data[4])
		}

		// 检查地址是否存在
		locInfo, err := b.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data[0], data[1], data[2], data[3])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v, Street:%v", zoneCode, data[0], data[1], data[2], data[3])
		}

		// 检查地址唯一性（完全相同的地址唯一）
		checkDulKey := str.Join(LocationJoinCode, locInfo.GetLocationID(), data[4])
		if _, exist := checkDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil,
				"Duplicate Data : State %v, City %v, District %v, Street %v, Postcode %v;Zone Code: %v",
				data[0], data[1], data[2], data[3], data[4], data[5])
		}
		checkDulMap[checkDulKey] = struct{}{}

		z := zone{zoneCode, int(locInfo.GetLocationID()), data[4]}
		checkCoverMap[z] = data
		zoneCodes[zoneCode] = append(zoneCodes[zoneCode], z)
	}
	// 检查同一Zone Code下地址是否覆盖
	if err := b.checkZoneAndPostcodeLocCover(ctx, checkCoverMap); err != nil {
		return nil, err
	}

	maskProd, pErr := b.LpsApi.GetProductDetail(ctx, maskProductID)
	if pErr != nil {
		return nil, pErr
	}
	if len(maskProd.GetComponentProduct().ComponentProducts) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "Get none Component product by Mask product id %v", maskProductID)
	}
	componentProdMap := make(map[int]struct{}, len(maskProd.GetComponentProduct().ComponentProducts))
	for _, componentProdID := range maskProd.GetComponentProduct().ComponentProducts {
		componentProdMap[componentProdID] = struct{}{}
	}

	//3. 解析excel文件，得到zone value数据
	volRows, tempVolHeaders, fErr := getFileData(ctx, volFileUrl, volFile)
	if fErr != nil {
		return nil, fErr
	}
	if !checkDefHeaders(tempVolHeaders, targetVolZoneHeaders) {
		return nil, srerr.New(srerr.ExcelValidateError, nil, "check header fail")
	}

	var result []rulevolume.BatchAllocateForecastVolumeTab
	checkVolDulMap := make(map[string]struct{})
	for _, data := range volRows {
		//跳过空白行
		if isEmptyRow(data) {
			continue
		}
		zoneCode := data[0]
		if zoneCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone Code can not be empty: %v", data)
		}
		zoneInfos, exist := zoneCodes[zoneCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone code %v not found in definition file", zoneCode)
		}
		componentProductID, err := strconv.Atoi(data[1])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}
		checkDulKey := formatCheckLocVolDulKey(zoneCode, componentProductID)
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Zone Code: %v, Component product id: %v", zoneCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}
		if _, exist := componentProdMap[componentProductID]; !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductID)
		}
		minVol, err := parseVol(data[3], constant.FillBlankZero)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse min volume err: %v", data)
		}
		maxVol, err := parseVol(data[4], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max volume err: %v", data)
		}
		var maxCodVol, maxBulkyVol, maxHighValueVol, maxDgVol int
		maxCodVol, err = parseVol(data[5], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max cod volume err: %v", data)
		}
		maxBulkyVol, err = parseVol(data[6], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max bulky volume err: %v", data)
		}
		maxHighValueVol, err = parseVol(data[7], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max high-value volume err: %v", data)
		}
		maxDgVol, err = parseVol(data[8], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max dg volume err: %v", data)
		}
		for _, zoneInfo := range zoneInfos {
			//生成base zone volume
			minZoneVolume := rulevolume.BatchAllocateForecastVolumeTab{
				MaskProductID:           uint64(maskProductID),
				BatchAllocateForecastId: forecastId,
				ComponentProductID:      uint64(componentProductID),
				VolumeScene:             constant.VolumeSceneTarget,
				VolumeType:              constant.VolumeTypeZone,
				ZoneCode:                zoneCode,
				ZoneMinLocationId:       int64(zoneInfo.LocID),
				ZonePostcode:            zoneInfo.PostCode,
				MinVolumeValue:          int64(minVol),
				MaxVolumeValue:          int64(maxVol),
				MaxCodVolumeValue:       int64(maxCodVol),
				MaxBulkyVolumeValue:     int64(maxBulkyVol),
				MaxHighValueVolumeValue: int64(maxHighValueVol),
				MaxDgVolumeValue:        int64(maxDgVol),
				CTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
				MTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
			}
			result = append(result, minZoneVolume)
		}
	}

	return result, nil
}

func (b *BatchAllocateForecastImpl) ParseRouteExcel(ctx context.Context, forecastId uint64, maskProductID int, defFileUrl, volFileUrl string) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error) {
	var result []rulevolume.BatchAllocateForecastVolumeTab

	//1. 下载s3文件
	defFile, err := httputil.Get(ctx, defFileUrl, nil, S3TimeOut, nil)
	if err != nil {
		return nil, srerr.With(srerr.S3DownloadFail, defFileUrl, err)
	}
	volFile, err := httputil.Get(ctx, volFileUrl, nil, S3TimeOut, nil)
	if err != nil {
		return nil, srerr.With(srerr.S3DownloadFail, volFileUrl, err)
	}
	//2. 解析excel文件，得到route definition数据
	defRows, tempDefHeaders, fErr := getFileData(ctx, defFileUrl, defFile)
	if fErr != nil {
		return nil, fErr
	}
	if !checkDefHeaders(tempDefHeaders, defRouteHeaders) {
		return nil, srerr.New(srerr.ExcelValidateError, nil, "check header fail")
	}

	routeCodes := make(map[string][]route)
	checkDulMap := make(map[string]struct{})
	checkCoverMap := make(map[route][]string)
	for _, data := range defRows {
		if isEmptyRow(data) {
			continue
		}
		// 检查Route Code
		routeCode := data[10]
		if routeCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Route Code can not be empty: %v", data)
		}

		// 检查上级地址是否为空
		if !checkLocValid(data[:4]) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Origin Route %v is invalid; State %v, City %v, District %v, Street:%v ", routeCode, data[0], data[1], data[2], data[3])
		}
		if !checkLocValid(data[5:9]) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Destination Route %v is invalid; State %v, City %v, District %v, Street:%v ", routeCode, data[5], data[6], data[7], data[8])
		}

		// 检查地址是否存在
		originLocInfo, err := b.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data[0], data[1], data[2], data[3])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v", routeCode, data[0], data[1], data[2], data[3])
		}
		destLocInfo, err := b.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data[5], data[6], data[7], data[8])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v", routeCode, data[5], data[6], data[7], data[8])
		}

		// 检查地址唯一性（完全相同的地址唯一）
		checkDulKey := str.Join("-", originLocInfo.GetLocationID(), destLocInfo.GetLocationID())
		if _, exist := checkDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil,
				"Duplicate Data : Origin: State %v, City %v, District %v, Street %v;Destination: State %v, City %v, District %v, Street %v;Route Code: %v",
				data[0], data[1], data[2], data[3], data[5], data[6], data[7], data[8], data[10])
		}
		checkDulMap[checkDulKey] = struct{}{}

		r := route{routeCode, int(originLocInfo.GetLocationID()), data[4], int(destLocInfo.GetLocationID()), data[9]}
		checkCoverMap[r] = data
		routeCodes[routeCode] = append(routeCodes[routeCode], r)
	}
	// 检查同一Route Code下地址是否覆盖
	if err := b.checkRouteLocCover(ctx, checkCoverMap); err != nil {
		return nil, err
	}

	maskProd, pErr := b.LpsApi.GetProductDetail(ctx, maskProductID)
	if pErr != nil {
		return nil, pErr
	}
	if len(maskProd.GetComponentProduct().ComponentProducts) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "Get none Component product by Mask product id %v", maskProductID)
	}
	componentProdMap := make(map[int]struct{}, len(maskProd.GetComponentProduct().ComponentProducts))
	for _, componentProdID := range maskProd.GetComponentProduct().ComponentProducts {
		componentProdMap[componentProdID] = struct{}{}
	}

	//3. 解析excel文件，得到route value数据
	volRows, tempVolHeaders, fErr := getFileData(ctx, volFileUrl, volFile)
	if fErr != nil {
		return nil, fErr
	}
	if !checkDefHeaders(tempVolHeaders, targetVolRouteHeaders) {
		return nil, srerr.New(srerr.ExcelValidateError, nil, "check header fail")
	}

	checkVolDulMap := make(map[string]struct{})
	for _, data := range volRows {
		if isEmptyRow(data) {
			continue
		}
		routeCode := data[0]
		if routeCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Route Code can not be empty: %v", data)
		}
		routeInfos, exist := routeCodes[routeCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Route code %v not found in definition file", routeCode)
		}
		componentProductID, err := strconv.Atoi(data[1])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}
		if _, exist := componentProdMap[componentProductID]; !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductID)
		}
		checkDulKey := formatCheckLocVolDulKey(routeCode, componentProductID)
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Route Code: %v, Component product id: %v", routeCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}
		minVol, err := parseVol(data[3], constant.FillBlankZero)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid vol: %v", data)
		}
		maxVol, err := parseVol(data[4], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid vol: %v", data)
		}
		var maxCodVol, maxBulkyVol, maxHighValueVol, maxDgVol int
		maxCodVol, err = parseVol(data[5], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max cod volume err: %v", data)
		}
		maxBulkyVol, err = parseVol(data[6], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max bulky volume err: %v", data)
		}
		maxHighValueVol, err = parseVol(data[7], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max high-value volume err: %v", data)
		}
		maxDgVol, err = parseVol(data[8], constant.FillBlankMax)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Parse max dg volume err: %v", data)
		}
		for _, routeInfo := range routeInfos {
			result = append(result, rulevolume.BatchAllocateForecastVolumeTab{
				MaskProductID:           uint64(maskProductID),
				BatchAllocateForecastId: forecastId,
				VolumeScene:             constant.VolumeSceneTarget,
				ComponentProductID:      uint64(componentProductID),
				RouteCode:               routeCode,
				RouteMinOriginLocId:     int64(routeInfo.OriginLocID),
				RouteOriginPostcode:     routeInfo.OriginPostcode,
				RouteMinDestLocId:       int64(routeInfo.DestLocID),
				RouteDestPostcode:       routeInfo.DestPostcode,
				VolumeType:              constant.VolumeTypeRoute,
				MinVolumeValue:          int64(minVol),
				MaxVolumeValue:          int64(maxVol),
				MaxCodVolumeValue:       int64(maxCodVol),
				MaxBulkyVolumeValue:     int64(maxBulkyVol),
				MaxHighValueVolumeValue: int64(maxHighValueVol),
				MaxDgVolumeValue:        int64(maxDgVol),
				CTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
				MTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
			})
		}
	}

	return result, nil
}

// checkLocValid 检查上级地址是否为空
func checkLocValid(locs []string) bool {
	if len(locs) != 4 {
		return false
	}
	if locs[0] == "" {
		return false
	}
	if locs[1] == "" && locs[2] != "" {
		return false
	}
	if locs[2] == "" && locs[3] != "" {
		return false
	}

	return true
}

func checkLocPostcode(locs []string, postcode string) bool {
	if locs[0] == "" && locs[1] == "" && locs[2] == "" && locs[3] == "" && postcode != "" {
		return false
	}
	return true
}

// checkZoneLocCover 检查同RouteCode下的地址是否有覆盖
func (b *BatchAllocateForecastImpl) checkZoneAndPostcodeLocCover(ctx context.Context, checkMap map[zone][]string) *srerr.Error {
	jobs := make(chan zone, len(checkMap))
	results := make(chan *srerr.Error, len(checkMap))
	// Start run workers
	for i := 0; i < workerNum; i++ {
		go b.checkZoneLocCoverWorker(ctx, checkMap, jobs, results)
	}
	// Send jobs to worker
	for job := range checkMap {
		jobs <- job
	}
	close(jobs)
	// Check worker return result(error)
	for i := 0; i < len(checkMap); i++ {
		if err := <-results; err != nil {
			return err
		}
	}

	return nil
}

// 并发执行的worker
func (b *BatchAllocateForecastImpl) checkZoneLocCoverWorker(ctx context.Context, checkMap map[zone][]string, jobs <-chan zone, results chan<- *srerr.Error) {
	for job := range jobs {
		curNode, _ := b.AddrRepo.GetLocationByLocId(ctx, int64(job.LocID))
		// 防止LocationTree有问题的情况下for无限循环
		var levelCount int
		for curNode != nil && levelCount < address.GetMaxLocationLevel() {
			checkKey := zone{job.ZoneCode, int(curNode.GetLocationID()), job.PostCode}
			if data, exist := checkMap[checkKey]; exist && checkKey != job {
				retErr := srerr.New(srerr.ParamErr, nil,
					"Coverage Data : State %v, City %v, District %v, Postcode %v;Zone Code: %v",
					data[0], data[1], data[2], data[3], data[4])
				results <- retErr
				return
			}
			curNode, _ = b.AddrRepo.GetLocationByLocId(ctx, curNode.GetParentID())
			levelCount++
		}
		results <- nil
	}
}

// checkRouteLocCover 检查同RouteCode下的地址是否有覆盖
func (b *BatchAllocateForecastImpl) checkRouteLocCover(ctx context.Context, checkMap map[route][]string) *srerr.Error {
	jobs := make(chan route, len(checkMap))
	results := make(chan *srerr.Error, len(checkMap))
	// Start run workers
	for i := 0; i < workerNum; i++ {
		go b.checkRouteLocCoverWorker(ctx, checkMap, jobs, results)
	}
	// Send jobs to worker
	for job := range checkMap {
		jobs <- job
	}
	close(jobs)
	// Check worker return result(error)
	for i := 0; i < len(checkMap); i++ {
		if err := <-results; err != nil {
			return err
		}
	}

	return nil
}

// 并发执行的worker
func (b *BatchAllocateForecastImpl) checkRouteLocCoverWorker(ctx context.Context, checkMap map[route][]string, jobs <-chan route, results chan<- *srerr.Error) {
	for job := range jobs {
		originNode, _ := b.AddrRepo.GetLocationByLocId(ctx, int64(job.OriginLocID))
		destNode, _ := b.AddrRepo.GetLocationByLocId(ctx, int64(job.DestLocID))
		// 防止LocationTree有问题的情况下for无限循环
		var originLevelCount int
		for originCurNode := originNode; originCurNode != nil && originLevelCount < address.GetMaxLocationLevel(); {
			var destLevelCount int
			for destCurNode := destNode; destCurNode != nil && destLevelCount < address.GetMaxLocationLevel(); {
				checkKey := route{job.RouteCode, int(originCurNode.GetLocationID()), job.OriginPostcode, int(destCurNode.GetLocationID()), job.DestPostcode}
				if data, exist := checkMap[checkKey]; exist && checkKey != job {
					retErr := srerr.New(srerr.ParamErr, nil,
						"Coverage Data : Origin: State %v, City %v, District %v;Destination: State %v, City %v, District %v;Route Code: %v",
						data[0], data[1], data[2], data[3], data[4], data[5], data[6])
					results <- retErr
					return
				}
				destCurNode, _ = b.AddrRepo.GetLocationByLocId(ctx, destCurNode.GetParentID())
				destLevelCount++
			}
			originCurNode, _ = b.AddrRepo.GetLocationByLocId(ctx, originCurNode.GetParentID())
			originLevelCount++
		}
		results <- nil
	}
}

func formatCheckLocVolDulKey(locCode string, productID int) string {
	return fmt.Sprintf("%v:%v", locCode, productID)
}

// parseMinVol 为空设置Min/max Vol默认值，否则正常解析str->int
// min类型的，设置为0，max类型的设置为9个9
func parseVol(data string, fillBlankType int) (int, error) {
	if data == "" || data == " " {
		if fillBlankType == constant.FillBlankMax {
			return rulevolume.DefaultMaskMinVolume, nil
		}
		if fillBlankType == constant.FillBlankZero {
			return 0, nil
		}
		return 0, srerr.New(srerr.BlankFillError, nil, "illegal blank fill type")
	}
	return strconv.Atoi(data)
}

func getFileData(ctx context.Context, url string, fileData []byte) ([][]string, []string, *srerr.Error) {
	rows, header, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(fileData), true)
	if fErr == nil {
		return rows, header, nil
	}
	if strings.Contains(url, ".csv?") {
		rows, header = fileutil.ReadCsv(fileData)
	}
	if strings.Contains(url, ".xls?") {
		rows, header, fErr = fileutil.ParseXlsFile(ctx, fileData)
	}
	if fErr != nil && len(rows) < 1 {
		return nil, nil, srerr.With(srerr.ParseExcelError, nil, fErr)
	}
	return rows, header, nil
}

func checkDefHeaders(headers1, headers2 []string) bool {
	if len(headers1) == 0 || len(headers2) == 0 {
		return false
	}
	// 去掉空白格
	headers1 = removeWhiteSpace(headers1)
	headers2 = removeWhiteSpace(headers2)
	if len(headers1) != len(headers2) {
		return false
	}
	for i := 0; i < len(headers1); i++ {
		if headers1[i] != headers2[i] {
			return false
		}
	}
	return true
}

//func checkMultiDefHeaders(headers, template1, template2 []string) bool {
//	if len(headers) == 0 || len(template1) == 0 || len(template2) == 0 {
//		return false
//	}
//	if (len(headers) != len(template1)) && (len(headers) != len(template2)) { //header要为两个模板之一
//		return false
//	}
//	template1Flag := true
//	if len(headers) == len(template1) {
//		for i := 0; i < len(headers); i++ {
//			if strings.EqualFold(strings.ToLower(headers[i]), strings.ToLower(template1[i])) {
//				template1Flag = false
//				break
//			}
//		}
//	}
//	template2Flag := true
//	if len(headers) == len(template2) {
//		for i := 0; i < len(headers); i++ {
//			if strings.EqualFold(strings.ToLower(headers[i]), strings.ToLower(template2[i])) {
//				template2Flag = false
//				break
//			}
//		}
//	}
//
//	return template1Flag || template2Flag
//}

//func parseHardCapString(hardCapStr string) int {
//	if "y" == strings.ToLower(hardCapStr) {
//		return 1
//	}
//	if "n" == strings.ToLower(hardCapStr) {
//		return 2
//	}
//	return 0
//}

func isEmptyRow(columns []string) bool {
	for _, column := range columns {
		if column != "" && column != " " {
			return false
		}
	}

	return true
}

func removeWhiteSpace(header []string) []string {
	var result []string
	for i := 0; i < len(header); i++ {
		if header[i] == "" || header[i] == " " {
			break
		}
		result = append(result, header[i])
	}

	return result
}
