package service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
)

func (b *BatchAllocateForecastImpl) ValidateRequest(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error {
	if req.BaseInfo.Status == constant.TaskConfigStatusPending {
		if vErr := b.validateBatchSize(ctx, req); vErr != nil {
			logger.CtxLogErrorf(ctx, "ValidateRequest|validate not pass, err:%v", vErr)
			return vErr
		}

		// 校验zone define && zone volume的header
		if req.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled {
			var defFileUrl, volFileUrl string
			var defHeaders, targetVolumeHeaders []string
			if req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.LocalVolumeRuleType == int(rulevolume.LocVolumeTypeZone) {
				defFileUrl = req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.ZoneDefinitionUrl
				volFileUrl = req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.ZoneVolumeValueUrl
				defHeaders = defZoneHeaders
				targetVolumeHeaders = targetVolZoneHeaders
			} else if req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.LocalVolumeRuleType == int(rulevolume.LocVolumeTypeRoute) {
				defFileUrl = req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.RouteDefinitionUrl
				volFileUrl = req.BatchAllocationRuleConfig.ZoneRouteVolumeDetail.RouteVolumeValueUrl
				defHeaders = defRouteHeaders
				targetVolumeHeaders = targetVolRouteHeaders
			}

			//1. 下载s3文件
			defFile, err := httputil.Get(ctx, defFileUrl, nil, S3TimeOut, nil)
			if err != nil {
				return srerr.With(srerr.S3DownloadFail, "download definition file failed", err)
			}
			volFile, err := httputil.Get(ctx, volFileUrl, nil, S3TimeOut, nil)
			if err != nil {
				return srerr.With(srerr.S3DownloadFail, "download volume file failed", err)
			}
			//2. 解析excel文件，得到zone definition数据
			_, tempDefHeaders, fErr := getFileData(ctx, defFileUrl, defFile)
			if fErr != nil {
				return fErr
			}
			if !checkDefHeaders(tempDefHeaders, defHeaders) {
				return srerr.New(srerr.ExcelValidateError, nil, "check definition header fail.  the format of upload file is error")
			}
			//3. 解析excel文件，得到zone volume数据
			_, tempVolHeaders, fErr := getFileData(ctx, volFileUrl, volFile)
			if fErr != nil {
				return fErr
			}
			if !checkDefHeaders(tempVolHeaders, targetVolumeHeaders) {
				return srerr.New(srerr.ExcelValidateError, nil, "check volume header fail.  the format of upload file is error")
			}
		}

	}
	//校验target enable字段，一定要开启，否则算法sdk会panic
	if !req.BatchAllocationRuleConfig.CountryVolumeEnabled && !req.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled {
		logger.CtxLogErrorf(ctx, "ValidateRequest|must open country volume or zone/route volume toggle")
		return srerr.New(srerr.ParamErr, nil, "must open country volume or zone/route volume toggle")
	}
	//填充request中的shipping fee toggle，因为其一定要开启
	logger.CtxLogInfof(ctx, "ValidateRequest|req shipping fee toggle enabled:%v, next will set it true", req.BatchAllocationRuleConfig.CheapestShippingFeeEnabled)
	req.BatchAllocationRuleConfig.CheapestShippingFeeEnabled = true

	return nil
}

func (b *BatchAllocateForecastImpl) validateBatchSize(ctx context.Context, req schema.BAForecastCreateUpdateReq) *srerr.Error {
	batchSizeList := req.BatchSizeList
	//1.校验batch size list长度
	if len(batchSizeList) == 0 {
		logger.CtxLogErrorf(ctx, "validate|batch size list is empty")
		return srerr.New(srerr.ParamErr, nil, "batch size list is empty")
	}
	//last end time + 1 = next start time；整个区间 = OneDayUnix - 1
	//校验时间切片的连续性
	for _, batchSize := range batchSizeList {
		if batchSize.BatchSizeType == constant.FixedByTime || batchSize.BatchSizeType == constant.FixedByQAndT {
			//fixed time下的list只有一个元素，校验其起止时间是否为一天
			fixedTimeUnitList := batchSize.FixedTime.FixedTimeUnitList
			if len(fixedTimeUnitList) == 1 {
				fixedTimeUnit := fixedTimeUnitList[0]
				//整个区间 = OneDayUnix - 1，即00：00：00 ~ 23：59：59
				if (fixedTimeUnit.EndTime - fixedTimeUnit.StartTime) != (timeutil.OneDayUnix - timeutil.OneSecond) {
					logger.CtxLogErrorf(ctx, "validate|fixed by time, start or end time is illegal, start time:%v, end time:%v", fixedTimeUnit.StartTime, fixedTimeUnit.EndTime)
					return srerr.New(srerr.ParamErr, nil, "fixed by time, start or end time is illegal")
				}
			} else {
				//fixed time下的list有多个元素，校验元素的起止时间是否连贯
				//排序
				sort.Slice(fixedTimeUnitList, func(i, j int) bool {
					return fixedTimeUnitList[i].StartTime < fixedTimeUnitList[j].StartTime
				})
				//排序后，最后一个元素的end time与第一个元素的start time间隔必需满足一天
				//整个区间 = OneDayUnix - 1，即00：00：00 ~ 23：59：59
				if (fixedTimeUnitList[len(fixedTimeUnitList)-1].EndTime - fixedTimeUnitList[0].StartTime) != (timeutil.OneDayUnix - timeutil.OneSecond) {
					logger.CtxLogErrorf(ctx, "validate|fixed by time, first start or last end time, first unit:%v, last unit:%v", fixedTimeUnitList[0], fixedTimeUnitList[len(fixedTimeUnitList)-1])
					return srerr.New(srerr.ParamErr, nil, "fixed by time, first start or last end time is illegal")
				}
				for i := 0; i < len(fixedTimeUnitList)-1; i++ {
					//排序后，连续的元素不允许有相同的start time或end time
					if fixedTimeUnitList[i].StartTime == fixedTimeUnitList[i+1].StartTime ||
						fixedTimeUnitList[i].EndTime == fixedTimeUnitList[i+1].EndTime {
						logger.CtxLogErrorf(ctx, "validate|fixed by time, same start or end time, first unit:%v, second unit:%v", fixedTimeUnitList[i], fixedTimeUnitList[i+1])
						return srerr.New(srerr.ParamErr, nil, "fixed by time, same start or end time")
					}
					//排序后，前一个元素的end time必需和后一个元素的start time差1秒
					if fixedTimeUnitList[i].EndTime != (fixedTimeUnitList[i+1].StartTime - timeutil.OneSecond) {
						logger.CtxLogErrorf(ctx, "validate|fixed by time, first end time not equal to next start time, first unit:%v, second unit:%v", fixedTimeUnitList[i], fixedTimeUnitList[i+1])
						return srerr.New(srerr.ParamErr, nil, "fixed by time, first end time not equal to next start time")
					}
				}
			}
		}
	}
	return nil
}
