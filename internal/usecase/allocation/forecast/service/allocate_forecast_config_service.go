package service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type AllocateForecastTaskConfigService interface {
	CreateForecastTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) *srerr.Error
	UpdateForecastTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) *srerr.Error
	DelForecastTaskConfig(ctx context.Context, taskId int64) *srerr.Error
	ListForecastTaskConfigs(ctx context.Context, req *schema.ForecastListRequest) (*schema.ForecastListResp, *srerr.Error)
	GetForecastTaskConfigById(ctx context.Context, taskId int64) (*schema.AllocateForecastTaskConfigResp, *srerr.Error)
	CopyForecastTaskConfigById(ctx context.Context, taskId int64) (*schema.AllocateForecastTaskConfigResp, *srerr.Error)
	GetForecastTaskConfigEntityById(ctx context.Context, id int64) (*schema.AllocateForecastTaskConfigEntity, *srerr.Error)
	GetForecastTaskConfigsByStatus(ctx context.Context, status int) ([]*schema.AllocateForecastTaskConfigEntity, *srerr.Error)
	ValidateProcessTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) (bool, *srerr.Error)
	GetOnlineSoftRule(ctx context.Context, req *schema.SoftRuleSyncReq) (*schema.SoftRuleSyncResp, *srerr.Error)
	GetOnlineProductPriority(ctx context.Context, req *schema.ProductPrioritySyncReq) (*schema.ProductPrioritySyncResp, *srerr.Error)
	UpdateTaskWithCondition(ctx context.Context, condition map[string]interface{}, value map[string]interface{}, maskingProductId uint64) *srerr.Error
	SplitSubTaskByMainTask(ctx context.Context, mainTaskTab *model.AllocateForecastTaskConfigTab) *srerr.Error
	CreateBASubTaskByTaskConfig(ctx context.Context) *srerr.Error
	//// debug tool
	//SyncForecastTaskConfigForApi(ctx context.Context, taskId int64) *srerr.Error
	//StartForecastTask(ctx context.Context, taskId int64) *srerr.Error
}

type AllocateForecastTaskConfigServiceImpl struct {
	AllocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo
	LpsApi                         lpsclient.LpsApi
	AllocateShippingFeeRepo        repo.AllocateShippingFeeRepo
	IMaskRuleVolumeRepo            rulevolume.IMaskRuleVolumeRepo
	AllocationRuleRepo             rule.AllocationRuleRepo
	MaskRuleVolumeService          rulevolume2.MaskRuleVolumeService
	AllocateForecastRankRepo       repo.AllocateForecastRankRepo
	priorityServer                 productpriority.PriorityBusiness
	ForecastingSubTaskRepo         forecasting_sub_task.ForecastingSubTaskRepo
	ForecastingSubTaskService      forecasting_sub_task.ForecastingSubTaskService
}

func NewAllocateForecastTaskConfigServiceImpl(
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	LpsApi lpsclient.LpsApi,
	AllocateShippingFeeRepo repo.AllocateShippingFeeRepo,
	IMaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
	AllocationRuleRepo rule.AllocationRuleRepo,
	MaskRuleVolumeService rulevolume2.MaskRuleVolumeService,
	AllocateForecastRankRepo repo.AllocateForecastRankRepo,
	priorityServer productpriority.PriorityBusiness,
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService,
	ForecastingSubTaskRepo forecasting_sub_task.ForecastingSubTaskRepo) *AllocateForecastTaskConfigServiceImpl {
	return &AllocateForecastTaskConfigServiceImpl{
		AllocateForecastTaskConfigRepo: allocateForecastTaskConfigRepo,
		LpsApi:                         LpsApi,
		AllocateShippingFeeRepo:        AllocateShippingFeeRepo,
		IMaskRuleVolumeRepo:            IMaskRuleVolumeRepo,
		AllocationRuleRepo:             AllocationRuleRepo,
		MaskRuleVolumeService:          MaskRuleVolumeService,
		AllocateForecastRankRepo:       AllocateForecastRankRepo,
		priorityServer:                 priorityServer,
		ForecastingSubTaskRepo:         ForecastingSubTaskRepo,
		ForecastingSubTaskService:      ForecastingSubTaskService,
	}
}

func (a AllocateForecastTaskConfigServiceImpl) CreateForecastTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) *srerr.Error {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	if config.BaseInfo.Status == constant.TaskConfigStatusDraft {
		allocateForecastTaskConfigTab, err := a.convertDraftTaskConfigToAdminData(ctx, config)
		if err != nil {
			return err
		}
		allocateForecastTaskConfigTab.OperatedBy = operateBy
		allocateForecastTaskConfigTab.CTime = uint32(recorder.Now(ctx).Unix())
		allocateForecastTaskConfigTab.MTime = uint32(recorder.Now(ctx).Unix())
		err = a.AllocateForecastTaskConfigRepo.CreateForecastTaskConfig(ctx, allocateForecastTaskConfigTab)
		return err
	}
	allocateForecastTaskConfigTab, err := a.convertTaskConfigToAdminData(ctx, config)
	if err != nil {
		return err
	}
	allocateForecastTaskConfigTab.OperatedBy = operateBy
	allocateForecastTaskConfigTab.CTime = uint32(recorder.Now(ctx).Unix())
	allocateForecastTaskConfigTab.MTime = uint32(recorder.Now(ctx).Unix())

	// validate task config
	if config.BaseInfo.Status == constant.TaskConfigStatusProcess {
		if validateResult, err := a.ValidateProcessTaskConfig(ctx, config); !validateResult {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--validate process task config failed. config=%v, err=%v", *config, err)
			return err
		}
	}
	//SSCSMR-1480：校验前置，如果没有allocate rule，就获取online rule，如果获取失败则不允许预测
	if !config.BaseInfo.LocalSoftCriteriaToggle {
		// 未打开soft criteria 开关的任务，使用线上规则进行预测
		req := &schema.SoftRuleSyncReq{
			MaskingProductId: int64(config.BaseInfo.MaskProductId),
		}
		onlineAllocateRule, gErr := a.GetOnlineSoftRule(ctx, req)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "CreateForecastTaskConfig|GetOnlineSoftRule fail|err=%v", gErr)
			return srerr.With(srerr.GetOnlineAllocateRuleError, nil, gErr)
		}
		allocationRuleConfig, mErr := json.Marshal(onlineAllocateRule.AllocationRuleConfig)
		if mErr != nil {
			logger.CtxLogErrorf(ctx, "marshal online allocate rule err:%v", mErr)
			return srerr.With(srerr.MarshalOnlineAllocateRuleError, nil, mErr)
		}
		//SSCSMR-1480:将后续的赋值前置到本处
		allocateForecastTaskConfigTab.AllocationRuleConfig = allocationRuleConfig
	}

	logger.CtxLogInfof(ctx, "before create masking forecast, task id:%v", allocateForecastTaskConfigTab.Id)
	if err = a.AllocateForecastTaskConfigRepo.CreateForecastTaskConfig(ctx, allocateForecastTaskConfigTab); err != nil {
		logger.CtxLogErrorf(ctx, "create masking forecast task err:%v", err)
		return srerr.With(srerr.CreateMaskingForecastTaskError, nil, err)
	}
	logger.CtxLogInfof(ctx, "after create masking forecast, task id:%v", allocateForecastTaskConfigTab.Id)
	if allocateForecastTaskConfigTab.ConfigSyncStatus == constant.WaitToSyncConfigStatus {
		config.BaseInfo.Id = int(allocateForecastTaskConfigTab.Id)
		// 需要发一条消息到kafka，由定时任务判断是否需要异步生成volume 配置
		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		msg, _ := jsoniter.Marshal(config)
		if err := kafkahelper.DeliveryMessage(ctx, namespace, constant2.TaskNameLoadForecastVolumeConfig, msg, nil, ""); err != nil {
			return srerr.With(srerr.SendMessageError, nil, err)
		}
		logger.CtxLogInfof(ctx, "Allocate Forecast--config = %v", *config)
	}
	sErr := a.SplitSubTaskByMainTask(ctx, allocateForecastTaskConfigTab)
	if sErr != nil {
		//拆分失败需要更新main task到failed
		allocateForecastTaskConfigTab.Status = constant.TaskConfigStatusFailed
		if uErr := a.AllocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, allocateForecastTaskConfigTab); uErr != nil {
			logger.CtxLogErrorf(ctx, "update task to failed err:%v", uErr)
			return uErr
		}
	}
	return sErr
}

func (a AllocateForecastTaskConfigServiceImpl) ValidateProcessTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) (bool, *srerr.Error) {
	groupToProductList := make(map[string][]int)

	if config.BaseInfo.OrderPaidTime == nil {
		return false, srerr.New(srerr.ParamErr, config, "order_paid_time can't be nil.")
	}
	if !config.BaseInfo.LocalSoftCriteriaToggle && !config.BaseInfo.ShopGroupChannelPriorityToggle {
		return false, srerr.New(srerr.ParamErr, config, "both local_soft_criteria_toggle and shop_group_channel_priority_toggle off. ")
	}
	if config.BaseInfo.LocalSoftCriteriaToggle {
		if config.AllocationRuleConfig == nil {
			return false, srerr.New(srerr.ParamErr, config, "allocation_rule_config can't be nil when local_soft_criteria_toggle on.")
		}
		if config.AllocationRuleConfig.RuleDetail == nil {
			return false, srerr.New(srerr.ParamErr, config, "rule_detail can't be nil when local_soft_criteria_toggle on.")
		}
		if config.AllocationRuleConfig.RuleDetail.CheapestFeeEnable && len(config.AllocationRuleConfig.ShippingFeeConfig) == 0 {
			return false, srerr.New(srerr.ParamErr, config, "shipping_fee_config must config when cheapest_fee_enable on.")
		}
		if config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable && config.AllocationRuleConfig.MaskProductRuleVolumeConfig == nil {
			return false, srerr.New(srerr.ParamErr, config, "min_volume_enable toggle on, mask_product_rule_volume_config can't be nil.")
		}
		if config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable && config.AllocationRuleConfig.MaskProductRuleVolumeConfig == nil {
			return false, srerr.New(srerr.ParamErr, config, "max_capacity_enable toggle on, mask_product_rule_volume_config can't be nil.")
		}

		if config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable || config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable {
			ruleVolumeConfig := config.AllocationRuleConfig.MaskProductRuleVolumeConfig

			ruleType := config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType
			// 校验上传文件是否合法
			if ruleType == uint32(rulevolume.LocVolumeTypeRoute) {
				_, pErr := a.MaskRuleVolumeService.ParseRouteExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList, ruleVolumeConfig.RouteDefinitionFile, ruleVolumeConfig.RouteLimitFile, ruleVolumeConfig.SetVolumeBlankAsMinimum, int64(config.BaseInfo.AllocationMethod))
				if pErr != nil {
					return false, pErr
				}
			} else if ruleType == uint32(rulevolume.LocVolumeTypeZone) {
				_, pErr := a.MaskRuleVolumeService.ParseZoneExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList, ruleVolumeConfig.ZoneDefinitionFile, ruleVolumeConfig.ZoneLimitFile, ruleVolumeConfig.SetVolumeBlankAsMinimum, int64(config.BaseInfo.AllocationMethod))
				if pErr != nil {
					return false, pErr
				}
			} else if ruleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
				_, pErr := a.MaskRuleVolumeService.ParseRouteExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList, ruleVolumeConfig.RouteDefinitionFile, ruleVolumeConfig.RouteLimitFile, ruleVolumeConfig.SetVolumeBlankAsMinimum, int64(config.BaseInfo.AllocationMethod))
				if pErr != nil {
					return false, pErr
				}
				_, pErr = a.MaskRuleVolumeService.ParseZoneExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList, ruleVolumeConfig.ZoneDefinitionFile, ruleVolumeConfig.ZoneLimitFile, ruleVolumeConfig.SetVolumeBlankAsMinimum, int64(config.BaseInfo.AllocationMethod))
				if pErr != nil {
					return false, pErr
				}
			} else if ruleType != uint32(rulevolume.LocVolumeTypeCountry) {
				return false, srerr.New(srerr.ParamErr, config, "max_capacity_enable or min_volume_enable toggle on, local volume type un-support. ruleType=%v", ruleType)
			}
		}
	}
	if config.BaseInfo.ShopGroupChannelPriorityToggle {
		if len(config.ProductPriorityConfigs) == 0 {
			return false, srerr.New(srerr.ParamErr, config, "product_priority_configs can't be nil when shop_group_channel_priority_toggle on.")
		}
		// 需要校验default shop group必须配置
		configDefaultProductPriority := false
		for _, productPriority := range config.ProductPriorityConfigs {
			if productPriority.ShopGroupId == constant2.DefaultShopGroupId {
				configDefaultProductPriority = true
				break
			}
		}
		if !configDefaultProductPriority {
			return false, srerr.New(srerr.ParamErr, config, "product_priority_configs required default shop group config when shop_group_channel_priority_toggle on. ")
		}
	}
	configs, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigByCondition(ctx, map[string]interface{}{
		"task_status in (?)":    []int{constant.TaskConfigStatusProcess, constant.TaskConfigStatusPending},
		"allocation_method = ?": config.BaseInfo.AllocationMethod,
	})
	if err != nil {
		return false, srerr.New(srerr.ParamErr, config, "Validate error, please contract administer.")
	}
	if len(configs) > allocation.MaxConcurrentWorkForecastTask {
		return false, srerr.New(srerr.ParamErr, config, "The concurrent processing number of task is %v, it is overlimit now.", allocation.MaxConcurrentWorkForecastTask)
	}
	for _, c := range configs {
		if c.MaskingProductID == config.BaseInfo.MaskProductId {
			return false, srerr.New(srerr.ParamErr, config, "It is required that only one processing status task of same masking product exist . masking product id:%v", config.BaseInfo.MaskProductId)
		}
	}
	return true, nil
}

func (a AllocateForecastTaskConfigServiceImpl) UpdateForecastTaskConfig(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) *srerr.Error {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	allocateForecastTaskConfigTab, err := a.convertUpdateTaskConfigToAdminData(ctx, config)
	if err != nil {
		return err
	}
	allocateForecastTaskConfigTab.OperatedBy = operateBy
	allocateForecastTaskConfigTab.MTime = uint32(recorder.Now(ctx).Unix())
	//if update task to process, need validate and fulfill soft rule
	if config.BaseInfo.Status == constant.TaskConfigStatusProcess {
		// validate task config
		if validateResult, err := a.ValidateProcessTaskConfig(ctx, config); !validateResult {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--validate process task config failed. config=%v, err=%v", *config, err)
			return err
		}
		//SSCSMR-1480：校验前置，如果没有allocate rule，就获取online rule，如果获取失败则不允许预测
		if !config.BaseInfo.LocalSoftCriteriaToggle {
			// 未打开soft criteria 开关的任务，使用线上规则进行预测
			req := &schema.SoftRuleSyncReq{
				MaskingProductId: int64(config.BaseInfo.MaskProductId),
			}
			onlineAllocateRule, gErr := a.GetOnlineSoftRule(ctx, req)
			if gErr != nil {
				logger.CtxLogErrorf(ctx, "CreateForecastTaskConfig|GetOnlineSoftRule fail|err=%v", gErr)
				return srerr.With(srerr.GetOnlineAllocateRuleError, nil, gErr)
			}
			allocationRuleConfig, mErr := json.Marshal(onlineAllocateRule.AllocationRuleConfig)
			if mErr != nil {
				logger.CtxLogErrorf(ctx, "marshal online allocate rule err:%v", mErr)
				return srerr.With(srerr.MarshalOnlineAllocateRuleError, nil, mErr)
			}
			//SSCSMR-1480:将后续的赋值前置到本处
			allocateForecastTaskConfigTab.AllocationRuleConfig = allocationRuleConfig
		}
	}
	err = a.AllocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, allocateForecastTaskConfigTab)
	if err == nil && allocateForecastTaskConfigTab.ConfigSyncStatus == constant.WaitToSyncConfigStatus {
		config.BaseInfo.Id = int(allocateForecastTaskConfigTab.Id)
		// 需要发一条消息到kafka，由定时任务判断是否需要异步生成volume 配置
		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		msg, _ := jsoniter.Marshal(config)
		if err := kafkahelper.DeliveryMessage(ctx, namespace, constant2.TaskNameLoadForecastVolumeConfig, msg, nil, ""); err != nil {
			return srerr.With(srerr.SendMessageError, nil, err)
		}
		logger.CtxLogInfof(ctx, "Allocate Forecast--config = %v", *config)
	}
	//if config status == process (corresponding to pending in tab), need to split sub-tasks
	if config.BaseInfo.Status == constant.TaskConfigStatusProcess {
		if err = a.SplitSubTaskByMainTask(ctx, allocateForecastTaskConfigTab); err != nil {
			//拆分失败需要更新main task到failed
			allocateForecastTaskConfigTab.Status = constant.TaskConfigStatusFailed
			if uErr := a.AllocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, allocateForecastTaskConfigTab); uErr != nil {
				logger.CtxLogErrorf(ctx, "update task to 'failed' status, err:%v", uErr)
				return uErr
			}
		}
	}
	return err
}

func (a AllocateForecastTaskConfigServiceImpl) DelForecastTaskConfig(ctx context.Context, taskId int64) *srerr.Error {
	taskConfig, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, taskId)
	if err != nil {
		return err
	}
	if taskConfig.Status != constant.TaskConfigStatusDraft {
		return srerr.New(srerr.DataErr, taskId, "The status of task must be draft. but task_id =%v is not.", taskId)
	}
	err = a.AllocateForecastTaskConfigRepo.DelForecastTaskConfig(ctx, taskConfig.Id)
	return err
}

func (a AllocateForecastTaskConfigServiceImpl) GetForecastTaskConfigById(ctx context.Context, taskId int64) (*schema.AllocateForecastTaskConfigResp, *srerr.Error) {
	allocateForecastTaskConfigTab, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, taskId)
	if err != nil {
		return nil, err
	}
	resp, err := a.convertTaskConfigToRespData(ctx, allocateForecastTaskConfigTab)
	return resp, err
}

func (a AllocateForecastTaskConfigServiceImpl) ListForecastTaskConfigs(ctx context.Context, req *schema.ForecastListRequest) (*schema.ForecastListResp, *srerr.Error) {
	info := &model.AllocateForecastTaskConfigTab{}
	info.MaskingProductID = req.MaskProductId
	info.Status = req.Status
	info.AllocationMethod = req.AllocationMethod
	//SSCSMR-1480:这里不兼容检索pending态的任务
	data, total, err := a.AllocateForecastTaskConfigRepo.ListForecastTaskConfigs(ctx, info, (req.Pageno-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}
	if len(data) == 0 {
		ret := &schema.ForecastListResp{}
		ret.Total = total
		ret.Pageno = req.Pageno
		ret.Size = req.Limit
		return ret, err
	}
	ret, err := a.convertTaskConfigsToRespData(ctx, data)
	if err != nil {
		return nil, err
	}
	ret.Total = total
	ret.Pageno = req.Pageno
	ret.Size = req.Limit
	return ret, err
}

func (a AllocateForecastTaskConfigServiceImpl) CopyForecastTaskConfigById(ctx context.Context, taskId int64) (*schema.AllocateForecastTaskConfigResp, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	taskConfig, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, taskId)

	if err != nil {
		return nil, err
	}
	if taskConfig.Status == constant.TaskConfigStatusDraft {
		return nil, srerr.New(srerr.DataErr, taskId, "The status of task can't be draft. but task_id =%v status is draft.", taskId)
	}
	taskConfigCopy := &model.AllocateForecastTaskConfigTab{}
	taskConfigCopy.TaskName = fmt.Sprintf("%s-copy", taskConfig.TaskName)
	taskConfigCopy.Status = constant.TaskConfigStatusDraft
	taskConfigCopy.OperatedBy = operateBy
	taskConfigCopy.CTime = uint32(recorder.Now(ctx).Unix())
	taskConfigCopy.MTime = uint32(recorder.Now(ctx).Unix())
	taskConfigCopy.ProductPriorityConfigs = taskConfig.ProductPriorityConfigs
	taskConfigCopy.OrderPaidTime = taskConfig.OrderPaidTime
	taskConfigCopy.MaskingProductID = taskConfig.MaskingProductID
	taskConfigCopy.RunSoftRuleOnlyToggle = taskConfig.RunSoftRuleOnlyToggle
	taskConfigCopy.RecalculateShippingFeeToggle = taskConfig.RecalculateShippingFeeToggle
	taskConfigCopy.ShopGroupChannelPriorityToggle = taskConfig.ShopGroupChannelPriorityToggle
	taskConfigCopy.LocalSoftCriteriaToggle = taskConfig.LocalSoftCriteriaToggle
	taskConfigCopy.AllocationRuleConfig = taskConfig.AllocationRuleConfig

	taskConfigCopy.AllocationMethod = taskConfig.AllocationMethod
	taskConfigCopy.BatchSizeList = taskConfig.BatchSizeList
	taskConfigCopy.BatchAllocationRuleConfig = taskConfig.BatchAllocationRuleConfig
	err = a.AllocateForecastTaskConfigRepo.CreateForecastTaskConfig(ctx, taskConfigCopy)
	return nil, err
}

func (a AllocateForecastTaskConfigServiceImpl) GetForecastTaskConfigsByStatus(ctx context.Context, status int) ([]*schema.AllocateForecastTaskConfigEntity, *srerr.Error) {
	info := &model.AllocateForecastTaskConfigTab{}
	info.Status = status
	configs, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	ret := make([]*schema.AllocateForecastTaskConfigEntity, 0)
	for _, cf := range configs {
		if cf == nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--convertTaskConfigToEntity err. config is nil, configs=%v", configs)
			continue
		}
		configEntity, err := a.convertTaskConfigToEntity(ctx, cf)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--convertTaskConfigToEntity err. config=%s,error=%+v", cf.Id, *err)
			continue
		}
		ret = append(ret, configEntity)
	}
	return ret, nil
}

func (a *AllocateForecastTaskConfigServiceImpl) GetForecastTaskConfigEntityById(ctx context.Context, id int64) (*schema.AllocateForecastTaskConfigEntity, *srerr.Error) {
	configTab, err := a.AllocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, id)
	if err != nil {
		return nil, err
	}
	if configTab == nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--convertTaskConfigToEntity err. config is nil, config=%v", configTab)
		return nil, srerr.New(srerr.DataErr, nil, "masking forecast config tab is nil")
	}
	configEntity, err := a.convertTaskConfigToEntity(ctx, configTab)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--convertTaskConfigToEntity err. config=%s,error=%+v", configEntity.BaseInfo.Id, *err)
		return nil, srerr.With(srerr.TypeConvertErr, nil, err)
	}
	return configEntity, nil
}

func (a AllocateForecastTaskConfigServiceImpl) GetOnlineSoftRule(ctx context.Context, req *schema.SoftRuleSyncReq) (*schema.SoftRuleSyncResp, *srerr.Error) {
	// 查询运力配置
	maskProductRuleVolumeConfig, err := a.IMaskRuleVolumeRepo.GetRuleVolumeByMaskProductId(ctx, req.MaskingProductId, constant.SingleAllocate)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetOnlineSoftRule| get volume err:%v", err)
	}
	// 查询规则配置
	ruleTab, _ := a.AllocationRuleRepo.GetEffectiveRuleByMaskProductID(ctx, req.MaskingProductId, rule_mode.MplOrderRule)
	//if e != nil {
	//	return nil, err
	//}
	// 查询运费配置
	r := &schema.ShippingFeeReq{
		MaskingProductId: int(req.MaskingProductId),
	}
	shippingFeeConfig, err := a.AllocateShippingFeeRepo.QueryAllocatingRateFee(ctx, r)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetOnlineSoftRule| get shipping fee config err:%v", err)
	}
	resp, err := a.convertToSoftRuleSyncResp(ctx, req.MaskingProductId, maskProductRuleVolumeConfig, ruleTab, shippingFeeConfig)
	return resp, err
}

func (a *AllocateForecastTaskConfigServiceImpl) GetOnlineProductPriority(ctx context.Context,
	req *schema.ProductPrioritySyncReq) (*schema.ProductPrioritySyncResp, *srerr.Error) {
	result := &schema.ProductPrioritySyncResp{}
	result.ProductPriorities = make([]*schema.ProductPriorityConfig, 0)
	// 查询线上product priority 配置
	var onlineProductPriorities []entity.ProductPriority
	onlineProductPriorities, err := a.priorityServer.GetActiveProductPrioritiesByProductId(ctx, int(req.MaskingProductId))
	if err != nil {
		return nil, err
	}
	// 取3pl预测标签下的shop group
	//从lps获取
	clientGroupList, gErr := a.LpsApi.GetClientGroupTabsByTag(ctx, uint64(lpsclient.ClientTag3PLMaskingForecast))
	if gErr != nil {
		return nil, gErr
	}
	clientGroupMap := make(map[string]*lpsclient.ClientGroupTab)
	for _, tab := range clientGroupList {
		clientGroupMap[tab.ClientGroupID] = tab
	}
	// 取线上product priority 配置与 3pl 预测标签下shop group 的交集
	for _, productPriority := range onlineProductPriorities {
		// shop group priority 模块需要把 default group 也同步过来，配置时，在 forecast tag 下是不会配置 default shop group，
		// 后端取交集时，即使 default shop group 没有交集也要加上
		if _, exist := clientGroupMap[strconv.FormatInt(productPriority.ShopGroupID, 10)]; exist || (productPriority.ShopGroupID == constant2.DefaultShopGroupId) {
			productPriorityEntity := &schema.ProductPriorityConfig{}
			productPriorityEntity.MaskProductId = int(productPriority.MaskProductID)
			productPriorityEntity.ShopGroupId = productPriority.ShopGroupID
			productPriorityEntity.RuleType = uint32(productPriority.RuleType)
			priorityDetails := make([]*schema.PriorityDetail, 0)
			for _, priority := range productPriority.ComponentPriorities {
				fulfillmentProduct, pErr := a.LpsApi.GetProductDetail(ctx, int(priority.ProductID))
				if pErr != nil {
					logger.CtxLogErrorf(ctx, "Allocate Forecast-- GetProductInfoBase err. ProductId == %v", priority.ProductID)
					continue
				}
				detail := &schema.PriorityDetail{}
				detail.Priority = int(priority.Priority)
				detail.ProductId = int(priority.ProductID)
				detail.Status = int(priority.Status)
				detail.Weightage = int(priority.Weightage)
				detail.Name = fulfillmentProduct.SellerDisplayName
				priorityDetails = append(priorityDetails, detail)
			}
			productPriorityEntity.PriorityDetails = priorityDetails
			result.ProductPriorities = append(result.ProductPriorities, productPriorityEntity)
		}
	}
	return result, nil
}

func (a *AllocateForecastTaskConfigServiceImpl) UpdateTaskWithCondition(ctx context.Context, condition map[string]interface{}, value map[string]interface{}, maskingProductId uint64) *srerr.Error {
	return a.AllocateForecastTaskConfigRepo.UpdateTaskWithCondition(ctx, condition, value, maskingProductId)
}

func (a *AllocateForecastTaskConfigServiceImpl) SplitSubTaskByMainTask(ctx context.Context, mainTaskTab *model.AllocateForecastTaskConfigTab) *srerr.Error {
	orderPaidTime := &schema.OrderPaidTime{}
	if len(mainTaskTab.OrderPaidTime) != 0 {
		if err := json.Unmarshal(mainTaskTab.OrderPaidTime, orderPaidTime); err != nil {
			return srerr.New(srerr.TypeConvertErr, nil, "SplitSubTaskById|OrderPaidTime convert err.")
		}
	}

	//SSCSMR-1499:拆分sub task
	subTasks, sErr := a.ForecastingSubTaskService.SplitMaskingTask(ctx, mainTaskTab, orderPaidTime)
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "split task into sub task err:%v", sErr)
		return srerr.With(srerr.SplitSubTaskError, nil, sErr)
	}
	if cErr := a.ForecastingSubTaskService.BatchCreateSubTasks(ctx, subTasks); cErr != nil {
		logger.CtxLogErrorf(ctx, "batch create sub task err:%v", cErr)
		return srerr.With(srerr.BatchCreateSubTaskError, nil, cErr)
	}
	return nil
}

func (a AllocateForecastTaskConfigServiceImpl) convertTaskConfigToRespData(ctx context.Context, data *model.AllocateForecastTaskConfigTab) (*schema.AllocateForecastTaskConfigResp, *srerr.Error) {
	resp := &schema.AllocateForecastTaskConfigResp{
		ProductPriorityConfigs: make([]*schema.ProductPriorityConfig, 0),
	}
	if data == nil {
		return nil, srerr.New(srerr.EmptyResultErr, nil, "not found task config.")
	}
	baseInfo := &schema.AllocateForecastBaseInfo{}
	baseInfo.Id = int(data.Id)
	baseInfo.MaskProductId = data.MaskingProductID
	baseInfo.TaskName = data.TaskName
	baseInfo.Status = data.Status
	//SSCSMR-1480:兼容pending
	if data.Status == constant.TaskConfigStatusPending {
		baseInfo.Status = constant.TaskConfigStatusProcess
	}
	baseInfo.LocalSoftCriteriaToggle = objutil.Int2Bool(data.LocalSoftCriteriaToggle)
	baseInfo.RunSoftRuleOnlyToggle = objutil.Int2Bool(data.RunSoftRuleOnlyToggle)
	baseInfo.ReCalcFee = objutil.Int2Bool(data.RecalculateShippingFeeToggle)
	baseInfo.ShopGroupChannelPriorityToggle = objutil.Int2Bool(data.ShopGroupChannelPriorityToggle)
	baseInfo.AllocationMethod = data.AllocationMethod
	orderPaidTime := &schema.OrderPaidTime{}
	if data.OrderPaidTime != nil {
		err := json.Unmarshal(data.OrderPaidTime, orderPaidTime)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.OrderPaidTime, "OrderPaidTime convert err.")
		}
	}
	if len(orderPaidTime.TimeSections) == 0 { //兼容前端，初始化空list
		emptySection := &schema.TimeSection{BeginTime: 0, EndTime: 0}
		orderPaidTime.TimeSections = []*schema.TimeSection{emptySection}
	}
	baseInfo.OrderPaidTime = orderPaidTime
	// 处理进度条
	if data.Status == constant.TaskConfigStatusProcess {
		if data.AllocationMethod == constant.BatchAllocate {
			baseInfo.TaskSchedule = GetTaskScheduleRatio(ctx, data.Id)
		} else {
			baseInfo.TaskSchedule = GetTaskSchedule(ctx, data.Id)
		}
	}
	if data.Status == constant.TaskConfigStatusComplete {
		baseInfo.TaskSchedule = constant2.TaskScheduleComplete
	}
	//填充batch size list
	batchSizeList := make([]schema.BatchSize, 0)
	if len(data.BatchSizeList) != 0 {
		if err := jsoniter.Unmarshal(data.BatchSizeList, &batchSizeList); err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, nil, "convert batch size list err:%v", err)
		}
	}
	//填充batch size中fix time的空字段
	for _, batchSize := range batchSizeList {
		if batchSize.FixedTime.FixedTimeUnitList == nil {
			batchSize.FixedTime.FixedTimeUnitList = make([]schema.FixedTimeUnit, 0)
		}
	}
	baseInfo.BatchSizeList = batchSizeList
	resp.BaseInfo = baseInfo
	resp.AllocationRuleConfig = &schema.AllocationRuleConfig{} //兼容batch allocate模式下single rule config为null的场景
	if data.AllocationRuleConfig != nil {
		allocationRuleConfig := &schema.AllocationRuleConfig{}
		err := json.Unmarshal(data.AllocationRuleConfig, allocationRuleConfig)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.AllocationRuleConfig, "AllocationRuleConfig convert err.")
		}

		// 兼容新旧数据
		if allocationRuleConfig.RuleDetail != nil {
			if allocationRuleConfig.RuleDetail.MinVolumeEnable && !allocationRuleConfig.RuleDetail.MinVolumeCountryEnable && !allocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable {
				allocationRuleConfig.RuleDetail.MinVolumeCountryEnable = allocationRuleConfig.RuleDetail.MinVolumeEnable
				allocationRuleConfig.RuleDetail.MinVolumeCountrySort = allocationRuleConfig.RuleDetail.MinVolumeSort
				if allocationRuleConfig.MaskProductRuleVolumeConfig.RuleType != uint32(rulevolume.LocVolumeTypeCountry) {
					allocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable = allocationRuleConfig.RuleDetail.MinVolumeEnable
					allocationRuleConfig.RuleDetail.MinVolumeZoneRouteSort = allocationRuleConfig.RuleDetail.MinVolumeSort
				}
			}
			if allocationRuleConfig.RuleDetail.MaxCapacityEnable && !allocationRuleConfig.RuleDetail.MaxCapacityCountryEnable && !allocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable {
				allocationRuleConfig.RuleDetail.MaxCapacityCountryEnable = allocationRuleConfig.RuleDetail.MaxCapacityEnable
				allocationRuleConfig.RuleDetail.MaxCapacityCountrySort = allocationRuleConfig.RuleDetail.MaxCapacitySort
				if allocationRuleConfig.MaskProductRuleVolumeConfig.RuleType != uint32(rulevolume.LocVolumeTypeCountry) {
					allocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable = allocationRuleConfig.RuleDetail.MaxCapacityEnable
					allocationRuleConfig.RuleDetail.MaxCapacityZoneRouteSort = allocationRuleConfig.RuleDetail.MaxCapacitySort
				}
			}
		}
		resp.AllocationRuleConfig = allocationRuleConfig
	}
	if data.ProductPriorityConfigs != nil {
		productPriorityConfigs := make([]*schema.ProductPriorityConfig, 0)
		err := json.Unmarshal(data.ProductPriorityConfigs, &productPriorityConfigs)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.ProductPriorityConfigs, "ProductPriorityConfigs convert err.")
		}
		// 增加过滤没有关联关系的fulfillment product
		filterProductPriorityConfig := make([]*schema.ProductPriorityConfig, 0)
		for _, productPriorityConfig := range productPriorityConfigs {
			for _, detail := range productPriorityConfig.PriorityDetails {
				fulfillmentProduct, pErr := a.LpsApi.GetProductDetail(ctx, detail.ProductId)
				if pErr != nil {
					logger.CtxLogErrorf(ctx, "Allocate Forecast-- GetProductInfoBase err. ProductId == %v", detail.ProductId)
					continue
				}
				detail.Name = fulfillmentProduct.SellerDisplayName
			}
			filterProductPriorityConfig = append(filterProductPriorityConfig, productPriorityConfig)
		}
		resp.ProductPriorityConfigs = filterProductPriorityConfig
	}
	//填充batch allocation rule config
	if len(data.BatchAllocationRuleConfig) != 0 {
		if err := jsoniter.Unmarshal(data.BatchAllocationRuleConfig, &resp.BatchAllocationRuleConfig); err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, nil, "convert batch allocation rule config err:%v", err)
		}
	}
	if resp.BatchAllocationRuleConfig.CountryVolumeDetailList == nil {
		resp.BatchAllocationRuleConfig.CountryVolumeDetailList = make([]schema.CountryVolumeDetail, 0)
	}
	if resp.BatchAllocationRuleConfig.ShippingFeeConfigList == nil {
		resp.BatchAllocationRuleConfig.ShippingFeeConfigList = make([]schema.BatchShippingFeeConfig, 0)
	}
	//装填product name
	productNameMap, gErr := a.LpsApi.GetAllProductIdNameList(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast -- get all product name list err:%v", gErr)
	} else {
		for i := 0; i < len(resp.BatchAllocationRuleConfig.CountryVolumeDetailList); i++ {
			productId := resp.BatchAllocationRuleConfig.CountryVolumeDetailList[i].ProductId
			resp.BatchAllocationRuleConfig.CountryVolumeDetailList[i].ProductName = productNameMap[productId]
		}
	}
	//填充country min/max
	for i := 0; i < len(resp.BatchAllocationRuleConfig.CountryVolumeDetailList); i++ {
		countryDetail := resp.BatchAllocationRuleConfig.CountryVolumeDetailList[i]
		if countryDetail.MinDailyVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MinDailyVolume = batch_allocate.DefaultMinVolume
		}
		if countryDetail.MaxDailyVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MaxDailyVolume = batch_allocate.DefaultMaxVolume
		}
		if countryDetail.MaxDailyCodVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MaxDailyCodVolume = batch_allocate.DefaultMaxVolume
		}
		if countryDetail.MaxDailyBulkyVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MaxDailyBulkyVolume = batch_allocate.DefaultMaxVolume
		}
		if countryDetail.MaxDailyHighValueVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MaxDailyHighValueVolume = batch_allocate.DefaultMaxVolume
		}
		if countryDetail.MaxDailyDgVolume == batch_allocate.DefaultInputVolume {
			countryDetail.MaxDailyDgVolume = batch_allocate.DefaultMaxVolume
		}
		resp.BatchAllocationRuleConfig.CountryVolumeDetailList[i] = countryDetail
	}

	return resp, nil

}

func (a AllocateForecastTaskConfigServiceImpl) convertTaskConfigsToRespData(ctx context.Context, data []*model.AllocateForecastTaskConfigTab) (*schema.ForecastListResp, *srerr.Error) {
	ret := &schema.ForecastListResp{
		List: make([]*schema.ForecastDisplayEntity, 0),
	}
	checkForceDeployTaskIds := make([]int, 0)
	entities := make([]*schema.ForecastDisplayEntity, 0)
	for _, d := range data {
		if constant.TaskConfigStatusComplete == d.Status {
			// 完成预测的task，需要计算其blocked 率
			checkForceDeployTaskIds = append(checkForceDeployTaskIds, int(d.Id))
		}
	}
	totalMap := make(map[int]int, 0)
	blockedMap := make(map[int]int, 0)
	if len(checkForceDeployTaskIds) > 0 {
		rankTypes := []int{constant.AllocateForecastRankTypeEnum[constant.Blocked], constant.AllocateForecastRankTypeEnum[constant.Overall]}
		rankList, err := a.AllocateForecastRankRepo.BatchGetForecastRankList(ctx, checkForceDeployTaskIds, rankTypes)
		if err != nil {
			return nil, err
		}
		for _, rank := range rankList {
			if value, exist := totalMap[int(rank.ForecastTaskId)]; exist {
				totalMap[int(rank.ForecastTaskId)] = value + rank.OrderQuantity
			} else {
				totalMap[int(rank.ForecastTaskId)] = rank.OrderQuantity
			}
			if rank.RankType == constant.AllocateForecastRankTypeEnum[constant.Blocked] {
				if value, exist := blockedMap[int(rank.ForecastTaskId)]; exist {
					blockedMap[int(rank.ForecastTaskId)] = value + rank.OrderQuantity
				} else {
					blockedMap[int(rank.ForecastTaskId)] = rank.OrderQuantity
				}
			}
		}
	}
	for _, d := range data {
		maskProduct, err := a.LpsApi.GetProductDetail(ctx, d.MaskingProductID)
		if err != nil {
			return nil, err
		}
		e := &schema.ForecastDisplayEntity{}
		e.Id = d.Id
		e.TaskName = d.TaskName
		e.MaskProductId = d.MaskingProductID
		e.MaskProductName = maskProduct.SellerDisplayName
		e.Status = d.Status
		//SSCSMR-1480:兼容pending
		if d.Status == constant.TaskConfigStatusPending {
			e.Status = constant.TaskConfigStatusProcess
		}
		e.CompleteTime = d.CompleteTime
		e.LatestDeployTime = d.EffectiveStartTime
		e.Operator = d.OperatedBy
		e.Mtime = d.MTime
		e.DeployFailedDesc = d.DeployFailedDesc
		e.AllocationMethod = d.AllocationMethod
		displayForceDeploy := false
		if d.Status == constant.TaskConfigStatusComplete {
			if value, exist := blockedMap[int(d.Id)]; exist {
				totalValue := totalMap[int(d.Id)]
				errorRateConfig := configutil.GetAllocateForecastErrorRateConfig(ctx)
				if errorRateConfig.ErrorRate > 0 && (value*100/totalValue) > errorRateConfig.ErrorRate {
					displayForceDeploy = true
				}
			}
		}
		e.DisplayForceDeploy = &displayForceDeploy
		if d.DeployConfigDetail != nil {
			deployedDetails := &schema.DeployedDetails{}
			err := json.Unmarshal(d.DeployConfigDetail, deployedDetails)
			if err != nil {
				return nil, srerr.New(srerr.TypeConvertErr, d.DeployConfigDetail, "AllocationRuleConfig convert err.")
			}
			e.DeployedDetails = deployedDetails
		}
		// 查询任务进度
		if d.Status == constant.TaskConfigStatusProcess {
			if e.AllocationMethod == constant.BatchAllocate {
				e.TaskSchedule = GetTaskScheduleRatio(ctx, e.Id)
			} else {
				e.TaskSchedule = GetTaskSchedule(ctx, e.Id)
			}
		}
		if d.Status == constant.TaskConfigStatusComplete {
			e.TaskSchedule = constant2.TaskScheduleComplete
		}
		entities = append(entities, e)
	}
	ret.List = entities
	return ret, nil
}

func GetTaskSchedule(ctx context.Context, taskId int64) int64 {
	totalOrderNum, err := redisutil.GetInt(ctx, fmt.Sprintf(constant2.TaskScheduleTotalOrderNum, taskId))
	if err != nil || totalOrderNum == 0 {
		logger.CtxLogInfof(ctx, "get task schedule total order num error, taskId=%v", taskId)
		return 0
	}
	currentOrderNum, err := redisutil.GetInt(ctx, fmt.Sprintf(constant2.TaskScheduleCurrentOrderNum, taskId))
	if err != nil {
		logger.CtxLogInfof(ctx, "get task schedule current order num error, taskId=%v", taskId)
		return 0
	}
	return int64(currentOrderNum * 100 / totalOrderNum)
}

func GetTaskScheduleRatio(ctx context.Context, taskId int64) int64 {
	ratio, err := redisutil.GetFloat64(ctx, fmt.Sprintf(constant2.TaskScheduleRatio, taskId))
	if err != nil || ratio == 0 {
		logger.CtxLogInfof(ctx, " taskId=%d, get task schedule ratio error:%v", taskId, err)
		return 0
	}
	return int64(ratio * 100)
}

func (a AllocateForecastTaskConfigServiceImpl) convertDraftTaskConfigToAdminData(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) (*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	allocateForecastTaskConfigTab := &model.AllocateForecastTaskConfigTab{
		//兼容batch
		AllocationMethod:          constant.AllocateMethodSingle,
		BatchAllocationRuleConfig: make([]byte, 0),
		BatchSizeList:             make([]byte, 0),
	}
	allocateForecastTaskConfigTab.TaskName = config.BaseInfo.TaskName
	allocateForecastTaskConfigTab.MaskingProductID = config.BaseInfo.MaskProductId
	allocateForecastTaskConfigTab.ShopGroupChannelPriorityToggle = objutil.Bool2Int(config.BaseInfo.ShopGroupChannelPriorityToggle)
	allocateForecastTaskConfigTab.LocalSoftCriteriaToggle = objutil.Bool2Int(config.BaseInfo.LocalSoftCriteriaToggle)
	allocateForecastTaskConfigTab.LocalSoftCriteriaToggle = objutil.Bool2Int(config.BaseInfo.LocalSoftCriteriaToggle)
	allocateForecastTaskConfigTab.RunSoftRuleOnlyToggle = objutil.Bool2Int(config.BaseInfo.RunSoftRuleOnlyToggle)
	allocateForecastTaskConfigTab.RecalculateShippingFeeToggle = objutil.Bool2Int(config.BaseInfo.ReCalcFee)
	allocateForecastTaskConfigTab.ConfigSyncStatus = constant.NoNeedToSyncConfigStatus
	if config.BaseInfo.OrderPaidTime != nil {
		orderPaidTime, err := json.Marshal(config.BaseInfo.OrderPaidTime)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "order_paid_time convert error. err:%v", err)
		}
		allocateForecastTaskConfigTab.OrderPaidTime = orderPaidTime
	}
	allocateForecastTaskConfigTab.Status = config.BaseInfo.Status
	if config.ProductPriorityConfigs != nil {
		productPriorityConfigs, err := json.Marshal(config.ProductPriorityConfigs)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "product_priority_configs convert error. err:%v", err)
		}
		allocateForecastTaskConfigTab.ProductPriorityConfigs = productPriorityConfigs
	}
	if config.AllocationRuleConfig != nil {
		allocationRuleConfig, err := json.Marshal(config.AllocationRuleConfig)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "allocation_rule_config convert error. err:%v", err)
		}
		// 判断是否需要同步local volume 配置文件
		if config.AllocationRuleConfig.RuleDetail != nil {
			if config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable {
				config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
				config.AllocationRuleConfig.RuleDetail.MaxCapacityCountrySort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
					config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
					config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteSort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				}
			}
			if config.AllocationRuleConfig.RuleDetail.MinVolumeEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable {
				config.AllocationRuleConfig.RuleDetail.MinVolumeCountryEnable = config.AllocationRuleConfig.RuleDetail.MinVolumeEnable
				config.AllocationRuleConfig.RuleDetail.MinVolumeCountrySort = config.AllocationRuleConfig.RuleDetail.MinVolumeSort
				if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
					config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
					config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteSort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				}
			}
			if (config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable || config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable) && (config.AllocationRuleConfig.MaskProductRuleVolumeConfig != nil) &&
				(config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone)) {
				allocateForecastTaskConfigTab.ConfigSyncStatus = constant.WaitToSyncConfigStatus
			}
		}
		allocateForecastTaskConfigTab.AllocationRuleConfig = allocationRuleConfig
	}
	return allocateForecastTaskConfigTab, nil

}

func (a AllocateForecastTaskConfigServiceImpl) convertTaskConfigToAdminData(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) (*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	allocateForecastTaskConfigTab := &model.AllocateForecastTaskConfigTab{
		//兼容batch
		AllocationMethod:          constant.AllocateMethodSingle,
		BatchAllocationRuleConfig: make([]byte, 0),
		BatchSizeList:             make([]byte, 0),
	}
	if !config.BaseInfo.ShopGroupChannelPriorityToggle && !config.BaseInfo.LocalSoftCriteriaToggle {
		return nil, srerr.New(srerr.ParamErr, nil,
			"shop_group_channel_priority_toggle and local_soft_criteria_toggle can't both be closed")
	}
	if config.BaseInfo.ShopGroupChannelPriorityToggle && config.ProductPriorityConfigs == nil {
		return nil, srerr.New(srerr.ParamErr, nil,
			"ProductPriorityConfigs can't be nil when shop_group_channel_priority_toggle is opened")
	}
	if config.BaseInfo.LocalSoftCriteriaToggle && config.AllocationRuleConfig == nil {
		return nil, srerr.New(srerr.ParamErr, nil,
			"AllocationRuleConfig can't be nil when local_soft_criteria_toggle is opened")
	}
	allocateForecastTaskConfigTab.TaskName = config.BaseInfo.TaskName
	allocateForecastTaskConfigTab.MaskingProductID = config.BaseInfo.MaskProductId
	allocateForecastTaskConfigTab.ShopGroupChannelPriorityToggle = objutil.Bool2Int(config.BaseInfo.ShopGroupChannelPriorityToggle)
	allocateForecastTaskConfigTab.LocalSoftCriteriaToggle = objutil.Bool2Int(config.BaseInfo.LocalSoftCriteriaToggle)
	allocateForecastTaskConfigTab.RunSoftRuleOnlyToggle = objutil.Bool2Int(config.BaseInfo.RunSoftRuleOnlyToggle)
	allocateForecastTaskConfigTab.RecalculateShippingFeeToggle = objutil.Bool2Int(config.BaseInfo.ReCalcFee)
	allocateForecastTaskConfigTab.ConfigSyncStatus = constant.NoNeedToSyncConfigStatus
	if config.BaseInfo.OrderPaidTime != nil {
		orderPaidTime, err := json.Marshal(config.BaseInfo.OrderPaidTime)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "order_paid_time convert error. err:%v", err)
		}
		allocateForecastTaskConfigTab.OrderPaidTime = orderPaidTime
	} else {
		return nil, srerr.New(srerr.ParamErr, nil, "order_paid_time can't be nil")
	}
	allocateForecastTaskConfigTab.Status = config.BaseInfo.Status
	//SSCSMR-1480:兼容process态到pending态
	if config.BaseInfo.Status == constant.TaskConfigStatusProcess {
		allocateForecastTaskConfigTab.Status = constant.TaskConfigStatusPending
	}
	if config.ProductPriorityConfigs != nil {
		productPriorityConfigs, err := json.Marshal(config.ProductPriorityConfigs)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "product_priority_configs convert error. err:%v", err)
		}
		allocateForecastTaskConfigTab.ProductPriorityConfigs = productPriorityConfigs
	}
	if config.AllocationRuleConfig != nil {
		allocationRuleConfig, err := json.Marshal(config.AllocationRuleConfig)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "allocation_rule_config convert error. err:%v", err)
		}
		// 判断是否需要同步local volume 配置文件
		if config.AllocationRuleConfig.RuleDetail != nil {
			if config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable && !config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable {
				config.AllocationRuleConfig.RuleDetail.MaxCapacityCountryEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
				config.AllocationRuleConfig.RuleDetail.MaxCapacityCountrySort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
					config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
					config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteSort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				}
			}
			if config.AllocationRuleConfig.RuleDetail.MinVolumeEnable && !config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable && !config.AllocationRuleConfig.RuleDetail.MinVolumeCountryEnable {
				config.AllocationRuleConfig.RuleDetail.MinVolumeCountryEnable = config.AllocationRuleConfig.RuleDetail.MinVolumeEnable
				config.AllocationRuleConfig.RuleDetail.MinVolumeCountrySort = config.AllocationRuleConfig.RuleDetail.MinVolumeSort
				if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
					config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable = config.AllocationRuleConfig.RuleDetail.MaxCapacityEnable
					config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteSort = config.AllocationRuleConfig.RuleDetail.MaxCapacitySort
				}
			}
			if (config.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable || config.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable) &&
				(config.AllocationRuleConfig.MaskProductRuleVolumeConfig != nil) &&
				(config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) || config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone)) {
				allocateForecastTaskConfigTab.ConfigSyncStatus = constant.WaitToSyncConfigStatus
			}
		}
		allocateForecastTaskConfigTab.AllocationRuleConfig = allocationRuleConfig
	}
	return allocateForecastTaskConfigTab, nil
}

func (a AllocateForecastTaskConfigServiceImpl) convertUpdateTaskConfigToAdminData(ctx context.Context, config *schema.AllocateForecastTaskConfigRequest) (*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	if config.BaseInfo.Id == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "base_info id can't be zero.")
	}
	if config.BaseInfo.Status == constant.TaskConfigStatusDraft {
		allocateForecastTaskConfigTab, err := a.convertDraftTaskConfigToAdminData(ctx, config)
		if err != nil {
			return nil, err
		}
		allocateForecastTaskConfigTab.Id = int64(config.BaseInfo.Id)
		return allocateForecastTaskConfigTab, nil
	}
	allocateForecastTaskConfigTab, err := a.convertTaskConfigToAdminData(ctx, config)
	if err != nil {
		return nil, err
	}
	allocateForecastTaskConfigTab.Id = int64(config.BaseInfo.Id)
	return allocateForecastTaskConfigTab, err
}

func (a AllocateForecastTaskConfigServiceImpl) convertTaskConfigToEntity(ctx context.Context, data *model.AllocateForecastTaskConfigTab) (*schema.AllocateForecastTaskConfigEntity, *srerr.Error) {
	resp := &schema.AllocateForecastTaskConfigEntity{
		ProductPriorityConfigs: make([]*schema.ProductPriorityConfig, 0),
	}
	if data == nil {
		return nil, srerr.New(srerr.EmptyResultErr, nil, "not found task config.")
	}
	baseInfo := &schema.AllocateForecastBaseInfo{}
	baseInfo.Id = int(data.Id)
	baseInfo.TaskName = data.TaskName
	baseInfo.Status = data.Status
	baseInfo.OperatedBy = data.OperatedBy
	baseInfo.EffectiveStartTime = data.EffectiveStartTime
	baseInfo.LocalSoftCriteriaToggle = objutil.Int2Bool(data.LocalSoftCriteriaToggle)
	baseInfo.ReCalcFee = objutil.Int2Bool(data.RecalculateShippingFeeToggle)
	baseInfo.RunSoftRuleOnlyToggle = objutil.Int2Bool(data.RunSoftRuleOnlyToggle)
	baseInfo.MaskProductId = data.MaskingProductID
	baseInfo.ShopGroupChannelPriorityToggle = objutil.Int2Bool(data.ShopGroupChannelPriorityToggle)
	baseInfo.ConfigSyncStatus = data.ConfigSyncStatus
	if data.OrderPaidTime != nil {
		orderPaidTime := &schema.OrderPaidTime{}
		err := json.Unmarshal(data.OrderPaidTime, orderPaidTime)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.OrderPaidTime, "OrderPaidTime convert err.")
		}
		baseInfo.OrderPaidTime = orderPaidTime
	}
	resp.BaseInfo = baseInfo
	if data.AllocationRuleConfig != nil {
		allocationRuleConfig := &schema.AllocationRuleConfig{}
		err := json.Unmarshal(data.AllocationRuleConfig, allocationRuleConfig)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.AllocationRuleConfig, "AllocationRuleConfig convert err.")
		}
		allocationRuleConfig.MaskProductId = baseInfo.MaskProductId
		if allocationRuleConfig.MaskProductRuleVolumeConfig != nil {
			allocationRuleConfig.MaskProductRuleVolumeConfig.MaskProductId = baseInfo.MaskProductId
		}

		resp.AllocationRuleConfig = allocationRuleConfig
	}
	if data.ProductPriorityConfigs != nil {
		productPriorityConfigs := make([]*schema.ProductPriorityConfig, 0)
		err := json.Unmarshal(data.ProductPriorityConfigs, &productPriorityConfigs)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, data.ProductPriorityConfigs, "ProductPriorityConfigs convert err.")
		}
		resp.ProductPriorityConfigs = productPriorityConfigs
	}
	return resp, nil
}

func (a AllocateForecastTaskConfigServiceImpl) convertToSoftRuleSyncResp(
	ctx context.Context, maskingProductId int64, ruleVolumeTab *rulevolume.MaskRuleVolumeTab,
	ruleTab *rule.MaskAllocationRuleTab, allocatingRateFeeListResp *chargeentity.AllocatingRateFeeListResp) (
	*schema.SoftRuleSyncResp, *srerr.Error) {
	resp := &schema.SoftRuleSyncResp{
		AllocationRuleConfig: &schema.AllocationRuleConfig{
			MaskProductId: int(maskingProductId),
		},
	}
	if ruleVolumeTab != nil {
		maskProductRuleVolumeConfig := &schema.MaskProductRuleVolumeConfig{
			MaskProductId:       ruleVolumeTab.MaskProductID,
			RouteDefinitionFile: ruleVolumeTab.RouteDefinitionFile,
			RouteLimitFile:      ruleVolumeTab.RouteLimitFile,
			ZoneDefinitionFile:  ruleVolumeTab.ZoneDefinitionFile,
			ZoneLimitFile:       ruleVolumeTab.ZoneLimitFile,
			RuleType:            uint32(ruleVolumeTab.RuleType),
			RuleStatus:          uint32(rule.MaskRuleStatusActive),
		}
		unmarshalErr := jsoniter.Unmarshal(ruleVolumeTab.DefaultVolumeLimitByte, &maskProductRuleVolumeConfig.DefaultVolumeLimit)
		if unmarshalErr != nil {
			return nil, srerr.New(srerr.ServerErr, unmarshalErr, "Unmarshal volume limit config task config err.")
		}
		resp.AllocationRuleConfig.MaskProductRuleVolumeConfig = maskProductRuleVolumeConfig
	}
	if ruleTab != nil {
		ruleDetail := &schema.RuleDetail{}
		err := jsoniter.Unmarshal(ruleTab.Rule, ruleDetail)
		if err != nil {
			return nil, srerr.New(srerr.TypeConvertErr, err, "convertToSoftRuleSyncResp, err=%v.", err)
		}
		resp.AllocationRuleConfig.RuleDetail = ruleDetail
	}
	if allocatingRateFeeListResp != nil {
		data := allocatingRateFeeListResp.Data
		if data == nil {
			return resp, nil
		}
		shippingFeeConfig := make([]*schema.ShippingFeeConfig, 0)
		for _, item := range data.List {
			if item.Status != allocation.FeeRuleStatusActive {
				continue
			}
			s := &schema.ShippingFeeConfig{
				ProductId: item.ChannelId,
				RateId:    item.RateId,
				WmsFlag:   item.WmsFlag,
			}
			shippingFeeConfig = append(shippingFeeConfig, s)
		}
		resp.AllocationRuleConfig.ShippingFeeConfig = shippingFeeConfig
	}
	return resp, nil
}

func (a AllocateForecastTaskConfigServiceImpl) CreateBASubTaskByTaskConfig(ctx context.Context) *srerr.Error {
	taskList, err := a.GetPendingBatchTask(ctx)
	if err != nil {
		return err
	}
	if len(taskList) == 0 {
		logger.CtxLogErrorf(ctx, "AllocateForecastTaskConfigServiceImpl|empty list")
		return nil
	}

	//拆分sub task
	for _, task := range taskList {
		//SSCSMR-1698:判断是否解析volume完成 -> 使用redis来记录
		value, gErr := redisutil.GetInt(ctx, repo.BatchVolumeKey(uint64(task.Id)))
		if gErr != nil || value == 0 {
			logger.CtxLogErrorf(ctx, "AnalyzeBatchVolume|task:%v is not ready, value:%v, err:%v, just continue", task.Id, value, gErr)
			continue
		}
		cerr := a.ForecastingSubTaskRepo.CreateSubTaskByTaskConfig(ctx, task)
		taskStatus := constant.TaskConfigStatusProcess
		if cerr != nil {
			logger.CtxLogErrorf(ctx, "AllocateForecastTaskConfigServiceImpl|create sub task err:%v", cerr)
			taskStatus = constant.TaskConfigStatusFailed
		}
		if err := a.UpdateTaskConfig(ctx, uint64(task.Id), map[string]interface{}{
			"task_status": taskStatus,
		}); err != nil {
			logger.CtxLogErrorf(ctx, "update forecast config failed %v", err)
			continue
		}
	}

	return nil
}

func (a AllocateForecastTaskConfigServiceImpl) GetPendingBatchTask(ctx context.Context) ([]*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	// 查找出batch allocate task且处于
	taskConfigList, terr := a.GeTaskConfigByConditions(ctx, map[string]interface{}{
		"task_status = ?":       constant.TaskConfigStatusPending,
		"allocation_method = ?": constant.AllocateMethodBatch,
	})
	if terr != nil {
		logger.CtxLogErrorf(ctx, "get pending status task config failed %v", terr)
		return nil, srerr.With(srerr.ParamErr, nil, terr)
	}

	return taskConfigList, nil
}

func (a AllocateForecastTaskConfigServiceImpl) GeTaskConfigByConditions(ctx context.Context, condition map[string]interface{}) ([]*model.AllocateForecastTaskConfigTab, error) {
	taskList := []*model.AllocateForecastTaskConfigTab{}
	if err := dbutil.Select(ctx, model.AllocateForecastTaskConfigHook, condition, &taskList); err != nil {
		logger.CtxLogErrorf(ctx, "get forecast task config failed %v", err)
		return nil, err
	}

	return taskList, nil
}

func (a AllocateForecastTaskConfigServiceImpl) UpdateTaskConfig(ctx context.Context, id uint64, condition map[string]interface{}) error {
	return dbutil.Update(ctx, model.AllocateForecastTaskConfigHook, map[string]interface{}{
		"id = ?": id,
	}, condition, dbutil.ModelInfo{})
}
