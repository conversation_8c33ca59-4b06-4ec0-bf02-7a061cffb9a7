package constant

const (
	Both  = 0
	NoWms = 1
	Wms   = 2
)

const (
	TaskConfigStatusDraft        = 1
	TaskConfigStatusProcess      = 2
	TaskConfigStatusComplete     = 3
	TaskConfigStatusFailed       = 4
	TaskConfigStatusDeploying    = 5
	TaskConfigStatusDeployFailed = 6
	TaskConfigStatusDeployed     = 7
	TaskConfigStatusPending      = 8
)

const (
	Overall    = "Overall"
	ShopGroup  = "ShopGroup"
	Zone       = "Zone"
	OriginZone = "OriginZone"
	DestZone   = "DestZone"
	Route      = "Route"
	Blocked    = "Blocked"
	Sum        = "Sum"
)

var AllocateForecastRankTypeEnum = map[string]int{
	Overall:    0,
	ShopGroup:  1,
	Zone:       2,
	Route:      3,
	Blocked:    4,
	Sum:        5,
	OriginZone: 6,
	DestZone:   7,
}

const (
	WaitToSyncConfigStatus   = 1
	CompleteSyncConfigStatus = 2
	FailedToSyncConfigStatus = 3
	NoNeedToSyncConfigStatus = 4
)

// batch size type
const (
	FixedByQuantity = 1
	FixedByTime     = 2
	FixedByQAndT    = 3 //fixed by quantity and time
)

const (
	OperationCreate = "create"
	OperationUpdate = "update"
)

const (
	FillBlankMax  = 1
	FillBlankZero = 2
)

const (
	VolumeTypeZone  = 1
	VolumeTypeRoute = 2
)

const (
	ParseCapacity = 1
	ParseTarget   = 2
)

const (
	VolumeSceneTarget = "target" //运力目标
)

const (
	ReqCodeTypeRoute        = 1
	ReqCodeTypeZone         = 2
	ReqCodeTypeZoneAndRoute = 3
)

const (
	UpperZone  = "Zone"
	UpperRoute = "Route"
)

const (
	RankTypeOverAll   = 0
	RankTypeShopGroup = 1
	RankTypeZone      = 2
	RankTypeRoute     = 3
)

const (
	ZoneRouteCodeAll    = "all"
	ZoneRouteCodeNotAll = "not-all"
)

const (
	ResultTypeOverAll = 1
	ResultTypeZone    = 2
	ResultTypeRoute   = 3
)

const (
	AllocateMethodSingle = 1
	AllocateMethodBatch  = 2
)

var ReqResultTypeMap = map[int]int{
	ReqCodeTypeZone:  ResultTypeZone,
	ReqCodeTypeRoute: ResultTypeRoute,
}

const (
	LevelTypeOverall   = 1
	LevelTypeZoneRoute = 2
)

const CreateBASubTaskPrefix = "create_BA_sub_task_prefix"
const RedisKeyUnExpired = 0

const (
	SingleAllocate = 1
	BatchAllocate  = 2
)

const (
	TargetZoneKey  = "zone:%d:%s"        // DestLocationID:DestPostcode
	TargetRouteKey = "route:%d:%s:%d:%s" // OriginLocationID:OriginPostcode:DestLocationID:DestPostcode
)
