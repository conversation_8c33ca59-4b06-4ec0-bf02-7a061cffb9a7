package forecast

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"github.com/google/wire"
)

var AllocateForecastTaskConfigServiceProviderSet = wire.NewSet(
	service.NewAllocateForecastTaskConfigServiceImpl,
	wire.Bind(new(service.AllocateForecastTaskConfigService), new(*service.AllocateForecastTaskConfigServiceImpl)),
)

var AllocateRankProviderSet = wire.NewSet(
	service.NewAllocateRankServiceImpl,
	wire.Bind(new(service.AllocateRankService), new(*service.AllocateRankServiceImpl)),
)
