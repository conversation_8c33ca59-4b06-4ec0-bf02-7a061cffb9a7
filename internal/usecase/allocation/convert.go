package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

func (a *AllocationServiceImpl) addFulfillmentShippingFee(ctx context.Context, req *pb.MaskingReq, data *order.BatchAllocateHoldOrderTab) *srerr.Error {
	for index, eachFulfillmentHardResult := range data.FulfillmentHardResult {
		// get shipping fee
		shippingFee := a.getShippingFeeForAllocatedProduct(ctx, req.GetFulfillmentShippingFeeInfoList(), int64(eachFulfillmentHardResult.FulfillmentProductId))
		// add shipping fee info
		data.FulfillmentHardResult[index].FulfillmentShippingFee = shippingFee.FulfillmentShippingFee
		data.FulfillmentHardResult[index].FulfillmentShippingFeeDetail = shippingFee.Detail
	}
	return nil
}

const noShippingFee float64 = -1

func (a *AllocationServiceImpl) initHoldOrderData(ctx context.Context,
	req *pb.MaskingReq, preCalcFee *chargeentity.BatchAllocationESFResp) (*order.BatchAllocateHoldOrderTab, *srerr.Error) {

	var (
		fulfillmentHardResult          []order.FulfillmentHardResult
		fulfillmentListWithShippingFee []int64 // 代表可以计算出运费的渠道集合
	)

	// 运费检查以及数据转换
	if preCalcFee == nil || preCalcFee.Data == nil || len(preCalcFee.Data.AllocatingShippingFeeResult) == 0 {
		// 1.运费返回为空 —— 用req(入参)中硬性检验结果替代
		for _, fulfillmentProductId := range req.GetProductIds() {
			fulfillmentHardResult = append(fulfillmentHardResult, order.FulfillmentHardResult{
				FulfillmentProductId:  int(fulfillmentProductId),
				AllocationShippingFee: noShippingFee, // 初始化为-1，与SDK协议保持一致
			})
		}
		logger.CtxLogErrorf(ctx, "empty allocation shipping fee from charge system|rsp=%v|shipping fee result=%v", preCalcFee, fulfillmentHardResult)
	} else {
		// 2. 运费信息不为空 —— 使用运费计算结果
		for _, eachAllocatingShippingFee := range preCalcFee.Data.AllocatingShippingFeeResult {
			if eachAllocatingShippingFee != nil {
				// 数据不为nil
				if eachAllocatingShippingFee.RetCode == 0 {
					// 单个channel成功 - retCode为0
					fulfillmentHardResult = append(fulfillmentHardResult, order.FulfillmentHardResult{
						FulfillmentProductId:  int(eachAllocatingShippingFee.ProductId),
						AllocationShippingFee: eachAllocatingShippingFee.AllocatingShippingFee,
					})
					// 存入有运费的渠道集合中
					fulfillmentListWithShippingFee = append(fulfillmentListWithShippingFee, eachAllocatingShippingFee.ProductId)
				}
			} else {
				// 数据为nil - 这是一种错误的数据格式，应直接报错
				logger.CtxLogErrorf(ctx, "empty allocation shipping fee for each channel from charge system|rsp=%v", preCalcFee)
				return nil, srerr.New(srerr.FormatErr, nil, "empty shipping fee for each channel")
			}
		}
	}

	// 运费兜底逻辑 —— 如果部份渠道没有计算出运费，那么运费设置为-1
	for _, productId := range req.GetProductIds() {
		if !objutil.ContainInt64(fulfillmentListWithShippingFee, productId) {
			logger.CtxLogErrorf(ctx, "empty allocation shipping fee for from charge system|product_id=%v", productId)
			fulfillmentHardResult = append(fulfillmentHardResult, order.FulfillmentHardResult{
				FulfillmentProductId:  int(productId),
				AllocationShippingFee: noShippingFee, // 初始化为-1，与SDK协议保持一致
			})
		}
	}

	// 统一在这里填充Parcel Attribute
	parcelTypeAttrMap := a.getAllocateParcelTypeAttr(ctx, req.GetMaskingProductId(), req.GetProductIds(), req.GetOrderInfo(), req.GetProductDgInfoList())
	for index, f := range fulfillmentHardResult {
		attr, exist := parcelTypeAttrMap[int64(f.FulfillmentProductId)]
		if !exist {
			logger.CtxLogErrorf(ctx, "fulfillment product's parcel type attribute not found | product:%d", f.FulfillmentProductId)
			continue
		}
		fulfillmentHardResult[index].ParcelAttribute = order.ParcelAttribute{
			IsCod:       attr.IsCod,
			IsBulky:     attr.IsBulky,
			IsHighValue: attr.IsHighValue,
			IsDg:        attr.IsDg,
		}
	}

	// 订单信息转换
	orderId, convErr := strconv.Atoi(req.GetOrderInfo().GetOrderId())
	if convErr != nil {
		return nil, srerr.With(srerr.FormatErr, nil, convErr)
	}
	data := &order.BatchAllocateHoldOrderTab{
		OrderID:       uint64(orderId),
		Day:           timeutil.GetCurrentTime(ctx).Day(),
		MaskProductID: int(req.GetMaskingProductId()),
		PickupAddress: order.AddressInfo{
			Postcode:       req.GetOrderInfo().GetPickupAddress().GetPostalCode(),
			LocationIdList: req.GetOrderInfo().GetPickupAddress().GetLocationIds(),
		},
		DeliverAddress: order.AddressInfo{
			Postcode:       req.GetOrderInfo().GetDeliveryAddress().GetPostalCode(),
			LocationIdList: req.GetOrderInfo().GetDeliveryAddress().GetLocationIds(),
		},
		FulfillmentHardResult: fulfillmentHardResult,
		ShopID:                int64(req.GetOrderInfo().GetShopId()),
		RequestTimeStamp:      req.GetRequestTimeStamp(),
	}
	return data, nil
}

func (a *AllocationServiceImpl) getShippingFeeForAllocatedProduct(ctx context.Context,
	fulfillmentShippingFeeInfoList []*pb.FulfillmentShippingFeeInfo, allocatedProductId int64) order.FulfillmentShippingFeeInfo {

	// 遍历所有运费结果
	for _, eachItem := range fulfillmentShippingFeeInfoList {
		if eachItem.GetProductId() == allocatedProductId {
			// 查询到指定渠道，填充具体运费
			shippingFee := order.FulfillmentShippingFeeInfo{}
			shippingFee.FulfillmentShippingFee = eachItem.GetFulfillmentShippingFee()
			shippingFee.Detail = order.FulfillmentShippingFeeDetail{
				BasicShippingFee:        eachItem.GetDetail().GetBasicShippingFee(),
				InsuranceFee:            eachItem.GetDetail().GetInsuranceFee(),
				CodFee:                  eachItem.GetDetail().GetCodFee(),
				RemoteFee:               eachItem.GetDetail().GetRemoteFee(),
				FuelFee:                 eachItem.GetDetail().GetFuelFee(),
				VatFee:                  eachItem.GetDetail().GetVatFee(),
				AdditionalFee:           eachItem.GetDetail().GetAdditionalFee(),
				CodFeeWithVat:           eachItem.GetDetail().GetCodFeeWithVat(),
				VatForCodFee:            eachItem.GetDetail().GetVatForCodFee(),
				AdjustmentFee:           eachItem.GetDetail().GetAdditionalFee(),
				AdjustmentFeeWithoutCod: eachItem.GetDetail().GetAdjustmentFeeWithoutCod(),
			}
			// 提前返回运费结果
			return shippingFee
		}
	}

	// 查询不到对应运费，进行提示不报错（是否有运费取决于mask channel的settlement配置）
	logger.CtxLogInfof(ctx, "shipping fee not found for product=%v", allocatedProductId)
	return order.FulfillmentShippingFeeInfo{}
}
