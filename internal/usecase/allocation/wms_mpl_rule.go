package allocation

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

func GetRuleMode(ctx context.Context, reqType int) rule_mode.RuleMode {

	if reqType == AllocateRequest {
		return rule_mode.MplOrderRule
	}
	if !configutil.IsWmsRuleReady(ctx) {
		return rule_mode.MplOrderRule
	}
	return rule_mode.WmsOrderRule
}

func OrderTypeMappingRuleMode(ctx context.Context, orderType pb.OrderType) rule_mode.RuleMode {
	switch orderType {
	case pb.OrderType_MplOrderType:
		return GetRuleMode(ctx, AllocateRequest)
	case pb.OrderType_WmsOrderType:
		return GetRuleMode(ctx, EstimateMaskingChannelRequest)
	}
	return rule_mode.MplOrderRule
}
