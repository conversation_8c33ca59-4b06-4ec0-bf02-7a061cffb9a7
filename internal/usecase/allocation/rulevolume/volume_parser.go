package rulevolume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type CheckVolumeParser interface {
	ParseReqVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error)
}

type CountryVolumeParserImpl struct {
}

// 入参：FE request
// 出参：按mask聚合的volume info；；不同的mask product指向一份volume配置
func (c *CountryVolumeParserImpl) ParseReqVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	var (
		result            = make(map[uint64]*rulevolume.MaskRuleVolumeTab, 0)
		countryVolumeInfo = &rulevolume.MaskRuleVolumeTab{
			ID:                  req.ID,
			MaskProductID:       req.MaskProductID,
			RuleType:            req.RuleType,
			RuleMode:            req.RuleMode,
			AllocationMethod:    int(req.AllocationMethod),
			DefaultVolumeLimit:  req.DefaultVolumeLimit,
			GroupInfo:           req.GroupInfo,
			MaskCombinationMode: req.MaskCombinationMode,
			ShareVolume:         req.ShareVolume,
		}
	)
	result[req.ID] = countryVolumeInfo

	return result, nil
}

type ZoneVolumeParserImpl struct {
	MaskRuleVolumeService MaskRuleVolumeService
}

func (z *ZoneVolumeParserImpl) ParseReqVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	//需要跟ParseZoneExcel一样，校验Zone的合法新
	volumeTab := &rulevolume.MaskRuleVolumeTab{
		ID:                  req.ID,
		MaskProductID:       req.MaskProductID,
		RuleType:            req.RuleType,
		RuleMode:            req.RuleMode,
		AllocationMethod:    int(req.AllocationMethod),
		DefaultVolumeLimit:  req.DefaultVolumeLimit,
		MaskCombinationMode: req.MaskCombinationMode,
		ShareVolume:         req.ShareVolume,
		GroupInfo:           req.GroupInfo,
	}

	var (
		maskProductIDList  []int // mask combination mode，一个rule volume用多个mask product
		groupToProductList = make(map[string][]int)
		volumeRuleMap      = make(map[uint64]*rulevolume.MaskRuleVolumeTab, 0) // 用来聚合tab时
	)

	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDList = append(maskProductIDList, m.MaskProductID)
		}
	} else {
		maskProductIDList = []int{req.MaskProductID}
	}

	if req.ShareVolume {
		for _, groupInfo := range req.GroupInfo.FulfillmentProductGroupInfos {
			if _, exist := groupToProductList[groupInfo.GroupCode]; !exist {
				groupToProductList[groupInfo.GroupCode] = make([]int, 0, len(groupInfo.FulfillmentProductInfos))
			}
			for _, f := range groupInfo.FulfillmentProductInfos {
				groupToProductList[groupInfo.GroupCode] = append(groupToProductList[groupInfo.GroupCode], f.FulfillmentProductID)
			}
		}
	}

	// parse zone
	zoneTabs, pErr := z.MaskRuleVolumeService.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
	if pErr != nil {
		return nil, pErr
	}
	volumeTab.ZoneVolumes = append(volumeTab.ZoneVolumes, zoneTabs...)
	volumeRuleMap[req.ID] = volumeTab

	return volumeRuleMap, nil
}

type RouteVolumeParserImpl struct {
	MaskRuleVolumeService MaskRuleVolumeService
}

func (r *RouteVolumeParserImpl) ParseReqVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	//需要跟ParseRouteExcel一样，校验Route的合法新
	volumeTab := &rulevolume.MaskRuleVolumeTab{
		ID:                  req.ID,
		MaskProductID:       req.MaskProductID,
		RuleType:            req.RuleType,
		RuleMode:            req.RuleMode,
		AllocationMethod:    int(req.AllocationMethod),
		DefaultVolumeLimit:  req.DefaultVolumeLimit,
		MaskCombinationMode: req.MaskCombinationMode,
		ShareVolume:         req.ShareVolume,
		GroupInfo:           req.GroupInfo,
	}

	var (
		maskProductIDList  []int // mask combination mode，一个rule volume用多个mask product
		groupToProductList = make(map[string][]int)
		volumeRuleMap      = make(map[uint64]*rulevolume.MaskRuleVolumeTab, 0) // 用来聚合tab时
	)

	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDList = append(maskProductIDList, m.MaskProductID)
		}
	} else {
		maskProductIDList = []int{req.MaskProductID}
	}

	if req.ShareVolume {
		for _, groupInfo := range req.GroupInfo.FulfillmentProductGroupInfos {
			if _, exist := groupToProductList[groupInfo.GroupCode]; !exist {
				groupToProductList[groupInfo.GroupCode] = make([]int, 0, len(groupInfo.FulfillmentProductInfos))
			}
			for _, f := range groupInfo.FulfillmentProductInfos {
				groupToProductList[groupInfo.GroupCode] = append(groupToProductList[groupInfo.GroupCode], f.FulfillmentProductID)
			}
		}
	}
	// parse route
	routeTabs, pErr := r.MaskRuleVolumeService.ParseRouteExcel(
		ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
	if pErr != nil {
		return nil, pErr
	}
	volumeTab.RouteVolumes = append(volumeTab.RouteVolumes, routeTabs...)
	volumeRuleMap[req.ID] = volumeTab

	return volumeRuleMap, nil
}

type ZoneRouteVolumeParserImpl struct {
	MaskRuleVolumeService MaskRuleVolumeService
}

func (z *ZoneRouteVolumeParserImpl) ParseReqVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	volumeTab := &rulevolume.MaskRuleVolumeTab{
		ID:                  req.ID,
		MaskProductID:       req.MaskProductID,
		RuleType:            req.RuleType,
		RuleMode:            req.RuleMode,
		AllocationMethod:    int(req.AllocationMethod),
		DefaultVolumeLimit:  req.DefaultVolumeLimit,
		MaskCombinationMode: req.MaskCombinationMode,
		ShareVolume:         req.ShareVolume,
		GroupInfo:           req.GroupInfo,
	}

	var (
		maskProductIDList  []int // mask combination mode，一个rule volume用多个mask product
		groupToProductList = make(map[string][]int)
		volumeRuleMap      = make(map[uint64]*rulevolume.MaskRuleVolumeTab, 0) // 用来聚合tab时
	)

	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDList = append(maskProductIDList, m.MaskProductID)
		}
	} else {
		maskProductIDList = []int{req.MaskProductID}
	}

	if req.ShareVolume {
		for _, groupInfo := range req.GroupInfo.FulfillmentProductGroupInfos {
			if _, exist := groupToProductList[groupInfo.GroupCode]; !exist {
				groupToProductList[groupInfo.GroupCode] = make([]int, 0, len(groupInfo.FulfillmentProductInfos))
			}
			for _, f := range groupInfo.FulfillmentProductInfos {
				groupToProductList[groupInfo.GroupCode] = append(groupToProductList[groupInfo.GroupCode], f.FulfillmentProductID)
			}
		}
	}
	// parse zone
	zoneTabs, pErr := z.MaskRuleVolumeService.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
	if pErr != nil {
		return nil, pErr
	}
	volumeTab.ZoneVolumes = append(volumeTab.ZoneVolumes, zoneTabs...)

	// parse route
	routeTabs, pErr := z.MaskRuleVolumeService.ParseRouteExcel(
		ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
	if pErr != nil {
		return nil, pErr
	}
	volumeTab.RouteVolumes = append(volumeTab.RouteVolumes, routeTabs...)

	volumeRuleMap[req.ID] = volumeTab

	return volumeRuleMap, nil
}
