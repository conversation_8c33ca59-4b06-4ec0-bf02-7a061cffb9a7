package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type DumpInfo struct {
	RouteVolumeCount int64
	ZoneVolumeCount  int64
	RouteVolumeData  map[string]interface{}
	ZoneVolumeData   map[string]interface{}
}

var (
	globalDumpInfoMap  = make(map[uint64]DumpInfo)
	globalLastDumpTime int64
)

func DumpMaskLocationVolume() (map[string]interface{}, error) {
	var (
		ctx                 = context.TODO()
		routeVolumeDumpData = make(map[string]interface{})
		zoneVolumeDumpData  = make(map[string]interface{})
		dumpData            = make(map[string]interface{})
		ruleVolumes         = make([]*rulevolume.MaskRuleVolumeTab, 0)
		dumpedMaskProduct   = make(map[string]uint64) // KEY: MaskProductID:RuleMode:AllocationMethod, VALUE: VolumeRuleID
	)

	// 1. get active rule volumes
	condition := map[string]interface{}{
		"rule_status = ?":           rulevolume.MaskRuleVolumeStatusActive,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	if err := dbutil.Select(ctx, rulevolume.MaskRuleVolumeTabHook, condition, &ruleVolumes, dbutil.WithOrder("effective_start_time DESC")); err != nil {
		return nil, err
	}
	// 1.1 get last dump time
	var lastDumpTime int64
	if globalLastDumpTime > 0 {
		lastDumpTime = globalLastDumpTime

		// 1.2 remove globalDumpInfoMap when not exists in ruleVolumes
		ruleVolumeIdList := make([]uint64, 0)
		for _, ruleVolume := range ruleVolumes {
			ruleVolumeIdList = append(ruleVolumeIdList, ruleVolume.ID)
		}
		for ruleVolumeId := range globalDumpInfoMap {
			if !objutil.ContainsUint64(ruleVolumeIdList, ruleVolumeId) {
				delete(globalDumpInfoMap, ruleVolumeId)
			}
		}
	}
	globalLastDumpTime = timeutil.GetCurrentUnixTimeStamp(ctx)

	// 2. load related location volumes
	for _, ruleVolume := range ruleVolumes {
		if err := ruleVolume.Unmarshal(); err != nil {
			logger.CtxLogErrorf(ctx, "Unmarshal rule volume failed|id=%v,err=%v", ruleVolume.ID, err)
			continue
		}

		var needDumpMaskProductList []int
		for _, maskProduct := range ruleVolume.GetMaskProducts() {
			loadKey := fmt.Sprintf("%v-%v-%v", maskProduct, ruleVolume.RuleMode, ruleVolume.AllocationMethod)
			if loadedID, exist := dumpedMaskProduct[loadKey]; exist {
				// 该Mask有另外一个更新的Volume Rule的情况，就不需要Load该Mask了
				logger.CtxLogInfof(ctx, "mask product [%s] loaded rule volume [%d] before, no need load in rule volume [%d]",
					loadKey, loadedID, ruleVolume.ID)
				continue
			}
			needDumpMaskProductList = append(needDumpMaskProductList, maskProduct)
			dumpedMaskProduct[loadKey] = ruleVolume.ID
		}

		var (
			globalDumpInfo DumpInfo
			ok             bool
		)
		if globalDumpInfo, ok = globalDumpInfoMap[ruleVolume.ID]; !ok {
			globalDumpInfo = DumpInfo{
				RouteVolumeCount: 0,
				ZoneVolumeCount:  0,
				RouteVolumeData:  make(map[string]interface{}),
				ZoneVolumeData:   make(map[string]interface{}),
			}
			globalDumpInfoMap[ruleVolume.ID] = globalDumpInfo
		}
		// 2.1 load route volumes
		if ruleVolume.RuleType == rulevolume.LocVolumeTypeRoute {
			getRouteVolume(ctx, ruleVolume, &globalDumpInfo, routeVolumeDumpData, lastDumpTime, needDumpMaskProductList)
		} else if ruleVolume.RuleType == rulevolume.LocVolumeTypeZone {
			getZoneVolume(ctx, ruleVolume, &globalDumpInfo, zoneVolumeDumpData, lastDumpTime, needDumpMaskProductList)
		} else if ruleVolume.RuleType == rulevolume.LocVolumeTypeRouteAndZone {
			//这里是route&zone模式，所以要route和zone都加载
			getRouteVolume(ctx, ruleVolume, &globalDumpInfo, routeVolumeDumpData, lastDumpTime, needDumpMaskProductList)
			getZoneVolume(ctx, ruleVolume, &globalDumpInfo, zoneVolumeDumpData, lastDumpTime, needDumpMaskProductList)
		}

		globalDumpInfoMap[ruleVolume.ID] = globalDumpInfo
	}

	for key, value := range routeVolumeDumpData {
		dumpData[key] = value
	}
	for key, value := range zoneVolumeDumpData {
		dumpData[key] = value
	}

	logger.CtxLogInfof(ctx, "dumped mask product to rule volume id: %v", dumpedMaskProduct)

	return dumpData, nil
}

func getRouteVolume(
	ctx context.Context, volumeRule *rulevolume.MaskRuleVolumeTab, globalDumpInfo *DumpInfo,
	routeVolumeDumpData map[string]interface{}, lastDumpTime int64, needDumpMaskProductList []int,
) {
	condition := map[string]interface{}{
		"rule_volume_id = ?":     volumeRule.ID,
		"mask_product_id IN (?)": needDumpMaskProductList,
	}
	var routeVolumeCount int64
	if err := dbutil.Count(ctx, rulevolume.MaskRouteVolumeTabHook, condition, &routeVolumeCount); err != nil {
		return
	}
	// 2.1.1 full update is required when the count changes
	if routeVolumeCount != globalDumpInfo.RouteVolumeCount { // total refresh
		condition = map[string]interface{}{
			"rule_volume_id = ?":     volumeRule.ID,
			"mask_product_id IN (?)": needDumpMaskProductList,
		}
	} else { // local refresh
		for cacheKey, routeVolumeData := range globalDumpInfo.RouteVolumeData {
			routeVolumeDumpData[cacheKey] = routeVolumeData
		}
		condition = map[string]interface{}{
			"rule_volume_id = ?":     volumeRule.ID,
			"mask_product_id IN (?)": needDumpMaskProductList,
			"mtime >= ? ":            lastDumpTime,
		}
	}
	var routeVolumes []*rulevolume.MaskRouteVolumeTab
	if err := dbutil.Select(ctx, rulevolume.MaskRouteVolumeTabHook, condition, &routeVolumes); err != nil {
		return
	}

	for _, routeVolume := range routeVolumes {
		cacheKey := formatRouteVolumeCacheKey(
			int64(routeVolume.MaskProductID),
			int64(routeVolume.ComponentProductID),
			int64(routeVolume.OriginDistrictID),
			int64(routeVolume.DestinationDistrictID),
			routeVolume.OriginPostcode,
			routeVolume.DestinationPostcode,
			rule_mode.RuleMode(volumeRule.RuleMode),
			volumeRule.AllocationMethod,
		)

		routeVolumeDumpData[cacheKey] = &MaskRouteVolume{
			RuleVolumeId:         routeVolume.RuleVolumeID,
			RouteCode:            routeVolume.RouteCode,
			MinVolume:            routeVolume.MinVolume,
			MaxCapacity:          routeVolume.MaxCapacity,
			MaxCodCapacity:       routeVolume.MaxCodCapacity,
			MaxBulkyCapacity:     routeVolume.MaxBulkyCapacity,
			MaxHighValueCapacity: routeVolume.MaxHighValueCapacity,
			MaxDgCapacity:        routeVolume.MaxDgCapacity,
			IsHardCap:            routeVolume.IsHardCap,
			IsCodHardCap:         routeVolume.IsCodHardCap,
			IsBulkyHardCap:       routeVolume.IsBulkyHardCap,
			IsHighValueHardCap:   routeVolume.IsHighValueHardCap,
			IsDgHardCap:          routeVolume.IsDgHardCap,
			GroupCode:            routeVolume.GroupCode,
		}
	}
	if len(routeVolumeDumpData) > 0 {
		globalDumpInfo.RouteVolumeData = routeVolumeDumpData
		globalDumpInfo.RouteVolumeCount = int64(len(routeVolumeDumpData))
	}
}

func getZoneVolume(
	ctx context.Context, volumeRule *rulevolume.MaskRuleVolumeTab, globalDumpInfo *DumpInfo,
	zoneVolumeDumpData map[string]interface{}, lastDumpTime int64, needDumpMaskProductList []int,
) {
	// 2.2 load zone volumes
	condition := map[string]interface{}{
		"rule_volume_id = ?":     volumeRule.ID,
		"mask_product_id IN (?)": needDumpMaskProductList,
	}
	var zoneVolumeCount int64
	if err := dbutil.Count(ctx, rulevolume.MaskZoneVolumeTabHook, condition, &zoneVolumeCount); err != nil {
		return
	}
	// 2.2.1 full update is required when the count changes
	if zoneVolumeCount != globalDumpInfo.ZoneVolumeCount { // total refresh
		condition = map[string]interface{}{
			"rule_volume_id = ?":     volumeRule.ID,
			"mask_product_id IN (?)": needDumpMaskProductList,
		}
	} else { // local refresh
		for cacheKey, zoneVolumeData := range globalDumpInfo.ZoneVolumeData {
			zoneVolumeDumpData[cacheKey] = zoneVolumeData
		}
		condition = map[string]interface{}{
			"rule_volume_id = ?":     volumeRule.ID,
			"mask_product_id IN (?)": needDumpMaskProductList,
			"mtime >= ? ":            lastDumpTime,
		}
	}
	var zoneVolumes []*rulevolume.MaskZoneVolumeTab
	if err := dbutil.Select(ctx, rulevolume.MaskZoneVolumeTabHook, condition, &zoneVolumes); err != nil {
		return
	}

	for _, zoneVolume := range zoneVolumes {
		cacheKey := formatZoneVolumeCacheKey(
			int64(zoneVolume.MaskProductID),
			int64(zoneVolume.ComponentProductID),
			int64(zoneVolume.DistrictID),
			zoneVolume.Postcode,
			rule_mode.RuleMode(volumeRule.RuleMode),
			volumeRule.AllocationMethod,
		)
		zoneVolumeDumpData[cacheKey] = &MaskZoneVolume{
			RuleVolumeId:             zoneVolume.RuleVolumeID,
			ZoneCode:                 zoneVolume.ZoneCode,
			OriginMinVolume:          zoneVolume.OriginMinVolume,
			DestMinVolume:            zoneVolume.DestinationMinVolume,
			OriginMaxCapacity:        zoneVolume.OriginMaxCapacity,
			DestMaxCapacity:          zoneVolume.DestinationMaxCapacity,
			DestMaxCodCapacity:       zoneVolume.DestinationMaxCodCapacity,
			DestMaxBulkyCapacity:     zoneVolume.DestinationMaxBulkyCapacity,
			DestMaxHighValueCapacity: zoneVolume.DestinationMaxHighValueCapacity,
			DestMaxDgCapacity:        zoneVolume.DestinationMaxDgCapacity,
			IsHardCap:                zoneVolume.IsHardCap,
			IsCodHardCap:             zoneVolume.IsCodHardCap,
			IsBulkyHardCap:           zoneVolume.IsBulkyHardCap,
			IsHighValueHardCap:       zoneVolume.IsHighValueHardCap,
			IsDgHardCap:              zoneVolume.IsDgHardCap,
			GroupCode:                zoneVolume.GroupCode,
		}
	}
	if len(zoneVolumeDumpData) > 0 {
		globalDumpInfo.ZoneVolumeData = zoneVolumeDumpData
		globalDumpInfo.ZoneVolumeCount = int64(len(zoneVolumeDumpData))
	}
}

func DumpBatchAllocateRuleVolume() (map[string]interface{}, error) {
	ctx := context.TODO()
	dumpData := make(map[string]interface{})

	// 1. get active rule volumes
	var ruleVolumes []*rulevolume.MaskRuleVolumeTab
	condition := map[string]interface{}{
		"rule_status = ?":           rulevolume.MaskRuleVolumeStatusActive,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx),
		"allocation_method = ?":     allocation.BatchAllocate,
	}
	if err := dbutil.Select(ctx, rulevolume.MaskRuleVolumeTabHook, condition, &ruleVolumes); err != nil {
		return nil, err
	}

	// 2. load related location volumes
	for _, ruleVolume := range ruleVolumes {
		if err := ruleVolume.Unmarshal(); err != nil {
			return nil, err
		}
		condition = map[string]interface{}{
			"rule_volume_id = ?": ruleVolume.ID,
		}
		if err := dbutil.Select(ctx, rulevolume.MaskZoneVolumeTabHook, condition, &ruleVolume.ZoneVolumes); err != nil {
			return nil, err
		}
		if err := dbutil.Select(ctx, rulevolume.MaskRouteVolumeTabHook, condition, &ruleVolume.RouteVolumes); err != nil {
			return nil, err
		}
		if ruleVolume.MaskCombinationMode {
			for _, maskProductInfo := range ruleVolume.GroupInfo.MaskProductInfos {
				dumpData[strconv.Itoa(maskProductInfo.MaskProductID)] = ruleVolume
			}
		} else {
			dumpData[strconv.Itoa(ruleVolume.MaskProductID)] = ruleVolume
		}
	}

	return dumpData, nil
}
