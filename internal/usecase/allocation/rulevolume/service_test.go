package rulevolume

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestMaskRuleVolumeServiceImpl_GetActiveRuleVolumeByMaskProductIDWithCache(t *testing.T) {
	ctx := context.Background()
	var levelCache *layercache.LevelCache
	var patch *gomonkey.Patches
	type args struct {
		maskProductID    int64
		rm               rule_mode.RuleMode
		allocationMethod int64
	}
	tests := []struct {
		name    string
		m       *MaskRuleVolumeServiceImpl
		args    args
		want    *rulevolume.MaskRuleVolumeTab
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: level cache get failed",
			m:       &MaskRuleVolumeServiceImpl{LevelCache: levelCache},
			want:    nil,
			wantErr: srerr.With(srerr.LevelCacheErr, "key", errors.New("mock levelCache get error")),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(levelCache), "Get", func(p *layercache.LevelCache, ctx context.Context, namespace, id string, obj interface{}, opts ...layercache.LevelOption) error {
					return srerr.With(srerr.LevelCacheErr, "key", errors.New("mock levelCache get error"))
				})
			},
		},
		{
			name:    "case 2: level cache get success",
			m:       &MaskRuleVolumeServiceImpl{LevelCache: levelCache},
			want:    &rulevolume.MaskRuleVolumeTab{},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(levelCache), "Get", func(p *layercache.LevelCache, ctx context.Context, namespace, id string, obj interface{}, opts ...layercache.LevelOption) error {
					obj = &rulevolume.MaskRuleVolumeTab{}
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := tt.m.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, tt.args.maskProductID, tt.args.rm, tt.args.allocationMethod)
			common.AssertResult(t, tt.want, got, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
