package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"math"
	"strconv"
)

const (
	Y           = "Y"
	N           = "N"
	FullyChange = 100.00
)

//定义compare方法策略接口
type Comparator interface {
	Compare(ctx context.Context, existedVolumeMap, reqVolumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) (*CheckRuleVolumeResp, *srerr.Error)
}

type CountryComparator struct {
	LpsApi lpsclient.LpsApi
}

// Compare
// 只比较country volume的变化
// 入参1：existedMaskVolumeMap：按mask product聚合的active volume信息，key: 非0 mask product；value：对应volume info
// 入参2：reqTab
func (c *CountryComparator) Compare(ctx context.Context, existedVolumeMap, reqVolumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) (*CheckRuleVolumeResp, *srerr.Error) {
	resp := &CheckRuleVolumeResp{}
	//1. 将existedTab映射成3pl-country volume info的map
	existedMap, existedMaskProductGroupMap, _ := getCountryVolumeInfoMap(existedVolumeMap)
	if len(existedMap) == 0 {
		logger.CtxLogErrorf(ctx, "CountryComparator|get empty existed country volume")
		return nil, srerr.New(srerr.DataErr, nil, "get empty existed country volume")
	}
	reqMap, reqMaskProductGroupMap, reqGroupInfos := getCountryVolumeInfoMap(reqVolumeMap)
	if len(reqMap) == 0 {
		logger.CtxLogErrorf(ctx, "CountryComparator|get empty req country volume")
		return nil, srerr.New(srerr.DataErr, nil, "get empty req country volume")
	}
	nameMap, gErr := c.LpsApi.GetAllProductIdNameList(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get product id name dict err:%v", gErr)
		nameMap = make(map[int64]string, 0)
	}

	//2. 比较channel维度双方差别
	for maskProduct, reqGroupVolumeMap := range reqMap {
		existGroupVolumeMap := existedMap[maskProduct]
		existedProductGroupMap := existedMaskProductGroupMap[maskProduct]

		for productID, reqVolumeInfo := range reqGroupVolumeMap {
			var (
				totalMinReqVolume, totalMaxReqVolume     = reqVolumeInfo.MinVolume, reqVolumeInfo.MaxVolume
				totalMinExistVolume, totalMaxExistVolume int64
				reqHardCap                               = convertHardCap(reqVolumeInfo.IsHardCap)
			)
			productIDInt, err := strconv.ParseInt(productID, 10, 64)

			// 先跳过group code相关的，后续单独处理group code，避免重复记录（因为不同mask 指向同一个group code
			if err != nil || productIDInt == 0 {
				continue
			}

			productName := ""
			productName = nameMap[productIDInt]

			// 根据product id直接在exist map中匹配成功，直接记录exist volume信息
			if existVolume, ok := existGroupVolumeMap[productID]; ok {
				totalMinExistVolume = existVolume.MinVolume
				totalMaxExistVolume = existVolume.MaxVolume
				existHardCap := convertHardCap(existVolume.IsHardCap)

				countryHardCap := CountryHardCap{
					ProductID:   productID,
					ProductName: productName,
					OldHardCap:  existHardCap,
					NewHardCap:  reqHardCap,
				}
				resp.CountryHardCapList = append(resp.CountryHardCapList, countryHardCap)

			} else { // product id匹配失败，根据exist product -- group的绑定关系，去group volume中检索
				if existedGroupCode, ok := existedProductGroupMap[productID]; ok {
					existedVolumeInfo := existGroupVolumeMap[existedGroupCode]
					totalMinExistVolume += existedVolumeInfo.MinVolume
					totalMaxExistVolume += existedVolumeInfo.MaxVolume
					existHardCap := convertHardCap(existedVolumeInfo.IsHardCap)

					countryHardCap := CountryHardCap{
						ProductID:   productID,
						ProductName: productName,
						OldHardCap:  existHardCap,
						NewHardCap:  reqHardCap,
					}
					resp.CountryHardCapList = append(resp.CountryHardCapList, countryHardCap)
				}
			}

			change := CountryVolumeChange{
				ProductID:                 productID,
				ProductName:               productName,
				OldMinVolume:              totalMinExistVolume,
				OldMaxVolume:              totalMaxExistVolume,
				NewMinVolume:              totalMinReqVolume,
				NewMaxVolume:              totalMaxReqVolume,
				AbsoluteChangeOfMinVolume: absDiffInt(totalMinExistVolume, totalMinReqVolume),
				AbsoluteChangeOfMaxVolume: absDiffInt(totalMaxExistVolume, totalMaxReqVolume),
				//百分比控制在小数点后2位
				PercentChangeOfMinVolume: strconv.FormatFloat(getPercentage(totalMinExistVolume, totalMinReqVolume)*100, 'f', 0, 64),
				PercentChangeOfMaxVolume: strconv.FormatFloat(getPercentage(totalMaxExistVolume, totalMaxReqVolume)*100, 'f', 0, 64),
			}
			resp.CountryVolumeChangeList = append(resp.CountryVolumeChangeList, change)
		}
	}
	// 3. 比较group code双方的差别
	for _, groupInfo := range reqGroupInfos {
		var (
			groupCode                  = groupInfo.GroupCode
			newIsHardCap               bool
			newMinVolume, newMaxVolume int64
			oldMinVolume, oldMaxVolume int64
		)
		// 因为不同mask tab对应的group volume都是一样的，所以随机match一个即可
		for _, reqGroupVolumeMap := range reqMap {
			if volume, ok := reqGroupVolumeMap[groupCode]; ok {
				newMinVolume = volume.MinVolume
				newMaxVolume = volume.MaxVolume
				newIsHardCap = volume.IsHardCap
				break
			}
		}
		// 匹配exist volume
		notFound := true
		// 先看看不同mask rule对应的运力中，是否有相同的group code，若有，则直接获取运力
		for _, existGroupVolumeMap := range existedMap {
			// 能直接根据req group code匹配上
			if volume, ok := existGroupVolumeMap[groupCode]; ok {
				oldMinVolume = volume.MinVolume
				oldMaxVolume = volume.MaxVolume
				notFound = false

				countryHardCap := CountryHardCap{
					ProductID:   groupCode,
					ProductName: "",
					OldHardCap:  convertHardCap(volume.IsHardCap),
					NewHardCap:  convertHardCap(newIsHardCap),
				}
				resp.CountryHardCapList = append(resp.CountryHardCapList, countryHardCap)
				break
			}
		}
		if notFound { // 没有相同的group code，需要用group code - (product1 + product2 + ...) 去获取运力数据
			for existMaskProductID, existGroupVolumeMap := range existedMap {
				// req group code匹配失败，根据channel去匹配
				for maskProductID, reqProductGroupMap := range reqMaskProductGroupMap {
					if maskProductID != existMaskProductID {
						continue
					}
					// 遍历mask下的channel-group绑定关系, 得到需要获取运力的product
					productIDList := make([]string, 0)
					for productID, tempGroupCode := range reqProductGroupMap {
						if groupCode == tempGroupCode {
							productIDList = append(productIDList, productID)
						}
					}
					// 先按product获取，获取不到说明该product被绑到了group code中，此时再去检索group code volume
					for _, productID := range productIDList {
						// product维度匹配成功，直接获取运力信息
						if existedVolumeInfo, ok := existGroupVolumeMap[productID]; ok {
							oldMinVolume += existedVolumeInfo.MinVolume
							oldMaxVolume += existedVolumeInfo.MaxVolume

							productIDInt, _ := strconv.ParseInt(groupCode, 10, 64)
							countryHardCap := CountryHardCap{
								ProductID:   groupCode,
								ProductName: nameMap[productIDInt],
								OldHardCap:  convertHardCap(existedVolumeInfo.IsHardCap),
								NewHardCap:  convertHardCap(newIsHardCap),
							}
							resp.CountryHardCapList = append(resp.CountryHardCapList, countryHardCap)
						} else {
							existedProductGroupMap := existedMaskProductGroupMap[maskProductID]
							// product维度找不到volume，说明被绑到了group中，根据group映射关系找到对应的exist group
							if existedGroupCode, ok := existedProductGroupMap[productID]; ok {
								existedVolumeInfo := existGroupVolumeMap[existedGroupCode]
								oldMinVolume += existedVolumeInfo.MinVolume
								oldMaxVolume += existedVolumeInfo.MaxVolume

								countryHardCap := CountryHardCap{
									ProductID:   groupCode,
									ProductName: "",
									OldHardCap:  convertHardCap(existedVolumeInfo.IsHardCap),
									NewHardCap:  convertHardCap(newIsHardCap),
								}
								resp.CountryHardCapList = append(resp.CountryHardCapList, countryHardCap)
							}
						}
					}
				}

			}
		}

		change := CountryVolumeChange{
			ProductID:                 groupCode,
			ProductName:               "",
			OldMinVolume:              oldMinVolume,
			OldMaxVolume:              oldMaxVolume,
			NewMinVolume:              newMinVolume,
			NewMaxVolume:              newMaxVolume,
			AbsoluteChangeOfMinVolume: absDiffInt(oldMinVolume, newMinVolume),
			AbsoluteChangeOfMaxVolume: absDiffInt(oldMaxVolume, newMaxVolume),
			//百分比控制在小数点后2位
			PercentChangeOfMinVolume: strconv.FormatFloat(getPercentage(oldMinVolume, newMinVolume)*100, 'f', 0, 64),
			PercentChangeOfMaxVolume: strconv.FormatFloat(getPercentage(oldMaxVolume, newMaxVolume)*100, 'f', 0, 64),
		}
		resp.CountryVolumeChangeList = append(resp.CountryVolumeChangeList, change)
	}

	resp.ModeChangeList = getModeChangeList(existedVolumeMap, reqVolumeMap)

	resp.BasicInfo = getBasicInfo(existedVolumeMap, reqVolumeMap)

	return resp, nil
}

// getCountryVolumeInfoMap
// 入参：m_channel: volume_info 的map
// 出参：[mask - [group_code - volume_info]] 的map；不同的mask product指向不同的f product及其运力配置
func getCountryVolumeInfoMap(volumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) (map[int]map[string]volumeInfo, map[int]map[string]string, []rulevolume2.FulfillmentProductGroupInfo) {
	// 最终的结果 [mask - [group_code -  volume_info]]
	// 第一层key: mask
	// 第二层key：group code ( 或f channel本身，如果该Channel没有绑定group
	result := make(map[int]map[string]volumeInfo, 0)
	maskProductGroupMap := make(map[int]map[string]string, 0)
	groupInfos := make([]rulevolume2.FulfillmentProductGroupInfo, 0)
	maskVolumeMap := splitTabByMaskProduct(volumeMap)

	for maskProductID, tab := range maskVolumeMap {
		if result[maskProductID] == nil {
			result[maskProductID] = make(map[string]volumeInfo, 0)
			maskProductGroupMap[maskProductID] = make(map[string]string, 0)
		}

		// 获取group code及其绑定的product关系

		for _, groupInfo := range tab.GroupInfo.FulfillmentProductGroupInfos {
			for _, p := range groupInfo.FulfillmentProductInfos {
				if p.MaskProductID != maskProductID {
					continue
				}
				maskProductGroupMap[maskProductID][strconv.Itoa(p.FulfillmentProductID)] = groupInfo.GroupCode
			}
		}
		// 遍历country volume, 并初始化 map
		for _, countryVolume := range tab.DefaultVolumeLimit {
			// 需要兼容存量数据，存量数据的mask product id为0
			if countryVolume.MaskProductID != 0 && countryVolume.MaskProductID != int64(maskProductID) {
				continue
			}
			// 初始化group code层级map
			productID := ""
			// 未绑定到group中，令group_code = f_channel
			if countryVolume.LimitType == rulevolume2.MaskRuleLimitTypeFulfillmentProduct {
				productID = strconv.FormatInt(countryVolume.ID, 10)
			}
			if countryVolume.LimitType == rulevolume2.MaskRuleLimitTypeGroup {
				// 获取product id对应的group code
				productID = countryVolume.GroupCode
			}

			result[maskProductID][productID] = volumeInfo{
				MinVolume: int64(countryVolume.MinVolume),
				MaxVolume: int64(countryVolume.MaxCapacity),
				IsHardCap: countryVolume.IsHardCap,
			}
		}
		// 因为req中不同mask volume tab包含的group info是一样的，所以重复赋值没有问题；只给req group info用（因为exist group info按mask product区分tab
		groupInfos = tab.GroupInfo.FulfillmentProductGroupInfos
	}

	return result, maskProductGroupMap, groupInfos
}

func getPercentage(existedInfoVolume, reqInfoVolume int64) float64 {
	if existedInfoVolume == reqInfoVolume {
		return 0
	}
	// 纯新增，百分百改变
	if existedInfoVolume == 0 {
		return 1
	}
	return math.Abs(float64(reqInfoVolume)-float64(existedInfoVolume)) / float64(existedInfoVolume)
}

func convertHardCap(isHardCap bool) string {
	if isHardCap {
		return Y
	}

	return N
}

type ZoneRouteComparator struct {
	LpsApi lpsclient.LpsApi
}

func (c *ZoneRouteComparator) Compare(ctx context.Context, existedVolumeMap, reqVolumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) (*CheckRuleVolumeResp, *srerr.Error) {
	var (
		resp                                                                          = &CheckRuleVolumeResp{}
		listOfZoneRoute                                                               []ZoneRouteInfo
		zoneRouteHardCapList                                                          []ZoneRouteHardCap
		zoneRouteVolumeChangeList                                                     []ZoneRouteVolumeChange
		existedMaskVolumeMap, reqMaskVolumeMap                                        = splitTabByMaskProduct(existedVolumeMap), splitTabByMaskProduct(reqVolumeMap)
		existedMaskCodeProductVolumeMap, existedMaskProductGroupMap, existZRCodeGroup = getZoneRouteVolumeInfoMap(existedMaskVolumeMap)
		reqCodeMaskProductVolumeMap, reqMaskProductGroupMap, reqZRCodeGroup           = getZoneRouteVolumeInfoMap(reqMaskVolumeMap)
	)
	nameMap, gErr := c.LpsApi.GetAllProductIdNameList(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get product id name dict err:%v", gErr)
		nameMap = make(map[int64]string, 0)
	}
	for maskProduct, reqTab := range reqMaskVolumeMap {
		var (
			activeTab = existedMaskVolumeMap[maskProduct]
			// active 'zone/route' - '3pl/group' - 'volume' map
			existedCodeProductVolumeMap = existedMaskCodeProductVolumeMap[maskProduct]
			// request 'zone/route' - '3pl/group' - 'volume' map
			reqCodeProductVolumeMap = reqCodeMaskProductVolumeMap[maskProduct]

			tempListOfZoneRoute           []ZoneRouteInfo
			tempZoneRouteHardCapList      []ZoneRouteHardCap
			tempZoneRouteVolumeChangeList []ZoneRouteVolumeChange
		)

		if reqTab.RuleType == activeTab.RuleType { //相同类型，需要比较zone变化，zone对应的运力变化，及其对应的Hard cap数据
			tempListOfZoneRoute, _ = getListOfZoneRoute(existedCodeProductVolumeMap, reqCodeProductVolumeMap, nameMap, existedMaskProductGroupMap[maskProduct], reqMaskProductGroupMap[maskProduct])
			// 获取Zone Route下product层级的 volume改动, 及其Hard cap数据（这里的Hard cap数据更全面，所以以这里的为准
			tempZoneRouteVolumeChangeList, tempZoneRouteHardCapList = getZoneRouteVolumeChangeList(existedCodeProductVolumeMap, reqCodeProductVolumeMap, nameMap, existedMaskProductGroupMap[maskProduct])
		} else {
			// 不同类型，只需要获取Zone Route code改动，及其对应的Hard cap数据
			tempListOfZoneRoute, tempZoneRouteHardCapList = getListOfZoneRoute(nil, reqCodeProductVolumeMap, nameMap, existedMaskProductGroupMap[maskProduct], reqMaskProductGroupMap[maskProduct])
		}

		listOfZoneRoute = append(listOfZoneRoute, tempListOfZoneRoute...)
		zoneRouteHardCapList = append(zoneRouteHardCapList, tempZoneRouteHardCapList...)
		zoneRouteVolumeChangeList = append(zoneRouteVolumeChangeList, tempZoneRouteVolumeChangeList...)
	}

	// 获取zone route下group volume变动
	// 2. 比较zone/route下，group维度的volume
	tempZoneRouteVolumeChangeList, tempZoneRouteHardCapList := getGroupZoneRouteVolumeChangeList(existedMaskCodeProductVolumeMap, reqCodeMaskProductVolumeMap, existedMaskProductGroupMap, reqMaskProductGroupMap, existZRCodeGroup, reqZRCodeGroup)
	zoneRouteHardCapList = append(zoneRouteHardCapList, tempZoneRouteHardCapList...)
	zoneRouteVolumeChangeList = append(zoneRouteVolumeChangeList, tempZoneRouteVolumeChangeList...)

	//get country volume compare result
	countryComparator := &CountryComparator{LpsApi: c.LpsApi}
	countryResult, _ := countryComparator.Compare(ctx, existedVolumeMap, reqVolumeMap)

	// 装填resp
	resp.ZoneRouteHardCapList = zoneRouteHardCapList
	resp.ListOfZoneRoute = listOfZoneRoute
	resp.ZoneRouteVolumeChangeList = zoneRouteVolumeChangeList
	resp.CountryVolumeChangeList = countryResult.CountryVolumeChangeList
	resp.CountryHardCapList = countryResult.CountryHardCapList
	resp.BasicInfo = countryResult.BasicInfo
	resp.ModeChangeList = countryResult.ModeChangeList

	return resp, nil
}

type volumeInfo struct {
	MinVolume int64
	MaxVolume int64
	IsHardCap bool
}

// getZoneRouteVolumeInfoMap
// 入参：按mask聚合的volume tab
// 出参1：'mask' - 'zone/route' - '3pl/group_code' - 'volume' info的map
// 出参2：3pl-group 的绑定关系
func getZoneRouteVolumeInfoMap(maskVolumeMap map[int]*rulevolume2.MaskRuleVolumeTab) (map[int]map[string]map[string]volumeInfo, map[int]map[string]string, map[string]string) {
	result := make(map[int]map[string]map[string]volumeInfo, 0)
	maskProductGroupMap := make(map[int]map[string]string, 0)
	zrCodeGroup := make(map[string]string, 0)
	for maskProductID, tab := range maskVolumeMap {
		if result[maskProductID] == nil {
			result[maskProductID] = make(map[string]map[string]volumeInfo, 0)
			maskProductGroupMap[maskProductID] = make(map[string]string, 0)
		}
		if tab.RuleType.String() == rulevolume2.LocVolumeTypeZone.String() {
			for _, zone := range tab.ZoneVolumes {
				if zone.MaskProductID != 0 && zone.MaskProductID != maskProductID { // 判断非group volume
					continue
				}
				if result[maskProductID][zone.ZoneCode] == nil {
					result[maskProductID][zone.ZoneCode] = make(map[string]volumeInfo, 0)
				}
				productID := strconv.Itoa(zone.ComponentProductID)
				if zone.GroupCode != "" {
					// 额外装填product - group的绑定关系，方便后续对比
					maskProductGroupMap[maskProductID][productID] = zone.GroupCode
					productID = zone.GroupCode

					zrCodeGroup[zone.ZoneCode] = zone.GroupCode
				}
				result[maskProductID][zone.ZoneCode][productID] = volumeInfo{
					// 只拿dest volume
					MinVolume: int64(zone.DestinationMinVolume),
					MaxVolume: int64(zone.DestinationMaxCapacity),
					IsHardCap: zone.IsHardCap,
				}
			}
		}
		if tab.RuleType.String() == rulevolume2.LocVolumeTypeRoute.String() {
			for _, route := range tab.RouteVolumes {
				if route.MaskProductID != 0 && route.MaskProductID != maskProductID {
					continue
				}
				if result[maskProductID][route.RouteCode] == nil {
					result[maskProductID][route.RouteCode] = make(map[string]volumeInfo, 0)
				}
				productID := strconv.Itoa(route.ComponentProductID)
				if route.GroupCode != "" {
					// 额外装填product - group的绑定关系，方便后续对比
					maskProductGroupMap[maskProductID][productID] = route.GroupCode
					productID = route.GroupCode

					zrCodeGroup[route.RouteCode] = route.GroupCode
				}
				result[maskProductID][route.RouteCode][productID] = volumeInfo{
					MinVolume: int64(route.MinVolume),
					MaxVolume: int64(route.MaxCapacity),
					IsHardCap: route.IsHardCap,
				}
			}
		}
		if tab.RuleType.String() == rulevolume2.LocVolumeTypeRouteAndZone.String() {
			for _, zone := range tab.ZoneVolumes {
				if zone.MaskProductID != 0 && zone.MaskProductID != maskProductID {
					continue
				}
				if result[maskProductID][zone.ZoneCode] == nil {
					result[maskProductID][zone.ZoneCode] = make(map[string]volumeInfo, 0)
				}
				productID := strconv.Itoa(zone.ComponentProductID)
				if zone.GroupCode != "" {
					// 额外装填product - group的绑定关系，方便后续对比
					maskProductGroupMap[maskProductID][productID] = zone.GroupCode
					productID = zone.GroupCode

					zrCodeGroup[zone.ZoneCode] = zone.GroupCode
				}
				result[maskProductID][zone.ZoneCode][productID] = volumeInfo{
					// 只拿dest volume
					MinVolume: int64(zone.DestinationMinVolume),
					MaxVolume: int64(zone.DestinationMaxCapacity),
					IsHardCap: zone.IsHardCap,
				}
			}
			for _, route := range tab.RouteVolumes {
				if route.MaskProductID != 0 && route.MaskProductID != maskProductID {
					continue
				}
				if result[maskProductID][route.RouteCode] == nil {
					result[maskProductID][route.RouteCode] = make(map[string]volumeInfo, 0)
				}
				productID := strconv.Itoa(route.ComponentProductID)
				if route.GroupCode != "" {
					productID = route.GroupCode
					// 额外装填product - group的绑定关系，方便后续对比
					maskProductGroupMap[maskProductID][productID] = route.GroupCode

					zrCodeGroup[route.RouteCode] = route.GroupCode
				}
				result[maskProductID][route.RouteCode][productID] = volumeInfo{
					MinVolume: int64(route.MinVolume),
					MaxVolume: int64(route.MaxCapacity),
					IsHardCap: route.IsHardCap,
				}
			}
		}
	}

	return result, maskProductGroupMap, zrCodeGroup
}

// getListOfZoneRoute: 取新、旧volume的差集
// 先check existedMap,可以得到：1.只在old的数据 -> 得到remove的数据
// 再check reqMap，可以得到：1.只在new的数据 -> 得到add的数据
func getListOfZoneRoute(existedMap, reqMap map[string]map[string]volumeInfo, nameMap map[int64]string, existProductGroupMap, reqProductGroupMap map[string]string) ([]ZoneRouteInfo, []ZoneRouteHardCap) {
	var (
		result                []ZoneRouteInfo
		hardCapResult         []ZoneRouteHardCap
		checkedCodeProductMap = make(map[string]map[string]struct{}, 0)
	)
	// 1.获取zone route result, 包括：1.只在old（即delete模式）；2.只在new（即add模式）
	// check existed map
	for code, existedProductMap := range existedMap {
		// 【Zone Route code level】只存在old中 -- delete模式
		if _, ok := reqMap[code]; !ok {
			for productID, tempInfo := range existedProductMap {
				productName := ""
				productIDInt, err := strconv.Atoi(productID)
				if err == nil && productIDInt != 0 {
					productName = nameMap[int64(productIDInt)]
				} else {
					for product, tempGroupCOde := range existProductGroupMap {
						if tempGroupCOde == productID {
							productID = productID + " - " + product
						}
					}
				}
				result = append(result, ZoneRouteInfo{
					ZoneRouteCode: code,
					ProductID:     productID,
					ProductName:   productName,
					ExistInOld:    Y,
					ExistInNew:    N,
					MinVolume:     tempInfo.MinVolume,
					MaxVolume:     tempInfo.MaxVolume,
				})
				hardCapResult = append(hardCapResult, ZoneRouteHardCap{
					ZoneRouteCode: code,
					ProductID:     productID,
					ProductName:   productName,
					OldHardCap:    convertHardCap(tempInfo.IsHardCap),
				})
			}
			continue
		}
		// Zone Route code only in old
		for productID, existedInfo := range existedProductMap {
			// 【Zone Route - 3pl level】in old, not in new -- delete模式
			if _, ok := reqMap[code][productID]; !ok {
				productName := ""
				productIDInt, err := strconv.Atoi(productID)
				if err == nil && productIDInt != 0 {
					productName = nameMap[int64(productIDInt)]
				} else {
					for product, tempGroupCOde := range existProductGroupMap {
						if tempGroupCOde == productID {
							productID = productID + " - " + product
						}
					}
				}
				result = append(result, ZoneRouteInfo{
					ZoneRouteCode: code,
					ProductID:     productID,
					ProductName:   productName,
					ExistInOld:    Y,
					ExistInNew:    N,
					MinVolume:     existedInfo.MinVolume,
					MaxVolume:     existedInfo.MaxVolume,
				})
				hardCapResult = append(hardCapResult, ZoneRouteHardCap{
					ZoneRouteCode: code,
					ProductID:     productID,
					ProductName:   productName,
					OldHardCap:    convertHardCap(existedInfo.IsHardCap),
				})
			} else {
				// 记录both in new&old的数据，后续跳过这些数据，因为getListOfZoneRoute只比较新增或删除的数据
				if checkedCodeProductMap[code] == nil {
					checkedCodeProductMap[code] = make(map[string]struct{}, 0)
				}
				checkedCodeProductMap[code][productID] = struct{}{}
			}
		}
	}
	// check req map -- Add模式
	for code, productMap := range reqMap {
		for productID, reqInfo := range productMap {
			// skip not "only existed in new"
			if _, ok := checkedCodeProductMap[code][productID]; ok {
				continue
			}
			// get only existed in new
			productName := ""
			productIDInt, err := strconv.Atoi(productID)
			if err == nil && productIDInt != 0 {
				productName = nameMap[int64(productIDInt)]
			} else {
				for product, tempGroupCOde := range reqProductGroupMap {
					if tempGroupCOde == productID {
						productID = productID + " - " + product
					}
				}
			}
			result = append(result, ZoneRouteInfo{
				ZoneRouteCode: code,
				ProductID:     productID,
				ProductName:   productName,
				ExistInOld:    N,
				ExistInNew:    Y,
				MinVolume:     reqInfo.MinVolume,
				MaxVolume:     reqInfo.MaxVolume,
			})
			hardCapResult = append(hardCapResult, ZoneRouteHardCap{
				ZoneRouteCode: code,
				ProductID:     productID,
				ProductName:   productName,
				NewHardCap:    convertHardCap(reqInfo.IsHardCap),
			})
		}
	}
	return result, hardCapResult
}

// getZoneRouteVolumeChangeList: 以request中的数据为主，比较：
// 1.只存在于request中，也存在于exist中，展示新增的数据、exist的数据
/*
	入参1：existedMap：'zone/route' - '3pl/group' - 'volume' map，保存当前active volume数据
	入参2：reqMap：'zone/route' - '3pl/group' - 'volume' map，保存request volume数据
	入参3：nameMap：'product' - 'buyer name' map
	入参4：existedProductGroupMap: 'product' - 'group' map,保存active volume对应的product与group code的绑定关系，用来进行比较
	入参5：reqProductGroupMap: 'product' - 'group' map,保存req volume对应的product与group code的绑定关系，用来进行比较
*/
func getZoneRouteVolumeChangeList(existedMap, reqMap map[string]map[string]volumeInfo, nameMap map[int64]string, existedProductGroupMap map[string]string) ([]ZoneRouteVolumeChange, []ZoneRouteHardCap) {
	var (
		result        []ZoneRouteVolumeChange
		hardCapResult []ZoneRouteHardCap
	)

	for code, reqProductMap := range reqMap {
		// 必须要都存在，才进行比较
		if existedMap[code] == nil {
			continue
		}
		// 1. 比较zone/route下，product维度的volume
		for productID, reqInfo := range reqProductMap {
			// 定义基础信息
			var (
				oldMinVolume int64
				oldMaxVolume int64
				oldIsHardCap bool
			)
			productIDInt, err := strconv.Atoi(productID)
			if productIDInt == 0 || err != nil { // 跳过group 维度的
				continue
			}
			productName := ""
			productName = nameMap[int64(productIDInt)]

			// case 1: product in exist and req, 直接用新老volume相减得到比较结果
			if existedInfo, ok := existedMap[code][productID]; ok {
				oldMinVolume = existedInfo.MinVolume
				oldMaxVolume = existedInfo.MaxVolume
				oldIsHardCap = existedInfo.IsHardCap

				// 一对一，直接保存Hard cap
				hardCapResult = append(hardCapResult, ZoneRouteHardCap{
					ZoneRouteCode: code,
					ProductID:     productID,
					ProductName:   productName,
					OldHardCap:    convertHardCap(oldIsHardCap),
					NewHardCap:    convertHardCap(reqInfo.IsHardCap),
				})
			} else {
				// case 2: exist volume中product被绑定到了group中，先按product id获取exist group code
				if existedGroupCode, ok := existedProductGroupMap[productID]; ok {
					// 检索到product对应的group，获取该Group的volume，并求和
					oldMinVolume = existedMap[code][existedGroupCode].MinVolume
					oldMaxVolume = existedMap[code][existedGroupCode].MaxVolume
					oldIsHardCap = existedMap[code][existedGroupCode].IsHardCap

					// 多对一，保存所有的Hard cap
					hardCapResult = append(hardCapResult, ZoneRouteHardCap{
						ZoneRouteCode: code,
						ProductID:     productID,
						ProductName:   productName,
						OldHardCap:    convertHardCap(oldIsHardCap),
						NewHardCap:    convertHardCap(reqInfo.IsHardCap),
					})
				}
			}

			change := ZoneRouteVolumeChange{
				ZoneRouteCode:             code,
				ProductID:                 productID,
				ProductName:               productName,
				OldMinVolume:              oldMinVolume,
				NewMinVolume:              reqInfo.MinVolume,
				AbsoluteChangeOfMinVolume: absDiffInt(oldMinVolume, reqInfo.MinVolume),
				OldMaxVolume:              oldMaxVolume,
				NewMaxVolume:              reqInfo.MaxVolume,
				AbsoluteChangeOfMaxVolume: absDiffInt(oldMaxVolume, reqInfo.MaxVolume),
			}
			var (
				// 默认百分百，避免panic
				percentChangeOfMinVolume = FullyChange
				percentChangeOfMaxVolume = FullyChange
			)
			if oldMinVolume != 0 {
				minChangeStr := strconv.FormatFloat(float64(absDiffInt(oldMinVolume, reqInfo.MinVolume))/float64(oldMinVolume)*100, 'f', 0, 64)
				percentChangeOfMinVolume, _ = strconv.ParseFloat(minChangeStr, 64)
			}
			if oldMaxVolume != 0 {
				maxChangeStr := strconv.FormatFloat(float64(absDiffInt(oldMaxVolume, reqInfo.MaxVolume))/float64(oldMaxVolume)*100, 'f', 0, 64)
				percentChangeOfMaxVolume, _ = strconv.ParseFloat(maxChangeStr, 64)
			}

			change.PercentChangeOfMinVolume = percentChangeOfMinVolume
			change.PercentChangeOfMaxVolume = percentChangeOfMaxVolume
			result = append(result, change)
		}
	}
	return result, hardCapResult
}

func getGroupZoneRouteVolumeChangeList(maskExistedMap, maskReqMap map[int]map[string]map[string]volumeInfo, maskExistedProductGroupMap, maskReqProductGroupMap map[int]map[string]string, existZRCodeGroup, reqZRCodeGroup map[string]string) ([]ZoneRouteVolumeChange, []ZoneRouteHardCap) {
	var (
		result        []ZoneRouteVolumeChange
		hardCapResult []ZoneRouteHardCap
	)

	for zrCode, groupCode := range reqZRCodeGroup {
		var (
			newMinVolume, newMaxVolume int64
			oldMinVolume, oldMaxVolume int64
			//newIsHardCap, oldIsHardCap bool
		)
		// 1.先直接判断group code是否相同，即可以直接用group code进行比较
		if existZRCodeGroup[zrCode] == groupCode {

			for maskProductID, zrCodeGroupVolumeMap := range maskReqMap {
				if zrCodeGroupVolumeMap[zrCode] == nil {
					continue
				}
				if _, ok := zrCodeGroupVolumeMap[zrCode][groupCode]; !ok {
					continue
				}
				// 获取exist volume info
				existZRCodeGroupVolumeMap := maskExistedMap[maskProductID]
				if existZRCodeGroupVolumeMap[zrCode] == nil {
					continue
				}
				if _, ok := existZRCodeGroupVolumeMap[zrCode][groupCode]; !ok {
					continue
				}
				reqInfo := zrCodeGroupVolumeMap[zrCode][groupCode]
				existInfo := existZRCodeGroupVolumeMap[zrCode][groupCode]
				newMinVolume, newMaxVolume = reqInfo.MinVolume, reqInfo.MaxVolume
				oldMinVolume, oldMaxVolume = existInfo.MinVolume, existInfo.MaxVolume

				// 一对一，直接保存Hard cap
				hardCapResult = append(hardCapResult, ZoneRouteHardCap{
					ZoneRouteCode: zrCode,
					ProductID:     groupCode,
					ProductName:   "",
					OldHardCap:    convertHardCap(existInfo.IsHardCap),
					NewHardCap:    convertHardCap(reqInfo.IsHardCap),
				})
				// 因为group下运力配置都是一样的，所以只需要统计一次即可
				break
			}
		} else {
			// 2.group code不相同，需要用group volume - (product volume1 + ... + product volume n)； 这里product按mask product进行了聚合，不会出现重复
			for maskProductID, reqProductGroupMap := range maskReqProductGroupMap {
				// 先获取request中绑定了group code的product id
				productIDList := make([]string, 0)
				for product, tempGroupCode := range reqProductGroupMap {
					if groupCode == tempGroupCode {
						productIDList = append(productIDList, product)
					}
				}
				if maskReqMap[maskProductID][zrCode] == nil {
					continue
				}
				if _, ok := maskReqMap[maskProductID][zrCode][groupCode]; !ok {
					continue
				}
				// 获取req volume info
				reqInfo := maskReqMap[maskProductID][zrCode][groupCode]
				newMinVolume, newMaxVolume = reqInfo.MinVolume, reqInfo.MaxVolume
				// 获取exist volume info
				existZRCodeGroupVolumeMap := maskExistedMap[maskProductID]
				if existZRCodeGroupVolumeMap[zrCode] == nil {
					continue
				}
				for _, productID := range productIDList {
					// product id能直接找到值
					if volume, ok := existZRCodeGroupVolumeMap[zrCode][productID]; ok {
						oldMinVolume += volume.MinVolume
						oldMaxVolume += volume.MaxVolume

						// 多对一，直接保存Hard cap
						hardCapResult = append(hardCapResult, ZoneRouteHardCap{
							ZoneRouteCode: zrCode,
							ProductID:     groupCode,
							ProductName:   "",
							OldHardCap:    convertHardCap(volume.IsHardCap),
							NewHardCap:    convertHardCap(reqInfo.IsHardCap),
						})
					} else {
						// 根据product id去获取绑定的group code
						existedProductGroupMap := maskExistedProductGroupMap[maskProductID]
						if tempGroupCode, ok := existedProductGroupMap[productID]; ok {
							volume := maskExistedMap[maskProductID][zrCode][tempGroupCode]
							oldMinVolume += volume.MinVolume
							oldMaxVolume += volume.MaxVolume

							// 多对一，直接保存Hard cap
							hardCapResult = append(hardCapResult, ZoneRouteHardCap{
								ZoneRouteCode: zrCode,
								ProductID:     groupCode,
								ProductName:   "",
								OldHardCap:    convertHardCap(volume.IsHardCap),
								NewHardCap:    convertHardCap(reqInfo.IsHardCap),
							})
						}
					}
				}
			}
		}

		change := ZoneRouteVolumeChange{
			ZoneRouteCode:             zrCode,
			ProductID:                 groupCode,
			ProductName:               "",
			OldMinVolume:              oldMinVolume,
			NewMinVolume:              newMinVolume,
			AbsoluteChangeOfMinVolume: absDiffInt(oldMinVolume, newMinVolume),
			OldMaxVolume:              oldMaxVolume,
			NewMaxVolume:              newMaxVolume,
			AbsoluteChangeOfMaxVolume: absDiffInt(oldMaxVolume, newMaxVolume),
		}
		var (
			// 默认百分百，避免panic
			percentChangeOfMinVolume = FullyChange
			percentChangeOfMaxVolume = FullyChange
		)
		if oldMinVolume != 0 {
			minChangeStr := strconv.FormatFloat(float64(absDiffInt(oldMinVolume, newMinVolume))/float64(oldMinVolume)*100, 'f', 0, 64)
			percentChangeOfMinVolume, _ = strconv.ParseFloat(minChangeStr, 64)
		}
		if oldMaxVolume != 0 {
			maxChangeStr := strconv.FormatFloat(float64(absDiffInt(oldMaxVolume, newMaxVolume))/float64(oldMaxVolume)*100, 'f', 0, 64)
			percentChangeOfMaxVolume, _ = strconv.ParseFloat(maxChangeStr, 64)
		}

		change.PercentChangeOfMinVolume = percentChangeOfMinVolume
		change.PercentChangeOfMaxVolume = percentChangeOfMaxVolume
		result = append(result, change)
	}

	return result, hardCapResult
}

func absDiffInt(a, b int64) int64 {
	if a <= b {
		return b - a
	}
	return a - b
}

func bool2String(flag bool) string {
	if flag {
		return "Y"
	}

	return "N"
}

func getModeChangeList(existedVolumeMap, reqVolumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) []ModeChange {
	// 按mask product装填tab
	existedMaskVolumeMap := splitTabByMaskProduct(existedVolumeMap)
	reqMaskVolumeMap := splitTabByMaskProduct(reqVolumeMap)

	// 装填mode change
	oldType, newType := "", ""
	oldDuplicateMap, newDuplicateMap := make(map[string]struct{}, 0), make(map[string]struct{}, 0)
	for maskProduct, volumeTab := range reqMaskVolumeMap {
		if _, ok := newDuplicateMap[volumeTab.RuleType.String()]; !ok {
			newDuplicateMap[volumeTab.RuleType.String()] = struct{}{}
			if newType == "" {
				newType = newType + volumeTab.RuleType.String()
			} else {
				newType = newType + "\n" + volumeTab.RuleType.String()
			}
		}

		// type不相同的时候才添加（用空格键隔开
		if existedMaskVolumeMap[maskProduct] != nil {
			if _, ok := oldDuplicateMap[existedMaskVolumeMap[maskProduct].RuleType.String()]; !ok {
				oldDuplicateMap[existedMaskVolumeMap[maskProduct].RuleType.String()] = struct{}{}
				if oldType == "" {
					oldType = oldType + existedMaskVolumeMap[maskProduct].RuleType.String()
				} else {
					oldType = oldType + "\n" + existedMaskVolumeMap[maskProduct].RuleType.String()
				}
			}
		}
	}
	// 排序
	newType = objutil.SortString(newType, "\n")
	oldType = objutil.SortString(oldType, "\n")
	return []ModeChange{{
		VolumeRuleMode: newType,
		New:            newType,
		Old:            oldType,
	}}
}

func getBasicInfo(existedVolumeMap, reqVolumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) BasicInfo {
	// 按mask product装填tab
	reqMaskVolumeMap := splitTabByMaskProduct(reqVolumeMap)

	// 装填basic info
	basicInfo := BasicInfo{}
	for id := range reqVolumeMap {
		basicInfo.NewRuleID = strconv.FormatUint(id, 10)
	}
	for id := range existedVolumeMap {
		basicInfo.OldRuleIDList = append(basicInfo.OldRuleIDList, strconv.FormatUint(id, 10))
	}

	groupInfos := make([]rulevolume2.FulfillmentProductGroupInfo, 0)
	for maskProduct, tab := range reqMaskVolumeMap {
		if basicInfo.NewMaskingProductID == "" {
			basicInfo.NewMaskingProductID = strconv.Itoa(maskProduct)
		} else {
			basicInfo.NewMaskingProductID = basicInfo.NewMaskingProductID + "\n" + strconv.Itoa(maskProduct)
		}

		if basicInfo.NewWhetherVolumeShare == "" {
			basicInfo.NewWhetherVolumeShare = strconv.Itoa(maskProduct) + "-" + bool2String(tab.ShareVolume)
		} else {
			basicInfo.NewWhetherVolumeShare = basicInfo.NewWhetherVolumeShare + "\n" + strconv.Itoa(maskProduct) + "-" + bool2String(tab.ShareVolume)
		}

		// 不同mask下的group info一样，因此可以重复赋值
		groupInfos = tab.GroupInfo.FulfillmentProductGroupInfos
	}
	for _, groupInfo := range groupInfos { //
		basicInfo.NewGroupCode = groupInfo.GroupCode
		for _, productInfo := range groupInfo.FulfillmentProductInfos {
			if basicInfo.NewShareGroupChannel == "" {
				basicInfo.NewShareGroupChannel = fmt.Sprintf("%d-%s-%d", productInfo.MaskProductID, groupInfo.GroupCode, productInfo.FulfillmentProductID)
			} else {
				basicInfo.NewShareGroupChannel = basicInfo.NewShareGroupChannel + "\n" + fmt.Sprintf("%d-%s-%d", productInfo.MaskProductID, groupInfo.GroupCode, productInfo.FulfillmentProductID)
			}
		}
	}
	for _, tab := range existedVolumeMap {
		maskProductIDList := make([]int, 0)
		if tab.MaskCombinationMode {
			for _, m := range tab.GroupInfo.MaskProductInfos {
				maskProductIDList = append(maskProductIDList, m.MaskProductID)
			}
		} else {
			maskProductIDList = []int{tab.MaskProductID}
		}

		for _, m := range maskProductIDList {
			if basicInfo.OldMaskingProductID == "" {
				basicInfo.OldMaskingProductID = strconv.Itoa(m)
			} else {
				basicInfo.OldMaskingProductID = basicInfo.OldMaskingProductID + "\n" + strconv.Itoa(m)
			}

			if basicInfo.OldWhetherVolumeShare == "" {
				basicInfo.OldWhetherVolumeShare = strconv.Itoa(m) + "-" + bool2String(tab.ShareVolume)
			} else {
				basicInfo.OldWhetherVolumeShare = basicInfo.OldWhetherVolumeShare + "\n" + strconv.Itoa(m) + "-" + bool2String(tab.ShareVolume)
			}
		}

		for _, groupInfo := range tab.GroupInfo.FulfillmentProductGroupInfos {
			basicInfo.OldGroupCode = groupInfo.GroupCode
			for _, productInfo := range groupInfo.FulfillmentProductInfos {
				if basicInfo.OldShareGroupChannel == "" {
					basicInfo.OldShareGroupChannel = fmt.Sprintf("%d-%s-%d", productInfo.MaskProductID, groupInfo.GroupCode, productInfo.FulfillmentProductID)
				} else {
					basicInfo.OldShareGroupChannel = basicInfo.OldShareGroupChannel + "\n" + fmt.Sprintf("%d-%s-%d", productInfo.MaskProductID, groupInfo.GroupCode, productInfo.FulfillmentProductID)
				}
			}
		}
	}
	// 排序
	basicInfo.OldMaskingProductID = objutil.SortString(basicInfo.OldMaskingProductID, "\n")
	basicInfo.OldWhetherVolumeShare = objutil.SortString(basicInfo.OldWhetherVolumeShare, "\n")
	basicInfo.OldShareGroupChannel = objutil.SortString(basicInfo.OldShareGroupChannel, "\n")
	basicInfo.NewMaskingProductID = objutil.SortString(basicInfo.NewMaskingProductID, "\n")
	basicInfo.NewWhetherVolumeShare = objutil.SortString(basicInfo.NewWhetherVolumeShare, "\n")
	basicInfo.NewShareGroupChannel = objutil.SortString(basicInfo.NewShareGroupChannel, "\n")

	return basicInfo
}

func splitTabByMaskProduct(volumeMap map[uint64]*rulevolume2.MaskRuleVolumeTab) map[int]*rulevolume2.MaskRuleVolumeTab {
	maskVolumeMap := make(map[int]*rulevolume2.MaskRuleVolumeTab, 0)

	for _, tab := range volumeMap {
		// 按mask product装填tab
		maskProductIDList := make([]int, 0)
		if tab.MaskCombinationMode {
			for _, m := range tab.GroupInfo.MaskProductInfos {
				maskProductIDList = append(maskProductIDList, m.MaskProductID)
			}
		} else {
			maskProductIDList = []int{tab.MaskProductID}
		}

		for _, maskProductID := range maskProductIDList {
			maskVolumeMap[maskProductID] = tab
		}
	}

	return maskVolumeMap
}
