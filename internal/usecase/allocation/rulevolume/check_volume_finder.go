package rulevolume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type CheckVolumeFinder interface {
	GetCurrentVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error)
}

type CheckVolumeFinderImpl struct {
	IMaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
}

func NewCheckVolumeFinderImpl(IMaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo) *CheckVolumeFinderImpl {
	return &CheckVolumeFinderImpl{
		IMaskRuleVolumeRepo: IMaskRuleVolumeRepo,
	}
}

// GetCurrentVolume 根据request 设计的mask product，获取当前active的volume Rule
func (i *CheckVolumeFinderImpl) GetCurrentVolume(ctx context.Context, req *CheckRuleVolumeRequest) (map[uint64]*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	// 1.获取检索条件
	var (
		ruleMode         = req.RuleMode
		allocationMethod = req.AllocationMethod
		maskProductIDMap = make(map[int]struct{}, 0)
		volumeRuleMap    = make(map[uint64]*rulevolume.MaskRuleVolumeTab, 0)
	)
	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDMap[m.MaskProductID] = struct{}{}
		}
	} else {
		maskProductIDMap[req.MaskProductID] = struct{}{}
	}
	// 2.按mask, Rule mode， method获取当前所有active volume
	// 底层已按生效时间排序，只会拿到最近生效的那条
	for maskProductID := range maskProductIDMap {
		tab, gErr := i.IMaskRuleVolumeRepo.GetActiveRuleVolumeByMaskProductID(ctx, int64(maskProductID), rule_mode.RuleMode(ruleMode), allocationMethod)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "get rule volume by allocation mask product:%d, method:%d, err:%v", maskProductID, allocationMethod, gErr)
			return nil, gErr
		}
		//maskProductVolumeMap[maskProductID] = tab
		if _, ok := volumeRuleMap[tab.ID]; !ok {
			volumeRuleMap[tab.ID] = tab
		}
	}
	// 判断检索到的rule是否重复，即特殊处理group rule -> group rule的场景

	// 3.填充zone，route volume -> 如何减少sql调用？
	for id, volume := range volumeRuleMap {
		zoneVolumes, gErr := i.IMaskRuleVolumeRepo.ListZoneVolumesByRuleID(ctx, id)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "get zone volume list by rule volume id:%d, err:%v", id, gErr)
			return nil, gErr
		}
		volume.ZoneVolumes = zoneVolumes
		volumeRuleMap[id] = volume
	}

	for id, volume := range volumeRuleMap {
		routeVolumes, gErr := i.IMaskRuleVolumeRepo.ListRouteVolumesByRuleID(ctx, id)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "get route volume list by rule volume id:%d, err:%v", id, gErr)
			return nil, gErr
		}
		volume.RouteVolumes = routeVolumes
		volumeRuleMap[id] = volume
	}

	return volumeRuleMap, nil
}
