package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strings"
)

const (
	maskVolumeLinkFormat = "https://ops.ssc.%sshopeemobile.com/allocating/local-volume-routing/view/%d?_p_=sls&_c_=%s" // nolint
)

type MaskVolumeUnitImpl struct {
	BusinessAuditRepo  business_audit.BusinessAuditRepo
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
}

func NewMaskVolumeUnitImpl(
	BusinessAuditRepo business_audit.BusinessAuditRepo,
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
) *MaskVolumeUnitImpl {
	return &MaskVolumeUnitImpl{
		BusinessAuditRepo:  BusinessAuditRepo,
		MaskRuleVolumeRepo: MaskRuleVolumeRepo,
	}
}

// Listener 监听审批结果
func (u *MaskVolumeUnitImpl) Listener(ctx context.Context, ticketID string, approvalStatus string, tab *business_audit.BusinessAuditTab) *srerr.Error {
	//1. 更新business audit tab
	if uErr := u.BusinessAuditRepo.Update(ctx, map[string]interface{}{"ticket_id = ?": ticketID}, map[string]interface{}{"approval_status": approvalStatus}); uErr != nil {
		logger.CtxLogErrorf(ctx, "ticket id:%s, update approval ticket status err:%v", ticketID, uErr)
		return uErr
	}
	//2. 根据ticket id获取对应的soft rule
	maskVolumeRule, err := u.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, tab.ConfigDbID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "mask volume rule id:%d, get rule err:%v", tab.ConfigDbID, err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	//3. 判断approval status是否是approved(审批单状态（PENDING,APPROVED,CANCELED,REJECTED)
	if approvalStatus == string(business_audit.ApprovalStatusRejected) || approvalStatus == string(business_audit.ApprovalStatusCanceled) {
		//3.1 once rejected, rule back to draft
		maskVolumeRule.RuleStatus = rulevolume.MaskRuleVolumeStatusDraft
	}
	if approvalStatus == string(business_audit.ApprovalStatusApproved) {
		//3.2 比较effective start time和current time（approval time）
		if int64(maskVolumeRule.EffectiveStartTime) <= timeutil.GetCurrentTime(ctx).Unix() {
			// effective time <= approval time, effective immediately
			// 由schedule rule使用active + effective time来判断是否是upcoming
			maskVolumeRule.RuleStatus = rulevolume.MaskRuleVolumeStatusActive
		} else {
			// effective time > approval time, set status to upcoming
			// 由schedule rule使用active + effective time来判断是否是upcoming
			maskVolumeRule.RuleStatus = rulevolume.MaskRuleVolumeStatusActive
		}
		maskVolumeRule.ApprovalTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	if _, err := u.MaskRuleVolumeRepo.UpdateRuleVolume(ctx, maskVolumeRule); err != nil {
		logger.CtxLogErrorf(ctx, "update mask volume rule to status:%s err:%v", maskVolumeRule.RuleStatus.Type(), err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (u *MaskVolumeUnitImpl) BusinessType() string {
	return "mask_volume"
}

// 获取审批请求时的extra data
func (u *MaskVolumeUnitImpl) GetExtraData() string {
	return allocation.ExtraInfo
}

//	{"application_info": {"Application Details:": {"href": "链接","text": "文案"}}}
//
// 获取审批请求时的application info
func (u *MaskVolumeUnitImpl) GetApplicationInfo(configDbId int64) string {
	link := fmt.Sprintf(maskVolumeLinkFormat, strings.ToLower(envvar.GetEnv())+".", configDbId, envvar.GetCID())
	if envvar.GetEnv() == "LIVE" {
		link = fmt.Sprintf(maskVolumeLinkFormat, "", configDbId, envvar.GetCID())
	}
	info := fmt.Sprintf(allocation.ApplicationInfo, link, "update smartrouting allocation mask volume")
	return info
}

func (u *MaskVolumeUnitImpl) GenerateAuditTab(ctx context.Context, configDbId uint64) *business_audit.BusinessAuditTab {
	timeStamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	userEmail, _ := apiutil.GetUserInfo(ctx)
	tab := &business_audit.BusinessAuditTab{
		BusinessType:   u.BusinessType(),
		ApprovalStatus: string(business_audit.ApprovalStatusPending),
		Ctime:          timeStamp,
		Mtime:          timeStamp,
		Operator:       userEmail,
		ConfigDbID:     configDbId,
	}
	return tab
}
