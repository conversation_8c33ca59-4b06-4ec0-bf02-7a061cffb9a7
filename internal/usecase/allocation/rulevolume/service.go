package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	workerNum                  = 64
	updateVolumeDataExpireTime = 10 * time.Minute
	MaxRuleVolumeFileRows      = 500000
	LocationJoinCode           = "-"

	// 业务背景：业务实际基本不会对Origin Zone配置运力，只会配置Destination Zone运力，所以如果整体都置为极小值会影响实际调度
	// blank as minimum逻辑仅作用于Destination Zone
	originZoneSetBlankAsMini = false

	// Excel文件解析后数据切片的Index到Excel文件行号的offset（表头1行 + 切片）
	rowIndexOffset = 2
	// Route/Zone code的最大长度(DB字段的长度)
	maxRouteZoneCodeLen = 64
	maxPostCodeLen      = 32
	maxGroupCodeLen     = 64
	FillBlankMax        = 1
	FillBlankZero       = 2
)

// 校验报错文案
const (
	negativeErrMsg              = "Volume Limit cannot be configured as negative or decimal numbers at row %d. Please re-confirm"
	exceedLengthErrMsg          = "Name can't exceed 64 character for column 'zone code'/'route code' at row %d. Please re-confirm"
	productAlreadyInGroupErrMsg = "Product has been in volume share group at row %d, must configure in group level. Please re-confirm"
)

type MaskRuleVolumeService interface {
	MatchLocsToRoutes(ctx context.Context, maskProductID, componentProductID int64, originLocIDs, destLocIDs []int64, rm rule_mode.RuleMode, originPostcode, destPostcode string, allocationMethod int) ([]*MaskLocVolume, *srerr.Error)
	MatchLocsToZones(ctx context.Context, maskProductID, componentProductID int64, originLocIDs, destLocIDs []int64, rm rule_mode.RuleMode, originPostcode, destPostcode string, allocationMethod int) ([]*MaskLocVolume, *srerr.Error)
	ParseRouteExcel(ctx context.Context, maskProductIDList []int, groupToProductList map[string][]int, defFileUrl, volFileUrl string, setBlankAsMini bool, allocationMethod int64) ([]*rulevolume.MaskRouteVolumeTab, *srerr.Error)
	ParseZoneExcel(ctx context.Context, maskProductIDList []int, groupToProductList map[string][]int, defFileUrl, volFileUrl string, setBlankAsMini bool, allocationMethod int64) ([]*rulevolume.MaskZoneVolumeTab, *srerr.Error)
	ListRuleVolumes(ctx context.Context, req *GetRuleVolumeListRequest) ([]*rulevolume.MaskRuleVolumeTab, int, *srerr.Error)
	GetRuleVolumeByID(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	GetRuleVolumeByIDWithCache(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	GetActiveRuleVolumeByMaskProductIDWithCache(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	GetEffectiveBatchAllocateRuleVolume(ctx context.Context, maskProductID int) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	CreateRuleVolume(ctx context.Context, req *CreateRuleVolumeRequest) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	UpdateRuleVolume(ctx context.Context, req *UpdateRuleVolumeRequest) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	CopyRuleVolume(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error)
	DeleteRuleVolume(ctx context.Context, id uint64) *srerr.Error
	CheckRuleVolume(ctx context.Context, req *CheckRuleVolumeRequest, isExport bool) (*CheckRuleVolumeResp, *srerr.Error)
	ExportCheckRuleVolume(ctx context.Context, req *CheckRuleVolumeRequest, isExport bool) (*ExportCheckVolumeResp, *srerr.Error)
}

type MaskRuleVolumeServiceImpl struct {
	AddrRepo           address.AddrRepo
	LpsApi             lpsclient.LpsApi
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
	LevelCache         *layercache.LevelCache
	CheckVolumeFinder  CheckVolumeFinder
	ApprovalExecutor   approval_manager.ApprovalExecutor
}

func NewMaskRuleVolumeServiceImpl(AddrRepo address.AddrRepo,
	LpsApi lpsclient.LpsApi,
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
	levelCache *layercache.LevelCache,
	CheckVolumeFinder CheckVolumeFinder,
	ApprovalExecutor approval_manager.ApprovalExecutor,
	BusinessAuditRepo business_audit.BusinessAuditRepo) *MaskRuleVolumeServiceImpl {

	maskRuleVolumeServiceImpl := &MaskRuleVolumeServiceImpl{
		AddrRepo:           AddrRepo,
		LpsApi:             LpsApi,
		MaskRuleVolumeRepo: MaskRuleVolumeRepo,
		LevelCache:         levelCache,
		CheckVolumeFinder:  CheckVolumeFinder,
		ApprovalExecutor:   ApprovalExecutor,
	}

	once := sync.Once{}
	once.Do(func() {
		NewCheckVolumeParserMap(maskRuleVolumeServiceImpl)
		NewComparatorMap(LpsApi)
		unit := NewMaskVolumeUnitImpl(BusinessAuditRepo, MaskRuleVolumeRepo)
		approval_unit.AppendBusinessUnitMap(unit)
	})

	return maskRuleVolumeServiceImpl
}

// MatchLocsToRoutes 匹配订单信息到Route Volumes
// 区分wms || MPL get-limits
func (m *MaskRuleVolumeServiceImpl) MatchLocsToRoutes(
	ctx context.Context, maskProductID, componentProductID int64, originLocIDs, destLocIDs []int64,
	rm rule_mode.RuleMode, originPostcode, destPostcode string, allocationMethod int,
) ([]*MaskLocVolume, *srerr.Error) {
	var result []*MaskLocVolume
	// 获取地址+postcode的组合信息
	originVolumeLocInfoList, destVolumeLocInfoList := GetComposeAddress(originLocIDs, destLocIDs, originPostcode, destPostcode)
	// 匹配运力配置需要考虑postcode
	for _, originVolumeLocInfo := range originVolumeLocInfoList {
		for _, destVolumeLocInfo := range destVolumeLocInfoList {
			value, err := localcache.Get(ctx, constant.MaskLocationVolume,
				formatRouteVolumeCacheKey(maskProductID, componentProductID, originVolumeLocInfo.LocationId, destVolumeLocInfo.LocationId, originVolumeLocInfo.Postcode, destVolumeLocInfo.Postcode, rm, allocationMethod))
			if err != nil {
				continue
			}
			routeVolume := value.(*MaskRouteVolume)
			result = append(result, &MaskLocVolume{
				RuleVolumeId: routeVolume.RuleVolumeId,
				LocCode:      routeVolume.RouteCode,
				ProductId:    componentProductID,
				LocType:      MaskLocTypeRoute,
				RouteVolume:  routeVolume,
				GroupCode:    routeVolume.GroupCode,
			})
		}
	}

	return result, nil
}

// MatchLocsToZones 匹配订单信息到Zone Volumes
func (m *MaskRuleVolumeServiceImpl) MatchLocsToZones(
	ctx context.Context, maskProductID, componentProductID int64, originLocIDs, destLocIDs []int64,
	rm rule_mode.RuleMode, originPostcode, destPostcode string, allocationMethod int,
) ([]*MaskLocVolume, *srerr.Error) {
	var result []*MaskLocVolume
	//Zone因为区分Origin和Dest的计数器，在LocLimit中会通过ZoneDirection区分
	// 获取地址+postcode的组合信息
	originVolumeLocInfoList, destVolumeLocInfoList := GetComposeAddress(originLocIDs, destLocIDs, originPostcode, destPostcode)
	// 匹配运力配置需要考虑postcode
	// 匹配发货地址的运力配置
	// batch模式不需要origin zone code信息
	if allocationMethod != allocation.BatchAllocate {
		for _, originVolumeLocInfo := range originVolumeLocInfoList {
			value, err := localcache.Get(ctx, constant.MaskLocationVolume, formatZoneVolumeCacheKey(maskProductID, componentProductID, originVolumeLocInfo.LocationId, originVolumeLocInfo.Postcode, rm, allocationMethod))
			if err != nil {
				continue
			}
			zoneVolume := value.(*MaskZoneVolume)
			result = append(result, &MaskLocVolume{
				RuleVolumeId:  zoneVolume.RuleVolumeId,
				LocCode:       zoneVolume.ZoneCode,
				LocType:       MaskLocTypeZone,
				ZoneDirection: MaskZoneDirectionOrigin,
				ZoneVolume:    zoneVolume,
				ProductId:     componentProductID,
				GroupCode:     zoneVolume.GroupCode,
			})
		}
	}

	// 匹配收货地址的运力配置
	for _, destVolumeLocInfo := range destVolumeLocInfoList {
		value, err := localcache.Get(ctx, constant.MaskLocationVolume,
			formatZoneVolumeCacheKey(maskProductID, componentProductID, destVolumeLocInfo.LocationId, destVolumeLocInfo.Postcode, rm, allocationMethod))
		if err != nil {
			continue
		}
		zoneVolume := value.(*MaskZoneVolume)
		result = append(result, &MaskLocVolume{
			LocCode:       zoneVolume.ZoneCode,
			LocType:       MaskLocTypeZone,
			ZoneDirection: MaskZoneDirectionDest,
			ZoneVolume:    zoneVolume,
			ProductId:     componentProductID,
			GroupCode:     zoneVolume.GroupCode,
		})
	}

	return result, nil
}

func (m *MaskRuleVolumeServiceImpl) ParseRouteExcel(
	ctx context.Context, maskProductIDList []int, groupToProductList map[string][]int, defFileUrl, volFileUrl string,
	setBlankAsMini bool, allocationMethod int64,
) ([]*rulevolume.MaskRouteVolumeTab, *srerr.Error) {

	defFile, err := client.SendRequest(ctx, client.GetMethod, defFileUrl, nil, nil, nil, client.TextContent)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, "can not download route definition file", err)
	}
	defer defFile.Body.Close() // 确保在函数结束时关闭响应体

	volFile, err := client.SendRequest(ctx, client.GetMethod, volFileUrl, nil, nil, nil, client.TextContent)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, "can not download route volume file", err)
	}
	defer volFile.Body.Close()

	// 读取上传的volFile文件 (route场景)
	volData, rErr := getImportRouteVolData(allocationMethod, volFile)
	if rErr != nil {
		return nil, rErr
	}

	defData, rErr := getImportRouteData(ctx, defFile)
	if rErr != nil {
		return nil, rErr
	}
	// 文件条数不能超过500000
	if len(defData) > MaxRuleVolumeFileRows {
		monitoring.ReportError(ctx, monitoring.CatModuleAdminMonitor, monitoring.RouteVolumeFileExceedLimit, fmt.Sprintf("route volume data exceed limit %v", MaxRuleVolumeFileRows))
		return nil, srerr.With(srerr.ParamErr, fmt.Sprintf("route data|The number of rows for the uploading file cannot exceed %v", MaxRuleVolumeFileRows), nil)
	}

	routeCodes := make(map[string][]*route)
	checkDulMap := make(map[string]struct{})
	checkCoverMap := make(map[route]*ImportRouteCode)
	for index, data := range defData {
		rowIndex := index + rowIndexOffset

		// 检查Route Code
		routeCode := data.RouteCode
		if routeCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Route Code can not be empty: %v", data)
		}
		if len(routeCode) > maxRouteZoneCodeLen || len(data.OriginPostcode) > maxPostCodeLen || len(data.DestinationPostcode) > maxPostCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}

		// 检查上级地址是否为空
		if !checkLocValid(data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v ", routeCode, data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet)
		}
		if !checkLocValid(data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v ", routeCode, data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet)
		}
		// 检查是否仅配置了postcode
		if !checkLocPostcode(data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet, data.OriginPostcode) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v, Postcode %v ", routeCode, data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet, data.OriginPostcode)
		}
		if !checkLocPostcode(data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet, data.DestinationPostcode) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v, Postcode %v ", routeCode, data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet, data.DestinationPostcode)
		}

		// 检查地址是否存在
		originLocInfo, err := m.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v ", routeCode, data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet)
		}
		destLocInfo, err := m.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Route %v is invalid; State %v, City %v, District %v, Street %v ", routeCode, data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet)
		}

		// 检查地址唯一性（完全相同的地址唯一）
		checkDulKey := str.Join(LocationJoinCode, originLocInfo.GetLocationID(), destLocInfo.GetLocationID(), data.OriginPostcode, data.DestinationPostcode)
		if _, exist := checkDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil,
				"Duplicate Data : Origin: State %v, City %v, District %v, Street %v;Destination: State %v, City %v, District %v, Street %v;Route Code: %v",
				data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet, data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet, data.RouteCode)
		}
		checkDulMap[checkDulKey] = struct{}{}

		r := route{routeCode, int(originLocInfo.GetLocationID()), int(destLocInfo.GetLocationID()), data.OriginPostcode, data.DestinationPostcode}
		checkCoverMap[r] = data
		routeCodes[routeCode] = append(routeCodes[routeCode], &r)
	}
	// 检查同一Route Code下地址是否覆盖
	if err := m.checkRouteAndPostcodeLocCover(ctx, checkCoverMap); err != nil {
		return nil, err
	}

	componentToMaskMap, pErr := m.getComponentProductMap(ctx, maskProductIDList)
	if pErr != nil {
		return nil, pErr
	}

	if allocationMethod == allocation.BatchAllocate {
		return m.getBatchRouteVolume(volData, routeCodes, componentToMaskMap, groupToProductList, setBlankAsMini, maskProductIDList)
	} else {
		return m.getSingleRouteVolume(volData, routeCodes, componentToMaskMap, groupToProductList, setBlankAsMini, maskProductIDList)
	}
}

func getImportRouteVolData(allocationMethod int64, volFile *http.Response) ([][]string, *srerr.Error) {
	var (
		volData    [][]string
		volHeaders = []string{
			"Route Code", "Component Product ID", "Group Code", "Min Vol", "Max Vol", "Hard-Cap (Max)", "Max COD Vol",
			"COD Hard-Cap (Max)", "Max Bulky Vol", "Bulky Hard-Cap (Max)", "Max High-Value Vol", "High-Value Hard-Cap (Max)",
			"Max DG vol", "DG Hard-cap(Max)",
		}
		targetVolHeaders = []string{
			"Route Code", "Component Product ID", "Group Code", "Min Vol", "Max Vol", "Max COD Vol", "Max Bulky Vol", "Max High-Value Vol", "Max DG vol",
		}
		rErr *srerr.Error
	)

	if allocationMethod == allocation.BatchAllocate {
		volData, rErr = fileutil.ReadData(volFile.Body, fileutil.DefaultSheet, targetVolHeaders)
	} else {
		volData, rErr = fileutil.ReadData(volFile.Body, fileutil.DefaultSheet, volHeaders)
	}
	if rErr != nil {
		return nil, rErr
	}

	if len(volData) > MaxRuleVolumeFileRows {
		return nil, srerr.With(srerr.ParamErr, fmt.Sprintf("route limit data|The number of rows for the uploading file cannot exceed %v", MaxRuleVolumeFileRows), nil)
	}

	return volData, nil
}

// single&batch definition一样，limit value不一样
func (m *MaskRuleVolumeServiceImpl) ParseZoneExcel(
	ctx context.Context, maskProductIDList []int, groupToProductList map[string][]int, defFileUrl, volFileUrl string, setBlankAsMini bool, allocationMethod int64,
) ([]*rulevolume.MaskZoneVolumeTab, *srerr.Error) {
	defFile, err := client.SendRequest(ctx, client.GetMethod, defFileUrl, nil, nil, nil, client.TextContent)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, "can not download route definition file", err)
	}
	defer defFile.Body.Close()

	volFile, err := client.SendRequest(ctx, client.GetMethod, volFileUrl, nil, nil, nil, client.TextContent)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, "can not download route volume file", err)
	}
	defer volFile.Body.Close()

	volData, rErr := getImportZoneVolData(allocationMethod, volFile)
	if rErr != nil {
		return nil, rErr
	}

	defData, rErr := getImportZoneData(ctx, defFile)
	if rErr != nil {
		return nil, rErr
	}
	if len(defData) > MaxRuleVolumeFileRows {
		monitoring.ReportError(ctx, monitoring.CatModuleAdminMonitor, monitoring.ZoneVolumeFileExceedLimit, fmt.Sprintf("route volume data exceed limit %v", MaxRuleVolumeFileRows))
		return nil, srerr.With(srerr.ParamErr, fmt.Sprintf("zone data|The number of rows for the uploading file cannot exceed %v", MaxRuleVolumeFileRows), nil)
	}
	zoneCodes := make(map[string][]*zone)
	// 用于检查全部数据的地址唯一性
	checkDulMap := make(map[string]struct{})
	//用于检查同一Zone Code下的地址不能含有覆盖关系
	checkCoverMap := make(map[zone]*ImportZoneCode)
	for index, data := range defData {
		rowIndex := index + rowIndexOffset

		// 检查Zone Code
		zoneCode := data.ZoneCode
		if zoneCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone Code can not be empty: %v", data)
		}
		if len(zoneCode) > maxRouteZoneCodeLen || len(data.Postcode) > maxPostCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}

		// 检查上级地址是否为空
		if !checkLocValid(data.State, data.City, data.District, data.Street) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v, Street %v ", zoneCode, data.State, data.City, data.District, data.Street)
		}

		// 检查是否仅配置了postcode
		if !checkLocPostcode(data.State, data.City, data.District, data.Street, data.Postcode) {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v, Street %v, Postcode %v ", zoneCode, data.State, data.City, data.District, data.Street, data.Postcode)
		}

		// 检查地址是否存在
		locInfo, err := m.AddrRepo.GetLocationByLocName(ctx, envvar.GetCID(), data.State, data.City, data.District, data.Street)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil,
				"The address of Zone %v is invalid; State %v, City %v, District %v, Street %v ", zoneCode, data.State, data.City, data.District, data.Street)
		}

		// 检查地址唯一性（完全相同的地址唯一）
		checkDulKey := str.Join(LocationJoinCode, locInfo.GetLocationID(), data.Postcode)
		if _, exist := checkDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil,
				"Duplicate Data : State %v, City %v, District %v, Street %v, Postcode %v;Zone Code: %v",
				data.State, data.City, data.District, data.Street, data.Postcode, data.ZoneCode)
		}
		checkDulMap[checkDulKey] = struct{}{}

		z := zone{zoneCode, int(locInfo.GetLocationID()), data.Postcode}
		checkCoverMap[z] = data
		zoneCodes[zoneCode] = append(zoneCodes[zoneCode], &z)
	}
	// 检查同一Zone Code下地址是否覆盖
	if err := m.checkZoneAndPostcodeLocCover(ctx, checkCoverMap); err != nil {
		return nil, err
	}

	componentToMaskMap, pErr := m.getComponentProductMap(ctx, maskProductIDList)
	if pErr != nil {
		return nil, pErr
	}

	if allocationMethod == allocation.BatchAllocate {
		return m.getBatchZoneVolume(volData, zoneCodes, componentToMaskMap, groupToProductList, maskProductIDList)
	} else {
		return m.getSingleZoneVolume(volData, zoneCodes, componentToMaskMap, groupToProductList, setBlankAsMini, maskProductIDList)
	}
}

func getImportZoneVolData(allocationMethod int64, volFile *http.Response) ([][]string, *srerr.Error) {
	var (
		volData    [][]string
		volHeaders = []string{
			"Zone Code", "Component Product ID", "Group Code", "Min Vol origin", "Min Vol Dest", "Max Vol origin",
			"Max Vol Dest", "Hard-Cap (Max)", "Max COD Vol", "COD Hard-Cap (Max)", "Max Bulky Vol",
			"Bulky Hard-Cap (Max)", "Max High-Value Vol", "High-Value Hard-Cap (Max)", "Max DG vol", "DG Hard-cap(Max)",
		}
		targetVolZoneHeaders = []string{
			"Zone Code", "Component Product ID", "Group Code", "Min Vol Dest", "Max Vol Dest", "Max COD vol dest",
			"Max Bulky vol dest", "Max high-value vol dest", "Max DG vol dest",
		}
		rErr *srerr.Error
	)

	if allocationMethod == allocation.BatchAllocate {
		volData, rErr = fileutil.ReadData(volFile.Body, fileutil.DefaultSheet, targetVolZoneHeaders)
	} else {
		volData, rErr = fileutil.ReadData(volFile.Body, fileutil.DefaultSheet, volHeaders)
	}
	if rErr != nil {
		return nil, rErr
	}

	if len(volData) > MaxRuleVolumeFileRows {
		return nil, srerr.With(srerr.ParamErr, fmt.Sprintf("zone limit data|The number of rows for the uploading file cannot exceed %v", MaxRuleVolumeFileRows), nil)
	}

	return volData, nil
}

func (m *MaskRuleVolumeServiceImpl) getSingleZoneVolume(
	volData [][]string, zoneCodes map[string][]*zone, componentToMaskMap map[int]int, groupToProductList map[string][]int,
	setBlankAsMini bool, maskProductIDList []int,
) ([]*rulevolume.MaskZoneVolumeTab, *srerr.Error) {

	var (
		result            []*rulevolume.MaskZoneVolumeTab
		checkVolDulMap    = make(map[string]struct{})
		productInGroupMap = make(map[int]struct{})
	)
	for _, productList := range groupToProductList {
		for _, p := range productList {
			productInGroupMap[p] = struct{}{}
		}
	}

	for index, data := range volData {
		rowIndex := index + rowIndexOffset

		zoneCode, groupCode := data[0], data[2]
		if zoneCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone Code can not be empty: %v", data)
		}
		if len(zoneCode) > maxRouteZoneCodeLen || len(groupCode) > maxGroupCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}

		zoneInfos, exist := zoneCodes[zoneCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone code %v not found in definition file", zoneCode)
		}

		componentProductID, err := strconv.Atoi(data[1])
		// group code 和 component product id二选一
		if err != nil && groupCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}

		if err := checkProductAlreadyConfigInGroup(rowIndex, componentProductID, productInGroupMap); err != nil {
			return nil, err
		}

		checkDulKey := formatCheckLocVolDulKey(zoneCode, strconv.Itoa(componentProductID))
		if groupCode != "" {
			checkDulKey = formatCheckLocVolDulKey(zoneCode, groupCode)
		}
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Zone Code: %v, Component product id: %v", zoneCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}

		if groupCode == "" && componentProductID != 0 {
			// 如果Group Code为空，那就需要校验Component Product的归属
			if _, exist := componentToMaskMap[componentProductID]; !exist {
				return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductIDList)
			}
		}

		originMaxCap, err := parseMaxCap(data[5], originZoneSetBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid origin max cap: %v", data)
		}
		destMaxCap, err := parseMaxCap(data[6], setBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest max cap: %v", data)
		}
		originMinVol, err := parseMinVol(data[3], originZoneSetBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid origin min vol: %v", data)
		}
		destMinVol, err := parseMinVol(data[4], setBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest min vol: %v", data)
		}
		destMaxCodCap, err := parseParcelTypeMaxCap(data[8], setBlankAsMini, destMaxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest max cod cap: %v", data)
		}
		destMaxBulkyCap, err := parseParcelTypeMaxCap(data[10], setBlankAsMini, destMaxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest max bulky cap: %v", data)
		}
		destMaxHighValueCap, err := parseParcelTypeMaxCap(data[12], setBlankAsMini, destMaxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest max high value cap: %v", data)
		}
		destMaxDgValueCap, err := parseParcelTypeMaxCap(data[14], setBlankAsMini, destMaxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dest max dg cap: %v", data)
		}
		if originMaxCap < 0 || destMaxCap < 0 || originMinVol < 0 || destMinVol < 0 || destMaxCodCap < 0 || destMaxBulkyCap < 0 || destMaxHighValueCap < 0 || destMaxDgValueCap < 0 {
			return nil, srerr.New(srerr.ParamErr, data, negativeErrMsg, rowIndex)
		}

		isHardCap, err := parseHardCap(data[7])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid hard cap: %v", data)
		}
		isCodHardCap, err := parseHardCap(data[9])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid cod hard cap: %v", data)
		}
		isBulkyHardCap, err := parseHardCap(data[11])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid bulky hard cap: %v", data)
		}
		isHighValueHardCap, err := parseHardCap(data[13])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid high value hard cap: %v", data)
		}
		isDgHardCap, err := parseHardCap(data[15])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dg hard cap: %v", data)
		}
		for _, zoneInfo := range zoneInfos {
			if groupCode != "" {
				for _, fulfillmentProductId := range groupToProductList[groupCode] {
					result = append(result, &rulevolume.MaskZoneVolumeTab{
						MaskProductID:                   componentToMaskMap[fulfillmentProductId],
						ComponentProductID:              fulfillmentProductId,
						ZoneCode:                        zoneCode,
						DistrictID:                      zoneInfo.LocID,
						Postcode:                        zoneInfo.PostCode,
						OriginMaxCapacity:               int32(originMaxCap),
						OriginMinVolume:                 int32(originMinVol),
						DestinationMaxCapacity:          int32(destMaxCap),
						DestinationMaxCodCapacity:       int32(destMaxCodCap),
						DestinationMaxBulkyCapacity:     int32(destMaxBulkyCap),
						DestinationMaxHighValueCapacity: int32(destMaxHighValueCap),
						DestinationMaxDgCapacity:        int32(destMaxDgValueCap),
						DestinationMinVolume:            int32(destMinVol),
						IsHardCap:                       isHardCap,
						IsCodHardCap:                    isCodHardCap,
						IsBulkyHardCap:                  isBulkyHardCap,
						IsHighValueHardCap:              isHighValueHardCap,
						IsDgHardCap:                     isDgHardCap,
						GroupCode:                       groupCode,
					})
				}
			} else {
				result = append(result, &rulevolume.MaskZoneVolumeTab{
					MaskProductID:                   componentToMaskMap[componentProductID],
					ComponentProductID:              componentProductID,
					ZoneCode:                        zoneCode,
					DistrictID:                      zoneInfo.LocID,
					Postcode:                        zoneInfo.PostCode,
					OriginMaxCapacity:               int32(originMaxCap),
					DestinationMaxCodCapacity:       int32(destMaxCodCap),
					DestinationMaxBulkyCapacity:     int32(destMaxBulkyCap),
					DestinationMaxHighValueCapacity: int32(destMaxHighValueCap),
					DestinationMaxDgCapacity:        int32(destMaxDgValueCap),
					OriginMinVolume:                 int32(originMinVol),
					DestinationMaxCapacity:          int32(destMaxCap),
					DestinationMinVolume:            int32(destMinVol),
					IsHardCap:                       isHardCap,
					IsCodHardCap:                    isCodHardCap,
					IsBulkyHardCap:                  isBulkyHardCap,
					IsHighValueHardCap:              isHighValueHardCap,
					IsDgHardCap:                     isDgHardCap,
					GroupCode:                       groupCode,
				})
			}
		}
	}
	return result, nil
}

func (m *MaskRuleVolumeServiceImpl) getBatchZoneVolume(
	volData [][]string, zoneCodes map[string][]*zone, componentToMaskMap map[int]int,
	groupToProductList map[string][]int, maskProductIDList []int,
) ([]*rulevolume.MaskZoneVolumeTab, *srerr.Error) {
	var (
		result            []*rulevolume.MaskZoneVolumeTab
		checkVolDulMap    = make(map[string]struct{})
		productInGroupMap = make(map[int]struct{})
	)

	for _, productList := range groupToProductList {
		for _, p := range productList {
			productInGroupMap[p] = struct{}{}
		}
	}

	for index, data := range volData {
		// 跳过空白行
		if isEmptyRow(data) {
			continue
		}

		rowIndex := index + rowIndexOffset

		zoneCode, groupCode := data[0], data[2]
		if zoneCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone Code can not be empty: %v", data)
		}
		if len(zoneCode) > maxRouteZoneCodeLen || len(groupCode) > maxGroupCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}
		zoneInfos, exist := zoneCodes[zoneCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Zone code %v not found in definition file", zoneCode)
		}

		componentProductID, err := strconv.Atoi(data[1])
		// group code 和 component product id二选一
		if err != nil && groupCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}

		if err := checkProductAlreadyConfigInGroup(rowIndex, componentProductID, productInGroupMap); err != nil {
			return nil, err
		}

		checkDulKey := formatCheckLocVolDulKey(zoneCode, strconv.Itoa(componentProductID))
		if groupCode != "" {
			checkDulKey = formatCheckLocVolDulKey(zoneCode, groupCode)
		}
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Zone Code: %v, Component product id: %v", zoneCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}

		if groupCode == "" && componentProductID != 0 {
			// 如果Group Code为空，那就需要校验Component Product的归属
			if _, exist := componentToMaskMap[componentProductID]; !exist {
				return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductIDList)
			}
		}

		minVol, maxVol, maxCodVol, maxBulkyVol, maxHighValueVol, maxDgVol, err := m.parseBatchZoneRouteVolumes(data, false)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, rowIndex, err)
		}
		if maxVol < 0 || minVol < 0 || maxCodVol < 0 || maxBulkyVol < 0 || maxHighValueVol < 0 || maxDgVol < 0 {
			return nil, srerr.New(srerr.ParamErr, data, negativeErrMsg, rowIndex)
		}
		for _, zoneInfo := range zoneInfos {
			if groupCode != "" {
				for _, fulfillmentProductId := range groupToProductList[groupCode] {
					result = append(result, &rulevolume.MaskZoneVolumeTab{
						MaskProductID:                   componentToMaskMap[fulfillmentProductId],
						ComponentProductID:              fulfillmentProductId,
						GroupCode:                       groupCode,
						ZoneCode:                        zoneCode,
						DistrictID:                      zoneInfo.LocID,
						Postcode:                        zoneInfo.PostCode,
						DestinationMaxCapacity:          int32(maxVol),
						DestinationMaxCodCapacity:       int32(maxCodVol),
						DestinationMaxBulkyCapacity:     int32(maxBulkyVol),
						DestinationMaxHighValueCapacity: int32(maxHighValueVol),
						DestinationMaxDgCapacity:        int32(maxDgVol),
						DestinationMinVolume:            int32(minVol),
					})
				}
			} else {
				result = append(result, &rulevolume.MaskZoneVolumeTab{
					MaskProductID:                   componentToMaskMap[componentProductID],
					ComponentProductID:              componentProductID,
					ZoneCode:                        zoneCode,
					DistrictID:                      zoneInfo.LocID,
					Postcode:                        zoneInfo.PostCode,
					DestinationMaxCapacity:          int32(maxVol),
					DestinationMaxCodCapacity:       int32(maxCodVol),
					DestinationMaxBulkyCapacity:     int32(maxBulkyVol),
					DestinationMaxHighValueCapacity: int32(maxHighValueVol),
					DestinationMaxDgCapacity:        int32(maxDgVol),
					DestinationMinVolume:            int32(minVol),
				})
			}
		}
	}

	return result, nil
}

func (m *MaskRuleVolumeServiceImpl) getSingleRouteVolume(
	volData [][]string, routeCodes map[string][]*route, componentToMaskMap map[int]int, groupToProductList map[string][]int,
	setBlankAsMini bool, maskProductIDList []int,
) ([]*rulevolume.MaskRouteVolumeTab, *srerr.Error) {

	var (
		result            = make([]*rulevolume.MaskRouteVolumeTab, 0)
		checkVolDulMap    = make(map[string]struct{})
		productInGroupMap = make(map[int]struct{})
	)

	for _, productList := range groupToProductList {
		for _, p := range productList {
			productInGroupMap[p] = struct{}{}
		}
	}
	for index, data := range volData {
		rowIndex := index + rowIndexOffset

		routeCode, groupCode := data[0], data[2]
		if routeCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Route Code can not be empty: %v", data)
		}
		if len(routeCode) > maxRouteZoneCodeLen || len(groupCode) > maxGroupCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}
		routeInfos, exist := routeCodes[routeCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Route code %v not found in definition file", routeCode)
		}

		componentProductID, err := strconv.Atoi(data[1])
		// group code 和 component product id二选一
		if err != nil && groupCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}

		if groupCode == "" && componentProductID != 0 {
			// 如果Group Code为空，那就需要校验Component Product的归属
			if _, exist := componentToMaskMap[componentProductID]; !exist {
				return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductIDList)
			}
		}

		if err := checkProductAlreadyConfigInGroup(rowIndex, componentProductID, productInGroupMap); err != nil {
			return nil, err
		}

		checkDulKey := formatCheckLocVolDulKey(routeCode, strconv.Itoa(componentProductID))
		if groupCode != "" {
			checkDulKey = formatCheckLocVolDulKey(routeCode, groupCode)
		}
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Route Code: %v, Component product id: %v", routeCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}
		maxCap, err := parseMaxCap(data[4], setBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid max cap: %v", data)
		}
		minVol, err := parseMinVol(data[3], setBlankAsMini)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid min vol: %v", data)
		}
		maxCodCap, err := parseParcelTypeMaxCap(data[6], setBlankAsMini, maxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid max cod cap: %v", data)
		}
		maxBulkyCap, err := parseParcelTypeMaxCap(data[8], setBlankAsMini, maxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid max bulky cap: %v", data)
		}
		maxHighValueCap, err := parseParcelTypeMaxCap(data[10], setBlankAsMini, maxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid max high value cap: %v", data)
		}
		maxDgCap, err := parseParcelTypeMaxCap(data[12], setBlankAsMini, maxCap)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid max dg cap: %v", data)
		}
		if maxCap < 0 || minVol < 0 || maxCodCap < 0 || maxBulkyCap < 0 || maxHighValueCap < 0 || maxDgCap < 0 {
			return nil, srerr.New(srerr.ParamErr, data, negativeErrMsg, rowIndex)
		}

		isHardCap, err := parseHardCap(data[5])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid hard cap: %v", data)
		}
		isCodHardCap, err := parseHardCap(data[7])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid cod hard cap: %v", data)
		}
		isBulkyHardCap, err := parseHardCap(data[9])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid bulky hard cap: %v", data)
		}
		isHighValueHardCap, err := parseHardCap(data[11])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid high value hard cap: %v", data)
		}
		isDgHardCap, err := parseHardCap(data[13])
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid dg hard cap: %v", data)
		}
		for _, routeInfo := range routeInfos {
			if groupCode != "" {
				for _, fulfillmentProductId := range groupToProductList[groupCode] {
					result = append(result, &rulevolume.MaskRouteVolumeTab{
						MaskProductID:         componentToMaskMap[fulfillmentProductId],
						ComponentProductID:    fulfillmentProductId,
						RouteCode:             routeCode,
						OriginDistrictID:      routeInfo.OriginLocID,
						OriginPostcode:        routeInfo.OriginPostcode,
						DestinationDistrictID: routeInfo.DestLocID,
						DestinationPostcode:   routeInfo.DestPostcode,
						MaxCapacity:           int32(maxCap),
						MaxCodCapacity:        int32(maxCodCap),
						MaxBulkyCapacity:      int32(maxBulkyCap),
						MaxHighValueCapacity:  int32(maxHighValueCap),
						MaxDgCapacity:         int32(maxDgCap),
						MinVolume:             int32(minVol),
						IsHardCap:             isHardCap,
						IsCodHardCap:          isCodHardCap,
						IsBulkyHardCap:        isBulkyHardCap,
						IsHighValueHardCap:    isHighValueHardCap,
						IsDgHardCap:           isDgHardCap,
						GroupCode:             groupCode,
					})
				}
			} else {
				result = append(result, &rulevolume.MaskRouteVolumeTab{
					MaskProductID:         componentToMaskMap[componentProductID],
					ComponentProductID:    componentProductID,
					RouteCode:             routeCode,
					OriginDistrictID:      routeInfo.OriginLocID,
					OriginPostcode:        routeInfo.OriginPostcode,
					DestinationDistrictID: routeInfo.DestLocID,
					DestinationPostcode:   routeInfo.DestPostcode,
					MaxCapacity:           int32(maxCap),
					MaxCodCapacity:        int32(maxCodCap),
					MaxBulkyCapacity:      int32(maxBulkyCap),
					MaxHighValueCapacity:  int32(maxHighValueCap),
					MaxDgCapacity:         int32(maxDgCap),
					MinVolume:             int32(minVol),
					IsHardCap:             isHardCap,
					IsCodHardCap:          isCodHardCap,
					IsBulkyHardCap:        isBulkyHardCap,
					IsHighValueHardCap:    isHighValueHardCap,
					IsDgHardCap:           isDgHardCap,
					GroupCode:             groupCode,
				})
			}
		}
	}

	return result, nil
}

func (m *MaskRuleVolumeServiceImpl) getBatchRouteVolume(
	volData [][]string, routeCodes map[string][]*route, componentToMaskMap map[int]int, groupToProductList map[string][]int,
	setBlankAsMini bool, maskProductIDList []int,
) ([]*rulevolume.MaskRouteVolumeTab, *srerr.Error) {

	var (
		result         = make([]*rulevolume.MaskRouteVolumeTab, 0)
		checkVolDulMap = make(map[string]struct{})
	)

	for index, data := range volData {
		rowIndex := index + rowIndexOffset

		routeCode, groupCode := data[0], data[2]
		if routeCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Route Code can not be empty: %v", data)
		}
		if len(routeCode) > maxRouteZoneCodeLen || len(groupCode) > maxGroupCodeLen {
			return nil, srerr.New(srerr.ParamErr, data, exceedLengthErrMsg, rowIndex)
		}
		routeInfos, exist := routeCodes[routeCode]
		if !exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Route code %v not found in definition file", routeCode)
		}

		componentProductID, err := strconv.Atoi(data[1])
		// group code 和 component product id二选一
		if err != nil && groupCode == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "Invalid component product ID: %v", data)
		}

		if groupCode == "" && componentProductID != 0 {
			// 如果Group Code为空，那就需要校验Component Product的归属
			if _, exist := componentToMaskMap[componentProductID]; !exist {
				return nil, srerr.New(srerr.ParamErr, nil, "Component product id %v does not belong to Mask product id %v", componentProductID, maskProductIDList)
			}
		}

		checkDulKey := formatCheckLocVolDulKey(routeCode, strconv.Itoa(componentProductID))
		if groupCode != "" {
			checkDulKey = formatCheckLocVolDulKey(routeCode, groupCode)
		}
		if _, exist := checkVolDulMap[checkDulKey]; exist {
			return nil, srerr.New(srerr.ParamErr, nil, "Duplicate Data : Route Code: %v, Component product id: %v", routeCode, componentProductID)
		}
		checkVolDulMap[checkDulKey] = struct{}{}

		minVol, maxCap, maxCodCap, maxBulkyCap, maxHighValueCap, maxDgCap, err := m.parseBatchZoneRouteVolumes(data, setBlankAsMini)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, rowIndex, err)
		}
		if maxCap < 0 || minVol < 0 || maxCodCap < 0 || maxBulkyCap < 0 || maxHighValueCap < 0 {
			return nil, srerr.New(srerr.ParamErr, data, negativeErrMsg, rowIndex)
		}

		for _, routeInfo := range routeInfos {
			if groupCode != "" {
				for _, fulfillmentProductId := range groupToProductList[groupCode] {
					result = append(result, &rulevolume.MaskRouteVolumeTab{
						MaskProductID:         componentToMaskMap[fulfillmentProductId],
						ComponentProductID:    fulfillmentProductId,
						RouteCode:             routeCode,
						OriginDistrictID:      routeInfo.OriginLocID,
						OriginPostcode:        routeInfo.OriginPostcode,
						DestinationDistrictID: routeInfo.DestLocID,
						DestinationPostcode:   routeInfo.DestPostcode,
						MaxCapacity:           int32(maxCap),
						MaxCodCapacity:        int32(maxCodCap),
						MaxBulkyCapacity:      int32(maxBulkyCap),
						MaxHighValueCapacity:  int32(maxHighValueCap),
						MaxDgCapacity:         int32(maxDgCap),
						MinVolume:             int32(minVol),
						GroupCode:             groupCode,
					})
				}
			} else {
				result = append(result, &rulevolume.MaskRouteVolumeTab{
					MaskProductID:         componentToMaskMap[componentProductID],
					ComponentProductID:    componentProductID,
					RouteCode:             routeCode,
					OriginDistrictID:      routeInfo.OriginLocID,
					OriginPostcode:        routeInfo.OriginPostcode,
					DestinationDistrictID: routeInfo.DestLocID,
					DestinationPostcode:   routeInfo.DestPostcode,
					MaxCapacity:           int32(maxCap),
					MaxCodCapacity:        int32(maxCodCap),
					MaxBulkyCapacity:      int32(maxBulkyCap),
					MaxHighValueCapacity:  int32(maxHighValueCap),
					MaxDgCapacity:         int32(maxDgCap),
					MinVolume:             int32(minVol),
				})
			}
		}
	}

	return result, nil
}

// parseBatchZoneRouteVolumes 解析Batch模式Route Volume数据
func (m *MaskRuleVolumeServiceImpl) parseBatchZoneRouteVolumes(data []string, setBlankAsMini bool) (int, int, int, int, int, int, error) {
	minVol, err := parseVol(data[3], FillBlankZero)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	maxCap, err := parseVol(data[4], FillBlankMax)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	maxCodCap, err := parseParcelTypeVol(data[5], FillBlankMax, maxCap)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	maxBulkyCap, err := parseParcelTypeVol(data[6], FillBlankMax, maxCap)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	maxHighValueCap, err := parseParcelTypeVol(data[7], FillBlankMax, maxCap)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	maxDgCap, err := parseParcelTypeVol(data[8], FillBlankMax, maxCap)
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}

	return minVol, maxCap, maxCodCap, maxBulkyCap, maxHighValueCap, maxDgCap, nil
}

func isEmptyRow(row []string) bool {
	if len(row) == 0 {
		return true
	}
	for _, unit := range row {
		if unit != "" {
			return false
		}
	}

	return true
}

// parseMinVol 为空设置Min/max Vol默认值，否则正常解析str->int
// min类型的，设置为0，max类型的设置为9个9
func parseVol(data string, fillBlankType int) (int, error) {
	if data == "" {
		if fillBlankType == FillBlankMax {
			return rulevolume.DefaultMaskMaxCapacity, nil
		}
		if fillBlankType == FillBlankZero {
			return 0, nil
		}
		return 0, srerr.New(srerr.BlankFillError, nil, "illegal blank fill type")
	}
	return strconv.Atoi(data)
}

func parseParcelTypeVol(data string, fillBlankType int, maxVol int) (int, error) {
	parcelTypeMaxVol, err := parseVol(data, fillBlankType)
	if err != nil {
		return 0, err
	}

	if parcelTypeMaxVol > maxVol {
		// Parcel Type Max Volume要小于Max Volume，如果比Max Volume大则取Max Volume
		parcelTypeMaxVol = maxVol
	}

	return parcelTypeMaxVol, nil
}

func (m *MaskRuleVolumeServiceImpl) ListRuleVolumes(ctx context.Context, req *GetRuleVolumeListRequest) ([]*rulevolume.MaskRuleVolumeTab, int, *srerr.Error) {
	_, offset, limit := apiutil.GetOffsetAndLimit(int(req.PageNo), int(req.Limit))
	list, total, err := m.MaskRuleVolumeRepo.ListRuleVolume(ctx, req.ID, req.MaskProductID, req.RuleStatus, req.RuleType, offset, limit, req.RuleMode, req.AllocationMethod)
	if err != nil {
		return nil, 0, err
	}

	for i := 0; i < len(list); i++ {
		list[i].AllocationMethod = convertAllocationMethod(list[i].AllocationMethod)
	}

	return list, total, err
}

func (m *MaskRuleVolumeServiceImpl) GetActiveRuleVolumeByMaskProductIDWithCache(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	var (
		key = fmt.Sprintf("%d:%d:%d", maskProductID, rm, allocationMethod)
		ret = rulevolume.MaskRuleVolumeTab{}
	)

	if err := m.LevelCache.Get(ctx, constant.LevelCacheGetActiveRuleVolumeByMaskProductID, key, &ret,
		layercache.WithLoader(m.getActiveRuleVolumeByMaskProductIDLoader),
		layercache.WithWarmExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetActiveRuleVolumeByMaskProductID|get_cache_fail|id=%d, value=%+v", key, ret)
		return nil, srerr.With(srerr.LevelCacheErr, key, err)
	}

	return &ret, nil
}

func (m *MaskRuleVolumeServiceImpl) getActiveRuleVolumeByMaskProductIDLoader(ctx context.Context, key string) (interface{}, error) {
	splitKeys := strings.Split(key, ":")
	if len(splitKeys) != 3 {
		return nil, fmt.Errorf("key not valid, key=%s", key)
	}

	maskProductID, sErr := strconv.Atoi(splitKeys[0])
	if sErr != nil {
		return nil, fmt.Errorf("can not convert key to mask product id, key=%s", key)
	}

	ruleMode, sErr := strconv.Atoi(splitKeys[1])
	if sErr != nil {
		return nil, fmt.Errorf("can not convert key to rule mode, key=%s", key)
	}

	allocationMethod, sErr := strconv.Atoi(splitKeys[2])
	if sErr != nil {
		return nil, fmt.Errorf("can not convert key to allocation method, key=%s", key)
	}

	ret, err := m.MaskRuleVolumeRepo.GetActiveRuleVolumeByMaskProductID(ctx, int64(maskProductID), rule_mode.RuleMode(ruleMode), int64(allocationMethod))
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (m *MaskRuleVolumeServiceImpl) GetRuleVolumeByID(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	data, err := m.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if data.RouteLimitFile != "" {
		data.RouteLimitFile = fileutil.GetLocalVolumeS3Url(ctx, data.RouteLimitFile)
	}
	if data.RouteDefinitionFile != "" {
		data.RouteDefinitionFile = fileutil.GetLocalVolumeS3Url(ctx, data.RouteDefinitionFile)
	}
	if data.ZoneLimitFile != "" {
		data.ZoneLimitFile = fileutil.GetLocalVolumeS3Url(ctx, data.ZoneLimitFile)
	}
	if data.ZoneDefinitionFile != "" {
		data.ZoneDefinitionFile = fileutil.GetLocalVolumeS3Url(ctx, data.ZoneDefinitionFile)
	}
	data.AllocationMethod = convertAllocationMethod(data.AllocationMethod)

	return data, nil
}

func (m *MaskRuleVolumeServiceImpl) GetRuleVolumeByIDWithCache(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	var (
		key = strconv.Itoa(int(id))
		ret rulevolume.MaskRuleVolumeTab
	)

	if err := m.LevelCache.Get(ctx, constant.LevelCacheGetMaskRuleVolumeByID, key, &ret,
		layercache.WithLoader(m.getRuleVolumeByIDLoader),
		layercache.WithWarmExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetRuleVolumeByIDWithCache|get_cache_fail|id=%d, value=%+v", key, ret)
		return nil, srerr.With(srerr.LevelCacheErr, id, err)
	}

	return &ret, nil
}

func (m *MaskRuleVolumeServiceImpl) getRuleVolumeByIDLoader(ctx context.Context, key string) (interface{}, error) {
	id, _ := strconv.Atoi(key)
	ret, err := m.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, uint64(id))
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (m *MaskRuleVolumeServiceImpl) GetEffectiveBatchAllocateRuleVolume(ctx context.Context, maskProductID int) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	data, err := localcache.Get(ctx, constant.BatchAllocateRuleVolume, strconv.Itoa(maskProductID))
	if err != nil {
		return nil, srerr.With(srerr.LocalCacheErr, maskProductID, err)
	}
	return data.(*rulevolume.MaskRuleVolumeTab), nil
}

func (m *MaskRuleVolumeServiceImpl) CreateRuleVolume(ctx context.Context, req *CreateRuleVolumeRequest) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	// 1. 从Excel中解析Route/Zone数据
	var (
		maskProductIDList  []int // mask combination mode，一个rule volume用多个mask product
		groupToProductList = make(map[string][]int)
		routeVolumes       []*rulevolume.MaskRouteVolumeTab
		zoneVolumes        []*rulevolume.MaskZoneVolumeTab
		pErr               *srerr.Error
	)

	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDList = append(maskProductIDList, m.MaskProductID)
		}
	} else {
		maskProductIDList = []int{req.MaskProductID}
	}

	if req.ShareVolume {
		for _, groupInfo := range req.GroupInfo.FulfillmentProductGroupInfos {
			if _, exist := groupToProductList[groupInfo.GroupCode]; !exist {
				groupToProductList[groupInfo.GroupCode] = make([]int, 0, len(groupInfo.FulfillmentProductInfos))
			}
			for _, f := range groupInfo.FulfillmentProductInfos {
				groupToProductList[groupInfo.GroupCode] = append(groupToProductList[groupInfo.GroupCode], f.FulfillmentProductID)
			}
		}
	}

	if req.RuleType == rulevolume.LocVolumeTypeRoute {
		if req.RouteDefinitionFile != "" && req.RouteLimitFile != "" {
			routeVolumes, pErr = m.ParseRouteExcel(ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
		}
	} else if req.RuleType == rulevolume.LocVolumeTypeZone {
		if req.ZoneDefinitionFile != "" && req.ZoneLimitFile != "" {
			zoneVolumes, pErr = m.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
		}
	} else if req.RuleType == rulevolume.LocVolumeTypeRouteAndZone {
		if req.RouteDefinitionFile != "" && req.RouteLimitFile != "" {
			if routeVolumes, pErr = m.ParseRouteExcel(ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod); pErr != nil {
				return nil, pErr
			}
		}
		if req.ZoneDefinitionFile != "" && req.ZoneLimitFile != "" {
			zoneVolumes, pErr = m.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
		}
	}
	if pErr != nil {
		return nil, pErr
	}

	// 2，Create Rule Volume
	operateBy, _ := apiutil.GetUserInfo(ctx)
	req.OperateBy = operateBy
	if req.EffectiveImmediately {
		req.EffectiveStartTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	}
	newVolume := &rulevolume.MaskRuleVolumeTab{
		MaskProductID:           req.MaskProductID,
		AllocationMethod:        int(req.AllocationMethod),
		DefaultVolumeLimit:      req.DefaultVolumeLimit,
		RuleStatus:              rulevolume.MaskRuleVolumeStatusDraft,
		RuleType:                req.RuleType,
		RouteDefinitionFile:     req.RouteDefinitionFile,
		RouteLimitFile:          req.RouteLimitFile,
		ZoneDefinitionFile:      req.ZoneDefinitionFile,
		ZoneLimitFile:           req.ZoneLimitFile,
		OperateBy:               req.OperateBy,
		EffectiveStartTime:      req.EffectiveStartTime,
		SetVolumeBlankAsMinimum: req.SetVolumeBlankAsMinimum,
		RuleMode:                req.RuleMode,
		MaskCombinationMode:     req.MaskCombinationMode,
		ShareVolume:             req.ShareVolume,
		GroupInfo:               req.GroupInfo,
	}
	convertCountryLimit(newVolume.DefaultVolumeLimit)
	//forecast && 【BE FE 发布间隔期】兼容
	if newVolume.RuleMode == 0 {
		newVolume.RuleMode = int32(rule_mode.MplOrderRule)
	}
	// 如果无需插入route/zone，可以直接update到request的status
	if len(routeVolumes) == 0 && len(zoneVolumes) == 0 {
		newVolume.RuleStatus = req.RuleStatus
	}
	ruleVolume, err := m.MaskRuleVolumeRepo.CreateRuleVolume(ctx, newVolume)
	if err != nil {
		return nil, err
	}

	// 3. 插入Route/Zone数据
	newVolume.RuleStatus = req.RuleStatus
	msg := &UpdateMaskRouteAndZoneVolumeMsg{
		RuleVolume: newVolume,
	}
	if len(routeVolumes) != 0 {
		msg.RouteVolumes = routeVolumes
	}
	if len(zoneVolumes) != 0 {
		msg.ZoneVolumes = zoneVolumes
	}

	if err := m.sendKafka(ctx, msg); err != nil {
		logger.CtxLogErrorf(ctx, "send kafka failed %v", err)
		return nil, err
	}

	return ruleVolume, nil
}

func (m *MaskRuleVolumeServiceImpl) sendKafka(ctx context.Context, msg *UpdateMaskRouteAndZoneVolumeMsg) *srerr.Error {
	msgBody, err := jsoniter.Marshal(msg)
	if err != nil {
		return srerr.With(srerr.JsonErr, nil, err)
	}
	key := genUpdateVolumeKey(ctx, msg.RuleVolume.ID, msg.RuleVolume.RuleType)
	if err := redisutil.GetDefaultInstance().Set(ctx, key, msgBody, updateVolumeDataExpireTime).Err(); err != nil {
		return srerr.With(srerr.ServerErr, nil, err)
	}
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskUpdateRouteAndZoneVolume, []byte(key), nil, ""); err != nil {
		return srerr.With(srerr.SendMessageError, nil, err)
	}
	return nil
}

func (m *MaskRuleVolumeServiceImpl) UpdateRuleVolume(ctx context.Context, req *UpdateRuleVolumeRequest) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	// 1. 检查是否存在
	draft, err := m.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	draft.AllocationMethod = convertAllocationMethod(draft.AllocationMethod)

	// 2. 检查是否可编辑
	if !draft.IsEditable(ctx) {
		return nil, srerr.New(srerr.ParamErr, nil, "rule volume %v is not editable", req.ID)
	}

	// 3. 从Excel中解析Route/Zone数据
	var (
		maskProductIDList  []int // share volume下，一个rule volume用多个mask product
		groupToProductList = make(map[string][]int)
		routeVolumes       []*rulevolume.MaskRouteVolumeTab
		zoneVolumes        []*rulevolume.MaskZoneVolumeTab
		pErr               *srerr.Error
	)

	if req.ShareVolume {
		for _, groupInfo := range req.GroupInfo.FulfillmentProductGroupInfos {
			if _, exist := groupToProductList[groupInfo.GroupCode]; !exist {
				groupToProductList[groupInfo.GroupCode] = make([]int, 0, len(groupInfo.FulfillmentProductInfos))
			}
			for _, f := range groupInfo.FulfillmentProductInfos {
				groupToProductList[groupInfo.GroupCode] = append(groupToProductList[groupInfo.GroupCode], f.FulfillmentProductID)
			}
		}
	}

	if req.MaskCombinationMode {
		for _, m := range req.GroupInfo.MaskProductInfos {
			maskProductIDList = append(maskProductIDList, m.MaskProductID)
		}
	} else {
		maskProductIDList = []int{draft.MaskProductID}
	}

	if req.RuleType == rulevolume.LocVolumeTypeRoute {
		if req.RouteDefinitionFile != "" && req.RouteLimitFile != "" {
			routeVolumes, pErr = m.ParseRouteExcel(ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod)
		}
	} else if req.RuleType == rulevolume.LocVolumeTypeZone {
		if req.ZoneDefinitionFile != "" && req.ZoneLimitFile != "" {
			zoneVolumes, pErr = m.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, int64(draft.AllocationMethod))
		}
	} else if req.RuleType == rulevolume.LocVolumeTypeRouteAndZone {
		if req.RouteDefinitionFile != "" && req.RouteLimitFile != "" {
			if routeVolumes, pErr = m.ParseRouteExcel(ctx, maskProductIDList, groupToProductList, req.RouteDefinitionFile, req.RouteLimitFile, req.SetVolumeBlankAsMinimum, req.AllocationMethod); pErr != nil {
				monitoring.ReportError(ctx, monitoring.RouteAndZoneParseError, monitoring.RouteParseError, fmt.Sprintf("parse route failed %v", pErr))
				return nil, pErr
			}
		}
		if req.ZoneDefinitionFile != "" && req.ZoneLimitFile != "" {
			if zoneVolumes, pErr = m.ParseZoneExcel(ctx, maskProductIDList, groupToProductList, req.ZoneDefinitionFile, req.ZoneLimitFile, req.SetVolumeBlankAsMinimum, int64(draft.AllocationMethod)); pErr != nil {
				monitoring.ReportError(ctx, monitoring.RouteAndZoneParseError, monitoring.ZoneParseError, fmt.Sprintf("parse zone failed %v", pErr))
				return nil, pErr
			}
		}
	}
	if pErr != nil {
		return nil, pErr
	}
	if req.RuleType == rulevolume.LocVolumeTypeRouteAndZone && (len(routeVolumes) == 0 || len(zoneVolumes) == 0) {
		monitoring.ReportError(ctx, monitoring.RouteAndZoneParseError, monitoring.RouteAndZoneAllZero, "route & zone mode route and zone can not empty")
		return nil, srerr.New(srerr.ParamErr, nil, "route & zone mode route and zone can not empty")
	}

	// 4. Update Rule Volume
	if req.EffectiveImmediately {
		req.EffectiveStartTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	draft = &rulevolume.MaskRuleVolumeTab{
		ID:                      draft.ID,
		MaskProductID:           draft.MaskProductID,
		DefaultVolumeLimit:      req.DefaultVolumeLimit,
		RuleStatus:              rulevolume.MaskRuleVolumeStatusDraft,
		RuleType:                req.RuleType,
		RouteDefinitionFile:     req.RouteDefinitionFile,
		RouteLimitFile:          req.RouteLimitFile,
		ZoneDefinitionFile:      req.ZoneDefinitionFile,
		ZoneLimitFile:           req.ZoneLimitFile,
		OperateBy:               operateBy,
		EffectiveStartTime:      req.EffectiveStartTime,
		SetVolumeBlankAsMinimum: req.SetVolumeBlankAsMinimum,
		CTime:                   draft.CTime,
		RuleMode:                draft.RuleMode,
		AllocationMethod:        int(req.AllocationMethod),
		MaskCombinationMode:     req.MaskCombinationMode,
		ShareVolume:             req.ShareVolume,
		GroupInfo:               req.GroupInfo,
	}
	convertCountryLimit(draft.DefaultVolumeLimit)
	// 如果无需插入route/zone，可以直接update到request的status
	if len(routeVolumes) == 0 && len(zoneVolumes) == 0 {
		draft.RuleStatus = req.RuleStatus
	}

	//SSCSMR-2660:接入审批流, 目前只有single allocate需要接入
	if m.needAudit(ctx, draft, req) {
		if eErr := m.ApprovalExecutor.Execute(ctx, auditclient.AllocateVolumeRule, int64(draft.ID)); eErr != nil {
			logger.CtxLogErrorf(ctx, "apply for approval err:%v", eErr)
			return nil, eErr
		}
		draft.RuleStatus = rulevolume.MaskRuleVolumeStatusPendingApproval
	}

	data, err := m.MaskRuleVolumeRepo.UpdateRuleVolume(ctx, draft)
	if err != nil {
		return nil, err
	}

	// 5. 删除该Rule Volume下旧的Route/Zone，插入新的Route/Zone
	if data.RuleStatus != rulevolume.MaskRuleVolumeStatusPendingApproval {
		data.RuleStatus = req.RuleStatus // update rule volume会把active改成queueing状态（仅为展示状态，实际没使用），因此需要重置status字段
	}
	msg := &UpdateMaskRouteAndZoneVolumeMsg{
		RuleVolume: data,
	}
	if len(routeVolumes) != 0 {
		msg.RouteVolumes = routeVolumes
	}
	if len(zoneVolumes) != 0 {
		msg.ZoneVolumes = zoneVolumes
	}

	if err := m.sendKafka(ctx, msg); err != nil {
		logger.CtxLogErrorf(ctx, "send kafka failed %v", err)
		return nil, err
	}
	return data, nil
}
func (m *MaskRuleVolumeServiceImpl) CopyRuleVolume(ctx context.Context, id uint64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	ruleVolume, err := m.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, id)
	if err != nil {
		return nil, err
	}
	ruleVolume.AllocationMethod = convertAllocationMethod(ruleVolume.AllocationMethod)
	if ruleVolume.RuleStatus == rulevolume.MaskRuleVolumeStatusDraft || ruleVolume.RuleStatus == rulevolume.MaskRuleVolumeStatusUncompleted {
		return nil, srerr.New(srerr.ParamErr, nil, "cannot copy a draft rule volume")
	}
	newRuleVol := &rulevolume.MaskRuleVolumeTab{
		MaskProductID:           ruleVolume.MaskProductID,
		EffectiveStartTime:      ruleVolume.EffectiveStartTime,
		SetVolumeBlankAsMinimum: ruleVolume.SetVolumeBlankAsMinimum,
		RuleStatus:              rulevolume.MaskRuleVolumeStatusDraft,
		RuleType:                ruleVolume.RuleType,
		OperateBy:               operateBy,
		DefaultVolumeLimit:      ruleVolume.DefaultVolumeLimit,
		RouteDefinitionFile:     ruleVolume.RouteDefinitionFile,
		RouteLimitFile:          ruleVolume.RouteLimitFile,
		ZoneDefinitionFile:      ruleVolume.ZoneDefinitionFile,
		ZoneLimitFile:           ruleVolume.ZoneLimitFile,
		RuleMode:                ruleVolume.RuleMode,
		AllocationMethod:        ruleVolume.AllocationMethod,
		MaskCombinationMode:     ruleVolume.MaskCombinationMode,
		ShareVolume:             ruleVolume.ShareVolume,
		GroupInfo:               ruleVolume.GroupInfo,
	}
	data, err := m.MaskRuleVolumeRepo.CreateRuleVolume(ctx, newRuleVol)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (m *MaskRuleVolumeServiceImpl) DeleteRuleVolume(ctx context.Context, id uint64) *srerr.Error {
	volume, err := m.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, id)
	if err != nil {
		return err
	}
	if volume.RuleStatus != rulevolume.MaskRuleVolumeStatusUncompleted && volume.RuleStatus != rulevolume.MaskRuleVolumeStatusDraft {
		return srerr.New(srerr.ParamErr, nil, "only the rules of state draft can be deleted")
	}
	if err := m.MaskRuleVolumeRepo.DeleteRuleVolume(ctx, id, volume.MaskProductID); err != nil {
		return err
	}
	if err := m.MaskRuleVolumeRepo.DeleteRouteLimitsByRuleVolumeID(ctx, id); err != nil {
		return err
	}
	if err := m.MaskRuleVolumeRepo.DeleteZoneLimitsByRuleVolumeID(ctx, id); err != nil {
		return err
	}

	return nil
}

// 这里是 规则配置数据的key,localcache 中使用的
func formatRouteVolumeCacheKey(
	maskProductID, componentProductID, originLocID, destLocID int64, originPostcode, destPostcode string,
	rm rule_mode.RuleMode, allocationMethod int,
) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v:%v:%v",
		rm.String(), allocationMethod, maskProductID, componentProductID, originLocID, destLocID, originPostcode, destPostcode)
}

func formatZoneVolumeCacheKey(
	maskProductID, componentProductID, locID int64, postcode string, rm rule_mode.RuleMode, allocationMethod int,
) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v", rm.String(), allocationMethod, maskProductID, componentProductID, locID, postcode)
}

// checkLocValid 检查上级地址是否为空
func checkLocValid(state, city, district, street string) bool {
	if state == "" {
		return false
	}
	if city == "" && district != "" {
		return false
	}
	if district == "" && street != "" {
		return false
	}
	return true
}

func checkLocPostcode(state, city, district, street, postcode string) bool {
	if state == "" && city == "" && district == "" && street == "" && postcode != "" {
		return false
	}
	return true
}

// checkRouteAndPostcodeLocCover
// 增加了postcode层级，需要检查RouteCode + Postcode下的地址是否有覆盖。
func (m *MaskRuleVolumeServiceImpl) checkRouteAndPostcodeLocCover(ctx context.Context, checkMap map[route]*ImportRouteCode) *srerr.Error {
	jobs := make(chan route, len(checkMap))
	results := make(chan *srerr.Error, len(checkMap))
	// Start run workers
	for i := 0; i < workerNum; i++ {
		go m.checkRouteAndPostcodeLocCoverWorker(ctx, checkMap, jobs, results)
	}
	// Send jobs to worker
	for job := range checkMap {
		jobs <- job
	}
	close(jobs)
	// Check worker return result(error)
	for i := 0; i < len(checkMap); i++ {
		if err := <-results; err != nil {
			return err
		}
	}

	return nil
}

// 并发执行的worker
func (m *MaskRuleVolumeServiceImpl) checkRouteAndPostcodeLocCoverWorker(ctx context.Context, checkMap map[route]*ImportRouteCode, jobs <-chan route, results chan<- *srerr.Error) {
	for job := range jobs {
		originNode, _ := m.AddrRepo.GetLocationByLocId(ctx, int64(job.OriginLocID))
		destNode, _ := m.AddrRepo.GetLocationByLocId(ctx, int64(job.DestLocID))
		// 防止LocationTree有问题的情况下for无限循环
		var originLevelCount int
		for originCurNode := originNode; originCurNode != nil && originLevelCount < constant.MaxLocationLevel; {
			var destLevelCount int
			for destCurNode := destNode; destCurNode != nil && destLevelCount < constant.MaxLocationLevel; {
				checkKey := route{job.RouteCode, int(originCurNode.GetLocationID()), int(destCurNode.GetLocationID()), job.OriginPostcode, job.DestPostcode}
				if data, exist := checkMap[checkKey]; exist && checkKey != job {
					retErr := srerr.New(srerr.ParamErr, nil,
						"Coverage Data : Origin: State %v, City %v, District %v, Street %v;Destination: State %v, City %v, District %v, Street %v;Route Code: %v",
						data.OriginState, data.OriginCity, data.OriginDistrict, data.OriginStreet, data.DestinationState, data.DestinationCity, data.DestinationDistrict, data.DestinationStreet, data.RouteCode)
					results <- retErr
					return
				}
				destCurNode, _ = m.AddrRepo.GetLocationByLocId(ctx, destCurNode.GetParentID())
				destLevelCount++
			}
			originCurNode, _ = m.AddrRepo.GetLocationByLocId(ctx, originCurNode.GetParentID())
			originLevelCount++
		}
		results <- nil
	}
}

// checkZoneAndPostcodeLocCover
// 增加了postcode层级，需要检查zoneCode + postcode下的地址是否有覆盖
func (m *MaskRuleVolumeServiceImpl) checkZoneAndPostcodeLocCover(ctx context.Context, checkMap map[zone]*ImportZoneCode) *srerr.Error {
	jobs := make(chan zone, len(checkMap))
	results := make(chan *srerr.Error, len(checkMap))
	// Start run workers
	for i := 0; i < workerNum; i++ {
		go m.checkZoneAndPostcodeLocCoverWorker(ctx, checkMap, jobs, results)
	}
	// Send jobs to worker
	for job := range checkMap {
		jobs <- job
	}
	close(jobs)
	// Check worker return result(error)
	for i := 0; i < len(checkMap); i++ {
		if err := <-results; err != nil {
			return err
		}
	}

	return nil
}

// 并发执行的worker
func (m *MaskRuleVolumeServiceImpl) checkZoneAndPostcodeLocCoverWorker(ctx context.Context, checkMap map[zone]*ImportZoneCode, jobs <-chan zone, results chan<- *srerr.Error) {
	for job := range jobs {
		curNode, _ := m.AddrRepo.GetLocationByLocId(ctx, int64(job.LocID))
		// 防止LocationTree有问题的情况下for无限循环
		var levelCount int
		for curNode != nil && levelCount < constant.MaxLocationLevel {
			checkKey := zone{job.ZoneCode, int(curNode.GetLocationID()), job.PostCode}
			if data, exist := checkMap[checkKey]; exist && checkKey != job {
				retErr := srerr.New(srerr.ParamErr, nil,
					"Coverage Data : State %v, City %v, District %v, Street %v;Zone Code: %v",
					data.State, data.City, data.District, data.Street, data.ZoneCode)
				results <- retErr
				return
			}
			curNode, _ = m.AddrRepo.GetLocationByLocId(ctx, curNode.GetParentID())
			levelCount++
		}
		results <- nil
	}
}

func (m *MaskRuleVolumeServiceImpl) getComponentProductMap(ctx context.Context, maskProductIDList []int) (map[int]int, *srerr.Error) {
	componentToMaskMap := make(map[int]int)
	for _, maskProductID := range maskProductIDList {
		maskProductDetail, pErr := m.LpsApi.GetProductDetail(ctx, maskProductID)
		if pErr != nil {
			return nil, pErr
		}
		if len(maskProductDetail.GetComponentProduct().ComponentProducts) == 0 {
			return nil, srerr.New(srerr.ParamErr, nil, "Get none Component product by Mask product id %v", maskProductID)
		}
		for _, componentProdID := range maskProductDetail.GetComponentProduct().ComponentProducts {
			componentToMaskMap[componentProdID] = maskProductID
		}
	}

	return componentToMaskMap, nil
}

func formatCheckLocVolDulKey(locCode string, productOrGroup string) string {
	return fmt.Sprintf("%v:%v", locCode, productOrGroup)
}

// parseMaxCap 为空设置MaxCap默认值，否则正常解析str->int
func parseMaxCap(data string, setBlankAsMini bool) (int, error) {
	if data == "" {
		if setBlankAsMini {
			return rulevolume.MinimumVolume, nil
		}
		return rulevolume.DefaultMaskMaxCapacity, nil
	}
	return strconv.Atoi(data)
}

func parseParcelTypeMaxCap(data string, setBlankAsMini bool, maxVol int) (int, error) {
	parcelTypeMaxVol, err := parseMaxCap(data, setBlankAsMini)
	if err != nil {
		return 0, err
	}

	return parcelTypeMaxVol, nil
}

// parseMinVol 为空设置MinVol默认值，否则正常解析str->int
func parseMinVol(data string, setBlankAsMini bool) (int, error) {
	if data == "" {
		if setBlankAsMini {
			return rulevolume.MinimumVolume, nil
		}
		return rulevolume.DefaultMaskMinVolume, nil
	}
	return strconv.Atoi(data)
}

// parseHardCap 为空设置MinVol默认值，否则正常解析str->int
func parseHardCap(data string) (bool, error) {
	if data == "" {
		return rulevolume.DefaultHardCap, nil
	}
	switch data {
	case "Y":
		return true, nil
	case "N":
		return false, nil
	}
	return rulevolume.DefaultHardCap, nil
}

// 这里modetype有route、zone、route&zone三种
func genUpdateVolumeKey(ctx context.Context, ruleID uint64, modeType rulevolume.MaskLocVolumeType) string {
	return fmt.Sprintf("update_volume:%v:%v:%v", ruleID, timeutil.GetCurrentUnixTimeStamp(ctx), modeType)
}

func getImportRouteData(ctx context.Context, defFile *http.Response) ([]*ImportRouteCode, *srerr.Error) {
	defHeaders := []string{"Origin state", "Origin city", "Origin district", "Origin street", "Destination state", "Destination city", "Destination district", "Destination street", "Route Code"}
	newDefHeaders := []string{"Origin state", "Origin city", "Origin district", "Origin street", "Origin postcode", "Destination state", "Destination city", "Destination district", "Destination street", "Destination postcode", "Route Code"}
	rows, err := fileutil.ReadOriginalData(defFile.Body, fileutil.DefaultSheet)
	if err != nil {
		return nil, err
	}
	if len(rows) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "excel headers invalid, must have headers")
	}
	if len(rows[0]) != len(defHeaders) && len(rows[0]) != len(newDefHeaders) {
		return nil, srerr.New(srerr.ParamErr, nil, "excel headers invalid, headers length is invalid, headers length: %v", len(rows[0]))
	}
	var routeCodeList []*ImportRouteCode
	if len(rows[0]) == len(defHeaders) {
		dataList, err1 := fileutil.RowsToData(rows, len(defHeaders))
		if err1 != nil {
			return nil, err1
		}
		for _, data := range dataList {
			importRouteCode := ImportRouteCode{
				OriginState:         data[0],
				OriginCity:          data[1],
				OriginDistrict:      data[2],
				OriginStreet:        data[3],
				DestinationState:    data[4],
				DestinationCity:     data[5],
				DestinationDistrict: data[6],
				DestinationStreet:   data[7],
				RouteCode:           data[8],
			}
			routeCodeList = append(routeCodeList, &importRouteCode)
		}
	} else if len(rows[0]) == len(newDefHeaders) {
		dataList, err1 := fileutil.RowsToData(rows, len(newDefHeaders))
		if err1 != nil {
			return nil, err1
		}
		for _, data := range dataList {
			importRouteCode := ImportRouteCode{
				OriginState:         data[0],
				OriginCity:          data[1],
				OriginDistrict:      data[2],
				OriginStreet:        data[3],
				OriginPostcode:      data[4],
				DestinationState:    data[5],
				DestinationCity:     data[6],
				DestinationDistrict: data[7],
				DestinationStreet:   data[8],
				DestinationPostcode: data[9],
				RouteCode:           data[10],
			}
			routeCodeList = append(routeCodeList, &importRouteCode)
		}
	}
	if len(routeCodeList) < 1 {
		return nil, srerr.New(srerr.ParamErr, nil, "parse file is null")
	}
	return routeCodeList, nil
}

func getImportZoneData(ctx context.Context, defFile *http.Response) ([]*ImportZoneCode, *srerr.Error) {
	defHeaders := []string{"State", "City", "District", "Street", "Zone Code"}
	newDefHeaders := []string{"State", "City", "District", "Street", "Postcode", "Zone Code"}
	rows, err := fileutil.ReadOriginalData(defFile.Body, fileutil.DefaultSheet)
	if err != nil {
		return nil, err
	}
	if len(rows) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "excel headers invalid, must have headers")
	}
	if len(rows[0]) != len(defHeaders) && len(rows[0]) != len(newDefHeaders) {
		return nil, srerr.New(srerr.ParamErr, nil, "excel headers invalid, headers length is invalid, headers length %v", len(rows[0]))
	}
	var importZoneCodeList []*ImportZoneCode
	if len(rows[0]) == len(defHeaders) {
		dataList, err1 := fileutil.RowsToData(rows, len(defHeaders))
		if err1 != nil {
			return nil, err1
		}
		for _, data := range dataList {
			importZoneCode := ImportZoneCode{
				State:    data[0],
				City:     data[1],
				District: data[2],
				Street:   data[3],
				ZoneCode: data[4],
			}
			importZoneCodeList = append(importZoneCodeList, &importZoneCode)
		}
	} else if len(rows[0]) == len(newDefHeaders) {
		dataList, err1 := fileutil.RowsToData(rows, len(newDefHeaders))
		if err1 != nil {
			return nil, err1
		}
		for _, data := range dataList {
			importZoneCode := ImportZoneCode{
				State:    data[0],
				City:     data[1],
				District: data[2],
				Street:   data[3],
				Postcode: data[4],
				ZoneCode: data[5],
			}
			importZoneCodeList = append(importZoneCodeList, &importZoneCode)
		}
	}
	if len(importZoneCodeList) < 1 {
		return nil, srerr.New(srerr.ParamErr, nil, "parse file is null")
	}
	return importZoneCodeList, nil
}

func convertAllocationMethod(allocationMethod int) int {
	if allocationMethod == allocation.Zero {
		return allocation.SingleAllocate
	}

	return allocationMethod
}

func convertCountryLimit(limits []*rulevolume.MaskDefaultVolumeLimitItem) {
	if len(limits) == 0 {
		return
	}
	for i := 0; i < len(limits); i++ {
		if limits[i].MinVolume == -1 {
			limits[i].MinVolume = fillBlank(limits[i].MinVolume, FillBlankZero)
		}
		if limits[i].MaxCapacity == -1 {
			limits[i].MaxCapacity = fillBlank(limits[i].MaxCapacity, FillBlankMax)
		}
		if limits[i].MaxCodCapacity == -1 {
			limits[i].MaxCodCapacity = fillBlank(limits[i].MaxCodCapacity, FillBlankMax)
		}
		if limits[i].MaxBulkyCapacity == -1 {
			limits[i].MaxBulkyCapacity = fillBlank(limits[i].MaxBulkyCapacity, FillBlankMax)
		}
		if limits[i].MaxHighValueCapacity == -1 {
			limits[i].MaxHighValueCapacity = fillBlank(limits[i].MaxHighValueCapacity, FillBlankMax)
		}
		if limits[i].MaxDgCapacity == -1 {
			limits[i].MaxDgCapacity = fillBlank(limits[i].MaxDgCapacity, FillBlankMax)
		}
	}
}

func fillBlank(data, fillBlankType int32) int32 {
	if fillBlankType == FillBlankMax {
		return rulevolume.DefaultMaskMaxCapacity
	}
	if fillBlankType == FillBlankZero {
		return 0
	}

	return data
}

// checkProductAlreadyConfigInGroup 检查ProductID是在已经配置在Group里，是的话报错
func checkProductAlreadyConfigInGroup(rowIndex int, productID int, productInGroupMap map[int]struct{}) *srerr.Error {
	// 如果是Group模式就不需要检查
	if productID == 0 {
		return nil
	}

	// 已经配置在Group中，报错
	if _, exist := productInGroupMap[productID]; exist {
		return srerr.New(srerr.ParamErr, nil, productAlreadyInGroupErrMsg, rowIndex)
	}

	return nil
}

func (m *MaskRuleVolumeServiceImpl) needAudit(ctx context.Context, tab *rulevolume.MaskRuleVolumeTab, updateReq *UpdateRuleVolumeRequest) bool {
	if updateReq.AllocationMethod != allocation.SingleAllocate {
		return false
	}
	if updateReq.RuleStatus != rulevolume.MaskRuleVolumeStatusActive {
		return false
	}
	if !configutil.GetBusinessAuditConf(ctx).NeedAudit {
		return false
	}

	//判断volume是否需要触发审批流
	//1.change of the zone-3PL is bigger than x% and the original value is >= Y, OR (Y is an integer)
	//2.change of sum of zone per 3PL is bigger than z%
	resp, cErr := m.CheckRuleVolume(ctx, m.convertCheckReq(tab.MaskProductID, updateReq), true)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "get summary err:%v", cErr)
		return false
	}
	return m.checkZoneChange(ctx, resp)
}

func (m *MaskRuleVolumeServiceImpl) convertCheckReq(maskProductID int, updateReq *UpdateRuleVolumeRequest) *CheckRuleVolumeRequest {
	result := &CheckRuleVolumeRequest{
		MaskProductID:    maskProductID,
		AllocationMethod: updateReq.AllocationMethod,
		BaseRuleVolumeInfo: BaseRuleVolumeInfo{
			RuleType:                updateReq.RuleType,
			RuleStatus:              updateReq.RuleStatus,
			EffectiveImmediately:    updateReq.EffectiveImmediately,
			EffectiveStartTime:      updateReq.EffectiveStartTime,
			RouteDefinitionFile:     updateReq.RouteDefinitionFile,
			RouteLimitFile:          updateReq.RouteLimitFile,
			ZoneDefinitionFile:      updateReq.ZoneDefinitionFile,
			ZoneLimitFile:           updateReq.ZoneLimitFile,
			RuleMode:                updateReq.RuleMode,
			SetVolumeBlankAsMinimum: updateReq.SetVolumeBlankAsMinimum,
			DefaultVolumeLimit:      updateReq.DefaultVolumeLimit,
		},
	}
	return result
}

// 判断volume是否需要触发审批流
// 1.change of the zone-3PL is bigger than x% and the original value is >= Y, OR (Y is an integer)
// 2.change of sum of zone per 3PL is bigger than z%
func (m *MaskRuleVolumeServiceImpl) checkZoneChange(ctx context.Context, resp *CheckRuleVolumeResp) bool {
	conf := configutil.GetBusinessAuditConf(ctx)
	x := conf.ZoneChangeRatio
	y := conf.ZoneChangeOriginValue
	z := conf.ZoneProductChangeRatio
	productZoneChangeMinMap := make(map[string]float64, 0)
	productZoneChangeMaxMap := make(map[string]float64, 0)
	for _, zoneRouteChange := range resp.ZoneRouteVolumeChangeList {
		// 跳过group code模式
		productIDInt, err := strconv.Atoi(zoneRouteChange.ProductID)
		if productIDInt == 0 || err != nil {
			continue
		}
		if zoneRouteChange.PercentChangeOfMaxVolume > float64(x) && zoneRouteChange.OldMaxVolume >= int64(y) {
			logger.CtxLogInfof(ctx, "change:%+v, need audit", zoneRouteChange)
			return true
		}
		if zoneRouteChange.PercentChangeOfMinVolume > float64(x) && zoneRouteChange.OldMinVolume >= int64(y) {
			logger.CtxLogInfof(ctx, "change:%+v, need audit", zoneRouteChange)
			return true
		}
		productZoneChangeMinMap[zoneRouteChange.ProductID] += zoneRouteChange.PercentChangeOfMinVolume
		productZoneChangeMaxMap[zoneRouteChange.ProductID] += zoneRouteChange.PercentChangeOfMaxVolume
	}
	for product, change := range productZoneChangeMinMap {
		if change > float64(z) {
			logger.CtxLogInfof(ctx, "product:%d, min change over:%d, need audit", product, z)
			return true
		}
	}
	for product, change := range productZoneChangeMaxMap {
		if change > float64(z) {
			logger.CtxLogInfof(ctx, "product:%d, max change over:%d, need audit", product, z)
			return true
		}
	}
	return false
}
