package rulevolume

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
)

type (
	MaskLocType       int
	MaskZoneDirection int
)

const (
	MaskLocTypeRoute MaskLocType = 1
	MaskLocTypeZone  MaskLocType = 2

	MaskZoneDirectionOrigin MaskZoneDirection = 1
	MaskZoneDirectionDest   MaskZoneDirection = 2
)

func (z MaskLocType) String() string {
	switch z {
	case MaskLocTypeRoute:
		return "route"
	case MaskLocTypeZone:
		return "zone"
	}

	return "unknown"
}

func (z MaskZoneDirection) String() string {
	switch z {
	case MaskZoneDirectionOrigin:
		return "origin"
	case MaskZoneDirectionDest:
		return "dest"
	}

	return "unknown"
}

type ProductLocVolumes struct {
	RouteVolumes []*MaskLocVolume
	ZoneVolumes  []*MaskLocVolume
}

type MaskLocVolume struct {
	RuleVolumeId  uint64
	LocCode       string // RouteCode/ZoneCode
	LocType       MaskLocType
	ProductId     int64
	ZoneDirection MaskZoneDirection
	RouteVolume   *MaskRouteVolume
	ZoneVolume    *MaskZoneVolume
	GroupCode     string
}

type MaskRouteVolume struct {
	RuleVolumeId         uint64
	RouteCode            string
	MinVolume            int32
	MaxCapacity          int32
	MaxCodCapacity       int32
	MaxBulkyCapacity     int32
	MaxHighValueCapacity int32
	MaxDgCapacity        int32
	IsHardCap            bool
	IsCodHardCap         bool
	IsBulkyHardCap       bool
	IsHighValueHardCap   bool
	IsDgHardCap          bool
	GroupCode            string
}

type MaskZoneVolume struct {
	RuleVolumeId             uint64
	ZoneCode                 string
	OriginMinVolume          int32
	DestMinVolume            int32
	OriginMaxCapacity        int32
	DestMaxCapacity          int32
	DestMaxCodCapacity       int32
	DestMaxBulkyCapacity     int32
	DestMaxHighValueCapacity int32
	DestMaxDgCapacity        int32
	IsHardCap                bool
	IsCodHardCap             bool
	IsBulkyHardCap           bool
	IsHighValueHardCap       bool
	IsDgHardCap              bool
	GroupCode                string
}

//type maskRoute struct {
//	RouteCode   string
//	OriginLocID int
//	DestLocID   int
//}
//
//type maskZone struct {
//	ZoneCode string
//	LocID    int
//}

type UpdateMaskRouteVolumeMsg struct {
	RuleVolume   *rulevolume.MaskRuleVolumeTab
	RouteVolumes []*rulevolume.MaskRouteVolumeTab
}

type UpdateMaskZoneVolumeMsg struct {
	RuleVolume  *rulevolume.MaskRuleVolumeTab
	ZoneVolumes []*rulevolume.MaskZoneVolumeTab
}

type UpdateMaskRouteAndZoneVolumeMsg struct {
	RuleVolume   *rulevolume.MaskRuleVolumeTab
	RouteVolumes []*rulevolume.MaskRouteVolumeTab
	ZoneVolumes  []*rulevolume.MaskZoneVolumeTab
}

type route struct {
	RouteCode      string
	OriginLocID    int
	DestLocID      int
	OriginPostcode string
	DestPostcode   string
}

type zone struct {
	ZoneCode string
	LocID    int
	PostCode string
}

type ImportRouteCode struct {
	OriginState         string
	OriginCity          string
	OriginDistrict      string
	OriginStreet        string
	OriginPostcode      string
	DestinationState    string
	DestinationCity     string
	DestinationDistrict string
	DestinationStreet   string
	DestinationPostcode string
	RouteCode           string
}

type ImportZoneCode struct {
	State    string
	City     string
	District string
	Street   string
	Postcode string
	ZoneCode string
}

const (
	removeDuplicateRoutePrefix = "RouteCode"
	removeDuplicateZonePrefix  = "ZoneCode"
)

// RemoveDuplicationLocVols 增加postcode之后，运力匹配可能匹配到多条zoneCode/routeCode相同的数据，因此需要对zoneCode/routeCode去重
// 在更新运力时只会用到(zoneCode + ZoneDirection)和routeCode和locType，最大最小运力配置不会使用，因此只需要保留一条就行
func RemoveDuplicationLocVols(locVols map[int64][]*MaskLocVolume) map[int64][]*MaskLocVolume {
	newLocVols := make(map[int64][]*MaskLocVolume)
	distinctMap := make(map[string]bool)
	for productId, locVolList := range locVols {
		//1. productId不在新运力配置中，则初始化productId的运力
		if _, ok := newLocVols[productId]; !ok {
			var tempLocVol []*MaskLocVolume
			newLocVols[productId] = tempLocVol
		}
		//2. 对productId匹配到的运力配置进行遍历
		for _, locVol := range locVolList {
			// 以productId + zoneCode + zoneDirection/productId + routeCode为key进行去重判断
			var key string
			switch locVol.LocType {
			case MaskLocTypeRoute:
				key = fmt.Sprintf("%v:%v-%v", removeDuplicateRoutePrefix, productId, locVol.LocCode)
			case MaskLocTypeZone:
				key = fmt.Sprintf("%v:%v-%v-%v", removeDuplicateZonePrefix, productId, locVol.LocCode, locVol.ZoneDirection)
			}
			// key存在则表示运力code重复
			if _, ok := distinctMap[key]; ok {
				continue
			}
			distinctMap[key] = true
			newLocVols[productId] = append(newLocVols[productId], locVol)
		}
	}
	return newLocVols
}

type VolumeLocInfo struct {
	LocationId int64
	Postcode   string
}

// GetComposeAddress 获取地址+postcode的组合，包括postcode为空的情况
func GetComposeAddress(originLocIds []int64, destLocIds []int64, originPostcode string, destPostcode string) ([]VolumeLocInfo, []VolumeLocInfo) {
	var originAddressList, destAddressList []VolumeLocInfo
	// 构建发货地的地址组合(locId + postcode)
	originPostcodeList := []string{originPostcode}
	// 需要加上postcode不存在的情况
	if originPostcode != "" {
		originPostcodeList = append(originPostcodeList, "")
	}
	for _, originLocId := range originLocIds {
		for _, postcode := range originPostcodeList {
			originAddressList = append(originAddressList, VolumeLocInfo{
				LocationId: originLocId,
				Postcode:   postcode,
			})
		}
	}
	// 构建收货地的地址组合（locId + postcode）
	destPostcodeList := []string{destPostcode}
	// 需要加上postcode不存在的情况
	if destPostcode != "" {
		destPostcodeList = append(destPostcodeList, "")
	}
	for _, destLocId := range destLocIds {
		for _, postcode := range destPostcodeList {
			destAddressList = append(destAddressList, VolumeLocInfo{
				LocationId: destLocId,
				Postcode:   postcode,
			})
		}
	}
	return originAddressList, destAddressList
}

var ComparatorMap = map[string]Comparator{}

func NewComparatorMap(lpsApi lpsclient.LpsApi) {
	ComparatorMap = make(map[string]Comparator, 0)
	countryComparator := &CountryComparator{LpsApi: lpsApi}
	zoneRouteComparator := &ZoneRouteComparator{LpsApi: lpsApi}

	ComparatorMap[rulevolume.LocVolumeTypeRoute.String()] = zoneRouteComparator
	ComparatorMap[rulevolume.LocVolumeTypeZone.String()] = zoneRouteComparator
	ComparatorMap[rulevolume.LocVolumeTypeRouteAndZone.String()] = zoneRouteComparator
	ComparatorMap[rulevolume.LocVolumeTypeCountry.String()] = countryComparator
}

var CheckVolumeParserMap = map[string]CheckVolumeParser{}

func NewCheckVolumeParserMap(MaskRuleVolumeService MaskRuleVolumeService) {
	countryParser := &CountryVolumeParserImpl{}
	zoneParser := &ZoneVolumeParserImpl{MaskRuleVolumeService: MaskRuleVolumeService}
	routeParser := &RouteVolumeParserImpl{MaskRuleVolumeService: MaskRuleVolumeService}
	zoneRouteParser := &ZoneRouteVolumeParserImpl{MaskRuleVolumeService: MaskRuleVolumeService}

	CheckVolumeParserMap = make(map[string]CheckVolumeParser, 0)
	CheckVolumeParserMap[rulevolume.LocVolumeTypeRoute.String()] = routeParser
	CheckVolumeParserMap[rulevolume.LocVolumeTypeZone.String()] = zoneParser
	CheckVolumeParserMap[rulevolume.LocVolumeTypeCountry.String()] = countryParser
	CheckVolumeParserMap[rulevolume.LocVolumeTypeRouteAndZone.String()] = zoneRouteParser
}
