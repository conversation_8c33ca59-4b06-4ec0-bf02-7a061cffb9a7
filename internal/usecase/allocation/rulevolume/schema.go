package rulevolume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type GetRuleVolumeByIDRequest struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type GetRuleVolumeListRequest struct {
	ID               uint64                           `form:"id" json:"id"`
	RuleStatus       *rulevolume.MaskRuleVolumeStatus `form:"rule_status" json:"rule_status"`
	MaskProductID    int                              `form:"mask_product_id" json:"mask_product_id"`
	RuleType         rulevolume.MaskLocVolumeType     `form:"rule_type" json:"rule_type"`
	PageNo           uint32                           `form:"pageno" json:"pageno" default:"1"`
	Limit            uint32                           `form:"limit" json:"limit" default:"20"`
	RuleMode         int32                            `form:"rule_mode" json:"rule_mode" default:"0"`     //请求schema 修改后 ，枚举值 【0/1/2】 分别为   all / mpl /wms
	AllocationMethod int64                            `form:"allocation_method" json:"allocation_method"` //1:single; 2:batch
}

type GetRuleVolumeListResponse struct {
	List   []*rulevolume.MaskRuleVolumeTab `json:"list"`
	Pageno uint32                          `json:"pageno"`
	Count  uint32                          `json:"count"`
	Total  int                             `json:"total"`
}

type CreateRuleVolumeRequest struct {
	MaskProductID    int   `json:"mask_product_id"`
	AllocationMethod int64 `json:"allocation_method"` //1:single; 2:batch
	BaseRuleVolumeInfo
	ForecastTaskId int64  `json:"forecast_task_id"` // 预测任务id
	OperateBy      string `json:"operate_by"`
}

func (c *CreateRuleVolumeRequest) Validate(ctx context.Context) *srerr.Error {
	return c.checkBaseRuleVolume(ctx)
}

func (b BaseRuleVolumeInfo) checkBaseRuleVolume(ctx context.Context) *srerr.Error {
	if !b.RuleType.IsValidMaskLocVolumeType() {
		return srerr.New(srerr.ParamErr, nil, "rule type must in (route/zone/country/route&zone)")
	}
	if !b.EffectiveImmediately && b.EffectiveStartTime < uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		return srerr.New(srerr.ParamErr, nil, "effective_start_time must be greater than the current time. ")
	}
	if b.RuleStatus == rulevolume.MaskRuleVolumeStatusActive {
		if b.RuleType == rulevolume.LocVolumeTypeRoute {
			if b.RouteDefinitionFile == "" || b.RouteLimitFile == "" {
				return srerr.New(srerr.ParamErr, nil, "route define/limit file should not be empty when active status")
			}
		} else if b.RuleType == rulevolume.LocVolumeTypeZone {
			if b.ZoneDefinitionFile == "" || b.ZoneLimitFile == "" {
				return srerr.New(srerr.ParamErr, nil, "route define/limit file should not be empty when active status")
			}
		} else if b.RuleType == rulevolume.LocVolumeTypeRouteAndZone {
			if b.ZoneLimitFile == "" && b.ZoneDefinitionFile == "" && b.RouteDefinitionFile == "" && b.RouteLimitFile == "" {
				return srerr.New(srerr.ParamErr, nil, "route&zone route or zone define/limit file should not be empty when active status")
			}
		}
	}
	return nil
}

type BaseRuleVolumeInfo struct {
	RuleType                rulevolume.MaskLocVolumeType             `form:"rule_type" json:"rule_type"`
	RuleStatus              rulevolume.MaskRuleVolumeStatus          `form:"rule_status" json:"rule_status" binding:"required"`
	EffectiveImmediately    bool                                     `form:"effective_immediately" json:"effective_immediately"`
	EffectiveStartTime      uint32                                   `form:"effective_start_time" json:"effective_start_time"`
	DefaultVolumeLimit      []*rulevolume.MaskDefaultVolumeLimitItem `form:"default_volume_limit" json:"default_volume_limit" binding:"required"`
	RouteDefinitionFile     string                                   `json:"route_definition_file"`
	RouteLimitFile          string                                   `json:"route_limit_file"`
	ZoneDefinitionFile      string                                   `json:"zone_definition_file"`
	ZoneLimitFile           string                                   `json:"zone_limit_file"`
	RuleMode                int32                                    `json:"rule_mode"`
	SetVolumeBlankAsMinimum bool                                     `json:"set_volume_blank_as_minimum"`
	MaskCombinationMode     bool                                     `json:"mask_combination_mode"`
	ShareVolume             bool                                     `json:"share_volume"`
	GroupInfo               rulevolume.MaskRuleGroupInfo             `json:"group_info"`
}

type UpdateRuleVolumeRequest struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
	BaseRuleVolumeInfo
	AllocationMethod int64 `json:"allocation_method"` //1:single; 2:batch
}

func (u *UpdateRuleVolumeRequest) Validate(ctx context.Context) *srerr.Error {
	return u.checkBaseRuleVolume(ctx)
}

type DeleteRuleVolumeRequest struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type CopyRuleVolumeRequest struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type CheckRuleVolumeRequest struct {
	ID               uint64 `json:"id"`
	MaskProductID    int    `json:"mask_product_id"`
	AllocationMethod int64  `json:"allocation_method"` //1:single; 2:batch
	BaseRuleVolumeInfo
	ForecastTaskId int64  `json:"forecast_task_id"` // 预测任务id
	OperateBy      string `json:"operate_by"`
}

type CheckRuleVolumeResp struct {
	ZoneRouteVolumeChangeList []ZoneRouteVolumeChange `json:"zone_route_volume_change_list"`
	ZoneRouteHardCapList      []ZoneRouteHardCap      `json:"zone_route_hard_cap_list"`
	ListOfZoneRoute           []ZoneRouteInfo         `json:"list_of_zone_route"`
	CountryVolumeChangeList   []CountryVolumeChange   `json:"country_volume_change_list"`
	CountryHardCapList        []CountryHardCap        `json:"country_hard_cap_list"`
	ModeChangeList            []ModeChange            `json:"mode_change_list"`
	BasicInfo                 BasicInfo               `json:"basic_info"`
}

type (
	ZoneRouteVolumeChange struct {
		ZoneRouteCode             string  `json:"zone_route_code"`
		ProductID                 string  `json:"product_id"` // value可能为：group code; product id
		ProductName               string  `json:"product_name"`
		OldMinVolume              int64   `json:"old_min_volume"`
		NewMinVolume              int64   `json:"new_min_volume"`
		AbsoluteChangeOfMinVolume int64   `json:"absolute_change_of_min_volume"`
		PercentChangeOfMinVolume  float64 `json:"percent_change_of_min_volume"`
		OldMaxVolume              int64   `json:"old_max_volume"`
		NewMaxVolume              int64   `json:"new_max_volume"`
		AbsoluteChangeOfMaxVolume int64   `json:"absolute_change_of_max_volume"`
		PercentChangeOfMaxVolume  float64 `json:"percent_change_of_max_volume"`
	}

	ZoneRouteHardCap struct {
		ZoneRouteCode string `json:"zone_route_code"`
		ProductID     string `json:"product_id"` // value可能为：group code; product id
		ProductName   string `json:"product_name"`
		OldHardCap    string `json:"old_hard_cap"`
		NewHardCap    string `json:"new_hard_cap"`
	}

	ZoneRouteInfo struct {
		ZoneRouteCode string `json:"zone_route_code"`
		ProductID     string `json:"product_id"` // value可能为：group code; product id
		ProductName   string `json:"product_name"`
		// existed in old, not in new -> means remove
		// existed in new, not in old -> means add
		ExistInOld string `json:"exist_in_old"`
		ExistInNew string `json:"exist_in_new"`
		MinVolume  int64  `json:"min_volume"`
		MaxVolume  int64  `json:"max_volume"`
	}

	CountryVolumeChange struct {
		ProductID                 string `json:"product_id"`
		ProductName               string `json:"product_name"`
		OldMinVolume              int64  `json:"old_min_volume"`
		OldMaxVolume              int64  `json:"old_max_volume"`
		NewMinVolume              int64  `json:"new_min_volume"`
		NewMaxVolume              int64  `json:"new_max_volume"`
		AbsoluteChangeOfMinVolume int64  `json:"absolute_change_of_min_volume"` //负数表示下降，整数表示增长，由fe渲染正负
		PercentChangeOfMinVolume  string `json:"percent_change_of_min_volume"`
		AbsoluteChangeOfMaxVolume int64  `json:"absolute_change_of_max_volume"` //负数表示下降，整数表示增长，由fe渲染正负
		PercentChangeOfMaxVolume  string `json:"percent_change_of_max_volume"`
	}

	CountryHardCap struct {
		ProductID   string `json:"product_id"`
		ProductName string `json:"product_name"`
		OldHardCap  string `json:"old_hard_cap"`
		NewHardCap  string `json:"new_hard_cap"`
	}

	// ModeChange 模式发生改变，mpl <-> wms
	ModeChange struct {
		VolumeRuleMode string `json:"volume_rule_mode"`
		Old            string `json:"old"`
		New            string `json:"new"`
	}

	// BasicInfo 记录基础信息
	BasicInfo struct {
		NewMaskingProductID   string   `json:"new_masking_product_id"`
		OldMaskingProductID   string   `json:"old_masking_product_id"`
		NewWhetherVolumeShare string   `json:"new_whether_volume_share"`
		OldWhetherVolumeShare string   `json:"old_whether_volume_share"`
		NewShareGroupChannel  string   `json:"new_share_group_channel"`
		OldShareGroupChannel  string   `json:"old_share_group_channel"`
		NewGroupCode          string   `json:"new_group_code"`
		OldGroupCode          string   `json:"old_group_code"`
		NewRuleID             string   `json:"new_rule_id"`
		OldRuleIDList         []string `json:"old_rule_id_list"`
	}
)

type ExportCheckVolumeResp struct {
	Url string `json:"url"`
}
