package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"sort"
	"strconv"
)

const (
	zoneRouteVolumeChangeName = "Zone/Route-volume change"
	zoneRouteHardCapName      = "Zone/Route-hardcap"
	listOfZoneRouteName       = "List of Zone/Route"
	countryVolumeChangeName   = "Country-volume change"
	countryHardCapName        = "Country-hardcap"
	modeChangeName            = "Mode change"
	basicInfoName             = "Basic info"
)

var (
	zoneRouteVolumeChangeHeader = []string{"Zone/Route code", "3PL", "Old min volume", "New min volume", "absolute change of min volume", "% change of min volume", "Old max volume", "New max volume", "abs change of max volume", "% change of max volume"}
	zoneRouteHardCapHeader      = []string{"Zone/Route code", "3PL", "Old hard cap", "New hard cap"}
	listOfZoneRouteHeader       = []string{"Zone/Route code", "3PL", "Exist in old", "Exist in new"}
	countryVolumeChangeHeader   = []string{"3PL", "Old min volume", "New min volume", "absolute change of min volume", "% change of min volume", "Old max volume", "New max volume", "abs change of max volume", "% change of max volume"}
	countryHardCapHeader        = []string{"3PL", "Old hard cap", "New hard cap"}
	modeChangeHeader            = []string{"Volume rule mode", "Old", "New"}
	basicInfoHeader             = []string{"New masking product id", "Old masking product id", "New whether volume share", "Old whether volume share", "New shop group channel", "Old  shop group channel", "New group code", "Old group code", "Old rule id list"}
)

func (m *MaskRuleVolumeServiceImpl) CheckRuleVolume(ctx context.Context, req *CheckRuleVolumeRequest, isExport bool) (*CheckRuleVolumeResp, *srerr.Error) {
	//1. 获取对应rule volume
	// 不同的mask product获取其对应的active rule
	existedVolumeMap, gErr := m.CheckVolumeFinder.GetCurrentVolume(ctx, req)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get existed volume err:%v", gErr)
		return nil, gErr
	}
	//2. 获取当前active volume配置，解析req中的volume配置
	checkVolumeParser := CheckVolumeParserMap[req.RuleType.String()]
	reqVolumeMap, pErr := checkVolumeParser.ParseReqVolume(ctx, req)
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "parse req volume err:%v", pErr)
		return nil, pErr
	}
	//3. 进行比较，装填response。装填时根据is_export判断是否需要截断top 10
	comparator := ComparatorMap[req.RuleType.String()]
	resp, cErr := comparator.Compare(ctx, existedVolumeMap, reqVolumeMap)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "compare volumes err:%v", cErr)
		return nil, cErr
	}

	// 排序
	m.sortResp(resp)

	if !isExport {
		// 非导出接口，需要限制长度
		resp = m.limitResp(ctx, resp)
	}

	return resp, nil
}

func (m *MaskRuleVolumeServiceImpl) sortResp(resp *CheckRuleVolumeResp) {
	// prepare check data
	zoneRouteHardCapChange := make(map[string]bool, 0)
	countryHardCapChange := make(map[string]bool, 0)
	for _, zrHard := range resp.ZoneRouteHardCapList {
		if zrHard.OldHardCap != zrHard.NewHardCap {
			key := fmt.Sprintf("%s:%s", zrHard.ZoneRouteCode, zrHard.ProductID)
			zoneRouteHardCapChange[key] = true
		}
	}
	for _, countryHard := range resp.CountryHardCapList {
		if countryHard.OldHardCap != countryHard.NewHardCap {
			countryHardCapChange[countryHard.ProductID] = true
		}
	}
	// sort
	if len(resp.ZoneRouteVolumeChangeList) != 0 {
		sort.Slice(resp.ZoneRouteVolumeChangeList, func(i, j int) bool {
			a := objutil.MaxForInt64(resp.ZoneRouteVolumeChangeList[i].AbsoluteChangeOfMinVolume, resp.ZoneRouteVolumeChangeList[i].AbsoluteChangeOfMaxVolume)
			b := objutil.MaxForInt64(resp.ZoneRouteVolumeChangeList[j].AbsoluteChangeOfMinVolume, resp.ZoneRouteVolumeChangeList[j].AbsoluteChangeOfMaxVolume)
			if a >= b { //firstly sorted by absolute change
				return true
			}
			// secondly sorted hard cap change
			keyA := fmt.Sprintf("%s:%s", resp.ZoneRouteVolumeChangeList[i].ZoneRouteCode, resp.ZoneRouteVolumeChangeList[i].ProductID)
			keyB := fmt.Sprintf("%s:%s", resp.ZoneRouteVolumeChangeList[j].ZoneRouteCode, resp.ZoneRouteVolumeChangeList[j].ProductID)
			if zoneRouteHardCapChange[keyA] && !zoneRouteHardCapChange[keyB] {
				return true
			}

			return false
		})
	}
	if len(resp.CountryVolumeChangeList) != 0 {
		sort.Slice(resp.CountryVolumeChangeList, func(i, j int) bool {
			a := objutil.MaxForInt64(resp.CountryVolumeChangeList[i].AbsoluteChangeOfMaxVolume, resp.CountryVolumeChangeList[i].AbsoluteChangeOfMinVolume)
			b := objutil.MaxForInt64(resp.CountryVolumeChangeList[j].AbsoluteChangeOfMaxVolume, resp.CountryVolumeChangeList[j].AbsoluteChangeOfMinVolume)
			if a >= b {
				return true
			}
			if countryHardCapChange[resp.CountryVolumeChangeList[i].ProductID] && !countryHardCapChange[resp.CountryVolumeChangeList[j].ProductID] {
				return true
			}

			return false
		})
	}
}

func (m *MaskRuleVolumeServiceImpl) limitResp(ctx context.Context, resp *CheckRuleVolumeResp) *CheckRuleVolumeResp {
	if resp == nil {
		return resp
	}
	newResp := &CheckRuleVolumeResp{
		ModeChangeList:          resp.ModeChangeList,
		CountryHardCapList:      resp.CountryHardCapList,
		CountryVolumeChangeList: resp.CountryVolumeChangeList,
		BasicInfo:               resp.BasicInfo,
	}

	//限制最大长度，根据Apollo配置
	conf := configutil.GetMaskVolumeCheckConf(ctx)
	// zone route hard cap
	if len(resp.ZoneRouteHardCapList) != 0 {
		if len(resp.ZoneRouteHardCapList) <= conf.ListLimit {
			newResp.ZoneRouteHardCapList = resp.ZoneRouteHardCapList
		} else {
			newResp.ZoneRouteHardCapList = make([]ZoneRouteHardCap, conf.ListLimit)
			for i := 0; i < conf.ListLimit; i++ {
				newResp.ZoneRouteHardCapList[i] = resp.ZoneRouteHardCapList[i]
			}
		}
	}
	// zone route volume change
	if len(resp.ZoneRouteVolumeChangeList) != 0 {
		if len(resp.ZoneRouteVolumeChangeList) <= conf.ListLimit {
			newResp.ZoneRouteVolumeChangeList = resp.ZoneRouteVolumeChangeList
		} else {
			newResp.ZoneRouteVolumeChangeList = make([]ZoneRouteVolumeChange, conf.ListLimit)
			for i := 0; i < conf.ListLimit; i++ {
				newResp.ZoneRouteVolumeChangeList[i] = resp.ZoneRouteVolumeChangeList[i]
			}
		}
	}
	// list of zone route code
	if len(resp.ListOfZoneRoute) != 0 {
		if len(resp.ListOfZoneRoute) <= conf.ListLimit {
			newResp.ListOfZoneRoute = resp.ListOfZoneRoute
		} else {
			newResp.ListOfZoneRoute = make([]ZoneRouteInfo, conf.ListLimit)
			for i := 0; i < conf.ListLimit; i++ {
				newResp.ListOfZoneRoute[i] = resp.ListOfZoneRoute[i]
			}
		}
	}

	return newResp
}

func (m *MaskRuleVolumeServiceImpl) ExportCheckRuleVolume(ctx context.Context, req *CheckRuleVolumeRequest, isExport bool) (*ExportCheckVolumeResp, *srerr.Error) {
	var result = &ExportCheckVolumeResp{}

	resp, cErr := m.CheckRuleVolume(ctx, req, isExport)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "get change list err:%v", cErr)
		return nil, cErr
	}
	var (
		zoneRouteVolumeChangeUnits [][]string
		zoneRouteHardCapUnits      [][]string
		listOfZoneRouteUnits       [][]string
		countryVolumeChangeUnits   [][]string
		countryHardCapUnits        [][]string
		modeChangeUnits            [][]string
		basicInfoUnits             [][]string
		file                       = excelize.NewFile()
		err                        error
	)
	// sheet for Zone/Route-volume change
	if len(resp.ZoneRouteVolumeChangeList) != 0 {
		for _, change := range resp.ZoneRouteVolumeChangeList {
			zoneRouteVolumeChangeUnits = append(zoneRouteVolumeChangeUnits, []string{
				change.ZoneRouteCode,
				change.ProductID,
				strconv.FormatInt(change.OldMinVolume, 10),
				strconv.FormatInt(change.NewMinVolume, 10),
				strconv.FormatInt(change.AbsoluteChangeOfMinVolume, 10),
				strconv.FormatFloat(change.PercentChangeOfMinVolume, 'f', -1, 64),
				strconv.FormatInt(change.OldMaxVolume, 10),
				strconv.FormatInt(change.NewMaxVolume, 10),
				strconv.FormatInt(change.AbsoluteChangeOfMaxVolume, 10),
				strconv.FormatFloat(change.PercentChangeOfMaxVolume, 'f', -1, 64),
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, zoneRouteVolumeChangeHeader, zoneRouteVolumeChangeUnits, zoneRouteVolumeChangeName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make zone route volume change err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// sheet for zone route hard cap
	if len(resp.ZoneRouteHardCapList) != 0 {
		for _, change := range resp.ZoneRouteHardCapList {
			zoneRouteHardCapUnits = append(zoneRouteHardCapUnits, []string{
				change.ZoneRouteCode,
				change.ProductID,
				change.OldHardCap,
				change.NewHardCap,
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, zoneRouteHardCapHeader, zoneRouteHardCapUnits, zoneRouteHardCapName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make zone route hard cap err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// sheet for list of zone route
	if len(resp.ListOfZoneRoute) != 0 {
		for _, change := range resp.ListOfZoneRoute {
			listOfZoneRouteUnits = append(listOfZoneRouteUnits, []string{
				change.ZoneRouteCode,
				change.ProductID,
				change.ExistInOld,
				change.ExistInNew,
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, listOfZoneRouteHeader, listOfZoneRouteUnits, listOfZoneRouteName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make list of zone route err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// sheet for country volume change
	if len(resp.CountryVolumeChangeList) != 0 {
		for _, change := range resp.CountryVolumeChangeList {
			countryVolumeChangeUnits = append(countryVolumeChangeUnits, []string{
				change.ProductID,
				strconv.FormatInt(change.OldMinVolume, 10),
				strconv.FormatInt(change.NewMinVolume, 10),
				strconv.FormatInt(change.AbsoluteChangeOfMinVolume, 10),
				change.PercentChangeOfMinVolume,

				strconv.FormatInt(change.OldMaxVolume, 10),
				strconv.FormatInt(change.NewMaxVolume, 10),
				strconv.FormatInt(change.AbsoluteChangeOfMaxVolume, 10),
				change.PercentChangeOfMaxVolume,
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, countryVolumeChangeHeader, countryVolumeChangeUnits, countryVolumeChangeName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make country volume change err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// sheet for country hard cap
	if len(resp.CountryHardCapList) != 0 {
		for _, change := range resp.CountryHardCapList {
			countryHardCapUnits = append(countryHardCapUnits, []string{
				change.ProductID,
				change.OldHardCap,
				change.NewHardCap,
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, countryHardCapHeader, countryHardCapUnits, countryHardCapName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make country hard cap err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// sheet for mode change
	if len(resp.ModeChangeList) != 0 {
		for _, change := range resp.ModeChangeList {
			modeChangeUnits = append(modeChangeUnits, []string{
				change.VolumeRuleMode,
				change.Old,
				change.New,
			})
		}
		file, err = fileutil.AppendExcel(ctx, file, modeChangeHeader, modeChangeUnits, modeChangeName, false)
		if err != nil {
			logger.CtxLogErrorf(ctx, "make mode change err:%v", err)
			return nil, srerr.With(srerr.TypeConvertErr, nil, err)
		}
	}
	// basic info
	basicInfoUnits = append(basicInfoUnits, []string{
		resp.BasicInfo.NewMaskingProductID,
		resp.BasicInfo.OldWhetherVolumeShare,
		resp.BasicInfo.NewWhetherVolumeShare,
		resp.BasicInfo.OldWhetherVolumeShare,
		resp.BasicInfo.NewShareGroupChannel,
		resp.BasicInfo.OldShareGroupChannel,
		resp.BasicInfo.NewGroupCode,
		resp.BasicInfo.OldGroupCode,
		objutil.JsonString(resp.BasicInfo.OldRuleIDList),
	})
	file, err = fileutil.AppendExcel(ctx, file, basicInfoHeader, basicInfoUnits, basicInfoName, true)
	if err != nil {
		logger.CtxLogErrorf(ctx, "make mode change err:%v", err)
		return nil, srerr.With(srerr.TypeConvertErr, nil, err)
	}

	if file != nil {
		file.DeleteSheet("Sheet1")
		excelBuffer, wErr := file.WriteToBuffer()
		if wErr != nil {
			return nil, srerr.With(srerr.ZoneExportFail, nil, wErr)
		}
		exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
		s3Key := fmt.Sprintf("Masking volume check:%v.xlsx", exportTime)
		bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
		if err := fileutil.Upload(ctx, bucket, s3Key, excelBuffer); err != nil {
			return nil, srerr.With(srerr.ZoneExportFail, "failed to upload s3", err)
		}
		url := fileutil.GetS3Url(ctx, bucket, s3Key)
		result.Url = url
	}

	return result, nil
}
