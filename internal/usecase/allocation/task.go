package allocation

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	rv "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	UniqPattern = "uniq_%v"
)

type Task struct {
	service                   *SoftRuleService
	MaskingProductID          int64
	Rule                      *rule.MaskRule
	LocationVolumes           map[int64]rulevolume.ProductLocVolumes
	DefaultRuleType           entity.DefaultRuleType
	productPriorities         map[int64]int32
	productWeightages         map[int64]int32
	VolumeCounter             volumecounter.MaskVolumeCounter
	productVolumeCache        map[string]int32
	productVolumeInBatchCache map[int64]int32
	ruleMode                  rule_mode.RuleMode //ruleMode 1/2 表示 mpl / wms,这里控制以下点： 【运力counter 读取 】
	reqType                   int                // 对应 【allocate / estimate】
	SortedRes                 []*pb.SortedResult
	PreCalcFee                *chargeentity.BatchAllocationESFResp // 保存提前计算的运费结果
	productGroupCodeMapping   map[int64]string
	businessType              int8
	attrMap                   map[int64]*parcel_type_definition.ParcelTypeAttr
	shopWhitelistService      whitelist2.ShopWhitelistService
}

func newTask(
	maskingProductID int64, rule *rule.MaskRule, locationVolumes map[int64]rulevolume.ProductLocVolumes,
	volumeCounter volumecounter.MaskVolumeCounter, defaultRuleType entity.DefaultRuleType, productPriorities,
	productWeightages map[int64]int32, m *SoftRuleService, rm rule_mode.RuleMode, reqType int,
	preCalcFee *chargeentity.BatchAllocationESFResp, productGroupCodeMapping map[int64]string, businessType int8,
	attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, shopWhitelistService whitelist2.ShopWhitelistService,
) *Task {
	return &Task{
		service:                   m,
		MaskingProductID:          maskingProductID,
		Rule:                      rule,
		LocationVolumes:           locationVolumes,
		DefaultRuleType:           defaultRuleType,
		productPriorities:         productPriorities,
		productWeightages:         productWeightages,
		VolumeCounter:             volumeCounter,
		productVolumeCache:        map[string]int32{},
		productVolumeInBatchCache: map[int64]int32{},
		ruleMode:                  rm,
		reqType:                   reqType,
		PreCalcFee:                preCalcFee,
		productGroupCodeMapping:   productGroupCodeMapping,
		businessType:              businessType,
		attrMap:                   attrMap,
		shopWhitelistService:      shopWhitelistService,
	}
}

func (t *Task) GetProductVolume(ctx context.Context, maskProductID, id int64, groupCode string, parcelType parcel_type_definition.ParcelType) int32 {
	if groupCode != "" {
		return t.GetGroupVolume(ctx, groupCode, parcelType)
	}

	volumeCacheKey := fmt.Sprintf("%d:%s", id, parcelType)
	v, ok := t.productVolumeCache[volumeCacheKey]
	if ok {
		return v
	}
	v, err := t.VolumeCounter.GetProductVolume(ctx, maskProductID, id, t.ruleMode, parcelType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetProductVolume of %v from counter: %v", id, err)
		return 0
	}
	t.productVolumeCache[volumeCacheKey] = v
	return v
}

func (t *Task) GetGroupVolume(ctx context.Context, groupCode string, parcelType parcel_type_definition.ParcelType) int32 {
	v, err := t.VolumeCounter.GetGroupVolume(ctx, groupCode, t.ruleMode, parcelType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetGroupVolume of %v from counter: %v", groupCode, err)
		return 0
	}

	return v
}

func (t *Task) GetProductVolumeInBatch(ctx context.Context, id int64, maskProductID int64) int32 {
	v, ok := t.productVolumeInBatchCache[id]
	if ok {
		return v
	}
	v, err := t.VolumeCounter.GetProductVolumeInBatch(ctx, t.Rule.Id, id, maskProductID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetProductVolumeInBatch of %v in rule %v from counter: %v", id, t.Rule.Id, err)
		return 0
	}
	t.productVolumeInBatchCache[id] = v
	return v
}

func (t *Task) GetResult(ctx context.Context, productIDs []int64, orderInfo *pb.MaskingOrderInfo, maskProductID int64, shippingFeeConfigs []*allocation.ShippingFeeConfig) (int64, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Start routing, maskingProductID=%v|productIDs=%+v|rule=%+v", t.MaskingProductID, productIDs, t.Rule)

	// 添加allocation path log细节
	allocationLog := GetAllocationLogFromContext(ctx)
	allocationLog.SetSoftInput(productIDs)
	allocationLog.SetRuleId(t.Rule.Id)

	if len(productIDs) == 0 {
		//return 0, ErrNoAvailableChannel
		return 0, srerr.New(srerr.NoAvailableProduct, productIDs, "")
	}
	if len(productIDs) == 1 {
		return productIDs[0], nil
	}
	var productNumofFactorPassed = initSortedRes(productIDs)
	defer func() {
		if t.reqType == EstimateMaskingChannelRequest {
			t.SortedRes = mapToSortedRes(productNumofFactorPassed)
		}
	}()
	// 上报分单规则占比
	reportMaskingRuleFactor(maskProductID, t.Rule, t.DefaultRuleType)
	allocationLogs := GetAllocationLogFromContext(ctx)
	for i, step := range t.Rule.MaskRuleSteps {
		allocationLogs.AppendSoftCriteriaList(ctx, i+1, step.MaskStepType.String())
		switch step.MaskStepType {
		case rule.MaskStepMinVolumeCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MinVolumeData.MinVolumes,
				step.MinVolumeData.GroupMinVolumes, nil, nil, volumeFilterTypeMinVolume,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeNone)
		case rule.MaskStepMaxCapacityCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MaxCapacityData.MaxCapacities,
				step.MaxCapacityData.GroupMaxCapacities, step.IsHardCapacityData.IsHardCaps,
				step.IsHardCapacityData.GroupIsHardCaps, volumeFilterTypeMaxCapacity, t.productGroupCodeMapping,
				t.attrMap, parcel_type_definition.ParcelTypeNone)
		case rule.MaskStepMinVolumeZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMinVolume,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeNone)
		case rule.MaskStepMaxCapacityZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMaxCapacity,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeNone)
		case rule.MaskStepMaxBatchVolume:
			productIDs = t.filterByVolumeInBatch(ctx, productIDs, step.BatchVolumeData.BatchVolume, step.BatchVolumeData.VolumeInOneBatch, maskProductID)
		case rule.MaskStepCheapestFee:
			productIDs = t.filterByCheapestFee(ctx, productIDs, orderInfo, shippingFeeConfigs)
		case rule.MaskStepPickupEfficiencyWhitelist:
			productIDs = t.filterByPickupEfficiencyWhitelist(ctx, maskProductID, productIDs, orderInfo)
		case rule.MaskStepMaxCodCapacityCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MaxCodCapacityData.MaxCapacities,
				step.MaxCodCapacityData.GroupMaxCapacities, step.IsCodHardCapacityData.IsHardCaps,
				step.IsCodHardCapacityData.GroupIsHardCaps, volumeFilterTypeMaxCapacity, t.productGroupCodeMapping,
				t.attrMap, parcel_type_definition.ParcelTypeCod)
		case rule.MaskStepMaxCodCapacityZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMaxCapacity,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeCod)
		case rule.MaskStepMaxBulkyCapacityCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MaxBulkyCapacityData.MaxCapacities,
				step.MaxBulkyCapacityData.GroupMaxCapacities, step.IsBulkyHardCapacityData.IsHardCaps,
				step.IsBulkyHardCapacityData.GroupIsHardCaps, volumeFilterTypeMaxCapacity, t.productGroupCodeMapping,
				t.attrMap, parcel_type_definition.ParcelTypeBulky)
		case rule.MaskStepMaxBulkyCapacityZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMaxCapacity,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeBulky)
		case rule.MaskStepMaxHighValueCapacityCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MaxHighValueCapacityData.MaxCapacities,
				step.MaxHighValueCapacityData.GroupMaxCapacities, step.IsHighValueHardCapacityData.IsHardCaps,
				step.IsHighValueHardCapacityData.GroupIsHardCaps, volumeFilterTypeMaxCapacity, t.productGroupCodeMapping,
				t.attrMap, parcel_type_definition.ParcelTypeHighValue)
		case rule.MaskStepMaxHighValueCapacityZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMaxCapacity,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeHighValue)
		case rule.MaskStepMaxDgCapacityCountry:
			productIDs = t.filterByCountryVolume(ctx, maskProductID, productIDs, step.MaxDgCapacityData.MaxCapacities,
				step.MaxDgCapacityData.GroupMaxCapacities, step.IsDgHardCapacityData.IsHardCaps,
				step.IsDgHardCapacityData.GroupIsHardCaps, volumeFilterTypeMaxCapacity, t.productGroupCodeMapping,
				t.attrMap, parcel_type_definition.ParcelTypeDg)
		case rule.MaskStepMaxDgCapacityZoneRoute:
			productIDs = t.filterByZoneRouteVolume(ctx, maskProductID, productIDs, volumeFilterTypeMaxCapacity,
				t.productGroupCodeMapping, t.attrMap, parcel_type_definition.ParcelTypeDg)
		}
		incrCntMap(t.reqType, productNumofFactorPassed, productIDs)
		logger.CtxLogInfof(ctx, "After step %s, channels=%+v", step.MaskStepType, productIDs)
		if len(productIDs) == 1 {
			return productIDs[0], nil
		}
	}

	if !t.Rule.EnableProductPriority {
		logger.CtxLogInfof(ctx, "Product priority not enabled, returning first channel %v", productIDs[0])
		return productIDs[0], nil
	}

	var selectedProduct int64
	switch t.DefaultRuleType {
	case entity.Priority:
		selectedProduct = t.filterByPriority(productIDs)
		logger.CtxLogInfof(ctx, "Select product %v by channel_priority %+v", selectedProduct, t.productPriorities)
	case entity.Weightage:
		selectedProduct = t.filterByWeightage(ctx, productIDs)
		logger.CtxLogInfof(ctx, "Select product %v by channel_weightage %+v", selectedProduct, t.productWeightages)
	}
	incrCntMap(t.reqType, productNumofFactorPassed, []int64{selectedProduct})
	allocationLogs.AddDefaultSoftCriteriaItem(ctx, productIDs, selectedProduct,
		t.DefaultRuleType, t.productPriorities, t.productWeightages)
	return selectedProduct, nil
}

func (t *Task) filterByPriority(productIDs []int64) int64 {
	var (
		minPriority     = int32(math.MaxInt32)
		selectedProduct = productIDs[0]
	)
	for _, ch := range productIDs {
		p, ok := t.productPriorities[ch]
		if !ok {
			continue
		}
		if p < minPriority {
			minPriority = p
			selectedProduct = ch
		}
	}

	return selectedProduct
}

func (t *Task) filterByWeightage(ctx context.Context, productIDs []int64) int64 {
	var selectedProduct int
	items := make(map[string]uint)
	for _, prod := range productIDs {
		w, ok := t.productWeightages[prod]
		if !ok {
			continue
		}
		items[strconv.Itoa(int(prod))] = uint(w)
	}
	result := mathutil.ChoiceByWeightage(ctx, items)
	selectedProduct, _ = strconv.Atoi(result)

	return int64(selectedProduct)
}

func (t *Task) filterByCountryVolume(
	ctx context.Context, maskProductID int64, products []int64, volumeThreshold map[rule.VolumeKey]int32,
	groupVolumeThreshold map[string]int32, isHardCap map[rule.VolumeKey]bool, groupIsHardCap map[string]bool,
	volType volumeFilterType, productGroupCodeMapping map[int64]string,
	attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, filterParcelType parcel_type_definition.ParcelType,
) []int64 {
	var (
		filtered       []int64
		currentVolumes = map[string]int32{} // currentVolumes is for logging
		allocationLogs = GetAllocationLogFromContext(ctx)
	)

	allocationLogs.AppendSoftCriteriaDetail(ctx)
	for _, prod := range products {
		allocationLogs.UpdateSoftCriteriaDetailInput(ctx, prod)
		if !t.needFilterByParcelType(prod, attrMap, filterParcelType) {
			// 不需要校验的，视为通过
			filtered = append(filtered, prod)
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, prod)
			continue
		}

		var (
			groupCode   = productGroupCodeMapping[prod]
			threshold   int32
			thresholdOK bool
		)

		if groupCode != "" {
			threshold, thresholdOK = groupVolumeThreshold[groupCode]
			if !thresholdOK {
				continue
			}
		} else {
			key := rule.VolumeKey{MaskProductID: maskProductID, FulfillmentProductID: prod}
			threshold, thresholdOK = volumeThreshold[key]
			if !thresholdOK {
				continue
			}
		}

		v := t.GetProductVolume(ctx, maskProductID, prod, groupCode, filterParcelType)

		logKey := strconv.Itoa(int(prod))
		if groupCode != "" {
			logKey = fmt.Sprintf("%s:%s", logKey, groupCode)
		}
		logKey = volumecounter.AddParcelTypeSuffix(logKey, filterParcelType)
		currentVolumes[logKey] = v
		allocationLogs.UpdateSoftCriteriaDetailCountryVolume(ctx, prod, v, threshold, volType)

		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if v == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.MaskingCountryVolumeZeroNum, "")
		}

		// 先校验Country Level的Volume
		if v >= threshold {
			continue
		}
		filtered = append(filtered, prod)
		allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, prod)
	}

	logger.CtxLogInfof(ctx, "Current Country Volumes: %v", currentVolumes)
	if filtered != nil {
		return filtered
	}

	// SSCSMR-543：校验hard cap
	if volType == volumeFilterTypeMaxCapacity {
		for _, prod := range products {
			var (
				groupCode = productGroupCodeMapping[prod]
				hardCap   bool
				hardCapOK bool
			)

			if groupCode != "" {
				if hardCap, hardCapOK = groupIsHardCap[groupCode]; hardCapOK && hardCap {
					continue
				}
			} else {
				key := rule.VolumeKey{MaskProductID: maskProductID, FulfillmentProductID: prod}
				if hardCap, hardCapOK = isHardCap[key]; hardCapOK && hardCap {
					continue
				}
			}

			filtered = append(filtered, prod)
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, prod)
		}
		if filtered != nil {
			return filtered
		}
	}

	allocationLogs.SortCriteriaBottomLogic(ctx, products)
	return products
}

func (t *Task) filterByZoneRouteVolume(
	ctx context.Context, maskProductID int64, products []int64, volType volumeFilterType, productGroupCodeMapping map[int64]string,
	attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, filterParcelType parcel_type_definition.ParcelType,
) []int64 {
	var (
		filtered       []int64
		currentVolumes = make(map[string]int32)
		allocationLogs = GetAllocationLogFromContext(ctx)
	)

	allocationLogs.AppendSoftCriteriaDetail(ctx)

	// 优先校验zone volume
	filtered, _ = t.ZoneRouteIndependentFilter(
		ctx, maskProductID, products, volType, rulevolume.MaskLocTypeZone, currentVolumes, productGroupCodeMapping, attrMap, filterParcelType)
	// 如果zone volume能决定出唯一则直接返回，不需要再校验Route,所有都校验失败了或者len(filtered) > 1的都继续校验Route
	if len(filtered) == 1 {
		logger.CtxLogInfof(ctx, "only one product after filtered by zone volume or all fail, product: %v", filtered)
		return filtered
	}

	// 用zone volume通过的去校验route volume
	filtered, _ = t.ZoneRouteIndependentFilter(ctx, maskProductID, filtered, volType, rulevolume.MaskLocTypeRoute,
		currentVolumes, productGroupCodeMapping, attrMap, filterParcelType)
	if len(filtered) != 0 {
		logger.CtxLogInfof(ctx, "after filter by route volume, product: %v", filtered)
		return filtered
	}

	logger.CtxLogDebugf(ctx, "all products were filtered out, use fallback")
	allocationLogs.SortCriteriaBottomLogic(ctx, products)

	return products
}

// zoneRouteIndependentFilter Zone/Route的运力筛选
// 返回：筛选后的渠道 + 是否所有Product都校验Hard Cap不通过
func (t *Task) ZoneRouteIndependentFilter(
	ctx context.Context, maskProductID int64, products []int64, volType volumeFilterType, locType rulevolume.MaskLocType,
	currentVolumes map[string]int32, productGroupCodeMapping map[int64]string,
	attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, filterParcelType parcel_type_definition.ParcelType,
) ([]int64, bool) {
	var (
		filtered       []int64
		allocationLogs = GetAllocationLogFromContext(ctx)
	)

	for _, product := range products {
		allocationLogs.UpdateSoftCriteriaDetailInput(ctx, product)

		if !t.needFilterByParcelType(product, attrMap, filterParcelType) {
			// 不需要校验的，视为通过
			filtered = append(filtered, product)
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, product)
			continue
		}

		if ok := t.filterByLocVolume(ctx, maskProductID, product, volType, locType, currentVolumes, productGroupCodeMapping[product], filterParcelType); !ok {
			continue
		}
		filtered = append(filtered, product)
		allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, product)
	}

	logger.CtxLogInfof(ctx, "Current %s Volumes: %v", locType, currentVolumes)

	if filtered != nil {
		return filtered, false
	}

	//校验hard cap
	if volType == volumeFilterTypeMaxCapacity {
		logger.CtxLogInfof(ctx, "all products were filtered out, use hard cap filter, loc type: %s", locType)

		for _, product := range products {
			if ok := t.filterByLocHardCap(product, locType, filterParcelType); !ok {
				continue
			}
			filtered = append(filtered, product)
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, product)
		}
		if filtered != nil {
			return filtered, false
		}
	}

	// 所有Product都校验不通过
	return products, true
}

func (t *Task) filterByLocHardCap(productId int64, locType rulevolume.MaskLocType, filterParcelType parcel_type_definition.ParcelType) bool {
	productLocVols, exist := t.LocationVolumes[productId]
	if !exist {
		return true
	}

	var locVols []*rulevolume.MaskLocVolume
	switch locType {
	case rulevolume.MaskLocTypeZone:
		locVols = productLocVols.ZoneVolumes
	case rulevolume.MaskLocTypeRoute:
		locVols = productLocVols.RouteVolumes
	}
	if len(locVols) == 0 {
		return true
	}

	for _, locVol := range locVols {
		var hardCap bool // 获取Location Type对应的hard cap配置
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			hardCap = t.getRouteHardCapByParcelType(locVol, filterParcelType)
		case rulevolume.MaskLocTypeZone:
			switch locVol.ZoneDirection {
			case rulevolume.MaskZoneDirectionOrigin:
				if locVol.ZoneVolume.OriginMaxCapacity != rv.DefaultMaskMaxCapacity {
					hardCap = locVol.ZoneVolume.IsHardCap
				}
			case rulevolume.MaskZoneDirectionDest:
				if locVol.ZoneVolume.DestMaxCapacity != rv.DefaultMaskMaxCapacity {
					hardCap = t.getDestZoneHardCapByParcelType(locVol, filterParcelType)
				}
			}
		}
		if hardCap { // hard cap 配置为true，则表示强制关闭分配
			return false
		}
	}

	return true
}

func (t *Task) filterByLocVolume(
	ctx context.Context, maskProductID, productId int64, volType volumeFilterType, locType rulevolume.MaskLocType,
	counterForLog map[string]int32, groupCode string, filterParcelType parcel_type_definition.ParcelType,
) bool {
	productLocVols, exist := t.LocationVolumes[productId]
	if !exist {
		return true
	}

	var locVols []*rulevolume.MaskLocVolume
	switch locType {
	case rulevolume.MaskLocTypeZone:
		locVols = productLocVols.ZoneVolumes
	case rulevolume.MaskLocTypeRoute:
		locVols = productLocVols.RouteVolumes
	}
	if len(locVols) == 0 {
		return true
	}

	allocationLogs := GetAllocationLogFromContext(ctx)
	for _, locVol := range locVols {
		var v, threshold int32
		switch volType {
		case volumeFilterTypeMinVolume:
			switch locVol.LocType {
			case rulevolume.MaskLocTypeRoute:
				threshold = locVol.RouteVolume.MinVolume
				allocationLogs.SetRouteCode(locVol.LocCode)
			case rulevolume.MaskLocTypeZone:
				switch locVol.ZoneDirection {
				case rulevolume.MaskZoneDirectionOrigin:
					threshold = locVol.ZoneVolume.OriginMinVolume
					allocationLogs.SetOriginZoneCode(locVol.LocCode)
				case rulevolume.MaskZoneDirectionDest:
					threshold = locVol.ZoneVolume.DestMinVolume
					allocationLogs.SetDestZoneCode(locVol.LocCode)
				}
			}
		case volumeFilterTypeMaxCapacity:
			switch locVol.LocType {
			case rulevolume.MaskLocTypeRoute:
				threshold = t.getRouteThresholdByParcelType(locVol, filterParcelType)
				allocationLogs.SetRouteCode(locVol.LocCode)
			case rulevolume.MaskLocTypeZone:
				switch locVol.ZoneDirection {
				case rulevolume.MaskZoneDirectionOrigin:
					threshold = locVol.ZoneVolume.OriginMaxCapacity
					allocationLogs.SetOriginZoneCode(locVol.LocCode)
				case rulevolume.MaskZoneDirectionDest:
					threshold = t.getDestZoneThresholdByParcelType(locVol, filterParcelType)
					allocationLogs.SetDestZoneCode(locVol.LocCode)
				}
			}
		}
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			var logKey string
			if groupCode != "" {
				v, _ = t.VolumeCounter.GetGroupRouteVolume(ctx, groupCode, locVol.LocCode, t.ruleMode, filterParcelType)
				logKey = fmt.Sprintf("%v:%s:%s", groupCode, locVol.LocType, locVol.LocCode)
			} else {
				v, _ = t.VolumeCounter.GetProductRouteVolume(ctx, maskProductID, productId, locVol.LocCode, t.ruleMode, filterParcelType)
				logKey = fmt.Sprintf("%v:%s:%s", productId, locVol.LocType, locVol.LocCode)
			}
			counterForLog[logKey] = v
		case rulevolume.MaskLocTypeZone:
			var logKey string
			if groupCode != "" {
				v, _ = t.VolumeCounter.GetGroupZoneVolume(ctx, groupCode, locVol.LocCode, locVol.ZoneDirection, t.ruleMode, filterParcelType)
				logKey = fmt.Sprintf("%v:%s:%s:%s", groupCode, locVol.LocType, locVol.LocCode, locVol.ZoneDirection)
				logKey = volumecounter.AddParcelTypeSuffix(logKey, filterParcelType)
			} else {
				v, _ = t.VolumeCounter.GetProductZoneVolume(ctx, maskProductID, productId, locVol.LocCode, locVol.ZoneDirection, t.ruleMode, filterParcelType)
				logKey = fmt.Sprintf("%v:%s:%s:%s", productId, locVol.LocType, locVol.LocCode, locVol.ZoneDirection)
				logKey = volumecounter.AddParcelTypeSuffix(logKey, filterParcelType)
			}
			counterForLog[logKey] = v
		}
		allocationLogs.UpdateSortCriteriaDetailLocVolume(ctx, productId, v, locVol, volType, threshold)

		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if v == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.MaskingZoneRouteVolumeZeroNum, "")
		}

		if v >= threshold {
			return false
		}
	}

	return true
}

func (t *Task) filterByVolumeInBatch(ctx context.Context, products []int64, batchSize int32, volumeThreshold map[int64]int32, maskProductID int64) []int64 {
	var filtered []int64

	// currentVolumes is for logging
	currentVolumes := map[int64]int32{}
	allocationLogs := GetAllocationLogFromContext(ctx)
	allocationLogs.AppendSoftCriteriaDetail(ctx)
	for _, ch := range products {
		allocationLogs.UpdateSoftCriteriaDetailInput(ctx, ch)
		threshold, ok := volumeThreshold[ch]
		if !ok {
			continue
		}
		v := t.GetProductVolumeInBatch(ctx, ch, maskProductID)
		currentVolumes[ch] = v
		allocationLogs.UpdateSoftCriteriaDetailBatchSize(ctx, ch, threshold, v, batchSize)
		if v < threshold {
			filtered = append(filtered, ch)
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, ch)
		}
	}

	logger.CtxLogInfof(ctx, "Current Volumes In Batch: %v", currentVolumes)
	if filtered != nil {
		return filtered
	}
	allocationLogs.SortCriteriaBottomLogic(ctx, products)
	return products
}

func (t *Task) filterByCheapestFee(ctx context.Context, products []int64, info *pb.MaskingOrderInfo, shippingFeeConfigs []*allocation.ShippingFeeConfig) []int64 {
	if len(products) <= 1 {
		return products
	}
	monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.EnterAllocateShippingFeeFactor, "")

	var (
		minESF         = math.MaxFloat64
		minFeeProducts []int64
	)
	// 记录需要计算运费的订单数量
	prometheusutil.MaskingShippingFeeReportProductTotal(t.MaskingProductID, products)

	allocationLogs := GetAllocationLogFromContext(ctx)
	allocationLogs.AppendSoftCriteriaDetail(ctx)
	// 初始化allocate log运费调度记录，记录本次调度有多少product进入运费调度因子
	for _, product := range products {
		allocationLogs.UpdateSoftCriteriaDetailInput(ctx, product)
	}

	var rateResp *chargeentity.BatchAllocationESFResp
	var err *srerr.Error
	if len(shippingFeeConfigs) > 0 {
		// get forecast shipping fee,判断是否需要重新计算运费
		rateResp = getCalculateEsfResult(ctx)
	} else {
		//从redis中取提前计算的运费结果，并返回是否有没有提前计算运费的product
		var realTimeShippingFeeResp *chargeentity.BatchAllocationESFResp
		preRateResp, remainingProductIds := t.getPreCalculateShippingFee(ctx, products)
		if len(remainingProductIds) != 0 { //计算剩余product运费
			monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingFeeFactorRequestFinanceNum, fmt.Sprintf("shipping fee factor request finance num, productIds=%v", remainingProductIds))
			rateReq := chargeentity.BatchAllocationESFReq{
				Token: constant.FreightApiTokens[strings.ToLower(envvar.GetEnvWithCtx(ctx))],
			}

			commonReqDataItem := GetCommonReqDataItemForOrder(info)

			for _, productID := range remainingProductIds {
				reqDataItem := commonReqDataItem
				reqDataItem.ProductID = productID
				reqDataItem.UniqueId = fmt.Sprintf(UniqPattern, productID)
				reqDataItem.Timestamp = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
				rateReq.Data = append(rateReq.Data, &reqDataItem)
			}
			realTimeShippingFeeResp, err = t.service.RateClient.BatchAllocatingESFWithGrpc(ctx, &rateReq, chargeentity.SceneAllocating)
		}
		rateResp = mergeRateResp(preRateResp, realTimeShippingFeeResp)
	}

	if err != nil || rateResp == nil {
		monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.AllocateShippingFeeReqError, fmt.Sprintf("Cheapest fee get esf from rateapi, rateResp:%+v|err:%+v", rateResp, err))
		logger.CtxLogErrorf(ctx, "Cheapest fee get esf from rateapi, rateResp:%+v|err:%+v", rateResp, err)
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		return products
	}

	if !rateResp.IsSuccess() {
		monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.AllocateShippingFeeReqError, fmt.Sprintf("Cheapest fee get esf from rateapi not success: %+v", rateResp))
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		logger.CtxLogErrorf(ctx, "Cheapest fee get esf from rateapi not success: %+v", rateResp)
		return products
	}

	// RateApi的Batch接口中，请求数据列表与响应数据列表的顺序保持一致
	for _, esfResult := range rateResp.Data.AllocatingShippingFeeResult {
		if !esfResult.IsSuccess() {
			monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.AllocateShippingFeeEsfResultError, fmt.Sprintf("Cheapest fee get batch esf item from rateapi not success: %+v", esfResult))
			logger.CtxLogErrorf(ctx, "Cheapest fee get batch esf item from rateapi not success: %+v", esfResult)
			continue
		}
		// 上报运费计算成功的product的订单数
		prometheusutil.MaskingShippingFeeReportProductSuccess(t.MaskingProductID, esfResult.ProductId)
		prometheusutil.AllocateShippingFeeReport(esfResult.ProductId, esfResult.AllocatingShippingFee)
		allocationLogs.UpdateSoftCriteriaDetailFee(ctx, esfResult.ProductId, esfResult.AllocatingShippingFee)
		if esfResult.AllocatingShippingFee < minESF {
			minESF = esfResult.AllocatingShippingFee
			minFeeProducts = []int64{esfResult.ProductId}
			//allocationLogs.UpdateSoftCriteriaDetailOutput(ctx,esfResult.ProductId)
		} else if esfResult.AllocatingShippingFee == minESF {
			minFeeProducts = append(minFeeProducts, esfResult.ProductId)
			//allocationLogs.UpdateSoftCriteriaDetailOutput(ctx,esfResult.ProductId)
		}
	}

	if len(minFeeProducts) != 0 {
		for _, productId := range minFeeProducts {
			allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, productId)
		}
		// 上报通过了最低运费过滤的订单数
		prometheusutil.MaskingShippingFeeReportSuccess(t.MaskingProductID)
		return minFeeProducts
	}

	allocationLogs.SortCriteriaBottomLogic(ctx, products)
	return products
}

func reportMaskingRuleFactor(maskProductId int64, rule *rule.MaskRule, defaultType entity.DefaultRuleType) {
	if rule == nil {
		return
	}
	var factorList []string
	// 软性规则上报
	for _, step := range rule.MaskRuleSteps {
		factorList = append(factorList, step.MaskStepType.String())
	}
	// 默认规则上报
	factorList = append(factorList, defaultType.String())
	prometheusutil.MaskingScheduleFactorReport(maskProductId, factorList)
}

func mergeRateResp(preShippingFee *chargeentity.BatchAllocationESFResp, realTimeShippingFee *chargeentity.BatchAllocationESFResp) *chargeentity.BatchAllocationESFResp {
	if preShippingFee == nil {
		return realTimeShippingFee
	}

	if realTimeShippingFee == nil {
		return preShippingFee
	}
	//合并一下提前计算运费和剩余product计算运费结果，AllocatingShippingFeeResult为nil可以用append
	preShippingFee.Data.AllocatingShippingFeeResult = append(preShippingFee.Data.AllocatingShippingFeeResult, realTimeShippingFee.Data.AllocatingShippingFeeResult...)

	return preShippingFee
}

// 获取productIds的提前计算运费结果
// 返回结果：第一个参数表示获取到的提前计算运费结果，第二个参数表示未提前计算运费，需要补齐运费的product列表
func (t *Task) getPreCalculateShippingFee(ctx context.Context, productIds []int64) (*chargeentity.BatchAllocationESFResp, []int64) {
	// 获取提前计算运费结果
	preCalcFee := t.PreCalcFee
	preShippingFeeResultMap := make(map[string]*chargeentity.AllocatingShippingFeeResultItem, 0)
	// 只有提前计算运费请求成功才需要统计每个product的运费
	if preCalcFee != nil && preCalcFee.Retcode == 0 && preCalcFee.Data != nil {
		for _, shippingFeeResult := range preCalcFee.Data.AllocatingShippingFeeResult {
			// 记录运费计算过运费的product，由于运费计算失败不会返回productId，故这里以uniq_id为key
			preShippingFeeResultMap[shippingFeeResult.UniqueId] = shippingFeeResult
		}
	}
	// 提前计算运费的结果为空，则直接返回
	if len(preShippingFeeResultMap) == 0 {
		logger.CtxLogInfof(ctx, "get masking pre calc fee is nil, productIds=%v, preCalcFee=%+v", productIds, preCalcFee)
		monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingGetPreCalcFeeIsNil, fmt.Sprintf("get masking pre calc fee is nil, productIds=%v, preCalcFee=%+v", productIds, preCalcFee))
		return nil, productIds
	}

	if preCalcFee == nil {
		monitoring.ReportError(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingPreCalcFeeIsNil, fmt.Sprintf("preCalcFee is nil, productIds=%v", productIds))
		return nil, productIds
	}

	// 将提前计算运费请求的结果copy一份
	preCalcFeeResult := &chargeentity.BatchAllocationESFResp{
		Retcode: preCalcFee.Retcode,
		Message: preCalcFee.Message,
		Detail:  preCalcFee.Detail,
		Data:    &chargeentity.BatchAllocationESFRespData{},
	}
	var shippingFeeResult []*chargeentity.AllocatingShippingFeeResultItem
	var needCalcProductIds []int64
	// 遍历进入运费调度因子的product列表，product存在运费则赋值，不存在运费则记录下来，重新请求finance计算运费
	for _, productId := range productIds {
		key := fmt.Sprintf(UniqPattern, productId)
		if _, ok := preShippingFeeResultMap[key]; ok {
			shippingFeeResult = append(shippingFeeResult, preShippingFeeResultMap[key])
		} else {
			needCalcProductIds = append(needCalcProductIds, productId)
		}
	}
	if len(needCalcProductIds) != 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingNeedCompensateFee, fmt.Sprintf("need compensate shipping fee, needCompensateProduct=%v", needCalcProductIds))
	}
	preCalcFeeResult.Data.AllocatingShippingFeeResult = shippingFeeResult
	logger.CtxLogInfof(ctx, "get masking pre calc fee success, preCalcFee=%+v, getPreCalcFeeResult=%+v, input productIds=%v, need Compensate productIds=%+v", preCalcFee, preCalcFeeResult, productIds, needCalcProductIds)
	monitoring.ReportSuccess(ctx, monitoring.CatMaskingCalcFeeMonitor, monitoring.MaskingGetPreCalcFeeSuccess, fmt.Sprintf("get masking pre calc fee success, preCalcFee=%+v, getPreCalcFeeResult=%+v, input productIds=%v, need Compensate productIds=%+v", preCalcFee, preCalcFeeResult, productIds, needCalcProductIds))
	return preCalcFeeResult, needCalcProductIds
}

func getCalculateEsfResult(ctx context.Context) *chargeentity.BatchAllocationESFResp {
	value := requestid.GetCtxWithKey(ctx, requestid.CtxKey{})
	if value == nil {
		return nil
	}
	if resp, ok := value.(*chargeentity.BatchAllocationESFResp); ok {
		return resp
	} else {
		return nil
	}
}

func (t *Task) filterByPickupEfficiencyWhitelist(ctx context.Context, maskProductID int64, products []int64, info *pb.MaskingOrderInfo) []int64 {
	allocationLogs := GetAllocationLogFromContext(ctx)
	allocationLogs.AppendSoftCriteriaDetail(ctx)
	// 初始化allocate log运费调度记录，记录本次调度有多少product进入运费调度因子
	for _, product := range products {
		allocationLogs.UpdateSoftCriteriaDetailInput(ctx, product)
	}

	// 1.根据shop id + Order type获取白名单
	checkoutItems := info.GetCheckoutItems()
	if len(checkoutItems) == 0 {
		prometheusutil.MaskingWhitelistReport(strconv.FormatInt(maskProductID, 10), "no_checkout_items", "")
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		return products
	}

	var meetWhitelist bool
	// 只要有一个shop命中，就视为命中白名单
	for _, checkoutItem := range checkoutItems {
		shopId := checkoutItem.GetShopId()
		if hit := t.shopWhitelistService.CheckShopWhitelistAndFulfillmentType(ctx, int64(shopId), t.GetOrderType()); hit {
			meetWhitelist = true
			logger.CtxLogInfof(ctx, "shop %d meet pickup efficiency whitelist, fulfillmentType: %s", shopId, t.GetOrderType())
			break
		}
	}

	// 2.未命中，直接全部过滤返回
	if !meetWhitelist {
		prometheusutil.MaskingWhitelistReport(strconv.FormatInt(maskProductID, 10), "no_meet_whitelist", "")
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		return products
	}

	// 3.命中白名单，取优先级
	priorityMap, err := t.shopWhitelistService.GetPickupPriorityByMaskProductIDWithCache(ctx, maskProductID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "filterByPickupEfficiencyWhitelist| get priority err:%v", err)
		prometheusutil.MaskingWhitelistReport(strconv.FormatInt(maskProductID, 10), "get_priority_err", "")
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		return products
	}

	// 4. 比较products
	var minPriority int64 = math.MaxInt64
	var finalProduct int64 = 0

	for _, product := range products {
		if priority, ok := priorityMap[product]; ok {
			allocationLogs.UpdateSoftCriteriaDetailWhitelistPriority(ctx, product, int32(priority))

			if priority < minPriority {
				finalProduct = product
				minPriority = priority
			}
		}
	}
	logger.CtxLogInfof(ctx, "filterByPickupEfficiencyWhitelist|final priority:%d, final product:%d", minPriority, finalProduct)

	if finalProduct == 0 {
		logger.CtxLogErrorf(ctx, "filterByPickupEfficiencyWhitelist|get empty final result, plz check the priority configuration and hard check result")
		prometheusutil.MaskingWhitelistReport(strconv.FormatInt(maskProductID, 10), "no_priority", "")
		allocationLogs.SortCriteriaBottomLogic(ctx, products)
		return products
	}

	prometheusutil.MaskingWhitelistReport(strconv.FormatInt(maskProductID, 10), "whitelist_result", strconv.FormatInt(finalProduct, 10))
	allocationLogs.UpdateSoftCriteriaDetailOutput(ctx, finalProduct)
	return []int64{finalProduct}
}

func (t *Task) GetOrderType() whitelist.FulfillmentType {
	if t.businessType == constant.AllocateMasking || t.businessType == constant.ForecastMasking {
		return whitelist.MPLFulfillment
	}
	if t.businessType == constant.EstimateMasking {
		return whitelist.WMSFulfillment
	}
	return whitelist.IllegalType
}

func (t *Task) needFilterByParcelType(
	productId int64, attrMap map[int64]*parcel_type_definition.ParcelTypeAttr, filterParcelType parcel_type_definition.ParcelType,
) bool {
	if filterParcelType == parcel_type_definition.ParcelTypeNone {
		// 如果filter不属于三者(Cod/Bulky/HighValue)之一，则不用看Attribute了，需要进行Filter
		return true
	}

	// 属于Cod/Bulky/HighValue/DG的Filter Type情况下

	parcelAttr, exist := attrMap[productId]
	if !exist {
		// 先找对应的Attribute，找不到就不需要Filter
		return false
	}

	// 找到的话再看Filter Type和Attribute对不对的上，对的上则需要Filter

	if filterParcelType == parcel_type_definition.ParcelTypeCod && parcelAttr.IsCod {
		return true
	}

	if filterParcelType == parcel_type_definition.ParcelTypeBulky && parcelAttr.IsBulky {
		return true
	}

	if filterParcelType == parcel_type_definition.ParcelTypeHighValue && parcelAttr.IsHighValue {
		return true
	}

	if filterParcelType == parcel_type_definition.ParcelTypeDg && parcelAttr.IsDg {
		return true
	}

	return false
}

func (t *Task) getDestZoneThresholdByParcelType(locVolume *rulevolume.MaskLocVolume, parcelType parcel_type_definition.ParcelType) int32 {
	switch parcelType {
	case parcel_type_definition.ParcelTypeNone:
		return locVolume.ZoneVolume.DestMaxCapacity
	case parcel_type_definition.ParcelTypeCod:
		return locVolume.ZoneVolume.DestMaxCodCapacity
	case parcel_type_definition.ParcelTypeBulky:
		return locVolume.ZoneVolume.DestMaxBulkyCapacity
	case parcel_type_definition.ParcelTypeHighValue:
		return locVolume.ZoneVolume.DestMaxHighValueCapacity
	case parcel_type_definition.ParcelTypeDg:
		return locVolume.ZoneVolume.DestMaxDgCapacity
	}

	return 0
}

func (t *Task) getDestZoneHardCapByParcelType(locVolume *rulevolume.MaskLocVolume, parcelType parcel_type_definition.ParcelType) bool {
	switch parcelType {
	case parcel_type_definition.ParcelTypeNone:
		return locVolume.ZoneVolume.IsHardCap
	case parcel_type_definition.ParcelTypeCod:
		return locVolume.ZoneVolume.IsCodHardCap
	case parcel_type_definition.ParcelTypeBulky:
		return locVolume.ZoneVolume.IsBulkyHardCap
	case parcel_type_definition.ParcelTypeHighValue:
		return locVolume.ZoneVolume.IsHighValueHardCap
	case parcel_type_definition.ParcelTypeDg:
		return locVolume.ZoneVolume.IsDgHardCap
	}

	return false
}

func (t *Task) getRouteThresholdByParcelType(locVolume *rulevolume.MaskLocVolume, parcelType parcel_type_definition.ParcelType) int32 {
	switch parcelType {
	case parcel_type_definition.ParcelTypeNone:
		return locVolume.RouteVolume.MaxCapacity
	case parcel_type_definition.ParcelTypeCod:
		return locVolume.RouteVolume.MaxCodCapacity
	case parcel_type_definition.ParcelTypeBulky:
		return locVolume.RouteVolume.MaxBulkyCapacity
	case parcel_type_definition.ParcelTypeHighValue:
		return locVolume.RouteVolume.MaxHighValueCapacity
	case parcel_type_definition.ParcelTypeDg:
		return locVolume.RouteVolume.MaxDgCapacity
	}

	return 0
}

func (t *Task) getRouteHardCapByParcelType(locVolume *rulevolume.MaskLocVolume, parcelType parcel_type_definition.ParcelType) bool {
	switch parcelType {
	case parcel_type_definition.ParcelTypeNone:
		return locVolume.RouteVolume.IsHardCap
	case parcel_type_definition.ParcelTypeCod:
		return locVolume.RouteVolume.IsCodHardCap
	case parcel_type_definition.ParcelTypeBulky:
		return locVolume.RouteVolume.IsBulkyHardCap
	case parcel_type_definition.ParcelTypeHighValue:
		return locVolume.RouteVolume.IsHighValueHardCap
	case parcel_type_definition.ParcelTypeDg:
		return locVolume.RouteVolume.IsDgHardCap
	}

	return false
}
