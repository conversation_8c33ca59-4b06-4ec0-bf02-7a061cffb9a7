package parcel_type_definition

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	repo "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParcelTypeDefinitionServiceImpl_GetParcelTypeAttr(t *testing.T) {
	ctx := context.TODO()
	impl := &ParcelTypeDefinitionServiceImpl{}

	type args struct {
		maskProductID        int
		fulfillmentProductID int
		lineId               string
		scenarioType         int
		isCod                bool
		length               float64
		width                float64
		height               float64
		weight               float64
		cogs                 float64
		dimensionExist       bool
		dgType               pb.DgType
	}
	tests := []struct {
		name  string
		args  args
		want  *ParcelTypeAttr
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				maskProductID:        1,
				fulfillmentProductID: 2,
				isCod:                true,
				length:               1.22, //
				width:                1.22,
				height:               1.22,
				weight:               2,
				cogs:                 2,
				dimensionExist:       true,
				dgType:               pb.DgType_DG,
			},
			want: &ParcelTypeAttr{
				IsCod:       true,
				IsBulky:     true,
				IsHighValue: true,
				IsDg:        true,
			},
			setup: func() {
				localcache.InitTest(constant.ParcelTypeDefinition, "1:2", &repo.ParcelTypeDefinitionTab{
					BulkyLengthThreshold:       2,
					BulkyWidthThreshold:        2,
					BulkyHeightThreshold:       2,
					BulkyWeightThreshold:       0.002,
					HighValueCogPriceThreshold: 2,
				}, 3)
			},
		},
		{
			name: "case 2: convert parcel type definition to struct failed",
			args: args{
				maskProductID:        1,
				fulfillmentProductID: 2,
				isCod:                true,
				length:               1.22,
				width:                1.22,
				height:               1.22,
				weight:               1.22,
				cogs:                 2,
				dimensionExist:       true,
			},
			want: &ParcelTypeAttr{
				IsCod: true,
			},
			setup: func() {
				localcache.InitTest(constant.ParcelTypeDefinition, "1:2", nil, 1)
			},
		},
		{
			name: "case 3: parcel type definition not found",
			args: args{
				maskProductID:        1,
				fulfillmentProductID: 2,
				isCod:                true,
				length:               1.22,
				width:                1.22,
				height:               1.22,
				weight:               1.22,
				cogs:                 2,
				dimensionExist:       true,
			},
			want: &ParcelTypeAttr{
				IsCod: true,
			},
			setup: func() {
				localcache.InitTest(constant.ParcelTypeDefinition, "1", 2, 1)
			},
		},
		{
			name: "case 3: parcel type definition not found",
			args: args{
				maskProductID:        1,
				fulfillmentProductID: 2,
				isCod:                true,
				length:               1.22,
				width:                1.22,
				height:               1.22,
				weight:               1.22,
				cogs:                 2,
				dimensionExist:       true,
			},
			want: &ParcelTypeAttr{
				IsCod: true,
			},
			setup: func() {
				localcache.InitTest(constant.ParcelTypeDefinition, "1", 2, 1)
			},
		},
	}
	for _, tt := range tests {
		tt.setup()
		t.Run(tt.name, func(t *testing.T) {
			if got := impl.GetParcelTypeAttr(ctx, tt.args.maskProductID, tt.args.fulfillmentProductID, tt.args.lineId, tt.args.scenarioType, tt.args.isCod, tt.args.length, tt.args.width, tt.args.height, tt.args.weight, tt.args.cogs, tt.args.dimensionExist, tt.args.dgType); !assert.Equal(t, got, tt.want) {
				t.Errorf("GetParcelTypeAttr() = %v, want %v", got, tt.want)
			}
		})
	}
}
