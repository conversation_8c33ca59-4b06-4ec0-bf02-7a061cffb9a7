package parcel_type_definition

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	repo "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ParcelType int

const (
	ParcelTypeNone      ParcelType = 0
	ParcelTypeCod       ParcelType = 1
	ParcelTypeBulky     ParcelType = 2
	ParcelTypeHighValue ParcelType = 3
	ParcelTypeDg        ParcelType = 4
)

var (
	ParcelTypeList = []ParcelType{ParcelTypeNone, ParcelTypeCod, ParcelTypeBulky, ParcelTypeHighValue, ParcelTypeDg}
)

func (t ParcelType) String() string {
	switch t {
	case ParcelTypeCod:
		return "cod"
	case ParcelTypeBulky:
		return "bulky"
	case ParcelTypeHighValue:
		return "high_value"
	case ParcelTypeDg:
		return "dg"
	}

	return ""
}

type ParcelTypeAttr struct {
	IsCod       bool
	IsBulky     bool
	IsHighValue bool
	IsDg        bool
}

func (p *ParcelTypeAttr) GetIsCod() bool {
	if p != nil {
		return p.IsCod
	}
	return false
}

func (p *ParcelTypeAttr) GetIsBulky() bool {
	if p != nil {
		return p.IsBulky
	}
	return false
}

func (p *ParcelTypeAttr) GetIsHighValue() bool {
	if p != nil {
		return p.IsHighValue
	}
	return false
}

func (p *ParcelTypeAttr) GetIsDg() bool {
	if p != nil {
		return p.IsDg
	}
	return false
}

type ParcelTypeDefinitionService interface {
	CreateParcelTypeDefinition(ctx context.Context, entity *ParcelTypeDefinitionItem) (*ParcelTypeDefinitionItem, *srerr.Error)
	UpdateParcelTypeDefinition(ctx context.Context, entity *ParcelTypeDefinitionItem) (*ParcelTypeDefinitionItem, *srerr.Error)
	GetParcelTypeDefinition(ctx context.Context, id uint64) (*ParcelTypeDefinitionItem, *srerr.Error)
	ListParcelTypeDefinition(ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, status *int, pageNo, limit, scenarioType int) ([]*ParcelTypeDefinitionItem, int64, *srerr.Error)
	DeleteParcelTypeDefinition(ctx context.Context, id uint64) *srerr.Error
	GetParcelTypeAttr(ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, scenarioType int, isCod bool, length, width, height, weight, cogs float64, dimensionExist bool, dgType pb.DgType) *ParcelTypeAttr
}

type ParcelTypeDefinitionServiceImpl struct {
	repo   repo.ParcelTypeDefinitionRepo
	lpsApi lpsclient.LpsApi
}

func NewParcelTypeDefinitionServiceImpl(
	repo repo.ParcelTypeDefinitionRepo,
	lpsApi lpsclient.LpsApi,
) *ParcelTypeDefinitionServiceImpl {
	return &ParcelTypeDefinitionServiceImpl{
		repo:   repo,
		lpsApi: lpsApi,
	}
}

func (p ParcelTypeDefinitionServiceImpl) GetParcelTypeDefinition(ctx context.Context, id uint64) (*ParcelTypeDefinitionItem, *srerr.Error) {
	def, err := p.repo.GetParcelTypeDefinitionByID(ctx, id)
	if err != nil {
		return nil, err
	}

	productNameMap, err := p.lpsApi.GetAllProductIdNameList(ctx)
	if err != nil {
		return nil, err
	}

	return &ParcelTypeDefinitionItem{
		ID:                     def.ID,
		MaskProductID:          def.MaskProductID,
		MaskProductName:        productNameMap[int64(def.MaskProductID)],
		FulfillmentProductID:   def.FulfillmentProductID,
		FulfillmentProductName: productNameMap[int64(def.FulfillmentProductID)],
		LineID:                 def.LineID,
		ScenarioType:           def.ScenarioType,
		BulkyDefinition: BulkyDefinition{
			Weight: def.BulkyWeightThreshold,
			Length: def.BulkyLengthThreshold,
			Width:  def.BulkyWidthThreshold,
			Height: def.BulkyHeightThreshold,
		},
		HighValueDefinition: def.HighValueCogPriceThreshold,
		Status:              int(def.Status),
		ModifyTime:          def.Mtime,
		Operator:            def.Operator,
	}, nil
}

func (p ParcelTypeDefinitionServiceImpl) ListParcelTypeDefinition(
	ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, status *int, pageNo, limit, scenarioType int,
) ([]*ParcelTypeDefinitionItem, int64, *srerr.Error) {
	_, offset, limit := apiutil.GetOffsetAndLimit(pageNo, limit)
	list, total, err := p.repo.ListParcelTypeDefinition(ctx, maskProductID, fulfillmentProductID, lineID, status, offset, limit, scenarioType)
	if err != nil {
		return nil, 0, err
	}

	productNameMap, err := p.lpsApi.GetAllProductIdNameList(ctx)
	if err != nil {
		return nil, 0, err
	}

	entityList := make([]*ParcelTypeDefinitionItem, 0, len(list))
	for _, def := range list {
		entityList = append(entityList, &ParcelTypeDefinitionItem{
			ID:                     def.ID,
			MaskProductID:          def.MaskProductID,
			MaskProductName:        productNameMap[int64(def.MaskProductID)],
			FulfillmentProductID:   def.FulfillmentProductID,
			FulfillmentProductName: productNameMap[int64(def.FulfillmentProductID)],
			LineID:                 def.LineID,
			ScenarioType:           def.ScenarioType,
			BulkyDefinition: BulkyDefinition{
				Weight: def.BulkyWeightThreshold,
				Length: def.BulkyLengthThreshold,
				Width:  def.BulkyWidthThreshold,
				Height: def.BulkyHeightThreshold,
			},
			HighValueDefinition: def.HighValueCogPriceThreshold,
			Status:              int(def.Status),
			ModifyTime:          def.Mtime,
			Operator:            def.Operator,
		})
	}

	return entityList, total, nil
}

func (p ParcelTypeDefinitionServiceImpl) DeleteParcelTypeDefinition(ctx context.Context, id uint64) *srerr.Error {
	if err := p.repo.DeleteParcelTypeDefinitionByID(ctx, id); err != nil {
		return err
	}

	return nil
}

func (p ParcelTypeDefinitionServiceImpl) CreateParcelTypeDefinition(ctx context.Context, entity *ParcelTypeDefinitionItem) (*ParcelTypeDefinitionItem, *srerr.Error) {
	tab := &repo.ParcelTypeDefinitionTab{
		MaskProductID:              entity.MaskProductID,
		FulfillmentProductID:       entity.FulfillmentProductID,
		LineID:                     entity.LineID,
		ScenarioType:               entity.ScenarioType,
		BulkyLengthThreshold:       entity.BulkyDefinition.Length,
		BulkyWidthThreshold:        entity.BulkyDefinition.Width,
		BulkyHeightThreshold:       entity.BulkyDefinition.Height,
		BulkyWeightThreshold:       entity.BulkyDefinition.Weight,
		HighValueCogPriceThreshold: entity.HighValueDefinition,
		Status:                     repo.Status(entity.Status),
		Operator:                   entity.Operator,
	}

	if err := p.repo.CreateParcelTypeDefinition(ctx, tab); err != nil {
		return nil, err
	}

	entity.ID = tab.ID
	entity.ModifyTime = tab.Mtime

	return entity, nil
}

func (p ParcelTypeDefinitionServiceImpl) UpdateParcelTypeDefinition(ctx context.Context, entity *ParcelTypeDefinitionItem) (*ParcelTypeDefinitionItem, *srerr.Error) {
	tab := &repo.ParcelTypeDefinitionTab{
		ID:                         entity.ID,
		MaskProductID:              entity.MaskProductID,
		FulfillmentProductID:       entity.FulfillmentProductID,
		LineID:                     entity.LineID,
		ScenarioType:               entity.ScenarioType,
		BulkyLengthThreshold:       entity.BulkyDefinition.Length,
		BulkyWidthThreshold:        entity.BulkyDefinition.Width,
		BulkyHeightThreshold:       entity.BulkyDefinition.Height,
		BulkyWeightThreshold:       entity.BulkyDefinition.Weight,
		HighValueCogPriceThreshold: entity.HighValueDefinition,
		Status:                     repo.Status(entity.Status),
		Operator:                   entity.Operator,
	}

	if err := p.repo.UpdateParcelTypeDefinition(ctx, tab); err != nil {
		return nil, err
	}

	entity.ModifyTime = tab.Mtime

	return entity, nil
}

// GetParcelTypeAttr 根据订单信息判断Parcel Type
func (p ParcelTypeDefinitionServiceImpl) GetParcelTypeAttr(
	ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, scenarioType int, isCod bool,
	length, width, height, weight, cogs float64, dimensionExist bool, dgType pb.DgType) *ParcelTypeAttr {
	attr := &ParcelTypeAttr{}
	if isCod {
		attr.IsCod = true
	}

	if dgType == pb.DgType_DG || dgType == pb.DgType_PROHIBIT {
		attr.IsDg = true
	}

	key := parcel_type_definition2.GetParcelTypeDefinitionCacheKey(scenarioType, maskProductID, fulfillmentProductID, lineID)
	cacheVal, err := localcache.Get(ctx, constant.ParcelTypeDefinition, key)
	if err != nil {
		// 不配置属于正常情况
		logger.CtxLogDebugf(ctx, "parcel type definition not found")
		return attr
	}

	def, ok := cacheVal.(*repo.ParcelTypeDefinitionTab)
	if !ok {
		// 理论上不存在这种情况
		logger.CtxLogErrorf(ctx, "convert parcel type definition to struct failed | key=%s", key)
		return attr
	}

	// Dimension信息存在的时候才需要判断是否Bulky
	if dimensionExist {
		// 只要其中任一维度的超过了就算是Bulky
		// 需要注意Weight Threshold单位是KG，上游的Weight单位是G，判断时需要进行单位转换
		if length >= def.BulkyLengthThreshold || width >= def.BulkyWidthThreshold ||
			height >= def.BulkyHeightThreshold || weight >= def.BulkyWeightThreshold*1000 {
			attr.IsBulky = true
		}
	}

	if cogs >= def.HighValueCogPriceThreshold {
		attr.IsHighValue = true
	}

	return attr
}
