package parcel_type_definition

type (
	BulkyDefinition struct {
		Weight float64 `json:"weight"  validate:"gte=0"`
		Length float64 `json:"length"  validate:"gte=0"`
		Width  float64 `json:"width"  validate:"gte=0"`
		Height float64 `json:"height"  validate:"gte=0"`
	}

	ParcelTypeDefinitionItem struct {
		ID                   uint64          `json:"id"`
		MaskProductID        int             `json:"mask_product_id"`
		FulfillmentProductID int             `json:"fulfillment_product_id"`
		LineID               string          `json:"line_id"`
		ScenarioType         int             `json:"scenario_type"`
		BulkyDefinition      BulkyDefinition `json:"bulky_definition"`
		HighValueDefinition  float64         `json:"high_value_definition"`
		Status               int             `json:"status"`
		Operator             string          `json:"operator"`
		ModifyTime           int64           `json:"modify_time"`

		MaskProductName        string `json:"mask_product_name"`
		FulfillmentProductName string `json:"fulfillment_product_name"`
	}
)
