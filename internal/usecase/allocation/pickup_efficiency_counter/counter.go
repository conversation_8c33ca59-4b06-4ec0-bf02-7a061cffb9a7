package pickup_efficiency_counter

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	shopFulfillmentProductsKeyPrefix = "shop_fulfillment_products"
	maskProductActualBudgetPrefix    = "mask_product_actual_budget"
	shopIdSetKey                     = "shop_id_set"
)

type PickupEffCounter interface {
	GetMaskProductActualBudget(ctx context.Context, maskingProductId int64, date time.Time) (float64, *srerr.Error)
	IncrMaskProductActualBudget(ctx context.Context, maskingProductId int64, usedBudget int64, date time.Time) *srerr.Error

	GetShopFulfillmentProducts(ctx context.Context, shopId, maskProductId int64, date time.Time) ([]int64, *srerr.Error)
	BatchGetShopFulfillmentProducts(ctx context.Context, shopIds []int64, maskProductId int64, date time.Time) (map[int64][]int64, *srerr.Error)
	IncrShopFulfillmentProducts(ctx context.Context, shopId, maskProductId, fulfillmentProductId int64, count int, date time.Time) *srerr.Error
	BatchIncrShopFulfillmentProducts(ctx context.Context, maskProductId int64, date time.Time, items []ShopFulfillmentItem) *srerr.Error

	GetShopIdSet(ctx context.Context) ([]int64, *srerr.Error)
}

type PickupEffCounterImpl struct{}

func NewPickupEffCounterImpl() *PickupEffCounterImpl {
	return &PickupEffCounterImpl{}
}

func (p *PickupEffCounterImpl) GetMaskProductActualBudget(
	ctx context.Context, maskingProductId int64, date time.Time) (float64, *srerr.Error) {

	key := formatMaskProductActualBudgetKey(maskingProductId, date)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Float64()
	if err != nil && err != redis.Nil {
		logger.CtxLogErrorf(ctx, "get mask product actual budget failed | err=%v", err)
		return 0, srerr.With(srerr.RedisErr, "get mask product actual budget error", err)
	}

	return val, nil
}

func (p *PickupEffCounterImpl) IncrMaskProductActualBudget(
	ctx context.Context, maskingProductId int64, usedBudget int64, date time.Time) *srerr.Error {

	key := formatMaskProductActualBudgetKey(maskingProductId, date)
	if err := redisutil.GetDefaultInstance().IncrBy(ctx, key, usedBudget).Err(); err != nil {
		return srerr.With(srerr.RedisErr, "incr mask product actual budget error", err)
	}

	return nil
}

func (p *PickupEffCounterImpl) GetShopFulfillmentProducts(
	ctx context.Context, shopId, maskProductId int64, date time.Time) ([]int64, *srerr.Error) {

	var (
		key = formatShopFulfillmentProductsKey(maskProductId, shopId, date)
	)

	productStrList, err := redisutil.GetDefaultInstance().HGetAll(ctx, key).Result()
	if err != nil {
		return nil, srerr.With(srerr.RedisErr, "get shop fulfillment products error", err)
	}

	productList := make([]int64, 0, len(productStrList))
	for productStr, countStr := range productStrList {
		productId, pErr := strconv.Atoi(productStr)
		if pErr != nil {
			logger.CtxLogErrorf(ctx, "convert shop fulfillment product string to int fail | val: %s , err: %v", productStr, pErr)
			continue
		}
		count, cErr := strconv.Atoi(countStr)
		if cErr != nil {
			logger.CtxLogErrorf(ctx, "convert shop fulfillment product count string to int fail | val: %s , err: %v", countStr, cErr)
			continue
		}

		// 只需要单量>0的Product
		if count > 0 {
			productList = append(productList, int64(productId))
		}
	}

	return productList, nil
}

func (p *PickupEffCounterImpl) BatchGetShopFulfillmentProducts(
	ctx context.Context, shopIds []int64, maskProductId int64, date time.Time) (map[int64][]int64, *srerr.Error) {

	const maxPipelineSize = 1000
	results := make(map[int64][]int64, len(shopIds))

	for i := 0; i < len(shopIds); i += maxPipelineSize {
		end := i + maxPipelineSize
		if end > len(shopIds) {
			end = len(shopIds)
		}

		// Initialize the Redis pipeline for this batch
		var (
			pipe = redisutil.GetDefaultInstance().Pipeline()
			keys = make(map[string]int64)
		)

		// Add commands to the pipeline
		for _, shopId := range shopIds[i:end] {
			key := formatShopFulfillmentProductsKey(maskProductId, shopId, date)
			keys[key] = shopId
			pipe.HGetAll(ctx, key)
		}

		// Execute the pipeline
		cmders, err := pipe.Exec(ctx)
		if err != nil {
			return nil, srerr.With(srerr.RedisErr, "get shops fulfillment products error", err)
		}

		// Process the results
		for j, cmder := range cmders {
			shopId := shopIds[i+j]
			productStrList, err := cmder.(*redis.StringStringMapCmd).Result()
			if err != nil {
				logger.CtxLogErrorf(ctx, "failed to get results for shop ID %d: %v", shopId, err)
				continue
			}

			productList := make([]int64, 0, len(productStrList))
			for productStr, countStr := range productStrList {
				productId, pErr := strconv.Atoi(productStr)
				if pErr != nil {
					logger.CtxLogErrorf(ctx, "convert shop fulfillment product string to int fail | val: %s, err: %v", productStr, pErr)
					continue
				}
				count, cErr := strconv.Atoi(countStr)
				if cErr != nil {
					logger.CtxLogErrorf(ctx, "convert shop fulfillment product count string to int fail | val: %s, err: %v", countStr, cErr)
					continue
				}

				if count > 0 {
					productList = append(productList, int64(productId))
				}
			}

			results[shopId] = productList
		}
	}

	return results, nil
}

// IncrShopFulfillmentProducts 增加单个Shop的Fulfillment Product计数
// 内部实现改为调用批量方法以提高代码复用性和一致性
func (p *PickupEffCounterImpl) IncrShopFulfillmentProducts(
	ctx context.Context, shopId, maskProductId, fulfillmentProductId int64, count int, date time.Time) *srerr.Error {

	// 创建单个ShopFulfillmentItem
	item := ShopFulfillmentItem{
		ShopID:               shopId,
		FulfillmentProductID: fulfillmentProductId,
		Count:                count,
	}

	// 调用批量方法处理单个item
	return p.BatchIncrShopFulfillmentProducts(ctx, maskProductId, date, []ShopFulfillmentItem{item})
}

// BatchAddShopIdSet 批量添加Shop ID到集合，使用Redis Pipeline优化性能
// 每个pipeline最多处理1000个Shop ID
func (p *PickupEffCounterImpl) batchAddShopIdSet(ctx context.Context, shopIds []int64) *srerr.Error {
	// 如果没有数据，直接返回
	if len(shopIds) == 0 {
		return nil
	}

	const batchSize = 1000 // 每个pipeline最多处理1000个Shop ID

	// 分批处理shopIds
	for i := 0; i < len(shopIds); i += batchSize {
		// 计算当前批次的结束索引
		end := i + batchSize
		if end > len(shopIds) {
			end = len(shopIds)
		}

		// 获取当前批次的shopIds
		batchShopIds := shopIds[i:end]

		// 使用Redis Pipeline批量处理当前批次
		pipe := redisutil.GetDefaultInstance().Pipeline()

		// 批量添加SADD命令到Pipeline
		for _, shopId := range batchShopIds {
			pipe.SAdd(ctx, shopIdSetKey, shopId)
		}

		// 执行Pipeline
		cmds, err := pipe.Exec(ctx)
		if err != nil {
			return srerr.With(srerr.RedisErr, fmt.Sprintf("batch add shop id set error (batch %d-%d)", i, end-1), err)
		}

		// 检查命令执行结果
		for j, cmd := range cmds {
			if cmd.Err() != nil {
				logger.CtxLogErrorf(ctx, "pipeline command error for shop ID %d: %v", batchShopIds[j], cmd.Err())
			}
		}
	}

	return nil
}

func (p *PickupEffCounterImpl) GetShopIdSet(ctx context.Context) ([]int64, *srerr.Error) {
	const batchSize = 1000
	var (
		cursor  uint64
		shopIDs []int64
	)
	// 防呆，以防死循环
	for i := 0; i < 10000; i++ {
		// 使用 SSCAN 命令获取一批 ShopID
		batchShopIDs, newCursor, err := redisutil.GetDefaultInstance().SScan(ctx, shopIdSetKey, cursor, "*", batchSize).Result()
		if err != nil {
			return nil, srerr.With(srerr.RedisErr, "failed to scan shop IDs", err)
		}

		// 将获取的 ID 转换为 int64 并添加到结果列表中
		for _, shopIDStr := range batchShopIDs {
			shopID, err := strconv.ParseInt(shopIDStr, 10, 64)
			if err != nil {
				return nil, srerr.With(srerr.RedisErr, "failed to parse shop ID", err)

			}
			shopIDs = append(shopIDs, shopID)
		}

		// 更新游标
		cursor = newCursor

		// 如果游标为 0，表示已经遍历完整个集合
		if cursor == 0 {
			break
		}
	}

	return shopIDs, nil
}

func formatMaskProductActualBudgetKey(maskingProductId int64, date time.Time) string {
	return fmt.Sprintf("%s:%d:%s",
		maskProductActualBudgetPrefix, maskingProductId, timeutil.FormatTimeToVolumeDateString(date))
}

func formatShopFulfillmentProductsKey(maskProductId, shopId int64, date time.Time) string {
	return fmt.Sprintf("%s:%d:%d:%s",
		shopFulfillmentProductsKeyPrefix, maskProductId, shopId, timeutil.FormatTimeToVolumeDateString(date))
}

// BatchIncrShopFulfillmentProducts 批量增加店铺履约产品计数，使用Redis Pipeline优化性能
func (p *PickupEffCounterImpl) BatchIncrShopFulfillmentProducts(
	ctx context.Context, maskProductId int64, date time.Time, items []ShopFulfillmentItem) *srerr.Error {

	var (
		batchAllocateConf = configutil.GetBatchAllocateConf()
		statsEnable       = batchAllocateConf.PickupEffStatsEnable
		statsExpireDays   = time.Hour * 24 * time.Duration(batchAllocateConf.PickupEffStatsExpireDays)
		batchSize         = 1000 // 每个pipeline最多处理1000个item
	)

	// 按需开启
	if !statsEnable {
		return nil
	}

	// 如果没有数据，直接返回
	if len(items) == 0 {
		return nil
	}

	// 收集所有需要添加的Shop ID
	shopIdSet := make(map[int64]struct{})

	// 分批处理items
	for i := 0; i < len(items); i += batchSize {
		// 计算当前批次的结束索引
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}

		// 获取当前批次的items
		batchItems := items[i:end]

		// 使用Redis Pipeline批量处理当前批次
		pipe := redisutil.GetDefaultInstance().Pipeline()

		// 记录需要设置过期时间的键
		keySet := make(map[string]struct{})

		// 批量添加HINCRBY命令到Pipeline
		for _, item := range batchItems {
			key := formatShopFulfillmentProductsKey(maskProductId, item.ShopID, date)
			keySet[key] = struct{}{}
			shopIdSet[item.ShopID] = struct{}{}

			pipe.HIncrBy(ctx, key, strconv.Itoa(int(item.FulfillmentProductID)), int64(item.Count))
		}

		// 批量设置过期时间
		for key := range keySet {
			pipe.Expire(ctx, key, statsExpireDays)
		}

		// 执行Pipeline
		cmds, err := pipe.Exec(ctx)
		if err != nil {
			return srerr.With(srerr.RedisErr, fmt.Sprintf("batch incr shop fulfillment products error (batch %d-%d)", i, end-1), err)
		}

		// 检查命令执行结果
		for _, cmd := range cmds {
			if cmd.Err() != nil {
				logger.CtxLogErrorf(ctx, "pipeline command error: %v", cmd.Err())
			}
		}
	}

	// 批量添加Shop ID到集合
	shopIds := make([]int64, 0, len(shopIdSet))
	for shopID := range shopIdSet {
		shopIds = append(shopIds, shopID)
	}

	if err := p.batchAddShopIdSet(ctx, shopIds); err != nil {
		logger.CtxLogErrorf(ctx, "batch add shop id set error: %v", err)
	}

	return nil
}

// ShopFulfillmentItem 表示一个Shop的Fulfillment Product计数项
type ShopFulfillmentItem struct {
	ShopID               int64
	FulfillmentProductID int64
	Count                int
}
