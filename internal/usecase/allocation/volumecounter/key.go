package volumecounter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"time"
)

const (
	PrefixMaskProductVolume              = "mask_product_volume:"
	PrefixProductVolume                  = "product_volume:"
	PrefixAllocationRuleBatchVolumes     = "alloc_rule_batch_volumes:"
	PrefixBatchShippingChannelOrderCount = "batch_shipping_channel_order_count:"
	KeySlsBatchId                        = "sls_smart_routing_batch_id"
	KeySlsRuleId                         = "sls_smart_routing_rule_id"
	LockPrefixAllocationRuleBatchVolumes = "lock_alloc_rule_batch_volumes:"
	PrefixGroupVolume                    = "group_volume:"
)

type AllocateCheckBatchVolumeJobMsg struct {
	RuleID      int64
	BatchVolume int32
}

type GetProductZoneVolumeRequest struct {
	MaskProductID        int64
	FulfillmentProductID int64
	GroupCode            string
	ZoneCode             string
	Direction            rulevolume.MaskZoneDirection
	RuleMode             rule_mode.RuleMode
	ParcelType           parcel_type_definition.ParcelType
}

type GetProductRouteVolumeRequest struct {
	MaskProductID        int64
	FulfillmentProductID int64
	GroupCode            string
	RouteCode            string
	RuleMode             rule_mode.RuleMode
	ParcelType           parcel_type_definition.ParcelType
}

func AllocationRuleVolumesKey(ctx context.Context, ruleID int64) string {
	return PrefixAllocationRuleBatchVolumes + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(ruleID, 10)
}

func ProductMaskVolumeKey(ctx context.Context, maskProductID int64) string {
	return ProductMaskVolumeKeyByDate(maskProductID, timeutil.GetLocalTime(ctx))
}

func ProductMaskVolumeKeyByDate(maskProductID int64, date time.Time) string {
	return PrefixMaskProductVolume + timeutil.FormatTimeToVolumeDateString(date) + ":" + strconv.FormatInt(maskProductID, 10)
}

func ProductVolumeKey(ctx context.Context, productID int64, rm rule_mode.RuleMode) string {
	return ProductVolumeKeyByDate(productID, rm, timeutil.GetLocalTime(ctx))
}

func GroupVolumeKey(ctx context.Context, groupCode string, rm rule_mode.RuleMode) string {
	return GroupVolumeKeyByDate(groupCode, rm, timeutil.GetLocalTime(ctx))
}

// 只按照fulfillment维度控制country运力
func ProductVolumeKeyByDate(productID int64, rm rule_mode.RuleMode, date time.Time) string {
	dateStr := timeutil.FormatTimeToVolumeDateString(date)
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + dateStr + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + dateStr + ":" + strconv.FormatInt(productID, 10)
}

func GroupVolumeKeyByDate(groupCode string, rm rule_mode.RuleMode, date time.Time) string {
	dateStr := timeutil.FormatTimeToVolumeDateString(date)
	if rm == rule_mode.WmsOrderRule {
		return PrefixGroupVolume + "wms:" + dateStr + ":" + groupCode
	}
	return PrefixGroupVolume + dateStr + ":" + groupCode
}

func SmartRoutingBatchVolumeKey(ctx context.Context, ruleID, productID, batchId int) string {
	return PrefixBatchShippingChannelOrderCount + timeutil.GetTodayDateString(ctx) + ":" + strconv.Itoa(ruleID) + ":" + strconv.Itoa(productID) + strconv.Itoa(batchId)
}

// 只按照fulfillment维度控制route运力
func ProductRouteVolumeKey(ctx context.Context, productID int64, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:route:%s", ProductVolumeKey(ctx, productID, rm), routeCode)
}

func GroupRouteVolumeKey(ctx context.Context, groupCode string, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:route:%s", GroupVolumeKey(ctx, groupCode, rm), routeCode)
}

// 只按照fulfillment维度控制zone运力
func ProductZoneVolumeKey(ctx context.Context, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return ProductZoneVolumeKeyByDate(productID, zoneCode, direction, rm, timeutil.GetLocalTime(ctx))
}

func GroupZoneVolumeKey(ctx context.Context, groupCode, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return GroupZoneVolumeKeyByDate(groupCode, zoneCode, direction, rm, timeutil.GetLocalTime(ctx))
}

func ProductZoneVolumeKeyByDate(
	productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, date time.Time,
) string {
	return fmt.Sprintf("%s:zone:%s:%s", ProductVolumeKeyByDate(productID, rm, date), zoneCode, direction)
}

// SSCSMR-3055: 使用mask+fulfillment管理country维度运力
func MaskProductVolumeKey(ctx context.Context, maskProductID, productID int64, rm rule_mode.RuleMode) string {
	dateStr := timeutil.FormatTimeToVolumeDateString(timeutil.GetLocalTime(ctx))
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + dateStr + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + dateStr + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
}

func MaskProductVolumeKeyByDate(
	maskProductID, productID int64, rm rule_mode.RuleMode, date time.Time) string {
	dateStr := timeutil.FormatTimeToVolumeDateString(date)
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + dateStr + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + dateStr + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
}

// SSCSMR-3055: 使用mask+fulfillment管理zone维度运力
func MaskProductZoneVolumeKey(
	ctx context.Context, maskProductID, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode,
) string {
	prefix := MaskProductVolumeKey(ctx, maskProductID, productID, rm)
	return fmt.Sprintf("%s:zone:%s:%s", prefix, zoneCode, direction)
}

func MaskProductZoneVolumeKeyByDate(
	maskProductID, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, date time.Time,
) string {
	prefix := MaskProductVolumeKeyByDate(maskProductID, productID, rm, date)
	return fmt.Sprintf("%s:zone:%s:%s", prefix, zoneCode, direction)
}

// SSCSMR-3055: 使用mask+fulfillment管理route维度运力
func MaskProductRouteVolumeKey(ctx context.Context, maskProductID, productID int64, routeCode string, rm rule_mode.RuleMode) string {
	prefix := MaskProductVolumeKey(ctx, maskProductID, productID, rm)
	return fmt.Sprintf("%s:route:%s", prefix, routeCode)
}

func MaskProductRouteVolumeKeyByDate(ctx context.Context, maskProductID, productID int64, routeCode string, rm rule_mode.RuleMode, date time.Time) string {
	prefix := MaskProductVolumeKeyByDate(maskProductID, productID, rm, date)
	return fmt.Sprintf("%s:route:%s", prefix, routeCode)
}

func GroupZoneVolumeKeyByDate(
	groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, date time.Time,
) string {
	return fmt.Sprintf("%s:zone:%s:%s", GroupVolumeKeyByDate(groupCode, rm, date), zoneCode, direction)
}

func GroupRouteVolumeKeyByDate(
	groupCode string, routeCode string, rm rule_mode.RuleMode, date time.Time,
) string {
	return fmt.Sprintf("%s:route:%s", GroupVolumeKeyByDate(groupCode, rm, date), routeCode)
}

func BatchVolumesLockKey(ruleID int64) string {
	return LockPrefixAllocationRuleBatchVolumes + strconv.FormatInt(ruleID, 10)
}

func AddParcelTypeSuffix(key string, parcelType parcel_type_definition.ParcelType) string {
	if parcelType == parcel_type_definition.ParcelTypeNone {
		// None类型不需要加后缀
		return key
	}

	return fmt.Sprintf("%s:%s", key, parcelType)
}
