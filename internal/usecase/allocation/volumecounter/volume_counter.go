package volumecounter

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/redislock"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/inter/rlock"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	jsoniter "github.com/json-iterator/go"
)

type MaskVolumeCounterImpl struct {
	locker rlock.RedisLockClient
}

func NewMaskVolumeCounterImpl() *MaskVolumeCounterImpl {
	return &MaskVolumeCounterImpl{
		locker: rlock.New(),
	}
}

type MaskVolumeCounter interface {
	IncrMaskVolume(ctx context.Context, maskingProductID int64, productID int64, groupCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error
	IncrVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32) error
	GetProductVolume(ctx context.Context, maskProductID, id int64, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	GetGroupVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	GetProductVolumeInBatch(ctx context.Context, ruleID int64, productID int64, maskProductID int64) (int32, error)
	GetProductRouteVolume(ctx context.Context, maskProductID, id int64, routeCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	GetGroupRouteVolume(ctx context.Context, groupCode, routeCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	BatchGetProductRouteVolumes(ctx context.Context, requests []GetProductRouteVolumeRequest) ([]int32, error)
	GetProductZoneVolume(ctx context.Context, maskProductID, id int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	BatchGetProductZoneVolumes(ctx context.Context, requests []GetProductZoneVolumeRequest) ([]int32, error)
	GetGroupZoneVolume(ctx context.Context, groupCode, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error)
	IncrProductVolume(ctx context.Context, maskProductID, productID int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error
	DecrProductVolume(ctx context.Context, maskProductID, productID int64) error
	IncrRouteVolume(ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, rm rule_mode.RuleMode, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error
	IncrZoneVolume(ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error
	CheckAllocRuleBatchVolumes(ctx context.Context, ruleID int64, batchVolume int32) error
	UpdateRuleVolumesForTesting(ctx context.Context, ruleID int64, volumes map[int64]int32) error
	UpdateVolumesForTesting(ctx context.Context, volumes map[int64]int32, rm rule_mode.RuleMode) error

	MaskVolumeBatchAllocateCounter
}

type MaskVolumeBatchAllocateCounter interface {
	IncrByMaskVolumeByDate(ctx context.Context, maskingProductID int64, value int64, date time.Time) error
	IncrByProductVolumeByDate(ctx context.Context, maskProductID, productID int64, groupCode string, value int64, date time.Time, parcelType parcel_type_definition.ParcelType) error
	BatchIncrByZoneVolumeByDate(ctx context.Context, items []ZoneVolumeItem, date time.Time) error
	BatchIncrByRouteVolumeByDate(ctx context.Context, items []RouteVolumeItem, date time.Time) error
}

// ZoneVolumeItem 表示一个Zone单量更新项
type ZoneVolumeItem struct {
	MaskProductID        int64
	FulfillmentProductID int64
	GroupCode            string
	ZoneCode             string
	Direction            rulevolume.MaskZoneDirection
	RuleMode             rule_mode.RuleMode
	Value                int64
	ParcelType           parcel_type_definition.ParcelType
}

// RouteVolumeItem 表示一个Route单量更新项
type RouteVolumeItem struct {
	MaskProductID        int64
	FulfillmentProductID int64
	GroupCode            string
	RouteCode            string
	RuleMode             rule_mode.RuleMode
	Value                int64
	ParcelType           parcel_type_definition.ParcelType
}

func (v *MaskVolumeCounterImpl) DecrProductVolume(ctx context.Context, maskProductID, productID int64) error {
	// 更新 product 当天总单量
	if _, err := redisutil.GetDefaultInstance().Decr(ctx, ProductVolumeKey(ctx, productID, rule_mode.MplOrderRule)).Result(); err != nil {
		return fmt.Errorf("decr product volume: %v", err)
	}

	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		// 按mask+fulfillment维度扣减country运力
		if _, err := redisutil.GetDefaultInstance().Decr(ctx, MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule)).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "DecrProductVolume", fmt.Sprintf("decr product volume by new key:%s, err:%v", MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule), err))
			return fmt.Errorf("decr product volume: %v", err)
		}
		logger.CtxLogInfof(ctx, "DecrProductVolume|old key:%s, new key:%s", ProductVolumeKey(ctx, productID, rule_mode.MplOrderRule), MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule))
	}

	return nil
}

func (v *MaskVolumeCounterImpl) IncrProductVolume(
	ctx context.Context, maskProductID, productID int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	// 更新 product 当天总单量
	key := ProductVolumeKey(ctx, productID, rule_mode.MplOrderRule)
	if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
		return fmt.Errorf("incr product volume: %v", err)
	}
	if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
		return err
	}

	// 按mask+fulfillment维度增加country运力
	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		key = MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "IncrProductVolume", fmt.Sprintf("incr product volume by new key:%s, err:%v", MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule), err))
			return fmt.Errorf("incr product volume: %v", err)
		}
		if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
			return err
		}
		logger.CtxLogInfof(ctx, "IncrProductVolume|old key:%s, new key:%s", ProductVolumeKey(ctx, productID, rule_mode.MplOrderRule), MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule))
	}
	return nil
}

func (v *MaskVolumeCounterImpl) IncrGroupVolume(ctx context.Context, groupCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error {
	// 更新 group 当天总单量
	key := GroupVolumeKey(ctx, groupCode, rule_mode.MplOrderRule)
	if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
		return fmt.Errorf("incr group volume: %v", err)
	}

	if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
		return err
	}

	return nil
}

// batch allocate or async single
func (v *MaskVolumeCounterImpl) IncrByProductVolumeByDate(
	ctx context.Context, maskProductID, productID int64, groupCode string, value int64, date time.Time, parcelType parcel_type_definition.ParcelType) error {
	// 更新 product 当天总单量
	key := ProductVolumeKeyByDate(productID, rule_mode.MplOrderRule, date)
	key = AddParcelTypeSuffix(key, parcelType)
	if _, err := redisutil.GetDefaultInstance().IncrBy(ctx, key, value).Result(); err != nil {
		return fmt.Errorf("incr product volume: %v", err)
	}

	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		// 按mask+fulfillment维度增加country运力
		key = MaskProductVolumeKeyByDate(maskProductID, productID, rule_mode.MplOrderRule, date)
		key = AddParcelTypeSuffix(key, parcelType)
		if _, err := redisutil.GetDefaultInstance().IncrBy(ctx, key, value).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "IncrByProductVolumeByDate", fmt.Sprintf("incr product volume by date by new key:%s, err:%v", MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule), err))
			return fmt.Errorf("incr product volume: %v", err)
		}
		logger.CtxLogInfof(ctx, "IncrByProductVolumeByDate|old key:%s, new key:%s", ProductVolumeKeyByDate(productID, rule_mode.MplOrderRule, date), MaskProductVolumeKey(ctx, maskProductID, productID, rule_mode.MplOrderRule))
	}

	if groupCode != "" {
		groupKey := GroupVolumeKeyByDate(groupCode, rule_mode.MplOrderRule, date)
		groupKey = AddParcelTypeSuffix(groupKey, parcelType)
		if _, err := redisutil.GetDefaultInstance().IncrBy(ctx, groupKey, value).Result(); err != nil {
			return fmt.Errorf("incr group volume: %v", err)
		}
	}

	return nil
}

func (v *MaskVolumeCounterImpl) IncrRouteVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, rm rule_mode.RuleMode,
	parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	key := ProductRouteVolumeKey(ctx, productID, routeCode, rm)
	if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
		return fmt.Errorf("incr product route volume: %v", err)
	}
	if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
		return err
	}

	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		// 按mask+fulfillment维度增加route运力
		key = MaskProductRouteVolumeKey(ctx, maskProductID, productID, routeCode, rm)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "IncrRouteVolume", fmt.Sprintf("incr route volume by new key:%s, err:%v", MaskProductRouteVolumeKey(ctx, maskProductID, productID, routeCode, rm), err))
			return fmt.Errorf("incr product route volume: %v", err)
		}
		if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
			return err
		}
		logger.CtxLogInfof(ctx, "IncrRouteVolume|old key:%s, new key:%s", ProductRouteVolumeKey(ctx, productID, routeCode, rm), MaskProductRouteVolumeKey(ctx, maskProductID, productID, routeCode, rm))
	}

	if groupCode != "" {
		key = GroupRouteVolumeKey(ctx, groupCode, routeCode, rm)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
			return fmt.Errorf("incr group route volume: %v", err)
		}
		if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
			return err
		}
	}

	return nil
}

func (v *MaskVolumeCounterImpl) IncrZoneVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string,
	direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	key := ProductZoneVolumeKey(ctx, productID, zoneCode, direction, rm)
	if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
		return fmt.Errorf("incr product zone volume: %v", err)
	}
	if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
		return err
	}

	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		// 按mask+fulfillment维度增加zone运力
		key = MaskProductZoneVolumeKey(ctx, maskProductID, productID, zoneCode, direction, rm)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "IncrZoneVolume", fmt.Sprintf("incr zone volume by new key:%s, err:%v", MaskProductZoneVolumeKey(ctx, maskProductID, productID, zoneCode, direction, rm), err))
			return fmt.Errorf("incr product route volume: %v", err)
		}
		if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
			return err
		}
		logger.CtxLogInfof(ctx, "IncrZoneVolume|old key:%s, new key:%s", ProductZoneVolumeKey(ctx, productID, zoneCode, direction, rm), MaskProductZoneVolumeKey(ctx, maskProductID, productID, zoneCode, direction, rm))
	}

	if groupCode != "" {
		key = GroupZoneVolumeKey(ctx, groupCode, zoneCode, direction, rm)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, key).Result(); err != nil {
			return fmt.Errorf("incr group zone volume: %v", err)
		}
		if err := v.IncrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr); err != nil {
			return err
		}
	}

	return nil
}

func (v *MaskVolumeCounterImpl) IncrMaskVolume(
	ctx context.Context, maskingProductID int64, productID int64, groupCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	// 1. 更新 maskProduct 当天总单量
	if _, err := redisutil.GetDefaultInstance().Incr(ctx, ProductMaskVolumeKey(ctx, maskingProductID)).Result(); err != nil {
		return fmt.Errorf("incr mask_product volume: %v", err)
	}
	// 2. 更新 product 当天总单量
	if err := v.IncrProductVolume(ctx, maskingProductID, productID, parcelTypeAttr); err != nil {
		return fmt.Errorf("incr product volume: %v", err)
	}

	if groupCode != "" {
		// 3. 更新 group 当天总单量
		if err := v.IncrGroupVolume(ctx, groupCode, parcelTypeAttr); err != nil {
			return fmt.Errorf("incr group volume: %v", err)
		}
	}

	return nil
}

func (v *MaskVolumeCounterImpl) IncrByMaskVolumeByDate(
	ctx context.Context, maskingProductID int64, value int64, date time.Time) error {
	if _, err := redisutil.GetDefaultInstance().IncrBy(ctx, ProductMaskVolumeKeyByDate(maskingProductID, date), value).Result(); err != nil {
		return fmt.Errorf("incr mask_product volume: %v", err)
	}
	return nil
}

func (v *MaskVolumeCounterImpl) GetProductVolume(
	ctx context.Context, maskProductID, id int64, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType,
) (int32, error) {
	key := ""
	//切换开关后，使用mask+fulfillment获取运力
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = MaskProductVolumeKey(ctx, maskProductID, id, rm)
	} else {
		key = ProductVolumeKey(ctx, id, rm)
	}
	logger.CtxLogInfof(ctx, "use mask prefix:%v, get product volume, key:%v", configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix, key)
	// 开关未切换，继续按老流向，在fulfillment product下去获取运力
	key = AddParcelTypeSuffix(key, parcelType)
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}
	return int32(v1), nil
}

func (v *MaskVolumeCounterImpl) GetGroupVolume(
	ctx context.Context, groupCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType,
) (int32, error) {
	key := GroupVolumeKey(ctx, groupCode, rm)
	key = AddParcelTypeSuffix(key, parcelType)
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}
	return int32(v1), nil
}

func (v *MaskVolumeCounterImpl) GetProductZoneVolume(
	ctx context.Context, maskProductID, id int64, zoneCode string, direction rulevolume.MaskZoneDirection,
	rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	key := ""
	//切换开关后，使用mask+fulfillment获取运力
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = MaskProductZoneVolumeKey(ctx, maskProductID, id, zoneCode, direction, rm)
	} else {
		key = ProductZoneVolumeKey(ctx, id, zoneCode, direction, rm)
	}
	key = AddParcelTypeSuffix(key, parcelType)
	logger.CtxLogInfof(ctx, "use mask prefix:%v, get product volume, key:%v", configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix, key)
	// 开关未切换，继续按老流向，在fulfillment product下去获取运力
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}
	return int32(v1), nil
}

// BatchGetProductZoneVolumes 获取Product Zone运力
func (v *MaskVolumeCounterImpl) BatchGetProductZoneVolumes(ctx context.Context, requests []GetProductZoneVolumeRequest) ([]int32, error) {
	keys := make([]string, len(requests))
	for i, req := range requests {
		key := MaskProductZoneVolumeKey(ctx, req.MaskProductID, req.FulfillmentProductID, req.ZoneCode, req.Direction, req.RuleMode)
		if req.GroupCode != "" {
			// 如果有对应的group，则按group维度获取zone运力
			key = GroupZoneVolumeKey(ctx, req.GroupCode, req.ZoneCode, req.Direction, req.RuleMode)
		}
		key = AddParcelTypeSuffix(key, req.ParcelType)
		keys[i] = key
	}

	results, err := v.mgetVolumesByKeys(ctx, keys)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// BatchGetProductZoneVolumes 获取Product Route运力
func (v *MaskVolumeCounterImpl) BatchGetProductRouteVolumes(ctx context.Context, requests []GetProductRouteVolumeRequest) ([]int32, error) {
	keys := make([]string, len(requests))
	for i, req := range requests {
		key := MaskProductRouteVolumeKey(ctx, req.MaskProductID, req.FulfillmentProductID, req.RouteCode, req.RuleMode)
		if req.GroupCode != "" {
			// 如果有对应的group，则按group维度获取zone运力
			key = GroupRouteVolumeKey(ctx, req.GroupCode, req.RouteCode, req.RuleMode)
		}
		key = AddParcelTypeSuffix(key, req.ParcelType)
		keys[i] = key
	}

	results, err := v.mgetVolumesByKeys(ctx, keys)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// mgetVolumesByKeys 分批次地批量获取redis key对应的value
func (v *MaskVolumeCounterImpl) mgetVolumesByKeys(ctx context.Context, keys []string) ([]int32, error) {
	const batchSize = 1000
	var results = make([]int32, 0, len(keys))

	for start := 0; start < len(keys); start += batchSize {
		end := start + batchSize
		if end > len(keys) {
			end = len(keys)
		}

		// 存储当前批次的Key
		keys := keys[start:end]

		// 执行MGET命令
		values, err := redisutil.GetDefaultInstance().MGet(ctx, keys...).Result()
		if err != nil {
			return nil, fmt.Errorf("use MGET command get product zone/route volume failed: %v", err)
		}

		// 处理结果
		for _, value := range values {
			valueStr, ok := value.(string)
			if !ok || valueStr == "" {
				// Value为Nil或空字符串，则返回0, 正常情况
				results = append(results, 0)
				continue
			}
			valueInt, err := strconv.Atoi(valueStr)
			if err != nil {
				errMsg := fmt.Sprintf("convert string to int failed|value=%v|err=%v", value, err)
				logger.CtxLogErrorf(ctx, errMsg)
				monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.InvalidProductZoneRouteVolumeValue, errMsg)
				results = append(results, 0)
				continue
			}
			results = append(results, int32(valueInt))
		}
	}

	return results, nil
}

func (v *MaskVolumeCounterImpl) GetGroupZoneVolume(
	ctx context.Context, groupCode, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode,
	parcelType parcel_type_definition.ParcelType) (int32, error) {
	key := GroupZoneVolumeKey(ctx, groupCode, zoneCode, direction, rm)
	key = AddParcelTypeSuffix(key, parcelType)
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}
	return int32(v1), nil
}

func (v *MaskVolumeCounterImpl) GetProductRouteVolume(
	ctx context.Context, maskProductID, id int64, routeCode string, rm rule_mode.RuleMode,
	parcelType parcel_type_definition.ParcelType) (int32, error) {
	key := ""
	//切换开关后，使用mask+fulfillment获取运力
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = MaskProductRouteVolumeKey(ctx, maskProductID, id, routeCode, rm)
	} else {
		key = ProductRouteVolumeKey(ctx, id, routeCode, rm)
	}
	key = AddParcelTypeSuffix(key, parcelType)
	logger.CtxLogInfof(ctx, "use mask prefix:%v, get product volume, key:%v", configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix, key)
	// 开关未切换，继续按老流向，在fulfillment product下去获取运力
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}
	return int32(v1), nil
}

func (v *MaskVolumeCounterImpl) GetGroupRouteVolume(
	ctx context.Context, groupCode, routeCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType,
) (int32, error) {
	key := GroupRouteVolumeKey(ctx, groupCode, routeCode, rm)
	key = AddParcelTypeSuffix(key, parcelType)
	v1, err := redisutil.GetDefaultInstance().Get(ctx, key).Int()
	if err != nil {
		return 0, err
	}

	return int32(v1), nil
}

func (v *MaskVolumeCounterImpl) GetProductVolumeInBatch(ctx context.Context, ruleID int64, productID int64, maskProductID int64) (int32, error) {
	v1, err := redisutil.GetDefaultInstance().HGet(ctx, AllocationRuleVolumesKey(ctx, ruleID), strconv.FormatInt(productID, 10)).Int()
	batchID, gErr := redisutil.GetDefaultInstance().Get(ctx, fmt.Sprintf("%s:%d", KeySlsBatchId, maskProductID)).Int()
	slsRuleId, gErr2 := redisutil.GetDefaultInstance().Get(ctx, fmt.Sprintf("%s:%d", KeySlsRuleId, maskProductID)).Int()
	//获取batch/rule id 失败，只返回lps的volume
	if gErr != nil || gErr2 != nil {
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "fail to get smart-routing's batch id|err=%v", gErr)

		}
		if gErr2 != nil {
			logger.CtxLogErrorf(ctx, "fail to get smart-routing's rule id|err=%v", gErr2)
		}
		if err == redis.Nil {
			return 0, nil
		}
		return int32(v1), err
	}
	v2, err2 := redisutil.GetDefaultInstance().Get(ctx, SmartRoutingBatchVolumeKey(ctx, slsRuleId, int(productID), batchID)).Int()
	if err != nil && err2 == nil {
		return 0, nil
	}
	if err != nil {
		return int32(v2), err2
	}
	if err2 != nil {
		return int32(v1), err
	}
	v3 := v1 + v2
	return int32(v3), err2
}

func (v *MaskVolumeCounterImpl) IncrVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32) error {
	if batchVolume == 0 {
		// if batch volume == 0 that mean rule has not enable batch volume routing factor, so no need to update batch volume counter
		return nil
	}

	// 更新规则内的product单量
	if _, err := redisutil.GetDefaultInstance().HIncrBy(ctx, AllocationRuleVolumesKey(ctx, ruleID), strconv.FormatInt(productID, 10), 1).Result(); err != nil {
		return fmt.Errorf("incr product in batch_volume: %v", err)
	}

	checkMsg, err := jsoniter.Marshal(AllocateCheckBatchVolumeJobMsg{
		RuleID:      ruleID,
		BatchVolume: batchVolume,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "Marshal check msg failed|err=%v", err)
		return err
	}
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	newCtx := ctxhelper.CloneTrace(ctx)
	go func(ctx context.Context, namespace string, checkMsg []byte) {
		if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameAllocateCheckBatchVolumeCounter, checkMsg, nil, ""); err != nil {
			logger.CtxLogErrorf(ctx, "SendMessage failed|err=%v", err)
		}
	}(newCtx, namespace, checkMsg)

	return nil
}

func (v *MaskVolumeCounterImpl) CheckAllocRuleBatchVolumes(ctx context.Context, ruleID int64, batchVolume int32) error {
	overflow, err := v.isBatchOverflow(ctx, ruleID, batchVolume)
	if err != nil {
		return err
	}
	if !overflow {
		return nil
	}

	return v.updateBatch(ctx, ruleID, batchVolume)
}

func (v *MaskVolumeCounterImpl) isBatchOverflow(ctx context.Context, ruleID int64, batchVolume int32) (bool, error) {
	key := AllocationRuleVolumesKey(ctx, ruleID)
	values, err := redisutil.GetDefaultInstance().HVals(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("get batch volumes from redis: %w", err)
	}
	var total int32
	for _, val := range values {
		volume, err := strconv.Atoi(val)
		if err != nil {
			return false, fmt.Errorf("parse volume value %v: %w", val, err)
		}
		total += int32(volume)
	}
	return total >= batchVolume, nil

}

func (v *MaskVolumeCounterImpl) updateBatch(ctx context.Context, ruleID int64, batchVolume int32) error {
	lock, err := v.locker.Obtain(ctx, BatchVolumesLockKey(ruleID), 400*time.Millisecond, &redislock.Options{
		RetryStrategy: redislock.LinearBackoff(200 * time.Millisecond),
	})
	if err != nil {
		return err
	}
	defer func() {
		if err := lock.Release(ctx); err != nil {
			logger.CtxLogErrorf(ctx, "release lock fail|err=%+v", err)
		}
	}()
	overflow, err := v.isBatchOverflow(ctx, ruleID, batchVolume)
	if err != nil {
		return err
	}
	if !overflow {
		return nil
	}
	key := AllocationRuleVolumesKey(ctx, ruleID)
	if err := redisutil.GetDefaultInstance().Del(ctx, key); err != nil {
		return fmt.Errorf("delete volumes: %v", err)
	}
	return nil
}

func (v *MaskVolumeCounterImpl) UpdateRuleVolumesForTesting(ctx context.Context, ruleID int64, volumes map[int64]int32) error {
	// note: 仅供测试
	for productID, volume := range volumes {
		err := redisutil.GetDefaultInstance().HSet(ctx, AllocationRuleVolumesKey(ctx, ruleID), strconv.FormatInt(productID, 10), volume).Err()
		if err != nil {
			return err
		}
	}
	return nil
}

func (v *MaskVolumeCounterImpl) UpdateVolumesForTesting(ctx context.Context, volumes map[int64]int32, rm rule_mode.RuleMode) error {
	// note: 仅供测试
	for productID, volume := range volumes {
		err := redisutil.GetDefaultInstance().Set(ctx, ProductVolumeKey(ctx, productID, rm), volume, 0).Err()
		if err != nil {
			return err
		}
	}
	return nil
}

// IncrParcelTypeVolumeByKey 判断订单属性增加对应ParcelType的单量
func (v *MaskVolumeCounterImpl) IncrParcelTypeVolumeByKey(ctx context.Context, key string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error {
	if parcelTypeAttr.IsCod {
		codKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeCod)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, codKey).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatParcelTypeVolume, monitoring.COD, err.Error())
			return fmt.Errorf("incr cod volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.COD, codKey)
	}
	if parcelTypeAttr.IsBulky {
		bulkyKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeBulky)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, bulkyKey).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatParcelTypeVolume, monitoring.Bulky, err.Error())
			return fmt.Errorf("incr bulky volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.Bulky, bulkyKey)
	}
	if parcelTypeAttr.IsHighValue {
		highValueKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeHighValue)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, highValueKey).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatParcelTypeVolume, monitoring.HighValue, err.Error())
			return fmt.Errorf("incr high value volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.HighValue, highValueKey)
	}
	if parcelTypeAttr.IsDg {
		dgKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeDg)
		if _, err := redisutil.GetDefaultInstance().Incr(ctx, dgKey).Result(); err != nil {
			monitoring.ReportError(ctx, monitoring.CatParcelTypeVolume, monitoring.DG, err.Error())
			return fmt.Errorf("incr dg volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.DG, dgKey)
	}
	return nil
}

// batchIncrVolumeByDate 是一个通用的批量运力更新函数，用于处理Zone和Route的批量运力更新
// itemProcessor 是一个函数，用于处理每个item并生成相应的key和value
func (v *MaskVolumeCounterImpl) batchIncrVolumeWithPipeline(
	ctx context.Context,
	volumeName string, // only for log
	itemCount int,
	itemProcessor func(pipe redis.Pipeliner, index int) error,
) error {
	if itemCount == 0 {
		return nil
	}

	const batchSize = 1000 // 每个pipeline最多处理1000个item

	// 分批处理items
	for i := 0; i < itemCount; i += batchSize {
		// 计算当前批次的结束索引
		end := i + batchSize
		if end > itemCount {
			end = itemCount
		}

		// 使用Redis Pipeline批量处理当前批次
		pipe := redisutil.GetDefaultInstance().Pipeline()

		// 处理当前批次的每个item
		for j := i; j < end; j++ {
			if err := itemProcessor(pipe, j); err != nil {
				return fmt.Errorf("process %s item error at index %d: %v", volumeName, j, err)
			}
		}

		// 执行Pipeline
		cmds, err := pipe.Exec(ctx)
		if err != nil {
			return fmt.Errorf("batch incr %s volume error (batch %d-%d): %v", volumeName, i, end-1, err)
		}

		// 检查命令执行结果
		for _, cmd := range cmds {
			if cmd.Err() != nil {
				logger.CtxLogErrorf(ctx, "pipeline command error: %v", cmd.Err())
			}
		}
	}

	return nil
}

// BatchIncrByZoneVolumeByDate 批量更新Zone单量，使用Redis Pipeline优化性能
func (v *MaskVolumeCounterImpl) BatchIncrByZoneVolumeByDate(
	ctx context.Context, items []ZoneVolumeItem, date time.Time,
) error {
	return v.batchIncrVolumeWithPipeline(ctx, "zone", len(items), func(pipe redis.Pipeliner, index int) error {
		item := items[index]

		// Fulfillment Product维度
		key := MaskProductZoneVolumeKeyByDate(item.MaskProductID, item.FulfillmentProductID, item.ZoneCode, item.Direction, item.RuleMode, date)
		key = AddParcelTypeSuffix(key, item.ParcelType)
		pipe.IncrBy(ctx, key, item.Value)

		// Group维度（如果有）
		if item.GroupCode != "" {
			groupKey := GroupZoneVolumeKeyByDate(item.GroupCode, item.ZoneCode, item.Direction, item.RuleMode, date)
			groupKey = AddParcelTypeSuffix(groupKey, item.ParcelType)
			pipe.IncrBy(ctx, groupKey, item.Value)
		}

		return nil
	})
}

// BatchIncrByRouteVolumeByDate 批量更新Route单量，使用Redis Pipeline优化性能
func (v *MaskVolumeCounterImpl) BatchIncrByRouteVolumeByDate(
	ctx context.Context, items []RouteVolumeItem, date time.Time,
) error {
	return v.batchIncrVolumeWithPipeline(ctx, "route", len(items), func(pipe redis.Pipeliner, index int) error {
		item := items[index]

		// Fulfillment Product维度
		key := MaskProductRouteVolumeKeyByDate(ctx, item.MaskProductID, item.FulfillmentProductID, item.RouteCode, item.RuleMode, date)
		key = AddParcelTypeSuffix(key, item.ParcelType)
		pipe.IncrBy(ctx, key, item.Value)

		// Group维度（如果有）
		if item.GroupCode != "" {
			groupKey := GroupRouteVolumeKeyByDate(item.GroupCode, item.RouteCode, item.RuleMode, date)
			groupKey = AddParcelTypeSuffix(groupKey, item.ParcelType)
			pipe.IncrBy(ctx, groupKey, item.Value)
		}

		return nil
	})
}
