package volume_counter_service

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
)

type VolumeCounterServiceInterface interface {
	RoutingVolumeCounter(ctx context.Context, req *pb.VolumeInfo) *srerr.Error
	CheckVolumeDuplicateCounter(ctx context.Context, updateVolumeScene pb.UpdateVolumeScene, volumeType pb.UpdateVolumeType, slsTn string) (bool, *srerr.Error)
	SetVolumeDuplicateCounter(ctx context.Context, updateVolumeScene pb.UpdateVolumeScene, volumeType pb.UpdateVolumeType, slsTn string) *srerr.Error
}

type VolumeCounterService struct {
	ZoneRuleRepo     vrrepo.ZoneRuleRepo
	ZoneGroupRepo    vrrepo.ZoneGroupRepo
	ZoneRepo         vrrepo.ZoneRepo
	VolumeCounter    volume_counter.VolumeCounter
	RoutingConfig    routing_config.RoutingConfigService
	LocationZoneRepo locationzone.ZoneRepo
	LaneSrv          lane.LaneService
}

func NewVolumeCounterService(zoneRuleRepo vrrepo.ZoneRuleRepo, zoneGroupRepo vrrepo.ZoneGroupRepo, volumeCounter volume_counter.VolumeCounter,
	zoneRepo vrrepo.ZoneRepo, routingConfig routing_config.RoutingConfigService,
	locationZoneRepo locationzone.ZoneRepo, laneSrv lane.LaneService) *VolumeCounterService {
	return &VolumeCounterService{
		ZoneRuleRepo:     zoneRuleRepo,
		ZoneGroupRepo:    zoneGroupRepo,
		ZoneRepo:         zoneRepo,
		VolumeCounter:    volumeCounter,
		RoutingConfig:    routingConfig,
		LocationZoneRepo: locationZoneRepo,
		LaneSrv:          laneSrv,
	}
}

// CheckVolumeDuplicateCounter 运力计数时，以slsTn和volumeType做幂等性判断
func (p *VolumeCounterService) CheckVolumeDuplicateCounter(ctx context.Context, updateVolumeScene pb.UpdateVolumeScene, volumeType pb.UpdateVolumeType, slsTn string) (bool, *srerr.Error) {
	rds, err := redisutil.Client()
	if err != nil {
		return false, srerr.With(srerr.RedisErr, fmt.Sprintf("CheckVolumeDuplicateCounter|get redisClient|volumeType:%v, slsTn:%v", volumeType, slsTn), err)
	}
	val, err := rds.Get(ctx, formatVolumeDuplicateCounterKey(updateVolumeScene, volumeType, slsTn)).Result()
	if err != nil && err != redis.Nil {
		return false, srerr.With(srerr.RedisErr, fmt.Sprintf("CheckVolumeDuplicateCounter|volumeType:%v, slsTn:%v", volumeType, slsTn), err)
	}
	return val != "", nil
}

func (p *VolumeCounterService) RoutingVolumeCounter(ctx context.Context, req *pb.VolumeInfo) *srerr.Error {
	routingConfig, err := p.RoutingConfig.GetRoutingConfigCacheByProductID(ctx, int(req.GetProductId()))
	if err != nil {
		return err
	}
	routingType := routingConfig.GetRoutingType()
	lineParcelMap := p.GetLineParcelSnapshot(ctx, req.GetForderId(), req.GetLineIdList(), routingType)
	if err = p.RoutingLineVolumeCounter(ctx, req, routingType, lineParcelMap); err != nil {
		return err
	}
	// 保证和老逻辑一致
	return p.RoutingZoneVolumeCounter(ctx, req, lineParcelMap, int(routingType))
}

func (p *VolumeCounterService) SetVolumeDuplicateCounter(ctx context.Context, updateVolumeScene pb.UpdateVolumeScene, volumeType pb.UpdateVolumeType, slsTn string) *srerr.Error {
	rds, err := redisutil.Client()
	if err != nil {
		return srerr.With(srerr.RedisErr, fmt.Sprintf("SetVolumeDuplicateCounter|get redisClient|volumeType:%v, slsTn:%v", volumeType, slsTn), err)
	}
	_, err = rds.Set(ctx, formatVolumeDuplicateCounterKey(updateVolumeScene, volumeType, slsTn), 1, constant.CheckOrderKeyExpire).Result()
	if err != nil {
		return srerr.With(srerr.RedisErr, fmt.Sprintf("SetVolumeDuplicateCounter|volumeType:%v, slsTn:%v", volumeType, slsTn), err)
	}
	return nil
}

func (p *VolumeCounterService) GetLineParcelSnapshot(ctx context.Context, forderId string, lineIdList []string, routingType uint8) map[string]*parcel_type_definition.ParcelTypeAttr {
	lineParcelMap := make(map[string]*parcel_type_definition.ParcelTypeAttr)
	if routingType == rule.UnknownRoutingType {
		return lineParcelMap
	}

	value, err := redisutil.GetString(ctx, forderId)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.LineParcelGetError, fmt.Sprintf("error:%v", err))
		return lineParcelMap
	}
	if unmarshalErr := json.Unmarshal([]byte(value), &lineParcelMap); unmarshalErr != nil {
		monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.LineParcelFormatError, fmt.Sprintf("error:%v", unmarshalErr))
		return lineParcelMap
	}
	if delErr := redisutil.Del(ctx, forderId); delErr != nil {
		monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.LineParcelDelError, fmt.Sprintf("error:%v", delErr))
	}

	for _, lineId := range lineIdList {
		if _, ok := lineParcelMap[lineId]; !ok {
			monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.LineParcelNotFoundError, fmt.Sprintf("lineId:%v, forderId:%v", lineId, forderId))
		}
	}
	return lineParcelMap
}

func (p *VolumeCounterService) RoutingLineVolumeCounter(ctx context.Context, req *pb.VolumeInfo, routingType uint8, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	// 因为SPX走的老逻辑是单纯Line的单量，而新逻辑Product:Line维度的单量，所以是SPX的话Line的ProductID全部设置为0
	// 在SPX改成新逻辑时要改成统一使用request中的ProductID
	var productIDForLineVolume int
	if routingType == rule.SPXRoutingType {
		productIDForLineVolume = 0
	} else {
		productIDForLineVolume = int(req.GetProductId())
	}

	productZoneCodes, err := p.LocationZoneRepo.GetZoneCodeListFromCache(ctx, productIDForLineVolume, routingType, locationzone.RunningZoneType)
	if err != nil {
		return err
	}
	zoneCode := p.LocationZoneRepo.MatchDistrictToZone(ctx, productIDForLineVolume, common.Uint32ToUntList(req.DeliverLocationIdList), productZoneCodes, routingType, locationzone.RunningZoneType, req.GetDeliveryPostcode())

	if err = p.VolumeCounter.UpdateProductVolume(ctx, req.GetProductId(), req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
		return err
	}

	for _, lineID := range req.LineIdList {
		if err = p.VolumeCounter.UpdateLineVolume(ctx, int64(productIDForLineVolume), lineID, req.GetSloCreateTime(), lineParcelMap[lineID], req.GetUpdateVolumeType()); err != nil {
			return err
		}
		if zoneCode != "" {
			if err = p.VolumeCounter.UpdateLineZoneVolume(ctx, int64(productIDForLineVolume), lineID, zoneCode, req.GetSloCreateTime(), lineParcelMap[lineID], req.GetUpdateVolumeType()); err != nil {
				return err
			}
		}
	}

	if len(req.GetLaneCodeGroup()) == 0 {
		if err = p.VolumeCounter.UpdateLaneFieldVolume(ctx, req.GetProductId(), req.GetLaneCode(), int(req.GetDgFlag()), req.GetServiceCode(), req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
			return err
		}
		if zoneCode != "" {
			if err = p.VolumeCounter.UpdateLaneFieldZoneVolume(ctx, req.GetProductId(), req.GetLaneCode(), int(req.GetDgFlag()), req.GetServiceCode(), zoneCode, req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
				return err
			}
		}
		if err = p.VolumeCounter.UpdateLaneVolume(ctx, req.GetProductId(), req.GetLaneCode(), req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
			return err
		}
	} else {
		pointKey := getActualPointKey(req.ActualPointList)
		if err = p.VolumeCounter.UpdateMultiLaneFieldVolume(ctx, req.GetProductId(), req.GetLaneCodeGroup(), int(req.GetDgFlag()), pointKey, req.GetServiceCode(), req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
			return err
		}
		if zoneCode != "" {
			if err = p.VolumeCounter.UpdateMultiLaneFieldZoneVolume(ctx, req.GetProductId(), req.GetLaneCodeGroup(), int(req.GetDgFlag()), pointKey, req.GetServiceCode(), zoneCode, req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
				return err
			}
		}
		for _, laneCode := range req.GetLaneCodeGroup() {
			if err = p.VolumeCounter.UpdateLaneVolume(ctx, req.GetProductId(), laneCode, req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
				return err
			}
		}
	}

	// 上报actual point单量
	err = p.VolumeCounter.UpdateActualPointVolume(ctx, req.GetProductId(), req.GetLaneCode(), req.GetLineIdList(), req.GetActualPointList(), req.GetSloCreateTime(), req.GetUpdateVolumeType())
	if err != nil {
		logger.CtxLogErrorf(ctx, "UpdateActualPointVolume error|err=%v", err)
		monitoring.ReportError(ctx, monitoring.CatUpdateVolumeApi, monitoring.UpdateActualPointVolumeError, fmt.Sprintf("update actual point volume error, err=%v", err))
	}

	// dg group id is not empty when product is multi product
	if req.GetDgGroupId() != "" {
		if err = p.VolumeCounter.UpdateMultiProductServiceCodeVolume(ctx, req.GetProductId(), req.GetServiceCode(), req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
			return err
		}
	}

	// 上报routing运力看板运力，上报失败不能影响主流程
	err = p.reportDashboardVolume(ctx, req, zoneCode, req.GetSloCreateTime(), req.GetUpdateVolumeType())
	if err != nil {
		logger.CtxLogErrorf(ctx, "reportDashboardVolume error|err=%v", err)
		monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ReportDashboardVolumeError, fmt.Sprintf("report routing dashboard volume error, err=%v", err))
	}

	return nil
}

func (p *VolumeCounterService) RoutingZoneVolumeCounter(ctx context.Context, req *pb.VolumeInfo, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr, routingType int) *srerr.Error {
	//1.counter-lines
	date := timeutil.FormatDate(timeutil.GetLocalTime(ctx))
	if err := p.VolumeCounter.UpdateVolumeLineDimension(ctx, req.GetProductId(), req.GetLineIdList(), date, req.GetSloCreateTime(), req.GetUpdateVolumeType(), lineParcelMap); err != nil {
		logger.CtxLogErrorf(ctx, "IncrVolumeLineDimension %v", err)
	}
	// v2运力只有lm会配置zoneCode，故只会有一个zoneName不为空的数据，routing运力看板上报只需要上报不为空的zoneName即可
	var zoneCode string
	defer func() {
		// 上报routing运力看板运力，上报失败不能影响主流程
		if err := p.reportZoneDashboardVolume(ctx, req, zoneCode, req.GetSloCreateTime(), req.GetUpdateVolumeType()); err != nil {
			logger.CtxLogErrorf(ctx, "reportDashboardVolume error|err=%v", err)
			monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ReportDashboardVolumeError, fmt.Sprintf("report routing dashboard volume error, err=%v", err))
		}
	}()

	//1.product-id find rule---
	r, err := p.ZoneRuleRepo.GetRuleByProduct(ctx, req.GetProductId(), routingType, constant.NonForecastType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "not.add.zone.counter.GetRuleByProduct err=%v", err)
		return err
	}
	if r == nil {
		logger.CtxLogErrorf(ctx, "not.add.zone.counter.GetRuleByProduct r==nil ")
		return nil
	}
	if r.RuleType == enum.VolumeRuleTypeProduct {
		logger.CtxLogInfof(ctx, "not.add.zone.counter.rule_type product")
		return nil
	}

	lineGroupMap := make(map[string]string)
	zoneLimits := r.VolumeZoneLimits
	for i := 0; i < len(zoneLimits); i++ {
		if _, ok := lineGroupMap[zoneLimits[i].LineId]; !ok {
			if groupId, _ := p.ZoneGroupRepo.GetGroupIdByLineWithCache(ctx, zoneLimits[i].LineId, r.RoutingType, constant.NonForecastType); groupId != "" {
				lineGroupMap[zoneLimits[i].LineId] = groupId
			}
		}
	}

	//in new page ,fl wont bind a group ,so will set a default limit
	postcode := common.ReshapePostcode(req.GetDeliveryPostcode())
	for _, lineId := range req.GetLineIdList() {
		groupId, ok := lineGroupMap[lineId]
		if !ok {
			logger.CtxLogInfof(ctx, "line_id=%v not bind group", lineId)
			continue
		}
		groupInfo, _ := p.ZoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, groupId, r.RoutingType, constant.NonForecastType)
		if groupInfo == nil {
			logger.CtxLogInfof(ctx, "line_id=%v group_id=%v get group_info err", lineId, groupId)
			continue
		}
		zoneNameList := p.getZoneName(ctx, groupInfo, req.GetDeliverLocationIdList(), postcode)
		zoneNameList = objutil.RemoveDuplicatedStrings(zoneNameList) // 这里对zone code去重，可能存在深圳市南山区->z1,南山区->z1
		for _, zoneName := range zoneNameList {
			logger.CtxLogInfof(ctx, "line_id=%v, zone_name=%v, group_id=%v,  g_type=%v, postcode=%v", lineId, zoneName, groupInfo.GroupId, enum.ZoneTypeNameMap[groupInfo.ZoneType], postcode)
			_ = p.VolumeCounter.UpdateVolumeZoneDimension(ctx, req.GetProductId(), lineId, date, groupId, zoneName, lineParcelMap[lineId], req.GetSloCreateTime(), req.GetUpdateVolumeType())
			zoneCode = zoneName
		}
	}

	//3.counter zones
	return nil
}

func (p *VolumeCounterService) getZoneName(ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, deliverLocationIdList []uint32, postcode string) []string {
	var zoneNameList []string
	switch groupInfo.ZoneType {
	case enum.ZoneTypeLocation:
		if len(deliverLocationIdList) != 0 {
			zoneNameList, _ = p.ZoneRepo.GetLocationZoneName(ctx, groupInfo.GroupId, groupInfo.RoutingType, groupInfo.IsForecastType, objutil.Uint32ToInt64(deliverLocationIdList))
		}
	case enum.ZoneTypePostcode:
		zoneName, _ := p.ZoneRepo.GetPostcodeZoneName(ctx, groupInfo.GroupId, postcode, groupInfo.RoutingType, groupInfo.IsForecastType)
		zoneNameList = []string{zoneName}
	default:
		zoneName, _ := p.ZoneRepo.GetCepRangeZoneName(ctx, groupInfo.GroupId, postcode, groupInfo.RoutingType, groupInfo.IsForecastType)
		zoneNameList = []string{zoneName}
	}
	return zoneNameList
}

func (p *VolumeCounterService) reportDashboardVolume(ctx context.Context, req *pb.VolumeInfo, zoneCode string, sloCrateTime int64, updateVolumeType pb.UpdateVolumeType) *srerr.Error {
	// routing运力看板单量上报，上报失败不能影响主流程
	fmLine, mmLine, lmLine, err := p.classifyLineType(ctx, req.GetLaneCode(), req.GetLineIdList())
	if err != nil {
		return err
	}
	var actualPointId, siteId string
	for _, actualPointInfo := range req.GetActualPointList() {
		if actualPointInfo.GetPointType() == lfslib.ActualPointTypeOut {
			actualPointId = actualPointInfo.GetPointId()
			siteId = actualPointInfo.GetSiteId()
		}
	}
	return p.VolumeCounter.UpdateDashboardVolume(ctx, req.GetProductId(), fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, sloCrateTime, updateVolumeType)
}

func (p *VolumeCounterService) reportZoneDashboardVolume(ctx context.Context, req *pb.VolumeInfo, zoneCode string, sloCrateTime int64, updateVolumeType pb.UpdateVolumeType) *srerr.Error {
	// routing运力看板单量上报，上报失败不能影响主流程
	fmLine, mmLine, lmLine, err := p.classifyLineType(ctx, req.GetLaneCode(), req.GetLineIdList())
	if err != nil {
		return err
	}
	var actualPointId, siteId string
	for _, actualPointInfo := range req.GetActualPointList() {
		if actualPointInfo.GetPointType() == lfslib.ActualPointTypeOut {
			actualPointId = actualPointInfo.GetPointId()
			siteId = actualPointInfo.GetSiteId()
		}
	}
	return p.VolumeCounter.UpdateZoneDashboardVolume(ctx, req.GetProductId(), fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, sloCrateTime, updateVolumeType)
}

// classifyLineType 对传入的lineIdList进行类型分类，分为FM、MM、LM三类
func (p *VolumeCounterService) classifyLineType(ctx context.Context, laneCode string, lineIdList []string) (string, string, string, *srerr.Error) {
	// 1. 查询laneCode下的line信息
	_, lineInfoMap := p.LaneSrv.GetLineIdToLineNameMap(ctx)
	var fmLine, mmLine, lmLine string
	for _, lineId := range lineIdList {
		if _, ok := lineInfoMap[lineId]; ok {
			lineCategory := lfslib.GetLineCategory(lineInfoMap[lineId].SubType)
			switch lineCategory {
			case lfslib.FM:
				fmLine = lineId
			case lfslib.MM:
				mmLine = lineId
			case lfslib.LM:
				lmLine = lineId
			case lfslib.UnknownType:
				logger.CtxLogErrorf(ctx, "exist unknown line type, laneCode=%v, lineIdList=%v", laneCode, lineIdList)
				monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.MatchLineTypeError, "exist unknown line type")
				return "", "", "", srerr.New(srerr.ParamErr, nil, "exist unknown line type")
			}
		}
	}
	return fmLine, mmLine, lmLine, nil
}

func formatVolumeDuplicateCounterKey(updateVolumeScene pb.UpdateVolumeScene, volumeType pb.UpdateVolumeType, key string) string {
	return fmt.Sprintf(constant.VolumeCounterIdempotencePrefix, updateVolumeScene, volumeType, key)
}

func getActualPointKey(actionPointList []*pb.VolumeActualPoint) string {
	var pointIdList []string
	for _, actualPoint := range actionPointList {
		pointIdList = append(pointIdList, actualPoint.GetPointId())
	}
	pointIdList = common.RemoveDuplicateString(pointIdList)
	// 防止调用方传过来的是乱序，这个运力暂时没有用到，可以直接切换
	sort.Strings(pointIdList)
	return str.Join("/", pointIdList)
}
