package volume_counter_service

import (
	"context"
	"errors"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestVolumeCounterService_GetLineParcelSnapshot(t *testing.T) {
	ctx := context.Background()
	p := &VolumeCounterService{}
	var patch, patchDel *gomonkey.Patches
	type args struct {
		forderId    string
		lineIdList  []string
		routingType uint8
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]*parcel_type_definition.ParcelTypeAttr
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: routingType == rule.UnknownRoutingType",
			args: args{
				routingType: rule.UnknownRoutingType,
			},
			want:  map[string]*parcel_type_definition.ParcelTypeAttr{},
			setup: func() {},
		},
		{
			name: "case 2: redisutil.GetString error",
			args: args{},
			want: map[string]*parcel_type_definition.ParcelTypeAttr{},
			setup: func() {
				patch = gomonkey.ApplyFunc(redisutil.GetString, func(ctx context.Context, key string) (string, error) {
					return "", errors.New("mock redis error")
				})
			},
		},
		{
			name: "case 3: Unmarshal error",
			args: args{},
			want: map[string]*parcel_type_definition.ParcelTypeAttr{},
			setup: func() {
				patch = gomonkey.ApplyFunc(redisutil.GetString, func(ctx context.Context, key string) (string, error) {
					return "11", nil
				})
			},
		},
		//{
		//	name: "case 4: redisutil.Del error",
		//	args: args{
		//		lineIdList: []string{"line1"},
		//	},
		//	want: map[string]*parcel_type_definition.ParcelTypeAttr{
		//		"line1": &parcel_type_definition.ParcelTypeAttr{
		//			IsCod:       true,
		//			IsBulky:     true,
		//			IsHighValue: true,
		//			IsDg:        true,
		//		},
		//	},
		//	setup: func() {
		//		patch = gomonkey.ApplyFunc(redisutil.GetString, func(ctx context.Context, key string) (string, error) {
		//			return "{\"line1111\":{\"IsCod\":true,\"IsBulky\":true,\"IsHighValue\":true,\"IsDg\":true}}", nil
		//		})
		//		patchDel = gomonkey.ApplyFunc(redisutil.Del, func(ctx context.Context, key string) error {
		//			return errors.New("mock del redis error")
		//		})
		//	},
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := p.GetLineParcelSnapshot(ctx, tt.args.forderId, tt.args.lineIdList, tt.args.routingType); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLineParcelSnapshot() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
			if patchDel != nil {
				patchDel.Reset()
			}
		})
	}
}
