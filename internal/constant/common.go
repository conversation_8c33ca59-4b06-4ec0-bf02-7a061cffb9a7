package constant

const (
	// 请求路由
	URLKey = "url_key"

	// 请求用户
	AccountKey = "account_key"

	// 请求用户email
	EmailKey = "email_key"

	// 请求用户对应国家
	ReqCountryKey = "request_country_key"

	// 请求用户对应时区
	ReqTimeZoneKey = "request_time_zone_key"

	// User category
	CategoryKey = "category_key"

	// 转发wbc
	NeedTransferWbc = "need_transfer_wbc"

	// User ID
	RequestUserIDKey = "__user_id"
)

const (
	RequestIpCtxKey   = "__REQUEST_IP__"
	HeaderRequestIP   = "X-FORWARDED-FOR"
	HeaderAccount     = "X-Account"
	HeaderToken       = "X-Token"
	HeaderJWTToken    = "JWT-Token"
	HeaderTimeStamp   = "X-Timestamp"
	HeaderRetCode     = "Ret-Code"
	HeaderShadowToken = "X-Shadow-Token"
)

const (
	ID = "ID"
	MY = "MY"
	PH = "PH"
	TH = "TH"
	SG = "SG"
	TW = "TW"
	VN = "VN"
	BR = "BR"
	MX = "MX"
	CN = "CN"
	IN = "IN"
	JP = "JP"
	KR = "KR"
	US = "US"
	AR = "AR"
	PL = "PL"
	ES = "ES"
	FR = "FR"
	CO = "CO"
	CL = "CL"
)

const (
	IDLocationZone = "Asia/Jakarta"
	VNLocationZone = "Asia/Ho_Chi_Minh"
	THLocationZone = "Asia/Bangkok"
	MYLocationZone = "Asia/Kuala_Lumpur"
	PHLocationZone = "Asia/Manila"
	SGLocationZone = "Asia/Singapore"
	TWLocationZone = "Asia/Taipei"
	// BRLocationZone 巴西夏令时 (DST) 的最近变动的时区数据, 南美洲（圣保罗）区域 – 将时区 由America/Sao_Paulo 设置为 America/Fortaleza
	// ref: https://docs.aws.amazon.com/zh_cn/AmazonRDS/latest/AuroraUserGuide/Concepts.RegionsAndAvailabilityZones.html
	BRLocationZone = "America/Fortaleza"
	MXLocationZone = "America/Mexico_City"
	COLocationZone = "America/Bogota"
	CLLocationZone = "America/Santiago"
	ARLocationZone = "America/Argentina"
	PLLocationZone = "Europe/Warsaw"
	ESLocationZone = "Europe/Madrid"
	FRLocationZone = "Europe/Paris"
	INLocationZone = "Asia/Kolkata"
)

const TimeLayout = "********"

var (
	FreightApiTokens = map[string]string{
		"local":    "^tKyQ*G>MvL0JaWExd:e6wt~o7A^BQ~k",
		"test":     "^tKyQ*G>MvL0JaWExd:e6wt~o7A^BQ~k",
		"uat":      "^tKyQ*G>MvL0JaWExd:e6wt~o7A^BQ~k",
		"staging":  "^tKyQ*G>MvL0JaWExd:e6wt~o7A^BQ~k",
		"stable":   "^tKyQ*G>MvL0JaWExd:e6wt~o7A^BQ~k",
		"live":     "60qe@]xW)7xyVU]BdcwMa+)>^jhC4cTM",
		"livetest": "60qe@]xW)7xyVU]BdcwMa+)>^jhC4cTM",
	}
)

// System const
const (
	SysSmartRouting          = "smartrouting"
	SystemLFS                = "lfs"
	SystemLLS                = "lls"
	SystemLPSAdmin           = "lps-admin"
	SystemLPS                = "lps"
	SystemLpsFulfillment     = "lps-fulfillment"
	SystemLocation           = "location-grpc"
	SystemChargeApi          = "charge-api"
	SystemChargeCheckoutGrpc = "charge-checkout-api"
	SystemChargeCoreGrpc     = "charge-core-api"
	SystemLNP                = "lnp"
	SystemWBCApi             = "wbc-api"
	SystemLCOS               = "lcos"
)

type AggregateType int

const (
	AggregateByMaskingProduct     AggregateType = 0
	AggregateByFulfillmentProduct AggregateType = 1
)

const MaxExcelRows = 1000000 //rows cannot exceed 1,000,000 to avoid break down the system

type ContextKey string

const (
	CtxMock             ContextKey = "ctxMock"
	AllocationLogCtxKey ContextKey = "allocation_log"
)

const ReportDataId = 0

const (
	Success = "success"
)

const DB_PRICE_INFLATION_FACTOR = 100000

const (
	DGTypeNoneDG uint32 = 0
	DGTypeDG     uint32 = 1
)
