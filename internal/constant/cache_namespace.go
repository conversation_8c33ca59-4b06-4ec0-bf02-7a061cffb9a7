package constant

// local cache
const (
	ProductNameDict             = "PRODUCT_NAME_DICT"
	VolumeZoneGroup             = "VOLUME_ZONE_GROUP"
	ProductVolumeRoutingRule    = "PRODUCT_VOLUME_ROUTING_RULE"
	LineToVolumeGroup           = "LINE_TO_VOLUME_GROUP"
	LaneInfo                    = "LANE_INFO"
	RoutingRule                 = "ROUTING_RULE"
	RoutingRole                 = "ROUTING_ROLE"
	LocationZone                = "LOCATION_ZONE"
	ProductBaseInfoList         = "PRODUCT_BASE_INFO_LIST"
	MaskAllocationConfig        = "MASK_ALLOCATION_CONFIG"
	MaskEffectiveRule           = "MASK_EFFECTIVE_RULE"
	MaskLocationVolume          = "MASK_LOCATION_VOLUME"
	ForecastMaskLocationVolume  = "FORECAST_MASK_LOCATION_VOLUME"
	MaskLogisticProductPriority = "MASK_LOGISTIC_PRODUCT_PRIORITY"
	CCRoutingRule               = "CC_ROUTING_RULE"
	MaskingProductRef           = "Masking_Product_Ref"
	GroupCodeList               = "GROUP_CODE_LIST"
	ProductLaneCode             = "Product_Lane_Code"
	BatchAllocateRuleVolume     = "BATCH_ALLOCATE_RULE_VOLUME"
	MaskingWhitelist            = "MASKING_WHITELIST"
	PickupPriority              = "PICKUP_PRIORITY"
	ParcelTypeDefinition        = "PARCEL_TYPE_DEFINITION"
	AvailableLHConfig           = "AVAILABLE_LH_CONFIG"
	LHCapacityConfig            = "LH_CAPACITY_CONFIG"
)

// level cache
const (
	LevelCacheGetLocationByLocationId               = "LevelCache-GetLocationByLocationId"
	LevelCacheGetFullLocationByLocationName         = "LevelCache-GetFullLocationByLocationName"
	LevelCacheGetFullLocationByLocationId           = "LevelCache-GetFullLocationByLocationId"
	LevelCacheGetLocationTreeByCountry              = "LevelCache-GetLocationTreeByCountry"
	LevelCacheGetFullLocationByLocationFullPathName = "LevelCache-GetFullLocationByLocationFullPathName"
	LevelCacheProductZoneCodes                      = "LevelCache-ProductZoneCodes"
	LevelCacheRoutingConfig                         = "LevelCache-RoutingConfig"
	LevelCacheProductDetailInfo                     = "LevelCache-ProductDetailInfo"
	LevelCacheGetMaskRuleVolumeByID                 = "LevelCache-GetMaskRuleVolumeByID"
	LevelCacheGetActiveRuleVolumeByMaskProductID    = "LevelCache-GetActiveRuleVolumeByMaskProductID"
)
