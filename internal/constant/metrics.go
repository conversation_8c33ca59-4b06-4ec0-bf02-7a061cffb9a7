package constant

// CAT Status
const (
	StatusSuccess = "0"
	StatusError   = "error"
)

// Prometheus Metric
const (
	PrometheusMetricFactor                   = "SmartRouting_Factor"
	PrometheusMetricAdmin                    = "SMR_ADMIN"
	MetricSmartRoutingSuccess                = "SmartRouting_success"
	MetricSmartRoutingProductCounter         = "SmartRouting_ProductCounter"
	MetricSmartRoutingLaneCounter            = "SmartRouting_LaneCounter"
	MetricSmartRoutingResourceCounter        = "SmartRouting_ResourceCounter"
	MetricSmartRoutingActualPointCounter     = "SmartRouting_ActualPointCounter"
	MetricSmartRoutingLineActualPointCounter = "SmartRouting_LineActualPointCounter"
	MetricSmartRoutingProduct                = "SmartRouting_Product"
	MetricSmartRoutingRule                   = "SmartRouting_Rule"
	MetricSmartRoutingLane                   = "SmartRouting_Lane"
	MetricSmartRoutingResource               = "SmartRouting_Resource"
	MetricBatchAllocateMonitor               = "BatchAllocate_Monitor"
)
