package enum

type ZoneType int

const (
	ZoneTypeLocation ZoneType = 1
	ZoneTypePostcode ZoneType = 2
	ZoneTypeCEPRange ZoneType = 3
)

var ZoneTypeNameMap = map[ZoneType]string{
	ZoneTypeLocation: "Location",
	ZoneTypePostcode: "Postcode",
	ZoneTypeCEPRange: "CEP Range",
}

type VolumeTaskOperationType int

const (
	TaskOperationTypeImport VolumeTaskOperationType = 1
	TaskOperationTypeExport VolumeTaskOperationType = 2
)

var VolumeTaskOperationTypeNameMap = map[VolumeTaskOperationType]string{
	TaskOperationTypeImport: "Import",
	TaskOperationTypeExport: "Export",
}

type VolumeTaskStatus int

const (
	TaskStatusInitial   VolumeTaskStatus = 1
	TaskStatusInProcess VolumeTaskStatus = 2
	TaskStatusDone      VolumeTaskStatus = 3
	TaskStatusFailed    VolumeTaskStatus = 4
)

var TaskStatusNameMap = map[VolumeTaskStatus]string{
	TaskStatusInitial:   "Initial",
	TaskStatusInProcess: "In Process",
	TaskStatusDone:      "Done",
	TaskStatusFailed:    "Failed",
}

type VolumeRuleStatus int

const (
	VolumeRuleStatusDraft    VolumeRuleStatus = 1
	VolumeRuleStatusActive   VolumeRuleStatus = 2
	VolumeRuleStatusExpired  VolumeRuleStatus = 3
	VolumeRuleStatusQueuing  VolumeRuleStatus = 4
	VolumeRuleStatusForecast VolumeRuleStatus = 5
	VolumeRuleStatusSubmit   VolumeRuleStatus = 6
)

var VolumeRuleStatusNameMap = map[VolumeRuleStatus]string{
	VolumeRuleStatusDraft:   "Draft",
	VolumeRuleStatusActive:  "Active",
	VolumeRuleStatusExpired: "Expired",
	VolumeRuleStatusQueuing: "Queuing",
	VolumeRuleStatusSubmit:  "Submit",
}

type VolumeRuleType int

const (
	VolumeRuleTypeProduct        VolumeRuleType = 1
	VolumeRuleTypeLineVolumeZone VolumeRuleType = 2
)

var VolumeRuleTypeNameMap = map[VolumeRuleType]string{
	VolumeRuleTypeProduct:        "Product",
	VolumeRuleTypeLineVolumeZone: "Line Volume Zone",
}

func (v VolumeRuleType) String() string {
	return VolumeRuleTypeNameMap[v]
}

type VerificationScene int

const (
	StartForecast  VerificationScene = 1
	DeployForecast VerificationScene = 2
)
