package enum

type TaskType int

// task type
const (
	Download TaskType = 1
	Upload   TaskType = 2
)

// business scene
const (
	MaskingPanel       string = "Masking Panel"
	MaskingVolumePanel string = "Masking Volume Panel"
	RoutingVolumePanel string = "Routing Volume Panel"
)

type TaskStatus int

// task status
const (
	Created    TaskStatus = 1
	Pending    TaskStatus = 2
	Doing      TaskStatus = 3
	Stopped    TaskStatus = 4
	Success    TaskStatus = 5
	Failed     TaskStatus = 6
	Terminated TaskStatus = 7
)
