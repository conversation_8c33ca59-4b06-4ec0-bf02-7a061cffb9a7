package enum

import "strings"

type Env = string

const (
	LIVE    Env = "LIVE"
	STAGING Env = "STAGING"
	STABLE  Env = "STABLE"
	UAT     Env = "UAT"
	TEST    Env = "TEST"
	LOCAL   Env = "LOCAL"
	LIVEISH Env = "LIVEISH"
)

func GetUrlEnv(e Env) string {
	if e == LIVE || e == LIVEISH {
		return ""
	} else if e == LOCAL {
		return "test"
	}
	if e == STABLE {
		return "test-stable"
	}
	return strings.ToLower(e)
}

func GetSupportedEnv() []Env {
	// LPS 没有 LIVEISH 环境, 暂时不支持
	return []Env{LIVE, STAGING, STABLE, UAT, TEST}
}
