package constant

const (
	// Smart Routing
	TaskNameZoneExport                     = "VolumeZoneExport"
	TaskNameZoneImport                     = "VolumeZoneImport"
	TaskNameZoneRuleLimitImport            = "VolumeZoneRuleLimitImport"
	TaskNameRuleEffective                  = "VolumeRuleEffective"
	TaskNameReportOrderCount               = "report_order_count"
	TaskNameSmartRoutingForecast           = "smart_routing_forecast"
	TaskNameILHRoutingForecast             = "ilh_routing_forecast"
	TaskNameLocalSpxRoutingForecast        = "local_spx_forecast"
	TaskNameLfsHardCriteriaCreatedBySystem = "created_by_system"
	TaskNameExecutedHardCriteria           = "executed_hard_criteria"
	TaskNameLfsHardCriteriaCheck           = "check_hard_criteria"
	TaskNameScheduleRule                   = "schedule_rule"
	TaskNameRoutingLogVisual               = "routing_log_visual"

	// 3PL Masking
	TaskNameExportMaskingData                 = "export_masking_result_data"
	TaskNameAllocateCheckBatchVolumeCounter   = "allocate_check_batch_volume_counter"
	TaskNameCheckoutFulfillmentProductCounter = "checkout_fulfillment_product_counter"
	TaskNameUpdateZoneVolume                  = "update_zone_volume"
	TaskNameUpdateRouteVolume                 = "update_route_volume"
	TaskNameAllocateStoreConsumer             = "allocate_store_consumer"
	TaskNameLoadForecastVolumeConfig          = "load_forecast_volume_config"
	TaskNameMaskingForecast                   = "masking_forecast"
	TaskDeleteMaskingSubTask                  = "delete_masking_sub_task"
	TaskCheckMaskingProcessTask               = "check_masking_process_task"
	TaskGetForecastTotalCount                 = "get_forecast_total_count"
	TaskUpdateRouteAndZoneVolume              = "update_route_zone_volume"
	TaskNameAllocateStoreHbaseConsumer        = "allocate_store_hbase_consumer"
	TaskNameScheduleCountStat                 = "schedule_count_stat"
	TaskReportMaskingVolume                   = "report_masking_volume"
	TaskMergeMaskingVolume                    = "merge_masking_volume"
	TaskClearMaskingVolume                    = "clear_masking_volume"
	TaskReportRoutingVolume                   = "report_routing_volume"
	TaskMergeRoutingVolume                    = "merge_routing_volume"
	TaskClearRoutingVolume                    = "clear_routing_volume"
	TaskAllocateScheduleVisual                = "allocate_schedule_visual"
	TaskNameReportMaskingOrderCount           = "report_masking_order_count"
	TaskNameDeductVolumeCount                 = "deduct_volume_count"

	// Batch Allocate Forecast Tool
	TaskStartBatchForecastUnit          = "start_batch_forecast_unit"
	TaskUpdateBatchAllocateForecastTask = "update_batch_allocate_forecast_task"
	TaskAllocateHistory                 = "allocate_history"
	TaskParseBatchVolume                = "parse_batch_volume"
	TaskNameCreateBASubTask             = "create_ba_sub_task"
	TaskBAForecastToolProgress          = "batch_allocate_forecast_tool_progress"

	// batch allocate
	TaskNameBatchAllocateHoldOrders  = "batch_allocate_hold_orders"
	TaskSplitBatchAllocateOrders     = "split_batch_allocate_orders"
	TaskNameBatchAllocate            = "batch_allocate"
	TaskNameAbnormalBatchAllocate    = "abnormal_batch_allocate"
	TaskNameBatchAbnormalInspection  = "batch_abnormal_inspection"
	TaskNamePushOrderResult          = "push_order_result"
	TaskNameUpdateOrderResult        = "update_order_result"
	TaskNameClearOrderAndResult      = "clear_order_and_result"
	TaskNameAllocationPathEmpty      = "allocation_path_empty_job"
	TaskNameMakeUpAsyncAllocationLog = "make_up_async_allocation_log"
	TaskNameBatchAllocateMonitor     = "batch_allocate_monitor"

	//Audit log
	TaskAuditLogReport = "audit_log_report"

	// schedule visual
	TaskSyncScheduleVisualCount  = "sync_schedule_visual_count"
	TaskClearScheduleVisualCount = "clear_schedule_visual_count"
)
