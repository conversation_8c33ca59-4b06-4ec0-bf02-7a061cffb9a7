package ilh_smart_routing

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// CreateAvailableLHConfigReq  表示创建AvailableLH配置的请求
type CreateAvailableLHConfigReq struct {
	MultiProductID int                      `json:"multi_product_id" validate:"required"`
	RuleStatus     rule.RuleStatus          `json:"rule_status" validate:"required"`
	Rules          []entity.AvailableLHRule `json:"rules" validate:"required"`
	Operator       string                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

type CreateAvailableLHConfigResp struct {
	ID int `json:"id"`
}

// UpdateAvailableLHConfigReq  表示更新AvailableLH配置的请求
type UpdateAvailableLHConfigReq struct {
	ID             int                      `json:"id" validate:"required"`
	MultiProductID int                      `json:"multi_product_id" validate:"required"`
	RuleStatus     rule.RuleStatus          `json:"rule_status" validate:"required"`
	Rules          []entity.AvailableLHRule `json:"rules" validate:"required"`
	Operator       string                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

// GetAvailableLHConfigReq 表示获取AvailableLH配置的请求
type GetAvailableLHConfigReq struct {
	ID int `json:"id" validate:"required"`
}

// GetAvailableLHConfigResp 表示获取AvailableLH配置的响应
type GetAvailableLHConfigResp struct {
	ID             int                      `json:"id"`
	MultiProductID int                      `json:"multi_product_id"`
	RuleStatus     rule.RuleStatus          `json:"rule_status"`
	Rules          []entity.AvailableLHRule `json:"rules"`
}

// ListAvailableLHConfigReq 表示获取AvailableLH配置列表的请求
type ListAvailableLHConfigReq struct {
	MultiProductID int              `json:"multi_product_id"`
	RuleStatus     *rule.RuleStatus `json:"rule_status,omitempty"`
	Pageno         int              `json:"pageno"`
	Limit          int              `json:"limit"`
}

// ListAvailableLHConfigResp 表示获取AvailableLH配置列表的响应
type ListAvailableLHConfigResp struct {
	Count  int                             `json:"count"`
	Total  int64                           `json:"total"`
	Pageno int                             `json:"pageno"`
	List   []ListAvailableLHConfigRespItem `json:"list"`
}

// ListAvailableLHConfigRespItem 表示获取AvailableLH配置列表的展示内容
type ListAvailableLHConfigRespItem struct {
	ID                     int                 `json:"id"`
	MultiProductID         int                 `json:"multi_product_id"`
	MultiRules             bool                `json:"multi_rules"`
	RuleStatus             rule.RuleStatus     `json:"rule_status"`
	EffectiveStartTime     int64               `json:"effective_start_time"`
	DefaultRuleAvailableLH []rule.BaseLineInfo `json:"default_rule_available_lh"`
	DefaultRuleAvailableCC []rule.BaseLineInfo `json:"default_rule_available_cc"`
}

// DeleteAvailableLHConfigReq 表示删除AvailableLH配置的请求
type DeleteAvailableLHConfigReq struct {
	ID       int    `json:"id" validate:"required"`
	Operator string `json:"-"` // 后端通过context获取用户信息，非前端传入
}

// CopyAvailableLHConfigReq 表示复制AvailableLH配置的请求
type CopyAvailableLHConfigReq struct {
	ID       int    `json:"id" validate:"required"` // 源配置ID
	Operator string `json:"-"`                      // 后端通过context获取用户信息，非前端传入
}

// CheckAvailableLHConfigReq 表示检查是否存在同一MultiProductID且状态为Active的配置的请求
type CheckAvailableLHConfigReq struct {
	MultiProductID int `json:"multi_product_id" validate:"required"`
}

// CheckAvailableLHConfigResp 表示检查结果的响应
type CheckAvailableLHConfigResp struct {
	Exists bool `json:"exists"`
}

// UpdateAvailableLHStatusReq 表示更新AvailableLH配置状态的请求
type UpdateAvailableLHStatusReq struct {
	ID       int             `json:"id" validate:"required"`
	Status   rule.RuleStatus `json:"status" validate:"required"` // 只能为Active或Expired
	Operator string          `json:"-"`                          // 后端通过context获取用户信息，非前端传入
}
