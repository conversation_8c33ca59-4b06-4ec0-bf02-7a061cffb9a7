package ilh_smart_routing

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// CreateLHCapacityReq 创建容量配置请求
type CreateLHCapacityReq struct {
	CapacityName     string                   `json:"capacity_name" validate:"required"`
	ILHVendorName    string                   `json:"ilh_vendor_name" validate:"required"`
	ILHLineID        string                   `json:"ilh_line_id" validate:"required"`
	ILHLineName      string                   `json:"ilh_line_name"`
	TWS              []string                 `json:"tws" validate:"required,min=1"`
	DestinationPort  []string                 `json:"destination_port" validate:"required,min=1"`
	DGType           rule.DGFlag              `json:"dg_type"`
	CapacitySettings []entity.CapacitySetting `json:"capacity_settings" validate:"required"`
	Status           rule.RuleStatus          `json:"status"`
	Operator         string                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

// UpdateLHCapacityReq 更新容量配置请求
type UpdateLHCapacityReq struct {
	ID               int                      `json:"id" validate:"required"`
	CapacityName     string                   `json:"capacity_name" validate:"required"`
	ILHVendorName    string                   `json:"ilh_vendor_name" validate:"required"`
	ILHLineID        string                   `json:"ilh_line_id" validate:"required"`
	ILHLineName      string                   `json:"ilh_line_name"`
	TWS              []string                 `json:"tws" validate:"required,min=1"`
	DestinationPort  []string                 `json:"destination_port" validate:"required,min=1"`
	DGType           rule.DGFlag              `json:"dg_type"`
	CapacitySettings []entity.CapacitySetting `json:"capacity_settings" validate:"required"`
	Status           rule.RuleStatus          `json:"status"`
	Operator         string                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

// UpdateLHCapacityStatusReq 更新LH容量配置状态请求
type UpdateLHCapacityStatusReq struct {
	ID       int             `json:"id" validate:"required"`
	Status   rule.RuleStatus `json:"status"`
	Operator string          `json:"operator"` // 后端通过context获取用户信息，非前端传入
}

// GetLHCapacityReq 获取容量配置请求
type GetLHCapacityReq struct {
	ID int `json:"id" form:"id" validate:"required"`
}

// GetLHCapacityResp 获取容量配置响应
type GetLHCapacityResp struct {
	ID               int                      `json:"id"`
	CapacityName     string                   `json:"capacity_name"`
	ILHVendorName    string                   `json:"ilh_vendor_name"`
	ILHLineID        string                   `json:"ilh_line_id"`
	ILHLineName      string                   `json:"ilh_line_name"`
	TWS              []string                 `json:"tws"`
	DestinationPort  []string                 `json:"destination_port"`
	DGType           rule.DGFlag              `json:"dg_type"`
	CapacitySettings []entity.CapacitySetting `json:"capacity_settings"`
	Status           rule.RuleStatus          `json:"status"`
	Operator         string                   `json:"operator"`
}

// ListLHCapacityReq 列出容量配置请求
type ListLHCapacityReq struct {
	ILHVendorName   string      `json:"ilh_vendor_name" form:"ilh_vendor_name"`
	LineID          string      `json:"line_id" form:"line_id"`
	DGType          rule.DGFlag `json:"dg_type" form:"dg_type"`
	DestinationPort string      `json:"destination_port" form:"destination_port"`
	TWS             string      `json:"tws" form:"tws"`
	Pageno          int         `json:"pageno" form:"pageno"`
	Limit           int         `json:"limit" form:"limit"`
}

// ListLHCapacityResp 列出容量配置响应
type ListLHCapacityResp struct {
	Total  int64                `json:"total"`
	Pageno int                  `json:"pageno"`
	Count  int                  `json:"count"`
	List   []LHCapacityListItem `json:"list"`
}

// SpecialDateCapacity 特殊日期容量配置
type SpecialDateCapacity struct {
	Date      string  `json:"date"`       // 特殊日期，格式如"2025-01-01"
	BSAWeight float64 `json:"bsa_weight"` // 特殊日期BSA重量(kg)
}

// WeeklyCapacity 周容量配置
type WeeklyCapacity struct {
	WeekDay   int     `json:"week_day"`   // 星期几，1-7对应周一至周日
	BSAWeight float64 `json:"bsa_weight"` // 周几BSA重量(kg)
}

// SpecialTimeSlotCapacity 特殊时间段容量配置
type SpecialTimeSlotCapacity struct {
	StartTime string  `json:"start_time"` // 特殊时间段开始，格式如"20:00:01"
	EndTime   string  `json:"end_time"`   // 特殊时间段结束，格式如"24:00:00"
	BSAWeight float64 `json:"bsa_weight"` // 特殊时间段BSA重量(kg)
}

// SpecialDateTimeSlotCapacity 特殊日期+时间段容量配置
type SpecialDateTimeSlotCapacity struct {
	StartDateTime string  `json:"start_date_time"` // 特殊日期+时间段开始，格式如"2025-01-10 14:00:00"
	EndDateTime   string  `json:"end_date_time"`   // 特殊日期+时间段结束，格式如"2025-01-10 20:00:00"
	BSAWeight     float64 `json:"bsa_weight"`      // 特殊日期+时间段BSA重量(kg)
}

// LHCapacityListItem 容量配置列表项
type LHCapacityListItem struct {
	ID                           int                           `json:"id"`
	CapacityName                 string                        `json:"capacity_name"`
	ILHVendorName                string                        `json:"ilh_vendor_name"`
	ILHLineID                    string                        `json:"ilh_line_id"`
	ILHLineName                  string                        `json:"ilh_line_name"`
	TWS                          []string                      `json:"tws"`
	DestinationPort              []string                      `json:"destination_port"`
	DGType                       rule.DGFlag                   `json:"dg_type"`
	Status                       rule.RuleStatus               `json:"status"`
	EffectiveStartTime           int64                         `json:"effective_start_time"`
	Operator                     string                        `json:"operator"`
	DefaultBSAWeight             float64                       `json:"default_bsa_weight"`                        // 默认BSA重量(kg)
	DefaultAdhocWeight           float64                       `json:"default_adhoc_weight"`                      // 默认临时重量(kg)
	DefaultProductSpecialConfigs []entity.ProductSetting       `json:"default_product_special_configs,omitempty"` // 特殊产品配置列表
	SpecialDates                 []SpecialDateCapacity         `json:"special_dates,omitempty"`
	WeeklyDays                   []WeeklyCapacity              `json:"weekly_days,omitempty"` // 周容量配置列表
	SpecialTimeSlots             []SpecialTimeSlotCapacity     `json:"special_time_slots,omitempty"`
	SpecialDateTimeSlots         []SpecialDateTimeSlotCapacity `json:"special_date_time_slots,omitempty"`
}

// DeleteLHCapacityReq 删除容量配置请求
type DeleteLHCapacityReq struct {
	ID int `json:"id" validate:"required"`
}

// CreateLHCapacityResp 创建容量配置响应
type CreateLHCapacityResp struct {
	ID int `json:"id"`
}

// CopyLHCapacityReq 复制LH容量配置请求
type CopyLHCapacityReq struct {
	ID       int    `json:"id" validate:"required"` // 要复制的LH容量配置ID
	Operator string `json:"-"`                      // 后端通过context获取用户信息，非前端传入
}

// CopyLHCapacityResp 复制LH容量配置响应
type CopyLHCapacityResp struct {
	ID int `json:"id"` // 新创建的LH容量配置ID
}

// UploadLHCapacityReq 上传LH容量配置请求
type UploadLHCapacityReq struct {
	FileUrl  string `json:"file_url" validate:"required"` // S3文件链接
	Operator string `json:"-"`                            // 后端通过context获取用户信息，非前端传入
}

// UploadLHCapacityResp 上传LH容量配置响应
type UploadLHCapacityResp struct {
	TotalCount   int `json:"total_count"`   // 总处理记录数
	SuccessCount int `json:"success_count"` // 成功处理记录数
	FailCount    int `json:"fail_count"`    // 失败记录数
}

// CheckLHCapacityReq 检查是否存在特定组合且状态为Active的配置请求
type CheckLHCapacityReq struct {
	ILHLineID       string      `json:"ilh_line_id" validate:"required"`
	TWS             []string    `json:"tws" validate:"required,min=1"`
	DestinationPort []string    `json:"destination_port" validate:"required,min=1"`
	DGType          rule.DGFlag `json:"dg_type"`
}

// CheckLHCapacityResp 检查结果响应
type CheckLHCapacityResp struct {
	Exists bool `json:"exists"` // 是否存在
}
