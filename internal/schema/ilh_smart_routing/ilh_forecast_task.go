package ilh_smart_routing

import (
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"

	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
)

type CreateILHForecastTaskReq struct {
	TaskName            string                                   `json:"task_name" validate:"required"`
	StartDate           string                                   `json:"start_date"`
	EndDate             string                                   `json:"end_date"`
	TaskStatus          persistent.TaskStatus                    `json:"task_status"`
	ShipmentResource    persistent.ShipmentResource              `json:"shipment_resource"`
	AvailableLHRuleList []forecastentity.ForecastAvailableLHRule `json:"available_lh_rule_list"`
	LHCapacityList      []forecastentity.ForecastLHCapacityItem  `json:"lh_capacity_list"`
	Operator            string                                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

type CreateILHForecastTaskResp struct {
	ID int `json:"id"`
}

type UpdateILHForecastTaskReq struct {
	ID                  int                                      `json:"id" validate:"required"`
	TaskName            string                                   `json:"task_name" validate:"required"`
	StartDate           string                                   `json:"start_date"`
	EndDate             string                                   `json:"end_date"`
	TaskStatus          persistent.TaskStatus                    `json:"task_status"`
	ShipmentResource    persistent.ShipmentResource              `json:"shipment_resource"`
	AvailableLHRuleList []forecastentity.ForecastAvailableLHRule `json:"available_lh_rule_list"`
	LHCapacityList      []forecastentity.ForecastLHCapacityItem  `json:"lh_capacity_list"`
	Operator            string                                   `json:"-"` // 后端通过context获取用户信息，非前端传入
}

type GetILHForecastTaskReq struct {
	ID int `json:"id" validate:"required"`
}

type GetILHForecastTaskResp struct {
	ID                   int                                      `json:"id"`
	TaskName             string                                   `json:"task_name"`
	StartDate            string                                   `json:"start_date"`
	EndDate              string                                   `json:"end_date"`
	TaskStatus           persistent.TaskStatus                    `json:"task_status"`
	DeployStatus         persistent.DeployStatus                  `json:"deploy_status"`
	ShipmentResource     persistent.ShipmentResource              `json:"shipment_resource"`
	AvailableLHRuleList  []forecastentity.ForecastAvailableLHRule `json:"available_lh_rule_list"`
	LHCapacityList       []forecastentity.ForecastLHCapacityItem  `json:"lh_capacity_list"`
	ForecastCompleteTime int                                      `json:"forecast_complete_time"`
	TaskOperator         string                                   `json:"task_operator"`
	DeployTime           int                                      `json:"deploy_time"`
	DeployOperator       string                                   `json:"deploy_operator"`
}

type ListILHForecastTaskReq struct {
	ID           int                     `json:"id"`
	TaskName     string                  `json:"task_name"`
	TaskStatus   persistent.TaskStatus   `json:"task_status"`
	DeployStatus persistent.DeployStatus `json:"deploy_status"`
	Pageno       int                     `json:"pageno"`
	Limit        int                     `json:"limit"`
}

type ListILHForecastTaskResp struct {
	Count  int                           `json:"count"`
	Total  int64                         `json:"total"`
	Pageno int                           `json:"pageno"`
	List   []ListILHForecastTaskRespItem `json:"list"`
}

type ListILHForecastTaskRespItem struct {
	ID                   int                     `json:"id"`
	ILHLineList          []ListILHLineInfo       `json:"ilh_line_list"`
	MultiProductIDList   []int                   `json:"multi_product_id_list"`
	TaskName             string                  `json:"task_name"`
	TaskStatus           persistent.TaskStatus   `json:"task_status"`
	DeployStatus         persistent.DeployStatus `json:"deploy_status"`
	ForecastCompleteTime int                     `json:"forecast_complete_time"`
	TaskOperator         string                  `json:"task_operator"`
	DeployTime           int                     `json:"deploy_time"`
	DeployOperator       string                  `json:"deploy_operator"`
}

type ListILHLineInfo struct {
	ILHLineID   string `json:"ilh_line_id"`
	ILHLineName string `json:"ilh_line_name"`
}

type CopyILHForecastTaskReq struct {
	ID int `json:"id" validate:"required"`
}

type CopyILHForecastTaskResp struct {
	ID int `json:"id"`
}

type DeleteILHForecastTaskReq struct {
	ID int `json:"id" validate:"required"`
}

type GetILHForecastTaskResultReq struct {
	ID int `json:"id" validate:"required"`
}

type GetILHForecastTaskResultResp struct {
	// Forecast Result By LH 部分
	LHResults []ForecastLHResult `json:"lh_results"`
	// Forecast Result By CC 部分
	CCResults []ForecastCCResult `json:"cc_results"`
	// Forecast Result By Product 部分
	ProductResults []ForecastProductResult `json:"product_results"`
}

// ForecastLHResult LH分配结果
type ForecastLHResult struct {
	ILHLine         ILHLineInfo             `json:"ilh_line"`         // ILH线路信息
	DestinationPort string                  `json:"destination_port"` // 起始-目的地
	DGType          rule.DGFlag             `json:"dg_type"`          // DG类型
	ProductInfos    []ForecastLHProductInfo `json:"product_infos"`    // 产品分配信息
}

// ILHLineInfo ILH线路信息
type ILHLineInfo struct {
	LineID   string `json:"line_id"`   // 线路ID
	LineName string `json:"line_name"` // 线路名称
}

// ForecastLHProductInfo LH分配的产品信息
type ForecastLHProductInfo struct {
	ProductID                int     `json:"product_id"`                  // 产品ID
	ProductName              string  `json:"product_name"`                // 产品名称
	ReservedBSA              float64 `json:"reserved_bsa"`                // 预留BSA重量(kg)
	ReservedBSAProportion    int     `json:"reserved_bsa_proportion"`     // 预留BSA比例
	NonReservedBSA           float64 `json:"non_reserved_bsa"`            // 非预留BSA重量(kg)
	NonReservedBSAProportion int     `json:"non_reserved_bsa_proportion"` // 非预留BSA比例
	AdhocWeight              float64 `json:"adhoc_weight"`                // Adhoc重量(kg)
	AdhocWeightProportion    int     `json:"adhoc_weight_proportion"`     // Adhoc重量比例
}

// ForecastCCResult CC分配结果
type ForecastCCResult struct {
	ImportILH       ILHLineInfo             `json:"import_ilh"`       // 进口ILH
	DestinationPort string                  `json:"destination_port"` // 起始-目的地
	DGType          rule.DGFlag             `json:"dg_type"`          // DG类型
	ILHLines        []ForecastCCILHLineInfo `json:"ilh_lines"`        // ILH线路分配信息
}

// ForecastCCILHLineInfo CC分配的ILH线路信息
type ForecastCCILHLineInfo struct {
	ILHLine       ILHLineInfo       `json:"ilh_line"` // ILH线路信息
	ProductResult []CCProductResult `json:"product_result"`
}

type CCProductResult struct {
	ProductID         int     `json:"product_id"`
	ProductName       string  `json:"product_name"`
	AssignedWeight    float64 `json:"assigned_weight"`     // 分配重量(kg)
	WeightProportion  int     `json:"weight_proportion"`   // 重量占比
	MinRequiredWeight float64 `json:"min_required_weight"` // 最小需求重量(kg)
	MetMinimumVolume  bool    `json:"met_minimum_volume"`  // 是否达到最小容量
}

// ForecastProductResult 按产品分配结果
type ForecastProductResult struct {
	MultiProductID   int                       `json:"multi_product_id"` // 多产品信息
	MultiProductName string                    `json:"multi_product_name"`
	ILHProductID     string                    `json:"ilh_product_id"` // ILH产品
	ILHProductName   string                    `json:"ilh_product_name"`
	Rules            []ForecastProductRuleInfo `json:"rules"` // 规则信息
}

// ForecastProductRuleInfo 产品规则信息
type ForecastProductRuleInfo struct {
	RuleID   int                           `json:"rule_id"`   // 规则ID
	RuleName string                        `json:"rule_name"` // 规则名称
	Priority int                           `json:"priority"`  // 优先级
	ILHLines []ForecastProductRuleLineInfo `json:"ilh_lines"` // ILH线路信息
}

// ForecastProductRuleLineInfo 产品规则线路信息
type ForecastProductRuleLineInfo struct {
	ILHLine             ILHLineInfo `json:"ilh_line"`              // ILH线路信息
	ImportILH           ILHLineInfo `json:"import_ilh"`            // 进口ILH
	DGType              rule.DGFlag `json:"dg_type"`               // DG类型
	TotalAssignedWeight float64     `json:"total_assigned_weight"` // 总分配重量(kg)
	WeightProportion    int         `json:"weight_proportion"`     // 重量占比
	AssignedBSAWeight   float64     `json:"assigned_bsa_weight"`   // 分配BSA重量(kg)
	AssignedAdhocWeight float64     `json:"assigned_adhoc_weight"` // 分配Adhoc重量(kg)
}

type DeployILHForecastTaskReq struct {
	ID                   int   `json:"id" validate:"required"`
	EffectiveStartTime   int64 `json:"effective_start_time"`
	EffectiveImmediately bool  `json:"effective_immediately"`
}

// Validate 校验请求参数，EffectiveStartTime或EffectiveImmediately其中一个必须有值
func (r *DeployILHForecastTaskReq) Validate() error {
	if r.EffectiveStartTime == 0 && !r.EffectiveImmediately {
		return errors.New("Please specify when the task should take effect: either set a specific effective time or select 'Effective Immediately'")
	}
	return nil
}

type DeployILHForecastTaskResp struct {
}

// ExportILHForecastTaskResultReq 导出ILH预测任务结果请求
type ExportILHForecastTaskResultReq struct {
	ID int `json:"id" validate:"required"`
}

// ExportILHForecastTaskResultResp 导出ILH预测任务结果响应
type ExportILHForecastTaskResultResp struct {
	URL string `json:"url"` // 导出文件的下载链接
}
