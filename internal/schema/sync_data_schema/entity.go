package sync_data_schema

type SyncMfOrdersReq struct {
	StartDate string `json:"start_date"` //e.g. 2023-04-03
	EndDate   string `json:"end_date"`   //e.g. 2023-04-03
	HbaseInfo
}

type GetSyncDataReq struct {
	RowKeyList []string `json:"row_key_list"` //will get data by row key in this list
	HbaseInfo
}

type TestBalanceReq struct {
	LoopSeconds      int64  `json:"loop_seconds"` //used to control loop time, for example, 60 means will loop util 60s passed.
	StartDate        string `json:"start_date"`   //e.g. 2023-04-03
	EndDate          string `json:"end_date"`     //e.g. 2023-04-03
	MaskingProductId uint64 `json:"masking_product_id"`
	HbaseInfo
}

type HbaseInfo struct {
	HostUrl    string `json:"host_url"`  //non-live host url, used to store live data
	UserName   string `json:"user_name"` //non-live user name, used to store live data
	PassWord   string `json:"pass_word"` //non-live pass word, used to store live data
	AuthMethod string `json:"auth_method"`
	ZkRoot     string `json:"zk_root"`    //non-live zk root, used to store live data
	TableName  string `json:"table_name"` //non-live table name, used to store live data
}
