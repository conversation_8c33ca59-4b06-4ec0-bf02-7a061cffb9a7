package schema

type (
	QueryPostCodeListReq struct {
		ProductId    int    `json:"product_id"`
		PostCode     string `json:"post_code"`
		ZoneCode     string `json:"zone_code"`
		RoutingType  int    `json:"routing_type"`
		PostCodeType int    `json:"post_code_type"`
		Page         int64  `json:"page" validate:"required"`
		PagSize      int64  `json:"count" validate:"required"`
	}

	QueryPostCodeListResp struct {
		Page  int64                         `json:"page"`
		Total int64                         `json:"total"`
		List  []QueryPostCodeListRespDetail `json:"list"`
	}

	QueryPostCodeListRespDetail struct {
		Id             int    `json:"id"`
		ZoneCode       string `json:"zone_code"`
		PostCode       string `json:"post_code"`
		LastOperator   string `json:"last_operator"`
		ProductId      int    `json:"product_id"`
		LastUpdateTime int64  `json:"last_update_time"`
	}

	DeleteSinglePostCode struct {
		PostCodeId int64 `json:"post_code_id"`
	}

	EditSinglePostCode struct {
		Id           int    `json:"id"`
		ZoneCode     string `json:"zone_code"`
		PostCode     string `json:"post_code"`
		LastOperator string `json:"last_operator"`
		RoutingType  int    `json:"routing_type"`
		PostCodeType int    `json:"post_code_type"`
		ProductId    int    `json:"product_id"`
	}

	ImportPostCodeReq struct {
		PostCodeUrl  string `json:"post_url" validate:"UssHostValidate"`
		RoutingType  int    `json:"routing_type"`
		LastOperator string `json:"last_operator"`
		PostCodeType int    `json:"post_code_type"`
	}

	ExportPostCodeReq struct {
		ProductId    int    `json:"product_id"`
		ZoneCode     string `json:"zone_code"`
		PostCode     string `json:"post_code"`
		RoutingType  int    `json:"routing_type"`
		PostCodeType int    `json:"post_code_type"`
	}
)
