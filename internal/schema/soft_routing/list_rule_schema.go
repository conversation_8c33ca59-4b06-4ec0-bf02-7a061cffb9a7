package soft_routing

import rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"

func (req *ListRuleRequest) GetLimit() int32 {
	if req.Limit == 0 {
		return 20
	}
	return int32(req.Limit)
}

func (req *ListRuleRequest) GetPageNo() int32 {
	if req.PageNO == 0 {
		return 1
	}
	return int32(req.PageNO)
}

func (req *ListRuleRequest) GetProductID() int64 {
	return req.ProductID
}

func (req *ListRuleRequest) GetRuleId() int64 {
	return req.Id
}

func (req *ListRuleRequest) GetStatus() rule.RuleStatus {
	if req.Status != nil {
		return *req.Status
	}
	return -1
}

func (req *ListRuleRequest) GetTaskID() int64 {
	return req.TaskID
}
