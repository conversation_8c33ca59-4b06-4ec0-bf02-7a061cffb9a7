package soft_routing

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"sort"
)

type (
	RoutingRuleDetailReq struct {
		Id int `json:"id"`
	}

	CreateRuleRequest struct {
		ProductID      int64  `json:"product_id"`
		RuleName       string `json:"rule_name"`
		Priority       int32  `json:"priority"`
		RoutingType    uint8  `json:"routing_type"`
		IsMultiProduct bool   `json:"is_multi_product"`
	}

	UpdateRuleRequest struct {
		ID                          int64                             `json:"id"`
		ProductID                   int64                             `json:"product_id"`
		Status                      rule.RuleStatus                   `json:"rule_status"`
		RuleName                    string                            `json:"rule_name"`
		Priority                    int32                             `json:"priority"`
		RuleDetails                 *rule.RuleDetails                 `json:"rule_detail,omitempty"`
		DefaultCriteria             *rule.DefaultCriteria             `json:"default_criteria,omitempty"` // 默认规则权重配置
		DisabledInfo                []*rule.DisabledInfo              `json:"disabled_info,omitempty"`
		CombinationSetting          []*rule.CombinationSetting        `json:"combination_setting"`
		MultiProductDefaultCriteria *rule.MultiProductDefaultCriteria `json:"multi_product_default_criteria,omitempty"` // Multi Product默认规则权重配置
		MultiProductDisableInfo     *rule.MultiProductDisabledInfo    `json:"multi_product_disable_info,omitempty"`
		EffectiveStartTime          uint32                            `json:"effective_start_time,omitempty"`
		WhsId                       []string                          `json:"whs_id,omitempty"`
		DestinationPorts            []string                          `json:"destination_ports,omitempty"` //SSCSMR-278: used to store ho site id
		DgType                      int                               `json:"dg_type,omitempty"`           //SSCSMR-278: dg type(dg flag) of this rule, 1:non-dg; 2:dg                  //DG; NON-DG
		ParcelDimension             rule.ParcelDimension              `json:"parcel_dimension,omitempty"`
		ZoneCode                    []string                          `json:"zone_code,omitempty"`
		ItemCategoryLevel           int                               `json:"item_category_level,omitempty"`
		ItemCategoryID              []int                             `json:"item_category_id,omitempty"`
		ParcelValueMin              float32                           `json:"parcel_value_min,omitempty"`
		ParcelValueMax              float32                           `json:"parcel_value_max,omitempty"`
		ParcelWeightMin             int                               `json:"parcel_weight_min,omitempty"`
		ParcelWeightMax             int                               `json:"parcel_weight_max,omitempty"`
		RoutingType                 *uint8                            `json:"routing_type,omitempty"`
		WmsToggleEnable             bool                              `json:"wms_toggle_enable"`
		CCMode                      rule.CCMode                       `json:"cc_mode"`
		ShopGroupListVo             []int64                           `json:"shop_group_list"`
	}

	RuleDisplay struct {
		ID                    int64                `json:"id"`
		ProductID             int64                `json:"product_id"`
		RuleName              string               `json:"rule_name"`
		Status                rule.RuleStatus      `json:"rule_status"`
		WhsId                 []string             `json:"whs_id"`
		ItemCategoryLevel     int                  `json:"item_category_level,omitempty"`
		ItemCategoryID        []int                `json:"item_category_id,omitempty"`
		ParcelValueMin        float32              `json:"parcel_value_min,omitempty"`
		ParcelValueMax        float32              `json:"parcel_value_max,omitempty"`
		ParcelWeightMin       int                  `json:"parcel_weight_min,omitempty"`
		ParcelWeightMax       int                  `json:"parcel_weight_max,omitempty"`
		ParcelDimension       rule.ParcelDimension `json:"parcel_dimension,omitempty"`
		DgType                int                  `json:"dg_type,omitempty"` //DG; NON-DG
		Priority              int32                `json:"priority"`
		EnableProductPriority bool                 `json:"enable_product_priority"`
		OperatedBy            string               `json:"operated_by"`
		EffectiveStartTime    uint32               `json:"effective_start_time"`
		TaskID                int                  `json:"task_id"`
		TaskName              string               `json:"task_name"`
		CTime                 uint32               `json:"ctime"`
		MTime                 uint32               `json:"mtime"`
	}

	DeleteRuleRequest struct {
		ID int64 `json:"id"`
	}
	CopyRuleRequest struct {
		ID int64 `json:"id"`
	}
	RollbackRuleRequest struct {
		ID int64 `json:"id"`
	}
	DisableRuleRequest struct {
		ID int64 `json:"id"`
	}
	BatchDisableRuleRequest struct {
		IDList []int64 `json:"id_list"`
	}
	CreateConfigRequest struct {
		ProductId                int64  `json:"product_id" validate:"required"`
		SmartRoutingEnabled      bool   `json:"routing_enable"`
		LocalSmartRoutingEnabled bool   `json:"local_routing_enable"`
		SpxSmartRoutingEnabled   bool   `json:"spx_routing_enable"`
		IlhSmartRoutingEnabled   bool   `json:"ilh_routing_enable"`
		DefaultLaneCode          string `json:"default_lane_code"`
	}

	UpdateConfigRequest struct {
		ProductId                int64  `json:"product_id" validate:"required"`
		SmartRoutingEnabled      bool   `json:"routing_enable"`
		LocalSmartRoutingEnabled bool   `json:"local_routing_enable"`
		SpxSmartRoutingEnabled   bool   `json:"spx_routing_enable"`
		IlhSmartRoutingEnabled   bool   `json:"ilh_routing_enable"`
		DefaultLaneCode          string `json:"default_lane_code"`
	}

	ImportZoneRequest struct {
		ZoneType    uint8 `form:"zone_type" json:"zone_type"`
		RoutingType uint8 `form:"routing_type" json:"routing_type"`
	}

	ListReceiverZoneCodeReq struct {
		PageNO      int32  `json:"pageno" default:"1"`
		Limit       int32  `json:"limit" default:"20"`
		ProductID   int    `json:"product_id"`
		ZoneCode    string `json:"zone_code"`
		DistrictId  uint   `json:"district_id"`
		State       string `json:"state"`
		City        string `json:"city"`
		District    string `json:"district"`
		Street      string `json:"street"`
		RoutingType uint8  `json:"routing_type"`
		ZoneType    uint8  `json:"zone_type"`
	}
	ListReceiverZoneCodeResp struct {
		Count  int32                `json:"count"`
		Total  int32                `json:"total"`
		PageNo int32                `json:"pageno"`
		List   []*zone.LocationZone `json:"list"`
	}
	ListReceiverZoneCode struct {
		ProductID   int   `json:"product_id"`
		ZoneType    uint8 `json:"zone_type"`
		RoutingType uint8 `json:"routing_type"`
	}

	UpdateZoneRequest struct {
		ID         int64  `json:"id"`
		ZoneCode   string `json:"zone_code"`
		DistrictId uint   `json:"district_id"`
	}
	DeleteZoneRequest struct {
		ID int64 `json:"id"`
	}

	ListRuleRequest struct {
		Status          *rule.RuleStatus `json:"rule_status,omitempty"`
		Id              int64            `json:"id"`
		IsLocalForecast bool             `json:"is_local_forecast"`
		ProductID       int64            `json:"product_id,omitempty"`
		TaskID          int64            `json:"task_id"`
		RoutingType     uint8            `json:"routing_type"`
		IsMultiProduct  bool             `json:"is_multi_product"`
		PageNO          int              `json:"pageno" default:"1"`
		Limit           int              `json:"limit" default:"20"`
	}
	ListRuleResponse struct {
		Count  int32          `json:"count"`
		Total  int32          `json:"total"`
		PageNo int32          `json:"pageno"`
		List   []*RuleDisplay `json:"list"`
	}

	InitRoutingRuleReq struct {
		ProductID      int64 `json:"product_id"`
		RoutingType    uint8 `json:"routing_type"`
		IsMultiProduct bool  `json:"is_multi_product"`
	}

	ListProductWhsInfo struct {
		ProductID      int64 `json:"product_id"`
		IsMultiProduct bool  `json:"is_multi_product"`
	}
	ListWhsCodeResp struct {
		WhsCode        []string     `json:"whs_id_list"`
		IlhWhsInfoList []IlhWhsInfo `json:"ilh_whs_info_list"`
	}
	IlhWhsInfo struct {
		WhsID            string `json:"whs_id"`
		InterceptionTime string `json:"interception_time"`
		Timezone         int    `json:"timezone"`
	}

	ListILHWhsInfo struct {
		ILHLineID string `json:"ilh_line_id" validate:"required"`
	}

	ListILHDestinationPort struct {
		ILHLineID string `json:"ilh_line_id" validate:"required"`
	}

	DestinationPortInfo struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	GetConfigRequest struct {
		Id int64 `json:"id" validate:"required"`
	}

	ListConfigRequest struct {
		ProductId int64 `json:"product_id" default:"0"`
		PageNo    int32 `json:"pageno" default:"1"`
		Limit     int32 `json:"limit" default:"20"`
	}
	ListConfigResponse struct {
		Count  int32                 `json:"count"`
		Total  int32                 `json:"total"`
		PageNo int32                 `json:"pageno"`
		List   []*rule.RoutingConfig `json:"list"`
	}

	ListResourceTypeReq struct {
		ProductId      int64 `json:"product_id"`
		RoutingType    uint8 `json:"routing_type"` //0:cb; 1:spx; 2:local
		IsMultiProduct bool  `json:"is_multi_product"`
	}

	ListResourceTypeResp struct {
		LaneCodes    []string         `json:"lane_code_list"`
		ResourceList []*rule.LaneInfo `json:"resource_sub_type_list"`
	}

	CheckLaneEnableReq struct {
		ProductId      int      `json:"product_id"`
		IsMultiProduct bool     `json:"is_multi_product"`
		EnableList     []string `json:"enable_list"`
		DisableList    []string `json:"disable_list"`
	}

	GetSmartRoutingProductListReq struct {
		RoutingType    uint8 `json:"routing_type" binding:"required"`
		IsMultiProduct bool  `json:"is_multi_product"`
	}

	SimpleProductInfo struct {
		ProductID   int64  `json:"product_id"`
		ProductName string `json:"product_name"`
	}

	GetSmartRoutingProductListResp struct {
		List []*SimpleProductInfo `json:"list"`
	}

	GetRedisRequest struct {
		Key string `form:"key" json:"key" binding:"required"`
	}

	ListIlhCombinationInfoRequest struct {
		ProductId int `form:"product_id" json:"product_id" validate:"required,gte=1"`
	}

	IlhCombinationInfo struct {
		ImportIlhList     []rule.BaseLineInfo `json:"import_ilh_list"`
		IlhAndLmGroupList []IlhAndLmGroup     `json:"ilh_and_lm_group_list"`
	}

	IlhAndLmGroup struct {
		Ilh rule.BaseLineInfo `json:"ilh"`
		Lm  rule.BaseLineInfo `json:"lm"`
	}
)

func (l *ListResourceTypeResp) ConvertToRuleListResourceType() *rule.ListResourceType {
	if l == nil {
		return nil
	}
	return &rule.ListResourceType{
		LaneCodes:    l.LaneCodes,
		ResourceList: l.ResourceList,
	}
}

func (r *CreateRuleRequest) ValidateReq() *srerr.Error {
	if r.Priority < rule.MinPriority || r.Priority > rule.MaxPriority {
		return srerr.New(srerr.PriorityNotAvailable, r.ProductID, "priority less than 1 or greater than 1000")
	}
	return nil
}

func (r *UpdateRuleRequest) ValidateReq() *srerr.Error {
	//1.校验rule的权重值，必须在[1,1000]内
	if r.Priority < rule.MinPriority || r.Priority > rule.MaxPriority {
		return srerr.New(srerr.PriorityNotAvailable, r.ID, "update rule")
	}
	//2.检验whs id， zone code的长度
	if len(r.WhsId) > rule.LengthLimit || len(r.ZoneCode) > rule.LengthLimit {
		return srerr.New(srerr.ListParamsLengthLimitExceed, r.ID, "update rule")
	}
	if len(r.ItemCategoryID) > 15 {
		return srerr.New(srerr.ListParamsLengthLimitExceed, r.ID, "update rule")
	}

	//3.校验Dg type 和 parcel dimension
	if r.DgType != int(rule.UndefinedDGFlag) && r.DgType != int(rule.NonDG) && r.DgType != int(rule.DG) {
		return srerr.New(srerr.DgTypeInvalidError, r.ID, "update rule")
	}
	if err := r.ParcelDimension.ValidateParcelDimension(); err != nil {
		return srerr.New(srerr.ParcelDimensionInvalidError, r.ID, "update rule")
	}

	//4.校验rule detail
	if err := r.Validate(); err != nil {
		return err
	}
	sort.Strings(r.WhsId)
	sort.Strings(r.ZoneCode)
	sort.Ints(r.ItemCategoryID)

	return nil
}

func (r *UpdateRuleRequest) Validate() *srerr.Error {
	checkDuplicatePriority := make(map[int32]struct{})
	for _, ruleDetail := range r.RuleDetails.Rules {
		err := r.ValidateDetail(ruleDetail)
		if err != nil {
			return err
		}
		if r.CCMode == rule.CCModeCCRouting {
			_, exist := checkDuplicatePriority[ruleDetail.Priority]
			if exist {
				return srerr.New(srerr.ParamErr, r.ID, "the priority of soft criteria was duplicated.")
			}
			checkDuplicatePriority[ruleDetail.Priority] = struct{}{}
		}
	}
	if err := r.ValidateDefaultCriteria(); err != nil {
		return err
	}
	return nil
}

func (r *UpdateRuleRequest) ValidateDetail(detail *rule.Detail) *srerr.Error {
	//1.校验各种sort开关的权重值是否相等
	if !checkDuplicateSort(detail) {
		return srerr.New(srerr.ParamErr, r.ID, "The priorities of soft criteria(toggle on) can't be the same")
	}
	//SSCSMR-278: ilh rule validate
	if err := ValidateIlhPriorities(detail); err != nil {
		return err
	}

	return nil
}

func checkDuplicateSort(detail *rule.Detail) bool {
	existMap := make(map[int32]bool)
	if detail.DgClassificationEnable && existMap[detail.DgClassificationSort] {
		return false
	}
	if detail.DgClassificationEnable {
		existMap[detail.DgClassificationSort] = true
	}

	if detail.MinVolumeEnable && existMap[detail.MinVolumeSort] {
		return false
	}
	if detail.MinVolumeEnable {
		existMap[detail.MinVolumeSort] = true
	}

	if detail.MaxCapacityEnable && existMap[detail.MaxCapacitySort] {
		return false
	}
	if detail.MaxCapacityEnable {
		existMap[detail.MaxCapacitySort] = true
	}

	if detail.MaxCodCapacityEnable && existMap[detail.MaxCodCapacitySort] {
		return false
	}
	if detail.MaxCodCapacityEnable {
		existMap[detail.MaxCodCapacitySort] = true
	}

	if detail.MaxBulkyCapacityEnable && existMap[detail.MaxBulkyCapacitySort] {
		return false
	}
	if detail.MaxBulkyCapacityEnable {
		existMap[detail.MaxBulkyCapacitySort] = true
	}

	if detail.MaxHighValueCapacityEnable && existMap[detail.MaxHighValueCapacitySort] {
		return false
	}
	if detail.MaxHighValueCapacityEnable {
		existMap[detail.MaxHighValueCapacitySort] = true
	}

	if detail.MaxDgCapacityEnable && existMap[detail.MaxDgCapacitySort] {
		return false
	}
	if detail.MaxDgCapacityEnable {
		existMap[detail.MaxDgCapacitySort] = true
	}
	return true
}

// SSCSMR-278: ilh rule validate
func ValidateIlhPriorities(detail *rule.Detail) *srerr.Error {
	sortMap := make(map[string]int32)
	//开启的时候才校验
	if detail.MinVolumeEnable {
		sortMap["min_volume_sort"] = detail.MinVolumeSort
	}
	if detail.MaxCapacityEnable {
		sortMap["max_capacity_sort"] = detail.MaxCapacitySort
	}
	if detail.MinWeightEnable {
		sortMap["min_weight_sort"] = detail.MinWeightSort
	}
	if detail.MaxWeightCapacityEnable {
		sortMap["max_weight_capacity_sort"] = detail.MaxWeightCapacitySort
	}

	duplicateMap := make(map[int32]struct{})
	var duplicateNameList []string
	for sortName, priority := range sortMap {
		if _, existed := duplicateMap[priority]; existed {
			duplicateNameList = append(duplicateNameList, sortName)
			continue
		} else {
			duplicateMap[priority] = struct{}{}
		}
	}
	if len(duplicateNameList) != 0 {
		errMsg := fmt.Sprintf("same priority existed, duplicated name list:%v", duplicateNameList)
		return srerr.New(srerr.ParamErr, nil, errMsg)
	}

	return nil
}

func (r *UpdateRuleRequest) ValidateDefaultCriteria() *srerr.Error {
	if r.DefaultCriteria != nil {
		switch r.DefaultCriteria.CriteriaType {
		case rule.CriteriaPriority:
			return r.ValidatePriorityCriteria()
		case rule.CriteriaWeightAge:
			// do nothing
		}
	}

	// CC Mode需要校验Priority是否有重复
	if r.CCMode == rule.CCModeCCRouting {
		checkMap := make(map[int]struct{})
		if r.DefaultCriteria == nil {
			return srerr.New(srerr.ParamErr, r.ID, "the priority of default criteria can not be nil.")
		}

		checkMap[r.DefaultCriteria.CombinationPriorityCriteria.Priority] = struct{}{}
		for _, d := range r.DefaultCriteria.WeightageCriteria {
			if d == nil {
				return srerr.New(srerr.ParamErr, r.ID, "the weightageCriteria of default criteria can not be nil.")
			}

			if _, exist := checkMap[d.Priority]; exist {
				return srerr.New(srerr.ParamErr, r.ID, "the priority of default criteria was duplicated.")
			}
			checkMap[d.Priority] = struct{}{}
		}
	}

	return nil
}

func (r *UpdateRuleRequest) ValidatePriorityCriteria() *srerr.Error {
	// 关闭的3PL个数
	disabledNumOf3PL := 0
	for _, info := range r.DisabledInfo {
		disabledNumOf3PL += len(info.LineList)
	}
	// 全部的3PL个数
	totalNumOf3PL := 0
	for _, info := range r.RuleDetails.Rules {
		totalNumOf3PL += len(info.LineLimit)
	}
	// 打开的3PL个数
	enabledNumOf3PL := totalNumOf3PL - disabledNumOf3PL
	// 非0的priority需要从1-N（N为打开的3PL个数），不允许重复
	priorityLookup := make(map[int]bool)
	for priority := 1; priority <= enabledNumOf3PL; priority++ {
		priorityLookup[priority] = true
	}
	for _, criteria := range r.DefaultCriteria.PriorityCriteria {
		for _, info := range criteria.LineInfoList {
			if info.Priority == 0 {
				continue
			}
			if _, ok := priorityLookup[info.Priority]; !ok {
				return srerr.New(srerr.ParamErr, r.ID, "the priority of rule can't be more than enabled number of 3PL when criteria type is priority.")
			}
			delete(priorityLookup, info.Priority)
		}
	}
	return nil
}
