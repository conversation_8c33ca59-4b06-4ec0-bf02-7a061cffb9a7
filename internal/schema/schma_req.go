package schema

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
)

type MaskForecastReq struct {
	OrderData   *masking_forecast.AllocateOrderDataEntity    `json:"order_data"`
	TaskConfig  *allocation.AllocateForecastTaskConfigEntity `json:"task_config"`
	MaskRule    *rule.MaskRule                               `json:"mask_rule"`
	MaskConfig  *config.MaskAllocationConfigTab              `json:"mask_config"`
	ProductInfo *lpsclient.ProductDetailInfo                 `json:"product_info"`
}
