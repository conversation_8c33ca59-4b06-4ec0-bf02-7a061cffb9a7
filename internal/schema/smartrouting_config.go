package schema

import jsoniter "github.com/json-iterator/go"

type SmrCreateRoutingCfgAndRoleMapReq struct {
	ReqType              int                   `json:"req_type"` // [1,2] means [insert update]
	Opetator             string                `json:"opetator"`
	ProductId            int                   `json:"product_id"`
	SellerDisplayName    string                `json:"seller_display_name"`
	ProductRoutingRole   *ProductRoutingRole   `json:"routing_role_info,omitempty"`
	ProductRoutingConfig *ProductRoutingConfig `json:"routing_config,omitempty"`
}

type RoutingRoleInfo struct {
	ResourceSubType int `json:"resource_sub_type"` //线的类型， 包括C_FM, C_LM, C_DFM, C_DLM, L_LM等
	RoutingType     int `json:"routing_type"`      //这条线在Smart Routing中的角色类型
}

// 产品smart routing对应的role信息
type ProductRoutingRole struct {
	CBRoutingRole      []RoutingRoleInfo `json:"cb_routing_role"`
	CBMultiRoutingRole []RoutingRoleInfo `json:"cb_multi_routing_role"`
	SpxRoutingRole     []RoutingRoleInfo `json:"spx_routing_role"`
	LocalRoutingRole   []RoutingRoleInfo `json:"local_routing_role"`
	IlhRoutingRole     []RoutingRoleInfo `json:"ilh_routing_role"`
}

func JsonRoleInfo(routingRole string) []RoutingRoleInfo {
	var roleInfo []RoutingRoleInfo
	if jErr := jsoniter.UnmarshalFromString(routingRole, &roleInfo); jErr != nil {
		return []RoutingRoleInfo{}
	}
	return roleInfo
}

type SmrCreateUpdateCfgRsp struct {
	Retcode int    `json:"retcode" validate:"required"`
	Message string `json:"message"`
}

type ProductRoutingConfig struct {
	CBRoutingEnabled         bool   `json:"cb_routing_enable"`
	CBMultiRoutingEnabled    bool   `json:"cb_multi_routing_enable"`
	LocalSmartRoutingEnabled bool   `json:"local_routing_enable"`
	SpxSmartRoutingEnabled   bool   `json:"spx_routing_enable"`
	IlhSmartRoutingEnabled   bool   `json:"ilh_routing_enable"`
	DefaultLaneCode          string `json:"default_lane_code"`
	SmartRoutingToggle       bool   `json:"smartrouting_toggle"`
}

func ConvertRoutingRole(roleInfo []RoutingRoleInfo) string {
	if len(roleInfo) > 0 {
		roleInfoStr, _ := jsoniter.Marshal(roleInfo)
		return string(roleInfoStr)
	}
	return ""
}
