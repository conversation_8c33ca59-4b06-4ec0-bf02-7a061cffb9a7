package schema

// smart routing debug api
type (
	DeleteHardCriteriaTaskReq struct {
		TaskId int `json:"task_id"`
	}

	DeleteForecastTaskReq struct {
		TaskId int `json:"task_id"`
	}

	DebugSmartRoutingForecastReq struct {
		ProductId        int    `json:"product_id"`
		StartDate        string `json:"start_date"`
		EndDate          string `json:"end_date"`
		RoutingType      uint8  `json:"routing_type"`
		IsMultiProduct   bool   `json:"is_multi_product"`
		TaskId           int    `json:"task_id"`
		ShipmentResource int    `json:"shipment_resource"`
	}

	GetMaskingHbaseDataReq struct {
		RequestTime   int64  `json:"request_time"  validate:"required"`
		MaskProductId int    `json:"mask_product_id"  validate:"required"`
		RequestId     string `json:"request_id"  validate:"required"`
	}

	ExecuteBatchAllocateReq struct {
		ShardingNo int32 `json:"sharding_no"`
	}

	GetRoutingLogHbaseDataReq struct {
		RowKey string `json:"row_key"`
	}
)

type RefreshS3UrlReq struct {
	Url string `json:"url"`
}

type AllocateScheduleVisualReq struct {
	MsgText      string `json:"msg_text"`
	OffsetId     int64  `json:"offset_id"`
	BusinessType int64  `json:"business_type"`
}

type BatchPullOrdersReq struct {
	OrderList []uint64 `json:"order_list"`
	Date      string   `json:"date"` //2006-01-02
	Region    string   `json:"region"`
}

func (b *BatchPullOrdersReq) GetOrderIdList() []uint64 {
	if len(b.OrderList) == 0 {
		return make([]uint64, 0)
	}

	return b.OrderList
}

type CheckoutMaskingCountReq struct {
	MsgBusMessage                string `json:"msg_bus_message"`
	NewOFOrderStatusEvent        string `json:"new_of_order_status_event"`
	NewOFOrderStatusEventExtInfo string `json:"new_of_order_status_event_ext_info"`
	OldOFOrderStatusEvent        string `json:"old_of_order_status_event"`
	OldOFOrderStatusEventExtInfo string `json:"old_of_order_status_event_ext_info"`

	MsgText string `json:"msg_text"`
}

type DebugMakeUpAsyncAllocationLogReq struct {
	MsgText   string `json:"msg_text" validate:"required"`
	OrderList string `json:"order_list" validate:"required"`
}

type DebugClearMaskingVolumeReq struct {
	ShardingParam string `json:"sharding_param"`
}

type DebugSyncMaskingVolumeReq struct {
	StartDay string `json:"start_day" validate:"required"`
	EndDay   string `json:"end_day" validate:"required"`
	NewToOld bool   `json:"new_to_old"`
}
