package schema

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"

type VolumeRoutingHistoryQueryParam struct {
	Offset      int64 `json:"offset" validate:"min=0"`
	Size        int64 `json:"size" validate:"required"`
	RoutingType int   `json:"routing_type"`
}

type (
	VolumeRoutingHistoryPageResult struct {
		Offset int64                           `json:"offset"`
		Total  int64                           `json:"total"`
		Size   int64                           `json:"size"`
		List   []VolumeRoutingHistoryPageModel `json:"list"`
	}

	VolumeRoutingHistoryPageModel struct {
		TaskId        string                       `json:"task_id"`
		OperationType enum.VolumeTaskOperationType `json:"operation_type,omitempty"`
		ZoneGroupId   string                       `json:"zone_group_id,omitempty"`
		FileName      string                       `json:"file_name,omitempty"`
		ErrMsg        string                       `json:"err_msg"`
		Status        enum.VolumeTaskStatus        `json:"status,omitempty"`
		Operator      string                       `json:"operator"`
		OperationTime string                       `json:"operation_time"`
	}
)

type VolumeRoutingHistoryDownloadParam struct {
	TaskId string `json:"task_id" validate:"required"`
}
