package schema

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

type ZoneRulePageParam struct {
	RuleId         uint64                `json:"rule_id"`
	RuleName       string                `json:"rule_name"`
	ProductId      int64                 `json:"product_id"`
	Status         enum.VolumeRuleStatus `json:"status"`
	RuleType       enum.VolumeRuleType   `json:"rule_type"`
	Offset         int64                 `json:"offset" validate:"min=0"`
	Size           int64                 `json:"size" validate:"required"`
	RoutingType    int                   `json:"routing_type"`
	IsForecastType bool                  `json:"is_forecast_type"`
}

func (p *ZoneRulePageParam) FormatQueryCondition() map[string]interface{} {
	query := make(map[string]interface{})
	if p.RuleId > 0 {
		query["id = ?"] = p.RuleId
	}
	if p.RuleName != "" {
		query["locate(?, rule_name) > 0"] = p.RuleName
	}
	if p.ProductId > 0 {
		query[fmt.Sprintf("(product_id = ? OR JSON_CONTAINS(product_id_list,'%d','$'))", p.ProductId)] = p.ProductId
	}
	if p.Status > 0 {
		query["`rule_status` = ?"] = p.Status
	}
	if p.RuleType > 0 {
		query["rule_type = ?"] = p.RuleType
	}
	query[constant.RoutingTypeSql] = p.RoutingType
	if p.RoutingType != rule.CBRoutingType { // 如果是cb的Forecast和列表都要读取出来
		query[constant.IsForecastTypeSql] = p.IsForecastType
	}
	return query
}

type (
	ZoneRulePageResult struct {
		Offset int64               `json:"offset"`
		Total  int64               `json:"total"`
		Size   int64               `json:"size"`
		List   []ZoneRulePageModel `json:"list"`
	}

	ZoneRulePageModel struct {
		RuleId             uint64                `json:"rule_id,omitempty"`
		RuleName           string                `json:"rule_name,omitempty"`
		ProductId          int64                 `json:"product_id,omitempty"`
		ProductIdList      []int64               `json:"product_id_list,omitempty"`
		ShareVolume        bool                  `json:"share_volume"`
		Priority           int64                 `json:"priority,omitempty"`
		RuleType           enum.VolumeRuleType   `json:"rule_type,omitempty"`
		Status             enum.VolumeRuleStatus `json:"status,omitempty"`
		EffectiveStartTime int64                 `json:"effective_start_time,omitempty"`
		Operator           string                `json:"operator"`
		UpdateTime         string                `json:"update_time"`
	}
)

type ZoneRuleCreateParam struct {
	RuleName       string  `json:"rule_name"`
	ProductId      int64   `json:"product_id"`
	ProductIdList  []int64 `json:"product_id_list"`
	ShareVolume    bool    `json:"share_volume"`
	RoutingType    int     `json:"routing_type"`
	IsForecastType bool    `json:"is_forecast_type"`
}

type ZoneRuleCreateResult struct {
	RuleId uint64 `json:"rule_id"`
}

type ZoneRuleExportTemplateParam struct {
	ProductIdList string `json:"product_id_list" validate:"required"`
	RoutingType   int    `json:"routing_type"`
}

type (
	//ok,empty
	ZoneRuleEditParam struct {
		RuleId               uint64                         `json:"rule_id"`
		RuleName             string                         `json:"rule_name"`
		RuleType             enum.VolumeRuleType            `json:"rule_type"`
		ProductId            int64                          `json:"product_id"`
		ProductIdList        []int64                        `json:"product_id_list"`
		ShareVolume          bool                           `json:"share_volume"`
		Priority             int64                          `json:"priority"`
		EffectiveStartTime   int64                          `json:"effective_start_time"`
		EffectiveImmediately bool                           `json:"effective_immediately"`
		LineLimit            map[string][]ZoneRuleLineLimit `json:"line_limit"`
		ZoneLimit            *ZoneLimitImportFile           `json:"zone_limit"`
		DataVersion          int64                          `json:"data_version"`
		RoutingType          int                            `json:"routing_type"`
		IsForecastType       bool                           `json:"is_forecast_type"`
	}

	ZoneRuleLineLimit struct {
		LineId                 string  `json:"line_id"`
		ProductIdList          []int64 `json:"product_id_list"`
		MinDailyLimit          int64   `json:"min_daily_limit" validate:"required,min=0"`
		MaxDailyLimit          int64   `json:"max_daily_limit" validate:"required,min=0"`
		MaxCodDailyLimit       int64   `json:"max_cod_daily_limit" validate:"required,min=0"`
		MaxBulkyDailyLimit     int64   `json:"max_bulky_daily_limit" validate:"required,min=0"`
		MaxHighValueDailyLimit int64   `json:"max_high_value_daily_limit" validate:"required,min=0"`
		MaxDgDailyLimit        int64   `json:"max_dg_daily_limit" validate:"required,min=0"`
	}

	ZoneLimitImportFile struct {
		FilePath string `json:"file_path"`
		FileName string `json:"file_name"`
	}
)

// for rule delete, rule disable
type ZoneRuleIdParam struct {
	RuleId uint64 `json:"rule_id" validate:"required"`
}

type RuleListRequest struct {
	ProductId      uint64 `json:"product_id" validate:"required"`
	PageSize       int    `json:"page_size"`
	RuleStatusList []int  `json:"rule_status_list" validate:"required"`
	RoutingType    int    `json:"routing_type"`
}

type RuleListResponse struct {
	ProductId     int64                 `json:"product_id"`
	ProductIdList []int64               `json:"product_id_list"`
	ShareVolume   bool                  `json:"share_volume"`
	RuleId        uint64                `json:"rule_id"`
	RuleName      string                `json:"rule_name"`
	RuleStatus    enum.VolumeRuleStatus `json:"rule_status"`
}

type ZoneRuleDisableCheckParam struct {
	RuleId        uint64  `json:"rule_id" validate:"required"`
	ProductId     int64   `json:"product_id"`
	ProductIdList []int64 `json:"product_id_list"`
	ShareVolume   bool    `json:"share_volume"`
}

type ZoneRuleDisableCheckResult struct {
	ProductActive bool `json:"product_active"`
}

type ZoneRuleLimitPageParam struct {
	RuleId         uint64 `json:"rule_id" validate:"required"`
	LineId         string `json:"line_id"`
	ZoneGroupId    string `json:"zone_group_id"`
	ZoneName       string `json:"zone_name"`
	LineType       int    `json:"line_type"`
	Offset         int64  `json:"offset" validate:"min=0"`
	Size           int64  `json:"size" validate:"required"`
	DataVersion    int64  `json:"data_version"`
	RoutingType    int    `json:"routing_type"`
	IsForecastType bool   `json:"is_forecast_type"`
}

func (p *ZoneRuleLimitPageParam) FormatQueryCondition() map[string]interface{} {
	query := map[string]interface{}{
		"rule_id = ?": p.RuleId,
	}
	if p.LineId != "" {
		query["line_id = ?"] = p.LineId
	}
	if p.LineType > 0 {
		query["line_type = ?"] = p.LineType
	}
	if p.ZoneGroupId != "" {
		query["group_id = ?"] = p.ZoneGroupId
	}
	if p.ZoneName != "" {
		query["zone_name = ?"] = p.ZoneName
	} else {
		query["zone_name <> ?"] = ""
	}
	query["data_version = ?"] = p.DataVersion
	return query
}

type (
	ZoneRuleLimitPageResult struct {
		Offset int64                    `json:"offset"`
		Total  int64                    `json:"total"`
		Size   int64                    `json:"size"`
		List   []ZoneRuleLimitPageModel `json:"list"`
	}

	ZoneRuleLimitPageModel struct {
		LineId                 string  `json:"line_id,omitempty"`
		LineType               int     `json:"line_type,omitempty"`
		ZoneGroupId            string  `json:"zone_group_id,omitempty"`
		ZoneName               string  `json:"zone_name,omitempty"`
		MinDailyLimit          int64   `json:"min_daily_limit"`
		MaxDailyLimit          int64   `json:"max_daily_limit"`
		ProductIdList          []int64 `json:"product_id_list"`
		MaxCodDailyLimit       int64   `json:"max_cod_daily_limit" validate:"required,min=1"`
		MaxBulkyDailyLimit     int64   `json:"max_bulky_daily_limit" validate:"required,min=1"`
		MaxHighValueDailyLimit int64   `json:"max_high_value_daily_limit" validate:"required,min=1"`
		MaxDgDailyLimit        int64   `json:"max_dg_daily_limit" validate:"required,min=1"`
	}
)

type ZoneRuleLimitImportParam struct {
	RuleId        uint64  `json:"rule_id" validate:"required"`
	FileUrl       string  `json:"file_path" validate:"required,UssHostValidate"`
	FileName      string  `json:"file_name" validate:"required"`
	ProductIdList []int64 `json:"product_id_list"`
	RoutingType   int     `json:"routing_type"`
}

type LineInfoRequest struct {
	ProductId int64 `json:"product_id" validate:"required"`
}

type LineInfoResponse struct {
	LineId        string `json:"line_id"`
	LineName      string `json:"line_name"`
	LineType      int    `json:"line_type"`
	MinDailyLimit int64  `json:"min_daily_limit,omitempty" validate:"required,min=1"`
	MaxDailyLimit int64  `json:"max_daily_limit,omitempty" validate:"required,min=1"`
}

type ZoneRuleLimitImportResult struct {
	DataVersion     int64    `json:"data_version"`
	CheckResultList []string `json:"check_result_list"`
}

type ZoneRuleLimitExportParam struct {
	RuleId         uint64 `json:"rule_id" validate:"required"`
	LineId         string `json:"line_id"`
	ZoneGroupId    string `json:"zone_group_id"`
	ZoneName       string `json:"zone_name"`
	LineType       int    `json:"line_type"`
	DataVersion    int64  `json:"data_version"`
	RoutingType    int    `json:"routing_type"`
	IsForecastType bool   `json:"is_forecast_type"`
}

func (p *ZoneRuleLimitExportParam) FormatQueryCondition() map[string]interface{} {
	query := map[string]interface{}{
		"rule_id = ?":      p.RuleId,
		"data_version = ?": p.DataVersion,
	}
	if p.LineId != "" {
		query["line_id = ?"] = p.LineId
	}
	if p.LineType > 0 {
		query["line_type = ?"] = p.LineType
	}
	if p.ZoneGroupId != "" {
		query["group_id = ?"] = p.ZoneGroupId
	}
	if p.ZoneName != "" {
		query["zone_name = ?"] = p.ZoneName
	} else {
		query["zone_name <> ?"] = ""
	}
	return query
}

type ZoneRuleInfo struct {
	RuleId               uint64                         `json:"rule_id,omitempty"`
	RuleName             string                         `json:"rule_name,omitempty"`
	ZoneGroupId          string                         `json:"zone_group_id,omitempty"`
	ProductId            int64                          `json:"product_id,omitempty"`
	ProductIdList        []int64                        `json:"product_id_list,omitempty"`
	ShareVolume          bool                           `json:"share_volume,omitempty"`
	RuleType             enum.VolumeRuleType            `json:"rule_type,omitempty"`
	Priority             int64                          `json:"priority,omitempty"`
	EffectiveStartTime   int64                          `json:"effective_start_time,omitempty"`
	EffectiveImmediately bool                           `json:"effective_immediately"`
	LineLimit            map[string][]ZoneRuleLineLimit `json:"line_limit,omitempty"`
	DataVersion          int64                          `json:"data_version"`
}

type VolumeRuleDetailImportParam struct {
	RuleId   uint64 `json:"rule_id"`
	FilePath string `json:"file_path"`
}

type CopyVolumeRuleParam struct {
	RuleId uint64 `json:"rule_id"`
}

type CheckGroupUpdateVolume struct {
	RuleId uint64 `json:"rule_id"`
}

type GetAllGroupParam struct {
	RoutingType    int  `json:"routing_type"`
	IsForecastType bool `json:"is_forecast_type"`
}

type GroupDetail struct {
	GroupId   string `json:"group_id"`
	GroupName string `json:"group_name"`
}
