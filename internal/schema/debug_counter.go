package schema

type DebugGetCounter struct {
	//reads-cks
	//ProductId string `json:"product_id" form:"product_id"`
	//LineId    string `json:"line_id" form:""`
	//ZoneName  string `json:"zone_name"`
	Key string `json:"key,omitempty" form:"key"`
}

type RecoverRedisCounter struct {
	// 天数
	Days int64 `json:"days" form:"days" validate:"required"`
}

const (
	Incr = iota + 1
	Decr
	IncrBy
	HIncrBy
)

type RedisCounterOption struct {
	Option int   `json:"option" validate:"required"`
	Count  int64 `json:"count"`
}
