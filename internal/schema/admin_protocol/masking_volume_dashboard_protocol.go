package admin_protocol

type SearchMaskingVolumeRequest struct {
	StartTime              string   `json:"start_time" validate:"required"`
	EndTime                string   `json:"end_time" validate:"required"`
	ProductType            uint8    `json:"product_type" validate:"required,min=1,max=2"`
	MaskingProductList     []int64  `json:"masking_product_list" validate:"required"`
	FulfillmentProductList []int64  `json:"fulfillment_product_list"`
	GroupCodeList          []string `json:"group_code_list"`
	//SSCSMR-2371: zone\route\shop group id 只能三选一
	ZoneCodeList    []string `json:"zone_code_list"`
	RouteCodeList   []string `json:"route_code_list"`
	ShopGroupIdList []string `json:"shop_group_id_list"`
	BusinessType    uint8    `json:"business_type" validate:"omitempty,oneof=0 1 2"`
}

type SearchMaskingVolumeResp struct {
	StatTime     TableValue     `json:"stat_time"`
	TotalOrder   []TotalOrder   `json:"total_order"`
	Distribution []Distribution `json:"distribution"`
}

type TableValue struct {
	Title string   `json:"title"`
	Value []string `json:"value"`
}

type TotalOrder struct {
	Title string   `json:"title"`
	Value []*int64 `json:"value"`
}

type Distribution struct {
	Title string     `json:"title"`
	Value []*float64 `json:"value"`
}

type MaskingVolumeDict struct {
	MaskingProductInfo []MaskingProductInfo `json:"masking_product_info"`
}

type MaskingProductInfo struct {
	MaskingProductId           int64                    `json:"masking_product_id"`
	MaskingProductName         string                   `json:"masking_product_name"`
	FulfillmentProductInfoList []FulfillmentProductInfo `json:"fulfillment_product_info_list"`
}

type FulfillmentProductInfo struct {
	FulfillmentProductId   int64  `json:"fulfillment_product_id"`
	FulfillmentProductName string `json:"fulfillment_product_name"`
	GroupCode              string `json:"group_code"`
}
