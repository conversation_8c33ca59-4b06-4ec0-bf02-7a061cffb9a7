package admin_protocol

type SearchByDayRequest struct {
	StartDay    string   `json:"start_day" validate:"required"`
	EndDay      string   `json:"end_day" validate:"required"`
	BarType     uint8    `json:"bar_type" validate:"required"`
	BarValue    string   `json:"bar_value" validate:"required"`
	SlicerType  uint8    `json:"slicer_type"`
	SlicerValue []string `json:"slicer_value"`
}

type RoutingSearchDataResp struct {
	TotalOrder   []RoutingTotalOrder   `json:"total_order"`
	Distribution []RoutingDistribution `json:"distribution"`
}

type RoutingTotalOrder struct {
	Title string   `json:"title"`
	Value []string `json:"value"`
}

type RoutingDistribution struct {
	Title string   `json:"title"`
	Value []string `json:"value"`
}

type SearchByHourRequest struct {
	ProductId  int64               `json:"product_id"`
	FilterList []RequestFilterInfo `json:"filter_list"`
}

type RequestFilterInfo struct {
	FilterType  uint8    `json:"filter_type"`
	FilterValue []string `json:"filter_value"`
}

type RoutingDictResp struct {
	FilterList []FilterList `json:"filter_list"`
	Product    []FilterInfo `json:"product"`
}

type FilterList struct {
	FilterType  uint8        `json:"filter_type"`
	FilterName  string       `json:"filter_name"`
	FilterValue []FilterInfo `json:"filter_value"`
}

type FilterInfo struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Entity int    `json:"entity"`
}

type RoutingProductRelateDictRequest struct {
	ProductId int64 `json:"product_id"`
}

type RoutingProductRelateDictResp struct {
	FilterType  uint8        `json:"filter_type"`
	FilterName  string       `json:"filter_name"`
	FilterValue []FilterInfo `json:"filter_value"`
}

type StatTimeResult struct {
	Title    string `json:"title"`
	OrderNum int64  `json:"order_num"`
}
