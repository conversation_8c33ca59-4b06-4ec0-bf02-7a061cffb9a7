package admin_protocol

type GetFlowStatRequest struct {
	ScheduleDimension string `json:"schedule_dimension"`
	ScheduleResult    string `json:"schedule_result"`
	BusinessType      uint   `json:"business_type" validate:"required,min=1,max=2"`
	BusinessId        string `json:"business_id" validate:"required"`
}

type GetFlowStatResp struct {
	ScheduleResult             string      `json:"schedule_result"`
	EnterTotallyOrder          int64       `json:"enter_totally_order"`
	PassHardCriteriaCount      int64       `json:"pass_hard_criteria_count"`
	PassToggleCheckCount       int64       `json:"pass_toggle_check_count"`
	EnterSoftCriteriaCount     int64       `json:"enter_soft_criteria_count"`
	EnterDefaultCriteriaCount  int64       `json:"enter_default_criteria_count"`
	ScheduleTotallyOrder       int64       `json:"schedule_totally_order"`
	ScheduleBeforeSoftCriteria int64       `json:"schedule_before_soft_criteria"`
	ScheduleBySoftCriteria     int64       `json:"schedule_by_soft_criteria"`
	ScheduleByDefaultCriteria  int64       `json:"schedule_by_default_criteria"`
	ScheduleFailed             int64       `json:"schedule_failed"`
	Subs                       []*FlowStat `json:"subs"`
}

type FlowStat struct {
	ScheduleResult             string `json:"schedule_result"`
	EnterTotallyOrder          int64  `json:"enter_totally_order"`
	PassHardCriteriaCount      int64  `json:"pass_hard_criteria_count"`
	PassToggleCheckCount       int64  `json:"pass_toggle_check_count"`
	EnterSoftCriteriaCount     int64  `json:"enter_soft_criteria_count"`
	EnterDefaultCriteriaCount  int64  `json:"enter_default_criteria_count"`
	ScheduleTotallyOrder       int64  `json:"schedule_totally_order"`
	ScheduleBeforeSoftCriteria int64  `json:"schedule_before_soft_criteria"`
	ScheduleBySoftCriteria     int64  `json:"schedule_by_soft_criteria"`
	ScheduleByDefaultCriteria  int64  `json:"schedule_by_default_criteria"`
	ScheduleFailed             int64  `json:"schedule_failed"`
}

type GetResultStatRequest struct {
	ScheduleDimension string `json:"schedule_dimension"`
	ScheduleResult    string `json:"schedule_result"`
	BusinessType      uint   `json:"business_type" validate:"required,min=1,max=2"`
	BusinessId        string `json:"business_id" validate:"required"`
	ScheduleFactor    string `json:"schedule_factor"`
}

type GetResultStatResp struct {
	ScheduleDimension string                `json:"schedule_dimension"`
	Count             int64                 `json:"count"`
	Subs              []*ScheduleResultResp `json:"subs"`
}

type ScheduleResultResp struct {
	ScheduleResult     string                    `json:"schedule_result"`
	Count              int64                     `json:"count"`
	ScheduleFactorStat []*ScheduleFactorStatResp `json:"schedule_factor_stat"`
}

type ScheduleFactorStatResp struct {
	ScheduleFactor     string                    `json:"schedule_factor"`
	ScheduleFactorName string                    `json:"schedule_factor_name"`
	Count              int64                     `json:"count"`
	Subs               []*ScheduleFactorStatResp `json:"subs"`
}

type ScheduleVisualExport struct {
	Url string `json:"url"`
}
