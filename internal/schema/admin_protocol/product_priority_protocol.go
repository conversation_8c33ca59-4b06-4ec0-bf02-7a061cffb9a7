package admin_protocol

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ListRequest struct {
	Page          uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count         uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	MaskProductId int32    `protobuf:"varint,3,opt,name=mask_product_id,json=maskProductId,proto3" json:"mask_product_id,omitempty"`
	ShopGroupId   int32    `protobuf:"varint,4,opt,name=shop_group_id,json=shopGroupId,proto3" json:"shop_group_id,omitempty"`
	RuleType      uint32   `protobuf:"varint,5,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	StatusList    []uint32 `protobuf:"varint,6,rep,packed,name=status_list,json=statusList,proto3" json:"status_list,omitempty"`
}

func (l *ListRequest) Validate() *srerr.Error {
	if l.Page <= 0 {
		err := srerr.New(srerr.ParamErr, nil, "Page value must be greater than 0")
		return err
	}
	if l.Count <= 0 {
		err := srerr.New(srerr.ParamErr, nil, "Count value must be greater than 0")
		return err
	}

	return nil
}

type Priority struct {
	Id                 uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	MaskProductId      int32  `protobuf:"varint,2,opt,name=mask_product_id,json=maskProductId,proto3" json:"mask_product_id"`
	MaskProductName    string `protobuf:"bytes,3,opt,name=mask_product_name,json=maskProductName,proto3" json:"mask_product_name"`
	ShopGroupId        int32  `protobuf:"varint,4,opt,name=shop_group_id,json=shopGroupId,proto3" json:"shop_group_id"`
	ShopGroupName      string `protobuf:"bytes,5,opt,name=shop_group_name,json=shopGroupName,proto3" json:"shop_group_name"`
	RuleType           uint32 `protobuf:"varint,6,opt,name=rule_type,json=ruleType,proto3" json:"rule_type"`
	Operator           string `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator"`
	Ctime              int32  `protobuf:"varint,8,opt,name=ctime,proto3" json:"ctime"`
	Mtime              int32  `protobuf:"varint,9,opt,name=mtime,proto3" json:"mtime"`
	Status             uint32 `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	EffectiveStartTime int32  `protobuf:"varint,11,opt,name=effective_start_time,json=effectiveStartTime,proto3" json:"effective_start_time"`
	ForecastTaskId     int32  `protobuf:"varint,12,opt,name=forecast_task_id,json=forecastTaskId,proto3" json:"forecast_task_id"`
}

type ListResponse struct {
	Page              uint32      `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	Count             uint32      `protobuf:"varint,2,opt,name=count,proto3" json:"count"`
	Total             uint32      `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	ProductPriorities []*Priority `protobuf:"bytes,4,rep,name=product_priorities,json=productPriorities,proto3" json:"product_priorities"`
}

type DetailRequest struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

type DetailResponse struct {
	EffectStartTime int       `json:"effective_start_time"`
	RuleType        uint32    `protobuf:"varint,1,opt,name=rule_type,json=ruleType,proto3" json:"rule_type"`
	PriorityDetails []*Detail `protobuf:"bytes,2,rep,name=priority_details,json=priorityDetails,proto3" json:"priority_details"`
}

type Detail struct {
	Id        int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Priority  uint32 `protobuf:"varint,3,opt,name=priority,proto3" json:"priority"`
	Weightage uint32 `protobuf:"varint,4,opt,name=weightage,proto3" json:"weightage"`
	Status    uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
}

func (d *DetailRequest) Validate() *srerr.Error {
	if d.GetId() == 0 {
		return srerr.New(srerr.ParamErr, nil, "value must not be")
	}

	return nil
}

func (d *DetailRequest) GetId() uint64 {
	if d == nil {
		return 0
	}
	return d.Id
}

type (
	CreateRequest struct {
		MaskProductId        int32             `protobuf:"varint,1,opt,name=mask_product_id,json=maskProductId,proto3" json:"mask_product_id,omitempty"`
		ShopGroupId          int32             `protobuf:"varint,2,opt,name=shop_group_id,json=shopGroupId,proto3" json:"shop_group_id,omitempty"`
		RuleType             uint32            `protobuf:"varint,3,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
		PriorityDetails      []*DetailOfCreate `protobuf:"bytes,4,rep,name=priority_details,json=priorityDetails,proto3" json:"priority_details,omitempty"`
		EffectiveImmediately bool              `json:"effective_immediately"`
		EffectiveStartTime   int               `json:"effective_start_time"`
	}

	DetailOfCreate struct {
		Id        int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
		Priority  uint32 `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`
		Weightage uint32 `protobuf:"varint,3,opt,name=weightage,proto3" json:"weightage,omitempty"`
		Status    uint32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	}
)

func (c *CreateRequest) Validate() *srerr.Error {
	if c.GetMaskProductId() == 0 {
		return srerr.New(srerr.ParamErr, nil, "mask product id must not be 0")
	}
	if c.GetShopGroupId() == 0 {
		return srerr.New(srerr.ParamErr, nil, "shop group id must not be 0")
	}
	if c.GetRuleType() == 0 {
		return srerr.New(srerr.ParamErr, nil, "rule type must not be 0")
	}
	if len(c.GetPriorityDetails()) < 1 {
		return srerr.New(srerr.ParamErr, nil, "length of priority details must greater than 0")
	}
	return nil
}

func (c *CreateRequest) GetMaskProductId() int32 {
	if c == nil {
		return 0
	}
	return c.MaskProductId
}

func (c *CreateRequest) GetShopGroupId() int32 {
	if c == nil {
		return 0
	}
	return c.ShopGroupId
}

func (c *CreateRequest) GetRuleType() uint32 {
	if c == nil {
		return 0
	}
	return c.RuleType
}

func (c *CreateRequest) GetPriorityDetails() []*DetailOfCreate {
	if c == nil {
		return make([]*DetailOfCreate, 0)
	}
	return c.PriorityDetails
}

type (
	UpdateRequest struct {
		Id                   uint64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
		RuleType             uint32            `protobuf:"varint,2,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
		PriorityDetails      []*DetailOfUpdate `protobuf:"bytes,3,rep,name=priority_details,json=priorityDetails,proto3" json:"priority_details,omitempty"`
		EffectiveImmediately bool              `json:"effective_immediately"`
		EffectiveStartTime   int               `json:"effective_start_time"`
	}

	DetailOfUpdate struct {
		Id        int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
		Priority  uint32 `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`
		Weightage uint32 `protobuf:"varint,3,opt,name=weightage,proto3" json:"weightage,omitempty"`
		Status    uint32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	}
)

func (m *UpdateRequest) Validate() *srerr.Error {
	if m.GetId() == 0 {
		return srerr.New(srerr.ParamErr, nil, "mask product id must not be 0")
	}
	if m.GetRuleType() == 0 {
		return srerr.New(srerr.ParamErr, nil, "rule type must not be 0")
	}
	if len(m.GetPriorityDetails()) < 1 {
		return srerr.New(srerr.ParamErr, nil, "priority details must contain at least 1 item(s)")
	}
	return nil
}

func (m *UpdateRequest) GetId() uint64 {
	if m == nil {
		return 0
	}
	return m.Id
}

func (m *UpdateRequest) GetRuleType() uint32 {
	if m == nil {
		return 0
	}
	return m.RuleType
}

func (m *UpdateRequest) GetPriorityDetails() []*DetailOfUpdate {
	if m == nil {
		return make([]*DetailOfUpdate, 0)
	}
	return m.PriorityDetails
}

type ImportValidateRequest struct {
	MaskingProductId int64  `json:"masking_product_id" validate:"required"`
	ActionMode       uint8  `json:"action_mode" validate:"required,min=1,max=2"`
	Url              string `json:"url" validate:"required,UssHostValidate"`
}

type ImportValidateResponse struct {
	Url           string `json:"url"`
	IsPartSuccess bool   `json:"is_part_success"`
}

type ImportRequest struct {
	MaskingProductId int64  `json:"masking_product_id" validate:"required"`
	ActionMode       uint8  `json:"action_mode" validate:"required,min=1,max=2"`
	Url              string `json:"url" validate:"required,UssHostValidate"`
}

type ExportRequest struct {
	Ids              []int64 `json:"ids"`
	ExportAllData    bool    `json:"export_all_data"`
	MaskingProductId int64   `json:"masking_product_id"`
	ShopGroupId      int64   `json:"shop_group_id"`
	RuleType         int32   `json:"rule_type"`
	StatusList       []int32 `json:"status_list"`
}

type ExportResponse struct {
	Url string `json:"url"`
}

type ExportTemplateRequest struct {
	MaskingProductId int64 `json:"masking_product_id"`
}

type ExportTemplateResponse struct {
	Url string `json:"url"`
}

type ListActiveGroupId struct {
	MaskingProductId int64 `json:"masking_product_id"`
}

type ListActiveGroupIdRsp struct {
	GroupIdList []int64 `json:"groupid_list"`
}
