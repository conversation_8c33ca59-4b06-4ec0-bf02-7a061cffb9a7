package schema

type GetUnioncfg struct {
	ProductId int64 `json:"product_id" validate:"required"`
}

type GetUnionCfgRsp struct {
	ProductRoutingRole        *ProductRoutingRole       `json:"routing_role_info,omitempty"`
	ProductRoutingConfig      *ProductRoutingConfig     `json:"routing_config,omitempty"`
	SmrScenesWithEnabledRules *SmrScenesWithActiveRules `json:"routing_scenes_with_active_rules"`
}

type SmrScenesWithActiveRules struct {
	CBRoutingEnabled         bool `json:"cb_routing_enable"`
	CBMultiRoutingEnabled    bool `json:"cb_multi_routing_enable"`
	LocalSmartRoutingEnabled bool `json:"local_routing_enable"`
	SpxSmartRoutingEnabled   bool `json:"spx_routing_enable"`
	IlhSmartRoutingEnabled   bool `json:"ilh_routing_enable"`
}
