package masking_panel

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"

type (
	//GetKeyDataRequest :对于所有的时间段，前端都会传该段时间的start date的0点到end date的23点59分59秒
	GetKeyDataRequest struct {
		StartTime          *int64  `json:"start_time"`       //unix time
		EndTime            *int64  `json:"end_time"`         //unix time
		MaskProductIDs     []int64 `json:"mask_product_ids"` //masking product id list
		ProductIDs         []int64 `json:"product_ids"`      //fulfillment product id list
		ShopGroupID        *int64  `json:"shop_group_id"`
		SoftCriteriaRuleID *uint64 `json:"soft_criteria_rule_id"`
		VolumeRuleID       *uint64 `json:"volume_rule_id"`
		ZoneCode           *string `json:"zone_code"`
		RouteCode          *string `json:"route_code"`
		Operator           *string `json:"operator"` //operator is from smart routing system, not from fe
		RequestID          string  `json:"request_id"`
		TaskID             uint64  `json:"task_id"` //used for exporting data
	}

	GetKeyDataResponse struct {
		List []AggregatedKeyData `json:"list"`
	}

	AggregatedKeyData struct {
		GraphName string            `json:"graph_name"` //current element's name
		LineList  []MaskingDataLine `json:"line_list"`
	}

	MaskingDataLine struct {
		LineID        string            `json:"line_id"`   //product id. A line is about the total info for a product
		LineName      string            `json:"line_name"` //product name
		AggregateData []MaskingLineItem `json:"aggregate_data"`
	}

	MaskingLineItem struct {
		XAxis string `json:"x_axis"` //x-axis, means time unit  ps:x轴坐标（从原点到最大，按顺序，每一个点都需要有数据，因为前端根据这个字段获取横坐标轴）
		YAxis string `json:"y_axis"` //y-axis, means order count
	}
)

type (
	CreateExportTaskReq struct {
		TaskStatus        enum.TaskStatus `json:"task_status"`
		TaskType          enum.TaskType   `json:"task_type"`
		TaskBusinessScene string          `json:"task_business_scene"`
		LastOperator      string          `json:"last_operator"`
		LastOperateTime   int64           `json:"last_operate_time"`
		ErrorMessage      string          `json:"error_message"`
		DownloadUrl       string          `json:"download_url"`
	}
)

func (p *GetKeyDataRequest) GetOperator() string {
	if p == nil {
		return ""
	}
	if p.Operator == nil {
		return ""
	}
	return *p.Operator
}
