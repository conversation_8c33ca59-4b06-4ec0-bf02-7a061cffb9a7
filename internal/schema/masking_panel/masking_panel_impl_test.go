package masking_panel

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/gogo/protobuf/proto"
	"reflect"
	"testing"
)

func TestGetKeyDataRequest_Validate(t *testing.T) {
	type fields struct {
		StartTime          *int64
		EndTime            *int64
		MaskProductIDs     []int64
		ProductIDs         []int64
		ShopGroupID        *int64
		SoftCriteriaRuleID *uint64
		VolumeRuleID       *uint64
		ZoneCode           *string
		RouteCode          *string
	}
	type args struct {
		ctx context.Context
	}
	testFileds := fields{
		StartTime:          nil,
		EndTime:            proto.Int64(1),
		MaskProductIDs:     []int64{},
		ProductIDs:         []int64{},
		ShopGroupID:        proto.Int64(1),
		SoftCriteriaRuleID: proto.Uint64(1),
		VolumeRuleID:       proto.Uint64(1),
		ZoneCode:           proto.String("haha"),
		RouteCode:          proto.String("wahaha"),
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name:   "test",
			fields: testFileds,
			want:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &GetKeyDataRequest{
				StartTime:          tt.fields.StartTime,
				EndTime:            tt.fields.EndTime,
				MaskProductIDs:     tt.fields.MaskProductIDs,
				ProductIDs:         tt.fields.ProductIDs,
				ShopGroupID:        tt.fields.ShopGroupID,
				SoftCriteriaRuleID: tt.fields.SoftCriteriaRuleID,
				VolumeRuleID:       tt.fields.VolumeRuleID,
				ZoneCode:           tt.fields.ZoneCode,
				RouteCode:          tt.fields.RouteCode,
			}
			if got := p.Validate(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Validate() = %v, want %v", got, tt.want)
			}
		})
	}
}
