package masking_panel

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// Validate :validate param of GetKeyDataRequest
func (p *GetKeyDataRequest) Validate(ctx context.Context) *srerr.Error {
	if p.StartTime == nil || p.EndTime == nil {
		logger.CtxLogErrorf(ctx, "GetKeyDataRequest| Validate fail, one of start and end time is nil, start time:%v, end time:%v", p.StartTime, p.EndTime)
		return srerr.New(srerr.ParamErr, nil, "one of start and end time is nil")
	}

	if (*p.EndTime - *p.StartTime) < 0 {
		logger.CtxLogErrorf(ctx, "GetKeyDataRequest| Validate fail, start time is bigger than end time, start time:%v, end time:%v", p.StartTime, p.EndTime)
		return srerr.New(srerr.ParamErr, nil, "start time is bigger than end time")
	}

	return nil
}

// GetAggregatedKeyList :aggregated key-> 0:will aggregate by mask product; 1: will aggregate by fulfillment product
func (p *GetKeyDataRequest) GetAggregatedKeyList() []int {
	var aggregatedKeyList []int
	aggregateByMasking := int(constant.AggregateByMaskingProduct)
	aggregateByFulfillment := int(constant.AggregateByFulfillmentProduct)

	//1.both mask product list and fulfillment product list are empty -> means default scene, will aggregate by both
	if len(p.MaskProductIDs) == 0 && len(p.ProductIDs) == 0 {
		aggregatedKeyList = append(aggregatedKeyList, aggregateByMasking)
		aggregatedKeyList = append(aggregatedKeyList, aggregateByFulfillment)
		return aggregatedKeyList
	}

	//2.masking product id list not empty, aggregated by them
	if len(p.MaskProductIDs) != 0 {
		aggregatedKeyList = append(aggregatedKeyList, aggregateByMasking)
	}
	//3.fulfillment product id list not empty, aggregated by them
	if len(p.ProductIDs) != 0 {
		aggregatedKeyList = append(aggregatedKeyList, aggregateByFulfillment)
	}
	return aggregatedKeyList
}

func (p *GetKeyDataRequest) GetStartTime() int64 {
	if p.StartTime == nil {
		return 0
	}
	return *p.StartTime
}

func (p *GetKeyDataRequest) GetEndTime() int64 {
	if p.EndTime == nil {
		return 0
	}
	return *p.EndTime
}

func (p *GetKeyDataRequest) GetShopGroupID() int64 {
	if p.ShopGroupID == nil {
		return 0
	}
	return *p.ShopGroupID
}

func (p *GetKeyDataRequest) GetSoftRuleID() uint64 {
	if p.SoftCriteriaRuleID == nil {
		return 0
	}
	return *p.SoftCriteriaRuleID
}

func (p *GetKeyDataRequest) GetVolumeRuleID() uint64 {
	if p.VolumeRuleID == nil {
		return 0
	}
	return *p.VolumeRuleID
}

func (p *GetKeyDataRequest) GetZoneCode() string {
	if p.ZoneCode == nil {
		return ""
	}
	return *p.ZoneCode
}

func (p *GetKeyDataRequest) GetRouteCode() string {
	if p.RouteCode == nil {
		return ""
	}
	return *p.RouteCode
}
