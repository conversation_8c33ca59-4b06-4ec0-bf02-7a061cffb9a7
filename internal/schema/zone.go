package schema

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
)

type LocationZonePageParam struct {
	ZoneGroupId    string `json:"zone_group_id"`
	ZoneName       string `json:"zone_name"`
	LocationId     int64  `json:"location_id"`
	State          string `json:"state"`
	City           string `json:"city"`
	District       string `json:"district"`
	Street         string `json:"street"`
	Offset         int64  `json:"offset" validate:"min=0"`
	Size           int64  `json:"size" validate:"required"`
	RoutingType    int    `json:"routing_type"`
	IsForecastType bool   `json:"is_forecast_type"`
}

func (p *LocationZonePageParam) FormatQueryCondition() map[string]interface{} {
	condition := make(map[string]interface{})
	if p.ZoneGroupId != "" {
		condition[constant.GroupIdSql] = p.ZoneGroupId
	}
	if p.ZoneName != "" {
		condition["zone_name = ?"] = p.ZoneName
	}
	if p.LocationId > 0 {
		condition["location_id = ?"] = p.LocationId
	}
	if p.State != "" {
		condition[`locate(?, state) > 0`] = p.State
	}
	if p.City != "" {
		condition[`locate(?, city) > 0`] = p.City
	}
	if p.District != "" {
		condition[`locate(?, district) > 0`] = p.District
	}
	if p.Street != "" {
		condition[`locate(?, street) > 0`] = p.Street
	}
	condition[constant.RoutingTypeSql] = p.RoutingType
	condition[constant.IsForecastTypeSql] = p.IsForecastType
	return condition
}

type (
	LocationZonePageResult struct {
		Offset int64                   `json:"offset"`
		Total  int64                   `json:"total"`
		Size   int64                   `json:"size"`
		List   []LocationZonePageModel `json:"list"`
	}

	LocationZonePageModel struct {
		Id          uint64 `json:"id"`
		ZoneGroupId string `json:"zone_group_id"`
		ZoneName    string `json:"zone_name"`
		Region      string `json:"region"`
		LocationId  int64  `json:"location_id"`
		State       string `json:"state"`
		City        string `json:"city"`
		District    string `json:"district"`
		Street      string `json:"street"`
		Operator    string `json:"operator"`
		UpdateTime  string `json:"update_time"`
	}
)

type PostcodeZonePageParam struct {
	ZoneGroupId    string `json:"zone_group_id"`
	ZoneName       string `json:"zone_name"`
	Postcode       string `json:"postcode"`
	Offset         int64  `json:"offset" validate:"min=0"`
	Size           int64  `json:"size" validate:"required"`
	RoutingType    int    `json:"routing_type"`
	IsForecastType bool   `json:"is_forecast_type"`
}

func (p *PostcodeZonePageParam) FormatQueryCondition() map[string]interface{} {
	condition := make(map[string]interface{})
	if p.ZoneGroupId != "" {
		condition[constant.GroupIdSql] = p.ZoneGroupId
	}
	if p.ZoneName != "" {
		condition["zone_name = ?"] = p.ZoneName
	}
	if p.Postcode != "" {
		condition["postcode = ?"] = p.Postcode
	}
	condition[constant.RoutingTypeSql] = p.RoutingType
	condition[constant.IsForecastTypeSql] = p.IsForecastType
	return condition
}

type (
	PostcodeZonePageResult struct {
		Offset int64                   `json:"offset"`
		Total  int64                   `json:"total"`
		Size   int64                   `json:"size"`
		List   []PostcodeZonePageModel `json:"list"`
	}

	PostcodeZonePageModel struct {
		Id          uint64 `json:"id"`
		ZoneGroupId string `json:"zone_group_id"`
		ZoneName    string `json:"zone_name"`
		Region      string `json:"region"`
		Postcode    string `json:"postcode"`
		Operator    string `json:"operator"`
		UpdateTime  string `json:"update_time"`
	}
)

type CepRangeZonePageParam struct {
	ZoneGroupId    string `json:"zone_group_id"`
	ZoneName       string `json:"zone_name"`
	CepCode        int64  `json:"cep_code"`
	Offset         int64  `json:"offset" validate:"min=0"`
	Size           int64  `json:"size" validate:"required"`
	RoutingType    int    `json:"routing_type"`
	IsForecastType bool   `json:"is_forecast_type"`
}

func (p *CepRangeZonePageParam) FormatQueryCondition() map[string]interface{} {
	condition := make(map[string]interface{})
	if p.ZoneGroupId != "" {
		condition[constant.GroupIdSql] = p.ZoneGroupId
	}
	if p.ZoneName != "" {
		condition["zone_name = ?"] = p.ZoneName
	}
	if p.CepCode > 0 {
		condition["cep_initial <= ?"] = p.CepCode
		condition["cep_final >= ?"] = p.CepCode
	}
	condition[constant.RoutingTypeSql] = p.RoutingType
	condition[constant.IsForecastTypeSql] = p.IsForecastType
	return condition
}

type (
	CepRangeZonePageResult struct {
		Offset int64                   `json:"offset"`
		Total  int64                   `json:"total"`
		Size   int64                   `json:"size"`
		List   []CepRangeZonePageModel `json:"list"`
	}

	CepRangeZonePageModel struct {
		Id          uint64 `json:"id"`
		ZoneGroupId string `json:"zone_group_id"`
		ZoneName    string `json:"zone_name"`
		Region      string `json:"region"`
		CepInitial  int64  `json:"cep_initial,omitempty"`
		CepFinal    int64  `json:"cep_final,omitempty"`
		Operator    string `json:"operator"`
		UpdateTime  string `json:"update_time"`
	}
)

type ZoneExportParam struct {
	ZoneGroupId    string        `json:"zone_group_id"`
	ZoneName       string        `json:"zone_name"`
	LocationId     int64         `json:"location_id"`
	State          string        `json:"state"`
	City           string        `json:"city"`
	District       string        `json:"district"`
	Street         string        `json:"street"`
	ZoneType       enum.ZoneType `json:"zone_type"`
	Postcode       string        `json:"postcode"`
	CepCode        int64         `json:"cep_code"`
	RoutingType    int           `json:"routing_type"`
	IsForecastType bool          `json:"is_forecast_type"`
}

func (p *ZoneExportParam) GetExportInfo() (enum.ZoneType, map[string]interface{}) {
	switch p.ZoneType {
	case enum.ZoneTypeLocation:
		return p.ZoneType, p.locationZoneExportParam().FormatQueryCondition()
	case enum.ZoneTypePostcode:
		return p.ZoneType, p.postcodeZoneExportParam().FormatQueryCondition()
	default:
		return p.ZoneType, p.cepRangeZoneExportParam().FormatQueryCondition()
	}
}

func (p *ZoneExportParam) locationZoneExportParam() *LocationZonePageParam {
	return &LocationZonePageParam{
		ZoneGroupId:    p.ZoneGroupId,
		ZoneName:       p.ZoneName,
		LocationId:     p.LocationId,
		State:          p.State,
		City:           p.City,
		District:       p.District,
		Street:         p.Street,
		RoutingType:    p.RoutingType,
		IsForecastType: p.IsForecastType,
	}
}

func (p *ZoneExportParam) postcodeZoneExportParam() *PostcodeZonePageParam {
	return &PostcodeZonePageParam{
		ZoneGroupId:    p.ZoneGroupId,
		ZoneName:       p.ZoneName,
		Postcode:       p.Postcode,
		RoutingType:    p.RoutingType,
		IsForecastType: p.IsForecastType,
	}
}

func (p *ZoneExportParam) cepRangeZoneExportParam() *CepRangeZonePageParam {
	return &CepRangeZonePageParam{
		ZoneGroupId:    p.ZoneGroupId,
		ZoneName:       p.ZoneName,
		CepCode:        p.CepCode,
		RoutingType:    p.RoutingType,
		IsForecastType: p.IsForecastType,
	}
}

type ZoneImportParam struct {
	ZoneGroupId    string        `json:"zone_group_id" validate:"required"`
	ZoneType       enum.ZoneType `json:"zone_type" validate:"required"`
	FileUrl        string        `json:"file_url" validate:"required,UssHostValidate"`
	FileName       string        `json:"file_name" validate:"required"`
	RoutingType    int           `json:"routing_type"`
	IsForecastType bool          `json:"is_forecast_type"`
	IsSameLevel    bool          `json:"is_same_level"`
}

type VolumeZoneDictResult struct {
	ZoneTypeEnum   []IntDictEnum `json:"zone_type_enum"`
	RuleTypeEnum   []IntDictEnum `json:"rule_type_enum"`
	RuleStatusEnum []IntDictEnum `json:"rule_status_enum"`
	TaskStatusEnum []IntDictEnum `json:"task_status_enum"`
}

type ZoneLocationDeleteParam struct {
	Id int64 `json:"id" validate:"required"`
}

func (p *ZoneLocationDeleteParam) FormatQueryCondition() map[string]interface{} {
	return map[string]interface{}{
		"id = ?": p.Id,
	}
}

type ZonePostcodeDeleteParam struct {
	Id int64 `json:"id" validate:"required"`
}

func (p *ZonePostcodeDeleteParam) FormatQueryCondition() map[string]interface{} {
	return map[string]interface{}{
		"id = ?": p.Id,
	}
}

type ZoneCepRangeDeleteParam struct {
	Id int64 `json:"Id" validate:"required"`
}

func (p *ZoneCepRangeDeleteParam) FormatQueryCondition() map[string]interface{} {
	return map[string]interface{}{
		"id = ?": p.Id,
	}
}
