package schema

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type ZoneGroupPageParam struct {
	Offset           int64         `json:"offset" validate:"min=0"`
	Size             int64         `json:"size" validate:"required"`
	ZoneGroupId      string        `json:"zone_group_id"`
	ZoneGroupName    string        `json:"zone_group_name"`
	ZoneType         enum.ZoneType `json:"zone_type"`
	MaskingProductId int           `json:"mask_product_id"`
	ProductId        int           `json:"product_id"`
	LineId           string        `json:"line_id"`
	RoutingType      int           `json:"routing_type"`
	IsForecastType   bool          `json:"is_forecast_type"`
}

func (p *ZoneGroupPageParam) FormatQueryCondition() map[string]interface{} {
	condition := make(map[string]interface{})
	if p.ZoneGroupId != "" {
		condition[constant.GroupIdSql] = p.ZoneGroupId
	}
	if p.ZoneGroupName != "" {
		condition[`locate(?, group_name) > 0`] = p.ZoneGroupName
	}
	if p.ZoneType > 0 {
		condition["zone_type = ?"] = p.ZoneType
	}
	condition[constant.RoutingTypeSql] = p.RoutingType
	condition[constant.IsForecastTypeSql] = p.IsForecastType
	return condition
}

type (
	ZoneGroupPageResult struct {
		Offset int64                `json:"offset"`
		Total  int64                `json:"total"`
		Size   int64                `json:"size"`
		List   []ZoneGroupPageModel `json:"list"`
	}

	ZoneGroupPageModel struct {
		Id             uint64   `json:"id"`
		GroupId        string   `json:"group_id,omitempty"`
		GroupName      string   `json:"group_name,omitempty"`
		ZoneType       string   `json:"zone_type,omitempty"`
		MaskProductIds []int64  `json:"mask_product_ids,omitempty"`
		ProductIds     []int64  `json:"product_ids,omitempty"`
		LineIds        []string `json:"line_ids,omitempty"`
		Operator       string   `json:"operator,omitempty"`
		UpdateTime     string   `json:"update_time,omitempty"`
	}
)

type ZoneGroupCreateOrUpdateParam struct {
	ZoneGroupId       string        `json:"zone_group_id" validate:"required"`
	ZoneGroupName     string        `json:"zone_group_name" validate:"required"`
	ZoneType          enum.ZoneType `json:"zone_type"`
	ZoneGroupCapacity int64         `json:"zone_group_capacity" validate:"min=0"`
	MaskProductIds    []int64       `json:"mask_product_ids"`
	ProductIds        []int64       `json:"product_ids"`
	LineIds           []string      `json:"line_ids"`
	RoutingType       int           `json:"routing_type"`
	IsForecastType    bool          `json:"is_forecast_type"`
}

func (p *ZoneGroupCreateOrUpdateParam) FormatUpdateData(ctx context.Context, operator string) map[string]interface{} {
	return map[string]interface{}{
		"group_name":     p.ZoneGroupName,
		"zone_type":      p.ZoneType,
		"group_capacity": p.ZoneGroupCapacity,
		"mtime":          timeutil.GetCurrentUnixTimeStamp(ctx),
		"operator":       operator,
	}
}

type ZoneGroupIdParam struct {
	Id int `json:"id" validate:"required"`
}

type ZoneGroupDetailResult struct {
	ZoneGroupId       string        `json:"zone_group_id,omitempty"`
	ZoneGroupName     string        `json:"zone_group_name,omitempty"`
	ZoneType          enum.ZoneType `json:"zone_type,omitempty"`
	ZoneGroupCapacity int64         `json:"zone_group_capacity,omitempty"`
	MaskProductIds    []int64       `json:"mask_product_ids,omitempty"`
	ProductIds        []int64       `json:"product_ids,omitempty"`
	LineIds           []string      `json:"line_ids,omitempty"`
}

type ZoneGroupZoneCapacityParam struct {
	ZoneGroupId string `json:"zone_group_id" validate:"required"`
	ZoneName    string `json:"zone_name" validate:"required"`
	Capacity    int64  `json:"capacity" validate:"min=0"`
	RoutingType int    `json:"routing_type"`
}

type ZoneEstimatedCapacityPageParam struct {
	Offset      int64  `json:"offset" validate:"min=0"`
	Size        int64  `json:"size" validate:"required"`
	ZoneGroupId string `json:"zone_group_id" validate:"required"`
	ZoneName    string `json:"zone_name"`
	RoutingType int    `json:"routing_type"`
}

type (
	ZoneEstimatedCapacityPageResult struct {
		Offset int64                            `json:"offset"`
		Total  int64                            `json:"total"`
		Size   int64                            `json:"size"`
		List   []ZoneEstimatedCapacityPageModel `json:"list"`
	}

	ZoneEstimatedCapacityPageModel struct {
		ZoneName string `json:"zone_name"`
		Capacity int64  `json:"capacity"`
	}
)

type ZoneEstimatedCapacityExportParam struct {
	ZoneGroupId string `json:"zone_group_id" validate:"required"`
	ZoneName    string `json:"zone_name"`
	RoutingType int    `json:"routing_type"`
}
