package schema

type IntDictEnum struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type StrDictEnum struct {
	Uid  string `json:"uid"`
	Name string `json:"name"`
}

type (
	GetExportTasksRequest struct {
		PageNo        int64  `json:"page_no" form:"page_no"`     // page number
		PageSize      int64  `json:"page_size" form:"page_size"` // size of one page
		BusinessScene string `json:"business_scene" form:"business_scene"`
	}

	GetExportTasksResponse struct {
		PageNo   int64        `json:"page_no"`   // page number
		PageSize int64        `json:"page_size"` // size of one page
		Total    int64        `json:"total"`     //total count
		List     []ExportTask `json:"list"`
	}

	ExportTask struct {
		TaskID        uint64 `json:"task_id"`     //primary key: id
		TaskStatus    int    `json:"task_status"` //status of task-> 1:created,2:pending,3:doing,4:stopped,5:success,6:failed,7:terminated
		Operator      string `json:"operator"`
		OperationTime int64  `json:"operation_time"`
		ErrorMessage  string `json:"error_message"` //default by empty string
		S3Url         string `json:"s3_url"`        //url of s3 to store data
	}
)
