package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type (
	RoutingVisualListReq struct {
		RequestId string `json:"request_id"`
		SlsTn     string `json:"sls_tn"`
		ForderId  string `json:"forder_id"`
		Pageno    int    `json:"pageno"`
		Count     int    `json:"count"`
	}

	RoutingVisualListResp struct {
		Pageno int            `json:"pageno"`
		Count  int            `json:"count"`
		Total  int            `json:"total"`
		List   []ListBaseInfo `json:"list"`
	}

	ListBaseInfo struct {
		RequestId     string `json:"request_id"`
		SlsTn         string `json:"sls_tn"`
		SlsId         string `json:"slo_id"`
		ForderId      string `json:"forder_id"`
		RequestTime   uint32 `json:"request_time"`
		RoutingStatus bool   `json:"routing_status"`
		ProductId     int    `json:"product_id"`
		RowKey        string `json:"row_key"`
		EsFOrderId    string `json:"forderid"`
	}
)

type (
	RoutingToggle struct {
		CB    bool `json:"cb"`
		Local bool `json:"local"`
		Spx   bool `json:"spx"`
		ILH   bool `json:"ILH"`
	}
	BaseInfo struct {
		RequestId     string        `json:"request_id"`
		RequestTime   uint32        `json:"request_time"`
		ForderId      string        `json:"forder_id"`
		SlsTn         string        `json:"sls_tn"`
		RuleId        int           `json:"rule_id"`
		VolumeRuleId  uint64        `json:"volume_rule_id"`
		RoutingToggle RoutingToggle `json:"routing_toggle"`
		RoutingStatus bool          `json:"routing_status"`
		RoutingResult string        `json:"routing_result"`
		ProductId     int           `json:"product_id"`
		SlsId         string        `json:"slo_id"`
	}
	HardCriteria struct {
		BeforeHardCriteriaLanes []string                     `json:"before_hard_criteria_lanes"`
		AfterHardCriteriaLanes  []string                     `json:"after_hard_criteria_lanes"`
		LineToggle              map[string]map[string]string `json:"line_toggle"`
		InputLaneCode           []string                     `json:"input_lane_code"`
		OutputLaneCode          []string                     `json:"output_lane_code"`
	}
	LaneDetail struct {
		InputLaneCode        string  `json:"input_lane_code"`
		OutputLaneCode       string  `json:"output_lane_code"`
		LineId               string  `json:"line_id"`
		LineLimit            string  `json:"line_limit"`
		CurrentLineVolume    string  `json:"current_line_volume"`
		ZoneCodeLimit        string  `json:"zone_code_limit"`
		CurrentZoneVolume    string  `json:"current_zone_volume"`
		ShippingFee          float64 `json:"shipping_fee"`
		RateId               int     `json:"rate_id"`
		DefaultCriteriaParam int     `json:"default_criteria_param"`
	}
	Detail struct {
		Priority      int32        `json:"priority"`
		SoftCriteria  string       `json:"soft_criteria"`
		Lanes         []LaneDetail `json:"lanes"`
		TableDataList [][]string   `json:"table_data_list"`
	}
	SoftCriteria struct {
		RoutingSeq     string   `json:"routing_seq"`
		InputLaneCode  []string `json:"input_lane_code"`
		OutputLaneCode []string `json:"output_lane_code"`
		Detail         []Detail `json:"detail"`
		TableTitleList []string `json:"table_title_list"`
	}
	RoutingDetail struct {
		HardCriteria HardCriteria `json:"hard_criteria"`
		SoftCriteria SoftCriteria `json:"soft_criteria"`
	}
	RoutingVisualDeatilResp struct {
		BaseInfo      BaseInfo      `json:"base_info"`
		RoutingDetail RoutingDetail `json:"routing_detail"`
	}
)

func (r *RoutingVisualListReq) CheckParam(ctx context.Context) *srerr.Error {
	if r.RequestId == "" && r.SlsTn == "" && r.ForderId == "" {
		logger.CtxLogInfof(ctx, "You must ensure that one of the parameters has a value and param is %+V", r)
		return srerr.New(srerr.ParamErr, r, "You must ensure that one of the parameters has a value")
	}
	if r.Pageno <= 0 || r.Count <= 0 {
		r.Pageno = 10
		r.Count = 1
	}

	return nil
}
