package routing

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strings"
	"testing"
)

var lineToLaneMap = make(map[string]string)

func TestRouting(t *testing.T) {
	jwt, err := jwtutil.BuildJWT(context.TODO(), "SG", "SMR", "SMR", "faedwhAe322423$%#~Aw#4s")
	if err != nil {
		panic(err)
	}

	url := "https://waybillcenter.ssc.uat.shopee.sg/admin/waybill_list/slo"
	header := map[string]string{
		"jwt-token":    jwt,
		"Content-Type": "application/json",
	}
	postJson, err := httputil.PostJson(context.TODO(), url, []byte("{\n    \"forder_id\":\"*********\",\n    \"pageno\":1,\n    \"count\":10\n}"), 100, header)
	if err != nil {
		panic(err)
	}

	println(string(postJson))

}

func TesLogEntry(log *routing_log.RoutingLog) {
	resp := RoutingVisualDeatilResp{}
	for _, lane := range log.HardCriteriaResult {
		for _, line := range lane.ResourceServiceableCollection {
			lineToLaneMap[line.GetResourceId()] = lane.GetLaneCode()
		}
	}
	resp.BaseInfo.ForderId = log.FOrderId
	resp.BaseInfo.SlsTn = log.SlsTn
	resp.BaseInfo.RoutingStatus = log.RoutingStatus
	resp.BaseInfo.RoutingResult = log.FinalResult.Result
	resp.BaseInfo.RequestTime = log.RequestTime
	resp.BaseInfo.VolumeRuleId = log.VolumeRuleID
	resp.BaseInfo.RequestId = log.RequestId
	resp.BaseInfo.RuleId = log.RuleId
	resp.BaseInfo.RoutingToggle = getRoutingToggle(log)

	resp.RoutingDetail.HardCriteria.BeforeHardCriteriaLanes = log.BeforeHardCriteriaLaneList
	resp.RoutingDetail.HardCriteria.AfterHardCriteriaLanes = getHardCriteriaResult(log)
	resp.RoutingDetail.HardCriteria.LineToggle = getLineToggle(log)
	resp.RoutingDetail.HardCriteria.InputLaneCode = getLane(log.RoutingResult.AvailableFilterProcess.Before)
	resp.RoutingDetail.HardCriteria.OutputLaneCode = getLane(log.RoutingResult.AvailableFilterProcess.After)

	resp.RoutingDetail.SoftCriteria.RoutingSeq = log.RoutingSeq
	resp.RoutingDetail.SoftCriteria.InputLaneCode = log.RoutingResult.SoftCriteriaFilterProcess.Before
	resp.RoutingDetail.SoftCriteria.OutputLaneCode = log.RoutingResult.SoftCriteriaFilterProcess.After
	resp.RoutingDetail.SoftCriteria.Detail = getSoftCriteriaDetail(log)

	marshal, _ := json.Marshal(resp)
	jsonSt := string(marshal)
	if jsonSt != "" {

	}
	println()
}

func getRoutingToggle(log *routing_log.RoutingLog) RoutingToggle {
	res := RoutingToggle{}
	res.Local = (log.RoutingToggle & 1) > 0
	res.CB = (log.RoutingToggle & (1 << 1)) > 0
	res.Spx = (log.RoutingToggle & (1 << 2)) > 0
	res.ILH = (log.RoutingToggle & (1 << 3)) > 0
	return res
}

func getHardCriteriaResult(log *routing_log.RoutingLog) []string {
	res := []string{}

	for _, lane := range log.HardCriteriaResult {
		res = append(res, *lane.LaneCode)
	}

	return res
}

func getLineToggle(log *routing_log.RoutingLog) map[string]map[string]string {
	res := map[string]map[string]string{}
	for k, v := range log.LineToggle {
		if res[k] == nil {
			res[k] = map[string]string{}
		}
		res[k]["on"] = strings.Join(v.On, "_")
		res[k]["off"] = strings.Join(v.Off, "_")
	}

	return res
}

func getLane(data map[string]map[string][]string) []string {
	res := []string{}

	for laneCode, lineMap := range data {
		temp := laneCode + ":"
		for lineType, lins := range lineMap {
			temp += lineType + "-" + strings.Join(lins, "_") + ","
		}
		res = append(res, temp)
	}
	return res
}

func getSoftCriteriaDetail(log *routing_log.RoutingLog) []Detail {
	details := make([]Detail, 0)

	for _, stage := range log.RoutingResult.SoftCriteriaFilterProcess.StageList {
		for _, factory := range stage.FactorCombination {
			detail := Detail{}
			detail.SoftCriteria = stage.DisplayResourceSubType + ":" + factory.FactorName
			detail.Lanes = getFactoryLanes(factory, log.ProductId)
			detail.Priority = factory.Priority
			details = append(details, detail)
		}
	}

	defaultCriteria := Detail{}
	defaultCriteria.SoftCriteria = log.RoutingResult.DefaultCriteriaFilterProcess.FactorName
	defaultCriteria.Priority = 1
	defaultCriteria.Lanes = getDefaultCriteriaFactoryLanes(log.RoutingResult.DefaultCriteriaFilterProcess.Before, log.RoutingResult.DefaultCriteriaFilterProcess.After)
	details = append(details, defaultCriteria)

	spxDetail := Detail{}
	spxDetail.SoftCriteria = "spx-self-build"
	spxDetail.Priority = 1
	spxDetail.Lanes = getDefaultCriteriaFactoryLanes(log.RoutingResult.SpxSelfBuildProcess.Before, log.RoutingResult.SpxSelfBuildProcess.After)
	return details
}
func getDefaultCriteriaFactoryLanes(before, after []string) []LaneDetail {
	lanes := []LaneDetail{}

	for _, lineCode := range before {
		lane := LaneDetail{}
		lane.InputLaneCode = lineToLaneMap[lineCode]
		if objutil.ContainStr(after, lineCode) {
			lane.OutputLaneCode = lineToLaneMap[lineCode]
			lane.LineId = lineCode
		}
		lanes = append(lanes, lane)
	}

	return lanes
}
func getFactoryLanes(f routing_log.SchedulingFactorCombination, productId int) []LaneDetail {
	lanes := make([]LaneDetail, 0)

	for _, lineCode := range f.Before {
		lane := LaneDetail{}
		lane.InputLaneCode = lineToLaneMap[lineCode]
		if objutil.ContainStr(f.After, lineCode) {
			lane.OutputLaneCode = lineToLaneMap[lineCode]
			lane.LineId = lineCode
			if f.FactorName == schedule_factor.MinVolume || f.FactorName == schedule_factor.MinVolumeV2Name {
				m := f.ProcessData.(map[string]string)
				lineKey := fmt.Sprintf("%v:%v", productId, lineCode)
				lane.LineLimit = m[lineKey+":min"]
				lane.CurrentLineVolume = m[lineKey+":current"]
				lane.CurrentZoneVolume = m[lineCode]
				lane.ZoneCodeLimit = m[lineCode+":min"]
			} else if f.FactorName == schedule_factor.MaxCapacity || f.FactorName == schedule_factor.MaxCapacityV2Name {
				m := f.ProcessData.(map[string]string)
				lineKey := fmt.Sprintf("%v:%v", productId, lineCode)
				lane.LineLimit = m[lineKey+":max"]
				lane.CurrentLineVolume = m[lineKey+":current"]
				lane.CurrentZoneVolume = m[lineCode]
				lane.ZoneCodeLimit = m[lineCode+":max"]
			} else if f.FactorName == schedule_factor.LineCheapestShippingFee {
				m := f.ProcessData.(map[string]float64)
				lane.ShippingFee = m[lineCode]
			}
		}

		lanes = append(lanes, lane)
	}

	return lanes
}
