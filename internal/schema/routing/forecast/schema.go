package forecast

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"sort"
	"time"
)

var (
	TaskDuplicatedRule     = "Duplicated rule(s) in task. Please check rule's priority, TWS, receiver zone, item category, parcel value or parcel weight."
	LocalSpxDuplicatedRule = "Duplicated rule(s) in task. Please check rule's priority,receiver zone."
	IlhTaskDuplicatedRule  = "Duplicated rule(s) in task. Please check rule's priority, TWS, Destination Port, or DG Type."
)

type CreateTaskRequest struct {
	TaskName       string `form:"task_name" json:"task_name" binding:"required"`
	ProductId      int    `form:"product_id" json:"product_id" binding:"required"`
	RoutingType    uint8  `form:"routing_type" json:"routing_type"`
	IsMultiProduct bool   `form:"is_multi_product" json:"is_multi_product"`
}

type GetTaskByIdRequest struct {
	Id int `form:"id"  json:"id" binding:"required"`
}

type TaskResultRequest struct {
	Id                 int  `form:"id" json:"id" binding:"required"`
	GroupByIlhGroup    bool `form:"group_by_ilh_group" json:"group_by_ilh_group"`
	GroupByTp          bool `form:"group_by_tp" json:"group_by_tp"`
	GroupByServiceCode bool `form:"group_by_service_code" json:"group_by_service_code"`
}

func (t *TaskResultRequest) Validate() error {

	return nil
}

type GetTaskListRequest struct {
	TaskStatus     *int  `form:"task_status" json:"task_status,omitempty"` // 状态 0: Draft，1: waiting 2: processing
	Id             int   `form:"id" json:"id"`
	ProductId      int   `form:"product_id" json:"product_id"`
	RoutingType    uint8 `form:"routing_type" json:"routing_type"`
	IsMultiProduct bool  `form:"is_multi_product" json:"is_multi_product"`
	PageNo         int   `form:"pageno" json:"pageno" default:"1"`
	Limit          int   `form:"limit" json:"limit" default:"20"`
}

type (
	GetTaskOutlineReq struct {
		TaskId int `json:"task_id"`
	}
	GetTaskOutlineResp struct {
		TaskOutline persistent.ForecastingTaskTab `json:"task_outline"`
	}
)

type GetTaskListResponse struct {
	List   []*persistent.ForecastingTaskTab `json:"list"`
	PageNo int                              `json:"pageno"`
	Count  int                              `json:"count"`
	Total  int                              `json:"total"`
}

type DeployTaskRequest struct {
	Id                   int   `form:"id"  json:"id" binding:"required"`
	EffectiveStartTime   int64 `form:"effective_start_time" json:"effective_start_time"`
	EffectiveImmediately bool  `form:"effective_immediately" json:"effective_immediately"`
	IsForce              bool  `form:"is_force" json:"is_force"`
}

func (r *DeployTaskRequest) Validate(ctx context.Context) error {
	if err := r.validateEffectiveStartTime(ctx); err != nil {
		return err
	}
	return nil
}

func (r *DeployTaskRequest) validateEffectiveStartTime(ctx context.Context) error {
	if !r.EffectiveImmediately && r.EffectiveStartTime < timeutil.GetCurrentUnixTimeStamp(ctx) {
		return errors.New("Effective_start_time must be greater than the current time. ")
	}
	return nil
}

type TaskDeployResponse struct {
	Msg string `json:"msg"`
}

type ShipmentSummaryRequest struct {
	ProductId      int       `form:"product_id" json:"product_id"`
	IsMultiProduct bool      `form:"is_multi_product" json:"is_multi_product"`
	StartDate      string    `form:"start_date" json:"start_date"`
	EndDate        string    `form:"end_date" json:"end_date"`
	Start          time.Time `form:"-" json:"-"`
	End            time.Time `form:"-" json:"-"`
	RoutingType    int       `form:"routing_type" json:"routing_type"`
}

func (r *ShipmentSummaryRequest) Validate() error {
	if err := r.validateDate(); err != nil {
		return err
	}

	return nil
}

func (r *ShipmentSummaryRequest) validateDate() error {
	start, err := timeutil.ParseLocalTime(timeutil.DateFormat, r.StartDate)
	if err != nil {
		return fmt.Errorf("invalid start_date: %s, layout: 2006-01-02", r.StartDate)
	}
	r.Start = start
	end, err := timeutil.ParseLocalTime(timeutil.DateFormat, r.EndDate)
	if err != nil {
		return fmt.Errorf("invalid end_date: %s, layout: 2006-01-02", r.EndDate)
	}
	r.End = end
	if end.Sub(start) < 0 {
		return errors.New("The end_date must be later than the start_date. ")
	}
	return nil
}

type ShipmentSummary struct {
	LaneCode                   string      `json:"lane_code"`
	DgFlag                     int32       `json:"dg_flag"`
	ServiceCode                string      `json:"service_code"`
	DgGroupInfo                DgGroupInfo `json:"dg_group_info"`
	Tws                        string      `json:"tws"`
	Joint                      string      `json:"joint"`
	FlName                     string      `json:"fl_name"`
	LmName                     string      `json:"lm_name"`
	FlId                       string      `json:"fl_id"`
	LmId                       string      `json:"lm_id"`
	Quantity                   int         `json:"quantity"`
	Percentage                 int         `json:"percentage"`
	RuleId                     int         `json:"rule_id"`
	RuleName                   string      `json:"rule_name"`
	RulePriority               int         `json:"rule_priority"`
	ShippingFeePerOrder        float64     `json:"shipping_fee_per_order"`
	ShippingFeePerOrderUsd     float64     `json:"shipping_fee_per_order_usd"`
	MissingShippingFeeQuantity int         `json:"missing_shipping_fee_quantity"`
}

type IlhShipmentSummary struct {
	TP                       string  `json:"tp"`              //NON-DG or DG
	FlInfo                   string  `json:"fl_info"`         // fl line id+fl name
	ImportIlhInfo            string  `json:"import_ilh_info"` // import ilh line id + name
	ServiceCode              string  `json:"service_code"`
	CartonQuantity           int     `json:"carton_quantity"`
	QuantityPercentage       int     `json:"quantity_percentage"` //percentage of carton quantity in total
	ParcelQuantity           int     `json:"parcel_quantity"`
	ParcelQuantityPercentage int     `json:"parcel_quantity_percentage"`
	WeightQuantity           float64 `json:"weight_quantity"`
	WeightPercent            int     `json:"weight_percent"`
	StatDate                 string  `json:"stat_date"`

	RuleId       int    `json:"rule_id"`
	RuleName     string `json:"rule_name"`
	RulePriority int    `json:"rule_priority"`
}

type ShipmentSummaryResponse struct {
	List               []ShipmentSummary `json:"list"`
	ConsolidatedResult []ShipmentSummary `json:"consolidated_result"`
	Error              string            `json:"error"`
}

type IlhShipmentSummaryResponse struct {
	List  []*IlhShipmentSummary `json:"list"`
	Error string                `json:"error"`
}

type LineInfo struct {
	LineId   string `json:"line_id"`
	LineName string `json:"line_name"`
}

type DgGroupInfo struct {
	DgGroupId   string     `json:"dg_group_id"`
	DgGroupName string     `json:"dg_group_name"`
	LineList    []LineInfo `json:"line_list"`
}

type CheckTaskZoneValidRequest struct {
	Id          int                    `json:"id" binding:"required"`
	RoutingType uint8                  `json:"routing_type" binding:"required"` //0:CB, 1:spx, 2:local, 3:ilh
	Scene       enum.VerificationScene `json:"scene"`
}

type CheckTaskZoneValidResponse struct {
	RetCode  int    `json:"retcode"`
	RuleName string `json:"rule_name"`
	ZoneCode string `json:"zone_code"`
	Msg      string `json:"msg"`
}

type StartForecastRequest struct {
	TaskId int64 `json:"task_id"`
}

type UpdateTaskRequest struct {
	Id                   int                                `form:"id" json:"id" binding:"required"`
	TaskName             string                             `form:"task_name" json:"task_name" binding:"required"`
	TaskStatus           persistent.TaskStatus              `form:"task_status" json:"task_status"` // 状态 0: Draft，1: waiting 2: processing
	StartDate            string                             `form:"start_date" json:"start_date"`
	EndDate              string                             `form:"end_date" json:"end_date"`
	RuleList             []*RuleInfo                        `form:"rule_list" json:"rule_list"`
	VolumeRuleList       []*VolumeRuleInfo                  `form:"volume_rule_list" json:"volume_rule_list"`
	UseOpsTask           bool                               `form:"use_ops_task" json:"use_ops_task"` //true：使用ops创建的硬性校验任务， false：不使用
	HCTaskId             uint64                             `form:"hc_task_id" json:"hc_task_id"`     //硬性校验刷新任务主键id
	RoutingType          uint8                              `form:"routing_type" json:"routing_type"`
	WeightRange          []*persistent.WeightRange          `form:"weight_range" json:"weight_range"`
	SimulationOrderCount []*persistent.SimulationOrderCount `form:"simulation_order_count" json:"simulation_order_count"`
	ReCalcFee            bool                               `form:"re_calc_fee" json:"re_calc_fee"`       //true: 重新计算运费，false：不重新计算运费
	VolumeRuleId         int64                              `form:"volume_rule_id" json:"volume_rule_id"` //cb 预测LM对应的Volume rule id
	ShipmentResource     persistent.ShipmentResource        `form:"shipment_resource" json:"shipment_resource"`
}

type (
	VolumeRuleInfo struct {
		Id        uint64                 `json:"id"`
		TaskId    int                    `json:"task_id"`
		Priority  int                    `json:"priority"`
		RuleType  enum.VolumeRuleType    `json:"rule_type"`
		RuleName  string                 `json:"rule_name"`
		LineLimit map[string][]LineLimit `json:"line_limit"`
		ZoneLimit []ZoneLimit            `json:"zone_limit"`
		RuleId    int64                  `json:"rule_id"`
	}

	LineLimit struct {
		LineId                 string `json:"line_id"`
		LineType               int    `json:"line_type"`
		MinDailyLimit          int64  `json:"min_daily_limit,omitempty" validate:"required,min=1"`
		MaxDailyLimit          int64  `json:"max_daily_limit,omitempty" validate:"required,min=1"`
		MaxCodDailyLimit       int64  `json:"max_cod_daily_limit" validate:"required,min=1"`
		MaxBulkyDailyLimit     int64  `json:"max_bulky_daily_limit" validate:"required,min=1"`
		MaxHighValueDailyLimit int64  `json:"max_high_value_daily_limit" validate:"required,min=1"`
		MaxDgDailyLimit        int64  `json:"max_dg_daily_limit" validate:"required,min=1"`
	}

	ZoneLimit struct {
		LineId                 string `json:"line_id"`
		ZoneGroupId            string `json:"zone_group_id"`
		ZoneName               string `json:"zone_name"`
		LineType               int    `json:"line_type"`
		MinDailyLimit          int64  `json:"min_daily_limit"`
		MaxDailyLimit          int64  `json:"max_daily_limit"`
		MaxCodDailyLimit       int64  `json:"max_cod_daily_limit"`
		MaxBulkyDailyLimit     int64  `json:"max_bulky_daily_limit"`
		MaxHighValueDailyLimit int64  `json:"max_high_value_daily_limit"`
		MaxDgDailyLimit        int64  `json:"max_dg_daily_limit"`
	}
)

type RuleInfo struct {
	RuleId                      int                               `form:"rule_id" json:"rule_id"`
	RuleName                    string                            `form:"rule_name" json:"rule_name"`
	RuleDetail                  *rule.RuleDetails                 `form:"rule_detail" json:"rule_detail,omitempty"`
	DefaultCriteria             *rule.DefaultCriteria             `form:"default_criteria" json:"default_criteria,omitempty"` // 默认规则权重配置
	DisabledInfo                []*rule.DisabledInfo              `form:"disabled_info" json:"disabled_info,omitempty"`
	MultiProductDefaultCriteria *rule.MultiProductDefaultCriteria `json:"multi_product_default_criteria,omitempty"` // Multi Product默认规则权重配置
	MultiProductDisableInfo     *rule.MultiProductDisabledInfo    `json:"multi_product_disable_info,omitempty"`
	CombinationSetting          []*rule.CombinationSetting        `json:"combination_setting"`
	Priority                    int                               `form:"priority" json:"priority"` // 1 is high
	WhsId                       []string                          `form:"whs_id" json:"whs_id"`
	ZoneCode                    []string                          `form:"zone_code" json:"zone_code"`
	ItemCategoryLevel           int                               `from:"item_category_level" json:"item_category_level,omitempty"`
	ItemCategoryId              []int                             `form:"item_category_id" json:"item_category_id,omitempty"`
	ParcelValueMin              float32                           `from:"parcel_value_min" json:"parcel_value_min,omitempty"`
	ParcelValueMax              float32                           `form:"parcel_value_max" json:"parcel_value_max,omitempty"`
	ParcelWeightMin             int                               `form:"parcel_weight_min" json:"parcel_weight_min,omitempty"`
	ParcelWeightMax             int                               `form:"parcel_weight_max" json:"parcel_weight_max,omitempty"`
	IsMultiProduct              bool                              `form:"is_multi_product" json:"is_multi_product"`
	DestinationPorts            []string                          `form:"column:destination_ports" json:"destination_ports"` //SSCSMR-278 add destination ports, here is used to accept data from fe
	DgType                      int                               `form:"column:dg_type" json:"dg_type"`                     //1:non-dg; 2:dg
	ParcelDimension             rule.ParcelDimension              `json:"parcel_dimension,omitempty"`
	CCMode                      rule.CCMode                       `form:"cc_mode" json:"cc_mode"`
	TplToggleInfo               *rule.ListResourceType            `form:"tpl_toggle_info" json:"tpl_toggle_info"` // tpl toggle info
	ShopGroupListVo             []int64                           `form:"shop_group_list" json:"shop_group_list"`
}

func (r *UpdateTaskRequest) Validate() error {
	if err := r.validateStatus(); err != nil {
		return err
	}
	if err := r.validateSubmitStatus(); err != nil {
		return err
	}
	for _, ruleInfo := range r.RuleList {
		if err := r.validateDefaultPriority(ruleInfo); err != nil {
			return err
		}
		sort.Strings(ruleInfo.WhsId)
		sort.Strings(ruleInfo.ZoneCode)
		sort.Ints(ruleInfo.ItemCategoryId)
	}

	return nil
}

func (r *UpdateTaskRequest) validateStatus() error {
	if r.TaskStatus != persistent.TaskStatusDraft && r.TaskStatus != persistent.TaskStatusWaiting {
		return errors.New("The update task only allows the status to be set to draft or waiting. ")
	}
	return nil
}

func (r *UpdateTaskRequest) validateSubmitStatus() error {
	// 这里如果是local/spx预测任务业务要求不需要校验default rule
	if r.RoutingType == rule.LocalRoutingType || r.RoutingType == rule.SPXRoutingType {
		return nil
	}
	if r.TaskStatus == persistent.TaskStatusWaiting && len(r.RuleList) > 0 {
		for _, ruleInfo := range r.RuleList {
			if ruleInfo.Priority == rule.DefaultPriority {
				return nil
			}
		}
		return errors.New("When submit task, must have default priority rule. ")
	}
	return nil
}

func (r *UpdateTaskRequest) validateDefaultPriority(ruleInfo *RuleInfo) error {
	if ruleInfo.Priority == rule.DefaultPriority && (len(ruleInfo.ZoneCode) != 0 || len(ruleInfo.WhsId) != 0) {
		return errors.New("DefaultPriority rule, zoneCode and whsCode must also be default. ")
	}
	return nil
}

func (r *UpdateTaskRequest) Copy2TaskTab(model *persistent.ForecastingTaskTab) *persistent.ForecastingTaskTab {
	model.Id = r.Id
	model.TaskName = r.TaskName
	model.TaskStatus = r.TaskStatus
	model.StartDate = r.StartDate
	model.EndDate = r.EndDate
	model.HCTaskId = r.HCTaskId
	model.UseOpsTask = r.UseOpsTask
	model.RoutingType = r.RoutingType
	model.ReCalcFee = r.ReCalcFee
	model.VolumeRuleId = r.VolumeRuleId
	model.ShipmentResource = r.ShipmentResource
	return model
}

func (r *UpdateTaskRequest) CopyRuleTab() ([]*persistent.ForecastingRuleTab, *srerr.Error) {
	var tmpRules []*persistent.ForecastingRuleTab
	var existPriorityRule = make(map[int]bool)
	var existConditionRule = make(map[string]bool)
	for _, ruleInfo := range r.RuleList {
		tmpRule := new(persistent.ForecastingRuleTab)
		// local spx 只有一个receiver zone，业务要求这里不做校验
		if r.RoutingType != rule.SPXRoutingType && r.RoutingType != rule.LocalRoutingType {
			if _, ok := existPriorityRule[ruleInfo.Priority]; ok {
				//ilh返回自己的错误信息
				if r.RoutingType == rule.IlhRoutingType {
					return nil, srerr.New(srerr.ParamErr, nil, IlhTaskDuplicatedRule)
				}
				return nil, srerr.New(srerr.ParamErr, nil, TaskDuplicatedRule)
			} else {
				existPriorityRule[ruleInfo.Priority] = true
			}
			sort.Slice(ruleInfo.ShopGroupListVo, func(i, j int) bool {
				return ruleInfo.ShopGroupListVo[i] < ruleInfo.ShopGroupListVo[j]
			})
			key := fmt.Sprintf("forecast-rule:%v-%v-%v-%v-%v-%v-%v-%v-%v-%v-%v",
				ruleInfo.WhsId, ruleInfo.ZoneCode, ruleInfo.ItemCategoryLevel, ruleInfo.ItemCategoryId, ruleInfo.ParcelValueMax, ruleInfo.ParcelValueMin, ruleInfo.ParcelWeightMax, ruleInfo.ParcelWeightMin, ruleInfo.ShopGroupListVo,
				ruleInfo.DgType, ruleInfo.ParcelDimension)
			//priority不同，ilh只判断 whs id, destination port, dg type
			if r.RoutingType == rule.IlhRoutingType {
				key = fmt.Sprintf("forecast-rule:%v-%v-%v", ruleInfo.WhsId, ruleInfo.DestinationPorts, ruleInfo.DgType)
			}
			if _, ok := existConditionRule[key]; ok {
				//ilh返回自己的错误信息
				if r.RoutingType == rule.IlhRoutingType {
					return nil, srerr.New(srerr.ParamErr, nil, IlhTaskDuplicatedRule)
				}
				return nil, srerr.New(srerr.ParamErr, nil, TaskDuplicatedRule)
			} else {
				existConditionRule[key] = true
			}
		}

		if ruleInfo.RuleDetail != nil {
			tmpRule.RuleDetails = ruleInfo.RuleDetail
			if err := tmpRule.SetRuleDetails(ruleInfo.RuleDetail); err != nil {
				return nil, srerr.With(srerr.ParamErr, nil, err)
			}
		}

		if ruleInfo.IsMultiProduct && r.RoutingType != rule.IlhRoutingType {
			if ruleInfo.MultiProductDefaultCriteria != nil {
				tmpRule.MultiProductDefaultCriteria = ruleInfo.MultiProductDefaultCriteria
				if err := tmpRule.SetMultiProductDefaultCriteria(ruleInfo.MultiProductDefaultCriteria); err != nil {
					return nil, srerr.With(srerr.ParamErr, nil, err)
				}
			}
			if ruleInfo.MultiProductDisableInfo != nil {
				tmpRule.MultiProductDisableInfo = ruleInfo.MultiProductDisableInfo
				if err := tmpRule.SetMultiProductDisabledInfo(ruleInfo.MultiProductDisableInfo); err != nil {
					return nil, srerr.With(srerr.ParamErr, nil, err)
				}
			}
		} else {
			//rule of ilh product or single product, will use default-criteria and disabled-info
			if ruleInfo.DefaultCriteria != nil {
				tmpRule.DefaultCriteria = ruleInfo.DefaultCriteria
				if err := tmpRule.SetDefaultCriteria(ruleInfo.DefaultCriteria); err != nil {
					return nil, srerr.With(srerr.ParamErr, nil, err)
				}
			}
			if ruleInfo.DisabledInfo != nil {
				tmpRule.DisabledInfo = ruleInfo.DisabledInfo
				if err := tmpRule.SetDisabledInfo(ruleInfo.DisabledInfo); err != nil {
					return nil, srerr.With(srerr.ParamErr, nil, err)
				}
			}
		}

		var cErr error
		tmpRule.StrCombinationSetting, cErr = jsoniter.Marshal(ruleInfo.CombinationSetting)
		if cErr != nil {
			return nil, srerr.With(srerr.ParamErr, ruleInfo.CombinationSetting, cErr)
		}

		if err := tmpRule.SetTplToggleInfo(ruleInfo.TplToggleInfo); err != nil {
			return nil, srerr.With(srerr.ParamErr, nil, err)
		}

		tmpRule.Id = ruleInfo.RuleId
		tmpRule.TaskId = r.Id
		tmpRule.RuleName = ruleInfo.RuleName
		tmpRule.Priority = ruleInfo.Priority
		tmpRule.WhsId = objutil.SetSliceToString(ruleInfo.WhsId)
		tmpRule.ZoneCode = objutil.SetSliceToString(ruleInfo.ZoneCode)
		tmpRule.ItemCategoryLevel = ruleInfo.ItemCategoryLevel
		tmpRule.ItemCategoryId = objutil.SetIntSliceToString(ruleInfo.ItemCategoryId)
		tmpRule.ParcelValueMax = ruleInfo.ParcelValueMax
		tmpRule.ParcelValueMin = ruleInfo.ParcelValueMin
		tmpRule.ParcelWeightMax = ruleInfo.ParcelWeightMax
		tmpRule.ParcelWeightMin = ruleInfo.ParcelWeightMin
		tmpRule.WhsIdList = ruleInfo.WhsId
		tmpRule.ZoneCodeList = ruleInfo.ZoneCode
		tmpRule.ItemCategoryIdList = ruleInfo.ItemCategoryId
		tmpRule.IsMultiProduct = ruleInfo.IsMultiProduct
		tmpRule.RoutingType = r.RoutingType
		tmpRule.DgType = ruleInfo.DgType
		tmpRule.ParcelDimension = ruleInfo.ParcelDimension
		tmpRule.DestinationPorts = objutil.SetSliceToString(ruleInfo.DestinationPorts)
		tmpRule.CCMode = ruleInfo.CCMode
		tmpRule.ShopGroupList = objutil.JsonBytes(ruleInfo.ShopGroupListVo)
		tmpRule.ShopGroupListVo = ruleInfo.ShopGroupListVo
		tmpRules = append(tmpRules, tmpRule)
	}
	return tmpRules, nil
}

type (
	// CreateHCTaskRequest 创建草稿态task任务的请求
	//后续会将[OrderStartTime, OrderEndTime]范围内的订单进行硬性校验
	CreateHCTaskRequest struct {
		TaskName       string `json:"task_name"`
		ProductId      uint64 `json:"product_id"`
		IsMultiProduct bool   `json:"is_multi_product"`
		OrderStartTime string `json:"order_start_time"`
		OrderEndTime   string `json:"order_end_time"`
		OrderCount     int64  `json:"order_count"`
		Operator       string `json:"operator"`
		RoutingType    int    `json:"routing_type"`
	}
	// DraftTaskResponse 创建/编辑草稿态task任务的响应
	DraftTaskResponse struct {
		TaskId uint64 `json:"task_id"`
	}
	// EditHCTaskRequest 编辑草稿态硬性校验task任务的请求
	EditHCTaskRequest struct {
		TaskId         uint64 `json:"task_id"`
		TaskName       string `json:"task_name"`
		OrderStartTime string `json:"order_start_time"`
		OrderEndTime   string `json:"order_end_time"`
		OrderCount     int64  `json:"order_count"`
	}
	// QueryOrderCountRequest 查询订单数的请求
	QueryOrderCountRequest struct {
		TaskId         uint64 `json:"task_id"`
		ProductId      uint64 `json:"product_id"`
		OrderStartTime string `json:"order_start_time"`
		OrderEndTime   string `json:"order_end_time"`
	}
	// QueryOrderCountResponse 查询订单数的响应
	QueryOrderCountResponse struct {
		OrderCount       uint64            `json:"order_count"`
		EstimatedTime    uint64            `json:"estimated_time"`
		CompletionOrders []CompletionOrder `json:"completion_orders"` //对应的order信息列表
	}
	// DeleteDraftHCTaskRequest 删除草稿态硬性校验任务的请求
	DeleteDraftHCTaskRequest struct {
		TaskId uint64 `json:"task_id"`
	}
	// UpdateHCTaskStatusRequest 更新硬性校验任务状态的请求
	UpdateHCTaskStatusRequest struct {
		TaskId uint64 `json:"task_id"`
		//0-Draft,1-Waiting,2-Doing,3-Done,4-Stop,5-Fail
		TaskStatus int `json:"task_status"` //目标态
		OldStatus  int `json:"old_status"`  //task当前状态
	}
	// GetHCTaskListRequest 获取硬性校验刷新任务列表的请求
	GetHCTaskListRequest struct {
		TaskType       uint8  `json:"task_type"` //任务类型：1-ops， 2-system
		TaskId         uint64 `json:"task_id"`
		TaskName       string `json:"task_name"`
		ProductId      uint64 `json:"product_id"`
		TaskStatus     string `json:"task_status"` //任务状态：0-draft,allowing edit, 1-waiting, 2-pending, 3-doing, 4-done, 5-stopped, 6-terminated, 7-failed
		IsMultiProduct bool   `json:"is_multi_product"`
		RoutingType    int    `json:"routing_type"`
		PageNo         int64  `json:"pageno"`    //分页序号，从1开始
		PageCount      int64  `json:"pagecount"` //每页的限制数量
	}
	// GetHCTaskListResponse 获取硬性校验刷新任务列表的返回
	GetHCTaskListResponse struct {
		TaskList  []GetHCTaskList `json:"task_list"`
		Total     int             `json:"total"`
		PageNo    int64           `json:"pageno"`    //分页序号，从1开始
		PageCount int64           `json:"pagecount"` //每页的限制数量
	}
	GetHCTaskList struct {
		TaskType       uint8   `json:"task_type"`
		TaskId         uint64  `json:"task_id"`
		TaskName       string  `json:"task_name"`
		ProductId      uint64  `json:"product_id"`
		IsMultiProduct bool    `json:"is_multi_product"`
		TaskStatus     uint8   `json:"task_status"`      //任务状态：0-draft,allowing edit, 1-waiting, 2-pending, 3-doing, 4-done, 5-stopped, 6-terminated, 7-failed
		OrderStartTime string  `json:"order_start_time"` //要刷新的订单的起始时间
		OrderEndTime   string  `json:"order_end_time"`   //要刷新的订单的结束时间
		OrderCount     uint64  `json:"order_count"`      //该批次订单的总数
		TaskStartTime  uint32  `json:"task_start_time"`  //task开始的时间
		TaskEndTime    uint32  `json:"task_end_time"`    //task结束的时间
		TaskProgress   float64 `json:"task_progress"`    //任务执行进度（总）
		Operator       string  `json:"operator"`         //上次执行操作的人
		Ctime          uint64  `json:"ctime"`            //该任务创建的时间
	}
	// GetHCTaskDetailRequest 获取硬性校验刷新任务详情的请求
	GetHCTaskDetailRequest struct {
		TaskId uint64 `json:"task_id"`
	}
	// GetHCTaskDetailResponse 获取硬性校验刷新任务详情的响应
	GetHCTaskDetailResponse struct {
		TaskType             uint8             `json:"task_type"`
		TaskId               uint64            `json:"task_id"`
		TaskName             string            `json:"task_name"`
		ProductId            uint64            `json:"product_id"`
		TaskStatus           uint8             `json:"task_status"`       //任务状态：0-draft,allowing edit, 1-waiting, 2-pending, 3-doing, 4-done, 5-stopped, 6-terminated, 7-failed
		OrderStartTime       string            `json:"order_start_time"`  //要刷新的订单的起始时间(对应order_start_time)
		OrderEndTime         string            `json:"order_end_time"`    //要刷新的订单的结束时间(对应order_end_time)
		OrderCount           uint64            `json:"order_count"`       //该批次订单的总数
		TaskStartTime        uint32            `json:"task_start_time"`   //task开始的时间
		TaskEndTime          uint32            `json:"task_end_time"`     //task结束的时间
		LastStartTime        uint32            `json:"last_start_time"`   //上一次任务开始时间
		LastEndTime          uint32            `json:"last_end_time"`     //上一次任务结束时间
		TaskProgress         float64           `json:"task_progress"`     //任务执行进度（总）
		CompletionOrders     []CompletionOrder `json:"completion_orders"` //对应的order信息列表
		EstimatedTime        uint64            `json:"estimated_time"`
		Operator             string            `json:"operator"` //上次执行操作的人
		Ctime                uint64            `json:"ctime"`    //该任务创建的时间
		BlockOrderQuantity   int               `json:"block_order_quantity"`
		BlockOrderPercentage float64           `json:"block_order_percentage"`
	}

	CompletionOrder struct {
		OrderPaidTime    string  `json:"order_paid_time"`   //该批order下单的日期
		ShipmentQuantity uint64  `json:"shipment_quantity"` //该批order的总数
		Percentage       float64 `json:"percentage"`        //该批order硬性校验刷新进度
	}

	UpdateHCTaskRequest struct {
		Id             uint64 `json:"id"`
		TaskName       string `json:"task_name"`
		OrderStartTime string `json:"order_start_time"`
		OrderEndTime   string `json:"order_end_time"`
		OrderCount     int64  `json:"order_count"`
		TaskStatus     int    `json:"task_status"`   //硬校验任务状态：0-draft,allowing edit,1-waiting,2-pending,3-doing,4-done,5-stopped,6-terminated,7-failed
		LastOrderId    uint64 `json:"last_order_id"` //断点续做的订单id
		LastEndTime    int64  `json:"last_end_time"`
		LastOrderTime  int64  `json:"last_order_time"` //断点续做的订单id对应的那一天的时间
	}

	ImportWeightRangeRequest struct {
		TaskId   int64  `json:"task_id"`
		FilePath string `json:"file_path" validate:"UssHostValidate"`
	}

	QueryOrderAggregationRequest struct {
		TaskId                int    `json:"task_id"`
		StartDate             string `json:"start_date"`
		EndDate               string `json:"end_date"`
		FmAble                bool   `json:"fm_code"`
		LmAble                bool   `json:"lm_code"`
		SiteAble              bool   `json:"site_code"`
		ActualPoint           bool   `json:"actual_point"`
		BuyerStateAble        bool   `json:"buyer_state"`
		BuyerCityAble         bool   `json:"buyer_city"`
		WeightRangeAble       bool   `json:"weight_range"`
		OrderAmountAble       bool   `json:"order_amount"`
		PercentageOfTotalAble bool   `json:"percentage_of_total"`
		ADOAble               bool   `json:"ado"`
		ShippingFee           bool   `json:"shipping_fee"`
		IsResult              bool   `json:"is_result"`
	}

	ForecastRuleInfo struct {
		RuleId   int64                           `json:"rule_id"`
		RuleName string                          `json:"rule_name"`
		Priority int                             `json:"priority"`
		Data     []QueryOrderAggregationResponse `json:"data"`
	}

	QueryOrderAggregationResponse struct {
		FMCode            string   `json:"fm_code"`
		LMCode            string   `json:"lm_code"`
		SiteCode          []string `json:"site_code"`
		SiteCode1         string   `json:"site_code1"` //前端不太好展示所以返回site_code 1和2，但保留原来的siteCode数组
		SiteCode2         string   `json:"site_code2"`
		RuleId            int64    `json:"rule_id"`
		Priority          int64    `json:"priority"`
		ActualPoint       []string `json:"actual_point"` //前端不太好展示所以返回actual_point 1和2，但保留原来的actp数组
		ActualPoint1      string   `json:"actual_point1"`
		ActualPoint2      string   `json:"actual_point2"`
		BuyerState        string   `json:"buyer_state"`
		BuyerCity         string   `json:"buyer_city"`
		WeightRange       string   `json:"weight_range"`
		OrderAmount       int      `json:"order_amount"`
		PercentageOfTotal float64  `json:"percentage_of_total"`
		Ado               float64  `json:"ado"`
		ShippingFee       float64  `json:"shipping_fee"`
	}

	ExecuteHCTaskRequest struct {
		TaskType      int `json:"task_type" binding:"required"` // 1:Ops or 2:Sys
		RoutineNum    int `json:"routine_num"`                  //number of go routines to be used
		MinOrderCount int `json:"min_order_count"`              //minimum number of orders
		MaxOrderCount int `json:"max_order_count"`              //maximum number of orders
	}

	RefreshHbaseHardCriteriaRequest struct {
		ProductId      int    `json:"product_id"`
		StartDate      string `json:"start_date"`
		EndDate        string `json:"end_date"`
		IsMultiProduct bool   `json:"is_multi_product"`
		Concurrency    int    `json:"concurrency"`
	}
)

func ValidateDate(startTime string, endTime string) error {
	start, err := timeutil.ParseLocalTime(timeutil.DateFormat, startTime)
	if err != nil {
		return fmt.Errorf("invalid start_date: %s, layout: %s", startTime, timeutil.DateFormat)
	}
	end, err := timeutil.ParseLocalTime(timeutil.DateFormat, endTime)
	if err != nil {
		return fmt.Errorf("invalid end_date: %s, layout: %s", endTime, timeutil.DateFormat)
	}
	if end.Sub(start) < 0 {
		return errors.New("The end_date must be later than the start_date. ")
	}
	return nil
}

type BlockOrderListRequest struct {
	TaskId int `json:"task_id"`
}

type BlockOrderListResponse struct {
	BlockType       string  `json:"block_type"`
	BlockOrderCount int     `json:"block_order_count"`
	BlockPercentage float64 `json:"block_percentage"`
}

type CheckCanDeployTask struct {
	TaskId int64 `json:"task_id"`
}

type GetShopGroupListReq struct {
	ProductId int64 `form:"product_id" json:"product_id"`
}

type GetShopGroupListResp struct {
	List []ShopGroupInfo `json:"list"`
}

type ShopGroupInfo struct {
	ShopGroupId   int64  `json:"shop_group_id"`
	ShopGroupName string `json:"shop_group_name"`
}

type (
	ExportCbShopGroupReq struct {
		IsTask bool  `json:"is_task"`
		RuleId int64 `json:"rule_id"`
	}

	ExportCbShopGroupResp struct {
		Url string `json:"url"`
	}
)

type (
	BatchDeployReq struct {
		FileUrl string `json:"file_url"`
	}

	BlockItem struct {
		MultiProductId int `json:"multi_product_id"`
		TaskId         int `json:"task_id"`
		BlockRate      int `json:"block_rate"`
	}

	BatchDeployCheckResp struct {
		BlockList []BlockItem `json:"block_list"`
	}

	UploadPredictionVolumeReq struct {
		TaskId  int    `json:"task_id"`
		FileUrl string `json:"file_url"`
	}

	GetPredictionVolumeByTaskIdReq struct {
		TaskId int `form:"task_id" json:"task_id"`
	}
)
