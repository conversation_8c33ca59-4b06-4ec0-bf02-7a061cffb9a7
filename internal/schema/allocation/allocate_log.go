package allocation

import jsoniter "github.com/json-iterator/go"

type AllocationLog struct {
	List []*LogDetail
}

type LogDetail struct {
	RequestId            string `json:"request_id"`
	FOrderId             uint64 `json:"forder_id"`
	OrderId              uint64 `json:"order_id"`
	Status               bool   `json:"status"` // 请求结果状态
	UniqueId             string `json:"unique_id"`
	FulfillmentProductId int    `json:"fulfillment_product_id"`
	MaskProductId        int    `json:"mask_product_id"`
	RequestTime          int64  `json:"request_time"`

	RequestDataStr  string      `json:"request_data"`
	RequestData     interface{} `json:"-"`
	ResponseDataStr string      `json:"response_data"`
	ResponseData    interface{} `json:"-"`

	HardCriteriaList    map[int64][]ProductToggle `json:"-"`
	HardCriteriaListStr string                    `json:"hard_criteria_list"`
	HardInput           []int                     `json:"hard_input"`
	HardOutput          []int                     `json:"hard_output"`

	SoftCriteriaList    []SoftCriteria `json:"-"`
	SoftCriteriaListStr string         `json:"soft_criteria_list"`

	SoftInput           []int64  `json:"soft_input"`
	SoftOutput          []int64  `json:"soft_output"`
	SoftRuleId          int64    `json:"soft_rule_id"`
	VolumeRuleId        uint64   `json:"volume_rule_id"`
	ZoneDestinationCode string   `json:"zone_destination_code"`
	ZoneOriginCode      string   `json:"zone_origin_code"`
	RouteCodes          []string `json:"route_codes"`
	ShopGroupId         int64    `json:"shop_group_id"`
}

type ProductToggle struct {
	Available int `json:"available"`
	Priority  int `json:"priority"`
	ProductId int `json:"product_id"`
}

type LocationVolume struct {
	OriginZone string
	DestZone   string
	RouteZone  string
}

type SoftCriteria struct {
	Id     int                           `json:"id"`
	Name   string                        `json:"name"`
	Detail map[int64]*SoftCriteriaDetail `json:"detail"`
}

type SoftCriteriaDetail struct {
	Input                          int64        `json:"input"`
	Output                         int64        `json:"output"`
	MaxDailyLimitOfCountry         int32        `json:"max_daily_limit_of_country"`
	MaxDailyLimitOfRoutes          []RouteLimit `json:"max_daily_limit_of_routes"`
	MaxDailyLimitOfZoneDestination []ZoneLimit  `json:"max_daily_limit_of_zone_destination"`
	MaxDailyLimitOfZoneOrigin      []ZoneLimit  `json:"max_daily_limit_of_zone_origin"`
	MinDailyLimitOfCountry         int32        `json:"min_daily_limit_of_country"`
	MinDailyLimitOfRoutes          []RouteLimit `json:"min_daily_limit_of_routes"`
	MinDailyLimitOfZoneDestination []ZoneLimit  `json:"min_daily_limit_of_zone_destination"`
	MinDailyLimitOfZoneOrigin      []ZoneLimit  `json:"min_daily_limit_of_zone_origin"`
	ShippingFee                    float64      `json:"shipping_fee"`
	SystemVolumeOfProduct          int32        `json:"system_volume_of_product"`
	SystemVolumeOfRoutes           []RouteLimit `json:"system_volume_of_routes"`
	SystemVolumeOfZoneDestination  []ZoneLimit  `json:"system_volume_of_zone_destination"`
	SystemVolumeOfZoneOrigin       []ZoneLimit  `json:"system_volume_of_zone_origin"`
	MaxVolumeInBatch               int32        `json:"max_volume_in_batch"`
	SystemVolumeInBatch            int32        `json:"system_volume_in_batch"`
	BatchSize                      int32        `json:"batch_size"`
	ProductWeightage               int32        `json:"product_weightage"`
	ProductStatus                  bool         `json:"product_status"`
	ProductPriority                int32        `json:"product_priority"`
	WhitelistPriority              int32        `json:"whitelist_priority"`
}

type RouteLimit struct {
	RouteCode string `json:"route_code"`
	Limit     int32  `json:"limit"`
}

type ZoneLimit struct {
	ZoneCode string `json:"zone_code"`
	Limit    int32  `json:"limit"`
}

type ScheduleCountStatTaskMsg struct {
	AllocationLog *LogDetail `json:"allocation_log"`
	BusinessType  int        `json:"business_type"`
	BusinessId    string     `json:"business_id"`
}

// MarshallStatMsg 序列化统计数据结构体到字节
func MarshallStatMsg(allocationLog *LogDetail, businessType int, businessId string) []byte {
	msg := ScheduleCountStatTaskMsg{
		AllocationLog: allocationLog,
		BusinessType:  businessType,
		BusinessId:    businessId,
	}
	msgByte, _ := jsoniter.Marshal(msg)

	return msgByte
}

type BatchAllocationDetail struct {
	// basic info
	RequestId            string `json:"request_id"`
	FOrderId             uint64 `json:"forder_id"`
	OrderId              uint64 `json:"order_id"`
	Status               bool   `json:"status"` // 请求结果状态
	UniqueId             string `json:"unique_id"`
	FulfillmentProductId int    `json:"fulfillment_product_id"`
	MaskProductId        int    `json:"mask_product_id"`
	RequestTime          int64  `json:"request_time"`
	IsWms                bool   `json:"is_wms"`
	AllocationMethod     string `json:"allocation_method"`
	BatchId              int    `json:"batch_id"`
	BatchName            string `json:"batch_name"`
	BatchTime            string `json:"batch_time"` //e.g. 15:10 - 15:13
	BatchSize            int    `json:"batch_size"`

	// hard check
	HardCriteriaList    map[int64][]ProductToggle `json:"-"`
	HardCriteriaListStr string                    `json:"hard_criteria_list"`
	HardInput           []int                     `json:"hard_input"`
	HardOutput          []int                     `json:"hard_output"`
	IgnoreSnapShot      bool                      `json:"ignore_snap_shot"`

	// soft check
	Input            []int    `json:"input"`  // products will go through soft check
	Output           []int    `json:"output"` // final allocated results
	SoftRuleId       int      `json:"soft_rule_id"`
	ShopGroupId      int      `json:"shop_group_id"`
	VolumeRuleId     int      `json:"volume_rule_id"`
	RuleType         string   `json:"rule_type"` // e.g. Zone
	DestZoneCodeList []string `json:"dest_zone_code_list"`
	RouteCodeList    []string `json:"route_code_list"`
}
