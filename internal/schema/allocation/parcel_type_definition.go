package allocation

import (
	entity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
)

type (
	CreateParcelTypeDefinitionReq struct {
		MaskProductID        int                    `json:"mask_product_id"`
		FulfillmentProductID int                    `json:"fulfillment_product_id"`
		LineID               string                 `json:"line_id"`
		ScenarioType         int                    `json:"scenario_type" validate:"oneof=0 1 2 4"`
		BulkyDefinition      entity.BulkyDefinition `json:"bulky_definition"`
		HighValueDefinition  float64                `json:"high_value_definition"  validate:"gte=0"`
		Status               int                    `json:"status"`
	}

	UpdateParcelTypeDefinitionReq struct {
		ID                   uint64                 `json:"id"`
		MaskProductID        int                    `json:"mask_product_id"`
		FulfillmentProductID int                    `json:"fulfillment_product_id"`
		LineID               string                 `json:"line_id"`
		ScenarioType         int                    `json:"scenario_type" validate:"oneof=0 1 2 4"`
		BulkyDefinition      entity.BulkyDefinition `json:"bulky_definition"`
		HighValueDefinition  float64                `json:"high_value_definition"`
		Status               int                    `json:"status"`
	}

	GetParcelTypeDefinitionReq struct {
		ID uint64 `json:"id"`
	}

	ListParcelTypeDefinitionReq struct {
		MaskProductID        int    `json:"mask_product_id"`
		FulfillmentProductID int    `json:"fulfillment_product_id"`
		LineID               string `json:"line_id"`
		ScenarioType         int    `json:"scenario_type" validate:"oneof=0 1 2 4"`
		Status               *int   `json:"status"`
		PageNo               int    `json:"page_no"`
		Limit                int    `json:"limit"`
	}
	ListParcelTypeDefinitionResp struct {
		Count  int                                `json:"count"`
		PageNo int                                `json:"page_no"`
		Total  int64                              `json:"total"`
		List   []*entity.ParcelTypeDefinitionItem `json:"list"`
	}

	DeleteParcelTypeDefinitionReq struct {
		ID uint64 `json:"id"`
	}
)
