package allocation

type (
	//get
	GetMinuteOrderConfReq struct {
		Id            uint64 `json:"id"`
		MaskProductId uint64 `json:"mask_product_id"`
		ConfStatus    int    `json:"conf_status"`
		ConfType      int    `json:"conf_type"`
	}
	GetMinuteOrderConfResp struct {
		List []BatchMinuteOrderConfUnit `json:"list"`
	}
	BatchMinuteOrderConfUnit struct {
		Minute int64  `json:"minute"`
		Ratio  string `json:"ratio"` //使用string保存，避免前端展示时丢失精度
	}

	//get list
	GetMinuteOrderConfListReq struct {
	}
	GetMinuteOrderConfListResp struct {
	}
)

type (
	CreateMinuteOrderConfReq struct {
		MaskProductId uint64 `json:"mask_product_id"`
		ConfType      int    `json:"conf_type"` //0: normal; 1: campaign
		Url           string `json:"url"`
	}
)

type (
	UpdateMinuteConfReq struct {
		Id  uint64 `json:"id"`
		Url string `json:"url"`
	}
)
