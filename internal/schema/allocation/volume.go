package allocation

import (
	"fmt"
	"sort"
	"strings"
)

type UpdateVolumeRequest struct {
	Volumes       map[int32]int32                    `json:"volumes,omitempty"`
	VolumeInRules []*UpdateVolumeRequestVolumeInRule `json:"volume_in_rules,omitempty"`
}

type UpdateVolumeRequestVolumeInRule struct {
	RuleId  int32           `json:"rule_id,omitempty"`
	Volumes map[int32]int32 `json:"volumes,omitempty"`
}

func (m *UpdateVolumeRequest) Validate() error {
	return m.validate(false)
}

func (m *UpdateVolumeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVolumeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]int32, len(m.Volumes))
		i := 0
		for key := range m.Volumes {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.Volumes[key]
			_ = val

			// no validation rules for Volumes[key]

			if val < 0 {
				err := UpdateVolumeRequestValidationError{
					field:  fmt.Sprintf("Volumes[%v]", key),
					reason: "value must be greater than or equal to 0",
				}
				if !all {
					return err
				}
				errors = append(errors, err)
			}

		}
	}

	for idx, item := range m.VolumeInRules {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateVolumeRequestValidationError{
						field:  fmt.Sprintf("VolumeInRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateVolumeRequestValidationError{
						field:  fmt.Sprintf("VolumeInRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateVolumeRequestValidationError{
					field:  fmt.Sprintf("VolumeInRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateVolumeRequestMultiError(errors)
	}

	return nil
}

type UpdateVolumeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

func (e UpdateVolumeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVolumeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

type UpdateVolumeRequestMultiError []error

func (m UpdateVolumeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}
