package allocation

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
)

const (
	maxBatchVolumeValue int64 = *********
)

type GetMaskRuleRequest struct {
	Id int64 `json:"id"`
}

type MaskRule struct {
	Id                    int64           `json:"id,omitempty"`
	MaskProductId         int64           `json:"mask_product_id,omitempty"`
	RuleStatus            int64           `json:"rule_status,omitempty"`
	RuleName              string          `json:"rule_name,omitempty"`
	EnableProductPriority bool            `json:"enable_product_priority,omitempty"`
	RuleDetail            *MaskRuleDetail `json:"rule_detail,omitempty"`
	OperatedBy            string          `json:"operated_by,omitempty"`
	EffectiveStartTime    uint32          `json:"effective_start_time,omitempty"`
	CTime                 uint32          `json:"ctime,omitempty"`
	MTime                 uint32          `json:"mtime,omitempty"`
	ForecastTaskId        int64           `json:"forecast_task_id,omitempty"`
	RuleMode              int32           `json:"rule_mode"`
	AllocationMethod      int64           `json:"allocation_method"` //1:single ; 2:batch -- todo:SSCSMR-1695:将0兼容成1返回
	BatchRuleDetail       BatchRuleDetail `json:"batch_rule_detail"`
	ApprovalTime          int64           `json:"approval_time"`
}

type MaskRuleDetail struct {
	MinVolumeCountryEnable              bool                          `json:"min_volume_country_enable"`
	MinVolumeCountrySort                int32                         `json:"min_volume_country_sort,omitempty"`
	MinVolumeZoneRouteEnable            bool                          `json:"min_volume_zone_route_enable"`
	MinVolumeZoneRouteSort              int32                         `json:"min_volume_zone_route_sort,omitempty"`
	MaxCapacityCountryEnable            bool                          `json:"max_capacity_country_enable"`
	MaxCapacityCountrySort              int32                         `json:"max_capacity_country_sort,omitempty"`
	MaxCapacityZoneRouteEnable          bool                          `json:"max_capacity_zone_route_enable"`
	MaxCapacityZoneRouteSort            int32                         `json:"max_capacity_zone_route_sort,omitempty"`
	CheapestFeeEnable                   bool                          `json:"cheapest_fee_enable,omitempty"`
	CheapestFeeSort                     int32                         `json:"cheapest_fee_sort,omitempty"`
	MaxBatchVolumeEnable                bool                          `json:"max_batch_volume_enable,omitempty"`
	MaxBatchVolumeSort                  int32                         `json:"max_batch_volume_sort,omitempty"`
	MaxBatchVolume                      int32                         `json:"max_batch_volume,omitempty"`
	PickupEfficiencyWhitelistEnable     bool                          `json:"pickup_efficiency_whitelist_enable"`
	PickupEfficiencyWhitelistSort       int32                         `json:"pickup_efficiency_whitelist_sort,omitempty"`
	MaxCodCapacityCountryEnable         bool                          `json:"max_cod_capacity_country_enable"`
	MaxCodCapacityCountrySort           int32                         `json:"max_cod_capacity_country_sort,omitempty"`
	MaxCodCapacityZoneRouteEnable       bool                          `json:"max_cod_capacity_zone_route_enable"`
	MaxCodCapacityZoneRouteSort         int32                         `json:"max_cod_capacity_zone_route_sort,omitempty"`
	MaxBulkyCapacityCountryEnable       bool                          `json:"max_bulky_capacity_country_enable"`
	MaxBulkyCapacityCountrySort         int32                         `json:"max_bulky_capacity_country_sort,omitempty"`
	MaxBulkyCapacityZoneRouteEnable     bool                          `json:"max_bulky_capacity_zone_route_enable"`
	MaxBulkyCapacityZoneRouteSort       int32                         `json:"max_bulky_capacity_zone_route_sort,omitempty"`
	MaxHighValueCapacityCountryEnable   bool                          `json:"max_high_value_capacity_country_enable"`
	MaxHighValueCapacityCountrySort     int32                         `json:"max_high_value_capacity_country_sort,omitempty"`
	MaxHighValueCapacityZoneRouteEnable bool                          `json:"max_high_value_capacity_zone_route_enable"`
	MaxHighValueCapacityZoneRouteSort   int32                         `json:"max_high_value_capacity_zone_route_sort,omitempty"`
	MaxDgCapacityCountryEnable          bool                          `json:"max_dg_capacity_country_enable"`
	MaxDgCapacityCountrySort            int32                         `json:"max_dg_capacity_country_sort,omitempty"`
	MaxDgCapacityZoneRouteEnable        bool                          `json:"max_dg_capacity_zone_route_enable"`
	MaxDgCapacityZoneRouteSort          int32                         `json:"max_dg_capacity_zone_route_sort,omitempty"`
	Limit                               []*MaskRuleDetailProductLimit `json:"limit,omitempty"`
}

type MaskRuleDetailProductLimit struct {
	Id                 int64  `json:"id,omitempty"`
	Name               string `json:"name,omitempty"`
	MinVolume          int32  `json:"min_volume,omitempty"`
	MaxCapacity        int32  `json:"max_capacity,omitempty"`
	MaxVolumeEachBatch int32  `json:"max_volume_each_batch"`
	IsHardCap          bool   `json:"is_hard_cap,omitempty"`
}

type BatchRuleDetail struct {
	BatchAllocationRuleConfig BatchAllocationRuleConfig `json:"batch_allocation_rule_config"`
	BatchSize                 BatchSize                 `json:"batch_size"`
}

type GetRulesNameAndIdResponse struct {
	RuleSimpleInfo []*RuleSimpleInfo `json:"rule_simple_info"`
}

type RuleSimpleInfo struct {
	Id   int32  `json:"id"`
	Name string `json:"name"`
}

type GetRuleCfg struct {
	RuleMode int32 `json:"rule_mode" form:"rule_mode"`
}

type RuleConf struct {
	Steps                  []*RuleConfStep `json:"steps"`
	DisableProductPriority bool            `json:"disable_product_priority"`
}

type RuleConfStep struct {
	Step     int32 `json:"step"`
	Disabled bool  `json:"disabled"`
}

type ListRuleRequest struct {
	FilterById            bool  `json:"filter_by_id"`
	Id                    int32 `json:"id"`
	FilterByStatus        bool  `json:"filter_by_status"`
	Status                int32 `json:"status"`
	FilterByMaskProductId bool  `json:"filter_by_mask_product_id"`
	MaskProductId         int32 `json:"mask_product_id"`
	Pageno                int32 `json:"pageno"`
	Limit                 int32 `json:"limit"`
	RuleMode              int32 `json:"rule_mode"`
	AllocationMethod      int64 `json:"allocation_method"`
}

type ListRuleReply struct {
	List   []*MaskRule `json:"list,omitempty"`
	Pageno int32       `json:"pageno,omitempty"`
	Count  int32       `json:"count,omitempty"`
	Total  int32       `json:"total,omitempty"`
}

func (x *ListRuleRequest) GetFilterById() bool {
	if x != nil {
		return x.FilterById
	}
	return false
}

func (x *ListRuleRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListRuleRequest) GetFilterByStatus() bool {
	if x != nil {
		return x.FilterByStatus
	}
	return false
}

func (x *ListRuleRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListRuleRequest) GetFilterByMaskProductId() bool {
	if x != nil {
		return x.FilterByMaskProductId
	}
	return false
}

func (x *ListRuleRequest) GetMaskProductId() int32 {
	if x != nil {
		return x.MaskProductId
	}
	return 0
}

func (x *ListRuleRequest) GetPageno() int32 {
	if x != nil {
		return x.Pageno
	}
	return 0
}

func (x *ListRuleRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListConfigRequest struct {
	FilterByMaskProductId bool  `json:"filter_by_mask_product_id"`
	MaskProductId         int32 `json:"mask_product_id"`
	FilterByMaskType      bool  `json:"filter_by_mask_type"`
	MaskType              int32 `json:"mask_type"`
	Pageno                int32 `json:"pageno"`
	Limit                 int32 `json:"limit"`
}

type ListConfigReply struct {
	List   []*AllocationConfig `json:"list"`
	Pageno int32               `json:"pageno"`
	Count  int32               `json:"count"`
	Total  int32               `json:"total"`
}

type AllocationConfig struct {
	Id                     int32        `json:"id"`
	MaskProduct            *MaskProduct `json:"mask_product"`
	SmartAllocationEnabled bool         `json:"smart_allocation_enabled"`
	DefaultProduct         *Product     `json:"default_product"`
	DefaultWmsProduct      *Product     `json:"default_wms_product"`
	OperatedBy             string       `json:"operated_by"`
	Ctime                  uint32       `json:"ctime"`
	Mtime                  uint32       `json:"mtime"`
}

type Product struct {
	Id   int32  `json:"id"`
	Name string `json:"name"`
}

type MaskProduct struct {
	Id       int32  `json:"id"`
	Name     string `json:"name"`
	MaskType int32  `json:"mask_type"`
}

type GetConfigRequest struct {
	Id int32 `json:"id"`
}

type CreateRuleRequest struct {
	RuleName         string `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	MaskProductId    int32  `protobuf:"varint,2,opt,name=mask_product_id,json=maskProductId,proto3" json:"mask_product_id,omitempty"`
	RuleMode         int32  `json:"rule_mode"`
	AllocationMethod int64  `json:"allocation_method"`
}

type (
	UpdateRuleRequest struct {
		Id                    int32                    `json:"id,omitempty"`
		MaskProductId         int32                    `json:"mask_product_id,omitempty"`
		RuleStatus            int32                    `json:"rule_status,omitempty"`
		RuleName              string                   `json:"rule_name,omitempty"`
		EnableProductPriority bool                     `json:"enable_product_priority,omitempty"`
		RuleDetail            *UpdateRuleRequestDetail `json:"rule_detail,omitempty"`
		OperatedBy            string                   `json:"operated_by,omitempty"`
		EffectiveStartTime    int32                    `json:"effective_start_time,omitempty"`
		Ctime                 int32                    `json:"ctime,omitempty"`
		Mtime                 int32                    `json:"mtime,omitempty"`
		ForecastTaskId        int64                    `json:"forecast_task_id,omitempty"`
		RuleMode              int32                    `json:"rule_mode"`
		EffectiveImmediately  bool                     `json:"effective_immediately,omitempty"`
		AllocationMethod      int64                    `json:"allocation_method"` //1:single ; 2:batch -- todo:SSCSMR-1695:将0兼容成1返回
		BatchRuleDetail       BatchRuleDetail          `json:"batch_rule_detail"`
	}

	UpdateRuleRequestDetail struct {
		MinVolumeCountryEnable              bool  `json:"min_volume_country_enable"`
		MinVolumeCountrySort                int32 `json:"min_volume_country_sort,omitempty"`
		MinVolumeZoneRouteEnable            bool  `json:"min_volume_zone_route_enable"`
		MinVolumeZoneRouteSort              int32 `json:"min_volume_zone_route_sort,omitempty"`
		MaxCapacityCountryEnable            bool  `json:"max_capacity_country_enable"`
		MaxCapacityCountrySort              int32 `json:"max_capacity_country_sort,omitempty"`
		MaxCapacityZoneRouteEnable          bool  `json:"max_capacity_zone_route_enable"`
		MaxCapacityZoneRouteSort            int32 `json:"max_capacity_zone_route_sort,omitempty"`
		CheapestFeeEnable                   bool  `json:"cheapest_fee_enable,omitempty"`
		CheapestFeeSort                     int32 `json:"cheapest_fee_sort,omitempty"`
		MaxBatchVolumeEnable                bool  `json:"max_batch_volume_enable,omitempty"`
		MaxBatchVolumeSort                  int32 `json:"max_batch_volume_sort,omitempty"`
		MaxBatchVolume                      int32 `json:"max_batch_volume,omitempty"`
		PickupEfficiencyWhitelistEnable     bool  `json:"pickup_efficiency_whitelist_enable"`
		PickupEfficiencyWhitelistSort       int32 `json:"pickup_efficiency_whitelist_sort"`
		MaxCodCapacityCountryEnable         bool  `json:"max_cod_capacity_country_enable,omitempty"`
		MaxCodCapacityCountrySort           int32 `json:"max_cod_capacity_country_sort,omitempty"`
		MaxCodCapacityZoneRouteEnable       bool  `json:"max_cod_capacity_zone_route_enable,omitempty"`
		MaxCodCapacityZoneRouteSort         int32 `json:"max_cod_capacity_zone_route_sort,omitempty"`
		MaxBulkyCapacityCountryEnable       bool  `json:"max_bulky_capacity_country_enable,omitempty"`
		MaxBulkyCapacityCountrySort         int32 `json:"max_bulky_capacity_country_sort,omitempty"`
		MaxBulkyCapacityZoneRouteEnable     bool  `json:"max_bulky_capacity_zone_route_enable,omitempty"`
		MaxBulkyCapacityZoneRouteSort       int32 `json:"max_bulky_capacity_zone_route_sort,omitempty"`
		MaxHighValueCapacityCountryEnable   bool  `json:"max_high_value_capacity_country_enable,omitempty"`
		MaxHighValueCapacityCountrySort     int32 `json:"max_high_value_capacity_country_sort,omitempty"`
		MaxHighValueCapacityZoneRouteEnable bool  `json:"max_high_value_capacity_zone_route_enable,omitempty"`
		MaxHighValueCapacityZoneRouteSort   int32 `json:"max_high_value_capacity_zone_route_sort,omitempty"`
		MaxDgCapacityCountryEnable          bool  `json:"max_dg_capacity_country_enable,omitempty"`
		MaxDgCapacityCountrySort            int32 `json:"max_dg_capacity_country_sort,omitempty"`
		MaxDgCapacityZoneRouteEnable        bool  `json:"max_dg_capacity_zone_route_enable,omitempty"`
		MaxDgCapacityZoneRouteSort          int32 `json:"max_dg_capacity_zone_route_sort,omitempty"`

		Limit []*UpdateRuleRequestDetailProductLimit `json:"limit,omitempty"`
	}

	UpdateRuleRequestDetailProductLimit struct {
		Id                 int64  `json:"id,omitempty"`
		Name               string `json:"name,omitempty"`
		MinVolume          int32  `json:"min_volume,omitempty"`
		MaxCapacity        int32  `json:"max_capacity,omitempty"`
		MaxVolumeEachBatch int32  `json:"max_volume_each_batch"`
		IsHardCap          bool   `json:"is_hard_cap,omitempty"`
	}
)

type DeleteRuleRequest struct {
	Id int32 `json:"id"`
}

type CopyRuleRequest struct {
	Id int32 `json:"id"`
}

type CreateConfigRequest struct {
	MaskProductId          int32 `json:"mask_product_id"`
	SmartAllocationEnabled bool  `json:"smart_allocation_enabled"`
	DefaultProduct         int32 `json:"default_product"`
	DefaultWmsProduct      int32 `json:"default_wms_product"`
}

type UpdateConfigRequest struct {
	MaskProductId          int32 `json:"mask_product_id"`
	SmartAllocationEnabled bool  `json:"smart_allocation_enabled"`
	DefaultProduct         int32 `json:"default_product"`
	DefaultWmsProduct      int32 `json:"default_wms_product"`
}

func (m *UpdateRuleRequest) Validate() error {
	if m == nil || m.RuleDetail == nil {
		return fmt.Errorf("UpdateRuleRequest is nil")
	}

	err := fmt.Errorf("the priority of an active soft criteria can not be same")

	//校验rule detail的priority是否重复
	sortMap := make(map[int32]bool)
	if m.RuleDetail.MinVolumeCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MinVolumeCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MinVolumeCountrySort] = true
	}
	if m.RuleDetail.MinVolumeZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MinVolumeZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MinVolumeZoneRouteSort] = true
	}
	if m.RuleDetail.MaxCapacityCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MaxCapacityCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxCapacityCountrySort] = true
	}
	if m.RuleDetail.MaxCapacityZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MaxCapacityZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxCapacityZoneRouteSort] = true
	}
	if m.RuleDetail.MaxBatchVolumeEnable {
		if _, ok := sortMap[m.RuleDetail.MaxBatchVolumeSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxBatchVolumeSort] = true
		// 如果启用了Batch Volume,需要校验Volume的数值
		// 1. batch size必须大于0
		if m.RuleDetail.MaxBatchVolume <= 0 {
			return errors.New("If max batch volume is enabled, batch size equal to 0 is not allowed.")
		}

		// 2. F Channel的Volume之和必须大于等于batch size
		if !validateBatchVolumeSum(m.RuleDetail) {
			return errors.New("The total number of volumes configured for each F-channel must be greater than or equal to the batch size.")
		}
	}
	if m.RuleDetail.CheapestFeeEnable {
		if _, ok := sortMap[m.RuleDetail.CheapestFeeSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.CheapestFeeSort] = true
	}
	if m.RuleDetail.PickupEfficiencyWhitelistEnable {
		if _, ok := sortMap[m.RuleDetail.PickupEfficiencyWhitelistSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.PickupEfficiencyWhitelistSort] = true
	}
	if m.RuleDetail.MaxCodCapacityCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MaxCodCapacityCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxCodCapacityCountrySort] = true
	}
	if m.RuleDetail.MaxCodCapacityZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MaxCodCapacityZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxCodCapacityZoneRouteSort] = true
	}
	if m.RuleDetail.MaxBulkyCapacityCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MaxBulkyCapacityCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxBulkyCapacityCountrySort] = true
	}
	if m.RuleDetail.MaxBulkyCapacityZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MaxBulkyCapacityZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxBulkyCapacityZoneRouteSort] = true
	}
	if m.RuleDetail.MaxHighValueCapacityCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MaxHighValueCapacityCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxHighValueCapacityCountrySort] = true
	}
	if m.RuleDetail.MaxHighValueCapacityZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MaxHighValueCapacityZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxHighValueCapacityZoneRouteSort] = true
	}
	if m.RuleDetail.MaxDgCapacityCountryEnable {
		if _, ok := sortMap[m.RuleDetail.MaxDgCapacityCountrySort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxDgCapacityCountrySort] = true
	}
	if m.RuleDetail.MaxDgCapacityZoneRouteEnable {
		if _, ok := sortMap[m.RuleDetail.MaxDgCapacityZoneRouteSort]; ok {
			return err
		}
		sortMap[m.RuleDetail.MaxDgCapacityZoneRouteSort] = true
	}
	return nil
}

func validateBatchVolumeSum(detail *UpdateRuleRequestDetail) bool {
	var sumOfVolume int64
	for _, l := range detail.Limit {
		volume := int64(l.MaxVolumeEachBatch)
		// 如果其中一个Volume是最大值，直接通过
		if volume == maxBatchVolumeValue {
			return true
		}
		sumOfVolume += volume
	}

	// Volume之和需要大于Batch Volume Size
	return sumOfVolume >= int64(detail.MaxBatchVolume)
}

func (m *UpdateRuleRequest) ValidateBatch(ctx context.Context) error {
	// 1 means draft, avoiding import cycle, just using 1 here
	if m.RuleStatus != 1 {
		if vErr := m.validateBatchSize(ctx); vErr != nil {
			logger.CtxLogErrorf(ctx, "ValidateRequest|validate not pass, err:%v", vErr)
			return vErr
		}
	}
	//校验target enable字段，一定要开启，否则算法sdk会panic
	if !m.BatchRuleDetail.BatchAllocationRuleConfig.CountryVolumeEnabled && !m.BatchRuleDetail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled {
		logger.CtxLogErrorf(ctx, "ValidateRequest|must open country volume or zone volume toggle")
		return srerr.New(srerr.ParamErr, nil, "must open country volume or zone volume toggle")
	}

	return nil
}

func (m *UpdateRuleRequest) validateBatchSize(ctx context.Context) error {
	batchSize := m.BatchRuleDetail.BatchSize
	//last end time + 1 = next start time；整个区间 = OneDayUnix - 1
	//校验时间切片的连续性
	//fixed time下的list只有一个元素，校验其起止时间是否为一天
	fixedTimeUnitList := batchSize.FixedTime.FixedTimeUnitList
	if len(fixedTimeUnitList) == 0 {
		return errors.New("splitting rule is empty, please check it")
	}
	if len(fixedTimeUnitList) == 1 {
		fixedTimeUnit := fixedTimeUnitList[0]
		//整个区间 = OneDayUnix - 1，即00：00：00 ~ 23：59：59
		if (fixedTimeUnit.EndTime - fixedTimeUnit.StartTime) != (timeutil.OneDayUnix - timeutil.OneSecond) {
			logger.CtxLogErrorf(ctx, "validate|fixed by time, start or end time is illegal, start time:%v, end time:%v", fixedTimeUnit.StartTime, fixedTimeUnit.EndTime)
			return errors.New("fixed by time, start or end time is illegal")
		}
	} else {
		//fixed time下的list有多个元素，校验元素的起止时间是否连贯
		//排序
		sort.Slice(fixedTimeUnitList, func(i, j int) bool {
			return fixedTimeUnitList[i].StartTime < fixedTimeUnitList[j].StartTime
		})
		//排序后，最后一个元素的end time与第一个元素的start time间隔必需满足一天
		//整个区间 = OneDayUnix - 1，即00：00：00 ~ 23：59：59
		if (fixedTimeUnitList[len(fixedTimeUnitList)-1].EndTime - fixedTimeUnitList[0].StartTime) != (timeutil.OneDayUnix - timeutil.OneSecond) {
			logger.CtxLogErrorf(ctx, "validate|fixed by time, first start or last end time, first unit:%v, last unit:%v", fixedTimeUnitList[0], fixedTimeUnitList[len(fixedTimeUnitList)-1])
			return srerr.New(srerr.ParamErr, nil, "fixed by time, first start or last end time is illegal")
		}
		for i := 0; i < len(fixedTimeUnitList)-1; i++ {
			//排序后，连续的元素不允许有相同的start time或end time
			if fixedTimeUnitList[i].StartTime == fixedTimeUnitList[i+1].StartTime ||
				fixedTimeUnitList[i].EndTime == fixedTimeUnitList[i+1].EndTime {
				logger.CtxLogErrorf(ctx, "validate|fixed by time, same start or end time, first unit:%v, second unit:%v", fixedTimeUnitList[i], fixedTimeUnitList[i+1])
				return srerr.New(srerr.ParamErr, nil, "fixed by time, same start or end time")
			}
			//排序后，前一个元素的end time必需和后一个元素的start time差1秒
			if fixedTimeUnitList[i].EndTime != (fixedTimeUnitList[i+1].StartTime - timeutil.OneSecond) {
				logger.CtxLogErrorf(ctx, "validate|fixed by time, first end time not equal to next start time, first unit:%v, second unit:%v", fixedTimeUnitList[i], fixedTimeUnitList[i+1])
				return srerr.New(srerr.ParamErr, nil, "fixed by time, first end time not equal to next start time")
			}
		}
	}
	return nil
}
