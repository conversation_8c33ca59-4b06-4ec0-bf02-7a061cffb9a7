package allocation

import pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"

type CheckoutFulfillmentProductCounterRequest struct {
	OrderId              uint64      `json:"order_id"`
	ShopId               uint64      `json:"shop_id"`
	UpdateTime           int32       `json:"update_time"`
	ProductId            int64       `json:"product_id"`
	FulfillmentProductId int64       `json:"fulfillment_product_id"`
	DeliveryAddress      AddressInfo `json:"delivery_address"`
}

type AddressInfo struct {
	Country  string `json:"country"`
	State    string `json:"state"`
	City     string `json:"city"`
	District string `json:"district"`
	Town     string `json:"town"`
	Address  string `json:"address"`
	Zipcode  string `json:"zipcode"`
}

type CheckoutFulfillmentProductOrderInfo struct {
	Cogs                  float64             `json:"cogs"`
	PackageAmount         float64             `json:"package_amount"`
	PaymentMethod         string              `json:"payment_method"`
	OrderShopItemInfoList []OrderShopItemInfo `json:"order_shop_item_info_list"`
	BuyerAddress          AddressInfo         `json:"buyer_address"`
	SellerAddress         AddressInfo         `json:"seller_address"`
	OrderType             pb.OrderType        `json:"order_type"`
}

type OrderShopItemInfo struct {
	ShopId   int64
	ItemId   int64
	ModelId  int64
	Quantity int32
}
