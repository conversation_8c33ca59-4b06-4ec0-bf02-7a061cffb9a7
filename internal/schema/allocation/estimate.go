package allocation

type (
	BaseInfo struct {
		// parcel common info
		ChannelId     uint64      `json:"channel_id" validate:"required"`
		PaymentMethod string      `json:"payment_method" validate:"required"`
		DeliveryInfo  DeliverInfo `json:"deliver_info" validate:"required"`
	}

	MChannelEstimateReq struct {
		//BaseInfo     BaseInfo               `json:"base_info" validate:"required,dive,required"`
		EstimateList []MChannelEstimateItem `json:"estimate_list" validate:"required,dive,required"`
	}

	PickupInfo struct {
		PickupState      string `json:"pickup_state"`
		PickupCity       string `json:"pickup_city"`
		PickupDistrict   string `json:"pickup_district"`
		PickupStreet     string `json:"pickup_street"`
		PickupPostalCode string `json:"pickup_postal_code"`
		PickupCountry    string `json:"pickup_country"`
		PickupLongitude  string `json:"pickup_longitude"`
		PickupLatitude   string `json:"pickup_latitude"`
	}

	DeliverInfo struct {
		DeliverState      string `json:"deliver_state"`
		DeliverCity       string `json:"deliver_city"`
		DeliverDistrict   string `json:"deliver_district"`
		DeliverStreet     string `json:"deliver_street"`
		DeliverPostalCode string `json:"deliver_postal_code"`
		DeliverCountry    string `json:"deliver_country"`
		DeliverLongitude  string `json:"deliver_longitude"`
		DeliverLatitude   string `json:"deliver_latitude"`
	}

	Snapshot struct {
		IgnoreSnapshot   bool    `json:"ignore_snapshot"`
		ShippingChannels []int64 `json:"shipping_channels"`
	}

	// MChannelEstimateItem single parcel in masking channel estimation
	MChannelEstimateItem struct {
		UniqueId string `json:"unique_id" validate:"required"`
		//WhsId                string                    `json:"whs_id" validate:"omitempty"`
		//PartialFulfillment   bool                      `json:"partial_fulfillment" validate:"omitempty"`
		//PickupInfo           PickupInfo                `json:"pickup_info" validate:"required"`
		//TotalPrice           float64                   `json:"total_price"`
		//Cogs                 float64                   `json:"cogs"`
		//CodAmount            float64                   `json:"cod_amount"`
		//BuyerPaidShippingFee float64                   `json:"buyer_paid_shipping_fee"`
		//CheckoutItems        []*ordentity.CheckoutItem `json:"checkout_items" validate:"required,dive,required"`
		SnapShot *Snapshot `json:"snapshot"`
	}

	EstimateItemRsp struct {
		UniqueId string           `json:"unique_id"`
		RetCode  int              `json:"retcode"`
		Message  string           `json:"message"`
		Result   AvailableChannel `json:"result"`
	}

	AvailableChannel struct {
		ShippingChannelId   int64   `json:"shipping_channel_id"`
		ShippingChannelName string  `json:"shipping_channel_name"`
		Cdt                 float32 `json:"cdt"`
		AllocateShippingFee float64 `json:"allocate_shipping_fee"` // AllocateShippingFee value after inflation (v*100000)
	}
)
