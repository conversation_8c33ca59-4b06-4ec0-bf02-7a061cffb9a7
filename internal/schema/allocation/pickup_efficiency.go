package allocation

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
)

// 白名单
type (
	UploadWhitelistReq struct {
		Url string `json:"url"`
	}

	ListWhitelistReq struct {
		ShopId          string                    `json:"shop_id"`
		FulfillmentType whitelist.FulfillmentType `json:"fulfillment_type"`
		Page            int64                     `json:"page"`
		Size            int64                     `json:"size"`
	}
	ListWhitelistResp struct {
		Page  int64           `json:"page"`
		Size  int64           `json:"size"`
		Total int64           `json:"total"`
		List  []WhitelistItem `json:"list"`
	}
	WhitelistItem struct {
		ShopId          int64                     `json:"shop_id"`
		FulfillmentType whitelist.FulfillmentType `json:"fulfillment_type"`
		ModifyTime      int64                     `json:"modify_time"`
		LastUpdatedBy   string                    `json:"last_updated_by"`
	}

	ExportWhitelistResp struct {
		Url string `json:"url"`
	}
)

// 优先级
type (
	ListPriorityReq struct {
		MaskingProductId int64 `json:"masking_product_id"`
		Page             int64
		Size             int64
	}

	ListPriorityResp struct {
		List  []Item `json:"list"`
		Page  int64
		Size  int64
		Total int64
	}

	Item struct {
		MaskingProductId   int64      `json:"masking_product_id"`
		MaskingProductName string     `json:"masking_product_name"`
		ModifyTime         int64      `json:"modify_time"`
		LastUpdatedBy      string     `json:"last_updated_by"`
		PriorityList       []Priority `json:"priority_list"`
	}

	CreatePriorityReq struct {
		MaskingProductId int64      `json:"masking_product_id"`
		List             []Priority `json:"list"`
	}

	Priority struct {
		Priority               int64  `json:"priority"`
		FulfillmentProductId   int64  `json:"fulfillment_product_id"`
		FulfillmentProductName string `json:"fulfillment_product_name"`
	}

	DeletePriorityReq struct {
		MaskingProductId int64 `json:"masking_product_id"`
	}

	GetPriorityReq struct {
		MaskingProductId int64 `json:"masking_product_id"`
	}
)
