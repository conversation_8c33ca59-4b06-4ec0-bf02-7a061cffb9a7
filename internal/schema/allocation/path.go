package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

type (
	GetAllocPathDetailRequest struct {
		RequestId string `form:"request_id" json:"request_id" validate:"required"`
	}

	GetAllocPathListRequest struct {
		RequestId    string `form:"request_id" json:"request_id"`
		OrderId      string `form:"order_id" json:"order_id"`
		FOrderId     string `form:"forder_id" json:"forder_id"`
		OrderIdUint  uint64 `form:"-" json:"-"`
		FOrderIdUint uint64 `form:"-" json:"-"`
		PageNo       int    `form:"page_no" json:"page_no" validate:"required"`
		PageSize     int    `form:"page_size" json:"page_size" validate:"required"`
	}
)

func (lr *GetAllocPathListRequest) Validate(ctx context.Context) *srerr.Error {
	if lr.RequestId == "" && lr.FOrderId == "" && lr.OrderId == "" {
		logger.CtxLogErrorf(ctx, "ListRequest| Validate fail, request_id, forder_id and order_id are nil")
		return srerr.New(srerr.ParamErr, nil, "all query params are nil")
	}
	err := checkRequestId(ctx, lr.RequestId)
	if err != nil {
		return err
	}
	if lr.PageNo == 0 || lr.PageSize == 0 {
		logger.CtxLogErrorf(ctx, "ListRequest| Validate fail, page_no or page_size is nil, page_no:%d, page_size:%d", lr.PageNo, lr.PageSize)
		return srerr.New(srerr.ParamErr, nil, "page params are nil")
	}
	lr.OrderIdUint, err = checkIdNumber(ctx, lr.OrderId)
	if err != nil {
		return err
	}
	lr.FOrderIdUint, err = checkIdNumber(ctx, lr.FOrderId)
	if err != nil {
		return err
	}
	return nil
}

func (lr *GetAllocPathDetailRequest) Validate(ctx context.Context) *srerr.Error {
	return checkRequestId(ctx, lr.RequestId)
}

func checkRequestId(ctx context.Context, requestId string) *srerr.Error {
	if requestId != "" && len(requestId) < 20 {
		logger.CtxLogErrorf(ctx, "RequestId| Validate fail, length of request_id is less than 20, request_id:%s", requestId)
		return srerr.New(srerr.ParamErr, nil, "length of request_id is less than 20")
	}
	return nil
}

func checkIdNumber(ctx context.Context, id string) (uint64, *srerr.Error) {
	if id == "" {
		return 0, nil
	}
	idUint, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ListRequest| Validate fail, id is not number, id:%s, error: %s", id, err.Error())
		return 0, srerr.With(srerr.ParamErr, nil, err)
	}
	return idUint, nil
}

type (
	AllocateRequest struct {
		//MaskingChannelId     *int            `json:"channel_id,omitempty"`
		//CheckoutItems        []*CheckoutItem `json:"checkout_items,omitempty"`
		//PickupInfo           *PickupInfo     `json:"pickup_info,omitempty"`
		//DeliveryInfo         *DeliverInfo    `json:"deliver_info,omitempty"`
		//PaymentMethod        *string         `json:"payment_method,omitempty"`
		//CodAmount            *float64        `json:"cod_amount,omitempty"`
		//TotalPrice           *float64        `json:"total_price,omitempty"`
		//Cogs                 *float64        `json:"cogs,omitempty"`
		//PartialFulfillment   *bool           `json:"partial_fulfillment,omitempty"`
		//IsWms                *bool           `json:"is_wms,omitempty"`
		//WhsId                *string         `json:"whs_id,omitempty"`
		//OrderID              *uint64         `json:"order_id,omitempty"`
		//FOrderID             *uint64         `json:"forder_id,omitempty"`
		//FOrderIDStr          *string         `json:"forder_id_str,omitempty"`
		//BuyerPaidShippingFee *float64        `json:"buyer_paid_shipping_fee,omitempty"`
		//SellerTaxNumber      *string         `json:"seller_tax_number,omitempty"`
		//StateRegistration    *string         `json:"state_registration,omitempty"`
		Snapshot *Snapshot `json:"snapshot,omitempty"`
	}

	CheckoutItem struct {
		ShopId                 int         `json:"shop_id" validate:"required"`
		ChannelPriorityGroupId int         `json:"channel_priority_group_id"`
		Items                  []*ItemInfo `json:"items" validate:"required,dive,required"`
	}

	ItemInfo struct {
		ItemId               uint64   `json:"item_id" validate:"required"`
		Quantity             int      `json:"quantity" validate:"required"`
		ModelId              uint64   `json:"model_id"`
		Weight               *float64 `json:"weight"`
		Length               *float64 `json:"length"`
		Width                *float64 `json:"width"`
		Height               *float64 `json:"height"`
		CoverShippingFee     *bool    `json:"cover_shipping_fee"`
		IsDg                 *uint32  `json:"is_dg"`
		CategoryId           *uint64  `json:"category_id"`
		GlobalCategoryId     *uint64  `json:"global_category_id"`
		GlobalCategoryIdL1   *uint64  `json:"global_category_id_L1"`
		GlobalCategoryIdL2   *uint64  `json:"global_category_id_L2"`
		GlobalCategoryIdL3   *uint64  `json:"global_category_id_L3"`
		GlobalCategoryIdL4   *uint64  `json:"global_category_id_L4"`
		GlobalCategoryIdL5   *uint64  `json:"global_category_id_L5"`
		Price                *float64 `json:"price"`
		GlobalCategoryIdList []uint64 `json:"global_category_id_list"`
		IsPreOrder           bool     `json:"is_pre_order"`
		EstimateDays         uint32   `json:"estimate_days"`
		DgSpecificType       uint32   `json:"dg_specific_type"`
	}
)

func (i *ItemInfo) GetWeight() float64 {
	if i == nil || i.Weight == nil {
		return 0
	}
	return *i.Weight
}

func (i *ItemInfo) GetCategoryId() uint64 {
	if i == nil || i.CategoryId == nil {
		return 0
	}
	return *i.CategoryId
}

func (i *ItemInfo) GetGlobalCategoryId() uint64 {
	if i == nil || i.GlobalCategoryId == nil {
		return 0
	}
	return *i.GlobalCategoryId
}

func (i *ItemInfo) GetIsDg() uint32 {
	if i == nil || i.IsDg == nil {
		return 0
	}
	return *i.IsDg
}

func (i *ItemInfo) GetPrice() float64 {
	if i == nil || i.Price == nil {
		return 0
	}
	return *i.Price
}
