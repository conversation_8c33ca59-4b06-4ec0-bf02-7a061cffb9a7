package allocation

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"time"
)

type AllocateForecastTaskConfigRequest struct {
	ProductPriorityConfigs    []ProductPriorityConfig   `json:"product_priority_configs"`
	AllocationRuleConfig      *AllocationRuleConfig     `json:"allocation_rule_config"`
	BaseInfo                  *AllocateForecastBaseInfo `json:"base_info" validate:"required"`
	BatchAllocationRuleConfig BatchAllocationRuleConfig `json:"batch_allocation_rule_config"`
}

type AllocateForecastTaskConfigGetRequest struct {
	Id int64 `json:"id" validate:"required"`
}

type AllocateForecastTaskConfigResp struct {
	ProductPriorityConfigs    []*ProductPriorityConfig  `json:"product_priority_configs"`
	AllocationRuleConfig      *AllocationRuleConfig     `json:"allocation_rule_config"`
	BaseInfo                  *AllocateForecastBaseInfo `json:"base_info" validate:"required"`
	BatchAllocationRuleConfig BatchAllocationRuleConfig `json:"batch_allocation_rule_config"`
}

type AllocateForecastBaseInfo struct {
	Id                             int            `json:"id"`
	TaskName                       string         `json:"task_name" validate:"required"`
	MaskProductId                  int            `json:"mask_product_id" validate:"required"`
	ShopGroupChannelPriorityToggle bool           `json:"shop_group_channel_priority_toggle"`
	LocalSoftCriteriaToggle        bool           `json:"local_soft_criteria_toggle"` //打开，由业务配置；关闭，获取线上规则
	RunSoftRuleOnlyToggle          bool           `json:"run_soft_rule_only_toggle"`
	ReCalcFee                      bool           `json:"re_calc_fee"`
	Status                         int            `json:"status" validate:"required"`
	OrderPaidTime                  *OrderPaidTime `json:"order_paid_time"`
	ConfigSyncStatus               int            `json:"config_sync_status"`
	OperatedBy                     string         `json:"operated_by"`
	EffectiveStartTime             uint32         `json:"effective_start_time"`
	TaskSchedule                   int64          `json:"task_schedule"`
	AllocationMethod               int            `json:"allocation_method"` //1:single allocate; 2:batch allocate
	BatchSizeList                  []BatchSize    `json:"batch_size_list"`   //分单规则列表
}

type OrderPaidTime struct {
	TimeSections []*TimeSection `json:"time_sections"`
}

type TimeSection struct {
	BeginTime uint32 `json:"begin_time"`
	EndTime   uint32 `json:"end_time"`
}

type BAForecastCreateUpdateReq struct {
	BaseInfo                  BatchAllocateBaseInfo     `json:"base_info"`
	ProductPriorityConfigs    []*ProductPriorityConfig  `json:"product_priority_configs"`
	BatchAllocationRuleConfig BatchAllocationRuleConfig `json:"batch_allocation_rule_config"`
	BatchSizeList             []BatchSize               `json:"batch_size_list"`
}

type BatchAllocateBaseInfo struct {
	Id                               uint64        `json:"id"`
	TaskName                         string        `json:"task_name"`
	RuleMode                         int           `json:"rule_mode"`         //1:mpl; 2:wms
	AllocationMethod                 int           `json:"allocation_method"` //1:single; 2:batch allocate
	MaskProductId                    int           `json:"mask_product_id"`
	RunSoftRuleOnlyToggle            bool          `json:"run_soft_rule_only_toggle"`
	LocalSoftCriteriaToggle          bool          `json:"local_soft_criteria_toggle"`
	ShopGroupChannelPriorityToggle   bool          `json:"shop_group_channel_priority_toggle"`
	RecalculateAllocationShippingFee bool          `json:"recalculate_allocation_shipping_fee"`
	Status                           int           `json:"status"` //1.draft; 8.pending
	OrderPaidTime                    OrderPaidTime `json:"order_paid_time"`
	ReCalcFee                        bool          `json:"re_calc_fee"`
}

type BatchOrderPaidTime struct {
	OrderStartTime int64 `json:"order_start_time"`
	OrderEndTime   int64 `json:"order_end_time"`
}

type BatchAllocationRuleConfig struct {
	UseForCampaign                   bool                   `json:"use_for_campaign"` //true:大促模板；FALSE：日常模板；simulation tool默认关闭
	ZoneRouteVolumeEnabled           bool                   `json:"zone_route_volume_enabled"`
	CountryVolumeEnabled             bool                   `json:"country_volume_enabled"`
	CheapestShippingFeeEnabled       bool                   `json:"cheapest_shipping_fee_enabled"`
	PickupEfficiencyEnabled          bool                   `json:"pickup_efficiency_enabled"`
	PickupEfficiencyDetail           PickupEfficiencyDetail `json:"pickup_efficiency_detail"`
	PickupEfficiencyWhitelistEnabled bool                   `json:"pickup_efficiency_whitelist_enabled"` // 默认是最高优先级
	//下面为forecast部分使用，online部分还是从mask volume rule里获取
	ZoneRouteVolumeDetail   ZoneRouteVolumeDetail    `json:"zone_route_volume_detail"`
	CountryVolumeDetailList []CountryVolumeDetail    `json:"country_volume_detail_list"`
	ShippingFeeConfigList   []BatchShippingFeeConfig `json:"shipping_fee_config_list"`
}

type PickupEfficiencyDetail struct {
	Budget float64 `json:"budget"`
}

type ShippingFeeConfig struct {
	ProductId   int     `json:"product_id"`
	RateId      int     `json:"rate_id"`
	WmsFlag     int     `json:"wms_flag"`
	StartTime   uint32  `json:"start_time"` //SSCSMR-1698: batch allocate中用不到该字段
	ShippingFee float64 `json:"shipping_fee"`
}

type BatchShippingFeeConfig struct {
	ChannelId int `json:"channel_id"`
	RateId    int `json:"rate_id"`
	WmsFlag   int `json:"wms_flag"`
}

type ZoneRouteCapacityDetail struct {
	FillBlankType         int    `json:"fill_blank_type"`          //1:将Excel空白单元视为无穷大; 2:视为0
	LocalVolumeRuleType   int    `json:"local_volume_rule_type"`   //1：route; 2:zone; 3:route&zone
	ZoneDefinitionUrl     string `json:"zone_definition_url"`      //关于zone定义的s3连接
	ZoneCapacityValueUrl  string `json:"zone_capacity_value_url"`  //zone对应的容量值 -- s3连接
	RouteDefinitionUrl    string `json:"route_definition_url"`     //关于route定义的s3连接
	RouteCapacityValueUrl string `json:"route_capacity_value_url"` //route对应的容量值 -- s3连接
}

type ZoneRouteVolumeDetail struct {
	//todo:SSCSMR-1698:不再需要fill blank type, 而是用固定的逻辑，min空白=0， max空白=9个9
	//FillBlankType       int    `json:"fill_blank_type"`        //1:将Excel空白单元视为无穷大; 2:视为0
	LocalVolumeRuleType int    `json:"local_volume_rule_type"` //1：route; 2:zone; 3:route&zone
	ZoneDefinitionUrl   string `json:"zone_definition_url"`    //关于zone定义的s3连接
	ZoneVolumeValueUrl  string `json:"zone_volume_value_url"`  //zone对应的容量值 -- s3连接
	RouteDefinitionUrl  string `json:"route_definition_url"`   //关于route定义的s3连接
	RouteVolumeValueUrl string `json:"route_volume_value_url"` //route对应的容量值 -- s3连接
}

type BatchSize struct {
	BatchSizeType int       `json:"batch_size_type"` //分单规则类型。1:fixed by quantity, 2: fixed by time; 3:fixed by quantity&time
	BatchSizeName string    `json:"batch_size_name"`
	FixedQuantity int       `json:"fixed_quantity"` //按大小分片，分片的大小
	FixedTime     FixedTime `json:"fixed_time"`     //按时间分片，分片的设置
}

type FixedTime struct {
	FixedTimeUnitList []FixedTimeUnit `json:"fixed_time_unit_list"`
}

/*
@param

	StartTime：2023-01-01 00：00 Unix time
	EndTime: 2023-01-01 24：00 unix time
	TimeRange: 3600

将2023-01-01按3600秒拆分
*/
type FixedTimeUnit struct {
	StartTime int64 `json:"start_time"` //时间区间的左区间，记录的是开始的秒数
	EndTime   int64 `json:"end_time"`   //时间区间的右区间，记录的是结束的秒数
	TimeRange int64 `json:"time_range"` //时间分片单元内的切片范围, 单位：秒
}

type CountryVolumeDetail struct {
	ProductId               int64  `json:"product_id"`
	ProductName             string `json:"product_name"`
	MaxDailyVolume          int64  `json:"max_daily_volume"`     //region维度最大运力上限
	MinDailyVolume          int64  `json:"min_daily_volume"`     //region维度最小运力需求
	MaxDailyCodVolume       int64  `json:"max_daily_cod_volume"` //region维度最大COD运力上限
	MaxDailyHighValueVolume int64  `json:"max_daily_high_value_volume"`
	MaxDailyBulkyVolume     int64  `json:"max_daily_bulky_volume"`
	MaxDailyDgVolume        int64  `json:"max_daily_dg_volume"`
}

type ProductPriorityConfig struct {
	MaskProductId   int               `json:"mask_product_id"`
	ShopGroupId     int64             `json:"shop_group_id"`
	RuleType        uint32            `json:"rule_type"`
	PriorityDetails []*PriorityDetail `json:"priority_details"` //batch只存该部分
}

type PriorityDetail struct {
	ProductId int    `json:"id"`
	Priority  int    `json:"priority"`
	Weightage int    `json:"weightage"`
	Status    int    `json:"status"`
	Name      string `json:"name"`
}

type MaskProductRuleVolumeConfig struct {
	MaskProductId           int                   `json:"mask_product_id"`
	DefaultVolumeLimit      []*DefaultVolumeLimit `json:"default_volume_limit"`
	RouteDefinitionFile     string                `json:"route_definition_file"`
	RouteLimitFile          string                `json:"route_limit_file"`
	ZoneDefinitionFile      string                `json:"zone_definition_file"`
	ZoneLimitFile           string                `json:"zone_limit_file"`
	RuleType                uint32                `json:"rule_type"`
	RuleStatus              uint32                `json:"rule_status"`
	SetVolumeBlankAsMinimum bool                  `json:"set_volume_blank_as_minimum"`
}

type DefaultVolumeLimit struct {
	ProductId   int    `json:"id"`
	Name        string `json:"name"`
	MinVolume   int    `json:"min_volume"`
	MaxCapacity int    `json:"max_capacity"`
	IsHardCap   bool   `json:"is_hard_cap"`
}

type AllocationRuleConfig struct {
	MaskProductId               int                          `json:"mask_product_id"`
	RuleName                    string                       `json:"rule_name"`
	RuleStatus                  uint32                       `json:"rule_status"`
	RuleDetail                  *RuleDetail                  `json:"rule_detail"`
	MaskProductRuleVolumeConfig *MaskProductRuleVolumeConfig `json:"mask_product_rule_volume_config"`
	ShippingFeeConfig           []*ShippingFeeConfig         `json:"shipping_fee_config"`
}

type RuleDetail struct {
	MinVolumeEnable                     bool     `json:"min_volume_enable"`
	MaxCapacityEnable                   bool     `json:"max_capacity_enable"`
	MinVolumeSort                       int      `json:"min_volume_sort"`
	MaxCapacitySort                     int      `json:"max_capacity_sort"`
	MinVolumeCountryEnable              bool     `json:"min_volume_country_enable"`
	MinVolumeCountrySort                int      `json:"min_volume_country_sort,omitempty"`
	MinVolumeZoneRouteEnable            bool     `json:"min_volume_zone_route_enable"`
	MinVolumeZoneRouteSort              int      `json:"min_volume_zone_route_sort,omitempty"`
	MaxCapacityCountryEnable            bool     `json:"max_capacity_country_enable"`
	MaxCapacityCountrySort              int      `json:"max_capacity_country_sort,omitempty"`
	MaxCapacityZoneRouteEnable          bool     `json:"max_capacity_zone_route_enable"`
	MaxCapacityZoneRouteSort            int      `json:"max_capacity_zone_route_sort,omitempty"`
	MaxCodCapacityCountryEnable         bool     `json:"max_cod_capacity_country_enable"`
	MaxCodCapacityCountrySort           int      `json:"max_cod_capacity_country_sort,omitempty"`
	MaxCodCapacityZoneRouteEnable       bool     `json:"max_cod_capacity_zone_route_enable"`
	MaxCodCapacityZoneRouteSort         int      `json:"max_cod_capacity_zone_route_sort,omitempty"`
	MaxBulkyCapacityCountryEnable       bool     `json:"max_bulky_capacity_country_enable"`
	MaxBulkyCapacityCountrySort         int      `json:"max_bulky_capacity_country_sort,omitempty"`
	MaxBulkyCapacityZoneRouteEnable     bool     `json:"max_bulky_capacity_zone_route_enable"`
	MaxBulkyCapacityZoneRouteSort       int      `json:"max_bulky_capacity_zone_route_sort,omitempty"`
	MaxHighValueCapacityCountryEnable   bool     `json:"max_high_value_capacity_country_enable"`
	MaxHighValueCapacityCountrySort     int      `json:"max_high_value_capacity_country_sort,omitempty"`
	MaxHighValueCapacityZoneRouteEnable bool     `json:"max_high_value_capacity_zone_route_enable"`
	MaxHighValueCapacityZoneRouteSort   int      `json:"max_high_value_capacity_zone_route_sort,omitempty"`
	MaxDgCapacityCountryEnable          bool     `json:"max_dg_capacity_country_enable"`
	MaxDgCapacityCountrySort            int      `json:"max_dg_capacity_country_sort,omitempty"`
	MaxDgCapacityZoneRouteEnable        bool     `json:"max_dg_capacity_zone_route_enable"`
	MaxDgCapacityZoneRouteSort          int      `json:"max_dg_capacity_zone_route_sort,omitempty"`
	CheapestFeeEnable                   bool     `json:"cheapest_fee_enable"`
	MaxBatchVolumeEnable                bool     `json:"max_batch_volume_enable"`
	MaxBatchVolumeSort                  int      `json:"max_batch_volume_sort"`
	CheapestFeeSort                     int      `json:"cheapest_fee_sort"`
	MaxBatchVolume                      int      `json:"max_batch_volume"`
	Limit                               []*Limit `json:"limit"`
}

type Limit struct {
	ProductId          int    `json:"id"`
	Name               string `json:"name"`
	MaxVolumeEachBatch int    `json:"max_volume_each_batch"`
}

type ForecastListRequest struct {
	MaskProductId    int   `json:"mask_product_id"`
	Status           int   `json:"status"`
	AllocationMethod int   `json:"allocation_method"`
	Pageno           int64 `json:"pageno" default:"1"`
	Limit            int64 `json:"limit" default:"20"`
}

type ForecastListResp struct {
	Pageno int64                    `json:"pageno" default:"0"`
	Size   int64                    `json:"size" default:"20"`
	Total  int64                    `json:"total"`
	List   []*ForecastDisplayEntity `json:"list"`
}

type ForecastDisplayEntity struct {
	Id                 int64            `json:"id"`
	TaskName           string           `json:"task_name" validate:"required"`
	MaskProductId      int              `json:"mask_product_id" validate:"required"`
	MaskProductName    string           `json:"mask_product_name" validate:"required"`
	Status             int              `json:"status" validate:"required"`
	CompleteTime       uint32           `json:"complete_time"`
	LatestDeployTime   uint32           `json:"latest_deploy_time"`
	DisplayForceDeploy *bool            `json:"display_force_deploy"`
	Operator           string           `json:"operator"`
	Mtime              uint32           `json:"mtime"`
	DeployedDetails    *DeployedDetails `json:"deployed_details"`
	DeployFailedDesc   string           `json:"deploy_failed_desc"`
	TaskSchedule       int64            `json:"task_schedule"`
	AllocationMethod   int              `json:"allocation_method"`
}

type DeployedDetails struct {
	ShopGroupIds       []int `json:"shop_group_ids"`
	SoftCriteriaRuleId int   `json:"soft_criteria_rule_id"`
	LocalVolumeRuleId  int   `json:"local_volume_rule_id"`
}

func (b *OrderPaidTime) OrderPaidTimeToStrList() ([]string, *srerr.Error) {
	dateList := make([]string, 0)
	if b.TimeSections == nil {
		return dateList, nil
	}
	for _, timeSection := range b.TimeSections {
		beginTime := timeutil.ConvertTimeStampToTime(int64(timeSection.BeginTime))
		endTime := timeutil.ConvertTimeStampToTime(int64(timeSection.EndTime))
		if beginTime.After(endTime) {
			return nil, srerr.New(srerr.TypeConvertErr, b.TimeSections, "order_paid_time config err. begin time must less than end time")
		}
		for beginTime.Before(endTime) {
			dateList = append(dateList, beginTime.Format(constant.TimeLayout))
			d, _ := time.ParseDuration("24h")
			beginTime = beginTime.Add(d)
		}
	}
	return dateList, nil
}

func (b *OrderPaidTime) OrderPaidTimeToTimeList() ([]time.Time, *srerr.Error) {
	dateList := make([]time.Time, 0)
	if b.TimeSections == nil {
		return dateList, nil
	}
	for _, timeSection := range b.TimeSections {
		beginTime := timeutil.ConvertTimeStampToTime(int64(timeSection.BeginTime))
		endTime := timeutil.ConvertTimeStampToTime(int64(timeSection.EndTime))
		if beginTime.After(endTime) {
			return nil, srerr.New(srerr.TypeConvertErr, b.TimeSections, "order_paid_time config err. begin time must less than end time")
		}
		for beginTime.Before(endTime) {
			dateList = append(dateList, beginTime)
			d, _ := time.ParseDuration("24h")
			beginTime = beginTime.Add(d)
		}
	}
	return dateList, nil
}

type DelDraftTaskConfigReq struct {
	TaskId int64 `json:"task_id" validate:"required"`
}

type CopyTaskConfigReq struct {
	TaskId int64 `json:"task_id" validate:"required"`
}

type SyncTaskConfigReq struct {
	TaskId               int64  `json:"task_id" validate:"required"`
	EffectiveStartTime   uint32 `json:"effective_start_time"`
	EffectiveImmediately *bool  `json:"effective_immediately"`
}

type SoftRuleSyncReq struct {
	MaskingProductId int64 `json:"masking_product_id" validate:"required"`
}

type SoftRuleSyncResp struct {
	AllocationRuleConfig *AllocationRuleConfig `json:"allocation_rule_config"`
}

type ProductPrioritySyncReq struct {
	MaskingProductId int64 `json:"masking_product_id" validate:"required"`
}

type ProductPrioritySyncResp struct {
	ProductPriorities []*ProductPriorityConfig `json:"product_priorities"`
}

/*----------------------------------------------------------------------------------------------------*/

type AllocateForecastTaskConfigEntity struct {
	ProductPriorityConfigs []*ProductPriorityConfig  `json:"product_priority_configs"`
	AllocationRuleConfig   *AllocationRuleConfig     `json:"allocation_rule_config"`
	BaseInfo               *AllocateForecastBaseInfo `json:"base_info" validate:"required"`
}

type ShippingFeeReq struct {
	MaskingProductId int `json:"masking_product_id" validate:"required"`
}

type DateRankCodeListReq struct {
	RankType         *int `json:"rank_type" validate:"required"`
	MaskingProductId *int `json:"masking_product_id" validate:"required"`
}

type ResultRankCodeListReq struct {
	TaskId           *int `json:"task_id" validate:"required"`
	RankType         *int `json:"rank_type" validate:"required"`
	MaskingProductId *int `json:"masking_product_id" validate:"required"`
	Type             *int `json:"type" validate:"required"`
}

type RankCodeListResp struct {
	RankCodeList []*RankCode `json:"list"`
}

type RankCode struct {
	RankType    int    `json:"rank_type"`
	RankCode    string `json:"rank_code"`
	DisplayName string `json:"display_name"`
}

type DateRankListReq struct {
	MaskingProductId *int           `json:"masking_product_id" validate:"required"`
	RankType         *int           `json:"rank_type" validate:"required"`
	RankCode         *string        `json:"rank_code" validate:"required"`
	OrderPaidTime    *OrderPaidTime `json:"order_paid_time" validate:"required"`
	SizeLimit        int            `json:"size_limit"`
}

type RankListResp struct {
	List []*RankList `json:"list"`
}

type RankList struct {
	RankType  int         `json:"rank_type"`
	RankCode  string      `json:"rank_code"`
	TableName string      `json:"table_name"`
	DataList  []*RankItem `json:"data_list"`
}

type RankItem struct {
	MaskingProductId  int     `json:"masking_product_id"`
	MaskProductName   string  `json:"mask_product_name"`
	ProductId         int     `json:"product_id"`
	ProductName       string  `json:"product_name"`
	RankCode          string  `json:"rank_code"`
	RankType          int     `json:"rank_type"`
	OrderQuantity     int     `json:"order_quantity"`
	AverageDailyOrder int     `json:"average_daily_order"`
	Percentage        float64 `json:"percentage"`
	DisplayName       string  `json:"display_name"`
}

type ResultRankListReq struct {
	Type             *int    `json:"type" validate:"required"`
	MaskingProductId *int    `json:"masking_product_id" validate:"required"`
	RankType         *int    `json:"rank_type" validate:"required"`
	RankCode         *string `json:"rank_code" validate:"required"`
	TaskId           *int    `json:"task_id" validate:"required"`
	SizeLimit        int64   `json:"size_limit"`
}

type ShopGroup struct {
	GroupId   int64  `json:"id"`
	GroupName string `json:"name"`
}

type BAHistoricalCodeReq struct {
	MaskProductId uint64        `json:"mask_product_id"`
	OrderPaidTime OrderPaidTime `json:"order_paid_time"`
	ZoneRouteType int           `json:"zone_route_type"` //1:route; 2:zone; 3:zone&route
}

type BAHistoricalInfoReq struct {
	MaskProductId uint64        `json:"mask_product_id"`
	ZoneRouteType int           `json:"zone_route_type"` //一级条件 -- 1:route; 2:zone; 3:zone&route
	ZoneRouteCode string        `json:"zone_route_code"` //二级条件 -- 枚举1:all; 枚举2:具体的zone or route code
	OrderPaidTime OrderPaidTime `json:"order_paid_time"`
}

type BAHistoricalInfoResp struct {
	List []HistoricalInfo `json:"list"`
}

type HistoricalInfo struct {
	ResultType int      `json:"result_type"` //1:all; 2:zone; 3:route
	ResultName string   `json:"result_name"`
	ResultList []Result `json:"result_list"`
	ZRCode     string   //用来导出, zone code or route code
	ZRCodeType string   //用来导出, zone or route
}

type Result struct {
	FulfillmentChannel uint64  `json:"fulfillment_channel"` //todo:SSCSMR-1698:是否改成传string，传string再带上product name
	OrderQuantity      int64   `json:"order_quantity"`
	Ado                int64   `json:"ado"`
	OrderCountRatio    string  `json:"order_count_ratio"`
	TotalShippingFee   float64 `json:"total_shipping_fee"`
}

type BAForecastCodeReq struct {
	BatchAllocateForecastId uint64 `json:"batch_allocate_forecast_id"`
}

type BAForecastCodeResp struct {
	List []string `json:"list"`
}

type BAForecastInfoReq struct {
	BatchAllocateForecastId uint64 `json:"batch_allocate_forecast_id"`
	ZoneCode                string `json:"zone_code"`
}

type BAForecastInfoResp struct {
	OverallResultList          []OverallResult          `json:"overall_result_list"`
	ZoneResultList             []ZoneRouteResult        `json:"zone_result_list"`
	RouteResultList            []ZoneRouteResult        `json:"route_result_list"`
	PickupEfficiencyResultList []PickupEfficiencyResult `json:"pickup_efficiency_result_list"`
}

type (
	OverallResult struct {
		BatchSizeName        string         `json:"batch_size_name"`
		BatchQuantity        int            `json:"batch_quantity"`
		AvgOrdersEachBatch   int            `json:"avg_orders_each_batch"`
		AvgMaxHoldingTime    int            `json:"avg_max_holding_time"` //min维度
		MaxHoldingTime       int            `json:"max_holding_time"`     //min维度
		TotalShippingFee     string         `json:"total_shipping_fee"`   //定义成string，方便fe直接渲染，避免精度丢失
		TotalCostSaving      string         `json:"total_cost_saving"`    //定义成string，方便fe直接渲染，避免精度丢失
		PickupEfficiencyCost string         `json:"pickup_efficiency_cost"`
		ResultDetailList     []ResultDetail `json:"result_detail_list"`
	}
	ResultDetail struct {
		FulfillmentProduct uint64 `json:"fulfillment_product"`
		OrderQuantity      int64  `json:"order_quantity"`
		Ado                int64  `json:"ado"`
		Percentage         string `json:"percentage"`   //定义成string，方便fe直接渲染，避免精度丢失 -- 订单占比
		ShippingFee        string `json:"shipping_fee"` //定义成string，方便fe直接渲染，避免精度丢失 -- SDK交换后实际消耗的运费
		CostSaving         string `json:"cost_saving"`  //定义成string，方便fe直接渲染，避免精度丢失 -- 节省运费
	}
)

type (
	ZoneRouteResult struct {
		BatchSizeName       string            `json:"batch_size_name"`
		BatchSizeResultList []BatchSizeResult `json:"batch_size_result_list"`
	}
	BatchSizeResult struct {
		ZoneCode           string `json:"zone_code"`
		RouteCode          string `json:"route_code"`
		BatchQuantity      int64  `json:"batch_quantity"`
		FulfillmentProduct uint64 `json:"fulfillment_product"`
		ADO                int64  `json:"ado"` //单量除以天数
		Percentage         string `json:"percentage"`
	}
)
type (
	PickupEfficiencyResult struct {
		BatchSizeName string                         `json:"batch_size_name"`
		ResultList    []PickupEfficiencyResultDetail `json:"result_list"`
	}

	PickupEfficiencyResultDetail struct {
		NumberOf3pls  int `json:"number_of_3pls"`
		NumberOfShops int `json:"number_of_shops"`
	}
)

type (
	BAExportHistoryReq struct {
		MaskProductId  uint64        `json:"mask_product_id"`
		OrderPaidTime  OrderPaidTime `json:"order_paid_time"`
		ExportCodeType int           `json:"export_code_type"` //1:zone; 2.route; 3.zone & route
		ExportCode     string        `json:"export_code"`      //all:导出region维度；其余zone code：导出对应zone or route维度
	}
	BAExportHistoryResp struct {
		Url string `json:"url"`
	}
)

type (
	BAExportForecastResultReq struct {
		BatchAllocateForecastId uint64 `json:"batch_allocate_forecast_id"`
		LevelType               int    `json:"level_type"` //1.export over all level; 2.export zone / route level
	}
	BAExportForecastResultResp struct {
		Url string `json:"url"`
	}
)
