package traceutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/fulfilment_event_trace/trace_sdk"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"github.com/panjf2000/ants/v2"
)

func InitExceptionReporter() error {
	poolSize := configutil.GetReportPoolConf()
	// 非核心链路，使用非阻塞式，抛弃的任务通过日志进行上报
	pool, err := ants.NewPool(poolSize, ants.WithNonblocking(true))
	if err != nil {
		return err
	}
	ReportPool = pool
	return nil
}
func StartReport(ctx context.Context, requestId string, eventId uint64) (context.Context, EndReport) {
	nexCtx, endFunc := trace_sdk.ReportServerNodeEventStart(ctx, trace_sdk.StandardEventHeader{
		Region:    envvar.GetCIDLower(),
		RequestId: requestId,
		EventId:   eventId})
	return nexCtx, func(ctx context.Context, cartonNo string, err *srerr.Error) {
		eventBody := map[string]interface{}{}
		body := generateEventReportBody(eventBody, err)
		sysKey := generateEventReportKey(cartonNo)
		endFunc(ctx, body, sysKey)
	}
}

func generateEventReportBody(eventBody map[string]interface{}, err *srerr.Error) trace_sdk.StandardEventBody {
	retcode := 0
	message := ""
	if err != nil {
		retcode = err.GetCode()
		message = err.GetMessage()
	}
	eventBody["err"] = buildReportErr(retcode, message)
	eventResult := trace_sdk.EventResultSuccess
	if err != nil {
		eventResult = trace_sdk.EventResultFail
	}
	return trace_sdk.StandardEventBody{EventBody: eventBody, EventResult: eventResult, EventMessage: message}
}

func generateEventReportKey(cartonNo string) trace_sdk.SysKeys {
	return trace_sdk.SysKeys{CartonNo: cartonNo}
}
