package traceutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/panjf2000/ants/v2"
)

const smrSystem = "SMR"

var ReportPool *ants.Pool

type EndReport func(ctx context.Context, cartonNo string, err *srerr.Error)

type ReportErr struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
	System  string `json:"sys"`
}

func buildReportErr(retcode int, message string) ReportErr {
	return ReportErr{
		Retcode: retcode,
		Message: message,
		System:  smrSystem,
	}
}
