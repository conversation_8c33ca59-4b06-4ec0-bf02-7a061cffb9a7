package traceutil

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfilment_event_trace/trace_sdk"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
)

func InitOrderTraceSdk() error {
	traceSdkConf := configutil.GetTraceSdkConf()
	cid := envvar.GetCID()
	router, ok := traceSdkConf.FulfilmentEventTrace.EnvCidRouter[cid]
	if !ok {
		return fmt.Errorf("wbc_public.fulfilment_event_trace.env_cid_router.%s is nil", cid)
	}

	aclKafkaConfig := traceSdkConf.FulfilmentEventTrace.AclKafkaConfig
	if envvar.IsIDCUs3() || envvar.IsIDCUs2() {
		// 对于US3/US2，需要读另外的的Kafka配置
		aclKafkaConfig = traceSdkConf.FulfilmentEventTraceUs3.AclKafkaConfig
	}

	kafkaConfig := &trace_sdk.KafkaConfig{
		Brokers:      aclKafkaConfig.Brokers,
		User:         aclKafkaConfig.User,
		Password:     aclKafkaConfig.Password,
		Topic:        aclKafkaConfig.Topic,
		PartitionNum: aclKafkaConfig.PartitionNum,
	}
	kafkaConfig.Topic = strings.Replace(kafkaConfig.Topic, "{cid}", strings.ToLower(router.TargetCID), 1)

	excludeRegions := strings.Split(strings.ToLower(traceSdkConf.FulfilmentEventTrace.SmrFilterConfig.ExcludeRegions), ";")
	filterConfig := &trace_sdk.FilterConfig{
		EnableTrace:   router.SmrEnableTrace,
		ExcludeRegion: excludeRegions,
	}
	trace_sdk.Init(trace_sdk.WithFilterEvent(filterConfig), trace_sdk.WithKafkaRecorder(kafkaConfig))

	// 注册自己服务/市场降级开关
	configutil.GetWbcPublicConf().RegisterHook("wbc_public.fulfilment_event_trace.env_cid_router."+cid+".smr_enable_trace", UpdateTraceSwitch)
	configutil.GetWbcPublicConf().RegisterHook("wbc_public.fulfilment_event_trace.smr_filter_config.exclude_regions", UpdateExcludeRegions)

	return nil
}

func UpdateTraceSwitch() error {
	traceSdkConf := configutil.GetTraceSdkConf()
	cid := envvar.GetCID()
	router, ok := traceSdkConf.FulfilmentEventTrace.EnvCidRouter[cid]
	if !ok {
		logger.LogInfof("wbc_public.fulfilment_event_trace.env_cid_router.%s is nil", cid)
		return fmt.Errorf("wbc_public.fulfilment_event_trace.env_cid_router.%s is nil", cid)
	}
	logger.LogInfof("wbc_public %s value:%v", "UpdateTraceSwitch", traceSdkConf.FulfilmentEventTrace)
	if router.SmrEnableTrace != trace_sdk.ENABLE_TRACE && router.SmrEnableTrace != trace_sdk.DISABLE_TRACE {
		logger.LogInfof("wbc_public.fulfilment_event_trace.env_cid_router.%s.smr_enable_trace is invalid", cid)
		return fmt.Errorf("wbc_public.fulfilment_event_trace.env_cid_router.%s.smr_enable_trace is invalid", cid)
	}
	return trace_sdk.UpdateTraceSwitch(router.SmrEnableTrace)
}

func UpdateExcludeRegions() error {
	traceSdkConf := configutil.GetTraceSdkConf()
	logger.LogInfof("wbc_public %s value:%v", "UpdateExcludeRegions", traceSdkConf.FulfilmentEventTrace)
	excludeRegions := strings.Split(strings.ToLower(traceSdkConf.FulfilmentEventTrace.SmrFilterConfig.ExcludeRegions), ";")
	return trace_sdk.UpdateExcludeRegions(excludeRegions)
}
