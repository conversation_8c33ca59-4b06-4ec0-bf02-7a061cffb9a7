package clickhouseutil

const (
	QueryCountByDaySql = `
SELECT 
      request_date as order_date
      ,COUNT(DISTINCT forderid) as order_count
FROM %s.%s
WHERE product_id = %d
AND routing_type = %d
AND grass_region = '%s'
AND request_date BETWEEN '%s' AND '%s'
GROUP BY request_date
`
	AggregationSql = `
	select 
      request_date
      ,lane_code
      ,buyer_state_id
      ,buyer_city_id
      ,in_ap
      ,out_ap
      ,weight_range
      ,sum(1) as log_qty_1d
      ,cast(avg(shipping_fee) as decimal(28,2)) as allocation_shipping_fee_per_order
 from
(
select request_date
       ,lane_code
       ,buyer_state_id
       ,buyer_city_id
       ,in_ap
       ,out_ap
       ,shipping_fee
       ,%s as weight_range 
from %s.%s final
prewhere product_id = %d
and routing_type = %d
and grass_region = '%s'
and request_date between '%s' and '%s'
and (%s)
)
group by request_date,lane_code,buyer_state_id,buyer_city_id,in_ap,out_ap,weight_range
limit %d,%d
`
	AggregationOrderCountSql = `
select count(*) as totalCount from
	(select 
      request_date
      ,lane_code
      ,buyer_state_id
      ,buyer_city_id
      ,in_ap
      ,out_ap
      ,weight_range
      ,sum(1) as log_qty_1d
      ,cast(avg(shipping_fee) as decimal(28,2)) as allocation_shipping_fee_per_order
 from
(
select request_date
       ,lane_code
       ,buyer_state_id
       ,buyer_city_id
       ,in_ap
       ,out_ap
       ,shipping_fee
       ,%s as weight_range 
from %s.%s final
prewhere product_id = %d
and routing_type = %d
and grass_region = '%s'
and request_date between '%s' and '%s'
and (%s)
)
group by request_date,lane_code,buyer_state_id,buyer_city_id,in_ap,out_ap,weight_range)
`
	QueryDetailSql = `
select 
       zstd_encode_message
from %s.%s final
prewhere product_id = %d
and routing_type = %d
and grass_region = '%s'
and request_date between '%s' and '%s'
and sipHash64(forderid)%%%d=%d
`
)

const (
	ClickhouseModule = "clickhouse"
	ClickhouseRead   = "read"
)
