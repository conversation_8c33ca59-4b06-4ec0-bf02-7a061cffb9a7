package redisutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"testing"
)

func TestGetInt64(t *testing.T) {
	if err := chassis.Init(chassis.WithChassisConfigPrefix("admin_server")); err != nil {
		panic(err)
	}
	if err := configutil.Init(); err != nil {
		panic(err)
	}
	if err := InitDefaultClient(); err != nil {
		panic(err)
	}
	ctx := context.Background()
	key := "48002*LPH51*2023-11-08"
	//key1 := "48002*LPH26*PH401*A3*2023-11-08"
	if err := Set(ctx, key, 0, 0); err != nil {
		panic(err)
	}
	//res, err := GetInt(ctx, "ddd")
	getInt64, err := GetInt64(ctx, key)
	if err != nil {
		panic(err)
	}
	//err := Set(ctx, "HardCriteriaStatusKey:3533", 5, 86400*time.Second*30)
	//if err != nil {
	//	panic(err)
	//}
	//val := Get(ctx, "HardCriteriaStatusKey:3568")
	//println(val)
	//timeutil.TransferTimeStampToTime(**********)
	//all-day_3576_91003_2023-07-17_2023-08-15
	//val := GetOldVolumeInstance().Get(ctx, "current-day_3576_91003_2023-07-31_2023-07-31").Val()

	println(getInt64)

}
