package redisutil

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

var (
	notInitErr = errors.New("redis client not init")
	rdsClient  *redis.Client
)

func InitDefaultClient() error {
	clusterName := "default"
	if envvar.IsIDCUs() {
		clusterName = "latam_default"
	}

	rdsClient = redis.NewStandardClient(redis.NewStandardOption().WithClusterName(clusterName))
	if _, err := rdsClient.Ping(context.TODO()).Result(); err != nil {
		return errors.Errorf("ping redis fail,client=%+v, err=%s", rdsClient, err)
	}

	return nil
}

func Set(ctx context.Context, key string, v interface{}, exp time.Duration) error {
	return rdsClient.Set(ctx, key, v, exp).Err()
}

func HSet(ctx context.Context, key string, values ...interface{}) error {
	return rdsClient.HSet(ctx, key, values).Err()
}

func Incr(ctx context.Context, key string) (int64, error) {
	return rdsClient.Incr(ctx, key).Result()
}

func Decr(ctx context.Context, key string) (int64, error) {
	return rdsClient.Decr(ctx, key).Result()
}

func IncrBy(ctx context.Context, key string, num int64) (int64, error) {
	return rdsClient.IncrBy(ctx, key, num).Result()
}

func GetString(ctx context.Context, key string) (string, error) {
	return rdsClient.Get(ctx, key).Result()
}

func MGet(ctx context.Context, keys []string) ([]interface{}, error) {
	return rdsClient.MGet(ctx, keys...).Result()
}

func GetInt64(ctx context.Context, key string) (int64, error) {
	return rdsClient.Get(ctx, key).Int64()
}

func GetInt(ctx context.Context, key string) (int, error) {
	return rdsClient.Get(ctx, key).Int()
}

func GetFloat64(ctx context.Context, key string) (float64, error) {
	return rdsClient.Get(ctx, key).Float64()
}

func HGetInt64(ctx context.Context, key, field string) (int64, error) {
	return rdsClient.HGet(ctx, key, field).Int64()
}

func HIncrBy(ctx context.Context, key string, field string, incr int64) (int64, error) {
	return rdsClient.HIncrBy(ctx, key, field, incr).Result()
}

func HGetInt(ctx context.Context, key, field string) (int, error) {
	return rdsClient.HGet(ctx, key, field).Int()
}

func HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return rdsClient.HGetAll(ctx, key).Result()
}

func Del(ctx context.Context, key string) error {
	return rdsClient.Del(ctx, key).Err()
}

func Client() (*redis.Client, error) {
	if rdsClient == nil {
		return nil, notInitErr
	}
	return rdsClient, nil
}

func GetDefaultInstance() *redis.Client {
	return rdsClient
}

func SetExpiredTime(ctx context.Context, key string, exp time.Duration) error {
	return rdsClient.Expire(ctx, key, exp).Err()
}
