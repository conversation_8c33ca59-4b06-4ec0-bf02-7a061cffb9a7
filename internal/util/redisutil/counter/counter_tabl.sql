CREATE TABLE `counter_tab` (
   `id` bigint NOT NULL AUTO_INCREMENT,
   `codis_key` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
   `codis_type` tinyint(4) unsigned NOT NULL DEFAULT '0',
   `field` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
   `codis_value` bigint(20) NOT NULL DEFAULT '0',
   `ctime` int(10) unsigned NOT NULL DEFAULT '0',
   `mtime` int(10) unsigned NOT NULL DEFAULT '0',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uniq_key_field` (`codis_key`,`field`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci