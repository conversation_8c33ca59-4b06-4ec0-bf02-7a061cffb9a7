package counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"strings"
	"sync"
	"time"
)

type RedisCounter interface {
	Incr(ctx context.Context, key string) (int64, error)
	Decr(ctx context.Context, key string) (int64, error)
	IncrBy(ctx context.Context, key string, value int64) (int64, error)
	GetInt64(ctx context.Context, key string) (int64, error)
	GetInt(ctx context.Context, key string) (int, error)
	HGetInt64(ctx context.Context, key string, field string) (int64, error)
	HGetInt(ctx context.Context, key string, field string) (int, error)
	HIncrBy(ctx context.Context, key string, field string, incr int64) (int64, error)
}

type redisCounter struct{}

const (
	RefreshCounterInterval = 5 * time.Second
	MaxBatchSize           = 200
	Delimiter              = "{&=&}"
	Integer                = 1
	Hash                   = 2
)

var (
	incrMap  sync.Map
	hIncrMap sync.Map
	initOnce sync.Once
)

func NewRedisCounterImpl() *redisCounter {
	redisCounterInst := &redisCounter{}
	initOnce.Do(syncCounter)
	return redisCounterInst
}

func syncCounter() {
	ctx := context.Background()
	db, dbErr := dbutil.MasterDB(ctx, CounterTabHook)
	if dbErr != nil {
		logger.LogErrorf("redis counter, get db %s error", counterTab)
		return
	}
	if configutil.GetCloseRedisCounterToDBSwitch(ctx) {
		logger.LogInfof("close redis counter to db switch is open, nothing to do")
		return
	}

	ticker := time.NewTicker(RefreshCounterInterval)
	go func(db scormv2.SQLCommon) {
		// 作为异常保护，如果连续10次存储出错，直接退出该后台携程
		errCount := 0
		for range ticker.C {
			if configutil.GetCloseRedisCounterToDBSwitch(ctx) {
				incrMap.Range(func(key, value interface{}) bool {
					incrMap.Delete(key)
					return true
				})
				hIncrMap.Range(func(key, value interface{}) bool {
					hIncrMap.Delete(key)
					return true
				})
				logger.LogInfof("close redis counter to db switch is open, return from go")
				return
			}
			valueStrings := make([]string, 0)
			valueArgs := make([]interface{}, 0)
			syncCounterResultList := make([]SyncCounterResult, 0)
			now := timeutil.GetCurrentUnixTimeStamp(ctx)
			incrMap.Range(func(key, count interface{}) bool {
				keyStr := key.(string)
				v, _ := count.(int64)
				valueStrings = append(valueStrings, "(?,?,?,?,?,?)")

				syncCounterResultList = append(syncCounterResultList, SyncCounterResult{
					RedisKey:   keyStr,
					RedisType:  Integer,
					Field:      "",
					RedisValue: v,
					Ctime:      now,
					Mtime:      now,
				})

				incrMap.Delete(key)
				return true
			})

			hIncrMap.Range(func(key, count interface{}) bool {
				keyStr := key.(string)
				tmpArr := strings.Split(keyStr, Delimiter)
				if len(tmpArr) != 2 {
					logger.LogErrorf("redis counter, the length of hash is not equal 2")
					return true
				}

				hashKey := tmpArr[0]
				fieldStr := tmpArr[1]
				v, _ := count.(int64)

				valueStrings = append(valueStrings, "(?,?,?,?,?,?)")

				syncCounterResultList = append(syncCounterResultList, SyncCounterResult{
					RedisKey:   hashKey,
					RedisType:  Hash,
					Field:      fieldStr,
					RedisValue: v,
					Ctime:      now,
					Mtime:      now,
				})

				hIncrMap.Delete(key)
				return true
			})

			sort.Slice(syncCounterResultList, func(i, j int) bool {
				if syncCounterResultList[i].RedisKey == syncCounterResultList[j].RedisKey {
					return syncCounterResultList[i].Field < syncCounterResultList[j].Field
				}
				return syncCounterResultList[i].RedisKey < syncCounterResultList[j].RedisKey
			})
			for _, syncCounterResult := range syncCounterResultList {
				valueArgs = append(valueArgs, syncCounterResult.RedisKey)
				valueArgs = append(valueArgs, syncCounterResult.RedisType)
				valueArgs = append(valueArgs, syncCounterResult.Field)
				valueArgs = append(valueArgs, syncCounterResult.RedisValue)
				valueArgs = append(valueArgs, syncCounterResult.Ctime)
				valueArgs = append(valueArgs, syncCounterResult.Mtime)
			}

			reqLen := len(valueStrings)
			for i := 0; i < reqLen; i += MaxBatchSize {
				j := i + MaxBatchSize
				if j > reqLen {
					j = reqLen
				}
				tmpValueStrings := valueStrings[i:j]
				tmpValueArgs := valueArgs[i*6 : j*6]
				sqlStmt := fmt.Sprintf("INSERT INTO `%s` (`codis_key`, `codis_type`, `field`, `codis_value`, `ctime`, `mtime`) "+
					"VALUES %s ON DUPLICATE KEY UPDATE `codis_key`=VALUES(`codis_key`), `codis_type`=VALUES(`codis_type`), `field`=VALUES(`field`), `codis_value`=VALUES(`codis_value`),"+
					"`ctime`=VALUES(`ctime`), `mtime`=VALUES(`mtime`)",
					counterTab, strings.Join(tmpValueStrings, ","))
				if db == nil {
					logger.LogErrorf("redis counter insert or update to db %s error %s", counterTab, "db is nil")
					return
				}
				d := db.Exec(sqlStmt, tmpValueArgs...)
				if d.GetError() != nil {
					logger.LogErrorf("redis counter insert or update to db %s error %v", counterTab, d.GetError())
					errCount++
					if errCount > 10 {
						logger.LogInfof("redis counter insert or update to db %s kill for err count greater than 10", counterTab)
						return
					}
				} else {
					logger.LogDebugf("redis counter insert or update success")
					errCount = 0
				}
			}
		}
	}(db)
}

func (s *redisCounter) Incr(ctx context.Context, key string) (int64, error) {
	count, err := redisutil.Incr(ctx, key)
	if err != nil {
		return 0, fmt.Errorf("incr error: %v", err)
	}
	if !configutil.GetCloseRedisCounterToDBSwitch(ctx) {
		incrMap.Store(key, count)
	}
	return count, nil
}

func (s *redisCounter) Decr(ctx context.Context, key string) (int64, error) {
	count, err := redisutil.Decr(ctx, key)
	if err != nil {
		return 0, fmt.Errorf("decr error: %v", err)
	}
	if !configutil.GetCloseRedisCounterToDBSwitch(ctx) {
		incrMap.Store(key, count)
	}
	return count, nil
}

func (s *redisCounter) IncrBy(ctx context.Context, key string, value int64) (int64, error) {
	count, err := redisutil.IncrBy(ctx, key, value)
	if err != nil {
		return 0, fmt.Errorf("incr error: %v", err)
	}
	if !configutil.GetCloseRedisCounterToDBSwitch(ctx) {
		incrMap.Store(key, count)
	}
	return count, nil
}

func (s *redisCounter) GetInt64(ctx context.Context, key string) (int64, error) {
	return redisutil.GetInt64(ctx, key)
}

func (s *redisCounter) GetInt(ctx context.Context, key string) (int, error) {
	return redisutil.GetInt(ctx, key)
}

func (s *redisCounter) HGetInt64(ctx context.Context, key, field string) (int64, error) {
	return redisutil.HGetInt64(ctx, key, field)
}

func (s *redisCounter) HGetInt(ctx context.Context, key, field string) (int, error) {
	return redisutil.HGetInt(ctx, key, field)
}

func (s *redisCounter) HIncrBy(ctx context.Context, key string, field string, incr int64) (int64, error) {
	count, err := redisutil.HIncrBy(ctx, key, field, incr)
	tmpKey := str.Join(Delimiter, key, field)
	if err != nil {
		return 0, fmt.Errorf("hincrby error: %v", err)
	}
	if !configutil.GetCloseRedisCounterToDBSwitch(ctx) {
		hIncrMap.Store(tmpKey, count)
	}
	return count, nil
}
