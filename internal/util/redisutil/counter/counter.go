package counter

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const counterTab = "counter_tab"

var CounterTabHook = &CounterTab{}

type CounterTab struct {
	Id    int    `gorm:"column:id" json:"id"`
	Key   string `gorm:"column:codis_key" json:"codis_key"`
	Type  int    `gorm:"column:codis_type" json:"codis_type"`
	Field string `gorm:"column:field" json:"field"`
	Value int64  `gorm:"column:codis_value" json:"codis_value"`
	Ctime int    `gorm:"column:ctime" json:"ctime"`
	Mtime int    `gorm:"column:mtime" json:"mtime"`
}

func (p *CounterTab) TableName() string {
	return counterTab
}

func (p *CounterTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p *CounterTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p *CounterTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(p.Id),
		ModelName: p.TableName(),
	}
}
