//go:generate mockgen -destination=../mocks/redis_lock.go -package=mocks . RedisLockClient,RedisLock

package rlock

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/redislock"
	"time"
)

type RedisLockClient interface {
	Obtain(ctx context.Context, key string, ttl time.Duration, opt *redislock.Options) (RedisLock, error)
}

type RedisLock interface {
	Refresh(ctx context.Context, ttl time.Duration, opt *redislock.Options) error
	Release(ctx context.Context) error
}

type RedisLockWrapper struct {
	*redislock.Client
}

func New(cli *redis.Client) *RedisLockWrapper {
	c := redislock.New(cli)
	return Wrap(c)
}

func Wrap(c *redislock.Client) *RedisLockWrapper {
	return &RedisLockWrapper{Client: c}
}

func (w *RedisLockWrapper) Obtain(ctx context.Context, key string, ttl time.Duration, opt *redislock.Options) (RedisLock, error) {
	l, err := w.Client.Obtain(ctx, key, ttl, opt)
	return l, err
}
