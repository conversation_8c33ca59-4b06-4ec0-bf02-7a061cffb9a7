package esutil

import (
	"context"
	es "git.garena.com/shopee/bg-logistics/go/go-elasticsearch/v7"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	jsoniter "github.com/json-iterator/go"
	"io/ioutil"
)

var esClient *es.Client

func InitEsClient() error {
	esConf := configutil.GetEsConf(context.TODO())
	cof := es.Config{}
	cof.Addresses = []string{esConf.Host}
	cof.Username = esConf.Username
	cof.Password = esConf.Password
	client, err := es.NewClient(cof)
	if err != nil {
		return err
	}
	esClient = client
	return nil
}

func ExactSearch(ctx context.Context, request EsRequest) (EsResponse, error) {
	esRsp := EsResponse{}
	req, reqErr := request.GetRequestParam()
	if reqErr != nil {
		return esRsp, reqErr
	}
	resp, err := req.Do(ctx, esClient)
	if err != nil {
		return esRsp, err
	}
	defer resp.Body.Close()
	ret, rerr := ioutil.ReadAll(resp.Body)
	if rerr != nil {
		return esRsp, rerr
	}
	if err := jsoniter.Unmarshal(ret, &esRsp); err != nil {
		return esRsp, err
	}

	return esRsp, nil
}
