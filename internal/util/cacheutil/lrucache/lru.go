package cache

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	lru "github.com/hashicorp/golang-lru"
	"math/rand"
	"time"
)

// LRU实例名称
type lruCacheClientName string

// LfsClientLruName 等常量
// 以最小粒度管理LRU缓存实例
// 名称要与Apollo配置对应
//
//	注：
//	如新增LRU的Name则需要同时维护好Apollo，不然进行参数调整时，取不到值则会使用默认值
const (
	LfsClientLruName         lruCacheClientName = "LfsClient"
	LpsClientLruName         lruCacheClientName = "LpsClient"
	ResourceNeedDGLruName    lruCacheClientName = "ResourceNeedDG"
	ParseRoutingRuleLruName  lruCacheClientName = "ParseRoutingRule"
	VolumeRoutingRuleLruName lruCacheClientName = "VolumeRoutingRule"
)

// LruValue 元素包装值
// 与原生value的区别：增加了过期时间
// 目的：快速淘汰元素，不等lru队列填满，即进行释放，提高使用率
type LruValue struct {
	Value      interface{} // 缓存的元素
	Expiration int64       // 过期时间（时间戳）
}

type LruCache struct {
	name   lruCacheClientName // LRU缓存实例名称
	Client *lru.Cache         // lruCacheClient
	size   int                // 初始化缓存长度
	enable bool               // lruCache 是否可用
}

const (
	defaultSize    int = 1000             // 默认缓存长度
	defaultTimeout     = 60 * time.Second // 默认过期时长，60s，默认单位：秒（s）
	resizeTimeout      = 300              // 变更时间默认 300s，随机在 300 秒完成 resize
)

// NewLruCache 创建LRU内存缓存实例
// @param lruName 缓存实例名称
// @return LRU缓存实例 *LruCache
// <AUTHOR> Bo | SLS BE | <EMAIL>
func NewLruCache(lruName lruCacheClientName) (*LruCache, error) {
	// 定义LRU缓存实例
	lc := new(LruCache)
	lc.name = lruName
	lc.size = defaultSize
	// 默认为true
	lc.enable = true
	// 初始化lruCacheClient，采用默认值进行初始化，在增加元素时再进行重新计算size
	l, err := lru.New(lc.size)
	if err != nil {
		return nil, err
	}
	lc.Client = l

	return lc, nil
}

// expired 过期
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func (lv *LruValue) expired(ctx context.Context) bool {
	if lv != nil && recorder.Now(ctx).UnixNano() > lv.Expiration {
		return true
	}
	return false
}

// newLruValue 设置元素值与超时时长
// @param value 缓存元素的value
// @param expire 超时时长
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func newLruValue(ctx context.Context, value interface{}, expire time.Duration) *LruValue {
	lruValue := new(LruValue)
	var endTime int64
	if expire == 0 {
		expire = defaultTimeout
	}
	if expire > 0 {
		endTime = recorder.Now(ctx).Add(expire).UnixNano()
	}
	lruValue.Value = value
	lruValue.Expiration = endTime
	return lruValue
}

func (lc *LruCache) Get(ctx context.Context, key string) (interface{}, bool) {
	if lc != nil && lc.Client != nil {
		if !lc.enable {
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusNotEnable, "")
			return nil, false
		}
		val, ok := lc.Client.Get(key)
		if !ok {
			if lc.Client.Len() >= lc.size {
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusLru, "")
			} else {
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusNotExist, "")
			}
			return nil, false
		}
		lruValue, ok := val.(*LruValue)
		if !ok {
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusError, "")
			return nil, false
		}
		if lruValue.expired(ctx) {
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusExpired, "")
			lc.Client.Remove(key)
			return nil, false
		}

		// 正常上报的量太大，会比较影响服务的性能，消耗较多的CPU，所以这里把量最大的正常上报注释掉
		//_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, string(lc.name), monitoring.StatusSuccess, "")
		return lruValue.Value, true
	}

	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleLruCache, monitoring.LruCacheGetErr, monitoring.StatusError, "")
	return nil, false
}

func (lc *LruCache) Add(ctx context.Context, key, val interface{}) bool {
	timeout, size, enable := lc.getParam(ctx)
	if !enable {
		if !lc.enable {
			return false
		} else {
			lc.enable = false
			lc.resize(ctx, 0)
			return false
		}
	}

	if enable && !lc.enable {
		lc.enable = true
	}
	// 增加、减少元素时，重新设置LRU缓存长度，不在get时计算，因为查询远多过新增
	lc.resize(ctx, size)

	if lc != nil && lc.Client != nil {
		lruValue := newLruValue(ctx, val, timeout)
		return lc.Client.Add(key, lruValue)
	}
	return false
}

func (lc *LruCache) Exist(key string) bool {
	if lc != nil && lc.Client != nil {
		_, ok := lc.Client.Get(key)
		return ok
	}
	return false
}

func (lc *LruCache) GetWithString(ctx context.Context, key string) (string, bool) {
	if val, ok := lc.Get(ctx, key); ok {
		if value, ok := val.(string); ok {
			return value, true
		}
	}
	return "", false
}

func (lc *LruCache) GetWithFloat64(ctx context.Context, key string) (float64, bool) {
	if val, ok := lc.Get(ctx, key); ok {
		if value, ok := val.(float64); ok {
			return value, true
		}
	}
	return 0, false
}

func (lc *LruCache) GetWithInt64(ctx context.Context, key string) (int64, bool) {
	if val, ok := lc.Get(ctx, key); ok {
		if value, ok := val.(int64); ok {
			return value, true
		}
	}
	return 0, false
}

func (lc *LruCache) GetWithInt(ctx context.Context, key string) (int, bool) {
	if val, ok := lc.Get(ctx, key); ok {
		if value, ok := val.(int); ok {
			return value, true
		}
	}
	return 0, false
}

// resize 重新设定LRU缓存长度
//
//	1.小于当前长度，增加随机时间进行移除，避免缓存雪崩
//	2.大于当前长度，则刷新长度
//	3.长度为0,清空缓存
//	@param size 重设定的缓存长度
//	<AUTHOR>
func (lc *LruCache) resize(ctx context.Context, size int) {
	if size == 0 {
		// 清零需要增加睡眠时间，避免缓存雪崩
		logger.CtxLogInfof(ctx, "lru_cache %v resize purge, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		go func(size int) {
			rand.Seed(time.Now().UnixNano()) // nolint
			seconds := recorder.RandIntn(ctx, resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Purge()
			}
			logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish purge", lc.name, seconds)
		}(size)
	} else if lc.size == size {

	} else if lc.size < size {
		// 扩容直接扩
		logger.CtxLogInfof(ctx, "lru_cache %v resize scale up, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		lc.Client.Resize(size)
	} else if lc.size > size {
		// 缩容需要增加睡眠时间，避免缓存雪崩
		lc.size = size
		logger.CtxLogInfof(ctx, "lru_cache %v resize scale down, source_size:%d, target_size:%d", lc.name, lc.size, size)
		go func(size int) {
			rand.Seed(time.Now().UnixNano()) // nolint
			seconds := recorder.RandIntn(ctx, resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Resize(size)
				logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish scale down", lc.name, seconds)
			}
		}(size)
	}
}

// getParam 获取lru参数，来源于Apollo配置
//
//	根据LRU缓存实例名，来查询Apollo对应的配置，若未配置，则取默认值返回
//	@return 过期时长，单位：秒（s） time.Duration
//	@return 缓存长度 int
//	<AUTHOR> Bo | SLS BE | <EMAIL>
func (lc *LruCache) getParam(ctx context.Context) (timeout time.Duration, size int, enable bool) {
	name := string(lc.name)
	timeout = defaultTimeout
	size = defaultSize
	enable = true

	lruCacheConfig := configutil.GetLruCacheConf(ctx)
	param, ok := lruCacheConfig.Param[name]
	if ok {
		// 配置过期时长数值，默认单位：秒（s），转换为过期时长（time.Duration实例）
		timeout = time.Duration(param.Timeout) * time.Second
		size = param.Size
		enable = param.Enable
	}
	if size <= 0 {
		size = defaultSize
	}

	return timeout, size, enable
}
