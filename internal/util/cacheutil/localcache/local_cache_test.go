package localcache

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"sync"
	"testing"
	"time"
)

func initDepends() {
	if err := chassis.Init(chassis.WithChassisConfigPrefix("grpc_server")); err != nil {
		panic(err)
	}

	if err := configutil.Init(); err != nil {
		panic(err)
	}

	if err := dbutil.InitGrpcDb(); err != nil {
		panic(err)
	}

	GrpcCacheConfig := []*Conf{
		NewConf(constant.RoutingRule, ruledata.DumpRoutingRule, (*ruledata.RoutingRuleTab)(nil)).WithReloadInterval(time.Minute * 2),
	}

	if err := Init(GrpcCacheConfig...); err != nil {
		panic(err)
	}
}

func BenchmarkGet(b *testing.B) {
	ctx := context.Background()
	initDepends()
	maxLen := 1000
	wg := sync.WaitGroup{}
	wg.Add(maxLen)
	for i := 0; i < maxLen; i++ {
		go func(i int) {
			defer wg.Done()
			data, err := Get(ctx, constant.RoutingRule, ruledata.RuleTabList)
			if err != nil {
				b.Errorf("get rule list error:%d", i)
				return
			}

			ruleList, ok := data.([]*ruledata.RoutingRuleTab)
			if !ok {
				b.Errorf("fail to convert, value:%v, %d", ruleList, i)
				return
			}
			rules := make([]*ruledata.RoutingRuleTab, 0)
			//排序
			sort.SliceStable(ruleList, func(i, j int) bool {
				return ruleList[i].Priority < ruleList[j].Priority
			})
			//
			for _, item := range ruleList {
				if item.ProductID == 18025 && item.Status == rule.RuleStatusActive && int64(item.EffectiveStartTime) <= timeutil.GetCurrentUnixTimeStamp(ctx) {
					rules = append(rules, item)
				}
			}
			if len(rules) == 0 {
				b.Errorf("rule not found, productID:%d, %d", 18025, i)
			}
			if len(rules) != 5 {
				b.Errorf("rule result is error:%d", len(rules))
			}
		}(i)
	}
	wg.Wait()
}
