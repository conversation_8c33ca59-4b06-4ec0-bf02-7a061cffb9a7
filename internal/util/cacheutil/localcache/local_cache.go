package localcache

import (
	"bytes"
	"context"
	"encoding/gob"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"github.com/patrickmn/go-cache"
	"github.com/pkg/errors"
	"math/rand"
	"sync"
	"time"
)

const maxKeyspace = 100

var cacheMgr *Manager

var (
	ErrNamespaceNotFound = errors.New("namespace not found")
	ErrKeyNotFound       = errors.New("key not found")
	ErrNotInit           = errors.New("cache manager not inited")
)

type DumpFn func() (map[string]interface{}, error)

type UnmarshalFn func(key string, data []byte, value interface{}) error

type Conf struct {
	namespace      string
	reloadInterval time.Duration
	dump           DumpFn
	cacheSize      int
	model          interface{}
	asyncInit      bool
}

func NewConf(namespace string, dump DumpFn, model interface{}) *Conf {
	return &Conf{
		namespace: namespace,
		dump:      dump,
		model:     model,
	}
}

func (c *Conf) WithReloadInterval(interval time.Duration) *Conf {
	c.reloadInterval = interval
	return c
}

func (c *Conf) WithCacheSize(size int) *Conf {
	c.cacheSize = size
	return c
}

func (c *Conf) WithAsyncInit(async bool) *Conf {
	c.asyncInit = async
	return c
}

type Cache struct {
	*cache.Cache
	config *Conf
	initCh chan struct{}
	// 通过channel通信来调用reload函数来刷新localCache
	reloadCh chan struct{}
	version  *int
}

// IsCacheVersionLatest 当前缓存版本号是否最新
func (c *Cache) IsCacheVersionLatest(ctx context.Context) bool {
	isCacheVersionLatest := true
	if c.version == nil {
		//如果cache版本未初始化，返回false
		logger.LogInfof("local_cache version nil |key=%v", c.config.namespace)
		isCacheVersionLatest = false
	} else {
		//如果配置了版本, 并且和cache版本不同
		latestConfigVersion := c.GetCacheVersionConfigLatest(ctx)
		if *c.version != latestConfigVersion {
			logger.LogInfof("local_cache version diff |key=%v version=%v configVersion=%v", c.config.namespace, c.version, latestConfigVersion)
			isCacheVersionLatest = false
		}
	}

	return isCacheVersionLatest
}

// GetCacheVersionConfigLatest 当前配置缓存最新版本号
func (c *Cache) GetCacheVersionConfigLatest(ctx context.Context) int {
	//默认0, 如果显式配置了key, 则用这个配置
	var version = 0
	localCacheConfig := configutil.GetLocalCacheConf(ctx)
	keyConfigMap := localCacheConfig.GetKeyConfigMap()
	if keyConfig, ok := keyConfigMap[c.config.namespace]; ok && keyConfig.Version != nil {
		version = *keyConfig.Version
	}

	return version
}

// SetVersionLatest 设置缓存版本号到最新
func (c *Cache) SetVersionLatest(ctx context.Context) {
	version := c.GetCacheVersionConfigLatest(ctx)
	c.version = &version
}

// IsAutoReload 是否自动刷新
func (c *Cache) IsAutoReload(ctx context.Context) bool {
	//开关 https://jira.shopee.io/browse/SPLPS-1462
	//如果显式配置了key不自动刷新, 则return false
	var isAutoRefresh = true
	localCacheConfig := configutil.GetLocalCacheConf(ctx)
	if localCacheConfig.DefaultAutoRefresh != nil {
		//赋值默认值
		isAutoRefresh = *localCacheConfig.DefaultAutoRefresh
	}

	keyConfigMap := localCacheConfig.GetKeyConfigMap()
	if localCacheKeyConfig, ok := keyConfigMap[c.config.namespace]; ok && localCacheKeyConfig.IsAutoRefresh != nil {
		isAutoRefresh = *localCacheKeyConfig.IsAutoRefresh
	}

	logger.LogInfof("local_cache auto reload %v |key=%v conf=%v", isAutoRefresh, c.config.namespace, localCacheConfig)
	return isAutoRefresh
}

// isAsyncInit
func (c *Cache) isAsyncInit() bool {
	var isAsyncInit = c.config.asyncInit //默认使用asyncInit配置

	// 上游无context传入
	ctx := context.Background()
	localCacheConfig := configutil.GetLocalCacheConf(ctx)

	//如果开启layer-cache，则异步加载local-cache
	if localCacheConfig.DefaultUsingLayerCache != nil && *localCacheConfig.DefaultUsingLayerCache {
		isAsyncInit = true
	}

	//如果有针对单独key的layer-cache配置
	keyConfigMap := localCacheConfig.GetKeyConfigMap()
	if localCacheKeyConfig, ok := keyConfigMap[c.config.namespace]; ok && localCacheKeyConfig.IsUsingLayerCache != nil && *localCacheKeyConfig.IsUsingLayerCache {
		isAsyncInit = true
	}

	logger.LogInfof("local_cache isAsyncInit %v |key=%v conf=%v", isAsyncInit, c.config.namespace, localCacheConfig)
	return isAsyncInit
}

type Manager struct {
	defaultReloadInterval time.Duration
	client                *redis.Client
	country               enum.Region
	caches                map[string]*Cache
	lock                  sync.RWMutex
	tmpCache              *cache.Cache // a temporary cache to use when the whole table is still loading
	isInitialized         bool         //local cache是否初始化完成

	// 流量回放方法
	wrapOfAllItems func(ctx context.Context, namespace string) map[string]interface{}
}

func NewCacheManager(country enum.Region, defaultReloadInterval time.Duration, redisClient *redis.Client) (*Manager, error) {
	m := &Manager{
		defaultReloadInterval: defaultReloadInterval,
		client:                redisClient,
		country:               country,
		caches:                map[string]*Cache{},
		tmpCache:              cache.New(time.Minute*5, time.Minute*5),
	}

	// 流量回放方法注册
	m.wrapOfAllItems = recorder.Wrap(m.allItems).(func(ctx context.Context, namespace string) map[string]interface{})

	return m, nil
}

func (m *Manager) Register(ctx context.Context, conf *Conf) (<-chan struct{}, error) {
	if conf == nil {
		return nil, nil
	}
	if conf.namespace == "" {
		return nil, errors.New("empty namespace")
	}
	logger.CtxLogInfof(context.Background(), "Local cache registering namespace=%s", conf.namespace)
	if conf.dump == nil {
		return nil, errors.New("empty dump function")
	}
	if conf.model != nil {
		gob.Register(conf.model)
	}

	// AllItems中间转换类型结构也需gob注册
	gob.Register(map[string]interface{}{})

	if conf.reloadInterval <= 0 {
		conf.reloadInterval = m.defaultReloadInterval
	}

	m.lock.Lock()
	defer m.lock.Unlock()

	_, ok := m.caches[conf.namespace]
	if ok {
		return nil, errors.New("namespace already registered")
	}
	if len(m.caches) > maxKeyspace {
		return nil, errors.New("exceed max key space")
	}

	data := cache.New(0, 0)
	initCh := make(chan struct{})
	// 通过reloadCh来唤起协程的reload函数从而刷新localCache
	reloadCh := make(chan struct{})
	m.caches[conf.namespace] = &Cache{Cache: data, config: conf, initCh: initCh, reloadCh: reloadCh}

	reload := func(conf *Conf) {
		_ = monitoring.Trace(context.Background(), monitoring.CatModuleMemoryCache, conf.namespace, func(ctx context.Context) error {
			err := m.reload(ctx, conf)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Local cache reload error, conf=%+v, err=%v", *conf, err)
			}
			return err
		})
	}

	go func() {
		reload(conf)
		close(initCh)

		// 两种定时任务分别设置不同的ticker，放到一个for循环里，然后用case去判断， 这样同一时间只有一个定时任务启动
		ticker1 := time.NewTicker(conf.reloadInterval) //定时自动刷新的ticker
		ticker2 := time.NewTicker(time.Minute * 1)     //定时从Apollo获取要不要自动刷新的ticker
		cache, ok := m.getCache(context.TODO(), conf.namespace)
		if !ok {
			logger.LogErrorf("local_cache getCache fail, err=%v", ErrNamespaceNotFound)
		}

		//首次启动时采用随机数睡眠，以错开每个服务器刷新的初始时间，减小db压力
		wait := time.Duration(rand.Float64() * float64(conf.reloadInterval)) // nolint
		time.Sleep(wait)

		// reload刷新缓存
		for true {
			select {
			case <-cache.reloadCh: //接口调用reload函数
				logger.LogInfof("got reloadCh, start to reload")
				reload(conf)
			case <-ticker1.C:
				if !m.caches[conf.namespace].IsAutoReload(ctx) { //定时自动刷新开关
					continue
				}
				reload(conf)
			case <-ticker2.C:
				// 协程中不传入上游的ctx
				ctx := context.Background()
				//检查cache version是否最新，如果不一致则主动reload。这个version由apollo配置, 在reload时更新
				if !cache.IsCacheVersionLatest(ctx) {
					logger.LogInfof("local_cache version not latest, reload manually")
					reload(conf)
				}
			}
		}
	}()

	return initCh, nil

}

func (m *Manager) redisBackupKey(namespace string) string {
	return fmt.Sprintf("localcache:%s:%s", m.country, namespace)
}

func (m *Manager) reload(ctx context.Context, conf *Conf) error {
	logger.CtxLogInfof(context.Background(), "Local cache reloading namespace=%s\n", conf.namespace)
	redisKey := m.redisBackupKey(conf.namespace)

	var (
		newCache *cache.Cache
		isFresh  bool
		numKeys  int
	)

	data, err := conf.dump()

	if err != nil {
		logger.CtxLogInfof(context.Background(), "Local cache dump from db namespace=%s, err=%v", conf.namespace, err)
		if m.client != nil {
			dataFromRedis, e := m.client.Get(context.Background(), redisKey).Bytes()
			if e == redis.Nil {
				return errors.Wrap(err, "call dump function")
			}
			if e != nil {
				return errors.Wrap(e, "get backup from redis")
			}
			b := bytes.NewBuffer(dataFromRedis)
			cacheItems := map[string]cache.Item{}
			e = gob.NewDecoder(b).Decode(&cacheItems)
			if e != nil {
				return errors.Wrap(e, "decode redis backup")
			}
			newCache = cache.NewFrom(0, 0, cacheItems)
			numKeys = len(cacheItems)
		} else {
			// 上报更新的缓存数量
			prometheusutil.ServiceCacheReport(conf.namespace, 0)
			return err
		}
	} else {
		newCache = cache.New(0, 0)
		for k, v := range data {
			newCache.Set(k, v, -1)
		}
		isFresh = true
		numKeys = len(data)
	}
	// 上报更新的缓存数量
	prometheusutil.ServiceCacheReport(conf.namespace, int64(len(data)))

	m.lock.Lock()
	m.caches[conf.namespace].Cache = newCache
	m.caches[conf.namespace].SetVersionLatest(ctx)
	m.lock.Unlock()

	logger.CtxLogInfof(context.Background(), "Local cache reloaded namespace=%s, numKeys=%d, fromRedisBackup=%v\n", conf.namespace, numKeys, !isFresh)

	if isFresh {
		err := m.backupContent(redisKey, newCache)
		if err != nil {
			logger.CtxLogInfof(context.Background(), "Local cache backup|namespace=%s, err=%v\n", conf.namespace, err)
		}
	}

	return nil
}

func (m *Manager) backupContent(key string, newCache *cache.Cache) error {
	if m.client == nil {
		return errors.New("redis client not inited")
	}
	ttl, err := m.client.TTL(context.Background(), key).Result()
	if err == nil && ttl > time.Hour {
		logger.CtxLogInfof(context.Background(), "Local cache backup, key=%v, ttl=%v, no need to update", key, ttl)
		return nil
	}
	b := &bytes.Buffer{}
	err = gob.NewEncoder(b).Encode(newCache.Items())
	if err != nil {
		return errors.WithMessage(err, "encode content")
	}
	_, err = m.client.Set(context.Background(), key, b.Bytes(), time.Hour*2).Result()
	if err != nil {
		return errors.WithMessage(err, "save to redis")
	}
	return nil
}

func (m *Manager) getCache(ctx context.Context, namespace string) (*Cache, bool) {
	m.lock.RLock()
	defer m.lock.RUnlock()
	data, ok := m.caches[namespace]
	if !ok {
		return nil, false
	}
	return data, ok
}

func (m *Manager) get(ctx context.Context, namespace string, key string, wait bool) (ret interface{}, err error) {
	data, ok := m.getCache(ctx, namespace)
	if !ok {
		return nil, ErrNamespaceNotFound
	}

	if !wait {
		select {
		case <-data.initCh:
		default:
			return nil, ErrNotInit
		}
	} else {
		<-data.initCh
	}

	ret, ok = data.Get(key)
	if !ok {
		return nil, ErrKeyNotFound
	}

	return ret, nil
}

// GetOrQuery finds from local cache, if cache is not ready, use dataQuery.
// When the whole table is still loading, it will query data from the `dataQuery` function,
// and save the results to tmpCache.
func GetOrQuery(ctx context.Context, namespace, key string, dataQuery func() (interface{}, error)) (interface{}, error) {
	ret, err := cacheMgr.get(ctx, namespace, key, false)
	if err == ErrNotInit {
		// the whole table is not ready, try tmpCache
		tmpKey := namespace + ":" + key
		val, ok := cacheMgr.tmpCache.Get(tmpKey)
		if ok {
			return val, nil
		}
		ret, err = dataQuery()
		if err != nil {
			return ret, err
		}
		cacheMgr.tmpCache.Set(tmpKey, ret, 0)
		return ret, err
	}
	return ret, err
}

func AllItems(ctx context.Context, namespace string) map[string]interface{} {
	if configutil.IsRecorderEnable() && (recorder.IsRecorderContext(ctx) || recorder.ExtractReplayId(ctx) != "") {
		return cacheMgr.wrapOfAllItems(ctx, namespace)
	}
	return cacheMgr.allItems(ctx, namespace)
}

func (m *Manager) allItems(ctx context.Context, namespace string) map[string]interface{} {
	data, ok := m.getCache(ctx, namespace)
	if !ok {
		return nil
	}
	<-data.initCh

	ret := map[string]interface{}{}
	for k, item := range data.Items() {
		ret[k] = item.Object
	}
	return ret
}

func Get(ctx context.Context, namespace string, key string) (interface{}, error) {
	if cacheMgr == nil {
		return nil, errors.New("local cache manger init not finished")
	}
	wrap := recorder.Wrap(cacheMgr.get).(func(ctx context.Context, namespace string, key string, wait bool) (val interface{}, err error))
	if wrap == nil {
		return nil, errors.New("local cache wrap init not finished")
	}

	return wrap(ctx, namespace, key, true)
}

func (m *Manager) IsInitialized() bool {
	cacheMgr.lock.RLock()
	defer cacheMgr.lock.Unlock()
	return cacheMgr.isInitialized
}

func (m *Manager) SetInitialized() {
	cacheMgr.lock.Lock()
	cacheMgr.isInitialized = true
	cacheMgr.lock.Unlock()
}

func (m *Manager) waitInit() {
	asyncInitMap := make(map[string]chan struct{})
	for _, c := range m.caches {
		if !c.isAsyncInit() {
			//如果不用异步加载，阻塞等待local-cache加载完成
			<-c.initCh
			logger.LogInfof("local_cache waitInit ok namespace=%s\n", c.config.namespace)
		} else {
			//如果使用异步加载，加入异步加载local-cache标记队列
			asyncInitMap[c.config.namespace] = c.initCh
		}
	}

	//更新local-cache加载状态
	go func() {
		for namespace, ch := range asyncInitMap {
			<-ch
			logger.LogInfof("local_cache waitInit async ok namespace=%s\n", namespace)
		}
		//全部加载完成
		m.SetInitialized()
	}()
}

// IsUseLayerCache 是否使用layer cache
func IsUseLayerCache(ctx context.Context, namespace string) bool {
	var isUseLayerCache = false
	localCacheConfig := configutil.GetLocalCacheConf(ctx)
	if localCacheConfig.DefaultUsingLayerCache != nil {
		//赋值默认值
		isUseLayerCache = *localCacheConfig.DefaultUsingLayerCache
	}

	//如果配置了强制切换local-cache，且local-cache已经初始化完成，则不使用layer-cache
	if localCacheConfig.DefaultForceSwitch != nil && *localCacheConfig.DefaultForceSwitch && cacheMgr.IsInitialized() {
		isUseLayerCache = false
	}

	//如果有针对单独key的layer-cache配置
	keyConfigMap := localCacheConfig.GetKeyConfigMap()
	if localCacheKeyConfig, ok := keyConfigMap[namespace]; ok && localCacheKeyConfig.IsUsingLayerCache != nil && *localCacheKeyConfig.IsUsingLayerCache {
		isUseLayerCache = true
	}

	return isUseLayerCache
}

// 根据namespace获取cache
func ReloadCache(namespace string) error {
	//获取namespace对应的缓存数据
	data, ok := cacheMgr.getCache(context.TODO(), namespace)
	if !ok {
		return fmt.Errorf("cache[%s] is not configed", namespace)
	}
	//写入reloadCh中，触发刷新缓存
	data.reloadCh <- struct{}{}
	return nil
}

func ReloadAllCache() error {
	if cacheMgr == nil {
		return errors.New("local cache not init")
	}
	var wg sync.WaitGroup
	for namespace := range cacheMgr.caches {
		wg.Add(1)
		namespaceName := namespace
		go func() {
			defer func() {
				if err := recover(); err != nil {

				}
			}()
			defer wg.Done()
			err := ReloadCache(namespaceName)
			if err != nil {
				fmt.Println(fmt.Printf("cache[%s] refresh failure, err=%v", namespaceName, err))
			}
		}()
	}
	wg.Wait()
	return nil
}

func Init(registry ...*Conf) error {
	m, err := NewCacheManager(enum.Region(envvar.GetCID()), time.Hour, nil)
	if err != nil {
		return errors.WithMessage(err, "NewCacheManager")
	}
	cacheMgr = m

	// 上游无context传入
	ctx := context.Background()
	var chs []<-chan struct{}
	for _, conf := range registry {
		ch, err := cacheMgr.Register(ctx, conf)
		if err != nil {
			return errors.WithMessage(err, "Register")
		}
		if !conf.asyncInit {
			chs = append(chs, ch)
		}
	}

	//等待local-cache初始化完成
	cacheMgr.waitInit()
	return nil
}

func InitTest(namespace, key string, value interface{}, count int) error {
	m, err := NewCacheManager("ID", time.Hour, nil)
	if err != nil {
		return errors.WithMessage(err, "NewCacheManager")
	}
	cacheMgr = m

	m.caches[namespace] = &Cache{Cache: cache.New(0, 0), initCh: make(chan struct{}, count)}

	for i := 0; i < count; i++ {
		m.caches[namespace].initCh <- struct{}{}
	}
	m.caches[namespace].Set(key, value, time.Hour)

	return nil
}
