package layercache

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/api"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/layered"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/memory"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/remote"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"github.com/modern-go/reflect2"
	"github.com/pkg/errors"
)

var (
	dataLoaderMiss = errors.New("data loader not found")
	dataNotFound   = errors.New("data not found")
)

type LevelCache struct {
	c api.CacheClient
}

func NewLayerCache() (*LevelCache, error) {
	var (
		ctx         = context.Background()
		cfg         = configutil.GetLayerCacheConf(ctx)
		clusterName = "default"
	)

	if envvar.IsIDCUs() {
		clusterName = "latam_default"
	}

	client := redis.NewStandardClient(redis.NewStandardOption().WithClusterName(clusterName))
	if _, err := client.Ping(ctx).Result(); err != nil {
		return nil, errors.Errorf("ping layer cache redis client failed|cluster:%s, err:%v", clusterName, err)
	}
	c := layered.NewCacheClient(&layered.CacheConfig{
		MemoryCacheConfig: memory.MemoryCacheConfig{
			Size:               uint32(cfg.MemSize),
			NumOfCounters:      uint32(cfg.MemNumOfCounters),
			PhysicalTtlSeconds: uint32(cfg.MemPhysicalTTL),
			LogicTtlSeconds:    uint32(cfg.MemLogicTTL),
		},
		RedisConfig: remote.RedisConfig{
			Client: client,
		},
		LimitKeySize:              false,
		CircuitBreakerEnable:      false,
		PenetrationQpsLimit:       uint32(cfg.PenetrationQpsLimit),
		PenetrationProtect:        true,
		RebuildOnPenetrationError: true,
	})
	return &LevelCache{c: c}, nil
}

func (p *LevelCache) Get(ctx context.Context, namespace, id string, obj interface{}, opts ...LevelOption) error {
	layerCacheConfig := configutil.GetLayerCacheConf(ctx)
	// get the default expireSeconds
	cacheOption := &LevelOptions{
		expiration: uint32(configutil.GetLayerCacheConf(ctx).MemPhysicalTTL),
	}
	if len(opts) > 0 {
		for _, opt := range opts {
			opt(cacheOption)
		}
	}
	key := unique(namespace, id)
	// read the expireSeconds in Apollo config first
	if expireSeconds, ok := layerCacheConfig.ExpireConfig[namespace]; ok {
		cacheOption.expiration = expireSeconds
	}
	if cacheErr := p.c.GetOrRebuild(ctx, key, func() (interface{}, error) {
		if cacheOption.loader == nil {
			sendMonitor(ctx, namespace, genErrMsg(id, monitoring.LayerCacheLoaderIsNil), monitoring.StatusError)
			return nil, dataLoaderMiss
		}
		// 从数据库捞数据
		data, err := cacheOption.loader(ctx, id)
		if !reflect2.IsNil(err) {
			sendMonitor(ctx, namespace+"_loadErr", genErrMsg(id, err.Error()), monitoring.StatusError)
			return nil, err
		}
		if data == nil {
			sendMonitor(ctx, namespace+"_loadNil", genErrMsg(id, dataNotFound.Error()), monitoring.StatusError)
			return nil, dataNotFound
		}
		return data, nil
	}, cacheOption.expiration).Struct(obj); cacheErr == nil {
		sendMonitor(ctx, namespace, id, monitoring.StatusSuccess)
	} else {
		//抛出一个错误给上层，和约定写法保持一致
		if cacheErr == cache.HotPenetrationError {
			sendMonitor(ctx, namespace, genErrMsg(id, monitoring.LayerCacheHotKeyNotInDB), monitoring.StatusError)
		} else {
			sendMonitor(ctx, namespace, genErrMsg(id, monitoring.LayerCacheErr), monitoring.StatusError)
		}
		return cacheErr
	}
	return nil
}

func (p *LevelCache) Set(ctx context.Context, namespace, id string, obj interface{}, expiration uint32) error {
	key := unique(namespace, id)
	return p.c.Set(ctx, key, obj, expiration)
}

func unique(namespace, id string) string {
	return fmt.Sprintf("%s.%s", namespace, id)
}

func sendMonitor(ctx context.Context, namespace, msg, status string) {
	if configutil.GetLayerCacheConf(ctx).IsMonitor {
		_ = monitor.AwesomeReportEvent(ctx, "LayeredCache", namespace, status, msg)
	}
}

func genErrMsg(id, errMsg string) string {
	return id + ":" + errMsg
}
