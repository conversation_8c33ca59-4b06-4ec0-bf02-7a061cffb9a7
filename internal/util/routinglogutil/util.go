package routinglogutil

import (
	"context"
	"encoding/base64"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	jsoniter "github.com/json-iterator/go"
)

func DecodeRoutingLog(ctx context.Context, messageBody string) (routing_log.RoutingLog, []byte, error) {
	// 0.base64 decode
	base64DecodeResult, berr := base64.StdEncoding.DecodeString(messageBody)
	if berr != nil {
		errMsg := fmt.Sprintf("base64 decode failed | message=%s, err=%v", messageBody, berr)
		logger.CtxLogErrorf(ctx, errMsg)
		return routing_log.RoutingLog{}, nil, berr
	}
	// 1.解压缩
	decompressResult, zErr := zip.ZSTDDecompress(base64DecodeResult)
	if zErr != nil {
		errMsg := fmt.Sprintf("zstd decode failed | message=%s, err=%v", messageBody, zErr)
		logger.CtxLogErrorf(ctx, errMsg)
		return routing_log.RoutingLog{}, nil, zErr
	}

	// 2.反序列化order
	log := routing_log.RoutingLog{}
	if err := jsoniter.Unmarshal(decompressResult, &log); err != nil {
		errMsg := fmt.Sprintf("unmarshal order routing log failed | message=%s, err=%v,", messageBody, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return routing_log.RoutingLog{}, nil, err
	}

	return log, decompressResult, nil
}
