package requestid

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/emicklei/go-restful"
	uuid "github.com/satori/go.uuid"
	"math/rand"
	"strings"
)

const incomingRequestIDHeader = "X-Request-Id"
const alphaNumBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

const (
	traceIDSize          = 16
	typeMarkerMask uint8 = 7 << 5
	// indicates the total number of bits being used for components of the flag
	flagBitsTotal                 = 8
	flagBitsTypeMarker            = 3
	flagBitsNonTypeMarker         = flagBitsTotal - flagBitsTypeMarker
	traceIDSizeStringSize         = traceIDSize * 2
	SpanContextEncodingFormatSize = 3
	traceIDPos                    = 0
)

type requestIDCtxKey struct{}
type CtxKey struct{}

func randString(n int) string {
	sb := strings.Builder{}
	sb.Grow(n)
	for i := 0; i < n; i++ {
		sb.WriteByte(alphaNumBytes[rand.Int63()%int64(len(alphaNumBytes))]) // nolint
	}
	return sb.String()
}

func GetRequestID(req *restful.Request) string {
	requestId := req.HeaderParameter(incomingRequestIDHeader)
	if requestId == "" {
		return "|" + uuid.NewV4().String() + "|" // nolint
	}
	return "|" + requestId + "|" + randString(4) // nolint
}

func GenRequestId() string {
	return "|" + uuid.NewV4().String() + "|" + randString(4) // nolint
}

func SetToCtx(ctx context.Context, id string) context.Context {
	if oid := GetFromCtx(ctx); id != "" && oid == id {
		return ctx
	}
	return context.WithValue(ctx, requestIDCtxKey{}, id)
}

func GetFromCtx(ctx context.Context) string {
	idv := ctx.Value(requestIDCtxKey{})
	if idv == nil {
		return ""
	}
	id, ok := idv.(string)
	if !ok {
		return ""
	}
	if id != "" && strings.Contains(id, "mock") {
		mockArr := strings.Split(id, "|")
		if len(mockArr) >= 2 {
			return mockArr[1]
		}
	}
	return id
}

func SetToCtxWithKey(ctx context.Context, id interface{}, key CtxKey) context.Context {
	return context.WithValue(ctx, key, id)
}

func GetCtxWithKey(ctx context.Context, key CtxKey) interface{} {
	return ctx.Value(key)
}

func NewCtx(parent context.Context) context.Context {
	newCtx := ctxhelper.CloneTrace(parent)
	requestId := GetFromCtx(parent)
	newCtx = logger.NewLogContext(newCtx, requestId)
	newCtx = SetToCtx(newCtx, requestId)

	return newCtx
}
