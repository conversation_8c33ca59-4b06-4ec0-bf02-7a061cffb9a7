package mockutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
)

func MockCtx(ctx *restful.Context) context.Context {
	//get value of "testMock" from ctx, if "mock" is not found, return false
	if mockFlag := ctx.ReadHeader(MockAutoTestKey); mockFlag != DoMockFlag {
		return ctx.Ctx
	}
	c := ctx.Ctx
	if len(MockValues(c)) > 0 {
		return c
	}
	//get and set key-value, including: X-Request-Id, sysMock, typeMock
	mockValues := map[string]string{
		MockRequestID:  ctx.ReadHeader(MockRequestID),
		MockSystemsKey: ctx.ReadHeader(MockSystemsKey),
		MockTypeKey:    ctx.ReadHeader(MockTypeKey),
	}
	return context.WithValue(c, CtxMockDef{}, mockValues)
}

func PatchGrpcMockCtx(ctx context.Context, mockReqId, mockSysKey string) context.Context {
	mockValues := map[string]string{
		MockRequestID:  mockReqId,
		MockSystemsKey: mockSysKey,
	}
	return context.WithValue(ctx, CtxMockDef{}, mockValues)
}

func DebugMock(ctx context.Context) {
	fmt.Println("debug ", MockValues(ctx), IsUseMock(ctx, "lfs"), IsUseMock(ctx, "xyz"))
}

func IsUseMock(ctx context.Context, key string) bool {
	//get header key-value from mock ctx
	headers := MockValues(ctx)
	if len(headers) == 0 {
		return false
	}
	//get sysMock
	systemKeys, ok := headers[MockSystemsKey]
	if !ok {
		return false
	}
	//if find key in sysMock, return true, else return false
	for _, systemKey := range strings.Split(systemKeys, ",") {
		if key == systemKey {
			return true
		}
	}
	return false
}

func MockValues(ctx context.Context) map[string]string {
	//get key-value from ctxMock
	var headers map[string]string
	if mockOption, ok := ctx.Value(CtxMockDef{}).(map[string]string); ok {
		headers = make(map[string]string)
		for mockKey, mockValue := range mockOption {
			headers[mockKey] = mockValue
		}
	}
	return headers
}

// IsSkipCache for test only
func IsSkipCache(ctx context.Context) bool {
	if envvar.GetEnvWithCtx(ctx) == enum.LIVE {
		return false
	}
	headers := MockValues(ctx)
	if len(headers) == 0 {
		return false
	}
	skipValue, ok := headers[SkipCacheKey]
	if !ok {
		return false
	}
	if skipValue == "true" {
		return true
	}
	return false
}
