package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

const (
	ServiceRetcodeMonitor = "Service_retcode"
	ServiceCacheMonitor   = "Service_cache"
	// task
	ServiceTaskMonitor = "Service_sync_num"
)

func InitServiceMonitor() {
	cm := map[string][]string{
		ServiceRetcodeMonitor: []string{"retcode"},
		ServiceCacheMonitor:   []string{"cache_namespace"},
	}

	for k, v := range cm {
		label := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(label)
		if err != nil {
			logger.LogErrorf("init InitServiceMonitor metrics failed %+v", err)
		}
	}
}

func InitServiceTaskMonitor() {
	cm := map[string][]string{
		ServiceTaskMonitor: []string{"task_sync_num"},
	}

	for k, v := range cm {
		label := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(label)
		if err != nil {
			logger.LogErrorf("init InitServiceTaskMonitor metrics failed %+v", err)
		}
	}

	gauge := map[string][]string{
		AllocateMaskingProductVolumeMonitor:     []string{"mask_product_id"},
		AllocateFulfillmentProductVolumeMonitor: []string{"mask_product_id", "fulfillment_product_id", "order_type"},
	}

	for k, v := range gauge {
		label := metrics.GaugeOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateGauge(label)
		if err != nil {
			logger.LogErrorf("init InitServiceTaskMonitor metrics failed %+v", err)
		}
	}
}

func ServiceRetcodeReport(retcode int32) {
	_ = CounterIncr(ServiceRetcodeMonitor, map[string]string{
		"retcode": strconv.FormatInt(int64(retcode), 10),
	})
}

func ServiceCacheReport(namespace string, num int64) {
	_ = CounterAdd(ServiceCacheMonitor, map[string]string{
		"cache_namespace": namespace,
	}, float64(num))
}

func ServiceTaskSyncNumReport(taskName string, num int64) {
	_ = CounterAdd(ServiceTaskMonitor, map[string]string{
		"task_sync_num": taskName,
	}, float64(num))
}
