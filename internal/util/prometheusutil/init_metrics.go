package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
)

func InitGrpcMetrics() error {
	opt := metrics.CounterOpts{
		Name:   constant.PrometheusMetricFactor,
		Help:   "request_status",
		Labels: []string{"product_id", "factor_name", "count"},
	}
	err := metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("init metrics failed %+v", err)
		return err
	}

	smrSuccessLabel := metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingSuccess,
		Help:   "request_status",
		Labels: []string{"product_id", "rule_id", "status"},
	}

	err = metrics.CreateCounter(smrSuccessLabel)
	if err != nil {
		logger.LogErrorf("init SmartRoutingSuccess metrics failed %+v", err)
		return err
	}

	//
	InitMaskingMonitor()
	InitSelectLaneMonitor()
	InitScheduleMonitor()
	InitServiceMonitor()
	InitVolumeMonitor()
	InitBatchAllocateMetric()

	return nil
}

func InitTaskMetrics() error {
	opt := metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingProduct,
		Help:   "smart routing product",
		Labels: []string{"product_id"},
	}
	err := metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	opt = metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingRule,
		Help:   "smart routing rule",
		Labels: []string{"product_id", "rule_id"},
	}
	err = metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	opt = metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingSuccess,
		Help:   "smart routing rule",
		Labels: []string{"product_id", "rule_id", "status"},
	}
	err = metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	opt = metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingLane,
		Help:   "smart routing lane",
		Labels: []string{"product_id", "lane_code"},
	}
	err = metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	opt = metrics.CounterOpts{
		Name:   constant.MetricSmartRoutingResource,
		Help:   "smart routing resource",
		Labels: []string{"product_id", "resource_id"},
	}
	err = metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	gaugeOpt := metrics.GaugeOpts{
		Name:   constant.MetricSmartRoutingProductCounter,
		Help:   "smart routing product",
		Labels: []string{"product_id"},
	}

	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
		return err
	}

	gaugeOpt = metrics.GaugeOpts{
		Name:   constant.MetricSmartRoutingLaneCounter,
		Help:   "smart routing product",
		Labels: []string{"product_id", "lane_code"},
	}

	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
		return err
	}

	gaugeOpt = metrics.GaugeOpts{
		Name:   constant.MetricSmartRoutingResourceCounter,
		Help:   "smart routing product",
		Labels: []string{"product_id", "lane_code", "resource_id"},
	}

	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
	}

	gaugeOpt = metrics.GaugeOpts{
		Name:   constant.MetricSmartRoutingActualPointCounter,
		Help:   "smart routing product lane actual point counter",
		Labels: []string{"product_id", "lane_code", "actual_point_type", "actual_point"},
	}
	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
	}

	gaugeOpt = metrics.GaugeOpts{
		Name:   constant.MetricSmartRoutingLineActualPointCounter,
		Help:   "smart routing product lane line actual point counter",
		Labels: []string{"product_id", "lane_code", "line_id", "actual_point_type", "actual_point"},
	}
	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateGauge error", err)
	}

	gaugeOpt = metrics.GaugeOpts{
		Name:   constant.MetricBatchAllocateMonitor,
		Help:   "batch allocate monitor",
		Labels: []string{"masking_product_id", "product_id", "scene", "field", "field_value"},
	}
	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	InitServiceTaskMonitor()
	InitScheduleMonitor()

	InitCheckoutFulfillmentProductMonitor()
	InitDeductVolumeCountMonitor()

	return nil
}

func InitAdminMetrics() error {
	opt := metrics.CounterOpts{
		Name:   constant.PrometheusMetricAdmin,
		Help:   "report admin error code",
		Labels: []string{"url", "retcode"},
	}
	err := metrics.CreateCounter(opt)
	if err != nil {
		logger.LogErrorf("init metrics:%v, failed, err:%v", constant.PrometheusMetricAdmin, err)
	}

	gaugeOpt := metrics.GaugeOpts{
		Name:   constant.MetricBatchAllocateMonitor,
		Help:   "batch allocate monitor",
		Labels: []string{"masking_product_id", "product_id", "scene", "field", "field_value"},
	}
	err = metrics.CreateGauge(gaugeOpt)
	if err != nil {
		logger.LogErrorf("metrics.CreateCounter error", err)
		return err
	}

	return err
}

// TODO
func InitBATaskMetrics() error {
	InitBatchAllocateMetric()

	return nil
}
