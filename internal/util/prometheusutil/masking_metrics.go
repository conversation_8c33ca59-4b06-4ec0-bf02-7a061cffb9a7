package prometheusutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"strconv"
)

// done ,
const (
	AllocateMonitorShopId                    = "MaskProduct_shop_id"
	AllocateMonitorRuleId                    = "MaskProduct_rule_id"
	AllocateMonitorPriority                  = "MaskProduct_priority"
	AllocateMonitorFProductId                = "MaskProduct_fulfillment_product_id"
	AllocateMonitorSchedule                  = "MaskProduct_schedule"
	AllocateMonitorScheduleFactor            = "MaskProduct_schedule_factor"
	AllocateMonitorScheduleNum               = "MaskProduct_schedule_num"
	AllocateMonitorShippingFeeTotal          = "MaskProduct_shipping_fee_total"
	AllocateMonitorShippingFeeProductTotal   = "MaskProduct_shipping_fee_product_total"
	AllocateMonitorShippingFeeProductSuccess = "MaskProduct_shipping_fee_product_success"
	AllocateMonitorShippingFeeSuccess        = "MaskProduct_shipping_fee_success"
	AllocateMonitorEstimateProductMonitor    = "MaskProduct_estimate_fulfillment_product_id"
	AllocateWhitelist                        = "MaskProduct_whitelist"
	AllocateMaskingProductVolumeMonitor      = "MaskProduct_volume"
	AllocateFulfillmentProductVolumeMonitor  = "FulfillmentProduct_volume"
)

func InitMaskingMonitor() {
	cm := map[string][]string{
		AllocateMonitorShopId:                    []string{"mask_product_id", "shop_group_id"},
		AllocateMonitorRuleId:                    []string{"mask_product_id", "rule_id"},
		AllocateMonitorPriority:                  []string{"mask_product_id", "priority_type"}, // types
		AllocateMonitorFProductId:                []string{"mask_product_id", "fulfillment_product_id"},
		AllocateMonitorSchedule:                  []string{"mask_product_id", "fulfillment_product_id", "schedule"},
		AllocateMonitorScheduleFactor:            []string{"mask_product_id", "schedule_factor"},
		AllocateMonitorScheduleNum:               []string{"mask_product_id"},
		AllocateMonitorShippingFeeTotal:          []string{"mask_product_id"},
		AllocateMonitorShippingFeeProductTotal:   []string{"mask_product_id", "fulfillment_product_id"},
		AllocateMonitorShippingFeeProductSuccess: []string{"mask_product_id", "fulfillment_product_id"},
		AllocateMonitorShippingFeeSuccess:        []string{"mask_product_id"},
		AllocateMonitorEstimateProductMonitor:    []string{"mask_product_id", "fulfillment_product_id"},
		AllocateWhitelist:                        []string{"mask_product_id", "field", "field_value"},
	}

	for k, v := range cm {
		maskLabel := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(maskLabel)
		if err != nil {
			logger.LogErrorf("init AllocateMonitor metrics failed %+v", err)

		}
	}
}

func UnifiedMaskingMonitorReport(res *pb.Allocate, maskId int64, priority *entity.ProductPriority, schedule string) {
	//counters ,
	if res == nil {
		return
	}

	_ = CounterIncr(AllocateMonitorRuleId, map[string]string{
		"mask_product_id": strconv.FormatInt(maskId, 10),
		"rule_id":         strconv.FormatInt(res.GetRuleId(), 10),
	})

	_ = CounterIncr(AllocateMonitorShopId, map[string]string{
		"mask_product_id": strconv.FormatInt(maskId, 10),
		"shop_group_id":   strconv.FormatInt(res.GetShopGroupId(), 10),
	})

	if priority != nil {

		_ = CounterIncr(AllocateMonitorPriority, map[string]string{
			"mask_product_id": strconv.FormatInt(maskId, 10),
			"priority_type":   strconv.Itoa(int(priority.RuleType)),
		})
	}

	_ = CounterIncr(AllocateMonitorFProductId, map[string]string{
		"mask_product_id":        strconv.FormatInt(maskId, 10),
		"fulfillment_product_id": strconv.FormatInt(res.GetProductId(), 10),
	})

	// 上报调度因子调度结果
	if schedule != "" {
		_ = CounterIncr(AllocateMonitorSchedule, map[string]string{
			"mask_product_id":        strconv.FormatInt(maskId, 10),
			"fulfillment_product_id": strconv.FormatInt(res.GetProductId(), 10),
			"schedule":               schedule,
		})
	}
}

func MaskingScheduleFactorReport(maskProductId int64, scheduleFactor []string) {
	if len(scheduleFactor) < 1 {
		return
	}
	_ = CounterIncr(AllocateMonitorScheduleNum, map[string]string{
		"mask_product_id": strconv.FormatInt(maskProductId, 10),
	})
	for _, factor := range scheduleFactor {
		_ = CounterIncr(AllocateMonitorScheduleFactor, map[string]string{
			"mask_product_id": strconv.FormatInt(maskProductId, 10),
			"schedule_factor": factor,
		})
	}
}

func MaskingShippingFeeReportProductTotal(maskProductId int64, fulfillmentProductIds []int64) {
	_ = CounterIncr(AllocateMonitorShippingFeeTotal, map[string]string{
		"mask_product_id": strconv.FormatInt(maskProductId, 10),
	})
	for _, fulfillmentProductId := range fulfillmentProductIds {
		_ = CounterIncr(AllocateMonitorShippingFeeProductTotal, map[string]string{
			"mask_product_id":        strconv.FormatInt(maskProductId, 10),
			"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
		})
	}
}

func MaskingShippingFeeReportProductSuccess(maskProductId int64, fulfillmentProductId int64) {
	_ = CounterIncr(AllocateMonitorShippingFeeProductSuccess, map[string]string{
		"mask_product_id":        strconv.FormatInt(maskProductId, 10),
		"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
	})
}

func MaskingShippingFeeReportSuccess(maskProductId int64) {
	_ = CounterIncr(AllocateMonitorShippingFeeSuccess, map[string]string{
		"mask_product_id": strconv.FormatInt(maskProductId, 10),
	})
}

func EstimateOrderRateMonitorReport(maskId int64, fulfillmentPId int64) {
	_ = CounterIncr(AllocateMonitorEstimateProductMonitor, map[string]string{
		"mask_product_id":        strconv.FormatInt(maskId, 10),
		"fulfillment_product_id": strconv.FormatInt(fulfillmentPId, 10),
	})
}

func MaskingWhitelistReport(maskProductId, field, fieldValue string) {
	_ = CounterIncr(AllocateWhitelist, map[string]string{
		"mask_product_id": maskProductId,
		"field":           field,
		"field_value":     fieldValue,
	})
}

func ReportMaskingProductVolume(ctx context.Context, maskProductId int64, volume int64) {
	_ = GaugeSet(ctx, AllocateMaskingProductVolumeMonitor, float64(volume), map[string]string{
		"mask_product_id": strconv.FormatInt(maskProductId, 10),
	})
}

func ReportFulfillmentProductVolume(ctx context.Context, maskProductId int64, fulfillmentProductId int64, orderType string, volume int64) {
	_ = GaugeSet(ctx, AllocateFulfillmentProductVolumeMonitor, float64(volume), map[string]string{
		"mask_product_id":        strconv.FormatInt(maskProductId, 10),
		"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
		"order_type":             orderType,
	})
}
