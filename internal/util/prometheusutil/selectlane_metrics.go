package prometheusutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"strconv"
)

const (
	SelectLaneProductOrder = "product_order"
	SelectLaneLaneOrder    = "product_lane_order"
	SelectLaneRule         = "product_rule"
	SelectLaneSchedule     = "product_schedule"
	SelectLaneScene        = "product_scene"
)

func InitSelectLaneMonitor() {
	cm := map[string][]string{
		SelectLaneProductOrder: []string{"product_id"},
		SelectLaneLaneOrder:    []string{"product_id", "lane"},
		SelectLaneRule:         []string{"product_id", "rule_id"}, // types
		SelectLaneSchedule:     []string{"product_id", "lane", "schedule"},
		SelectLaneScene:        []string{"product_id", "lane", "scene"},
	}

	for k, v := range cm {
		maskLabel := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(maskLabel)
		if err != nil {
			logger.LogErrorf("init AllocateMonitor metrics failed %+v", err)

		}
	}
}

func UnifiedSelectLaneReport(productId, ruleId int, laneCode string, schedule string, scene string) {
	//ctx-reports,

	_ = CounterIncr(SelectLaneProductOrder, map[string]string{
		"product_id": strconv.Itoa(productId),
	})

	_ = CounterIncr(SelectLaneLaneOrder, map[string]string{
		"product_id": strconv.Itoa(productId),
		"lane":       laneCode,
	})

	_ = CounterIncr(SelectLaneRule, map[string]string{
		"product_id": strconv.Itoa(productId),
		"rule_id":    strconv.Itoa(ruleId),
	})

	_ = CounterIncr(SelectLaneSchedule, map[string]string{
		"product_id": strconv.Itoa(productId),
		"lane":       laneCode,
		"schedule":   schedule,
	})

	_ = CounterIncr(SelectLaneScene, map[string]string{
		"product_id": strconv.Itoa(productId),
		"lane":       laneCode,
		"scene":      scene,
	})

}

func ReportSmartRoutingSuccess(productId int, ruleId int64, status string) {
	metricsLabel := map[string]string{
		"product_id": strconv.Itoa(productId),
		"rule_id":    strconv.Itoa(int(ruleId)),
		"status":     status,
	}
	_ = CounterIncr(constant.MetricSmartRoutingSuccess, metricsLabel)
}

func ReportProductCount(ctx context.Context, productId int, count int) {
	// 上报prometheus
	var labels = map[string]string{
		"product_id": strconv.Itoa(productId),
	}
	_ = GaugeSet(ctx, constant.MetricSmartRoutingProductCounter, float64(count), labels)
}

func ReportLaneCount(ctx context.Context, productId int, laneCode string, count int) {
	// 上报prometheus
	var labels = map[string]string{
		"product_id": strconv.Itoa(productId),
		"lane_code":  laneCode,
	}
	_ = GaugeSet(ctx, constant.MetricSmartRoutingLaneCounter, float64(count), labels)
}

func ReportLineCount(ctx context.Context, productId int, laneCode string, resourceId string, count int) {
	// 上报prometheus
	var labels = map[string]string{
		"product_id":  strconv.Itoa(productId),
		"lane_code":   laneCode,
		"resource_id": resourceId,
	}
	_ = GaugeSet(ctx, constant.MetricSmartRoutingResourceCounter, float64(count), labels)
}

func ReportActualPointCount(ctx context.Context, productId int, laneCode, actualPoint string, actualPointType int, count float64) {
	var labels = map[string]string{
		"product_id":        strconv.Itoa(productId),
		"lane_code":         laneCode,
		"actual_point_type": strconv.Itoa(actualPointType),
		"actual_point":      actualPoint,
	}
	_ = GaugeSet(ctx, constant.MetricSmartRoutingActualPointCounter, count, labels)
}

func ReportLineActualPointCount(ctx context.Context, productId int, laneCode, lineId, actualPoint string, actualPointType int, count float64) {
	var labels = map[string]string{
		"product_id":        strconv.Itoa(productId),
		"lane_code":         laneCode,
		"line_id":           lineId,
		"actual_point_type": strconv.Itoa(actualPointType),
		"actual_point":      actualPoint,
	}
	_ = GaugeSet(ctx, constant.MetricSmartRoutingLineActualPointCounter, count, labels)
}
