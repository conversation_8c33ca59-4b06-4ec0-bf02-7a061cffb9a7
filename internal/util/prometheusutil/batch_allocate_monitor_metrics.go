package prometheusutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"strconv"
)

// 针对订单批次状态及其数量进行上报，可以实时观察到当前有多少个Batch没调度
func ReportBatches(ctx context.Context, maskingProductId, productId uint64, batchStatus int, count float64) {
	var labels = map[string]string{
		"masking_product_id": strconv.FormatUint(maskingProductId, 10),
		"product_id":         strconv.FormatUint(productId, 10),
		"scene":              "batches",
		"field":              "batch_status",
		"field_value":        strconv.Itoa(batchStatus),
	}
	_ = GaugeSet(ctx, constant.MetricBatchAllocateMonitor, count, labels)
}

// 针对masking product上报不同状态的订单数，实时查看积压订单数
func ReportStatus(ctx context.Context, maskingProductId, productId uint64, batchStatus int, count float64) {
	var labels = map[string]string{
		"masking_product_id": strconv.FormatUint(maskingProductId, 10),
		"product_id":         strconv.FormatUint(productId, 10),
		"scene":              "Status",
		"field":              "batch_status",
		"field_value":        strconv.Itoa(batchStatus),
	}
	_ = GaugeSet(ctx, constant.MetricBatchAllocateMonitor, count, labels)
}
