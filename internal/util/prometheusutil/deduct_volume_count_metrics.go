package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

const (
	MetricDeductVolumeCountSystem   = "DeductVolumeCountSystem"
	MetricDeductVolumeCountBusiness = "DeductVolumeCountBusiness"
)

const (
	FulfillmentProductNotFound       = "FulfillmentProductNotFound"
	GenerateDeductVolumeRequestError = "GenerateDeductVolumeRequestError"
	DeductVolumeError                = "DeductVolumeError"
)

func InitDeductVolumeCountMonitor() {
	cm := map[string][]string{
		MetricDeductVolumeCountSystem:   []string{"staging", "event"},
		MetricDeductVolumeCountBusiness: []string{"fulfillment_product_id"},
	}

	for k, v := range cm {
		counterOpts := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}
		err := metrics.CreateCounter(counterOpts)
		if err != nil {
			logger.LogErrorf("create metrics counter error, err=%v", err)
		}
	}
}

func ReportDeductVolumeCountEvent(staging string, event string) {
	_ = CounterIncr(MetricDeductVolumeCountSystem, map[string]string{
		"staging": staging,
		"event":   event,
	})
}

func ReportDeductVolumeCountBusiness(fulfillmentProductId int64) {
	_ = CounterIncr(MetricDeductVolumeCountBusiness, map[string]string{
		"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
	})
}
