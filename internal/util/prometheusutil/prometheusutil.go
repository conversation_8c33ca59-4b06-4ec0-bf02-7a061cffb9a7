package prometheusutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

func PrometheusReportHistogram(ctx context.Context, name string, label map[string]string, addValue float64) error {
	if !configutil.GetPrometheusReportConfig(ctx).Switch {
		return nil
	}
	return CounterAdd(name, label, addValue)
}

func CounterIncr(name string, label map[string]string) error {
	return metrics.CounterIncr(name, label)
}

func GaugeSet(ctx context.Context, name string, val float64, labels map[string]string) error {
	return metrics.GaugeSet(name, val, labels)
}

func CounterAdd(name string, label map[string]string, val float64) error {
	// val负数会panic
	if val < 0 {
		val = 0
	}
	return metrics.CounterAdd(name, val, label)
}

func HistogramObserve(name string, val float64, labels map[string]string) error {
	return metrics.HistogramObserve(name, val, labels)
}
