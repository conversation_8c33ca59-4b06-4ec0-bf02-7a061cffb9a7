package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

const (
	MetricCheckoutFulfillmentCounterSystem   = "CheckoutFulfillmentCounterSystem"
	MetricCheckoutFulfillmentCounterBusiness = "CheckoutFulfillmentCounterBusiness"
)

const (
	// 消息处理阶段
	MessageProcess = "MessageProcess"

	// 消息处理event
	TotalMsgNumber               = "TotalMsgNumber"
	MsgTextUnmarshalError        = "MsgTextUnmarshalError"
	PayloadUnmarshalError        = "PayloadUnmarshalError"
	PayloadNewDataUnmarshalError = "PayloadNewDataUnmarshalError"
	PayloadOldDataUnmarshalError = "PayloadOldDataUnmarshalError"
	NoReadyLogisticStatus        = "NoReadyLogisticStatus"
	ExtInfoUnmarshalError        = "ExtInfoUnmarshalError"
	BuyerAddressIsNil            = "BuyerAddressIsNil"
	UnOpenSwitch                 = "UnOpenSwitch"
	ExecuteMsgError              = "ExecuteMsgError"
	ExecuteMsgSuccess            = "ExecuteMsgSuccess"

	// 业务处理阶段
	BusinessProcess = "BusinessProcess"

	// 业务处理event
	TotalBusinessProcessNumber       = "TotalBusinessProcessNumber"
	GetOrderIdFromRedisError         = "GetOrderIdFromRedisError"
	DuplicateOrder                   = "DuplicateOrder"
	NonTodayOrder                    = "NonTodayOrder"
	NonCheckoutFulfillmentProduct    = "NonCheckoutFulfillmentProduct"
	GenerateUpdateVolumeRequestError = "GenerateUpdateVolumeRequestError"
	MultiMaskingProductNum           = "MultiMaskingProductNum"
	UpdateVolumeError                = "UpdateVolumeError"
	RecordOrderIdToRedisError        = "RecordOrderIdToRedisError"
	ExecuteBusinessSuccess           = "ExecuteBusinessSuccess"

	// 参数补全阶段
	CompleteParameter = "CompleteParameter"

	// 参数补全event
	TotalCompleteParameterNumber = "TotalCompleteParameterNumber"
	GetOrderInfoError            = "GetOrderInfoError"
	GetItemInfoError             = "GetItemInfoError"
	GetParcelInfoError           = "GetParcelInfoError"
	GetLocationIdsError          = "GetLocationIdsError"
	GetDgError                   = "GetDgError"
	CompleteSuccess              = "CompleteSuccess"
)

func InitCheckoutFulfillmentProductMonitor() {
	cm := map[string][]string{
		MetricCheckoutFulfillmentCounterSystem:   []string{"staging", "event"},
		MetricCheckoutFulfillmentCounterBusiness: []string{"fulfillment_product_id"},
	}

	for k, v := range cm {
		counterOpts := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}
		err := metrics.CreateCounter(counterOpts)
		if err != nil {
			logger.LogErrorf("create metrics counter error, err=%v", err)
		}
	}
}

func ReportCheckoutFulfillmentProductEvent(staging string, event string) {
	_ = CounterIncr(MetricCheckoutFulfillmentCounterSystem, map[string]string{
		"staging": staging,
		"event":   event,
	})
}

func ReportCheckoutFulfillmentCounterBusiness(fulfillmentProductId int64) {
	_ = CounterIncr(MetricCheckoutFulfillmentCounterBusiness, map[string]string{
		"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
	})
}
