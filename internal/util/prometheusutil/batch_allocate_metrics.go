package prometheusutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	counterName            = "counter"
	gaugeName              = "gauge"
	histogramName          = "histogram"
	BatchAllocateTotalName = "batch_allocate"
)

// scene
const (
	SplitBatch = "split_batch"
)

// field
const (
	MaskNotExist      = "mask_not_exist"
	RuleNotExist      = "rule_not_exist"
	FixByTimeNotExist = "fix_by_time_not_exist"
	LastBatchNotExist = "last_batch_not_exist"
	NextOrderNotExist = "next_order_not_exist"
	ExceedMaxQuantity = "exceed_max_quantity"
	InsertBatchError  = "insert_batch_error"
)

var (
	// DefaultLabels
	// 1. mask product贯穿allocate的整个过程；scene标识不同的场景，field表示该场景下某块领域,field value记录该域下具体的字段
	// 2. e.g. mask product=8003， scene=split_batch_order, field=batch_id, field_value=1(即batch id = 1)
	DefaultLabels = []string{"mask_product_id", "scene", "field", "field_value"}
)

func InitBatchAllocateMetric() {
	InitBatchAllocateCounter()
	InitBatchAllocateGauge()
	InitBatchAllocateHistogram()
}

func GetBatchAllocateCounterName() string {
	return BatchAllocateTotalName + "_" + counterName
}

func GetBatchAllocateGaugeName() string {
	return BatchAllocateTotalName + "_" + gaugeName
}

func GetBatchAllocateHistogramName() string {
	return BatchAllocateTotalName + "_" + histogramName
}

func InitBatchAllocateCounter() {
	opts := metrics.CounterOpts{
		Name:   GetBatchAllocateCounterName(),
		Help:   "",
		Labels: DefaultLabels,
	}

	err := metrics.CreateCounter(opts)
	if err != nil {
		logger.LogErrorf("InitBatchAllocateCounter failed %+v", err)
	}
}

func InitBatchAllocateGauge() {
	opts := metrics.GaugeOpts{
		Name:   GetBatchAllocateGaugeName(),
		Help:   "",
		Labels: DefaultLabels,
	}

	err := metrics.CreateGauge(opts)
	if err != nil {
		logger.LogErrorf("InitBatchAllocateGauge failed %+v", err)
	}
}

func InitBatchAllocateHistogram() {
	opts := metrics.HistogramOpts{
		Name:   GetBatchAllocateHistogramName(),
		Help:   "",
		Labels: DefaultLabels,
	}

	err := metrics.CreateHistogram(opts)
	if err != nil {
		logger.LogErrorf("InitBatchAllocateHistogram failed %+v", err)
	}
}

type CounterInfo struct {
	MaskProductID string
	Scene         string
	Field         string
	FieldValue    string
	ReportValue   float64
}

func IncrBatchAllocateCounter(info CounterInfo) {
	reportMap := map[string]string{
		"mask_product_id": info.MaskProductID,
		"scene":           info.Scene,
		"field":           info.Field,
		"field_value":     info.FieldValue,
	}
	_ = CounterIncr(GetBatchAllocateCounterName(), reportMap)
}

func AddBatchAllocateCounter(info CounterInfo) {
	reportMap := map[string]string{
		"mask_product_id": info.MaskProductID,
		"scene":           info.Scene,
		"field":           info.Field,
		"field_value":     info.FieldValue,
	}
	_ = CounterAdd(GetBatchAllocateCounterName(), reportMap, info.ReportValue)
}

// SetBatchAllocateGauge 可以设置负数，不会panic
func SetBatchAllocateGauge(ctx context.Context, info CounterInfo) {
	reportMap := map[string]string{
		"mask_product_id": info.MaskProductID,
		"scene":           info.Scene,
		"field":           info.Field,
		"field_value":     info.FieldValue,
	}
	_ = GaugeSet(ctx, GetBatchAllocateGaugeName(), info.ReportValue, reportMap)
}

func ObserveBatchAllocateHistogram(info CounterInfo, val float64) {
	reportMap := map[string]string{
		"mask_product_id": info.MaskProductID,
		"scene":           info.Scene,
		"field":           info.Field,
		"field_value":     info.FieldValue,
	}
	_ = HistogramObserve(GetBatchAllocateHistogramName(), val, reportMap)
}
