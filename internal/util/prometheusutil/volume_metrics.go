package prometheusutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/metrics"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

const (
	MaskingVolumeSceneMonitor = "MaskingVolumeSceneMonitor"
	RoutingVolumeSceneMonitor = "RoutingVolumeSceneMonitor"
	AllocateVolumeCounter     = "AllocateVolumeCounter"
)

// interface name
const (
	UpdateVolumeInterface    = "UpdateVolume"
	AdjustCapacityInterface  = "AdjustCapacity"
	AdjustLocVolumeInterface = "AdjustLocVolume"
	IncrVolumeInterface      = "IncrVolume"
)

func InitVolumeMonitor() {
	cm := map[string][]string{
		MaskingVolumeSceneMonitor: {"interface_name", "scene", "masking_product_id", "fulfillment_product_id", "operator_type"},
		RoutingVolumeSceneMonitor: {"interface_name", "scene", "product_id", "operator_type"},
		AllocateVolumeCounter:     {"url", "masking_product_id", "fulfillment_product_id", "order_type", "operator_type", "volume_scene"},
	}

	for k, v := range cm {
		volumeLabel := metrics.CounterOpts{
			Name:   k,
			Help:   "",
			Labels: v,
		}

		err := metrics.CreateCounter(volumeLabel)
		if err != nil {
			logger.LogErrorf("init VolumeMonitor metrics failed %+v", err)

		}
	}
}

func ReportMaskingVolumeSceneMonitor(interfaceName string, scene string, maskingProductId int64, fulfillmentProductId int64, operatorType string) {
	_ = CounterIncr(MaskingVolumeSceneMonitor, map[string]string{
		"interface_name":         interfaceName,
		"scene":                  scene,
		"masking_product_id":     strconv.FormatInt(maskingProductId, 10),
		"fulfillment_product_id": strconv.FormatInt(fulfillmentProductId, 10),
		"operator_type":          operatorType,
	})
}

func ReportRoutingVolumeSceneMonitor(interfaceName string, scene string, productId int64, operatorType string) {
	_ = CounterIncr(RoutingVolumeSceneMonitor, map[string]string{
		"interface_name": interfaceName,
		"scene":          scene,
		"product_id":     strconv.FormatInt(productId, 10),
		"operator_type":  operatorType,
	})
}

func ReportAllocateVolumeCounter(url, maskingProductId, fulfillmentProductId, orderType, operatorType, volumeScene string) {
	_ = CounterIncr(AllocateVolumeCounter, map[string]string{
		"url":                    url,
		"masking_product_id":     maskingProductId,
		"fulfillment_product_id": fulfillmentProductId,
		"order_type":             orderType,
		"operator_type":          operatorType,
		"volume_scene":           volumeScene,
	})
}
