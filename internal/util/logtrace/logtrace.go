package logtrace

import (
	"context"
	"sync/atomic"
)

type (
	traceNumCtxKey  string
	traceInfoCtxKey string

	TraceEntity struct {
		LogTrace  uint32 `json:"log_trace"`
		Name      string `json:"name"`
		FromCache bool   `json:"-"`
		StartTime string `json:"start_time"`
		Cost      string `json:"cost"`
		System    string `json:"system"`
		Request   string `json:"request"`
		Response  string `json:"response"`
		Error     string `json:"error"`
		ExtraData string `json:"extra_data"`
	}

	ProductExtraData struct {
		ProductId string `json:"product_id"`
		Status    string `json:"status"`
		Message   string `json:"message"`
	}
)

const (
	//EasyLevel    logLevel        = 0
	//DetailLevel  logLevel        = 1
	traceNumKey  traceNumCtxKey  = "log_trace_num"
	traceInfoKey traceInfoCtxKey = "log_trace_info"
)

func GetLogTraceNum(ctx context.Context) *uint32 {
	var tmpLogOrder uint32
	if logOrderObj := ctx.Value(traceNumKey); logOrderObj != nil {
		if logOrder, ok := logOrderObj.(*uint32); ok {
			return logOrder
		}
	}
	return &tmpLogOrder
}

func SetLogTraceNum(ctx context.Context) (context.Context, uint32) {
	// TODO::SSCSMR-517
	//url := common.FormatUrl(envvar.GetRequestUrl(ctx))
	//urlConfig := config.GetLogTrace().UrlConfig
	//if _, ok := urlConfig[url]; !ok {
	//	return ctx, 0
	//}
	logTrace := GetLogTraceNum(ctx)
	newLogTrace := atomic.AddUint32(logTrace, 1)
	newCtx := context.WithValue(ctx, traceNumKey, &newLogTrace)

	return newCtx, newLogTrace
}

func GetLogTraceInfo(ctx context.Context) *[]TraceEntity {
	tmpLogTraceInfo := make([]TraceEntity, 0)
	if logTraceInfo := ctx.Value(traceInfoKey); logTraceInfo != nil {
		if logTraceObj, ok := logTraceInfo.(*[]TraceEntity); ok {
			return logTraceObj
		}
	}

	return &tmpLogTraceInfo
}

func SetLogTraceInfo(ctx context.Context, logInfo TraceEntity) context.Context {
	logTraceInfo := GetLogTraceInfo(ctx)
	if logTraceInfo == nil {
		tmpLogTraceInfo := make([]TraceEntity, 0)
		logTraceInfo = &tmpLogTraceInfo
	}
	*logTraceInfo = append(*logTraceInfo, logInfo)

	newCtx := context.WithValue(ctx, traceInfoKey, logTraceInfo)

	return newCtx
}
