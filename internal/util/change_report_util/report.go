package change_report_util

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
)

func ChangeReportByTask(ctx context.Context, dataId uint64, startTime int64, level1Scene, level2Scene, extraBusinessDetail string, riskLevel report_constant.RiskLevel) {
	cid := envvar.GetCID()
	endTime := recorder.Now(ctx).Unix()
	change_report.ReportTimedTaskChange(ctx, dataId, cid, startTime, endTime, level1Scene, level2Scene, extraBusinessDetail, riskLevel)
}
