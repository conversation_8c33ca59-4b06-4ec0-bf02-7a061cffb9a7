package volumeutil

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

func IsCBForecast(routingType int, isForecastType bool) bool {
	return routingType == rule.CBRoutingType && isForecastType
}

func IsLocalSpxRouting(routingType int) bool {
	return routingType == rule.SPXRoutingType || routingType == rule.LocalRoutingType
}

func IsOpenVolumeRouting(ctx context.Context) bool {
	return configutil.GetVolumeRoutingRegionToggleConf(ctx)
}
