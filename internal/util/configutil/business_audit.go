package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	jsoniter "github.com/json-iterator/go"
	"strings"
)

const BusinessAuditKey = "business_audit"

type BusinessAuditConf struct {
	NeedAudit              bool   `yaml:"need_audit"`
	AuditAssigneeMapStr    string `yaml:"audit_assignee_map_str"`
	Host                   string `yaml:"host"`
	Timeout                int64  `yaml:"timeout"`
	XOpaToken              string `yaml:"XOpaToken"` //用于跟审批中心对接的token
	PermissionCodeMapStr   string `yaml:"permission_code_map_str"`
	ZoneChangeRatio        int    `yaml:"zone_change_ratio"`
	ZoneChangeOriginValue  int    `yaml:"zone_change_origin_value"`
	ZoneProductChangeRatio int    `yaml:"zone_product_change_ratio"`

	// yaml:"-"的放最后面，避免打断反序列化
	AuditAssigneeMap  map[string]string `yaml:"-"`
	PermissionCodeMap map[string]string `yaml:"-"`
}

func refreshBusinessAuditConf() BusinessAuditConf {
	var conf BusinessAuditConf
	if err := config.UnmarshalConfig("config."+BusinessAuditKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh business_audit fail, err:%+v", err)
	}

	auditAssigneeMap := make(map[string]string, 0)
	if err := jsoniter.UnmarshalFromString(conf.AuditAssigneeMapStr, &auditAssigneeMap); err != nil {
		logger.LogErrorf("unmarshal audit assignee map err:%v", err)
	}
	conf.AuditAssigneeMap = auditAssigneeMap

	permissionCodeMap := make(map[string]string, 0)
	if err := jsoniter.UnmarshalFromString(conf.PermissionCodeMapStr, &permissionCodeMap); err != nil {
		logger.LogErrorf("unmarshal permission code map err:%v", err)
	}
	conf.PermissionCodeMap = permissionCodeMap

	return conf
}

func GetBusinessAuditConf(ctx context.Context) BusinessAuditConf {
	c := BusinessAuditConf{}

	if GetGlobalConfig(ctx) != nil {
		c = GetGlobalConfig(ctx).BusinessAuditConf
	}
	if c.Timeout == 0 {
		c.Timeout = 6 //默认6s
	}
	if c.Host == "" {
		c.Host = hostUrlMap[strings.ToLower(envvar.GetEnv())]
	}
	return c
}

var (
	hostUrlMap = map[string]string{
		"test":    "https://ops.ssc.test.shopeemobile.com",    // nolint
		"uat":     "https://ops.ssc.uat.shopeemobile.com",     // nolint
		"staging": "https://ops.ssc.staging.shopeemobile.com", // nolint
		"live":    "https://ops.ssc.shopeemobile.com",         // nolint
	}
)
