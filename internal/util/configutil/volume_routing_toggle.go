package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const volumeRoutingToggle = "volume_routing_region_toggle"

func refreshVolumeRoutingRegionToggleConf() bool {
	var conf bool
	if err := config.UnmarshalConfig(configPrefix+volumeRoutingToggle, &conf); err != nil { // nolint
		logger.LogErrorf("refresh VolumeRoutingRegionToggleConf fail, err:%v", err)
	}
	return conf
}

func GetVolumeRoutingRegionToggleConf(ctx context.Context) bool {
	return GetGlobalConfig(ctx).VolumeRoutingRegionToggleConf
}
