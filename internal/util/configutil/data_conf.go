package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const dataConfKey = "data_conf"

type DataConf struct {
	Host                                string `yaml:"host"`
	MaskingResultListEndPoint           string `yaml:"maskingResultListEndPoint"`
	MaskingAllocationPathDetailEndPoint string `yaml:"maskingAllocationPathDetailEndPoint"`
	MaskingAllocationPathListEndPoint   string `yaml:"maskingAllocationPathListEndPoint"`
	MaskingAllocationAccount            string `yaml:"maskingAllocationAccount"`
	MaskingAllocationSecret             string `yaml:"maskingAllocationSecret"`
	RoutingLogListEndPoint              string `yaml:"routingLogListEndPoint"`
	RoutingLogDetailEndPoint            string `yaml:"routingLogDetailEndPoint"`
	RoutingLogAccount                   string `yaml:"routingLogAccount"`
	RoutingLogSecret                    string `yaml:"routingLogSecret"`
}

func refreshDataConf() DataConf {
	var conf DataConf
	if err := config.UnmarshalConfig(configPrefix+dataConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh data api config fail, err:%+v", err)
	}
	return conf
}

func GetDataConf(ctx context.Context) DataConf {
	return GetGlobalConfig(ctx).DataConf
}
