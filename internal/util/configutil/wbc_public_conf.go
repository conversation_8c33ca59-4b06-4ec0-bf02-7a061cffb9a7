package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/event"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strings"
)

const wbcPublicPrefix = "wbc_public"

var wbcPublicConf *WbcPublicConf

type WbcPublicConf struct {
	traceSdkConf TraceSdkConf
	HookMap      map[string]func() error
}

type TraceSdkConf struct {
	FulfilmentEventTrace    FulfilmentEventTrace `yaml:"fulfilment_event_trace"`
	FulfilmentEventTraceUs3 FulfilmentEventTrace `yaml:"fulfilment_event_trace_us3"`
}

type EventRouter struct {
	TargetCID      string `yaml:"target_cid"`
	SmrEnableTrace uint8  `yaml:"smr_enable_trace"`
}

type FulfilmentEventTrace struct {
	EnvCidRouter   map[string]EventRouter `yaml:"env_cid_router"`
	AclKafkaConfig struct {
		Brokers      string `yaml:"brokers"`
		User         string `yaml:"user"`
		Password     string `yaml:"password"`
		Topic        string `yaml:"topic"`
		PartitionNum int32  `yaml:"partition_num"`
	} `yaml:"kafka_config"`

	SmrFilterConfig struct {
		ExcludeRegions string `yaml:"exclude_regions"`
	} `yaml:"smr_filter_config"`
}

func refreshTraceSdkConf() TraceSdkConf {
	var conf TraceSdkConf
	if err := config.UnmarshalConfig(wbcPublicPrefix, &conf); err != nil { // nolint
		logger.LogErrorf("refresh WbcPublicConf fail, err:%+v", err)
	}
	for _, hook := range wbcPublicConf.HookMap {
		if err := hook(); err != nil {
			logger.CtxLogErrorf(context.TODO(), "wbc_public hook err=%v", err)
		}
	}
	return conf
}

func GetTraceSdkConf() TraceSdkConf {
	return wbcPublicConf.traceSdkConf
}

func GetWbcPublicConf() *WbcPublicConf {
	if wbcPublicConf != nil {
		return wbcPublicConf
	}
	return &WbcPublicConf{}
}

func (w *WbcPublicConf) RegisterHook(key string, fn func() error) {
	if w.HookMap == nil {
		w.HookMap = make(map[string]func() error)
	}
	w.HookMap[key] = fn
}

func (w *WbcPublicConf) ConfPrefix() string {
	return wbcPublicPrefix
}

func (w *WbcPublicConf) Event(events []*event.Event) {
	for _, e := range events {
		segments := strings.Split(e.Key, ".")
		if len(segments) < 2 {
			continue
		}
		switch segments[0] {
		case wbcPublicPrefix:
			wbcPublicConf.traceSdkConf = refreshTraceSdkConf()
		}
	}
}
