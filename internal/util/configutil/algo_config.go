package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const AlgoConfigKey = "algo_config"

type AlgoConfig struct {
	LocalSearchSwitch bool `yaml:"local_search_switch"` // 是否开启localSearch算法，true：开启，false：关闭。localSearch算法会对初始解进行优化，但比较耗时，故可以关闭
	LocalSearchTime   int  `yaml:"local_search_time"`   // localSearch算法最大运行时长，只有开启了localSearch才会生效，表示超过该时长localSearch就会停止，单位：ms（毫秒）
	EarlyStopIter     int  `yaml:"early_stop_iter"`     // localSearch算法早停迭代次数，表示算法迭代了多少次之后结果还没变优则停止算法，提前结束算法。只有开启了localSearch才会生效
}

func refreshAlgoConfig() AlgoConfig {
	var conf AlgoConfig
	if err := config.UnmarshalConfig(configPrefix+AlgoConfigKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh algo config fail, err:%v", err)
	}
	return conf
}

func GetAlgoConfig() AlgoConfig {
	return gConf.AlgoConfig
}
