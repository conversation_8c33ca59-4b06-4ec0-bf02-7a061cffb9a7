package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	MaskVolumeCheckKey = "volume_check"
	defaultLimit       = 1000
)

type MaskVolumeCheckConf struct {
	AllowCheck bool `yaml:"allow_check"`
	ListLimit  int  `yaml:"list_limit"`
}

func refreshMaskVolumeCheckConf() MaskVolumeCheckConf {
	c := MaskVolumeCheckConf{}
	if err := config.UnmarshalConfig(configPrefix+MaskVolumeCheckKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", MaskVolumeCheckKey)
		return c
	}
	if c.ListLimit == 0 {
		c.ListLimit = defaultLimit
	}
	return c
}

func GetMaskVolumeCheckConf(ctx context.Context) MaskVolumeCheckConf {
	maskVolumeCheckConf := MaskVolumeCheckConf{}
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).MaskVolumeCheckConf
	}

	return maskVolumeCheckConf
}
