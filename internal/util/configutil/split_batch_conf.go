package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const splitBatchConfKey = "split_batch_conf"

type SplitBatchConf struct {
	DefaultMaxQuantity int64 `json:"default_max_quantity"`
}

func refreshSplitBatchConf() SplitBatchConf {
	var conf SplitBatchConf
	if err := config.UnmarshalConfig(configPrefix+splitBatchConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh 'split batch' config fail, err:%v", err)
	}

	return conf
}

func GetSplitBatchConf(ctx context.Context) SplitBatchConf {
	return GetGlobalConfig(ctx).SplitBatchConf
}
