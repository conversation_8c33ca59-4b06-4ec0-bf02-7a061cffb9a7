package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

const wmsToggleConfKey = "wms_toggle"

type WmsToggleConf struct {
	WhiteListYaml string `yaml:"white_list"`
	WhiteList     []int  `yaml:"-"`
}

func refreshWmsToggleConf() WmsToggleConf {
	var conf WmsToggleConf
	if err := config.UnmarshalConfig(configPrefix+wmsToggleConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh wms toggle fail, err:%+v", err)
	}
	if err := jsoniter.UnmarshalFromString(conf.WhiteListYaml, &conf.WhiteList); err != nil {
		logger.LogErrorf("unmarshall wms toggle's white list fail, err:%+v", err)
	}

	return conf
}

func GetWmsToggleConf(ctx context.Context) WmsToggleConf {
	return GetGlobalConfig(ctx).WmsToggleConf
}

func GetProductIsWmsToggleEnable(ctx context.Context, productId int) bool {
	if objutil.ContainInt(GetWmsToggleConf(ctx).WhiteList, productId) {
		logger.CtxLogInfof(ctx, "product enable wms toggle, product id = %d", productId)
		return true
	}

	return false
}
