package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const (
	lpsGrpcKey      = "lps_grpc"
	lfsGrpcKey      = "lfs_grpc"
	llsGrpcKey      = "lls_grpc"
	locationGrpcKey = "location_grpc"
	chargeGrpcKey   = "charge_grpc"
	lcosGrpcKey     = "lcos_grpc"
)

type (
	GrpcConf struct {
		ServiceName      string                   `yaml:"service_name"`
		Account          string                   `yaml:"account"`
		Token            string                   `yaml:"token"`
		DefaultTimeout   int64                    `yaml:"default_timeout"`
		TimeoutConfigStr string                   `yaml:"timeout_config"` // Json格式，需注意timeout value的单位是ms，如：{"BatchAllocationShippingFeeCalculate":5000}
		TimeoutConfig    map[string]time.Duration `yaml:"-"`              // key: grpc operation id, value: timeout value
	}
)

func refreshGrpcConf(serviceKey string) GrpcConf {
	var conf GrpcConf
	if err := config.UnmarshalConfig(configPrefix+serviceKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh service grpc config fail, serviceKey:%s, err:%+v", serviceKey, err)
	}

	conf.TimeoutConfig = make(map[string]time.Duration)
	// unmarshall timeout config
	if conf.TimeoutConfigStr != "" {
		timeoutMap := make(map[string]int)
		if err := jsoniter.UnmarshalFromString(conf.TimeoutConfigStr, &timeoutMap); err != nil {
			logger.LogErrorf("unmarshall timeout config fail, config: %s, err: %+v", conf.TimeoutConfigStr, err)
		}
		for api, timeout := range timeoutMap {
			conf.TimeoutConfig[api] = time.Duration(timeout) * time.Millisecond
		}
	}

	return conf
}

func GetLpsGrpcConf(ctx context.Context) GrpcConf {
	return GetGlobalConfig(ctx).LpsGrpcConf
}

func GetLfsGrpcConf(ctx context.Context) GrpcConf {
	return GetGlobalConfig(ctx).LfsGrpcConf
}

func GetLlsGrpcConf(ctx context.Context) GrpcConf {
	return GetGlobalConfig(ctx).LlsGrpcConf
}

func GetLocationGrpcConf(ctx context.Context) GrpcConf {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).LocationGrpcConf
	}
	return GrpcConf{}
}

func GetChargeGrpcConf(ctx context.Context) GrpcConf {
	return GetGlobalConfig(ctx).ChargeGrpcConf
}

func GetLcosGrpcConf(ctx context.Context) GrpcConf {
	return GetGlobalConfig(ctx).LcosGrpcConf
}
