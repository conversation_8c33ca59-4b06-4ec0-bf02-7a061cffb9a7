package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strconv"
)

// config in namespace : grpc_server
const vncbSwitch = "$CHASSIS.bypass_vncb"

func BypassVncb(ctx context.Context) bool {
	return config.GetIntWithContext(ctx, vncbSwitch, 0) == 1
}

const (
	preVnCbToggleCheckConfig = "config.pre-vncb-toggle-check"
	preVnCbToggleCheckKey    = "pre-vncb-toggle-check"
)

func refreshPreVnCbToggleCheckConfig() bool {
	preVnCbToggleCheckStr := config.GetString(preVnCbToggleCheckConfig, "")
	preVnCbToggleCheck, err := strconv.ParseBool(preVnCbToggleCheckStr)
	if err != nil {
		logger.LogErrorf("parse pre vncb toggle check error, err=%v", err)
		return false
	}
	return preVnCbToggleCheck
}

func IsOpenPreVnCbToggleCheck(ctx context.Context) bool {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).PreVnCbToggleCheck
	}
	return false
}
