package configutil

import (
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const slsOpsS3ConfKey = "sls_ops_conf"

type SlsOpsS3Conf struct {
	Host            string `yaml:"host"`
	Access          string `yaml:"access"`
	Secret          string `yaml:"secret"`
	BucketMapString string `yaml:"bucket_map_string"`
	BucketMap       map[string]string
}

func refreshSlsOpsS3Conf() SlsOpsS3Conf {
	var conf SlsOpsS3Conf
	if err := config.UnmarshalConfig(configPrefix+slsOpsS3ConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh sls_ops s3 config fail, err:%v", err)
	}

	//unmarshal bucket map
	bucketMap := make(map[string]string, 0)
	if err := json.Unmarshal([]byte(conf.BucketMapString), &bucketMap); err != nil {
		logger.LogErrorf("refresh sls_ops s3 config fail, unmarshal bucket map string err:%v", err)
	} else {
		conf.BucketMap = bucketMap
	}

	return conf
}

func GetSlsOpsS3Conf() SlsOpsS3Conf {
	return GetGlobalConfigWithoutCtx().SlsOpsS3Conf
}
