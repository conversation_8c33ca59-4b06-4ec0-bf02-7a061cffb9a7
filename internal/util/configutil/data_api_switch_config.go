package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const dataApiSwitchConfKey = "data_api_switch_conf"

type DataApiSwitchConf struct {
	QueryOrderCountSwitch      bool `yaml:"query_order_count_switch"`
	OrderAggregationSwitch     bool `yaml:"order_aggregation_switch"`
	ReadOrderFromDataApiSwitch bool `yaml:"read_order_from_data_api_switch"`
	ReadOrderPageCount         int  `yaml:"read_order_page_count"`
}

func refreshDataApiSwitchConf() DataApiSwitchConf {
	var conf DataApiSwitchConf
	if err := config.UnmarshalConfig(configPrefix+dataApiSwitchConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh data api config fail, err:%+v", err)
	}
	return conf
}

func GetDataApiSwitchConf(ctx context.Context) DataApiSwitchConf {
	return GetGlobalConfig(ctx).DataApiSwitchConf
}
