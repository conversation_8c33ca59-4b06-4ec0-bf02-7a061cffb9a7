package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const postCodeMaxUpload = "post_code_max_upload"
const defaultMaxRow = 30000

type PostCodeMaxUploadConf struct {
	MaxRow int `yaml:"max_row"`
}

func GetPostCodeMaxUploadConf() PostCodeMaxUploadConf {
	if gConf != nil {
		return gConf.PostCodeMaxUploadConf
	}
	return PostCodeMaxUploadConf{}
}

func refreshPostCodeMaxUploadConf() PostCodeMaxUploadConf {
	c := PostCodeMaxUploadConf{}
	if err := config.UnmarshalConfig(configPrefix+postCodeMaxUpload, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", postCodeMaxUpload)
		return c
	}
	if c.MaxRow == 0 {
		c.MaxRow = defaultMaxRow
	}
	logger.LogDebugf("get config refreshPostCodeMaxUploadConf | PostCodeMaxUploadConf=%+v", c)
	return c
}
