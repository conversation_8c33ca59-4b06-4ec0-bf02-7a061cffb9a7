package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

//	"git.garena.com/shopee/bg-logistics/go/chassis/config"

const wmsRuleGray = "$CHASSIS.wms-rule-ready"
const (
	WmsCfgKey = "config.wms_product_allocation"
)

func IsWmsRuleReady(ctx context.Context) bool {
	return config.GetBoolWithContext(ctx, wmsRuleGray, false)
}

func RefreshWmsProductAllocationConfig() ProductAllocationConf {
	c := ProductAllocationConf{}
	if err := config.UnmarshalConfig(WmsCfgKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", WmsCfgKey)
		return c
	}
	//Logger.LogDebugf("get config RefreshProductAllocationConfig | productAllocationConfig=%+v", c)
	return c
}
