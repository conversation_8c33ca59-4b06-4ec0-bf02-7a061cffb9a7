package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const jwtKey = "jwt"

type JWTConf struct {
	Issuer       string   `yaml:"issuer"`
	Optr         string   `yaml:"optr"`
	Secret       string   `yaml:"secret"`
	ExpiresIn    int      `yaml:"expires_in"`
	Closed       bool     `yaml:"closed"`
	NeedJwtPaths []string `yaml:"need_jwt_paths"`
}

func refreshJWTConf() JWTConf {
	var conf JWTConf
	if err := config.UnmarshalConfig(configPrefix+jwtKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh jwt config fail, err:%+v", err)
	}
	return conf
}

func GetJWTConf(ctx context.Context) JWTConf {
	return GetGlobalConfig(ctx).JwtConf
}
