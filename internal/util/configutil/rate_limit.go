package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	rateLimitKey         = "ratelimitkey"
	HardCriteriaLimitKey = "hard_criteria_limit_key"
)

const (
	defaultHardCriteriaLimitSize = 1000
)

type RateLimitConfig struct {
	LimitSize map[string]int `yaml:"limit_size"`
}

func refreshRateLimit() RateLimitConfig {
	var conf RateLimitConfig
	if err := config.UnmarshalConfig(configPrefix+rateLimitKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh rate limit config fail, err:%+v", err)
	}
	return conf
}

func GetRateLimitConfig(ctx context.Context) RateLimitConfig {
	var conf RateLimitConfig
	if GetGlobalConfig(ctx) != nil {
		conf = GetGlobalConfig(ctx).RateLimitConfig
	}
	checkUseDefaultConfig(&conf)
	return conf
}

func checkUseDefaultConfig(conf *RateLimitConfig) {
	if conf == nil {
		return
	}
	if conf.LimitSize[HardCriteriaLimitKey] == 0 {
		conf.LimitSize[HardCriteriaLimitKey] = defaultHardCriteriaLimitSize
	}
}
