package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	HardCriteriaTaskKey       = "hard_criteria_task"
	forbidCheckDonePendingKey = "forbid_check_done_pending"
)

type HardCriteriaTaskConfig struct {
	Qps             uint64 `yaml:"Qps"`
	OrderCount      int64  `yaml:"OrderCount"`
	PendingNum      int    `yaml:"PendingNum"`
	CreateYesterday int    `yaml:"CreateYesterday"` //用来控制创建昨天的任务时的时间
	CheckTTL        int64  `yaml:"CheckTTL"`        //心跳检查的健康时间
	CheckDoneTTL    int64  `yaml:"CheckDoneTTL"`    //检查done状态的健康时间
	BatchNum        int    `yaml:"BatchNum"`        //单个携程最大可处理订单数
	UseNewRedis     bool   `yaml:"UseNewRedis"`     //控制admin接口读取单量时用哪个redis
}

func GetHardCriteriaTaskConfig(ctx context.Context) HardCriteriaTaskConfig {
	var hardCriteriaTaskConfig HardCriteriaTaskConfig
	if GetGlobalConfig(ctx) != nil {
		hardCriteriaTaskConfig = GetGlobalConfig(ctx).HardCriteriaTaskConfig
	}
	logger.LogDebugf("get config GetHardCriteriaTaskConfig | HardCriteriaTaskConfig=%+v", hardCriteriaTaskConfig)
	return hardCriteriaTaskConfig
}

func RefreshHardCriteriaTaskConfig() HardCriteriaTaskConfig {
	c := HardCriteriaTaskConfig{}
	if err := config.UnmarshalConfig(configPrefix+HardCriteriaTaskKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", HardCriteriaTaskKey)
		return c
	}
	logger.LogDebugf("get config RefreshHardCriteriaTaskConfigConfig | HardCriteriaTaskConfig=%v", c)
	return c
}

func GetForbidCheckDonePending(ctx context.Context) bool {
	var forbidCheckDonePending bool
	if GetGlobalConfig(ctx) != nil {
		forbidCheckDonePending = GetGlobalConfig(ctx).ForbidCheckDonePending
	}
	logger.LogDebugf("get config GetForbidCheckDonePending | ForbidCheckDonePending=%v", forbidCheckDonePending)
	return forbidCheckDonePending
}

func refreshForbidCheckDonePending() bool {
	var c bool
	if err := config.UnmarshalConfig(configPrefix+forbidCheckDonePendingKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", forbidCheckDonePendingKey)
		return c
	}
	logger.LogDebugf("get config refreshForbidCheckDonePending | refreshForbidCheckDonePending=%v", c)
	return c
}
