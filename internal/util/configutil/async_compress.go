package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const asyncCompressKey = "async_compress"

type AsyncCompressConf struct {
	AllowRequest bool `yaml:"allow_request"`
}

func RefreshAsyncCompressConf() AsyncCompressConf {
	var conf AsyncCompressConf
	if err := config.UnmarshalConfig(configPrefix+asyncCompressKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh async compress config fail, key:%s, err:%+v", asyncCompressKey, err)
	}
	return conf
}

func GetAsyncCompressConf(ctx context.Context) AsyncCompressConf {
	var conf AsyncCompressConf
	if GetGlobalConfig(ctx) != nil {
		conf = GetGlobalConfig(ctx).AsyncCompressConf
	}
	logger.LogDebugf("get config AsyncCompressConf=%+v", conf)
	return conf
}
