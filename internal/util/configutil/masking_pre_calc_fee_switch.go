package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

const (
	maskingPreCalcFeeConfig = "config.masking-pre-calc-fee-switch"
	maskingPreCalcFeeKey    = "masking-pre-calc-fee-switch"
)

// MaskingPreCalcFeeSwitch
/** 开关配置格式
masking-pre-calc-fee-switch:
{
    "global_switch": true,     // 全局开关，true：下面的masking_switch配置才会生效，false：全局关闭提前计算运费，下面的配置会失效
    "masking_switch": [8003]   // 配置mask_product_id列表，配置了则表示该mask_product会提前计算运费
}
*/
type MaskingPreCalcFeeSwitch struct {
	GlobalSwitch  bool    `json:"global_switch"`
	MaskingSwitch []int64 `json:"masking_switch"`
}

func refreshMaskingPreCalcFeeSwitch() MaskingPreCalcFeeSwitch {
	maskingPreCalcFeeSwitch := MaskingPreCalcFeeSwitch{}
	maskingPreCalcFeeSwitchStr := config.GetString(maskingPreCalcFeeConfig, "") // nolint
	if err := jsoniter.UnmarshalFromString(maskingPreCalcFeeSwitchStr, &maskingPreCalcFeeSwitch); err != nil {
		logger.LogErrorf("refreshMaskCalculateShippingFee error | error is: %v", err)
		return maskingPreCalcFeeSwitch
	}
	logger.LogDebugf("refreshMaskCalculateShippingFee success | maskingPreCalcFeeSwitch=%v", maskingPreCalcFeeSwitch)
	return maskingPreCalcFeeSwitch
}

func GetMaskingPreCalcFeeSwitch(ctx context.Context) MaskingPreCalcFeeSwitch {
	var shippingFeeSwitch MaskingPreCalcFeeSwitch
	if GetGlobalConfig(ctx) != nil {
		shippingFeeSwitch = GetGlobalConfig(ctx).MaskingPreCalcFeeSwitch
	}
	return shippingFeeSwitch
}

// IsOpenMaskingPreCalcFeeSwitch 判断提前计算运费开关是否开启
func IsOpenMaskingPreCalcFeeSwitch(ctx context.Context, maskProductId int64) bool {
	maskSwitch := GetMaskingPreCalcFeeSwitch(ctx)
	// 总开关是否开启
	if maskSwitch.GlobalSwitch {
		// mask product是否配置
		if objutil.ContainInt64(maskSwitch.MaskingSwitch, maskProductId) {
			return true
		}
	}
	return false
}
