package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const lruCacheConfKey = "lru_cache"

type (
	LruCacheConf struct {
		Conf  string                   `yaml:"config"` // LRU缓存公共配置
		Param map[string]LruCacheParam // 用于保存config反序列化的内容
	}

	LruCacheParam struct {
		Timeout int64 `json:"timeout"` // LRU缓存过期时间，此为数值，默认单位：秒（s）
		Size    int   `json:"size"`    // LRU缓存长度
		Enable  bool  `json:"enable"`
	}
)

func refreshLruCacheConf() LruCacheConf {
	c := LruCacheConf{}

	if err := config.UnmarshalConfig(configPrefix+lruCacheConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", lruCacheConfKey)
		return c
	}

	// 没配置直接返回
	if c.Conf == "" {
		return c
	}
	param := make(map[string]LruCacheParam)
	err := jsoniter.UnmarshalFromString(c.Conf, &param)
	if err != nil {
		logger.LogErrorf("UnmarshalFromString failed|config=%s", c)
		return c
	}
	c.Param = param

	return c
}

func GetLruCacheConf(ctx context.Context) LruCacheConf {
	c := LruCacheConf{}

	if GetGlobalConfig(ctx) != nil {
		c = GetGlobalConfig(ctx).LruCacheConf
	}

	return c
}
