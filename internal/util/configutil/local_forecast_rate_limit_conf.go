package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	DefaultConcurrentNum = 1000
	localRateLimitKey    = "local_forecast_limit"
)

var defaultRateLimit = LocalForecastRateLimit{DefaultConcurrentNum}

type LocalForecastRateLimit struct {
	ConcurrentNum int `yaml:"concurrent_num"`
}

func refreshLocalForecastRateLimitConfig() LocalForecastRateLimit {
	var c LocalForecastRateLimit
	if err := config.UnmarshalConfig(configPrefix+localRateLimitKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", localRateLimitKey)
		return c
	}
	if c.ConcurrentNum == 0 {
		c.ConcurrentNum = DefaultConcurrentNum
	}
	logger.LogDebugf("get config RefreshFteRpcConfig | antPoolKey=%+v", c)
	return c
}

func GetLocalForecastRateLimitConfig(ctx context.Context) LocalForecastRateLimit {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).LocalForecastRateLimit
	}
	return defaultRateLimit
}
