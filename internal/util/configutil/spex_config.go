package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const (
	SpexConfigKey  = "SpexConfig"
	SpexCommandKey = "SpexCommandConfig"

	DefaultSpexCommandTimeout = 2000 * time.Millisecond
)

type SpexConfig struct {
	ServiceName string `yaml:"ServiceName"`
	ConfigKey   string `yaml:"ConfigKey"`
}

func refreshSpexConfig() SpexConfig {
	var conf SpexConfig
	if err := config.UnmarshalConfig(configPrefix+SpexConfigKey, &conf); err != nil {
		logger.LogErrorf("refresh spex config fail, err:%v", err)
	}
	return conf
}

func GetSpexConfig() SpexConfig {
	if gConf != nil {
		return gConf.SpexConfig
	}
	return SpexConfig{}
}

type SpexCommandConfig struct {
	SpexCommandTimeout map[string]int `json:"spex_command_timeout"`
}

func refreshSpexCommandConfig() SpexCommandConfig {
	var spexCommandConfig SpexCommandConfig
	if err := jsoniter.UnmarshalFromString(config.GetString(configPrefix+SpexCommandKey, ""), &spexCommandConfig); err != nil {
		logger.LogErrorf("unmarshal spex command config error|err=%v", err)
		return spexCommandConfig
	}
	return spexCommandConfig
}

func GetSpexCommandConfig(ctx context.Context, command string) time.Duration {
	tempGlobalConfig := GetGlobalConfig(ctx)
	if tempGlobalConfig == nil {
		return DefaultSpexCommandTimeout
	}
	if timeout, ok := tempGlobalConfig.SpexCommandConfig.SpexCommandTimeout[command]; ok {
		return time.Duration(timeout) * time.Millisecond
	}
	return DefaultSpexCommandTimeout
}
