package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const batchAllocateForecastKey = "batch_allocate_forecast"

type (
	BatchAllocateForecastConf struct {
		MaxLoopSize   int    `yaml:"max_loop_size"`
		RateLimit     int    `yaml:"rate_limit"`   //只跑软性情况下，控制令牌桶每秒生成多少个bucket
		RateBurst     int    `yaml:"rate_burst"`   //控制令牌桶每秒可以消费多少个bucket,也对应worker数
		ChannelSize   int    `yaml:"channel_size"` //控制生产者channel大小
		AntsPoolSize  int    `yaml:"ants_pool_size"`
		MaxBlockSize  int    `yaml:"max_block_size"`
		UsDollarRatio int    `yaml:"us_dollar_ratio"` // id转dollar的比例
		PickupEffSLA  int64  `yaml:"pickup_eff_sla"`  // Pickup Efficiency 调用SDK AIS的时间
		PickupEffUrl  string `yaml:"pickup_eff_url"`
	}
)

func refreshBatchAllocateForecastConf() BatchAllocateForecastConf {
	var conf BatchAllocateForecastConf
	if err := config.UnmarshalConfig("config."+batchAllocateForecastKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh batch allocate forecast config fail, key:%s, err:%v", batchAllocateForecastKey, err)
	}
	if conf.MaxLoopSize == 0 {
		conf.MaxLoopSize = 1000000
	}
	if conf.RateLimit == 0 {
		conf.RateLimit = 2000 //默认每秒生成2000个bucket
	}
	if conf.RateBurst == 0 {
		conf.RateBurst = 16 //默认最大并发数量为16
	}
	if conf.ChannelSize == 0 {
		conf.ChannelSize = 4 //默认同时缓存4批订单
	}
	if conf.AntsPoolSize == 0 {
		conf.AntsPoolSize = 12
	}
	if conf.MaxBlockSize == 0 {
		conf.MaxBlockSize = 1000
	}
	if conf.UsDollarRatio == 0 {
		conf.UsDollarRatio = 15412
	}
	if conf.PickupEffSLA == 0 {
		conf.PickupEffSLA = 60
	}

	return conf
}

func GetBatchAllocateForecastConf() BatchAllocateForecastConf {
	return gConf.BatchAllocateForecastConf
}
