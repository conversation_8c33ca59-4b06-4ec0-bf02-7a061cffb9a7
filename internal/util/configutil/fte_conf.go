package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	fteRpcConfigKey  = "fte_rpc"
	fteHttpConfigKey = "fte_http"
)

func GetFteRpcConf(ctx context.Context) string {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).FteRpcConf
	}
	return ""
}

func refreshFteRpcConf() string {
	var c string
	if err := config.UnmarshalConfig(configPrefix+fteRpcConfigKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", fteRpcConfigKey)
		return c
	}
	logger.LogDebugf("get config RefreshFteRpcConfig | fteRpcConfig=%+v", c)
	return c
}

func GetFteHttpConf(ctx context.Context) string {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).FteHttpConf
	}
	return ""
}

func refreshHttpConf() string {
	var c string
	if err := config.UnmarshalConfig(configPrefix+fteHttpConfigKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", fteHttpConfigKey)
		return c
	}
	logger.LogDebugf("get config RefreshFteHttpConfig | fteHttpConfig=%+v", c)
	return c
}
