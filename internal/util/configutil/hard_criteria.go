package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const RefreshHardCriteriaByTaskGoroutine = "config.hard_criteria_task.GoroutineNum"

func GetRefreshHardCriteriaByTaskGoroutineConfig(ctx context.Context) int {
	var refreshHardCriteriaByTaskGoroutine int
	if GetGlobalConfig(ctx) != nil {
		refreshHardCriteriaByTaskGoroutine = GetGlobalConfig(ctx).RefreshHardCriteriaByTaskGoroutine
	}
	logger.LogDebugf("get config GetRefreshHardCriteriaByTaskGoroutine | RefreshHardCriteriaByTaskGoroutine=%d", refreshHardCriteriaByTaskGoroutine)
	return refreshHardCriteriaByTaskGoroutine
}

func RefreshRefreshHardCriteriaByTaskGoroutineConfig() int {
	c := config.GetInt(RefreshHardCriteriaByTaskGoroutine, 4) // nolint
	logger.LogDebugf("get config RefreshRefreshHardCriteriaByTaskGoroutine | RefreshHardCriteriaByTaskGoroutine=%d", c)
	return c
}
