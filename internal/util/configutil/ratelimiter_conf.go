package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/ratelimiter"
	jsoniter "github.com/json-iterator/go"
)

const (
	rateLimiterKey = "rate_limiter"
)

type (
	RateLimiter struct {
		Http       string `yaml:"http"`
		Grpc       string `yaml:"grpc"`
		Spex       string `yaml:"spex"`
		LayerCache string `yaml:"layer_cache"`
		Kafka      string `yaml:"kafka"`
		Mysql      string `yaml:"mysql"`
	}
)

func refreshRateLimiterConf() RateLimiter {
	c := RateLimiter{}
	if err := config.UnmarshalConfig(configPrefix+rateLimiterKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s, err=%v", rateLimiterKey, err)
		return c
	}

	data, _ := jsoniter.Marshal(&c)
	m := make(map[string]string)
	err := jsoniter.Unmarshal(data, &m)
	if err != nil {
		logger.LogErrorf("Unmarshal config to map failed|key=%s, err=%v", rateLimiterKey, err)
		return c
	}

	ratelimiter.LoadRateLimitConfig(m)

	return c
}

func GetRateLimiterConf(ctx context.Context) RateLimiter {
	if GetGlobalConfig(ctx) == nil {
		return RateLimiter{}
	}
	return GetGlobalConfig(ctx).RateLimiterConf
}
