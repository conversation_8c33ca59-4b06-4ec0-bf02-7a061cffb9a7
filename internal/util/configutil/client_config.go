package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"strings"
)

type OauthToken string

const (
	ClientTokenSuffix = ".token"
	unknownToken      = "unknown"

	// TODO : Could add other info belongs to client. Like "xx_client.timeout" with ".time" as Suffix Key
)

const (
	//NinjavanServiceClient  = "ninjavan_service_client"
	//LnpServiceClient       = "lnp_service_client"
	//FreightServiceClient   = "freight_service_client"
	//LcosServiceClient      = "lcos_service_client"
	LfsServiceClient = "lfs_service_client"
	//RateAdminClient        = "rate_admin_service_client"
)

func refreshClientToken(key string, clientTokenConfig map[string]OauthToken) map[string]OauthToken {
	// init if no exist
	if len(clientTokenConfig) == 0 {
		clientTokenConfig = make(map[string]OauthToken)
	}
	// get config
	token := config.GetString(key, unknownToken) // nolint
	if token == unknownToken {
		logger.LogErrorf("get client token=%v", token)
	} else {
		logger.LogDebugf("get client token=%v", token)
	}
	clientTokenConfig[key] = OauthToken(token)
	logger.LogDebugf("ClientTokenConfig=%v", clientTokenConfig)
	return clientTokenConfig
}

func getClientToken(ctx context.Context) map[string]OauthToken {
	if GetGlobalConfig(ctx) != nil {
		if len(GetGlobalConfig(ctx).ClientTokenConfig) != 0 {
			return GetGlobalConfig(ctx).ClientTokenConfig
		} else {
			return make(map[string]OauthToken)
		}
	}
	return make(map[string]OauthToken)
}

func GetTokenByClientName(ctx context.Context, clientName string) string {
	clientTokenMapper := getClientToken(ctx)
	if len(clientTokenMapper) == 0 {
		logger.CtxLogErrorf(ctx, "get token=%v by clientName=%v", unknownToken, clientName)
		return unknownToken
	}
	token := string(clientTokenMapper[mergeString(configPrefix, clientName, ClientTokenSuffix)])
	if token == unknownToken {
		logger.CtxLogErrorf(ctx, "get token=%v by clientName=%v", token, clientName)
	} else {
		logger.CtxLogDebugf(ctx, "get token=%v by clientName=%v", token, clientName)
	}
	return token
}

func mergeString(a ...string) string {
	if len(a) == 0 {
		return ""
	}
	var builder strings.Builder
	for _, data := range a {
		builder.WriteString(data)
	}
	return builder.String()
}
