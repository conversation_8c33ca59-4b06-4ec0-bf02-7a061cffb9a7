package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	// config key
	batchAllocateConfigKey = "batch_allocate"

	// default value
	defaultBatchAllocateMaxRetryTime        = 3
	defaultBatchAllocateSLA                 = 60
	defaultBatchAllocatePushResultBatchSize = 1000
	defaultTableRetentionDays               = 7
	defaultPushResultConcurrentNum          = 8
	defaultPickupEffStatsExpireDays         = 7
	defaultPickupEffSLA                     = 60
)

type BatchAllocateConfig struct {
	MaskProductTableMappingStr string `yaml:"mask_product_table_mapping"` // MaskProductID到Hold单表分表的映射关系表
	MaxRetryTime               int    `yaml:"max_retry_time"`             // Batch最大重试次数
	SLA                        int64  `yaml:"sla"`                        // Batch执行SLA，单位：秒
	DisableDegradation         bool   `yaml:"disable_degradation"`        // 是否关闭Batch多次失败后退化成Single的逻辑，默认False，即打开退化
	PushResultBatchSize        int    `yaml:"push_result_batch_size"`     // 单次捞取DB推送订单结果的批次大小
	GreySwitchStr              string `yaml:"grey_switch"`                // 灰度切换开关
	TableRetentionDays         int    `yaml:"table_retention_days"`       // 数据表保留天数
	PushResultConcurrentNum    int    `yaml:"push_result_concurrent_num"` // 订单结果回推的并发数
	UsDollarRatio              int    `yaml:"us_dollar_ratio"`            // id转dollar的比例
	UsDollarLimiter            int    `yaml:"us_dollar_limiter"`
	MaskingNeedToMonitorStr    string `yaml:"masking_need_to_monitor_str"` //需要监控的masking product
	PickupEffStatsEnable       bool   `yaml:"pickup_eff_stats_enable"`
	PickupEffStatsExpireDays   int    `yaml:"pickup_eff_stats_expire_days"`
	PickupEffEnable            bool   `yaml:"pickup_eff_enable"` // Pickup Efficiency 因子开关
	PickupEffSLA               int64  `yaml:"pickup_eff_sla"`    // Pickup Efficiency 因子调用SDK AIS的时间，单位S
	PickupEffUrl               string `yaml:"pickup_eff_url"`

	// 不用反序列化的放到最后，避免影响正常的反序列化流程
	MaskProductTableMapping map[int]int                     `yaml:"-"`
	GreySwitchMap           map[int]BatchAllocateGreySwitch `yaml:"-"`
	MaskingNeedToMonitor    []uint64                        `yaml:"-"`
}

type BatchAllocateGreySwitch struct {
	GreyType   int `json:"grey_type"`
	Count      int `json:"count"`
	Percentage int `json:"percentage"` // 万分比
}

func refreshBatchAllocateConf() BatchAllocateConfig {
	// 拉取Apollo配置

	var conf BatchAllocateConfig
	if err := config.UnmarshalConfig(configPrefix+batchAllocateConfigKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh batch allocate config fail, key:%s, err:%v", batchAllocateConfigKey, err)
	}

	// Unmarshall具体字段，以及默认值赋值
	maskTableMapping := make(map[int]int, 0)
	if err := jsoniter.UnmarshalFromString(conf.MaskProductTableMappingStr, &maskTableMapping); err != nil {
		logger.LogErrorf("unmarshall mask product table mapping config failed |  config=%s, err:%v", conf.MaskProductTableMappingStr, err)
	}
	conf.MaskProductTableMapping = maskTableMapping

	if err := jsoniter.UnmarshalFromString(conf.GreySwitchStr, &conf.GreySwitchMap); err != nil {
		logger.LogErrorf("unmarshall grey switch failed |config=%s, err:%v", conf.GreySwitchStr, err)
	}

	if conf.SLA == 0 {
		conf.SLA = defaultBatchAllocateSLA
	}

	if conf.MaxRetryTime == 0 {
		conf.MaxRetryTime = defaultBatchAllocateMaxRetryTime
	}

	if conf.PushResultBatchSize == 0 {
		conf.PushResultBatchSize = defaultBatchAllocatePushResultBatchSize
	}

	if conf.TableRetentionDays == 0 {
		conf.TableRetentionDays = defaultTableRetentionDays
	}

	if conf.PushResultConcurrentNum == 0 {
		conf.PushResultConcurrentNum = defaultPushResultConcurrentNum
	}

	if conf.PickupEffStatsExpireDays == 0 {
		conf.PickupEffStatsExpireDays = defaultPickupEffStatsExpireDays
	}

	if conf.PickupEffSLA == 0 {
		conf.PickupEffSLA = defaultPickupEffSLA
	}

	if err := jsoniter.UnmarshalFromString(conf.MaskingNeedToMonitorStr, &conf.MaskingNeedToMonitor); err != nil {
		logger.LogErrorf("unmarshall masking need to monitor failed |config=%s, err:%v", conf.MaskingNeedToMonitorStr, err)
	}

	return conf
}

func GetBatchAllocateConf() BatchAllocateConfig {
	return gConf.BatchAllocateConf
}
