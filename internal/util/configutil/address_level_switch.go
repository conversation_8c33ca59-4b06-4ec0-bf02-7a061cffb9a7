package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	AddressLevelUpSwitch    = "config.address_level_up"
	AddressLevelUpSwitchKey = "address_level_up"
)

// AddressLevelUpConfig 开关控制是否 使用新的地址层级与新地址接口，true: 调用旧接口，false：调用新接口
type AddressLevelUpConfig struct {
	Switch bool `yaml:"switch"`
}

func RefreshAddressLevelUpSwitch() AddressLevelUpConfig {
	c := AddressLevelUpConfig{}
	if err := config.UnmarshalConfig(AddressLevelUpSwitch, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", AddressLevelUpSwitchKey)
		return c
	}

	return c
}

// GetAddressLevelUpSwitch 是否使用新接口，true: 使用旧接口，false：使用新接口，默认返回false，即默认调新接口
func GetAddressLevelUpSwitch(ctx context.Context) bool {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AddressLevelUpConfig.Switch
	}
	return false
}
