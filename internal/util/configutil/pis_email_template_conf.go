package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	pisEmailTemplateConf    = "config.pis-email-template-conf"
	pisEmailTemplateConfKey = "pis-email-template-conf"
)

type PisEmailTemplateConf struct {
	TemplateId    string `json:"template_id"`
	SendingMethod int    `json:"sending_method"`
}

func refreshPisEmailTemplateConf() PisEmailTemplateConf {
	pisEmailTemplate := PisEmailTemplateConf{}
	pisEmailTemplateStr := config.GetString(pisEmailTemplateConf, "") // nolint
	err := jsoniter.UnmarshalFromString(pisEmailTemplateStr, &pisEmailTemplate)
	if err != nil {
		logger.LogErrorf("refreshPisEmailTemplateConf error:%v", err)
		return pisEmailTemplate
	}
	return pisEmailTemplate
}

func GetPisEmailTemplateConf(ctx context.Context) PisEmailTemplateConf {
	pisEmailTemplate := PisEmailTemplateConf{}
	if GetGlobalConfig(ctx) != nil {
		pisEmailTemplate = GetGlobalConfig(ctx).PisEmailTemplateConf
	}
	return pisEmailTemplate
}
