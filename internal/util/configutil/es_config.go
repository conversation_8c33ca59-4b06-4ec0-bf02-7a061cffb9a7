package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const esConfKey = "es_conf"

type EsConf struct {
	Host                string `yaml:"host"`
	Username            string `yaml:"username"`
	Password            string `yaml:"password"`
	AllocationPathIndex string `yaml:"allocationPathIndex"`
	RoutingPathIndex    string `yaml:"routingPathIndex"`
}

func refreshEsConf() EsConf {
	c := EsConf{}
	if err := config.UnmarshalConfig(configPrefix+esConfKey, &c); err != nil { // nolint
		logger.LogErrorf("refreshEsConf failed:%s, err:%+v", esConfKey, err)
	}
	return c
}

func GetEsConf(ctx context.Context) EsConf {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).EsConf
	}
	return EsConf{}
}
