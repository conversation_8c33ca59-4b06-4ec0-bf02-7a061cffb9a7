package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const redisKey = "redis"

type RedisConf struct {
	Addr         string `yaml:"addr"`
	DB           int    `yaml:"db"`
	DialTimeout  int    `yaml:"dial_timeout"`
	ReadTimeout  int    `yaml:"read_timeout"`
	WriteTimeout int    `yaml:"write_timeout"`
	PoolSize     int    `yaml:"pool_size"`
	PoolTimeout  int    `yaml:"pool_timeout"`
	Password     string `yaml:"password"`
	LivetestAddr string `yaml:"LivetestAddr"`
	LivetestPass string `yaml:"LivetestPass"`
}

func refreshRedisConf() RedisConf {
	var conf RedisConf
	if err := config.UnmarshalConfig(configPrefix+redisKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh redis config fail, err:%+v", err)
	}
	return conf
}

func GetRedisConf() RedisConf {
	return GetGlobalConfigWithoutCtx().RedisConf
}
