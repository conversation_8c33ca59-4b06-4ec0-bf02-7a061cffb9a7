package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	volumeSwitchtoNewSys = "config.volumefactor-switch-to-newservice"
	watchVolumeSwitchKey = "volumefactor-switch-to-newservice"
)

func WatchVolumeSwitch() map[string]int {
	// read,map-loadings-cks?
	str := config.GetString(volumeSwitchtoNewSys, "") // nolint
	param := map[string]int{}
	err := jsoniter.UnmarshalFromString(str, &param)
	if err != nil {
		logger.LogErrorf("refresh config failed|UnmarshalFromString fail|key=%s, config=%s", volumeSwitchtoNewSys, str)
	}
	logger.LogInfof("refresh config success|key=%s, config=%s", volumeSwitchtoNewSys, str)
	//
	return param
}

func GetVolumeSwitch(ctx context.Context) map[string]int {
	ret := map[string]int{}

	retval := config.GetStringWithContext(ctx, volumeSwitchtoNewSys, "")
	err := jsoniter.UnmarshalFromString(retval, &ret)
	if err != nil {
		logger.CtxLogErrorf(context.TODO(), "not.validjson %v %v", volumeSwitchtoNewSys, retval)
	}
	return ret
}
