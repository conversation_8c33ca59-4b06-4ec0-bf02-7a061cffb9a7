package configutil

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	lpsApiKey = "lps_api"
	lfsApiKey = "lfs_api"
	lnpApiKey = "lnp_api"
	aiApiKey  = "ai_api"
)

type ApiConf struct {
	Account string `yaml:"Account"`
	Host    string `yaml:"host"`
	Timeout int64  `yaml:"timeout"`
	Token   string `yaml:"token"`
}

func refreshApiConf(externalKey string) ApiConf {
	var conf ApiConf
	if err := config.UnmarshalConfig(configPrefix+externalKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh external api config fail, key:%s, err:%+v", externalKey, err)
	}
	return conf
}

func GetLpsApiConf(ctx context.Context) ApiConf {
	return GetGlobalConfig(ctx).LpsApiConf
}

func GetLfsApiConf(ctx context.Context) ApiConf {
	return GetGlobalConfig(ctx).LfsApiConf
}

func GetLnpApiConf(ctx context.Context) ApiConf {
	return GetGlobalConfig(ctx).LnpApiConf
}

func GetAiApiConf(ctx context.Context) ApiConf {
	if GetGlobalConfig(ctx) == nil {
		return ApiConf{}
	}
	return GetGlobalConfig(ctx).AiApiConf
}
