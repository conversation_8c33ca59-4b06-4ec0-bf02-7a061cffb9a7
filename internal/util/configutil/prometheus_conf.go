package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const PrometheusReportConfigKey = "config.prometheus_report"

type PrometheusReportConfig struct {
	Switch bool `yaml:"switch"` // prometheus上报开关，false:不上报 true:上报
}

func RefreshPrometheusReportConfig() PrometheusReportConfig {
	c := PrometheusReportConfig{}
	if err := config.UnmarshalConfig(PrometheusReportConfigKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", PrometheusReportConfigKey)
		c.Switch = true
		return c
	}
	return c
}

// GetPrometheusReportConfig 获取配置,从内存中获取
func GetPrometheusReportConfig(ctx context.Context) PrometheusReportConfig {
	var prometheusReportConfig PrometheusReportConfig
	if GetGlobalConfig(ctx) != nil {
		prometheusReportConfig = GetGlobalConfig(ctx).PrometheusReportConfig
	}

	return prometheusReportConfig
}
