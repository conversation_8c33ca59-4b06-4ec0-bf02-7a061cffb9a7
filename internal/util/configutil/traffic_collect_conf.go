package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	trafficCollectConf    = "config.traffic-collect"
	trafficCollectConfKey = "traffic-collect"
)

type TrafficCollectConfig struct {
	SwitchUseCollectInterface string          `yaml:"switch_use_collect_interface"` // 决定是否使用traffic collect的接口
	CollectType               string          `yaml:"collect_type"`                 // 使用collect方式，ALL收集所有容器、IP指定容器IP收集、CLOSE关闭收集 默认CLOSE
	CollectIps                string          `yaml:"collect_ips"`                  // 使用collect方式为IP时指定CollectIp容器IP才收集
	SwitchUseCollectMap       map[string]bool // 转为map类型
}

func refreshTrafficCollectConfig() TrafficCollectConfig {
	collectConfigConfig := TrafficCollectConfig{}
	if err := config.UnmarshalConfig(trafficCollectConf, &collectConfigConfig); err != nil { // nolint
		logger.LogErrorf("refreshTrafficCollectConfig|unmarshal traffic collect config error, err=%v", err)
		return collectConfigConfig
	}
	// 接口配置转换为map类型
	switchUseCollectMap := make(map[string]bool)
	if err := jsoniter.UnmarshalFromString(collectConfigConfig.SwitchUseCollectInterface, &switchUseCollectMap); err != nil {
		logger.LogErrorf("refreshTrafficCollectConfig|unmarshal traffic collect interface error, err=%v", err)
	}
	collectConfigConfig.SwitchUseCollectMap = switchUseCollectMap
	return collectConfigConfig
}

func GetTrafficCollectConfig(ctx context.Context) TrafficCollectConfig {
	collectConfigConfig := TrafficCollectConfig{}
	if GetGlobalConfig(ctx) != nil {
		collectConfigConfig = GetGlobalConfig(ctx).TrafficCollectConfig
	}
	// 初始化map，防止外部调用遗漏对map空指针的处理导致panic
	if collectConfigConfig.SwitchUseCollectMap == nil {
		collectConfigConfig.SwitchUseCollectMap = make(map[string]bool)
	}
	return collectConfigConfig
}
