package configutil

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

// default value
const (
	ilhForecastDefaultConcurrentNum = 16
)

// key
const (
	twsCutoffTimeWithTimezonePrefix = "tws_cutoff_time_with_timezone"
	ilhForecastKey                  = "ilh_forecast"
	revampIlhRoutingKey             = "revamp_ilh_routing"
)

type CutoffTimeConfig struct {
	CutoffTime     float64 `json:"cutoff_time"`
	TimezoneOffset int     `json:"timezone_offset"`
}

// GetCutoffTimePoint 计算根据CutoffTime和时区偏移计算对应的时间点
func (c CutoffTimeConfig) GetCutoffTimePoint(ctx context.Context) int64 {
	return timeutil.GetCutoffTimePoint(ctx, c.CutoffTime, c.TimezoneOffset)
}

// GetTimezone 获取时区对象
func (c CutoffTimeConfig) GetTimezone() *time.Location {
	return timeutil.GetTimezoneByOffset(c.TimezoneOffset)
}

type IlhForecastConfig struct {
	ConcurrentNum int `yaml:"concurrent_num"`
}

func refreshTwsCutoffTimeWithTimezone() map[string]CutoffTimeConfig {
	conf := make(map[string]CutoffTimeConfig)
	if err := jsoniter.UnmarshalFromString(config.GetString(configPrefix+twsCutoffTimeWithTimezonePrefix, ""), &conf); err != nil { // nolint
		logger.LogErrorf("refresh tws cutoff time fail, err:%+v", err)
	}

	return conf
}

func GetTwsCutoffTimeWithTimezone(ctx context.Context) map[string]CutoffTimeConfig {
	return GetGlobalConfig(ctx).TwsCutoffTimeWithTimezone
}

func refreshIlhForecastConfig() IlhForecastConfig {
	var conf IlhForecastConfig
	if err := config.UnmarshalConfig(configPrefix+ilhForecastKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh ilh forecast config fail, err:%+v", err)
	}

	if conf.ConcurrentNum == 0 {
		conf.ConcurrentNum = ilhForecastDefaultConcurrentNum
	}

	return conf
}

func GetIlhForecastConfig() IlhForecastConfig {
	return gConf.IlhForecastConfig
}

type RevampILHRoutingConfig struct {
	Enabled           bool   `yaml:"enabled"`         // 总开关
	ProductPercentStr string `yaml:"product_percent"` // ProductID -> 万分比值（0-10000）

	ProductPercent map[int64]int `yaml:"-"`
}

func refreshRevampILHRoutingConfig() RevampILHRoutingConfig {
	var conf RevampILHRoutingConfig
	if err := config.UnmarshalConfig(configPrefix+revampIlhRoutingKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh revamp ilh routing config failed, err:%+v", err)
	}

	if err := jsoniter.UnmarshalFromString(conf.ProductPercentStr, &conf.ProductPercent); err != nil {
		logger.LogErrorf("unmarshal product percent config failed, err:%+v", err)
	}

	// 确保万分比在有效范围内
	for productID, percent := range conf.ProductPercent {
		if percent < 0 {
			conf.ProductPercent[productID] = 0
		} else if percent > 10000 {
			conf.ProductPercent[productID] = 10000
		}
	}

	return conf
}

func GetRevampILHRoutingConfig(ctx context.Context) RevampILHRoutingConfig {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).RevampILHRoutingConfig
	}

	return RevampILHRoutingConfig{}
}

// IsRevampILHRoutingEnabled 检查指定ProductID的revamp ilh routing是否启用
func IsRevampILHRoutingEnabled(ctx context.Context, productID int64) bool {
	conf := GetRevampILHRoutingConfig(ctx)
	if conf.Enabled {
		return true // 总开关打开时，直接全部启用
	}

	// 总开关关闭时，才检查产品灰度配置
	percent, ok := conf.ProductPercent[productID]
	if !ok {
		return false
	}

	return recorder.RandIntn(ctx, 10000) < percent
}
