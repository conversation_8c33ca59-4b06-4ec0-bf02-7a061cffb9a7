package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const volumeRuleConfKey = "volume_rule_conf"

type VolumeRuleConf struct {
	CheckRuleTimeout       int `yaml:"check_rule_timeout"`
	ImportRuleTimeout      int `yaml:"import_rule_timeout"`
	HistoryDownloadTimeout int `yaml:"history_download_timeout"`
}

func refreshVolumeRuleConf() VolumeRuleConf {
	var conf VolumeRuleConf
	if err := config.UnmarshalConfig(configPrefix+volumeRuleConfKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh redis config fail, err:%+v", err)
	}
	return conf
}

func GetVolumeRuleConf(ctx context.Context) VolumeRuleConf {
	return GetGlobalConfig(ctx).VolumeRuleConf
}
