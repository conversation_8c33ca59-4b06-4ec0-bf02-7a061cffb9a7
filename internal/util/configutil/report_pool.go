package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const reportPoolKey = "ec_report_pool_size"
const defaultECPoolSize = 10

func refreshReportPoolConf() int {
	var poolSize int
	if err := config.UnmarshalConfig(configPrefix+reportPoolKey, &poolSize); err != nil { // nolint
		logger.LogErrorf("refresh ReportPoolConf fail, err:%+v", err)
	}

	return poolSize
}

func GetReportPoolConf() int {
	if gConf == nil || gConf.ECReportPoolSize == 0 {
		return defaultECPoolSize
	}

	return gConf.ECReportPoolSize
}
