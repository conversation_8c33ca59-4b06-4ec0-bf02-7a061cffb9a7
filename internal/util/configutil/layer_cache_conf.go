package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

type (
	LayerCacheConf struct {
		MemSize             int    `yaml:"MemSize"`
		MemNumOfCounters    int    `yaml:"MemNumOfCounters"`
		MemLogicTTL         int    `yaml:"MemLogicTTL"`
		MemPhysicalTTL      int    `yaml:"MemPhysicalTTL"`
		PenetrationQpsLimit int    `yaml:"PenetrationQpsLimit"`
		IsMonitor           bool   `yaml:"IsMonitor"`
		Expire              string `yaml:"Expire"`
		ExpireConfig        map[string]uint32
	}

	LayerCacheExpireConf struct {
		Namespace     string `json:"namespace"`
		ExpireSeconds uint32 `json:"expire_seconds"`
	}
)

const (
	layCacheKey   = "layer_cache"
	LayerCacheKey = configPrefix + layCacheKey
)

// refreshLayerCacheConf 当apollo更新时刷新配置
func refreshLayerCacheConf() LayerCacheConf {
	c := LayerCacheConf{}

	if err := config.UnmarshalConfig(LayerCacheKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", LayerCacheKey)
	}

	if c.Expire != "" {
		expireConfigList := make([]LayerCacheExpireConf, 0)
		err := jsoniter.UnmarshalFromString(c.Expire, &expireConfigList)
		if err != nil {
			logger.LogErrorf("UnmarshalFromString failed|config=%s,err=%s", c.Expire, err.Error())
			return c
		}
		for _, expireConfig := range expireConfigList {
			c.ExpireConfig[expireConfig.Namespace] = expireConfig.ExpireSeconds
		}
	}

	return c
}

// GetLayerCacheConf 获取配置,从内存中获取
func GetLayerCacheConf(ctx context.Context) LayerCacheConf {
	c := LayerCacheConf{}

	if gConf != nil {
		c = GetGlobalConfig(ctx).LayerCacheConf
	}
	return c
}
