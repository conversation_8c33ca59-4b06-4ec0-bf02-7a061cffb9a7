package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	MockDomainKey = "mock_domain"
)

type MockDomainConf struct {
	HttpDomain string `yaml:"http_domain"`
	GrpcDomain string `yaml:"grpc_domain"`
}

func refreshMockDomainConf() MockDomainConf {
	var conf MockDomainConf
	if err := config.UnmarshalConfig("config."+MockDomainKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh mock domain config fail, key:%s, err:%+v", MockDomainKey, err)
	}
	return conf
}

func GetMockDomainConf(ctx context.Context) MockDomainConf {
	return GetGlobalConfig(ctx).MockDomainConf
}
