package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	dataMigrateSwitch    = "data_migrate_switch"
	allocationPathSwitch = "allocation_path_switch"
	forecastSwitch       = "forecast_switch"
)

type DataMigrateSwitchConf struct {
	ApiSwitch map[string]bool `json:"api_switch"`
}

func refreshDataMigrateSwitchConf() DataMigrateSwitchConf {
	var conf DataMigrateSwitchConf
	confStr := config.GetString(configPrefix+dataMigrateSwitch, "{}") // nolint
	if err := jsoniter.UnmarshalFromString(confStr, &conf); err != nil {
		logger.LogErrorf("refresh data migrate switch config fail, err:%+v", err)
	}

	return conf
}

func AllocationPathSwitch() bool {
	isChange, ok := gConf.DataMigrateSwitchConf.ApiSwitch[allocationPathSwitch]
	return ok && isChange
}

func ForecastSwitch() bool {
	isChange, ok := gConf.DataMigrateSwitchConf.ApiSwitch[forecastSwitch]
	return ok && isChange
}
