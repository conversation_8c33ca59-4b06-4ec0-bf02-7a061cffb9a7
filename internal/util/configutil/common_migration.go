package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"github.com/howeyc/crc16"
	"math"
)

const (
	CommonMigrationKey = "common_migration_switch"

	MaxGreyPercentage int = 10000
	MinGreyPercentage int = 0
)

type CommonMigrationConfig struct {
	SwitchConfig map[string]int `yaml:"config.common_migration_switch"`
}

// RefreshMigrationConfig 通用迁移 配置刷新
func refreshMigrationConfig() CommonMigrationConfig {
	commonMigrationConfig := &CommonMigrationConfig{}
	err := config.UnmarshalConfigWithoutPrefix(commonMigrationConfig)
	if err != nil {
		return CommonMigrationConfig{}
	}
	return *commonMigrationConfig
}

// GetMigrationConfig 通用迁移 配置获取
func GetMigrationConfig(ctx context.Context) CommonMigrationConfig {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).CommonMigrationConfig
	}
	return CommonMigrationConfig{}
}

// IsSwitch 切换万分比灰度
func IsSwitch(ctx context.Context, key string, value string) bool {
	val, ok := GetMigrationConfig(ctx).SwitchConfig[key]
	if !ok {
		monitoring.ReportError(ctx, monitoring.SwitchModuleName, key, "key not found")
		logger.CtxLogDebugf(ctx, "%s|key=%s not found", monitoring.SwitchModuleName, key)
		return false
	}
	// 如果万分比全开，直接返回true，提升性能
	if val == MaxGreyPercentage {
		return true
	}
	// 如果万分比全关，直接返回false，提升性能
	if val == MinGreyPercentage {
		return false
	}
	// 命中灰度 - 以传入的value来生成灰度值
	randNum := RandByString(value)
	hit := randNum < val
	logger.CtxLogDebugf(ctx, "%s|key=%s|grey=%v", monitoring.SwitchModuleName, key, hit)
	return hit
}

// RandByString 根据字符串计算随机数，输入内容一样，输出一样，算法：CRC16，区间：[0, 10000)
//  @param str 字符串
//  @return 10000内的随机数，0 ~ 10000
//  <AUTHOR> Bo | SLS BE | <EMAIL>
func RandByString(str string) int {
	// 因为crc16.Checksum返回uint16，范围为0 ~ 65535（math.MaxUint16）
	checkSum := float64(crc16.Checksum([]byte(str), crc16.IBMTable))

	// 需要将数值压缩至0 ~ 10000，然后取余，由于math.MaxUint16=65535，直接取余分布不均匀
	return int(checkSum/float64(math.MaxUint16)*float64(MaxGreyPercentage)) % MaxGreyPercentage
}
