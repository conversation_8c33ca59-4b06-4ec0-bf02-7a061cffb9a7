package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	UseClientModConfigKey = "use_client_mod"
)

type UseClientModConfig struct {
	Switch                               bool `yaml:"switch"`
	ApiSwitch                            bool `yaml:"api_switch"`
	ShippingFeeShopGroupAdminSwitch      bool `yaml:"shipping_fee_shop_group_admin_switch"`
	ShippingFeeShopGroupApiSwitch        bool `yaml:"shipping_fee_shop_group_api_switch"`
	ShippingFeeShopGroupApiCompareSwitch bool `yaml:"shipping_fee_shop_group_api_compare_switch"`
}

// RefreshUseClientModConfig 当apollo更新时刷新配置
func refreshUseClientModConfig() UseClientModConfig {
	c := UseClientModConfig{}
	if err := config.UnmarshalConfig(configPrefix+UseClientModConfigKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", UseClientModConfigKey)
		return c
	}
	return c
}

// GetUseClientModConfig 获取使用client模块的配置
func GetUseClientModConfig(ctx context.Context) UseClientModConfig {
	c := UseClientModConfig{}

	if GetGlobalConfig(ctx) != nil {
		c = GetGlobalConfig(ctx).UseClientModConfig
	}
	return c
}
