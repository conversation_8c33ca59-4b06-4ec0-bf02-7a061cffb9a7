package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

type AntPoolConfig struct {
	PoolSize  int `yaml:"pool_size"`
	BlockSize int `yaml:"block_size"`
}

const (
	antPoolKey       = "ant_pool"
	defaultPoolSize  = 10
	defaultBlockSize = 1000
)

var defaultConfig = AntPoolConfig{PoolSize: defaultPoolSize, BlockSize: defaultBlockSize}

func refreshAntPoolConfig() AntPoolConfig {
	var c AntPoolConfig
	if err := config.UnmarshalConfig(configPrefix+antPoolKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", antPoolKey)
		return c
	}
	checkUseDefaultAntPool(&c)
	logger.LogDebugf("get config RefreshFteRpcConfig | antPoolKey=%+v", c)
	return c
}

func checkUseDefaultAntPool(c *AntPoolConfig) {
	if c.PoolSize == 0 {
		c.PoolSize = defaultPoolSize
	}
	if c.BlockSize == 0 {
		c.BlockSize = defaultBlockSize
	}
}

func GetAntPoolConfig(ctx context.Context) AntPoolConfig {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AntPoolConfig
	}
	return defaultConfig
}
