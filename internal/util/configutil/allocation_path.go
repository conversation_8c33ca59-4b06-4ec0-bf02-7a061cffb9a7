package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const AllocationPathKey = "allocation_path"

type AllocationPathConf struct {
	UseV2Api          bool   `yaml:"use_v2_api"`
	NewSecret         string `yaml:"new_secret"`
	AsyncGoRoutineNum int    `yaml:"async_go_routine_num"`
}

func refreshAllocationPathConf() AllocationPathConf {
	var conf AllocationPathConf
	if err := config.UnmarshalConfig(configPrefix+AllocationPathKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh allocation path config fail, err:%v", err)
	}
	return conf
}

func GetAllocationPathConf(ctx context.Context) AllocationPathConf {
	return GetGlobalConfig(ctx).AllocationPathConf
}
