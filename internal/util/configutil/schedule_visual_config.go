package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	ScheduleVisualSwitch    = "config.schedule_visual_switch"
	ScheduleVisualSwitchKey = "schedule_visual_switch"
	ScheduleVisualConfigKey = "schedule_visual_config"
)

// defaultClearTime 默认清理60天前的数据
const defaultClearTime = 60

func refreshScheduleVisualSwitch() map[string]bool {
	str := config.GetString(ScheduleVisualSwitch, "") // nolint
	param := make(map[string]bool)
	err := jsoniter.UnmarshalFromString(str, &param)
	if err != nil {
		logger.LogErrorf("refresh config failed|UnmarshalFromString fail|key=%s, config=%s", ScheduleVisualSwitch, str)
	}
	logger.LogInfof("refresh config success|key=%s, config=%s", ScheduleVisualSwitch, str)
	//
	return param
}

func GetScheduleVisualSwitch(ctx context.Context) map[string]bool {
	return GetGlobalConfig(ctx).ScheduleVisualSwitch
}

// IsOpenScheduleVisualSwitch true表示开启，false表示关闭
func IsOpenScheduleVisualSwitch(ctx context.Context, businessType string) bool {
	scheduleVisualSwitch := GetScheduleVisualSwitch(ctx)
	if _, ok := scheduleVisualSwitch[businessType]; ok {
		return scheduleVisualSwitch[businessType]
	}
	// 默认返回false
	return false
}

type ScheduleVisualConfig struct {
	ClearTime int `yaml:"clear_time"`
}

func refreshScheduleVisualConfig() ScheduleVisualConfig {
	var scheduleVisualConfig ScheduleVisualConfig
	if err := config.UnmarshalConfig(configPrefix+ScheduleVisualConfigKey, &scheduleVisualConfig); err != nil { // nolint
		logger.LogErrorf("refreshScheduleVisualConfig|refresh schedule visual config error:%v", err)
	}
	return scheduleVisualConfig
}

// GetScheduleVisualClearTime 清理多久之前的调度可视化数据，时间单位为（天），默认清理60天之前的数据
func GetScheduleVisualClearTime(ctx context.Context) int {
	if GetGlobalConfig(ctx) != nil && GetGlobalConfig(ctx).ScheduleVisualConfig.ClearTime != 0 {
		return GetGlobalConfig(ctx).ScheduleVisualConfig.ClearTime
	}
	return defaultClearTime
}
