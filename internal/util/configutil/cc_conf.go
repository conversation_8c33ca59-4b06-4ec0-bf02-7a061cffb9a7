package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	customsServiceKey = "customs_service"
)

type CustomsServiceConf struct {
	Host      string `yaml:"host"`
	JwtOptr   string `yaml:"jwt_optr"`
	JwtSecret string `yaml:"jwt_secret"`
	Timeout   int64  `yaml:"timeout"`
}

func refreshCustomsServiceConf() CustomsServiceConf {
	var conf CustomsServiceConf
	if err := config.UnmarshalConfig("config."+customsServiceKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh customs service config fail, key:%s, err:%+v", customsServiceKey, err)
	}
	return conf
}

func GetCustomsServiceConf(ctx context.Context) CustomsServiceConf {
	return GetGlobalConfig(ctx).CustomsServiceConf
}
