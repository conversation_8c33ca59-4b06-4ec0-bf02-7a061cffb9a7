package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

const (
	routingPreCalcFeeConfig = "config.routing-pre-calc-fee-switch"
	routingPreCalcFeeKey    = "routing-pre-calc-fee-switch"
)

// RoutingPreCalcFeeSwitch
/** 开关配置格式
routing-pre-calc-fee-switch:
{
    "global_switch": true,
    "routing_switch": {
        "91003": [64, 128]   // 64: LocalFM, 128: LocalLM
    }
}
*/
type RoutingPreCalcFeeSwitch struct {
	GlobalSwitch  bool             `json:"global_switch"`
	RoutingSwitch map[string][]int `json:"routing_switch"`
}

func refreshRoutingPreCalcFeeConfig() RoutingPreCalcFeeSwitch {
	preCalcFeeSwitchMap := RoutingPreCalcFeeSwitch{}
	preCalFeeConfigStr := config.GetString(routingPreCalcFeeConfig, "") // nolint
	err := jsoniter.UnmarshalFromString(preCalFeeConfigStr, &preCalcFeeSwitchMap)
	if err != nil {
		logger.LogErrorf("RefreshRoutingPreCalcFeeConfig error:%v", err)
		return preCalcFeeSwitchMap
	}
	return preCalcFeeSwitchMap
}

func GetRoutingPreCalcFeeConfig(ctx context.Context) RoutingPreCalcFeeSwitch {
	preCalcFeeSwitchMap := RoutingPreCalcFeeSwitch{}
	if GetGlobalConfig(ctx) != nil {
		preCalcFeeSwitchMap = GetGlobalConfig(ctx).RoutingPreCalcFeeSwitch
	}
	return preCalcFeeSwitchMap
}

// IsOpenGlobalSwitch 该市场globalSwitch开关是否打开，true表示开关打开，false表示开关关闭
func IsOpenGlobalSwitch(ctx context.Context) bool {
	preCalcFeeSwitchMap := GetRoutingPreCalcFeeConfig(ctx)
	return preCalcFeeSwitchMap.GlobalSwitch
}

// IsOpenRoutingPreCalcFeeSwitch true表示开关打开，false表示开关关闭
func IsOpenRoutingPreCalcFeeSwitch(ctx context.Context, productId int64, lineType int) bool {
	preCalcFeeSwitchMap := GetRoutingPreCalcFeeConfig(ctx)
	if preCalcFeeSwitchMap.GlobalSwitch {
		if _, ok := preCalcFeeSwitchMap.RoutingSwitch[strconv.FormatInt(productId, 10)]; ok {
			lineTypeList := preCalcFeeSwitchMap.RoutingSwitch[strconv.FormatInt(productId, 10)]
			if objutil.ContainsInt(lineTypeList, lineType) {
				return true
			}
		}
	}
	return false
}
