package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

// main集群信息
const (
	MainHbaseKey = "main_hbase"
)

type MainHbaseConfig struct {
	HostUrl            string            `yaml:"HostUrl"`
	UserName           string            `yaml:"UserName"`
	PassWord           string            `yaml:"Password"`
	AuthMethod         string            `yaml:"AuthMethod"`
	ZkRoot             string            `yaml:"ZkRoot"`
	LocalHost          bool              `yaml:"LocalHost"`
	TableNameMapString string            `yaml:"TableNameMapString"`
	TableNameMap       map[string]string `json:"table_name_map"`
	MaxLoopSize        int               `yaml:"max_loop_size"`
	SaltSize           int               `yaml:"salt_size"`
	BatchSize          uint32            `yaml:"batch_size"`
	UseMain            bool              `yaml:"use_main"`
}

// SSCSMR-1320: get hbase conf of Data team
func GetMainHbaseConfig(ctx context.Context) MainHbaseConfig {
	var hbaseConfig MainHbaseConfig
	if GetGlobalConfig(ctx) != nil {
		hbaseConfig = GetGlobalConfig(ctx).MainHbaseConfig
	}
	logger.LogDebugf("get config GetMainHbaseConfig | hbaseConfig=%+v", hbaseConfig)
	return hbaseConfig
}

// SSCSMR-1590: refresh main hbase conf
func RefreshMainHbaseConfig() MainHbaseConfig {
	c := MainHbaseConfig{}
	if err := config.UnmarshalConfig(configPrefix+MainHbaseKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", MainHbaseKey)
		return c
	}
	tableNameMap := make(map[string]string, 0)
	if err := jsoniter.Unmarshal([]byte(c.TableNameMapString), &tableNameMap); err != nil {
		logger.LogErrorf("RefreshMainHbaseConfig|Unmarshal table name map string err:%v", err)
	} else {
		c.TableNameMap = tableNameMap
	}
	if c.BatchSize == 0 {
		c.BatchSize = 1000
	}
	logger.LogDebugf("get config RefreshMainHbaseConfig | hbaseConfig=%v", c)
	return c
}
