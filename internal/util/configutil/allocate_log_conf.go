package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	allocationLogConfKey      = "enable_allocation_log"
	batchAllocationLogConfKey = "enable_batch_allocation_log"
)

func GetAllocationLogConf(ctx context.Context) bool {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AllocationLogConf
	}
	return false
}

func refreshAllocationLogConf() bool {
	var c bool
	if err := config.UnmarshalConfig(configPrefix+allocationLogConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", allocationLogConfKey)
		return c
	}
	logger.LogDebugf("get config refreshAllocationLogConf | refreshAllocationLogConf=%+v", c)
	return c
}

type BatchAllocationLogConf struct {
	EnableLog           bool `yaml:"enable_log"`
	EnableKafkaSend     bool `yaml:"enable_kafka_send"`
	DefaultBatchLogSize int  `yaml:"default_batch_log_size"`
	GoroutinePoolSize   int  `yaml:"goroutine_pool_size"`
}

func GetBatchAllocationLogConf(ctx context.Context) BatchAllocationLogConf {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).BatchAllocationLogConf
	}
	return BatchAllocationLogConf{}
}

func refreshBatchAllocationLogConf() BatchAllocationLogConf {
	var c BatchAllocationLogConf
	if err := config.UnmarshalConfig(configPrefix+batchAllocationLogConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", batchAllocationLogConfKey)
		return c
	}
	logger.LogDebugf("get config refreshBatchAllocationLogConf | refreshBatchAllocationLogConf=%+v", c)
	return c
}
