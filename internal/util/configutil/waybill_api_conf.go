package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	waybillApiKey = "waybill_api"

	defaultTimeout = 6
)

type WbcApiConfig struct {
	SlsTnApi string `yaml:"sls_tn_api"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Timeout  int    `yaml:"timeout"` // 单位：S
}

func refreshWbcConfig() WbcApiConfig {
	var conf WbcApiConfig
	if err := config.UnmarshalConfig("config."+waybillApiKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh waybill api config fail, err:%+v", err)
	}

	if conf.Timeout == 0 {
		conf.Timeout = defaultTimeout
	}

	return conf
}

func GetWaybillApi(ctx context.Context) WbcApiConfig {
	return GetGlobalConfig(ctx).WaybillApi
}
