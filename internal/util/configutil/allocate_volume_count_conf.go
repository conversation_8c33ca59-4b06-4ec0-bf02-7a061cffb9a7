package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const AllocateVolumeCount = "allocate_volume_count"

type AllocateVolumeCountConf struct {
	UseMaskPrefix     bool `yaml:"use_mask_prefix"`
	AllowNewRedisKey  bool `yaml:"allow_new_redis_key"`
	AllowDeductVolume bool `yaml:"allow_deduct_volume"`
}

func refreshAllocateVolumeCountConf() AllocateVolumeCountConf {
	var conf AllocateVolumeCountConf
	if err := config.UnmarshalConfig(configPrefix+AllocateVolumeCount, &conf); err != nil { // nolint
		logger.LogErrorf("refresh allocate volume count config fail, err:%v", err)
	}

	return conf
}

func GetAllocateVolumeCountConf(ctx context.Context) AllocateVolumeCountConf {
	if GetGlobalConfig(ctx) == nil {
		return AllocateVolumeCountConf{}
	}
	return GetGlobalConfig(ctx).AllocateVolumeCountConf
}
