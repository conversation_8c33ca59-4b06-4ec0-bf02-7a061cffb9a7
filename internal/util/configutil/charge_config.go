package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	chargeapiKey = "chargeapi_key"
)

type ChargeApiConfig struct {
	Host          string `yaml:"host"`
	ChargeAccount string `yaml:"charge_account"`
	ChargeSecret  string `yaml:"charge_secret"`
}

func refreshChargeApiConfig() ChargeApiConfig {
	var conf ChargeApiConfig
	if err := config.UnmarshalConfig("config."+chargeapiKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh customs service config fail, key:%s, err:%+v", customsServiceKey, err)
	}

	return conf
}

func GetChargeApiConfig(ctx context.Context) ChargeApiConfig {
	return GetGlobalConfig(ctx).ChargeApiConfig
}
