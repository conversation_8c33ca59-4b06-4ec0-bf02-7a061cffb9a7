package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	SaturnKafkaConfigKey = configPrefix + "client.saturn" // 初始化Saturn Producer时传入的Config Prefix
	saturnNamespace      = "saturn_namespace"
)

type SaturnNamespaceConf struct {
	SmrNamespace   string `yaml:"smr_namespace"`
	SmrBANamespace string `yaml:"smr_ba_namespace"`
}

func refreshSaturnNamespace() SaturnNamespaceConf {
	var conf SaturnNamespaceConf
	if err := config.UnmarshalConfig(configPrefix+saturnNamespace, &conf); err != nil { // nolint
		logger.LogErrorf("refresh s3 config fail, err:%+v", err)
	}
	return conf
}

func GetSaturnNamespaceConf(ctx context.Context) SaturnNamespaceConf {
	return GetGlobalConfig(ctx).SaturnNamespaceConf
}
