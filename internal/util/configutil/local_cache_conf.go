package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	jsoniter "github.com/json-iterator/go"
)

const (
	localCacheKey = "local_cache"
)

type (
	LocalCacheConf struct {
		DefaultForceSwitch     *bool                         `yaml:"default_force_switch"`      //是否强制切换。true时，local-cache初始化成功后强制切换使用local-cache
		DefaultAutoRefresh     *bool                         `yaml:"default_auto_refresh"`      //默认是否开启定时刷新。没有单独配置key的属性的话，则用这个值
		DefaultUsingLayerCache *bool                         `yaml:"default_using_layer_cache"` //默认是否使用layer-cache。没有单独配置key的属性的话，则用这个值
		KeyConfigsJson         *string                       `yaml:"key_configs_json"`          //json map key是local cache key, value是LocalCacheKeyConfig
		KeyConfigsMap          map[string]*LocalCacheKeyConf //key_configs_json的struct实体
	}

	LocalCacheKeyConf struct {
		IsAutoRefresh     *bool   `json:"is_auto_refresh"`      //是否开启定时刷新
		IsUsingLayerCache *bool   `json:"is_using_layer_cache"` //是否使用layer-cache
		Version           *int    `json:"version"`              //配置版本号，守护协程会根据这个版本决定是否立即刷新
		CacheKey          *string `json:"cache_key"`            //配置作用的缓存key
	}
)

func (p LocalCacheConf) GetKeyConfigMap() map[string]*LocalCacheKeyConf {
	if p.KeyConfigsMap == nil {
		m := make(map[string]*LocalCacheKeyConf)
		if p.KeyConfigsJson != nil {
			_ = jsoniter.UnmarshalFromString(*p.KeyConfigsJson, &m)
		}
		p.KeyConfigsMap = m
	}
	return p.KeyConfigsMap
}

func refreshLocalCacheConf() LocalCacheConf {
	defaultAutoRefresh := true //apollo 没配的时候，会返回默认值false，不是nil，所以这里要赋默认值
	defaultUsingLayerCache := false
	defaultForceSwitch := true
	conf := LocalCacheConf{
		DefaultAutoRefresh:     &defaultAutoRefresh,
		DefaultUsingLayerCache: &defaultUsingLayerCache,
		DefaultForceSwitch:     &defaultForceSwitch,
	}
	if err := config.UnmarshalConfig(configPrefix+localCacheKey, &conf); err != nil { // nolint
		return LocalCacheConf{
			DefaultAutoRefresh:     &defaultAutoRefresh,
			DefaultUsingLayerCache: &defaultUsingLayerCache,
			DefaultForceSwitch:     &defaultForceSwitch,
		}
	}
	conf.KeyConfigsMap = make(map[string]*LocalCacheKeyConf)
	if conf.KeyConfigsJson != nil {
		_ = jsoniter.UnmarshalFromString(*conf.KeyConfigsJson, &conf.KeyConfigsMap)
	}
	return conf
}

func GetLocalCacheConf(ctx context.Context) LocalCacheConf {
	return GetGlobalConfig(ctx).LocalCacheConf
}
