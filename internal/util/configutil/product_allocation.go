package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const productAllocationKey = "product_allocation"

type ProductAllocationConf struct {
	DisableRuleSteps       string `yaml:"DisableRuleSteps"`
	DisableProductPriority bool   `yaml:"DisableProductPriority"`
}

func GetProductAllocationConf(ctx context.Context) ProductAllocationConf {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).ProductAllocationConf
	}
	return ProductAllocationConf{}
}

func refreshProductAllocationConf() ProductAllocationConf {
	c := ProductAllocationConf{}
	if err := config.UnmarshalConfig(configPrefix+productAllocationKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", productAllocationKey)
		return c
	}
	logger.LogDebugf("get config RefreshProductAllocationConfig | productAllocationConfig=%+v", c)
	return c
}
