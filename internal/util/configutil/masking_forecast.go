package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	AllocateForecastErrorRateKey = "allocate_forecast_error_rate"
	AllocateForecastUseMockKey   = "allocate_forecast_use_mock"
	AllocateForecastRateLimitKey = "allocate_forecast_rate_limit"
	MaskingForecastKey           = "masking_forecast"
)

type (
	AllocateForecastErrorRateConfig struct {
		ErrorRate int `yaml:"error_rate"`
	}

	AllocateForecastUseMockConfig struct {
		Switch         bool   `yaml:"switch"` // 是否使用mock开关，false:不使用 true:使用
		MockRequestID  string `yaml:"X-Request-ID"`
		MockSystemsKey string `yaml:"sysMock"`
		MockTypeKey    string `yaml:"typeMock"`
	}

	AllocateForecastRateLimitConfig struct {
		MaxConcurrentNum int `yaml:"max_concurrent_num"`
	}

	MaskingForecastConf struct {
		DividingTime      string `yaml:"dividing_time"` //e.g. 2022-12-14
		LoopTimes         int    `yaml:"loop_times"`
		ChannelSize       int    `yaml:"channel_size"`
		ReadLimit         int    `yaml:"read_limit"`
		ForbidTidbData    bool   `yaml:"forbid_tidb_data"`
		TimeSliceString   string `yaml:"time_slice_string"`   //e.g. ["0-1","1-4","4-24"]
		DeleteDays        int    `yaml:"delete_days"`         //在refresh函数中初始化默认值, masking sub task存活期阈值
		CheckTimeDuration int64  `yaml:"check_time_duration"` //在refresh函数中初始化默认值，masking sub task断点续作阈值
		RateLimit         int    `yaml:"rate_limit"`          //只跑软性情况下，控制令牌桶每秒生成多少个bucket
		RateBurst         int    `yaml:"rate_burst"`          //控制令牌桶每秒可以消费多少个bucket,也对应worker数
		HardRateLimit     int    `yaml:"hard_rate_limit"`     //开启硬性情况下，控制令牌桶每秒生成多少个bucket
		SleepTime         int    `json:"sleep_time"`          //兜底开关，用来控制生产者速率
		NeedSleep         bool   `yaml:"need_sleep"`          //开关，用来强制控制生产者速率，与sleep time配套
	}
)

func refreshAllocateForecastErrorRateConfig() AllocateForecastErrorRateConfig {
	c := AllocateForecastErrorRateConfig{}
	if err := config.UnmarshalConfig(configPrefix+AllocateForecastErrorRateKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", AllocateForecastErrorRateKey)
		return c
	}
	return c
}

func refreshAllocateForecastUseMockConfig() AllocateForecastUseMockConfig {
	c := AllocateForecastUseMockConfig{}
	if err := config.UnmarshalConfig(configPrefix+AllocateForecastUseMockKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", AllocateForecastUseMockKey)
		return c
	}
	return c
}

func refreshAllocateForecastRateLimitConfig() AllocateForecastRateLimitConfig {
	c := AllocateForecastRateLimitConfig{}
	if err := config.UnmarshalConfig(configPrefix+AllocateForecastRateLimitKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", AllocateForecastRateLimitKey)
		return c
	}
	return c
}

func GetAllocateForecastErrorRateConfig(ctx context.Context) AllocateForecastErrorRateConfig {
	allocateForecastErrorRateConfig := AllocateForecastErrorRateConfig{}
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AllocateForecastErrorRateConfig
	}

	return allocateForecastErrorRateConfig
}

func GetAllocateForecastUseMockConfig(ctx context.Context) AllocateForecastUseMockConfig {
	allocateForecastUseMockConfig := AllocateForecastUseMockConfig{}
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AllocateForecastUseMockConfig
	}

	return allocateForecastUseMockConfig
}

func GetAllocateForecastRateLimitConfig(ctx context.Context) AllocateForecastRateLimitConfig {
	allocateForecastRateLimitConfig := AllocateForecastRateLimitConfig{}
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).AllocateForecastRateLimitConfig
	}

	return allocateForecastRateLimitConfig
}

func GetMaskingForecastConf(ctx context.Context) MaskingForecastConf {
	conf := MaskingForecastConf{}
	if GetGlobalConfig(ctx) != nil {
		conf = GetGlobalConfig(ctx).MaskingForecastConf
	}
	return conf
}

func RefreshMaskingForecastConf() MaskingForecastConf {
	c := MaskingForecastConf{}
	if err := config.UnmarshalConfig(configPrefix+MaskingForecastKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", MaskingForecastKey)
		return c
	}
	//默认删除35天以前的任务
	if c.DeleteDays == 0 {
		c.DeleteDays = 35
	}
	if c.CheckTimeDuration == 0 {
		c.CheckTimeDuration = 3600 //默认阈值为一小时
	}
	if c.SleepTime == 0 {
		c.SleepTime = 1
	}
	if c.RateLimit == 0 {
		c.RateLimit = 2000 //默认每秒生成2000个bucket
	}
	if c.RateBurst == 0 {
		c.RateBurst = 32 //默认最大并发数量为32
	}
	if c.HardRateLimit == 0 {
		c.HardRateLimit = 1000 //默认生成1000个bucket
	}
	return c
}
