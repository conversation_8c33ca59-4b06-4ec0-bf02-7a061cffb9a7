package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	volumeDashboardConfig    = "config.volume-dashboard-config"
	volumeDashboardConfigKey = "volume-dashboard-config"
	DefaultRedisConcurrency  = 1000
)

type VolumeDashboardConfig struct {
	RoutingDashboardRedisConcurrency int  `json:"routing_dashboard_redis_concurrency"`
	MaskingDashboardSwitch           bool `json:"masking_dashboard_switch"`
}

func refreshVolumeDashboardConfig() VolumeDashboardConfig {
	volumeDashboardConf := VolumeDashboardConfig{}
	volumeDashboardConfStr := config.GetString(volumeDashboardConfig, "") // nolint
	err := jsoniter.UnmarshalFromString(volumeDashboardConfStr, &volumeDashboardConf)
	if err != nil {
		logger.LogErrorf("RefreshVolumeDashboardConfig error:%v", err)
		return volumeDashboardConf
	}
	return volumeDashboardConf
}

func GetVolumeDashboardConfig(ctx context.Context) VolumeDashboardConfig {
	volumeDashboardConf := VolumeDashboardConfig{}
	if GetGlobalConfig(ctx) != nil {
		volumeDashboardConf = GetGlobalConfig(ctx).VolumeDashboardConfig
	}
	return volumeDashboardConf
}

func GetRoutingDashboardRedisConcurrency(ctx context.Context) int {
	volumeDashboardConf := GetVolumeDashboardConfig(ctx)
	if volumeDashboardConf.RoutingDashboardRedisConcurrency == 0 {
		return DefaultRedisConcurrency
	}
	return volumeDashboardConf.RoutingDashboardRedisConcurrency
}

func GetMaskingDashboardSwitch(ctx context.Context) bool {
	volumeDashboardConf := GetVolumeDashboardConfig(ctx)
	return volumeDashboardConf.MaskingDashboardSwitch
}
