package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	localForecastKey           = "local_forecast"
	defaultHbaseReadLimit      = 1000
	DefaultMinSamplingRate     = 0.3
	DefaultSalt                = 1000
	DefaultSamplingPoolMaxLoop = 4
	DefaultHbaseReadMaxLoop    = 10000
)

type LocalForecastConfig struct {
	ReadLimit           int     `yaml:"read_limit"`             //一次从hbase服务端拿多少条数据
	MinSamplingRate     float64 `yaml:"min_sampling_rate"`      //预测最低采样率
	PrefixSalt          int     `yaml:"prefix_salt"`            //hbase查找数据salt前缀
	SamplingPoolMaxLoop int     `yaml:"sampling_pool_max_loop"` //样本池最大循环次数
	HbaseReadMaxLoop    int     `yaml:"hbase_read_max_loop"`    //查询hbase最大循环次数
}

func refreshLocalForecastConfig() LocalForecastConfig {
	var c LocalForecastConfig
	if err := config.UnmarshalConfig(configPrefix+localForecastKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", localForecastKey)
		return c
	}
	checkUseDefaultLocalForecastConfig(&c)
	logger.LogDebugf("get config RefreshFteRpcConfig | antPoolKey=%+v", c)
	return c
}

func checkUseDefaultLocalForecastConfig(l *LocalForecastConfig) {
	if l == nil {
		return
	}
	if l.PrefixSalt == 0 {
		l.PrefixSalt = DefaultSalt
	}
	if l.SamplingPoolMaxLoop == 0 {
		l.SamplingPoolMaxLoop = DefaultSamplingPoolMaxLoop
	}
	if l.HbaseReadMaxLoop == 0 {
		l.HbaseReadMaxLoop = DefaultHbaseReadMaxLoop
	}
	if l.ReadLimit == 0 {
		l.ReadLimit = defaultHbaseReadLimit
	}
	if l.MinSamplingRate == 0 {
		l.MinSamplingRate = DefaultMinSamplingRate
	}
}

func GetLocalForecastConfig(ctx context.Context) LocalForecastConfig {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).LocalForecastConfig
	}
	forecastConf := GetGlobalConfig(ctx).LocalForecastConfig
	checkUseDefaultLocalForecastConfig(&forecastConf)
	return forecastConf
}
