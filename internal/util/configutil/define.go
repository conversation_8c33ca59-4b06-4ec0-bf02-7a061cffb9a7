package configutil

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/event"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"github.com/go-chassis/go-archaius"
)

const configPrefix = "config."

var gConf *globalConfig
var getGlobalConfig func(ctx context.Context) interface{}
var registerGlobalConfig = func(conf *globalConfig) {
	getGlobalConfig = recorder.RegisterGlobalVar("global_config", gConf)
}
var GetGlobalConfig = func(ctx context.Context) *globalConfig {
	defer func() {
		if err := recover(); err != nil {
			return
		}
	}()
	if v, ok := getGlobalConfig(ctx).(*globalConfig); ok {
		return v
	}
	return gConf
}

var GetGlobalConfigWithoutCtx = func() *globalConfig {
	return gConf
}

func Init() error {
	gConf = new(globalConfig)
	wbcPublicConf = new(WbcPublicConf)
	gConf.RedisConf = refreshRedisConf()
	gConf.DsnConf = refreshDSNConf()
	gConf.LpsGrpcConf = refreshGrpcConf(lpsGrpcKey)
	gConf.LfsGrpcConf = refreshGrpcConf(lfsGrpcKey)
	gConf.LlsGrpcConf = refreshGrpcConf(llsGrpcKey)
	gConf.LocationGrpcConf = refreshGrpcConf(locationGrpcKey)
	gConf.ChargeGrpcConf = refreshGrpcConf(chargeGrpcKey)
	gConf.LcosGrpcConf = refreshGrpcConf(lcosGrpcKey)
	gConf.JwtConf = refreshJWTConf()
	gConf.LpsJwtConf = refreshAdminConf(lpsAdminKey)
	gConf.LpsApiConf = refreshApiConf(lpsApiKey)
	gConf.LfsApiConf = refreshApiConf(lfsApiKey)
	gConf.LnpApiConf = refreshApiConf(lnpApiKey)
	gConf.AiApiConf = refreshApiConf(aiApiKey)
	gConf.S3Conf = refreshS3Conf()
	gConf.LocalCacheConf = refreshLocalCacheConf()
	gConf.TwsCutoffTimeWithTimezone = refreshTwsCutoffTimeWithTimezone()
	gConf.LayerCacheConf = refreshLayerCacheConf()
	gConf.LruCacheConf = refreshLruCacheConf()
	gConf.FteRpcConf = refreshFteRpcConf()
	gConf.FteHttpConf = refreshHttpConf()
	gConf.RateLimiterConf = refreshRateLimiterConf()
	gConf.VolumeSwitchCfg = WatchVolumeSwitch()
	gConf.BreakerConf = refreshBreakerConf()
	gConf.GrpcBreakerConf = refreshGrpcBreakerConf()
	gConf.VolumeRuleConf = refreshVolumeRuleConf()
	gConf.IsCloseRedisCounterDB = refreshCloseRedisCounterToDBSwitch()
	gConf.DataConf = refreshDataConf()
	gConf.PrometheusReportConfig = RefreshPrometheusReportConfig()
	gConf.AllocationLogConf = refreshAllocationLogConf()
	gConf.BatchAllocationLogConf = refreshBatchAllocationLogConf()
	gConf.ProductAllocationConf = refreshProductAllocationConf()
	gConf.CustomsServiceConf = refreshCustomsServiceConf()
	gConf.MockDomainConf = refreshMockDomainConf()
	gConf.AllocateForecastErrorRateConfig = refreshAllocateForecastErrorRateConfig()
	gConf.AllocateForecastUseMockConfig = refreshAllocateForecastUseMockConfig()
	gConf.AllocateForecastRateLimitConfig = refreshAllocateForecastRateLimitConfig()
	gConf.UseClientModConfig = refreshUseClientModConfig()
	gConf.RefreshHardCriteriaByTaskGoroutine = RefreshRefreshHardCriteriaByTaskGoroutineConfig()
	gConf.HardCriteriaTaskConfig = RefreshHardCriteriaTaskConfig()
	gConf.SlsOpsS3Conf = refreshSlsOpsS3Conf()
	gConf.SaturnNamespaceConf = refreshSaturnNamespace()
	gConf.KafkaMonitorSwitch = refreshKafkaMonitorSwitch()
	gConf.MaskingForecastConf = RefreshMaskingForecastConf()
	gConf.DataHbaseConfig = RefreshDataHBaseConfig()
	gConf.MainHbaseConfig = RefreshMainHbaseConfig()
	gConf.AllocationPathConf = refreshAllocationPathConf()
	gConf.WmsToggleConf = refreshWmsToggleConf()
	gConf.AuditLogConfig = RefreshAuditLogConfig()
	gConf.ScheduleVisualSwitch = refreshScheduleVisualSwitch()
	gConf.ScheduleVisualConfig = refreshScheduleVisualConfig()
	updateKeys()
	gConf.WaybillApi = refreshWbcConfig()
	gConf.RoutingLogWriteListConfig = refreshWriteListConfig()
	gConf.ForbidCheckDonePending = refreshForbidCheckDonePending()
	gConf.AddressLevelUpConfig = RefreshAddressLevelUpSwitch()
	gConf.AntPoolConfig = refreshAntPoolConfig()
	gConf.LocalForecastRateLimit = refreshLocalForecastRateLimitConfig()
	gConf.LocalForecastConfig = refreshLocalForecastConfig()
	gConf.RateLimitConfig = refreshRateLimit()
	gConf.RoutingPreCalcFeeSwitch = refreshRoutingPreCalcFeeConfig()
	gConf.MaskingPreCalcFeeSwitch = refreshMaskingPreCalcFeeSwitch()
	gConf.ChargeApiConfig = refreshChargeApiConfig()
	gConf.DataApiSwitchConf = refreshDataApiSwitchConf()
	gConf.IlhForecastConfig = refreshIlhForecastConfig()
	gConf.RevampILHRoutingConfig = refreshRevampILHRoutingConfig()
	gConf.VolumeRoutingRegionToggleConf = refreshVolumeRoutingRegionToggleConf()
	gConf.PostCodeMaxUploadConf = refreshPostCodeMaxUploadConf()
	gConf.AlgoConfig = refreshAlgoConfig()
	gConf.BatchAllocateForecastConf = refreshBatchAllocateForecastConf()
	gConf.ECReportPoolSize = refreshReportPoolConf()
	gConf.SplitBatchConf = refreshSplitBatchConf()
	gConf.BatchAllocateConf = refreshBatchAllocateConf()
	gConf.AsyncCompressConf = RefreshAsyncCompressConf()
	gConf.DataMigrateSwitchConf = refreshDataMigrateSwitchConf()
	gConf.EsConf = refreshEsConf()
	gConf.ClickHouseConfig = refreshClickHouseConfig()
	gConf.TrafficCollectConfig = refreshTrafficCollectConfig()
	gConf.UssWhiteHost = RefreshUssWhiteHost()
	gConf.AllocateVolumeCountConf = refreshAllocateVolumeCountConf()
	gConf.BusinessAuditConf = refreshBusinessAuditConf()
	gConf.MaskVolumeCheckConf = refreshMaskVolumeCheckConf()
	gConf.VolumeDashboardConfig = refreshVolumeDashboardConfig()
	gConf.PisEmailTemplateConf = refreshPisEmailTemplateConf()
	gConf.PreVnCbToggleCheck = refreshPreVnCbToggleCheckConfig()
	gConf.SpexConfig = refreshSpexConfig()
	gConf.SpexCommandConfig = refreshSpexCommandConfig()
	gConf.CommonMigrationConfig = refreshMigrationConfig()
	wbcPublicConf.traceSdkConf = refreshTraceSdkConf()
	if err := config.RegisterListener(gConf); err != nil {
		return err
	}
	if err := config.RegisterListener(wbcPublicConf); err != nil {
		return err
	}

	registerGlobalConfig(gConf)

	return nil
}

func InitTest() *globalConfig {
	gConf = new(globalConfig)
	registerGlobalConfig(gConf)
	return gConf
}

func updateKeys() {
	cfgs := archaius.GetConfigs()
	logger.LogInfof("before updating, config=%v", gConf)
	for key := range cfgs {
		if strings.HasPrefix(key, configPrefix) && strings.HasSuffix(key, ClientTokenSuffix) {
			gConf.ClientTokenConfig = refreshClientToken(key, gConf.ClientTokenConfig)
		}
	}
	logger.LogInfof("after updating, config=%v", gConf)
}

type globalConfig struct {
	RedisConf                          RedisConf
	DsnConf                            DSNConf
	LpsGrpcConf                        GrpcConf
	LfsGrpcConf                        GrpcConf
	LlsGrpcConf                        GrpcConf
	LocationGrpcConf                   GrpcConf
	ChargeGrpcConf                     GrpcConf
	LcosGrpcConf                       GrpcConf
	JwtConf                            JWTConf
	LpsJwtConf                         AdminConf
	S3Conf                             S3Conf
	LocalCacheConf                     LocalCacheConf
	LayerCacheConf                     LayerCacheConf
	LruCacheConf                       LruCacheConf
	LpsApiConf                         ApiConf
	FteRpcConf                         string
	FteHttpConf                        string
	IsCloseRedisCounterDB              bool
	RateLimiterConf                    RateLimiter
	BreakerConf                        BreakerConf
	GrpcBreakerConf                    GrpcBreakerConf
	VolumeSwitchCfg                    map[string]int
	DataConf                           DataConf
	VolumeRuleConf                     VolumeRuleConf
	TwsCutoffTimeWithTimezone          map[string]CutoffTimeConfig
	PrometheusReportConfig             PrometheusReportConfig
	CustomsServiceConf                 CustomsServiceConf
	MockDomainConf                     MockDomainConf
	AllocationLogConf                  bool
	BatchAllocationLogConf             BatchAllocationLogConf
	ProductAllocationConf              ProductAllocationConf
	ClientTokenConfig                  map[string]OauthToken
	LfsApiConf                         ApiConf
	LnpApiConf                         ApiConf
	AiApiConf                          ApiConf
	AllocateForecastErrorRateConfig    AllocateForecastErrorRateConfig
	AllocateForecastUseMockConfig      AllocateForecastUseMockConfig
	AllocateForecastRateLimitConfig    AllocateForecastRateLimitConfig
	UseClientModConfig                 UseClientModConfig
	RefreshHardCriteriaByTaskGoroutine int
	HardCriteriaTaskConfig             HardCriteriaTaskConfig
	SlsOpsS3Conf                       SlsOpsS3Conf
	SaturnNamespaceConf                SaturnNamespaceConf
	KafkaMonitorSwitch                 KafkaMonitorSwitch
	DataHbaseConfig                    DataHbaseConfig
	MainHbaseConfig                    MainHbaseConfig
	MaskingForecastConf                MaskingForecastConf
	AllocationPathConf                 AllocationPathConf
	WmsToggleConf                      WmsToggleConf
	AuditLogConfig                     AuditLogConfig
	WaybillApi                         WbcApiConfig
	RoutingLogWriteListConfig          RoutingLogWriteListConfig
	MaskingPreCalcFeeSwitch            MaskingPreCalcFeeSwitch
	ForbidCheckDonePending             bool
	AntPoolConfig                      AntPoolConfig
	LocalForecastRateLimit             LocalForecastRateLimit
	LocalForecastConfig                LocalForecastConfig
	ScheduleVisualSwitch               map[string]bool
	AddressLevelUpConfig               AddressLevelUpConfig
	RateLimitConfig                    RateLimitConfig
	ScheduleVisualConfig               ScheduleVisualConfig
	RoutingPreCalcFeeSwitch            RoutingPreCalcFeeSwitch
	ChargeApiConfig                    ChargeApiConfig
	IlhForecastConfig                  IlhForecastConfig
	RevampILHRoutingConfig             RevampILHRoutingConfig
	DataApiSwitchConf                  DataApiSwitchConf
	VolumeRoutingRegionToggleConf      bool
	PostCodeMaxUploadConf              PostCodeMaxUploadConf
	AlgoConfig                         AlgoConfig
	BatchAllocateForecastConf          BatchAllocateForecastConf
	ECReportPoolSize                   int
	SplitBatchConf                     SplitBatchConf
	BatchAllocateConf                  BatchAllocateConfig
	AsyncCompressConf                  AsyncCompressConf
	TrafficCollectConfig               TrafficCollectConfig
	DataMigrateSwitchConf              DataMigrateSwitchConf
	EsConf                             EsConf
	ClickHouseConfig                   ClickHouseConfig
	UssWhiteHost                       []string
	AllocateVolumeCountConf            AllocateVolumeCountConf
	BusinessAuditConf                  BusinessAuditConf
	MaskVolumeCheckConf                MaskVolumeCheckConf
	VolumeDashboardConfig              VolumeDashboardConfig
	PisEmailTemplateConf               PisEmailTemplateConf
	PreVnCbToggleCheck                 bool
	SpexConfig                         SpexConfig
	SpexCommandConfig                  SpexCommandConfig
	CommonMigrationConfig              CommonMigrationConfig
}

func (p *globalConfig) ConfPrefix() string {
	return "config"
}

func (p *globalConfig) Event(events []*event.Event) {
	for _, e := range events {
		segments := strings.Split(e.Key, ".")
		if len(segments) < 2 {
			continue
		}
		switch segments[1] {
		case redisKey:
			gConf.RedisConf = refreshRedisConf()
		case lpsGrpcKey:
			gConf.LpsGrpcConf = refreshGrpcConf(lpsGrpcKey)
		case lfsGrpcKey:
			gConf.LfsGrpcConf = refreshGrpcConf(lfsGrpcKey)
		case llsGrpcKey:
			gConf.LlsGrpcConf = refreshGrpcConf(llsGrpcKey)
		case locationGrpcKey:
			gConf.LocationGrpcConf = refreshGrpcConf(locationGrpcKey)
		case chargeGrpcKey:
			gConf.ChargeGrpcConf = refreshGrpcConf(chargeGrpcKey)
		case lcosGrpcKey:
			gConf.LcosGrpcConf = refreshGrpcConf(lcosGrpcKey)
		case jwtKey:
			gConf.JwtConf = refreshJWTConf()
		case s3Key:
			gConf.S3Conf = refreshS3Conf()
		case localCacheKey:
			gConf.LocalCacheConf = refreshLocalCacheConf()
		case lpsAdminKey:
			gConf.LpsJwtConf = refreshAdminConf(lpsAdminKey)
		case lpsApiKey:
			gConf.LpsApiConf = refreshApiConf(lpsApiKey)
		case lfsApiKey:
			gConf.LfsApiConf = refreshApiConf(lfsApiKey)
		case lnpApiKey:
			gConf.LnpApiConf = refreshApiConf(lnpApiKey)
		case aiApiKey:
			gConf.AiApiConf = refreshApiConf(aiApiKey)
		case twsCutoffTimeWithTimezonePrefix:
			gConf.TwsCutoffTimeWithTimezone = refreshTwsCutoffTimeWithTimezone()
		case watchVolumeSwitchKey:
			gConf.VolumeSwitchCfg = WatchVolumeSwitch()
		case layCacheKey:
			gConf.LayerCacheConf = refreshLayerCacheConf()
		case rateLimiterKey:
			gConf.RateLimiterConf = refreshRateLimiterConf()
		case BreakerConfKey:
			gConf.BreakerConf = refreshBreakerConf()
		case volumeRuleConfKey:
			gConf.VolumeRuleConf = refreshVolumeRuleConf()
		case CloseRedisCounterToDBSwitchKey:
			gConf.IsCloseRedisCounterDB = refreshCloseRedisCounterToDBSwitch()
		case dataConfKey:
			gConf.DataConf = refreshDataConf()
		case GrpcBreakerConfKey:
			gConf.GrpcBreakerConf = refreshGrpcBreakerConf()
		case allocationLogConfKey:
			gConf.AllocationLogConf = refreshAllocationLogConf()
		case batchAllocationLogConfKey:
			gConf.BatchAllocationLogConf = refreshBatchAllocationLogConf()
		case productAllocationKey:
			gConf.ProductAllocationConf = refreshProductAllocationConf()
		case fteHttpConfigKey:
			gConf.FteHttpConf = refreshHttpConf()
		case fteRpcConfigKey:
			gConf.FteRpcConf = refreshFteRpcConf()
		case lruCacheConfKey:
			gConf.LruCacheConf = refreshLruCacheConf()
		case LfsServiceClient:
			gConf.ClientTokenConfig = refreshClientToken(LfsServiceClient, gConf.ClientTokenConfig)
		case customsServiceKey:
			gConf.CustomsServiceConf = refreshCustomsServiceConf()
		case MockDomainKey:
			gConf.MockDomainConf = refreshMockDomainConf()
		case AllocateForecastErrorRateKey:
			gConf.AllocateForecastErrorRateConfig = refreshAllocateForecastErrorRateConfig()
		case AllocateForecastRateLimitKey:
			gConf.AllocateForecastRateLimitConfig = refreshAllocateForecastRateLimitConfig()
		case AllocateForecastUseMockKey:
			gConf.AllocateForecastUseMockConfig = refreshAllocateForecastUseMockConfig()
		case UseClientModConfigKey:
			gConf.UseClientModConfig = refreshUseClientModConfig()
		case RefreshHardCriteriaByTaskGoroutine:
			gConf.RefreshHardCriteriaByTaskGoroutine = RefreshRefreshHardCriteriaByTaskGoroutineConfig()
		case HardCriteriaTaskKey:
			gConf.HardCriteriaTaskConfig = RefreshHardCriteriaTaskConfig()
		case slsOpsS3ConfKey:
			gConf.SlsOpsS3Conf = refreshSlsOpsS3Conf()
		case kafkaMonitorSwitch:
			gConf.KafkaMonitorSwitch = refreshKafkaMonitorSwitch()
		case saturnNamespace:
			gConf.SaturnNamespaceConf = refreshSaturnNamespace()
		case DataHbaseKey:
			gConf.DataHbaseConfig = RefreshDataHBaseConfig()
		case MainHbaseKey:
			gConf.MainHbaseConfig = RefreshMainHbaseConfig()
		case MaskingForecastKey:
			gConf.MaskingForecastConf = RefreshMaskingForecastConf()
		case AllocationPathKey:
			gConf.AllocationPathConf = refreshAllocationPathConf()
		case wmsToggleConfKey:
			gConf.WmsToggleConf = refreshWmsToggleConf()
		case AuditLogConfKey:
			gConf.AuditLogConfig = RefreshAuditLogConfig()
		case waybillApiKey:
			gConf.WaybillApi = refreshWbcConfig()
		case routingLogWhiteListKey:
			gConf.RoutingLogWriteListConfig = refreshWriteListConfig()
		case maskingPreCalcFeeKey:
			gConf.MaskingPreCalcFeeSwitch = refreshMaskingPreCalcFeeSwitch()
		case forbidCheckDonePendingKey:
			gConf.ForbidCheckDonePending = refreshForbidCheckDonePending()
		case ScheduleVisualSwitchKey:
			gConf.ScheduleVisualSwitch = refreshScheduleVisualSwitch()
		case AddressLevelUpSwitchKey:
			gConf.AddressLevelUpConfig = RefreshAddressLevelUpSwitch()
		case antPoolKey:
			gConf.AntPoolConfig = refreshAntPoolConfig()
		case localRateLimitKey:
			gConf.LocalForecastRateLimit = refreshLocalForecastRateLimitConfig()
		case localForecastKey:
			gConf.LocalForecastConfig = refreshLocalForecastConfig()
		case rateLimitKey:
			gConf.RateLimitConfig = refreshRateLimit()
		case ScheduleVisualConfigKey:
			gConf.ScheduleVisualConfig = refreshScheduleVisualConfig()
		case routingPreCalcFeeKey:
			gConf.RoutingPreCalcFeeSwitch = refreshRoutingPreCalcFeeConfig()
		case chargeapiKey:
			gConf.ChargeApiConfig = refreshChargeApiConfig()
		case ilhForecastKey:
			gConf.IlhForecastConfig = refreshIlhForecastConfig()
		case dataApiSwitchConfKey:
			gConf.DataApiSwitchConf = refreshDataApiSwitchConf()
		case postCodeMaxUpload:
			gConf.PostCodeMaxUploadConf = refreshPostCodeMaxUploadConf()
		case volumeRoutingToggle:
			gConf.VolumeRoutingRegionToggleConf = refreshVolumeRoutingRegionToggleConf()
		case AlgoConfigKey:
			gConf.AlgoConfig = refreshAlgoConfig()
		case batchAllocateForecastKey:
			gConf.BatchAllocateForecastConf = refreshBatchAllocateForecastConf()
		case reportPoolKey:
			gConf.ECReportPoolSize = refreshReportPoolConf()
		case splitBatchConfKey:
			gConf.SplitBatchConf = refreshSplitBatchConf()
		case batchAllocateConfigKey:
			gConf.BatchAllocateConf = refreshBatchAllocateConf()
		case asyncCompressKey:
			gConf.AsyncCompressConf = RefreshAsyncCompressConf()
		case trafficCollectConfKey:
			gConf.TrafficCollectConfig = refreshTrafficCollectConfig()
		case dataMigrateSwitch:
			gConf.DataMigrateSwitchConf = refreshDataMigrateSwitchConf()
		case esConfKey:
			gConf.EsConf = refreshEsConf()
		case clickhouseKey:
			gConf.ClickHouseConfig = refreshClickHouseConfig()
		case ussWhiteHost:
			gConf.UssWhiteHost = RefreshUssWhiteHost()
		case AllocateVolumeCount:
			gConf.AllocateVolumeCountConf = refreshAllocateVolumeCountConf()
		case BusinessAuditKey:
			gConf.BusinessAuditConf = refreshBusinessAuditConf()
		case MaskVolumeCheckKey:
			gConf.MaskVolumeCheckConf = refreshMaskVolumeCheckConf()
		case volumeDashboardConfigKey:
			gConf.VolumeDashboardConfig = refreshVolumeDashboardConfig()
		case pisEmailTemplateConfKey:
			gConf.PisEmailTemplateConf = refreshPisEmailTemplateConf()
		case preVnCbToggleCheckKey:
			gConf.PreVnCbToggleCheck = refreshPreVnCbToggleCheckConfig()
		case SpexConfigKey:
			gConf.SpexConfig = refreshSpexConfig()
		case SpexCommandKey:
			gConf.SpexCommandConfig = refreshSpexCommandConfig()
		case CommonMigrationKey:
			gConf.CommonMigrationConfig = refreshMigrationConfig()
		case revampIlhRoutingKey:
			gConf.RevampILHRoutingConfig = refreshRevampILHRoutingConfig()
		}
	}
	registerGlobalConfig(gConf)
}
