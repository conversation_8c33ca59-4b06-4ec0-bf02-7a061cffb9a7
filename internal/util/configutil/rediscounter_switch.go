package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const CloseRedisCounterToDBSwitchKey = "close_rediscountertodb_switch"

func refreshCloseRedisCounterToDBSwitch() bool {
	c := false
	if err := config.UnmarshalConfig(configPrefix+CloseRedisCounterToDBSwitchKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s, err=%v", CloseRedisCounterToDBSwitchKey, err)
		return c
	}

	return c
}

func GetCloseRedisCounterToDBSwitch(ctx context.Context) bool {
	if GetGlobalConfig(ctx) == nil {
		return false
	}

	return GetGlobalConfig(ctx).IsCloseRedisCounterDB
}
