package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	AuditLogConfKey = "audit_log"
)

type AuditLogConfig struct {
	NeedRecord               bool                     `yaml:"need_record"`
	PrefixesString           string                   `yaml:"prefixes_string"`
	Prefixes                 []string                 `json:"prefixes"`
	WhiteUrlString           string                   `yaml:"white_url_string"` //在入口处对admin服务进行限制，在白名单中的url将直接被放行
	WhiteUrls                []string                 `json:"white_urls"`
	BlackTableString         string                   `yaml:"black_table_string"` //在底层对定时、异步任务进行限制，在黑名单中的table将不被记录，例如import mask zone volume时，会有大量的插入操作，这时应停止记录
	BlackTables              []string                 `json:"black_tables"`
	OperationTypeString      string                   `yaml:"operation_type_string"`
	OperationTypeList        []string                 `json:"operation_type_list"` //操作类型，用来标识url的substring
	WebHookListString        string                   `yaml:"web_hook_list_string"`
	WebHookList              []string                 `json:"web_hook_list"`
	CheckTime                int64                    `yaml:"check_time"`
	AtAll                    bool                     `yaml:"at_all"`
	MentionedListString      string                   `yaml:"mentioned_list_string"`
	MentionedList            []string                 `json:"mentioned_list"`
	MentionedEmailListString string                   `yaml:"mentioned_email_list_string"`
	MentionedEmailList       []string                 `yaml:"mentioned_email_list"`
	NotAllowToReportString   string                   `yaml:"not_allow_to_report_string"`
	NotAllowToReportList     []string                 `json:"not_allow_to_report_list"`
	SceneConfigStr           string                   `yaml:"scene_config_string"`
	SceneConfigMap           map[string][]SceneConfig `json:"scene_config_list"`
}

// use to control reporting scenario
type SceneConfig struct {
	ModelName            string `yaml:"modelName"` //todo: 注意，这里yaml标签不能带下划线，不然会unmarshal from Apollo失败，猜测是slice中的元素不支持下划线，暂未深究
	Operator             string `yaml:"operator"`
	MaskProductId        uint64 `yaml:"maskProductId"`
	FulfillmentProductId uint64 `yaml:"fulfillmentProductId"`
	RuleId               uint64 `yaml:"ruleId"`
	RuleVolumeId         uint64 `yaml:"ruleVolumeId"`
	TaskId               uint64 `yaml:"taskId"`
	ModelId              uint64 `yaml:"modelId"`
	ModuleType           string `yaml:"moduleType"`
	Interface            string `yaml:"interface"`
	ExtendInfo           string `yaml:"extendInfo"`
}

// SSCSMR-1320: get hbase conf of Data team
func GetAuditLogConfig(ctx context.Context) AuditLogConfig {
	var auditLogConfig AuditLogConfig
	if GetGlobalConfig(ctx) != nil {
		auditLogConfig = GetGlobalConfig(ctx).AuditLogConfig
	}
	logger.LogDebugf("get config GetAuditLogConfig | audit log=%+v", auditLogConfig)
	return auditLogConfig
}

// SSCSMR-1320: refresh hbase conf of Data team
func RefreshAuditLogConfig() AuditLogConfig {
	c := AuditLogConfig{}
	if err := config.UnmarshalConfig(configPrefix+AuditLogConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", AuditLogConfKey)
		return c
	}
	if err := jsoniter.UnmarshalFromString(c.PrefixesString, &c.Prefixes); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal prefixes err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.WhiteUrlString, &c.WhiteUrls); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal black urls err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.BlackTableString, &c.BlackTables); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal black tables err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.OperationTypeString, &c.OperationTypeList); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal operation type list err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.WebHookListString, &c.WebHookList); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal web hook list err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.MentionedListString, &c.MentionedList); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal web hook mentioned list err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.MentionedEmailListString, &c.MentionedEmailList); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal web hook mentioned email list err:%v", err)
	}
	if err := jsoniter.UnmarshalFromString(c.NotAllowToReportString, &c.NotAllowToReportList); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal web hook mentioned email list err:%v", err)
	}
	if c.CheckTime == 0 {
		c.CheckTime = 60 //默认检查60s之内的变更
	}

	if err := jsoniter.UnmarshalFromString(c.SceneConfigStr, &c.SceneConfigMap); err != nil {
		logger.LogErrorf("get config RefreshAuditLogConfig| unmarshal scenario config err:%v", err)
	}

	logger.LogDebugf("get config RefreshAuditLogConfig | AuditLogConfig=%v", c)
	return c
}
