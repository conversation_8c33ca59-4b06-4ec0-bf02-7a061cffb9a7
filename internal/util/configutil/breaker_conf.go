package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	BreakerConfKey     = "breaker_switch"
	GrpcBreakerConfKey = "grpc_breaker_conf"
)

type (
	BreakerConf struct {
		ApiSwitch    string `yaml:"api_switch"`    // 用于保存 key 对应的熔断开关
		GlobalSwitch bool   `yaml:"global_switch"` // 用于保存反序列化的内容
		Config       map[string]bool
	}

	GrpcBreakerConf struct {
		ClientConfig           string `yaml:"client_config"`             // 用于保存 key 对应的熔断开关
		GrpcClientGlobalSwitch bool   `yaml:"grpc_client_global_switch"` //决定是否开启客户端全局的grpc熔断
		ClientConfigMap        map[string]bool
	}
)

func refreshBreakerConf() BreakerConf {
	c := BreakerConf{
		Config: make(map[string]bool),
	}
	if err := config.UnmarshalConfig(configPrefix+BreakerConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s, err=%v", BreakerConfKey, err)
		return c
	}

	if c.ApiSwitch != "" {
		if err := jsoniter.Unmarshal([]byte(c.ApiSwitch), &c.Config); err != nil {
			logger.LogErrorf("Unmarshal config failed|key=%s, err=%v", BreakerConfKey, err)
		}
	}

	return c
}

func GetBreakerConf(ctx context.Context) BreakerConf {
	if GetGlobalConfig(ctx) == nil {
		return BreakerConf{}
	}

	return GetGlobalConfig(ctx).BreakerConf
}

// refreshGrpcBreakerConf :将Apollo上grpc 限流，熔断相关的配置反序列化到内存中
func refreshGrpcBreakerConf() GrpcBreakerConf {
	//初始化map用来保存反序列化的内容
	c := GrpcBreakerConf{
		ClientConfigMap: make(map[string]bool),
	}
	//反序列化Apollo的配置
	if err := config.UnmarshalConfig(configPrefix+GrpcBreakerConfKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s, err=%v", GrpcBreakerConfKey, err)
		return c
	}
	//将grpc客户端的配置从string反序列化到map中
	if c.ClientConfig != "" {
		if err := jsoniter.Unmarshal([]byte(c.ClientConfig), &c.ClientConfigMap); err != nil {
			logger.LogErrorf("Unmarshal client config into map failed, err=%v", err)
		}
	}

	return c
}

func GetGrpcBreakerConf(ctx context.Context) GrpcBreakerConf {
	if GetGlobalConfig(ctx) == nil {
		return GrpcBreakerConf{}
	}

	return GetGlobalConfig(ctx).GrpcBreakerConf
}
