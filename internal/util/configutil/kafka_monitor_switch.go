package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const kafkaMonitorSwitch = "kafka_monitor_switch"

type KafkaMonitorSwitch struct {
	Switch bool `yaml:"switch"`
}

func refreshKafkaMonitorSwitch() KafkaMonitorSwitch {
	var conf KafkaMonitorSwitch
	if err := config.UnmarshalConfig(configPrefix+kafkaMonitorSwitch, &conf); err != nil { // nolint
		logger.LogErrorf("refresh KafkaMonitorSwitch config fail,key:%s, err:%+v", kafkaMonitorSwitch, err)
	}
	return conf
}

func GetKafkaMonitorSwitch(ctx context.Context) KafkaMonitorSwitch {
	if GetGlobalConfig(ctx) != nil {
		return GetGlobalConfig(ctx).KafkaMonitorSwitch
	}
	return KafkaMonitorSwitch{}
}
