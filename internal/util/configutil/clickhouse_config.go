package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	clickhouseKey = "clickhouse_key"
)

type ClickHouseConfig struct {
	Host  string `yaml:"host"`
	Db    string `yaml:"db"`
	Table string `yaml:"table"`
}

func refreshClickHouseConfig() ClickHouseConfig {
	var conf ClickHouseConfig
	if err := config.UnmarshalConfig("config."+clickhouseKey, &conf); err != nil { // nolint
		logger.LogErrorf("refresh clickhouse fail, key:%s, err:%+v", clickhouse<PERSON>ey, err)
	}

	return conf
}

func GetClickHouseConfig(ctx context.Context) ClickHouseConfig {
	return GetGlobalConfig(ctx).ClickHouseConfig
}
