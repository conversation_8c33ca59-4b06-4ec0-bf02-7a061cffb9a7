package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const s3Key = "s3"

type S3Conf struct {
	Host   string `yaml:"host"`
	Access string `yaml:"access"`
	Secret string `yaml:"secret"`
}

func refreshS3Conf() S3Conf {
	var conf S3Conf
	if err := config.UnmarshalConfig(configPrefix+s3Key, &conf); err != nil { // nolint
		logger.LogErrorf("refresh s3 config fail, err:%+v", err)
	}
	return conf
}

func GetS3Conf() S3Conf {
	return GetGlobalConfigWithoutCtx().S3Conf
}
