package configutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
)

const (
	DataHbaseKey = "data_hbase"
)

type DataHbaseConfig struct {
	HostUrl            string            `yaml:"HostUrl"`
	UserName           string            `yaml:"UserName"`
	PassWord           string            `yaml:"Password"`
	AuthMethod         string            `yaml:"AuthMethod"`
	ZkRoot             string            `yaml:"ZkRoot"`
	LocalHost          bool              `yaml:"LocalHost"`
	TableNameMapString string            `yaml:"TableNameMapString"`
	TableNameMap       map[string]string `json:"table_name_map"`
	MaxLoopSize        int               `yaml:"max_loop_size"`
	SaltSize           int               `yaml:"salt_size"`
	BatchSize          uint32            `yaml:"batch_size"`
}

// SSCSMR-1320: get hbase conf of Data team
func GetDataHbaseConfig(ctx context.Context) DataHbaseConfig {
	var hbaseConfig DataHbaseConfig
	if GetGlobalConfig(ctx) != nil {
		hbaseConfig = GetGlobalConfig(ctx).DataHbaseConfig
	}
	logger.LogDebugf("get config GetHbaseConfig | hbaseConfig=%+v", hbaseConfig)
	return hbaseConfig
}

// SSCSMR-1320: refresh hbase conf of Data team
func RefreshDataHBaseConfig() DataHbaseConfig {
	c := DataHbaseConfig{}
	if err := config.UnmarshalConfig(configPrefix+DataHbaseKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal config failed|key=%s", DataHbaseKey)
		return c
	}
	tableNameMap := make(map[string]string, 0)
	if err := jsoniter.Unmarshal([]byte(c.TableNameMapString), &tableNameMap); err != nil {
		logger.LogErrorf("Unmarshal table name map string err:%v", err)
	} else {
		c.TableNameMap = tableNameMap
	}
	if c.BatchSize == 0 {
		c.BatchSize = 1000
	}
	logger.LogDebugf("get config RefreshDataHBaseConfig | hbaseConfig=%v", c)
	return c
}
