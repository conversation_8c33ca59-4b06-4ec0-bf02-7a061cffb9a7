package configutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const dsnConfPrefix = "dbs"

const DefaultSlowQueryTime = 200 // 慢sql上报阈值默认设置为200ms

type DSNConf struct {
	ConnMaxAge                      int               `yaml:"conn_max_age"` //链接最大可存活时间，单位：s
	MaxIdleConn                     int               `yaml:"max_idle_conn"`
	MaxOpenConn                     int               `yaml:"max_open_conn"`
	SmartRoutingMaster              string            `yaml:"smart_routing_master"`
	SmartRoutingSlave               string            `yaml:"smart_routing_slave"`
	SmartRoutingSg11Master          string            `yaml:"smart_routing_sg11_master"`
	SmartRoutingSg11Slave           string            `yaml:"smart_routing_sg11_slave"`
	SmartRoutingUs2Master           string            `yaml:"smart_routing_us2_master"`
	SmartRoutingUs2Slave            string            `yaml:"smart_routing_us2_slave"`
	SmartRoutingBatchAllocateMaster string            `yaml:"smart_routing_batch_allocate_master"`
	SmartRoutingBatchAllocateSlave  string            `yaml:"smart_routing_batch_allocate_slave"`
	LfsOrderRead                    map[string]string `yaml:"ssc_lfs_order_read"`
	LhsCidRead                      string            `yaml:"ssc_lhs_cid_read"`
	DisableShadowDBForReadOnly      bool              `yaml:"disable_shadow_db_for_read_only"`
	SlowQueryTime                   int               `yaml:"slow_query_time"`
}

func refreshDSNConf() DSNConf {
	var conf DSNConf
	if err := config.UnmarshalConfig(configPrefix+dsnConfPrefix, &conf); err != nil { // nolint
		logger.LogErrorf("refresh saturn kafka config fail, err:%+v", err)
	}
	return conf
}

func GetDSNConf() DSNConf {
	dsnConf := GetGlobalConfigWithoutCtx().DsnConf
	if dsnConf.SlowQueryTime == 0 {
		dsnConf.SlowQueryTime = DefaultSlowQueryTime
	}
	return dsnConf
}
