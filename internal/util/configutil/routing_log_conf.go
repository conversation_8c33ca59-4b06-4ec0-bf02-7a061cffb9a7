package configutil

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
)

const (
	routingLogWhiteListKey = "routing_log_write_list"
)

type (
	RoutingLogWriteListConfig struct {
		ListString string `yaml:"list"`
		List       []int  `json:"list"`
	}
)

func refreshWriteListConfig() RoutingLogWriteListConfig {
	c := RoutingLogWriteListConfig{}
	if err := config.UnmarshalConfig("config."+routingLogWhiteListKey, &c); err != nil { // nolint
		logger.LogErrorf("Unmarshal white list failed|key=%s, err=%v", routingLogWhiteListKey, err)
		return c
	}
	c.List = []int{}
	err := json.Unmarshal([]byte(c.ListString), &c.List)
	if err != nil {
		logger.LogErrorf("Unmarshal white list failed|key=%s, err=%v", routingLogWhiteListKey, err)
	}

	return c
}

func GetWriteList(ctx context.Context) RoutingLogWriteListConfig {
	if GetGlobalConfig(ctx) == nil {
		return RoutingLogWriteListConfig{List: []int{}}
	}
	if GetGlobalConfig(ctx).RoutingLogWriteListConfig.List == nil {
		GetGlobalConfig(ctx).RoutingLogWriteListConfig.List = []int{}
	}
	return GetGlobalConfig(ctx).RoutingLogWriteListConfig
}
