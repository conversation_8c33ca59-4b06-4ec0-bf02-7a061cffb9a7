package itemutil

import "github.com/gogo/protobuf/sortkeys"

/*
从lps迁移过来，业务逻辑和lps保持一致，为了避免后续歧义，方法名和lps保持一致
*/

const defaultPath = 3

// GetGlobalCategoryByPathV2 用于item新数据结构
func GetGlobalCategoryByPathV2(CatIds []uint32) uint32 {
	paths := len(CatIds)
	for i := paths; i > 0; i-- {
		if CatIds[i-1] > 0 {
			return CatIds[i-1]
		}
	}
	return 0
}

// GetLocalCategoryByPathV2 用于item新数据结构
func GetLocalCategoryByPathV2(CatIds []uint32) uint32 {
	if len(CatIds) == 0 || len(CatIds) < defaultPath {
		return 0
	}
	return CatIds[defaultPath-1]
}

// GetGlobalCategoryIdListV2 用于item新数据结构
func GetGlobalCategoryIdListV2(CatIds []uint32) []uint64 {
	length := len(CatIds)
	globalCategoryIdList := make([]uint64, 0, length)
	if len(CatIds) == 0 {
		return globalCategoryIdList
	}
	for _, catId := range CatIds {
		globalCategoryIdList = append(globalCategoryIdList, uint64(catId))
	}
	sortkeys.Uint64s(globalCategoryIdList)
	return globalCategoryIdList
}
