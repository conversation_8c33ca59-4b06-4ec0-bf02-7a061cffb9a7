package fileutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"net/url"
	"strings"
	"time"
)

const (
	external2Prefix = "https://external2.shopee.sg" // nolint
)

// SSCSMR-1216:init sls ops s3
var slsOpsSess *session.Session

func SlsOpsS3Init() error {
	slsOpsSess = session.Must(newSlsOpsSession())
	return nil
}

func newSlsOpsSession() (*session.Session, error) {
	// 上游无context传入
	conf := configutil.GetSlsOpsS3Conf()
	return session.NewSession(&aws.Config{
		Credentials:          credentials.NewStaticCredentials(conf.Access, conf.Secret, ""),
		Endpoint:             aws.String(conf.Host),
		Region:               aws.String("us-east-1"),
		DisableSSL:           aws.Bool(true),
		S3ForcePathStyle:     aws.Bool(true),
		S3Disable100Continue: aws.Bool(false),
	})
}

func GetS3UrlForSlsOps(ctx context.Context, s3Url string) string {
	// 做一层保护
	if slsOpsSess == nil {
		logger.CtxLogErrorf(ctx, "sls ops session not init, return origin s3 url")
		return s3Url
	}

	var err error
	s3Url, err = url.PathUnescape(s3Url)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetS3UrlForSlsOps| origin s3 url: %v, path unescape err:%v", s3Url, err)
		return s3Url
	}

	//split s3Url to get non-key string by "?", like https://xxxxx.xxxx.xxx/xxx/xxx/xx?key=xxxx
	nonKeyStrings := strings.Split(s3Url, "?")

	//split s3Url to get file path by "/"
	filePathStrings := strings.Split(nonKeyStrings[0], "/")

	//get bucket
	conf := configutil.GetSlsOpsS3Conf()

	key := strings.Join(filePathStrings[len(filePathStrings)-4:], "/")

	s3Client := s3.New(slsOpsSess)
	req, _ := s3Client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(conf.BucketMap["sls_ops"]),
		Key:    aws.String(key),
	})
	urlStr, err := req.Presign(24 * time.Hour) //expire time will last for 1 day
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetS3UrlForSlsOps|s3Url:%v, err:%v", s3Url, err)
		return ""
	}

	return strings.Replace(urlStr, "http", "https", 1)
}

func GetLocalVolumeS3Url(ctx context.Context, s3Url string) string {

	return GetS3UrlForSlsOps(ctx, s3Url)
}
