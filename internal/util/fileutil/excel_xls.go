package fileutil

import (
	"bytes"
	"context"
	"github.com/shakinm/xlsReader/xls"
	"strings"
)

func ParseXlsFile(ctx context.Context, fileData []byte) ([][]string, []string, error) {
	wb, err := xls.OpenReader(bytes.NewReader(fileData))
	if err != nil {
		return nil, nil, err
	}
	sheet, err := wb.GetSheet(0)
	if err != nil {
		return nil, nil, err
	}
	rn := sheet.GetNumberRows()
	// 忽略表头
	var result [][]string
	var header []string
	for i := 0; i < rn; i++ {
		if row, e := sheet.GetRow(i); e == nil && row != nil {
			cols := row.GetCols()
			if cols == nil || len(cols) < 1 {
				continue
			}
			coLen := len(cols)
			var rowResult []string
			for j := 0; j < coLen; j++ {
				rowResult = append(rowResult, strings.TrimSpace(cols[j].GetString()))
			}
			if i == 0 {
				header = rowResult
			} else {
				if !isEmptyRow(rowResult) {
					result = append(result, rowResult)
				}
			}
		}
	}
	return result, header, nil
}

func isEmptyRow(columns []string) bool {
	for _, column := range columns {
		if column != "" {
			return false
		}
	}

	return true
}

func IsEmptyRow(columns []string) bool {
	return isEmptyRow(columns)
}
