package fileutil

import (
	"context"
	"encoding/csv"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"io"
	"mime/multipart"
	"reflect"
	"strconv"
	"strings"
	"time"
)

const TimeError = 1e-9

const (
	DefaultSheet = "Sheet1"
)

func ParseExcel(ctx context.Context, reader io.Reader, includingHeader bool) ([][]string, []string, error) {
	file, err := excelize.OpenReader(reader)
	if err != nil {
		return nil, nil, err
	}
	sheets := file.GetSheetList()
	if len(sheets) == 0 {
		return nil, nil, errors.New("sheets.len==0")
	}
	data, err := file.GetRows(sheets[0])
	if err != nil {
		return nil, nil, err
	}
	if !includingHeader {
		return data, nil, nil
	}
	if len(data) == 1 {
		return make([][]string, 0), data[0], nil
	}
	header := data[0]
	for i := 1; i < len(data); i++ {
		olen := len(data[i])
		for j := 0; j < len(header)-olen; j++ {
			data[i] = append(data[i], "")
		}
	}
	return data[1:], header, nil
}

func MakeExcel(ctx context.Context, header []string, tab [][]string, sheetName string) (*excelize.File, error) {
	if sheetName == "" {
		sheetName = "Sheet1"
	}
	file := excelize.NewFile()
	index := file.NewSheet(sheetName)
	var begin int
	if len(header) > 0 {
		for i := 0; i < len(header); i++ {
			_ = file.SetCellStr(sheetName, getAxis(0, i), header[i])
		}
		begin = 1
	}
	for i := 0; i < len(tab); i++ {
		for j := 0; j < len(tab[i]); j++ {
			if err := file.SetCellStr(sheetName, getAxis(begin+i, j), tab[i][j]); err != nil {
				continue
			}
		}
	}
	file.SetActiveSheet(index)
	return file, nil
}

func MakeExcelWithMultiSheet(ctx context.Context, fileInfoList []*FileInfo) (*excelize.File, error) {
	file := excelize.NewFile()
	for index, fileInfo := range fileInfoList {
		if index == 0 {
			file.SetSheetName(file.GetSheetName(index), fileInfo.SheetName)
		} else {
			file.NewSheet(fileInfo.SheetName)
		}
		var begin int
		if len(fileInfo.Header) > 0 {
			for i := 0; i < len(fileInfo.Header); i++ {
				_ = file.SetCellStr(fileInfo.SheetName, getAxis(0, i), fileInfo.Header[i])
			}
			begin = 1
		}
		for i := 0; i < len(fileInfo.Data); i++ {
			for j := 0; j < len(fileInfo.Data[i]); j++ {
				if err := file.SetCellStr(fileInfo.SheetName, getAxis(begin+i, j), fileInfo.Data[i][j]); err != nil {
					continue
				}
			}
		}
	}
	file.SetActiveSheet(0)
	return file, nil
}

func AppendExcel(ctx context.Context, file *excelize.File, header []string, tab [][]string, sheetName string, needWrap bool) (*excelize.File, error) {
	existedIndex := file.GetSheetIndex(sheetName)
	if existedIndex != -1 {
		return nil, srerr.New(srerr.ParamErr, nil, "duplicate sheet name")
	}
	index := file.NewSheet(sheetName)
	var begin int
	if len(header) > 0 {
		for i := 0; i < len(header); i++ {
			_ = file.SetCellStr(sheetName, getAxis(0, i), header[i])
		}
		begin = 1
	}
	// 设置单元格样式，包括自动换行
	style, _ := file.NewStyle(`{"alignment":{"wrap_text":true}}`)
	for i := 0; i < len(tab); i++ {
		for j := 0; j < len(tab[i]); j++ {
			err := file.SetCellStr(sheetName, getAxis(begin+i, j), tab[i][j])
			if err != nil {
				continue
			}
			if needWrap { //需要自动换行
				_ = file.SetCellStyle(sheetName, getAxis(begin+i, j), getAxis(begin+i, j), style)
			}
		}
	}
	file.SetActiveSheet(index)
	return file, nil
}

func MakeExcelWithSheetName(ctx context.Context, header []string, tab [][]string, sheetName string) (*excelize.File, error) {
	defaultSheetName := "Sheet1"
	file := excelize.NewFile()
	var begin int
	if len(header) > 0 {
		for i := 0; i < len(header); i++ {
			_ = file.SetCellStr(defaultSheetName, getAxis(0, i), header[i])
		}
		begin = 1
	}
	for i := 0; i < len(tab); i++ {
		for j := 0; j < len(tab[i]); j++ {
			if err := file.SetCellStr(defaultSheetName, getAxis(begin+i, j), tab[i][j]); err != nil {
				continue
			}
		}
	}
	if sheetName != "" {
		file.SetSheetName(defaultSheetName, sheetName)
	}
	return file, nil
}

func MakeExcelWithSheetNames(ctx context.Context, header []string, dataWithSheetName map[string][][]string) (*excelize.File, error) {
	file := excelize.NewFile()
	//定义一个下标替换掉默认的sheet
	index := 0
	defaultSheetName := "Sheet1"
	for sheetName, data := range dataWithSheetName {
		if index == 0 {
			file.SetSheetName(defaultSheetName, sheetName)
		} else {
			file.NewSheet(sheetName)
		}
		var begin int
		if len(header) > 0 {
			for i := 0; i < len(header); i++ {
				_ = file.SetCellStr(sheetName, getAxis(0, i), header[i])
			}
			begin = 1
		}
		for i := 0; i < len(data); i++ {
			for j := 0; j < len(data[i]); j++ {
				if err := file.SetCellStr(sheetName, getAxis(begin+i, j), data[i][j]); err != nil {
					continue
				}
			}
		}
		index++
	}

	return file, nil
}

// ExcelRowsToInterface 对整个excel进行转换，传入指针类型，缺点，返回的是数组，转回实际struct太麻烦
func ExcelRowsToInterface(rows [][]string, target interface{}) interface{} {
	var targetList []interface{}
	for _, row := range rows {
		targetPtrType := reflect.TypeOf(target)
		targetTrueType := targetPtrType.Elem()
		targetPtrValue := reflect.New(targetTrueType)
		targetTrueValue := targetPtrValue.Elem()
		for k := 0; k < len(row); k++ {
			targetTrueValue.Field(k).Set(reflect.ValueOf(row[k]))
		}
		targetList = append(targetList, targetTrueValue.Interface())
	}
	return targetList
}

// ExcelRowToInterface 当行赋值，传入struct的指针，直接赋值，不用在外层进行断言
func ExcelRowToInterface(row []string, target interface{}, column int) {
	targetValue := reflect.ValueOf(target).Elem()
	for k := 0; k < column; k++ {
		//接收的entity字段类型定义为string，防止字段类型转换出错，导致整个转换失败，看不到具体的错误信息
		targetValue.Field(k).Set(reflect.ValueOf(strings.TrimSpace(row[k])))
	}
}

func getAxis(rowN, colN int) string {
	return toAlphaString(colN) + strconv.Itoa(rowN+1)
}

func toAlphaString(value int) string {
	if value < 0 {
		return ""
	}
	var ans string
	i := value + 1
	for i > 0 {
		ans = string(rune((i-1)%26+65)) + ans
		i = (i - 1) / 26
	}
	return ans
}

func GetRows(file multipart.File) ([][]string, *srerr.Error) {
	xlsx, err := excelize.OpenReader(file)
	if err != nil {
		_, _ = file.Seek(0, io.SeekStart)
		rows, csvErr := getCsvRows(file)
		if csvErr == nil {
			return rows, nil
		}
		return nil, srerr.With(srerr.ExcelFileOpenError, nil, err)
	}
	sheetList := xlsx.GetSheetList()
	if len(sheetList) <= 0 {
		return nil, srerr.With(srerr.ExcelFileOpenError, "no workbook sheet", err)
	}
	rows, err := xlsx.GetRows(sheetList[0])
	if err != nil {
		return nil, srerr.With(srerr.ExcelFileOpenError, sheetList[0], err)
	}

	return rows, nil
}

func CheckMustFieldExisted(field []string, header []string) bool {
	for _, item := range field {
		ok := objutil.ContainStr(header, item)
		if !ok {
			return false
		}
	}
	return true
}

func CheckEmpty(target string) bool {
	return target == ""
}

func CheckLenGrater(target string, val int) bool {
	return len(target) > val
}

func getCsvRows(reader io.Reader) ([][]string, error) {
	r := csv.NewReader(reader)
	return r.ReadAll()
}

func GetAxis(rowN, colN int) string {
	return getAxis(rowN, colN)
}

func ReadOriginalData(file io.ReadCloser, sheet string) ([][]string, *srerr.Error) {
	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	rows, err := f.GetRows(sheet)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	return rows, nil
}

func RowsToData(rows [][]string, rowLength int) ([][]string, *srerr.Error) {
	data := make([][]string, 0, len(rows)-1)
	for _, row := range rows[1:] {
		if !checkEmpty(row) {
			break
		}
		data = append(data, fillRow(row, rowLength))
	}
	if len(data) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "File is blank")
	}

	return data, nil
}

func ReadData(file io.ReadCloser, sheet string, headers []string) ([][]string, *srerr.Error) {
	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	rows, err := f.GetRows(sheet)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	if !checkHeaders(rows, headers) {
		return nil, srerr.New(srerr.ParamErr, nil, "excel headers invalid, must have %v", headers)
	}
	data := make([][]string, 0, len(rows)-1)
	for _, row := range rows[1:] {
		if !checkEmpty(row) {
			break
		}
		data = append(data, fillRow(row, len(headers)))
	}
	if len(data) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "File is blank")
	}

	return data, nil
}

func checkHeaders(rows [][]string, headers []string) bool {
	if len(rows) == 0 {
		return false
	}
	if len(rows[0]) != len(headers) {
		return false
	}
	for idx, val := range rows[0] {
		if headers[idx] != val {
			return false
		}
	}

	return true
}

func checkEmpty(row []string) bool {
	for _, item := range row {
		if item != "" {
			return true
		}
	}
	return false
}

func fillRow(row []string, length int) []string {
	if len(row) >= length {
		return row
	}
	for i := len(row); i < length; i++ {
		row = append(row, "")
	}
	return row
}

// ExcelDateStringToDate 将excel的文本格式日期（数字）转成日期格式
func ExcelDateStringToDate(excelData string) time.Time {
	excelTime := time.Date(1899, time.December, 30, 0, 0, 0, 0, time.UTC)
	days, _ := strconv.ParseFloat(excelData, 64)
	daysDecimal := decimal.NewFromFloat(days)
	timeError := decimal.NewFromFloat(TimeError)
	daysDecimal = daysDecimal.Add(timeError)
	secondUnit := decimal.NewFromInt(86400)
	secondNum := daysDecimal.Mul(secondUnit)
	return excelTime.Add(time.Second * time.Duration(secondNum.IntPart()))
}
