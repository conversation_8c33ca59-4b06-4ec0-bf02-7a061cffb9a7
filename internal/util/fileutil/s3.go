package fileutil

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/hex"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"
)

const (
	smartRoutingTask       = "smart-routing-task-"
	securityKey            = "ohjoPhie1oofoa5g"
	tokenTTL         int64 = 3600 * 24
	aesIv                  = "pi9eoPh1ahpiyies"
)

var sess *session.Session
var uploader *s3manager.Uploader
var downloader *s3manager.Downloader

func S3Init() error {
	sess = session.Must(newSession())
	uploader = s3manager.NewUploader(sess)
	downloader = s3manager.NewDownloader(sess)
	if err := initSmartRoutingBucket(); err != nil {
		return err
	}
	return nil
}

func initSmartRoutingBucket() error {
	existBucketNames, _ := listBuckets()
	var needCreateBucketNames []string
	for i := 0; i < 12; i++ {
		bucketName := objutil.Merge(
			smartRoutingTask,
			strings.ToLower(envvar.GetEnv()), "-",
			strings.ToLower(envvar.GetCID()), "-",
			strconv.Itoa(i+1),
		)
		needCreateBucketNames = append(needCreateBucketNames, bucketName)
	}

	var toCreateBucketNames []string
	if len(existBucketNames) == 0 {
		toCreateBucketNames = needCreateBucketNames
	} else {
		for _, needCreateName := range needCreateBucketNames {
			needCreate := true
			for _, existName := range existBucketNames {
				if needCreateName == existName {
					needCreate = false
					break
				}
			}
			if needCreate {
				toCreateBucketNames = append(toCreateBucketNames, needCreateName)
			}
		}
	}

	for _, needCreateName := range toCreateBucketNames {
		if err := CreateBucket(needCreateName); err != nil {
			return err
		}
	}

	return nil
}

func newSession() (*session.Session, error) {
	conf := configutil.GetS3Conf()
	return session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(conf.Access, conf.Secret, ""),
		Endpoint:         aws.String(conf.Host),
		Region:           aws.String("us-east-1"),
		DisableSSL:       aws.Bool(true),
		S3ForcePathStyle: aws.Bool(true),
	})
}

func Upload(ctx context.Context, bucket string, key string, reader io.Reader) error {
	if uploader == nil {
		return fmt.Errorf("s3 uploader not init")
	}

	// Upload the file to S3.
	_, err := uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		Body:   reader,
	})
	if err != nil {
		logger.CtxLogInfof(ctx, "file uploaded to location fail, bucket:%s. key:%s, err:%+v", bucket, key, err)
		return fmt.Errorf("failed to upload file, %v", err)
	}
	return nil
}

func Download(ctx context.Context, bucket string, key string) ([]byte, error) {
	if downloader == nil {
		return nil, fmt.Errorf("s3 downloader not init")
	}

	w := &aws.WriteAtBuffer{}
	// Write the contents of S3 Object to the file
	_, err := downloader.Download(w, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		logger.CtxLogInfof(ctx, "download file fail, bucket:%s. key:%s, err:%+v", bucket, key, err)
		return nil, fmt.Errorf("failed to download file, %v", err)
	}
	return w.Bytes(), nil
}

func CreateBucket(name string) error {
	svc := s3.New(sess)
	_, err := svc.CreateBucket(&s3.CreateBucketInput{
		Bucket: aws.String(name),
	})
	return err
}

func listBuckets() ([]string, error) {
	svc := s3.New(sess)
	result, err := svc.ListBuckets(nil)

	if err != nil {
		return nil, err
	}

	var bucketNames []string
	for _, b := range result.Buckets {
		bucketNames = append(bucketNames, aws.StringValue(b.Name))
	}

	return bucketNames, nil
}

func ListObjects(bucket string) ([]*string, error) {
	svc := s3.New(sess)

	params := &s3.ListObjectsInput{
		Bucket: aws.String(bucket),
	}
	resp, err := svc.ListObjects(params)
	if err != nil {
		return nil, err
	}
	var keys []*string
	if len(resp.Contents) > 0 {
		for _, content := range resp.Contents {
			keys = append(keys, content.Key)
		}
	}
	return keys, nil
}

// must delete all objects before
func DeleteEmptyBucket(bucketName string) {
	svc := s3.New(sess)

	_, _ = svc.DeleteBucket(&s3.DeleteBucketInput{
		Bucket: aws.String(bucketName),
	})
	_ = svc.WaitUntilBucketNotExists(&s3.HeadBucketInput{
		Bucket: aws.String(bucketName),
	})
}

func DeleteObject(bucketName string, objectName string) {
	svc := s3.New(sess)
	_, err := svc.DeleteObject(&s3.DeleteObjectInput{Bucket: aws.String(bucketName), Key: aws.String(objectName)})
	if err != nil {

	}
	err = svc.WaitUntilObjectNotExists(&s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectName),
	})
}

func DeleteAllObjectOfBucket(bucketName string) {
	for i := 0; i < 1000; i++ {
		objs, _ := ListObjects(bucketName)
		if objs == nil {
			break
		}
		for _, obj := range objs {
			DeleteObject(bucketName, *obj)
		}
	}
}

func GetBucketName(timestamp int64) string {
	month := time.Unix(timestamp, 0).Month()
	return objutil.Merge(
		smartRoutingTask,
		strings.ToLower(envvar.GetEnv()), "-",
		strings.ToLower(envvar.GetCID()), "-",
		strconv.Itoa(int(month)),
	)
}

func GetS3Url(ctx context.Context, bucket string, key string) string {
	if sess == nil {
		logger.CtxLogErrorf(ctx, "s3 session not init")
		return ""
	}

	s3Client := s3.New(sess)
	req, _ := s3Client.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	urlStr, err := req.Presign(24 * time.Hour * 7) //expire time will last for max 7 days
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetS3Url|bucket:%v, key:%v, err:%v", bucket, key, err)
		return ""
	}

	return strings.Replace(urlStr, "http", "https", 1)
}

func getFileNameFromS3Url(s3Url string) string {
	// remove params
	s3Url = strings.Split(s3Url, "?")[0]
	splits := strings.Split(s3Url, "/")

	return splits[len(splits)-1]
}

func GetS3UrlWithNewKey(ctx context.Context, s3Url string) string {
	fileName := getFileNameFromS3Url(s3Url)
	key, err := AesCbcEncrypt(ctx, fileName)
	if err != nil {
		return s3Url
	}

	s3Url = strings.Split(s3Url, "?")[0]

	return s3Url + "?key=" + key
}

func AesCbcEncrypt(ctx context.Context, plainText string) (string, error) {
	text := []byte(plainText)
	block, err := aes.NewCipher([]byte(securityKey))
	if err != nil {
		return "", err
	}
	expireTime := recorder.Now(ctx).Unix() + tokenTTL
	text = []byte(fmt.Sprintf("%s %d", string(text), expireTime))

	text = padding(text, block.BlockSize())
	blockMode := cipher.NewCBCEncrypter(block, []byte(aesIv))
	cipherText := make([]byte, len(text))
	blockMode.CryptBlocks(cipherText, text)
	return hex.EncodeToString(cipherText), nil
}

func padding(plainText []byte, blockSize int) []byte {
	needPad := blockSize - len(plainText)%blockSize
	sPad := ""
	for i := 0; i < needPad; i++ {
		sPad += "&"
	}
	return []byte(string(plainText) + sPad)
}

func GetS3UrlByUrl(ctx context.Context, s3Url string) string {
	var err error
	s3Url, err = url.PathUnescape(s3Url)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetS3UrlForSlsOps| origin s3 url: %v, path unescape err:%v", s3Url, err)
		return s3Url
	}

	//split s3Url to get non-key string by "?", like https://xxxxx.xxxx.xxx/xxx/xxx/xx?key=xxxx
	nonKeyStrings := strings.Split(s3Url, "?")
	if len(nonKeyStrings) == 0 {
		logger.CtxLogErrorf(ctx, "illegal s3 url:%s", s3Url)
		return ""
	}
	details := strings.Split(nonKeyStrings[0], "/")
	length := len(details)
	return GetS3Url(ctx, details[length-2], details[length-1])
}
