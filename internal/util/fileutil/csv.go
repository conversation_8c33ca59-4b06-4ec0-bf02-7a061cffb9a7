package fileutil

import (
	"bufio"
	"bytes"
	"encoding/csv"
	"io"
	"strings"
)

const ReadRowRestrict = 1000000

func ReadCsv(fileData []byte) ([][]string, []string) {
	r := csv.NewReader(bufio.NewReader(bytes.NewReader(fileData)))
	var records [][]string
	var header []string
	var recordIndex int
	var myIndex int
	for {
		// 防止死循环
		if myIndex > ReadRowRestrict {
			break
		}
		record, err := r.Read()
		if err == io.EOF {
			break
		}
		if myIndex >= 1 {
			// remove bom head
			if recordIndex == 0 {
				for index := range record {
					if index == 0 && strings.Contains(record[index], "\ufeff") {
						record[index] = strings.Replace(record[index], "\ufeff", "", 1)
						break
					}
				}
				recordIndex += 1
			}
			records = append(records, record)
		} else {
			header = record
		}
		myIndex++
	}
	return records, header
}
