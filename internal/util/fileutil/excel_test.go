package fileutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"testing"
)

func invTestParseExcel(t *testing.T) {
	tests := []struct {
		k        string
		filePath string
	}{
		{
			k:        "no header",
			filePath: "/Users/<USER>/Downloads/no_header.xlsx",
		},
		{
			k:        "header",
			filePath: "/Users/<USER>/Downloads/header.xlsx",
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			file, fErr := os.Open(filepath.Clean(tt.filePath))
			if fErr != nil {
				t.Errorf("read file fail, err:%+v", fErr)
				return
			}
			switch tt.k {
			case "no header":
				data, header, err := ParseExcel(context.TODO(), file, false)
				assert.Nil(t, err)
				assert.Equal(t, 0, len(header))
				assert.Equal(t, 10, len(data))
				t.Logf("result:%v", objutil.JsonString(data))
			case "header":
				data, header, err := ParseExcel(context.TODO(), file, true)
				assert.Nil(t, err)
				assert.Equal(t, 8, len(header))
				assert.Equal(t, 10, len(data))
				assert.Equal(t, len(header), len(data[1]))
				t.Logf("result:%v", objutil.JsonString(data))
			}
		})
	}
}

func TestMakeExcel(t *testing.T) {
	//
	cepRangeZoneImportResultHeader := []string{"*Action mode (1=add;-1=delete)", "*Zone Name", "*Region", "*CEP Initial", "*CEP Final"}

	datarows := [][]string{}

	gap := 100
	for p := 0; p < 7000; p = p + 2 {
		//imported,
		// p*Gaps , [0,gap], 0-100,200-300,400-.
		datarows = append(datarows, []string{"1", "z" + strconv.Itoa(p%100), "BR", strconv.Itoa(p * gap), strconv.Itoa((p + 1) * gap)})

	}
	f, err := MakeExcel(context.TODO(), cepRangeZoneImportResultHeader, datarows, "")
	if err != nil {
		fmt.Println("check,", err)
		return
	}
	f.SaveAs("local.hugh.xlsx")

}

func invTestMakeExcel(t *testing.T) {
	tests := []struct {
		k        string
		header   []string
		data     [][]string
		filename string
	}{
		{
			k:        "header",
			header:   []string{"T1", "T2", "T3"},
			data:     [][]string{{"1", "2", "3"}, {"4", "5", "6"}, {"7", "8", "9"}},
			filename: "./header.xlsx",
		},
		{
			k:        "no header",
			data:     [][]string{{"11", "12", "13"}, {"14", "15", "16"}, {"17", "18", "19"}},
			filename: "./no header.xlsx",
		},
		{
			k:        "template",
			header:   []string{"T1", "T2", "T3"},
			filename: "./template.xlsx",
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			f, err := MakeExcel(context.TODO(), tt.header, tt.data, "")
			if err != nil {
				t.Errorf("make excel fail, err:%+v", err)
				return
			}
			_ = f.SaveAs(tt.filename)
		})
	}
}

type ProductPriorityImport struct {
	MaskingProductId     string `json:"masking_product_id"`
	ShopGroupId          string `json:"shop_group_id"`
	RuleType             string `json:"rule_type"`
	EffectiveTime        string `json:"effective_time"`
	FulfillmentProductId string `json:"fulfillment_product_id"`
	Value                string `json:"value"`
	ToggleStatus         string `json:"toggle_status"`
	ActionCode           string `json:"action_code"`
}

func TestExcelRowsToInterface(t *testing.T) {
	var priorityImport *ProductPriorityImport

	ptrType := reflect.TypeOf(priorityImport) //获取call的指针的reflect.Type

	trueType := ptrType.Elem() //获取type的真实类型

	ptrValue := reflect.New(trueType) //返回对象的指针对应的reflect.Value

	//priorityImport = ptrValue.Interface().(*ProductPriorityImport)

	trueValue := ptrValue.Elem() //获取真实的结构体类型

	trueValue.FieldByName("MaskingProductId").Set(reflect.ValueOf("123")) //设置对象属性，注意这个一定要是真实的结构类型的reflect.Value才能调用,指针类型reflect.Value的会报错
	//ptrValue.FieldByName("Num2").SetInt(23)
	trueValue.FieldByName("ShopGroupId").Set(reflect.ValueOf("123"))

	for k := 0; k < trueValue.NumField(); k++ {
		trueValue.Field(k).Set(reflect.ValueOf("456"))
	}

	fmt.Println(priorityImport)

	var testTotal [][]string
	var test []string
	test = append(test, "8000")
	test = append(test, "26")
	test = append(test, "Weightage")
	test = append(test, "8000")
	test = append(test, "80012")
	test = append(test, "1")
	test = append(test, "on")
	test = append(test, "2")
	testTotal = append(testTotal, test)
	testTotal = append(testTotal, test)
	testTotal = append(testTotal, test)
	importV := new(ProductPriorityImport)
	ExcelRowToInterface(test, importV, 0)

	fmt.Println(importV)
}

func TestExcelDateStringToDate(t *testing.T) {
	date := ExcelDateStringToDate("44875.4590740741")
	fmt.Println(date)
}

func TestExcelSelect(t *testing.T) {
	// create a new spreadsheet
	f := excelize.NewFile()
	var (
		// cell values
		data = [][]interface{}{
			{"Fruits", "Vegetables"},
			{"Mango", "Potato", nil, "Drop Down 1", "Drop Down 2"},
			{"Apple", "Tomato"},
			{"Grapes", "Spinach"},
			{"Strawberry", "Onion"},
			{"Kiwi", "Cucumber"},
		}
		addr string
		err  error
		//cellsStyle, headerStyle int
	)
	// set each cell value
	for r, row := range data {
		if addr, err = excelize.JoinCellName("A", r+1); err != nil {
			fmt.Println(err)
			return
		}
		if err = f.SetSheetRow("Sheet1", addr, &row); err != nil {
			fmt.Println(err)
			return
		}
	}
	// set data validation
	dvRange1 := excelize.NewDataValidation(true)
	dvRange1.Sqref = "F1:F65535"
	//dvRange1.SetSqrefDropList("$A$1:$A$6", true)
	list1 := []string{"1", "2", "3"}
	dvRange1.SetDropList(list1)
	if err = f.AddDataValidation("Sheet1", dvRange1); err != nil {
		fmt.Println(err)
		return
	}
	//dvRange2 := excelize.NewDataValidation(true)
	//dvRange2.Sqref = "E3:E3"
	//dvRange2.SetSqrefDropList("INDIRECT(D3)", true)
	//if err = f.AddDataValidation("Sheet1", dvRange2); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	// set defined name
	//if err = f.SetDefinedName(&excelize.DefinedName{
	//	Name:     "Fruits",
	//	RefersTo: "Sheet1!$A$2:$A$6",
	//	Scope:    "Sheet1",
	//}); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//if err = f.SetDefinedName(&excelize.DefinedName{
	//	Name:     "Vegetables",
	//	RefersTo: "Sheet1!$B$2:$B$6",
	//	Scope:    "Sheet1",
	//}); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	// set custom column width
	//for col, width := range map[string]float64{
	//	"A": 12, "B": 12, "C": 6, "D": 12, "E": 12} {
	//	if err = f.SetColWidth("Sheet1", col, col, width); err != nil {
	//		fmt.Println(err)
	//		return
	//	}
	//}
	//// hide gridlines for the worksheet
	//if err = f.SetSheetViewOptions("Sheet1", 0,
	//	excelize.ShowGridLines(false)); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//// define the border style
	//border := []excelize.Border{
	//	{Type: "top", Style: 1, Color: "cccccc"},
	//	{Type: "left", Style: 1, Color: "cccccc"},
	//	{Type: "right", Style: 1, Color: "cccccc"},
	//	{Type: "bottom", Style: 1, Color: "cccccc"},
	//}
	//// define the style of cells
	//if cellsStyle, err = f.NewStyle(&excelize.Style{
	//	Font:   &excelize.Font{Color: "333333"},
	//	Border: border}); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//// define the style of the header row
	//if headerStyle, err = f.NewStyle(&excelize.Style{
	//	Font: &excelize.Font{Bold: true},
	//	Fill: excelize.Fill{
	//		Type: "pattern", Color: []string{"dae9f3"}, Pattern: 1},
	//	Border: border},
	//); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//// set cell style
	//if err = f.SetCellStyle("Sheet1", "A2", "B6", cellsStyle); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//if err = f.SetCellStyle("Sheet1", "D3", "E3", cellsStyle); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//// set cell style for the header row
	//if err = f.SetCellStyle("Sheet1", "A1", "B1", headerStyle); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//if err = f.SetCellStyle("Sheet1", "D2", "E2", headerStyle); err != nil {
	//	fmt.Println(err)
	//	return
	//}
	// save spreadsheet file
	if err := f.SaveAs("Book1.xlsx"); err != nil {
		fmt.Println(err)
	}
}

func TestDataValidation(t *testing.T) {
	resultFile := filepath.Join("", "TestDataValidation.xlsx")

	f := excelize.NewFile()

	dvRange := excelize.NewDataValidation(true)
	dvRange.Sqref = "A1:B2"
	assert.NoError(t, dvRange.SetRange(10, 20, excelize.DataValidationTypeWhole, excelize.DataValidationOperatorBetween))
	dvRange.SetError(excelize.DataValidationErrorStyleStop, "error title", "error body")
	dvRange.SetError(excelize.DataValidationErrorStyleWarning, "error title", "error body")
	dvRange.SetError(excelize.DataValidationErrorStyleInformation, "error title", "error body")
	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))
	assert.NoError(t, f.SaveAs(resultFile))

	dvRange = excelize.NewDataValidation(true)
	dvRange.Sqref = "A3:B4"
	assert.NoError(t, dvRange.SetRange(10, 20, excelize.DataValidationTypeWhole, excelize.DataValidationOperatorGreaterThan))
	dvRange.SetInput("input title", "input body")
	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))
	assert.NoError(t, f.SaveAs(resultFile))

	dvRange = excelize.NewDataValidation(true)
	dvRange.Sqref = "A5:B6"
	assert.NoError(t, dvRange.SetDropList([]string{"1", "2", "3"}))
	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))
	assert.NoError(t, f.SaveAs(resultFile))
}

func TestDataValidationError(t *testing.T) {
	resultFile := filepath.Join("test", "TestDataValidationError.xlsx")

	f := excelize.NewFile()
	assert.NoError(t, f.SetCellStr("Sheet1", "E1", "E1"))
	assert.NoError(t, f.SetCellStr("Sheet1", "E2", "E2"))
	assert.NoError(t, f.SetCellStr("Sheet1", "E3", "E3"))

	dvRange := excelize.NewDataValidation(true)
	dvRange.SetSqref("A7:B8")
	dvRange.SetSqref("A7:B8")
	assert.NoError(t, dvRange.SetSqrefDropList("$E$1:$E$3", true))

	err := dvRange.SetSqrefDropList("$E$1:$E$3", false)
	assert.EqualError(t, err, "cross-sheet sqref cell are not supported")

	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))
	assert.NoError(t, f.SaveAs(resultFile))

	dvRange = excelize.NewDataValidation(true)
	err = dvRange.SetDropList(make([]string, 258))
	if dvRange.Formula1 != "" {
		t.Errorf("data validation error. Formula1 must be empty!")
		return
	}
	assert.EqualError(t, err, "data validation must be 0-255 characters")
	assert.NoError(t, dvRange.SetRange(10, 20, excelize.DataValidationTypeWhole, excelize.DataValidationOperatorGreaterThan))
	dvRange.SetSqref("A9:B10")

	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))
	assert.NoError(t, f.SaveAs(resultFile))

	// Test width invalid data validation formula.
	dvRange.Formula1 = strings.Repeat("s", 257+22)
	assert.EqualError(t, dvRange.SetRange(10, 20, excelize.DataValidationTypeWhole, excelize.DataValidationOperatorGreaterThan), "data validation must be 0-255 characters")

	// Test add data validation on no exists worksheet.
	f = excelize.NewFile()
	assert.EqualError(t, f.AddDataValidation("SheetN", nil), "sheet SheetN is not exist")
}

func TestDeleteDataValidation(t *testing.T) {
	f := excelize.NewFile()
	assert.NoError(t, f.DeleteDataValidation("Sheet1", "A1:B2"))

	dvRange := excelize.NewDataValidation(true)
	dvRange.Sqref = "A1:B2"
	assert.NoError(t, dvRange.SetRange(10, 20, excelize.DataValidationTypeWhole, excelize.DataValidationOperatorBetween))
	dvRange.SetInput("input title", "input body")
	assert.NoError(t, f.AddDataValidation("Sheet1", dvRange))

	assert.NoError(t, f.DeleteDataValidation("Sheet1", "A1:B2"))
	assert.NoError(t, f.SaveAs(filepath.Join("test", "TestDeleteDataValidation.xlsx")))

	// Test delete data validation on no exists worksheet.
	assert.EqualError(t, f.DeleteDataValidation("SheetN", "A1:B2"), "sheet SheetN is not exist")
}

func TestMakeExcel1(t *testing.T) {
	list1 := make([]string, 5)
	list1[4] = "1"
	fmt.Println(list1)
}
