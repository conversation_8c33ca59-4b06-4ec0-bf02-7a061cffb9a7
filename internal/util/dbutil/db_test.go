package dbutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"testing"
)

type VolumeZoneLocationTab struct {
	Id         uint64 `gorm:"column:id" json:"id"`
	GroupId    string `gorm:"column:group_id" json:"group_id"`
	ZoneName   string `gorm:"column:zone_name" json:"zone_name"`
	Region     string `gorm:"column:region" json:"region"`
	LocationId int64  `gorm:"column:location_id" json:"location_id"`
	State      string `gorm:"column:state" json:"state"`
	City       string `gorm:"column:city" json:"city"`
	District   string `gorm:"column:district" json:"district"`
	Street     string `gorm:"column:street" json:"street"`
	Operator   string `gorm:"column:operator" json:"operator"`
	Ctime      int64  `gorm:"column:ctime" json:"ctime"`
	Mtime      int64  `gorm:"column:mtime" json:"mtime"`
}

func (p VolumeZoneLocationTab) TableName() string {
	return "volume_zone_location_tab"
}

func (p VolumeZoneLocationTab) DBForRead() DBTag {
	return SmartRoutingRead
}

func (p VolumeZoneLocationTab) DBForWrite() DBTag {
	return SmartRoutingWrite
}

func (p VolumeZoneLocationTab) ModelInfo() ModelInfo {
	return ModelInfo{}
}

func Test_db(t *testing.T) {
	if err := chassis.Init(chassis.WithChassisConfigPrefix("grpc_server")); err != nil {
		panic(err)
	}
	if err := configutil.Init(); err != nil {
		panic(err)
	}
	err := Init()
	if err != nil {
		panic(err)
	}
	ctx := context.TODO()
	r := &VolumeZoneLocationTab{}
	db, derr := MasterDB(ctx, r)
	if derr != nil {
		panic(derr)
	}
	tab := &VolumeZoneLocationTab{GroupId: "123"}
	if err := db.Table("volume_zone_location_tab").Create(tab).GetError(); err != nil {
		panic(err)
	}
	println()
}

func GetGroupIds(db scormv2.SQLCommon, ctx context.Context, res interface{}) {
	if err := db.Table("volume_routing_rule_detail_tab").Where("rule_id = ?", 7).Select("distinct(group_id)").Find(res).GetError(); err != nil {
		panic(err)
	}
}
