package dbutil

import (
	"errors"
	"fmt"
)

type ErrDataNotFound struct {
	Column string
	Value  interface{}
}

func (err *ErrDataNotFound) Error() string {
	return fmt.Sprintf("data not found, %v = %v", err.Column, err.Value)
}

func NewErrDataNotFound(col string, value interface{}) *ErrDataNotFound {
	return &ErrDataNotFound{
		Column: col,
		Value:  value,
	}
}

func IsDataNotFound(err error) bool {
	x := &ErrDataNotFound{}
	return errors.As(err, &x)
}

type ErrDatabase struct {
	source error
	info   string
}

func NewErrDatabase(err error, info string) *ErrDatabase {
	return &ErrDatabase{source: err, info: info}
}

func (err *ErrDatabase) Error() string {
	return fmt.Sprintf("%s database error: %v", err.info, err.source)
}

type ErrDataExisted struct {
	Column string
	Value  interface{}
}

func (err *ErrDataExisted) Error() string {
	return fmt.Sprintf("data existed, %v = %v", err.Column, err.Value)
}

func NewErrDataExisted(col string, value interface{}) *ErrDataExisted {
	return &ErrDataExisted{
		Column: col,
		Value:  value,
	}
}
