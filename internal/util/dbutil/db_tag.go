package dbutil

type DBTag string

const (
	SmartRoutingRead               DBTag = "smart_routing_read"
	SmartRoutingWrite              DBTag = "smart_routing_write"
	SmartRoutingLogCidWrite        DBTag = "smart_routing_log_cid_write" //已下线
	SmartRoutingLogCidRead         DBTag = "smart_routing_log_cid_read"  //已下线
	SscLhsCidRead                  DBTag = "ssc_lhs_cid_read"
	SmartRoutingBatchAllocateRead  DBTag = "smart_routing_batch_allocate_read"
	SmartRoutingBatchAllocateWrite DBTag = "smart_routing_batch_allocate_write"
)

type DBModel interface {
	DBForRead() DBTag
	DBForWrite() DBTag
	TableName() string
	ModelInfo() ModelInfo
}

func (tag DBTag) String() string {
	return string(tag)
}
