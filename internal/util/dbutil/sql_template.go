package dbutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"sort"
	"strings"
)

const DefaultDbDumpLimit = 1000
const (
	DefaultDeleteLimit  = 1000
	DefaultMaxDeleteNum = 10000
)

type queryOptions struct {
	Offset  int64
	Limit   int64
	OrderBy string
}

type QueryOption func(options *queryOptions)

func WithPage(offset, size int64) QueryOption {
	return func(options *queryOptions) {
		options.Offset = offset
		options.Limit = size
	}
}

func WithPageByPageNo(pageNo, pageSize int64) QueryOption {
	var offset int64
	// pageNo一般是从1开始，避免特殊情况传小于1的值。如果pageNo从0开始该方法不一定会符合预期
	if pageNo > 0 {
		offset = (pageNo - 1) * pageSize
	}
	return func(options *queryOptions) {
		options.Offset = offset
		options.Limit = pageSize
	}
}

func WithLimit(limit int64) QueryOption {
	return func(options *queryOptions) {
		options.Limit = limit
	}
}

func WithOrder(orderBy string) QueryOption {
	return func(options *queryOptions) {
		options.OrderBy = orderBy
	}
}

func Insert(ctx context.Context, model DBModel, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, model)
	if err != nil {
		return err
	}
	if err := db.Table(model.TableName()).Set(AuditLogModelKey, modelInfo).Create(model).GetError(); err != nil {
		return err
	}
	return nil
}

// 如果create 失败会报错还是？create成功后是否会刷新id字段？如果data是空数组会怎样？data长度超过batch size会怎样？
func InsertBatch(ctx context.Context, hook DBModel, list interface{}) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	if err := db.Table(hook.TableName()).CreateInBatches(list, 1000).GetError(); err != nil {
		return err
	}
	return nil
}

func Delete(ctx context.Context, hook DBModel, condition map[string]interface{}, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Delete(hook).GetError(); err != nil {
		return err
	}
	return nil
}

func DeleteBatch(ctx context.Context, hook DBModel, condition map[string]interface{}, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	var rowsAffected int64 = 1
	deleteNum := 0
	for rowsAffected != 0 {
		d := db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Limit(DefaultDeleteLimit).Delete(hook)
		if d.GetError() != nil {
			return d.GetError()
		}
		rowsAffected = d.RowsAffected()
		deleteNum += 1
		// 兜底保护
		if deleteNum > DefaultMaxDeleteNum {
			logger.CtxLogErrorf(ctx, "delete num exceed max limit, max limit: %v", deleteNum)
			break
		}
	}
	return nil
}

func DeleteBatchWithTableName(ctx context.Context, hook DBModel, condition map[string]interface{}, modelInfo ModelInfo, tableName string) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	var rowsAffected int64 = 1
	deleteNum := 0
	for rowsAffected != 0 {
		d := db.Table(tableName).Set(AuditLogModelKey, modelInfo).Where(query, args...).Limit(DefaultDeleteLimit).Delete(hook)
		if d.GetError() != nil {
			return d.GetError()
		}
		rowsAffected = d.RowsAffected()
		deleteNum += 1
		// 兜底保护
		if deleteNum > DefaultMaxDeleteNum {
			logger.CtxLogErrorf(ctx, "delete num exceed max limit, max limit: %v", deleteNum)
			break
		}
	}
	return nil
}

// Update 更新失败不会报错，要通过affected rows == 0来判断
func Update(ctx context.Context, hook DBModel, condition, data map[string]interface{}, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Updates(data).GetError(); err != nil {
		return err
	}
	return nil
}

// Update 更新失败不会报错，要通过affected rows == 0来判断
func UpdateByObj(ctx context.Context, hook DBModel, condition map[string]interface{}, data interface{}, modelInfo ModelInfo) (error, int64) {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err, 0
	}
	d := db.Table(hook.TableName())
	query, args := ParseCondition(condition)
	if err := d.Set(AuditLogModelKey, modelInfo).Where(query, args...).Updates(data).GetError(); err != nil {
		return err, 0
	}
	return nil, d.RowsAffected()
}

// UpdateWithAffectedRows 更新失败不会报错，要通过affected rows == 0来判断
func UpdateWithAffectedRows(ctx context.Context, hook DBModel, condition, data map[string]interface{}, modelInfo ModelInfo) (error, int64) {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err, 0
	}
	query, args := ParseCondition(condition)
	// 这里MasterDB得到的db是具备复制能力的，用原来的方式
	// db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Updates(data).GetError()可以得到err，
	// 但如果后面使用db.RowsAffected()是得不到RowsAffected的，因为Set后面的方法都是基于db.Table(hook.TableName())复制出来的db操作的，
	// 所以err和rowAffected都是存在于db.Table(hook.TableName())中，不存在于MasterDB中得到的db
	d := db.Table(hook.TableName())
	if err := d.Set(AuditLogModelKey, modelInfo).Where(query, args...).Updates(data).GetError(); err != nil {
		return err, 0
	}
	return nil, d.RowsAffected()
}

// SaveByObj 如果主键不存在，就会insert一条新的记录；反之将更新数据
// 通过结构体update将不能更新零值，但是使用save可以
func SaveByObj(ctx context.Context, hook DBModel, condition map[string]interface{}, data interface{}, omitSlice []string, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	var sErr error
	if len(omitSlice) == 0 {
		sErr = db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Save(data).GetError()
	} else {
		sErr = db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Omit(omitSlice...).Save(data).GetError()
	}
	if sErr != nil {
		return sErr
	}
	return nil
}

// Save 如果主键不存在，就会insert一条新的记录；反之将更新数据
// 通过map更新可以更新零值
func Save(ctx context.Context, hook DBModel, condition, data map[string]interface{}, modelInfo ModelInfo) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Set(AuditLogModelKey, modelInfo).Where(query, args...).Save(data).GetError(); err != nil {
		return err
	}
	return nil
}

// Select 如果检索不到，不会报错，需要手动判断len()是否为0
func Select(ctx context.Context, hook DBModel, condition map[string]interface{}, results interface{}, opts ...QueryOption) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	db = db.Table(hook.TableName())
	if len(condition) > 0 {
		query, args := ParseCondition(condition)
		db = db.Where(query, args...)
	}
	if len(opts) > 0 {
		o := new(queryOptions)
		for _, opt := range opts {
			opt(o)
		}
		if o.Offset > 0 {
			db = db.Offset(int(o.Offset))
		}
		if o.Limit > 0 {
			db = db.Limit(int(o.Limit))
		}
		if o.OrderBy != "" {
			db = db.Order(o.OrderBy)
		}
	}
	if err := db.Find(results).GetError(); err != nil {
		return err
	}
	return nil
}

func SelectWithTableName(ctx context.Context, hook DBModel, condition map[string]interface{}, results interface{}, tableName string, opts ...QueryOption) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	db = db.Table(tableName)
	if len(condition) > 0 {
		query, args := ParseCondition(condition)
		db = db.Where(query, args...)
	}
	if len(opts) > 0 {
		o := new(queryOptions)
		for _, opt := range opts {
			opt(o)
		}
		if o.Offset > 0 {
			db = db.Offset(int(o.Offset))
		}
		if o.Limit > 0 {
			db = db.Limit(int(o.Limit))
		}
		if o.OrderBy != "" {
			db = db.Order(o.OrderBy)
		}
	}
	if err := db.Find(results).GetError(); err != nil {
		return err
	}
	return nil
}

// Take 取不到数据会报错 ErrRecordNotFound
func Take(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Where(query, args...).Take(result).GetError(); err != nil {
		return err
	}
	return nil
}

func First(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Where(query, args...).First(result).GetError(); err != nil {
		return err
	}
	return nil
}

func MaxId(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Select("MAX(id) AS id").Where(query, args...).First(result).GetError(); err != nil {
		return err
	}
	return nil
}

func MaxIdWithTableName(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}, tableName string) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(tableName).Select("MAX(id) AS id").Where(query, args...).First(result).GetError(); err != nil {
		return err
	}
	return nil
}

func MinIdWithTableName(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}, tableName string) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(tableName).Select("MIN(id) AS id").Where(query, args...).First(result).GetError(); err != nil {
		return err
	}
	return nil
}

func Last(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Where(query, args...).Last(result).GetError(); err != nil {
		return err
	}
	return nil
}

func LastFromMaster(ctx context.Context, hook DBModel, condition map[string]interface{}, result interface{}) error {
	db, err := MasterDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Where(query, args...).Last(result).GetError(); err != nil {
		return err
	}
	return nil
}

func Count(ctx context.Context, hook DBModel, condition map[string]interface{}, total *int64) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	query, args := ParseCondition(condition)
	if err := db.Table(hook.TableName()).Where(query, args...).Count(total).GetError(); err != nil {
		return err
	}
	return nil
}

func ParseCondition(condition map[string]interface{}) (string, []interface{}) {
	if len(condition) == 0 {
		return "1 = ?", []interface{}{1}
	}
	var queries []string
	var args []interface{}
	for q := range condition {
		queries = append(queries, q)
	}
	//流量录制与回放需要保证sql参数顺序一致才能mock成功,排序为了保证入参顺序一致
	sort.Strings(queries)
	for _, param := range queries {
		if value, ok := condition[param]; ok {
			args = append(args, value)
		}
	}

	return strings.Join(queries, " AND "), args
}

func Distinct(ctx context.Context, hook DBModel, condition map[string]interface{}, field string, result interface{}) error {
	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}
	if len(condition) > 0 {
		query, args := ParseCondition(condition)
		db = db.Where(query, args...)
	}
	return db.Distinct(field).Find(result).GetError()
}

// SearchAllDataWithBatchesWithComplex 自带默认分批，防止数据量太大
func SearchAllDataWithBatchesWithComplex(ctx context.Context, hook DBModel, rsList interface{}, searchData map[string]interface{}) error {
	rsV, err := checkAndGetSliceValue(rsList)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, fmt.Sprintf("rsList not a list,err:%s", err), nil)
	}
	bufVPtr := reflect.New(rsV.Type())
	bufV := bufVPtr.Elem()

	db, err := SlaveDB(ctx, hook)
	if err != nil {
		return err
	}

	var (
		query, args = ParseCondition(searchData)
		start       int
		limit       = DefaultDbDumpLimit
	)
	// 第一次先直接查 limit 条
	err = db.Table(hook.TableName()).Where(query, args...).Order("id DESC").Limit(limit).Find(bufVPtr.Interface()).GetError()
	if err != nil {
		return err
	}
	rsV.Set(reflect.AppendSlice(rsV, bufV))
	if bufV.Len() < limit {
		// 如果长度小于 limit 证明已经找完
		return nil
	}
	// 判断是否有实现 IDInterface, 有的话使用 id>=? 来做分页。否则，用 limit ?,?
	for bufV.Len() > 0 && bufV.Len() == limit {
		dest := bufVPtr.Interface()
		dbWithWhere := db.Where(query, args...).Order("id DESC")
		start += limit
		err = dbWithWhere.Offset(start).Limit(limit).Find(dest).GetError()
		if err != nil {
			return err
		}
		rsV.Set(reflect.AppendSlice(rsV, bufV))
	}
	return nil
}

func checkAndGetSliceValue(obj interface{}) (reflect.Value, error) {
	v := reflect.ValueOf(obj)
	objType := v.Type()
	if objType.Kind() != reflect.Ptr {
		return v, fmt.Errorf("not a ptr, kind:%s", objType.Kind())
	}
	objValue := objType.Elem()
	if objValue.Kind() != reflect.Slice {
		return v, fmt.Errorf("not a Slice, kind:%s", objValue.Kind())
	}
	v = v.Elem()
	return v, nil
}
