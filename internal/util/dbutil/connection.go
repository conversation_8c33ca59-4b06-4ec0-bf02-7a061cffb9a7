package dbutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/meta"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"github.com/pkg/errors"
	"gorm.io/driver/mysql"
	"time"
)

var dbs map[DBTag]scormv2.SQLCommon

func Init() error {
	dbs = make(map[DBTag]scormv2.SQLCommon)
	dbConf := configutil.GetDSNConf()
	if err := InitSmartRoutingDB(dbConf); err != nil {
		return err
	}
	if err := InitSmartRoutingBatchAllocateDB(dbConf); err != nil {
		return err
	}

	return nil
}

func InitTest() error {
	dbs = make(map[DBTag]scormv2.SQLCommon)
	db, _ := initDB("sz_sc_test2:6vJaoy5HHiPP0ms_sUnO@(master.shopee_sls_smartrouting.mysql.cloud.test.shopee.io:6606)/shopee_ssc_smartrouting_id_db?timeout=1000ms&readTimeout=10000ms&charset=utf8mb4", 5, 15, 3600, true, 1)
	dbs[SmartRoutingWrite] = db
	return nil
}

func InitSmartRoutingDB(dbConf configutil.DSNConf) error {
	masterHost, slaveHost := dbConf.SmartRoutingMaster, dbConf.SmartRoutingSlave
	// 对于US3机房需要连接SG11的DB，US2是Live测试用的机房，单独连接一个DB
	if envvar.IsIDCUs3() {
		logger.LogInfof("IDC: %s, use SG11 DB", envvar.GetIDC())
		masterHost, slaveHost = dbConf.SmartRoutingSg11Master, dbConf.SmartRoutingSg11Slave
	} else if envvar.IsIDCUs2() {
		logger.LogInfof("IDC US2, use US2 DB")
		masterHost, slaveHost = dbConf.SmartRoutingUs2Master, dbConf.SmartRoutingUs2Slave
	}

	if db, err := initDB(masterHost, dbConf.MaxIdleConn, dbConf.MaxOpenConn, dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime); err == nil {
		dbs[SmartRoutingWrite] = db
	} else {
		return err
	}
	if db, err := initDB(slaveHost, dbConf.MaxIdleConn, dbConf.MaxOpenConn, dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime); err == nil {
		dbs[SmartRoutingRead] = db
	} else {
		return err
	}

	return nil
}

func InitLhsDB(dbConf configutil.DSNConf) error {
	if db, err := initDB(dbConf.LhsCidRead, dbConf.MaxIdleConn, dbConf.MaxOpenConn, dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime); err != nil {
		return err
	} else {
		dbs[SscLhsCidRead] = db
	}

	return nil
}

func InitSmartRoutingBatchAllocateDB(dbConf configutil.DSNConf) error {
	if !meta.IsEnableBatchAllocationRegion(envvar.GetCID()) {
		return nil
	}

	logger.LogInfof("init smartrouting batch allocate database")
	if db, err := initDB(dbConf.SmartRoutingBatchAllocateSlave, dbConf.MaxIdleConn, dbConf.MaxOpenConn, dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime); err != nil {
		return err
	} else {
		dbs[SmartRoutingBatchAllocateRead] = db
	}

	if db, err := initDB(dbConf.SmartRoutingBatchAllocateMaster, dbConf.MaxIdleConn, dbConf.MaxOpenConn, dbConf.ConnMaxAge, dbConf.DisableShadowDBForReadOnly, dbConf.SlowQueryTime); err != nil {
		return err
	} else {
		dbs[SmartRoutingBatchAllocateWrite] = db
	}

	return nil
}

func MasterDB(ctx context.Context, model DBModel) (scormv2.SQLCommon, error) {
	return GetDB(ctx, model.DBForWrite())
}

func SlaveDB(ctx context.Context, model DBModel) (scormv2.SQLCommon, error) {
	return GetDB(ctx, model.DBForRead())
}

func GetDB(ctx context.Context, dbTag DBTag) (scormv2.SQLCommon, error) {
	dbConn, ok := dbs[dbTag]
	if !ok {
		return nil, fmt.Errorf("db not found for tag:%s", dbTag)
	}
	sqlCommon := dbConn.WithContext(ctx)

	return sqlCommon, nil
}

func initDB(dsn string, maxIdleConn, maxOpenConn, connMaxAge int, disableShadowDBForReadOnly bool, slowQueryTime int) (*scormv2.OrmDB, error) {
	db, cErr := scormv2.Open(scormv2.NewMultiDialector().AddDefaultDialector(mysql.Open(dsn)),
		&scormv2.Config{AutoReport: true, DisableShadowDBForReadOnly: disableShadowDBForReadOnly, SlowQuery: time.Duration(slowQueryTime) * time.Millisecond})
	if cErr != nil {
		logger.CtxLogErrorf(context.TODO(), "initialize database connection fail, dsn:%s, error:%+v", dsn, cErr)
		return nil, cErr
	}

	//配置变更上报hook
	change_report.InitDBCallbackChangeReport(db.GetGormDB())
	//SSCSMR_1386:audit log hook
	InitDBCallbackAuditLog(db)

	sqlDB := db.DB()
	db.SetMaxIdleConns(maxIdleConn)
	db.SetMaxOpenConns(maxOpenConn)
	db.SetConnMaxLifetime(time.Duration(connMaxAge) * time.Second)
	if err := sqlDB.Ping(); err != nil {
		logger.CtxLogErrorf(context.TODO(), "ping database fail, error:%+v", err)
		return nil, err
	}

	return db, nil
}

// SSCSMR-1386:增加audit log对应的hook
func InitDBCallbackAuditLog(ormDB *scormv2.OrmDB) {
	_ = ormDB.Callback().Create().After("gorm:create").Register("audit_log:after_create", AuditLogAfter)
	_ = ormDB.Callback().Create().After("gorm:after_create").Register("audit_log:before_create_rollback", AuditLogBeforeRest)
	_ = ormDB.Callback().Update().After("gorm:update").Register("audit_log:after_update", AuditLogAfter)
	_ = ormDB.Callback().Update().After("gorm:after_update").Register("audit_log:before_update_rollback", AuditLogBeforeRest)
	_ = ormDB.Callback().Delete().After("gorm:delete").Register("audit_log:after_delete", AuditLogAfter)
	_ = ormDB.Callback().Delete().After("gorm:after_delete").Register("audit_log:before_delete_rollback", AuditLogBeforeRest)
}

func AuditLogAfter(ormDB scormv2.SQLCommon) {
	defer func() {
		if err := recover(); err != nil {
			if err, ok := err.(error); ok {
				_ = monitor.AwesomeReportEventWithDomain(context.Background(), monitoring.CatModuleAuditLog, "InitDBCallbackAuditLog", "AuditLogAfter", "panic", fmt.Sprintf("gormInterceptorAfter panic: %+v", errors.WithStack(err)))
				logger.LogErrorf("[Recovery]  gormInterceptorAfter panic recovered:%+v", errors.WithStack(err))
			}
		}
	}()
	db := ormDB.GetGormDB()
	// 上游无context传入
	ctx := context.Background()
	auditLogConf := configutil.GetAuditLogConfig(ctx)
	if auditLogConf.NeedRecord && db.Error == nil {

		tableName := db.Statement.Table
		//SSCSMR-1386:需要忽略audit_log_tab本身的写入
		if tableName == "audit_log_tab" {
			return
		}
		//SSCSMR-1386:获取黑名单，在黑名单中的table不执行audit log after方法，避免mask_product_zone_volume_tab
		// 等大批量更新导致大量audit log写入。此类url排查问题时由request body去排查
		for _, blackTable := range auditLogConf.BlackTables {
			if blackTable == tableName {
				return
			}
		}

		ctxParam := db.Statement.Context.Value(AuditLogAfterKey)

		//这里要求在提前注册该key进ctx中，考虑放在handler中进行注册，如果没有注册就不使用本处的方法
		if ctxParam == nil {
			return
		}

		modelInfo := ModelInfo{}
		manualModelInfo := ModelInfo{}
		model, ok := db.Statement.Model.(DBModel)
		if ok {
			modelInfo = model.ModelInfo()
		} else if dest, ok := db.Statement.Dest.(DBModel); modelInfo.Id == 0 && ok {
			//对于create方法来说，有时候model获取不到，则尝试从dest获取. （modelInfo.Id == 0说明model获取不到）
			modelInfo = dest.ModelInfo()
		}
		//当gorm自带的变量都无法获取时，采用指定填充的内容
		modelInterface, cOk := db.Get(AuditLogModelKey)
		if cOk {
			manualModelInfo, _ = modelInterface.(ModelInfo)
		}
		fillModelInfo(manualModelInfo, &modelInfo)

		if auditLogCtx, ok := ctxParam.(*AuditLogCtxParam); ok {
			tableCount := len(auditLogCtx.ModelNameInfoMap)
			// 只取前10个表信息，防止信息过多
			if tableCount >= 10 {
				return
			}
			//初始化空map
			if auditLogCtx.ModelNameInfoMap == nil {
				auditLogCtx.ModelNameInfoMap = make(map[string]ModelInfo, 0)
			}
			auditLogCtx.ModelNameInfoMap[tableName] = modelInfo
		}
	}
}

// SSCSMR-1386:gorm:create/update/delete勾子的触发在回滚之前，因此如果事务回滚了，需要重置ctx信息
func AuditLogBeforeRest(ormDB scormv2.SQLCommon) {
	//这里如果捕获panic的话，需要重新抛出一个panic。因为捕获panic会把二进制流读取出来，影响原来的panic，从而使得事务取消roll back
	db := ormDB.GetGormDB()
	// 上游无context传入
	ctx := context.Background()
	auditLogConf := configutil.GetAuditLogConfig(ctx)
	if auditLogConf.NeedRecord && db.Error != nil {
		ctxParam := db.Statement.Context.Value(AuditLogAfterKey)
		if auditLogCtx, ok := ctxParam.(*AuditLogCtxParam); ok {
			auditLogCtx.ModelNameInfoMap = make(map[string]ModelInfo, 0)
			auditLogCtx.AuditLogExtendInfo.Message = "transaction roll back"
		}
	}
}

func fillModelInfo(src ModelInfo, dest *ModelInfo) {
	if dest.Id == 0 {
		dest.Id = src.Id
	}
	if dest.ModelName == "" {
		dest.ModelName = src.ModelName
	}
	if dest.RuleId == 0 {
		dest.RuleId = src.RuleId
	}
	if dest.RuleVolumeId == 0 {
		dest.RuleVolumeId = src.RuleVolumeId
	}
	if dest.MaskProductId == 0 {
		dest.MaskProductId = src.MaskProductId
	}
	if dest.FulfillmentProductId == 0 {
		dest.FulfillmentProductId = src.FulfillmentProductId
	}
	if dest.TaskId == 0 {
		dest.TaskId = src.TaskId
	}
}
