package dbutil

type StringKey string

const AuditLogAfterKey StringKey = "audit_log_after"
const AuditLogModelKey string = "audit_log_model_key"

type AuditLogCtxParam struct {
	ModelNameInfoMap   map[string]ModelInfo `json:"model_name_info_map"`
	AuditLogExtendInfo AuditLogExtendInfo   `json:"audit_log_extend_info"` //用来保存与audit log相关的额外信息
}

// ModelInfo 用来保存model的信息，作为接口的返回，并由不同的table model自定义具体内容
type ModelInfo struct {
	Id                   uint64     `json:"id"`
	ModelName            string     `json:"model_name"`
	MaskProductId        uint64     `json:"mask_product_id"`
	FulfillmentProductId uint64     `json:"fulfillment_product_id"`
	RuleId               uint64     `json:"rule_id"` //包括allocation、smr rule，具体根据下面的module_type区分
	RuleVolumeId         uint64     `json:"rule_volume_id"`
	TaskId               uint64     `json:"task_id"`
	ExtendInfo           ExtendInfo `json:"extend_info"` //暂定把额外信息用string存储
}

// 如果需要对每个model进行拓展信息的写入，可以在这个模型里加入对应字段
type ExtendInfo struct {
}

// 如果需要对整个audit log para进行拓展信息的写入，可以在这个模型里加入对应字段
type AuditLogExtendInfo struct {
	Message string `json:"message"`
}
