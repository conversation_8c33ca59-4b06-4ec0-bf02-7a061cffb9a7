package dbutil

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

func InitGrpcDb() error {
	return Init()
}

func InitAdminDb() error {
	if err := Init(); err != nil {
		return err
	}
	dbConf := configutil.GetDSNConf()
	// debug接口预测功能需要用到
	if err := InitLhsDB(dbConf); err != nil {
		return err
	}

	return nil
}

func InitTaskDb() error {
	if err := Init(); err != nil {
		return err
	}
	dbConf := configutil.GetDSNConf()
	if err := InitLhsDB(dbConf); err != nil {
		return err
	}
	return nil
}

func InitBATaskDb() error {
	if err := Init(); err != nil {
		return err
	}

	return nil
}
