package dbutil

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"reflect"
	"strings"
)

func indirectType(typ reflect.Type) reflect.Type {
	for typ.Kind() == reflect.Ptr || typ.Kind() == reflect.Slice {
		typ = typ.Elem()
	}
	return typ
}
func indirectValue(val reflect.Value) reflect.Value {
	for val.Kind() == reflect.Ptr || val.Kind() == reflect.Slice {
		val = val.Elem()
	}
	return val
}

func parseGormTag(tag string) string {
	if tag == "" {
		return ""
	}
	if tagArr := strings.Split(tag, ":"); len(tagArr) > 1 {
		if tagArr[0] == "column" {
			return tagArr[1]
		}
	}
	return ""
}

// UpdateIncludeMapper 替代 UpdateMapper 方法， includes 表示更新时需要包含哪一些字段， 在更新时只会更新includes配置的字段
func UpdateIncludeMapper(obj interface{}, includes ...string) map[string]interface{} {
	m := make(map[string]interface{})
	t := indirectType(reflect.TypeOf(obj))
	v := indirectValue(reflect.ValueOf(obj))

	if t.Kind() == reflect.Map {
		for _, k := range v.MapKeys() {
			if s, ok := k.Interface().(string); ok && objutil.ContainStr(includes, s) {
				m[s] = v.MapIndex(k).Interface()
			}
		}
		return m
	}
	if t.Kind() == reflect.Struct {
		for i := 0; i < v.NumField(); i++ {
			fieldName := parseGormTag(t.Field(i).Tag.Get("gorm"))
			if fieldName == "" {
				continue
			}
			if !objutil.ContainStr(includes, fieldName) {
				continue
			}
			fv := v.Field(i)
			switch fv.Kind() {
			case reflect.Ptr:
				if fv.IsNil() {
					m[fieldName] = nil
				} else {
					m[fieldName] = fv.Elem().Interface()
				}
			default:
				m[fieldName] = v.Field(i).Interface()
			}
		}
		return m
	}
	return m
}
