package masking_forecast_hbase

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"golang.org/x/time/rate"
	"io"
	"modernc.org/mathutil"
)

const (
	maxLoopSizeConstant = ********
)

// 指定循环次数，检索完数据后一次返回。
func doHBaseScanRequestWithMonitor(ctx context.Context, p *gohbase.Scan, maxLoopSize int) ([]*gohbase.Result, error) {
	//SPLPS_9385:将default Max loop配置在Apollo上
	maxLoopSize = mathutil.Min(maxLoopSize, maxLoopSizeConstant)

	var err error
	var res *gohbase.Result

	rsp := make([]*gohbase.Result, 0, 10)

	successFlag := false

	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	tableName := str.GenKey(":", string(p.Table()), "scan")

	scanner := client.Scan(p)
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		res, err = scanner.Next()
		if err == io.EOF || res == nil {
			successFlag = true
			break
		}
		if err != nil {
			break
		}
		rsp = append(rsp, res)

		if loopIndex >= maxLoopSize {
			logger.CtxLogInfof(ctx, "loop over max size, max_size=%d, will return", maxLoopSize)
			successFlag = true
			break
		}
	}

	if successFlag {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
		err = nil
	} else if err != nil {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
	}
	return rsp, err
}

func doHBaseScanRequestWithMonitorAndChannel(ctx context.Context, p *gohbase.Scan, maxLoopSize int, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) error {
	defer func() {
		if err := recover(); err != nil {
			logger.CtxLogErrorf(ctx, "doHBaseScanRequestWithMonitorAndChannel err %v", err)
		}
	}()

	//SSCSMR-1480:配置限速器
	var limiter *rate.Limiter
	if rateLimiter != nil {
		limiter = rate.NewLimiter(rate.Limit(rateLimiter.Limit), rateLimiter.Burst)
	}

	//SPLPS_9385:将default Max loop配置在Apollo上
	maxLoopSize = mathutil.Min(maxLoopSize, maxLoopSizeConstant)

	var err error
	var res *gohbase.Result

	successFlag := false

	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	tableName := str.GenKey(":", string(p.Table()), "scan")
	scanner := client.Scan(p)
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		res, err = scanner.Next()
		if err == io.EOF || res == nil {
			successFlag = true
			break
		}
		if err != nil {
			break
		}
		//SSCSMR-1480:控制生产者速度
		if limiter != nil {
			_ = limiter.Wait(ctx)
		}
		ch <- res
		if loopIndex >= maxLoopSize {
			logger.CtxLogInfof(ctx, "loop over max size, max_size=%d, will return", maxLoopSize)
			successFlag = true
			break
		}
	}

	scanner.Close()

	if successFlag {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
		err = nil
	} else if err != nil {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
	}
	return err
}

func doHBasePutRequestWithMonitor(ctx context.Context, p *gohbase.Mutate) (*gohbase.Result, error) {
	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	tableName := str.GenKey(":", string(p.Table()), "create")

	result, err := client.Put(p)
	if err != nil {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
		return nil, err
	}
	endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
	return result, err
}
