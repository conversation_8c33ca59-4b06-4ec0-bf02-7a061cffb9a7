package masking_forecast_hbase

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
)

const (
	defaultScanLimit = 1000
)

var mainClient gohbase.Client //用来连接main集群

func InitMainHBase() {
	//初始化main集群连接
	// 上游无context传入
	ctx := context.Background()
	mainConf := configutil.GetMainHbaseConfig(ctx)
	if mainConf.LocalHost {
		mainClient = gohbase.NewClient("localhost")
	} else {
		logger.LogInfof("[main hbase connect, the info is %+v", mainConf)
		mainClient = gohbase.NewClient(mainConf.HostUrl, gohbase.EffectiveUser(mainConf.UserName), gohbase.ZookeeperRoot(mainConf.ZkRoot), gohbase.Auth(mainConf.AuthMethod), gohbase.Password(mainConf.PassWord))
	}
}

// 根据table name，row key和对应的value，创建记录到hbase main集群
func PutsByRowKeyIntoMain(ctx context.Context, table, rowKey string, values map[string]map[string][]byte) error {
	putRequest, err := gohbase.NewPutStr(ctx, table, rowKey, values)
	if err != nil {
		logger.CtxLogErrorf(ctx, "hrpc.NewPutStr: %s", err.Error())
		return err
	}
	_, err = doPutRequestIntoMain(ctx, putRequest)
	if err != nil {
		logger.CtxLogErrorf(ctx, "hbase clients: %s", err.Error())
		return err
	}
	return nil
}

func ScanFromMainWithChannel(ctx context.Context, tableName, startRowKey, endRowKey string, limit uint32, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) (err error) {
	scanRequest, err := gohbase.NewScanRangeStr(ctx, tableName, startRowKey, endRowKey, gohbase.NumberOfRows(limit))
	if err != nil {
		logger.CtxLogErrorf(ctx, "ScanFromMainWithChannel|get gohbase new scan str err: %v", err)
		return err
	}
	conf := configutil.GetMainHbaseConfig(ctx) //用Apollo配置控制循环次数
	return doScanFromMainWithChannel(ctx, scanRequest, conf.MaxLoopSize, ch, rateLimiter)
}

func GetFromMainByRowKey(ctx context.Context, table, rowKey string) ([]byte, error) {
	getRequest, err := gohbase.NewGetStr(ctx, table, rowKey)
	if err != nil {
		logger.CtxLogErrorf(ctx, "hrpc.NewGetStr: %s", err.Error())
		return []byte{}, err
	}
	result, err := doGetRequestIntoMain(ctx, getRequest)
	if err != nil {
		logger.CtxLogErrorf(ctx, "hbase clients: %s", err.Error())
		return []byte{}, err
	}

	if len(result.Cells) == 0 {
		return []byte{}, errors.New("no cells found")
	}

	return result.Cells[0].Value, nil
}

func ScanWithResultSize(ctx context.Context, tableName, startRowKey, endRowKey string, resultSize int, rateLimiter *hbaseutil.RateLimiter) ([]*gohbase.Result, error) {
	scanRequest, err := gohbase.NewScanRangeStr(ctx, tableName, startRowKey, endRowKey, gohbase.NumberOfRows(defaultScanLimit))
	if err != nil {
		logger.CtxLogErrorf(ctx, "ScanWithResultSize|get gohbase new scan str err: %v", err)
		return nil, err
	}
	conf := configutil.GetMainHbaseConfig(ctx) //用Apollo配置控制循环次数
	return doScanFromMainWithResultSize(ctx, scanRequest, conf.MaxLoopSize, resultSize, rateLimiter)
}
