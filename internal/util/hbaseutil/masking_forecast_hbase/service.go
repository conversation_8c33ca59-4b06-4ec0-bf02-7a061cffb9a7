package masking_forecast_hbase

import (
	"context"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
)

// 封装storage集群相关的CRUD，等main集群上线后，考虑下线该部分代码
type HBHelper struct {
}

func NewHBHelper() *HBHelper {
	return &HBHelper{}
}

func (h *HBHelper) UpdateRow(ctx context.Context, tableName, rowKey string, values map[string]map[string][]byte) (err error) {
	return UpdateHbase(ctx, tableName, rowKey, values)
}

func (h *HBHelper) ScanByIndex(ctx context.Context, tableName, startRow, stopRow string, limit uint32) ([]*gohbase.Result, string, error) {
	return GetHbaseIndex(ctx, startRow, stopRow, tableName, limit)
}

func (h *HBHelper) ScanByIndexWithChannel(ctx context.Context, tableName, startRow, stopRow string, limit uint32, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) error {
	return QueryWithLimitAndChannel(ctx, tableName, startRow, stopRow, limit, ch, rateLimiter)
}

func (h *HBHelper) GetDataFromHbaseByRowKey(ctx context.Context, tableName string, rowKey string) ([]byte, error) {
	return doGtDataFromHbaseByRowKey(ctx, tableName, rowKey)
}
