package masking_forecast_hbase

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
)

const (
	MASKING_FORECASTING = "3pl_forecast"
	ROUTING_LOG         = "routing_log"
)

var client gohbase.Client //当前连接storage集群，等main集群迁移完后再删掉

func InitDataHBase() {
	// 初始化需要传入一个空context
	ctx := context.Background()
	c := configutil.GetDataHbaseConfig(ctx)
	if c.LocalHost {
		client = gohbase.NewClient("localhost")
	} else {
		logger.LogInfof("[hbase connect the info is %+v", c)
		//HbaseClient = gohbase.NewClient(c.HostUrl, gohbase.EffectiveUser(c.UserName), gohbase.ZookeeperRoot(c.ZkRoot))
		client = gohbase.NewClient(c.HostUrl, gohbase.EffectiveUser(c.UserName), gohbase.ZookeeperRoot(c.ZkRoot), gohbase.Auth(c.AuthMethod), gohbase.Password(c.PassWord))
	}
}

func QueryWithLimit(ctx context.Context, table, startRow, stopRow string, limit uint32) ([]*gohbase.Result, error) {
	scanRequest, err := gohbase.NewScanRangeStr(context.Background(), table, startRow, stopRow, gohbase.NumberOfRows(limit))
	if err != nil {
		logger.CtxLogErrorf(ctx, "gohbase.NewScanStr: %s", err.Error())
		return nil, err
	}
	conf := configutil.GetDataHbaseConfig(ctx) //用Apollo配置控制循环次数
	return doHBaseScanRequestWithMonitor(ctx, scanRequest, conf.MaxLoopSize)
}

func GetHbaseIndex(ctx context.Context, startRow, stopRow, table string, limit uint32) ([]*gohbase.Result, string, error) {
	var nextIndex string
	temScanner, err := QueryWithLimit(ctx, table, startRow, stopRow, limit)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetHbaseIndex with limit: ", err)
		return nil, "", nil
	}
	if len(temScanner) <= int(limit) {
		nextIndex = ""
		return temScanner, nextIndex, nil
	} else {
		nextIndex = string(temScanner[limit].Cells[0].Row)
		return temScanner[:len(temScanner)-1], nextIndex, nil
	}
}

func QueryWithLimitAndChannel(ctx context.Context, table, startRow, stopRow string, limit uint32, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) error {
	scanRequest, err := gohbase.NewScanRangeStr(ctx, table, startRow, stopRow, gohbase.NumberOfRows(limit))
	if err != nil {
		logger.CtxLogErrorf(ctx, "gohbase.NewScanStr: %s", err.Error())
		return err
	}
	conf := configutil.GetDataHbaseConfig(ctx) //用Apollo配置控制循环次数
	return doHBaseScanRequestWithMonitorAndChannel(ctx, scanRequest, conf.MaxLoopSize, ch, rateLimiter)
}

// UpdateHbase
func UpdateHbase(ctx context.Context, table, rowKey string, values map[string]map[string][]byte) error {
	putRequest, err := gohbase.NewPutStr(ctx, table, rowKey, values)
	if err != nil {
		logger.CtxLogErrorf(ctx, "gohbase.NewPutStr: %s", err.Error())
		return err
	}
	res, err := doHBasePutRequestWithMonitor(ctx, putRequest)
	logger.CtxLogInfof(ctx, "res=%v", res)
	if err != nil {
		logger.CtxLogErrorf(ctx, "hbase clients: %s", err.Error())
		return err
	}
	return nil
}

func doGtDataFromHbaseByRowKey(ctx context.Context, table, rowKey string) ([]byte, error) {
	scanRequest, err := gohbase.NewGetStr(ctx, table, rowKey)
	if err != nil {
		logger.CtxLogErrorf(ctx, "gohbase.NewGetStr: %s", err.Error())
		return nil, err
	}
	result, rerr := client.Get(scanRequest)
	if rerr != nil {
		return nil, rerr
	}
	if len(result.Cells) == 0 {
		return nil, errors.New("cell is zero")
	}
	return result.Cells[0].Value, nil
}
