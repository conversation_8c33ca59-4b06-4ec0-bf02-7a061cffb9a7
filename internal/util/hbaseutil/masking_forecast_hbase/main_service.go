package masking_forecast_hbase

import (
	"context"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
)

// 封装hbase main集群相关的CRUD
type MainHBHelper struct {
}

func NewMainHBHelper() *MainHBHelper {
	return &MainHBHelper{}
}

// 根据table name，row key和对应的value，创建记录到hbase main集群
func (h *MainHBHelper) CreateNewRowIntoMain(ctx context.Context, tableName, rowKey string, values map[string]map[string][]byte) (err error) {
	return PutsByRowKeyIntoMain(ctx, tableName, rowKey, values)
}

// 根据tableName， startRowKey，endRowKey检索数据，两个row key均支持前缀匹配
func (h *MainHBHelper) ScanWithChannel(ctx context.Context, tableName, startRowKey, endRowKey string, limit uint32, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) (err error) {
	return ScanFromMainWithChannel(ctx, tableName, startRowKey, endRowKey, limit, ch, rateLimiter)
}

// 根据tableName, rowKey来获取数据
func (h *MainHBHelper) GetByRowKey(ctx context.Context, tableName, rowKey string) ([]byte, error) {
	return GetFromMainByRowKey(ctx, tableName, rowKey)
}

func (h *MainHBHelper) ScanWithResultSize(ctx context.Context, tableName, startRowKey, endRowKey string, resultSize int, rateLimiter *hbaseutil.RateLimiter) ([]*gohbase.Result, error) {
	return ScanWithResultSize(ctx, tableName, startRowKey, endRowKey, resultSize, rateLimiter)
}
