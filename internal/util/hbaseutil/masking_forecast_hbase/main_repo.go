package masking_forecast_hbase

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"golang.org/x/time/rate"
	"io"
	"modernc.org/mathutil"
)

func doPutRequestIntoMain(ctx context.Context, p *gohbase.Mutate) (*gohbase.Result, error) {
	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	tableName := str.GenKey(":", string(p.Table()), "create")

	result, err := mainClient.Put(p)
	if err != nil {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
		return nil, err
	}
	endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
	return result, err
}

func doScanFromMainWithChannel(ctx context.Context, p *gohbase.Scan, maxLoopSize int, ch chan *gohbase.Result, rateLimiter *hbaseutil.RateLimiter) error {
	//SSCSMR-1480:配置限速器
	var limiter *rate.Limiter
	if rateLimiter != nil {
		limiter = rate.NewLimiter(rate.Limit(rateLimiter.Limit), rateLimiter.Burst)
	}

	//SPLPS_9385:将default Max loop配置在Apollo上
	maxLoopSize = mathutil.Min(maxLoopSize, maxLoopSizeConstant)

	var (
		err         error
		res         *gohbase.Result
		successFlag = false
	)

	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	tableName := str.GenKey(":", string(p.Table()), "scan")

	scanner := mainClient.Scan(p)
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		res, err = scanner.Next()
		if err == io.EOF || res == nil {
			successFlag = true
			break
		}
		if err != nil {
			break
		}
		//SSCSMR-1480:控制生产者速度
		if limiter != nil {
			_ = limiter.Wait(ctx)
		}
		ch <- res

		if loopIndex >= maxLoopSize {
			logger.CtxLogInfof(ctx, "loop over max size, max_size=%d, will return", maxLoopSize)
			successFlag = true
			break
		}
	}

	_ = scanner.Close()

	if successFlag {
		endFunc(monitoring.CatModuleMainHBase, tableName, monitoring.StatusSuccess, "")
		err = nil
	} else if err != nil {
		endFunc(monitoring.CatModuleMainHBase, tableName, monitoring.StatusError, err.Error())
	}
	return err
}

func doGetRequestIntoMain(ctx context.Context, g *gohbase.Get) (*gohbase.Result, error) {
	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	tableName := str.GenKey(":", string(g.Table()), "get")

	result, err := mainClient.Get(g)
	if err != nil {
		endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusError, err.Error())
		return nil, err
	}
	endFunc(monitoring.CatModuleHBase, tableName, monitoring.StatusSuccess, "")
	return result, err
}

func doScanFromMainWithResultSize(ctx context.Context, p *gohbase.Scan, maxLoopSize int, resultSize int, rateLimiter *hbaseutil.RateLimiter) ([]*gohbase.Result, error) {
	var limiter *rate.Limiter
	if rateLimiter != nil {
		limiter = rate.NewLimiter(rate.Limit(rateLimiter.Limit), rateLimiter.Burst)
	}

	//SPLPS_9385:将default Max loop配置在Apollo上
	maxLoopSize = mathutil.Min(maxLoopSize, maxLoopSizeConstant)

	var (
		results     []*gohbase.Result
		err         error
		res         *gohbase.Result
		successFlag = false
	)

	_, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	tableName := str.GenKey(":", string(p.Table()), "scan")

	scanner := mainClient.Scan(p)
	var loopIndex int
	for loopIndex = 0; loopIndex < maxLoopSize; loopIndex++ {
		res, err = scanner.Next()
		if err == io.EOF || res == nil {
			logger.CtxLogErrorf(ctx, "scan hbase err:%v", err)
			successFlag = true
			break
		}
		if err != nil {
			break
		}

		//控制生产者速度
		if limiter != nil {
			_ = limiter.Wait(ctx)
		}

		results = append(results, res)
		if resultSize != 0 && len(results) == resultSize {
			break
		}

		if loopIndex >= maxLoopSize {
			logger.CtxLogInfof(ctx, "loop over max size, max_size=%d, will return", maxLoopSize)
			successFlag = true
			break
		}
	}

	_ = scanner.Close()

	if successFlag {
		endFunc(monitoring.CatModuleMainHBase, tableName, monitoring.StatusSuccess, "")
		err = nil
	} else if err != nil {
		endFunc(monitoring.CatModuleMainHBase, tableName, monitoring.StatusError, err.Error())
	}
	return results, err
}
