package srerr

import "fmt"

const (
	CodeTypeBusiness = 1
	CodeTypeService  = 2
	CodeTypeCommon   = 3
)

// Common Cat
const (
	CatSys      = 1
	CatRequest  = 2
	CatInfra    = 3
	CatExternal = 4
	CatSaturn   = 5
)

// Business Cat
const (
	CatAdmin = iota + 1
	CatLocation
	CatAllocationPath
	CatAllocation
	CatCCRouting
	CatForecastAdmin
	CatSelectLane
	CatVolumeCounter
	CatForecastTask
	CatBatchAllocation
	CatSpexService
)

var (
	codeMap = make(map[int]*Code)
)

type (
	Code struct {
		codeType CodeType
		category uint8
		msg      string
		code     int
	}

	CodeType uint8

	ExtSystem int

	ExtCode interface {
		GetRetCode() int
		GetMessage() string
	}
)

func GetMap() map[int]*Code {
	return codeMap
}

func newCode(codeType CodeType, category uint8, code int, msg string) *Code {
	c := Code{
		codeType: codeType,
		category: category,
		msg:      msg,
		code:     code,
	}
	typ, cat := c.getTypeCategory()
	if typ != codeType {
		panic(fmt.Sprintf("code type:%v not match for code:%v", codeType, code))
	}
	if cat != category {
		panic(fmt.Sprintf("code category:%v not match for code:%v", category, code))
	}
	return &c
}

func newTempCode(code int, msg string) *Code {
	return &Code{
		msg:  msg,
		code: code,
	}
}

func (c *Code) getTypeCategory() (CodeType, uint8) {
	if c.code < 100 {
		panic(fmt.Sprintf("code must have 3 digits at least, current:%d", c.code))
	}
	mux := 1
	muxTimes := 0
	for i := 0; ; i++ {
		if c.code < mux {
			break
		}
		mux *= 10
		muxTimes += 1
	}
	// 没有考虑错误码类型超过9时的场景，这里处理错误码类型大于9的场景
	if muxTimes == 6 {
		return CodeType(c.code * 10 / mux), uint8((c.code * 1000 / mux) % 100)
	}

	return CodeType(c.code * 10 / mux), uint8((c.code * 100 / mux) % 10)
}

func (c *Code) IsEqual(other Code) bool {
	return c.codeType == other.codeType &&
		c.category == other.category &&
		c.code == other.code
}

func (c *Code) Code() int {
	return c.code
}

func (c *Code) Msg() string {
	return c.msg
}

type CodeCategoryFactory struct {
	Type CodeType
}

type CodeFactory struct {
	Type     CodeType
	Category uint8
}

func Common() *CodeCategoryFactory {
	return &CodeCategoryFactory{
		Type: CodeTypeCommon,
	}
}

func Business() *CodeCategoryFactory {
	return &CodeCategoryFactory{
		Type: CodeTypeBusiness,
	}
}

func Service() *CodeCategoryFactory {
	return &CodeCategoryFactory{
		Type: CodeTypeService,
	}
}

func (f *CodeCategoryFactory) New(cat uint8) *CodeFactory {
	return &CodeFactory{
		Type:     f.Type,
		Category: cat,
	}
}

func (f *CodeFactory) New(code int, msg string) *Code {
	c := newCode(f.Type, f.Category, code, msg)
	c1, ok := codeMap[c.code]
	if ok {
		panic(fmt.Sprintf("create new error code fail, code:%d duplicated, current message:%v, past message:%v", code, msg, c1.msg))
	}
	codeMap[c.code] = c
	return c
}

func GetCodeFromIntCode(codeCode int) *Code {
	code, ok := codeMap[codeCode]
	if !ok {
		return nil
	}
	return code
}
