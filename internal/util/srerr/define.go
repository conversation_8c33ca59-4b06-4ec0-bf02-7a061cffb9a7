package srerr

var (
	SysError         = Common().New(CatSys)
	ServerErr        = SysError.New(31001, "server error")
	DataErr          = SysError.New(31002, "data error")
	JsonErr          = SysError.New(31003, "json error")
	NilErr           = SysError.New(31004, "nil pointer error")
	TypeConvertErr   = SysError.New(31005, "type convert error")
	NoImplementErr   = SysError.New(31006, "no implement")
	EmptyResultErr   = SysError.New(31007, "empty result")
	KafkaServerError = SysError.New(31008, "kafka server error")
	KafkaInitError   = SysError.New(31009, "kafka init error")
	SendMessage      = SysError.New(31010, "send message error")
)

var (
	RequestError       = Common().New(CatRequest)
	FormatErr          = RequestError.New(32001, "format error")
	ParamErr           = RequestError.New(32002, "parameter validate fail")
	JwtTokenErr        = RequestError.New(32003, "jwt token error")
	HeaderAuthErr      = RequestError.New(32004, "TokenValidateFail")
	ReadRequestBodyErr = RequestError.New(32005, "fail to read request's body")
	GoroutinePoolErr   = RequestError.New(32006, "goroutine pool failed")
	ZstdErr            = RequestError.New(32007, "zstd decompress failed")
)

var (
	InfraError      = Common().New(CatInfra)
	DatabaseErr     = InfraError.New(33001, "db error")
	CodisErr        = InfraError.New(33002, "codis error")
	LocalCacheErr   = InfraError.New(33003, "local cache error")
	LevelCacheErr   = InfraError.New(33004, "level cache error")
	S3UploadFail    = InfraError.New(33005, "s3 upload fail")
	S3DownloadFail  = InfraError.New(33006, "s3 download fail")
	RedisErr        = InfraError.New(33007, "redis error")
	ParseExcelError = InfraError.New(33008, "parse excel error")
	MakeExcelError  = InfraError.New(33009, "make excel error")
)

var (
	ExternalError     = Common().New(CatExternal)
	LpsError          = ExternalError.New(34001, "lps api error")
	LfsError          = ExternalError.New(34002, "lfs api error")
	LlsError          = ExternalError.New(34003, "lls api error")
	ChargeApiError    = ExternalError.New(34004, "charge api error")
	MockServerErr     = ExternalError.New(34005, "mock server error")
	DataApiErr        = ExternalError.New(34006, "data api error")
	CustomsServiceErr = ExternalError.New(34007, "customs service error")
	AuditApiErr       = ExternalError.New(34008, "audit api error")
	WbcApiErr         = ExternalError.New(34009, "wbc api error")
	SpexError         = ExternalError.New(34010, "spex service error")
	LcosError         = ExternalError.New(34011, "lcos api error")
)

var (
	SaturnError             = Common().New(CatSaturn)
	SendAsyncJobMsgFail     = SaturnError.New(35001, "saturn send msg job message fail")
	SyncScheduleCountError  = SaturnError.New(35002, "sync schedule count error")
	ClearScheduleCountError = SaturnError.New(35003, "clear schedule count error")
)

var (
	AdminError                     = Business().New(CatAdmin)
	ZoneGroupCountFail             = AdminError.New(11001, "zone group list page fail, count fail")
	ZoneGroupPageFail              = AdminError.New(11002, "zone group list page fail, query page fail")
	ZoneGroupCreateFail            = AdminError.New(11003, "zone group create fail, db insert fail")
	ZoneGroupUpdateFail            = AdminError.New(11004, "zone group update fail, db update fail")
	ZoneGroupGetFail               = AdminError.New(11005, "zone group get fail, db select fail")
	ZoneGroupNotFound              = AdminError.New(11006, "zone group not found")
	ZoneGroupDuplicate             = AdminError.New(11007, "duplicated zone group id")
	ZoneGroupMaskProductDuplicate  = AdminError.New(11008, "duplicated mask product for same group id")
	ZoneGroupProductDuplicate      = AdminError.New(11009, "duplicated product for same group id")
	ZoneGroupLineDuplicate         = AdminError.New(11010, "duplicated line for same group id")
	GroupCapacityRefUpdateFail     = AdminError.New(11011, "zone capacity update fail")
	GroupCapacityRefCountFail      = AdminError.New(11012, "zone capacity count fail, db count fail")
	GroupCapacityRefGetFail        = AdminError.New(11013, "zone capacity get fail, db get fail")
	GroupCapacityParseExcelFail    = AdminError.New(11014, "zone capacity parse upload file fail")
	LocationZoneCountFail          = AdminError.New(11015, "count location zone fail")
	PostcodeZoneCountFail          = AdminError.New(11016, "count postcode zone fail")
	CepRangeZoneCountFail          = AdminError.New(11017, "count cep range zone fail")
	LocationZoneQueryFail          = AdminError.New(11018, "query location zone fail")
	PostcodeZoneQueryFail          = AdminError.New(11019, "query postcode zone fail")
	CepRangeZoneQueryFail          = AdminError.New(11020, "query cep range zone fail")
	VolumeTaskCreateFail           = AdminError.New(11021, "create volume task fail")
	VolumeTaskQueryFail            = AdminError.New(11022, "query volume task fail")
	VolumeTaskUpdateFail           = AdminError.New(11023, "update volume task fail")
	ZoneExportFail                 = AdminError.New(11024, "zone export fail")
	ZoneImportEmptyGroupId         = AdminError.New(11025, "empty group id for zone import")
	ZoneImportInvalidTaskStatus    = AdminError.New(11026, "invalid task status for zone import")
	ZoneImportGetFileFail          = AdminError.New(11027, "get import file fail")
	ZoneImportParseExcelFail       = AdminError.New(11028, "parse import excel fail")
	ZoneImportCheckFail            = AdminError.New(11029, "check zone import location data fail")
	LocationZoneDeleteFail         = AdminError.New(11030, "location zone delete fail")
	LocationZoneBatchCreateFail    = AdminError.New(11031, "location zone batch create fail")
	ZoneImportUploadFail           = AdminError.New(11032, "zone import upload fail")
	PostcodeZoneDeleteFail         = AdminError.New(11033, "postcode zone delete fail")
	PostcodeZoneBatchCreateFail    = AdminError.New(11034, "postcode zone batch create fail")
	CepRangeZoneDeleteFail         = AdminError.New(11035, "cep range zone delete fail")
	CepRangeZoneBatchCreateFail    = AdminError.New(11036, "cep range zone batch create fail")
	ZoneRuleCountFail              = AdminError.New(11037, "")
	ZoneRuleQueryFail              = AdminError.New(11038, "")
	ZoneRuleCreateFail             = AdminError.New(11039, "")
	ZoneRuleCheckEditFail          = AdminError.New(11040, "")
	ZoneRuleUpdateFail             = AdminError.New(11041, "")
	ZoneRuleNotFound               = AdminError.New(11042, "")
	ZoneRuleCheckDeleteFail        = AdminError.New(11043, "")
	ZoneRuleDeleteFail             = AdminError.New(11044, "")
	ZoneRuleDetailDeleteFail       = AdminError.New(11045, "")
	ZoneRuleCheckDisableFail       = AdminError.New(11046, "")
	ZoneRuleDisableFail            = AdminError.New(11047, "")
	ZoneRuleDetailCountFail        = AdminError.New(11048, "")
	ZoneRuleDetailQueryFail        = AdminError.New(11049, "")
	ZoneRuleDetailDownloadFail     = AdminError.New(11050, "")
	ZoneRuleDetailParseExcelFail   = AdminError.New(11051, "")
	ZoneRuleDetailExportFail       = AdminError.New(11052, "")
	ZoneRuleDetailInsertFail       = AdminError.New(11053, "")
	ZoneNameNotFound               = AdminError.New(11054, "zone name not found")
	GroupDeleteFail                = AdminError.New(11055, "group not found")
	MaskingPanelExportFail         = AdminError.New(11056, "export masking panel data error")
	CreateTaskRecordFail           = AdminError.New(11057, "create task record error")
	UpdateTaskRecordFail           = AdminError.New(11058, "update task record error")
	PriorityNotAvailable           = AdminError.New(11059, "priority less than 1 or greater than 1000priority less than 1 or greater than 1000")
	ListParamsLengthLimitExceed    = AdminError.New(11060, "whs_id/Zone_code exceed length limit 15")
	RuleNotEditable                = AdminError.New(11061, "rule is not editable")
	DisableLineErr                 = AdminError.New(11062, "fail to toggle Line")
	RuleNotDeletable               = AdminError.New(11063, "rule is not deletable")
	RuleNotCopiable                = AdminError.New(11064, "rule is not copiable")
	RuleNotCopiableAsLanefee       = AdminError.New(11065, "The rule could not be duplicated as it contains the lane-cheapest shipping fee criteria.")
	RuleNotRollbackable            = AdminError.New(11066, "fail to roll-back routing rule data")
	DataNotFound                   = AdminError.New(11067, "Data not found from db")
	DataDuplicated                 = AdminError.New(11068, "Data duplicated in db")
	ConfigProductExisted           = AdminError.New(11069, "routing config of product existed")
	ExcelFileOpenError             = AdminError.New(11070, "cannot open excel file")
	InsertZoneCodeErr              = AdminError.New(11071, "error happen while insert zone code")
	ExcelValidateError             = AdminError.New(11072, "Header validate fail")
	GetProductBaseFail             = AdminError.New(11073, "get product base info fail")
	DeleteZoneCodeErr              = AdminError.New(11074, "error happen while delete zone code")
	SaveExcelErr                   = AdminError.New(11075, "fail to save excel file")
	ParseProductIDErr              = AdminError.New(11076, "fail to parse product id")
	ProductNotFound                = AdminError.New(11077, "product not found")
	LocationCoverErr               = AdminError.New(11078, "location cover err")
	GetLpsProductErr               = AdminError.New(11079, "get lps product err")
	AllocationConfigNotFound       = AdminError.New(11080, "allocation config not found")
	DraftRuleOfMaskProductExisted  = AdminError.New(11081, "draft rule of mask product existed")
	InValidEffectiveTimeError      = AdminError.New(11082, "must have effective rule volume before rule effective")
	InvalidAllocationConfig        = AdminError.New(11083, "invalid allocation config")
	ProductPriorityCreateFail      = AdminError.New(11084, "create product priority fail")
	ProductPriorityUpdateFail      = AdminError.New(11085, "update product priority fail")
	ShopGroupNotExisted            = AdminError.New(11086, "shop group not existed")
	ProductInfoNotFound            = AdminError.New(11087, "product info not found")
	ProductPriorityPartSuccess     = AdminError.New(11088, "import product priority part success")
	HaveMultiUpcomingPriority      = AdminError.New(11089, "have multi upcoming product priority")
	ImportSaveProductPriorityFail  = AdminError.New(11090, "import save product priority fail")
	RedisLockFail                  = AdminError.New(11091, "obtain redis lock fail")
	ExportParamCanNotNull          = AdminError.New(11092, "export param can not null")
	ImportDataExtendRestrict       = AdminError.New(11093, "import data too large, extend 60000")
	ImportHeaderError              = AdminError.New(11094, "import header is invalid")
	SplitSubTaskError              = AdminError.New(11095, "failed to split task into sub tasks")
	BatchCreateSubTaskError        = AdminError.New(11096, "failed to create sub tasks")
	CreateMaskingForecastTaskError = AdminError.New(11097, "failed to create masking forecast task")
	GetOnlineAllocateRuleError     = AdminError.New(11098, "failed to get online allocate rule")
	MarshalOnlineAllocateRuleError = AdminError.New(11099, "failed to marshal online allocate rule")
	EmptySubTasksError             = AdminError.New(11100, "subtasks are empty")
	SelectSubTaskError             = AdminError.New(11101, "failed to select sub tasks")
	UpdateSubTaskError             = AdminError.New(11102, "failed to update sub tasks")
	BatchDeleteSubTaskError        = AdminError.New(11103, "failed to batch delete sub tasks")
	BatchUpdateSubTaskError        = AdminError.New(11104, "failed to batch update sub tasks")
	ImportWeightRangeErr           = AdminError.New(11105, "import weight failed")
	WeightRangeErr                 = AdminError.New(11106, "weight range must be integer")
	ImportOrderCountErr            = AdminError.New(11107, "import order count err")
	OrderCountErr                  = AdminError.New(11108, "day and order count must be integer")
	VolumeRuleNotFount             = AdminError.New(11109, "Volume Rule is not found")
	VolumeRuleError                = AdminError.New(11110, "Volume Rule Error")
	LineMappingNotALlowed          = AdminError.New(11111, "cant update linemapping after enable smartrouting")
	CantEnableRouting              = AdminError.New(11112, "cant enable routing when no active rule")
	ProductModeCheckFailed         = AdminError.New(11113, "cant enable routing when no active rule")
	QueryListError                 = AdminError.New(11114, "query post code list failed")
	DeletePostCodeError            = AdminError.New(11115, "delete post code failed")
	EditPostCodeError              = AdminError.New(11116, "edit post code failed")
	ImportPostCodeError            = AdminError.New(11117, "import post code failed")
	ParsePostCodeExcelError        = AdminError.New(11118, "parse post code excel failed")
	PostCodeExcelHeaderError       = AdminError.New(11119, "header invalid header")
	PostCodeExcelAddModeError      = AdminError.New(11120, "add mode save failed")
	QueryPostForUpdateError        = AdminError.New(11121, "query post code for update failed")
	ImportPostCodeColumnError      = AdminError.New(11122, "Excel columns error")
	PostCodeActionModeError        = AdminError.New(11123, "import action is empty")
	ActionError                    = AdminError.New(11124, "action is not 1 or 2")
	PostCodeExcelContentError      = AdminError.New(11125, "excel content error")
	MultiFilterParamError          = AdminError.New(11126, "zone_code、route_code、shop_group only one can be search")
	TitleVolumeLengthInValid       = AdminError.New(11127, "title volume list length is invalid")
	BlankFillError                 = AdminError.New(11128, "illegal blank fill type")
	HistoryInfoExportErr           = AdminError.New(11129, "failed to export history info")
	ForecastInfoExportErr          = AdminError.New(11130, "failed to export forecast info")
	DifferentRoutingType           = AdminError.New(11131, "products have different routing type")
	DuplicateDefaultSelectGroup    = AdminError.New(11132, "duplicate default select group")
	DefaultSelectGroupNotExist     = AdminError.New(11133, "default select group not exist")
	ProductExistInOtherGroup       = AdminError.New(11134, "product exist in other group")
	RoleDiffInShareVolumeProduct   = AdminError.New(11135, "Line has the different routing role in Product Config")
	DgTypeInvalidError             = AdminError.New(11136, "DgType is invalid, should be 1 or 2")
	ParcelDimensionInvalidError    = AdminError.New(11137, "ParcelDimension is invalid, must both be present if one is present")
	RuleStatusInvalid              = AdminError.New(11138, "rule status is invalid")
)

var (
	ServiceError          = Business().New(CatLocation)
	InvalidAddressData    = ServiceError.New(12001, "invalid address data")
	LocationNotFound      = ServiceError.New(12002, "location not found")
	LineHasMultiPoint     = ServiceError.New(12003, "Line has multi pickup/deliver point, can not calc shipping fee")
	GetLocationInfoFailed = ServiceError.New(12004, "can't get location info by giving location")
	GetProductZonesFailed = ServiceError.New(12005, "can't get zone by giving product id")
	SameLevelLocationErr  = ServiceError.New(12006, "the same level upload district and street must empty")
)

var (
	AllocationPahError    = Business().New(CatAllocationPath)
	InvalidRequestIdError = AllocationPahError.New(13001, "invalid request_id")
	InvalidOrderIdError   = AllocationPahError.New(13002, "invalid order_id")
)

var (
	AllocationError                   = Business().New(CatAllocation)
	NoAvailableProduct                = AllocationError.New(14001, "no available product")
	AllocationConfigError             = AllocationError.New(14002, "get allocation config error")
	AllocationLocVolumeError          = AllocationError.New(14003, "get allocation local volume error")
	AllocationRuleError               = AllocationError.New(14004, "get allocation rule error")
	AllocateError                     = AllocationError.New(14005, "allocate error")
	AllocateProductPriorityError      = AllocationError.New(14006, "get masking product priority error")
	NoProductUnderShopsError          = AllocationError.New(14007, "No available products under the shop ids")
	AdjustCapacityError               = AllocationError.New(14008, "adjust masking capacity error")
	GetMaskRuleConfigError            = AllocationError.New(14009, "get mask rule config error")
	AlgoError                         = AllocationError.New(14010, "algo client error")
	BatchAllocateOrderNotFoundError   = AllocationError.New(14011, "batch allocate order not found error")
	OrderResultNotFoundError          = AllocationError.New(14012, "order result not found error")
	CheckoutFProductUpdateVolumeError = AllocationError.New(14013, "checkout fulfillment product update volume error")
	InstallAllocateError              = AllocationError.New(14014, "install allocate error")
	DuplicateItemUniqueIdError        = AllocationError.New(14015, "duplicate item unique id error")
)

var (
	CCRoutingError        = Business().New(CatCCRouting)
	CCRoutingRuleNotFound = CCRoutingError.New(15001, "cc routing rule not found")
	CCRoutingFailed       = CCRoutingError.New(15002, "cc routing failed, no available cc")
	CCRoutingRuleExist    = CCRoutingError.New(15003, "product's cc routing rule already exist")
)

var (
	ForecastAdminError                    = Business().New(CatForecastAdmin)
	TaskNotFound                          = ForecastAdminError.New(16001, "task not found")
	TaskErrorNotFound                     = ForecastAdminError.New(16002, "task error not found")
	TaskRuleNotFound                      = ForecastAdminError.New(16003, "task rule not found")
	ResultNotFound                        = ForecastAdminError.New(16004, "result not found")
	TaskExistDraft                        = ForecastAdminError.New(16005, "task exist draft")
	TaskNotAllowEdit                      = ForecastAdminError.New(16006, "draft task not found")
	TaskNotAllowDelete                    = ForecastAdminError.New(16007, "task not allow delete")
	TaskNotAllowCopy                      = ForecastAdminError.New(16008, "task not allow copy")
	TaskNotAllowDeploy                    = ForecastAdminError.New(16009, "task not allow deploy")
	TaskNotAllowStart                     = ForecastAdminError.New(16010, "task not allow start")
	TaskNotAllowStop                      = ForecastAdminError.New(16011, "task not allow stop")
	TaskOfOtherTypeExisted                = ForecastAdminError.New(16012, "other types of task existed")
	TaskProductNotAdaptedSmartRoutingType = ForecastAdminError.New(16013, "task's product does not adapted to smart routing type")
	ParseTimeErr                          = ForecastAdminError.New(16014, "Parse string to time error")
	HardCriteriaTaskExisted               = ForecastAdminError.New(16015, "Hard criteria task existed")
	UpdateErr                             = ForecastAdminError.New(16016, "Update err")
	ForecastFailed                        = ForecastAdminError.New(16017, "Forecast failed")
)

var (
	VolumeCounterError       = Business().New(CatVolumeCounter)
	VolumeCounterParamsError = VolumeCounterError.New(18001, "volume counter params verify fail")
	VolumeCounterDuplicate   = VolumeCounterError.New(18002, "volume counter duplicate")
)

var (
	ForecastTaskError             = Business().New(CatForecastTask)
	NotMatchError                 = ForecastTaskError.New(19001, "not match task")
	ConvertKeyError               = ForecastTaskError.New(19002, "failed to convert key")
	TaskNotReadyError             = ForecastTaskError.New(19003, "task is not ready")
	PutHbaseError                 = ForecastTaskError.New(19004, "put request into hbase error")
	GetHbaseError                 = ForecastTaskError.New(19005, "get request from hbase error")
	ScanHbaseError                = ForecastTaskError.New(19006, "scan from hbase error")
	BatchAllocateExecuteSDKFailed = ForecastTaskError.New(19007, "Execute SDK Failed")
	InitAntsPoolError             = ForecastTaskError.New(19008, "init ants pool Failed")
	GetLastBatchError             = ForecastTaskError.New(19009, "get last batch error")
	GetNoOrderError               = ForecastTaskError.New(19010, "get no order")
)

var (
	SpexServiceError = Business().New(CatSpexService)
	ItemNotFound     = SpexServiceError.New(111001, "item not found")
)
