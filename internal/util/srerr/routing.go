package srerr

var (
	SelectLaneError              = Business().New(CatSelectLane)
	RuleNotFound                 = SelectLaneError.New(17000, "rule not found")
	NoAvailableLane              = SelectLaneError.New(17001, "no available lane after filter rule's availability")
	RoutingRuleException         = SelectLaneError.New(17002, "get routing rule exception")
	RoutingConfigNotFound        = SelectLaneError.New(17003, "smart routing config not found")
	RoutingConfigDuplicate       = SelectLaneError.New(17004, "smart routing config duplicate")
	ParamInvalid                 = SelectLaneError.New(17005, "required filed not set")
	TooManyAvailableLanes        = SelectLaneError.New(17006, "too many available lanes, smart routing needed")
	NoAvailableLaneList          = SelectLaneError.New(17007, "no available lane list to smart routing")
	GetCCResultFailed            = SelectLaneError.New(17008, "get pre authorization customs clearance result failed")
	NoAvailableLaneAfterCCFilter = SelectLaneError.New(17009, "no available lane after cc filter")
	PlayBackRuleNotFount         = SelectLaneError.New(17010, "play back rule not found")

	SendMessageError = SelectLaneError.New(17105, "send-msg-to-kafka-failed")

	//需要在lps 新增mapping ?
	VnCbErr                = Business().New(CatRequest)
	RoutingCfgErr          = VnCbErr.New(12300, "wrong routing config")
	RoutingVnCbSpxPhaseErr = VnCbErr.New(12302, "there are still lanes after second spx routing")

	SystemError    = Business().New(0)
	RateLimiterErr = SystemError.New(10000, "rate limiter forbids this request")
)
