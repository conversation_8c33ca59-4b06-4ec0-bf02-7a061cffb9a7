package srerr

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"runtime"
	"strings"
)

type (
	/** 带调用链路跟踪的错误，
	使用说明：对于底层操作失败需要返回错误时，采用lpserr.New()或者lpserr.With()来新建错误
	对于上层操作失败需要返回错误时，采用lpserr.Wrap包裹错误从而形成错误跟踪，新增或包括错误时，
	如有必要，可以把错误参数加入到错误链路，可以跟清晰的发现问题
	*/
	Error struct {
		codeMsg     *Code
		cause       error
		InvokeLink  *invokeNode
		Location    string
		exchangeMsg bool
	}
	invokeNode struct {
		Name   string
		Param  interface{}
		Line   int
		ErrMsg string
		Next   *invokeNode
	}
)

func (p *Error) Error() string {
	if p == nil || p.cause == nil {
		return "nil"
	}
	return p.cause.Error()
}

func (p *Error) Cause() error {
	if p == nil || p.cause == nil {
		return nil
	}
	return p.cause
}

func (p *Error) GetCode() int {
	return p.codeMsg.code
}

func (p *Error) GetMessage() string {
	return p.codeMsg.msg
}

func (p *invokeNode) LinkMessages() []string {
	node := p
	var links []string
	for node != nil {
		links = append(links, fmt.Sprintf("Method: %s, Line %d, Param: %s Err:%s", node.Name, node.Line, objutil.JsonString(node.Param), node.ErrMsg))
		node = node.Next
	}
	return links
}

// 完全新建一个错误，用于偏底层操作错误
func New(cm *Code, param interface{}, format string, a ...interface{}) *Error {
	pc, filename, line, ok := runtime.Caller(1)
	err := fmt.Errorf(format, a...)
	return &Error{
		codeMsg:    cm,
		cause:      err,
		InvokeLink: NewInvokeNode(param, err.Error(), pc, line, ok),
		Location:   fmt.Sprintf("file %s, line %v", objutil.GetFileRelativePath(filename), line),
	}
}

// 新建一个依赖外部系统码字的错误
func NewExt(code int, msg string, param interface{}, err error) *Error {
	pc, filename, line, ok := runtime.Caller(1)
	return &Error{
		codeMsg:    newTempCode(code, msg),
		cause:      err,
		InvokeLink: NewInvokeNode(param, err.Error(), pc, line, ok),
		Location:   fmt.Sprintf("file %s, line %v", objutil.GetFileRelativePath(filename), line),
	}
}

// 以当前错误为基础生成一个错误，用于偏底层操作错误
func With(cm *Code, param interface{}, err error) *Error {
	if err == nil {
		return nil
	}
	pc, filename, line, ok := runtime.Caller(1)
	return &Error{
		codeMsg:    cm,
		cause:      err,
		InvokeLink: NewInvokeNode(param, err.Error(), pc, line, ok),
		Location:   fmt.Sprintf("file %s, line %v", objutil.GetFileRelativePath(filename), line),
	}
}

// 包括一个错误，可以构建调用链，用于偏上层操作错误
func Wrap(err *Error, param interface{}, cm *Code) *Error {
	pc, filename, line, ok := runtime.Caller(1)
	lpsErr := Error{
		codeMsg:    cm,
		cause:      err.cause,
		InvokeLink: NewInvokeNode(param, err.cause.Error(), pc, line, ok),
		Location:   fmt.Sprintf("file %s, line %v", objutil.GetFileRelativePath(filename), line),
	}
	lpsErr.InvokeLink.Next = err.InvokeLink
	return &lpsErr
}

func ReShape(err *Error, param interface{}, code int, msg string) *Error {
	pc, filename, line, ok := runtime.Caller(1)
	lpsErr := Error{
		codeMsg:    newTempCode(code, msg),
		cause:      err.cause,
		InvokeLink: NewInvokeNode(param, err.cause.Error(), pc, line, ok),
		Location:   fmt.Sprintf("file %s, line %v", objutil.GetFileRelativePath(filename), line),
	}
	lpsErr.InvokeLink.Next = err.InvokeLink
	return &lpsErr
}

func NewInvokeNode(param interface{}, errMsg string, pc uintptr, line int, ok bool) *invokeNode {
	if !ok {
		return &invokeNode{
			Name:   "XXX",
			Param:  param,
			Line:   0,
			ErrMsg: "",
			Next:   nil,
		}
	}
	funcName := runtime.FuncForPC(pc).Name()
	pos := strings.LastIndex(funcName, ".")
	funcName = funcName[pos+1:]
	return &invokeNode{
		Name:   funcName,
		Param:  param,
		Line:   line,
		ErrMsg: errMsg,
		Next:   nil,
	}
}
