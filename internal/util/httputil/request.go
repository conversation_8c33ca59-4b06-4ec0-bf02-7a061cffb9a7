package httputil

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	invoker2 "git.garena.com/shopee/bg-logistics/go/chassis/core/invoker"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/apm"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/ratelimiter"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var invoker = chassis.NewRestInvoker()

//var defaultHttpClient = http.Client{
//	Transport: &http.Transport{
//		DialContext: (&net.Dialer{
//			Timeout:   time.Second,
//			KeepAlive: 30 * time.Second,
//		}).DialContext,
//		MaxIdleConns:        0,
//		MaxIdleConnsPerHost: 50,
//		IdleConnTimeout:     120 * time.Second,
//	},
//	CheckRedirect: nil,
//	Jar:           nil,
//	Timeout:       0,
//}

func PostJson(ctx context.Context, url string, data []byte, timeoutSecond int, headers map[string]string) ([]byte, error) {
	jsonHeader := make(map[string]string, len(headers)+1)
	if len(headers) > 0 {
		for k, v := range headers {
			jsonHeader[k] = v
		}
	}
	jsonHeader["Content-Type"] = constant.ApplicationJSON
	return request(ctx, http.MethodPost, url, data, nil, timeoutSecond, jsonHeader)
}

//func PostForm(ctx context.Context, url string, param map[string]string, timeoutSecond int, headers map[string]string) ([]byte, error) {
//	formHeader := make(map[string]string, len(headers)+1)
//	if len(headers) > 0 {
//		for k, v := range headers {
//			formHeader[k] = v
//		}
//	}
//	formHeader["Content-Type"] = constant.ApplicationForm
//	return request(ctx, http.MethodPost, url, nil, param, timeoutSecond, formHeader)
//}

func Get(ctx context.Context, url string, param map[string]string, timeoutSecond int, headers map[string]string) ([]byte, error) {
	return request(ctx, http.MethodGet, url, nil, param, timeoutSecond, headers)
}

var breakerErr = errors.New("circuit breaker is open")

func checkFlow(ctx context.Context, key string) error {
	// 判断是否需要熔断
	breakerSwitch := configutil.GetBreakerConf(ctx).Config[key]
	if breakerSwitch || configutil.GetBreakerConf(ctx).GlobalSwitch {
		return breakerErr
	}

	// 判断是否需要限流
	if !ratelimiter.CheckRateLimit(ctx, key) {
		return ratelimiter.QpsLimit
	}

	return nil
}

func request(ctx context.Context, method string, reqUrl string, data []byte, param map[string]string, timeout int, headers map[string]string) ([]byte, error) {
	if err := checkFlow(ctx, getUrlWithoutSchema(reqUrl)); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	var (
		req *http.Request
		err error
	)
	if method == http.MethodGet {
		req, err = http.NewRequestWithContext(ctx, method, reqUrl, nil)
		if err != nil {
			return nil, err
		}
		q := req.URL.Query()
		for paramKey, paramVal := range param {
			q.Set(paramKey, paramVal)
		}
		req.URL.RawQuery = q.Encode()
	} else if method == http.MethodPost {
		if strings.Contains(headers["Content-Type"], constant.ApplicationForm) {
			values := url.Values{}
			for k, v := range param {
				values.Set(k, v)
			}
			req, err = http.NewRequestWithContext(ctx, method, reqUrl, strings.NewReader(values.Encode()))
			if err != nil {
				return nil, err
			}
		}
		req, err = http.NewRequestWithContext(ctx, method, reqUrl, bytes.NewBuffer(data))
		if err != nil {
			return nil, err
		}
	}
	if len(headers) > 0 {
		for k, v := range headers {
			req.Header.Add(k, v)
		}
	}
	req.URL.Scheme = invoker2.HTTP
	report, ctx := monitoring.Transaction(ctx, fmt.Sprintf("%s.%s", monitoring.CatModuleAPI, req.URL.Host), req.URL.Path)
	req.Header.Add(apm.ROOT_MESSAGE_ID, report.GetRootMsgId(ctx))
	req.Header.Add(apm.PARENT_MESSAGE_ID, report.GetParentMsgId(ctx))
	req.Header.Add(apm.CHILD_MESSAGE_ID, report.GetChildMsgId(ctx))
	req.Header.Set(apm.ViewercontextKey, apm.ExtractViewercontextString(ctx))

	var reqBody string
	if data != nil {
		reqBody = string(data)
	} else {
		reqBody = objutil.JsonString(param)
	}

	//WithoutServiceDiscovery这个表示不走服务注册发现那一套
	rsp, err := invoker.Invoke(ctx, req, chassis.WithoutServiceDiscovery())
	if err != nil {
		logger.CtxLogErrorf(ctx, "http_request|request fail|url=%s,header=%s,request=%s,err=%v", req.URL, objutil.JsonString(req.Header), reqBody, err)
		report.Error(ctx, err)
		return nil, err
	}

	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		logger.CtxLogErrorf(ctx, "http_request|response status not ok|url=%s,header=%s,request=%s,err=%v", req.URL, objutil.JsonString(req.Header), reqBody, err)
		err := fmt.Errorf("http status code %v", rsp.StatusCode)
		report.Error(ctx, err)
		return nil, err
	}
	ret, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		logger.CtxLogErrorf(ctx, "http_request|get response body fail|url=%s,header=%s,request=%s,err=%v", req.URL, objutil.JsonString(req.Header), reqBody, err)
		report.Error(ctx, err)
		return nil, err
	}

	logger.CtxLogInfof(ctx, "http_request|url=%s,header=%s,request=%s,response=%s", req.URL, objutil.JsonString(req.Header), reqBody, string(ret))
	report.Success(ctx)

	return ret, nil
}

func getUrlWithoutSchema(reqUrl string) string {
	u, err := url.Parse(reqUrl)
	if err != nil {
		return ""
	}
	return u.Path
}

func RemoteMockByPost(ctx context.Context, url string, req []byte) ([]byte, error) {
	header := make(map[string]string, 0)
	valMap, ok := ctx.Value("ctxMock").(map[string]string)
	if ok {
		header["X-Request-ID"] = fmt.Sprintf("%v", valMap["X-Request-Id"])
	}

	return PostJson(ctx, url, req, 100, header)
}

func GetWithUrl(ctx context.Context, url string) (resp *http.Response, err error) {
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}
	return invoker.Invoke(ctx, req, chassis.WithoutServiceDiscovery())
}

func GetDefaultHttpInvoker() *chassis.RestInvoker {
	if invoker == nil {
		invoker = chassis.NewRestInvoker()
	}
	return invoker
}
