package jwtutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/dgrijalva/jwt-go"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"strings"
)

func BuildJWT(ctx context.Context, country string, user, operator, secret string) (string, error) {
	info := Info{
		Timestamp: recorder.Now(ctx).Unix(),
		Info: JwtData{
			User: JwtUser{
				Name:     user,
				Email:    user,
				Level:    1,
				Category: 0,
			},
			Entity: JwtEntity{
				Country: country,
				Name:    country,
				//TimeZone: meta.GetTimezoneFromCountry(country),
			},
		},
		//Exp: int64(jwtConfig.ExpiresIn) + recorder.Now(ctx).Unix(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, info)
	token.Header["optr"] = operator
	ss, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}
	return ss, nil
}

func ParseJWT(ctx context.Context, tokenVal, secret, operator string) (*Info, error) {
	token, err := jwt.ParseWithClaims(tokenVal, &Info{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "parse jwt token fail, token:%s, operator:%s, error:%+v", tokenVal, operator, err)
		return nil, err
	}
	if value, ok := token.Header["optr"]; !ok || value != operator {
		logger.CtxLogErrorf(ctx, "operator not found or mismatch, operator:%s", operator)
		return nil, fmt.Errorf("operator[%s] not found or mismatch", operator)
	}
	claims, ok := token.Claims.(*Info)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("wrong token format, claims:%s", objutil.JsonString(claims))
	}
	return claims, nil
}

func GetUnverifiedHeader(jwtContent string) (map[string]interface{}, error) {
	parts := strings.Split(jwtContent, ".")
	if len(parts) != 3 {
		return nil, errors.New("token contains an invalid number of segments")
	}
	headerBytes, err := jwt.DecodeSegment(parts[0])
	if err != nil {
		return nil, err
	}
	headers := make(map[string]interface{}, 3)
	err = jsoniter.Unmarshal(headerBytes, &headers)
	if err != nil {
		return nil, err
	}
	return headers, nil
}
