package jwtutil

type (
	Info struct {
		Info      JwtData `json:"info"`
		Exp       int64   `json:"exp"`
		Timestamp int64   `json:"timestamp"`
	}

	JwtData struct {
		User   JwtUser   `json:"user"`
		Entity JwtEntity `json:"entity"`
	}

	JwtUser struct {
		Name     string `json:"name"`
		Email    string `json:"email"`
		Level    int    `json:"level"`
		Category int    `json:"category"`
	}

	JwtEntity struct {
		Country  string `json:"country"`
		Name     string `json:"name"`
		TimeZone int    `json:"time_zone"`
	}
)

func (c Info) Valid() error {
	return nil
}
