package mathutil

import (
	"context"
	"errors"
	"github.com/agiledragon/gomonkey/v2"
	wr "github.com/mroth/weightedrand"
	"reflect"
	"testing"
)

func TestChoiceByWeightage(t *testing.T) {
	testCases := []struct {
		items map[string]uint
	}{
		{
			items: map[string]uint{},
		}, {
			items: map[string]uint{"1": 1},
		}, {
			items: map[string]uint{"1": 1, "2": 2},
		},
		{
			items: map[string]uint{"1": 1, "2": 2, "999999": 999999},
		},
	}
	for _, testCase := range testCases {
		t.Log(ChoiceByWeightage(context.TODO(), testCase.items))
	}
}

func TestUnionInt(t *testing.T) {
	type args struct {
		arrays [][]int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "error 1",
			args: args{
				arrays: [][]int{{1, 2, 3}, {3, 52, 2}, {1}},
			},
			want: []int{},
		},
		{
			name: "nil",
			args: args{
				arrays: nil,
			},
			want: []int{},
		},
		{
			name: "normal",
			args: args{
				arrays: [][]int{{1, 2, 3}, {3, 52, 2}, {2}},
			},
			want: []int{2},
		},
		{
			name: "error 2",
			args: args{
				arrays: [][]int{nil, {3, 52, 2}, {2}},
			},
			want: []int{},
		},
		{
			name: "error 3",
			args: args{
				arrays: [][]int{{1, 2, 3}, {5, 9}, {2}},
			},
			want: []int{},
		},
		{
			name: "error 4",
			args: args{
				arrays: [][]int{{1, 2, 3}, nil, {2}},
			},
			want: []int{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := UnionInt(tt.args.arrays...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UnionInt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChoiceByWeightage1(t *testing.T) {
	ctx := context.TODO()
	var patch *gomonkey.Patches
	type args struct {
		items map[string]uint
		count int
	}
	tests := []struct {
		name  string
		args  args
		want  string
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: len(items) == 0",
			want:  "",
			setup: func() {},
		},
		{
			name: "case 2: NewChooser error",
			args: args{
				items: map[string]uint{
					"1": 1,
					"2": 1<<(32<<(^uint(0)>>63)-1) - 1,
				},
			},
			want: "1",
			setup: func() {
				patch = gomonkey.ApplyFunc(wr.NewChooser, func(choices ...wr.Choice) (*wr.Chooser, error) {
					return nil, errors.New("mock NewChooser error")
				})
			},
		},
		{
			name: "case 3: Pick failed",
			args: args{
				items: map[string]uint{
					"1": 10,
					"2": 90,
				},
			},
			want: "1",
			setup: func() {
				patch = gomonkey.ApplyFunc(Pick, func(ctx context.Context, chooser *wr.Chooser) interface{} {
					return 1
				})
			},
		},
		{
			name: "case 4: normal result",
			args: args{
				items: map[string]uint{
					"1": 10,
					"2": 90,
				},
				count: 100000,
			},
			want:  "2", //
			setup: func() {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if tt.args.count != 0 {
				result := make(map[string]float64)
				for i := 0; i < tt.args.count; i++ {
					result[ChoiceByWeightage(ctx, tt.args.items)]++
				}
				t.Log("ChoiceByWeightage() Result statistics ratio= ", (result["2"] / result["1"]), " want ", (float64(tt.args.items["2"]) / float64(tt.args.items["1"])))
			} else if got := ChoiceByWeightage(ctx, tt.args.items); got != tt.want {
				t.Errorf("ChoiceByWeightage() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}

}
