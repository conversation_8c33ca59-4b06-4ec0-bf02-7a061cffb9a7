/**
 * @Date: 2020/11/21 16:18
 * @Description //TODO
 **/

package mathutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	wr "github.com/mroth/weightedrand"
	"math"
	"math/rand"
	"time"
)

// UnionInt 计算所有一维数组的交集
func UnionInt(arrays ...[]int) []int {
	if len(arrays) == 0 {
		return []int{}
	}
	if len(arrays) == 1 {
		return arrays[0]
	}

	arraysLen := len(arrays)

	var ret = arrays[0]
	if len(ret) == 0 {
		return []int{}
	}

	for i := 1; i < arraysLen; i++ {
		ret = unionInt(ret, arrays[i])
		if len(ret) == 0 {
			return []int{}
		}
	}
	return ret
}

func unionInt(nums1 []int, nums2 []int) []int {
	var tempMap = make(map[int]struct{})
	var ret = make([]int, 0)
	if nums1 == nil || nums2 == nil || len(nums1) == 0 || len(nums2) == 0 {
		return ret
	}
	for _, v := range nums1 {
		tempMap[v] = struct{}{}
	}
	for _, v := range nums2 {
		if _, ok := tempMap[v]; ok {
			ret = append(ret, v)
		}
	}
	return ret
}

func ChoiceByWeightage(ctx context.Context, items map[string]uint) string {
	if len(items) == 0 {
		return ""
	}

	var defaultChoice string
	rand.Seed(time.Now().UTC().UnixNano()) // nolint
	choices := make([]wr.Choice, 0, len(items))
	for item, weightage := range items {
		choices = append(choices, wr.NewChoice(item, weightage))
		if defaultChoice == "" {
			defaultChoice = item
		}
	}
	chooser, err := wr.NewChooser(choices...)
	if err != nil {
		return defaultChoice
	}
	//result, ok := chooser.Pick().(string)
	result, ok := Pick(ctx, chooser).(string)
	if !ok {
		return defaultChoice
	}

	return result
}

var Pick = recorder.Wrap(pick).(func(ctx context.Context, chooser *wr.Chooser) interface{})

func pick(ctx context.Context, chooser *wr.Chooser) interface{} {
	return chooser.Pick()
}

// DistinctInt64 实现去重
func DistinctInt64(nums []int64) []int64 {
	if len(nums) == 0 {
		return []int64{}
	}
	var tempMap = make(map[int64]struct{})
	for _, v := range nums {
		tempMap[v] = struct{}{}
	}
	var ret = make([]int64, len(tempMap))
	i := 0
	for k := range tempMap {
		ret[i] = k
		i++
	}
	return ret
}

func ConvertBoolToInt(flag bool) int {
	if flag {
		return 1
	}

	return 0
}

func SetIntSliceToMap(nums []int) map[int]struct{} {
	if nums == nil {
		return map[int]struct{}{}
	}

	var numMap = map[int]struct{}{}
	for _, num := range nums {
		numMap[num] = struct{}{}
	}
	return numMap
}

// 浮点数保留固定小数位数, 3.0/6会直接返回2
func FloatWithFixDecimals(value float64, n int) float64 {
	ratio := math.Pow(10, float64(n))      // 计算精度范围，2 位小数 = 100
	res := math.Round(value*ratio) / ratio // 保留 2 位小数
	return res
}
