package zip

import "github.com/klauspost/compress/zstd"

var (
	decoder *zstd.Decoder
	encoder *zstd.Encoder
)

func init() {
	decoder, _ = zstd.NewReader(nil)
	encoder, _ = zstd.NewWriter(nil)
}

// Decompress a buffer. We don't supply a destination buffer,
// so it will be allocated by the decoder.
func ZSTDDecompress(src []byte) ([]byte, error) {
	return decoder.DecodeAll(src, nil)
}

// Compress a buffer.
// If you have a destination buffer, the allocation in the call can also be eliminated.
func ZSTDCompress(src []byte) []byte {
	return encoder.EncodeAll(src, make([]byte, 0, len(src)))
}
