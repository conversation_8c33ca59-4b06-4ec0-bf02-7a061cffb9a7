package apiutil

import (
	"io/ioutil"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// FileUploadConfig 文件上传配置
type FileUploadConfig struct {
	MaxSizeMB     int64    // 最大文件大小(MB)
	AllowedExts   []string // 允许的文件扩展名
	FormFieldName string   // 表单字段名，默认为 "file"
}

// DefaultFileUploadConfig 默认文件上传配置
func DefaultFileUploadConfig() *FileUploadConfig {
	return &FileUploadConfig{
		MaxSizeMB:     10,
		AllowedExts:   []string{".csv", ".xlsx"},
		FormFieldName: "file",
	}
}

// ParseMultipartFile 解析multipart表单中的文件
func ParseMultipartFile(ctx *restful.Context, config *FileUploadConfig) ([]byte, *srerr.Error) {
	if config == nil {
		config = DefaultFileUploadConfig()
	}

	// 解析multipart表单
	if err := ctx.ReadRequest().ParseMultipartForm(32 * (1 << 20)); err != nil { // 32MB limit
		logger.CtxLogErrorf(ctx.Ctx, "Failed to parse multipart form: %v", err)
		return nil, srerr.New(srerr.ParamErr, nil, "failed to parse form data")
	}

	// 获取文件
	file, fileHeader, fErr := ctx.ReadRequest().FormFile(config.FormFieldName)
	if fErr != nil {
		logger.CtxLogErrorf(ctx.Ctx, "Failed to get file from form field '%s': %v", config.FormFieldName, fErr)
		return nil, srerr.New(srerr.ParamErr, nil, "file is required")
	}
	defer file.Close()

	// 检查文件大小限制
	maxSizeBytes := config.MaxSizeMB * (1 << 20)
	if fileHeader.Size > maxSizeBytes {
		logger.CtxLogErrorf(ctx.Ctx, "File size too large: %d bytes, limit: %d bytes", fileHeader.Size, maxSizeBytes)
		return nil, srerr.New(srerr.ParamErr, nil, "file size exceeds %dMB limit", config.MaxSizeMB)
	}

	// 检查文件类型
	filename := strings.ToLower(fileHeader.Filename)
	if len(config.AllowedExts) > 0 {
		allowed := false
		for _, ext := range config.AllowedExts {
			if strings.HasSuffix(filename, strings.ToLower(ext)) {
				allowed = true
				break
			}
		}
		if !allowed {
			logger.CtxLogErrorf(ctx.Ctx, "Invalid file type: %s, allowed: %v", fileHeader.Filename, config.AllowedExts)
			return nil, srerr.New(srerr.ParamErr, nil, "only %s files are supported", strings.Join(config.AllowedExts, ", "))
		}
	}

	// 读取文件内容
	data, readErr := ioutil.ReadAll(file)
	if readErr != nil {
		logger.CtxLogErrorf(ctx.Ctx, "Failed to read file data: %v", readErr)
		return nil, srerr.New(srerr.ParamErr, nil, "failed to read file")
	}

	logger.CtxLogInfof(ctx.Ctx, "Successfully parsed file: %s, size: %d bytes", fileHeader.Filename, len(data))
	return data, nil
}
