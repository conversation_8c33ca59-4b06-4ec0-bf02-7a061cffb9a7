package apiutil

import (
	"context"
	"fmt"
	"log"
	"mime"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gopkg.in/go-playground/validator.v9"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

const (
	MIMEJSON              = "application/json"
	MIMEPOSTForm          = "application/x-www-form-urlencoded"
	MIMEMultipartPOSTForm = "multipart/form-data"
	UssHostValidateTag    = "UssHostValidate"
)

var validate = validator.New()

func init() {
	if err := validate.RegisterValidation(UssHostValidateTag, ussHostValidate); err != nil {
		log.Fatalf("RegisterValidation err=%v", err)
	}
}

func SchemaParseValidate(ctx *restful.Context, schemaPtr interface{}) *srerr.Error {
	if ctx.Request().Request.Method == http.MethodGet {
		if err := readURLQuery(ctx.Ctx, ctx.Request().Request.URL.Query(), schemaPtr); err != nil {
			return srerr.With(srerr.FormatErr, nil, err)
		}
		if err := validate.Struct(schemaPtr); err != nil {
			return srerr.With(srerr.ParamErr, nil, arrangeValidateError(err))
		}
		return nil
	}
	if strings.Contains(ctx.Request().HeaderParameter("Content-Type"), "form") {
		if err := ctx.ReadQueryEntity(schemaPtr); err != nil {
			return srerr.With(srerr.FormatErr, nil, err)
		}
		//1）解析post请求body中form格式的参数，并且不会覆盖ReadQueryEntity（即在url中的参数
		//2）只有ReadQueryEntity读取不到的时候才会在此处赋值
		if err := readFormOfPostBody(ctx, schemaPtr); err != nil {
			return srerr.With(srerr.ParamErr, nil, err)
		}
		if err := validate.Struct(schemaPtr); err != nil {
			return srerr.With(srerr.ParamErr, nil, arrangeValidateError(err))
		}
		return nil
	}
	if err := ctx.ReadEntity(schemaPtr); err != nil {
		return srerr.With(srerr.FormatErr, nil, err)
	}
	if err := validate.Struct(schemaPtr); err != nil {
		return srerr.With(srerr.ParamErr, nil, arrangeValidateError(err))
	}
	return nil

}

func readURLQuery(ctx context.Context, queries map[string][]string, schemaPtr interface{}) error {
	eType := reflect.TypeOf(schemaPtr).Elem()
	eVal := reflect.ValueOf(schemaPtr).Elem()
	for i := 0; i < eVal.NumField(); i++ {
		fVal := eVal.Field(i)
		if !fVal.CanSet() {
			continue
		}
		if fVal.Kind() == reflect.Struct {
			for j := 0; j < fVal.NumField(); j++ {
				gVal := fVal.Field(j)
				if !gVal.CanSet() {
					continue
				}
				gType := fVal.Type()
				tag := gType.Field(i).Tag.Get("json")
				input, ok := queries[tag]
				if !ok {
					continue
				}
				if err := convert(input, gVal); err != nil {
					logger.CtxLogErrorf(ctx, "parse url query error, tag:%s, error:%+v", tag, err)
					return err
				}
			}
		} else {
			tagSlice := strings.Split(eType.Field(i).Tag.Get("json"), ",")
			tag := tagSlice[0]
			input, ok := queries[tag]
			if !ok {
				continue
			}
			if err := convert(input, fVal); err != nil {
				logger.CtxLogErrorf(ctx, "parse url query error, tag:%s, error:%+v", tag, err)
				return err
			}
		}
	}

	return nil
}

func arrangeValidateError(err error) error {
	validateErrs, ok := err.(validator.ValidationErrors)
	if !ok {
		return err
	}
	var fieldErrTips []string
	for _, fieldErr := range validateErrs {
		ns := fieldErr.Namespace()
		ns = ns[strings.LastIndexByte(ns, '.')+1:]
		fieldErrTips = append(fieldErrTips, fmt.Sprintf("param:<%s> is limited [%s:%+v], but actual value is %+v",
			ns,
			fieldErr.Tag(),
			fieldErr.Param(),
			fieldErr.Value()),
		)
	}
	return errors.New(strings.Join(fieldErrTips, ";"))
}

func convert(sList []string, targetValue reflect.Value) error {
	if sList == nil || len(sList) == 0 {
		return nil
	}

	sLen := len(sList)

	var err error
	var iValue interface{}
	// 数值类型默认为10进制
	switch kind := targetValue.Kind(); kind {
	case reflect.Uint, reflect.Uint64, reflect.Uint32, reflect.Uint16, reflect.Uint8,
		reflect.Int, reflect.Int64, reflect.Int32, reflect.Int16, reflect.Int8,
		reflect.Float32, reflect.Float64, reflect.Bool, reflect.String:
		// use uint64 exchange uint
		iValue, err = ConvertBaseType(targetValue.Type(), sList[sLen-1])
		if err != nil {
			break
		}
		targetValue.Set(reflect.ValueOf(iValue).Convert(targetValue.Type()))

	case reflect.Slice:
		tempValue := reflect.MakeSlice(targetValue.Type(), sLen, sLen)
		for i, s := range sList {
			v := tempValue.Index(i)
			iValue, err = ConvertBaseType(tempValue.Type().Elem(), s)
			if err != nil {
				break
			}
			v.Set(reflect.ValueOf(iValue).Convert(tempValue.Type().Elem()))
		}
		targetValue.Set(tempValue)

	case reflect.Ptr:
		if targetValue.IsNil() {
			targetValue.Set(reflect.New(targetValue.Type().Elem()))
		}
		return convert(sList, targetValue.Elem())

	// other type will not support now, if you want to parse, please add you method
	default:
		err = fmt.Errorf("could not support convert string to this type %s", targetValue.Type().Name())
	}

	if err != nil {
		return err
	}

	return nil
}

func ConvertBaseType(p reflect.Type, v interface{}) (interface{}, error) {
	if p.Kind() == reflect.Ptr {
		p = p.Elem()
	}
	switch p.Kind() {
	case reflect.Bool:
		return cast.ToBoolE(v)
	case reflect.String:
		s, err := cast.ToStringE(v)
		if err != nil {
			return nil, err
		}
		return s, nil
	case reflect.Int:
		return cast.ToIntE(v)
	case reflect.Int8:
		return cast.ToInt8E(v)
	case reflect.Int16:
		return cast.ToInt16E(v)
	case reflect.Int32:
		return cast.ToInt32E(v)
	case reflect.Int64:
		return cast.ToInt64E(v)
	case reflect.Uint:
		return cast.ToUintE(v)
	case reflect.Uint8:
		return cast.ToUint8E(v)
	case reflect.Uint16:
		return cast.ToUint16E(v)
	case reflect.Uint32:
		return cast.ToUint32E(v)
	case reflect.Uint64:
		return cast.ToUint64E(v)
	case reflect.Float32:
		return cast.ToFloat32E(v)
	case reflect.Float64:
		return cast.ToFloat64E(v)
	default:
		return v, nil
	}
}

func readFormOfPostBody(bs *restful.Context, schema interface{}) error {
	if getContentType(bs) != MIMEPOSTForm && getContentType(bs) != MIMEMultipartPOSTForm {
		return nil
	}

	//解析post请求body中的form参数
	req := bs.ReadRequest()
	_ = req.ParseMultipartForm(16 * (1 << 20))
	return mapForm(schema, req.Form, "form")
}

func getContentType(bs *restful.Context) string {
	req := bs.ReadRequest()
	ct := req.Header.Get("Content-Type")
	if ct == "" {
		ct = MIMEJSON
	}
	mediaType, _, err := mime.ParseMediaType(ct)
	if err != nil {
		return MIMEJSON
	}
	return mediaType
}

func mapForm(ptr interface{}, form map[string][]string, tag string) error {
	rt, rv := reflect.TypeOf(ptr), reflect.ValueOf(ptr)
	if rt.Kind() != reflect.Ptr || rt.Elem().Kind() != reflect.Struct {
		return nil
	}

	rt, rv = rt.Elem(), rv.Elem()
	for i := 0; i < rt.NumField(); i++ {
		typeField := rt.Field(i)
		structField := rv.Field(i)
		if !structField.CanSet() {
			continue
		}

		structFieldKind := structField.Kind()
		inputFieldName := typeField.Tag.Get(tag)
		if inputFieldName == "" {
			inputFieldName = typeField.Name

			// if "form" tag is nil, we inspect if the field is a struct.
			// this would not make sense for JSON parsing but it does for a form
			// since data is flatten
			if structFieldKind == reflect.Struct {
				err := mapForm(structField.Addr().Interface(), form, tag)
				if err != nil {
					return err
				}
				continue
			}
		}
		inputValue, exists := form[inputFieldName]
		if !exists {
			continue
		}

		numElems := len(inputValue)
		if structFieldKind == reflect.Slice && numElems > 0 {
			sliceOf := structField.Type().Elem()
			slice := reflect.MakeSlice(structField.Type(), numElems, numElems)
			for i := 0; i < numElems; i++ {
				if err := preSetWithProperType(sliceOf, inputValue[i], slice.Index(i)); err != nil {
					return err
				}
			}
			rv.Field(i).Set(slice)
		} else {
			if _, isTime := structField.Interface().(time.Time); isTime {
				if err := setTimeField(inputValue[0], typeField, structField); err != nil {
					return err
				}
				continue
			}
			if err := preSetWithProperType(typeField.Type, inputValue[0], structField); err != nil {
				return err
			}
		}
	}
	return nil
}

func preSetWithProperType(rt reflect.Type, inputValue string, structField reflect.Value) error {
	if rt.Kind() == reflect.Ptr {
		newVal := reflect.New(rt.Elem())
		if err := setWithProperType(rt, inputValue, newVal); err != nil {
			return err
		}
		structField.Set(newVal)
	} else {
		if err := setWithProperType(rt, inputValue, structField); err != nil {
			return err
		}
	}
	return nil
}

func setWithProperType(valueKind reflect.Type, val string, structField reflect.Value) error {
	switch valueKind.Kind() {
	case reflect.Int:
		return setIntField(val, 0, structField)
	case reflect.Int8:
		return setIntField(val, 8, structField)
	case reflect.Int16:
		return setIntField(val, 16, structField)
	case reflect.Int32:
		return setIntField(val, 32, structField)
	case reflect.Int64:
		return setIntField(val, 64, structField)
	case reflect.Uint:
		return setUintField(val, 0, structField)
	case reflect.Uint8:
		return setUintField(val, 8, structField)
	case reflect.Uint16:
		return setUintField(val, 16, structField)
	case reflect.Uint32:
		return setUintField(val, 32, structField)
	case reflect.Uint64:
		return setUintField(val, 64, structField)
	case reflect.Bool:
		return setBoolField(val, structField)
	case reflect.Float32:
		return setFloatField(val, 32, structField)
	case reflect.Float64:
		return setFloatField(val, 64, structField)
	case reflect.String:
		structField.SetString(val)
	case reflect.Ptr:
		return setWithProperType(valueKind.Elem(), val, structField.Elem())

	default:
		return errors.New("Unknown type")
	}
	return nil
}

func setIntField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	intVal, err := strconv.ParseInt(val, 10, bitSize)
	if err == nil {
		field.SetInt(intVal)
	}
	return err
}

func setUintField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	uintVal, err := strconv.ParseUint(val, 10, bitSize)
	if err == nil {
		field.SetUint(uintVal)
	}
	return err
}

func setBoolField(val string, field reflect.Value) error {
	if val == "" {
		val = "false"
	}
	boolVal, err := strconv.ParseBool(val)
	if err == nil {
		field.SetBool(boolVal)
	}
	return nil
}

func setFloatField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0.0"
	}
	floatVal, err := strconv.ParseFloat(val, bitSize)
	if err == nil {
		field.SetFloat(floatVal)
	}
	return err
}

func setTimeField(val string, structField reflect.StructField, value reflect.Value) error {
	timeFormat := structField.Tag.Get("time_format")
	if timeFormat == "" {
		return errors.New("blank time format")
	}

	if val == "" {
		value.Set(reflect.ValueOf(time.Time{}))
		return nil
	}

	l := time.Local
	if isUTC, _ := strconv.ParseBool(structField.Tag.Get("time_utc")); isUTC {
		l = time.UTC
	}

	if locTag := structField.Tag.Get("time_location"); locTag != "" {
		loc, err := time.LoadLocation(locTag)
		if err != nil {
			return err
		}
		l = loc
	}

	t, err := time.ParseInLocation(timeFormat, val, l)
	if err != nil {
		return err
	}

	value.Set(reflect.ValueOf(t))
	return nil
}

func ussHostValidate(fl validator.FieldLevel) bool {
	field := fl.Field()

	if field.Kind() != reflect.String {
		return true
	}

	hosts := configutil.GetUssWhiteHost()
	if len(hosts) == 0 { // 如果白名单为空就不校验，兜底
		return true
	}
	// 校验前端传来的uss链接是否包含白名单
	for _, host := range hosts {
		if strings.Contains(field.String(), host) {
			return true
		}
	}

	return false
}
