package apiutil

import (
	"context"
	"github.com/stretchr/testify/assert"
	"testing"
)

type TCommonSchema struct {
	StringField string `json:"string_field"`
	IntField    int    `json:"int_field"`
	U<PERSON><PERSON>ield   uint   `json:"uint_field"`
	<PERSON><PERSON><PERSON><PERSON>   bool   `json:"bool_field"`
}

type TPointSchema struct {
	StringPointField *string `json:"string_point_field"`
	IntPointField    *int    `json:"int_point_field"`
}

type TEmbedSchema struct {
	QueryId string `json:"query_id"`
	TPage
}

type TPage struct {
	Offset int64 `json:"offset"`
	Size   int64 `json:"size"`
}

func (p *TPointSchema) getStringField() string {
	if p.StringPointField == nil {
		return ""
	}
	return *p.StringPointField
}

func (p *TPointSchema) getIntField() int {
	if p.IntPointField == nil {
		return 0
	}
	return *p.IntPointField
}

func TestReadURLQuery(t *testing.T) {
	tests := []struct {
		k       string
		schema  interface{}
		queries map[string][]string
	}{
		{
			k:       "common",
			schema:  new(TCommonSchema),
			queries: map[string][]string{"string_field": {"test_hello"}, "int_field": {"-1"}, "uint_field": {"1"}, "bool_field": {"true"}},
		},
		{
			k:       "point",
			schema:  new(TPointSchema),
			queries: map[string][]string{"string_point_field": {"test_hello"}, "int_point_field": {"-1"}},
		},
		{
			k:       "embed",
			schema:  new(TEmbedSchema),
			queries: map[string][]string{"query_id": {"test"}, "offset": {"10"}, "size": {"10"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			err := readURLQuery(context.TODO(), tt.queries, tt.schema)
			switch tt.k {
			case "common":
				assert.Nil(t, err)
				assert.Equal(t, "test_hello", tt.schema.(*TCommonSchema).StringField)
				assert.EqualValues(t, -1, tt.schema.(*TCommonSchema).IntField)
				assert.EqualValues(t, 1, tt.schema.(*TCommonSchema).UintField)
				assert.Equal(t, true, tt.schema.(*TCommonSchema).BoolField)
			case "point":
				assert.Nil(t, err)
				assert.EqualValues(t, -1, tt.schema.(*TPointSchema).getIntField())
				assert.Equal(t, "test_hello", tt.schema.(*TPointSchema).getStringField())
			case "embed":
				assert.Nil(t, err)
				assert.Equal(t, "test", tt.schema.(*TEmbedSchema).QueryId)
				assert.EqualValues(t, 10, tt.schema.(*TEmbedSchema).Offset)
				assert.EqualValues(t, 10, tt.schema.(*TEmbedSchema).Size)
			}

		})
	}
}
