package apiutil

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"strings"
)

type HttpResponse struct {
	RetCode int         `json:"retcode"`
	Message string      `json:"message"`
	Detail  string      `json:"detail"`
	Data    interface{} `json:"data,omitempty"`
}

type HttpError interface {
	GetCode() int
	GetMessage() string
}

func SuccessJSONResp(ctx *restful.Context, data interface{}) {
	writeJSONResp(ctx, data, nil, "")
}

func FailJSONResp(ctx *restful.Context, err HttpError, detail string) {
	writeJSONResp(ctx, nil, err, detail)
}

func FailJSONRespWithData(ctx *restful.Context, err HttpError, data interface{}, detail string) {
	writeJSONResp(ctx, data, err, detail)
}

func CustomJSONResp(ctx *restful.Context, body interface{}) {
	_ = ctx.WriteJSON(body, constant.ApplicationJSON)
}

func writeJSONResp(ctx *restful.Context, data interface{}, err HttpError, detail string) {
	resp := HttpResponse{
		RetCode: 0,
		Message: "success",
		Data:    data,
		Detail:  detail,
	}
	if err != nil && err.GetCode() != 0 {
		resp.RetCode = err.GetCode()
		resp.Message = err.GetMessage()
	}
	_ = ctx.WriteJSON(resp, constant.ApplicationJSON)
}

func WriteExcelResp(ctx *restful.Context, filename string, data []byte) {
	if !strings.HasSuffix(filename, ".xlsx") {
		filename = filename + ".xlsx"
	}
	writeFileResp(ctx, filename, data)
}

func writeFileResp(ctx *restful.Context, filename string, data []byte) {
	ctx.AddHeader("Content-Type", "application/octet-stream")
	ctx.AddHeader("Content-Disposition", "attachment; filename="+filename)
	ctx.AddHeader("Content-Transfer-Encoding", "binary")
	ctx.Write(data)
}
