package lpsclient

import (
	jsoniter "github.com/json-iterator/go"
)

type AddOperationLogReq struct {
	RecordKey  string `form:"record_key" json:"record_key"`
	ChangeData string `form:"change_data" json:"change_data"`
	Operation  string `form:"operation" json:"operation"`
	Operator   string `form:"operator" json:"operator"`
	Message    string `form:"message" json:"message"`
	ChangeId   uint64 `form:"change_id" json:"change_id"`
}

type AddOperationLogResp struct {
	Retcode int    `json:"retcode"`
	Message string `json:"message"`
}

func (p *AddOperationLogReq) Queries() map[string]string {
	body := make(map[string]string)
	if p.RecordKey != "" {
		body["record_key"] = p.RecordKey
	}
	if p.ChangeData != "" {
		body["change_data"] = p.ChangeData
	}
	if p.Operation != "" {
		body["operation"] = p.Operation
	}
	if p.Operator != "" {
		body["operator"] = p.Operator
	}
	if p.Message != "" {
		body["message"] = p.Message
	}

	return body
}

func (p *AddOperationLogReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *AddOperationLogResp) IsSuccess() bool {
	return p.Retcode == 0
}

func (p *AddOperationLogResp) FailMsg() string {
	return "lps get shop group list by tag fail"
}
