package lpsclient

import (
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type (
	ProductDetailRequest struct {
		ProductId    int  `form:"product_id" json:"product_id"`
		NeedShipment bool `form:"need_shipment" json:"need_shipment"`
		NeedSettle   bool `form:"need_settle" json:"need_settle"`
		NeedCod      bool `form:"need_cod" json:"need_cod"`
		NeedMeasure  bool `form:"need_measure" json:"need_measure"`
		NeedExtra    bool `form:"need_extra" json:"need_extra"`
		NeedScenario bool `form:"need_scenario" json:"need_scenario"`
		NeedSmrUnion bool `form:"need_smr_union" json:"need_smr_union"`
	}

	ProductDetailInfo struct {
		ProductId         int64               `json:"product_id"`
		Entity            int                 `json:"entity"`
		LaneCodes         []string            `json:"lane_codes"`
		MultiLaneCodes    []string            `json:"multi_lane_codes"`
		IlhLaneCodes      []string            `json:"ilh_lane_codes"`
		SellerDisplayName string              `json:"seller_display_name"`
		BuyerDisplayName  string              `json:"buyer_display_name"`
		IsMaskingProduct  bool                `json:"is_masking_product"`
		IsMultiProduct    bool                `json:"is_multi_product"`
		BusinessBlacklist []BusinessBlackItem `json:"business_blacklist"`
		ComponentProduct  *ProductComponent   `json:"component_product"`
		ProductFlag       uint64              `json:"product_flag"`
		ShippingMethod    int                 `json:"shipping_method"`
	}
	BusinessBlackItem struct {
		LineId   string   `json:"line_id"`
		LineList []string `json:"line_list"`
	}
	ProductComponent struct {
		MaskingType       int   `json:"masking_type"`
		ComponentProducts []int `json:"component_products"`
	}
	ShopGroup struct {
		GroupId   int64  `json:"id"`
		GroupName string `json:"name"`
	}
)

func (p *ProductDetailRequest) Queries() map[string]string {
	body := make(map[string]string)

	if p.ProductId != 0 {
		body["product_id"] = strconv.Itoa(p.ProductId)
	}

	trueStr := "true"
	if p.NeedShipment {
		body["need_shipment"] = trueStr
	}
	if p.NeedSettle {
		body["need_settle"] = trueStr
	}
	if p.NeedCod {
		body["need_cod"] = trueStr
	}
	if p.NeedMeasure {
		body["need_measure"] = trueStr
	}
	if p.NeedExtra {
		body["need_extra"] = trueStr
	}
	if p.NeedScenario {
		body["need_scenario"] = trueStr
	}
	if p.NeedSmrUnion {
		body["need_smr_union"] = trueStr
	}

	return body
}

func (p *ProductDetailRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *ProductDetailInfo) GetComponentProduct() *ProductComponent {
	if p.ComponentProduct != nil {
		return p.ComponentProduct
	}
	return &ProductComponent{}
}
