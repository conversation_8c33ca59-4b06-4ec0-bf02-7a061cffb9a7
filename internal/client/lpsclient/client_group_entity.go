package lpsclient

import (
	jsoniter "github.com/json-iterator/go"
)

type (
	GetClientGroupReq struct {
		ClientTagId int64   `form:"client_tag_id" json:"client_tag_id"`
		ShopGroupId []int64 `form:"shop_group_id" json:"shop_group_id"`
	}

	GetClientGroupInfo struct {
		Id              uint64 `json:"id"`                // primary key
		ClientGroupName string `json:"client_group_name"` // group name
		ClientTagID     uint64 `json:"client_tag_id"`     // tag id
		ClientGroupID   string `json:"client_group_id"`   // group id
		Ctime           uint   `json:"ctime"`             // create time
		Mtime           uint   `json:"mtime"`             // modify time
	}
)

func (p *GetClientGroupReq) Queries() map[string]string {
	body := make(map[string]string)
	return body
}

func (p *GetClientGroupReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type (
	GetProductRelateShopGroupReq struct {
		ClientTagId int64 `form:"client_tag_id" json:"client_tag_id"`
		ProductId   int64 `form:"product_id" json:"product_id"`
	}

	ProductRelateShopGroup struct {
		Id            int64  `json:"id"`
		ClientTagId   int64  `json:"client_tag_id"`
		ClientGroupId string `json:"client_group_id"`
		ProductId     int64  `json:"product_id"`
	}
)

func (p *GetProductRelateShopGroupReq) Queries() map[string]string {
	body := make(map[string]string)
	return body
}

func (p *GetProductRelateShopGroupReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}
