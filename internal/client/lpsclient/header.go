package lpsclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

type lpsHeader struct {
	requestId string
}

func LpsHeader(ctx context.Context) *lpsHeader {
	return &lpsHeader{requestId: requestid.GetFromCtx(ctx)}
}

func (p *lpsHeader) Header(ctx context.Context) (map[string]string, error) {
	conf := configutil.GetLpsAdminConf(ctx)
	jwtToken, err := jwtutil.BuildJWT(ctx, envvar.GetCID(), client.UserName, conf.JwtOptr, conf.JwtSecret)
	if err != nil {
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	return map[string]string{"jwt-token": jwtToken, "X-Request-Id": p.requestId}, nil
}

func (p *lpsHeader) SecondTimeout(ctx context.Context) int {
	lpsCnf := configutil.GetLpsApiConf(ctx)
	return int(lpsCnf.Timeout)
}
