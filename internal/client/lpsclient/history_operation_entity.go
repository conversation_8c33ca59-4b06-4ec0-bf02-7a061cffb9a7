package lpsclient

import jsoniter "github.com/json-iterator/go"

type (
	AddOperationLogRequest struct {
		RecordKey  string `json:"record_key"`
		ChangeData string `json:"change_data"`
		Operation  string `json:"operation"`
		Operator   string `json:"operator"`
		Message    string `json:"message"`
	}
)

func (a *AddOperationLogRequest) Queries() map[string]string {
	body := make(map[string]string)
	return body
}

func (a *AddOperationLogRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(a)
}
