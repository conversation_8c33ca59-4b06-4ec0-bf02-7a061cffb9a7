package lpsclient

import (
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/meta"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type (
	ProductDictResponse struct {
		RetCode int                `json:"retcode"`
		Message string             `json:"message"`
		Data    *ProductDictResult `json:"data,omitempty"`
	}
	ProductDictResult struct {
		DictList []ProductDict `json:"dict_list"`
	}

	ProductDict struct {
		ProductId   int64  `json:"product_id"`
		ProductName string `json:"product_name"`
	}

	GetProductDetailReq struct {
		ProductId int `json:"product_id"`
	}

	GetProductDetailResp struct {
		RetCode int                `json:"retcode"`
		Message string             `json:"message"`
		Data    *ProductDetailInfo `json:"data,omitempty"`
	}

	GetClientGroupResp struct {
		RetCode int                   `json:"retcode"`
		Message string                `json:"message"`
		Data    []*GetClientGroupInfo `json:"data,omitempty"`
	}

	AddHistoryOperationResp struct {
		RetCode int    `json:"retcode"`
		Message string `json:"message"`
	}

	GetProductRelateShopGroupResp struct {
		RetCode int                       `json:"retcode"`
		Message string                    `json:"message"`
		Data    []*ProductRelateShopGroup `json:"data,omitempty"`
	}
)

type GetLineListRequest struct {
	ProductId int64 `json:"product_id"`
}

type (
	LineListResponse struct {
		RetCode int             `json:"retcode"`
		Message string          `json:"message"`
		Data    *LineListResult `json:"data,omitempty"`
	}

	LineListResult struct {
		RoleMap      map[int]int `json:"role_map"`
		LineDictList []LineDict  `json:"line_dict_list"`
	}

	LineDict struct {
		LineId   string `json:"line_id"`
		LineType int    `json:"line_type"`
		LineName string `json:"line_name"`
	}
)

type (
	GetLaneCodesRequest struct {
		Token string `json:"token"`
	}

	GetLaneCodesResponse struct {
		RetCode int             `json:"retcode"`
		Message string          `json:"message"`
		Data    []LaneCodesInfo `json:"data,omitempty"`
	}

	LaneCodesInfo struct {
		ProductId   int      `json:"product_id"`
		ProductName string   `json:"product_name"`
		LaneCodes   []string `json:"lane_codes"`
	}
)

type (
	GetProductListResp struct {
		RetCode int           `json:"retcode"`
		Message string        `json:"message"`
		Data    []ProductInfo `json:"data,omitempty"`
	}

	ProductInfo struct {
		ProductId        uint64 `json:"product_id"`
		ProductName      string `json:"product_name"`
		IsMaskingProduct bool   `json:"is_masking_product"`
	}
)

type (
	BaseResponse struct {
		RetCode int    `json:"retcode"`
		Message string `json:"message"`
	}

	GetRuleVolumeDetail struct {
		BaseResponse
		Data *RuleVolumeTab `json:"data"`
	}

	RuleVolumeTab struct {
		Id            uint64        `gorm:"column:id" json:"id"`
		MaskProductId int           `gorm:"column:mask_product_id" json:"mask_product_id"`
		RuleType      LocVolumeType `gorm:"column:rule_type" json:"rule_type"`
	}
)

type (
	GetProductBaseInfoReq struct {
		IsNonIntegratedProduct int `json:"is_non_integrated_product" validate:"min=0,max=2"`
		// SPLN-17059 增加cb类型，集成非集成，是否为mask product的检索条件
		CBType         *uint8 `json:"cb_type"`
		IntegratedType *uint8 `json:"integrated_type"` // 目前没有非集成的prod，暂时不做检索
		MaskingType    *uint8 `json:"masking_type"`
		ProductIds     []int  `json:"product_ids"`
		Priorities     []int  `json:"priorities"`
		//SPLPS-7634: add param to filter open products
		IsOpenProduct *bool `json:"is_open_product"` //true: will get open product; false:will filter open product
	}
	GetProductBaseInfoResp struct {
		Retcode int                   `json:"retcode"`
		Message string                `json:"message"`
		Data    []*LogisticProductTab `json:"data"`
	}
	LogisticProductTab struct {
		Id                     uint64  `gorm:"column:id" json:"id"`
		ProductId              int     `gorm:"column:product_id" json:"product_id"`
		BuyerDisplayName       string  `gorm:"column:buyer_display_name" json:"buyer_display_name"`
		SellerDisplayName      string  `gorm:"column:seller_display_name" json:"seller_display_name"`
		IsMultiProduct         int     `gorm:"column:is_multi_product" json:"is_multi_product"`
		MultiProductModel      int     `gorm:"column:multi_product_model" json:"multi_product_model"`
		Entity                 int     `gorm:"column:entity" json:"entity"`
		ProductLaneType        int     `gorm:"column:product_lane_type" json:"product_lane_type"`
		ProductFlowType        int     `gorm:"column:product_flow_type" json:"product_flow_type"`
		FromRegion             string  `gorm:"column:from_region" json:"from_region"`
		ToRegion               string  `gorm:"column:to_region" json:"to_region"`
		ServiceType            *int    `gorm:"column:service_type" json:"service_type"`
		Cashless               bool    `gorm:"column:cashless" json:"cashless"`
		WhiteBlacklist         *int    `gorm:"column:white_blacklist" json:"white_blacklist"`
		DefaultShopToggle      bool    `gorm:"column:default_shop_toggle" json:"default_shop_toggle"`
		ShopLevelExclusive     *string `gorm:"column:shop_level_exclusive" json:"shop_level_exclusive"`
		ItemLevelExclusive     *string `gorm:"column:item_level_exclusive" json:"item_level_exclusive"`
		IsMaskingProduct       bool    `gorm:"column:is_masking_product" json:"is_masking_product"`
		MaskingType            int     `gorm:"column:masking_type" json:"masking_type"`
		Ctime                  int     `gorm:"column:ctime" json:"ctime"`
		Mtime                  int     `gorm:"column:mtime" json:"mtime"`
		Deleted                int     `gorm:"column:deleted" json:"deleted"`
		MaintenanceMode        bool    `gorm:"column:maintenance_mode" json:"maintenance_mode"`
		Priority               int32   `gorm:"column:priority" json:"priority"`
		OrderSource            int     `gorm:"column:order_source" json:"order_source"`
		CanSplit               bool    `gorm:"column:can_split" json:"can_split"`
		PrePrint               bool    `gorm:"column:pre_print" json:"pre_print"`
		SupportSip             bool    `gorm:"column:support_sip" json:"support_sip"`
		IsNonIntegratedProduct bool    `gorm:"column:is_non_integrated_product" json:"is_non_integrated_product"`
		KycControl             *bool   `gorm:"column:kyc_control" json:"kyc_control,omitempty"`
		Category               int     `gorm:"column:category" json:"category"`

		// NeedIntegrationSlug 仅集成渠道配置，如果配置这个，需要同步配置每条lane对应的 LogisticProductLaneRefTab.IntegrationSlugId 与
		// LogisticProductLaneRefTab.IntegrationSlugName，前端对应在产品编辑时lane编辑信息里面
		// 下单时 seller 会传入slug (在forder info 的 slug 字段), LPS 根据 slug 获取到对应的 LaneCode 向 LFS 下单
		NeedIntegrationSlug        bool   `gorm:"column:need_integration_slug" json:"need_integration_slug"`
		NonIntegratedUseCase       int    `gorm:"column:non_integrated_use_case" json:"non_integrated_use_case"`               //Enum of non-integrated product's use case
		NonIntegratedUseCaseDetail string `gorm:"column:non_integrated_use_case_detail" json:"non_integrated_use_case_detail"` //Explanation of non-integrated product's use case
		DaysToDeliver              int    `gorm:"column:days_to_deliver" json:"days_to_deliver"`                               //The day of delivery date, from 1 to 31, default by 0 in db
		LogisticsCapability        int    `gorm:"column:logistics_capability" json:"logistics_capability"`
	}

	GetProductVasInfoListReq struct {
		ProductType int32 `json:"product_type"`
		VasEntity   int32 `json:"entity"`
	}

	GetProductVasInfoListResp struct {
		RetCode int                        `json:"retcode"`
		Message string                     `json:"message"`
		Data    *GetProductVasInfoListData `json:"data,omitempty"`
	}

	GetProductVasInfoListData struct {
		VasProducts []*ProductVasInfo `json:"vas_products"`
		Detail      string            `json:"detail"`
	}

	ProductVasInfo struct {
		ProductId         int64  `json:"product_id"`
		ProductType       int32  `json:"product_type"`
		Entity            int32  `json:"entity"`
		ProductFlowType   int32  `json:"product_flow_type"`
		BuyerDisplayName  string `json:"buyer_display_name"`
		SellerDisplayName string `json:"seller_display_name"`
		FromRegion        string `json:"from_region"`
		ToRegion          string `json:"to_region"`
		WhiteBlackList    int32  `json:"white_black_list"`
		IsMasking         bool   `json:"is_masking"`
		MaskingType       int32  `json:"masking_type"`
	}
)

func (g *GetProductVasInfoListReq) Queries() map[string]string {
	return nil
}

func (g *GetProductVasInfoListReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetProductVasInfoListResp) IsSuccess() bool {
	return g.RetCode == 0
}

func (g *GetProductVasInfoListResp) FailMsg() string {
	return g.Message
}

func (g *GetProductVasInfoListResp) GetProductVasInfoList() []*ProductVasInfo {
	if g == nil || g.Data == nil {
		return nil
	}
	return g.Data.VasProducts
}

func (p *GetProductDetailResp) IsSuccess() bool {
	return p.RetCode == 0 && p.Data != nil
}

func (p *GetProductDetailResp) FailMsg() string {
	return "lps get product detail fail"
}

func (p *GetClientGroupResp) IsSuccess() bool {
	return p.RetCode == 0 && p.Data != nil
}

func (p *GetClientGroupResp) FailMsg() string {
	return "lps get client group fail"
}

func (p *AddHistoryOperationResp) IsSuccess() bool {
	return p.RetCode == 0
}

func (p *AddHistoryOperationResp) FailMsg() string {
	return "lps add history operation detail fail"
}

func (p *GetProductRelateShopGroupResp) IsSuccess() bool {
	return p.RetCode == 0
}

func (p *GetProductRelateShopGroupResp) FailMsg() string {
	return "lps get product relate shop group fail"
}

type ClientGroupTab struct {
	ID              uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT"` // primary key
	ClientGroupName string `gorm:"column:client_group_name;NOT NULL"`    // group name
	ClientTagID     uint64 `gorm:"column:client_tag_id;NOT NULL"`        // tag id
	ClientGroupID   string `gorm:"column:client_group_id;NOT NULL"`      // group id
	Ctime           uint   `gorm:"column:ctime;NOT NULL"`                // create time
	Mtime           uint   `gorm:"column:mtime;NOT NULL"`                // modify time
}

type GetClientGroupsByTagReq struct {
	ClientTagId uint64 `json:"client_tag_id"`
}

type GetClientGroupsByTagResp struct {
	RetCode int               `json:"retcode"`
	Message string            `json:"message"`
	Data    []*ClientGroupTab `json:"data,omitempty"`
}

func (p *GetClientGroupsByTagResp) IsSuccess() bool {
	return p.RetCode == 0 && p.Data != nil
}

func (p *GetClientGroupsByTagResp) FailMsg() string {
	return "lps get client groups by tag fail"
}

type HardCheckRequest struct {
	ProductId      int                           `json:"product_id"`
	IsMultiProduct bool                          `json:"is_multi_product"`
	OrderData      *lfspb.CreateOrderRequestData `json:"order_data"`
}

func (h *HardCheckRequest) Queries() map[string]string {
	return nil
}

func (h *HardCheckRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(h)
}

type HardCheckResponse struct {
	RetCode int                    `json:"retcode"`
	Message string                 `json:"message"`
	Data    *HardCheckResponseData `json:"data"`
}

func (h *HardCheckResponse) IsSuccess() bool {
	return h.RetCode == 0 && h.Data != nil
}

func (h *HardCheckResponse) FailMsg() string {
	return h.Message
}

type HardCheckResponseData struct {
	LaneList         []*rule.RoutingLaneInfo `json:"lane_list"`
	ValidationWeight int                     `json:"validation_weight"`
}

type ILHHardCheckRequest struct {
	ProductId     int      `json:"product_id"`
	TwsCode       string   `json:"tws_code"`
	DgChange      int      `json:"dg_change"`
	DgGroupId     string   `json:"dg_group_id"`
	LmId          string   `json:"lm_id"`
	DgFlag        int32    `json:"dg_flag"`
	ActualPointId string   `json:"actual_point_id"`
	SloList       []string `json:"slo_list"`
}

func (I *ILHHardCheckRequest) Queries() map[string]string {
	return nil
}

func (I *ILHHardCheckRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(I)
}

type ILHHardCheckResponse struct {
	RetCode int                       `json:"retcode"`
	Message string                    `json:"message"`
	Data    *ILHHardCheckResponseData `json:"data"`
}

func (I *ILHHardCheckResponse) IsSuccess() bool {
	return I.RetCode == 0 && I.Data != nil && len(I.Data.AvailableLaneCodes) > 0
}

func (I *ILHHardCheckResponse) FailMsg() string {
	return I.Message
}

type ILHHardCheckResponseData struct {
	AvailableLaneCodes []string `json:"available_lane_codes"`
}

type GetShopGroupsResp struct {
	RetCode int                       `json:"retcode"`
	Message string                    `json:"message"`
	Data    GetShopGroupsInfoResponse `json:"data"`
}

type GetShopGroupsInfoResponse struct {
	ShopGroups []*ShopGroup `json:"shop_groups"`
}

func (p *GetShopGroupsResp) IsSuccess() bool {
	return p.RetCode == 0 && len(p.Data.ShopGroups) != 0
}

func (p *GetShopGroupsResp) FailMsg() string {
	return "lps get shop groups fail"
}

type (
	GetShopGroupListByTagReq struct {
		ClientTag uint64 `json:"client_tag"`
	}

	GetShopGroupListByTagResp struct {
		RetCode int             `json:"ret_code"`
		Message string          `json:"message"`
		Data    []ShopGroupUnit `json:"data"`
	}

	ShopGroupUnit struct {
		ShopGroupId       int64             `json:"shop_group_id"`
		PriorityShopGroup PriorityShopGroup `json:"priority_shop_group"`
	}

	PriorityShopGroup struct {
		GroupID     int64       `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
		GroupName   string      `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
		GroupType   int32       `protobuf:"varint,3,opt,name=group_type,json=groupType,proto3,enum=shop.Constant_ShopGroupType" json:"group_type,omitempty"`
		GroupStatus int32       `protobuf:"varint,4,opt,name=group_status,json=groupStatus,proto3,enum=shop.Constant_ShopGroupStatus" json:"group_status,omitempty"`
		Region      meta.Region `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
		CreateTime  uint32      `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
		UpdateTime  uint32      `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
		// non-db fields
		TagID uint64 `protobuf:"varint,8,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	}
)

func (p *GetShopGroupListByTagReq) Queries() map[string]string {
	body := make(map[string]string)
	if p.ClientTag != 0 {
		body["client_tag"] = strconv.FormatUint(p.ClientTag, 10)
	}

	return body
}

func (p *GetShopGroupListByTagReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *GetShopGroupListByTagResp) IsSuccess() bool {
	return p.RetCode == 0 && p.Data != nil
}

func (p *GetShopGroupListByTagResp) FailMsg() string {
	return "lps get shop group list by tag fail"
}

type (
	MaskingHardCheckRequest struct {
		ClientTagId     uint64                    `json:"client_tag_id"`
		NeedHardCheck   bool                      `json:"need_hard_check"`
		OmsRequest      *model.OMSAllocateRequest `json:"oms_request"`
		ProductPriority *entity.ProductPriority   `json:"product_priority"`
	}

	MaskingHardCheckResponse struct {
		RetCode int                           `json:"ret_code"`
		Message string                        `json:"message"`
		Data    *MaskingHardCheckResponseData `json:"data"`
	}

	MaskingHardCheckResponseData struct {
		HardResult   *model.ValidateResult `json:"hard_result"`
		ShopGroupIds []int                 `json:"shop_group_ids"`
	}
)

func (m *MaskingHardCheckRequest) Queries() map[string]string {
	return nil
}

func (m *MaskingHardCheckRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(m)
}

func (m *MaskingHardCheckResponse) IsSuccess() bool {
	return m.RetCode == 0 && m.Data != nil
}

func (m *MaskingHardCheckResponse) FailMsg() string {
	return m.Message
}

type (
	GetShopGroupRequest struct {
		MaskProductId int    `json:"mask_product_id"`
		ClientTagId   uint64 `json:"client_tag_id"`
		ShopIds       []int  `json:"shop_ids"`
	}

	GetShopGroupResponse struct {
		RetCode int                       `json:"ret_code"`
		Message string                    `json:"message"`
		Data    *GetShopGroupResponseData `json:"data"`
	}

	GetShopGroupResponseData struct {
		ShopGroupIds []int64 `json:"shop_group_ids"`
	}
)

func (g *GetShopGroupRequest) Queries() map[string]string {
	return nil
}

func (g *GetShopGroupRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetShopGroupResponse) IsSuccess() bool {
	return g.RetCode == 0 && g.Data != nil
}

func (g *GetShopGroupResponse) FailMsg() string {
	return g.Message
}

type (
	GetMaskingProductRefResponse struct {
		RetCode int                         `json:"ret_code"`
		Message string                      `json:"message"`
		Data    []*GetMaskingProductRefData `json:"data"`
	}

	GetMaskingProductRefData struct {
		MaskingProductId   int   `json:"masking_product_id"`
		ComponentProductId []int `json:"component_product_id"`
	}
)

func (g *GetMaskingProductRefResponse) IsSuccess() bool {
	return g.RetCode == 0 && g.Data != nil
}

func (g *GetMaskingProductRefResponse) FailMsg() string {
	return g.Message
}

type (
	GetClientGroupTabsReq struct {
		ID              *uint64 `json:"id,omitempty"`
		ClientGroupName *string `json:"client_group_name,omitempty"`
		ClientTagID     *uint64 `json:"client_tag_id,omitempty"`
		ClientGroupID   *string `json:"client_group_id,omitempty"`
		Version         *string `json:"version,omitempty"`
		Operator        *string `json:"operator,omitempty"`
	}

	GetShopGroupInfoListResp struct {
		Retcode int                     `json:"retcode"`
		Message string                  `json:"message"`
		Data    *DisplayClientGroupResp `json:"data"`
	}
	DisplayClientGroupResp struct {
		List []GetClientGroupTab `json:"list"`
	}
	GetClientGroupTab struct {
		ID              uint64 `json:"ID"`
		ClientGroupName string `json:"ClientGroupName"`
		ClientTagID     uint64 `json:"ClientTagID"`
		ClientGroupID   string `json:"ClientGroupID"`
		Version         string `json:"Version"`
		Operator        string `json:"Operator"`
		Ctime           uint   `json:"Ctime"`
		Mtime           uint   `json:"Mtime"`
		// 单独的json标签
		ProductId int64 `json:"product_id"`
	}
)

func (g *GetClientGroupTabsReq) Queries() map[string]string {
	return nil
}

func (g *GetClientGroupTabsReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetShopGroupInfoListResp) IsSuccess() bool {
	return g.Retcode == 0 && g.Data != nil
}

func (g *GetShopGroupInfoListResp) FailMsg() string {
	return g.Message
}

// copy forecast shop group
type (
	CopyShopGroupReq struct {
		List []ShopGroupReq `json:"list"`
	}
	ShopGroupReq struct {
		VersionPrefix string  `json:"version_prefix"`
		RuleId        int64   `json:"rule_id"`
		ClientTagId   int64   `json:"client_tag_id"`
		ShopGroupList []int64 `json:"shop_group_list"`
	}

	CopyShopGroupResp struct {
		Retcode int    `json:"retcode"`
		Message string `json:"message"`
	}
)

func (g *CopyShopGroupReq) Queries() map[string]string {
	return nil
}

func (g *CopyShopGroupReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *CopyShopGroupResp) IsSuccess() bool {
	return g.Retcode == 0
}

func (g *CopyShopGroupResp) FailMsg() string {
	return g.Message
}

type (
	GetClientInfoByPageReq struct {
		ClientInfoType    ClientInfoType `json:"client_info_type"`
		ClientTagId       int64          `json:"client_tag_id"`
		ClientGroupIdList []int64        `json:"client_group_id_list"`
		Version           string         `json:"version"`
		Page              int64          `json:"page"`
		Size              int64          `json:"size"`
	}
	GetClientInfoByPageResp struct {
		Retcode int64       `json:"retcode"`
		Message string      `json:"message"`
		Data    *ClientInfo `json:"data"`
	}
	ClientInfo struct {
		List  []Model `json:"list"`
		Page  int64   `json:"page"`
		Size  int64   `json:"size"`
		Total int64   `json:"total"`
	}
	Model struct {
		ModelId       string `json:"model_id"`
		ProductId     int64  `json:"product_id"`
		ClientTagId   int64  `json:"client_tag_id"`
		ClientGroupId string `json:"client_group_id"`
		Version       string `json:"version"` //used to print log
	}
)

func (g *GetClientInfoByPageReq) Queries() map[string]string {
	return nil
}

func (g *GetClientInfoByPageReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetClientInfoByPageResp) IsSuccess() bool {
	return g.Retcode == 0 && g.Data != nil
}

func (g *GetClientInfoByPageResp) FailMsg() string {
	return g.Message
}

type (
	GetILHByMultiIDReq struct {
		MultiProductID int `json:"multi_product_id"`
	}

	GetILHByMultiIDResp struct {
		Retcode int64                    `json:"retcode"`
		Message string                   `json:"message"`
		Data    *GetILHByMultiIDRespData `json:"data"`
	}

	GetILHByMultiIDRespData struct {
		IlhProductID   string `json:"ilh_product_id"`
		IlhProductName string `json:"ilh_product_name"`
	}
)

func (g *GetILHByMultiIDReq) Queries() map[string]string {
	return map[string]string{"multi_product_id": strconv.Itoa(g.MultiProductID)}
}

func (g *GetILHByMultiIDReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetILHByMultiIDResp) IsSuccess() bool {
	return g.Retcode == 0 && g.Data != nil
}

func (g *GetILHByMultiIDResp) FailMsg() string {
	return g.Message
}
