package lpsclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"testing"
)

func InitConfig() {
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("grpc_sever")); err != nil {
		panic(err)
	}
	if err := configutil.Init(); err != nil {
		panic(err)
	}
	adminConf := configutil.GetLpsAdminConf(ctx)
	adminConf.Host = "https://admin.lps.test.shopee.com.vn"
	if err := dbutil.Init(); err != nil {
		panic(err)
	}
}

func TestLpsApiImpl_GetAllProductIdNameList(t *testing.T) {
	InitConfig()
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name  string
		args  args
		want  map[int64]string
		want1 *srerr.Error
	}{
		{
			name: "get all product dict",
			args: args{
				ctx: context.Background(),
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &LpsApiImpl{}
			got, got1 := p.GetAllProductIdNameList(tt.args.ctx)
			if got != nil {
				t.Logf("GetAllProductIdNameList() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetAllProductIdNameList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestLpsApiImpl_GetLaneCodes(t *testing.T) {
	err := chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	if err != nil {
		panic(err)
	}
	confErr := configutil.Init()
	if confErr != nil {
		panic(confErr)
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name  string
		args  args
		want  []LaneCodesInfo
		want1 *srerr.Error
	}{
		{
			name:  "testcase",
			args:  args{ctx: context.Background()},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &LpsApiImpl{}
			got, got1 := p.GetLaneCodes(tt.args.ctx)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLaneCodes() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetLaneCodes() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestLpsApiImpl_GetLineDictList(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	type args struct {
		ctx       context.Context
		productId int64
	}
	tests := []struct {
		name  string
		args  args
		want  *LineListResult
		want1 *srerr.Error
	}{
		{
			name: "testcase",
			args: args{
				ctx:       context.Background(),
				productId: 599993,
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &LpsApiImpl{}
			got, got1 := p.GetLineDictList(tt.args.ctx, tt.args.productId)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLineDictList() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetLineDictList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestLpsApiImpl_buildLpsAdminHeader(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]string
		want1 *srerr.Error
	}{
		{
			name:  "testcase",
			args:  args{ctx: context.Background()},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &LpsApiImpl{}
			got, got1 := p.buildLpsAdminHeader(tt.args.ctx)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("buildLpsAdminHeader() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("buildLpsAdminHeader() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestNewLpsApiImpl(t *testing.T) {
	tests := []struct {
		name string
		want *LpsApiImpl
	}{
		{
			name: "testcase",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewLpsApiImpl(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewLpsApiImpl() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLpsApiImpl_GetRuleVolumeType(t *testing.T) {
	InitConfig()
	type args struct {
		ctx context.Context
		id  int
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 *srerr.Error
	}{
		{
			name: "Get Rule Volume case",
			args: args{
				ctx: context.Background(),
				id:  53,
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &LpsApiImpl{}
			got, got1 := p.GetRuleVolumeType(tt.args.ctx, tt.args.id)
			if got != "" {
				t.Logf("GetRuleVolumeType() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetRuleVolumeType() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
