package auditclient

type AuditApprovalRequest struct {
	PermissionCode  string `json:"permission_code"`
	Applier         string `json:"applier"`
	Project         string `json:"project"`
	Region          string `json:"region"` //upper case
	ExtraData       string `json:"extra_data"`
	ApplicationInfo string `json:"application_info"`
}

type AuditApprovalResp struct {
	Retcode  int    `json:"retcode"`
	TicketID string `json:"data"` //审批中心用"data"装填ticket id
}
