package auditclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

type AuditApi interface {
	AuditApproval(ctx context.Context, xOpaToken string, req *AuditApprovalRequest) (string, *srerr.Error)
}

type AuditApiImpl struct {
}

func NewAuditApiImpl() *AuditApiImpl {
	return &AuditApiImpl{}
}

func (i *AuditApiImpl) AuditApproval(ctx context.Context, xOpaToken string, req *AuditApprovalRequest) (string, *srerr.Error) {
	conf := configutil.GetBusinessAuditConf(ctx)
	header := i.buildAuditHeader(ctx, xOpaToken)
	url := objutil.Merge(conf.Host, ApprovalPath)
	body, jErr := jsoniter.Marshal(req)
	if jErr != nil {
		return "", srerr.With(srerr.TypeConvertErr, nil, jErr)
	}

	data, gErr := httputil.PostJson(ctx, url, body, int(conf.Timeout), header)
	if gErr != nil {
		return "", srerr.With(srerr.AuditApiErr, body, gErr)
	}
	resp := AuditApprovalResp{}
	if err := jsoniter.Unmarshal(data, &resp); err != nil {
		return "", srerr.With(srerr.TypeConvertErr, nil, err)
	}
	if resp.Retcode != 0 {
		return "", srerr.New(srerr.AuditApiErr, nil, fmt.Sprintf("audit api retcode:%d", resp.Retcode))
	}

	//审批接口的ticket id直接放到data字段里
	return resp.TicketID, nil
}

func (i *AuditApiImpl) buildAuditHeader(ctx context.Context, xOpaToken string) map[string]string {
	return map[string]string{"X-OPA-Token": xOpaToken}
}
