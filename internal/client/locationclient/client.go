package locationclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	locationPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"strconv"
	"strings"
)

const (
	LocationServiceSchemaId = "protobuf.LocationService" // SchemaId
	CountryJoiner           = "||"
	AddressJoiner           = "#"
)

var (
	GetLocationByLocationId = client.GrpcService{
		OperationID: "GetLocationByLocationId",
		Scene:       client.MultiAlive,
		System:      constant.SystemLocation,
	}
	GetFullLocationByLocationId = client.GrpcService{
		OperationID: "GetFullLocationByLocationId",
		Scene:       client.MultiAlive,
		System:      constant.SystemLocation,
	}
	GetFullLocationByName = client.GrpcService{
		OperationID: "GetFullLocationByName",
		Scene:       client.MultiAlive,
		System:      constant.SystemLocation,
	}
	GetLocationTree = client.GrpcService{
		OperationID: "GetLocationTree",
		Scene:       client.MultiAlive,
		System:      constant.SystemLocation,
	}
	GetFullLocationByFullPathName = client.GrpcService{
		OperationID: "GetFullLocationByFullPathName",
		Scene:       client.MultiAlive,
		System:      constant.SystemLocation,
	}
)

type LocationClient interface {
	GetLocationByLocationIdWithCache(ctx context.Context, locationId int64) (*locationPb.LocationRsp, *srerr.Error)
	GetFullLocationByLocationIdWithCache(ctx context.Context, locationId int64) (*locationPb.FullLocationRsp, *srerr.Error)
	GetFullLocationByLocationNameWithCache(ctx context.Context, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error)
	GetLocationTreeWithCache(ctx context.Context, country string, locationId uint32, subLevel int32) (*locationPb.LocationTreeRsp, *srerr.Error)
	GetFullLocationByLocationFullPathNameWithCache(ctx context.Context, country string, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error)
}

type LocationClientImpl struct {
	Cache *layercache.LevelCache
}

func NewLocationClientImpl(
	c *layercache.LevelCache,
) *LocationClientImpl {
	return &LocationClientImpl{
		Cache: c,
	}
}

// GetLocationByLocationIdWithCache layer cache and original method
func (l *LocationClientImpl) GetLocationByLocationIdWithCache(ctx context.Context, locationId int64) (*locationPb.LocationRsp, *srerr.Error) {
	key := strconv.FormatInt(locationId, 10)
	var ret locationPb.LocationRsp
	if err := l.Cache.Get(ctx, constant.LevelCacheGetLocationByLocationId, key, &ret,
		layercache.WithLoader(l.getLocationByLocationIdLoader),
		layercache.WithColdExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetLocationByLocationIdWithCache|get_cache_fail|locationId=%+v, value=%+v", key, ret)
		return nil, srerr.With(srerr.GetLocationInfoFailed, locationId, err)
	}
	return &ret, nil
}

// splitCountryLocationId key to param
func splitCountryLocationId(key string) (locationId uint32) {
	id, err := strconv.ParseInt(key, 10, 64)
	if err != nil {
		return 0
	}
	locationId = uint32(id)
	return locationId
}

// getLocationByLocationIdLoader cache loader
func (l *LocationClientImpl) getLocationByLocationIdLoader(ctx context.Context, key string) (interface{}, error) {
	locationId := splitCountryLocationId(key)
	ret, retErr := l.getLocationByLocationId(ctx, locationId)
	if retErr != nil {
		return nil, retErr
	}
	return ret, nil

}

// getLocationByLocationId original method
func (l *LocationClientImpl) getLocationByLocationId(ctx context.Context, locationId uint32) (*locationPb.LocationRsp, *srerr.Error) {
	req := locationPb.LocationReq{
		LocationId: locationId,
	}
	var rsp locationPb.LocationRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLocation, LocationServiceSchemaId, GetLocationByLocationId, &req, &rsp); err != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, req, err)
	}
	return &rsp, nil
}

// GetFullLocationByLocationIdWithCache layer cache and original method
func (l *LocationClientImpl) GetFullLocationByLocationIdWithCache(ctx context.Context, locationId int64) (*locationPb.FullLocationRsp, *srerr.Error) {
	key := strconv.FormatInt(locationId, 10)
	var ret locationPb.FullLocationRsp
	if err := l.Cache.Get(ctx, constant.LevelCacheGetFullLocationByLocationId, key, &ret,
		layercache.WithLoader(l.getFullLocationByLocationIdLoader),
		layercache.WithColdExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetLocationByLocationIdWithCache|get_cache_fail|key=%+v, value=%+v, err=%v", key, ret, err)
		return nil, srerr.With(srerr.GetLocationInfoFailed, key, err)
	}
	return &ret, nil
}

// getFullLocationByLocationIdLoader cache loader
func (l *LocationClientImpl) getFullLocationByLocationIdLoader(ctx context.Context, key string) (interface{}, error) {
	locationId := splitCountryLocationId(key)
	ret, retErr := l.getFullLocationByLocationId(ctx, locationId)
	if retErr != nil {
		return nil, retErr
	}
	return ret, nil
}

// getFullLocationByLocationId original method
func (l *LocationClientImpl) getFullLocationByLocationId(ctx context.Context, locationId uint32) (*locationPb.FullLocationRsp, *srerr.Error) {
	req := locationPb.LocationReq{
		LocationId: locationId,
		// country should not add in request param, cause there will be some cb order, location are not in deliver country
		//Country:    envvar.GetCIDLowerWithCtx(),
	}
	var rsp locationPb.FullLocationRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLocation, LocationServiceSchemaId, GetFullLocationByLocationId, &req, &rsp); err != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, req, err)
	}
	return &rsp, nil
}

// GetFullLocationByLocationNameWithCache layer cache and original method
func (l *LocationClientImpl) GetFullLocationByLocationNameWithCache(ctx context.Context, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
	key := countryNamesToKey(splitNames)
	var ret locationPb.FullLocationRsp
	if err := l.Cache.Get(ctx, constant.LevelCacheGetFullLocationByLocationName, key, &ret,
		layercache.WithLoader(l.getFullLocationByLocationNameLoader),
		layercache.WithColdExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetLocationByLocationIdWithCache|get_cache_fail|key=%+v, value=%+v, err=%v", key, ret, err)
		return nil, srerr.With(srerr.GetLocationInfoFailed, key, err)
	}
	return &ret, nil
}

// countryNamesToKey param to key
func countryNamesToKey(splitNames []string) string {
	key := strings.Join(splitNames, "#")
	return key
}

// splitNamesKey key to param
func splitNamesKey(key string) []string {
	if key == "" {
		return []string{}
	}
	return strings.Split(key, "#")
}

// getFullLocationByLocationNameLoader cache loader
func (l *LocationClientImpl) getFullLocationByLocationNameLoader(ctx context.Context, key string) (interface{}, error) {
	splitNames := splitNamesKey(key)
	ret, retErr := l.getFullLocationByLocationName(ctx, splitNames)
	if retErr != nil {
		return nil, retErr
	}
	return ret, nil
}

// getFullLocationByLocationName original method
func (l *LocationClientImpl) getFullLocationByLocationName(ctx context.Context, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
	req := locationPb.LocationReq{
		SplitNames: splitNames,
	}
	var rsp locationPb.FullLocationRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLocation, LocationServiceSchemaId, GetFullLocationByName, &req, &rsp); err != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, req, err)
	}
	return &rsp, nil
}

// GetLocationTreeWithCache layer cache and original method
func (l *LocationClientImpl) GetLocationTreeWithCache(ctx context.Context, country string, locationId uint32, subLevel int32) (*locationPb.LocationTreeRsp, *srerr.Error) {
	if country == "" {
		country = envvar.GetCID()
	}
	key := fmt.Sprintf("%s#%d#%d", country, locationId, subLevel)
	var ret locationPb.LocationTreeRsp
	if err := l.Cache.Get(ctx, constant.LevelCacheGetLocationTreeByCountry, key, &ret,
		layercache.WithLoader(l.getLocationTreeByCountryLoader),
		layercache.WithColdExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetLocationTreeByCountry|get_cache_fail|key=%+v, value=%+v, err=%v", key, ret, err)
		return nil, srerr.With(srerr.GetLocationInfoFailed, key, err)
	}
	return &ret, nil
}

func splitCidLocationLevelKey(key string) (string, uint32, int32) {
	data := strings.Split(key, "#")
	// wrong length
	if len(data) != 3 {
		return "", 0, 0
	}
	// country
	country := data[0]
	// locationId
	locationId, err := strconv.ParseInt(data[1], 10, 64)
	if err != nil {
		return "", 0, 0
	}
	// subLevel
	subLevel, err := strconv.ParseInt(data[2], 10, 64)
	if err != nil {
		return "", 0, 0
	}
	return country, uint32(locationId), int32(subLevel)
}

// getLocationTreeByCountryLoader cache loader
func (l *LocationClientImpl) getLocationTreeByCountryLoader(ctx context.Context, key string) (interface{}, error) {
	country, locationId, subLevel := splitCidLocationLevelKey(key)
	ret, err := l.getLocationTreeByCountry(ctx, country, locationId, subLevel)
	if err != nil {
		return nil, err
	}
	return ret, nil

}

// getLocationTreeByCountry original method
func (l *LocationClientImpl) getLocationTreeByCountry(ctx context.Context, country string, locationId uint32, subLevel int32) (*locationPb.LocationTreeRsp, *srerr.Error) {
	req := locationPb.LocationTreeReq{
		Country:    country,
		LocationId: locationId,
		SubLevel:   subLevel,
	}
	var rsp locationPb.LocationTreeRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLocation, LocationServiceSchemaId, GetLocationTree, &req, &rsp); err != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, req, err)
	}
	return &rsp, nil
}

// GetFullLocationByLocationFullPathNameWithCache 模糊匹配地址。对MY从4级、TH从3级地址往上匹配，一直匹配到2级，匹配不到则报错（其他市场目前是根据地址精准匹配，后续其他市场也有可能会改为模糊匹配，需要注意）
func (l *LocationClientImpl) GetFullLocationByLocationFullPathNameWithCache(ctx context.Context, country string, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
	key := countryFullPathNamesToKey(country, splitNames)
	var ret locationPb.FullLocationRsp
	if err := l.Cache.Get(ctx, constant.LevelCacheGetFullLocationByLocationFullPathName, key, &ret,
		layercache.WithLoader(l.getFullLocationByLocationFullPathNameLoader),
		layercache.WithColdExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetLocationByLocationIdWithCache|get_cache_fail|key=%+v, value=%+v, err=%v", key, ret, err)
		return nil, srerr.With(srerr.GetLocationInfoFailed, key, err)
	}
	return &ret, nil
}

// getFullLocationByLocationFullPathNameLoader cache loader
func (l *LocationClientImpl) getFullLocationByLocationFullPathNameLoader(ctx context.Context, key string) (interface{}, error) {
	country, splitNames := splitFullPathNamesKey(ctx, key)
	if country == "" || len(splitNames) < 1 {
		return nil, srerr.New(srerr.GetLocationInfoFailed, key, "country is nil or address is nil")
	}
	ret, retErr := l.getFullLocationByLocationFullPathName(ctx, country, splitNames)
	if retErr != nil {
		return nil, retErr
	}
	return ret, nil
}

// getFullLocationByLocationFullPathName original method
func (l *LocationClientImpl) getFullLocationByLocationFullPathName(ctx context.Context, country string, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
	req := locationPb.LocationReq{
		Country:    country,
		SplitNames: splitNames,
	}
	var rsp locationPb.FullLocationRsp
	if err := client.GrpcInvoke(ctx, constant.SystemLocation, LocationServiceSchemaId, GetFullLocationByFullPathName, &req, &rsp); err != nil {
		monitoring.ReportError(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationRequestError, fmt.Sprintf("request locs full location path error: %v", err))
		return nil, srerr.With(srerr.GetLocationInfoFailed, req, err)
	}
	if rsp.Header != nil && rsp.GetHeader().GetRetcode() != 0 {
		monitoring.ReportError(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationRequestError, fmt.Sprintf("locs full location path error. retcode: %v, errorMessage: %v", rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage()))
		logger.CtxLogErrorf(ctx, "GetFullLocationByFullPathName error|retcode: %v, message: %v", rsp.GetHeader().GetRetcode(), rsp.GetHeader().GetMessage())
	}
	monitoring.ReportSuccess(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationSuccess, "")
	return &rsp, nil
}

// countryFullPathNamesToKey param to key
func countryFullPathNamesToKey(country string, splitNames []string) string {
	key := strings.Join(splitNames, AddressJoiner)
	return country + CountryJoiner + key
}

// splitFullPathNamesKey key to param
func splitFullPathNamesKey(ctx context.Context, key string) (string, []string) {
	if key == "" {
		// 异常上报
		monitoring.ReportError(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationReqParamNilError, "get location full path name param is nil")
		return "", []string{}
	}
	countryAndAddressList := strings.Split(key, CountryJoiner)
	if len(countryAndAddressList) != 2 {
		// 异常上报
		monitoring.ReportError(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationReqParamError, "get location full path name need country and address")
		return "", []string{}
	}

	return countryAndAddressList[0], strings.Split(countryAndAddressList[1], AddressJoiner)
}
