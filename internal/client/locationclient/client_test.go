package locationclient

import (
	"reflect"
	"testing"
)

func Test_splitNames<PERSON>ey(t *testing.T) {
	type args struct {
		key string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "test_case_empty_string",
			args: args{key: ""},
			want: []string{},
		},
		{
			name: "test_case_normal_string",
			args: args{key: "test"},
			want: []string{"test"},
		},
		{
			name: "test_case_normal_string_1",
			args: args{key: "test#hello"},
			want: []string{"test", "hello"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := splitNamesKey(tt.args.key); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("splitNamesKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
