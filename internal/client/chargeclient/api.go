package chargeclient

import (
	"context"
	"net/http"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	chargepb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v16/logistics-charge-platform/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
)

const (
	AllocationApiServiceSchemaId                = "charge_protobuf.AllocationApiService"
	ThirdPartyShippingFeeApiServiceSchemaId     = "charge_protobuf.ThirdPartyShippingFeeApiService"
	LMEstimationCostCalculateApiServiceSchemaId = "charge_protobuf.LMEstimationCostCalculateApiService"
)

var (
	BatchCalcForecastShippingFee = client.GrpcService{
		OperationID: "BatchCalcForecastShippingFee",
		Scene:       client.DR,
		System:      constant.SystemChargeCheckoutGrpc, // 这个没有具体使用的地方
	}
	BatchAllocationShippingFeeCalculate = client.GrpcService{
		OperationID: "BatchAllocationShippingFeeCalculate",
		Scene:       client.DR,
		System:      constant.SystemChargeCheckoutGrpc,
	}
	QueryAllocatingRateFee = client.HttpService{
		Endpoint: "/api/admin/rate_table/allocating/list",
		Scene:    client.DR,
		System:   constant.SystemChargeApi,
	}
	BatchForecastAllocatingESF = client.GrpcService{
		OperationID: "BatchForecastAllocationShippingFeeCalculate",
		Scene:       client.MultiAlive,
		System:      constant.SystemChargeCheckoutGrpc,
	}
	BatchLMEstimationCostCalculate = client.GrpcService{
		OperationID: "BatchLMEstimationCostCalculate",
		Scene:       client.DR,
		System:      constant.SystemChargeCoreGrpc,
	}
)

type ChargeApi interface {
	//selectLane接口计算line纬度运费， 3pl场景只有br 91003渠道在用line纬度的计算运费，4pl的场景my市场的20033这个渠道但是很久没有流量(计费给的结论是下线了)
	BatchCalcLineForecastShippingFeeWithGrpc(ctx context.Context, data []*chargeentity.CalcLineForecastShippingFeeReqData) (*chargeentity.CalcLineForecastShippingFeeResp, *srerr.Error)
	// allocate接口计算fulfillment product的esf运费
	BatchAllocatingESFWithGrpc(ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error)
	// allocate(仓单没有预测)预测计算esf运费，影响task服务，调用量比较少
	BatchForecastAllocatingESF(ctx context.Context, req *chargeentity.BatchForecastAllocationESFReq) (*chargeentity.BatchAllocationESFResp, *srerr.Error)
	// admin接口查询fulfillment product费率表，调用量极少
	QueryAllocatingRateFee(ctx context.Context, req *chargeentity.AllocatingRateFeeListReq) (*chargeentity.AllocatingRateFeeListResp, *srerr.Error)
	// CB LM ASF计算
	BatchLMEstimationCostCalculate(ctx context.Context, calcInfoList []*chargepb.SingleLMEstimationCostCalculateInfo) ([]*chargepb.SingleEstimationCostCalculateResp, *srerr.Error)
}

type ChargeApiImpl struct {
}

func NewChargeApiImpl() *ChargeApiImpl {
	return &ChargeApiImpl{}
}

// selectLane接口计算line纬度运费， 3pl场景只有br 91003渠道在用line纬度的计算运费，4pl的场景my市场的20033这个渠道但是很久没有流量(计费给的结论是下线了)
func (r *ChargeApiImpl) BatchCalcLineForecastShippingFeeWithGrpc(ctx context.Context, dataList []*chargeentity.CalcLineForecastShippingFeeReqData) (*chargeentity.CalcLineForecastShippingFeeResp, *srerr.Error) {
	if len(dataList) == 0 {
		return nil, srerr.New(srerr.ChargeApiError, nil, "BatchCalcLineForecastShippingFeeWithGrpc error, data_list is nil")
	}
	reqParam := r.lineShippingFeeReq(ctx, dataList)
	var grpcRsp chargepb.BatchCalcForecastShippingFeeResp
	if err := client.GrpcInvoke(ctx, constant.SystemChargeCheckoutGrpc, ThirdPartyShippingFeeApiServiceSchemaId, BatchCalcForecastShippingFee, reqParam, &grpcRsp); err != nil {
		logger.CtxLogErrorf(ctx, "BatchCalcLineForecastShippingFeeWithGrpc error, err=%+v", err)
		return nil, srerr.New(srerr.ChargeApiError, nil, "BatchCalcLineForecastShippingFeeWithGrpc error, err_msg=%v", err)
	}
	logger.CtxLogDebugf(ctx, "BatchCalcLineForecastShippingFeeWithGrpc|Response: %s", str.JsonStringForDebugLog(grpcRsp))
	header := grpcRsp.GetRespHeader()
	if header == nil {
		logger.CtxLogErrorf(ctx, "BatchCalcLineForecastShippingFeeWithGrpc error, invalid response from esf, rsp header is nil.")
		return nil, srerr.New(srerr.ChargeApiError, nil, "BatchCalcLineForecastShippingFeeWithGrpc error, invalid response from esf")
	}
	retCode := int(header.GetRetcode())
	// 正常返回
	if retCode == 0 {
		httpRsp := r.batchCalcLineForecastShippingFeeRespGrpcToHttp(grpcRsp)
		return httpRsp, nil
	}
	return nil, srerr.New(srerr.ChargeApiError, nil, "chargeApi code:%d, err:%s, detail:%s", retCode, header.GetMessage(), header.GetErrDetail())
}

func (r *ChargeApiImpl) BatchAllocatingESFWithGrpc(ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
	reqParam := r.batchAllocatingESFReq(ctx, req, scene)
	var grpcRsp chargepb.BatchAllocationShippingFeeResp
	var httpRsp chargeentity.BatchAllocationESFResp
	if err := client.GrpcInvoke(ctx, constant.SystemChargeCheckoutGrpc, AllocationApiServiceSchemaId, BatchAllocationShippingFeeCalculate, reqParam, &grpcRsp); err != nil {
		logger.CtxLogErrorf(ctx, "BatchAllocatingESFWithGrpc error, err=%+v", err)
		return nil, srerr.New(srerr.ChargeApiError, nil, "BatchAllocatingESFWithGrpc error, err_msg=%v", err)
	}
	header := grpcRsp.GetRespHeader()
	if header == nil {
		logger.CtxLogErrorf(ctx, "BatchAllocatingESFWithGrpc error, invalid response from esf, rsp header is nil.")
		return nil, srerr.New(srerr.ChargeApiError, nil, "CalcEsfWeightWithGrpc error, invalid response from esf")
	}
	retCode := int(header.GetRetcode())
	// 正常返回
	if retCode == 0 {
		httpRsp = r.batchAllocatingESFGrpcRespToHttp(grpcRsp)
		return &httpRsp, nil
	}
	return nil, srerr.New(srerr.ChargeApiError, nil, "chargeApi code:%d, err:%s, detail:%s", retCode, header.GetMessage(), header.GetErrDetail())
}

func (r *ChargeApiImpl) QueryAllocatingRateFee(ctx context.Context, req *chargeentity.AllocatingRateFeeListReq) (*chargeentity.AllocatingRateFeeListResp, *srerr.Error) {
	var rsp chargeentity.AllocatingRateFeeListResp
	if err := client.HttpInvoke(ctx, constant.SystemChargeApi, QueryAllocatingRateFee,
		http.MethodGet, req, &rsp, GenChargeApiHeader(ctx)); err != nil {
		return nil, srerr.With(srerr.ChargeApiError, req, err)
	}
	return &rsp, nil
}

func (r *ChargeApiImpl) BatchForecastAllocatingESF(ctx context.Context, req *chargeentity.BatchForecastAllocationESFReq) (*chargeentity.BatchAllocationESFResp, *srerr.Error) {
	realReq := r.transferBatchForecastAllocationShippingFeeRequestHttpToCharge(ctx, req)
	var rsp chargepb.BatchForecastAllocationShippingFeeResp
	if err := client.GrpcInvoke(ctx, constant.SystemChargeCheckoutGrpc, AllocationApiServiceSchemaId, BatchForecastAllocatingESF, realReq, &rsp); err != nil {
		return nil, srerr.With(srerr.ChargeApiError, req, err)
	}
	realResp := r.transferBatchForecastAllocationShippingFeeResponseChargeToHttp(&rsp)

	return realResp, nil
}

func (r *ChargeApiImpl) BatchLMEstimationCostCalculate(ctx context.Context, calcInfoList []*chargepb.SingleLMEstimationCostCalculateInfo) ([]*chargepb.SingleEstimationCostCalculateResp, *srerr.Error) {
	req := &chargepb.BatchLMEstimationCostCalculateReq{
		ReqHeader:          r.getChargeReqHeader(ctx),
		BatchCalculateInfo: calcInfoList,
	}

	var rsp chargepb.BatchLMEstimationCostCalculateResp
	if err := client.GrpcInvoke(ctx, constant.SystemChargeCoreGrpc, LMEstimationCostCalculateApiServiceSchemaId, BatchLMEstimationCostCalculate, req, &rsp); err != nil {
		return nil, srerr.With(srerr.ChargeApiError, req, err)
	}

	return rsp.GetBatchCalculateResult(), nil
}
