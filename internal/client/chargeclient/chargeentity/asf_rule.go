package chargeentity

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"github.com/json-iterator/go"
	"strconv"
)

const (
	UnknownShippingFee = -1
)

const (
	ForecastTypeBRLocal   = 0
	ForecastTypeMYFiveLeg = 1 // 枚举值不需要下线，后续重新开发finance还会用到

	PaymentTypeCod    = 0
	PaymentTypeNonCod = 1

	DirectionForward uint8 = 1
	DirectionReverse uint8 = 2
)

type (
	// CreateOrUpdateAsfRuleReq 向运费工具注册产品实际费用规则参数
	CreateOrUpdateAsfRuleReq struct {
		ProductId                      int     `json:"product_id"`
		CalculateRule                  int     `json:"calculate_rule"`
		CalculatedByShopType           int     `json:"calculated_by_shop_type"`
		VolumetricFactor               int     `json:"volumetric_factor"`
		FallbackRule                   int     `json:"fallback_rule"`
		ChargedWeightFunction          int     `json:"charged_weight_function"`
		VolumetricRatioCapRelationship int     `json:"volumetric_ratio_cap_relationship"`
		VolumetricRatioCap             float64 `json:"volumetric_ratio_cap"`
		MinimumDimension               float64 `json:"minimum_dimension,omitempty"`
		ThreePLGap                     uint    `json:"add_three_pl_gap"`      // 是否叠加 3PL gap，必填，默认为0，不使用3PL gap计算
		IncludeCodFee                  uint    `json:"include_cod_fee"`       // SCFS-46 新增字段， Include COD Service Fee， 枚举值，  0 关闭，1 打开
		IncludeInsuranceFee            uint    `json:"include_insurance_fee"` // SCFS-46 新增字段，Include Insurance Fee， 枚举值，  0 关闭，1 打开
	}
	// CreateOrUpdateAsfRuleResp 向运费工具注册产品实际费用规则响应
	CreateOrUpdateAsfRuleResp struct {
		Retcode int         `json:"retcode"`
		Message string      `json:"message"`
		Detail  string      `json:"detail"`
		Data    interface{} `json:"data,omitempty"`
	}
)

func (p *CreateOrUpdateAsfRuleReq) Queries() map[string]string {
	return nil
}

func (p *CreateOrUpdateAsfRuleReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *CreateOrUpdateAsfRuleResp) IsSuccess() bool {
	return p.Retcode == 0
}

func (p *CreateOrUpdateAsfRuleResp) FailMsg() string {
	return "create or update asf rule fail"
}

type (
	// QueryAsfRuleReq 查询实际运费规则请求
	QueryAsfRuleReq struct {
		ProductId string `json:"product_id"`
	}
	// QueryAsfRuleResp 查询实际费用规则响应
	QueryAsfRuleResp struct {
		Retcode int          `json:"retcode"`
		Message string       `json:"message"`
		Detail  string       `json:"detail"`
		Data    *AsfRuleInfo `json:"data,omitempty"`
	}
	// AsfRuleInfo 实际运费规则基本信息
	AsfRuleInfo struct {
		Id                             uint64  `json:"id"`
		ProductId                      int     `json:"product_id"`
		CalculateRule                  int     `json:"calculate_rule"`
		CalculatedByShopType           int     `json:"calculated_by_shop_type"`
		VolumetricFactor               int     `json:"volumetric_factor"`
		FallbackRule                   int     `json:"fallback_rule"`
		ChargedWeightFunction          int     `json:"charged_weight_function"`
		VolumetricRatioCapRelationship int     `json:"volumetric_ratio_cap_relationship"`
		VolumetricRatioCap             float64 `json:"volumetric_ratio_cap"`
		MinimumDimension               float64 `json:"minimum_dimension,omitempty"`
		ThreePLGap                     uint    `json:"add_three_pl_gap,omitempty"`
		Ctime                          int     `json:"ctime"`
		Mtime                          int     `json:"mtime"`
		IncludeCodeFee                 uint    `json:"include_cod_fee,omitempty"`
		IncludeInsuranceFee            uint    `json:"include_insurance_fee,omitempty"`
	}
)

func (p *QueryAsfRuleReq) Queries() map[string]string {
	return map[string]string{"product_id": p.ProductId}
}

func (p *QueryAsfRuleReq) Entity() ([]byte, error) {
	return nil, nil
}

func (p *QueryAsfRuleResp) IsSuccess() bool {
	return p.Retcode == 0 && p.Data != nil && p.Data.ProductId > 0
}

func (p *QueryAsfRuleResp) FailMsg() string {
	return "query asf rule info fail"
}

type (
	AllocatingESFReq struct {
		Token               string    `json:"token"`
		ProductID           int64     `json:"product_id"`
		CODAmount           *float64  `json:"cod_amount"`
		COGS                *float64  `json:"cogs"`
		Timestamp           uint32    `json:"timestamp"`
		WMSFlag             int       `json:"wms_flag"`
		PickupLocationIDs   []int     `json:"pickup_location_ids"`
		DeliveryLocationIDs []int     `json:"delivery_location_ids"`
		PickupPostcode      *string   `json:"pickup_postcode"`
		DeliveryPostcode    *string   `json:"delivery_postcode"`
		PickupLongitude     *string   `json:"pickup_longitude"`
		PickupLatitude      *string   `json:"pickup_latitude"`
		DeliverLongitude    *string   `json:"deliver_longitude"`
		DeliverLatitude     *string   `json:"deliver_latitude"`
		SkuInfo             []SkuInfo `json:"sku_info"`
		SellerTaxNumber     string    `json:"seller_tax_number"`
		StateRegistration   string    `json:"state_registration"`
	}
	SkuInfo struct {
		ItemID       uint64   `json:"item_id,omitempty"`
		ModelID      uint64   `json:"model_id,omitempty"`
		CategoryID   *uint64  `json:"category_id,omitempty"`
		Weight       *float64 `json:"weight,omitempty"`
		Quantity     int      `json:"quantity,omitempty"`
		Length       *float64 `json:"length,omitempty"`
		Width        *float64 `json:"width,omitempty"`
		Height       *float64 `json:"height,omitempty"`
		ItemPriceUSD float64  `json:"item_price_usd,omitempty"`
		ItemPrice    float64  `json:"item_price,omitempty"`
		ShopID       string   `json:"shop_id,omitempty"`
		ItemSizeId   *int     `json:"item_size_id,omitempty"`
	}

	AllocatingESFResp struct {
		Retcode int                `json:"retcode"`
		Message string             `json:"message"`
		Detail  string             `json:"detail"`
		Data    *AllocatingESFData `json:"data,omitempty"`
	}

	AllocatingESFData struct {
		AllocatingESF float64 `json:"allocating_shipping_fee"`
	}
)

func (p *AllocatingESFReq) Queries() map[string]string {
	return nil
}

func (p *AllocatingESFReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *AllocatingESFResp) IsSuccess() bool {
	return p.Retcode == 0 && p.Data != nil
}

func (p *AllocatingESFResp) FailMsg() string {
	return p.Message
}

func (p *SkuInfo) GetCategoryID() uint64 {
	if p != nil && p.CategoryID != nil {
		return *p.CategoryID
	}
	return 0
}

func (p *SkuInfo) GetWeight() float64 {
	if p != nil && p.Weight != nil {
		return *p.Weight
	}
	return 0
}

func (p *SkuInfo) GetLength() float64 {
	if p != nil && p.Length != nil {
		return *p.Length
	}
	return 0
}

func (p *SkuInfo) GetWidth() float64 {
	if p != nil && p.Width != nil {
		return *p.Width
	}
	return 0
}

func (p *SkuInfo) GetHeight() float64 {
	if p != nil && p.Height != nil {
		return *p.Height
	}
	return 0
}

func (p *SkuInfo) GetItemSizeId() int {
	if p != nil && p.ItemSizeId != nil {
		return *p.ItemSizeId
	}
	return 0
}

type (
	BatchAllocationESFReq struct {
		Token string                           `json:"token"`
		Data  []*BatchAllocationESFReqDataItem `json:"data"`
	}

	BatchAllocationESFReqDataItem struct {
		UniqueId            string    `json:"unique_id"`
		ProductID           int64     `json:"product_id"`
		CODAmount           *float64  `json:"cod_amount"`
		COGS                *float64  `json:"cogs"`
		AddCodFee           int32     `json:"add_cod_fee"`
		Timestamp           uint32    `json:"timestamp"`
		WMSFlag             int       `json:"wms_flag"`
		PickupLocationIDs   []int     `json:"pickup_location_ids"`
		DeliveryLocationIDs []int     `json:"delivery_location_ids"`
		PickupPostcode      *string   `json:"pickup_postcode"`
		DeliveryPostcode    *string   `json:"delivery_postcode"`
		PickupLongitude     *string   `json:"pickup_longitude"`
		PickupLatitude      *string   `json:"pickup_latitude"`
		DeliverLongitude    *string   `json:"deliver_longitude"`
		DeliverLatitude     *string   `json:"deliver_latitude"`
		SkuInfo             []SkuInfo `json:"sku_info"`
		SellerTaxNumber     string    `json:"seller_tax_number"`
		StateRegistration   string    `json:"state_registration"`
	}

	AllocatingShippingFeeResultItem struct {
		UniqueId              string  `json:"unique_id"`
		ProductId             int64   `json:"product_id"`
		AllocatingShippingFee float64 `json:"allocating_shipping_fee"`
		RetCode               int     `json:"ret_code"`
		Msg                   string  `json:"msg"`
		ErrDetail             string  `json:"err_detail"`
	}

	BatchAllocationESFRespData struct {
		AllocatingShippingFeeResult []*AllocatingShippingFeeResultItem `json:"allocating_shipping_fee_result"`
	}

	BatchAllocationESFResp struct {
		Retcode int                         `json:"retcode"`
		Message string                      `json:"message"`
		Detail  string                      `json:"detail"`
		Data    *BatchAllocationESFRespData `json:"data,omitempty"`
	}

	BatchForecastAllocationESFReq struct {
		Token string                                   `json:"token"`
		Data  []*BatchForecastAllocationESFReqDataItem `json:"data"`
	}

	BatchForecastAllocationESFReqDataItem struct {
		UniqueId            string    `json:"unique_id"`
		ProductID           int64     `json:"product_id"`
		RateID              int       `json:"rate_id"`
		CODAmount           *float64  `json:"cod_amount"`
		COGS                *float64  `json:"cogs"`
		Timestamp           uint32    `json:"timestamp"`
		WMSFlag             int       `json:"wms_flag"`
		PickupLocationIDs   []int     `json:"pickup_location_ids"`
		DeliveryLocationIDs []int     `json:"delivery_location_ids"`
		PickupPostcode      *string   `json:"pickup_postcode"`
		DeliveryPostcode    *string   `json:"delivery_postcode"`
		PickupLongitude     *string   `json:"pickup_longitude"`
		PickupLatitude      *string   `json:"pickup_latitude"`
		DeliverLongitude    *string   `json:"deliver_longitude"`
		DeliverLatitude     *string   `json:"deliver_latitude"`
		SkuInfo             []SkuInfo `json:"sku_info"`
		SellerTaxNumber     string    `json:"seller_tax_number"`
		StateRegistration   string    `json:"state_registration"`
	}
)

func (p *BatchAllocationESFReq) Queries() map[string]string {
	return nil
}

func (p *BatchAllocationESFReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *BatchAllocationESFResp) IsSuccess() bool {
	return p.Retcode == 0 && p.Data != nil
}

func (p *BatchAllocationESFResp) FailMsg() string {
	return p.Message
}

func (p *AllocatingShippingFeeResultItem) IsSuccess() bool {
	return p.RetCode == 0
}

type (
	// CalcLineForecastShippingFeeReq Local smart routing 运费调度预测计算接口
	CalcLineForecastShippingFeeReq struct {
		Token string                                `json:"token"`
		Data  []*CalcLineForecastShippingFeeReqData `json:"data"`
	}
	CalcLineForecastShippingFeeReqData struct {
		LineId              string                                    `json:"line_id"`
		CodAmount           float64                                   `json:"cod_amount"`
		Cogs                float64                                   `json:"cogs"`
		CreateOrderTime     uint32                                    `json:"create_order_time"`
		Timestamp           uint32                                    `json:"timestamp"`
		WmsFlag             uint8                                     `json:"wms_flag"`
		DgFlag              uint8                                     `json:"dg_flag"`
		Direction           uint8                                     `json:"direction"` // 1:正向订单 2:逆向订单
		PickupLocationIds   []int                                     `json:"pickup_location_ids"`
		DeliveryLocationIds []int                                     `json:"delivery_location_ids"`
		PickupPostcode      string                                    `json:"pickup_postcode"`
		DeliveryPostcode    string                                    `json:"delivery_postcode"`
		ShipmentType        uint8                                     `json:"shipment_type"` // 1:Pickup 2:Drop off
		SkuInfo             []*CalcLineForecastShippingFeeReqDataItem `json:"sku_info"`
		ForecastType        int                                       `json:"forecast_type"` // 1:BR 91003 2:MY Five Leg
		PaymentType         int                                       `json:"payment_type"`  // 0:COD 1:Non-COD
	}
	CalcLineForecastShippingFeeReqDataItem struct {
		ItemID     uint64  `json:"item_id"`
		ModelID    uint64  `json:"model_id"`
		CategoryID uint32  `json:"category_id"`
		Weight     float32 `json:"weight"`
		Height     float32 `json:"height"`
		Width      float32 `json:"width"`
		Length     float32 `json:"length"`
		Quantity   uint32  `json:"quantity"`
	}

	CalcLineForecastShippingFeeResp struct {
		Retcode int                                        `json:"retcode"`
		Message string                                     `json:"message"`
		Detail  string                                     `json:"detail"`
		Data    []*CalcLineForecastShippingFeeRespDataItem `json:"data,omitempty"`
	}

	CalcLineForecastShippingFeeRespDataItem struct {
		Retcode           int     `json:"ret_code"`
		Message           string  `json:"msg"`
		LineId            string  `json:"line_id"`
		RateChannelId     int     `json:"rate_channel_id"`
		ShippingFeeAmount float64 `json:"shipping_fee_amount"`
		ErrDetail         string  `json:"err_detail"`
	}
)

func (c *CalcLineForecastShippingFeeReq) Queries() map[string]string {
	return nil
}

func (c *CalcLineForecastShippingFeeReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(c)
}

func (c *CalcLineForecastShippingFeeResp) IsSuccess() bool {
	return c.Retcode == 0
}

func (c *CalcLineForecastShippingFeeResp) FailMsg() string {
	return c.Message
}

func (c *CalcLineForecastShippingFeeRespDataItem) GetShippingFeeAmount(ctx context.Context) float64 {
	if c.Retcode != 0 {
		logger.CtxLogInfof(ctx, "Get line shipping fee fail|line=%v,rate api message=%v", c.LineId, c.Message)
		return UnknownShippingFee
	}

	return c.ShippingFeeAmount
}

func (p *BatchForecastAllocationESFReq) Queries() map[string]string {
	return nil
}

func (p *BatchForecastAllocationESFReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

type (
	AllocatingRateFeeListReq struct {
		ChannelIds *string `json:"channel_ids"`
		WmsFlag    *int    `json:"wms_flag"`
		Status     *string `json:"status"`
	}

	AllocatingRateFeeListResp struct {
		Retcode int                    `json:"retcode"`
		Message string                 `json:"message"`
		Data    *AllocatingRateFeeData `json:"data,omitempty"`
	}

	AllocatingRateFeeData struct {
		List []*AllocatingRateFee `json:"list"`
	}

	AllocatingRateFee struct {
		ChannelId   int    `json:"channel_id"`
		Status      int    `json:"status"`
		RateId      int    `json:"rate_id"`
		ChannelName string `json:"channel_name"`
		Country     string `json:"country"`
		WmsFlag     int    `json:"wms_flag"`
	}
)

func (p *AllocatingRateFeeListReq) Queries() map[string]string {
	queryMap := make(map[string]string)
	if p.Status != nil {
		queryMap["status"] = *p.Status
	}
	if p.ChannelIds != nil {
		queryMap["channel_ids"] = *p.ChannelIds
	}
	if p.WmsFlag != nil {
		queryMap["wms_flag"] = strconv.Itoa(*p.WmsFlag)
	}
	return queryMap
}

func (p *AllocatingRateFeeListReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *AllocatingRateFeeListResp) IsSuccess() bool {
	return p.Retcode == 0 && p.Data != nil
}

func (p *AllocatingRateFeeListResp) FailMsg() string {
	return p.Message
}
