package chargeclient

import (
	"context"
	"errors"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	chargepb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v16/logistics-charge-platform/go"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
)

func TestChargeApiImpl_BatchAllocatingESFWithGrpc(t *testing.T) {
	ctx := context.Background()
	var r ChargeApiImpl
	var patch *gomonkey.Patches
	testgConf := configutil.InitTest()
	type args struct {
		req *chargeentity.BatchAllocationESFReq
	}
	tests := []struct {
		name    string
		args    args
		want    *chargeentity.BatchAllocationESFResp
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: BatchAllocatingESFWithGrpc error",
			args:    args{req: &chargeentity.BatchAllocationESFReq{}},
			want:    nil,
			wantErr: srerr.New(srerr.ChargeApiError, nil, "BatchAllocatingESFWithGrpc error, err_msg=%v", errors.New("mock BatchAllocatingESFWithGrpc error")),
			setup: func() {
				patch = gomonkey.ApplyFunc(client.GrpcInvoke, func(ctx context.Context, systemCode string, schemaID string, grpcService client.GrpcService, arg interface{}, reply interface{}) error {
					return errors.New("mock BatchAllocatingESFWithGrpc error")
				})
				testgConf.ChargeGrpcConf = configutil.GrpcConf{}
			},
		},
		{
			name:    "case 2: CalcEsfWeightWithGrpc error, invalid response from esf",
			args:    args{req: &chargeentity.BatchAllocationESFReq{}},
			want:    nil,
			wantErr: srerr.New(srerr.ChargeApiError, nil, "CalcEsfWeightWithGrpc error, invalid response from esf"),
			setup: func() {
				patch = gomonkey.ApplyFunc(client.GrpcInvoke, func(ctx context.Context, systemCode string, schemaID string, grpcService client.GrpcService, arg interface{}, reply interface{}) error {
					return nil
				})
				testgConf.ChargeGrpcConf = configutil.GrpcConf{}
			},
		},
		{
			name: "case 3: retCode == 0",
			args: args{req: &chargeentity.BatchAllocationESFReq{}},
			want: &chargeentity.BatchAllocationESFResp{
				Data: &chargeentity.BatchAllocationESFRespData{},
			},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(client.GrpcInvoke, func(ctx context.Context, systemCode string, schemaID string, grpcService client.GrpcService, arg interface{}, reply interface{}) error {
					if ptr, ok := reply.(*chargepb.BatchAllocationShippingFeeResp); ok {
						if ptr != nil {
							*ptr = chargepb.BatchAllocationShippingFeeResp{
								RespHeader: &chargepb.RateRespHeader{},
							}
						}
					}
					return nil
				})
				testgConf.ChargeGrpcConf = configutil.GrpcConf{}
			},
		},
		{
			name:    "case 4: chargeApi retCode ！= 0",
			args:    args{req: &chargeentity.BatchAllocationESFReq{}},
			want:    nil,
			wantErr: srerr.New(srerr.ChargeApiError, nil, "chargeApi code:-1, err:, detail:"),
			setup: func() {
				patch = gomonkey.ApplyFunc(client.GrpcInvoke, func(ctx context.Context, systemCode string, schemaID string, grpcService client.GrpcService, arg interface{}, reply interface{}) error {
					if ptr, ok := reply.(*chargepb.BatchAllocationShippingFeeResp); ok {
						if ptr != nil {
							*ptr = chargepb.BatchAllocationShippingFeeResp{
								RespHeader: &chargepb.RateRespHeader{
									Retcode: proto.Int32(-1),
								},
							}
						}
					}
					return nil
				})
				testgConf.ChargeGrpcConf = configutil.GrpcConf{}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := r.BatchAllocatingESFWithGrpc(ctx, tt.args.req, chargeentity.SceneAllocating)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
