package chargeclient

import (
	"context"
	"reflect"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	chargepb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v16/logistics-charge-platform/go"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
)

func TestChargeApiImpl_batchAllocatingESFReq(t *testing.T) {
	ctx := context.Background()
	testgConf := configutil.InitTest()
	var patch *gomonkey.Patches
	type args struct {
		req *chargeentity.BatchAllocationESFReq
	}
	tests := []struct {
		name  string
		args  args
		want  *chargepb.BatchAllocationShippingFeeReq
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				req: &chargeentity.BatchAllocationESFReq{
					Data: []*chargeentity.BatchAllocationESFReqDataItem{
						{
							PickupLocationIDs:   []int{1, 2, 3},
							DeliveryLocationIDs: []int{4, 5, 6},
						},
					},
				},
			},
			want: &chargepb.BatchAllocationShippingFeeReq{
				ReqHeader: &chargepb.RateReqHeader{
					RequestId:    proto.String(""),
					Account:      proto.String(""),
					Token:        proto.String(""),
					Timestamp:    proto.Uint32(uint32(recorder.Now(ctx).Unix())),
					CallerIp:     proto.String(objutil.GetLocalIP()),
					DeployRegion: proto.String(envvar.GetCID()),
				},
				BatchCalculateInfo: []*chargepb.SingleAllocationCalculateInfo{
					{
						UniqueId: proto.String(""),
						CalculateInfo: &chargepb.AllocationCalculateInfo{
							ServiceId: proto.String("0"),
							Timestamp: proto.Uint32(0),
							AddCodFee: chargepb.CodFeeFlag(0).Enum(),
							WmsFlag:   chargepb.WmsFlag(0).Enum(),
							DgFlag:    chargepb.DgFlag.Enum(0),
							LocationInfo: &chargepb.CommonLocationInfo{
								Distance: proto.Int32(0),
								PickupLocation: &chargepb.LocationInfo{
									LocationIds: []int64{1, 2, 3},
								},
								DeliverLocation: &chargepb.LocationInfo{
									LocationIds: []int64{4, 5, 6},
								},
							},
							OrderInfo: &chargepb.AllocationOrderInfo{
								OrderActualWeight:     proto.Int32(0),
								OrderVolumetricWeight: proto.Int32(0),
								OrderHeight:           proto.Float64(0.0),
								OrderWidth:            proto.Float64(0.0),
								OrderLength:           proto.Float64(0.0),
								SellerTaxNumber:       proto.String(""),
								StateRegistration:     proto.String(""),
							},
						},
					},
				},
				Scene: chargepb.AllocationScene_AllocationSceneForAllocating.Enum(),
			},
			setup: func() {
				testgConf.ChargeGrpcConf = configutil.GrpcConf{}
			},
		},
	}
	for _, tt := range tests {
		tt.setup()
		t.Run(tt.name, func(t *testing.T) {
			r := &ChargeApiImpl{}
			common.AssertResult(t, r.batchAllocatingESFReq(ctx, tt.args.req, chargeentity.SceneAllocating), tt.want, nil, nil)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestChargeApiImpl_skuInfoFromHttpToGrpc(t *testing.T) {
	type args struct {
		skuInfoList []chargeentity.SkuInfo
	}
	tests := []struct {
		name string
		args args
		want []*chargepb.SkuItem
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				skuInfoList: []chargeentity.SkuInfo{{}},
			},
			want: []*chargepb.SkuItem{
				{
					ItemId:       proto.Uint64(0),
					Quantity:     proto.Uint32(0),
					ItemPriceUsd: proto.Float64(0),
					ItemPrice:    proto.Float64(0),
					ShopId:       proto.String(""),
					ItemSizeId:   proto.Int32(0),
					ModelId:      proto.Uint64(0),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChargeApiImpl{}
			common.AssertResult(t, r.skuInfoFromHttpToGrpc(tt.args.skuInfoList), tt.want, nil, nil)
		})
	}
}

func TestChargeApiImpl_batchAllocatingESFGrpcRespToHttp(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	type args struct {
		grpcRsp chargepb.BatchAllocationShippingFeeResp
	}
	tests := []struct {
		name string
		args args
		want chargeentity.BatchAllocationESFResp
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				grpcRsp: chargepb.BatchAllocationShippingFeeResp{
					BatchCalculateResult: []*chargepb.SingleAllocationShippingFeeResp{{}},
				},
			},
			want: chargeentity.BatchAllocationESFResp{
				Data: &chargeentity.BatchAllocationESFRespData{
					AllocatingShippingFeeResult: []*chargeentity.AllocatingShippingFeeResultItem{{}},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChargeApiImpl{}
			if got := r.batchAllocatingESFGrpcRespToHttp(tt.args.grpcRsp); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("batchAllocatingESFGrpcRespToHttp() = %v, want %v", got, tt.want)
			}
		})
	}
}
