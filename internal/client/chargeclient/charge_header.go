package chargeclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"github.com/dgrijalva/jwt-go"
)

const defaultApiTimeout = 6

type ChargeApiHeader struct {
	RequestId string
}

func GenChargeApiHeader(ctx context.Context) client.CommonOption {
	return &ChargeApiHeader{
		RequestId: requestid.GetFromCtx(ctx),
	}
}

func (p *ChargeApiHeader) Header(ctx context.Context) (map[string]string, error) {
	header := make(map[string]string, 2)
	account := configutil.GetChargeApiConfig(ctx).ChargeAccount
	secret := configutil.GetChargeApiConfig(ctx).ChargeSecret
	token := jwt.Token{
		Method: jwt.SigningMethodHS256,
		Header: map[string]interface{}{
			"account": account,
			"typ":     "JWT",
			"alg":     jwt.SigningMethodHS256.Alg(),
			"optr":    "sls-ops",
		},
		Claims: p.getJwtData(ctx),
	}

	tokens, err := token.SignedString([]byte(secret))
	if err != nil {
		return nil, err
	}
	header["jwt-token"] = tokens
	header["request-id"] = p.RequestId
	return header, nil

}

func (p *ChargeApiHeader) getJwtData(ctx context.Context) jwtutil.Info {
	jwtData := jwtutil.JwtData{
		User: jwtutil.JwtUser{
			Name:  "ReconSync",
			Email: "<EMAIL>",
		},
		Entity: jwtutil.JwtEntity{
			Country:  envvar.GetCID(),
			Name:     envvar.GetCID(),
			TimeZone: 7,
		},
	}
	info := jwtutil.Info{
		Exp:  recorder.Now(ctx).Unix() + 10000,
		Info: jwtData,
	}
	return info
}

func (p *ChargeApiHeader) SecondTimeout(ctx context.Context) int {
	return defaultApiTimeout
}
