package chargeclient

import (
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	chargepb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v16/logistics-charge-platform/go"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

func (r *ChargeApiImpl) transferBatchForecastAllocationShippingFeeRequestHttpToCharge(ctx context.Context, data *chargeentity.BatchForecastAllocationESFReq) *chargepb.BatchForecastAllocationShippingFeeReq {
	chargeReq := &chargepb.BatchForecastAllocationShippingFeeReq{
		ReqHeader:          r.getChargeReqHeader(ctx),
		BatchCalculateInfo: make([]*chargepb.SingleForecastAllocationCalculateInfo, 0),
	}
	for _, v := range data.Data {
		codFeeFlag := chargepb.CodFeeFlag(0)
		wmsFlag := chargepb.WmsFlag(v.WMSFlag)
		orderInfo := genForecastAllocationOrderInfo(v)
		pickupLocationInfo := locationInfo2ChargePbLocationInfo(objutil.IntToInt64Slice(v.PickupLocationIDs), v.PickupLongitude, v.PickupLatitude, v.PickupPostcode)
		deliverLocationInfo := locationInfo2ChargePbLocationInfo(objutil.IntToInt64Slice(v.DeliveryLocationIDs), v.DeliverLongitude, v.DeliverLatitude, v.DeliveryPostcode)
		tmp := &chargepb.SingleForecastAllocationCalculateInfo{
			UniqueId: proto.String(v.UniqueId),
			CalculateInfo: &chargepb.AllocationCalculateInfo{
				ServiceId:  proto.String(strconv.FormatInt(v.ProductID, 10)),
				Timestamp:  proto.Uint32(v.Timestamp),
				AddCodFee:  &codFeeFlag,
				WmsFlag:    &wmsFlag,
				DgFlag:     chargepb.DgFlag.Enum(0),
				ShopTypeId: proto.Int32(0),
				LocationInfo: &chargepb.CommonLocationInfo{
					PickupLocation:  pickupLocationInfo,
					DeliverLocation: deliverLocationInfo,
				},
				OrderInfo: orderInfo,
				SkuInfo:   SkuItem2ChargePbSku(v.SkuInfo),
			},
		}
		if v.RateID != 0 {
			tmp.RateId = proto.Uint64(uint64(v.RateID))
		}
		chargeReq.BatchCalculateInfo = append(chargeReq.BatchCalculateInfo, tmp)
	}

	return chargeReq
}

func genForecastAllocationOrderInfo(data *chargeentity.BatchForecastAllocationESFReqDataItem) *chargepb.AllocationOrderInfo {
	return &chargepb.AllocationOrderInfo{
		CodAmount:             data.CODAmount,
		Cogs:                  data.COGS,
		OrderActualWeight:     proto.Int32(0),
		OrderVolumetricWeight: proto.Int32(0),
		OrderHeight:           proto.Float64(0),
		OrderLength:           proto.Float64(0),
		OrderWidth:            proto.Float64(0),
		SellerTaxNumber:       proto.String(data.SellerTaxNumber),
		StateRegistration:     proto.String(data.StateRegistration),
	}
}

func locationInfo2ChargePbLocationInfo(locationIDs []int64, longitude, latitude, postcode *string) *chargepb.LocationInfo {
	return &chargepb.LocationInfo{
		LocationIds: locationIDs,
		Longitude:   longitude,
		Latitude:    latitude,
		Postcode:    postcode,
	}
}

func SkuItem2ChargePbSku(skuInfo []chargeentity.SkuInfo) []*chargepb.SkuItem {
	newSkuInfo := make([]*chargepb.SkuItem, len(skuInfo))
	for i := range skuInfo {
		newSkuInfo[i] = &chargepb.SkuItem{
			ItemId:       &skuInfo[i].ItemID,
			CategoryId:   skuInfo[i].CategoryID,
			ModelId:      proto.Uint64(skuInfo[i].ModelID),
			Weight:       skuInfo[i].Weight,
			Quantity:     proto.Uint32(uint32(skuInfo[i].Quantity)),
			Length:       skuInfo[i].Length,
			Width:        skuInfo[i].Width,
			Height:       skuInfo[i].Height,
			ItemPriceUsd: &skuInfo[i].ItemPriceUSD,
			ItemPrice:    &skuInfo[i].ItemPrice,
			ShopId:       &skuInfo[i].ShopID,
			ItemSizeId:   proto.Int32(int32(skuInfo[i].GetItemSizeId())),
		}
	}

	return newSkuInfo
}

func (r *ChargeApiImpl) transferBatchForecastAllocationShippingFeeResponseChargeToHttp(chargeResp *chargepb.BatchForecastAllocationShippingFeeResp) *chargeentity.BatchAllocationESFResp {
	if chargeResp == nil {
		return nil
	}
	resp := &chargeentity.BatchAllocationESFResp{}
	resp.Data = &chargeentity.BatchAllocationESFRespData{}
	for _, singleResp := range chargeResp.GetBatchCalculateResult() {
		productId, err := strconv.ParseInt(singleResp.GetServiceId(), 10, 64)
		if err != nil {
			productId = 0
		}
		resp.Data.AllocatingShippingFeeResult = append(resp.Data.AllocatingShippingFeeResult, &chargeentity.AllocatingShippingFeeResultItem{
			UniqueId:              singleResp.GetUniqueId(),
			ProductId:             productId,
			AllocatingShippingFee: singleResp.GetAllocationShippingFee(),
			RetCode:               int(singleResp.GetRetCode()),
			Msg:                   singleResp.GetMessage(),
			ErrDetail:             singleResp.GetErrDetail(),
		})
	}
	return resp
}

// convertAllocationScene 将业务层的场景枚举转换为协议层枚举
// 在这里进行协议适配，保持业务层的纯净性
func convertAllocationScene(scene chargeentity.AllocationScene) chargepb.AllocationScene {
	if scene == chargeentity.SceneForecast {
		return chargepb.AllocationScene_AllocationSceneForForcast
	}

	return chargepb.AllocationScene_AllocationSceneForAllocating
}

func (r *ChargeApiImpl) batchAllocatingESFReq(ctx context.Context, req *chargeentity.BatchAllocationESFReq, scene chargeentity.AllocationScene) *chargepb.BatchAllocationShippingFeeReq {
	batchCalculateInfoList := make([]*chargepb.SingleAllocationCalculateInfo, 0)
	for _, data := range req.Data {
		pickupLocationIDs := make([]int64, 0)
		for _, pickupLocationID := range data.PickupLocationIDs {
			pickupLocationIDs = append(pickupLocationIDs, int64(pickupLocationID))
		}
		deliveryLocationIDs := make([]int64, 0)
		for _, deliveryLocationID := range data.DeliveryLocationIDs {
			deliveryLocationIDs = append(deliveryLocationIDs, int64(deliveryLocationID))
		}
		pickupLocation := &chargepb.LocationInfo{
			LocationIds: pickupLocationIDs,
			Postcode:    data.PickupPostcode,
			Longitude:   data.PickupLongitude,
			Latitude:    data.PickupLatitude,
		}
		deliverLocation := &chargepb.LocationInfo{
			LocationIds: deliveryLocationIDs,
			Postcode:    data.DeliveryPostcode,
			Longitude:   data.DeliverLongitude,
			Latitude:    data.DeliverLatitude,
		}
		commonLocationInfo := &chargepb.CommonLocationInfo{
			Distance:        proto.Int32(0),
			PickupLocation:  pickupLocation,
			DeliverLocation: deliverLocation,
		}
		orderInfo := &chargepb.AllocationOrderInfo{
			OrderActualWeight:     proto.Int32(0),
			OrderVolumetricWeight: proto.Int32(0),
			OrderHeight:           proto.Float64(0.0),
			OrderWidth:            proto.Float64(0.0),
			OrderLength:           proto.Float64(0.0),
			SellerTaxNumber:       proto.String(data.SellerTaxNumber),
			StateRegistration:     proto.String(data.StateRegistration),
			CodAmount:             data.CODAmount,
			Cogs:                  data.COGS,
		}
		skuItemList := r.skuInfoFromHttpToGrpc(data.SkuInfo)
		calculateInfo := &chargepb.AllocationCalculateInfo{
			ServiceId:    proto.String(fmt.Sprintf("%d", data.ProductID)),
			Timestamp:    proto.Uint32(data.Timestamp),
			AddCodFee:    chargepb.CodFeeFlag(data.AddCodFee).Enum(),
			WmsFlag:      chargepb.WmsFlag(data.WMSFlag).Enum(),
			DgFlag:       chargepb.DgFlag.Enum(0),
			LocationInfo: commonLocationInfo,
			OrderInfo:    orderInfo,
			SkuInfo:      skuItemList,
		}
		batchCalculateInfo := &chargepb.SingleAllocationCalculateInfo{
			UniqueId:      proto.String(data.UniqueId),
			CalculateInfo: calculateInfo,
		}
		batchCalculateInfoList = append(batchCalculateInfoList, batchCalculateInfo)
	}

	reqParam := &chargepb.BatchAllocationShippingFeeReq{
		ReqHeader:          r.getChargeReqHeader(ctx),
		BatchCalculateInfo: batchCalculateInfoList,
		Scene:              convertAllocationScene(scene).Enum(),
	}

	return reqParam
}

func (r *ChargeApiImpl) lineShippingFeeReq(ctx context.Context, dataList []*chargeentity.CalcLineForecastShippingFeeReqData) *chargepb.BatchCalcForecastShippingFeeReq {
	// 场景参数，Charge需求区分场景，仅用作监控告警用途，不影响运费计算逻辑
	// 默认赋值为线上Allocate，判断到Forecast（ctx信息传递）再重新赋值
	allocationScene := chargepb.AllocationScene_AllocationSceneForAllocating.Enum()
	if isForecast, _ := forecast.IsForecast(ctx); isForecast {
		allocationScene = chargepb.AllocationScene_AllocationSceneForForcast.Enum()
	}

	batchCalcInfoList := make([]*chargepb.SingleForecastShippingFeeCalcInfo, 0, len(dataList))
	for _, data := range dataList {
		pickupLocationIDs := make([]int64, 0)
		for _, pickupLocationID := range data.PickupLocationIds {
			pickupLocationIDs = append(pickupLocationIDs, int64(pickupLocationID))
		}
		deliveryLocationIDs := make([]int64, 0)
		for _, deliveryLocationID := range data.DeliveryLocationIds {
			deliveryLocationIDs = append(deliveryLocationIDs, int64(deliveryLocationID))
		}
		pickupLocation := &chargepb.LocationInfo{
			Longitude:   proto.String(""),
			Latitude:    proto.String(""),
			LocationIds: pickupLocationIDs,
			Postcode:    proto.String(data.PickupPostcode),
		}
		deliverLocation := &chargepb.LocationInfo{
			LocationIds: deliveryLocationIDs,
			Postcode:    proto.String(data.DeliveryPostcode),
		}
		commonLocationInfo := &chargepb.CommonLocationInfo{
			Distance:        proto.Int32(0),
			PickupLocation:  pickupLocation,
			DeliverLocation: deliverLocation,
		}

		skuItemList := make([]*chargepb.ForecastSkuItem, 0)
		for _, skuInfo := range data.SkuInfo {
			skuItem := &chargepb.ForecastSkuItem{
				ItemId:       proto.Uint64(skuInfo.ItemID),
				ModelId:      proto.Uint64(skuInfo.ModelID),
				CategoryId:   proto.Uint64(uint64(skuInfo.CategoryID)),
				Weight:       proto.Float64(float64(skuInfo.Weight)),
				Quantity:     proto.Uint32(skuInfo.Quantity),
				Length:       proto.Float64(float64(skuInfo.Length)),
				Width:        proto.Float64(float64(skuInfo.Width)),
				Height:       proto.Float64(float64(skuInfo.Height)),
				ItemPrice:    proto.Float64(0.0),
				ItemPriceUsd: proto.Float64(0.0),
				ShopId:       proto.String(""),
			}
			skuItemList = append(skuItemList, skuItem)
		}

		batchCalcInfo := &chargepb.SingleForecastShippingFeeCalcInfo{
			LineId:          proto.String(data.LineId),
			CodAmount:       proto.Float64(data.CodAmount),
			Cogs:            proto.Float64(data.Cogs),
			CreateOrderTime: proto.Uint32(data.CreateOrderTime),
			Timestamp:       proto.Uint32(data.Timestamp),
			WmsFlag:         chargepb.WmsFlag(data.WmsFlag).Enum(),
			DgFlag:          chargepb.DgFlag(data.DgFlag).Enum(),
			LocationInfo:    commonLocationInfo,
			ShipmentType:    chargepb.ShipmentType(data.ShipmentType).Enum(),
			SkuInfo:         skuItemList,
			Direction:       chargepb.Direction(data.Direction).Enum(),
			ForecastType:    chargepb.ForecastType(data.ForecastType).Enum(),
			PaymentType:     chargepb.PaymentType(data.PaymentType).Enum(),
			MerchantRegion:  proto.String(envvar.GetCID()),
			AllocationScene: allocationScene,
		}
		batchCalcInfoList = append(batchCalcInfoList, batchCalcInfo)
	}

	//
	reqParam := &chargepb.BatchCalcForecastShippingFeeReq{
		ReqHeader:     r.getChargeReqHeader(ctx),
		BatchCalcInfo: batchCalcInfoList,
	}

	return reqParam
}

func (r *ChargeApiImpl) batchCalcLineForecastShippingFeeRespGrpcToHttp(grpcRsp chargepb.BatchCalcForecastShippingFeeResp) *chargeentity.CalcLineForecastShippingFeeResp {
	calcLineForecastShippingFeeRespDataItemList := make([]*chargeentity.CalcLineForecastShippingFeeRespDataItem, 0)
	for _, grpcRspItem := range grpcRsp.BatchCalculateResult {
		calcLineForecastShippingFeeRespDataItem := &chargeentity.CalcLineForecastShippingFeeRespDataItem{
			Retcode:           int(grpcRspItem.GetRetCode()),
			Message:           grpcRspItem.GetMsg(),
			LineId:            grpcRspItem.GetLineId(),
			RateChannelId:     int(grpcRspItem.GetRateChannelId()),
			ShippingFeeAmount: grpcRspItem.GetShippingFeeAmount(),
			ErrDetail:         grpcRspItem.GetErrDetail(),
		}
		calcLineForecastShippingFeeRespDataItemList = append(calcLineForecastShippingFeeRespDataItemList, calcLineForecastShippingFeeRespDataItem)
		// 上报渠道维度的运费计算成功率
		prometheusutil.ReportRoutingFeeSuccessRate(calcLineForecastShippingFeeRespDataItem.LineId, calcLineForecastShippingFeeRespDataItem.RateChannelId, calcLineForecastShippingFeeRespDataItem.Retcode)
	}
	httpRsp := &chargeentity.CalcLineForecastShippingFeeResp{
		Retcode: int(grpcRsp.GetRespHeader().GetRetcode()),
		Message: grpcRsp.GetRespHeader().GetMessage(),
		Detail:  grpcRsp.GetRespHeader().GetErrDetail(),
		Data:    calcLineForecastShippingFeeRespDataItemList,
	}

	return httpRsp
}

func (r *ChargeApiImpl) getChargeReqHeader(ctx context.Context) *chargepb.RateReqHeader {
	var requestId string
	if _, reqId, ok := client.GrpcMockTarget(ctx, constant.SystemChargeApi); ok {
		requestId = reqId
	} else if _, reqIdV2, okV2 := client.GrpcMockTarget(ctx, constant.SystemChargeCheckoutGrpc); okV2 {
		requestId = reqIdV2
	} else {
		requestId = requestid.GetFromCtx(ctx)
	}
	timestamp := uint32(recorder.Now(ctx).Unix())
	chargeGrpcConf := configutil.GetChargeGrpcConf(ctx)

	return &chargepb.RateReqHeader{
		RequestId:    &requestId,
		Account:      proto.String(chargeGrpcConf.Account),
		Token:        proto.String(chargeGrpcConf.Token),
		DeployRegion: proto.String(envvar.GetCID()),
		Timestamp:    &timestamp,
		CallerIp:     proto.String(objutil.GetLocalIP()),
	}
}

func (r *ChargeApiImpl) batchAllocatingESFGrpcRespToHttp(grpcRsp chargepb.BatchAllocationShippingFeeResp) chargeentity.BatchAllocationESFResp {
	allocatingShippingFeeResultItemList := make([]*chargeentity.AllocatingShippingFeeResultItem, len(grpcRsp.BatchCalculateResult))
	for idx, grpcRspItem := range grpcRsp.BatchCalculateResult {
		productId, _ := strconv.ParseInt(grpcRspItem.GetServiceId(), 10, 64)
		allocatingShippingFeeResultItem := &chargeentity.AllocatingShippingFeeResultItem{
			UniqueId:              grpcRspItem.GetUniqueId(),
			ProductId:             productId,
			AllocatingShippingFee: grpcRspItem.GetAllocationShippingFee(),
			RetCode:               int(grpcRspItem.GetRetCode()),
			Msg:                   grpcRspItem.GetMessage(),
			ErrDetail:             grpcRspItem.GetErrDetail(),
		}
		allocatingShippingFeeResultItemList[idx] = allocatingShippingFeeResultItem
		// 上报product维度的运费计算成功率
		prometheusutil.ReportAllocateFeeSuccessRate(allocatingShippingFeeResultItem.UniqueId, allocatingShippingFeeResultItem.RetCode)
	}
	batchAllocationESFRespData := &chargeentity.BatchAllocationESFRespData{
		AllocatingShippingFeeResult: allocatingShippingFeeResultItemList,
	}
	rsp := chargeentity.BatchAllocationESFResp{
		Retcode: int(grpcRsp.GetRespHeader().GetRetcode()),
		Message: grpcRsp.GetRespHeader().GetMessage(),
		Detail:  grpcRsp.GetRespHeader().GetErrDetail(),
		Data:    batchAllocationESFRespData,
	}
	return rsp
}

func (r *ChargeApiImpl) skuInfoFromHttpToGrpc(skuInfoList []chargeentity.SkuInfo) []*chargepb.SkuItem {
	skuItemList := make([]*chargepb.SkuItem, 0)
	for _, skuInfo := range skuInfoList {
		skuItem := &chargepb.SkuItem{
			ItemId:       proto.Uint64(skuInfo.ItemID),
			ModelId:      proto.Uint64(skuInfo.ModelID),
			CategoryId:   skuInfo.CategoryID,
			Weight:       skuInfo.Weight,
			Quantity:     proto.Uint32(uint32(skuInfo.Quantity)),
			Length:       skuInfo.Length,
			Width:        skuInfo.Width,
			Height:       skuInfo.Height,
			ItemPriceUsd: proto.Float64(skuInfo.ItemPriceUSD),
			ItemPrice:    proto.Float64(skuInfo.ItemPrice),
			ShopId:       proto.String(skuInfo.ShopID),
			ItemSizeId:   proto.Int32(int32(skuInfo.GetItemSizeId())),
		}
		skuItemList = append(skuItemList, skuItem)
	}

	return skuItemList
}
