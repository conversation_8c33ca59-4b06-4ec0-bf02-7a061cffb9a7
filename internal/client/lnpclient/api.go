package lnpclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"net/http"
)

type LnpApi interface {
	SendEmail(ctx context.Context, s3FileUrl string, operatorList []string) *srerr.Error
}

type LnpApiImpl struct {
}

var (
	SendEmail = client.HttpService{
		Endpoint: "/lnp_protobuf.PushService/SendEmail",
		Scene:    client.DR,
		System:   constant.SystemLNP,
	}
)

func NewLnpApiImpl() *LnpApiImpl {
	return &LnpApiImpl{}
}

func (l *LnpApiImpl) SendEmail(ctx context.Context, s3FileUrl string, operatorList []string) *srerr.Error {
	header := LnpHeader(ctx)
	var rsp SendEmailResponse
	err := client.HttpInvoke(ctx, constant.SystemLNP, SendEmail, http.MethodPost, GenerateSendEmailRequest(ctx, s3FileUrl, operatorList), &rsp, header)
	if err != nil {
		return srerr.With(srerr.LpsError, nil, err)
	}
	if !rsp.IsSuccess() {
		logger.CtxLogErrorf(ctx, "send email fail, result=%v", rsp)
	}
	return nil
}

func GenerateSendEmailRequest(ctx context.Context, s3FileUrl string, operatorList []string) *SendEmailRequest {
	emailTemplateConf := configutil.GetPisEmailTemplateConf(ctx)
	return &SendEmailRequest{
		ReqHeader: ReqHeader{
			Timestamp: timeutil.GetCurrentUnixTimeStamp(ctx),
			RequestId: requestid.GetFromCtx(ctx),
		},
		TemplateId:    emailTemplateConf.TemplateId,
		SendingMethod: int64(emailTemplateConf.SendingMethod),
		EmailInfo: EmailInfo{
			Content:   s3FileUrl,
			Receivers: operatorList,
		},
	}
}
