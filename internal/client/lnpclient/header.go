package lnpclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
)

const (
	DefaultTimeout = 6000
)

type lnpHeader struct {
	requestId string
}

func LnpHeader(ctx context.Context) *lnpHeader {
	return &lnpHeader{requestId: requestid.GetFromCtx(ctx)}
}

func (l lnpHeader) Header(ctx context.Context) (map[string]string, error) {
	header := make(map[string]string)
	header["request-id"] = l.requestId
	return header, nil
}

func (l lnpHeader) SecondTimeout(ctx context.Context) int {
	conf := configutil.GetLnpApiConf(ctx)
	if conf.Timeout == 0 {
		return DefaultTimeout
	}
	return int(conf.Timeout)
}
