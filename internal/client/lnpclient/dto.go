package lnpclient

import jsoniter "github.com/json-iterator/go"

type (
	SendEmailRequest struct {
		ReqHeader     ReqHeader `json:"req_header"`
		TemplateId    string    `json:"template_id"`
		SendingMethod int64     `json:"sending_method"`
		EmailInfo     EmailInfo `json:"email_info"`
		Region        string    `json:"region"`
	}

	SendEmailResponse struct {
		RespHeader RespHeader `json:"resp_header"`
	}

	ReqHeader struct {
		Account   string `json:"account"`
		CallerIp  string `json:"caller_ip"`
		RequestId string `json:"request_id"`
		Timestamp int64  `json:"timestamp"`
		Token     string `json:"token"`
	}

	RespHeader struct {
		RequestId string `json:"request_id"`
		Retcode   int    `json:"retcode"`
		Message   string `json:"message"`
	}

	EmailInfo struct {
		Receivers      []string         `json:"receivers"`
		Subject        string           `json:"subject"`
		Content        string           `json:"content"`
		AttachmentList []AttachmentList `json:"attachment_list"`
	}

	AttachmentList struct {
		FileName string `json:"file_name"`
		Url      string `json:"url"`
	}
)

func (a *SendEmailRequest) Queries() map[string]string {
	body := make(map[string]string)
	return body
}

func (a *SendEmailRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(a)
}

func (p *SendEmailResponse) IsSuccess() bool {
	return p != nil && p.RespHeader.Retcode == 0
}

func (p *SendEmailResponse) FailMsg() string {
	if p == nil {
		return ""
	}
	return p.RespHeader.Message
}
