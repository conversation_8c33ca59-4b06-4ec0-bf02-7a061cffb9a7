package lnpclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"testing"
)

func TestLpsApiImpl_SendEmail(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	lpsApiImpl := NewLpsApiImpl()
	err := lpsApiImpl.SendEmail(context.Background(), "123", []string{"<EMAIL>"})
	fmt.Println(err)
}
