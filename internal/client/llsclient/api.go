package llsclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"github.com/gogo/protobuf/proto"
	uuid "github.com/satori/go.uuid"
	"strconv"
)

type LlsApi interface {
	MultiGetResource(ctx context.Context, lineIds, siteIds []string) (*llspb.MultiGetResourceResponse, *srerr.Error)
	GetActualPoints(ctx context.Context, request *llspb.GetActualPointRequest) (*llspb.GetActualPointResponse, *srerr.Error)
	GetDgGroup(ctx context.Context, lineIds []string) (map[string]*llspb.GetDgGroupData, *srerr.Error)
	CheckOrder(ctx context.Context, infos ...*llspb.CheckOrderInfo) (map[string]*llspb.CheckOrderReqItem, *srerr.Error)
}

type LlsApiImpl struct {
}

func NewLlsApiImpl() *LlsApiImpl {
	return &LlsApiImpl{}
}

var (
	MultiGetResource = client.GrpcService{
		OperationID: "MultiGetResource",
		Scene:       client.MultiAlive,
	}
	GetActualPoints = client.GrpcService{
		OperationID: "GetActualPoints",
		Scene:       client.MultiAlive,
	}
	GetDgGroup = client.GrpcService{
		OperationID: "GetDgGroup",
		Scene:       client.MultiAlive,
	}
	CheckOrder = client.GrpcService{
		OperationID: "CheckOrder",
		Scene:       client.MultiAlive,
		System:      constant.SystemLLS,
	}
)

func (l *LlsApiImpl) MultiGetResource(ctx context.Context, lineIds, siteIds []string) (*llspb.MultiGetResourceResponse, *srerr.Error) {
	//conf := configutil.GetLlsGrpcConf()

	reqParam := &llspb.MultiGetResourceRequest{
		ReqHeader: l.getGrpcReqHeader(ctx),
		SiteId:    siteIds,
		LineId:    lineIds,
	}

	//// mock
	//if target, reqId, ok := external_gateway.GrpcMockTarget(ctx, constant.SystemLFS); ok {
	//	conn, err := request.GetGrpcConn(ctx, target)
	//	if err != nil {
	//		logger.CtxLogErrorf(ctx, "MultiGetResource|get_grpc_conn_fail,err=%v", err)
	//		return nil, lpserr.With(errcode.LLSError, nil, err)
	//	}
	//	defer conn.Close()
	//	reqParam.ReqHeader.RequestId = proto.String(reqId)
	//
	//	rsp, err := llspb.NewQueryServiceClient(conn.ClientConn).MultiGetResource(ctx, reqParam)
	//	if err != nil {
	//		logger.CtxLogErrorf(ctx, "MultiGetResource|param:%s,err:%+v", str.JsonString(reqParam), err)
	//		return nil, lpserr.New(errcode.LLSError, nil, "batch check lane serviceable fail, err=%+v", err)
	//	}
	//	logger.CtxLogDebugf(ctx, "MultiGetResource|param:%s,result:%s", str.JsonStringForDebugLog(reqParam), str.JsonStringForDebugLog(rsp))
	//	if rsp.GetRespHeader() == nil || rsp.GetRespHeader().GetRetcode() != 0 {
	//		return nil, lpserr.New(errcode.LLSError, nil, "MultiGetResource fail, invalid response from mock lls")
	//	}
	//	return rsp, nil
	//}

	var rsp llspb.MultiGetResourceResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLLS, "lls_protobuf.QueryService", MultiGetResource, reqParam, &rsp); err != nil {
		return nil, srerr.New(srerr.LlsError, nil, "MultiGetResource fail|invoke grpc fail,err=%v", err)
	}
	if rsp.GetRespHeader() == nil {
		return nil, srerr.New(srerr.LlsError, nil, "MultiGetResource fail|invalid response from lls")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LlsError, nil, "MultiGetResource fail|response get error,err=%s", rsp.GetRespHeader().GetMessage())
	}

	return &rsp, nil
}

func (l *LlsApiImpl) GetActualPoints(ctx context.Context, request *llspb.GetActualPointRequest) (*llspb.GetActualPointResponse, *srerr.Error) {
	//conf := configutil.GetLlsGrpcConf()

	request.ReqHeader = l.getGrpcReqHeader(ctx)
	var rsp llspb.GetActualPointResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLLS, "lls_protobuf.BuildLaneService", GetActualPoints, request, &rsp); err != nil {
		return nil, srerr.New(srerr.LlsError, nil, "GetActualPoints fail|invoke grpc fail,err=%v", err)
	}
	if rsp.GetRespHeader() == nil {
		return &rsp, srerr.New(srerr.LlsError, nil, "GetActualPoints fail|invalid response from lls")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		return &rsp, srerr.New(srerr.LlsError, nil, "GetActualPoints fail|response get error,err=%s", rsp.GetRespHeader().GetMessage())
	}

	return &rsp, nil
}

func (l *LlsApiImpl) GetDgGroup(ctx context.Context, lineIds []string) (map[string]*llspb.GetDgGroupData, *srerr.Error) {
	reqParam := &llspb.DgGroupRequest{
		ReqHeader: l.getGrpcReqHeader(ctx),
		LineIds:   lineIds,
	}

	var rsp llspb.DgGroupResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLLS, "lls_protobuf.QueryService", GetDgGroup, reqParam, &rsp); err != nil {
		return nil, srerr.New(srerr.LlsError, nil, "GetDgGroup fail|invoke grpc fail,err=%v", err)
	}
	if rsp.GetRespHeader() == nil {
		return nil, srerr.New(srerr.LlsError, nil, "GetDgGroup fail|invalid response from lls")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LlsError, nil, "GetDgGroup fail|response get error,err=%s", rsp.GetRespHeader().GetMessage())
	}

	return rsp.GetDgGroups(), nil
}

func (l *LlsApiImpl) getGrpcReqHeader(ctx context.Context) *llspb.ReqHeader {
	requestId := requestid.GetFromCtx(ctx)
	if requestId == "" {
		requestId = uuid.NewV4().String() // nolint
	}
	return &llspb.ReqHeader{
		RequestId:    proto.String(requestId),
		Account:      proto.String(configutil.GetLlsGrpcConf(ctx).Account),
		Token:        proto.String(configutil.GetLlsGrpcConf(ctx).Token),
		Timestamp:    proto.Uint32(uint32(timeutil.GetCurrentUnixTimeStamp(ctx))),
		CallerIp:     proto.String(objutil.GetLocalIP()),
		DeployRegion: proto.String(envvar.GetCID()),
	}
}

func (l *LlsApiImpl) CheckOrder(ctx context.Context, infos ...*llspb.CheckOrderInfo) (map[string]*llspb.CheckOrderReqItem, *srerr.Error) {
	req := &llspb.CheckOrderRequest{
		ReqHeader:      l.getGrpcReqHeader(ctx),
		CheckOrderInfo: infos,
	}
	var rsp llspb.CheckOrderResp
	if err := client.GrpcInvoke(ctx, constant.SystemLLS, "lls_protobuf.OneApiService", CheckOrder, req, &rsp); err != nil {
		return nil, srerr.New(srerr.LlsError, nil, strconv.Itoa(srerr.LlsError.Code()), fmt.Sprintf("batch check_order error, err=%+v", err))
	}
	if rsp.GetRespHeader() == nil {
		return nil, srerr.New(srerr.LlsError, nil, strconv.Itoa(srerr.LlsError.Code()), "batch check_order fail, invalid response from lls")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LlsError, nil, "batch check_order error, err=%+v", rsp.GetRespHeader().GetMessage())
	}
	return rsp.CheckResult, nil
}
