package client

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	grpcPool "github.com/processout/grpc-go-pool"
	"google.golang.org/grpc"
)

var (
	connMap      = map[string]*grpcPool.Pool{}
	_initConn    = 50
	_maxConn     = 50
	_idleTimeout = 60
)

func initGrpcPool(target string) error {
	pool, err := doInitGrpcPool(target)
	if err != nil {
		return err
	}
	connMap[target] = pool
	return nil
}

func doInitGrpcPool(target string) (*grpcPool.Pool, error) {
	var initFail error
	for i := 0; i < 5; i++ {
		pool, err := grpcPool.New(
			func() (*grpc.ClientConn, error) {
				//safeOption := grpc.WithTransportCredentials(credentials.NewTLS(nil)
				safeOption := grpc.WithInsecure()
				return grpc.Dial(
					target,
					safeOption,
					grpc.WithUnaryInterceptor(monitoring.UnaryClientInterceptor()),
				)
			},
			_initConn,
			_maxConn,
			time.Second*time.Duration(_idleTimeout),
			time.Second*time.Duration(_maxConn),
		)
		if err == nil && pool != nil {
			return pool, nil
		}
		initFail = err
	}
	return nil, initFail
}

func getGrpcConn(ctx context.Context, target string) (*grpcPool.ClientConn, error) {
	if pool, ok := connMap[target]; ok {
		cc, err := pool.Get(ctx)
		if err != nil {
			return nil, err
		}
		return cc, nil
	}
	if err := initGrpcPool(target); err != nil {
		return nil, err
	}
	cc, err := connMap[target].Get(ctx)
	if err != nil {
		return nil, err
	}
	return cc, nil
}

func GetGrpcConnWithSecure(ctx context.Context, target string) (*grpc.ClientConn, error) {
	cc, err := getGrpcConn(ctx, target)
	if err != nil {
		return nil, err
	}
	return cc.ClientConn, err
}
