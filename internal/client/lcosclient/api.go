package lcosclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	lcospb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"github.com/gogo/protobuf/proto"
	uuid "github.com/satori/go.uuid"
)

const (
	CheckProductRuleSchemaId = "lcos_protobuf.LcosWeightLimitService"
)

var (
	BatchCheckProductRule = client.GrpcService{
		OperationID: "BatchCheckProductRule",
		Scene:       client.MultiAlive,
		System:      constant.SystemLCOS,
	}
)

type LcosApi interface {
	BatchCheckProductRule(ctx context.Context, productRules []*lcospb.SingleProductRule) (map[string]*lcospb.CheckProductRuleResponse, *srerr.Error)
}

type LcosApiImpl struct {
}

func NewLcosApiImpl() *LcosApiImpl {
	return &LcosApiImpl{}
}

func (l *LcosApiImpl) BatchCheckProductRule(ctx context.Context, productRules []*lcospb.SingleProductRule) (map[string]*lcospb.CheckProductRuleResponse, *srerr.Error) {
	req := &lcospb.BatchCheckProductRuleRequest{
		ReqHeader:    l.getGrpcReqHeader(ctx),
		ProductRules: productRules,
	}
	var rsp lcospb.BatchCheckProductRuleResponse
	if err := client.GrpcInvoke(ctx, constant.SystemLCOS, CheckProductRuleSchemaId, BatchCheckProductRule, req, &rsp); err != nil {
		logger.CtxLogErrorf(ctx, "BatchCheckProductRule error, err=%+v", err)
		return nil, srerr.New(srerr.LcosError, nil, "BatchCheckProductRule error, err_msg=%v", err)
	}
	if rsp.GetRespHeader() == nil {
		return nil, srerr.New(srerr.LcosError, nil, "BatchCheckProductRule fail, invalid response from lcos")
	}
	if rsp.GetRespHeader().GetRetcode() != 0 {
		return nil, srerr.New(srerr.LcosError, nil, "BatchCheckProductRule error, err=%+v", rsp.GetRespHeader().GetMessage())
	}

	return rsp.GetCheckProductRuleResultMap(), nil
}

func (l *LcosApiImpl) getGrpcReqHeader(ctx context.Context) *lcospb.ReqHeader {
	requestId := requestid.GetFromCtx(ctx)
	if requestId == "" {
		requestId = uuid.NewV4().String() // nolint
	}
	return &lcospb.ReqHeader{
		RequestId:    proto.String(requestId),
		Account:      proto.String(configutil.GetLcosGrpcConf(ctx).Account),
		Token:        proto.String(configutil.GetLcosGrpcConf(ctx).Token),
		Timestamp:    proto.Uint32(uint32(timeutil.GetCurrentUnixTimeStamp(ctx))),
		CallerIp:     proto.String(objutil.GetLocalIP()),
		DeployRegion: proto.String(envvar.GetCID()),
	}
}
