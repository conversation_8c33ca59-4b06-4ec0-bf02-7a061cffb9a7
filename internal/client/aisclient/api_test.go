package aisclient

import (
	"context"
	"encoding/base64"
	"errors"
	"io"
	"reflect"
	"testing"

	"git.garena.com/shopee/bg-logistics/algo/sls/allocationopt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/agiledragon/gomonkey/v2"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/zstd"
)

func TestAisApiImpl_ComputeTask(t *testing.T) {
	ctx := context.Background()
	aisApi := &AisApiImpl{}

	var patch *gomonkey.Patches

	tests := []struct {
		name    string
		req     *ComputeTaskRequest
		want    *ComputeTaskResponse
		wantErr *srerr.Error
		setup   func()
	}{
		{
			name: "case 1: marshal request failed",
			req: &ComputeTaskRequest{
				Business:       "test",
				Scenario:       "scenario",
				BusinessTaskID: "task123",
			},
			want:    nil,
			wantErr: srerr.With(srerr.JsonErr, nil, errors.New("marshal error")),
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return nil, errors.New("marshal error")
				})
			},
		},
		{
			name: "case 2: http request failed",
			req: &ComputeTaskRequest{
				Business:       "test",
				Scenario:       "scenario",
				BusinessTaskID: "task123",
			},
			want:    nil,
			wantErr: srerr.With(srerr.DataApiErr, nil, errors.New("http error")),
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return []byte(`{"business":"test"}`), nil
				})
				patch.ApplyFunc(configutil.GetAiApiConf, func(ctx context.Context) configutil.ApiConf {
					return configutil.ApiConf{
						Host:    "http://test.com",
						Timeout: 30,
					}
				})
				patch.ApplyFunc(objutil.Merge, func(args ...string) string {
					return "http://test.com/api/compute_task"
				})
				patch.ApplyFunc(AisHeader, func(ctx context.Context) *aisHeader {
					return &aisHeader{}
				})
				patch.ApplyMethod(reflect.TypeOf(&aisHeader{}), "Header", func(h *aisHeader, ctx context.Context) (map[string]string, error) {
					return map[string]string{"Content-Type": "application/json"}, nil
				})
				patch.ApplyFunc(httputil.PostJson, func(ctx context.Context, url string, body []byte, timeout int, headers map[string]string) ([]byte, error) {
					return nil, errors.New("http error")
				})
			},
		},
		{
			name: "case 3: unmarshal response failed",
			req: &ComputeTaskRequest{
				Business:       "test",
				Scenario:       "scenario",
				BusinessTaskID: "task123",
			},
			want:    nil,
			wantErr: srerr.With(srerr.FormatErr, nil, errors.New("readObjectStart: expect { or n, but found i, error found in #1 byte of ...|invalid jso|..., bigger context ...|invalid json|...")),
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return []byte(`{"business":"test"}`), nil
				})
				patch.ApplyFunc(configutil.GetAiApiConf, func(ctx context.Context) configutil.ApiConf {
					return configutil.ApiConf{
						Host:    "http://test.com",
						Timeout: 30,
					}
				})
				patch.ApplyFunc(objutil.Merge, func(args ...string) string {
					return "http://test.com/api/compute_task"
				})
				patch.ApplyFunc(AisHeader, func(ctx context.Context) *aisHeader {
					return &aisHeader{}
				})
				patch.ApplyMethod(reflect.TypeOf(&aisHeader{}), "Header", func(h *aisHeader, ctx context.Context) (map[string]string, error) {
					return map[string]string{"Content-Type": "application/json"}, nil
				})
				patch.ApplyFunc(httputil.PostJson, func(ctx context.Context, url string, body []byte, timeout int, headers map[string]string) ([]byte, error) {
					return []byte(`invalid json`), nil
				})
			},
		},
		{
			name: "case 4: successful request",
			req: &ComputeTaskRequest{
				Business:       "test",
				Scenario:       "scenario",
				BusinessTaskID: "task123",
				Input:          "input_data",
			},
			want: &ComputeTaskResponse{
				RetCode: 0,
				Message: "success",
				Data: ComputeTaskResponseData{
					TaskID:     "task_123",
					TaskStatus: 1,
					Output:     "output_data",
				},
			},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return []byte(`{"business":"test","scenario":"scenario","business_task_id":"task123","input":"input_data"}`), nil
				})
				patch.ApplyFunc(configutil.GetAiApiConf, func(ctx context.Context) configutil.ApiConf {
					return configutil.ApiConf{
						Host:    "http://test.com",
						Timeout: 30,
					}
				})
				patch.ApplyFunc(objutil.Merge, func(args ...string) string {
					return "http://test.com/api/compute_task"
				})
				patch.ApplyFunc(AisHeader, func(ctx context.Context) *aisHeader {
					return &aisHeader{}
				})
				patch.ApplyMethod(reflect.TypeOf(&aisHeader{}), "Header", func(h *aisHeader, ctx context.Context) (map[string]string, error) {
					return map[string]string{"Content-Type": "application/json"}, nil
				})
				patch.ApplyFunc(httputil.PostJson, func(ctx context.Context, url string, body []byte, timeout int, headers map[string]string) ([]byte, error) {
					response := `{
						"retcode": 0,
						"message": "success",
						"data": {
							"task_id": "task_123",
							"task_status": 1,
							"output": "output_data"
						}
					}`
					return []byte(response), nil
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer func() {
				if patch != nil {
					patch.Reset()
				}
			}()

			got, err := aisApi.ComputeTask(ctx, tt.req)
			common.AssertResult(t, got, tt.want, err, tt.wantErr)
			tt.req.Queries()
			tt.req.Entity()
			if got != nil {
				got.IsSuccess()
				got.FailMsg()
			}
		})
	}
}

func TestEncodeInput(t *testing.T) {
	var patch *gomonkey.Patches

	tests := []struct {
		name    string
		input   interface{}
		want    string
		wantErr error
		setup   func()
	}{
		{
			name:    "case 1: marshal JSON failed",
			input:   make(chan int), // 无法序列化的类型
			want:    "",
			wantErr: errors.New("failed to marshal JSON: marshal error"),
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return nil, errors.New("marshal error")
				})
			},
		},
		{
			name:    "case 2: create zstd encoder failed",
			input:   map[string]string{"key": "value"},
			want:    "",
			wantErr: errors.New("failed to create zstd encoder: zstd encoder error"),
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return []byte(`{"key":"value"}`), nil
				})
				patch.ApplyFunc(zstd.NewWriter, func(w io.Writer, opts ...zstd.EOption) (*zstd.Encoder, error) {
					return nil, errors.New("zstd encoder error")
				})
			},
		},
		{
			name:    "case 3: successful encoding",
			input:   map[string]string{"key": "value"},
			want:    "Y29tcHJlc3NlZF9kYXRh",
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(jsoniter.Marshal, func(v interface{}) ([]byte, error) {
					return []byte(`{"key":"value"}`), nil
				})

				// Mock zstd.NewWriter to return a real encoder
				encoder, _ := zstd.NewWriter(nil)
				patch.ApplyFunc(zstd.NewWriter, func(w io.Writer, opts ...zstd.EOption) (*zstd.Encoder, error) {
					return encoder, nil
				})

				// Mock encoder.EncodeAll 方法
				patch.ApplyMethod(reflect.TypeOf(encoder), "EncodeAll", func(e *zstd.Encoder, src, dst []byte) []byte {
					return []byte("compressed_data")
				})

				patch.ApplyFunc(base64.StdEncoding.EncodeToString, func(src []byte) string {
					return "base64_encoded_string"
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer func() {
				if patch != nil {
					patch.Reset()
				}
			}()

			got, err := encodeInput(tt.input)

			common.AssertResult(t, got, tt.want, srerr.New(srerr.JsonErr, err, ""), srerr.New(srerr.JsonErr, tt.wantErr, ""))
		})
	}
}

func TestDecodeResponseDataToAllocationResult(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches

	tests := []struct {
		name        string
		encodedData string
		want        *allocationopt.AllocationResult
		wantErr     error
		setup       func()
	}{
		{
			name:        "case 1: base64 decode failed",
			encodedData: "invalid_base64_data!@#",
			want:        nil,
			wantErr:     errors.New("failed to decode base64: illegal base64 data at input byte 7"),
			setup: func() {
				patch = gomonkey.ApplyFunc(base64.StdEncoding.DecodeString, func(s string) ([]byte, error) {
					return nil, errors.New("illegal base64 data at input byte 18")
				})
			},
		},
		{
			name:        "case 2: create zstd decoder failed",
			encodedData: "dGVzdF9kYXRh", // valid base64
			want:        nil,
			wantErr:     errors.New("failed to create zstd decoder: zstd decoder error"),
			setup: func() {
				patch = gomonkey.ApplyFunc(base64.StdEncoding.DecodeString, func(s string) ([]byte, error) {
					return []byte("test_data"), nil
				})
				patch.ApplyFunc(zstd.NewReader, func(r io.Reader, opts ...zstd.DOption) (*zstd.Decoder, error) {
					return nil, errors.New("zstd decoder error")
				})
			},
		},
		{
			name:        "case 3: zstd decompress failed",
			encodedData: "dGVzdF9kYXRh", // valid base64
			want:        nil,
			wantErr:     errors.New("failed to decompress data: decompress error"),
			setup: func() {
				patch = gomonkey.ApplyFunc(base64.StdEncoding.DecodeString, func(s string) ([]byte, error) {
					return []byte("test_data"), nil
				})

				decoder, _ := zstd.NewReader(nil)
				patch.ApplyFunc(zstd.NewReader, func(r io.Reader, opts ...zstd.DOption) (*zstd.Decoder, error) {
					return decoder, nil
				})
				patch.ApplyMethod(reflect.TypeOf(decoder), "DecodeAll", func(d *zstd.Decoder, input, dst []byte) ([]byte, error) {
					return nil, errors.New("decompress error")
				})
			},
		},
		{
			name:        "case 4: JSON unmarshal failed",
			encodedData: "dGVzdF9kYXRh", // valid base64
			want:        nil,
			wantErr:     errors.New("failed to unmarshal JSON to AllocationResult: unmarshal error"),
			setup: func() {
				patch = gomonkey.ApplyFunc(base64.StdEncoding.DecodeString, func(s string) ([]byte, error) {
					return []byte("test_data"), nil
				})

				decoder, _ := zstd.NewReader(nil)
				patch.ApplyFunc(zstd.NewReader, func(r io.Reader, opts ...zstd.DOption) (*zstd.Decoder, error) {
					return decoder, nil
				})
				patch.ApplyMethod(reflect.TypeOf(decoder), "DecodeAll", func(d *zstd.Decoder, input, dst []byte) ([]byte, error) {
					return []byte("invalid_json"), nil
				})
				patch.ApplyFunc(jsoniter.Unmarshal, func(data []byte, v interface{}) error {
					return errors.New("unmarshal error")
				})
			},
		},
		{
			name:        "case 5: successful decode",
			encodedData: "dGVzdF9kYXRh", // valid base64
			want:        &allocationopt.AllocationResult{},
			wantErr:     nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(base64.StdEncoding.DecodeString, func(s string) ([]byte, error) {
					return []byte("test_data"), nil
				})

				decoder, _ := zstd.NewReader(nil)
				patch.ApplyFunc(zstd.NewReader, func(r io.Reader, opts ...zstd.DOption) (*zstd.Decoder, error) {
					return decoder, nil
				})
				patch.ApplyMethod(reflect.TypeOf(decoder), "DecodeAll", func(d *zstd.Decoder, input, dst []byte) ([]byte, error) {
					return []byte(`{}`), nil
				})
				patch.ApplyFunc(jsoniter.Unmarshal, func(data []byte, v interface{}) error {
					// 模拟成功解析到 AllocationResult
					return nil
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer func() {
				if patch != nil {
					patch.Reset()
				}
			}()

			got, err := decodeResponseDataToAllocationResult(ctx, tt.encodedData)

			common.AssertResult(t, got, tt.want, srerr.New(srerr.JsonErr, err, ""), srerr.New(srerr.JsonErr, tt.wantErr, ""))
		})
	}
}

func TestAisApiImpl_ComputeTaskWithEncodedInput(t *testing.T) {
	ctx := context.Background()
	aisApi := &AisApiImpl{}
	businessTaskID := "test_task_123"

	var patch *gomonkey.Patches

	tests := []struct {
		name    string
		req     *algorithm_client.BatchAllocateReqBo
		want    *algorithm_client.BatchAllocateRespBo
		wantErr *srerr.Error
		setup   func()
	}{
		{
			name:    "case 1: encode input failed",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.New(srerr.JsonErr, businessTaskID, "failed to encode input data: encode error"),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "", errors.New("encode error")
				})
			},
		},
		{
			name:    "case 2: ComputeTask failed",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.With(srerr.DataApiErr, nil, errors.New("compute task error")),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return nil, srerr.With(srerr.DataApiErr, nil, errors.New("compute task error"))
				})
			},
		},
		{
			name:    "case 3: response not success",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.New(srerr.DataApiErr, businessTaskID, "AI service response failed: service error"),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return &ComputeTaskResponse{
						RetCode: 1,
						Message: "service error",
						Data: ComputeTaskResponseData{
							Output: "",
						},
					}, nil
				})
			},
		},
		{
			name:    "case 4: response success but no output",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.New(srerr.DataApiErr, businessTaskID, "AI service response failed: "),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return &ComputeTaskResponse{
						RetCode: 0,
						Message: "",
						Data: ComputeTaskResponseData{
							Output: "", // 没有输出
						},
					}, nil
				})
			},
		},
		{
			name:    "case 5: decode response failed",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.New(srerr.FormatErr, businessTaskID, "failed to decode response: decode error"),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return &ComputeTaskResponse{
						RetCode: 0,
						Message: "success",
						Data: ComputeTaskResponseData{
							Output: "encoded_output",
						},
					}, nil
				})
				patch.ApplyFunc(decodeResponseDataToAllocationResult, func(ctx context.Context, encodedData string) (*allocationopt.AllocationResult, error) {
					return nil, errors.New("decode error")
				})
			},
		},
		{
			name:    "case 6: convert AlgoResp failed",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    nil,
			wantErr: srerr.New(srerr.FormatErr, businessTaskID, "failed to convert AlgoResp to BatchAllocateResp: convert error"),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return &ComputeTaskResponse{
						RetCode: 0,
						Message: "success",
						Data: ComputeTaskResponseData{
							Output: "encoded_output",
						},
					}, nil
				})
				patch.ApplyFunc(decodeResponseDataToAllocationResult, func(ctx context.Context, encodedData string) (*allocationopt.AllocationResult, error) {
					return &allocationopt.AllocationResult{}, nil
				})
				patch.ApplyFunc(algorithm_client.AlgoRespConvertToBatchAllocateResp, func(ctx context.Context, algoResp *allocationopt.AllocationResult, orders interface{}) (*algorithm_client.BatchAllocateRespBo, error) {
					return nil, errors.New("convert error")
				})
			},
		},
		{
			name:    "case 7: successful execution",
			req:     &algorithm_client.BatchAllocateReqBo{},
			want:    &algorithm_client.BatchAllocateRespBo{},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(&algorithm_client.BatchAllocateReqBo{}), "ConvertToAlgoReq", func(req *algorithm_client.BatchAllocateReqBo, ctx context.Context) interface{} {
					return map[string]string{"key": "value"}
				})
				patch.ApplyFunc(encodeInput, func(data interface{}) (string, error) {
					return "encoded_input", nil
				})
				patch.ApplyMethod(reflect.TypeOf(aisApi), "ComputeTask", func(a *AisApiImpl, ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
					return &ComputeTaskResponse{
						RetCode: 0,
						Message: "success",
						Data: ComputeTaskResponseData{
							Output: "encoded_output",
						},
					}, nil
				})
				patch.ApplyFunc(decodeResponseDataToAllocationResult, func(ctx context.Context, encodedData string) (*allocationopt.AllocationResult, error) {
					return &allocationopt.AllocationResult{}, nil
				})
				patch.ApplyFunc(algorithm_client.AlgoRespConvertToBatchAllocateResp, func(ctx context.Context, algoResp *allocationopt.AllocationResult, orders interface{}) (*algorithm_client.BatchAllocateRespBo, error) {
					return &algorithm_client.BatchAllocateRespBo{}, nil
				})
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			defer func() {
				if patch != nil {
					patch.Reset()
				}
			}()

			got, err := aisApi.ComputeTaskWithEncodedInput(ctx, businessTaskID, tt.req)

			common.AssertResult(t, got, tt.want, err, tt.wantErr)
		})
	}
}
