package aisclient

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
)

type aisHeader struct {
	requestId string
}

// AisHeader 创建AI服务请求头实例
func AisHeader(ctx context.Context) *aisHeader {
	return &aisHeader{requestId: requestid.GetFromCtx(ctx)}
}

// Header 实现 CommonOption 接口，生成请求头
func (a *aisHeader) Header(ctx context.Context) (map[string]string, error) {
	headers := make(map[string]string)

	// 设置Content-Type
	headers["Content-Type"] = "application/json"

	// 设置Request-ID
	if a.requestId != "" {
		headers["X-Request-Id"] = a.requestId
	}

	// 如果需要JWT认证，可以在这里添加
	// 目前先使用基本的头部设置
	// 如果AI服务需要特定的认证方式，可以在这里扩展

	return headers, nil
}

// SecondTimeout 实现 CommonOption 接口，设置超时时间
func (a *aisHeader) SecondTimeout(ctx context.Context) int {
	return int(configutil.GetAiApiConf(ctx).Timeout)
}
