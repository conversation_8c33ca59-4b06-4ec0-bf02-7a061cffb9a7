package aisclient

import (
	jsoniter "github.com/json-iterator/go"
)

// ComputeTaskRequest 计算任务请求结构
type ComputeTaskRequest struct {
	Business              string `json:"business"`                           // 业务类型，最大128字符，必填
	Scenario              string `json:"scenario"`                           // 场景，最大128字符，必填
	BusinessTaskID        string `json:"business_task_id"`                   // 业务任务ID，最大128字符，必填
	TaskStatusCallbackURL string `json:"task_status_callback_url,omitempty"` // 任务状态回调URL，最大256字符，可选
	TimeLimit             int    `json:"time_limit,omitempty"`               // 时间限制（秒），可选
	Input                 string `json:"input,omitempty"`                    // 输入数据（JSON string -> Zstd compressed -> Base64 encoded），可选
}

// ComputeTaskResponse 计算任务响应结构
type ComputeTaskResponse struct {
	RetCode int                     `json:"retcode"` // 返回码
	Message string                  `json:"message"` // 返回消息
	Data    ComputeTaskResponseData `json:"data"`    // 响应数据
}

// ComputeTaskResponseData 计算任务响应数据
type ComputeTaskResponseData struct {
	TaskID     string `json:"task_id"`     // 任务ID
	TaskStatus int    `json:"task_status"` // 任务状态
	Output     string `json:"output"`      // 输出数据
}

// Queries 实现 CommonRequest 接口
func (r *ComputeTaskRequest) Queries() map[string]string {
	return nil
}

// Entity 实现 CommonRequest 接口
func (r *ComputeTaskRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(r)
}

// IsSuccess 实现 CommonResponse 接口
func (r *ComputeTaskResponse) IsSuccess() bool {
	return r.RetCode == 0
}

// FailMsg 实现 CommonResponse 接口
func (r *ComputeTaskResponse) FailMsg() string {
	if r.IsSuccess() {
		return ""
	}
	return r.Message
}
