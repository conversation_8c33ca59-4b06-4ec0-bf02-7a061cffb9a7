package aisclient

import (
	"context"
	"encoding/base64"
	"fmt"

	"git.garena.com/shopee/bg-logistics/algo/sls/allocationopt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/zstd"
)

const (
	ComputeTaskEndpoint = "/api/decision/compute-task/compute"
	SystemAI            = "ai"

	BatchAllocateSDKSwitch = "batch_allocate_sdk_switch"
)

// AisApi AI服务API接口定义
type AisApi interface {
	ComputeTaskWithEncodedInput(ctx context.Context, businessTaskID string, batchAllocateReq *algorithm_client.BatchAllocateReqBo) (*algorithm_client.BatchAllocateRespBo, *srerr.Error)
}

// AisApiImpl AI服务API实现
type AisApiImpl struct{}

// NewAisApi 创建AI服务API实例
func NewAisApi() AisApi {
	return &AisApiImpl{}
}

// ComputeTask 调用AI决策计算任务API
func (a *AisApiImpl) ComputeTask(ctx context.Context, req *ComputeTaskRequest) (*ComputeTaskResponse, *srerr.Error) {
	// 获取AI配置
	aiConf := configutil.GetAiApiConf(ctx)
	url := objutil.Merge(aiConf.Host, ComputeTaskEndpoint)

	// 序列化请求体
	body, jsonErr := jsoniter.Marshal(req)
	if jsonErr != nil {
		logger.CtxLogErrorf(ctx, "AisApi ComputeTask marshal request failed, err: %v", jsonErr)
		return nil, srerr.With(srerr.JsonErr, nil, jsonErr)
	}

	// 获取请求头
	aisHeader := AisHeader(ctx)
	headers, _ := aisHeader.Header(ctx)

	// 获取超时时间
	timeout := int(aiConf.Timeout)

	// 调用httputil.PostJson
	data, httpErr := httputil.PostJson(ctx, url, body, timeout, headers)
	if httpErr != nil {
		logger.CtxLogErrorf(ctx, "AisApi ComputeTask http request failed, url: %s, err: %v", url, httpErr)
		return nil, srerr.With(srerr.DataApiErr, nil, httpErr)
	}

	// 反序列化响应
	var response ComputeTaskResponse
	if unmarshalErr := jsoniter.Unmarshal(data, &response); unmarshalErr != nil {
		logger.CtxLogErrorf(ctx, "AisApi ComputeTask unmarshal response failed, err: %v, response: %s", unmarshalErr, string(data))
		return nil, srerr.With(srerr.FormatErr, nil, unmarshalErr)
	}

	return &response, nil
}

// encodeInput 将 JSON 对象编码为 JSON string -> Zstd compressed -> Base64 encoded
func encodeInput(data interface{}) (string, error) {
	// 1. JSON string
	jsonBytes, err := jsoniter.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// 2. Zstd compression
	encoder, err := zstd.NewWriter(nil)
	if err != nil {
		return "", fmt.Errorf("failed to create zstd encoder: %w", err)
	}
	defer encoder.Close()

	compressedData := encoder.EncodeAll(jsonBytes, make([]byte, 0, len(jsonBytes)))

	// 3. Base64 encoding
	encoded := base64.StdEncoding.EncodeToString(compressedData)
	return encoded, nil
}

// decodeResponseDataToAllocationResult 解码响应数据：Base64 encoded -> Zstd decompressed -> JSON string -> AllocationResult
func decodeResponseDataToAllocationResult(ctx context.Context, encodedData string) (*allocationopt.AllocationResult, error) {
	// 1. Base64 decoding
	decodedData, err := base64.StdEncoding.DecodeString(encodedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %w", err)
	}

	// 2. Zstd decompression
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create zstd decoder: %w", err)
	}
	defer decoder.Close()

	decompressedData, err := decoder.DecodeAll(decodedData, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	// 3. JSON unmarshal to AllocationResult
	var allocationResult allocationopt.AllocationResult
	if err := jsoniter.Unmarshal(decompressedData, &allocationResult); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON to AllocationResult: %w", err)
	}

	return &allocationResult, nil
}

// ComputeTaskWithEncodedInput 调用计算任务接口，使用固定的业务参数和编码的输入
func (a *AisApiImpl) ComputeTaskWithEncodedInput(ctx context.Context, businessTaskID string, batchAllocateReq *algorithm_client.BatchAllocateReqBo) (*algorithm_client.BatchAllocateRespBo, *srerr.Error) {
	// 编码输入数据
	encodedInput, err := encodeInput(batchAllocateReq.ConvertToAlgoReq(ctx))
	if err != nil {
		logger.CtxLogErrorf(ctx, "failed to encode input data: %v", err)
		return nil, srerr.New(srerr.JsonErr, businessTaskID, fmt.Sprintf("failed to encode input data: %v", err))
	}

	// 构建请求
	request := &ComputeTaskRequest{
		Business:       "SlsAllocation",
		Scenario:       "BatchAllocation",
		BusinessTaskID: businessTaskID,
		Input:          encodedInput,
	}

	// 调用ComputeTask
	response, computeErr := a.ComputeTask(ctx, request)
	if computeErr != nil {
		return nil, computeErr
	}
	//localsearchtime

	// 如果响应成功且包含输出数据，则解码输出数据
	if response.IsSuccess() && response.Data.Output != "" {
		algoResp, decodeErr := decodeResponseDataToAllocationResult(ctx, response.Data.Output)
		if decodeErr != nil {
			logger.CtxLogErrorf(ctx, "AisApi ComputeTaskWithEncodedInput decode response output failed, err: %v", decodeErr)
			return nil, srerr.New(srerr.FormatErr, businessTaskID, fmt.Sprintf("failed to decode response: %v", decodeErr))
		}

		result, convertErr := algorithm_client.AlgoRespConvertToBatchAllocateResp(ctx, algoResp, batchAllocateReq.Orders)
		if convertErr != nil {
			return nil, srerr.New(srerr.FormatErr, businessTaskID, fmt.Sprintf("failed to convert AlgoResp to BatchAllocateResp: %v", convertErr))
		}
		return result, nil
	}

	// 如果没有输出数据或响应失败，返回错误
	return nil, srerr.New(srerr.DataApiErr, businessTaskID, fmt.Sprintf("AI service response failed: %s", response.Message))
}
