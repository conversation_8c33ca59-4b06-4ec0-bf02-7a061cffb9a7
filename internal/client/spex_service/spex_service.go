package spex_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	itemPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"
)

type SpexService interface {
	GetOrderList(ctx context.Context, req *order_order_info.GetOrderListByIdListRequest, region string) (*order_order_info.GetOrderListByIdListResponse, *srerr.Error)
	GetItemProductInfo(ctx context.Context, req *itemPb.GetProductInfoRequest, region string) (*itemPb.GetProductInfoResponse, *srerr.Error)
}

type SpexServiceImpl struct {
}

func NewSpexServiceImpl() *SpexServiceImpl {
	return &SpexServiceImpl{}
}

func (s *SpexServiceImpl) GetOrderList(ctx context.Context, req *order_order_info.GetOrderListByIdListRequest, region string) (*order_order_info.GetOrderListByIdListResponse, *srerr.Error) {
	resp := new(order_order_info.GetOrderListByIdListResponse)
	err := RequestSpex(ctx, region, OrderListCommand, req, resp)
	return resp, err
}

func (s *SpexServiceImpl) GetItemProductInfo(ctx context.Context, req *itemPb.GetProductInfoRequest, region string) (*itemPb.GetProductInfoResponse, *srerr.Error) {
	resp := new(itemPb.GetProductInfoResponse)
	err := RequestSpex(ctx, region, ItemProductInfoCommand, req, resp)
	return resp, err
}
