package spex_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/scsps"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/platform/service-governance/viewercontext"
	"strings"
)

func RequestSpex(ctx context.Context, cid, command string, request, response interface{}) *srerr.Error {
	ctx, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	timeout := configutil.GetSpexCommandConfig(ctx, command)
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	reqCtx, err := viewercontext.WithCID(timeoutCtx, strings.ToLower(cid))
	if err != nil {
		cancel()
		errMsg := fmt.Sprintf("Generate CID context failed, error:%v", err)
		endFunc(monitoring.CatSpexService, command, monitoring.StatusError, fmt.Sprintf("command=%s, req=%+v, err=%v", command, request, errMsg))
		return srerr.With(srerr.SpexError, nil, err)
	}
	respCode := scsps.RPCRequest(reqCtx, command, request, response)
	cancel()
	if respCode == 0 {
		logger.CtxLogInfof(reqCtx, "spex command=%s, req=%+v, response=%+v, timeout=%v", command, request, response, timeout)
		endFunc(monitoring.CatSpexService, command, monitoring.StatusSuccess, fmt.Sprintf("command=%s, req=%+v", command, request))
		return nil
	}

	msg, ok := SpexErrorCodeMap[respCode]

	if !ok {
		msg = "spex request unknown err"
	}
	logger.CtxLogErrorf(ctx, "spex command=%s, req=%+v, response=%+v, code=%v, timeout=%v", command, request, response, respCode, timeout)
	endFunc(monitoring.CatSpexService, command, monitoring.StatusError, fmt.Sprintf("command=%s, req=%+v, err=%v", command, request, msg))
	return srerr.New(srerr.SpexError, nil, "request spex service error")
}
