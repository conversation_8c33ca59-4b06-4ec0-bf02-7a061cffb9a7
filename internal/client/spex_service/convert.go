package spex_service

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/itemutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	itemPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	"github.com/gogo/protobuf/proto"
)

// AttachItemInfoV2 使用item最新的数据结构，不再使用 AttachItemInfo 进行反序列化，从lps迁移过来，和lps逻辑保持一致
func AttachItemInfoV2(ctx context.Context, itemInfo *allocation.ItemInfo, itemObj *itemPb.ProductInfo) *srerr.Error {
	if itemInfo == nil || itemObj == nil {
		logger.CtxLogErrorf(ctx, "attach_item_info_fail|empty_item_info_or_item_item_obj")
		return srerr.New(srerr.ParamErr, nil, "attach item info fail, item info or item object is null")
	}
	if itemObj.ItemId == nil || itemObj.ShopId == nil {
		logger.CtxLogErrorf(ctx, "attach_item_info_fail|item_id=%v,invalid_item_obj", itemInfo.ItemId)
		return srerr.New(srerr.ItemNotFound, nil, "invalid item for item_id:%v", itemInfo.ItemId)
	}
	itemInfo.Price = removeInflateUint64(uint64(itemObj.GetItemPriceInfo().GetAggregatedPrice().GetPriceMin()))
	itemInfo.CategoryId = proto.Uint64(uint64(itemutil.GetLocalCategoryByPathV2(itemObj.GetCat().GetLocalCat().GetCatIds())))
	itemInfo.GlobalCategoryId = proto.Uint64(uint64(itemutil.GetGlobalCategoryByPathV2(itemObj.GetCat().GetGlobalCat().GetCatIds())))
	itemInfo.GlobalCategoryIdList = itemutil.GetGlobalCategoryIdListV2(itemObj.GetCat().GetGlobalCat().GetCatIds())
	itemInfo.IsDg = proto.Uint32(itemObj.GetLogistics().GetDangerousGoods())
	if itemObj.GetLogistics().GetDimension() != nil {
		if itemObj.GetLogistics().GetDimension().GetLength() != 0 {
			itemInfo.Length = removeInflateUint64(itemObj.GetLogistics().GetDimension().GetLength())
		}
		if itemObj.GetLogistics().GetDimension().GetWidth() != 0 {
			itemInfo.Width = removeInflateUint64(itemObj.GetLogistics().GetDimension().GetWidth())
		}
		if itemObj.GetLogistics().GetDimension().GetHeight() != 0 {
			itemInfo.Height = removeInflateUint64(itemObj.GetLogistics().GetDimension().GetHeight())
		}
	}
	// 数据兼容
	if itemInfo.IsDg == nil {
		logger.CtxLogInfof(ctx, "get_item_detail|data_compat,field:is_dg,default:%v", constant.DGTypeNoneDG)
		itemInfo.IsDg = proto.Uint32(constant.DGTypeNoneDG)
	}
	itemInfo.IsPreOrder, itemInfo.EstimateDays = itemObj.GetLogistics().GetIsPreOrder(), itemObj.GetLogistics().GetEstimateDays()

	// 默认 weight 取 item 维度
	if itemObj.GetLogistics() != nil {
		itemInfo.Weight = removeInflateUint64(itemObj.GetLogistics().GetWeight())
	}

	// 按照 model id 维度获取数据
	for _, modelLogistic := range itemObj.GetLogistics().GetModels() {
		if modelLogistic == nil || modelLogistic.GetModelId() != uint64(itemInfo.ModelId) {
			continue
		}
		itemInfo.Weight = removeInflateUint64(modelLogistic.GetWeight())
		itemInfo.IsPreOrder, itemInfo.EstimateDays = modelLogistic.GetIsPreOrder(), modelLogistic.GetEstimateDays()
		// 直接使用 model 数据
		itemInfo.Length = removeInflateUint64(modelLogistic.GetDimension().GetLength())
		itemInfo.Width = removeInflateUint64(modelLogistic.GetDimension().GetWidth())
		itemInfo.Height = removeInflateUint64(modelLogistic.GetDimension().GetHeight())
		break
	}
	return nil
}

func removeInflateUint64(val uint64) *float64 {
	ret := float64(val) / constant.DB_PRICE_INFLATION_FACTOR
	return &ret
}
