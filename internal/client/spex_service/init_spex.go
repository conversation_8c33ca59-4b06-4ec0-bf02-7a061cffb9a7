package spex_service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scsps"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
	"sync"
	"time"
)

var initOnce sync.Once

func InitSpex() error {
	spexConfig := configutil.GetSpexConfig()

	spexAddress := scsps.WithSpexAddress("unix", "/run/spex/spex.sock")
	ctx := context.Background()
	env := strings.ToLower(envvar.GetEnvWithCtx(ctx)) // 深坑，一定要转env小写

	spexDebug := recorder.Getenv(ctx, "spex_debug")
	if spexDebug == "true" {
		spexAddress = scsps.WithSpexAddress("tcp", "spex.lls-lcs.tech:8088")
	}
	instanceId, err := scsps.GenerateInstanceID(spexConfig.ServiceName, envvar.GetCID(), env, "", "", "")
	if err != nil {
		return fmt.Errorf(fmt.Sprintf("generate instance id :%s", err.Error()))
	}
	initOnce.Do(func() {
		err = scsps.Init(scsps.WithInstanceID(instanceId), scsps.WithConfigKey(spexConfig.ConfigKey), spexAddress)
		if err != nil {
			panic(fmt.Sprintf("Init sps :%s", err.Error()))
		}
		err = startSpex()
		if err != nil {
			panic(fmt.Sprintf("sart spex error:%s", err.Error()))
		}
	})
	return nil
}

func startSpex() error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	if err := scsps.SubscribeConfig(ctx); err != nil {
		logger.LogErrorf("subscribe config :%s", err.Error())
		return err
	}

	// service and client  start up after call Register
	if err := scsps.Register(ctx); err != nil {
		logger.LogErrorf("sps register  :%s", err.Error())
		return err
	}
	return nil
}
