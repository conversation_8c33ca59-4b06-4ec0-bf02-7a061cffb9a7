package spex_service

const (
	OrderListCommand       = "order.order_info.get_order_list_by_id_list"
	ItemProductInfoCommand = "marketplace.listing.item.itemaggregation.iteminfo.get_product_info"
)

const (
	ErrorBranchNotFound uint32 = 909000
	// Programming mistake, or some other problems that we can't blame external dependencies or API users
	ErrorInternal uint32 = 48400000
	// External dependencies error
	ErrorExternal uint32 = 48400001

	// General API and request errors ******** - ********

	// Logic is not implemented yet
	ErrorNotImplemented uint32 = ********
	// Deprecated API
	ErrorDeprecated uint32 = 48400011
	// Incorrect request type
	ErrorRequestAssertion uint32 = 48400012
	// Incorrect response type
	ErrorResponseAssertion uint32 = 48400013

	// Storage errors ********** - ********

	// DB query return error and this error is not "not found"
	ErrorDatabase uint32 = 48400050
	// Cache
	ErrorCache uint32 = 48400051
	// Redis
	ErrorRedis uint32 = 48400052

	// Business errors 48401000 - 48410000

	ErrorTokenInvalid     uint32 = 48401000
	ErrorTokenExpired     uint32 = 48401001
	ErrorNonceInvalid     uint32 = 48401002
	ErrorOtpInvalid       uint32 = 48401003
	ErrorPasswordInvalid  uint32 = ********
	ErrorCaptchaInvalid   uint32 = ********
	ErrorPasswordRequired uint32 = ********

	ErrorOtpLimitExceeded uint32 = ********

	ErrorPhoneTaken            uint32 = ********
	ErrorPhoneNotExist         uint32 = ********
	ErrorDeviceNotTrusted      uint32 = ********
	ErrorOtpChannelUnavailable uint32 = ********
	ErrorUsernameTaken         uint32 = ********
	ErrorUsernameNotExist      uint32 = ********
	ErrorEmailTaken            uint32 = ********
	ErrorEmailNotExist         uint32 = ********
	ErrorAccountBanned         uint32 = ********
	ErrorAccountDeleted        uint32 = ********
	ErrorNotAdmin              uint32 = ********
	ErrorMerchantAliasTaken    uint32 = ********
	ErrorNeedCaptcha           uint32 = ********
	ErrorAccountNormal         uint32 = ********
	ErrorFraudLogin            uint32 = ********
	ErrorEmailNotEmpty         uint32 = ********
	ErrorPhoneNotEmpty         uint32 = ********
	ErrorPasswordNotEmpty      uint32 = ********
	ErrorRegionMismatch        uint32 = ********
	ErrorBusinessIdMismatch    uint32 = ********
)

var SpexErrorCodeMap = map[uint32]string{
	// Programming mistake, or some other problems that we can't blame external dependencies or API users
	ErrorInternal: "error internal",
	// External dependencies error
	ErrorExternal: "error external",

	// General API and request errors ******** - ********

	// Logic is not implemented yet
	ErrorNotImplemented: "error not implemented",
	// Deprecated API
	ErrorDeprecated: "error deprecated",
	// Incorrect request type
	ErrorRequestAssertion: "error request assertion",
	// Incorrect response type
	ErrorResponseAssertion: "error response assertion",

	// Storage errors ********** - ********

	// DB query return error and this error is not "not found"
	ErrorDatabase: "error database",
	// Cache
	ErrorCache: "error cache",
	// Redis
	ErrorRedis: "error redis",

	// Business errors 48401000 - 48410000

	ErrorTokenInvalid:     "error token invalid",
	ErrorTokenExpired:     "error token expired",
	ErrorNonceInvalid:     "error nonce invalid",
	ErrorOtpInvalid:       "error otp invalid",
	ErrorPasswordInvalid:  "error password invalid",
	ErrorCaptchaInvalid:   "error captcha invalid",
	ErrorPasswordRequired: "error password required",

	ErrorOtpLimitExceeded: "error opt limit exceeded",

	ErrorPhoneTaken:            "error phone taken",
	ErrorPhoneNotExist:         "error phone not exist",
	ErrorDeviceNotTrusted:      "error device not trusted",
	ErrorOtpChannelUnavailable: "error otp channel unavailable",
	ErrorUsernameTaken:         "error user name taken",
	ErrorUsernameNotExist:      "error username not exist",
	ErrorEmailTaken:            "error email taken",
	ErrorEmailNotExist:         "error email not exist",
	ErrorAccountBanned:         "error account banned",
	ErrorAccountDeleted:        "error account deleted",
	ErrorNotAdmin:              "error not admin",
	ErrorMerchantAliasTaken:    "error merchant alias taken",
	ErrorNeedCaptcha:           "error need captcha",
	ErrorAccountNormal:         "error account normal",
	ErrorFraudLogin:            "error fraud login",
	ErrorEmailNotEmpty:         "error email not empty",
	ErrorPhoneNotEmpty:         "error phone not empty",
	ErrorPasswordNotEmpty:      "error password not empty",
	ErrorRegionMismatch:        "error region mismatch",
	ErrorBusinessIdMismatch:    "error business id mismatch",
}
