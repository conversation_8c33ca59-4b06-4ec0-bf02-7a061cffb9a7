package wbcclient

import jsoniter "github.com/json-iterator/go"

type WaybillResp struct {
	Retcode int       `json:"retcode"`
	Message string    `json:"message"`
	Data    OrderInfo `json:"data"`
}

type OrderInfo struct {
	BaseInfo []BaseInfo `json:"list"`
}

type BaseInfo struct {
	ForderId string `json:"forder_id"`
	SlsTn    string `json:"slo_tn"`
	SlsId    string `json:"slo_id"`
}

type (
	BatchGetSloInfoReqListItem struct {
		ForderId string `json:"forder_id"`
	}

	BatchGetSloInfoReq struct {
		List []BatchGetSloInfoReqListItem `json:"list"`
	}

	Location struct {
		Latitude    string  `json:"latitude"`
		Longitude   string  `json:"longitude"`
		LocationIDs []int64 `json:"location_ids"`
		Postcode    string  `json:"postcode"`
	}

	LmLocationInfo struct {
		DeliverLocation Location `json:"deliver_location"`
		PickupLocation  Location `json:"pickup_location"`
	}

	TwsInfo struct {
		ActualWeight float64 `json:"actual_weight"`
		ActualWidth  float64 `json:"actual_width"`
		ActualLength float64 `json:"actual_length"`
		ActualHeight float64 `json:"actual_height"`
		HasTwsAction bool    `json:"has_tws_action"`
	}

	BatchGetSloInfoRespDataListItem struct {
		ForderID       string         `json:"forder_id"`
		SlsTN          string         `json:"sls_tn,omitempty"` // 非必需
		LmTN           string         `json:"lm_tn,omitempty"`  // 非必需
		LmLocationInfo LmLocationInfo `json:"lm_location_info"`
		TwsInfo        TwsInfo        `json:"tws_info"`
		SloCtime       uint32         `json:"slo_ctime,omitempty"` // 非必需
		CombinedType   uint32         `json:"combined_type"`
		Retcode        int            `json:"retcode"`
		Message        string         `json:"message"`
	}

	BatchGetSloInfoRespData struct {
		List []BatchGetSloInfoRespDataListItem `json:"list"`
	}

	BatchGetSloInfoResp struct {
		Retcode int                     `json:"retcode"`
		Message string                  `json:"message"`
		Data    BatchGetSloInfoRespData `json:"data"`
	}
)

func (b BatchGetSloInfoReq) Queries() map[string]string {
	return nil
}

func (b BatchGetSloInfoReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(b)
}

func (b BatchGetSloInfoResp) IsSuccess() bool {
	return b.Retcode == 0
}

func (b BatchGetSloInfoResp) FailMsg() string {
	return b.Message
}
