package wbcclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	jsoniter "github.com/json-iterator/go"
	"net/http"
	"strings"
)

type WbcApi interface {
	GetSlsTnByForderId(ctx context.Context, forderId string) (string, string, *srerr.Error)
	GetForderIdBySlsTn(ctx context.Context, slsTn string) (string, string, *srerr.Error)
	BatchGetSloInfo(ctx context.Context, forderIDList []string) ([]BatchGetSloInfoRespDataListItem, *srerr.Error)
}

type WbcApiImpl struct {
}

func NewWbcApi() *WbcApiImpl {
	return &WbcApiImpl{}
}

var (
	BatchGetSloWeightInfo = client.HttpService{
		Endpoint: "/api/slo/batch_get_slo_info_for_smr_forecast_check",
		Scene:    client.DR,
		System:   constant.SystemWBCApi,
	}
)

func getApi(ctx context.Context) (string, *srerr.Error) {
	api := configutil.GetWaybillApi(ctx).SlsTnApi
	if api == "" {
		logger.CtxLogInfof(ctx, "waybill api is empty")
		return "", srerr.New(srerr.ParamErr, nil, "waybill api is empty")
	}
	api = formatApi(ctx, api)
	return api, nil
}

func (w *WbcApiImpl) GetSlsTnByForderId(ctx context.Context, forderId string) (string, string, *srerr.Error) {
	api, apiErr := getApi(ctx)
	if apiErr != nil {
		logger.CtxLogInfof(ctx, "waybill api is empty")
		return "", "", srerr.New(srerr.ParamErr, nil, "waybill api is empty")
	}
	header, err := builderHeader(ctx)
	if err != nil {
		logger.CtxLogInfof(ctx, "build header error %+v", err)
		return "", "", err
	}
	param, pErr := buildParam("", forderId)
	if pErr != nil {
		logger.CtxLogInfof(ctx, "build param error %+v", pErr)
		return "", "", srerr.With(srerr.ParamErr, forderId, pErr)
	}

	respByteArr, httpErr := httputil.PostJson(ctx, api, param, timeout, header)
	logger.CtxLogInfof(ctx, "Wbc response param is %s", string(respByteArr))
	if httpErr != nil {
		logger.CtxLogInfof(ctx, "data response error %+v", httpErr)
		return "", "", srerr.With(srerr.DataApiErr, forderId, httpErr)
	}

	return getResult(respByteArr, slsTnType)
}

func (w *WbcApiImpl) BatchGetSloInfo(ctx context.Context, forderIDList []string) ([]BatchGetSloInfoRespDataListItem, *srerr.Error) {
	reqForderIDList := make([]BatchGetSloInfoReqListItem, 0, len(forderIDList))
	for _, f := range forderIDList {
		reqForderIDList = append(reqForderIDList, BatchGetSloInfoReqListItem{ForderId: f})
	}

	var (
		req = BatchGetSloInfoReq{reqForderIDList}
		rsp BatchGetSloInfoResp
	)
	if err := client.HttpInvoke(ctx, constant.SystemWBCApi, BatchGetSloWeightInfo, http.MethodPost, req, &rsp, WbcHeader(ctx)); err != nil {
		return nil, srerr.With(srerr.WbcApiErr, nil, err)
	}

	return rsp.Data.List, nil
}

func getResult(respByteArr []byte, resultType int) (string, string, *srerr.Error) {

	resp := &WaybillResp{}
	jsonErr := jsoniter.Unmarshal(respByteArr, resp)
	if jsonErr != nil {
		logger.LogErrorf("json error %s", string(respByteArr))
		return "", "", srerr.With(srerr.JsonErr, nil, jsonErr)
	}

	if resp != nil && len(resp.Data.BaseInfo) > 0 {
		if resultType == slsTnType {
			return resp.Data.BaseInfo[0].SlsTn, resp.Data.BaseInfo[0].SlsId, nil
		} else {
			return resp.Data.BaseInfo[0].ForderId, resp.Data.BaseInfo[0].SlsId, nil
		}
	}
	logger.LogErrorf("WBC getResult failed response data is empty %s", string(respByteArr))
	return "", "", srerr.New(srerr.DataErr, string(respByteArr), "WBC getResult failed response list is empty")
}

func (w *WbcApiImpl) GetForderIdBySlsTn(ctx context.Context, slsTn string) (string, string, *srerr.Error) {
	api, apiErr := getApi(ctx)
	if apiErr != nil {
		logger.CtxLogInfof(ctx, "waybill api is empty")
		return "", "", srerr.New(srerr.ParamErr, nil, "waybill api is empty")
	}
	header, err := builderHeader(ctx)
	if err != nil {
		return "", "", err
	}
	param, pErr := buildParam(slsTn, "")
	if pErr != nil {
		logger.CtxLogInfof(ctx, "build param error %+v", pErr)
		return "", "", srerr.With(srerr.ParamErr, slsTn, pErr)
	}

	respByteArr, httpErr := httputil.PostJson(ctx, api, param, timeout, header)

	if httpErr != nil {
		logger.CtxLogInfof(ctx, "data response error %+v", httpErr)
		return "", "", srerr.With(srerr.DataApiErr, slsTn, httpErr)
	}

	return getResult(respByteArr, forderIdType)
}

func buildParam(slsTn, forderId string) ([]byte, *srerr.Error) {
	param := map[string]interface{}{}
	if slsTn != "" {
		param["slo_tn_list"] = []string{slsTn}
	}
	if forderId != "" {
		param["forder_id"] = forderId
	}

	param["pageno"] = 1
	param["count"] = 10

	marshal, jsonErr := jsoniter.Marshal(param)
	if jsonErr != nil {
		return nil, srerr.With(srerr.JsonErr, string(marshal), jsonErr)
	}
	return marshal, nil
}

func builderHeader(ctx context.Context) (map[string]string, *srerr.Error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}
	apiConf := configutil.GetWaybillApi(ctx)
	secret := apiConf.Password
	jwt, err := jwtutil.BuildJWT(ctx, envvar.GetCID(), user, operator, secret)
	if err != nil {
		logger.CtxLogErrorf(ctx, "generate jwt token error %+v", err)
		return nil, srerr.New(srerr.ParamErr, nil, "generate jwt token error %+v", err)
	}
	header["jwt-token"] = jwt

	return header, nil
}

func formatApi(ctx context.Context, api string) string {
	temp := strings.ReplaceAll(api, "{{env}}", strings.ToLower(envvar.GetEnvWithCtx(ctx)))
	return strings.ReplaceAll(temp, "{{cid}}", strings.ToLower(envvar.GetCID()))
}
