package client

const (
	//SystemData                             = "data"
	GetMaskingResultListEndPointV2           = "/data_api/data_sls/smart_routing/third_party_masking/v2/aggregate_list"
	GetMaskingAllocationPathListEndPointV2   = "/data_api/data_sls/smart_routing/third_party_masking/v2/allocation_path_list"
	GetMaskingAllocationPathDetailEndPointV2 = "/data_api/data_sls/smart_routing/third_party_masking/v2/allocation_path_detail"
	DataSecretV2                             = "test666" //non-live配置在代码里，live配置在Apollo上，live/non-live不一样
	DataAccountV2                            = "wbc-admin"
)

const (
	DailyGranularity  = "daily"
	HourlyGranularity = "hourly"
)

// grpc限流，熔断
const (
	GrpcClient = "client"
	GrpcServer = "server"
)

type RequestMethod string

const (
	GetMethod    RequestMethod = "GET"
	PostMethod   RequestMethod = "POST"
	PutMethod    RequestMethod = "PUT"
	DeleteMethod RequestMethod = "DELETE"
)

type ContentType string

const (
	HTMLContent ContentType = "text/html"
	TextContent ContentType = "text/plain"
	XMLContent  ContentType = "text/xml"
	GifContent  ContentType = "text/gif"
	JpgContent  ContentType = "text/jpeg"
	PngContent  ContentType = "text/png"

	ApplicationXHTMLContent   ContentType = "application/xhtml+xml"
	ApplicationXMLContent     ContentType = "application/xml"
	ApplicationAtomXMLContent ContentType = "application/atom+xml"
	JsonContent               ContentType = "application/json"
	PDFContent                ContentType = "application/pdf"
	WordContent               ContentType = "application/msword"
	OctetStreamContent        ContentType = "application/octet-stream"
	FormContent               ContentType = "application/x-www-form-urlencoded"

	FormDataContent ContentType = "multipart/form-data"
)
