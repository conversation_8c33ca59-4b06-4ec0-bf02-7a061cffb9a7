package lfsclient

import (
	"context"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func (p *LfsApiImpl) BatchCheckSmartRoutingLaneServiceable(ctx context.Context, req *lfspb.BatchCheckSmartRoutingLaneServiceableReqData) (*lfspb.BatchCheckLaneServiceableRspData, *srerr.Error) {
	// 去除多余赋值
	if req.GetReqData().GetOrder().GetOrderInfo() != nil &&
		req.GetReqData().GetOrder().GetOrderInfo().GetExtInfo().GetShopSnapshot().GetShop() != nil &&
		req.GetReqData().GetOrder().GetOrderInfo().GetExtInfo().GetShopSnapshot().GetShop().GetExtInfo() != nil {

		req.GetReqData().GetOrder().GetOrderInfo().GetExtInfo().GetShopSnapshot().GetShop().GetExtInfo().LogisticsInfo = nil
	}

	// 定义请求，响应，调用错误
	reqParam := &lfspb.BatchCheckSmartRoutingLaneServiceableReq{
		Header: p.getGrpcReqHeader(ctx),
		Data:   req,
	}

	respPtr := new(lfspb.BatchCheckLaneServiceableRsp)
	var err error

	// for mock
	//if target, reqId, ok := external_gateway.GrpcMockTarget(ctx, constant.MockApiCheckLane); ok {
	//	conn, err := request.GetGrpcConn(ctx, target)
	//	if err != nil {
	//		return nil, lpserr.With(errcode.LfsError, nil, err)
	//	}
	//
	//	defer conn.Close()
	//
	//	reqParam.Header.RequestId = proto.String(reqId)
	//	respPtr, err = lfspb.NewServiceableClient(conn.ClientConn).BatchCheckSmartRoutingLaneServiceableV2(ctx, reqParam)
	//	if err != nil {
	//		retCode := lpserr.New(errcode.LfsError, nil, "batch check smart routing lane serviceable fail, err=%+v", err)
	//		retCode = p.WrapErrReference(retCode, respPtr)
	//		return nil, retCode
	//	}
	//	logger.CtxLogDebugf(ctx, "BatchCheckSmartRoutingLaneServiceable|Mock|Response: %s", str.JsonStringForDebugLog(respPtr))
	//} else
	//conf := configutil.GetLfsGrpcConf()

	if err = client.GrpcInvoke(ctx, constant.SystemLFS, "protocol.Serviceable", BatchCheckSmartRoutingLaneServiceable, reqParam, respPtr); err != nil {
		retCode := srerr.New(srerr.LfsError, nil, "batch check smart routing lane serviceable, err=%+v", err)
		//retCode = p.WrapErrReference(retCode, respPtr)
		return nil, retCode
	}

	if respPtr.GetHeader() == nil {
		retCode := srerr.New(srerr.LfsError, nil, "batch check smart routing lane serviceable, invalid response from lfs")
		//retCode = p.WrapErrReference(retCode, respPtr)
		return nil, retCode
	}

	if respPtr.GetHeader().GetRetcode() != 0 {
		//if value, ok := channelsSpecRetCode[int(respPtr.GetHeader().GetRetcode())]; ok {
		//	tmpErr := fmt.Errorf("lfs Serviceable BatchCheckSmartRoutingLaneServiceable error,err_code:%v,err_msg=%v", respPtr.GetHeader().GetRetcode(), respPtr.GetHeader().GetMessage())
		//	retCode := lpserr.NewExt(value, "lfs Serviceable:BatchCheckSmartRoutingLaneServiceable error", req, tmpErr)
		//	retCode = p.WrapErrReference(retCode, respPtr)
		//	return nil, retCode
		//}
		retCode := srerr.New(srerr.LfsError, nil, "batch check smart routing lane serviceable error, err_msg=%v", respPtr.GetHeader().GetMessage())
		//retCode = p.WrapErrReference(retCode, respPtr)
		return nil, retCode
	}
	return respPtr.GetData(), nil

}
