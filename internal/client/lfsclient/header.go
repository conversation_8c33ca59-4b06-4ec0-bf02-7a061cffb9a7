package lfsclient

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/jwtutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"github.com/dgrijalva/jwt-go"
)

type lfsHeader struct {
	requestId string
}

func LfsHeader(ctx context.Context) *lfsHeader {
	return &lfsHeader{requestId: requestid.GetFromCtx(ctx)}
}

func (p *lfsHeader) Header(ctx context.Context) (map[string]string, error) {
	jwtData := jwtutil.JwtData{
		User: jwtutil.JwtUser{
			Name:  "xin.luo",
			Email: "<EMAIL>",
		},
		Entity: jwtutil.JwtEntity{
			Country:  envvar.GetCID(),
			Name:     envvar.GetCID(),
			TimeZone: 7,
		},
	}
	info := jwtutil.Info{
		Exp:  recorder.Now(ctx).Unix() + 10000,
		Info: jwtData,
	}
	token := jwt.Token{
		Method: jwt.SigningMethodHS256,
		Header: map[string]interface{}{
			"account": "sls-ops",
			"typ":     "JWT",
			"alg":     jwt.SigningMethodHS256.Alg(),
		},
		Claims: info,
	}

	secret := configutil.GetTokenByClientName(context.Background(), configutil.LfsServiceClient)
	tokens, err := token.SignedString([]byte(secret))
	if err != nil {
		return nil, err
	}

	lfsCnf := configutil.GetLfsApiConf(ctx)
	return map[string]string{"jwt-token": tokens, "X-Token": lfsCnf.Token, "X-Account": lfsCnf.Account, "X-Request-Id": p.requestId}, nil
}

func (p *lfsHeader) SecondTimeout(ctx context.Context) int {
	lfsCnf := configutil.GetLfsApiConf(ctx)
	return int(lfsCnf.Timeout)
}
