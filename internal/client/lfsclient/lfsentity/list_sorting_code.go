package lfsentity

import (
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type CodeStatus uint8

const (
	CodeStatusDisable CodeStatus = 0
	CodeStatusEnable  CodeStatus = 1
)

type (
	ListSortingCodeRequest struct {
		ServiceCode   string `json:"service_code,omitempty"`
		ProductId     string `json:"product_id,omitempty"`
		LmId          string `json:"lm_id,omitempty"`
		DGFlag        int32  `json:"dg_flag,omitempty"`
		DgGroup       string `json:"dg_group,omitempty"`
		Region        string
		ActualPointId string `json:"actual_point_id,omitempty"`
	}
	ListSortingCodeResponse struct {
		RetCode int                          `json:"retcode"`
		Message string                       `json:"message"`
		Detail  string                       `json:"detail,omitempty"`
		Data    *ListSortingCodeResponseData `json:"data,omitempty"`
	}

	ListSortingCodeResponseData struct {
		List []*SortingCodeInfo `json:"list"`
	}

	SortingCodeInfo struct {
		Id            uint64     `json:"id"`
		Region        string     `json:"region"`
		ProductId     string     `json:"product_id"`
		DgGroup       string     `json:"dg_group"`
		LmId          string     `json:"lm_id"`
		DgFlag        int32      `json:"dg_flag"`
		ActualPointId string     `json:"actual_point_id"`
		ServiceCode   string     `json:"service_code"`
		Operator      string     `json:"operator"`
		CodeStatus    CodeStatus `json:"code_status"`
		Mtime         uint32     `json:"mtime"`
		Ctime         uint32     `json:"ctime"`
	}
)

func (p *ListSortingCodeRequest) Queries() map[string]string {
	body := make(map[string]string)
	if p.ServiceCode != "" {
		body["service_code"] = p.ServiceCode
	}
	if p.ProductId != "" {
		body["product_id"] = p.ProductId
	}
	if p.LmId != "" {
		body["lm_id"] = p.LmId
	}
	if p.DGFlag != 0 {
		body["dg_flag"] = strconv.Itoa(int(p.DGFlag))
	}
	if p.DgGroup != "" {
		body["dg_group"] = p.DgGroup
	}
	if p.ActualPointId != "" {
		body["actual_point_id"] = p.ActualPointId
	}

	return body
}

func (p *ListSortingCodeRequest) Entity() ([]byte, error) {
	return jsoniter.Marshal(p)
}

func (p *ListSortingCodeResponse) IsSuccess() bool {
	return p.RetCode == 0 && p.Data != nil && len(p.Data.List) > 0
}

func (p *ListSortingCodeResponse) FailMsg() string {
	return "lfs list sorting code fail"
}
