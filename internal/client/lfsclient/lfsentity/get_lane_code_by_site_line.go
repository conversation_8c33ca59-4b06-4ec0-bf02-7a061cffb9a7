package lfsentity

import (
	jsoniter "github.com/json-iterator/go"
)

type (
	GetLaneCodeBySiteOrLineReq struct {
		SiteLineId string `json:"site_line_id"`
	}
	GetLaneCodeBySiteOrLineRsp struct {
		Retcode int           `json:"retcode"`
		Message string        `json:"message"`
		Detail  string        `json:"detail"`
		Data    LaneCodesInfo `json:"data"`
	}
	LaneCodesInfo struct {
		LaneCodes []string `json:"lane_code_list"`
	}
)

func (g GetLaneCodeBySiteOrLineReq) Queries() map[string]string {
	return nil
}

func (g GetLaneCodeBySiteOrLineReq) Entity() ([]byte, error) {
	return jsoniter.Marshal(g)
}

func (g *GetLaneCodeBySiteOrLineRsp) IsSuccess() bool {
	return g.Retcode == 0
}

func (g *GetLaneCodeBySiteOrLineRsp) FailMsg() string {
	return g.Message
}
