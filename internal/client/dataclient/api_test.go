package dataclient

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

//func TestBuildDataApiJwt(t *testing.T) {
//	var starTime, endTime int64
//	starTime = *************
//	endTime = *************
//	type args struct {
//		ctx    context.Context
//		data   GetPanelFromDataRequest
//		secret string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    string
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{name: "test jwt", args: args{
//			ctx:    context.TODO(),
//			secret: "test666",
//			data: GetPanelFromDataRequest{
//				StartTime:             &starTime,
//				EndTime:               &endTime,
//				TimeGranularity:       proto.String("daily"),
//				MaskProductIDs:        []int64{1755, 1578},
//				FulfillmentProductIDs: []int64{1625},
//				Country:               proto.String("id"),
//				AllocationStatus:      proto.Bool(true),
//				AggregateKey:          []int{1},
//			},
//		}, want: "", wantErr: true},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := BuildDataApiJwt(tt.args.data, "", tt.args.secret)
//			println(got)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("BuildDataApiJwt() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if got != tt.want {
//				t.Errorf("BuildDataApiJwt() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestDataApi_QueryOrderCount(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("task_server"))
	configutil.Init()
	api := NewDataApi()
	ctx := context.TODO()
	//resp, err := api.QueryOrderCount(ctx, 91003, 2, "2023-02-22", "2023-02-23")
	//if err != nil {
	//	panic(err)
	//}

	req := QueryOrderDetailReq{
		ProductId:   91003,
		RoutingType: 2,
		StartDate:   "2023-09-12",
		EndDate:     "2023-09-12",
		PageCount:   500,
		PageOffset:  1,
		//WeightRange: []*persistent.WeightRange{
		//	{
		//		Min: 0, Max: 1000,
		//	},
		//},
		Region: envvar.GetCID(),
	}
	wg := sync.WaitGroup{}
	for i := 0; i < 1000; i++ {
		go func() {
			defer wg.Done()
			wg.Add(1)
			_, err := api.OrderOrderDetail(ctx, req)
			if err != nil {
				fmt.Printf("err=%v", err)
			} else {
				//println(fmt.Sprintf("%+v", resp))
			}
		}()
	}
	wg.Wait()
}
