package dataclient

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
)

type (
	//GetPanelFromDataRequest :mask_product_ids和fulfillment_product_ids是过滤条件，
	//需要过滤哪个就传哪个，如果都不传，data那边会视为检索默认值（即把所有的masking和fulfillment product 都筛出来）
	GetPanelFromDataRequest struct {
		StartTime             *int64  `json:"start_time,omitempty"` //对应日期零点时间戳，如 2022-06-20 00:00:00 , 为了和数仓规范统一，使用毫秒级时间戳。  筛选： 业务时间 >= start_time
		EndTime               *int64  `json:"end_time,omitempty"`   //对应日期零点时间戳，如 2022-06-20 00:00:00 , 为了和数仓规范统一，使用毫秒级时间戳。 筛选： 业务时间 < end_time
		MaskProductIDs        []int64 `json:"mask_product_ids"`
		FulfillmentProductIDs []int64 `json:"fulfillment_product_ids"`
		ShopGroupID           *int64  `json:"shop_group_id,omitempty"`
		SoftCriteriaRuleID    *uint64 `json:"soft_criteria_rule_id,omitempty"`
		VolumeRuleID          *uint64 `json:"volume_rule_id,omitempty"`
		ZoneCode              *string `json:"zone_code,omitempty"`
		RouteCode             *string `json:"route_code,omitempty"`
		TimeGranularity       *string `json:"time_granularity,omitempty"`  //including "daily"， "hourly"
		Country               *string `json:"country,omitempty"`           //like "id", "vn" and so on
		AllocationStatus      *bool   `json:"allocation_status,omitempty"` //true:will select data of allocation_status==true, false:will select false data
		AggregateKey          []int   `json:"aggregate_key,omitempty"`     // pass 0:aggregate by masking product; pass 1:aggregate by fulfillment product
		Region                string  `json:"region"`
	}

	GetPanelFromDataResp struct {
		Retcode int         `json:"retcode"`
		Message string      `json:"message"`
		Data    ResultPanel `json:"data"` //aggregate info, including "product info" and "aggregate data list"
	}

	ResultPanel struct {
		List            []AggregatedInfo `json:"list"`
		TimeGranularity string           `json:"time_granularity"` //including "daily", "hourly"
	}

	//AggregatedInfo :an AggregatedInfo contains one product and product's all aggregated data
	AggregatedInfo struct {
		ProductID         uint64          `json:"product_id"`
		AggregateDataList []AggregateData `json:"aggregate_data"`
		AggregateKey      int             `json:"aggregate_key"` //0:means masking product; 1:means fulfillment product
	}

	//AggregateData :store aggregated data about time and order count
	AggregateData struct {
		Datetime   string `json:"datetime"`    //datetime, format by "yyyyMMddHH"
		OrderCount int    `json:"order_count"` //y-axis, also means the number of aggregated allocate requests
	}

	JwtReq struct {
		Jwt string `json:"jwt"`
	}
)

type (
	DataApiInfo struct {
		TimeStamp int         `json:"timestamp"`
		Data      interface{} `json:"data"`
	}
	DataApiInfoV2 struct {
		TimeStamp int    `json:"timestamp"`
		Region    string `json:"region"`
	}
)

func (c DataApiInfo) Valid() error {
	return nil
}

func (c DataApiInfoV2) Valid() error {
	return nil
}

func (g *GetPanelFromDataRequest) ClearEmptyString() {
	//兼容前端的隐藏特性， 对于传空的字段进行重置
	if g.ZoneCode != nil && *g.ZoneCode == "" {
		g.ZoneCode = nil
	}
	if g.RouteCode != nil && *g.RouteCode == "" {
		g.RouteCode = nil
	}
}

type (
	RoutingVisualDataListReq struct {
		RequestId string `json:"request_id,omitempty"`
		SlsTn     string `json:"sls_tn,omitempty"`
		ForderId  string `json:"forder_id,omitempty"`
		PageNo    int    `json:"page_no"`
		PageSize  int    `json:"page_size"`
		Region    string `json:"region"`
	}

	RoutingVisualDataListResp struct {
		Retcode int    `json:"retcode"`
		Message string `json:"message"`
		Data    ListResp
	}
	ListResp struct {
		List  []BaseInfoList `json:"list"`
		Page  int            `json:"page"`
		Size  int            `json:"size"`
		Total int            `json:"total"`
	}
	BaseInfoList struct {
		RequestId     string `json:"request_id"`
		SlsTn         string `json:"sls_tn"`
		ForderId      string `json:"forder_id"`
		RequestTime   uint32 `json:"request_time"`
		RoutingStatus bool   `json:"routing_status"`
		ProductId     int    `json:"product_id"`
		RowKey        string `json:"row_key"`
	}

	RoutingVisualDataDetailResp struct {
		Retcode int             `json:"retcode"`
		Message string          `json:"message"`
		Data    ResponseMessage `json:"data"`
	}

	ResponseMessage struct {
		LogEntry routing_log.RoutingLog `json:"message"`
	}
)

// local/spx预测工具
type (
	QueryOrderCountReq struct {
		ProductId   uint64 `json:"product_id"`
		RoutingType int    `json:"routing_type"`
		StartDate   string `json:"start_date"`
		EndDate     string `json:"end_date"`
		Region      string `json:"region"`
	}

	QueryOrderCountResp struct {
		Retcode int                     `json:"retcode"`
		Message string                  `json:"message"`
		Data    QueryCountOrderListResp `json:"data"`
	}

	QueryCountOrderListResp struct {
		List []QueryCountOrderDetailResp `json:"list"`
		Size int                         `json:"size"`
	}

	QueryCountOrderDetailResp struct {
		Date  string `json:"date"`
		Count int    `json:"log_qty_1d"`
	}

	QueryOrderAggregationRequest struct {
		ProductId   int                       `json:"product_id"`
		StartDate   string                    `json:"start_date"`
		EndDate     string                    `json:"end_date"`
		RoutingType uint8                     `json:"routing_type"`
		WeightRange []*persistent.WeightRange `json:"weight_range,omitempty"`
		Region      string                    `json:"region"`
		CurrentPage int                       `json:"current_page"`
		PageSize    int                       `json:"page_size"`
	}

	QueryOrderAggregationResponse struct {
		RetCode int                       `json:"retcode"`
		Message string                    `json:"message"`
		Data    QueryOrderAggregationList `json:"data"`
	}
	QueryOrderAggregationList struct {
		List []AggregationData `json:"list"`
	}
	AggregationData struct {
		Date         string  `json:"date"`
		LaneCode     string  `json:"lane_code"`
		WeightRange  string  `json:"weight_range"`
		BuyerStateId int64   `json:"buyer_state_id"`
		BuyerCityId  int64   `json:"buyer_city_id"`
		Quantity     int     `json:"log_qty_1d"`
		ShippingFee  float64 `json:"shipping_fee_local_1d"`
	}

	QueryOrderAggregationResponseV2 struct {
		RetCode int                         `json:"retcode"`
		Message string                      `json:"message"`
		Data    QueryOrderAggregationListV2 `json:"data"`
	}
	QueryOrderAggregationListV2 struct {
		List []AggregationDataV2 `json:"list"`
	}
	AggregationDataV2 struct {
		OrderCreateDate string  `json:"request_date" db:"request_date"`
		LaneCode        string  `json:"lane_code" db:"lane_code"`
		WeightRange     string  `json:"weight_range" db:"weight_range"`
		BuyerStateId    int64   `json:"buyer_state_id" db:"buyer_state_id"`
		BuyerCityId     int64   `json:"buyer_city_id" db:"buyer_city_id"`
		Quantity        int     `json:"log_qty_1d" db:"log_qty_1d"`
		ShippingFee     float64 `json:"allocation_shipping_fee_per_order" db:"allocation_shipping_fee_per_order"`
		InActualPoint   string  `json:"in_ap" db:"in_ap"`
		OutActualPoint  string  `json:"out_ap" db:"out_ap"`
	}

	QueryOrderDetailReq struct {
		ProductId   int    `json:"product_id"`
		RoutingType int    `json:"routing_type"`
		StartDate   string `json:"start_date"`
		EndDate     string `json:"end_date"`
		PageCount   int    `json:"page_count"`
		PageOffset  int    `json:"page_offset"`
		Region      string `json:"region"`
	}

	QueryOrderDetailResp struct {
		RetCode int                        `json:"retcode"`
		Message string                     `json:"message"`
		Data    QueryOrderDetailRespDetail `json:"data"`
	}

	QueryOrderDetailRespDetail struct {
		TotalSize       int               `json:"total_size"`
		PageSize        int               `json:"page_size"`
		PageNo          int               `json:"page_no"`
		OrderDetailList []RealOrderDetail `json:"list"` // 存的是订单用zstd压缩过的
	}
	RealOrderDetail struct {
		MessageBody string `json:"message_body"`
	}

	QueryCountResp struct {
		OrderDate  string `json:"order_date" db:"order_date"`
		OrderCount int    `json:"order_count" db:"order_count"`
	}
)

func (q *QueryOrderDetailResp) IsSuccess() bool {
	return q != nil && q.RetCode == 0
}
