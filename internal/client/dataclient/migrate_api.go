package dataclient

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/clickhouseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/esutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
)

func (receiver *DataApi) GetMaskingAllocationPathListV3(ctx context.Context, requestId string,
	orderId, fOrderId uint64,
	pageNo, pageSize int) (*allocpath.ListDataInfoV2, *srerr.Error) {
	cond, orderBy := genMaskAllocationPathList(requestId, orderId, fOrderId)
	idx := configutil.GetEsConf(ctx).AllocationPathIndex
	idx = strings.ReplaceAll(idx, "{cid}", envvar.GetCIDLower())
	esResponse, err := receiver.QueryES(ctx, idx, cond, orderBy, pageNo, pageSize)
	if err != nil {
		return nil, err
	}

	rsp := convertSearchResult(esResponse)
	rsp.PageSize = pageSize
	rsp.PageNo = pageNo

	return rsp, nil
}

func (receiver *DataApi) QueryES(ctx context.Context, index string, condition map[string]interface{}, orderBy []map[string]interface{},
	pageNo, pageSize int) (*esutil.EsResponse, *srerr.Error) {
	req := esutil.SearchRequest{
		Index:       index,
		CurrentPage: (pageNo - 1) * pageSize,
		PageSize:    pageSize,
		Condition:   condition,
		OrderBy:     orderBy,
	}
	searchResult, err := esutil.ExactSearch(ctx, req)
	if err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return &searchResult, nil
}

func genMaskAllocationPathList(requestId string,
	orderId, fOrderId uint64) (map[string]interface{}, []map[string]interface{}) {
	cond := map[string]interface{}{}
	if requestId != "" {
		cond[RequestIdMapping] = requestId
	}
	if orderId != 0 {
		cond[OrderIdMapping] = orderId
	}
	if fOrderId != 0 {
		cond[ForderIdMapping] = fOrderId
	}
	return cond, []map[string]interface{}{
		{"request_time": "desc"},
	}
}

func convertSearchResult(response *esutil.EsResponse) *allocpath.ListDataInfoV2 {
	res := &allocpath.ListDataInfoV2{}
	res.Total = response.Hits.Total.Value
	for _, hit := range response.Hits.Hits {
		marshalJSON, _ := hit.Source.MarshalJSON()
		baseInfo := allocpath.ListBasicInfo{}
		_ = json.Unmarshal(marshalJSON, &baseInfo)
		res.List = append(res.List, baseInfo)
	}
	return res
}

func (receiver *DataApi) GetMaskingAllocationPathDetailV3(ctx context.Context, rowKey string) (*allocpath.DetailV2, *srerr.Error) {
	table := configutil.GetMainHbaseConfig(ctx).TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
	detailBytes, err := getMaskAllocationPathDetail(ctx, table, rowKey)
	if err != nil {
		return nil, err
	}
	detail := &allocpath.DetailV2{}
	if err := json.Unmarshal(detailBytes, detail); err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return detail, nil
}

func getMaskAllocationPathDetail(ctx context.Context, table, rowKey string) ([]byte, *srerr.Error) {
	rspBytes, err := masking_forecast_hbase.GetFromMainByRowKey(ctx, table, rowKey)
	if err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	decompress, err := zip.ZSTDDecompress(rspBytes)
	if err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return decompress, nil
}

func (receiver *DataApi) GetRoutingLogListV2(ctx context.Context, requestId, forderId string, pageno, pagesize int) (*routing.RoutingVisualListResp, *srerr.Error) {
	cond, orderBy := genRoutingAllocationPathList(requestId, forderId)
	idx := configutil.GetEsConf(ctx).RoutingPathIndex
	idx = strings.ReplaceAll(idx, "{cid}", envvar.GetCIDLower())
	esResponse, err := receiver.QueryES(ctx, idx, cond, orderBy, pageno, pagesize)
	if err != nil {
		return nil, err
	}

	rsp := convertSearchResultToRouting(esResponse)
	rsp.Pageno = pageno
	rsp.Count = pagesize

	return rsp, nil
}

func (receiver *DataApi) GetRoutingLogDetailV2(ctx context.Context, rowKey string) (*RoutingVisualDataDetailResp, *srerr.Error) {
	table := configutil.GetDataHbaseConfig(ctx).TableNameMap[masking_forecast_hbase.ROUTING_LOG]
	logBytes, err := receiver.hbaseHelper.GetDataFromHbaseByRowKey(ctx, table, rowKey)
	if err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	decompress, err := zip.ZSTDDecompress(logBytes)
	if err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	detail := routing_log.RoutingLog{}
	if err := json.Unmarshal(decompress, &detail); err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}
	rsp := RoutingVisualDataDetailResp{}
	rsp.Data.LogEntry = detail
	return &rsp, nil
}

func convertSearchResultToRouting(response *esutil.EsResponse) *routing.RoutingVisualListResp {
	res := &routing.RoutingVisualListResp{}
	res.Total = response.Hits.Total.Value
	for _, hit := range response.Hits.Hits {
		marshalJSON, _ := hit.Source.MarshalJSON()
		baseInfo := routing.ListBaseInfo{}
		_ = json.Unmarshal(marshalJSON, &baseInfo)
		baseInfo.ForderId = baseInfo.EsFOrderId
		res.List = append(res.List, baseInfo)
	}
	return res
}

func genRoutingAllocationPathList(requestId string, fOrderId string) (map[string]interface{}, []map[string]interface{}) {
	cond := map[string]interface{}{}
	if requestId != "" {
		cond[RequestIdMapping] = requestId
	}

	if fOrderId != "" {
		cond[ForderIdMappingForRoutingLog] = fOrderId
	}
	return cond, []map[string]interface{}{
		{"request_time": "desc"},
	}
}

func (receiver *DataApi) OrderAggregationV3(ctx context.Context, req QueryOrderAggregationRequest) (*QueryOrderAggregationResponseV2, *srerr.Error) {
	// 如果weight range为空则查询的是订单数量
	weightRangeColSql, conditionSql := generateWeightRangeSql(req.WeightRange)
	var (
		aggResultV2 []AggregationDataV2
		resp        QueryOrderAggregationResponseV2
		offset      = req.CurrentPage * req.PageSize
		pageSize    = req.PageSize
	)
	db, table := clickhouseutil.GetClickhouseDbAndTable()
	realSql := fmt.Sprintf(clickhouseutil.AggregationSql, weightRangeColSql, db, table, req.ProductId, int(req.RoutingType), envvar.GetCID(), req.StartDate, req.EndDate, conditionSql, offset, pageSize)
	if err := clickhouseutil.Select(ctx, realSql, &aggResultV2); err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	resp.Data.List = aggResultV2
	return &resp, nil
}

func (receiver *DataApi) QueryOrderAggregationCount(ctx context.Context, req QueryOrderAggregationRequest) (int64, *srerr.Error) {
	var totalCount int64
	weightRangeColSql, conditionSql := generateWeightRangeSql(req.WeightRange)
	db, table := clickhouseutil.GetClickhouseDbAndTable()
	realSql := fmt.Sprintf(clickhouseutil.AggregationOrderCountSql, weightRangeColSql, db, table, req.ProductId, int(req.RoutingType), envvar.GetCID(), req.StartDate, req.EndDate, conditionSql)
	if err := clickhouseutil.Select(ctx, realSql, &totalCount); err != nil {
		return 0, srerr.With(srerr.DataApiErr, nil, err)
	}

	return totalCount, nil
}

func (receiver *DataApi) QueryOrderCountByDay(ctx context.Context, productId, routingType int, orderStartDate, orderEndDate string) ([]QueryCountResp, *srerr.Error) {
	var orderCountByDay []QueryCountResp
	db, table := clickhouseutil.GetClickhouseDbAndTable()
	realSql := fmt.Sprintf(clickhouseutil.QueryCountByDaySql, db, table, productId, routingType, envvar.GetCID(), orderStartDate, orderEndDate)
	if err := clickhouseutil.Select(ctx, realSql, &orderCountByDay); err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return orderCountByDay, nil
}

func (receiver *DataApi) QueryOrderDetailByPage(ctx context.Context, productId, routingType int, orderDate string, totalPage, currentPage int) ([]string, *srerr.Error) {
	var orderInfoList []string
	db, table := clickhouseutil.GetClickhouseDbAndTable()
	realSql := fmt.Sprintf(clickhouseutil.QueryDetailSql, db, table, productId, routingType, envvar.GetCID(), orderDate, orderDate, totalPage, currentPage)

	if err := clickhouseutil.Select(ctx, realSql, &orderInfoList); err != nil {
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return orderInfoList, nil
}

func convertToQueryOrderAggregationResponseV2(orderCountByDayList []QueryCountResp) *QueryOrderAggregationResponseV2 {
	aggResult := &QueryOrderAggregationResponseV2{}
	aggResult.Data = QueryOrderAggregationListV2{}
	aggResult.Data.List = make([]AggregationDataV2, 0, len(orderCountByDayList))
	for _, orderDetail := range orderCountByDayList {
		aggResult.Data.List = append(aggResult.Data.List, AggregationDataV2{
			OrderCreateDate: orderDetail.OrderDate,
			Quantity:        orderDetail.OrderCount,
		})
	}

	return aggResult
}

func generateWeightRangeSql(weightRange []*persistent.WeightRange) (string, string) {

	var (
		columnSql    = ""
		conditionSql = make([]string, 0)
	)
	for _, wr := range weightRange {
		columnSql += fmt.Sprintf("WHEN weight >= %d and weight < %d THEN '%d-%d' ", wr.Min, wr.Max, wr.Min, wr.Max)
		conditionSql = append(conditionSql, fmt.Sprintf("weight BETWEEN %d AND %d", wr.Min, wr.Max))
	}

	finalSql := "CASE " + columnSql + "END"
	return finalSql, strings.Join(conditionSql, " or ")
}
