package dataclient

import (
	jsoniter "github.com/json-iterator/go"
	"testing"
)

func TestJwt(t *testing.T) {
	//url := "https://data.ssc.test.shopeemobile.com/data_api/data_sls/waybill_center/portal/slo/history/scan"
	//routingLogAccount := "wbc-admin"
	//routingLogSecretNonLive := "test666"
	//jwt, err := BuildDataApiJwt(nil, routingLogAccount, routingLogSecretNonLive)
	//if err != nil {
	//	panic(err)
	//}
	//
	//respData, err2 := httputil.PostJson(context.TODO(), url, []byte("nil"), defaultTimeout, map[string]string{
	//	"jwt-token":    jwt,
	//	"Content-Type": "application/json",
	//})
	//
	//if err2 != nil {
	//	panic(err2)
	//}
	//
	//println(string(respData))
	jsonData := "{\n    \"retcode\": 0,\n    \"message\": \"Success.\",\n    \"data\": [\n        {\n            \"date\": \"2023-08-11\",\n            \"lane_code\": \"L-BR-213\",\n            \"buyer_state_id\": ********,\n            \"buyer_city_id\": ********,\n            \"in_ap\": \"\",\n            \"out_ap\": \"\",\n            \"weight_range\": \"0-**********\",\n            \"log_qty_1d\": 2,\n            \"allocation_shipping_fee_per_order\": 8.45\n        },\n        {\n            \"date\": \"2023-08-12\",\n            \"lane_code\": \"L-BR-200\",\n            \"buyer_state_id\": ********,\n            \"buyer_city_id\": ********,\n            \"in_ap\": \"\",\n            \"out_ap\": \"\",\n            \"weight_range\": \"0-**********\",\n            \"log_qty_1d\": 5,\n            \"allocation_shipping_fee_per_order\": 20.734\n        }\n    ]\n}"
	resp := QueryOrderAggregationResponseV2{}

	if err := jsoniter.Unmarshal([]byte(jsonData), &resp); err != nil {
		panic(err)
	}

	println()
}
