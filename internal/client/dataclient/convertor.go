package dataclient

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"strconv"
)

type MaskingPanelConvertor interface {
	//将从data api获取的结果转换成smart routing FE所需要的结构体
	ConvertToMaskingKeyDataList(ctx context.Context, aggregatedInfoList []AggregatedInfo,
		productIDNameMap map[string]string, maskProductDetail map[int64]*lpsclient.ProductDetailInfo) (aggregatedKeyDataList []masking_panel.AggregatedKeyData)
}

type HourlyConvertor struct {
}

type DailyConvertor struct {
}

func NewHourlyConvertor() MaskingPanelConvertor {
	return &HourlyConvertor{}
}

func NewDailyConvertor() MaskingPanelConvertor {
	return &DailyConvertor{}
}

func (c *HourlyConvertor) ConvertToMaskingKeyDataList(ctx context.Context, aggregatedInfoList []AggregatedInfo,
	productIDNameMap map[string]string, maskProductDetail map[int64]*lpsclient.ProductDetailInfo) (aggregatedKeyDataList []masking_panel.AggregatedKeyData) {
	//由于hourly维度的数据存在多图的情况，所以这里需要定义一个二维map，把数据抽成 "按天去分图",
	//第一维key是date，value是该date下所有的product信息的map；第二维的key是product id， value是该product id下所有的x，y轴信息（即这条线的信息）
	//ps：一天就是一张图；然后一张图里有多条线，一条线就是一个product id及其对应的所有聚合信息，包括x轴坐标，y轴坐标
	//1.abstract data into a graph map, which is a date[product_id[line_info_list]] 2-dimension map
	graphMap := make(map[string]map[string][]masking_panel.MaskingLineItem, 0) //init 2-dimension map
	for _, aggregatedInfo := range aggregatedInfoList {
		lineID := strconv.FormatUint(aggregatedInfo.ProductID, 10) //a line is about the total info for a product

		//组装所有的x，y轴数据
		for _, aggregateData := range aggregatedInfo.AggregateDataList {
			dayTime, hourTime := timeutil.SplitDateToDayAndHour(ctx, aggregateData.Datetime)
			tempAggregateData := masking_panel.MaskingLineItem{
				XAxis: hourTime,                               //hour维度把hour当作x轴
				YAxis: strconv.Itoa(aggregateData.OrderCount), //y-axis
			}
			//convert dateTime to day time and hour time
			//init if graph of the date is not existed
			if _, graphExisted := graphMap[dayTime]; !graphExisted {
				graphMap[dayTime] = make(map[string][]masking_panel.MaskingLineItem, 0)
			}
			//init if line of the graph is not existed
			if _, lineExisted := graphMap[dayTime][lineID]; !lineExisted {
				graphMap[dayTime][lineID] = make([]masking_panel.MaskingLineItem, 0)
			}
			//append item into line
			graphMap[dayTime][lineID] = append(graphMap[dayTime][lineID], tempAggregateData)
		}
	}

	//convert graph map to aggregated key data, including graph name and line list
	for aggregatedDate, productLinesMap := range graphMap {
		//init graph
		tempAggregatedKeyData := masking_panel.AggregatedKeyData{
			GraphName: aggregatedDate,
		}
		tempLineList := make([]masking_panel.MaskingDataLine, 0)
		// 按照 Mask Channel, F Channel, F Channel, F Channel, Mask Channel, F Channel...的顺序排列
		for maskProductId, productDetail := range maskProductDetail {
			// 先append Mask Channel
			maskProductIdStr := strconv.Itoa(int(maskProductId))
			lineItemList, ok := productLinesMap[maskProductIdStr]
			if !ok {
				logger.CtxLogErrorf(ctx, "can not get product's lines, mask product: %d", maskProductId)
				continue
			}
			tempLineList = append(tempLineList, masking_panel.MaskingDataLine{
				LineID:        maskProductIdStr,
				LineName:      productIDNameMap[maskProductIdStr],
				AggregateData: lineItemList,
			})
			// 再append该Mask Channel下属的F Channel
			for _, componentProductId := range productDetail.GetComponentProduct().ComponentProducts {
				componentProductIdStr := strconv.Itoa(componentProductId)
				lineItemList, ok := productLinesMap[componentProductIdStr]
				if !ok {
					logger.CtxLogErrorf(ctx, "can not get product's lines, component product: %d", componentProductId)
					continue
				}
				tempLineList = append(tempLineList, masking_panel.MaskingDataLine{
					LineID:        componentProductIdStr,
					LineName:      productIDNameMap[componentProductIdStr],
					AggregateData: lineItemList,
				})
			}
		}

		// 遍历Data返回的所有数据，对于单独请求的F Channel，在最后append
		for productIdStr, productLines := range productLinesMap {
			var isContained bool
			for _, line := range tempLineList {
				if line.LineID == productIdStr {
					isContained = true
					break
				}
			}
			// 如果已经append在response里，忽略，判断下一个
			if isContained {
				continue
			}

			// 将剩余的append到response里
			tempLineList = append(tempLineList, masking_panel.MaskingDataLine{
				LineID:        productIdStr,
				LineName:      productIDNameMap[productIdStr],
				AggregateData: productLines,
			})
		}

		//add line list into graph
		tempAggregatedKeyData.LineList = tempLineList
		//append graph
		aggregatedKeyDataList = append(aggregatedKeyDataList, tempAggregatedKeyData)
	}

	sort.Slice(aggregatedKeyDataList, func(i, j int) bool {
		return aggregatedKeyDataList[i].GraphName < aggregatedKeyDataList[j].GraphName
	})

	return aggregatedKeyDataList
}

func (d *DailyConvertor) ConvertToMaskingKeyDataList(ctx context.Context, aggregatedInfoList []AggregatedInfo,
	productIDNameMap map[string]string, maskProductDetail map[int64]*lpsclient.ProductDetailInfo) (aggregatedKeyDataList []masking_panel.AggregatedKeyData) {
	//as for daily granularity
	//由于daily维度的数据由一张图即可涵盖，所以这里只需要把所有的聚合数据转换成不同的线即可
	//一条线就是一个product id及其对应的所有聚合信息，包括x轴坐标，y轴坐标
	var timeList []string
	tempAggregatedKeyData := masking_panel.AggregatedKeyData{}       //天维度的数据只会有一张图来展示（15天内的数据）
	productLineMap := make(map[uint64]masking_panel.MaskingDataLine) //用来存储所有的线， 一条线由一个product id及其对应的所有x轴坐标，y轴坐标组成
	for _, aggregatedInfo := range aggregatedInfoList {              //data这边的数据是按product id返回的， 所以一个aggregatedInfo就对应前端这边一条线的数据
		productID := strconv.FormatUint(aggregatedInfo.ProductID, 10)
		//init line
		line := masking_panel.MaskingDataLine{
			LineID:   productID,
			LineName: productIDNameMap[productID],
		}
		//build up all x_axis,y_axis of a line
		for _, aggregateData := range aggregatedInfo.AggregateDataList {
			dayTime, _ := timeutil.SplitDateToDayAndHour(ctx, aggregateData.Datetime)
			tempAggregateData := masking_panel.MaskingLineItem{
				XAxis: dayTime, //day维度把date当作x轴
				YAxis: strconv.Itoa(aggregateData.OrderCount),
			}
			line.AggregateData = append(line.AggregateData, tempAggregateData)
			//存储所有的date
			timeList = append(timeList, dayTime)
		}
		//append line into line list
		productLineMap[aggregatedInfo.ProductID] = line
	}

	// 按照Mask Channel, F Channel, F Channel, Mask Channel, F Channel的下顺序进行排序
	sortLineList := make([]masking_panel.MaskingDataLine, 0, len(productLineMap))
	for maskProductId, productDetail := range maskProductDetail {
		maskLine, ok := productLineMap[uint64(maskProductId)]
		if !ok {
			logger.CtxLogErrorf(ctx, "can not find product's line, product id: %d", maskProductId)
			continue
		}
		// 先append Mask Channel
		sortLineList = append(sortLineList, maskLine)
		// append完后即可在map中清除，方便在下步中找到剩余未append的
		delete(productLineMap, uint64(maskProductId))

		for _, componentProductId := range productDetail.GetComponentProduct().ComponentProducts {
			componentLine, ok := productLineMap[uint64(componentProductId)]
			if !ok {
				logger.CtxLogErrorf(ctx, "can not find product's line, product id: %d", componentProductId)
				continue
			}
			// 再append Mask Channel下属的F Channel
			sortLineList = append(sortLineList, componentLine)
			// append完后即可在map中清除，方便在下步中找到剩余未append的
			delete(productLineMap, uint64(componentProductId))
		}
	}
	// 对于单独请求的F Channel，在最后append
	for _, line := range productLineMap {
		sortLineList = append(sortLineList, line)
	}
	tempAggregatedKeyData.LineList = sortLineList

	//升序排序并去重
	sort.Strings(timeList)
	deduplicatedTimeList := objutil.RemoveDuplicatedStrings(timeList)
	tempAggregatedKeyData.GraphName = fmt.Sprintf("%v ~ %v", deduplicatedTimeList[0], deduplicatedTimeList[len(deduplicatedTimeList)-1])

	aggregatedKeyDataList = append(aggregatedKeyDataList, tempAggregatedKeyData)
	return aggregatedKeyDataList
}
