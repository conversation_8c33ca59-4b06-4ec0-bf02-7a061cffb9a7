package dataclient

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/dgrijalva/jwt-go"
	jsoniter "github.com/json-iterator/go"
)

const (
	defaultTimeout     = 10
	QueryOrderCount    = "/data_api/data_sls/smart_routing/routing/metrics/log_qty_1d"
	OrderAggregation   = "/data_api/data_sls/smart_routing/routing/metrics/custom_weight_range/log_distribution"
	OrderAggregationV2 = "/data_api/data_sls/smart_routing/third_party_masking/v3/query_aggregate"
	QueryOrderDetail   = "/data_api/data_sls/smart_routing/third_party_masking/v3/query_detail"
)

type DataApi struct {
	hbaseHelper *masking_forecast_hbase.HBHelper
}

func NewDataApi() *DataApi {
	return &DataApi{hbaseHelper: masking_forecast_hbase.NewHBHelper()}
}

func (receiver *DataApi) SetLogForDataApi(ctx context.Context, api string, req interface{}) func() {
	start := recorder.Now(ctx)
	return func() {
		duration := time.Since(start)
		operator, _ := apiutil.GetUserInfo(ctx)
		reqStr, _ := jsoniter.MarshalToString(req)
		logger.CtxLogInfof(ctx, "DataAPI request|url=%s, operator=%s, req=%s, cost=%v", api, operator, reqStr, duration)
	}
}

// 这个要下线，不需要迁移
func (receiver *DataApi) GetMaskingResultListV2(ctx context.Context, req GetPanelFromDataRequest, requestID string) (ResultPanel, *srerr.Error) {
	dataConf := configutil.GetDataConf(ctx)
	defer receiver.SetLogForDataApi(ctx, client.GetMaskingResultListEndPointV2, req)()

	var response GetPanelFromDataResp
	//generate jwt for data api
	headers, err := GenJwtReqForDataV2(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingResultList|convert req to jwt for data err:%v", err)
		return response.Data, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	//add request id into header
	headers["X-Request-ID"] = requestID

	body, _ := jsoniter.Marshal(&req)
	url := objutil.Merge(dataConf.Host, client.GetMaskingResultListEndPointV2)

	data, gErr := httputil.PostJson(ctx, url, body, 10, headers)
	if gErr != nil {
		return response.Data, srerr.With(srerr.DataApiErr, nil, gErr)
	}
	//unmarshal response and return
	if err := jsoniter.Unmarshal(data, &response); err != nil {
		return response.Data, srerr.With(srerr.FormatErr, nil, err)
	}

	if response.Retcode != 0 {
		logger.CtxLogErrorf(ctx, "GetMaskingResultList| retcode is not 0, resp:%v", response)
		return response.Data, srerr.New(srerr.DataApiErr, nil, "invalid response:%s", objutil.JsonString(response))
	}

	return response.Data, nil
}

func BuildDataApiJwt(ctx context.Context, data interface{}, account, secret string) (string, error) {
	dataInfo := DataApiInfo{
		TimeStamp: int(timeutil.GetCurrentUnixTimeStamp(ctx)),
		Data:      data,
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, dataInfo)
	token.Header["account"] = account
	ss, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}
	return ss, nil
}

func BuildDataApiJwtV2(ctx context.Context, secret string) (string, error) {
	dataInfo := DataApiInfoV2{
		TimeStamp: int(timeutil.GetCurrentUnixTimeStamp(ctx)),
		Region:    envvar.GetCIDLower(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, dataInfo)
	token.Header["account"] = client.DataAccountV2
	ss, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}
	return ss, nil
}

func (receiver *DataApi) GetMaskingAllocationPathListV2(ctx context.Context, requestId string,
	orderId, fOrderId uint64,
	pageNo, pageSize int) (*allocpath.ListDataInfoV2, *srerr.Error) {
	// 开启切换走迁移后的代码
	if configutil.AllocationPathSwitch() {
		return receiver.GetMaskingAllocationPathListV3(ctx, requestId, orderId, fOrderId, pageNo, pageSize)
	}
	var response *allocpath.ListResponseV2
	req := GenListRequestV2(ctx, requestId, orderId, fOrderId, pageNo, pageSize)
	dataConf := configutil.GetDataConf(ctx)
	url := objutil.Merge(dataConf.Host, client.GetMaskingAllocationPathListEndPointV2)
	defer receiver.SetLogForDataApi(ctx, client.GetMaskingAllocationPathListEndPointV2, req)()

	headers, gErr := GenJwtReqForDataV2(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList|generate headers err:%v", gErr)
		return nil, gErr
	}
	body, bErr := jsoniter.Marshal(&req)
	if bErr != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList| Marshal error, req:%v, error:%s", req, bErr.Error())
		return nil, srerr.With(srerr.JsonErr, nil, bErr)
	}

	data, err := httputil.PostJson(ctx, url, body, 10, headers)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList| DataApiError, url:%s, error:%s", url, err.Error())
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	if err := jsoniter.Unmarshal(data, &response); err != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList| Marshal error, req:%v, error:%s", req, err.Error())
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}

	if response.RetCode != 0 {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList| retcode is not 0, resp:%v", response)
		return nil, srerr.New(srerr.DataApiErr, nil, "invalid response:%s", objutil.JsonString(response))
	}

	return &response.Data, nil
}

// V2版本jwt放在请求头中
func GenJwtReqForDataV2(ctx context.Context) (map[string]string, *srerr.Error) {
	//generate jwt for data api
	conf := configutil.GetAllocationPathConf(ctx)
	secret := client.DataSecretV2
	if conf.NewSecret != "" {
		secret = conf.NewSecret
	}
	dataJwt, dErr := BuildDataApiJwtV2(ctx, secret)
	if dErr != nil {
		return nil, srerr.With(srerr.JwtTokenErr, nil, dErr)
	}
	//marshal jwt and request for data api
	headers := make(map[string]string, 0)
	headers["jwt-token"] = dataJwt
	return headers, nil
}

// V2接口，data侧使用新的row key从hbase检索数据
func (receiver *DataApi) GetMaskingAllocationPathDetailV2(ctx context.Context, rowKey string) (*allocpath.DetailV2, *srerr.Error) {
	if configutil.AllocationPathSwitch() {
		return receiver.GetMaskingAllocationPathDetailV3(ctx, rowKey)
	}
	var response allocpath.DetailResponseV2
	req := GenDetailRequestV2(ctx, rowKey)
	dataConf := configutil.GetDataConf(ctx)
	url := objutil.Merge(dataConf.Host, client.GetMaskingAllocationPathDetailEndPointV2)
	defer receiver.SetLogForDataApi(ctx, client.GetMaskingAllocationPathDetailEndPointV2, req)()

	headers, gErr := GenJwtReqForDataV2(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList|generate headers err:%v", gErr)
		return nil, gErr
	}

	body, bErr := jsoniter.Marshal(&req)
	if bErr != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathList| Marshal error, req:%v, error:%s", req, bErr.Error())
		return nil, srerr.With(srerr.JsonErr, nil, bErr)
	}

	data, err := httputil.PostJson(ctx, url, body, 10, headers)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathDetail| DataApiError, url:%s, error:%s", url, err.Error())
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	if err := jsoniter.Unmarshal(data, &response); err != nil {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathDetail| Marshal error, req:%v, error:%s, body:%s", req, err.Error(), string(data))
		return nil, srerr.With(srerr.FormatErr, nil, err)
	}

	if response.RetCode != 0 {
		logger.CtxLogErrorf(ctx, "GetMaskingAllocationPathDetail| retcode is not 0, resp:%v", response)
		return nil, srerr.New(srerr.DataApiErr, nil, "invalid response:%s", objutil.JsonString(response))
	}

	return response.Data.Message, nil
}

func (recevier *DataApi) GetRoutingLogList(ctx context.Context, requestId, forderId, region string, pageno, pagesize int) (*routing.RoutingVisualListResp, *srerr.Error) {
	if configutil.AllocationPathSwitch() {
		return recevier.GetRoutingLogListV2(ctx, requestId, forderId, pageno, pagesize)
	}
	req := RoutingVisualDataListReq{
		RequestId: requestId,
		ForderId:  forderId,
		Region:    region,
		PageNo:    pageno,
		PageSize:  pagesize,
	}
	dataConf := configutil.GetDataConf(ctx)
	url := objutil.Merge(dataConf.Host, dataConf.RoutingLogListEndPoint)
	dataJwt, err := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingLogList|convert req to jwt for data err:%v", err)
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	//marshal jwt and request for data api
	body, jsonErr := jsoniter.Marshal(req)
	if jsonErr != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingLogList|convert seq failed for data err:%v", err)
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}
	respData, err := httputil.PostJson(ctx, url, body, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})
	if err != nil {
		return nil, srerr.New(srerr.DataApiErr, nil, "request error %+v", err)
	}
	resp := RoutingVisualDataListResp{}
	jsonErr = jsoniter.Unmarshal(respData, &resp)
	if jsonErr != nil {
		return nil, srerr.New(srerr.DataApiErr, nil, "json error resp %+v", jsonErr)
	}

	if resp.Retcode != 0 {
		return nil, srerr.New(srerr.DataApiErr, nil, resp.Message)
	}

	return buildResponse(resp), nil
}

func (receiver *DataApi) GetRoutingLogDetail(ctx context.Context, rowKey string) (*RoutingVisualDataDetailResp, *srerr.Error) {
	if configutil.AllocationPathSwitch() {
		return receiver.GetRoutingLogDetailV2(ctx, rowKey)
	}
	dataConf := configutil.GetDataConf(ctx)
	url := objutil.Merge(dataConf.Host, dataConf.RoutingLogDetailEndPoint)
	dataJwt, err := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingLogList|convert req to jwt for data err:%v", err)
		return nil, srerr.With(srerr.JwtTokenErr, nil, err)
	}

	param := map[string]string{}
	param["row_key"] = rowKey
	param["region"] = envvar.GetCID()
	marshal, err := jsoniter.Marshal(param)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingLogList|convert seq failed for data err:%v", err)
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}
	respData, err := httputil.PostJson(ctx, url, marshal, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})

	log := &RoutingVisualDataDetailResp{}
	seqErr := jsoniter.Unmarshal(respData, log)
	if seqErr != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingLogList|convert seq failed for data err:%v", err)
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	return log, nil
}
func buildResponse(resp RoutingVisualDataListResp) *routing.RoutingVisualListResp {
	res := &routing.RoutingVisualListResp{}
	res.Count = resp.Data.Size
	res.Total = resp.Data.Total
	res.Pageno = resp.Data.Page
	res.List = []routing.ListBaseInfo{}

	for _, v := range resp.Data.List {
		temp := routing.ListBaseInfo{}
		temp.RequestId = v.RequestId
		temp.SlsTn = v.SlsTn
		temp.ProductId = v.ProductId
		temp.ForderId = v.ForderId
		temp.RequestTime = v.RequestTime
		temp.RoutingStatus = v.RoutingStatus
		temp.RowKey = v.RowKey
		res.List = append(res.List, temp)
	}

	return res
}

// V2接口，data侧使用新的row key从hbase检索数据
func GenListRequestV2(ctx context.Context, requestId string, orderId, fOrderId uint64, pageNo, pageSize int) *allocpath.ListRequestV2 {
	//page no && page size must >= 1
	if pageNo == 0 {
		pageNo = 1
	}
	if pageSize == 0 {
		pageSize = 1
	}
	resp := &allocpath.ListRequestV2{
		Region:   envvar.GetCIDLower(),
		PageNo:   pageNo,
		PageSize: pageSize,
	}
	if requestId != "" {
		resp.RequestId = requestId
	}
	if orderId != 0 {
		resp.OrderId = orderId
	}
	if fOrderId != 0 {
		resp.FOrderId = strconv.FormatUint(fOrderId, 10)
	}
	return resp
}

func GenDetailRequestV2(ctx context.Context, rowKey string) *allocpath.DetailRequestV2 {
	return &allocpath.DetailRequestV2{
		RowKey: rowKey,
		Region: envvar.GetCIDLower(),
	}
}

// 这个接口会废弃掉
func (receiver *DataApi) QueryOrderCount(ctx context.Context, productId uint64, routingTpe int, startDate, endDate string) (*QueryOrderCountResp, *srerr.Error) {
	req := &QueryOrderCountReq{
		productId, routingTpe, startDate, endDate, envvar.GetCID(),
	}
	dataConf := configutil.GetDataConf(ctx)
	dataJwt, jwtErr := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if jwtErr != nil {
		logger.CtxLogErrorf(ctx, "QueryOrderCount|convert req to jwt for data err:%v", jwtErr)
		return nil, srerr.With(srerr.JwtTokenErr, nil, jwtErr)
	}

	url := dataConf.Host + QueryOrderCount
	reqBytes, mslErr := jsoniter.Marshal(req)
	if mslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count|convert seq failed for data err:%v, source data is %+v", mslErr, req)
		return nil, srerr.With(srerr.DataApiErr, nil, mslErr)
	}

	respData, httpErr := httputil.PostJson(ctx, url, reqBytes, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})
	if httpErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count from data resp err:%v", httpErr)
		return nil, srerr.With(srerr.DataApiErr, nil, httpErr)
	}

	resp := &QueryOrderCountResp{}
	if umslErr := jsoniter.Unmarshal(respData, resp); umslErr != nil || resp == nil {
		logger.CtxLogErrorf(ctx, "Query order count unmarshal err:%v resp is %+v", umslErr, resp)
	}
	if resp != nil && resp.Retcode != 0 {
		return nil, srerr.With(srerr.DataApiErr, nil, errors.New(resp.Message))
	}

	return resp, nil
}

/*
*订单数据聚合业务可以在ops上选择聚合选项，历史订单聚合调用data那边接口拿数据，data那边只做简单聚合，详细聚合在==>AggregationOrderData
 */
func (receiver *DataApi) OrderAggregation(ctx context.Context, req QueryOrderAggregationRequest) (*QueryOrderAggregationResponse, *srerr.Error) {
	dataConf := configutil.GetDataConf(ctx)
	dataJwt, jwtErr := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if jwtErr != nil {
		logger.CtxLogErrorf(ctx, "QueryOrderCount|convert req to jwt for data err:%v", jwtErr)
		return nil, srerr.With(srerr.JwtTokenErr, nil, jwtErr)
	}

	url := dataConf.Host + OrderAggregation
	reqBytes, mslErr := jsoniter.Marshal(req)
	if mslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count|convert seq failed for data err:%v, source data is %+v", mslErr, req)
		return nil, srerr.With(srerr.DataApiErr, nil, mslErr)
	}

	respData, httpErr := httputil.PostJson(ctx, url, reqBytes, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})
	if httpErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count from data resp err:%+v", httpErr)
		return nil, srerr.With(srerr.DataApiErr, nil, httpErr)
	}

	resp := &QueryOrderAggregationResponse{}
	if umslErr := jsoniter.Unmarshal(respData, resp); umslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count unmarshal err:%+v", umslErr)
	}

	if resp.RetCode != 0 {
		return nil, srerr.With(srerr.DataApiErr, nil, errors.New(resp.Message))
	}

	return resp, nil
}

// 查询订单聚合第二版，如果不穿weight_range就是查询Product对应日期的订单
func (receiver *DataApi) OrderAggregationV2(ctx context.Context, req QueryOrderAggregationRequest) (*QueryOrderAggregationResponseV2, *srerr.Error) {
	if configutil.ForecastSwitch() {
		if req.WeightRange == nil {
			orderCountByDayList, err := receiver.QueryOrderCountByDay(ctx, req.ProductId, int(req.RoutingType), req.StartDate, req.EndDate)
			if err != nil {
				return nil, err
			}
			return convertToQueryOrderAggregationResponseV2(orderCountByDayList), nil
		} else {
			return receiver.OrderAggregationV3(ctx, req)
		}
	}
	dataConf := configutil.GetDataConf(ctx)
	dataJwt, jwtErr := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if jwtErr != nil {
		logger.CtxLogErrorf(ctx, "QueryOrderCount|convert req to jwt for data err:%v", jwtErr)
		return nil, srerr.With(srerr.JwtTokenErr, nil, jwtErr)
	}

	url := dataConf.Host + OrderAggregationV2
	reqBytes, mslErr := jsoniter.Marshal(req)
	if mslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count|convert seq failed for data err:%v, source data is %+v", mslErr, req)
		return nil, srerr.With(srerr.DataApiErr, nil, mslErr)
	}

	respData, httpErr := httputil.PostJson(ctx, url, reqBytes, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})
	if httpErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count from data resp err:%v", httpErr)
		return nil, srerr.With(srerr.DataApiErr, nil, httpErr)
	}

	resp := &QueryOrderAggregationResponseV2{}
	if umslErr := jsoniter.Unmarshal(respData, resp); umslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count unmarshal err:%v", umslErr)
		return nil, srerr.With(srerr.DataApiErr, nil, umslErr)
	}

	if resp.RetCode != 0 {
		return nil, srerr.With(srerr.DataApiErr, nil, errors.New(resp.Message))
	}

	return resp, nil
}

// 查询订单详情，这里的starDate和endDate日期必须是一样的，否则data那边会返回err
func (receiver *DataApi) OrderOrderDetail(ctx context.Context, req QueryOrderDetailReq) (*QueryOrderDetailResp, *srerr.Error) {
	dataConf := configutil.GetDataConf(ctx)
	dataJwt, jwtErr := BuildDataApiJwt(ctx, nil, dataConf.RoutingLogAccount, dataConf.RoutingLogSecret)
	if jwtErr != nil {
		logger.CtxLogErrorf(ctx, "QueryOrderCount|convert req to jwt for data err:%v", jwtErr)
		return nil, srerr.With(srerr.JwtTokenErr, nil, jwtErr)
	}

	url := dataConf.Host + QueryOrderDetail
	reqBytes, mslErr := jsoniter.Marshal(req)
	if mslErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count|convert seq failed for data err:%v, source data is %+v", mslErr, req)
		return nil, srerr.With(srerr.DataApiErr, nil, mslErr)
	}

	respData, httpErr := httputil.PostJson(ctx, url, reqBytes, defaultTimeout, map[string]string{
		"jwt-token":    dataJwt,
		"Content-Type": "application/json",
	})
	if httpErr != nil {
		logger.CtxLogErrorf(ctx, "Query order count from data resp err:%+v", httpErr)
		return nil, srerr.With(srerr.DataApiErr, nil, httpErr)
	}

	resp := &QueryOrderDetailResp{}
	if err := jsoniter.Unmarshal(respData, resp); err != nil {
		logger.CtxLogErrorf(ctx, "Query order count unmarshal err:%+v", err)
		return nil, srerr.With(srerr.DataApiErr, nil, err)
	}

	if resp.RetCode != 0 {
		return nil, srerr.With(srerr.DataApiErr, nil, errors.New(resp.Message))
	}

	return resp, nil
}
