package allocpath

type (
	BaseResponse struct {
		RetCode int    `json:"retcode"`
		Message string `json:"message"`
	}
	// Deprecated
	BasicInfo struct {
		OrderId              uint64 `json:"order_id"`
		FOrderId             uint64 `json:"forder_id"`
		RequestId            string `json:"request_id"`
		UniqueId             string `json:"unique_id"`
		RequestTime          int    `json:"request_time"`
		Status               bool   `json:"status"`
		FulfillmentProductId int64  `json:"fulfillment_product_id"`
		MaskProductId        int64  `json:"mask_product_id"`
	}
	ListBasicInfo struct {
		OrderId              uint64 `json:"order_id"`
		FOrderId             string `json:"forder_id"` //SSCSMR-1312:data用string存储，避免int超过长度后自动专程string
		RequestId            string `json:"request_id"`
		UniqueId             string `json:"unique_id"`
		RequestTime          int    `json:"request_time"`
		Status               bool   `json:"status"`
		FulfillmentProductId int64  `json:"fulfillment_product_id"`
		MaskProductId        int64  `json:"mask_product_id"`
		RowKey               string `json:"row_key"`
	}

	// Deprecated : use v2 instead
	// ListRequest 表示list接口的请求数据
	ListRequest struct {
		RequestId string `json:"request_id,omitempty"`
		OrderId   uint64 `json:"order_id,omitempty"`
		FOrderId  uint64 `json:"forder_id,omitempty"`
		Country   string `json:"country"`
		PageNo    int    `json:"page_no"`
		PageSize  int    `json:"page_size"`
	}
	ListRequestV2 struct {
		RequestId string `json:"request_id,omitempty"`
		OrderId   uint64 `json:"order_id,omitempty"`
		FOrderId  string `json:"forder_id,omitempty"`
		PageNo    int    `json:"page_no"`
		PageSize  int    `json:"page_size"`
		Region    string `json:"region"`
	}
	// Deprecated : use v2 instead
	ListDataInfo struct {
		List     []BasicInfo `json:"list"`
		PageNo   int         `json:"page_no"`
		PageSize int         `json:"page_size"`
		Total    int         `json:"total"`
	}
	ListDataInfoV2 struct {
		List     []ListBasicInfo `json:"list"`
		PageNo   int             `json:"page_no"`
		PageSize int             `json:"page_size"`
		Total    int             `json:"total"`
	}

	// Deprecated : use v2 instead
	ListResponse struct {
		BaseResponse
		Data ListDataInfo `json:"data"`
	}

	ListResponseV2 struct {
		BaseResponse
		Data ListDataInfoV2 `json:"data"`
	}

	// Deprecated : use v2 instead
	//DetailRequest 表示detail请求数据的详情
	DetailRequest struct {
		RequestId string `json:"request_id"`
		Country   string `json:"country"`
	}
	DetailRequestV2 struct {
		RowKey string `json:"row_key"`
		Region string `json:"region"` //小写， id，vn等
	}
	DetailResponseV2 struct {
		BaseResponse
		Data DetailDataV2 `json:"data"`
	}
	//v2版本把detail包裹在message中
	DetailDataV2 struct {
		Message *DetailV2 `json:"message"`
	}
	DetailV2 struct {
		OrderId              uint64   `json:"order_id"`
		FOrderId             uint64   `json:"forder_id"`
		RequestId            string   `json:"request_id"`
		UniqueId             string   `json:"unique_id"`
		RequestTime          int      `json:"request_time"`
		Status               bool     `json:"status"`
		FulfillmentProductId int64    `json:"fulfillment_product_id"`
		MaskProductId        int64    `json:"mask_product_id"`
		SoftRuleId           int      `json:"soft_rule_id"`
		VolumeRuleId         int      `json:"volume_rule_id"`
		ZoneOriginCode       string   `json:"zone_origin_code"`
		ZoneDestinationCode  string   `json:"zone_destination_code"`
		RouteCodes           []string `json:"route_codes"`
		ShopGroupId          int      `json:"shop_group_id"`
		RequestData          string   `json:"request_data"`
		ResponseData         string   `json:"response_data"`
		HardInput            []int64  `json:"hard_input"`
		HardOutput           []int64  `json:"hard_output"`
		SoftInput            []int64  `json:"soft_input"`
		SoftOutput           []int64  `json:"soft_output"`
		SoftCriteriaList     string   `json:"soft_criteria_list"`
		HardCriteriaList     string   `json:"hard_criteria_list"`

		/*
			batch part
		*/
		//basic info
		IsWms            bool   `json:"is_wms"`
		AllocationMethod string `json:"allocation_method"`
		BatchId          int    `json:"batch_id"`
		BatchName        string `json:"batch_name"`
		BatchTime        string `json:"batch_time"` //e.g. 15:10 - 15:13
		BatchSize        int    `json:"batch_size"`
		// hard check
		IgnoreSnapShot bool `json:"ignore_snap_shot"`
		// soft check
		Input                       []int          `json:"input"`     // products will go through soft check
		Output                      []int          `json:"output"`    // final allocated results
		RuleType                    string         `json:"rule_type"` // e.g. Zone
		DestZoneCodeList            []string       `json:"dest_zone_code_list"`
		RouteCodeList               []string       `json:"route_code_list"`
		Steps                       []Step         `json:"steps"`
		BatchAllocationDistribution []Distribution `json:"batch_level_allocation_distribution"`
	}

	BatchAllocationDetail struct {
		// basic info
		RequestId            string `json:"request_id"`
		FOrderId             uint64 `json:"forder_id"`
		OrderId              uint64 `json:"order_id"`
		Status               bool   `json:"status"` // 请求结果状态
		UniqueId             string `json:"unique_id"`
		FulfillmentProductId int    `json:"fulfillment_product_id"`
		MaskProductId        int    `json:"mask_product_id"`
		RequestTime          int64  `json:"request_time"`
		IsWms                bool   `json:"is_wms"`
		AllocationMethod     string `json:"allocation_method"`
		BatchId              int    `json:"batch_id"`
		BatchName            string `json:"batch_name"`
		BatchTime            string `json:"batch_time"` //e.g. 15:10 - 15:13
		BatchSize            int    `json:"batch_size"`

		// hard check
		HardCriteriaList    map[int64][]ProductToggle `json:"-"`
		HardCriteriaListStr string                    `json:"hard_criteria_list"`
		HardInput           []int                     `json:"hard_input"`
		HardOutput          []int                     `json:"hard_output"`
		IgnoreSnapShot      bool                      `json:"ignore_snap_shot"`

		// soft check
		Input                       []int          `json:"input"`  // products will go through soft check
		Output                      []int          `json:"output"` // final allocated results
		SoftRuleId                  int            `json:"soft_rule_id"`
		ShopGroupId                 int            `json:"shop_group_id"`
		VolumeRuleId                int            `json:"volume_rule_id"`
		RuleType                    string         `json:"rule_type"` // e.g. Zone
		DestZoneCodeList            []string       `json:"dest_zone_code_list"`
		RouteCodeList               []string       `json:"route_code_list"`
		Steps                       []Step         `json:"steps"`
		BatchAllocationDistribution []Distribution `json:"batch_level_allocation_distribution"`
	}

	ProductToggle struct {
		Available int `json:"available"`
		Priority  int `json:"priority"`
		ProductId int `json:"product_id"`
	}

	Step struct {
		SoftCriteriaDetailList []SoftCriteriaDetail `json:"soft_criteria_detail_list"`
	}

	SoftCriteriaDetail struct {
		Step             string   `json:"step"`
		SoftCriteriaName string   `json:"soft_criteria_name"`
		InputProduct     string   `json:"input_product"`
		OutputProduct    string   `json:"output_product"`
		Titles           []string `json:"titles"` //涉及的zone
		Values           []string `json:"values"`
	}

	Distribution struct {
		InputProduct                  string `json:"input_product"`
		ProductName                   string `json:"product_name"`
		MaxDailyLimitCountry          int64  `json:"max_daily_limit_country"`
		MinBatchLimitCountry          int64  `json:"min_batch_limit_country"`
		MaxDailyCodLimitCountry       int64  `json:"max_daily_cod_limit_country"`
		MaxDailyBulkyLimitCountry     int64  `json:"max_daily_bulky_limit_country"`
		MaxDailyHighValueLimitCountry int64  `json:"max_daily_high_value_limit_country"`
		MaxDailyDgLimitCountry        int64  `json:"max_daily_dg_limit_country"`
		SystemVolumeProduct           int64  `json:"system_volume_product"`
		SystemVolumeCod               int64  `json:"system_volume_cod"`
		SystemVolumeBulky             int64  `json:"system_volume_bulky"`
		SystemVolumeHighValue         int64  `json:"system_volume_high_value"`
		SystemVolumeDg                int64  `json:"system_volume_dg"`
		AllocationShippingFee         string `json:"allocation_shipping_fee"`
	}

	BatchPathHbaseStruct struct {
		BatchAllocationDetail *BatchAllocationDetail `json:"batch_allocation_detail"`
	}
)
