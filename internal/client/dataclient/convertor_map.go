package dataclient

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client"
	"sync"
)

type MaskingConvertorMap struct {
	ConvertorMap map[string]MaskingPanelConvertor
}

var (
	once sync.Once
)

// 单例模式初始化 masking convertor
func NewMaskingConvertorMap() *MaskingConvertorMap {
	convertorMap := &MaskingConvertorMap{}
	once.Do(func() {
		convertorMap.ConvertorMap = loadMaskingConvertor()
	})

	return convertorMap
}

// 根据入参的convertor集合初始化一个convertor map， key是convertor的名字，value是对应的convertor
func loadMaskingConvertor() map[string]MaskingPanelConvertor {
	convertorMap := make(map[string]MaskingPanelConvertor, 0)
	convertorMap[client.DailyGranularity] = NewDailyConvertor()
	convertorMap[client.HourlyGranularity] = NewHourlyConvertor()

	return convertorMap
}
