package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	chassis_invoke "git.garena.com/shopee/bg-logistics/go/chassis/core/invoker"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/logtrace"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/meta"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	jsoniter "github.com/json-iterator/go"
	"github.com/oliveagle/jsonpath"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"strings"
	"time"
)

type (
	CommonRequest interface {
		Queries() map[string]string
		Entity() ([]byte, error)
	}

	CommonResponse interface {
		IsSuccess() bool
		FailMsg() string
	}

	CommonOption interface {
		Header(ctx context.Context) (map[string]string, error)
		SecondTimeout(ctx context.Context) int
	}

	HttpService struct {
		Endpoint string
		Scene    Scene
		System   string
	}
)

const (
	defaultHttpDRTimeout         = 6 // 单位s
	defaultHttpMultiAliveTimeout = 2 // 单位s

	UserName = "<EMAIL>"
)

func HttpInvoke(ctx context.Context, systemCode string, httpService HttpService, method string, req CommonRequest, rsp CommonResponse, option CommonOption) error {
	endpoint := httpService.Endpoint
	url := getUrl(ctx, systemCode, endpoint)
	formatEndPointStr := formatEndpoint(endpoint)
	namespace := systemCode + "+" + formatEndPointStr
	err := doProxyRemoteInvokeWithUrl(ctx, systemCode, httpService, url, method, req, rsp, option)
	if err != nil && strings.HasSuffix(err.Error(), context.DeadlineExceeded.Error()) {
		timeout := getHttpTimeout(httpService)
		logger.CtxLogErrorf(ctx, "HttpInvoke timeout,systemCode=%s,endpoint=%s,timeout=%v,err=%v", systemCode, endpoint, timeout, err)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleHttpTimeout, namespace, monitoring.StatusSuccess, fmt.Sprintf("timeout:%d seconds", timeout))
	}
	return err
}

//func RegionHttpInvoke(ctx context.Context, region string, systemCode string, httpService HttpService, method string, req CommonRequest, rsp CommonResponse, option CommonOption) error {
//	url := getRegionUrl(ctx, region, systemCode)
//	if len(url) > 0 {
//		endpoint := httpService.Endpoint
//		url += endpoint
//		return doHttpInvokeWithUrl(ctx, systemCode, httpService, url, method, req, rsp, option)
//	}
//	return srerr.New(srerr.EmptyResultErr, nil, "getRegionUrl empty region:%s system code:%s", region, systemCode)
//}

// doProxyRemoteInvokeWithUrl use this to request http
func doProxyRemoteInvokeWithUrl(ctx context.Context, systemCode string, httpService HttpService, url string, method string, request CommonRequest, response CommonResponse, option CommonOption) error {
	// set log trace
	startTime := recorder.Now(ctx)
	logTraceNumPtr := logtrace.GetLogTraceNum(ctx)
	err := doHttpInvokeWithUrl(ctx, systemCode, httpService, url, method, request, response, option)

	if logTraceNumPtr != nil && *logTraceNumPtr > 0 {
		var logTraceNum uint32
		ctx, logTraceNum = logtrace.SetLogTraceNum(ctx)
		traceEntity := logtrace.TraceEntity{
			LogTrace:  logTraceNum,
			Name:      httpService.Endpoint,
			StartTime: startTime.String(),
			System:    httpService.System,
			FromCache: false,
			Cost:      time.Since(startTime).String(),
		}
		if err != nil {
			traceEntity.Request = str.JsonString(request)
			traceEntity.Response = str.JsonString(response)
			traceEntity.Error = err.Error()
		}
		_ = logtrace.SetLogTraceInfo(ctx, traceEntity)
	}
	return err
}

func doHttpInvokeWithUrl(ctx context.Context, systemCode string, httpService HttpService, url string, method string, req CommonRequest, rsp CommonResponse, option CommonOption) error {
	endpoint := httpService.Endpoint
	use := mockutil.IsUseMock(ctx, systemCode)
	if use {
		if err := HttpMockWithEndpoint(ctx, endpoint, method, req, rsp, option); err != nil {
			return err
		}
		return nil
	}

	timeout := getHttpTimeout(httpService)

	err := httpInvokeWithUrl(ctx, url, method, timeout, req, rsp, option)
	if err != nil && strings.HasSuffix(err.Error(), context.DeadlineExceeded.Error()) {
		logger.CtxLogErrorf(ctx, "doHttpInvokeWithUrl timeout,systemCode=%s,endpoint=%s,timeout=%v,err=%v", systemCode, endpoint, timeout, err)
		namespace := systemCode + "+" + endpoint
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleHttpTimeout, namespace, monitoring.StatusSuccess, fmt.Sprintf("timeout:%d seconds", timeout))
	}

	return err
}

//func getRegionUrl(ctx context.Context, region string, systemCode string) string {
//	if envvar.IsFte() {
//		fteHost := GetFteHost(systemCode, envvar.GetCID())
//		if fteHost != "" {
//			return fteHost
//		}
//	}
//
//	var builder strings.Builder
//	if mockMap := mockutil.MockValues(ctx); len(mockMap) > 0 {
//		if mockSys, ok := mockMap[mockutil.MockSystemsKey]; ok {
//			systems := strings.Split(mockSys, ",")
//			for _, system := range systems {
//				if systemCode == system {
//					builder.WriteString(mockutil.MockHost)
//					return builder.String()
//				}
//			}
//		}
//	}
//
//	builder.WriteString("https://")
//	if envvar.GetEnv() != enum.LIVE {
//		env := getUrlEnv()
//		builder.WriteByte('.')
//		builder.WriteString(env)
//	}
//
//	builder.WriteString(".shopee.")
//	builder.WriteString(meta.GetDomainFromRegion(meta.Region(strings.ToUpper(region))))
//	host := builder.String()
//	return host
//}

func HttpMockWithEndpoint(ctx context.Context, endpoint string, method string, req CommonRequest, rsp CommonResponse, option CommonOption) error {
	return httpInvokeWithUrl(ctx, configutil.GetMockDomainConf(ctx).HttpDomain+endpoint, method, defaultHttpDRTimeout, req, rsp, option)
}

func getHttpTimeout(httpService HttpService) int {
	timeout := defaultHttpDRTimeout
	if httpService.Scene == MultiAlive {
		timeout = defaultHttpMultiAliveTimeout
	}

	return timeout
}

func formatEndpoint(endpoint string) string {
	if len(endpoint) == 0 {
		return ""
	}
	endpoint = strings.TrimLeft(endpoint, "/")
	endpoint = strings.TrimRight(endpoint, "/")
	endpoint = "/" + endpoint

	return endpoint
}

func httpInvokeWithUrl(ctx context.Context, url string, method string, timeout int, req CommonRequest, rsp CommonResponse, option CommonOption) error {
	var header map[string]string = nil

	if option != nil {
		var err error
		header, err = option.Header(ctx)
		if err != nil {
			logger.CtxLogErrorf(ctx, "http_invoke_fail|get_header_fail,url=%s,error=%+v", url, err)
			return err
		}
	}

	if header == nil {
		header = make(map[string]string)
	}
	if ctxhelper.IsShadow(ctx) {
		header[constant.HeaderShadowToken] = "true"
	}
	if header[constant.HeaderToken] == "" {
		header[constant.HeaderToken] = "token"
	}
	if header[constant.HeaderAccount] == "" {
		header[constant.HeaderAccount] = "LPS"
	}
	if mockValues := mockutil.MockValues(ctx); len(mockValues) > 0 {
		for k, v := range mockValues {
			header[k] = v
		}
	}
	if method == http.MethodGet {
		var queries map[string]string = nil
		if req != nil {
			queries = req.Queries()
		}
		resp, err := httputil.Get(ctx, url, queries, timeout, header)
		if err != nil {
			logger.CtxLogErrorf(ctx, "http_invoke_fail|http_get_fail,url=%s,header=%s,request=%s,error=%+v", url, str.JsonString(header), str.JsonString(queries), err)
			return err
		}
		if err = jsoniter.Unmarshal(resp, rsp); err != nil {
			logger.CtxLogErrorf(ctx, "http_invoke_fail|unmarshal_fail,url=%s,response=%v, err=%v", url, string(resp), err)
			return err
		}
		logger.CtxLogDebugf(ctx, "http_invoke_get|url=%s,header=%s,request=%s,response=%s", url, str.JsonStringForDebugLog(header), str.JsonStringForDebugLog(queries), str.JsonStringForDebugLog(rsp))
		if !rsp.IsSuccess() {
			return fmt.Errorf("%s,url=[%s],rsp=%+v", rsp.FailMsg(), url, str.JsonString(rsp))
		}
		return nil
	} else if method == http.MethodPost {
		body, err := req.Entity()
		if err != nil {
			logger.CtxLogErrorf(ctx, "http_invoke_fail|marshal_error,url=%s,error=%+v", url, err)
			return err
		}
		resp, err := httputil.PostJson(ctx, url, body, timeout, header)
		if err != nil {
			logger.CtxLogErrorf(ctx, "remote_invoke_fail|http_post_fail,url=%s,header=%s,request=%v,error=%+v", url, str.JsonString(header), str.JsonString(req), err)
			return err
		}
		if err = jsoniter.Unmarshal(resp, rsp); err != nil {
			logger.CtxLogErrorf(ctx, "remote_invoke_fail|unmarshal_fail,url=%s,response=%v, err=%v", url, string(resp), err)
			return err
		}
		logger.CtxLogDebugf(ctx, "remote_invoke_post|url=%s,header=%s,request=%s,response=%s", url, str.JsonStringForDebugLog(header), str.JsonStringForDebugLog(req), str.JsonStringForDebugLog(rsp))
		if !rsp.IsSuccess() {
			return fmt.Errorf("%s,url=[%s],rsp=%+v", rsp.FailMsg(), url, str.JsonString(rsp))
		}
		return nil
	}
	return errors.New("invalid request params")
}

func getUrl(ctx context.Context, systemCode, endpoint string) string {
	return getHost(ctx, systemCode) + endpoint
}

func getHost(ctx context.Context, systemCode string) string {
	host := ""
	if systemCode == constant.SystemLPSAdmin {
		conf := configutil.GetLpsAdminConf(ctx)
		return conf.Host
	}
	if envvar.IsLivetest() {
		suffix := meta.GetDomainFromRegion(meta.Region(envvar.GetCID()))
		switch systemCode {
		case constant.SystemLFS:
			return fmt.Sprintf("https://lfs-livetestapi.ssc.shopee.%s", suffix) // nolint
		case constant.SystemLPS, constant.SystemLpsFulfillment:
			return fmt.Sprintf("https://api-lps-livetest.ssc.shopee.%s", suffix) // nolint
		case constant.SystemWBCApi:
			return fmt.Sprintf("https://waybillcenter-livetest.ssc.shopee.%s", suffix) // nolint
		default:
			return ""
		}
	}
	var builder strings.Builder
	if mockMap := mockutil.MockValues(ctx); len(mockMap) > 0 {
		if mockSys, ok := mockMap[mockutil.MockSystemsKey]; ok {
			systems := strings.Split(mockSys, ",")
			for _, system := range systems {
				if systemCode == system {
					builder.WriteString(configutil.GetMockDomainConf(ctx).HttpDomain)
					return builder.String()
				}
			}
		}
	}
	builder.WriteString("https://") // nolint
	switch systemCode {
	case constant.SystemLPS:
		builder.WriteString("api.lps")
	case constant.SystemLpsFulfillment:
		builder.WriteString("lps-fulfillment.ssc")
	case constant.SystemLFS:
		builder.WriteString("api.lfs")
	case constant.SystemChargeApi:
		return configutil.GetChargeApiConfig(ctx).Host
	case constant.SystemLNP:
		builder.WriteString("lnp-grpc.ssc")
	case constant.SystemWBCApi:
		builder.WriteString("waybillcenter.ssc")
	}

	if envvar.GetEnvWithCtx(ctx) != enum.LIVE {
		env := getUrlEnv(ctx)
		builder.WriteByte('.')
		builder.WriteString(env)
	}

	builder.WriteString(".shopee.")
	builder.WriteString(meta.GetDomainFromRegion(meta.Region(envvar.GetCID())))

	host = builder.String()
	if envvar.IsFte(ctx) {
		fteHost := GetFteHost(ctx, systemCode, envvar.GetCID())
		if fteHost != "" {
			host = fteHost
		}
	}
	return host
}

func GetFteHost(ctx context.Context, module string, cid string) string {
	fteHosts := configutil.GetFteHttpConf(ctx)
	if fteHosts == "" {
		logger.LogErrorf("get chassis config of fte hosts failed")
	} else {
		fteName := envvar.GetFte() // fte-lfs-api-fte01
		if fteName != "" {
			fteList := strings.Split(fteName, "-")
			size := len(fteList)
			hostJsonPath := strings.ToLower(fmt.Sprintf("$.%s[0].%s[0].%s", fteList[size-1], module, cid))
			// fteHostsJson := slice.ConvertStringToJson(fteHosts)
			var fteHostsJson map[string]interface{}
			if err := jsoniter.Unmarshal([]byte(fteHosts), &fteHostsJson); err != nil {
				logger.LogErrorf("chassis config unmarshal fail, err: %v", err)
				return ""
			}
			fteHost, err := jsonpath.JsonPathLookup(fteHostsJson, hostJsonPath)
			if err != nil {
				logger.LogErrorf("Can't find host for fte hosts: %s", err)
				return ""
			}
			return fteHost.(string)
		}
	}
	return ""
}

func getUrlEnv(ctx context.Context) string {
	return enum.GetUrlEnv(envvar.GetEnvWithCtx(ctx))
}

func SendRequest(ctx context.Context, method RequestMethod, url string, params map[string]string, jsonData interface{},
	header map[string]string, contentType ContentType) (*http.Response, error) {

	if params != nil {
		var paramsQuery = make([]string, len(params))
		i := 0
		for k, v := range params {
			paramsQuery[i] = fmt.Sprintf("%s=%s", k, v)
			i++
		}
		url = url + "?" + strings.Join(paramsQuery, "&")

	}
	logger.CtxLogInfof(context.Background(), "request url: %s", url)

	var body io.Reader = nil
	if jsonData != nil {
		jsonStr, err := json.Marshal(jsonData)
		if err != nil {
			logger.CtxLogErrorf(context.Background(), "Marshal jsonData error: %v", err)
			return nil, err
		}
		body = bytes.NewBuffer(jsonStr)
	}

	request, err := http.NewRequestWithContext(ctx, string(method), url, body)
	if err != nil {
		logger.CtxLogErrorf(context.Background(), "create request error: %v", err)
		return nil, err
	}

	for k, v := range header {
		request.Header.Set(k, v)
	}

	if contentType == JsonContent || contentType == ApplicationXMLContent {
		request.Header.Set("Content-Type", string(contentType))
	} else if contentType == FormContent {
		request.Header.Set("Content-Type", string(contentType))
	}

	invoker := httputil.GetDefaultHttpInvoker()
	request.URL.Scheme = chassis_invoke.HTTP
	response, err := invoker.Invoke(ctx, request, chassis.WithoutServiceDiscovery())
	if err != nil {
		logger.CtxLogErrorf(ctx, "send request error: %v", err)
		return nil, err
	}

	return response, nil
}
