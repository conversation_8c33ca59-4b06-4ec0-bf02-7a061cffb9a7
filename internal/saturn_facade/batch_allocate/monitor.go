package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type BatchAllocateMonitor struct {
	SplitBatchRepo         batch_allocate.SplitBatchRepo
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo
}

func NewBatchAllocateMonitor(
	SplitBatchRepo batch_allocate.SplitBatchRepo,
	BatchAllocateOrderRepo order.BatchAllocateOrderRepo) *BatchAllocateMonitor {
	return &BatchAllocateMonitor{
		SplitBatchRepo:         SplitBatchRepo,
		BatchAllocateOrderRepo: BatchAllocateOrderRepo,
	}
}

func (m *BatchAllocateMonitor) Name() string {
	return constant.TaskNameBatchAllocateMonitor
}

func (m *BatchAllocateMonitor) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	//1.已调度的批次数，待调度的批次数
	m.reportBatches(ctx)
	//2.Allocate Result积压数->可以实时观察到hang住了多少单
	m.reportResults(ctx)

	return nil
}

func (m *BatchAllocateMonitor) reportBatches(ctx context.Context) {
	// 1. 获取非done的批次，并按状态分类
	condition := map[string]interface{}{
		"batch_status <> ?": batch_allocate.BatchDone,
	}
	tabs, err := m.SplitBatchRepo.GetBatchListByCondition(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchAllocateMonitor|get tabs by condition:%v, err:%v", err)
		return
	}
	// 按mask+status归类
	maskingStatusNumMap := make(map[uint64]map[int]float64, 0)
	for _, tab := range tabs {
		if maskingStatusNumMap[tab.MaskProductID] == nil {
			maskingStatusNumMap[tab.MaskProductID] = make(map[int]float64, 0)
		}
		maskingStatusNumMap[tab.MaskProductID][tab.BatchStatus] += 1
	}

	// 2. 上报prometheus
	for maskingProduct, statusNumMap := range maskingStatusNumMap {
		for status, num := range statusNumMap {
			prometheusutil.ReportBatches(ctx, maskingProduct, 0, status, num)
		}
	}
}

func (m *BatchAllocateMonitor) reportResults(ctx context.Context) {
	// 1.获取Apollo配置，决定需要检索哪些masking product对应的积压订单数
	maskingList := configutil.GetBatchAllocateConf().MaskingNeedToMonitor
	if len(maskingList) == 0 {
		logger.CtxLogInfof(ctx, "BatchAllocateMonitor|empty monitor masking list, no need to report the results")
		return
	}
	// 2.CountByCondition
	today := timeutil.GetLocalTime(ctx).Day()
	statusList := []batch_allocate2.OrderStatus{batch_allocate2.OrderStatusTypeAsyncAllocated, batch_allocate2.OrderStatusTypeAsyncFailPushed}
	for _, maskingProduct := range maskingList {
		for _, status := range statusList {
			condition := map[string]interface{}{
				"mask_product_id = ?": maskingProduct,
				"order_status = ?":    status,
			}
			totalNum, err := m.BatchAllocateOrderRepo.CountByCondition(ctx, today, condition)
			if err != nil {
				logger.CtxLogErrorf(ctx, "BatchAllocateMonitor|get total num by condition:%v, err:%v", condition, err)
				continue
			}
			prometheusutil.ReportStatus(ctx, maskingProduct, 0, int(status), float64(totalNum))
		}
	}
}
