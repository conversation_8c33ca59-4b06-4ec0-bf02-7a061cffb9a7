package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
)

type BatchAbnormalInspectionTask struct {
	batchAllocationService allocation.BatchAllocateService
}

func NewBatchAbnormalInspectionTask(batchAllocationService allocation.BatchAllocateService) *BatchAbnormalInspectionTask {
	return &BatchAbnormalInspectionTask{
		batchAllocationService: batchAllocationService,
	}
}

func (s BatchAbnormalInspectionTask) Name() string {
	return constant.TaskNameBatchAbnormalInspection
}

func (s BatchAbnormalInspectionTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	if err := s.batchAllocationService.AbnormalInspection(ctx); err != nil {
		logger.CtxLogErrorf(ctx, "AbnormalInspection failed: %v", err)
		return err
	}

	return nil
}
