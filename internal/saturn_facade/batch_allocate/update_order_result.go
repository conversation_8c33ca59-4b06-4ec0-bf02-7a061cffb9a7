package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
)

type UpdateOrderResultTask struct {
}

func NewUpdateOrderResultTask() *UpdateOrderResultTask {
	return &UpdateOrderResultTask{}
}

func (p UpdateOrderResultTask) Name() string {
	return constant.TaskNameUpdateOrderResult
}

func (p UpdateOrderResultTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	logger.CtxLogInfof(ctx, "msg: %s", string(message.MsgText))

	return &saturn.SaturnReply{}
}
