package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type PushOrderResultTask struct {
	batchAllocationService allocation.BatchAllocateService
}

func NewPushOrderResultTask(batchAllocationService allocation.BatchAllocateService) *PushOrderResultTask {
	return &PushOrderResultTask{
		batchAllocationService: batchAllocationService,
	}
}

func (p PushOrderResultTask) Name() string {
	return constant.TaskNamePushOrderResult
}

func (p PushOrderResultTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	today := timeutil.GetCurrentTime(ctx)

	// 推送T-1
	if err := p.batchAllocationService.PushOrderResult(ctx, today.AddDate(0, 0, -1)); err != nil {
		return err
	}

	// 推送T
	if err := p.batchAllocationService.PushOrderResult(ctx, today); err != nil {
		return err
	}

	return nil
}
