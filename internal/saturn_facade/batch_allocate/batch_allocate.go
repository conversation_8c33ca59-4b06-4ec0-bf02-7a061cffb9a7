package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
)

type BatchAllocateTask struct {
	batchAllocationService allocation.BatchAllocateService
}

func NewBatchAllocateTask(batchAllocationService allocation.BatchAllocateService) *BatchAllocateTask {
	return &BatchAllocateTask{
		batchAllocationService: batchAllocationService,
	}
}

func (s BatchAllocateTask) Name() string {
	return constant.TaskNameBatchAllocate
}

func (s BatchAllocateTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	// 根据Apollo配置匹配saturn切片序号
	var maskProductID int
	for key, value := range configutil.GetBatchAllocateConf().MaskProductTableMapping {
		if value == int(args.ShardingNo) {
			maskProductID = key
			break
		}
	}

	// 匹配失败，返回
	if maskProductID == 0 {
		logger.CtxLogInfof(ctx, "batch allocate | sharding has not mapping to masking product, sharding no: %d", args.ShardingNo)
		return nil
	}

	if err := s.batchAllocationService.BatchAllocate(ctx, uint64(maskProductID)); err != nil {
		logger.CtxLogErrorf(ctx, "execute batch allocate failed: %v", err)
		return err
	}

	return nil
}
