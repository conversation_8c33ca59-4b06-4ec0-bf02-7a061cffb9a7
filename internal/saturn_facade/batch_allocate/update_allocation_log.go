package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
)

type UpdateAllocationPathTask struct {
}

func NewUpdateAllocationPathTask() *UpdateAllocationPathTask {
	return &UpdateAllocationPathTask{}
}

func (p UpdateAllocationPathTask) Name() string {
	return constant.TaskNameAllocationPathEmpty
}

func (p UpdateAllocationPathTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	logger.CtxLogInfof(ctx, "allocation_path_empty_job|msg: %s", string(message.MsgText))

	return &saturn.SaturnReply{}
}
