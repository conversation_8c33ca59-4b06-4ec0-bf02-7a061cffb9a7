package saturn_facade

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	batch_allocate3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	available_lh "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/sync_lfs_order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/export_masking_panel_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/forecast_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/mask_product_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	routing2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	sv_facade "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/services"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/volumeroutingtask"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service/split_batch_chain"
	forecast2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	available_lh2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	svService "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(services.SaturnService), "*"),
	volumerouting.ZoneMgrProviderSet,
	volumerouting.ZoneRuleMgrProviderSet,
	vrservice.ServiceProviderSet,
	vrrepo.VolumeTaskRepoProviderSet,
	vrrepo.ZoneRepoProviderSet,
	vrrepo.ZoneGroupRepoProvideSet,
	vrrepo.ZoneRuleRepoProviderSet,
	masking_result_panel.KeyDataMgrProviderSet,
	product.ProdRepoProviderSet,
	address.AddrRepoProviderSet,
	lpsclient.LpsApiProviderSet,
	routing_log.RoutingLogSrvProviderSet,
	routing_config.RoutingConfigServiceProviderSet,
	volumecounter.MaskVolumeCounterProviderSet,
	sync_lfs_order.OrderSyncSet,
	schedule_factor.NewDgFactor,
	schedule_factor.NewMinVolumeFactor,
	schedule_factor.NewMaxCapacityFactor,
	schedule_factor.NewLinePriorityFactor,
	schedule_factor.NewLineCheapestShippingFeeFactor,
	schedule_factor.NewMinWeightFactor,
	schedule_factor.NewMaxWeightFactor,
	schedule_factor.NewDefaultPriorityFactor,
	schedule_factor.NewDefaultWeightageFactor,
	schedule_factor.NewMinVolumeV2,
	schedule_factor.NewMaxVolumeV2,
	schedule_factor.NewFactorSet,
	schedule_factor.NewILHParcelMinVolumeFactor,
	schedule_factor.NewILHParcelMaxCapacityFactor,
	schedule_factor.NewCombinationPriorityFactor,
	smart_routing_forecast.SmartRoutingForecastTaskProviderSet,
	forecastservice.ForecastTaskServiceProviderSet,
	forecastrepo.ProviderSet,
	lfsclient.LfsClientPset,
	llsclient.LlsApiProviderSet,
	lane.LaneServiceProviderSet,
	ruledata.SoftRuleRepoProviderSet,
	routing.RoutingRepoProviderSet,
	rule.MaskRuleProviderSet,
	routing_log.RoutingLogRepoProviderSet,
	routing.RoutingServiceProviderSet,
	routing.ILHRoutingServiceProviderSet,
	locationzone.LocationZoneProviderSet,
	layercache.NewLayerCache,
	cc_routing.CCRoutingServiceProviderSet,
	ccclient.CCApiProviderSet,
	cc_routing_rule.CCRoutingRuleRepoProviderSet,
	allocate_order_data_repo.AllocateOrderDataRepoProviderSet,
	forecast.AllocateForecastTaskConfigProviderSet,
	rulevolume.MaskRuleVolumeProviderSet,
	forecast2.AllocateForecastTaskConfigServiceProviderSet,
	forecast.AllocateForecastRankProviderSet,
	forecast.AllocateShippingFeeProviderSet,
	chargeclient.RateApiProviderSet,
	masking_forecast.AllocateForecastServiceProviderSet,
	volume_counter.VolumeCounterProviderSet,
	productpriority.BusinessProviderSet,
	productpriority.RepoProviderSet,
	outercheck.AllOutCheckProviderSet,
	forecast2.AllocateRankProviderSet,
	forecast_volume.NewForecastLocationVolumeServiceImpl,
	forecast_volume.NewForecastVolumeRepo,
	forecast.AllocateHistoricalRankProviderSet,
	forecast.AllocateDateRankProviderSet,
	allocation.MaskServerProviderSet,
	config.MaskConfigProviderSet,
	config.AllocationConfigProviderSet,
	allocation.NewSoftRuleService,
	volumeroutingtask.NewZoneImportTask,
	volumeroutingtask.NewZoneExportTask,
	volumeroutingtask.NewRuleLimitImportTask,
	volumeroutingtask.NewRuleEffectiveTask,
	export_masking_panel_data.NewMaskingDataExportTask,
	allocate_volume_counter.NewAllocateCheckBatchVolumeCounter,
	allocate_volume_counter.NewCheckoutFulfillmentProductCounter,
	allocate_volume_counter.NewDeductVolumeCounter,
	routing2.NewReportOrderCount,
	routing2.NewSmartRoutingForecastTask,
	routing2.NewILHForecastTask,
	routing2.NewLfsHardCriteriaCreatedBySystem,
	routing2.NewLfsHardCriteriaV2,
	routing2.NewLfsHardCriteriaCheck,
	routing2.NewScheduleRule,
	masking.NewUpdateZoneVolume,
	masking.NewUpdateRouteVolume,
	masking.NewAllocateStoreConsumer,
	masking.NewLoadForecastVolumeConfig,
	masking.NewMaskingForecast,
	mask_product_priority.NewMaskCronJob,
	audit_log_task.NewAuditLogTaskServer,
	audit_log.AuditLogServiceProviderSet,
	audit_log.AuditLogRepoProviderSet,
	routing2.NewLocalSpxForecasting,
	smart_routing_forecast.LocalForecastTaskPSet,
	repository.ScheduleVisualRepoProviderSet,
	svService.ScheduleVisualTaskServiceProviderSet,
	sv_facade.NewSyncScheduleCountTask,
	sv_facade.NewClearScheduleCountTask,
	schedule_stat.NewMaskingForecastScheduleVisualStat,
	schedule_stat.NewMaskingScheduleVisualStatV2,
	schedule_stat.NewMaskingScheduleVisualStat,
	schedule_stat.NewScheduleVisualSet,
	schedule_visual.ScheduleCountStatProviderSet,
	routing.RoutingPreCalcFeeProviderSet,
	forecasting_sub_task.ForecastingSubTaskServiceProviderSet,
	forecasting_sub_task.ForecastingSubTaskRepoProviderSet,
	forecast_chain.JobChainServiceProviderSet,
	masking.NewDeleteMaskingSubTaskImpl,
	masking.NewCheckMaskingProcessTaskImpl,
	masking.NewGetForecastTotalCountImpl,
	masking.NewUpdateRouteAndZoneVolume,
	masking.NewAllocateStoreHbaseConsumer,
	masking.NewScheduleCountStatTask,
	masking.NewAllocateScheduleVisualTask,
	repository2.MaskingProductOrderNumRepoProviderSet,
	repository2.RoutingProductOrderNumRepoProviderSet,
	volume_dashboard.MaskingVolumeServiceProviderSet,
	volume_dashboard.RoutingVolumeServiceProviderSet,
	masking.NewReportMaskingVolumeTask,
	masking.NewMergeMaskingVolumeTask,
	masking.NewClearMaskingVolumeTask,
	routing2.NewReportRoutingVolumeTask,
	routing2.NewMergeRoutingVolumeTask,
	routing2.NewClearRoutingVolumeTask,
	masking.NewStartBatchForecastUnitImpl,
	batch_allocate_forecast.NewCreateBASubTask,
	masking.NewUpdateBatchAllocateForecastTaskImpl,
	forecast.BatchUnitTargetResultRepoProviderSet,
	forecast.BatchUnitFeeResultRepoProviderSet,
	forecast.BatchAllocateForecastUnitResultRepoProviderSet,
	forecast.BatchAllocateForecastRepoProviderSet,
	forecast.BatchAllocateSubTaskRepoProviderSet,
	forecast.BatchAllocateForecastUnitRepoProviderSet,
	forecast.BatchAllocateForecastServiceProviderSet,
	forecast.BatchAllocateSubtaskOutlineRepoProviderSet,
	model.OrderCollectorProviderSet,
	model.SplittingRuleProviderSet,
	rulevolume2.BatchAllocateForecastVolumeProviderSet,
	volume_counter.NewBatchAllocateForecastCounter,
	forecast_unit.BatchForecastUnitServiceProviderSet,
	forecast.AllocateHistoryOutlineProviderSet,
	masking.NewAllocateHistory,
	masking.NewParseBatchVolume,
	service.BAForecastProviderSet,
	batch_allocate.BatchMinuteOrderConfServiceProviderSet,
	batch_minute_order_conf.BatchMinuteOrderConfRepoProvider,
	volume_dashboard.VolumeChangeServiceProviderSet,
	batch_allocate_forecast.NewBAForecastToolProgressImpl,
	masking.NewSplitBatchAllocateOrdersTask,
	order.BatchAllocateOrderRepoProviderSet,
	allocation2.BatchAllocateServiceProviderSet,
	batch_allocate2.NewBatchAllocateTask,
	batch_allocate2.NewAbnormalBatchAllocateTask,
	batch_allocate2.NewBatchAbnormalInspectionTask,
	batch_allocate2.NewPushOrderResultTask,
	batch_allocate2.NewUpdateOrderResultTask,
	batch_allocate2.NewClearOrderAndResultTask,
	split_batch_chain.ExecutorProvider,
	batch_allocate.SplitBatchServerProvider,
	batch_allocate3.SplitBatchRepoProvider,
	batch_allocate2.NewBatchAllocateHoldOrderConsumer,
	batch_allocate.GreyServiceProviderSet,
	batch_allocate2.NewUpdateAllocationPathTask,
	allocpath.AllocationPathSrvProviderSet,
	batch_allocate2.NewMakeUpAsyncAllocationLog,
	rulevolume.CheckVolumeFinderProvider,
	business_audit.BusinessAuditRepoProvider,
	approval_manager.ApprovalExecutorProvider,
	auditclient.AuditApiProvider,
	pickup_priority.PickupPriorityRepoProviderSet,
	batch_allocate2.NewBatchAllocateMonitor,
	lnpclient.LnpApiProviderSet,
	parcel_type_definition2.ParcelTypeDefinitionRepoProviderSet,
	parcel_type_definition.ParcelTypeDefinitionServiceProviderSet,
	wbcclient.WbcApiProviderSet,
	pickup_efficiency_counter.PickupEffCounterProviderSet,
	spex_service.SpexServiceProviderSet,
	lcosclient.LcosApiProviderSet,
	masking.NewReportMaskingOrderCount,
	whitelist.ShopWhiteListServiceProviderSet,
	whitelist2.ShopWhitelistRepoProviderSet,
	volume_counter.ILHWeightCounterProviderSet,
	available_lh.AvailableLHRepoProviderSet,
	available_lh2.AvailableLHServiceProviderSet,
	repo.LHCapacityRepoProviderSet,
	lh_capacity.LHCapacityServiceProviderSet,
	forecastrepo.ILHForecastTaskRepoProviderSet,
	ilh_forecast_task.ILHForecastTaskServiceProviderSet,
)
