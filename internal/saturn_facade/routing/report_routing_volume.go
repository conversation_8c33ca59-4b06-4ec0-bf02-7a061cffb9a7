package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type ReportRoutingVolumeTask struct {
	RoutingVolumeService volume_dashboard.RoutingVolumeService
}

func NewReportRoutingVolumeTask(routingVolumeService volume_dashboard.RoutingVolumeService) *ReportRoutingVolumeTask {
	return &ReportRoutingVolumeTask{
		RoutingVolumeService: routingVolumeService,
	}
}

func (r *ReportRoutingVolumeTask) Name() string {
	return constant.TaskReportRoutingVolume
}

func (r *ReportRoutingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "ReportRoutingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := r.RoutingVolumeService.ReportRoutingVolume(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ReportRoutingVolumeTask|report routing volume error, err=%v", err)
		return err
	}
	return nil
}
