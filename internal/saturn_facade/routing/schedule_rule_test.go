package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	"log"
	"testing"
	"time"
)

func TestScheduleRule_RpcHandle(t *testing.T) {
	if err := change_report.InitChangeReport(); err != nil {
		log.Fatalf("init change report failed %+v", err)
	}
	if err := change_report.RegisterHttpChangeReportHandler(); err != nil {
		log.Fatalf("registry change report failed %+v", err)
	}
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("admin_server"),
		chassis.WithDefaultProviderHandlerChain(
			change_report.ChangeReportHandlerName,
		),
	); err != nil {
		log.Fatalf("chassis init failed with err: %+v", err)
	}
	ctx := context.TODO()
	startTime := recorder.Now(ctx).Unix()
	change_report_util.ChangeReportByTask(context.TODO(), 0, startTime, "ScheduleRule", "", "上报", report_constant.RiskLevelHigh)
	time.Sleep(200 * time.Second)
}
