package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	uuid "github.com/satori/go.uuid"
	"strconv"
)

type LocalSpxForecasting struct {
	ForecastSrv  smart_routing_forecast.LocalForecastService
	ForecastRepo forecastrepo.IForecastRepo
}

func NewLocalSpxForecasting(
	ForecastSrv smart_routing_forecast.LocalForecastService,
	ForecastRepo forecastrepo.IForecastRepo,
) *LocalSpxForecasting {
	return &LocalSpxForecasting{
		ForecastSrv:  ForecastSrv,
		ForecastRepo: ForecastRepo,
	}
}

func (p *LocalSpxForecasting) Name() string {
	return constant.TaskNameLocalSpxRoutingForecast
}

func (p *LocalSpxForecasting) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	begin := recorder.Now(ctx).Unix()
	task, err := p.getPendingTask(ctx, []int{rule.LocalRoutingType, rule.SPXRoutingType})
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get local routing type with pending failed %+v", err)
		return err
	}
	if task == nil {
		logger.CtxLogInfof(ctx, "no pending task")
		return nil
	}
	ctx = getCtxWithRequestId(ctx, task)
	logger.CtxLogInfof(ctx, "start local_spx forecast task: %d", task.Id)
	ctx = forecast.NewForecastCtxWithTaskId(ctx, task.Id)

	if ferr := p.ForecastSrv.StartForecast(ctx, task); ferr != nil {
		logger.CtxLogErrorf(ctx, "StartForecast local_spx fail|err=%+v", ferr)
		// update status to processing
		task.TaskStatus = persistent.TaskStatusFailed
		p.updateTaskWithStatusAndReportCat(ctx, task, persistent.TaskStatusFailed, monitoring.StatusError, ferr.Error())
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "local_spx forecast", monitoring.StatusError, ferr.Error())
		return ferr
	}
	p.updateTaskWithStatusAndReportCat(ctx, task, persistent.TaskStatusDone, monitoring.StatusError, strconv.Itoa(task.Id))
	logger.CtxLogInfof(ctx, "use times is %d", recorder.Now(ctx).Unix()-begin)
	return nil
}

func getCtxWithRequestId(ctx context.Context, task *persistent.ForecastingTaskTab) context.Context {
	requestID := fmt.Sprintf("%s|task_id=%d", uuid.NewV4().String(), task.Id) // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	return ctx
}

func (p *LocalSpxForecasting) getPendingTask(ctx context.Context, routingType []int) (*persistent.ForecastingTaskTab, error) {

	task, err := p.ForecastRepo.GetFirstPendingTaskWithRoutingType(ctx, routingType)
	if err != nil {
		return nil, err
	}

	return task, nil
}

func (p *LocalSpxForecasting) updateTaskWithStatusAndReportCat(ctx context.Context, task *persistent.ForecastingTaskTab, taskStatus persistent.TaskStatus, monitorType string, message string) {
	task.TaskStatus = taskStatus
	if _, err := p.ForecastRepo.UpdateTask(ctx, task); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateTask fail|err=%+v", err)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "LocalSpxForecast", monitoring.StatusError, err.Error())
	}
	logger.CtxLogInfof(ctx, "update task %+v", task)
	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "LocalSpxForecast", monitorType, message)
}
