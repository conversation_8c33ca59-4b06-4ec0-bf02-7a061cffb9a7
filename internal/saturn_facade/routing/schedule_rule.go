package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
)

type ScheduleRule struct {
	routingRuleService routing.RoutingRuleRepo
	maskRuleRepo       rule2.IMaskRuleRepo
	lhCapacityService  lh_capacity.LHCapacityService
	availableLHService available_lh.AvailableLHService
}

func NewScheduleRule(
	routingRuleService routing.RoutingRuleRepo,
	maskRuleRepo rule2.IMaskRuleRepo,
	lhCapacityService lh_capacity.LHCapacityService,
	availableLHService available_lh.AvailableLHService,
) *ScheduleRule {
	return &ScheduleRule{
		routingRuleService: routingRuleService,
		maskRuleRepo:       maskRuleRepo,
		lhCapacityService:  lhCapacityService,
		availableLHService: availableLHService,
	}
}

func (s *ScheduleRule) Name() string {
	return constant.TaskNameScheduleRule
}

func (s *ScheduleRule) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	for _, routingType := range []int{rule.CBRoutingType, rule.SPXRoutingType, rule.LocalRoutingType, rule.IlhRoutingType} {
		var multiProductSceneList []bool
		if routingType == rule.CBRoutingType || routingType == rule.IlhRoutingType {
			multiProductSceneList = []bool{false, true}
		} else {
			multiProductSceneList = []bool{false}
		}

		for _, isMultiProduct := range multiProductSceneList {
			// deploy forecast rule to live; expired old live rule
			s.routingRuleService.UpdateForecastRuleStatusActive(ctx, routingType, isMultiProduct)
			// exist same product-zone-whs-priority, expired old live rule; case：1.forecast deploy first; 2.business manually create a new one
			s.routingRuleService.UpdateProductRuleStatusExpired(ctx, routingType, isMultiProduct)
		}
	}

	s.maskRuleRepo.UpdateAllocationRuleStatusExpired(ctx)
	s.maskRuleRepo.UpdateAllocationRuleVolumeStatusExpired(ctx)

	s.scheduleLHRule(ctx)

	return nil
}

func (s *ScheduleRule) scheduleLHRule(ctx context.Context) {
	// LH Capacity
	s.lhCapacityService.UpdatePendingLHCapacityStatusActive(ctx)
	s.lhCapacityService.UpdateActiveLHCapacityStatusExpired(ctx)

	// Available LH
	s.availableLHService.UpdatePendingAvailableLHStatusActive(ctx)
	s.availableLHService.UpdateActiveAvailableLHStatusExpired(ctx)
}
