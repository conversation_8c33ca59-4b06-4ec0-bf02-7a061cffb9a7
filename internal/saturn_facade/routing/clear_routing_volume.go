package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type ClearRoutingVolumeTask struct {
	RoutingVolumeService volume_dashboard.RoutingVolumeService
}

func NewClearRoutingVolumeTask(routingVolumeService volume_dashboard.RoutingVolumeService) *ClearRoutingVolumeTask {
	return &ClearRoutingVolumeTask{
		RoutingVolumeService: routingVolumeService,
	}
}

func (c *ClearRoutingVolumeTask) Name() string {
	return constant.TaskClearRoutingVolume
}

// 传入参数格式为数字，删除传入数字前的数据
func (c *ClearRoutingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "ClearRoutingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := c.RoutingVolumeService.ClearRoutingVolume(ctx, args.ShardingParam)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearRoutingVolumeTask|clear masking volume err:%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "ClearRoutingVolumeTask|end")
	return nil
}
