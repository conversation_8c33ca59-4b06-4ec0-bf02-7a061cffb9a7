package routing

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"strconv"
	"strings"
)

type LfsHardCriteriaV2 struct {
	syncService forecastservice.IForecastTaskService
}

func NewLfsHardCriteriaV2(
	syncService forecastservice.IForecastTaskService,
) *LfsHardCriteriaV2 {
	return &LfsHardCriteriaV2{
		syncService: syncService,
	}
}

func (s *LfsHardCriteriaV2) Name() string {
	return constant.TaskNameExecutedHardCriteria
}

// 实现定时任务接口
func (s *LfsHardCriteriaV2) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	req, err := getHardCriteriaSaturnParam(ctx, args.ShardingParam)
	if err != nil {
		logger.CtxLogErrorf(ctx, "LfsHardCriteriaV2| get saturn param err:%v", err)
		return nil
	}
	s.syncService.RefreshHardCriteriaByTask(ctx, req)
	return nil
}
func getHardCriteriaSaturnParam(ctx context.Context, shardingParam string) (foreschema.ExecuteHCTaskRequest, error) {
	logger.CtxLogInfof(ctx, "LfsHardCriteriaV2| shardingParam:%v", shardingParam)
	req := foreschema.ExecuteHCTaskRequest{}

	paramSlice := strings.Split(shardingParam, ",")
	if len(paramSlice) != 4 {
		return req, errors.New("LfsHardCriteriaV2| sharding params' length != 4, params should including 'task type', 'routine num', 'min order count' and 'max order count'")
	}
	taskType, err := strconv.Atoi(paramSlice[0])
	if err != nil {
		taskType = forecast.TaskTypeCreatedBySystem //default by sys
		logger.CtxLogInfof(ctx, "will use default value, get task type err:%v", err)
	}
	routineNum, err := strconv.Atoi(paramSlice[1])
	if err != nil {
		routineNum = configutil.GetRefreshHardCriteriaByTaskGoroutineConfig(ctx) //default routine num
		logger.CtxLogInfof(ctx, "will use default value, get routine num err:%v", err)
	}
	if routineNum >= 30 || routineNum <= 0 {
		logger.CtxLogInfof(ctx, "will use default value, get routine num:%v is illegal", routineNum)
		routineNum = configutil.GetRefreshHardCriteriaByTaskGoroutineConfig(ctx) //default routine num
	}
	minOrderCount, err := strconv.Atoi(paramSlice[2])
	if err != nil {
		minOrderCount = 0 // default by 0
		logger.CtxLogInfof(ctx, "will use default value, get min order count err:%v", err)
	}
	maxOrderCount, err := strconv.Atoi(paramSlice[3])
	if err != nil {
		maxOrderCount = 99999999 //default by infinite
		logger.CtxLogInfof(ctx, "will use default value, get max order count err:%v", err)
	}
	req.TaskType = taskType
	req.RoutineNum = routineNum
	req.MinOrderCount = minOrderCount
	req.MaxOrderCount = maxOrderCount
	logger.CtxLogInfof(ctx, "LfsHardCriteriaV2| shardingParam:%v, final req:%+v", shardingParam, req)

	return req, nil
}
