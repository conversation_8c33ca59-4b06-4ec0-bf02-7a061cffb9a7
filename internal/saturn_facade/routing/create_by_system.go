package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
)

type LfsHardCriteriaCreatedBySystem struct {
	syncService forecastservice.IForecastTaskService
}

func NewLfsHardCriteriaCreatedBySystem(
	syncService forecastservice.IForecastTaskService,
) *LfsHardCriteriaCreatedBySystem {
	return &LfsHardCriteriaCreatedBySystem{
		syncService: syncService,
	}
}

func (s *LfsHardCriteriaCreatedBySystem) Name() string {
	return constant.TaskNameLfsHardCriteriaCreatedBySystem
}

// 实现定时任务接口
func (s *LfsHardCriteriaCreatedBySystem) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	s.syncService.CreateHCTaskBySystem(ctx)
	return nil
}
