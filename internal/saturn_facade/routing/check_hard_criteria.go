package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
)

type LfsHardCriteriaCheck struct {
	syncService forecastservice.IForecastTaskService
}

func NewLfsHardCriteriaCheck(
	syncService forecastservice.IForecastTaskService,
) *LfsHardCriteriaCheck {
	return &LfsHardCriteriaCheck{
		syncService: syncService,
	}
}

func (s *LfsHardCriteriaCheck) Name() string {
	return constant.TaskNameLfsHardCriteriaCheck
}

func (s *LfsHardCriteriaCheck) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	s.syncService.CheckHCTask(ctx)
	return nil
}
