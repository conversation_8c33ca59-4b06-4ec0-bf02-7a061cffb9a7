package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type MergeRoutingVolumeTask struct {
	RoutingVolumeService volume_dashboard.RoutingVolumeService
}

func NewMergeRoutingVolumeTask(routingVolumeService volume_dashboard.RoutingVolumeService) *MergeRoutingVolumeTask {
	return &MergeRoutingVolumeTask{
		RoutingVolumeService: routingVolumeService,
	}
}

func (r *MergeRoutingVolumeTask) Name() string {
	return constant.TaskMergeRoutingVolume
}

// 定时任务参数格式为********
func (r *MergeRoutingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "MergeRoutingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := r.RoutingVolumeService.MergeRoutingVolume(ctx, args.ShardingParam)
	if err != nil {
		logger.CtxLogErrorf(ctx, "MergeRoutingVolumeTask|merge routing volume error, err=%v", err)
		return err
	}
	return nil
}
