package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
)

type ReportOrderCount struct {
	routingLogSrv routing_log.RoutingLogService
}

func NewReportOrderCount(routingLogSrv routing_log.RoutingLogService) *ReportOrderCount {
	return &ReportOrderCount{
		routingLogSrv: routingLogSrv,
	}
}

func (s *ReportOrderCount) Name() string {
	return constant.TaskNameReportOrderCount
}

func (s *ReportOrderCount) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	err := s.routingLogSrv.ReportOrderCount(ctx)
	if err != nil {
		return err
	}
	return nil
}
