package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
)

const (
	zoneType  = "zone"
	routeType = "route"
)

type LoadForecastVolumeConfig struct {
	ruleVolumeRepo                 rulevolume.IMaskRuleVolumeRepo
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo
	maskRuleVolumeSrv              rulevolume2.MaskRuleVolumeService
}

func NewLoadForecastVolumeConfig(
	ruleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	maskRuleVolumeSrv rulevolume2.MaskRuleVolumeService,
) *LoadForecastVolumeConfig {
	return &LoadForecastVolumeConfig{
		ruleVolumeRepo:                 ruleVolumeRepo,
		allocateForecastTaskConfigRepo: allocateForecastTaskConfigRepo,
		maskRuleVolumeSrv:              maskRuleVolumeSrv,
	}
}

func (s *LoadForecastVolumeConfig) Name() string {
	return constant2.TaskNameLoadForecastVolumeConfig
}

func (s *LoadForecastVolumeConfig) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	logger.CtxLogInfof(ctx, "LoadForecastVolumeConfigJob|start|job_key=%s", string(message.MsgText))

	// 1. load forecast task config by id
	var config allocation.AllocateForecastTaskConfigRequest
	if err := jsoniter.Unmarshal(message.MsgText, &config); err != nil {
		errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|consume meesage failed|key=%s,err=%v", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	// get task config by id
	configTab, err := s.allocateForecastTaskConfigRepo.GetForecastTaskConfigById(ctx, int64(config.BaseInfo.Id))
	if err != nil {
		errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|consume meesage failed, can't find task config|key=%s,err=%v", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	// 2. if no need rule volume, return success directly
	zoneVolumes := make([]*rulevolume.ForecastMaskZoneVolumeTab, 0)
	routeVolumes := make([]*rulevolume.ForecastMaskRouteVolumeTab, 0)
	groupToProductList := make(map[string][]int)
	if needImportVolumeData(config) {
		if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeZone) {
			mZoneVolumes, pErr := s.maskRuleVolumeSrv.ParseZoneExcel(
				ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.ZoneDefinitionFile,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.ZoneLimitFile,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.SetVolumeBlankAsMinimum,
				int64(config.BaseInfo.AllocationMethod))
			if pErr != nil {
				s.processParseFailed(ctx, pErr, configTab, zoneType)
				return &saturn.SaturnReply{Retcode: -1, Message: "parse zone failed"}
			}
			zoneVolumes = buildZoneVolumes(mZoneVolumes, &config)
		} else if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRoute) {
			mRouteVolumes, pErr := s.maskRuleVolumeSrv.ParseRouteExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RouteDefinitionFile,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RouteLimitFile,
				config.AllocationRuleConfig.MaskProductRuleVolumeConfig.SetVolumeBlankAsMinimum,
				int64(config.BaseInfo.AllocationMethod))
			if pErr != nil {
				s.processParseFailed(ctx, pErr, configTab, routeType)
				return &saturn.SaturnReply{Retcode: -1, Message: "parse route failed"}
			}
			routeVolumes = buildRouteVolumes(mRouteVolumes, &config)
		} else if config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
			routeVolume, zoneVolume, err := s.ParseRouteAndZoneVolume(ctx, &config, configTab)
			if err != nil {
				return &saturn.SaturnReply{Retcode: -1, Message: fmt.Sprintf("parse route or zone failed %v", err)}
			}
			routeVolumes = buildRouteVolumes(routeVolume, &config)
			zoneVolumes = buildZoneVolumes(zoneVolume, &config)
		}
	}

	if err := s.ruleVolumeRepo.DeleteForecastRouteLimitsByTaskId(ctx, uint64(config.BaseInfo.Id)); err != nil {
		errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|DeleteRouteLimitsByForecastID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		configTab.ConfigSyncStatus = constant.FailedToSyncConfigStatus
		configTab.Status = constant.TaskConfigStatusFailed
		updateErr := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab)
		if updateErr != nil {
			logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob| updateForecastTaskConfig err. forecast_task_id=%v, updateErr=%v", configTab.Id, updateErr)
		}
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if err := s.ruleVolumeRepo.DeleteForecastZoneLimitsByTaskId(ctx, uint64(config.BaseInfo.Id)); err != nil {
		errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|DeleteZoneLimitsByForecastID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		configTab.ConfigSyncStatus = constant.FailedToSyncConfigStatus
		configTab.Status = constant.TaskConfigStatusFailed
		updateErr := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab)
		if updateErr != nil {
			logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob| updateForecastTaskConfig err. forecast_task_id=%v, updateErr=%v", configTab.Id, updateErr)
		}
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if len(routeVolumes) > 0 {
		if err := s.ruleVolumeRepo.BatchCreateForecastRouteVolumes(ctx, routeVolumes); err != nil {
			errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|BatchCreateForecastRouteVolumes failed|err=%v", err)
			logger.CtxLogErrorf(ctx, errMsg)
			configTab.ConfigSyncStatus = constant.FailedToSyncConfigStatus
			configTab.Status = constant.TaskConfigStatusFailed
			updateErr := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab)
			if updateErr != nil {
				logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob| updateForecastTaskConfig err. forecast_task_id=%v, updateErr=%v", configTab.Id, updateErr)
			}
			return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
		}
		logger.CtxLogInfof(ctx, "LoadForecastVolumeConfigJob|BatchCreateRouteVolumes|route_volumes_len=%d", len(routeVolumes))
	}

	if len(zoneVolumes) > 0 {
		if err := s.ruleVolumeRepo.BatchCreateForecastZoneVolumes(ctx, zoneVolumes); err != nil {
			errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|BatchCreateForecastZoneVolumes failed|err=%v", err)
			logger.CtxLogErrorf(ctx, errMsg)
			configTab.ConfigSyncStatus = constant.FailedToSyncConfigStatus
			configTab.Status = constant.TaskConfigStatusFailed
			updateErr := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab)
			if updateErr != nil {
				logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob| updateForecastTaskConfig err. forecast_task_id=%v, updateErr=%v", configTab.Id, updateErr)
			}
			return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
		}
		logger.CtxLogInfof(ctx, "LoadForecastVolumeConfigJob|BatchCreateZoneVolumes|zone_volumes_len=%d", len(zoneVolumes))
	}

	configTab.ConfigSyncStatus = constant.CompleteSyncConfigStatus
	if err := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab); err != nil {
		logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob| updateForecastTaskConfig err. forecast_task_id=%v, updateErr=%v", configTab.Id, err)
	}

	successMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|update route volumes success, task_id = %v, routes len = %v, zone len= %v", config.BaseInfo.Id, len(routeVolumes), len(zoneVolumes))
	logger.CtxLogInfof(ctx, successMsg)
	return &saturn.SaturnReply{Retcode: 0, Message: successMsg}
}

// needImportVolumeData
func needImportVolumeData(c allocation.AllocateForecastTaskConfigRequest) bool {
	return c.BaseInfo.LocalSoftCriteriaToggle &&
		c.AllocationRuleConfig != nil && (c.AllocationRuleConfig.RuleDetail.MaxCapacityZoneRouteEnable || c.AllocationRuleConfig.RuleDetail.MinVolumeZoneRouteEnable) &&
		c.AllocationRuleConfig.MaskProductRuleVolumeConfig != nil
}
func buildZoneVolumes(mZoneVolumes []*rulevolume.MaskZoneVolumeTab, config *allocation.AllocateForecastTaskConfigRequest) []*rulevolume.ForecastMaskZoneVolumeTab {
	zoneVolumes := make([]*rulevolume.ForecastMaskZoneVolumeTab, 0, len(mZoneVolumes))
	for _, mZoneVolume := range mZoneVolumes {
		zoneVolume := &rulevolume.ForecastMaskZoneVolumeTab{
			ForecastTaskID:         uint64(config.BaseInfo.Id),
			MaskProductID:          config.BaseInfo.MaskProductId,
			ComponentProductID:     mZoneVolume.ComponentProductID,
			ZoneCode:               mZoneVolume.ZoneCode,
			DistrictID:             mZoneVolume.DistrictID,
			Postcode:               mZoneVolume.Postcode,
			OriginMaxCapacity:      mZoneVolume.OriginMaxCapacity,
			DestinationMaxCapacity: mZoneVolume.DestinationMaxCapacity,
			OriginMinVolume:        mZoneVolume.OriginMinVolume,
			DestinationMinVolume:   mZoneVolume.DestinationMinVolume,
			IsHardCap:              mZoneVolume.IsHardCap,
			CTime:                  mZoneVolume.CTime,
			MTime:                  mZoneVolume.MTime,
		}
		zoneVolumes = append(zoneVolumes, zoneVolume)
	}

	return zoneVolumes
}

func buildRouteVolumes(mRouteVolumes []*rulevolume.MaskRouteVolumeTab, config *allocation.AllocateForecastTaskConfigRequest) []*rulevolume.ForecastMaskRouteVolumeTab {
	routeVolumes := make([]*rulevolume.ForecastMaskRouteVolumeTab, 0, len(mRouteVolumes))
	for _, mRouteVolume := range mRouteVolumes {
		routeVolume := &rulevolume.ForecastMaskRouteVolumeTab{
			ForecastTaskID:        uint64(config.BaseInfo.Id),
			MaskProductID:         config.BaseInfo.MaskProductId,
			ComponentProductID:    mRouteVolume.ComponentProductID,
			RouteCode:             mRouteVolume.RouteCode,
			OriginDistrictID:      mRouteVolume.OriginDistrictID,
			OriginPostcode:        mRouteVolume.OriginPostcode,
			DestinationDistrictID: mRouteVolume.DestinationDistrictID,
			DestinationPostcode:   mRouteVolume.DestinationPostcode,
			MaxCapacity:           mRouteVolume.MaxCapacity,
			MinVolume:             mRouteVolume.MinVolume,
			IsHardCap:             mRouteVolume.IsHardCap,
			CTime:                 mRouteVolume.CTime,
			MTime:                 mRouteVolume.MTime,
		}
		routeVolumes = append(routeVolumes, routeVolume)
	}

	return routeVolumes
}

func (s *LoadForecastVolumeConfig) processParseFailed(ctx context.Context, err *srerr.Error, configTab *model.AllocateForecastTaskConfigTab, parseType string) {
	errMsg := fmt.Sprintf("LoadForecastVolumeConfigJob|parse %s excel failed|err=%v", parseType, err)
	logger.CtxLogErrorf(ctx, errMsg)
	configTab.ConfigSyncStatus = constant.FailedToSyncConfigStatus
	configTab.Status = constant.TaskConfigStatusFailed
	if err := s.allocateForecastTaskConfigRepo.UpdateForecastTaskConfig(ctx, configTab); err != nil {
		logger.CtxLogErrorf(ctx, "LoadForecastVolumeConfigJob|UpdateForecastTaskConfig fail|err=%v", err)
	}
}

func (s *LoadForecastVolumeConfig) ParseRouteAndZoneVolume(ctx context.Context, config *allocation.AllocateForecastTaskConfigRequest, configTab *model.AllocateForecastTaskConfigTab) ([]*rulevolume.MaskRouteVolumeTab, []*rulevolume.MaskZoneVolumeTab, *srerr.Error) {
	groupToProductList := make(map[string][]int)
	mRouteVolumes, pErr := s.maskRuleVolumeSrv.ParseRouteExcel(ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RouteDefinitionFile,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.RouteLimitFile,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.SetVolumeBlankAsMinimum,
		int64(config.BaseInfo.AllocationMethod))
	if pErr != nil {
		s.processParseFailed(ctx, pErr, configTab, zoneType)
		return nil, nil, pErr
	}

	mZoneVolumes, pErr := s.maskRuleVolumeSrv.ParseZoneExcel(
		ctx, []int{config.BaseInfo.MaskProductId}, groupToProductList,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.ZoneDefinitionFile,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.ZoneLimitFile,
		config.AllocationRuleConfig.MaskProductRuleVolumeConfig.SetVolumeBlankAsMinimum,
		int64(config.BaseInfo.AllocationMethod))
	if pErr != nil {
		s.processParseFailed(ctx, pErr, configTab, zoneType)
		return nil, nil, pErr
	}

	return mRouteVolumes, mZoneVolumes, nil
}
