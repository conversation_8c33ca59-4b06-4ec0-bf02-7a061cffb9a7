package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type MergeMaskingVolumeTask struct {
	MaskingVolumeService volume_dashboard.MaskingVolumeService
}

func NewMergeMaskingVolumeTask(maskingVolumeService volume_dashboard.MaskingVolumeService) *MergeMaskingVolumeTask {
	return &MergeMaskingVolumeTask{
		MaskingVolumeService: maskingVolumeService,
	}
}

func (r *MergeMaskingVolumeTask) Name() string {
	return constant.TaskMergeMaskingVolume
}

// 定时任务参数格式为********
func (r *MergeMaskingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "MergeMaskingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := r.MaskingVolumeService.MergeMaskingVolume(ctx, args.ShardingParam)
	if err != nil {
		return err
	}
	return nil
}
