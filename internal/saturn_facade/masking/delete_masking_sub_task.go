package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type DeleteMaskingSubTaskImpl struct {
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService
}

func NewDeleteMaskingSubTaskImpl(
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService) *DeleteMaskingSubTaskImpl {
	return &DeleteMaskingSubTaskImpl{
		ForecastingSubTaskService: ForecastingSubTaskService,
	}
}

func (d *DeleteMaskingSubTaskImpl) Name() string {
	return internal_constant.TaskDeleteMaskingSubTask
}

func (d *DeleteMaskingSubTaskImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	//1.获取界限，以35天为界限，可配置
	conf := configutil.GetMaskingForecastConf(ctx)
	limit := 0 - conf.DeleteDays
	limitTimeStr := strconv.FormatInt(timeutil.GetNextDayStartTime(ctx, limit), 10)
	//2.获取end_key小于界限的，即在35天前创建的任务
	condition := map[string]interface{}{
		"end_key < ?":     limitTimeStr,
		"module_name = ?": forecasting_sub_task.ModuleMaskingForecast,
	}
	taskList, err := d.ForecastingSubTaskService.SelectSubTask(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "DeleteMaskingSubTaskImpl|condition:%v, get expired sub task err:%v", condition, err)
		return err
	}
	var ids []uint64
	for _, task := range taskList {
		ids = append(ids, task.Id)
	}
	//3.删除这些任务
	if err := d.ForecastingSubTaskService.BatchDeleteSubTaskById(ctx, ids); err != nil {
		logger.CtxLogErrorf(ctx, "DeleteMaskingSubTaskImpl|ids:%v, batch delete sub task err:%v", ids, err)
		return err
	}
	return nil
}
