package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast_unit_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	uuid "github.com/satori/go.uuid"
)

//TaskStartBatchForecastUnit

type StartBatchForecastUnitImpl struct {
	BatchAllocateForecastRepo           repo.BatchAllocateForecastRepo
	SplittingRuleServer                 model.SplittingRuleServer
	BAForecastVolumeRepo                rulevolume.BatchAllocateForecastVolumeRepo
	LpsApi                              lpsclient.LpsApi
	RateApi                             chargeclient.ChargeApi
	AllocateForecastTaskConfigRepo      repo.AllocateForecastTaskConfigRepo
	BatchUnitTargetResultRepo           repo.BatchUnitTargetResultRepo
	BatchUnitFeeResultRepo              repo.BatchUnitFeeResultRepo
	BatchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo
	LocationVolumeService               forecast_volume.ForecastLocationVolumeService
	BatchForecastUnitService            forecast_unit.BatchForecastUnitService
	BatchMinuteOrderConfService         batch_allocate.BatchMinuteOrderConfService
	ShopWhitelistService                whitelist.ShopWhitelistService
}

func NewStartBatchForecastUnitImpl(
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo,
	SplittingRuleServer model.SplittingRuleServer,
	BAForecastVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo,
	LpsApi lpsclient.LpsApi,
	RateApi chargeclient.ChargeApi,
	AllocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	BatchUnitTargetResultRepo repo.BatchUnitTargetResultRepo,
	BatchUnitFeeResultRepo repo.BatchUnitFeeResultRepo,
	BatchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo,
	LocationVolumeService forecast_volume.ForecastLocationVolumeService,
	BatchForecastUnitService forecast_unit.BatchForecastUnitService,
	BatchMinuteOrderConfService batch_allocate.BatchMinuteOrderConfService,
	ShopWhitelistService whitelist.ShopWhitelistService,
) *StartBatchForecastUnitImpl {
	return &StartBatchForecastUnitImpl{
		BatchAllocateForecastRepo:           BatchAllocateForecastRepo,
		SplittingRuleServer:                 SplittingRuleServer,
		BAForecastVolumeRepo:                BAForecastVolumeRepo,
		LpsApi:                              LpsApi,
		RateApi:                             RateApi,
		AllocateForecastTaskConfigRepo:      AllocateForecastTaskConfigRepo,
		BatchUnitTargetResultRepo:           BatchUnitTargetResultRepo,
		BatchUnitFeeResultRepo:              BatchUnitFeeResultRepo,
		BatchAllocateForecastUnitResultRepo: BatchAllocateForecastUnitResultRepo,
		LocationVolumeService:               LocationVolumeService,
		BatchForecastUnitService:            BatchForecastUnitService,
		BatchMinuteOrderConfService:         BatchMinuteOrderConfService,
		ShopWhitelistService:                ShopWhitelistService,
	}
}

func (s *StartBatchForecastUnitImpl) Name() string {
	return constant.TaskStartBatchForecastUnit
}

// 定时任务--
func (s *StartBatchForecastUnitImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	//1. 使用uuid替换request id，方便查日志 => 原生request id字符串较短，容易查到不相干的日志
	requestID := uuid.NewV4().String() // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)
	logger.CtxLogInfof(ctx, "batch allocate forecast|start|ShardingParam=%s|sharding no=%v|sharding total=%v", args.ShardingParam, args.ShardingNo, args.TotalShardings)

	//2. 检索pending态的batch forecast unit
	forecastUnits, gErr := s.BatchAllocateForecastRepo.GetForecastUnitsByCondition(ctx, map[string]interface{}{
		"batch_status = ?": model.BatchForecastUnitPending,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "batch allocate StartBatchForecastUnitImpl| get pending forecast unit list err:%v", gErr)
		return gErr
	}

	//3.循环列表，并执行
	for _, forecastUnitTab := range forecastUnits {
		tab := forecastUnitTab
		tab.ShardingNo = args.ShardingNo
		tab.TotalShardingNum = args.TotalShardings

		//获取对应的executor，并执行
		startTime := timeutil.GetCurrentUnixTimeStamp(ctx)

		executor := batch_allocate_forecast_unit_chain.NewBAForecastUnitExecutor(
			s.BatchAllocateForecastRepo, s.SplittingRuleServer, s.BAForecastVolumeRepo, s.LpsApi, s.RateApi,
			s.AllocateForecastTaskConfigRepo, s.LocationVolumeService, s.BatchForecastUnitService,
			s.BatchUnitTargetResultRepo, s.BatchUnitFeeResultRepo, s.BatchAllocateForecastUnitResultRepo,
			s.BatchMinuteOrderConfService, s.ShopWhitelistService,
		)

		if err := executor.Execute(ctx, &tab); err != nil {
			logger.CtxLogErrorf(ctx, "batch allocate|execute task:%v, err:%v, will carry on next one", tab.Id, err)
			continue
		} else {
			//没有错误，说明正常执行完成，退出任务
			endTime := timeutil.GetCurrentUnixTimeStamp(ctx)
			logger.CtxLogInfof(ctx, "batch allocate|success to execute task:%v|cost time:%v", tab.Id, endTime-startTime)
			break
		}
	}

	return nil
}
