package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	jsoniter "github.com/json-iterator/go"
)

type AllocateStoreConsumer struct {
	AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo
	AllocateRankSrv       service.AllocateRankService
}

func NewAllocateStoreConsumer(
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	allocateRankSrv service.AllocateRankService,
) *AllocateStoreConsumer {
	return &AllocateStoreConsumer{
		AllocateOrderDataRepo: allocateOrderDataRepo,
		AllocateRankSrv:       allocateRankSrv,
	}
}

func (s *AllocateStoreConsumer) Name() string {
	return constant.TaskNameAllocateStoreConsumer
}

func (s *AllocateStoreConsumer) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var reply saturn.SaturnReply
	var msg AllocateStoreMsg
	if err := jsoniter.Unmarshal(message.MsgText, &msg); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	// create order data
	orderDataTab := msg.ToAllocateOrderDataTab()
	//SSCSMR-1499：使用开关控制，禁止tidb写入
	mForecastConf := configutil.GetMaskingForecastConf(ctx)
	if !mForecastConf.ForbidTidbData {
		if err := s.AllocateOrderDataRepo.CreateAllocateOrderData(ctx, orderDataTab); err != nil {
			logger.CtxLogErrorf(ctx, "CreateAllocateOrderData fail|err=%v", err)
			reply.Retcode = -1
			reply.Message = err.Error()
			return &reply
		}
	}

	// statics rank
	dateRank, err := s.AllocateRankSrv.StatisticsDateRank(ctx, []*model.AllocateOrderDataTab{orderDataTab})
	if err != nil {
		logger.CtxLogErrorf(ctx, "StatisticsDateRank fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	if err := s.AllocateRankSrv.IncrDateRank(ctx, dateRank); err != nil {
		logger.CtxLogErrorf(ctx, "IncrDateRank fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	return &reply
}
