package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	jsoniter "github.com/json-iterator/go"
)

// ScheduleCountStatTask 异步处理Allocate场景的调度可视化统计数据保存到Redis，由于保存到Redis这一步有放大，
// 为避免大促流量直接冲击Redis造成压力，主要目的是对大促流量进行削峰
type ScheduleCountStatTask struct {
	scheduleCountStat schedule_visual.ScheduleCountStatInterface
}

func NewScheduleCountStatTask(scheduleCountStat schedule_visual.ScheduleCountStatInterface) *ScheduleCountStatTask {
	return &ScheduleCountStatTask{
		scheduleCountStat: scheduleCountStat,
	}
}

func (a *ScheduleCountStatTask) Name() string {
	return constant.TaskNameScheduleCountStat
}

func (a *ScheduleCountStatTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var reply saturn.SaturnReply

	// 1. 反序列化消息为统计数据结构体
	var msg allocation.ScheduleCountStatTaskMsg
	if err := jsoniter.Unmarshal(message.MsgText, &msg); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	// 防止AllocationLog为nil出现空指针异常？
	if msg.AllocationLog != nil {
		var softCriteriaList []allocation.SoftCriteria
		if err := jsoniter.UnmarshalFromString(msg.AllocationLog.SoftCriteriaListStr, &softCriteriaList); err == nil {
			msg.AllocationLog.SoftCriteriaList = softCriteriaList
		}
	}

	// 2. 进行统计数据的处理
	if err := a.scheduleCountStat.ScheduleCountStat(ctx, msg.AllocationLog, msg.BusinessType, msg.BusinessId); err != nil { // nolint
		logger.CtxLogErrorf(ctx, "ScheduleCountStat fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}

	return &reply
}
