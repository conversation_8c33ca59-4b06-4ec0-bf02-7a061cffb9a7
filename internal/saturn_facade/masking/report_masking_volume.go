package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type ReportMaskingVolumeTask struct {
	MaskingVolumeService volume_dashboard.MaskingVolumeService
}

func NewReportMaskingVolumeTask(maskingVolumeService volume_dashboard.MaskingVolumeService) *ReportMaskingVolumeTask {
	return &ReportMaskingVolumeTask{
		MaskingVolumeService: maskingVolumeService,
	}
}

func (r *ReportMaskingVolumeTask) Name() string {
	return constant.TaskReportMaskingVolume
}

func (r *ReportMaskingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "ReportMaskingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := r.MaskingVolumeService.ReportMaskingVolume(ctx)
	if err != nil {
		return err
	}
	return nil
}
