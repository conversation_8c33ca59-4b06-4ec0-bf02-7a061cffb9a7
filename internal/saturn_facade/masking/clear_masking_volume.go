package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
)

type ClearMaskingVolumeTask struct {
	MaskingVolumeService volume_dashboard.MaskingVolumeService
}

func NewClearMaskingVolumeTask(maskingVolumeService volume_dashboard.MaskingVolumeService) *ClearMaskingVolumeTask {
	return &ClearMaskingVolumeTask{
		MaskingVolumeService: maskingVolumeService,
	}
}

func (c *ClearMaskingVolumeTask) Name() string {
	return constant.TaskClearMaskingVolume
}

// 传入参数格式为数字，删除传入数字前的数据
func (c *ClearMaskingVolumeTask) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "ClearMaskingVolumeTask|start|ShardingParam:%v", args.ShardingParam)
	err := c.MaskingVolumeService.ClearMaskingVolume(ctx, args.ShardingParam)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearMaskingVolumeTask|clear masking volume err:%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "ClearMaskingVolumeTask|end")
	return nil
}
