package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

const (
	BatchExecuteNum = 100
)

type AllocateScheduleVisualTask struct {
	ScheduleCountStat schedule_visual.ScheduleCountStatInterface
}

func NewAllocateScheduleVisualTask(scheduleCountStat schedule_visual.ScheduleCountStatInterface) *AllocateScheduleVisualTask {
	allocateScheduleVisualTask := &AllocateScheduleVisualTask{
		ScheduleCountStat: scheduleCountStat,
	}
	return allocateScheduleVisualTask
}

func (a *AllocateScheduleVisualTask) Name() string {
	return constant.TaskAllocateScheduleVisual
}

// MsgHandle
// 根据串行异步消费功能来实现批量消费。串行异步消费默认每条消息都是成功消费的，手动commit的offsetId标识消息消费到哪一步，服务重启时从commit的offsetId处重新开始消费
// offsetId commit失败但SyncStatResult成功执行时，如果发生服务重启，会出现重复消费
// offsetId commit成功但syncStatResult执行失败，此时会丢数据
// 该消息任务只能配置成串行消费，不能配置成并行消费
func (a *AllocateScheduleVisualTask) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) {
	// 1. 反序列化消息为统计数据结构体
	var msg *allocation.LogDetail
	tempText := message.MsgText
	marshalBytes, zipErr := zip.ZSTDDecompress(tempText)
	if zipErr != nil {
		logger.CtxLogErrorf(ctx, "AllocateScheduleVisualTask|failed to decompress, message=%v, err:%v", string(tempText), zipErr)
		return
	}
	if err := jsoniter.Unmarshal(marshalBytes, &msg); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal msg body fail|message=%v, err=%v", string(marshalBytes), err)
		return
	}
	logger.CtxLogInfof(ctx, "AllocateScheduleVisualTask|message=%v", string(marshalBytes))

	// 过滤estimate channel的订单，只统计allocate的单
	if msg.OrderId == 0 || msg.UniqueId != "" {
		return
	}

	// 防止AllocationLog为nil出现空指针异常
	if msg != nil {
		var softCriteriaList []allocation.SoftCriteria
		err := jsoniter.UnmarshalFromString(msg.SoftCriteriaListStr, &softCriteriaList)
		if err == nil {
			msg.SoftCriteriaList = softCriteriaList
		}
	}

	// 2. 解析log日志，统计订单分布
	businessId := timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), schedule_stat.MaskingScheduleStatHourTimeFormat)
	if msg.RequestTime != 0 { // nolint
		businessId = timeutil.FormatDateTimeByFormat(timeutil.GetLocalTimeByTimestamp(msg.RequestTime/1000), schedule_stat.MaskingScheduleStatHourTimeFormat) // nolint
		logger.CtxLogInfof(ctx, "request_id:%s, request_time:%d", msg.RequestId, msg.RequestTime)                                                             // nolint
	}
	err1 := a.ScheduleCountStat.ScheduleCountStat(ctx, msg, schedule_stat.AllocateV2, businessId) // nolint
	if err1 != nil {
		logger.CtxLogErrorf(ctx, "ScheduleCountStat error, err=%v", err1)
	}

	// 3. 实现批量消费。当offsetId % n == 0时执行运力同步，相当于n条数据批量消费
	if message.OffsetID%BatchExecuteNum == 0 {
		// 服务重启时会从上次commit的offsetId处开始重新消费，本次commit offsetId失败还是会继续往下消费，故不管commit成功与否都需要执行同步运力的任务
		err := saturn.Commit(ctx, message.OffsetID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "allocate schedule visual|batch consume error|commit failure, err=%v", err)
			monitoring.ReportError(ctx, monitoring.CatScheduleVisualMonitor, monitoring.BatchConsumeCommitError, fmt.Sprintf("allocate schedule visual|batch consume error|commit failure, err=%v", err))
		}
		syncErr := a.ScheduleCountStat.SyncStatResult(ctx, schedule_stat.AllocateV2, "")
		if syncErr != nil {
			logger.CtxLogErrorf(ctx, "allocate schedule visual|sync stat result error, err=%v", syncErr)
			monitoring.ReportError(ctx, monitoring.CatScheduleVisualMonitor, monitoring.SyncAllocateStatResultError, fmt.Sprintf("allocate schedule visual|sync stat result error, err=%v", syncErr))
		}
	}
}
