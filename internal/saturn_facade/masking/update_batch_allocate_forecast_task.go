package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast"
)

type UpdateBatchAllocateForecastTaskImpl struct {
	BatchAllocateForecastService forecast.BatchAllocateForecastService
}

func NewUpdateBatchAllocateForecastTaskImpl(batchAllocateForecastService forecast.BatchAllocateForecastService) *UpdateBatchAllocateForecastTaskImpl {
	return &UpdateBatchAllocateForecastTaskImpl{
		BatchAllocateForecastService: batchAllocateForecastService,
	}
}

func (u *UpdateBatchAllocateForecastTaskImpl) Name() string {
	return constant.TaskUpdateBatchAllocateForecastTask
}

// RpcHandle
/**
 * 1. 查询所有process状态的forecast_task，分别检查对应的sub_task的status，全部完成则更新forecast task的status
 * 2. 检查sub_task的status是否成功，是则统计sub_task预测结果大纲
 */
func (u *UpdateBatchAllocateForecastTaskImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	logger.CtxLogInfof(ctx, "execute update_batch_allocate_forecast_task")
	// 1. 更新sub_task及forecast_task的status
	err := u.BatchAllocateForecastService.UpdateBatchAllocateTaskStatus(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "execute update_batch_allocate_forecast_task fail|database error :%v", err)
		return err
	}
	logger.CtxLogInfof(ctx, "execute update_batch_allocate_forecast_task success")
	return nil
}
