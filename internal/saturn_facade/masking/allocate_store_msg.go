package masking

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
)

type AllocateStoreMsg struct {
	OrderId              uint64                   `json:"order_id"`
	MaskingProductID     int                      `json:"masking_product_id"`
	RequestData          model.OmsAllocRequest    `json:"request_data"`
	OmsAllocateRequest   model.OMSAllocateRequest `json:"oms_allocate_request"`
	HardResult           model.ValidateResult     `json:"hard_result"`
	ResponseData         model.OmsAllocResp       `json:"response_data"`
	FulfillmentProductID int                      `json:"fulfillment_product_id"`
	ShopGroupId          int                      `json:"shop_group_id"`
	ZoneCode             string                   `json:"zone_code"`
	OriginZoneCode       string                   `json:"origin_zone_code"`
	DestZoneCode         string                   `json:"dest_zone_code"`
	RouteCode            string                   `json:"route_code"`
	Ignore               bool                     `json:"ignore"`
	OrderTime            uint32                   `json:"order_time"`
	ShippingFee          float64                  `json:"shipping_fee"`
}

func (a *AllocateStoreMsg) ToAllocateOrderDataTab() *model.AllocateOrderDataTab {
	return &model.AllocateOrderDataTab{
		OrderId:              a.OrderId,
		MaskingProductID:     a.MaskingProductID,
		RequestData:          toBytes(a.RequestData),
		OmsAllocateRequest:   toBytes(a.OmsAllocateRequest),
		HardResult:           toBytes(a.HardResult),
		ResponseData:         toBytes(a.ResponseData),
		FulfillmentProductID: a.FulfillmentProductID,
		ShopGroupId:          a.ShopGroupId,
		ZoneCode:             a.ZoneCode,
		OriginZoneCode:       a.OriginZoneCode,
		DestZoneCode:         a.DestZoneCode,
		RouteCode:            a.RouteCode,
		OrderTime:            a.OrderTime,
	}
}

func toBytes(obj interface{}) []byte {
	return []byte(str.JsonString(obj))
}
