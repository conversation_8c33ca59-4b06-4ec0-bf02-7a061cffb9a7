package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	jsoniter "github.com/json-iterator/go"
)

type UpdateZoneVolume struct {
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
}

func NewUpdateZoneVolume(
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
) *UpdateZoneVolume {
	return &UpdateZoneVolume{
		MaskRuleVolumeRepo: MaskRuleVolumeRepo,
	}
}

func (s *UpdateZoneVolume) Name() string {
	return constant.TaskNameUpdateZoneVolume
}

func (s *UpdateZoneVolume) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	startTime := recorder.Now(ctx).Unix()
	jobMsgBody, err := redisutil.GetDefaultInstance().Get(ctx, string(message.MsgText)).Bytes()
	if err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolumeJob|Get data from redis failed|key=%s,err=%v", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}
	var jobMsg rulevolume2.UpdateMaskZoneVolumeMsg
	if err := jsoniter.Unmarshal(jobMsgBody, &jobMsg); err != nil {
		errMsg := fmt.Sprintf("Unmarshal failed|MsgText=%v,err=%v", message.MsgText, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	for _, zoneVolume := range jobMsg.ZoneVolumes {
		zoneVolume.MaskProductID = jobMsg.RuleVolume.MaskProductID
		zoneVolume.RuleVolumeID = jobMsg.RuleVolume.ID
	}

	if err := s.MaskRuleVolumeRepo.DeleteRouteLimitsByRuleVolumeID(ctx, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateZoneVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if err := s.MaskRuleVolumeRepo.DeleteZoneLimitsByRuleVolumeID(ctx, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateZoneVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if err := s.MaskRuleVolumeRepo.BatchCreateZoneVolumes(ctx, jobMsg.ZoneVolumes); err != nil {
		errMsg := fmt.Sprintf("UpdateZoneVolume|BatchCreateZoneVolumes failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	// 所有操作完成后再更新一次Status
	if _, err := s.MaskRuleVolumeRepo.UpdateRuleVolume(ctx, jobMsg.RuleVolume); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolumeJob|UpdateRuleVolume failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	successMsg := fmt.Sprintf("UpdateZoneVolume | update zone volumes success, rule volume id = %v, rule volume = %s, zone volumes len = %v",
		jobMsg.RuleVolume.ID, str.JsonString(jobMsg.RuleVolume), len(jobMsg.ZoneVolumes))
	logger.CtxLogInfof(ctx, successMsg)

	//上报配置变更平台
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "update_zone_volume", "mask product route volume import", successMsg, report_constant.RiskLevelMid)

	return &saturn.SaturnReply{Retcode: 0, Message: successMsg}
}

type UpdateRouteVolume struct {
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
}

func NewUpdateRouteVolume(
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
) *UpdateRouteVolume {
	return &UpdateRouteVolume{
		MaskRuleVolumeRepo: MaskRuleVolumeRepo,
	}
}

func (s *UpdateRouteVolume) Name() string {
	return constant.TaskNameUpdateRouteVolume
}

func (s *UpdateRouteVolume) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	startTime := recorder.Now(ctx).Unix()

	jobMsgBody, err := redisutil.GetDefaultInstance().Get(ctx, string(message.MsgText)).Bytes()
	if err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|Get data from redis failed|key=%s,err=%v", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}
	var jobMsg rulevolume2.UpdateMaskRouteVolumeMsg
	if err := jsoniter.Unmarshal(jobMsgBody, &jobMsg); err != nil {
		errMsg := fmt.Sprintf("Unmarshal failed|MsgText=%v,err=%v", message.MsgText, err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	for _, routeVolume := range jobMsg.RouteVolumes {
		routeVolume.MaskProductID = jobMsg.RuleVolume.MaskProductID
		routeVolume.RuleVolumeID = jobMsg.RuleVolume.ID
	}

	if err := s.MaskRuleVolumeRepo.DeleteRouteLimitsByRuleVolumeID(ctx, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if err := s.MaskRuleVolumeRepo.DeleteZoneLimitsByRuleVolumeID(ctx, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	if err := s.MaskRuleVolumeRepo.BatchCreateRouteVolumes(ctx, jobMsg.RouteVolumes); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|BatchCreateRouteVolumes failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	// 所有操作完成后再更新一次Status
	if _, err := s.MaskRuleVolumeRepo.UpdateRuleVolume(ctx, jobMsg.RuleVolume); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolumeJob|UpdateRuleVolume failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}

	successMsg := fmt.Sprintf("UpdateRouteVolume | update route volumes success, rule volume id = %v, rule volume = %s, route volumes len = %v",
		jobMsg.RuleVolume.ID, str.JsonString(jobMsg.RuleVolume), len(jobMsg.RouteVolumes))
	logger.CtxLogInfof(ctx, successMsg)
	//上报配置变更平台
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "update_route_volume", "mask product route volume import", successMsg, report_constant.RiskLevelMid)

	return &saturn.SaturnReply{Retcode: 0, Message: successMsg}
}

type UpdateRouteAndZoneVolume struct {
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo
}

func NewUpdateRouteAndZoneVolume(
	MaskRuleVolumeRepo rulevolume.IMaskRuleVolumeRepo,
) *UpdateRouteAndZoneVolume {
	return &UpdateRouteAndZoneVolume{
		MaskRuleVolumeRepo: MaskRuleVolumeRepo,
	}
}

func (s *UpdateRouteAndZoneVolume) Name() string {
	return constant.TaskUpdateRouteAndZoneVolume
}

func (s *UpdateRouteAndZoneVolume) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	startTime := recorder.Now(ctx).Unix()
	logger.CtxLogErrorf(ctx, "get a message %s", message.MsgText)
	jobMsgBody, err := redisutil.GetDefaultInstance().Get(ctx, string(message.MsgText)).Bytes()
	if err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|Get data from redis failed|key=%s,err=%v", string(message.MsgText), err)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneError, errMsg)
		return &saturn.SaturnReply{Retcode: -1, Message: errMsg}
	}
	jobMsg, uerr := UnmarshalMessageJob(ctx, jobMsgBody)
	if uerr != nil || jobMsg == nil {
		monitoring.ReportError(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneError, fmt.Sprintf("Unmarshal Message failed %v", uerr))
		return &saturn.SaturnReply{Retcode: -1, Message: fmt.Sprintf("Unmarshal Message failed %v", uerr)}
	}
	// 检查传过来的Volume rule是否存在
	if err := s.checkVolumeRuleExist(ctx, jobMsg); err != nil {
		monitoring.ReportError(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneError, fmt.Sprintf("check volume rule exist failed %v", err))
		return &saturn.SaturnReply{Retcode: -1, Message: fmt.Sprintf("check volume rule exist failed %v", err)}
	}
	// 处理一下route和zone
	processRouteAndZone(jobMsg)

	db, err := dbutil.MasterDB(ctx, rulevolume.MaskRuleVolumeTabHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get db err %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneError, fmt.Sprintf("get db err %v", err))
		return &saturn.SaturnReply{Retcode: -1, Message: fmt.Sprintf("get db failed %v", err)}
	}
	ctx = scormv2.BindContext(ctx, db)
	if err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		// 批量删除旧的route&zone
		if err := s.batchDeleteOldRouteAndZone(ctx, db, jobMsg); err != nil {
			return err
		}
		// 批量创建新的route&zone
		if err := s.batchCreateNewRouteAndZone(ctx, db, jobMsg); err != nil {
			return err
		}

		// 所有操作完成后再更新一次Status
		if err := s.MaskRuleVolumeRepo.UpdateRuleVolumeWithTx(ctx, db, jobMsg.RuleVolume); err != nil {
			return err
		}

		return nil
	}); err != nil {
		logger.CtxLogErrorf(ctx, "batch create route or zone failed %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneError, fmt.Sprintf("batch create route or zone failed %v", err))
		return &saturn.SaturnReply{Retcode: -1, Message: fmt.Sprintf("transaction failed %v", err)}
	}

	successMsg := fmt.Sprintf("UpdateRouteVolume | update route volumes success, rule volume id = %v, rule volume = %s, route volumes len = %v",
		jobMsg.RuleVolume.ID, str.JsonString(jobMsg.RuleVolume), len(jobMsg.RouteVolumes))
	logger.CtxLogInfof(ctx, successMsg)
	// 解析route or zone成功
	monitoring.ReportSuccess(ctx, monitoring.CatModuleMsgTask, monitoring.RouteAndZoneSuccess, successMsg)
	//上报配置变更平台
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "update_route_volume", "mask product route volume import", successMsg, report_constant.RiskLevelMid)

	return &saturn.SaturnReply{Retcode: 0, Message: successMsg}
}

func processRouteAndZone(jobMsg *rulevolume2.UpdateMaskRouteAndZoneVolumeMsg) {
	for _, routeVolume := range jobMsg.RouteVolumes {
		routeVolume.RuleVolumeID = jobMsg.RuleVolume.ID
	}

	for _, zoneVolume := range jobMsg.ZoneVolumes {
		zoneVolume.RuleVolumeID = jobMsg.RuleVolume.ID
	}
}

func (s *UpdateRouteAndZoneVolume) checkVolumeRuleExist(ctx context.Context, jobMsg *rulevolume2.UpdateMaskRouteAndZoneVolumeMsg) *srerr.Error {
	volumeRule, rerr := s.MaskRuleVolumeRepo.GetRuleVolumeByID(ctx, jobMsg.RuleVolume.ID)
	if rerr != nil || volumeRule == nil {
		errMsg := fmt.Sprintf("not find volume rle %+v, err is %v", volumeRule, rerr)
		logger.CtxLogErrorf(ctx, errMsg)
		return srerr.New(srerr.ParamErr, nil, errMsg)
	}

	return nil
}

func UnmarshalMessageJob(ctx context.Context, jobMsgBody []byte) (*rulevolume2.UpdateMaskRouteAndZoneVolumeMsg, error) {
	var jobMsg rulevolume2.UpdateMaskRouteAndZoneVolumeMsg
	if err := jsoniter.Unmarshal(jobMsgBody, &jobMsg); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal failed|MsgText=%s,err=%v", string(jobMsgBody), err)
		return nil, err
	}

	return &jobMsg, nil
}

func (s *UpdateRouteAndZoneVolume) batchDeleteOldRouteAndZone(ctx context.Context, db scormv2.SQLCommon, jobMsg *rulevolume2.UpdateMaskRouteAndZoneVolumeMsg) *srerr.Error {
	if err := s.MaskRuleVolumeRepo.DeleteRouteLimitsByRuleVolumeIDWithTx(ctx, db, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return err
	}

	if err := s.MaskRuleVolumeRepo.DeleteZoneLimitsByRuleVolumeIDWithTx(ctx, db, jobMsg.RuleVolume.ID); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|DeleteRouteLimitsByRuleVolumeID failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return err
	}

	return nil
}

func (s *UpdateRouteAndZoneVolume) batchCreateNewRouteAndZone(ctx context.Context, db scormv2.SQLCommon, jobMsg *rulevolume2.UpdateMaskRouteAndZoneVolumeMsg) *srerr.Error {
	if err := s.MaskRuleVolumeRepo.BatchCreateRouteVolumesTx(ctx, db, jobMsg.RouteVolumes); err != nil {
		errMsg := fmt.Sprintf("UpdateRouteVolume|BatchCreateRouteVolumes failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return err
	}
	if err := s.MaskRuleVolumeRepo.BatchCreateZoneVolumesWithTx(ctx, db, jobMsg.ZoneVolumes); err != nil {
		errMsg := fmt.Sprintf("UpdateZoneVolume|BatchCreateZoneVolumes failed|err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		return err
	}

	return nil
}
