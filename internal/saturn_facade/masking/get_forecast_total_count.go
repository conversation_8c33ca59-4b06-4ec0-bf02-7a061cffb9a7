package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"strconv"
)

const (
	defaultChannelSize = 1000
)

type GetForecastTotalCountImpl struct {
	maskMainTaskRepo          repo.AllocateForecastTaskConfigRepo
	allocateOrderDataRepo     allocate_order_data_repo.AllocateOrderDataRepo
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService
}

func NewGetForecastTotalCountImpl(maskMainTaskRepo repo.AllocateForecastTaskConfigRepo,
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService) *GetForecastTotalCountImpl {
	return &GetForecastTotalCountImpl{
		maskMainTaskRepo:          maskMainTaskRepo,
		allocateOrderDataRepo:     allocateOrderDataRepo,
		ForecastingSubTaskService: ForecastingSubTaskService,
	}
}

func (g *GetForecastTotalCountImpl) Name() string {
	return constant.TaskGetForecastTotalCount
}

// 定时任务--检索所有process态的masking forecast task，并计算其总单量，然后设置到redis中
func (g *GetForecastTotalCountImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	if !configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) {
		logger.CtxLogInfof(ctx, "GetForecastTotalCountImpl|config toggle not open, return")
		return nil
	}
	//1.检索所有process态、pending态masking forecast task
	condition := map[string]interface{}{
		"task_status in (?)": []int{constant2.TaskConfigStatusProcess, constant2.TaskConfigStatusPending},
	}
	taskList, gErr := g.maskMainTaskRepo.GetForecastTaskConfigByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl| get task of 'pending' or 'process' err:%v", gErr)
		return gErr
	}
	for _, tempTask := range taskList {
		//兼容batch
		if tempTask.AllocationMethod == constant2.AllocateMethodBatch {
			continue
		}
		//2.如果该任务不是第一次检索，即redis有值，就跳过 (不需要考虑并发，定时任务单机执行
		needCount := g.needCount(ctx, tempTask)
		if !needCount {
			continue
		}
		//3.根据main task获取所有sub task
		condition := map[string]interface{}{
			"main_task_id = ?": tempTask.Id,
			"module_name = ?":  forecasting_sub_task.ModuleMaskingForecast,
		}
		subTaskList, gErr := g.ForecastingSubTaskService.SelectSubTask(ctx, condition)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl|condition:%v, select sub task err:%v", gErr)
			continue
		}
		//遍历所有的sub task，检索对应区间的数据并填充到redis中
		channelSize := defaultChannelSize
		conf := configutil.GetMaskingForecastConf(ctx)
		if conf.ChannelSize != 0 {
			channelSize = conf.ChannelSize
		}
		for _, tempSubTask := range subTaskList {
			startTime, err := strconv.ParseInt(tempSubTask.StartKey, 10, 64)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl| start key:%v, parse to int err:%v", tempSubTask.StartKey, err)
				continue
			}
			endTime, err := strconv.ParseInt(tempSubTask.EndKey, 10, 64)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl| end key:%v, parse to int err:%v", tempSubTask.EndKey, err)
				continue
			}
			totalCount, _ := g.allocateOrderDataRepo.GetAllocateOrderTotalCount(ctx, uint64(tempTask.MaskingProductID), startTime, endTime, channelSize)
			if _, iErr := redisutil.IncrBy(ctx, fmt.Sprintf(constant.TaskScheduleTotalOrderNum, tempTask.Id), totalCount); iErr != nil {
				logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl|incr redis, key:%v, value:%v, err:%v", fmt.Sprintf(constant.TaskScheduleTotalOrderNum, tempTask.Id), totalCount, iErr)
				return iErr
			}
		}
	}
	return nil
}

func (g *GetForecastTotalCountImpl) needCount(ctx context.Context, task *model.AllocateForecastTaskConfigTab) bool {
	key := fmt.Sprintf(constant.TaskScheduleTotalOrderNum, task.Id)
	currentCount, err := redisutil.GetInt(ctx, key)
	//SSCSMR-1480:key不存在会返回redis.Nil
	if err != nil && err != redis.Nil {
		logger.CtxLogErrorf(ctx, "GetForecastTotalCountImpl|needCount|get value from redis,key:%v,err:%v", key, err)
		return false
	}
	if currentCount != 0 {
		logger.CtxLogInfof(ctx, "GetForecastTotalCountImpl|needCount|get value from redis,key:%v, count:%v, no need count", key, currentCount)
		return false
	}

	return true
}
