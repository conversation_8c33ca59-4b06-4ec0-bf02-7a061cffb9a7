package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/bytedance/sonic"
	uuid "github.com/satori/go.uuid"
	"github.com/shopspring/decimal"
	"runtime"
	"strings"
)

const (
	yesterday       = -1
	today           = 0
	defaultSaltSize = 1000
	channelSize     = 500
	panicMsgSize    = 4096
)

type AllocateHistoryOutLine struct {
	AllocateHistoryOutlineRepo repo.AllocateHistoryOutlineRepo
}

func NewAllocateHistory(AllocateHistoryOutlineRepo repo.AllocateHistoryOutlineRepo) *AllocateHistoryOutLine {
	return &AllocateHistoryOutLine{
		AllocateHistoryOutlineRepo: AllocateHistoryOutlineRepo,
	}
}

func (a *AllocateHistoryOutLine) Name() string {
	return constant.TaskAllocateHistory
}

//聚合allocate历史订单信息
func (a *AllocateHistoryOutLine) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	requestID := uuid.NewV4().String()
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)
	//1.获取所有的mask product
	productMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	var maskProductIds []uint64
	for _, product := range productMap {
		//convert product base info
		productBaseInfo, ok := product.(*lpsclient.LogisticProductTab)
		if !ok {
			logger.CtxLogErrorf(ctx, "product info:%+v, convert product base info err", product)
		} else {
			//add mask product id
			if productBaseInfo.IsMaskingProduct {
				maskProductIds = append(maskProductIds, uint64(productBaseInfo.ProductId))
			}
		}
	}
	//2.遍历所有的mask product，根据昨天的时间去检索, 获取hbase数据
	//allocateOrderCh 是用来存储hbase results
	allocateOrderCh := make(chan *gohbase.Result, channelSize)
	yesterdayUnix := timeutil.GetNextDayStartTimeByCid(ctx, yesterday, strings.ToUpper(envvar.GetCID()))
	todayUnix := timeutil.GetNextDayStartTimeByCid(ctx, today, strings.ToUpper(envvar.GetCID()))
	//开协程获取数据
	go func(allocateOrderCh chan *gohbase.Result) {
		//close channel
		defer close(allocateOrderCh)
		//recover goroutine panic
		defer func() {
			if err := recover(); err != nil {
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("AllocateHistoryOutLine|[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		//获取table name
		mainHbConf := configutil.GetMainHbaseConfig(ctx)
		tableName := mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
		saltSize := mainHbConf.SaltSize
		batchSize := mainHbConf.BatchSize

		if saltSize == 0 {
			saltSize = defaultSaltSize
		}
		hbHelper := masking_forecast_hbase.NewMainHBHelper()
		//按mask product在hbase检索数据
		for _, maskProductId := range maskProductIds {
			//salt是按request time整除3位数取余得到，因此遍历0～999相当于全表检索
			for i := 0; i < saltSize; i++ {
				//定义检索的region，开始key，结束key
				salt := i
				startRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, yesterdayUnix)
				endRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, todayUnix)

				logger.CtxLogInfof(ctx, "AllocateHistoryOutLine| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

				err := hbHelper.ScanWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, nil)
				if err != nil {
					logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
					continue
				}
			}
		}
	}(allocateOrderCh)
	//3.按两个维度聚合，然后批量插入db：1.region level；2.zone level
	maskFulfillmentAggregatedMap := make(map[uint64]map[uint64]productInfo, 0)
	maskZoneProductCountMap := make(map[uint64]map[string]map[uint64]int64, 0)
	maskRouteProductCountMap := make(map[uint64]map[string]map[uint64]int64, 0)
	for allocateOrder := range allocateOrderCh {
		//3.1 转换entity
		entity, tErr := TransferResultToEntity(ctx, allocateOrder)
		if tErr != nil {
			logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|convert to entity err:%v", tErr)
			continue
		}
		if entity.OrderId == 0 || entity.MaskProductId == 0 || entity.FulfillmentProductId == 0 {
			logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|order id:%v, mask product:%v, fulfillment product:%v, request string:%v", entity.OrderId, entity.MaskProductId, entity.FulfillmentProductId, entity.RequestDataStr)
			continue
		}
		//3.2 聚合product维度
		//匹配shipping fee
		var shippingFee float64
		for _, feeInfo := range entity.ShippingFeeList {
			if feeInfo.ProductId == int64(entity.FulfillmentProductId) {
				shippingFee = feeInfo.AllocateShippingFee
			}
		}
		if shippingFee == 0 {
			logger.CtxLogErrorf(ctx, "empty shipping fee|order id:%v, hard result:%v, fee list:%v", entity.OrderId, entity.FulfillmentProductId, entity.ShippingFeeList)
		}
		if _, ok := maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)]; !ok {
			maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)] = make(map[uint64]productInfo, 0)
		}
		if _, ok := maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)][uint64(entity.FulfillmentProductId)]; !ok {
			maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)][uint64(entity.FulfillmentProductId)] = productInfo{}
		}
		tempProductInfo := maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)][uint64(entity.FulfillmentProductId)]
		tempProductInfo.OrderCount += 1
		tempProductInfo.TotalShippingFee += shippingFee
		maskFulfillmentAggregatedMap[uint64(entity.MaskProductId)][uint64(entity.FulfillmentProductId)] = tempProductInfo

		//3.3 聚合zone维度
		if entity.ZoneDestinationCode != "" {
			if _, ok := maskZoneProductCountMap[uint64(entity.MaskProductId)]; !ok {
				maskZoneProductCountMap[uint64(entity.MaskProductId)] = make(map[string]map[uint64]int64, 0)
			}
			if _, ok := maskZoneProductCountMap[uint64(entity.MaskProductId)][entity.ZoneDestinationCode]; !ok {
				maskZoneProductCountMap[uint64(entity.MaskProductId)][entity.ZoneDestinationCode] = make(map[uint64]int64, 0)
			}
			//zone数据+1
			maskZoneProductCountMap[uint64(entity.MaskProductId)][entity.ZoneDestinationCode][uint64(entity.FulfillmentProductId)] += 1
		}

		// 3.4聚合route维度
		if len(entity.RouteCodes) != 0 {
			if _, ok := maskRouteProductCountMap[uint64(entity.MaskProductId)]; !ok {
				maskRouteProductCountMap[uint64(entity.MaskProductId)] = make(map[string]map[uint64]int64, 0)
			}
			if _, ok := maskRouteProductCountMap[uint64(entity.MaskProductId)][entity.RouteCodes[0]]; !ok {
				maskRouteProductCountMap[uint64(entity.MaskProductId)][entity.RouteCodes[0]] = make(map[uint64]int64, 0)
			}
			//route数据+1
			maskRouteProductCountMap[uint64(entity.MaskProductId)][entity.RouteCodes[0]][uint64(entity.FulfillmentProductId)] += 1
		}
	}
	logger.CtxLogInfof(ctx, "AllocateHistoryOutLine| product aggregated length:%v, zone aggregated length:%v, route aggregated length:%v", len(maskFulfillmentAggregatedMap), len(maskZoneProductCountMap), len(maskRouteProductCountMap))
	//4. 批量插入db
	var tabs []model.AllocateHistoryOutlineTab
	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)

	for maskProductId, productAggregatedMap := range maskFulfillmentAggregatedMap {
		for productId, aggregatedInfo := range productAggregatedMap {
			tab := model.AllocateHistoryOutlineTab{
				ResultType:    model.AllocateHistoryTypeProduct,
				MaskProductId: maskProductId,
				DateUnix:      yesterdayUnix,
				ProductId:     productId,
				OrderQuantity: aggregatedInfo.OrderCount,
				CTime:         nowTime,
				MTime:         nowTime,
			}
			d := decimal.NewFromFloat(aggregatedInfo.TotalShippingFee)
			tab.TotalShippingFee = d.String()
			tabs = append(tabs, tab)
		}
	}

	for maskProductId, zoneProductCountMap := range maskZoneProductCountMap {
		for zoneCode, productCountMap := range zoneProductCountMap {
			for product, count := range productCountMap {
				tab := model.AllocateHistoryOutlineTab{
					ResultType:    model.AllocateHistoryTypeZone,
					DateUnix:      yesterdayUnix,
					MaskProductId: maskProductId,
					ProductId:     product,
					ZoneCode:      zoneCode,
					OrderQuantity: count,
					CTime:         nowTime,
					MTime:         nowTime,
				}
				tabs = append(tabs, tab)
			}
		}
	}

	for maskProductId, routeProductCountMap := range maskRouteProductCountMap {
		for routeCode, productCountMap := range routeProductCountMap {
			for product, count := range productCountMap {
				tab := model.AllocateHistoryOutlineTab{
					ResultType:    model.AllocateHistoryTypeRoute,
					DateUnix:      yesterdayUnix,
					MaskProductId: maskProductId,
					ProductId:     product,
					RouteCode:     routeCode,
					OrderQuantity: count,
					CTime:         nowTime,
					MTime:         nowTime,
				}
				tabs = append(tabs, tab)
			}
		}
	}

	if err := a.AllocateHistoryOutlineRepo.BatchInsert(ctx, tabs); err != nil {
		logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|batch insert fail, err:%v", err)
		return err
	}

	return nil
}

type productInfo struct {
	OrderCount       int64
	TotalShippingFee float64
}

func TransferResultToEntity(ctx context.Context, result *gohbase.Result) (model.AllocationHbaseEntity, *srerr.Error) {
	resultEntity := model.AllocationHbaseEntity{}
	//转换数据（包括解压+模型转换） 并写入channel
	if result == nil {
		logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|TransferResultToEntity|cannot parse nil hbase result, will skip it")
		return resultEntity, srerr.New(srerr.ParamErr, nil, "result is nil, can't convert it")
	}
	//此处实际v只会有一个，for循环的模式能避免result.Cells[0]空指针panic
	for _, v := range result.Cells {
		key := string(v.Qualifier[:])
		decodeBytes, err := zip.ZSTDDecompress(v.Value)
		if err != nil {
			logger.CtxLogErrorf(ctx, "AllocateHistoryOutLine|TransferResultToEntity|key:%v, decode value err:%v", key, err)
			return resultEntity, srerr.With(srerr.ParamErr, nil, err)
		}
		value := string(decodeBytes)
		if err := sonic.Unmarshal(decodeBytes, &resultEntity); err != nil {
			logger.CtxLogDebugf(ctx, "AllocateHistoryOutLine|TransferResultToEntity|value:%v, unmarshal value err:%v", value, err)
			return resultEntity, srerr.With(srerr.ParamErr, nil, err)
		}
	}
	return resultEntity, nil
}
