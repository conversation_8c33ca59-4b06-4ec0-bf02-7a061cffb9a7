package masking

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/forecast_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	uuid "github.com/satori/go.uuid"
)

type MaskingForecast struct {
	allocateForecastTaskConfigSrv  service.AllocateForecastTaskConfigService
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo
	allocateForecastSrv            masking_forecast.AllocateForecastService
	forecastingSubTaskRepo         forecasting_sub_task.ForecastingSubTaskRepo
	jobChainService                forecast_chain.JobChainService
}

func NewMaskingForecast(allocateForecastTaskConfigSrv service.AllocateForecastTaskConfigService,
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	allocateForecastSrv masking_forecast.AllocateForecastService,
	forecastingSubTaskRepo forecasting_sub_task.ForecastingSubTaskRepo,
	jobChainService forecast_chain.JobChainService,
) *MaskingForecast {
	return &MaskingForecast{
		allocateForecastTaskConfigSrv:  allocateForecastTaskConfigSrv,
		allocateForecastSrv:            allocateForecastSrv,
		allocateForecastTaskConfigRepo: allocateForecastTaskConfigRepo,
		forecastingSubTaskRepo:         forecastingSubTaskRepo,
		jobChainService:                jobChainService,
	}
}

func (s *MaskingForecast) Name() string {
	return internal_constant.TaskNameMaskingForecast
}

func (s *MaskingForecast) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	requestID := uuid.NewV4().String()
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	logger.CtxLogInfof(ctx, "MaskingForecast|start|ShardingParam=%s|sharding no=%v|sharding total=%v", args.ShardingParam, args.ShardingNo, args.TotalShardings)

	//SSCSMR-1480:获取pending态子任务
	subTasks, sErr := s.forecastingSubTaskRepo.GetTasksDesc(ctx, map[string]interface{}{
		"task_status=?": constant.TaskConfigStatusPending,
		"module_name=?": forecasting_sub_task.ModuleMaskingForecast,
	})
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "select pending sub tasks fail|err=%v", sErr)
		return sErr
	}

	//SSCSMR-1480:虽然是for循环，但只会匹配并执行其中的一个，如果execute失败则会处理下一个，execute成功就结束任务
	for _, tempSubTask := range subTasks {
		subTask := tempSubTask
		subTask.ShardingNo = args.ShardingNo //todo:SSCSMR-1480:sharding no 是从0开始的？
		subTask.TotalShardingNum = args.TotalShardings
		//存储任务开始时间到ctx
		startTime := timeutil.GetCurrentUnixTimeStamp(ctx)
		subTask.JobStartTime = startTime
		//SSCSMR-1480:获取对应的executor，并执行
		maskingExecutor := s.jobChainService.GetJobExecutor(forecast_chain.MaskingExecutor)
		if err := maskingExecutor.ExecuteChain(ctx, &subTask); err != nil {
			logger.CtxLogErrorf(ctx, "execute task:%v err:%v, will carry on next one", subTask.Id, err)
			continue
		} else {
			//没有错误，说明正常执行完成，退出任务
			endTime := timeutil.GetCurrentUnixTimeStamp(ctx)
			logger.CtxLogInfof(ctx, "success to execute task:%v|cost time:%v", subTask.Id, endTime-startTime)
			break
		}
	}

	return nil
}
