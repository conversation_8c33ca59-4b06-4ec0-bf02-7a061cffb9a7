package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
)

const (
	insertBatchSize = 1000
)

type ParseBatchVolume struct {
	MaskMainTaskRepo             repo.AllocateForecastTaskConfigRepo
	BatchAllocateForecastService service.BatchAllocateForecastService
	BatchAllocateVolumeRepo      rulevolume.BatchAllocateForecastVolumeRepo
}

func NewParseBatchVolume(maskMainTaskRepo repo.AllocateForecastTaskConfigRepo,
	batchAllocateForecastService service.BatchAllocateForecastService,
	batchAllocateVolumeRepo rulevolume.BatchAllocateForecastVolumeRepo) *ParseBatchVolume {
	return &ParseBatchVolume{
		MaskMainTaskRepo:             maskMainTaskRepo,
		BatchAllocateForecastService: batchAllocateForecastService,
		BatchAllocateVolumeRepo:      batchAllocateVolumeRepo,
	}
}

func (p *ParseBatchVolume) Name() string {
	return constant.TaskParseBatchVolume
}

// 解析Excel
func (p *ParseBatchVolume) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	/*
		1.获取pending状态 batch forecast task
		2.解析Excel
			2.1 解析成功，落库，使用redis记录，redis过期时间为1天
			2.2 解析失败，修改main task状态到failed
	*/

	//1.获取pending态的batch forecast
	taskList, gErr := p.MaskMainTaskRepo.GetForecastTaskConfigByCondition(ctx, map[string]interface{}{
		"task_status = ?":       constant2.TaskConfigStatusPending,
		"allocation_method = ?": constant2.BatchAllocate,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ParseBatchVolume|get batch allocate forecast pending task err:%v", gErr)
		return gErr
	}
	//2.遍历task列表并解析
	for _, task := range taskList {
		//先判断是否已经解析过
		if value, _ := redisutil.GetInt(ctx, repo.BatchVolumeKey(uint64(task.Id))); value != 0 {
			logger.CtxLogInfof(ctx, "ParseBatchVolume|task:%v has already been analyzed", task.Id)
			continue
		}

		//转换模型
		rule := allocation.BatchAllocationRuleConfig{}
		if err := jsoniter.Unmarshal(task.BatchAllocationRuleConfig, &rule); err != nil {
			logger.CtxLogErrorf(ctx, "ParseBatchVolume|unmarshal rule config of task:%v, err:%v", task.Id, err)
			if err := p.setConfigFailed(ctx, task); err != nil {
				logger.CtxLogErrorf(ctx, "ParseBatchVolume|set task:%d, set failed err:%v", task.Id, err)
			}
			continue
		}

		var (
			pErr              *srerr.Error
			zoneCapacityList  = make([]rulevolume.BatchAllocateForecastVolumeTab, 0)
			routeCapacityList = make([]rulevolume.BatchAllocateForecastVolumeTab, 0)
			zoneTargetList    = make([]rulevolume.BatchAllocateForecastVolumeTab, 0)
			routeTargetList   = make([]rulevolume.BatchAllocateForecastVolumeTab, 0)
		)
		//开启了volume target
		volumeDetail := rule.ZoneRouteVolumeDetail
		if rule.ZoneRouteVolumeEnabled {
			if volumeDetail.LocalVolumeRuleType == int(rulevolume.LocVolumeTypeZone) {
				zoneTargetList, pErr = p.getZoneTargetList(ctx, task, volumeDetail)
			} else if volumeDetail.LocalVolumeRuleType == int(rulevolume.LocVolumeTypeRoute) {
				routeTargetList, pErr = p.getRouteTargetList(ctx, task, volumeDetail)
			}
			if pErr != nil {
				logger.CtxLogErrorf(ctx, "ParseBatchVolume| parse zone/route capacity err:%v", pErr)
				if err := p.setConfigFailed(ctx, task); err != nil {
					logger.CtxLogErrorf(ctx, "ParseBatchVolume|set task:%d, set failed err:%v", task.Id, err)
				}
				continue
			}
			//开启了capacity，则zone/route其一必须有对应的数据，否则报错
			if len(zoneTargetList) == 0 && len(routeTargetList) == 0 {
				logger.CtxLogErrorf(ctx, "ParseBatchVolume| empty zone/route volume")
				continue
			}
		}
		var deleteIds []uint64
		volumes, gErr := p.BatchAllocateVolumeRepo.GetVolumesByForecastId(ctx, task.Id)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "ParseBatchVolume|main task id:%v, get forecast volume err:%v", task.Id, gErr)
		} else {
			deleteIds = make([]uint64, len(volumes))
			for i := 0; i < 0; i++ {
				deleteIds[i] = volumes[i].ID
			}
		}
		//todo:SSCSMR-2557:删除同task id的volume，避免重复写入导致数据混乱
		db, err := dbutil.MasterDB(ctx, model.AllocateForecastTaskConfigHook)
		if err != nil {
			logger.CtxLogErrorf(ctx, "ParseBatchVolume|get master db err:%v", err)
			continue
		}
		ctx = scormv2.BindContext(ctx, db)
		txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
			tx := scormv2.Context(ctx)
			needDelete := false
			//create zone capacity
			if len(zoneCapacityList) != 0 {
				fillBatchVolumeTab(zoneCapacityList, fillInfo{BatchAllocateForecastId: task.Id})
				//SSCSMR-1698:创建zone capacity
				tx = tx.Table(rulevolume.BatchAllocateForecastVolumeTabHook.TableName()).CreateInBatches(zoneCapacityList, insertBatchSize)
				needDelete = true
			}
			//create route capacity
			if len(routeCapacityList) != 0 {
				fillBatchVolumeTab(routeCapacityList, fillInfo{BatchAllocateForecastId: task.Id})
				//SSCSMR-1698:创建route capacity
				tx = tx.Table(rulevolume.BatchAllocateForecastVolumeTabHook.TableName()).CreateInBatches(routeCapacityList, insertBatchSize)
				needDelete = true
			}
			//create zone target
			if len(zoneTargetList) != 0 {
				fillBatchVolumeTab(zoneTargetList, fillInfo{BatchAllocateForecastId: task.Id})
				//SSCSMR-1698:创建zone volume
				tx = tx.Table(rulevolume.BatchAllocateForecastVolumeTabHook.TableName()).CreateInBatches(zoneTargetList, insertBatchSize)
				needDelete = true
			}
			//create route target
			if len(routeTargetList) != 0 {
				fillBatchVolumeTab(routeTargetList, fillInfo{BatchAllocateForecastId: task.Id})
				//SSCSMR-1698:创建zone volume
				tx = tx.Table(rulevolume.BatchAllocateForecastVolumeTabHook.TableName()).CreateInBatches(routeTargetList, insertBatchSize)
				needDelete = true
			}
			//need delete current volume
			if needDelete && len(deleteIds) != 0 {
				tx = tx.Table(rulevolume.BatchAllocateForecastVolumeTabHook.TableName()).Delete(&rulevolume.BatchAllocateForecastVolumeTab{},
					"id in (?)", deleteIds)
			}

			if tx.GetError() != nil {
				return srerr.With(srerr.DatabaseErr, nil, tx.GetError())
			}
			return nil
		})
		if txErr != nil {
			logger.CtxLogErrorf(ctx, "ParseBatchVolume| transaction err:%v", txErr)
			continue
		}

		_, err = redisutil.Incr(ctx, repo.BatchVolumeKey(uint64(task.Id)))
		if err != nil {
			logger.CtxLogErrorf(ctx, "ParseBatchVolume|incr task:%v, err:%v", task.Id, err)
		}
	}

	return nil
}

// 对create行为要装填信息，因为部分信息create后才会生成
func fillBatchVolumeTab(tabs []rulevolume.BatchAllocateForecastVolumeTab, fillInfo fillInfo) {
	for i := 0; i < len(tabs); i++ {
		tabs[i].BatchAllocateForecastId = uint64(fillInfo.BatchAllocateForecastId)
	}
}

type fillInfo struct {
	BatchAllocateForecastId int64 `json:"batch_allocate_forecast_id"`
}

func (p *ParseBatchVolume) setConfigFailed(ctx context.Context, tab *model.AllocateForecastTaskConfigTab) error {
	if err := dbutil.Update(ctx, model.AllocateForecastTaskConfigHook, map[string]interface{}{
		"id = ?": tab.Id,
	}, map[string]interface{}{
		"task_status": constant2.TaskConfigStatusFailed,
	}, dbutil.ModelInfo{}); err != nil {
		return err
	}

	return nil
}

func (p *ParseBatchVolume) getZoneTargetList(
	ctx context.Context, task *model.AllocateForecastTaskConfigTab, volumeDetail allocation.ZoneRouteVolumeDetail,
) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error) {
	if volumeDetail.ZoneDefinitionUrl == "" || volumeDetail.ZoneVolumeValueUrl == "" {
		return nil, srerr.New(srerr.ParamErr, "", "zone definition url or volume value url is empty")
	}

	zoneDefinitionUrl := fileutil.GetS3UrlForSlsOps(ctx, volumeDetail.ZoneDefinitionUrl)
	zoneVolumeUrl := fileutil.GetS3UrlForSlsOps(ctx, volumeDetail.ZoneVolumeValueUrl)
	zoneTargetList, pErr := p.BatchAllocateForecastService.ParseZoneExcel(ctx, uint64(task.Id), task.MaskingProductID, zoneDefinitionUrl, zoneVolumeUrl, constant2.ParseTarget)
	if pErr != nil {
		return nil, pErr
	}

	return zoneTargetList, nil
}

func (p *ParseBatchVolume) getRouteTargetList(
	ctx context.Context, task *model.AllocateForecastTaskConfigTab, volumeDetail allocation.ZoneRouteVolumeDetail,
) ([]rulevolume.BatchAllocateForecastVolumeTab, *srerr.Error) {
	if volumeDetail.RouteDefinitionUrl == "" || volumeDetail.RouteVolumeValueUrl == "" {
		return nil, srerr.New(srerr.ParamErr, "", "route definition url or volume value url is empty")
	}

	routeDefinitionUrl := fileutil.GetS3UrlForSlsOps(ctx, volumeDetail.RouteDefinitionUrl)
	routeVolumeUrl := fileutil.GetS3UrlForSlsOps(ctx, volumeDetail.RouteVolumeValueUrl)
	routeTargetList, pErr := p.BatchAllocateForecastService.ParseRouteExcel(ctx, uint64(task.Id), task.MaskingProductID, routeDefinitionUrl, routeVolumeUrl)
	if pErr != nil {
		return nil, pErr
	}

	return routeTargetList, nil
}
