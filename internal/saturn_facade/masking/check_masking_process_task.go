package masking

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type CheckMaskingProcessTaskImpl struct {
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService
	maskMainTaskRepo          repo.AllocateForecastTaskConfigRepo
}

func NewCheckMaskingProcessTaskImpl(
	ForecastingSubTaskService forecasting_sub_task.ForecastingSubTaskService,
	maskMainTaskRepo repo.AllocateForecastTaskConfigRepo) *CheckMaskingProcessTaskImpl {
	return &CheckMaskingProcessTaskImpl{
		ForecastingSubTaskService: ForecastingSubTaskService,
		maskMainTaskRepo:          maskMainTaskRepo,
	}
}

func (d *CheckMaskingProcessTaskImpl) Name() string {
	return internal_constant.TaskCheckMaskingProcessTask
}

func (d *CheckMaskingProcessTaskImpl) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	//1.获取所有process sub task
	tasks, err := d.ForecastingSubTaskService.SelectSubTask(ctx, map[string]interface{}{
		"task_status = ?": constant.TaskConfigStatusProcess,
		"module_name = ?": forecasting_sub_task.ModuleMaskingForecast,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "CheckMaskingProcessTaskImpl|failed to get sub tasks ,err:%v", err)
		return err
	}
	//2.判断是否需要断点续作，并收集对应的主键id
	var needChangedIds []uint64
	conf := configutil.GetMaskingForecastConf(ctx)
	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, task := range tasks {
		limit := nowTime - conf.CheckTimeDuration
		//task last update time小于limit值，意味着任务处于"断点"状态的时间过长，需要重置其状态
		if limit > task.LastUpdateTime {
			needChangedIds = append(needChangedIds, task.Id)
		}
	}
	//3.根据主键id更新
	value := map[string]interface{}{
		"task_status": constant.TaskConfigStatusPending,
	}
	if err := d.ForecastingSubTaskService.BatchUpdateSubTaskById(ctx, needChangedIds, value); err != nil {
		logger.CtxLogErrorf(ctx, "CheckMaskingProcessTaskImpl|ids=%v, failed to batch update sub task to 'complete'", needChangedIds)
		return err
	}

	//定时任务检查main task是否完成
	if cErr := d.checkComplete(ctx); cErr != nil {
		logger.CtxLogErrorf(ctx, "CheckMaskingProcessTaskImpl|check complete err:%v", cErr)
		return cErr
	}

	return nil
}

// 定时任务检查sub task是否全部完成，若是则需要更新main task
func (d *CheckMaskingProcessTaskImpl) checkComplete(ctx context.Context) *srerr.Error {
	//获取所有process态的main task
	mainTaskList, gErr := d.maskMainTaskRepo.GetForecastTaskConfigByStatus(ctx, constant.TaskConfigStatusProcess)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "checkComplete|get process masking forecast task err:%v", gErr)
		return gErr
	}
	//遍历main task，并判断其所有sub task是否完成
	for _, mainTask := range mainTaskList {
		//兼容batch
		if mainTask.AllocationMethod == constant.AllocateMethodBatch {
			continue
		}
		mainTaskId := mainTask.Id

		//检索非complete状态的sub task
		subTasks, sErr := d.ForecastingSubTaskService.SelectSubTask(ctx, map[string]interface{}{
			"main_task_id=?":   mainTaskId,
			"module_name=?":    forecasting_sub_task.ModuleMaskingForecast,
			"task_status <> ?": constant.TaskConfigStatusComplete,
		})
		if sErr != nil {
			logger.CtxLogErrorf(ctx, "checkComplete|select sub task not completed, err:%v", sErr)
			continue
		}
		//如果检索不到，说明都complete，需要更新main task到complete
		if len(subTasks) == 0 {
			condition := map[string]interface{}{
				"id = ?": mainTaskId,
			}
			value := map[string]interface{}{
				"task_status":   constant.TaskConfigStatusComplete,
				"complete_time": timeutil.GetCurrentUnixTimeStamp(ctx),
			}
			if uErr := d.maskMainTaskRepo.UpdateTaskWithCondition(ctx, condition, value, uint64(mainTask.MaskingProductID)); uErr != nil {
				logger.CtxLogErrorf(ctx, "checkComplete|update main task to status:%v, err:%v", constant.TaskConfigStatusComplete, uErr)
				continue
			}
		}
	}

	return nil
}
