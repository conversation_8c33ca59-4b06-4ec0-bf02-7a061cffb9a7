package masking

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	jsoniter "github.com/json-iterator/go"
)

const (
	failed            = -1
	success           = 0
	maskingMessageKey = "message"
)

type AllocateStoreHbaseConsumer struct {
	AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo
}

func NewAllocateStoreHbaseConsumer(AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo) *AllocateStoreHbaseConsumer {
	return &AllocateStoreHbaseConsumer{
		AllocateOrderDataRepo: AllocateOrderDataRepo,
	}
}

func (a *AllocateStoreHbaseConsumer) Name() string {
	return constant.TaskNameAllocateStoreHbaseConsumer
}

func (a *AllocateStoreHbaseConsumer) MsgHandle(ctx context.Context, message *saturn.SaturnMessage) *saturn.SaturnReply {
	var reply saturn.SaturnReply
	//1.重新生成request id
	tempRequestId := message.MsgKey
	ctx = logger.NewLogContext(ctx, tempRequestId)
	ctx = requestid.SetToCtx(ctx, tempRequestId)
	//1.处理空数据
	if len(message.MsgText) == 0 {
		logger.CtxLogInfof(ctx, "AllocateStoreHbaseConsumer|empty message, just return")
		reply.Retcode = success
		reply.Message = "empty message"
		return &reply
	}
	logger.CtxLogInfof(ctx, "AllocateStoreHbaseConsumer|message key:%v", tempRequestId)

	//2. 反序列化数据，获取mask product id，request time，和order id
	//2.1 解压
	tempText := message.MsgText
	marshalBytes, err := zip.ZSTDDecompress(tempText)
	if err != nil {
		logger.CtxLogErrorf(ctx, "AllocateStoreHbaseConsumer|failed to decompress, err:%v", err)
		reply.Retcode = failed
		reply.Message = err.Error()
		return &reply
	}
	//2.2 反序列化
	var maskingLogDetail allocation.LogDetail //这里不用额外兼容batch，是因为反序列化只是为了获取公共部分字段
	if err := jsoniter.Unmarshal(marshalBytes, &maskingLogDetail); err != nil {
		logger.CtxLogErrorf(ctx, "AllocateStoreHbaseConsumer|Unmarshal msg body fail|err=%v", err)
		reply.Retcode = -1
		reply.Message = err.Error()
		return &reply
	}
	logger.CtxLogInfof(ctx, "AllocateStoreHbaseConsumer|unmarshal bytes:%v", string(marshalBytes))
	logger.CtxLogInfof(ctx, "AllocateStoreHbaseConsumer|maskingLogDetail:%+v", maskingLogDetail)
	//2.3 获取mask product id，request time，order id和request id
	maskProductId := maskingLogDetail.MaskProductId
	requestTime := maskingLogDetail.RequestTime
	requestId := message.MsgKey
	if requestId == "" {
		requestId = maskingLogDetail.RequestId
	}

	//3.生成row key并写入hbase
	rowKey := GenRowKey(requestTime, maskProductId, requestId)
	value := prepare(maskingMessageKey, message.MsgText)
	logger.CtxLogInfof(ctx, "AllocateStoreHbaseConsumer|row key:%v", rowKey)
	if pErr := a.AllocateOrderDataRepo.PutAllocateOrderIntoHbase(ctx, rowKey, value); pErr != nil {
		logger.CtxLogErrorf(ctx, "AllocateStoreHbaseConsumer|put data into hbase err:%v", pErr)
		reply.Retcode = failed
		reply.Message = pErr.Error()
	}

	return &reply
}

func GenRowKey(requestTime int64, maskProductId int, requestId string) string {
	//1.request time整除1000再对1000取余,并填充
	salt := fmt.Sprintf("%03d", (requestTime/1000)%1000)
	//2.request time整除1000并填充
	keyTime := fmt.Sprintf("%10d", requestTime/1000)
	//3.拼装row key
	rowKey := fmt.Sprintf("%s_%d_%s_%s", salt, maskProductId, keyTime, requestId)

	return rowKey
}

func prepare(key string, value []byte) map[string]map[string][]byte {
	tempResult := map[string][]byte{
		key: value,
	}
	return map[string]map[string][]byte{
		"c": tempResult,
	}
}
