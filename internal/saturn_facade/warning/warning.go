package warning

import (
	"go.uber.org/atomic"
	"sync"
)

//todo:SSCSMR-1698: 看看替换成go自带的atomic(性能更好)，然后自己实现CAS
type ShouldCloseObj struct {
	ShouldClose *atomic.Bool
	Once        sync.Once
}

func (s *ShouldCloseObj) Set(flag bool) {
	s.Once.Do(func() {
		if s.ShouldClose == nil {
			s.ShouldClose = atomic.NewBool(flag)
		}
	})
	s.ShouldClose.CAS(s.ShouldClose.Load(), flag)
}

func (s *ShouldCloseObj) Get() bool {
	return s.ShouldClose.Load()
}
