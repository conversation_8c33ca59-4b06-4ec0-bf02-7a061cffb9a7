package batch_entity

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"

type BatchAllocateReq struct {
	BatchUnitId uint64
	BAReq       *algorithm_client.BatchAllocateReqBo
	OrderInfo   map[uint64]*OrderLotionInfo
}

type OrderLotionInfo struct {
	OrderId            uint64
	PickUpLocationIds  []int
	DeliverLocationIds []int
}

type BatchAllocateResp struct {
	BatchUnitId uint64
	BAReq       *algorithm_client.BatchAllocateReqBo
	BAResp      *algorithm_client.BatchAllocateRespBo
	OrderInfo   map[uint64]*OrderLotionInfo
	ExecuteTime int64
}

//todo:SSCSMR-1698: delete
type ExtraInfo struct {
	AlgoExecuteTime int64
}
