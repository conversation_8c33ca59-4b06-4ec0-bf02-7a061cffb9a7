package services

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/export_masking_panel_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/mask_product_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/volumeroutingtask"
	"sync"
)

var (
	// 包括所有依赖
	gService *SaturnService
	gOnce    = &sync.Once{}
)

// 获取service，获取其中的依赖实现
func GetService() *SaturnService {
	return gService
}

// 仅初始化时使用
func SetService(service *SaturnService) {
	gOnce.Do(func() {
		gService = service
	})
}

type SaturnService struct {
	// TASK
	ZoneImportTask                      *volumeroutingtask.ZoneImportTask
	ZoneExportTask                      *volumeroutingtask.ZoneExportTask
	RuleLimitImportTask                 *volumeroutingtask.RuleLimitImportTask
	RuleEffectiveTask                   *volumeroutingtask.RuleEffectiveTask
	MaskingDataExportTask               *export_masking_panel_data.MaskingDataExportTask
	AllocateCheckBatchVolumeCounter     *allocate_volume_counter.AllocateCheckBatchVolumeCounter
	ReportOrderCount                    *routing.ReportOrderCount
	SmartRoutingForecastTask            *routing.SmartRoutingForecastTask
	ILHForecastTask                     *routing.ILHForecastTask
	LfsHardCriteriaCreatedBySystem      *routing.LfsHardCriteriaCreatedBySystem
	LfsHardCriteriaV2                   *routing.LfsHardCriteriaV2
	LfsHardCriteriaCheck                *routing.LfsHardCriteriaCheck
	ScheduleRule                        *routing.ScheduleRule
	UpdateZoneVolume                    *masking.UpdateZoneVolume
	UpdateRouteVolume                   *masking.UpdateRouteVolume
	AllocateStoreConsumer               *masking.AllocateStoreConsumer
	LoadForecastVolumeConfig            *masking.LoadForecastVolumeConfig
	MaskingForecast                     *masking.MaskingForecast
	MaskProductPriorityCronJob          *mask_product_priority.MaskProductPriorityJob
	AuditLogTaskServer                  *audit_log_task.AuditLogTaskServer
	SyncScheduleCountTask               *schedule_visual.SyncScheduleCountTask
	ClearScheduleCountTask              *schedule_visual.ClearScheduleCountTask
	DeleteMaskingSubTaskImpl            *masking.DeleteMaskingSubTaskImpl
	CheckMaskingProcessTaskImpl         *masking.CheckMaskingProcessTaskImpl
	GetForecastTotalCountImpl           *masking.GetForecastTotalCountImpl
	UpdateRouteAndZoneVolume            *masking.UpdateRouteAndZoneVolume
	AllocateStoreHbaseConsumer          *masking.AllocateStoreHbaseConsumer
	ScheduleCountStatTask               *masking.ScheduleCountStatTask
	LocalSpxForecasting                 *routing.LocalSpxForecasting
	ReportMaskingVolumeTask             *masking.ReportMaskingVolumeTask
	MergeMaskingVolumeTask              *masking.MergeMaskingVolumeTask
	ClearMaskingVolumeTask              *masking.ClearMaskingVolumeTask
	ReportRoutingVolumeTask             *routing.ReportRoutingVolumeTask
	MergeRoutingVolumeTask              *routing.MergeRoutingVolumeTask
	ClearRoutingVolumeTask              *routing.ClearRoutingVolumeTask
	StartBatchForecastUnitImpl          *masking.StartBatchForecastUnitImpl
	CreateBASubTask                     *batch_allocate_forecast.CreateBASubTask
	UpdateBatchAllocateForecastTaskImpl *masking.UpdateBatchAllocateForecastTaskImpl
	AllocateHistoryOutLine              *masking.AllocateHistoryOutLine
	AnalyzeBatchVolume                  *masking.ParseBatchVolume
	AllocateScheduleVisualTask          *masking.AllocateScheduleVisualTask
	BAForecastToolProgressImpl          *batch_allocate_forecast.BAForecastToolProgressImpl
	ReportMaskingOrderCount             *masking.ReportMaskingOrderCount
	CheckoutFulfillmentProductCounter   *allocate_volume_counter.CheckoutFulfillmentProductCounter
	DeductVolumeCounter                 *allocate_volume_counter.DeductVolumeCounter

	// BA TASK
	SplitBatchAllocateOrdersTask   *masking.SplitBatchAllocateOrdersTask
	BatchAllocateTask              *batch_allocate.BatchAllocateTask
	AbnormalBatchAllocateTask      *batch_allocate.AbnormalBatchAllocateTask
	BatchAbnormalInspectionTask    *batch_allocate.BatchAbnormalInspectionTask
	PushOrderResultTask            *batch_allocate.PushOrderResultTask
	UpdateOrderResultTask          *batch_allocate.UpdateOrderResultTask
	BatchAllocateHoldOrderConsumer *batch_allocate.BatchAllocateHoldOrderConsumer
	ClearOrderAndResult            *batch_allocate.ClearOrderAndResultTask
	UpdateAllocationPathTask       *batch_allocate.UpdateAllocationPathTask
	MakeUpAsyncAllocationLog       *batch_allocate.MakeUpAsyncAllocationLog
	BatchAllocateMonitor           *batch_allocate.BatchAllocateMonitor
}

func (s *SaturnService) RegisterSaturn() {
	chassis.RegisterSchema("saturn", s.ZoneImportTask)
	chassis.RegisterSchema("saturn", s.ZoneExportTask)
	chassis.RegisterSchema("saturn", s.RuleLimitImportTask)
	chassis.RegisterSchema("saturn", s.RuleEffectiveTask)
	chassis.RegisterSchema("saturn", s.MaskingDataExportTask)
	chassis.RegisterSchema("saturn", s.AllocateCheckBatchVolumeCounter)
	chassis.RegisterSchema("saturn", s.ReportOrderCount)
	chassis.RegisterSchema("saturn", s.SmartRoutingForecastTask)
	chassis.RegisterSchema("saturn", s.LfsHardCriteriaCreatedBySystem)
	chassis.RegisterSchema("saturn", s.LfsHardCriteriaV2)
	chassis.RegisterSchema("saturn", s.LfsHardCriteriaCheck)
	chassis.RegisterSchema("saturn", s.ScheduleRule)
	chassis.RegisterSchema("saturn", s.UpdateZoneVolume)
	chassis.RegisterSchema("saturn", s.UpdateRouteVolume)
	chassis.RegisterSchema("saturn", s.AllocateStoreConsumer)
	chassis.RegisterSchema("saturn", s.LoadForecastVolumeConfig)
	chassis.RegisterSchema("saturn", s.MaskingForecast)
	chassis.RegisterSchema("saturn", s.MaskProductPriorityCronJob)
	chassis.RegisterSchema("saturn", s.AuditLogTaskServer)
	chassis.RegisterSchema("saturn", s.LocalSpxForecasting)
	chassis.RegisterSchema("saturn", s.SyncScheduleCountTask)
	chassis.RegisterSchema("saturn", s.ClearScheduleCountTask)
	chassis.RegisterSchema("saturn", s.DeleteMaskingSubTaskImpl)
	chassis.RegisterSchema("saturn", s.CheckMaskingProcessTaskImpl)
	chassis.RegisterSchema("saturn", s.GetForecastTotalCountImpl)
	chassis.RegisterSchema("saturn", s.AllocateStoreHbaseConsumer)
	chassis.RegisterSchema("saturn", s.ScheduleCountStatTask)
	chassis.RegisterSchema("saturn", s.UpdateRouteAndZoneVolume)
	chassis.RegisterSchema("saturn", s.ReportMaskingVolumeTask)
	chassis.RegisterSchema("saturn", s.MergeMaskingVolumeTask)
	chassis.RegisterSchema("saturn", s.ClearMaskingVolumeTask)
	chassis.RegisterSchema("saturn", s.ReportRoutingVolumeTask)
	chassis.RegisterSchema("saturn", s.MergeRoutingVolumeTask)
	chassis.RegisterSchema("saturn", s.ClearRoutingVolumeTask)
	chassis.RegisterSchema("saturn", s.StartBatchForecastUnitImpl)
	chassis.RegisterSchema("saturn", s.CreateBASubTask)
	chassis.RegisterSchema("saturn", s.UpdateBatchAllocateForecastTaskImpl)
	chassis.RegisterSchema("saturn", s.AllocateHistoryOutLine)
	chassis.RegisterSchema("saturn", s.AnalyzeBatchVolume)
	chassis.RegisterSchema("saturn", s.AllocateScheduleVisualTask)
	chassis.RegisterSchema("saturn", s.BAForecastToolProgressImpl)
	chassis.RegisterSchema("saturn", s.MakeUpAsyncAllocationLog)
	chassis.RegisterSchema("saturn", s.UpdateAllocationPathTask)
	chassis.RegisterSchema("saturn", s.BatchAllocateMonitor)
	chassis.RegisterSchema("saturn", s.ReportMaskingOrderCount)
	chassis.RegisterSchema("saturn", s.CheckoutFulfillmentProductCounter)
	chassis.RegisterSchema("saturn", s.DeductVolumeCounter)
	chassis.RegisterSchema("saturn", s.ILHForecastTask)
}

func (s *SaturnService) RegisterBATaskSaturn() {
	chassis.RegisterSchema("saturn", s.SplitBatchAllocateOrdersTask)
	chassis.RegisterSchema("saturn", s.BatchAllocateTask)
	chassis.RegisterSchema("saturn", s.AbnormalBatchAllocateTask)
	chassis.RegisterSchema("saturn", s.BatchAbnormalInspectionTask)
	chassis.RegisterSchema("saturn", s.PushOrderResultTask)
	chassis.RegisterSchema("saturn", s.UpdateOrderResultTask)
	chassis.RegisterSchema("saturn", s.BatchAllocateHoldOrderConsumer)
	chassis.RegisterSchema("saturn", s.ClearOrderAndResult)
}
