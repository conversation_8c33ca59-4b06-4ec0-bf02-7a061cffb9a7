package audit_log_task

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	internal_constant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"strings"
)

type AuditLogTaskServer struct {
	AuditLogService audit_log.AuditLogService
}

func NewAuditLogTaskServer(AuditLogService audit_log.AuditLogService) *AuditLogTaskServer {
	return &AuditLogTaskServer{AuditLogService: AuditLogService}
}

func (s *AuditLogTaskServer) Name() string {
	return internal_constant.TaskAuditLogReport
}

func (s *AuditLogTaskServer) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	requestID := uuid.NewV4().String() // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)

	logger.CtxLogInfof(ctx, "AuditLogTaskServer|start|ShardingParam=%s", args.ShardingParam)

	conf := configutil.GetAuditLogConfig(ctx)

	//1.检索距离当前时间1分钟之内的数据
	tabs, err := s.AuditLogService.SelectAuditLogRecords(ctx, map[string]interface{}{
		"ctime >= ?": timeutil.GetCurrentUnixTimeStamp(ctx) - conf.CheckTime,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "AuditLogTaskServer|select records by condition err:%v", err)
		return err
	}
	//2.将检索到的数据根据web hook发送告警信息
	region := envvar.GetCID()
	for _, tab := range tabs {
		//2. 匹配conf condition，如果匹配上了，就发往对应的sea talk群
		matchScenario(ctx, tab, conf, region)

		//3. 发一份到默认群
		req, allowToReport := convertTabToWebhookReq(ctx, tab, region)
		if !allowToReport {
			continue
		}
		for _, webHook := range conf.WebHookList {
			body, _ := jsoniter.Marshal(req)
			_, err := httputil.PostJson(ctx, webHook, body, 6, nil)
			if err != nil {
				logger.CtxLogErrorf(ctx, "AuditLogTaskServer|send request:%v, to url:%v, err:%v", req, webHook, err)
			}
		}
	}

	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleTask, "AuditLog", monitoring.StatusSuccess, "success")

	return nil
}

type WebHookReq struct {
	Tag  string      `json:"tag"` //值固定为 "text"
	Text SeaTalkText `json:"text"`
}

type SeaTalkText struct {
	Content            string   `json:"content"`                        //具体内容
	MentionedList      []string `json:"mentioned_list,omitempty"`       //The list of SeaTalk IDs of the group chat members to @ in the message - The users with the SeaTalk IDs must be members of this group chat for the mention to be successful
	MentionedEmailList []string `json:"mentioned_email_list,omitempty"` //- The list of emails of the group chat members to @ in the message  - The users with the emails must be members of this group chat for the mention to be successful
	AtAll              bool     `json:"at_all"`                         //- Whether to @All in the message - Notifying all group chat members will succeed only if the "Notify all members with @All" setting is turned on in the group chat and the "at_all" is set to true
}

type ContextObj struct {
	Region               string `json:"region"`
	ModuleType           string `json:"module_name"`
	ModuleId             uint64 `json:"module_id,omitempty"`
	Operator             string `json:"operator"`
	MaskProductId        uint64 `json:"mask_product_id,omitempty"`
	FulfillmentProductId uint64 `json:"fulfillment_product_id,omitempty"`
	RuleId               uint64 `json:"rule_id,omitempty"`
	RuleVolumeId         uint64 `json:"rule_volume_id,omitempty"`
	TaskId               uint64 `json:"task_id,omitempty"`
	LocalCreateTime      string `json:"local_create_time"`
	SgCreateTime         string `json:"sg_create_time"`
	RequestUrl           string `json:"request_url"`
	AffectedScenario     string `json:"affected_scenario"` //used to alert important change like affecting order assignment
}

func convertTabToWebhookReq(ctx context.Context, tab audit_log.AudiLogTab, cid string) (WebHookReq, bool) {
	allowToReport := false
	conf := configutil.GetAuditLogConfig(ctx)
	//在黑名单中的table，禁止report，避免太多无用的告警信息
	for _, blackTable := range conf.NotAllowToReportList {
		if blackTable == tab.ModelName {
			return WebHookReq{}, allowToReport
		}
	}
	contentObj := ContextObj{
		Region:               cid,
		ModuleType:           tab.ModuleType,
		ModuleId:             tab.ModelId,
		Operator:             tab.Operator,
		MaskProductId:        tab.MaskProductId,
		FulfillmentProductId: tab.FulfillmentProductId,
		RuleId:               tab.RuleId,
		RuleVolumeId:         tab.RuleVolumeId,
		TaskId:               tab.TaskId,
		RequestUrl:           tab.Interface,
		AffectedScenario:     Translate(ctx, tab),
	}
	t := timeutil.ConvertTimeStampToTime(tab.Ctime)
	localCreateTime := timeutil.FormatDateTime(t)
	contentObj.LocalCreateTime = localCreateTime

	sgTime := timeutil.ConvertTimeStampToTimeByCountry(tab.Ctime, "SG")
	sgCreateTime := timeutil.FormatDateTime(sgTime)
	contentObj.SgCreateTime = sgCreateTime

	content, _ := jsoniter.MarshalToString(contentObj)
	req := WebHookReq{
		Tag: "text",
		Text: SeaTalkText{
			Content:            content,
			MentionedList:      conf.MentionedList,
			MentionedEmailList: conf.MentionedEmailList,
			AtAll:              conf.AtAll,
		},
	}
	allowToReport = true
	return req, allowToReport
}

func matchScenario(ctx context.Context, tab audit_log.AudiLogTab, conf configutil.AuditLogConfig, region string) {
	for hookUrl, scenarioList := range conf.SceneConfigMap {
		for _, scenario := range scenarioList {
			if !checkCanReport(scenario, tab) {
				continue
			}

			req, allowToReport := convertTabToWebhookReq(ctx, tab, region)
			if !allowToReport {
				logger.CtxLogInfof(ctx, "not allowed to report")
				continue
			}
			body, _ := jsoniter.Marshal(req)
			_, err := httputil.PostJson(ctx, hookUrl, body, 6, nil)
			if err != nil {
				logger.CtxLogErrorf(ctx, "AuditLogTaskServer|send request:%v, to url:%v, err:%v", req, hookUrl, err)
			}
		}
	}
}

func checkCanReport(scenario configutil.SceneConfig, tab audit_log.AudiLogTab) bool {
	if tab.ModelName != "" {
		if scenario.ModelName != "" && scenario.ModelName != tab.ModelName {
			return false
		}
	}
	if tab.Operator != "" {
		if scenario.Operator != "" && scenario.Operator != tab.Operator {
			return false
		}
	}
	if tab.MaskProductId != 0 {
		if scenario.MaskProductId != 0 && scenario.MaskProductId != tab.MaskProductId {
			return false
		}
	}
	if tab.FulfillmentProductId != 0 {
		if scenario.FulfillmentProductId != 0 && scenario.FulfillmentProductId != tab.FulfillmentProductId {
			return false
		}
	}
	if tab.RuleId != 0 {
		if scenario.RuleId != 0 && scenario.RuleId != tab.RuleId {
			return false
		}
	}
	if tab.RuleVolumeId != 0 {
		if scenario.RuleVolumeId != 0 && scenario.RuleVolumeId != tab.RuleVolumeId {
			return false
		}
	}
	if tab.TaskId != 0 {
		if scenario.TaskId != 0 && scenario.TaskId != tab.TaskId {
			return false
		}
	}
	if tab.ModelId != 0 {
		if scenario.ModelId != 0 && scenario.ModelId != tab.ModelId {
			return false
		}
	}
	if tab.ModuleType != "" {
		if scenario.ModuleType != "" && scenario.ModuleType != tab.ModuleType {
			return false
		}
	}
	if tab.Interface != "" {
		if scenario.Interface != "" && scenario.Interface != tab.Interface {
			return false
		}
	}
	if tab.ExtendInfo != "" {
		if scenario.ExtendInfo != "" && !strings.Contains(tab.ExtendInfo, scenario.ExtendInfo) {
			return false
		}
	}

	return true
}
