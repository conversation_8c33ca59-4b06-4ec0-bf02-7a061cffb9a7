package audit_log_task

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
)

// todo: translate report message

func Translate(ctx context.Context, tab audit_log.AudiLogTab) string {
	// 硬性场景
	if tab.ModelName == "logistic_product_priority_tab" {
		return "product禁用可能导致checkout，下单等硬性校验不通过；或软性路由优先级被修改，影响分单结果；请检查product priority配置中product是否被关闭，以及product的优先级"
	}

	// 软性场景
	// local, spx, cb, multi cb routing
	if tab.ModelName == "product_routing_role_tab" {
		return "场景：checkout，check order， create order； 可能导致掉单，卡单，影响分单比例。请检查routing role映射是否符合预期"
	}
	if tab.ModelName == "routing_config_tab" {
		return "场景：checkout，check order， create order； 可能导致掉单，卡单，影响分单比例。请检查开启smart routing是否符合预期"
	}
	if tab.ModelName == "routing_rule_tab" {
		return "场景：checkout，check order， create order； 可能导致掉单，卡单，影响分单比例。请检查3pl toggle开关，以及软性调度因子"
	}
	if tab.ModelName == "cc_routing_rule_tab" {
		return "场景：checkout，check order， create order； 可能导致cc routing调度结果失败"
	}
	if tab.ModelName == "volume_routing_rule_tab" || tab.ModelName == "volume_zone_location_tab" || tab.ModelName == "volume_zone_group_tab" {
		return "场景：发货；影响smr调度分单结果"
	}

	// allocate
	if tab.ModelName == "mask_product_rule_volume_tab" {
		return "场景：allocate； 运力规则变更，影响分单结果，请检查country维度运力，zone/route维度运力约束是变大或变小"
	}
	if tab.ModelName == "allocation_rule_tab" {
		return "场景：allocate； 软性规则变更，影响分单结果，请检查调度因子的变更是否符合预期"
	}
	if tab.ModelName == "allocation_config_tab" {
		return "场景：allocate； allocation默认设置变更，影响分单结果，请检查开启allocation，默认product，以及默认wms product是否符合预期"
	}

	return ""
}
