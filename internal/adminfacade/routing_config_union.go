package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	soft_routing2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

func (s *SoftRoutingFacade) GetUnionCfg(ctx *restful.Context) {
	//
	req := soft_routing2.GetUnioncfg{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//called,rolemap cfg_tab ,and can_enabled_scenes | 查active-rules , req & rsp,
	cfg, err1 := s.RoutingConfigServer.GetRoutingConfigByProductID(ctx.Ctx, req.ProductId)
	if err1 != nil {
		logger.CtxLogErrorf(ctx.Ctx, "%v", err1)
	}
	roleMap, err2 := routing.GetRoleInfoByProdcutId(ctx.Ctx, req.ProductId)
	//converted ?,
	if err2 != nil {
		logger.CtxLogErrorf(ctx.Ctx, "%v", err2)
	}

	activeRules := s.RoutingConfigServer.GetSmrScenes(ctx.Ctx, req.ProductId)
	//fixs,
	rsp := soft_routing2.GetUnionCfgRsp{
		ProductRoutingRole:        convert2rolemap(roleMap),
		ProductRoutingConfig:      convert2cfg(cfg),
		SmrScenesWithEnabledRules: activeRules,
	}
	apiutil.SuccessJSONResp(ctx, rsp)
}

func convert2rolemap(data *ruledata.ProductRoutingRoleTab) *soft_routing2.ProductRoutingRole {
	if data == nil {
		return nil
	}
	routingRole := new(soft_routing2.ProductRoutingRole)
	routingRole.CBRoutingRole = soft_routing2.JsonRoleInfo(data.CBRoutingRole)
	routingRole.CBMultiRoutingRole = soft_routing2.JsonRoleInfo(data.CBMultiRoutingRole)
	routingRole.SpxRoutingRole = soft_routing2.JsonRoleInfo(data.SpxRoutingRole)
	routingRole.LocalRoutingRole = soft_routing2.JsonRoleInfo(data.LocalRoutingRole)
	routingRole.IlhRoutingRole = soft_routing2.JsonRoleInfo(data.IlhRoutingRole)
	return routingRole
}

func convert2cfg(tab *rule.RoutingConfig) *soft_routing2.ProductRoutingConfig {
	if tab == nil {
		return nil
	}
	routingConfigTmp := new(soft_routing2.ProductRoutingConfig)
	routingConfigTmp.CBRoutingEnabled = tab.SmartRoutingEnabled
	routingConfigTmp.CBMultiRoutingEnabled = tab.CBMultiSmartRoutingEnabled
	routingConfigTmp.LocalSmartRoutingEnabled = tab.LocalSmartRoutingEnabled
	routingConfigTmp.SpxSmartRoutingEnabled = tab.SpxSmartRoutingEnabled
	routingConfigTmp.IlhSmartRoutingEnabled = tab.IlhSmartRoutingEnabled
	routingConfigTmp.DefaultLaneCode = tab.DefaultLaneCode
	routingConfigTmp.SmartRoutingToggle = tab.SmartRoutingToggle
	return routingConfigTmp
}
