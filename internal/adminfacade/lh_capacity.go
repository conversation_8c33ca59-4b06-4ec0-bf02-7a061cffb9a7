package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

// LHCapacityFacade 是LH容量配置的Facade层
type LHCapacityFacade struct {
	LHCapacityService lh_capacity.LHCapacityService
}

func (f *LHCapacityFacade) URLPatterns() []restful.Route {
	lhCapacity := restful.NewRouterGroup("/api/admin/smart_routing/lh_capacity")
	lhCapacity.POST("/create", f.Create)
	lhCapacity.POST("/update", f.Update)
	lhCapacity.POST("/update_status", f.UpdateStatus)
	lhCapacity.GET("/view", f.View)
	lhCapacity.GET("/list", f.List)
	lhCapacity.POST("/delete", f.Delete)
	lhCapacity.POST("/upload", f.Upload)
	lhCapacity.POST("/copy", f.Copy)
	lhCapacity.POST("/check", f.Check)

	return lhCapacity.GetRouters()
}

// Create 创建LH容量配置
func (f *LHCapacityFacade) Create(ctx *restful.Context) {
	var req ilh_smart_routing.CreateLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层创建配置
	id, err := f.LHCapacityService.Create(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ilh_smart_routing.CreateLHCapacityResp{ID: id})
}

// Update 更新LH容量配置
func (f *LHCapacityFacade) Update(ctx *restful.Context) {
	var req ilh_smart_routing.UpdateLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层更新配置
	if err := f.LHCapacityService.Update(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// View 获取LH容量配置详情
func (f *LHCapacityFacade) View(ctx *restful.Context) {
	var req ilh_smart_routing.GetLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层获取配置
	resp, err := f.LHCapacityService.Get(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// List 获取LH容量配置列表
func (f *LHCapacityFacade) List(ctx *restful.Context) {
	var req ilh_smart_routing.ListLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 查询列表数据
	req.Pageno, _, req.Limit = apiutil.GetOffsetAndLimit(req.Pageno, req.Limit)
	resp, err := f.LHCapacityService.List(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// Delete 删除LH容量配置
func (f *LHCapacityFacade) Delete(ctx *restful.Context) {
	var req ilh_smart_routing.GetLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层删除配置
	if err := f.LHCapacityService.Delete(ctx.Ctx, req.ID); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// UpdateStatus 更新LH容量配置状态
func (f *LHCapacityFacade) UpdateStatus(ctx *restful.Context) {
	var req ilh_smart_routing.UpdateLHCapacityStatusReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层更新配置状态
	if err := f.LHCapacityService.UpdateStatus(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// Upload 通过S3文件链接批量导入LH容量配置
func (f *LHCapacityFacade) Upload(ctx *restful.Context) {
	var req ilh_smart_routing.UploadLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层处理上传
	if err := f.LHCapacityService.Upload(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// Copy 复制LH容量配置
func (f *LHCapacityFacade) Copy(ctx *restful.Context) {
	var req ilh_smart_routing.CopyLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层复制配置
	id, err := f.LHCapacityService.Copy(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ilh_smart_routing.CopyLHCapacityResp{ID: id})
}

// Check 检查是否存在特定组合且状态为Active的配置
func (f *LHCapacityFacade) Check(ctx *restful.Context) {
	var req ilh_smart_routing.CheckLHCapacityReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层检查配置
	resp, err := f.LHCapacityService.CheckActive(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
