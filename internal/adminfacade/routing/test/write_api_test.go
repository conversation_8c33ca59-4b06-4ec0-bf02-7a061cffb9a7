package test

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	jsoniter "github.com/json-iterator/go"
	"testing"
)

var (
	createTask      = EndPointInfo{"/api/admin/smart_routing/create_task", Post}
	updateTask      = EndPointInfo{"/api/admin/smart_routing/update_task", Post}
	taskStart       = EndPointInfo{"/api/admin/smart_routing/task_start", Post}
	taskStop        = EndPointInfo{"/api/admin/smart_routing/task_stop", Post}
	taskCopy        = EndPointInfo{"/api/admin/smart_routing/task_copy", Post}
	taskRuleDeploy  = EndPointInfo{"/api/admin/smart_routing/task_rule_deploy", Post}
	shipmentExport  = EndPointInfo{"/api/admin/smart_routing/shipment/export", Get}
	ResultExport    = EndPointInfo{"/api/admin/smart_routing/forecasting_result/export", Get}
	ConfirmAsHCTask = EndPointInfo{"/api/admin/smart_routing/confirm_as_draft_hard_criteria_task", Post}
	editHCTask      = EndPointInfo{"/api/admin/smart_routing/edit_hard_criteria_task", Post}
	submitHCTask    = EndPointInfo{"/api/admin/smart_routing/submit_hard_criteria_task", Post}
	deleteHCTask    = EndPointInfo{"/api/admin/smart_routing/delete_hard_criteria_task", Post}
	updateHCTask    = EndPointInfo{"/api/admin/smart_routing/update_hard_criteria_task", Post}
	createHCTask    = EndPointInfo{"/api/admin/smart_routing/create_hard_criteria_task", Post}

	writeEndPoints = []EndPointInfo{
		//createTask,
		//updateTask,
		//taskStart,
		//taskStop,
		//taskCopy,
		//taskRuleDeploy,
		//shipmentExport,
		//ResultExport,
		ConfirmAsHCTask,
		//editHCTask,
		//submitHCTask,
		//updateHCTask,
		//deleteHCTask,
		//createHCTask,
	}
)

type BaseResponse struct {
	RetCode int    `json:"retcode"`
	Message string `json:"message"`
}

type SubmitHCTaskResponse struct {
	BaseResponse
	Data forecast.DraftTaskResponse `json:"data"`
}

var submitHcTaskResp SubmitHCTaskResponse

func ContrastWriteApi(t *testing.T, urls []string, conditions map[string]string, body string, endPoint EndPointInfo) {
	lpsResp := Query(t, urls, conditions, body, endPoint)
	switch endPoint {
	case submitHCTask, editHCTask, ConfirmAsHCTask:
		if err := jsoniter.Unmarshal(lpsResp, &submitHcTaskResp); err != nil {
			t.Errorf("lps submit hc task resp unmarshal error:%s", err.Error())
		}
	}
	newBody := fmt.Sprintf(`{"task_id":%d}`, submitHcTaskResp.Data.TaskId)
	_ = Query(t, urls, map[string]string{}, newBody, hardCriteriaTaskDetail)
	t.Logf("%s check finished", endPoint.EndPoint)
}

func Query(t *testing.T, urls []string, conditions map[string]string, body string, endPoint EndPointInfo) []byte {
	respSlice := make([][]byte, len(urls))
	var err error
	for i, url := range urls {
		switch endPoint.Method {
		case Get:
			respSlice[i], err = GetJson(ctx, url+endPoint.EndPoint, conditions, 35, headers[i])
		case Post:
			respSlice[i], err = PostJson(ctx, url+endPoint.EndPoint, []byte(body), 35, headers[i])
		}
		if err != nil {
			t.Errorf("%s get error: %s", url+endPoint.EndPoint, err.Error())
		}
	}
	if len(respSlice) == 2 && string(respSlice[0]) != string(respSlice[1]) {
		t.Errorf("response error: endpoint=%s, lps response=%s, smr response=%s", endPoint.EndPoint, string(respSlice[0]), string(respSlice[1]))
	}
	return respSlice[0]
}
