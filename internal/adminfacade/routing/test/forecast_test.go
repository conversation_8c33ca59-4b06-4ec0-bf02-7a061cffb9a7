package test

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"testing"
)

func TestReadApi(t *testing.T) {
	type args struct {
		ctx       context.Context
		condition map[string]map[string]string
		body      map[string]string
	}
	tests := []struct {
		name  string
		args  args
		want  interface{}
		want1 *srerr.Error
	}{
		{
			name: "General Use Cases",
			args: args{
				ctx: ctx,
				condition: map[string]map[string]string{
					taskDetail.EndPoint: {
						"routing_type": "0",
					},
					taskList.EndPoint: {
						"routing_type": "0",
					},
					checkTask.EndPoint: {
						"id":           "168",
						"routing_type": "0",
					},
					taskDetail.EndPoint: {
						"id": "168",
					},
					taskOutline.EndPoint: {
						"task_id": "168",
					},
					taskResult.EndPoint: {
						"id": "168",
					},
					ilhTaskResult.EndPoint: {
						"id": "168",
					},
					shipmentSummary.EndPoint: {
						"product_id":       "57119",
						"start_date":       "2022-08-03",
						"end_date":         "2022-08-03",
						"is_multi_product": "true",
					},
					ilhShipmentSummary.EndPoint: {
						"product_id": "57093",
						"start_date": "2022-07-26",
						"end_date":   "2022-07-26",
					},
				},
				body: map[string]string{
					queryHardCriteriaTaskList.EndPoint: `{"task_type":1,"task_status":"4","product_id":57106,"is_multi_product":true,"pageno":1,"pagecount":1000}`,
					queryOrderCount.EndPoint:           `{"task_id":1134,"product_id":57093,"order_start_time":"2022-08-10","order_end_time":"2022-08-14"}`,
					hardCriteriaTaskDetail.EndPoint:    `{"task_id":1493}`,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, endPoint := range readEndPoints {
				var condition map[string]string
				if _, ok := tt.args.condition[endPoint.EndPoint]; ok {
					condition = tt.args.condition[endPoint.EndPoint]
				}
				var body string
				if _, ok := tt.args.body[endPoint.EndPoint]; ok {
					body = tt.args.body[endPoint.EndPoint]
				}
				ContrastReadApi(t, urls, condition, body, endPoint)
			}
		})
	}
}

func TestWriteApi(t *testing.T) {
	type args struct {
		ctx       context.Context
		condition map[string]map[string]string
		body      map[string]string
	}
	tests := []struct {
		name  string
		args  args
		want  interface{}
		want1 *srerr.Error
	}{
		{
			name: "General Use Cases",
			args: args{
				ctx:       ctx,
				condition: map[string]map[string]string{},
				body: map[string]string{
					ConfirmAsHCTask.EndPoint: `{"task_name":"create draft test","product_id":5164,"operator":"<EMAIL>","order_start_time":"2022-10-13","order_end_time":"2022-10-13","routing_type":1}`,
					submitHCTask.EndPoint:    `{"task_type":1,"task_id":1500,"task_name":"test create","product_id":51094,"task_status":0,"order_start_time":"2022-10-12","order_end_time":"2022-10-12","order_count":0,"task_start_time":0,"task_end_time":0,"last_start_time":0,"last_end_time":0,"task_progress":0,"completion_orders":[{"order_paid_time":"2022-10-12","shipment_quantity":0,"percentage":0}],"estimated_time":1,"operator":"<EMAIL>","ctime":**********,"routing_type":1}`,
					editHCTask.EndPoint:      `{"task_type":1,"task_id":1517,"task_name":"create draft test","product_id":5164,"task_status":0,"order_start_time":"2022-10-13","order_end_time":"2022-10-13","order_count":0,"task_start_time":0,"task_end_time":0,"last_start_time":0,"last_end_time":0,"task_progress":0,"completion_orders":[{"order_paid_time":"2022-10-13","shipment_quantity":0,"percentage":0}],"estimated_time":1,"operator":"<EMAIL>","ctime":**********}`,
					updateHCTask.EndPoint:    `{"task_id":1500,"task_status":2,"old_status":1}`,
					deleteHCTask.EndPoint:    `{"task_id": 1517}`,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, endPoint := range readEndPoints {
				var condition map[string]string
				if _, ok := tt.args.condition[endPoint.EndPoint]; ok {
					condition = tt.args.condition[endPoint.EndPoint]
				}
				var body string
				if _, ok := tt.args.body[endPoint.EndPoint]; ok {
					body = tt.args.body[endPoint.EndPoint]
				}
				ContrastWriteApi(t, urls, condition, body, endPoint)
			}
		})
	}
}
