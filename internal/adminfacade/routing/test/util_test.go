package test

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"io/ioutil"
	"net/http"
	"time"
)

var (
	lps = "http://admin.lps.test.shopee.vn"
	smr = "http://0.0.0.0:8090"

	urls = []string{
		lps,
		smr,
	}
)

func Post<PERSON>son(ctx context.Context, url string, data []byte, timeoutSecond int, headers map[string]string) ([]byte, error) {
	jsonHeader := make(map[string]string, len(headers)+1)
	if len(headers) > 0 {
		for k, v := range headers {
			jsonHeader[k] = v
		}
	}
	jsonHeader["Content-Type"] = constant.ApplicationJSON
	return request(ctx, http.MethodPost, url, data, nil, timeoutSecond, jsonHeader)
}

func <PERSON><PERSON><PERSON>(ctx context.Context, url string, param map[string]string, timeoutSecond int, headers map[string]string) ([]byte, error) {
	return request(ctx, http.MethodGet, url, nil, param, timeoutSecond, headers)
}

func request(ctx context.Context, method string, reqUrl string, data []byte, param map[string]string, timeout int, headers map[string]string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	var (
		req *http.Request
		err error
	)
	if method == http.MethodGet {
		req, err = http.NewRequestWithContext(ctx, method, reqUrl, nil)
		if err != nil {
			return nil, err
		}
		q := req.URL.Query()
		for paramKey, paramVal := range param {
			q.Set(paramKey, paramVal)
		}
		req.URL.RawQuery = q.Encode()
	} else if method == http.MethodPost {
		req, err = http.NewRequestWithContext(ctx, method, reqUrl, bytes.NewBuffer(data))
		if err != nil {
			return nil, err
		}
	}
	if len(headers) > 0 {
		for k, v := range headers {
			req.Header.Add(k, v)
		}
	}
	rsp, err := httputil.GetDefaultHttpInvoker().Invoke(ctx, req)
	if err != nil {
		//logger.CtxLogErrorf(ctx, "http_request|request fail|url=%s,header=%s,request=%s,err=%v", req.URL, JsonString(req.Header), reqBody, err)
		return nil, err
	}

	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		err := fmt.Errorf("http status code %v", rsp.StatusCode)
		return nil, err
	}
	ret, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		//logger.CtxLogErrorf(ctx, "http_request|get response body fail|url=%s,header=%s,request=%s,err=%v", req.URL, JsonString(req.Header), reqBody, err)
		return nil, err
	}

	return ret, nil
}
