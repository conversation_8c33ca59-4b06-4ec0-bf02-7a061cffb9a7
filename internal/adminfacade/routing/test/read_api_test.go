package test

import (
	"context"
	"testing"
)

// Method
const (
	Get = iota
	Post
)

type EndPointInfo struct {
	EndPoint string
	Method   int
}

var (
	taskDetail                = EndPointInfo{"/api/admin/smart_routing/task_detail", Get}
	checkTask                 = EndPointInfo{"/api/admin/smart_routing/check_task", Get}
	taskList                  = EndPointInfo{"/api/admin/smart_routing/task_list", Get}
	taskOutline               = EndPointInfo{"/api/admin/smart_routing/task_outline", Get}
	taskResult                = EndPointInfo{"/api/admin/smart_routing/task_result", Get}
	ilhTaskResult             = EndPointInfo{"/api/admin/smart_routing/ilh_task_result", Get}
	shipmentSummary           = EndPointInfo{"/api/admin/smart_routing/shipment_summary", Get}
	ilhShipmentSummary        = EndPointInfo{"/api/admin/smart_routing/ilh_shipment_summary", Get}
	queryOrderCount           = EndPointInfo{"/api/admin/smart_routing/query_order_count", Post}
	queryHardCriteriaTaskList = EndPointInfo{"/api/admin/smart_routing/query_hard_criteria_task_list", Post}
	hardCriteriaTaskDetail    = EndPointInfo{"/api/admin/smart_routing/hard_criteria_task_detail", Post}
)

var (
	readEndPoints = []EndPointInfo{
		taskDetail,
		checkTask,
		taskList,
		taskOutline,
		taskResult,
		ilhTaskResult,
		shipmentSummary,
		ilhShipmentSummary,
		queryOrderCount,
		queryHardCriteriaTaskList,
		hardCriteriaTaskDetail,
	}

	headers = []map[string]string{
		{
			"jwt-token": "eyJhbGciOiJIUzI1NiIsIm9wdHIiOiJscHMtYWRtaW4tYXBpIiwidHlwIjoiSldUIn0.**************************************************************************************************************************************************************************************************************************************************************************************.tquUv1CQ0piTI47da3_pWdvB2_treHO73awNvHYIaBk",
		},
		{
			"shopee-baggage": "PFB=pfb-splps-8805",
			"jwt-token":      "eyJhbGciOiJIUzI1NiIsIm9wdHIiOiJzbWFydHJvdXRpbmctYWRtaW4iLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************.Sjd-eSd4GuytOs2xBn3ZCs1DvvB8UpHM37zjf3ys2G4",
		},
	}
	ctx = context.Background()
)

// ContrastReadApi 读接口的冒烟用例，用户对比读接口的返回与lps是否相同
func ContrastReadApi(t *testing.T, urls []string, conditions map[string]string, body string, endPoint EndPointInfo) {
	Query(t, urls, conditions, body, endPoint)
	t.Logf("%s check finished\n", endPoint.EndPoint)
}
