package routing

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type ForecastFacade struct {
	TaskService     forecastservice.IForecastService
	ForecastTaskSrv forecastservice.IForecastTaskService
	ForecastSrv     smart_routing_forecast.LocalForecastService
}

func (res *ForecastFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/smart_routing")
	routes.POST("/create_task", res.CreateTask)
	routes.POST("/update_task", res.UpdateTask)
	routes.POST("/task_start", res.StartTask)
	routes.POST("/task_stop", res.StopTask)
	routes.POST("/delete_task", res.DeleteTask)
	routes.POST("/task_copy", res.CopyTask)
	routes.GET("/task_detail", res.GetTaskDetail)
	routes.GET("/check_task", res.CheckTaskZoneValid)
	routes.GET("/task_list", res.GetTaskList)
	routes.GET("/task_outline", res.GetTaskOutline)
	routes.GET("/task_result", res.GetTaskResult) // 还有TODO要做
	routes.POST("/task_rule_deploy", res.DeployTask)
	routes.GET("/ilh_task_result", res.GetIlhTaskResult)

	routes.GET("/forecasting_result/export", res.ExportForecastingResult)
	routes.GET("/shipment/export", res.ExportShipment)
	routes.GET("/shipment_summary", res.GetShipmentSummary)
	routes.GET("/ilh_shipment_summary", res.GetILHShipmentSummary)
	routes.POST("/query_order_count", res.QueryOrderCount)
	routes.POST("/upload_prediction_volume", res.UploadPredictionVolume)
	routes.GET("/get_prediction_volume_by_task_id", res.GetPredictionVolumeByTaskId)
	routes.POST("/batch_deploy_check", res.BatchDeployCheck)
	routes.POST("/batch_deploy", res.BatchDeploy)

	routes.POST("/confirm_as_draft_hard_criteria_task", res.CreateDraftTask)
	routes.POST("/edit_hard_criteria_task", res.EditDraftTask)
	routes.POST("/submit_hard_criteria_task", res.CreateWaitingTask)
	routes.POST("/delete_hard_criteria_task", res.DeleteDraftTask)
	routes.POST("/update_hard_criteria_task", res.UpdateHCTaskStatus)
	routes.POST("/query_hard_criteria_task_list", res.GetHCTaskList)
	routes.POST("/hard_criteria_task_detail", res.GetHCTaskDetail)
	routes.POST("/create_hard_criteria_task", res.CreateHCTask)
	routes.POST("/check_hard_criteria_task", res.CheckHCTask)
	routes.POST("/execute_hard_criteria_task", res.ExecuteHCTask)
	routes.POST("/update/hard_criteria_task", res.UpdateHardCriteriaTask)
	routes.POST("/forecast/import/weight_range", res.ImportWeightRange)
	routes.POST("/forecast/import/order_count", res.ImportOrderCount)
	routes.POST("/query_order_aggregation", res.QueryOrderAggregation)
	routes.POST("/export_order_aggregation", res.ExportOrderAggregation)
	routes.POST("/block_result", res.GetForecastBlockOrder)
	// internal api
	routes.POST("/refresh_hbase_hard_criteria", res.RefreshHbaseHardCriteria)
	routes.POST("/start_forecast", res.StartForecast)

	//cb shop group list
	routes.GET("/list_shop_group", res.ListForecastShopGroup)
	routes.POST("/export_shop_group", res.ExportCbShopGroup)
	return routes.GetRouters()
}

func (res *ForecastFacade) StartForecast(ctx *restful.Context) {
	var req = &foreschema.StartForecastRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	task, taskErr := res.TaskService.GetTaskById(ctx.Ctx, int(req.TaskId))
	if taskErr != nil {
		apiutil.FailJSONResp(ctx, taskErr, taskErr.Error())
		return
	}
	_ = res.ForecastSrv.StartForecast(ctx.Ctx, task)

	task.TaskStatus = persistent.TaskStatusDone
	db, _ := dbutil.GetDB(ctx.Ctx, persistent.ForecastingTaskHook.DBForWrite())
	dbErr := db.Updates(task).GetError()
	if dbErr != nil {
		apiutil.FailJSONResp(ctx, nil, dbErr.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) CreateTask(ctx *restful.Context) {
	var req = &foreschema.CreateTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.CreateTask(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) UpdateTask(ctx *restful.Context) {
	var req = &foreschema.UpdateTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.UpdateTask(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) ImportWeightRange(ctx *restful.Context) {
	req := foreschema.ImportWeightRangeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.TaskService.ImportWeightRange(ctx.Ctx, req.TaskId, req.FilePath)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.LocalForecastAdmin, monitoring.ImportWeightFailed, fmt.Sprintf("import weight=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) ImportOrderCount(ctx *restful.Context) {
	req := foreschema.ImportWeightRangeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.TaskService.ImportOrderCount(ctx.Ctx, req.TaskId, req.FilePath)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.LocalForecastAdmin, monitoring.ImportSimulationOrderCountFailed, fmt.Sprintf("SimulationOrderCount=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) ExportOrderAggregation(ctx *restful.Context) {
	req := foreschema.QueryOrderAggregationRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.TaskService.ExportOrderAggregation(ctx.Ctx, req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.LocalForecastAdmin, monitoring.ExportOrderAggregationFailed, fmt.Sprintf("ExportOrderAggregationFailed=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetForecastBlockOrder(ctx *restful.Context) {
	req := foreschema.BlockOrderListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.TaskService.GetBlockOrderList(ctx.Ctx, req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.LocalForecastAdmin, monitoring.ForecastBlockOrder, fmt.Sprintf("task_id=%v block order=%v", req.TaskId, err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) QueryOrderAggregation(ctx *restful.Context) {
	req := foreschema.QueryOrderAggregationRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.TaskService.QueryOrderAggregation(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) StartTask(ctx *restful.Context) {
	var req = &foreschema.GetTaskByIdRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.StartTask(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) StopTask(ctx *restful.Context) {
	var req = &foreschema.GetTaskByIdRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.StopTask(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) DeleteTask(ctx *restful.Context) {
	var req = &foreschema.GetTaskByIdRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := res.TaskService.DeleteTaskById(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) CopyTask(ctx *restful.Context) {
	var req = &foreschema.GetTaskByIdRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := res.TaskService.CopyTaskById(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) GetTaskDetail(ctx *restful.Context) {
	var req = &foreschema.GetTaskByIdRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.GetTaskById(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//获取硬性校验task最近的完成时间
	//刚创建时及老版本对应的HCTaskId为0，会返回空列表错误，对HCTaskEndTime有疑惑可以去查看日志
	hcTaskDetail, _ := res.ForecastTaskSrv.GetHardCriteriaTaskDetail(ctx.Ctx, resp.HCTaskId)
	resp.HCTaskEndTime = int64(hcTaskDetail.LastEndTime)

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) CheckTaskZoneValid(ctx *restful.Context) {
	var req = &foreschema.CheckTaskZoneValidRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		//return // lps这里并没有return，原因不明
	}

	resp, err := res.TaskService.CheckTaskZoneValid(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetTaskResult(ctx *restful.Context) {
	var req = &foreschema.TaskResultRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.TaskResult(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetIlhTaskResult(ctx *restful.Context) {
	var req = &foreschema.TaskResultRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.IlhTaskResult(ctx.Ctx, req, forecastservice.ILHOpsResult)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetTaskList(ctx *restful.Context) {
	var req = &foreschema.GetTaskListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.GetTaskList(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetTaskOutline(ctx *restful.Context) {
	var req = foreschema.GetTaskOutlineReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.GetTaskById(ctx.Ctx, req.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) DeployTask(ctx *restful.Context) {
	var req = &foreschema.DeployTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := res.TaskService.DeployTaskById(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetShipmentSummary(ctx *restful.Context) {
	var req = &foreschema.ShipmentSummaryRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	if req.IsMultiProduct {
		resp, err := res.TaskService.GetMultiProductShipmentSummary(ctx.Ctx, req)
		if err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}

		apiutil.SuccessJSONResp(ctx, resp)
		return
	}

	resp, err := res.TaskService.GetShipmentSummary(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) GetILHShipmentSummary(ctx *restful.Context) {
	var req = &foreschema.ShipmentSummaryRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	resp, err := res.TaskService.GetIlhProductShipmentSummary(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (res *ForecastFacade) ExportShipment(ctx *restful.Context) {
	var req = &foreschema.ShipmentSummaryRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	}
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}
	var resp *bytes.Buffer
	var err *srerr.Error
	if req.RoutingType == rule.IlhRoutingType {
		resp, err = res.TaskService.ExportIlhShipmentSummary(ctx.Ctx, req)
	} else if req.IsMultiProduct {
		resp, err = res.TaskService.ExportMultiProductShipmentSummary(ctx.Ctx, req)
	} else {
		resp, err = res.TaskService.ExportShipmentSummary(ctx.Ctx, req)
	}

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.WriteExcelResp(ctx, "shipment_summary", resp.Bytes())
}

func (res *ForecastFacade) ExportForecastingResult(ctx *restful.Context) {
	var req = &foreschema.TaskResultRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	}

	resp, err := res.TaskService.ExportForecastResult(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.WriteExcelResp(ctx, "forecasting_result", resp.Bytes())
}

// CreateDraftTask 创建草稿态的硬性校验任务
func (res *ForecastFacade) CreateDraftTask(ctx *restful.Context) {
	req := foreschema.CreateHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.ForecastTaskSrv.CreateDraftHardCriteriaTask(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// EditDraftTask 编辑草稿态任务
func (res *ForecastFacade) EditDraftTask(ctx *restful.Context) {
	req := foreschema.EditHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.ForecastTaskSrv.EditHardCriteriaTask(ctx.Ctx, forecast.TaskDraft, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// CreateWaitingTask 创建任务，创建后任务的属性不可更改
func (res *ForecastFacade) CreateWaitingTask(ctx *restful.Context) {
	req := foreschema.EditHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	//校验起止时间
	if err := foreschema.ValidateDate(req.OrderStartTime, req.OrderEndTime); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	//人工创建的任务，需要把状态置为waiting
	resp, err := res.ForecastTaskSrv.EditHardCriteriaTask(ctx.Ctx, forecast.TaskWaiting, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// QueryOrderCount 查询一段时间内的订单数量
func (res *ForecastFacade) QueryOrderCount(ctx *restful.Context) {
	req := foreschema.QueryOrderCountRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	//校验日期的合法性
	if err := foreschema.ValidateDate(req.OrderStartTime, req.OrderStartTime); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	resp, err := res.ForecastTaskSrv.QueryOrderCountByTime(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// DeleteDraftTask 删除草稿态的硬性校验任务
func (res *ForecastFacade) DeleteDraftTask(ctx *restful.Context) {
	req := foreschema.DeleteDraftHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := res.ForecastTaskSrv.DeleteDraftHardCriteriaTask(ctx.Ctx, req.TaskId); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// UpdateHCTaskStatus 更新硬性校验任务的状态
// 0-Draft,1-Waiting,2-Doing,3-Done,4-Stop,5-Fail
func (res *ForecastFacade) UpdateHCTaskStatus(ctx *restful.Context) {
	req := foreschema.UpdateHCTaskStatusRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := res.ForecastTaskSrv.UpdateHardCriteriaTaskStatus(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// GetHCTaskList 获取硬性校验刷新任务的列表
func (res *ForecastFacade) GetHCTaskList(ctx *restful.Context) {
	req := foreschema.GetHCTaskListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.ForecastTaskSrv.GetHardCriteriaTaskList(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// GetHCTaskDetail 获取硬性校验刷新任务详情
func (res *ForecastFacade) GetHCTaskDetail(ctx *restful.Context) {
	req := foreschema.GetHCTaskDetailRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := res.ForecastTaskSrv.GetHardCriteriaTaskDetail(ctx.Ctx, req.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// CreateHCTask 手动执行硬性校验刷新任务
func (res *ForecastFacade) CreateHCTask(ctx *restful.Context) {
	res.ForecastTaskSrv.CreateHCTaskBySystem(ctx.Ctx)

	apiutil.SuccessJSONResp(ctx, nil)

}

// CheckHCTask 检查硬性校验刷新任务
func (res *ForecastFacade) CheckHCTask(ctx *restful.Context) {
	res.ForecastTaskSrv.CheckHCTask(ctx.Ctx)
	apiutil.SuccessJSONResp(ctx, nil)
}

// ExecuteHCTask 执行硬性校验刷新任务
func (res *ForecastFacade) ExecuteHCTask(ctx *restful.Context) {
	req := foreschema.ExecuteHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	res.ForecastTaskSrv.RefreshHardCriteriaByTask(ctx.Ctx, req)
	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) UpdateHardCriteriaTask(ctx *restful.Context) {
	req := foreschema.UpdateHCTaskRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := res.ForecastTaskSrv.UpdateHardCriteriaTask(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// RefreshHbaseHardCriteria 内部接口，异步刷新Product+Date维度的订单硬性校验数据
func (res *ForecastFacade) RefreshHbaseHardCriteria(ctx *restful.Context) {
	req := foreschema.RefreshHbaseHardCriteriaRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	concurrency := 1
	if req.Concurrency > 0 && req.Concurrency <= 16 {
		concurrency = req.Concurrency
	}

	startDate, err := timeutil.ParseDateStr(req.StartDate)
	if err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, req.StartDate, err), err.Error())
		return
	}

	endDate, err := timeutil.ParseDateStr(req.EndDate)
	if err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, req.EndDate, err), err.Error())
		return
	}

	newCtx := ctxhelper.CloneTrace(ctx.Ctx)
	go func() {
		reportModule := "RefreshHbaseHardCriteria"
		for _, date := range timeutil.GetDayListByTime(startDate, endDate) {
			reportInterface := fmt.Sprintf("%v:%v", req.ProductId, date)
			if req.IsMultiProduct {
				reportInterface = "MultiProduct:" + reportInterface
			}
			if err := res.ForecastTaskSrv.RefreshHbaseHardCriteriaByProduct(newCtx, req.ProductId, req.IsMultiProduct, date, concurrency); err != nil {
				monitoring.ReportError(newCtx, reportModule, reportInterface, err.Error())
				logger.CtxLogErrorf(newCtx, "refresh hard criteria fail | err=%v", err)
			} else {
				monitoring.ReportSuccess(newCtx, reportModule, reportInterface, "")
			}
		}
	}()

	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) ListForecastShopGroup(ctx *restful.Context) {
	req := foreschema.GetShopGroupListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	list, err := res.ForecastTaskSrv.GetForecastShopGroupList(ctx.Ctx)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, foreschema.GetShopGroupListResp{List: convertShopInfo(ctx.Ctx, list, req.ProductId)})
}

func convertShopInfo(ctx context.Context, tabs []lpsclient.GetClientGroupTab, productId int64) []foreschema.ShopGroupInfo {
	list := make([]foreschema.ShopGroupInfo, 0)
	for i := 0; i < len(tabs); i++ {
		tab := tabs[i]
		if tab.ProductId != productId {
			continue
		}
		shopGroupId, err := strconv.ParseInt(tab.ClientGroupID, 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "client group id:%s, format int err:%v", tab.ClientGroupID, err)
		}
		list = append(list, foreschema.ShopGroupInfo{
			ShopGroupId:   shopGroupId,
			ShopGroupName: tab.ClientGroupName,
		})
	}
	return list
}

func (res *ForecastFacade) ExportCbShopGroup(ctx *restful.Context) {
	req := foreschema.ExportCbShopGroupReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	url, err := res.TaskService.ExportCbShopGroup(ctx.Ctx, req.IsTask, req.RuleId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, foreschema.ExportCbShopGroupResp{Url: url})
}

func (res *ForecastFacade) BatchDeployCheck(ctx *restful.Context) {
	var req foreschema.BatchDeployReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	blockList, err := res.TaskService.BatchDeployCheck(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, blockList)
}

func (res *ForecastFacade) BatchDeploy(ctx *restful.Context) {
	var req foreschema.BatchDeployReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := res.TaskService.BatchDeploy(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) UploadPredictionVolume(ctx *restful.Context) {
	var req foreschema.UploadPredictionVolumeReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := res.TaskService.UploadPredictionVolume(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (res *ForecastFacade) GetPredictionVolumeByTaskId(ctx *restful.Context) {
	var req foreschema.GetPredictionVolumeByTaskIdReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	predictionVolumes, err := res.TaskService.GetPredictionVolumeByTaskId(ctx.Ctx, req.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, predictionVolumes)
}
