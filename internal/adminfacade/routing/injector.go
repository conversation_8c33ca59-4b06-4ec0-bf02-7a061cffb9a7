package routing

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/sync_lfs_order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(ForecastFacade), "*"),

	forecastservice.ProviderSet,
	forecastservice.ForecastTaskServiceProviderSet,
	sync_lfs_order.OrderSyncSet,
	schedule_factor.NewDgFactor,
	schedule_factor.NewMinVolumeFactor,
	schedule_factor.NewMaxCapacityFactor,
	schedule_factor.NewLinePriorityFactor,
	schedule_factor.NewLineCheapestShippingFeeFactor,
	schedule_factor.NewMinWeightFactor,
	schedule_factor.NewMaxWeightFactor,
	schedule_factor.NewDefaultPriorityFactor,
	schedule_factor.NewDefaultWeightageFactor,
	schedule_factor.NewMinVolumeV2,
	schedule_factor.NewMaxVolumeV2,
	schedule_factor.NewILHParcelMinVolumeFactor,
	schedule_factor.NewILHParcelMaxCapacityFactor,
	schedule_factor.NewCombinationPriorityFactor,
	schedule_factor.NewFactorSet,
	smart_routing_forecast.SmartRoutingForecastTaskProviderSet,
	smart_routing_forecast.LocalForecastTaskPSet,
	wire.NewSet(routing.NewLocalSpxForecasting),
)
