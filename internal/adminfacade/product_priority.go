package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/admin/masking"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type ProductPriorityFacade struct {
	ProductPriorityService masking.ProductPriorityServiceInterface
}

func (r *ProductPriorityFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/product_priority")
	routes.POST("/list", r.ListView)
	routes.GET("/detail", r.DetailView)
	routes.POST("/create", r.<PERSON>reateView)
	routes.POST("/update", r.UpdateView)

	routes.POST("/import_validate", r.ImportValidate)
	routes.POST("/import", r.Import)
	routes.POST("/export", r.Export)
	routes.POST("/export_template", r.ExportTemplate)
	routes.POST("/get_groupid_with_active_rules", r.ListGroupIdwithActiveCfg)

	return routes.GetRouters()
}

func (r *ProductPriorityFacade) ListView(ctx *restful.Context) {
	req := &admin_protocol.ListRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	page, count, maskProductID, shopGroupID, ruleType, statusList := convertProductPriorityListRequest(req)

	priorities, total, err := r.ProductPriorityService.List(ctx.Ctx, page, count, maskProductID, shopGroupID, ruleType, statusList)

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	var productPriorityResponses = make([]*admin_protocol.Priority, 0)

	if priorities != nil {
		count = uint32(len(priorities))
		productPriorityResponses = make([]*admin_protocol.Priority, count)
		for i, priority := range priorities {
			productPriorityResponses[i] = convertProductPriorityResponse(priority)
		}
	}

	res := &admin_protocol.ListResponse{
		Page:              page,
		Count:             req.Count,
		Total:             total,
		ProductPriorities: productPriorityResponses,
	}

	apiutil.SuccessJSONResp(ctx, res)
}

func (r *ProductPriorityFacade) DetailView(ctx *restful.Context) {
	req := &admin_protocol.DetailRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	ruleType, componentPriorities, ets, err := r.ProductPriorityService.Detail(ctx.Ctx, req.GetId())

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	var componentPriorityResponses = make([]*admin_protocol.Detail, len(componentPriorities))

	for i, componentPriority := range componentPriorities {
		componentPriorityResponses[i] = convertComponentPriorityResponse(componentPriority)
	}

	res := &admin_protocol.DetailResponse{
		EffectStartTime: ets,
		RuleType:        uint32(ruleType),
		PriorityDetails: componentPriorityResponses,
	}

	apiutil.SuccessJSONResp(ctx, res)
}

func (r *ProductPriorityFacade) CreateView(ctx *restful.Context) {
	req := &admin_protocol.CreateRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	maskProductID, shopGroupID, ruleType, componentPriorities := convertProductPriorityCreateRequest(req)
	if err := r.ProductPriorityService.Create(ctx.Ctx, maskProductID, shopGroupID, ruleType, componentPriorities, req.EffectiveImmediately, req.EffectiveStartTime); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (r *ProductPriorityFacade) UpdateView(ctx *restful.Context) {
	req := &admin_protocol.UpdateRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	id, ruleType, componentPriorities := convertProductPriorityUpdateRequest(req)

	err := r.ProductPriorityService.Update(ctx.Ctx, id, ruleType, componentPriorities, req.EffectiveImmediately, req.EffectiveStartTime)

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func convertProductPriorityListRequest(listReq *admin_protocol.ListRequest) (uint32, uint32, int, int64, entity.DefaultRuleType, []uint32) {
	return listReq.Page, listReq.Count, int(listReq.MaskProductId), int64(listReq.ShopGroupId), entity.DefaultRuleType(listReq.RuleType), listReq.StatusList
}

func convertComponentPriorityResponse(componentPriority entity.ComponentPriority) *admin_protocol.Detail {
	return &admin_protocol.Detail{
		Id:        int32(componentPriority.ProductID),
		Name:      componentPriority.Name,
		Priority:  componentPriority.Priority,
		Weightage: componentPriority.Weightage,
		Status:    uint32(componentPriority.Status),
	}
}

func convertProductPriorityResponse(priority entity.ProductPriority) *admin_protocol.Priority {
	return &admin_protocol.Priority{
		Id:                 uint32(priority.ID),
		MaskProductId:      int32(priority.MaskProductID),
		MaskProductName:    priority.MaskProductName,
		ShopGroupId:        int32(priority.ShopGroupID),
		ShopGroupName:      priority.ShopGroupName,
		RuleType:           uint32(priority.RuleType),
		Operator:           priority.Operator,
		Ctime:              int32(priority.Ctime),
		Mtime:              int32(priority.Mtime),
		Status:             uint32(priority.Status),
		ForecastTaskId:     int32(priority.ForecastTaskId),
		EffectiveStartTime: int32(priority.EffectiveStartTime),
	}
}

func convertProductPriorityCreateRequest(createReq *admin_protocol.CreateRequest) (int, int64, entity.DefaultRuleType, []entity.ComponentPriority) {
	var componentPriorities = make([]entity.ComponentPriority, len(createReq.PriorityDetails))
	for i, detailReq := range createReq.PriorityDetails {
		componentPriorities[i] = entity.ComponentPriority{
			ProductID: int64(int(detailReq.Id)),
			Priority:  detailReq.Priority,
			Weightage: detailReq.Weightage,
			Status:    entity.ComponentPriorityStatus(detailReq.Status),
		}
	}
	return int(createReq.MaskProductId), int64(createReq.ShopGroupId), entity.DefaultRuleType(createReq.RuleType), componentPriorities
}

func convertProductPriorityUpdateRequest(updateReq *admin_protocol.UpdateRequest) (uint64, entity.DefaultRuleType, []entity.ComponentPriority) {
	var componentPriorities = make([]entity.ComponentPriority, len(updateReq.PriorityDetails))
	for i, detailReq := range updateReq.PriorityDetails {
		componentPriorities[i] = entity.ComponentPriority{
			ProductID: int64(int(detailReq.Id)),
			Priority:  detailReq.Priority,
			Weightage: detailReq.Weightage,
			Status:    entity.ComponentPriorityStatus(detailReq.Status),
		}
	}
	return updateReq.GetId(), entity.DefaultRuleType(updateReq.RuleType), componentPriorities
}

func (p *ProductPriorityFacade) ImportValidate(ctx *restful.Context) {
	req := new(admin_protocol.ImportValidateRequest)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.ProductPriorityService.ImportValidate(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *ProductPriorityFacade) Import(ctx *restful.Context) {
	req := new(admin_protocol.ImportRequest)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.ProductPriorityService.Import(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *ProductPriorityFacade) Export(ctx *restful.Context) {
	req := new(admin_protocol.ExportRequest)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.ProductPriorityService.Export(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *ProductPriorityFacade) ExportTemplate(ctx *restful.Context) {
	req := new(admin_protocol.ExportTemplateRequest)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.ProductPriorityService.ExportTemplate(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}
