package adminfacade

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/debug"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/sync_data_schema"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/sync_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	rc "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	orderPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_order_fulfilment_core_message_bus.pb"
	msgPb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"github.com/gogo/protobuf/proto"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

type DebugFacade struct {
	Location                            address.AddrRepo
	RedisCounter                        rc.RedisCounter
	LpsApi                              lpsclient.LpsApi
	RateClient                          chargeclient.ChargeApi
	LoadForecastVolumeConfig            *masking.LoadForecastVolumeConfig
	MaskingForecast                     *masking.MaskingForecast
	AllocateDataStore                   *masking.AllocateStoreConsumer
	AuditLogTaskServer                  *audit_log_task.AuditLogTaskServer
	DeleteMaskingSubTaskImpl            *masking.DeleteMaskingSubTaskImpl
	CheckMaskingProcessTaskImpl         *masking.CheckMaskingProcessTaskImpl
	GetForecastTotalCountImpl           *masking.GetForecastTotalCountImpl
	AllocateStoreHbaseConsumer          *masking.AllocateStoreHbaseConsumer
	SmartRoutingService                 *select_lane.SmartRoutingServiceImpl
	ForecastTaskSrv                     forecastservice.IForecastTaskService
	AllocateForecastService             *masking_forecast.AllocateForecastServiceImpl
	SmartRoutingForecastSrv             smart_routing_forecast.SmartRoutingForecastService
	AllocateOrderDataRepo               allocate_order_data_repo.AllocateOrderDataRepo
	ReportMaskingVolumeTask             *masking.ReportMaskingVolumeTask
	SyncDataService                     sync_data.SyncDataService
	BatchAllocateService                *masking.StartBatchForecastUnitImpl
	CreateBASubTask                     *batch_allocate_forecast.CreateBASubTask
	AllocateHistoryOutLine              *masking.AllocateHistoryOutLine
	ParseBatchVolumeService             *masking.ParseBatchVolume
	UpdateBatchAllocateForecastTaskImpl *masking.UpdateBatchAllocateForecastTaskImpl
	AllocateScheduleVisualTask          *masking.AllocateScheduleVisualTask
	ScheduleCountStatTask               *masking.ScheduleCountStatTask
	BAForecastToolProgressService       *batch_allocate_forecast.BAForecastToolProgressImpl
	SplitBatchAllocateOrdersTask        *masking.SplitBatchAllocateOrdersTask
	BatchAllocateTask                   *batch_allocate.BatchAllocateTask
	BatchAbnormalInspectionTask         *batch_allocate.BatchAbnormalInspectionTask
	PushOrderResultTask                 *batch_allocate.PushOrderResultTask
	AbnormalBatchAllocateTask           *batch_allocate.AbnormalBatchAllocateTask
	BatchAllocateHoldOrderConsumer      *batch_allocate.BatchAllocateHoldOrderConsumer
	ClearOrderAndResultTask             *batch_allocate.ClearOrderAndResultTask
	ReportOrderCountTask                *routing.ReportOrderCount
	ScheduleRuleTask                    *routing.ScheduleRule
	BatchAllocateOrderRepo              order.BatchAllocateOrderRepo
	BatchAllocateMonitorTask            *batch_allocate.BatchAllocateMonitor
	ClearMaskingVolumeTask              *masking.ClearMaskingVolumeTask
	CheckoutFulfillmentProductCounter   *allocate_volume_counter.CheckoutFulfillmentProductCounter
	DeductVolumeCounter                 *allocate_volume_counter.DeductVolumeCounter
	MakeUpAsyncAllocationLog            *batch_allocate.MakeUpAsyncAllocationLog
	ILHForecastTask                     *routing.ILHForecastTask
}

func (p *DebugFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	DebugRouting := adminGroup.Group("/debug")
	DebugRouting.POST("/get_counter", p.DebugGetCounter)
	DebugRouting.POST("/recover/redis_counter", p.RecoverRedisCounter)
	DebugRouting.GET("/option/redis_counter", p.RedisCounterOption)
	DebugRouting.POST("/location/id", p.DBGetLocationById)
	DebugRouting.POST("/location/name", p.DBGetLocationByName)
	DebugRouting.POST("/ratelimiter", p.DBRateLimiter)
	DebugRouting.POST("/debug_get_shipping_fee", p.TestGetShippingFee)

	// Masking
	DebugRouting.POST("/load_forecast_volume_config", p.DebugLoadForecastVolumeConfig)
	DebugRouting.POST("/allocate_data_store", p.DebugAllocateDataStore)
	DebugRouting.POST("/masking_forecast", p.DebugMaskingForecast)
	DebugRouting.POST("/audit_log_report", p.DebugAuditLogReport)
	DebugRouting.POST("/check_mask_process_sub_task", p.CheckMaskProcessSubTask)
	DebugRouting.POST("/delete_mask_expired_sub_task", p.DeleteMaskExpiredSubTask)
	DebugRouting.POST("/get_forecast_total_count", p.GetForecastTotalCount)
	DebugRouting.POST("/store_allocate_into_main", p.AllocateStoreMsgIntoMain)
	DebugRouting.POST("/sync_masking_forecast_orders_to_non_live", p.DebugSyncMfOrders)
	DebugRouting.POST("/get_sync_data", p.GetSyncData)
	DebugRouting.POST("/test_balance", p.TestBalance)
	DebugRouting.POST("/batch_allocate_forecast", p.BatchAllocateForecast)
	DebugRouting.POST("/batch_allocate_create_sub_task", p.BatchAllocateCreateSubTask)
	DebugRouting.POST("/allocate_history_outline", p.DebugAllocateHistoryOutLine)
	DebugRouting.POST("/get_sls_ops_s3", p.DebugGetSlsOpsS3)
	DebugRouting.POST("/parse_batch_volume", p.AnalyzeBatchVolume)
	DebugRouting.POST("/clear_masking_volume", p.DebugClearMaskingVolume)
	DebugRouting.POST("/sync_masking_volume", p.DebugSyncMaskingVolume)

	DebugRouting.POST("/delete_hard_criteria_task", p.DeleteHardCriteriaTaskByTask)
	DebugRouting.POST("/delete_forecast_task", p.DeleteForecastTaskById)
	DebugRouting.POST("/debug_smart_routing_forecast", p.DebugSmartRoutingForecast)
	DebugRouting.POST("/ilh_forecast", p.ILHForecast)
	DebugRouting.POST("/allocate_forecast", p.AllocateForecast)
	DebugRouting.POST("/get_masking_hbase_data", p.GetMaskingHbaseData)
	DebugRouting.POST("/refresh_sls_ops_url", p.UpdateS3Url)
	DebugRouting.POST("/report_masking_volume", p.ReportMaskingVolume)
	DebugRouting.POST("/update_batch_allocate_forecast_task", p.UpdateBAForecastTask)
	DebugRouting.POST("/allocate_schedule_visual", p.AllocateScheduleVisual)
	DebugRouting.POST("/batch_allocate_forecast_tool_progress", p.BAForecastToolProgress)
	DebugRouting.POST("/split_batch", p.SplitBatch)
	DebugRouting.POST("/batch_allocate", p.BatchAllocate)
	DebugRouting.POST("/abnormal_batch_allocate", p.AbnormalBatchAllocate)
	DebugRouting.POST("/batch_abnormal_inspection", p.BatchAbnormalInspection)
	DebugRouting.POST("/push_order_result", p.PushOrderResult)
	DebugRouting.POST("/hold_order", p.HoldOrder)
	DebugRouting.POST("/clear_order_and_result", p.ClearOrderAndResult)
	DebugRouting.POST("/report_order_count", p.ReportOrderCount)
	DebugRouting.POST("/schedule_rule", p.ScheduleRule)
	DebugRouting.POST("/pull_async_allocate_results", p.BatchGetAllocateOrderResult)
	DebugRouting.POST("/batch_allocate_monitor", p.BatchAllocateMonitor)
	DebugRouting.GET("/get_routing_log_hbase_data", p.GetRoutingLogHbaseData)
	DebugRouting.POST("/checkout_masking_count", p.CheckoutMaskingCount)
	DebugRouting.POST("/deduct_volume", p.DeductVolume)
	DebugRouting.POST("/AllocateVolumeCounter", p.AllocateVolumeCounter)
	DebugRouting.POST("/make_up_async_allocate_log", p.DebugMakeUpAsyncAllocationLog)

	return adminGroup.GetRouters()
}

func (p *DebugFacade) AllocateForecast(ctx *restful.Context) {
	maskForecastReq := schema.MaskForecastReq{}
	if err := ctx.ReadEntity(&maskForecastReq); err != nil {
		apiutil.FailJSONResp(ctx, nil, err.Error())
	}
	softRuleService := allocation2.NewSoftRuleService(
		forecast_volume.NewAllocateForecastVolumeCounterImpl(),
		chargeclient.NewChargeApiImpl(),
		whitelist.NewShopWhitelistServiceImpl(whitelist2.NewShopWhitelistRepoImpl()),
	)
	allocate, err := p.AllocateForecastService.ForecastAllocate(ctx.Ctx, maskForecastReq.OrderData, maskForecastReq.TaskConfig, maskForecastReq.MaskRule, maskForecastReq.MaskConfig, maskForecastReq.ProductInfo, softRuleService)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, allocate)
}

func (p *DebugFacade) DebugGetCounter(ctx *restful.Context) {
	param := new(schema.DebugGetCounter)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	v, err := redisutil.GetInt64(ctx.Ctx, param.Key)
	if err != nil {
		logger.CtxLogErrorf(ctx.Ctx, "get-counter failed key=%v", param.Key)
		hErr := srerr.New(srerr.CodisErr, "", err.Error())
		apiutil.FailJSONResp(ctx, hErr, err.Error())
		return
	}
	ret := map[string]int64{param.Key: v}
	apiutil.SuccessJSONResp(ctx, ret)
}

const (
	MaxSize               = 400
	SecondsInOneDay int64 = 3600 * 24
)

func (p *DebugFacade) RecoverRedisCounter(ctx *restful.Context) {
	param := new(schema.RecoverRedisCounter)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	db, dbErr := dbutil.SlaveDB(ctx.Ctx, rc.CounterTabHook)
	if dbErr != nil {
		logger.LogErrorf("redis_counter, get %s error", rc.CounterTabHook.TableName())
		apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", dbErr.Error()), "redis_counter get db error")
		return
	}
	minTick := recorder.Now(ctx.Ctx).Unix() - (param.Days+1)*SecondsInOneDay
	var totalCount int64 = 0
	countDbErr := db.Table(rc.CounterTabHook.TableName()).Where("mtime > ?", minTick).Count(&totalCount).GetError()
	if countDbErr != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", countDbErr.Error()), "redis_counter get db count error")
		return
	}
	successCount := 0
	for offset := 0; offset < int(totalCount); offset = offset + MaxSize {
		counterList := make([]rc.CounterTab, 0)
		dbErr = db.Where("mtime > ?", minTick).Order("mtime DESC").Offset(offset).Limit(MaxSize).Find(&counterList).GetError()
		if dbErr != nil {
			apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", dbErr.Error()), "redis_counter get db info error")
			return
		}
		for _, counter := range counterList {
			var err error
			switch counter.Type {
			case rc.Integer:
				err = redisutil.Set(ctx.Ctx, counter.Key, counter.Value, 0)
			case rc.Hash:
				err = redisutil.HSet(ctx.Ctx, counter.Key, counter.Field, counter.Value)
			}
			if err != nil {
				errStr := fmt.Sprintf("get counter_tab list:%d,%s,%d", counter.Type, counter.Key, counter.Value)
				apiutil.FailJSONResp(ctx, srerr.New(srerr.RedisErr, "", errStr), "")
				return
			}
			successCount = successCount + 1
		}
	}

	rsp := make(map[string]interface{})
	rsp["total_count"] = totalCount
	rsp["success_count"] = successCount
	apiutil.SuccessJSONResp(ctx, rsp)
}

const (
	incrKey    = "test_incr_key"
	hIncrKey   = "test_hincr_key"
	hIncrField = "test_hincr_field"
)

func (p *DebugFacade) RedisCounterOption(ctx *restful.Context) {
	param := new(schema.RedisCounterOption)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	var count int64 = 0
	var err error
	switch param.Option {
	case schema.Incr:
		count, err = p.RedisCounter.Incr(ctx.Ctx, incrKey)
	case schema.Decr:
		count, err = p.RedisCounter.Decr(ctx.Ctx, incrKey)
	case schema.IncrBy:
		count, err = p.RedisCounter.IncrBy(ctx.Ctx, incrKey, param.Count)
	case schema.HIncrBy:
		count, err = p.RedisCounter.HIncrBy(ctx.Ctx, hIncrKey, hIncrField, param.Count)
	}
	if err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.CodisErr, nil, err), err.Error())
		return
	}
	rsp := make(map[string]interface{})
	rsp["total_count"] = count
	apiutil.SuccessJSONResp(ctx, rsp)
}

type (
	LocationReq struct {
		Country  string `json:"country"`
		State    string `json:"state"`
		City     string `json:"city"`
		District string `json:"district"`
		Street   string `json:"street"`

		LocationId int64 `json:"location_id"`
		MinLayer   int   `json:"min_layer"`
	}
)

func (p *DebugFacade) DBGetLocationById(ctx *restful.Context) {
	req := &LocationReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.Location.GetLocationByLocId(ctx.Ctx, req.LocationId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *DebugFacade) DBGetLocationByName(ctx *restful.Context) {
	req := &LocationReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.Location.GetLocationByLocName(ctx.Ctx, req.Country, req.State, req.City, req.District, req.Street)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *DebugFacade) DBRateLimiter(ctx *restful.Context) {
	param := new(schema.RecoverRedisCounter)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.LpsApi.GetLineDictList(ctx.Ctx, param.Days)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *DebugFacade) TestGetShippingFee(ctx *restful.Context) {
	req := chargeentity.BatchForecastAllocationESFReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.RateClient.BatchForecastAllocatingESF(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *DebugFacade) DebugLoadForecastVolumeConfig(ctx *restful.Context) {
	param := &allocation.AllocateForecastTaskConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	reply := p.LoadForecastVolumeConfig.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{
		MsgText: []byte(str.JsonString(param)),
	})

	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DebugAllocateDataStore(ctx *restful.Context) {
	param := &masking.AllocateStoreMsg{}
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	reply := p.AllocateDataStore.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{
		MsgText: []byte(str.JsonString(param)),
	})

	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DebugMaskingForecast(ctx *restful.Context) {
	//reply := p.MaskingForecast.RpcHandle(ctx.Ctx, saturn.JobArgs{})

	tabs, err := p.LpsApi.GetProductBaseInfoList(ctx.Ctx)

	if err != nil {
		apiutil.FailJSONResp(ctx, err, "")
		return
	}

	apiutil.SuccessJSONResp(ctx, tabs)
}

func (p *DebugFacade) DebugAuditLogReport(ctx *restful.Context) {
	reply := p.AuditLogTaskServer.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) CheckMaskProcessSubTask(ctx *restful.Context) {
	reply := p.CheckMaskingProcessTaskImpl.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DeleteMaskExpiredSubTask(ctx *restful.Context) {
	reply := p.DeleteMaskingSubTaskImpl.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) GetForecastTotalCount(ctx *restful.Context) {
	reply := p.GetForecastTotalCountImpl.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) AllocateStoreMsgIntoMain(ctx *restful.Context) {
	uniqKey := uuid.New().String() // nolint
	maskingLogDetail := allocation.LogDetail{
		RequestId:     uniqKey,
		MaskProductId: 5000,
		RequestTime:   recorder.Now(ctx.Ctx).UnixNano() / 1e6,
	}
	uniBytes, _ := jsoniter.Marshal(maskingLogDetail)
	compressBytes := zip.ZSTDCompress(uniBytes)
	reply := p.AllocateStoreHbaseConsumer.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{MsgKey: uniqKey, MsgText: compressBytes})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DeleteHardCriteriaTaskByTask(ctx *restful.Context) {
	var req schema.DeleteHardCriteriaTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := p.ForecastTaskSrv.DeleteHardCriteriaByTaskId(ctx.Ctx, req.TaskId); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DeleteForecastTaskById(ctx *restful.Context) {
	var req schema.DeleteForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := p.ForecastTaskSrv.DeleteForecastTaskByTaskId(ctx.Ctx, req.TaskId); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DebugSmartRoutingForecast(ctx *restful.Context) {
	var req schema.DebugSmartRoutingForecastReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	newCtx := requestid.NewCtx(ctx.Ctx)
	go func() {
		if err := p.SmartRoutingForecastSrv.DebugStartForecast(newCtx, req); err != nil {
			logger.CtxLogErrorf(newCtx, "debug smart routing forecast failed | err: %v", err)
		}
	}()

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) ILHForecast(ctx *restful.Context) {
	newCtx := requestid.NewCtx(ctx.Ctx)
	go func() {
		if err := p.ILHForecastTask.RpcHandle(newCtx, saturn.JobArgs{}); err != nil {
			logger.CtxLogErrorf(newCtx, "ilh forecast task failed | err: %v", err)
		}
	}()

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) GetMaskingHbaseData(ctx *restful.Context) {
	var req schema.GetMaskingHbaseDataReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	rowKey := masking.GenRowKey(req.RequestTime, req.MaskProductId, req.RequestId)
	result, err := p.AllocateOrderDataRepo.GetAllocateOrderFromHbase(ctx.Ctx, rowKey)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, result)
}

func (p *DebugFacade) GetRoutingLogHbaseData(ctx *restful.Context) {
	var req schema.GetRoutingLogHbaseDataReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	var (
		newCtx    = requestid.NewCtx(ctx.Ctx)
		rowKey    = req.RowKey
		tableName = configutil.GetDataHbaseConfig(newCtx).TableNameMap[masking_forecast_hbase.ROUTING_LOG]
		hbHelper  = masking_forecast_hbase.NewHBHelper()
	)

	result, err := hbHelper.GetDataFromHbaseByRowKey(newCtx, tableName, rowKey)
	if err != nil {
		logger.CtxLogErrorf(newCtx, "get row from hbase  err:%v", err)
		apiutil.FailJSONResp(ctx, srerr.With(srerr.GetHbaseError, rowKey, err), err.Error())
		return
	}

	decodeBytes, err := zip.ZSTDDecompress(result)
	if err != nil {
		logger.CtxLogErrorf(newCtx, "decompress data from fail |key:%v, err:%v", rowKey, err)
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ZstdErr, rowKey, err), err.Error())
		return
	}

	var routingLog routing_log.RoutingLog
	if err := jsoniter.Unmarshal(decodeBytes, &routingLog); err != nil {
		logger.CtxLogErrorf(newCtx, "unmarshal routing log fail: %v", err)
		apiutil.FailJSONResp(ctx, srerr.With(srerr.JsonErr, rowKey, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, routingLog)
}

func (p *DebugFacade) UpdateS3Url(ctx *restful.Context) {
	var req schema.RefreshS3UrlReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	url := fileutil.GetS3UrlForSlsOps(ctx.Ctx, req.Url)
	apiutil.SuccessJSONResp(ctx, url)
}

func (p *DebugFacade) ReportMaskingVolume(ctx *restful.Context) {
	_ = p.ReportMaskingVolumeTask.RpcHandle(ctx.Ctx, saturn.JobArgs{})

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DebugSyncMfOrders(ctx *restful.Context) {
	req := sync_data_schema.SyncMfOrdersReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//异步执行，执行完毕后上报cat，dev通过cat指标查看是否执行完毕
	go func() {
		sErr := p.SyncDataService.SyncMaskingForecastOrders(ctx.Ctx, req)
		if sErr != nil {
			monitoring.ReportError(ctx.Ctx, monitoring.CatModuleMaskingForecast, monitoring.SyncData, fmt.Sprintf("sync data error:%v", sErr))
			return
		}
		monitoring.ReportSuccess(ctx.Ctx, monitoring.CatModuleMaskingForecast, monitoring.SyncData, "success")
	}()
	apiutil.SuccessJSONResp(ctx, "success")
}

func (p *DebugFacade) GetSyncData(ctx *restful.Context) {
	req := sync_data_schema.GetSyncDataReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	_ = p.SyncDataService.GetSyncData(ctx.Ctx, req)
	apiutil.SuccessJSONResp(ctx, "success")
}

func (p *DebugFacade) TestBalance(ctx *restful.Context) {
	req := sync_data_schema.TestBalanceReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.SyncDataService.TestBalance(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, "success")
}

func (p *DebugFacade) BatchAllocateForecast(ctx *restful.Context) {
	reply := p.BatchAllocateService.RpcHandle(ctx.Ctx, saturn.JobArgs{
		TotalShardings: 1,
	})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) BatchAllocateCreateSubTask(ctx *restful.Context) {
	reply := p.CreateBASubTask.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DebugAllocateHistoryOutLine(ctx *restful.Context) {
	reply := p.AllocateHistoryOutLine.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) DebugGetSlsOpsS3(ctx *restful.Context) {
	req := debug.GetSlsOpsUrlReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	url := fileutil.GetS3UrlForSlsOps(ctx.Ctx, req.Url)
	apiutil.SuccessJSONResp(ctx, url)
}

func (p *DebugFacade) AnalyzeBatchVolume(ctx *restful.Context) {
	reply := p.ParseBatchVolumeService.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) UpdateBAForecastTask(ctx *restful.Context) {
	reply := p.UpdateBatchAllocateForecastTaskImpl.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *DebugFacade) AllocateScheduleVisual(ctx *restful.Context) {
	req := new(schema.AllocateScheduleVisualReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	msg := &saturn.SaturnMessage{
		MsgText:  zip.ZSTDCompress([]byte(req.MsgText)),
		OffsetID: req.OffsetId,
	}
	switch req.BusinessType {
	case schedule_stat.AllocateV2:
		p.AllocateScheduleVisualTask.MsgHandle(ctx.Ctx, msg)
	case schedule_stat.Allocate:
		p.ScheduleCountStatTask.MsgHandle(ctx.Ctx, msg)
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) BAForecastToolProgress(ctx *restful.Context) {
	err := p.BAForecastToolProgressService.RpcHandle(ctx.Ctx, saturn.JobArgs{})
	if err != nil {
		rErr := srerr.With(srerr.DatabaseErr, nil, err)
		apiutil.FailJSONResp(ctx, rErr, rErr.Error())
		return
	} else {
		apiutil.SuccessJSONResp(ctx, nil)
	}
}

func (p *DebugFacade) SplitBatch(ctx *restful.Context) {
	err := p.SplitBatchAllocateOrdersTask.RpcHandle(ctx.Ctx, saturn.JobArgs{
		ShardingParam: "8005",
	})
	if err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, nil)
	}
}

func (p *DebugFacade) BatchAllocate(ctx *restful.Context) {
	var req schema.ExecuteBatchAllocateReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := p.BatchAllocateTask.RpcHandle(ctx.Ctx, saturn.JobArgs{ShardingNo: req.ShardingNo}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) AbnormalBatchAllocate(ctx *restful.Context) {
	var req schema.ExecuteBatchAllocateReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := p.AbnormalBatchAllocateTask.RpcHandle(ctx.Ctx, saturn.JobArgs{ShardingNo: req.ShardingNo}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) BatchAbnormalInspection(ctx *restful.Context) {
	if err := p.BatchAbnormalInspectionTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) PushOrderResult(ctx *restful.Context) {
	if err := p.PushOrderResultTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) HoldOrder(ctx *restful.Context) {
	req := &order.BatchAllocateHoldOrderTab{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	// 数据发送至Kafka
	bytes, mErr := jsoniter.Marshal(req)
	if mErr != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, mErr), mErr.Error())
		return
	}
	msg := &saturn.SaturnMessage{MsgText: bytes}
	ret := p.BatchAllocateHoldOrderConsumer.MsgHandle(ctx.Ctx, msg)
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *DebugFacade) ClearOrderAndResult(ctx *restful.Context) {
	if err := p.ClearOrderAndResultTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) ReportOrderCount(ctx *restful.Context) {
	if err := p.ReportOrderCountTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) ScheduleRule(ctx *restful.Context) {
	if err := p.ScheduleRuleTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) BatchGetAllocateOrderResult(ctx *restful.Context) {
	var (
		resp = &pb.BatchGetAllocateOrderResultResp{Header: grpc_util.GenerateRespHeader(uuid.New().String(), nil)} // nolint
		req  = &schema.BatchPullOrdersReq{}
	)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	dateUnix := timeutil.GetStartTimeByString(req.Date)
	date := timeutil.ConvertTimeStampToTimeByCountry(dateUnix, req.Region)

	// 从T中获取
	result, err := p.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx.Ctx, req.GetOrderIdList(), date.Day())
	if err != nil {
		logger.CtxLogErrorf(ctx.Ctx, "BatchGetOrderResultByOrderID failed: %v", err)
	}

	// 如果从T中已经获取到的就不需要再从T-1获取了（如果两边都有就是以T为准）
	var remainOrderIDList []uint64
	if len(result) == 0 {
		remainOrderIDList = req.GetOrderIdList()
	} else {
		foundOrderIDMap := make(map[uint64]struct{}, len(result))
		for _, r := range result {
			foundOrderIDMap[r.OrderID] = struct{}{}
		}
		for _, o := range req.GetOrderIdList() {
			if _, exist := foundOrderIDMap[o]; exist {
				continue
			}
			remainOrderIDList = append(remainOrderIDList, o)
		}
	}

	// 从T-1中获取
	yesterdayResult, err := p.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx.Ctx, remainOrderIDList, date.AddDate(0, 0, -1).Day())
	if err != nil {
		logger.CtxLogErrorf(ctx.Ctx, "BatchGetOrderResultByOrderID failed: %v", err)
	}

	// 正常能获取到的订单
	result = append(result, yesterdayResult...)
	successOrders := make(map[uint64]struct{}, len(result))
	for _, r := range result {
		successOrders[r.OrderID] = struct{}{}

		var (
			// 模拟push流程
			retcode, _, message    = order.GetOrderStatus(ctx.Ctx, r)
			fulfillmentShippingFee float64 //默认为0，只有正常调度完成且不报错的单，才复制shipping fee
		)

		// 注意这里返回的运费可能为-1，由于是debug接口，因此不做处理，尽量返回全量数据
		fulfillmentShippingFee = r.FulfillmentShippingFeeInfo.FulfillmentShippingFee

		resp.OrderResultList = append(resp.OrderResultList, &pb.OrderResultUnit{
			Retcode:                proto.Int(retcode),
			Message:                proto.String(message),
			OrderId:                proto.Uint64(r.OrderID),
			FulfillmentProductId:   proto.Int(r.AllocateResult),
			FulfillmentShippingFee: proto.Float64(fulfillmentShippingFee),
		})
	}

	// 无法获取的订单
	for _, o := range req.GetOrderIdList() {
		if _, exist := successOrders[o]; exist {
			continue
		}

		resp.OrderResultList = append(resp.OrderResultList, &pb.OrderResultUnit{
			Retcode: proto.Int(srerr.OrderResultNotFoundError.Code()),
			Message: proto.String(srerr.OrderResultNotFoundError.Msg()),
			OrderId: proto.Uint64(o),
		})
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *DebugFacade) BatchAllocateMonitor(ctx *restful.Context) {
	if err := p.BatchAllocateMonitorTask.RpcHandle(ctx.Ctx, saturn.JobArgs{}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) CheckoutMaskingCount(ctx *restful.Context) {
	var req schema.CheckoutMaskingCountReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	record := msgPb.MsgBusMessage{}
	newOFOrderStatusEvent := orderPb.OFOrderStatusEvent{}
	newExtInfo := orderPb.OFOrderStatusEventExtInfo{}
	oldOFOrderStatusEvent := orderPb.OFOrderStatusEvent{}
	oldExtInfo := orderPb.OFOrderStatusEventExtInfo{}
	if err := jsoniter.UnmarshalFromString(req.MsgBusMessage, &record); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	if err := jsoniter.UnmarshalFromString(req.NewOFOrderStatusEvent, &newOFOrderStatusEvent); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	if err := jsoniter.UnmarshalFromString(req.NewOFOrderStatusEventExtInfo, &newExtInfo); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	if err := jsoniter.UnmarshalFromString(req.OldOFOrderStatusEvent, &oldOFOrderStatusEvent); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	if err := jsoniter.UnmarshalFromString(req.OldOFOrderStatusEventExtInfo, &oldExtInfo); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	newExtInfoPb, _ := proto.Marshal(&newExtInfo)
	newOFOrderStatusEvent.Extinfo = newExtInfoPb
	newOFOrderStatusEventPb, _ := proto.Marshal(&newOFOrderStatusEvent)
	oldExtInfoPb, _ := proto.Marshal(&oldExtInfo)
	oldOFOrderStatusEvent.Extinfo = oldExtInfoPb
	oldOFOrderStatusEventPb, _ := proto.Marshal(&oldOFOrderStatusEvent)
	record.Payload.NewData = newOFOrderStatusEventPb
	record.Payload.OldData = oldOFOrderStatusEventPb
	recordPb, _ := proto.Marshal(&record)

	reply := p.CheckoutFulfillmentProductCounter.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{
		MsgText: recordPb,
	})
	if reply.Retcode != 0 {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, fmt.Errorf("checkout masking count error: %d", reply.Retcode)), reply.Message)
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DeductVolume(ctx *restful.Context) {
	var req schema.CheckoutMaskingCountReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	record := msgPb.MsgBusMessage{}
	newOFOrderStatusEvent := orderPb.OFOrderStatusEvent{}
	newExtInfo := orderPb.OFOrderStatusEventExtInfo{}
	oldOFOrderStatusEvent := orderPb.OFOrderStatusEvent{}
	oldExtInfo := orderPb.OFOrderStatusEventExtInfo{}
	_ = jsoniter.UnmarshalFromString(req.MsgBusMessage, &record)
	_ = jsoniter.UnmarshalFromString(req.NewOFOrderStatusEvent, &newOFOrderStatusEvent)
	_ = jsoniter.UnmarshalFromString(req.NewOFOrderStatusEventExtInfo, &newExtInfo)
	_ = jsoniter.UnmarshalFromString(req.OldOFOrderStatusEvent, &oldOFOrderStatusEvent)
	_ = jsoniter.UnmarshalFromString(req.OldOFOrderStatusEventExtInfo, &oldExtInfo)
	newExtInfoPb, _ := proto.Marshal(&newExtInfo)
	newOFOrderStatusEvent.Extinfo = newExtInfoPb
	newOFOrderStatusEventPb, _ := proto.Marshal(&newOFOrderStatusEvent)
	oldExtInfoPb, _ := proto.Marshal(&oldExtInfo)
	oldOFOrderStatusEvent.Extinfo = oldExtInfoPb
	oldOFOrderStatusEventPb, _ := proto.Marshal(&oldOFOrderStatusEvent)
	if record.Payload != nil {
		record.Payload.NewData = newOFOrderStatusEventPb
		record.Payload.OldData = oldOFOrderStatusEventPb
	}
	recordPb, _ := proto.Marshal(&record)

	reply := p.DeductVolumeCounter.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{
		MsgText: recordPb,
	})
	if reply.Retcode != 0 {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, fmt.Errorf("checkout masking count error: %d", reply.Retcode)), reply.Message)
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) AllocateVolumeCounter(ctx *restful.Context) {
	var req allocate_volume_counter.DeductVolumeParam
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.DeductVolumeCounter.AllocateVolumeCounter(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DebugMakeUpAsyncAllocationLog(ctx *restful.Context) {
	var req schema.DebugMakeUpAsyncAllocationLogReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	// 转成结构体
	tempLog := &allocation2.TempAllocationLog{}
	if err := jsoniter.UnmarshalFromString(req.MsgText, tempLog); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	if tempLog.Log != nil {
		tempLog.Log.OrderListBytes = []byte(req.OrderList)
	}
	// 结构体转成msg
	tempLogStr, err := jsoniter.Marshal(tempLog)
	if err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.JsonErr, nil, ""), err.Error())
		return
	}
	dcStr := zip.ZSTDCompress(tempLogStr)
	reply := p.MakeUpAsyncAllocationLog.MsgHandle(ctx.Ctx, &saturn.SaturnMessage{
		MsgText: dcStr,
	})
	if reply != nil && reply.Retcode != 0 {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, fmt.Errorf("checkout masking count error: %d", reply.Retcode)), reply.Message)
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DebugClearMaskingVolume(ctx *restful.Context) {
	var req schema.DebugClearMaskingVolumeReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.ClearMaskingVolumeTask.RpcHandle(ctx.Ctx, saturn.JobArgs{ShardingParam: req.ShardingParam}); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *DebugFacade) DebugSyncMaskingVolume(ctx *restful.Context) {
	var req schema.DebugSyncMaskingVolumeReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if len(req.StartDay) != len(req.EndDay) || len(req.StartDay) != len(repository.VolumeDashboardDayFormat) {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, fmt.Errorf("StartDay and EndDay should like 20060102")), "StartDay and EndDay should like 20060102")
		return
	}
	if err := p.ClearMaskingVolumeTask.MaskingVolumeService.SyncMaskingVolume(ctx.Ctx, req.StartDay, req.EndDay, req.NewToOld); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, nil, err), err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}
