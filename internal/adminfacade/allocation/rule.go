package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/masking_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type MaskingRuleFacade struct {
	RuleRepo                    rule.IMaskRuleRepo
	VolumeCounter               volumecounter.MaskVolumeCounter
	RuleConfRepo                rule.MaskRuleConfRepo
	ProdRepo                    product.ProdRepo
	MaskConfigRepo              config.IMaskConfigRepo
	LpsApi                      lpsclient.LpsApi
	ShopWhitelistService        whitelist.ShopWhitelistService
	PickupPriorityService       masking_priority.PickupPriorityService
	ParcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
}

func (p *MaskingRuleFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	AllocationRule := adminGroup.Group("/allocation")
	AllocationRule.POST("/create_rule", p.CreateRule)
	AllocationRule.GET("/get_rule_simple_info", p.GetRuleSimpleInfo)
	AllocationRule.POST("/get_rule_conf", p.GetRuleConf)
	AllocationRule.POST("/update_rule", p.UpdateRule)
	AllocationRule.POST("/delete_rule", p.DeleteRule)
	AllocationRule.POST("/copy_rule", p.CopyRule)
	AllocationRule.POST("/list_rules", p.GetRuleList)
	AllocationRule.POST("/get_rule", p.GetRuleById)
	AllocationRule.POST("/create_config", p.CreateConfig)
	AllocationRule.POST("/update_config", p.UpdateConfig)
	AllocationRule.POST("/list_configs", p.ListConfigs)
	AllocationRule.POST("/get_config", p.GetConfig)
	AllocationRule.POST("/disable_batch_rule", p.DisableBatchRule)

	// pickup efficiency whitelist
	pickupEfficiency := AllocationRule.Group("/pickup_efficiency")
	pickupEfficiency.POST("/upload_shop", p.UploadShop)
	pickupEfficiency.POST("/list_shop", p.ListShopWhitelist)
	pickupEfficiency.POST("/export_shop", p.ExportShopWhitelist)
	pickupEfficiency.POST("/list_priority", p.ListPriority)
	pickupEfficiency.POST("/create_priority", p.CreatePriority)
	pickupEfficiency.POST("/delete_priority", p.DeletePriority)
	pickupEfficiency.GET("/detail_priority", p.PriorityDetail)
	pickupEfficiency.POST("/update_priority", p.UpdatePriority)

	// bulky / high value definition
	parcelTypeDefinition := AllocationRule.Group("/parcel_type_definition")
	parcelTypeDefinition.POST("/create", p.CreateParcelTypeDefinition)
	parcelTypeDefinition.POST("/update", p.UpdateParcelTypeDefinition)
	parcelTypeDefinition.GET("/get", p.GetParcelTypeDefinition)
	parcelTypeDefinition.GET("/list", p.ListParcelTypeDefinition)
	parcelTypeDefinition.POST("/delete", p.DeleteParcelTypeDefinition)

	return adminGroup.GetRouters()
}

func (p *MaskingRuleFacade) GetRuleById(ctx *restful.Context) {
	var req = &schema.GetMaskRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	r, err := p.RuleRepo.GetRule(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.convertRuleToAdminData(ctx.Ctx, r)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingRuleFacade) GetRuleConf(ctx *restful.Context) {
	req := &schema.GetRuleCfg{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	logger.CtxLogDebugf(ctx.Ctx, "check.cfg ", req.RuleMode)
	cnf, err := p.RuleConfRepo.GetRuleConf(ctx.Ctx, rule_mode.RuleMode(req.RuleMode))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	ret := &schema.RuleConf{}
	for _, st := range cnf.Steps {
		ret.Steps = append(ret.Steps, &schema.RuleConfStep{
			Step:     int32(st.Step),
			Disabled: st.Disabled,
		})
	}
	ret.DisableProductPriority = cnf.DisableProductPriority

	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *MaskingRuleFacade) CreateRule(ctx *restful.Context) {
	var req = &schema.CreateRuleRequest{}
	if err := ctx.ReadEntity(req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	if req.RuleMode == 0 {
		//兼容BE FE 发布间隔期
		req.RuleMode = int32(rule_mode.MplOrderRule)
	}

	r, err := p.RuleRepo.CreateRule(ctx.Ctx, req.RuleName, int64(req.MaskProductId), req.RuleMode, req.AllocationMethod)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	result, err := p.convertRuleToAdminData(ctx.Ctx, r)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingRuleFacade) UpdateRule(ctx *restful.Context) {
	var req = &schema.UpdateRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	var err error
	if req.AllocationMethod != allocation.BatchAllocate {
		err = req.Validate()
	} else {
		err = req.ValidateBatch(ctx.Ctx)
	}
	if err != nil {
		vErr := srerr.With(srerr.ParamErr, "validate request, ", err)
		apiutil.FailJSONResp(ctx, vErr, vErr.Error())
		return
	}

	//ruleData := convertRuleData(req)
	r := convertAdminDataToRule(req)
	if req.EffectiveImmediately {
		r.EffectiveStartTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx.Ctx))
	}
	ret, uErr := p.RuleRepo.UpdateRule(ctx.Ctx, r)
	if uErr != nil {
		apiutil.FailJSONResp(ctx, uErr, uErr.Error())
		return
	}

	result, cErr := p.convertRuleToAdminData(ctx.Ctx, ret)
	if cErr != nil {
		apiutil.FailJSONResp(ctx, cErr, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingRuleFacade) DeleteRule(ctx *restful.Context) {
	var req = &schema.DeleteRuleRequest{}
	if err := ctx.ReadEntity(req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	if err := p.RuleRepo.DeleteRule(ctx.Ctx, int64(req.Id)); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) CopyRule(ctx *restful.Context) {
	var req = &schema.CopyRuleRequest{}
	if err := ctx.ReadEntity(req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	if err := p.RuleRepo.CopyRule(ctx.Ctx, int64(req.Id)); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) GetRuleList(ctx *restful.Context) {
	var req = &schema.ListRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	q := rule.NewRuleQuery()
	if req.GetFilterById() {
		q = q.ByID(int64(req.GetId()))
	}
	if req.GetFilterByMaskProductId() {
		q = q.ByMaskProductID(int64(req.GetMaskProductId()))
	}
	if req.GetFilterByStatus() {
		q = q.ByStatus(rule.MaskRuleStatus(req.GetStatus()))
	}
	q = q.WithAllocationMethod(req.AllocationMethod)
	q = q.WithPage(req.GetPageno(), req.GetLimit()).WithRuleMode(req.RuleMode)
	rules, total, err := p.RuleRepo.ListRules(ctx.Ctx, q)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := &schema.ListRuleReply{
		Pageno: req.GetPageno(),
		Count:  req.GetLimit(),
		Total:  total,
	}
	for _, r := range rules {
		result, err := p.convertRuleToAdminData(ctx.Ctx, r)
		if err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}
		resp.List = append(resp.List, result)
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) GetRuleSimpleInfo(ctx *restful.Context) {
	infos, err := p.RuleRepo.GetRuleSimpleInfo(ctx.Ctx)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	var ruleSimpleInfos = make([]*schema.RuleSimpleInfo, len(infos))
	for i, info := range infos {
		ruleSimpleInfos[i] = &schema.RuleSimpleInfo{
			Id:   int32(info.Id),
			Name: info.Name,
		}
	}
	var reply = &schema.GetRulesNameAndIdResponse{RuleSimpleInfo: ruleSimpleInfos}
	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *MaskingRuleFacade) CreateConfig(ctx *restful.Context) {
	var req = &schema.CreateConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	maskProductInfo, err := p.LpsApi.GetProductDetail(ctx.Ctx, int(req.MaskProductId))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	allocationConfig, err := p.MaskConfigRepo.CreateConfig(ctx.Ctx, int64(req.MaskProductId), maskProductInfo.GetComponentProduct().MaskingType,
		req.SmartAllocationEnabled, int64(req.DefaultProduct), int64(req.DefaultWmsProduct))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := p.convertConfigToAdminData(ctx.Ctx, allocationConfig)
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) UpdateConfig(ctx *restful.Context) {
	var req = &schema.UpdateConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	maskProductInfo, err := p.LpsApi.GetProductDetail(ctx.Ctx, int(req.MaskProductId))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	allocationConfig, err := p.MaskConfigRepo.UpdateConfig(ctx.Ctx, int64(req.MaskProductId), maskProductInfo.GetComponentProduct().MaskingType,
		req.SmartAllocationEnabled, int64(req.DefaultProduct), int64(req.DefaultWmsProduct))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := p.convertConfigToAdminData(ctx.Ctx, allocationConfig)
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) ListConfigs(ctx *restful.Context) {
	var req = &schema.ListConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	var maskProductID int64 = 0
	if req.FilterByMaskProductId {
		maskProductID = int64(req.MaskProductId)
	}

	var maskProductType = 0
	if req.FilterByMaskType {
		maskProductType = int(req.MaskType)
	}

	allocationConfigs, total, sErr := p.MaskConfigRepo.ListAllConfigs(ctx.Ctx,
		(req.Pageno-1)*req.Limit, req.Limit, maskProductID, maskProductType)

	if sErr != nil {
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	var reply = &schema.ListConfigReply{
		List:   make([]*schema.AllocationConfig, len(allocationConfigs)),
		Pageno: req.Pageno,
		Total:  total,
		Count:  req.Limit,
	}

	for i, allocationConfig := range allocationConfigs {
		resp := p.convertConfigToAdminData(ctx.Ctx, allocationConfig)
		reply.List[i] = resp
	}

	apiutil.SuccessJSONResp(ctx, reply)
}

func (p *MaskingRuleFacade) GetConfig(ctx *restful.Context) {
	var req = &schema.GetConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	allocationConfig, gErr := p.MaskConfigRepo.GetConfig(ctx.Ctx, int64(req.Id))
	if gErr != nil {
		apiutil.FailJSONResp(ctx, gErr, gErr.Error())
		return
	}
	resp := p.convertConfigToAdminData(ctx.Ctx, allocationConfig)
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) DisableBatchRule(ctx *restful.Context) {
	var req = &schema.GetMaskRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}

	dErr := p.RuleRepo.DisableBatchRule(ctx.Ctx, req.Id)
	if dErr != nil {
		apiutil.FailJSONResp(ctx, dErr, dErr.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) convertConfigToAdminData(ctx context.Context, config *config.AllocationConfig,
) *schema.AllocationConfig {

	defaultProductEntity, gMaskErr := p.LpsApi.GetProductDetail(ctx, int(config.MaskProductID))
	var defaultMaskProductName string
	var defaultProductType int
	if gMaskErr != nil {
		defaultMaskProductName = ""
		defaultProductType = 0
	} else {
		defaultMaskProductName = defaultProductEntity.SellerDisplayName
		defaultProductType = defaultProductEntity.GetComponentProduct().MaskingType
	}
	defaultWmsProduct, gWmsErr := p.LpsApi.GetProductDetail(ctx, int(config.DefaultWmsProduct))
	defaultWmsProductName := ""
	if gWmsErr == nil {
		defaultWmsProductName = defaultWmsProduct.SellerDisplayName
	}
	defaultProduct, gDefaultErr := p.LpsApi.GetProductDetail(ctx, int(config.DefaultProduct))
	defaultProductName := ""
	if gDefaultErr == nil {
		defaultProductName = defaultProduct.SellerDisplayName
	}

	return &schema.AllocationConfig{
		Id: int32(config.ID),
		MaskProduct: &schema.MaskProduct{
			Id:       int32(config.MaskProductID),
			Name:     defaultMaskProductName,
			MaskType: int32(defaultProductType),
		},
		SmartAllocationEnabled: config.SmartAllocationEnabled,
		DefaultProduct: &schema.Product{
			Id:   int32(config.DefaultProduct),
			Name: defaultProductName,
		},
		DefaultWmsProduct: &schema.Product{
			Id:   int32(config.DefaultWmsProduct),
			Name: defaultWmsProductName,
		},
		OperatedBy: config.OperatedBy,
		Ctime:      config.CTime,
		Mtime:      config.MTime,
	}
}

func (p *MaskingRuleFacade) convertRuleToAdminData(ctx context.Context, r *rule.MaskRule) (*schema.MaskRule, *srerr.Error) {
	ret := &schema.MaskRule{
		Id:                    r.Id,
		MaskProductId:         r.MaskProductId,
		RuleStatus:            int64(r.Status),
		RuleName:              r.RuleName,
		EnableProductPriority: r.EnableProductPriority,
		OperatedBy:            r.OperatedBy,
		EffectiveStartTime:    r.EffectiveStartTime,
		CTime:                 r.CTime,
		MTime:                 r.MTime,
		RuleDetail:            &schema.MaskRuleDetail{},
		ForecastTaskId:        r.ForecastTaskId,
		RuleMode:              r.RuleMode,
		AllocationMethod:      convertAllocationMethod(r.AllocationMethod),
		BatchRuleDetail:       convertAdminBatchRuleDetail(r.BatchRuleDetail),
		ApprovalTime:          r.ApprovalTime,
	}
	//装填single allocate信息
	if r.AllocationMethod != allocation.BatchAllocate {
		limits := map[int64]*schema.MaskRuleDetailProductLimit{}
		productInfos, _ := p.LpsApi.GetProductBaseInfoList(ctx)
		productInfoMap := p.LpsApi.ConvertProductsToMap(ctx, productInfos)
		getProductLimit := func(productID int64) (*schema.MaskRuleDetailProductLimit, *srerr.Error) {
			productLimit, ok := limits[productID]
			if !ok {
				var sellerDisplayName string
				productInfo, existed := productInfoMap[int(productID)]
				if existed {
					sellerDisplayName = productInfo.SellerDisplayName
				} else {
					logger.CtxLogErrorf(ctx, "convertRuleToAdminData|product id:%v, convert product info fail, but won't break process")
				}
				productLimit = &schema.MaskRuleDetailProductLimit{
					Id:   productID,
					Name: sellerDisplayName,
				}
				limits[productID] = productLimit
			}
			return productLimit, nil
		}
		for _, step := range r.MaskRuleSteps {
			switch step.MaskStepType {
			case rule.MaskStepMaxBatchVolume:
				ret.RuleDetail.MaxBatchVolumeEnable = true
				ret.RuleDetail.MaxBatchVolumeSort = step.Priority
				ret.RuleDetail.MaxBatchVolume = step.BatchVolumeData.BatchVolume
				for productID, volume := range step.BatchVolumeData.VolumeInOneBatch {
					productLimit, lpsErr := getProductLimit(productID)
					if lpsErr != nil {
						return nil, lpsErr
					}
					productLimit.MaxVolumeEachBatch = volume
				}
			case rule.MaskStepCheapestFee:
				ret.RuleDetail.CheapestFeeEnable = true
				ret.RuleDetail.CheapestFeeSort = step.Priority
			case rule.MaskStepMinVolumeCountry:
				ret.RuleDetail.MinVolumeCountryEnable = true
				ret.RuleDetail.MinVolumeCountrySort = step.Priority
				for key, minVolume := range step.MinVolumeData.MinVolumes {
					productLimit, lpsErr := getProductLimit(key.FulfillmentProductID)
					if lpsErr != nil {
						return nil, lpsErr
					}
					productLimit.MinVolume = minVolume
				}
			case rule.MaskStepMinVolumeZoneRoute:
				ret.RuleDetail.MinVolumeZoneRouteEnable = true
				ret.RuleDetail.MinVolumeZoneRouteSort = step.Priority
			case rule.MaskStepMaxCapacityCountry:
				ret.RuleDetail.MaxCapacityCountryEnable = true
				ret.RuleDetail.MaxCapacityCountrySort = step.Priority
				for key, maxCapacity := range step.MaxCapacityData.MaxCapacities {
					productLimit, lpsErr := getProductLimit(key.FulfillmentProductID)
					if lpsErr != nil {
						return nil, lpsErr
					}
					productLimit.MaxCapacity = maxCapacity
				}
				for key, isHardCap := range step.IsHardCapacityData.IsHardCaps {
					productLimit, lpsErr := getProductLimit(key.FulfillmentProductID)
					if lpsErr != nil {
						return nil, lpsErr
					}
					productLimit.IsHardCap = isHardCap
				}
			case rule.MaskStepMaxCapacityZoneRoute:
				ret.RuleDetail.MaxCapacityZoneRouteEnable = true
				ret.RuleDetail.MaxCapacityZoneRouteSort = step.Priority
			case rule.MaskStepPickupEfficiencyWhitelist:
				ret.RuleDetail.PickupEfficiencyWhitelistEnable = true
				ret.RuleDetail.PickupEfficiencyWhitelistSort = step.Priority
			case rule.MaskStepMaxCodCapacityCountry:
				ret.RuleDetail.MaxCodCapacityCountryEnable = true
				ret.RuleDetail.MaxCodCapacityCountrySort = step.Priority
			case rule.MaskStepMaxCodCapacityZoneRoute:
				ret.RuleDetail.MaxCodCapacityZoneRouteEnable = true
				ret.RuleDetail.MaxCodCapacityZoneRouteSort = step.Priority
			case rule.MaskStepMaxBulkyCapacityCountry:
				ret.RuleDetail.MaxBulkyCapacityCountryEnable = true
				ret.RuleDetail.MaxBulkyCapacityCountrySort = step.Priority
			case rule.MaskStepMaxBulkyCapacityZoneRoute:
				ret.RuleDetail.MaxBulkyCapacityZoneRouteEnable = true
				ret.RuleDetail.MaxBulkyCapacityZoneRouteSort = step.Priority
			case rule.MaskStepMaxHighValueCapacityCountry:
				ret.RuleDetail.MaxHighValueCapacityCountryEnable = true
				ret.RuleDetail.MaxHighValueCapacityCountrySort = step.Priority
			case rule.MaskStepMaxHighValueCapacityZoneRoute:
				ret.RuleDetail.MaxHighValueCapacityZoneRouteEnable = true
				ret.RuleDetail.MaxHighValueCapacityZoneRouteSort = step.Priority
			case rule.MaskStepMaxDgCapacityCountry:
				ret.RuleDetail.MaxDgCapacityCountryEnable = true
				ret.RuleDetail.MaxDgCapacityCountrySort = step.Priority
			case rule.MaskStepMaxDgCapacityZoneRoute:
				ret.RuleDetail.MaxDgCapacityZoneRouteEnable = true
				ret.RuleDetail.MaxDgCapacityZoneRouteSort = step.Priority
			}

		}
		for _, lim := range limits {
			ret.RuleDetail.Limit = append(ret.RuleDetail.Limit, lim)
		}
	}

	return ret, nil
}

func convertAdminDataToRule(data *schema.UpdateRuleRequest) *rule.MaskRule {
	ret := &rule.MaskRule{
		Id:                    int64(data.Id),
		MaskProductId:         int64(data.MaskProductId),
		Status:                rule.MaskRuleStatus(data.RuleStatus),
		RuleName:              data.RuleName,
		EnableProductPriority: data.EnableProductPriority,
		OperatedBy:            data.OperatedBy,
		EffectiveStartTime:    uint32(data.EffectiveStartTime),
		CTime:                 uint32(data.Ctime),
		MTime:                 uint32(data.Mtime),
		AllocationMethod:      data.AllocationMethod,
		BatchRuleDetail:       convertRuleBatchRuleDetail(data.BatchRuleDetail),
	}

	if data.AllocationMethod != allocation.BatchAllocate {

		ruleSteps := rule.MaskRuleSteps{}
		detail := data.RuleDetail
		if detail.MinVolumeCountryEnable {
			minVolumes := make(map[rule.VolumeKey]int32)
			for _, limit := range detail.Limit {
				minVolumes[rule.VolumeKey{FulfillmentProductID: limit.Id}] = limit.MinVolume
			}
			step := rule.MaskRuleStep{
				Priority:      detail.MinVolumeCountrySort,
				MaskStepType:  rule.MaskStepMinVolumeCountry,
				MinVolumeData: &rule.MinVolumeData{MinVolumes: minVolumes},
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxCapacityCountryEnable {
			maxCapacities := make(map[rule.VolumeKey]int32)
			isHardCaps := make(map[rule.VolumeKey]bool)
			for _, limit := range detail.Limit {
				key := rule.VolumeKey{FulfillmentProductID: limit.Id}
				maxCapacities[key] = limit.MaxCapacity
				isHardCaps[key] = limit.IsHardCap
			}
			step := rule.MaskRuleStep{
				Priority:        detail.MaxCapacityCountrySort,
				MaskStepType:    rule.MaskStepMaxCapacityCountry,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxCapacities},
				IsHardCapacityData: &rule.IsHardCapacityData{
					IsHardCaps: isHardCaps,
				},
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxBatchVolumeEnable {
			volumesInOneBatch := map[int64]int32{}
			for _, limit := range detail.Limit {
				volumesInOneBatch[limit.Id] = limit.MaxVolumeEachBatch
			}
			step := rule.MaskRuleStep{
				Priority:     detail.MaxBatchVolumeSort,
				MaskStepType: rule.MaskStepMaxBatchVolume,
				BatchVolumeData: &rule.BatchVolumeData{
					BatchVolume:      detail.MaxBatchVolume,
					VolumeInOneBatch: volumesInOneBatch,
				},
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.CheapestFeeEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.CheapestFeeSort,
				MaskStepType: rule.MaskStepCheapestFee,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MinVolumeZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MinVolumeZoneRouteSort,
				MaskStepType: rule.MaskStepMinVolumeZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxCapacityZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxCapacityZoneRouteSort,
				MaskStepType: rule.MaskStepMaxCapacityZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.PickupEfficiencyWhitelistEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.PickupEfficiencyWhitelistSort,
				MaskStepType: rule.MaskStepPickupEfficiencyWhitelist,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxCodCapacityCountryEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxCodCapacityCountrySort,
				MaskStepType: rule.MaskStepMaxCodCapacityCountry,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxCodCapacityZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxCodCapacityZoneRouteSort,
				MaskStepType: rule.MaskStepMaxCodCapacityZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxBulkyCapacityCountryEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxBulkyCapacityCountrySort,
				MaskStepType: rule.MaskStepMaxBulkyCapacityCountry,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxBulkyCapacityZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxBulkyCapacityZoneRouteSort,
				MaskStepType: rule.MaskStepMaxBulkyCapacityZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxHighValueCapacityCountryEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxHighValueCapacityCountrySort,
				MaskStepType: rule.MaskStepMaxHighValueCapacityCountry,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxHighValueCapacityZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxHighValueCapacityZoneRouteSort,
				MaskStepType: rule.MaskStepMaxHighValueCapacityZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxDgCapacityCountryEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxDgCapacityCountrySort,
				MaskStepType: rule.MaskStepMaxDgCapacityCountry,
			}
			ruleSteps = append(ruleSteps, step)
		}
		if detail.MaxDgCapacityZoneRouteEnable {
			step := rule.MaskRuleStep{
				Priority:     detail.MaxDgCapacityZoneRouteSort,
				MaskStepType: rule.MaskStepMaxDgCapacityZoneRoute,
			}
			ruleSteps = append(ruleSteps, step)
		}

		ret.MaskRuleSteps = ruleSteps
	}

	return ret
}

func convertAdminBatchRuleDetail(detail rule.BatchRuleDetail) schema.BatchRuleDetail {
	ret := schema.BatchRuleDetail{
		BatchAllocationRuleConfig: schema.BatchAllocationRuleConfig{
			UseForCampaign:                   detail.BatchAllocationRuleConfig.UseForCampaign,
			ZoneRouteVolumeEnabled:           detail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled,
			CountryVolumeEnabled:             detail.BatchAllocationRuleConfig.CountryVolumeEnabled,
			CheapestShippingFeeEnabled:       detail.BatchAllocationRuleConfig.CheapestShippingFeeEnabled,
			PickupEfficiencyEnabled:          detail.BatchAllocationRuleConfig.PickupEfficiencyEnabled,
			PickupEfficiencyDetail:           schema.PickupEfficiencyDetail{Budget: detail.BatchAllocationRuleConfig.PickupEfficiencyDetail.Budget},
			PickupEfficiencyWhitelistEnabled: detail.BatchAllocationRuleConfig.PickupEfficiencyWhitelistEnabled,
		},
	}
	dbBatchSize := detail.BatchSize
	adminBatchSize := schema.BatchSize{
		BatchSizeType: dbBatchSize.BatchSizeType,
		BatchSizeName: dbBatchSize.BatchSizeName,
	}
	fixTime := schema.FixedTime{
		FixedTimeUnitList: make([]schema.FixedTimeUnit, len(dbBatchSize.FixedTime.FixedTimeUnitList)),
	}
	for j := 0; j < len(dbBatchSize.FixedTime.FixedTimeUnitList); j++ {
		fixTime.FixedTimeUnitList[j] = schema.FixedTimeUnit{
			StartTime: dbBatchSize.FixedTime.FixedTimeUnitList[j].StartTime,
			EndTime:   dbBatchSize.FixedTime.FixedTimeUnitList[j].EndTime,
			TimeRange: dbBatchSize.FixedTime.FixedTimeUnitList[j].TimeRange,
		}
	}
	adminBatchSize.FixedTime = fixTime

	ret.BatchSize = adminBatchSize

	return ret
}

func convertRuleBatchRuleDetail(detail schema.BatchRuleDetail) rule.BatchRuleDetail {
	ret := rule.BatchRuleDetail{
		BatchAllocationRuleConfig: model.BatchAllocationRuleConfig{
			UseForCampaign:                   detail.BatchAllocationRuleConfig.UseForCampaign,
			ZoneRouteVolumeEnabled:           detail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled,
			CountryVolumeEnabled:             detail.BatchAllocationRuleConfig.CountryVolumeEnabled,
			CheapestShippingFeeEnabled:       detail.BatchAllocationRuleConfig.CheapestShippingFeeEnabled,
			PickupEfficiencyEnabled:          detail.BatchAllocationRuleConfig.PickupEfficiencyEnabled,
			PickupEfficiencyDetail:           model.PickupEfficiencyDetail{Budget: detail.BatchAllocationRuleConfig.PickupEfficiencyDetail.Budget},
			PickupEfficiencyWhitelistEnabled: detail.BatchAllocationRuleConfig.PickupEfficiencyWhitelistEnabled,
		},
	}
	dbBatchSize := detail.BatchSize
	adminBatchSize := model.BatchSize{
		BatchSizeType: dbBatchSize.BatchSizeType,
		BatchSizeName: dbBatchSize.BatchSizeName,
	}
	fixTime := model.FixedTime{
		FixedTimeUnitList: make([]model.FixedTimeUnit, len(dbBatchSize.FixedTime.FixedTimeUnitList)),
	}
	for j := 0; j < len(dbBatchSize.FixedTime.FixedTimeUnitList); j++ {
		fixTime.FixedTimeUnitList[j] = model.FixedTimeUnit{
			StartTime: dbBatchSize.FixedTime.FixedTimeUnitList[j].StartTime,
			EndTime:   dbBatchSize.FixedTime.FixedTimeUnitList[j].EndTime,
			TimeRange: dbBatchSize.FixedTime.FixedTimeUnitList[j].TimeRange,
		}
	}
	adminBatchSize.FixedTime = fixTime

	ret.BatchSize = adminBatchSize

	return ret
}

func convertAllocationMethod(allocationMethod int64) int64 {
	if allocationMethod == constant.BatchAllocate {
		return allocationMethod
	}

	return constant.SingleAllocate
}

func (p *MaskingRuleFacade) UploadShop(ctx *restful.Context) {
	req := &schema.UploadWhitelistReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	err := p.ShopWhitelistService.ImportShopWhitelist(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) ListShopWhitelist(ctx *restful.Context) {
	req := &schema.ListWhitelistReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	resp, err := p.ShopWhitelistService.ListShopWhitelist(ctx.Ctx, req, true)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) ExportShopWhitelist(ctx *restful.Context) {
	req := &schema.ListWhitelistReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	resp, err := p.ShopWhitelistService.ExportShopWhitelist(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) ListPriority(ctx *restful.Context) {
	req := &schema.ListPriorityReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	resp, err := p.PickupPriorityService.List(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) CreatePriority(ctx *restful.Context) {
	req := &schema.CreatePriorityReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	err := p.PickupPriorityService.Create(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) DeletePriority(ctx *restful.Context) {
	req := &schema.DeletePriorityReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	err := p.PickupPriorityService.Delete(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingRuleFacade) PriorityDetail(ctx *restful.Context) {
	req := &schema.GetPriorityReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	resp, err := p.PickupPriorityService.Detail(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *MaskingRuleFacade) UpdatePriority(ctx *restful.Context) {
	req := &schema.CreatePriorityReq{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		sErr := srerr.With(srerr.ParamErr, "get request, ", err)
		apiutil.FailJSONResp(ctx, sErr, sErr.Error())
		return
	}
	err := p.PickupPriorityService.Update(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}
