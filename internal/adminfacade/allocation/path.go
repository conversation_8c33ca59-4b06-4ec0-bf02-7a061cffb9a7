package allocation

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	ap "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient/allocpath"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
)

type MaskingPathFacade struct {
	AllocationPathSrv allocpath.AllocationPathService
}

func (p *MaskingPathFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	AllocationPathRouting := adminGroup.Group("/allocation/path")
	AllocationPathRouting.POST("/list", p.GetAllocationPathList)
	AllocationPathRouting.GET("/detail", p.GetAllocationPathDetail)
	AllocationPathRouting.POST("/debug_detail", p.GetAllocationPathDebugDetail)

	routes := adminGroup.GetRouters()
	return routes
}

func (p *MaskingPathFacade) GetAllocationPathDebugDetail(ctx *restful.Context) {
	req := ap.DetailV2{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	mockCtx := mockutil.MockCtx(ctx)
	result, err1 := p.AllocationPathSrv.GetDebugDetail(mockCtx, &req)
	if err1 != nil {
		logger.CtxLogInfof(mockCtx, "GetAllocationPathDebugDetail: %v", err1)
		apiutil.FailJSONResp(ctx, err1, err1.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingPathFacade) GetAllocationPathList(ctx *restful.Context) {
	req := schema.GetAllocPathListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//validate param
	if err := req.Validate(ctx.Ctx); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.AllocationPathSrv.GetList(mockutil.MockCtx(ctx), req.RequestId, req.OrderIdUint, req.FOrderIdUint, req.PageNo, req.PageSize)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingPathFacade) GetAllocationPathDetail(ctx *restful.Context) {
	req := schema.GetAllocPathDetailRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//validate param, now will only validate time
	if err := req.Validate(ctx.Ctx); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.AllocationPathSrv.GetDetail(mockutil.MockCtx(ctx), req.RequestId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}
