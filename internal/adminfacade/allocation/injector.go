package allocation

import (
	forecast2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(MaskingResultPanelFacade), "*"),
	wire.Struct(new(MaskingPathFacade), "*"),
	wire.Struct(new(MaskingRuleFacade), "*"),
	wire.Struct(new(MaskingForecastFacade), "*"),
	wire.Struct(new(MaskingRuleVolumeFacade), "*"),

	masking_result_panel.KeyDataMgrProviderSet,
	allocpath.AllocationPathSrvProviderSet,
	volumecounter.MaskVolumeCounterProviderSet,
	forecast.AllocateForecastTaskConfigServiceProviderSet,
	forecast.AllocateRankProviderSet,
	forecast2.AllocateDateRankProviderSet,
	forecast2.AllocateHistoricalRankProviderSet,
	forecast2.AllocateShippingFeeProviderSet,
	forecast2.AllocateForecastRankProviderSet,
	forecast2.AllocateForecastTaskConfigProviderSet,
	service.BAForecastProviderSet,
	rulevolume.BatchAllocateForecastVolumeProviderSet,
	forecast2.BatchAllocateForecastRepoProviderSet,
	forecast2.BatchUnitTargetResultRepoProviderSet,
	forecast2.BatchUnitFeeResultRepoProviderSet,
	forecast2.BatchAllocateForecastUnitResultRepoProviderSet,
	forecast2.BatchAllocateSubTaskRepoProviderSet,
	forecast2.BatchAllocateForecastUnitRepoProviderSet,
	forecast2.BatchAllocateForecastServiceProviderSet,
	forecast2.AllocateHistoryOutlineProviderSet,
	forecast2.BatchAllocateSubtaskOutlineRepoProviderSet,
)
