package allocation

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	forecast2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type MaskingForecastFacade struct {
	AllocateForecastTaskConfigService service.AllocateForecastTaskConfigService
	AllocateShippingFeeRepo           forecast2.AllocateShippingFeeRepo
	AllocateRankService               service.AllocateRankService
	BatchAllocateForecastService      service.BatchAllocateForecastService
	BatchMinuteOrderConfService       batch_allocate.BatchMinuteOrderConfService
}

func (m *MaskingForecastFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	maskingForecast := adminGroup.Group("/allocate_forecast")
	maskingForecast.GET("/task_config_get", m.GetForecastTaskConfig)   //single&&batch detail
	maskingForecast.GET("/task_config_list", m.ListForecastTaskConfig) //single&&batch list
	maskingForecast.GET("/shipping_fee_list", m.ListShippingFeeRule)
	maskingForecast.GET("/result_rank_code_list", m.ListResultRankCode)
	maskingForecast.GET("/result_rank_list", m.ListResultRank)
	maskingForecast.GET("/date_rank_code_list", m.ListDateRankCode)
	maskingForecast.POST("/date_rank_list", m.ListDateRank)
	maskingForecast.GET("/soft_rule_sync", m.GetOnlineSoftRule)
	maskingForecast.GET("/product_priority_sync", m.GetOnlineProductPriority)

	maskingForecast.POST("/task_config_create", m.CreateForecastTaskConfig)
	maskingForecast.POST("/task_config_update", m.UpdateForecastTaskConfig)
	maskingForecast.POST("/task_config_copy", m.CopyForecastTaskConfig)     //single&&batch copy
	maskingForecast.POST("/del_draft_task_config", m.DelForecastTaskConfig) //single&&batch del
	maskingForecast.POST("/export_date_rank_list", m.ExportDateRankList)
	maskingForecast.GET("/export_result_rank_list", m.ExportResultRankList)

	//Batch allocation
	maskingForecast.POST("/batch_allocate/create", m.BatchAllocationCreate)
	maskingForecast.POST("/batch_allocate/update", m.BatchAllocationUpdate)
	maskingForecast.POST("/batch_allocate/historical_zone_route_code", m.BatchAllocationHistoricalCode)
	maskingForecast.POST("/batch_allocate/historical_orders_info", m.BatchAllocateHistoricalInfo)
	maskingForecast.POST("/batch_allocate/forecast_zone_route_code", m.BatchAllocateForecastCode)
	maskingForecast.POST("/batch_allocate/forecast_orders_info", m.BatchAllocateForecastInfo)
	maskingForecast.POST("/batch_allocate/export_history", m.BatchAllocateExportHistory)
	maskingForecast.POST("/batch_allocate/export_forecast_result", m.BatchAllocateExportForecastResult)
	maskingForecast.POST("/create_batch_minute_conf", m.CreateBatchMinuteConf)

	routes := adminGroup.GetRouters()
	return routes
}

func (m *MaskingForecastFacade) CreateForecastTaskConfig(ctx *restful.Context) {
	req := schema.AllocateForecastTaskConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := m.AllocateForecastTaskConfigService.CreateForecastTaskConfig(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (m *MaskingForecastFacade) UpdateForecastTaskConfig(ctx *restful.Context) {
	req := schema.AllocateForecastTaskConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := m.AllocateForecastTaskConfigService.UpdateForecastTaskConfig(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (m *MaskingForecastFacade) GetForecastTaskConfig(ctx *restful.Context) {
	req := schema.AllocateForecastTaskConfigGetRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateForecastTaskConfigService.GetForecastTaskConfigById(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) ListForecastTaskConfig(ctx *restful.Context) {
	req := schema.ForecastListRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateForecastTaskConfigService.ListForecastTaskConfigs(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) ListShippingFeeRule(ctx *restful.Context) {
	req := schema.ShippingFeeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateShippingFeeRepo.QueryAllocatingRateFee(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data.Data)
}

func (m *MaskingForecastFacade) ListDateRank(ctx *restful.Context) {
	req := schema.DateRankListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateRankService.GetDateRankList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) ListDateRankCode(ctx *restful.Context) {
	req := schema.DateRankCodeListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateRankService.GetDateRankCodeList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) ListResultRankCode(ctx *restful.Context) {
	req := schema.ResultRankCodeListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateRankService.GetResultRankCodeList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) ListResultRank(ctx *restful.Context) {
	req := schema.ResultRankListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := m.AllocateRankService.GetResultRankList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, data)
}

func (m *MaskingForecastFacade) DelForecastTaskConfig(ctx *restful.Context) {
	req := schema.DelDraftTaskConfigReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := m.AllocateForecastTaskConfigService.DelForecastTaskConfig(ctx.Ctx, req.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (m *MaskingForecastFacade) CopyForecastTaskConfig(ctx *restful.Context) {
	req := schema.CopyTaskConfigReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	_, err := m.AllocateForecastTaskConfigService.CopyForecastTaskConfigById(ctx.Ctx, req.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (m *MaskingForecastFacade) GetOnlineSoftRule(ctx *restful.Context) {
	req := schema.SoftRuleSyncReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := m.AllocateForecastTaskConfigService.GetOnlineSoftRule(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (m *MaskingForecastFacade) ExportDateRankList(ctx *restful.Context) {
	req := schema.DateRankListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	excelFile, err := m.AllocateRankService.ExportDateRankList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	now := recorder.Now(ctx.Ctx).Format(constant.TimeLayout)
	fileName := fmt.Sprintf("historical_shipment_taskname_%v", now)
	apiutil.WriteExcelResp(ctx, fileName, excelFile.Bytes())
}

func (m *MaskingForecastFacade) ExportResultRankList(ctx *restful.Context) {
	req := schema.ResultRankListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	excelFile, err := m.AllocateRankService.ExportResultRankList(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	now := recorder.Now(ctx.Ctx).Format(constant.TimeLayout)
	fileName := fmt.Sprintf("historical_shipment_taskname_%v_%v", now, req.TaskId)
	apiutil.WriteExcelResp(ctx, fileName, excelFile.Bytes())
}

func (m *MaskingForecastFacade) GetOnlineProductPriority(ctx *restful.Context) {
	req := schema.ProductPrioritySyncReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := m.AllocateForecastTaskConfigService.GetOnlineProductPriority(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (m *MaskingForecastFacade) BatchAllocationCreate(ctx *restful.Context) {
	req := schema.BAForecastCreateUpdateReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := m.BatchAllocateForecastService.CreateBatchAllocationTask(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, constant.Success)
}

func (m *MaskingForecastFacade) BatchAllocationUpdate(ctx *restful.Context) {
	req := schema.BAForecastCreateUpdateReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := m.BatchAllocateForecastService.UpdateBatchAllocationTask(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, constant.Success)
}

func (m *MaskingForecastFacade) BatchAllocationHistoricalCode(ctx *restful.Context) {
	req := schema.BAHistoricalCodeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if codeList, err := m.BatchAllocateForecastService.GetHistoricalCodeList(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, map[string][]string{
			"list": codeList,
		})
	}
}

func (m *MaskingForecastFacade) BatchAllocateHistoricalInfo(ctx *restful.Context) {
	req := schema.BAHistoricalInfoReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if resp, err := m.BatchAllocateForecastService.GetHistoricalInfo(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

func (m *MaskingForecastFacade) BatchAllocateForecastCode(ctx *restful.Context) {
	req := schema.BAForecastCodeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if codeList, err := m.BatchAllocateForecastService.GetForecastCodeList(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		resp := schema.BAForecastCodeResp{
			List: codeList,
		}
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

func (m *MaskingForecastFacade) BatchAllocateForecastInfo(ctx *restful.Context) {
	req := schema.BAForecastInfoReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if resp, err := m.BatchAllocateForecastService.GetForecastInfo(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

func (m *MaskingForecastFacade) BatchAllocateExportHistory(ctx *restful.Context) {
	req := schema.BAExportHistoryReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if resp, err := m.BatchAllocateForecastService.ExportHistory(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

func (m *MaskingForecastFacade) BatchAllocateExportForecastResult(ctx *restful.Context) {
	req := schema.BAExportForecastResultReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if resp, err := m.BatchAllocateForecastService.ExportForecastResult(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, resp)
	}
}

func (m *MaskingForecastFacade) CreateBatchMinuteConf(ctx *restful.Context) {
	req := schema.CreateMinuteOrderConfReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := m.BatchMinuteOrderConfService.CreateBatchMinuteOrderConf(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
	} else {
		apiutil.SuccessJSONResp(ctx, "success")
	}
}
