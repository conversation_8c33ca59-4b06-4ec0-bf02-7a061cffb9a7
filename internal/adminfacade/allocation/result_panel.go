package allocation

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
)

type MaskingResultPanelFacade struct {
	KeyDataManager masking_result_panel.KeyDataManager
}

func (p *MaskingResultPanelFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")

	//masking module
	maskingPanelRouting := adminGroup.Group("/masking_result_panel")
	maskingPanelRouting.POST("/get_key_data_list", p.GetKeyDataListView)
	maskingPanelRouting.POST("/export_key_data", p.ExportKeyDataView)
	maskingPanelRouting.GET("/get_export_task_list", p.GetExportTaskListView)
	maskingPanelRouting.POST("/test/get_and_update", p.TestGetAndUploadMaskingData)

	routes := adminGroup.GetRouters()
	return routes
}

// GetKeyDataListView :根据入参的条件，调用data的api获取关键数据
func (p *MaskingResultPanelFacade) GetKeyDataListView(ctx *restful.Context) {
	req := masking_panel.GetKeyDataRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//validate param, now will only validate time
	if vErr := req.Validate(ctx.Ctx); vErr != nil {
		apiutil.FailJSONResp(ctx, vErr, vErr.Error())
		return
	}
	mockCtx := mockutil.MockCtx(ctx)
	req.RequestID = ctx.ReadHeader("X-Request-ID")
	result, err := p.KeyDataManager.GetKeyDataList(mockCtx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *MaskingResultPanelFacade) ExportKeyDataView(ctx *restful.Context) {
	req := masking_panel.GetKeyDataRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.KeyDataManager.ExportData(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *MaskingResultPanelFacade) GetExportTaskListView(ctx *restful.Context) {
	req := schema.GetExportTasksRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	response, err := p.KeyDataManager.GetExportTasks(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, response)
}

func (p *MaskingResultPanelFacade) TestGetAndUploadMaskingData(ctx *restful.Context) {
	req := masking_panel.GetKeyDataRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	var taskID uint64 = 3
	err := p.KeyDataManager.GetAndUploadMaskingData(ctx.Ctx, req, taskID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}
