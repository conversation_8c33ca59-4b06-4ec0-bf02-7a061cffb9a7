package allocation

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	service "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

func (p *MaskingRuleFacade) CreateParcelTypeDefinition(ctx *restful.Context) {
	CreateParcelTypeDefinition(ctx, p.ParcelTypeDefinitionService)
}

func CreateParcelTypeDefinition(ctx *restful.Context, parcelTypeDefinitionService service.ParcelTypeDefinitionService) {
	var req schema.CreateParcelTypeDefinitionReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if !validateParcelTypeRequirement(ctx, req.MaskProductID, req.LineID) {
		apiutil.FailJSONResp(ctx, nil, "mask_product_id or line_id is required")
		return
	}
	def := &service.ParcelTypeDefinitionItem{
		MaskProductID:        req.MaskProductID,
		FulfillmentProductID: req.FulfillmentProductID,
		LineID:               req.LineID,
		ScenarioType:         req.ScenarioType,
		BulkyDefinition:      req.BulkyDefinition,
		HighValueDefinition:  req.HighValueDefinition,
		Status:               req.Status,
		Operator:             operator,
	}
	ret, err := parcelTypeDefinitionService.CreateParcelTypeDefinition(ctx.Ctx, def)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *MaskingRuleFacade) UpdateParcelTypeDefinition(ctx *restful.Context) {
	UpdateParcelTypeDefinition(ctx, p.ParcelTypeDefinitionService)
}

func UpdateParcelTypeDefinition(ctx *restful.Context, parcelTypeDefinitionService service.ParcelTypeDefinitionService) {
	var req schema.UpdateParcelTypeDefinitionReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if !validateParcelTypeRequirement(ctx, req.MaskProductID, req.LineID) {
		apiutil.FailJSONResp(ctx, nil, "mask_product_id or line_id is required")
		return
	}
	def := &service.ParcelTypeDefinitionItem{
		ID:                   req.ID,
		MaskProductID:        req.MaskProductID,
		FulfillmentProductID: req.FulfillmentProductID,
		LineID:               req.LineID,
		ScenarioType:         req.ScenarioType,
		BulkyDefinition:      req.BulkyDefinition,
		HighValueDefinition:  req.HighValueDefinition,
		Status:               req.Status,
		Operator:             operator,
	}

	ret, err := parcelTypeDefinitionService.UpdateParcelTypeDefinition(ctx.Ctx, def)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *MaskingRuleFacade) GetParcelTypeDefinition(ctx *restful.Context) {
	GetParcelTypeDefinition(ctx, p.ParcelTypeDefinitionService)
}

func GetParcelTypeDefinition(ctx *restful.Context, parcelTypeDefinitionService service.ParcelTypeDefinitionService) {
	var req schema.GetParcelTypeDefinitionReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	def, err := parcelTypeDefinitionService.GetParcelTypeDefinition(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, def)
}

func (p *MaskingRuleFacade) ListParcelTypeDefinition(ctx *restful.Context) {
	ListParcelTypeDefinition(ctx, p.ParcelTypeDefinitionService)
}

func ListParcelTypeDefinition(ctx *restful.Context, parcelTypeDefinitionService service.ParcelTypeDefinitionService) {
	var req schema.ListParcelTypeDefinitionReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	list, total, err := parcelTypeDefinitionService.ListParcelTypeDefinition(ctx.Ctx, req.MaskProductID, req.FulfillmentProductID, req.LineID, req.Status, req.PageNo, req.Limit, req.ScenarioType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := schema.ListParcelTypeDefinitionResp{
		Count:  len(list),
		PageNo: req.PageNo,
		Total:  total,
		List:   list,
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
func (p *MaskingRuleFacade) DeleteParcelTypeDefinition(ctx *restful.Context) {
	DeleteParcelTypeDefinition(ctx, p.ParcelTypeDefinitionService)
}

func DeleteParcelTypeDefinition(ctx *restful.Context, parcelTypeDefinitionService service.ParcelTypeDefinitionService) {
	var req schema.DeleteParcelTypeDefinitionReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	item, err := parcelTypeDefinitionService.GetParcelTypeDefinition(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if item.Status != 0 {
		monitoring.ReportError(ctx.Ctx, monitoring.CatParcelTypeDimension, monitoring.ParcelTypeDimensionParamError, "status is not disable")
		apiutil.FailJSONResp(ctx, nil, "only disable status can be deleted")
		return
	}
	if err := parcelTypeDefinitionService.DeleteParcelTypeDefinition(ctx.Ctx, req.ID); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func validateParcelTypeRequirement(ctx *restful.Context, maskProductID int, lineID string) bool {
	if maskProductID == 0 && lineID == "" {
		monitoring.ReportError(ctx.Ctx, monitoring.CatParcelTypeDimension, monitoring.ParcelTypeDimensionParamError, "mask_product_id or line_id is required")
		return false
	}
	if maskProductID != 0 && lineID != "" {
		monitoring.ReportError(ctx.Ctx, monitoring.CatParcelTypeDimension, monitoring.ParcelTypeDimensionParamError, "mask_product_id and line_id cannot be both set")
		return false
	}
	return true
}
