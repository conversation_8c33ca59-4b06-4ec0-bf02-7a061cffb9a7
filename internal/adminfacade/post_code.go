package adminfacade

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

type PostCodeFacade struct {
	ZoneRepo locationzone.ZoneRepo
}

func (p *PostCodeFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/post_code")
	routes.POST("/list", p.QueryPostCodeList)
	routes.POST("/delete", p.DeleteSinglePostCode)
	routes.POST("/edit", p.EditSinglePostCode)
	routes.POST("/import", p.ImportPostCode)
	routes.POST("/export", p.ExportPostCode)

	return routes.GetRouters()
}

func (p *PostCodeFacade) QueryPostCodeList(ctx *restful.Context) {
	req := schema.QueryPostCodeListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.ZoneRepo.QueryPostCodeList(ctx.Ctx, &req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.PostCodeMonitor, monitoring.QueryPostCodeListErr, fmt.Sprintf("query post code list err=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *PostCodeFacade) DeleteSinglePostCode(ctx *restful.Context) {
	req := schema.DeleteSinglePostCode{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.ZoneRepo.DeleteZone(ctx.Ctx, req.PostCodeId)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.PostCodeMonitor, monitoring.DeleteSinglePostCodeErr, fmt.Sprintf("delete post code err=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *PostCodeFacade) EditSinglePostCode(ctx *restful.Context) {
	req := schema.EditSinglePostCode{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.ZoneRepo.EditSinglePostCode(ctx.Ctx, &req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.PostCodeMonitor, monitoring.EditSinglePostCodeErr, fmt.Sprintf("edit post code err=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *PostCodeFacade) ImportPostCode(ctx *restful.Context) {
	req := schema.ImportPostCodeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.ZoneRepo.ImportPostCode(ctx.Ctx, &req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.PostCodeMonitor, monitoring.ImportPostCodeErr, fmt.Sprintf("import post code err=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *PostCodeFacade) ExportPostCode(ctx *restful.Context) {
	req := schema.ExportPostCodeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	bytes, err := p.ZoneRepo.ExportPostCode(ctx.Ctx, &req)
	if err != nil {
		monitoring.ReportError(ctx.Ctx, monitoring.PostCodeMonitor, monitoring.ExportPostCodeErr, fmt.Sprintf("export post code err=%v", err))
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, bytes)
}
