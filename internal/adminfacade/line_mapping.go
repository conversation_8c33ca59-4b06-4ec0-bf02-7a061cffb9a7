package adminfacade

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"sort"
)

func UpdateLineMapping(ctx *restful.Context) {
	// do in same tx,
	req := new(schema.LineMappingReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//load-mems compared ,
	db, dbErr := dbutil.MasterDB(ctx.Ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", dbErr.Error()), "")
	}

	record := routing.GetRoleInfoByPid(ctx.Ctx, int64(req.ProductId), db)
	if record == nil {
		//insert-mode ,
		record = convertToLineMappingRecord(ctx.Ctx, req)
		if err2 := db.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(record).GetError(); err2 != nil {
			apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", err2.Error()), err2.Error())
			return
		}
		//done-insert,
		apiutil.SuccessJSONResp(ctx, nil)
		return
	}
	//edit-mode,
	cfg := routing.GetRoutingcfgByPid(ctx.Ctx, int64(req.ProductId), db)
	if !checkAllowUpdateLineMapping(record, req, cfg) {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.LineMappingNotALlowed, nil, ""), "")
	}
	//allowed,
	updatedRow := convertToLineMappingRecord(ctx.Ctx, req)
	upd := dbutil.UpdateIncludeMapper(updatedRow,
		"cb_routing_role", "cb_multi_routing_role",
		"spx_routing_role", "local_routing_role",
		"ilh_routing_role", "mtime")
	if err2 := db.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Where("product_id = ? ", req.ProductId).Updates(upd).GetError(); err2 != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.DatabaseErr, "", err2.Error()), err2.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func convertToLineMappingRecord(ctx context.Context, req *schema.LineMappingReq) *ruledata.ProductRoutingRoleTab {
	//
	rettab := new(ruledata.ProductRoutingRoleTab)
	rettab.ProductId = req.ProductId
	rettab.Mtime = int(recorder.Now(ctx).Unix())
	rettab.Ctime = int(recorder.Now(ctx).Unix())
	if req.ProductRoutingRole != nil {
		rettab.LocalRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.LocalRoutingRole)
		rettab.SpxRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.SpxRoutingRole)
		rettab.CBRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.CBRoutingRole)
		rettab.CBMultiRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.CBMultiRoutingRole)
		rettab.IlhRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.IlhRoutingRole)
		return rettab
	}
	return nil
}

func checkAllowUpdateLineMapping(lineMapping *ruledata.ProductRoutingRoleTab, req *schema.LineMappingReq, cfg *ruledata.RoutingConfigTab) bool {
	if cfg == nil {
		return true
	}
	//check-diffs
	if cfg.SmartRoutingEnabled && !checkEqual(lineMapping.LocalRoutingRole, req.ProductRoutingRole.LocalRoutingRole) {
		return false
	}
	//check-all-fields
	return true
}

func checkEqual(src string, target []schema.RoutingRoleInfo) bool {
	srcObj := routing_role.JsonRoleInfo(src)
	//use deep-eqauls ??
	sort.Slice(srcObj, func(i, j int) bool {
		return srcObj[i].ResourceSubType < srcObj[j].ResourceSubType
	})

	sort.Slice(target, func(i, j int) bool {
		return target[i].ResourceSubType < target[j].ResourceSubType
	})
	return reflect.DeepEqual(srcObj, target)
}
