package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type AvailableLHFacade struct {
	AvailableLHService available_lh.AvailableLHService
}

func (a *AvailableLHFacade) URLPatterns() []restful.Route {
	availableLH := restful.NewRouterGroup("/api/admin/smart_routing/available_lh")
	availableLH.POST("/create", a.Create)
	availableLH.POST("/update", a.Update)
	availableLH.GET("/view", a.View)
	availableLH.GET("/list", a.List)
	availableLH.POST("/delete", a.Delete)
	availableLH.POST("/copy", a.Copy)
	availableLH.GET("/check", a.Check)
	availableLH.POST("/update_status", a.UpdateStatus)

	return availableLH.GetRouters()
}

func (a *AvailableLHFacade) Create(ctx *restful.Context) {
	var req ilh_smart_routing.CreateAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := a.validateDefaultRule(req.Rules); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)
	id, err := a.AvailableLHService.CreateAvailableLH(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := ilh_smart_routing.CreateAvailableLHConfigResp{ID: id}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (a *AvailableLHFacade) Update(ctx *restful.Context) {
	var req ilh_smart_routing.UpdateAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := a.validateDefaultRule(req.Rules); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)
	if err := a.AvailableLHService.UpdateAvailableLH(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (a *AvailableLHFacade) View(ctx *restful.Context) {
	var req ilh_smart_routing.GetAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := a.AvailableLHService.GetAvailableLH(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (a *AvailableLHFacade) List(ctx *restful.Context) {
	var req ilh_smart_routing.ListAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	req.Pageno, _, req.Limit = apiutil.GetOffsetAndLimit(req.Pageno, req.Limit)

	resp, err := a.AvailableLHService.ListAvailableLH(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// validateDefaultRule 检查是否存在优先级为1000的默认规则
func (a *AvailableLHFacade) validateDefaultRule(rules []entity.AvailableLHRule) *srerr.Error {
	for _, r := range rules {
		if r.Priority == 1000 {
			return nil // 找到默认规则后立即返回
		}
	}
	// 如果没有找到优先级为1000的默认规则，返回参数错误
	return srerr.New(srerr.ParamErr, nil, "missing default rule with priority 1000")
}

// Delete 删除可用线路配置
func (a *AvailableLHFacade) Delete(ctx *restful.Context) {
	var req ilh_smart_routing.DeleteAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)
	if err := a.AvailableLHService.DeleteAvailableLH(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// Copy 复制可用线路配置
func (a *AvailableLHFacade) Copy(ctx *restful.Context) {
	var req ilh_smart_routing.CopyAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)
	id, err := a.AvailableLHService.CopyAvailableLH(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := ilh_smart_routing.CreateAvailableLHConfigResp{ID: id}

	apiutil.SuccessJSONResp(ctx, resp)
}

// Check 检查是否存在相同MultiProductID且状态为Active的配置
func (a *AvailableLHFacade) Check(ctx *restful.Context) {
	var req ilh_smart_routing.CheckAvailableLHConfigReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := a.AvailableLHService.CheckActiveAvailableLH(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// UpdateStatus 更新可用线路配置状态，仅允许更新为Active或Expired
func (a *AvailableLHFacade) UpdateStatus(ctx *restful.Context) {
	var req ilh_smart_routing.UpdateAvailableLHStatusReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 验证状态只能是Active或Expired
	if req.Status != rule.RuleStatusActive && req.Status != rule.RuleStatusExpired {
		err := srerr.New(srerr.ParamErr, nil, "status can only be Active or Inactive")
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)
	if err := a.AvailableLHService.UpdateAvailableLHStatus(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}
