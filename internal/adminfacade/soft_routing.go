package adminfacade

import (
	"bytes"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type SoftRoutingFacade struct {
	SoftRuleServer              routing_rule.SoftRuleServer
	RoutingConfigServer         routing_config.RoutingConfigService
	ZoneRepo                    locationzone.ZoneRepo
	ForecastTaskSrv             *forecastservice.ForecastTaskServiceImpl
	ParcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
}

func (s *SoftRoutingFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")
	adminGroup.POST("/create_update_routingcfg", s.CreateOrUpdateSmrCfg)
	//soft routing module
	SoftRouting := adminGroup.Group("/smart_routing")
	SoftRouting.GET("/rule_detail", s.GetRoutingRule)
	SoftRouting.POST("/create_rule", s.CreateRoutingRule)
	SoftRouting.POST("/update_rule", s.UpdateRoutingRule)
	SoftRouting.POST("/delete_rule", s.DeleteRule)
	SoftRouting.POST("/copy_rule", s.CopyRule)
	SoftRouting.POST("/rollback_rule", s.RollbackRule)
	SoftRouting.POST("/disable_rule", s.DisableRule)
	SoftRouting.POST("/batch_disable_rule", s.BatchDisableRule)
	SoftRouting.POST("/create_switch", s.CreateConf)
	SoftRouting.POST("/update_switch", s.UpdateConf) //to-fixs,
	SoftRouting.POST("/zone/import", s.ImportZoneCode)
	SoftRouting.GET("/zone/export", s.ExportZoneCode)
	SoftRouting.POST("/zone/update", s.UpdateZoneCode)
	SoftRouting.POST("/zone/delete", s.DeleteZoneCode)
	SoftRouting.POST("/update_spx_rule", s.UpdateRoutingRule)
	SoftRouting.GET("/rule_list", s.ListRoutingRule)
	SoftRouting.GET("/init_rule", s.InitRoutingRule)
	SoftRouting.GET("/list_whs_id", s.ListWhsCode)
	SoftRouting.GET("/list_ilh_whs_id", s.ListILHWhsCode)
	SoftRouting.GET("/list_ilh_destination_port", s.ListILHDestinationPort)
	SoftRouting.GET("/switch_detail", s.GetConf)
	SoftRouting.GET("/switch_list", s.ListConf)
	SoftRouting.GET("/resource_sub_type_list", s.ListResourceType)
	SoftRouting.GET("/receiver/zone_list", s.ListReceiverZoneCode)
	SoftRouting.GET("/zone/list_zone_code", s.ListZoneCode)
	SoftRouting.POST("/check_lane_enabled", s.CheckLaneEnable)
	SoftRouting.GET("/product/list", s.GetSmartRoutingProductList)
	SoftRouting.GET("/get_redis/new", s.GetRedisNew)
	SoftRouting.GET("/get_redis", s.GetRedis)
	SoftRouting.POST("/get_union_config", s.GetUnionCfg)
	SoftRouting.GET("/list_combination_info", s.ListIlhCombinationInfo)

	// parcel type definition
	parcelTypeDefinition := SoftRouting.Group("/parcel_type_definition")
	parcelTypeDefinition.POST("/create", s.CreateParcelTypeDefinition)
	parcelTypeDefinition.POST("/update", s.UpdateParcelTypeDefinition)
	parcelTypeDefinition.GET("/get", s.GetParcelTypeDefinition)
	parcelTypeDefinition.GET("/list", s.ListParcelTypeDefinition)
	parcelTypeDefinition.POST("/delete", s.DeleteParcelTypeDefinition)

	routes := adminGroup.GetRouters()
	return routes
}

// GetRoutingRule 获取routing rule详情
func (s *SoftRoutingFacade) GetRoutingRule(ctx *restful.Context) {
	req := soft_routing.RoutingRuleDetailReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := s.SoftRuleServer.GetRoutingRule(ctx.Ctx, int64(req.Id))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

// CreateRoutingRule 创建routing rule草稿
func (s *SoftRoutingFacade) CreateRoutingRule(ctx *restful.Context) {
	req := soft_routing.CreateRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	vErr := req.ValidateReq()
	if vErr != nil {
		apiutil.FailJSONResp(ctx, vErr, vErr.Error())
		return
	}
	ret, err := s.SoftRuleServer.CreateRule(ctx.Ctx, req.RuleName, req.ProductID, req.Priority, req.RoutingType, req.IsMultiProduct)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := s.convertRuleToDisplay(ret)
	apiutil.SuccessJSONResp(ctx, resp)
}

// UpdateRoutingRule 更新routing rule，包括更新成active态，或仍为draft态
func (s *SoftRoutingFacade) UpdateRoutingRule(ctx *restful.Context) {
	var req = soft_routing.UpdateRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	_, err := s.SoftRuleServer.UpdateRule(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// DeleteRule 删除草稿态的rule
func (s *SoftRoutingFacade) DeleteRule(ctx *restful.Context) {
	var req = soft_routing.DeleteRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	err := s.SoftRuleServer.DeleteRule(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// CopyRule 将rule复制一份出来，复制出来的rule为draft态
func (s *SoftRoutingFacade) CopyRule(ctx *restful.Context) {
	var req = soft_routing.CopyRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	err := s.SoftRuleServer.CopyRule(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// RollbackRule 将expired态的rule回滚到active态
func (s *SoftRoutingFacade) RollbackRule(ctx *restful.Context) {
	var req = soft_routing.RollbackRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := s.SoftRuleServer.RollbackRule(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// DisableRule 将active态的rule禁用，更改为expired态
func (s *SoftRoutingFacade) DisableRule(ctx *restful.Context) {
	var req = soft_routing.DisableRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, "get request, ", err), err.Error())
		return
	}
	err := s.SoftRuleServer.DisabledRule(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// DisableRule 将active态的rule禁用，更改为expired态
func (s *SoftRoutingFacade) BatchDisableRule(ctx *restful.Context) {
	var req = soft_routing.BatchDisableRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := s.SoftRuleServer.BatchDisabledRule(ctx.Ctx, req.IDList); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// CreateConf 创建product routing config
func (s *SoftRoutingFacade) CreateConf(ctx *restful.Context) {
	var req = soft_routing.CreateConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	config, err := s.RoutingConfigServer.CreateRoutingConfig(ctx.Ctx, &req, apiutil.GetUserID(ctx.Ctx))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, config)
}

// UpdateConf 更新product routing config信息
func (s *SoftRoutingFacade) UpdateConf(ctx *restful.Context) {
	var req = soft_routing.UpdateConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	config, err := s.RoutingConfigServer.UpdateRoutingConfig(ctx.Ctx, &req, apiutil.GetUserID(ctx.Ctx))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, config)
}

// ImportZoneCode 导入zone code文件
func (s *SoftRoutingFacade) ImportZoneCode(ctx *restful.Context) {
	file, _, err := ctx.ReadRequest().FormFile("file")
	if err != nil {
		rErr := srerr.With(srerr.ParamErr, 0, err)
		apiutil.FailJSONResp(ctx, rErr, rErr.Error())
		return
	}
	var req soft_routing.ImportZoneRequest
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if req.RoutingType == rule.SPXRoutingType {
		err := s.ZoneRepo.ImportZone(ctx.Ctx, file, req.RoutingType)
		if err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}
	} else {
		err := s.ZoneRepo.ImportProductZone(ctx.Ctx, file, req.RoutingType, req.ZoneType)
		if err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// ExportZoneCode 导出zone code文件
func (s *SoftRoutingFacade) ExportZoneCode(ctx *restful.Context) {
	var req soft_routing.ListReceiverZoneCodeReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	query := zone.NewZoneQuery()
	if req.ProductID > 0 {
		query = query.ByProductID(req.ProductID)
	}
	if req.DistrictId > 0 {
		query = query.ByDistrictId(req.DistrictId)
	}
	if req.ZoneCode != "" {
		query = query.ByZoneCode(req.ZoneCode)
	}
	if req.State != "" {
		query = query.ByState(req.State)
	}
	if req.City != "" {
		query = query.ByCity(req.City)
	}
	if req.District != "" {
		query = query.ByDistrict(req.District)
	}
	if req.Street != "" {
		query = query.ByStreet(req.Street)
	}
	query = query.ByZoneType(int(req.ZoneType))
	query.RoutingType = req.RoutingType
	// 限制最多导出100000行数据
	query.WithLimit(100000)

	var (
		excelFile *bytes.Buffer
		err       *srerr.Error
	)
	if req.RoutingType == rule.SPXRoutingType {
		excelFile, err = s.ZoneRepo.ExportZone(ctx.Ctx, query)
	} else {
		excelFile, err = s.ZoneRepo.ExportProductZone(ctx.Ctx, query)
	}
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.WriteExcelResp(ctx, "smart_routing_zone_code", excelFile.Bytes())
}

// UpdateZoneCode 更新地址信息
func (s *SoftRoutingFacade) UpdateZoneCode(ctx *restful.Context) {
	var req = soft_routing.UpdateZoneRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := s.ZoneRepo.UpdateZone(ctx.Ctx, req.ID, req.ZoneCode, req.DistrictId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// DeleteZoneCode delete地址信息
func (s *SoftRoutingFacade) DeleteZoneCode(ctx *restful.Context) {
	var req = soft_routing.DeleteZoneRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := s.ZoneRepo.DeleteZone(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

// ListRoutingRule 获取rule list
func (s *SoftRoutingFacade) ListRoutingRule(ctx *restful.Context) {
	req := soft_routing.ListRuleRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	q := rule.NewRuleQuery()
	if req.GetProductID() != 0 {
		q.FilterByProductID = true
		q.ProductID = req.GetProductID()
	}
	if req.GetStatus() >= 0 {
		q.FilterByStatus = true
		q.Status = req.GetStatus()
	}
	if req.GetRuleId() != 0 {
		q.FilterByID = true
		q.ID = req.GetRuleId()
	}
	if req.GetTaskID() != 0 {
		q.FilterByTaskID = true
		q.TaskID = req.GetTaskID()
	}
	q.RoutingType = req.RoutingType
	q.ByIsMultiProduct(req.IsMultiProduct)
	q.IsLocalForecast = req.IsLocalForecast
	q = q.WithPage(req.GetPageNo(), req.GetLimit())
	ret, total, err := s.SoftRuleServer.ListRule(ctx.Ctx, q)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := &soft_routing.ListRuleResponse{
		PageNo: req.GetPageNo(),
		Count:  req.GetLimit(),
		Total:  total,
		List:   []*soft_routing.RuleDisplay{},
	}
	taskNameMap := make(map[int]string)
	for _, res := range ret {
		taskName, exist := taskNameMap[res.TaskID]
		if !exist && res.TaskID != 0 {
			taskName, _ = s.ForecastTaskSrv.GetForecastTaskName(ctx.Ctx, res.TaskID)
			taskNameMap[res.TaskID] = taskName
		}
		res.TaskName = taskName
		resp.List = append(resp.List, s.convertRuleToDisplay(res))
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *SoftRoutingFacade) InitRoutingRule(ctx *restful.Context) {
	var req = soft_routing.InitRoutingRuleReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	var (
		ret *rule.RoutingRule
		err *srerr.Error
	)
	if req.IsMultiProduct && req.RoutingType == rule.IlhRoutingType {
		ret, err = s.SoftRuleServer.InitIlhProductRoutingRuleData(ctx.Ctx, req.ProductID, req.RoutingType, req.IsMultiProduct)
	} else if req.IsMultiProduct && req.RoutingType != rule.IlhRoutingType {
		ret, err = s.SoftRuleServer.InitMultiProductRoutingRuleData(ctx.Ctx, req.ProductID, req.RoutingType, req.IsMultiProduct)
	} else {
		ret, err = s.SoftRuleServer.InitialRoutingRuleData(ctx.Ctx, req.ProductID, req.RoutingType, req.IsMultiProduct)
	}
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if req.RoutingType == rule.CBRoutingType {
		ret.IsVolumeRouting = volumeutil.IsOpenVolumeRouting(ctx.Ctx)
	}

	apiutil.SuccessJSONResp(ctx, ret)
}

func (s *SoftRoutingFacade) ListWhsCode(ctx *restful.Context) {
	var req = soft_routing.ListProductWhsInfo{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	mockCtx := mockutil.MockCtx(ctx)
	whsId, ilhWhsInfoList, err := s.SoftRuleServer.ListWhsInfo(mockCtx, req.ProductID, req.IsMultiProduct)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp := soft_routing.ListWhsCodeResp{
		WhsCode:        whsId,
		IlhWhsInfoList: ilhWhsInfoList,
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *SoftRoutingFacade) ListILHWhsCode(ctx *restful.Context) {
	var req = soft_routing.ListILHWhsInfo{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	mockCtx := mockutil.MockCtx(ctx)
	ilhWhsInfoList, err := s.SoftRuleServer.ListILHWhsInfo(mockCtx, req.ILHLineID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ilhWhsInfoList)
}

func (s *SoftRoutingFacade) ListILHDestinationPort(ctx *restful.Context) {
	var req = soft_routing.ListILHDestinationPort{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	mockCtx := mockutil.MockCtx(ctx)
	destinationPortList, err := s.SoftRuleServer.ListILHDestinationPort(mockCtx, req.ILHLineID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, destinationPortList)
}

func (s *SoftRoutingFacade) GetConf(ctx *restful.Context) {
	var req = soft_routing.GetConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	config, err := s.RoutingConfigServer.GetRoutingConfigByID(ctx.Ctx, req.Id)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, config)
}

func (s *SoftRoutingFacade) ListConf(ctx *restful.Context) {
	var req = soft_routing.ListConfigRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	pageNo := objutil.Max(req.PageNo, 1)
	var offset int32
	limit := req.Limit
	if limit <= 0 {
		limit = 20
	}
	offset = (pageNo - 1) * limit
	configs, total, err := s.RoutingConfigServer.ListRoutingConfig(ctx.Ctx, req.ProductId, offset, limit)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := &soft_routing.ListConfigResponse{
		Count:  limit,
		PageNo: pageNo,
		Total:  total,
		List:   configs,
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *SoftRoutingFacade) ListResourceType(ctx *restful.Context) {
	var req = soft_routing.ListResourceTypeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := s.SoftRuleServer.ListResourceType(ctx.Ctx, req.ProductId, req.RoutingType, req.IsMultiProduct)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *SoftRoutingFacade) ListReceiverZoneCode(ctx *restful.Context) {
	var req = soft_routing.ListReceiverZoneCodeReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	query := zone.NewZoneQuery()
	if req.ProductID > 0 {
		query = query.ByProductID(req.ProductID)
	}
	if req.DistrictId > 0 {
		query = query.ByDistrictId(req.DistrictId)
	}
	if req.ZoneCode != "" {
		query = query.ByZoneCode(req.ZoneCode)
	}
	if req.State != "" {
		query = query.ByState(req.State)
	}
	if req.City != "" {
		query = query.ByCity(req.City)
	}
	if req.District != "" {
		query = query.ByDistrict(req.District)
	}
	if req.Street != "" {
		query = query.ByStreet(req.Street)
	}
	query = query.ByZoneType(int(req.ZoneType))
	query.RoutingType = req.RoutingType
	query = query.WithPage(int64(req.PageNO), int64(req.Limit))
	ret, total, err := s.ZoneRepo.GetReceiverZoneList(ctx.Ctx, query)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp := &soft_routing.ListReceiverZoneCodeResp{
		Total:  total,
		Count:  req.Limit,
		PageNo: req.PageNO,
		List:   ret,
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *SoftRoutingFacade) ListZoneCode(ctx *restful.Context) {
	var req = soft_routing.ListReceiverZoneCode{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := s.ZoneRepo.GetZoneCodeList(ctx.Ctx, req.ProductID, req.RoutingType, req.ZoneType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, struct {
		ZoneCodeList []string `json:"zone_code_list"`
	}{ZoneCodeList: ret})
}

// CB forecast task, submit forecast rule的时候会调用
func (s *SoftRoutingFacade) CheckLaneEnable(ctx *restful.Context) {
	var req = soft_routing.CheckLaneEnableReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if req.IsMultiProduct {
		if err := s.SoftRuleServer.CheckMultiProductLaneEnabled(ctx.Ctx, req.EnableList, req.ProductId); err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}
	} else {
		if err := s.SoftRuleServer.CheckLaneEnabled(ctx.Ctx, req.DisableList, req.EnableList, req.ProductId, false); err != nil {
			apiutil.FailJSONResp(ctx, err, err.Error())
			return
		}
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

func (s *SoftRoutingFacade) GetSmartRoutingProductList(ctx *restful.Context) {
	var req = soft_routing.GetSmartRoutingProductListReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	list, err := s.SoftRuleServer.GetSmartRoutingProductList(ctx.Ctx, req.RoutingType, req.IsMultiProduct)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, &soft_routing.GetSmartRoutingProductListResp{List: list})
}

// GetRedisNew 获取smr的redis
func (s *SoftRoutingFacade) GetRedisNew(ctx *restful.Context) {
	var req = soft_routing.GetRedisRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	redisClient, cErr := redisutil.Client()
	if cErr != nil {
		codisErr := srerr.With(srerr.CodisErr, nil, cErr)
		apiutil.FailJSONResp(ctx, codisErr, codisErr.Error())
		return
	}

	result, err := redisClient.Get(mockutil.MockCtx(ctx), req.Key).Result()
	if err != nil {
		codisErr := srerr.With(srerr.CodisErr, req.Key, err)
		apiutil.FailJSONResp(ctx, codisErr, codisErr.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, result)
}

// GetRedis 获取lps的redis
func (s *SoftRoutingFacade) GetRedis(ctx *restful.Context) {
	var req = soft_routing.GetRedisRequest{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := redisutil.GetDefaultInstance().Get(mockutil.MockCtx(ctx), req.Key).Result()
	if err != nil {
		codisErr := srerr.With(srerr.CodisErr, req.Key, err)
		apiutil.FailJSONResp(ctx, codisErr, codisErr.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, result)
}

func (s *SoftRoutingFacade) convertRuleToDisplay(rule *rule.RoutingRule) *soft_routing.RuleDisplay {
	resp := &soft_routing.RuleDisplay{
		ID:                 rule.ID,
		ProductID:          rule.ProductID,
		RuleName:           rule.RuleName,
		Status:             rule.Status,
		WhsId:              rule.WhsId,
		ItemCategoryLevel:  rule.ItemCategoryLevel,
		ItemCategoryID:     rule.ItemCategoryID,
		ParcelValueMax:     rule.ParcelValueMax,
		ParcelValueMin:     rule.ParcelValueMin,
		ParcelWeightMax:    rule.ParcelWeightMax,
		ParcelWeightMin:    rule.ParcelWeightMin,
		DgType:             rule.DgType,
		ParcelDimension:    rule.ParcelDimension,
		Priority:           rule.Priority,
		OperatedBy:         rule.OperatedBy,
		TaskID:             rule.TaskID,
		TaskName:           rule.TaskName,
		EffectiveStartTime: rule.EffectiveStartTime,
		CTime:              rule.CTime,
		MTime:              rule.MTime,
	}
	return resp
}

// ListIlhCombinationInfo 获取ILH组合信息, 包括该Product下Import ILH(CC), ILH和LM的组合信息
func (s *SoftRoutingFacade) ListIlhCombinationInfo(ctx *restful.Context) {
	var req soft_routing.ListIlhCombinationInfoRequest
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	combinationInfo, err := s.SoftRuleServer.ListIlhCombinationInfo(ctx.Ctx, req.ProductId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, combinationInfo)
}

func (s *SoftRoutingFacade) CreateParcelTypeDefinition(ctx *restful.Context) {
	allocation.CreateParcelTypeDefinition(ctx, s.ParcelTypeDefinitionService)
}

func (s *SoftRoutingFacade) UpdateParcelTypeDefinition(ctx *restful.Context) {
	allocation.UpdateParcelTypeDefinition(ctx, s.ParcelTypeDefinitionService)
}

func (s *SoftRoutingFacade) GetParcelTypeDefinition(ctx *restful.Context) {
	allocation.GetParcelTypeDefinition(ctx, s.ParcelTypeDefinitionService)
}

func (s *SoftRoutingFacade) ListParcelTypeDefinition(ctx *restful.Context) {
	allocation.ListParcelTypeDefinition(ctx, s.ParcelTypeDefinitionService)
}

func (s *SoftRoutingFacade) DeleteParcelTypeDefinition(ctx *restful.Context) {
	allocation.DeleteParcelTypeDefinition(ctx, s.ParcelTypeDefinitionService)
}
