package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_listener"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type BusinessAuditFacade struct {
	ListenerExecutor approval_listener.ListenerExecutor
}

func (f *BusinessAuditFacade) URLPatterns() []restful.Route {
	businessAudit := restful.NewRouterGroup("/api/admin/business_audit")
	businessAudit.POST("/listener", f.Listener)

	return businessAudit.GetRouters()
}

func (f *BusinessAuditFacade) Listener(ctx *restful.Context) {
	req := &approval_listener.ListenerRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	if err := f.ListenerExecutor.Listen(ctx.Ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}
