package adminfacade

import (
	volume_dashboard2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	batch_allocate3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	masking_priority2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	available_lh "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	routing2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/volumeroutingtask"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service/split_batch_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/masking_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	available_lh2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_listener"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/allocation"
	routingadmin "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_rule"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/forecast_chain"
	masking2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/admin/masking"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/routing_visualization"
	svService "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/sync_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
)

// ProviderSet 注入参数依赖
var ProviderSet = wire.NewSet(
	wire.Struct(new(AdminFacade), "*"),
	wire.Struct(new(VolumeRoutingFacade), "*"),
	wire.Struct(new(DebugFacade), "*"),
	wire.Struct(new(CCRoutingFacade), "*"),
	wire.Struct(new(SoftRoutingFacade), "*"),
	wire.Struct(new(PingFacade), "*"),
	wire.Struct(new(ProductPriorityFacade), "*"),
	wire.Struct(new(RoutingVisual), "*"),
	wire.Struct(new(ScheduleVisualFacade), "*"),
	wire.Struct(new(PostCodeFacade), "*"),
	wire.Struct(new(volume_dashboard2.MaskingVolumeDashboardApiFacade), "*"),
	wire.Struct(new(volume_dashboard2.RoutingVolumeDashboardApiFacade), "*"),
	wire.Struct(new(BusinessAuditFacade), "*"),
	wire.Struct(new(AvailableLHFacade), "*"),
	wire.Struct(new(LHCapacityFacade), "*"),
	wire.Struct(new(ILHForecastTaskFacade), "*"),
	allocation.ProviderSet,
	routingadmin.ProviderSet,
	volumerouting.ZoneGroupMgrProviderSet,
	volumerouting.ZoneMgrProviderSet,
	volumerouting.ZoneRuleMgrProviderSet,
	vrservice.ServiceProviderSet,
	vrrepo.ZoneGroupRepoProvideSet,
	vrrepo.ZoneRepoProviderSet,
	vrrepo.VolumeTaskRepoProviderSet,
	vrrepo.ZoneRuleRepoProviderSet,
	product.ProdRepoProviderSet,
	address.AddrRepoProviderSet,
	lpsclient.LpsApiProviderSet,
	cc_routing_rule.CCRoutingRuleRepoProviderSet,
	cc_routing.CCRoutingServiceProviderSet,
	rule.MaskRuleProviderSet,
	routing_rule.SoftRoutingProviderSet,
	routing.RoutingRepoProviderSet,
	routing_log.RoutingLogSrvProviderSet,
	routing_log.RoutingLogRepoProviderSet,
	lane.LaneServiceProviderSet,
	lfsclient.LfsClientPset,
	llsclient.LlsApiProviderSet,
	ruledata.SoftRuleRepoProviderSet,
	config.MaskConfigProviderSet,
	config.AllocationConfigProviderSet,
	chargeclient.RateApiProviderSet,
	rulevolume.MaskRuleVolumeProviderSet,
	layercache.NewLayerCache,
	routing.RoutingServiceProviderSet,
	routing.ILHRoutingServiceProviderSet,
	locationzone.LocationZoneProviderSet,
	ccclient.CCApiProviderSet,
	masking.ProductPriorityServiceSet,
	productpriority.RepoProviderSet,
	productpriority.BusinessProviderSet,
	masking2.NewLoadForecastVolumeConfig,
	masking2.NewMaskingForecast,
	masking2.NewAllocateStoreConsumer,
	masking_forecast.AllocateForecastServiceProviderSet,
	allocation2.NewAllocationService,
	allocation2.NewSoftRuleService,
	forecast_volume.NewForecastLocationVolumeServiceImpl,
	forecast_volume.NewForecastVolumeRepo,
	outercheck.AllOutCheckProviderSet,
	allocate_order_data_repo.AllocateOrderDataRepoProviderSet,
	routing_visualization.RoutingVisualProviderSet,
	audit_log_task.NewAuditLogTaskServer,
	audit_log.AuditLogServiceProviderSet,
	audit_log.AuditLogRepoProviderSet,
	select_lane.SmartRoutingServiceProviderSet,
	svService.ScheduleVisualServiceProviderSet,
	repository.ScheduleVisualRepoProviderSet,
	schedule_stat.NewMaskingForecastScheduleVisualStat,
	schedule_stat.NewMaskingScheduleVisualStatV2,
	schedule_stat.NewMaskingScheduleVisualStat,
	schedule_stat.NewScheduleVisualSet,
	schedule_visual.ScheduleCountStatProviderSet,
	forecasting_sub_task.ForecastingSubTaskServiceProviderSet,
	forecasting_sub_task.ForecastingSubTaskRepoProviderSet,
	forecast_chain.JobChainServiceProviderSet,
	masking2.NewDeleteMaskingSubTaskImpl,
	masking2.NewCheckMaskingProcessTaskImpl,
	masking2.NewGetForecastTotalCountImpl,
	masking2.NewAllocateStoreHbaseConsumer,
	masking2.NewScheduleCountStatTask,
	masking2.NewAllocateScheduleVisualTask,
	routing.RoutingPreCalcFeeProviderSet,
	routing2.NewSmartRoutingForecastTask,
	routing2.NewILHForecastTask,
	volumeroutingtask.NewZoneImportTask,
	volumeroutingtask.NewRuleEffectiveTask,
	repository2.MaskingProductOrderNumRepoProviderSet,
	repository2.RoutingProductOrderNumRepoProviderSet,
	volume_dashboard.MaskingVolumeServiceProviderSet,
	volume_dashboard.RoutingVolumeServiceProviderSet,
	masking2.NewReportMaskingVolumeTask,
	routing2.NewReportRoutingVolumeTask,
	sync_data.SyncDataProviderSet,
	masking2.NewStartBatchForecastUnitImpl,
	model.SplittingRuleProviderSet,
	model.OrderCollectorProviderSet,
	forecast_unit.BatchForecastUnitServiceProviderSet,
	batch_allocate_forecast.NewCreateBASubTask,
	masking2.NewAllocateHistory,
	masking2.NewParseBatchVolume,
	masking2.NewUpdateBatchAllocateForecastTaskImpl,
	batch_allocate.BatchMinuteOrderConfServiceProviderSet,
	batch_minute_order_conf.BatchMinuteOrderConfRepoProvider,
	volume_dashboard.VolumeChangeServiceProviderSet,
	batch_allocate_forecast.NewBAForecastToolProgressImpl,
	split_batch_chain.ExecutorProvider,
	batch_allocate.SplitBatchServerProvider,
	batch_allocate3.SplitBatchRepoProvider,
	masking2.NewSplitBatchAllocateOrdersTask,
	order.BatchAllocateOrderRepoProviderSet,
	batch_allocate2.NewBatchAllocateTask,
	batch_allocate2.NewAbnormalBatchAllocateTask,
	batch_allocate2.NewBatchAbnormalInspectionTask,
	batch_allocate2.NewPushOrderResultTask,
	batch_allocate2.NewUpdateOrderResultTask,
	batch_allocate2.NewClearOrderAndResultTask,
	allocation3.BatchAllocateServiceProviderSet,
	batch_allocate2.NewBatchAllocateHoldOrderConsumer,
	batch_allocate.GreyServiceProviderSet,
	batch_allocate2.NewUpdateAllocationPathTask,
	batch_allocate2.NewMakeUpAsyncAllocationLog,
	routing2.NewReportOrderCount,
	routing2.NewScheduleRule,
	rulevolume.CheckVolumeFinderProvider,
	approval_manager.ApprovalExecutorProvider,
	approval_listener.ListenerExecutorProvider,
	business_audit.BusinessAuditRepoProvider,
	auditclient.AuditApiProvider,
	whitelist2.ShopWhiteListServiceProviderSet,
	whitelist.ShopWhitelistRepoProviderSet,
	masking_priority.PickupPriorityServiceProviderSet,
	masking_priority2.PickupPriorityRepoProviderSet,
	batch_allocate2.NewBatchAllocateMonitor,
	vrrepo.DefaultSelectGroupRepoSet,
	vrservice.DefaultSelectGroupServiceSet,
	lnpclient.LnpApiProviderSet,
	parcel_type_definition.ParcelTypeDefinitionServiceProviderSet,
	parcel_type_definition2.ParcelTypeDefinitionRepoProviderSet,
	masking2.NewClearMaskingVolumeTask,
	wbcclient.WbcApiProviderSet,
	masking_forecast_hbase.NewHBHelper,
	pickup_efficiency_counter.PickupEffCounterProviderSet,
	spex_service.SpexServiceProviderSet,
	lcosclient.LcosApiProviderSet,
	allocate_volume_counter.NewCheckoutFulfillmentProductCounter,
	allocate_volume_counter.NewDeductVolumeCounter,
	available_lh.AvailableLHRepoProviderSet,
	available_lh2.AvailableLHServiceProviderSet,
	repo.LHCapacityRepoProviderSet,
	lh_capacity.LHCapacityServiceProviderSet,
	ilh_forecast_task.ILHForecastTaskServiceProviderSet,
	forecastrepo.ILHForecastTaskRepoProviderSet,
	volume_counter.ILHWeightCounterProviderSet,
)
