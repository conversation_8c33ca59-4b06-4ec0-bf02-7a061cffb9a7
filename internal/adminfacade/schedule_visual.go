package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type ScheduleVisualFacade struct {
	ScheduleVisualService schedule_visual.ScheduleVisualServiceInterface
}

func (s *ScheduleVisualFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/schedule_visual")
	routes.POST("/flow_stat", s.GetFlowStat)
	routes.POST("/result_stat", s.GetResultStat)
	routes.POST("/flow_stat_export", s.FlowStatExport)
	routes.POST("/result_stat_export", s.ResultStatExport)

	return routes.GetRouters()
}

func (s *ScheduleVisualFacade) GetFlowStat(ctx *restful.Context) {
	req := &admin_protocol.GetFlowStatRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.ScheduleVisualService.GetFlowStat(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *ScheduleVisualFacade) GetResultStat(ctx *restful.Context) {
	req := &admin_protocol.GetResultStatRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.ScheduleVisualService.GetResultStat(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *ScheduleVisualFacade) FlowStatExport(ctx *restful.Context) {
	req := &admin_protocol.GetFlowStatRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.ScheduleVisualService.FlowStatExport(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *ScheduleVisualFacade) ResultStatExport(ctx *restful.Context) {
	req := &admin_protocol.GetResultStatRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.ScheduleVisualService.ResultStatExport(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
