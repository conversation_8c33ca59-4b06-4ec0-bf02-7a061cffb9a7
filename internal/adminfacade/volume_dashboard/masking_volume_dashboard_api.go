package volume_dashboard

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type MaskingVolumeDashboardApiFacade struct {
	MaskingVolumeService volume_dashboard.MaskingVolumeService
}

func (s *MaskingVolumeDashboardApiFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/masking_volume")
	routes.POST("/search", s.Search)
	routes.GET("/dict", s.GetDict)
	routes.POST("/export", s.Export)

	return routes.GetRouters()
}

func (s *MaskingVolumeDashboardApiFacade) Search(ctx *restful.Context) {
	req := &admin_protocol.SearchMaskingVolumeRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.MaskingVolumeService.Search(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *MaskingVolumeDashboardApiFacade) GetDict(ctx *restful.Context) {
	resp, err := s.MaskingVolumeService.GetDict(ctx.Ctx)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (s *MaskingVolumeDashboardApiFacade) Export(ctx *restful.Context) {
	req := &admin_protocol.SearchMaskingVolumeRequest{}
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := s.MaskingVolumeService.Export(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
