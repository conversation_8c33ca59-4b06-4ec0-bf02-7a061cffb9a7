package volume_dashboard

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type RoutingVolumeDashboardApiFacade struct {
	RoutingVolumeService volume_dashboard.RoutingVolumeService
}

func (r *RoutingVolumeDashboardApiFacade) URLPatterns() []restful.Route {
	routes := restful.NewRouterGroup("/api/admin/routing_volume")
	routes.POST("/day/search", r.SearchByDay)
	routes.POST("/hour/search", r.SearchByHour)
	routes.GET("/dict", r.GetRoutingDict)
	routes.POST("/product_relate_dict", r.GetRoutingProductRelateDict)
	routes.POST("/day/export", r.ExportByDay)
	routes.POST("/hour/export", r.ExportByHour)

	return routes.GetRouters()
}

func (r *RoutingVolumeDashboardApiFacade) SearchByDay(ctx *restful.Context) {
	req := &admin_protocol.SearchByDayRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := r.RoutingVolumeService.SearchByDay(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (r *RoutingVolumeDashboardApiFacade) SearchByHour(ctx *restful.Context) {
	req := &admin_protocol.SearchByHourRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := r.RoutingVolumeService.SearchByHour(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (r *RoutingVolumeDashboardApiFacade) GetRoutingDict(ctx *restful.Context) {
	resp, err := r.RoutingVolumeService.GetRoutingDict(ctx.Ctx)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (r *RoutingVolumeDashboardApiFacade) GetRoutingProductRelateDict(ctx *restful.Context) {
	req := &admin_protocol.RoutingProductRelateDictRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := r.RoutingVolumeService.GetRoutingProductRelateDict(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (r *RoutingVolumeDashboardApiFacade) ExportByDay(ctx *restful.Context) {
	req := &admin_protocol.SearchByDayRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := r.RoutingVolumeService.ExportByDay(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

func (r *RoutingVolumeDashboardApiFacade) ExportByHour(ctx *restful.Context) {
	req := &admin_protocol.SearchByHourRequest{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	resp, err := r.RoutingVolumeService.ExportByHour(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
