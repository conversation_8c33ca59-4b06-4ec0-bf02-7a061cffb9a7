package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/routing"
	volume_dashboard2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/adminfacade/volume_dashboard"
)

type AdminFacade struct {
	PingFacade                      *PingFacade
	DebugFacade                     *DebugFacade
	VolumeRoutingFacade             *VolumeRoutingFacade
	SoftRoutingFacade               *SoftRoutingFacade
	MaskingResultPanelFacade        *allocation.MaskingResultPanelFacade
	AllocPathFacade                 *allocation.MaskingPathFacade
	AllocRuleFacade                 *allocation.MaskingRuleFacade
	RoutingForecastFacade           *routing.ForecastFacade
	CCRoutingRuleFacade             *CCRoutingFacade
	RoutingVisual                   *RoutingVisual
	MaskingForecastFacade           *allocation.MaskingForecastFacade
	MaskingRuleVolumeFacade         *allocation.MaskingRuleVolumeFacade
	ProductPriorityFacade           *ProductPriorityFacade
	ScheduleVisualFacade            *ScheduleVisualFacade
	MaskingVolumeDashboardApiFacade *volume_dashboard2.MaskingVolumeDashboardApiFacade
	RoutingVolumeDashboardApiFacade *volume_dashboard2.RoutingVolumeDashboardApiFacade
	PostCodeFacade                  *PostCodeFacade
	BusinessAuditFacade             *BusinessAuditFacade
	AvailableLHFacade               *AvailableLHFacade
	LHCapacityFacade                *LHCapacityFacade
	ILHForecastTaskFacade           *ILHForecastTaskFacade
}

func (api *AdminFacade) Register() {
	chassis.RegisterSchema("rest", api.VolumeRoutingFacade)
	chassis.RegisterSchema("rest", api.DebugFacade)
	chassis.RegisterSchema("rest", api.MaskingResultPanelFacade)
	chassis.RegisterSchema("rest", api.AllocPathFacade)
	chassis.RegisterSchema("rest", api.AllocRuleFacade)
	chassis.RegisterSchema("rest", api.SoftRoutingFacade)
	chassis.RegisterSchema("rest", api.RoutingForecastFacade)
	chassis.RegisterSchema("rest", api.CCRoutingRuleFacade)
	chassis.RegisterSchema("rest", api.PingFacade)
	chassis.RegisterSchema("rest", api.MaskingForecastFacade)
	chassis.RegisterSchema("rest", api.MaskingRuleVolumeFacade)
	chassis.RegisterSchema("rest", api.ProductPriorityFacade)
	chassis.RegisterSchema("rest", api.RoutingVisual)
	chassis.RegisterSchema("rest", api.ScheduleVisualFacade)
	chassis.RegisterSchema("rest", api.PostCodeFacade)
	chassis.RegisterSchema("rest", api.MaskingVolumeDashboardApiFacade)
	chassis.RegisterSchema("rest", api.RoutingVolumeDashboardApiFacade)
	chassis.RegisterSchema("rest", api.BusinessAuditFacade)
	chassis.RegisterSchema("rest", api.AvailableLHFacade)
	chassis.RegisterSchema("rest", api.LHCapacityFacade)
	chassis.RegisterSchema("rest", api.ILHForecastTaskFacade)
}
