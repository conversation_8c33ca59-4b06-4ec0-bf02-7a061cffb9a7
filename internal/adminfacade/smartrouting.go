package adminfacade

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

//type SmartRouting struct {
//}
/*

	func (s *SoftRoutingFacade) GetRoutingRule(ctx *restful.Context) {
	req := soft_routing.RoutingRuleDetailReq{}
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := s.SoftRuleServer.GetRoutingRule(ctx.Ctx, int64(req.Id))
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}
*/

func (s *SoftRoutingFacade) CreateOrUpdateSmrCfg(ctx *restful.Context) {
	// do in same tx,
	req := new(schema.SmrCreateRoutingCfgAndRoleMapReq)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	//sqls - appended,
	cfgtab := converToRoutingCfgtab(ctx.Ctx, req)
	/*
			roleInfo := prodentity.ToProductRoutingRole(productInfo)
		if roleInfo != nil {
			stmts = append(stmts, dbstmt.SQLStatement().Action(dbstmt.Insert).
				Table(persistent.ProductRoutingRoleHook).Inject(roleInfo))
		}
	*/
	roleMapTab := convertToRoutingRolemap(ctx.Ctx, req)
	var err *srerr.Error

	if req.ReqType == 1 {
		//done test
		err = s.RoutingConfigServer.InsertRoutingConfigLinemapping(ctx.Ctx, cfgtab, roleMapTab)
	}
	if req.ReqType == 2 {
		//done test,处理方式，新增校验，即可，
		err = s.RoutingConfigServer.UpdateRoutingConfigLinemapping(ctx.Ctx, cfgtab, roleMapTab)
		//msgs 需要处理的，
	}
	if err != nil {
		//reponse-error-to-caller
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, "")
}

func converToRoutingCfgtab(ctx context.Context, req *schema.SmrCreateRoutingCfgAndRoleMapReq) *ruledata.RoutingConfigTab {
	//
	rettab := new(ruledata.RoutingConfigTab)
	rettab.CTime = uint32(recorder.Now(ctx).Unix())

	if req.ProductRoutingConfig != nil {
		//
		rettab.ProductID = int64(req.ProductId)
		rettab.ProductName = req.SellerDisplayName

		rettab.MTime = uint32(recorder.Now(ctx).Unix())
		rettab.OperatedBy = req.Opetator

		rettab.CBMultiRoutingEnabled = req.ProductRoutingConfig.CBMultiRoutingEnabled
		rettab.LocalSmartRoutingEnabled = req.ProductRoutingConfig.LocalSmartRoutingEnabled
		rettab.SmartRoutingEnabled = req.ProductRoutingConfig.CBRoutingEnabled
		rettab.SpxSmartRoutingEnabled = req.ProductRoutingConfig.SpxSmartRoutingEnabled
		rettab.IlhSmartRoutingEnabled = req.ProductRoutingConfig.IlhSmartRoutingEnabled
		rettab.DefaultLaneCode = req.ProductRoutingConfig.DefaultLaneCode
		rettab.SmartRoutingToggle = req.ProductRoutingConfig.SmartRoutingToggle
		return rettab
	}
	return nil
}

func convertToRoutingRolemap(ctx context.Context, req *schema.SmrCreateRoutingCfgAndRoleMapReq) *ruledata.ProductRoutingRoleTab {
	rettab := new(ruledata.ProductRoutingRoleTab)
	rettab.ProductId = req.ProductId
	rettab.Mtime = int(recorder.Now(ctx).Unix())
	rettab.Ctime = int(recorder.Now(ctx).Unix())
	if req.ProductRoutingRole != nil {
		rettab.LocalRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.LocalRoutingRole)
		rettab.SpxRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.SpxRoutingRole)
		rettab.CBRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.CBRoutingRole)
		rettab.CBMultiRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.CBMultiRoutingRole)
		rettab.IlhRoutingRole = schema.ConvertRoutingRole(req.ProductRoutingRole.IlhRoutingRole)
		return rettab
	}
	return nil
}
