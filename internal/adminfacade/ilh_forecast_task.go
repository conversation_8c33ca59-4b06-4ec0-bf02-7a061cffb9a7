package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ILHForecastTaskFacade struct {
	ILHForecastTaskService ilh_forecast_task.ILHForecastTaskService
}

func (f *ILHForecastTaskFacade) URLPatterns() []restful.Route {
	ilhForecastTask := restful.NewRouterGroup("/api/admin/smart_routing/ilh_forecast_task")
	ilhForecastTask.POST("/create", f.Create)
	ilhForecastTask.POST("/update", f.Update)
	ilhForecastTask.POST("/delete", f.Delete)
	ilhForecastTask.GET("/list", f.List)
	ilhForecastTask.GET("/view", f.View)
	ilhForecastTask.GET("/result", f.GetResult)
	ilhForecastTask.GET("/export", f.Export)
	ilhForecastTask.POST("/copy", f.Copy)
	ilhForecastTask.POST("/deploy", f.Deploy)

	return ilhForecastTask.GetRouters()
}

// Create 创建ILH预测任务
func (f *ILHForecastTaskFacade) Create(ctx *restful.Context) {
	var req ilh_smart_routing.CreateILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层创建任务
	id, err := f.ILHForecastTaskService.Create(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ilh_smart_routing.CreateILHForecastTaskResp{ID: id})
}

// Update 更新ILH预测任务
func (f *ILHForecastTaskFacade) Update(ctx *restful.Context) {
	var req ilh_smart_routing.UpdateILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 获取操作人信息
	req.Operator, _ = apiutil.GetUserInfo(ctx.Ctx)

	// 调用Service层更新任务
	if err := f.ILHForecastTaskService.Update(ctx.Ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// View 获取ILH预测任务详情
func (f *ILHForecastTaskFacade) View(ctx *restful.Context) {
	var req ilh_smart_routing.GetILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层获取任务详情
	resp, err := f.ILHForecastTaskService.Get(ctx.Ctx, req.ID)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// List 获取ILH预测任务列表
func (f *ILHForecastTaskFacade) List(ctx *restful.Context) {
	var req ilh_smart_routing.ListILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 查询列表数据
	req.Pageno, _, req.Limit = apiutil.GetOffsetAndLimit(req.Pageno, req.Limit)
	resp, err := f.ILHForecastTaskService.List(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// Delete 删除ILH预测任务
func (f *ILHForecastTaskFacade) Delete(ctx *restful.Context) {
	var req ilh_smart_routing.DeleteILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层删除任务
	if err := f.ILHForecastTaskService.Delete(ctx.Ctx, req.ID); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, nil)
}

// Copy 复制ILH预测任务
func (f *ILHForecastTaskFacade) Copy(ctx *restful.Context) {
	var req ilh_smart_routing.CopyILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层复制任务
	id, err := f.ILHForecastTaskService.Copy(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, ilh_smart_routing.CopyILHForecastTaskResp{ID: id})
}

// GetResult 获取ILH预测任务结果
func (f *ILHForecastTaskFacade) GetResult(ctx *restful.Context) {
	var req ilh_smart_routing.GetILHForecastTaskResultReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层获取任务结果
	resp, err := f.ILHForecastTaskService.GetResult(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// Deploy 部署ILH预测任务
func (f *ILHForecastTaskFacade) Deploy(ctx *restful.Context) {
	var req ilh_smart_routing.DeployILHForecastTaskReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 校验请求参数
	if err := req.Validate(); err != nil {
		apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, err.Error()), err.Error())
		return
	}

	// 调用Service层部署任务
	resp, err := f.ILHForecastTaskService.Deploy(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}

// Export 导出ILH预测任务结果
func (f *ILHForecastTaskFacade) Export(ctx *restful.Context) {
	var req ilh_smart_routing.ExportILHForecastTaskResultReq
	if err := apiutil.SchemaParseValidate(ctx, &req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	// 调用Service层导出任务结果
	resp, err := f.ILHForecastTaskService.Export(ctx.Ctx, &req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	apiutil.SuccessJSONResp(ctx, resp)
}
