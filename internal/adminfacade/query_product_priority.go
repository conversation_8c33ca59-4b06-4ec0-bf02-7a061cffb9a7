package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

func (p *ProductPriorityFacade) ListGroupIdwithActiveCfg(ctx *restful.Context) {
	req := new(admin_protocol.ListActiveGroupId)
	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.ProductPriorityService.ListActiveGroups(ctx.Ctx, req)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}
