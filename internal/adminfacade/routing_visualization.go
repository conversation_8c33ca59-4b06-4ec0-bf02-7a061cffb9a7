package adminfacade

import (
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/routing_visualization"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type RoutingVisual struct {
	RoutingVisualService routing_visualization.RoutingVisualService
}

func (r *RoutingVisual) URLPatterns() []restful.Route {
	////调度可视化数据展示接口
	routingVisual := restful.NewRouterGroup("api/admin/visual")
	routingVisual.POST("/list", r.GetList)
	routingVisual.GET("/detail", r.GetDetail)
	routingVisual.POST("/debug/detail", r.DebugVisualDetail)
	return routingVisual.GetRouters()
}

func (r *RoutingVisual) GetList(ctx *restful.Context) {
	req := &routing.RoutingVisualListReq{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	result, err := r.RoutingVisualService.GetList(ctx.Ctx, req.RequestId, req.ForderId, req.SlsTn, req.Pageno, req.Count)

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)

}

func (r *RoutingVisual) GetDetail(ctx *restful.Context) {
	rowKey := ctx.ReadQueryParameter("row_key")

	if rowKey == "" {
		apiutil.FailJSONResp(ctx, nil, "row_key can not be empty")
		return
	}
	detail, err := r.RoutingVisualService.GetDetail(ctx.Ctx, rowKey)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, detail)
}

func (p *RoutingVisual) DebugVisualDetail(ctx *restful.Context) {
	reqLogEntry := &routing_log.RoutingLog{}
	if err := apiutil.SchemaParseValidate(ctx, reqLogEntry); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	for _, stage := range reqLogEntry.RoutingResult.SoftCriteriaFilterProcess.StageList {
		for i, item := range stage.FactorCombination {
			dataMap := item.ProcessData.(map[string]interface{})
			for key, value := range dataMap {
				switch v := value.(type) {
				case json.Number:
					var err error
					dataMap[key], err = v.Float64()
					if err != nil {
						apiutil.FailJSONResp(ctx, srerr.New(srerr.ParamErr, nil, "invalid number"), err.Error())
						return
					}
					stage.FactorCombination[i].ProcessData = dataMap
				}
			}
		}
	}
	mockCtx := mockutil.MockCtx(ctx)
	result := p.RoutingVisualService.LogToResponse(mockCtx, reqLogEntry, "")
	apiutil.SuccessJSONResp(ctx, result)
}
