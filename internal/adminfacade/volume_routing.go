package adminfacade

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

type VolumeRoutingFacade struct {
	ZoneGroupMgr              volumerouting.ZoneGroupManager
	ZoneMgr                   volumerouting.ZoneManager
	ZoneRuleMgr               volumerouting.ZoneRuleMgr
	DefaultSelectGroupService vrservice.DefaultSelectGroupService
}

func (p *VolumeRoutingFacade) URLPatterns() []restful.Route {
	adminGroup := restful.NewRouterGroup("/api/admin")
	//volume routing module
	volumeRouting := adminGroup.Group("/volume_routing")
	volumeRouting.GET("/dict", p.Dict)
	volumeRouting.GET("/zone_group/list", p.ZoneGroupListPage)
	volumeRouting.POST("/zone_group/create", p.ZoneGroupCreate)
	volumeRouting.POST("/zone_group/edit", p.ZoneGroupEdit)
	volumeRouting.GET("/zone_group/view", p.ZoneGroupView)
	volumeRouting.POST("/zone_group/capacity/save", p.GroupZoneCapacitySave)
	volumeRouting.GET("/zone_group/capacity/list", p.GroupZoneCapacityPage)
	volumeRouting.GET("/zone_group/capacity/export", p.GroupZoneCapacityExport)
	volumeRouting.POST("/zone_group/capacity/upload", p.GroupZoneCapacityUpload)
	volumeRouting.GET("/zone/location/list", p.ZoneLocationListPage)
	volumeRouting.GET("/zone/location/export", p.ZoneLocationExport)
	volumeRouting.POST("/zone/location/delete", p.ZoneLocationDelete)
	volumeRouting.GET("/zone/postcode/list", p.ZonePostcodeListPage)
	volumeRouting.GET("/zone/postcode/export", p.ZonePostcodeExport)
	volumeRouting.POST("/zone/postcode/delete", p.ZonePostcodeDelete)
	volumeRouting.GET("/zone/cep_range/list", p.ZoneCEPRangeListPage)
	volumeRouting.GET("/zone/cep_range/export", p.ZoneCEPRangeExport)
	volumeRouting.POST("/zone/cep_range/delete", p.ZoneCepRangeDelete)
	volumeRouting.POST("/zone/import", p.ZoneImport)
	volumeRouting.GET("/zone/test_export", p.TestZoneExport)  // for async task logic validation
	volumeRouting.POST("/zone/test_import", p.TestZoneImport) // for async task logic validation
	volumeRouting.GET("/history/list", p.OperationHistoryListPage)
	volumeRouting.GET("/history/download", p.OperationHistoryDownload)
	volumeRouting.GET("/rule/list", p.RuleListPage)
	volumeRouting.POST("/rule/create", p.RuleCreate)
	volumeRouting.GET("/rule/export_template", p.RuleExportTemplate)
	volumeRouting.POST("/rule/edit/save_draft", p.RuleEditDraft)
	volumeRouting.POST("/rule/edit/submit", p.RuleEditSave)
	volumeRouting.POST("/rule/delete", p.RuleDelete)
	volumeRouting.POST("/rule/disable/check", p.RuleDisableCheck)
	volumeRouting.POST("/rule/disable", p.RuleDisable)
	volumeRouting.GET("/rule/zone_limit/list", p.RuleLimitListPage)
	volumeRouting.GET("/rule/zone_limit/export", p.RuleLimitExport)
	volumeRouting.POST("/rule/zone_limit/import", p.RuleLimitImport)
	volumeRouting.GET("/rule/view", p.RuleView)
	volumeRouting.POST("/volume_rule_list", p.RuleList)
	volumeRouting.POST("/forecast/zone/import", p.ForecastZoneImport)
	volumeRouting.POST("/line_info", p.GetLineInfoByProductId)
	volumeRouting.POST("/copy_volume_rule", p.CopyVolumeRule)
	volumeRouting.POST("/all_group_id", p.GetAllGroupId)

	defaultSelectGroup := adminGroup.Group("/default_select_group")
	defaultSelectGroup.GET("/list", p.DefaultSelectGroupListByPage)
	defaultSelectGroup.POST("/create", p.DefaultSelectGroupCreate)
	defaultSelectGroup.POST("/update", p.DefaultSelectGroupUpdate)
	defaultSelectGroup.POST("/delete", p.DefaultSelectGroupDelete)
	defaultSelectGroup.GET("/detail", p.DefaultSelectGroupGet)
	defaultSelectGroup.POST("/get_select_group_by_product", p.DefaultSelectGroupSearchByProduct)

	routes := adminGroup.GetRouters()
	return routes
}

func (p *VolumeRoutingFacade) Dict(ctx *restful.Context) {
	apiutil.SuccessJSONResp(ctx, p.ZoneGroupMgr.Dict(ctx.Ctx))
}

func (p *VolumeRoutingFacade) ZoneGroupListPage(ctx *restful.Context) {
	param := new(schema.ZoneGroupPageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneGroupMgr.GetZoneGroupListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) ZoneGroupCreate(ctx *restful.Context) {
	param := new(schema.ZoneGroupCreateOrUpdateParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneGroupMgr.CreateZoneGroup(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneGroupEdit(ctx *restful.Context) {
	param := new(schema.ZoneGroupCreateOrUpdateParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneGroupMgr.UpdateZoneGroup(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneGroupView(ctx *restful.Context) {
	param := new(schema.ZoneGroupIdParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneGroupMgr.GetZoneGroupDetail(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) GroupZoneCapacitySave(ctx *restful.Context) {
	param := new(schema.ZoneGroupZoneCapacityParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneGroupMgr.SaveZoneEstimatedCapacity(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) GroupZoneCapacityPage(ctx *restful.Context) {
	param := new(schema.ZoneEstimatedCapacityPageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	results, err := p.ZoneGroupMgr.GetZoneCapacityListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, results)
}

func (p *VolumeRoutingFacade) GroupZoneCapacityExport(ctx *restful.Context) {
	param := new(schema.ZoneEstimatedCapacityExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	b, err := p.ZoneGroupMgr.ExportZoneCapacityList(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	fileName := fmt.Sprintf("zone_capacity_%s.xlsx", timeutil.FormatDate(timeutil.GetCurrentTime(ctx.Ctx)))
	apiutil.WriteExcelResp(ctx, fileName, b)
}

func (p *VolumeRoutingFacade) GroupZoneCapacityUpload(ctx *restful.Context) {
	_ = ctx.ReadRequest().ParseMultipartForm(16 * (1 << 20))
	file, _, fErr := ctx.ReadRequest().FormFile("file")
	if fErr != nil {
		e := srerr.With(srerr.ParamErr, nil, fErr)
		apiutil.FailJSONResp(ctx, e, e.Error())
		return
	}
	defer file.Close()
	groupId := ctx.ReadRequest().FormValue("zone_group_id")
	routingTypeStr := ctx.ReadRequest().FormValue("routing_type")
	routingType, _ := strconv.Atoi(routingTypeStr)
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneGroupMgr.UploadZoneCapacity(ctx.Ctx, groupId, file, routingType, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneLocationListPage(ctx *restful.Context) {
	param := new(schema.LocationZonePageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneMgr.GetLocationZoneListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)

}

func (p *VolumeRoutingFacade) ZonePostcodeListPage(ctx *restful.Context) {
	param := new(schema.PostcodeZonePageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneMgr.GetPostcodeZoneListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) ZoneCEPRangeListPage(ctx *restful.Context) {
	param := new(schema.CepRangeZonePageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneMgr.GetCepRangeZoneListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) ZoneLocationDelete(ctx *restful.Context) {
	param := new(schema.ZoneLocationDeleteParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.ZoneMgr.DeleteLocationZone(ctx.Ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZonePostcodeDelete(ctx *restful.Context) {
	param := new(schema.ZonePostcodeDeleteParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.ZoneMgr.DeletePostcodeZone(ctx.Ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneCepRangeDelete(ctx *restful.Context) {
	param := new(schema.ZoneCepRangeDeleteParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.ZoneMgr.DeleteCepRangeZone(ctx.Ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneImport(ctx *restful.Context) {
	param := new(schema.ZoneImportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.BuildZoneImportTask(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ForecastZoneImport(ctx *restful.Context) {
	param := new(schema.ZoneRuleLimitImportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	ret, err := p.ZoneRuleMgr.ForecastZoneRuleLimitImport(ctx.Ctx, param, operator)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) GetLineInfoByProductId(ctx *restful.Context) {
	param := new(schema.LineInfoRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneRuleMgr.GetLineInfo(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) CopyVolumeRule(ctx *restful.Context) {
	param := new(schema.CopyVolumeRuleParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneRuleMgr.CopyVolumeRuleWithNewStatus(ctx.Ctx, int64(param.RuleId), enum.VolumeRuleStatusDraft, 0, constant.ForecastType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) GetAllGroupId(ctx *restful.Context) {
	param := new(schema.GetAllGroupParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	ret, err := p.ZoneRuleMgr.GetAllGroup(ctx.Ctx, param.RoutingType, param.IsForecastType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) ZoneLocationExport(ctx *restful.Context) {
	param := new(schema.ZoneExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	param.ZoneType = enum.ZoneTypeLocation
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.BuildZoneExportTask(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZonePostcodeExport(ctx *restful.Context) {
	param := new(schema.ZoneExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	param.ZoneType = enum.ZoneTypePostcode
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.BuildZoneExportTask(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) ZoneCEPRangeExport(ctx *restful.Context) {
	param := new(schema.ZoneExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	param.ZoneType = enum.ZoneTypeCEPRange
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.BuildZoneExportTask(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) TestZoneExport(ctx *restful.Context) {
	param := new(schema.ZoneExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.TestZoneExport(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) TestZoneImport(ctx *restful.Context) {
	param := new(schema.ZoneImportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneMgr.TestZoneImport(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) RuleListPage(ctx *restful.Context) {
	param := new(schema.ZoneRulePageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneRuleMgr.GetZoneRuleListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) RuleCreate(ctx *restful.Context) {
	param := new(schema.ZoneRuleCreateParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	result, err := p.ZoneRuleMgr.CreateZoneRuleDraft(ctx.Ctx, param, operator)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) RuleExportTemplate(ctx *restful.Context) {
	param := new(schema.ZoneRuleExportTemplateParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	var productIdList []int64
	productIdStrList := strings.Split(param.ProductIdList, ",")
	for _, productIdStr := range productIdStrList {
		productId, pErr := strconv.ParseInt(productIdStr, 10, 64)
		if pErr != nil {
			apiutil.FailJSONResp(ctx, srerr.With(srerr.ParamErr, "parse productId error", pErr), pErr.Error())
			return
		}
		productIdList = append(productIdList, productId)
	}
	data, err := p.ZoneRuleMgr.ExportRuleLimitTemplate(ctx.Ctx, productIdList, param.RoutingType)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	filename := objutil.Merge("volume_limit_template", timeutil.FormatDate(timeutil.GetCurrentTime(ctx.Ctx)))
	apiutil.WriteExcelResp(ctx, filename, data)
}

func (p *VolumeRoutingFacade) RuleEditDraft(ctx *restful.Context) {
	param := new(schema.ZoneRuleEditParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneRuleMgr.EditZoneRuleSaveDraft(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) RuleEditSave(ctx *restful.Context) {
	param := new(schema.ZoneRuleEditParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneRuleMgr.EditZoneRuleSubmit(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) RuleDelete(ctx *restful.Context) {
	param := new(schema.ZoneRuleIdParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	if err := p.ZoneRuleMgr.DeleteZoneRule(ctx.Ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) RuleDisableCheck(ctx *restful.Context) {
	param := new(schema.ZoneRuleDisableCheckParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneRuleMgr.CheckDisableZoneRule(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) RuleDisable(ctx *restful.Context) {
	param := new(schema.ZoneRuleIdParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	if err := p.ZoneRuleMgr.DisableZoneRule(ctx.Ctx, param, operator); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) RuleLimitListPage(ctx *restful.Context) {
	param := new(schema.ZoneRuleLimitPageParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneRuleMgr.GetZoneRuleLimitListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) RuleLimitExport(ctx *restful.Context) {
	param := new(schema.ZoneRuleLimitExportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	data, err := p.ZoneRuleMgr.ExportZoneRuleLimit(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	filename := objutil.Merge("volume_zone_limit_", timeutil.FormatDate(timeutil.GetCurrentTime(ctx.Ctx)))
	apiutil.WriteExcelResp(ctx, filename, data)
}

func (p *VolumeRoutingFacade) RuleLimitImport(ctx *restful.Context) {
	param := new(schema.ZoneRuleLimitImportParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	operator, _ := apiutil.GetUserInfo(ctx.Ctx)
	ret, err := p.ZoneRuleMgr.ImportZoneRuleLimit(ctx.Ctx, param, operator)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, ret)
}

func (p *VolumeRoutingFacade) RuleView(ctx *restful.Context) {
	param := new(schema.ZoneRuleIdParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneRuleMgr.GetZoneRuleDetail(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) RuleList(ctx *restful.Context) {
	param := new(schema.RuleListRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneRuleMgr.GetRuleListByStatus(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) OperationHistoryListPage(ctx *restful.Context) {
	param := new(schema.VolumeRoutingHistoryQueryParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	result, err := p.ZoneMgr.GetTaskHistoryListPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)
}

func (p *VolumeRoutingFacade) OperationHistoryDownload(ctx *restful.Context) {
	param := new(schema.VolumeRoutingHistoryDownloadParam)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	filename, data, err := p.ZoneMgr.Download(ctx.Ctx, param.TaskId)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.WriteExcelResp(ctx, filename, data)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupListByPage(ctx *restful.Context) {
	param := new(routing_volume.ListDefaultSelectGroupByPageRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.DefaultSelectGroupService.ListByPage(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupCreate(ctx *restful.Context) {
	param := new(routing_volume.CreateDefaultSelectGroupRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.DefaultSelectGroupService.Create(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupUpdate(ctx *restful.Context) {
	param := new(routing_volume.UpdateDefaultSelectGroupRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.DefaultSelectGroupService.Update(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupGet(ctx *restful.Context) {
	param := new(routing_volume.GetDefaultSelectGroupRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.DefaultSelectGroupService.Get(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupDelete(ctx *restful.Context) {
	param := new(routing_volume.DeleteDefaultSelectGroupRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	err := p.DefaultSelectGroupService.Delete(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, nil)
}

func (p *VolumeRoutingFacade) DefaultSelectGroupSearchByProduct(ctx *restful.Context) {
	param := new(routing_volume.SearchDefaultSelectGroupByProductIdRequest)
	if err := apiutil.SchemaParseValidate(ctx, param); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	resp, err := p.DefaultSelectGroupService.SearchByProduct(ctx.Ctx, param)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, resp)
}
