package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"math"
)

func FillVolume(ctx context.Context, originVolume int64, volumeType int) int64 {
	if originVolume != DefaultInputVolume {
		return originVolume
	}
	if volumeType == VolumeTypeMin {
		return DefaultMinVolume
	}
	if volumeType == VolumeTypeMax {
		return DefaultMaxVolume
	}
	logger.CtxLogErrorf(ctx, "unknown type:%v, just return 0")
	return 0
}

// e.g 4分40秒，拿第四分钟的累积（即list[0]+list[1]+list[2]+list[3]），和第5分钟的加权平均（即list[4]/60 * 40)
func GetBatchMinTarget(lastSecond int64, batchMinuteVolumeList []float64, minDailyTarget uint32) uint32 {
	lastMinute := lastSecond / 60
	leftSecond := lastSecond - (lastMinute * 60)

	if lastMinute > 1439 {
		return minDailyTarget
	}

	var totalRatio float64
	// 获取截止last minute为止，一共占比多少volume ratio
	for i := 0; i < int(lastMinute); i++ {
		totalRatio += batchMinuteVolumeList[i]
	}
	// 补足超出last minute的部分
	if lastSecond != 0 {
		totalRatio += batchMinuteVolumeList[lastMinute] / float64(60) * float64(leftSecond)
	}
	// 向上取整
	return uint32(math.Ceil(totalRatio * float64(minDailyTarget)))
}
