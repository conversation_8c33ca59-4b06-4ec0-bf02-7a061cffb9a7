package batch_minute_order_conf

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchMinuteOrderConfRepo interface {
	GetTab(ctx context.Context, condition map[string]interface{}) (BatchMinuteOrderConfigTab, *srerr.Error)
	GetTabList(ctx context.Context, condition map[string]interface{}) ([]BatchMinuteOrderConfigTab, *srerr.Error)
	CreateTabByObj(ctx context.Context, tab *BatchMinuteOrderConfigTab) *srerr.Error
	UpdateTabByObj(ctx context.Context, condition map[string]interface{}, tab *BatchMinuteOrderConfigTab) *srerr.Error
	UpdateTabByMap(ctx context.Context, condition, value map[string]interface{}) *srerr.Error
}

type BatchMinuteOrderConfRepoImpl struct {
}

func NewBatchMinuteOrderConfRepoImpl() *BatchMinuteOrderConfRepoImpl {
	return &BatchMinuteOrderConfRepoImpl{}
}

func (b *BatchMinuteOrderConfRepoImpl) GetTab(ctx context.Context, condition map[string]interface{}) (BatchMinuteOrderConfigTab, *srerr.Error) {
	var tab BatchMinuteOrderConfigTab
	if err := dbutil.Select(ctx, BatchMinuteOrderConfigTabHook, condition, &tab); err != nil {
		return tab, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tab, nil
}

func (b *BatchMinuteOrderConfRepoImpl) GetTabList(ctx context.Context, condition map[string]interface{}) ([]BatchMinuteOrderConfigTab, *srerr.Error) {
	var tabs []BatchMinuteOrderConfigTab
	if err := dbutil.Select(ctx, BatchMinuteOrderConfigTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tabs, nil
}

func (b *BatchMinuteOrderConfRepoImpl) CreateTabByObj(ctx context.Context, tab *BatchMinuteOrderConfigTab) *srerr.Error {
	if err := dbutil.Insert(ctx, tab, dbutil.ModelInfo{
		MaskProductId: tab.MaskProductId,
		ModelName:     tab.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (b *BatchMinuteOrderConfRepoImpl) UpdateTabByObj(ctx context.Context, condition map[string]interface{}, tab *BatchMinuteOrderConfigTab) *srerr.Error {
	err, rowsAffected := dbutil.UpdateByObj(ctx, BatchMinuteOrderConfigTabHook, condition, tab, dbutil.ModelInfo{MaskProductId: tab.MaskProductId})
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if rowsAffected == 0 {
		return srerr.New(srerr.DatabaseErr, nil, "no rows affected")
	}

	return nil
}

func (b *BatchMinuteOrderConfRepoImpl) UpdateTabByMap(ctx context.Context, condition, value map[string]interface{}) *srerr.Error {
	err, rowsAffected := dbutil.UpdateWithAffectedRows(ctx, BatchMinuteOrderConfigTabHook, condition, value, dbutil.ModelInfo{})
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if rowsAffected == 0 {
		return srerr.New(srerr.DatabaseErr, nil, "no rows affected")
	}

	return nil
}
