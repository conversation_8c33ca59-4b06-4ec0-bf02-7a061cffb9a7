package batch_minute_order_conf

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

const (
	batchMinuteOrderConfigTableName = "batch_minute_order_config_tab"
)

var (
	BatchMinuteOrderConfigTabHook = &BatchMinuteOrderConfigTab{}
)

//batch allocate运力关于每分钟的订单分布比例（=每分钟订单数 / 该天总订单数）
type BatchMinuteOrderConfigTab struct {
	Id               uint64 `gorm:"column:id" json:"id"`
	MaskProductId    uint64 `gorm:"column:mask_product_id" json:"mask_product_id"`       //每个mask都会对应一份自己的订单分布比例
	ConfType         int    `gorm:"column:conf_type" json:"conf_type"`                   //0:default 1:normal; 2:campaign
	MinuteOrderRatio []byte `gorm:"column:minute_order_ratio" json:"minute_order_ratio"` //一天内，每分钟的订单分布比例
	ConfStatus       int    `gorm:"column:conf_status" json:"conf_status"`               //1:active 2:expired
	CTime            int64  `gorm:"column:ctime" json:"ctime"`
	MTime            int64  `gorm:"column:mtime" json:"mtime"`
}

func (b *BatchMinuteOrderConfigTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchMinuteOrderConfigTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchMinuteOrderConfigTab) TableName() string {
	return batchMinuteOrderConfigTableName
}

func (b *BatchMinuteOrderConfigTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            b.Id,
		ModelName:     b.TableName(),
		MaskProductId: b.MaskProductId,
	}
}
