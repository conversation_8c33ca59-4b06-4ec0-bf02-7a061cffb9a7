package batch_allocate

// 场景
type BatchAllocationScenario uint8

const (
	BatchAllocationScenarioTypeNormal     BatchAllocationScenario = 1
	BatchAllocationScenarioTypeReallocate BatchAllocationScenario = 2
)

const (
	VolumeTypeMin = 1
	VolumeTypeMax = 2
)

const (
	DefaultInputVolume = -1
	DefaultMinVolume   = 0
	DefaultMaxVolume   = 999999999
)

type OrderStatus uint8

/*
Init -> BatchAllocated -> BatchPushed

Init -> SingleAllocated
BatchAllocated -> SingleAllocated
BatchPushed -> SingleAllocated
*/

const (
	OrderStatusTypeInit            OrderStatus = 1
	OrderStatusTypeSyncAllocated   OrderStatus = 2 // Sync流程下调度
	OrderStatusTypeAsyncAllocated  OrderStatus = 3 // Async流程下调度
	OrderStatusTypeAsyncPushed     OrderStatus = 4 // Async流程下调度后推送成功
	OrderStatusTypeAsyncFailed     OrderStatus = 5 // Async流程下调度失败
	OrderStatusTypeAsyncFailPushed OrderStatus = 6 // Async流程下调度失败后推送成功
)

type Target struct {
	MinVolume          uint32
	MaxVolume          uint32
	MaxCodVolume       uint32
	MaxBulkyVolume     uint32
	MaxHighValueVolume uint32
	MaxDgVolume        uint32
}

// 订单回推&拉取异常场景错误码
const (
	IllegalEsf              float64 = -1
	IllegalEsfRetCode       int     = 30000
	UnmarshalShippingFeeErr int     = 30001
	IllegalEsfMessage       string  = "illegal fulfillment shipping fee"

	Success int = 0
)
