package allocation

import (
	"context"
	"reflect"
	"testing"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"github.com/agiledragon/gomonkey/v2"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func TestBatchAllocateServiceImpl_parseCountryVolume(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		r                     *rulevolume2.MaskRuleVolumeTab
		maskProductID         int64
		lastSecond            int64
		batchMinuteVolumeList []float64
	}
	tests := []struct {
		name string
		args args
		want []*algorithm_client.ProductTargetVolumeBo
	}{
		// TODO: Add test cases.
		{
			args: args{
				r: &rulevolume2.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume2.MaskDefaultVolumeLimitItem{
						{},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			maskVolumeCounterImpl := &volumecounter.MaskVolumeCounterImpl{}
			patch = gomonkey.ApplyMethod(reflect.TypeOf(maskVolumeCounterImpl), "GetProductVolume", func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskProductID, id int64, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
				return 0, nil
			})
			b := &BatchAllocateServiceImpl{
				MaskVolumeCounter: maskVolumeCounterImpl,
				logInfo:           &BatchAllocationLogInfo{},
			}
			got := b.parseCountryVolume(ctx, tt.args.r, tt.args.maskProductID, tt.args.lastSecond, tt.args.batchMinuteVolumeList)
			t.Logf("got = %v", got)
			patch.Reset()
		})
	}
}

func TestBatchAllocateServiceImpl_aggregateVolume(t *testing.T) {
	b := &BatchAllocateServiceImpl{}
	type args struct {
		orderResults []*order.OrderResultTab
		orderInfoMap map[uint64]AllocateOrderInfo
	}
	tests := []struct {
		name  string
		args  args
		want  map[int]*orderCountDetail
		want1 map[int]map[string]*orderCountDetail
		want2 map[int]map[string]*orderCountDetail
	}{
		// TODO: Add test cases.
		{
			args: args{
				orderResults: []*order.OrderResultTab{{OrderID: 11}},
				orderInfoMap: map[uint64]AllocateOrderInfo{
					11: {
						ParcelAttrMap: map[int]order.ParcelAttribute{0: {IsDg: true, IsCod: true, IsBulky: true, IsHighValue: true}},
						ZoneCodeList:  []string{"zone1"},
						RouteCodeList: []string{"route1"},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := b.aggregateVolume(tt.args.orderResults, tt.args.orderInfoMap)
			t.Logf("%+v", got[0])
			t.Logf("%+v", got1[0]["zone1"])
			t.Logf("%+v", got2[0]["route1"])
		})
	}
}

func TestBatchAllocateServiceImpl_deductSingleAllocatedVolume(t *testing.T) {
	ctx := context.Background()
	maskVolumeCounterImpl := &volumecounter.MaskVolumeCounterImpl{}
	pickupEffCounterImpl := &pickup_efficiency_counter.PickupEffCounterImpl{}
	b := &BatchAllocateServiceImpl{}
	var patchIncrByMaskVolumeByDate, patchIncrByProductVolumeByDate, patchBatchIncrByZoneVolumeByDate, patchBatchIncrByRouteVolumeByDate, patchBatchIncrShopFulfillmentProducts *gomonkey.Patches
	type args struct {
		maskProductID           int64
		singleAllocatedOrders   map[uint64]struct{}
		orderInfoMap            map[uint64]AllocateOrderInfo
		allocateTime            time.Time
		productGroupCodeMapping map[int64]string
	}
	tests := []struct {
		name  string
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			args: args{
				singleAllocatedOrders: map[uint64]struct{}{1: {}},
				orderInfoMap: map[uint64]AllocateOrderInfo{
					1: {
						ZoneCodeList:        []string{"zone1"},
						RouteCodeList:       []string{"route1"},
						ParcelAttrMap:       map[int]order.ParcelAttribute{80016: {IsDg: true, IsCod: true, IsBulky: true, IsHighValue: true}},
						BatchAllocateResult: 80016,
					},
				},
			},
			setup: func() {
				b = &BatchAllocateServiceImpl{
					MaskVolumeCounter: maskVolumeCounterImpl,
					PickupEffCounter:  pickupEffCounterImpl,
				}
				patchIncrByMaskVolumeByDate = gomonkey.ApplyMethod(reflect.TypeOf(maskVolumeCounterImpl), "IncrByMaskVolumeByDate", func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskingProductID int64, value int64, date time.Time) error {
					return nil
				})
				patchIncrByProductVolumeByDate = gomonkey.ApplyMethod(reflect.TypeOf(maskVolumeCounterImpl), "IncrByProductVolumeByDate", func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, maskProductID, productID int64, groupCode string, value int64, date time.Time, parcelType parcel_type_definition.ParcelType) error {
					return nil
				})
				patchBatchIncrByZoneVolumeByDate = gomonkey.ApplyMethod(reflect.TypeOf(maskVolumeCounterImpl), "BatchIncrByZoneVolumeByDate", func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, items []volumecounter.ZoneVolumeItem, date time.Time) error {
					return nil
				})
				patchBatchIncrByRouteVolumeByDate = gomonkey.ApplyMethod(reflect.TypeOf(maskVolumeCounterImpl), "BatchIncrByRouteVolumeByDate", func(v *volumecounter.MaskVolumeCounterImpl, ctx context.Context, items []volumecounter.RouteVolumeItem, date time.Time) error {
					return nil
				})
				patchBatchIncrShopFulfillmentProducts = gomonkey.ApplyMethod(reflect.TypeOf(pickupEffCounterImpl), "BatchIncrShopFulfillmentProducts", func(v *pickup_efficiency_counter.PickupEffCounterImpl, ctx context.Context, maskProductId int64, date time.Time, items []pickup_efficiency_counter.ShopFulfillmentItem) *srerr.Error {
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			b.deductSingleAllocatedVolume(ctx, tt.args.maskProductID, tt.args.singleAllocatedOrders, tt.args.orderInfoMap, tt.args.allocateTime, tt.args.productGroupCodeMapping)
		})
		if patchIncrByMaskVolumeByDate != nil {
			patchIncrByMaskVolumeByDate.Reset()
		}
		if patchIncrByProductVolumeByDate != nil {
			patchIncrByProductVolumeByDate.Reset()
		}
		if patchBatchIncrByZoneVolumeByDate != nil {
			patchBatchIncrByZoneVolumeByDate.Reset()
		}
		if patchBatchIncrByRouteVolumeByDate != nil {
			patchBatchIncrByRouteVolumeByDate.Reset()
		}
		if patchBatchIncrShopFulfillmentProducts != nil {
			patchBatchIncrShopFulfillmentProducts.Reset()
		}
	}
}

func TestBatchAllocateServiceImpl_checkOrderAllocated(t *testing.T) {
	type fields struct {
		BatchAllocateOrderRepo order.BatchAllocateOrderRepo
	}
	type args struct {
		ctx          context.Context
		orderInfoMap map[uint64]AllocateOrderInfo
		dayIndex     int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[uint64]struct{}
	}{
		{
			name: "no allocated orders",
			fields: fields{
				BatchAllocateOrderRepo: &order.BatchAllocateOrderRepoImpl{},
			},
			args: args{
				ctx:          context.Background(),
				orderInfoMap: map[uint64]AllocateOrderInfo{1: {}, 2: {}},
				dayIndex:     1,
			},
			want: map[uint64]struct{}{},
		},
		{
			name: "orders allocated in db",
			fields: fields{
				BatchAllocateOrderRepo: &order.BatchAllocateOrderRepoImpl{},
			},
			args: args{
				ctx:          context.Background(),
				orderInfoMap: map[uint64]AllocateOrderInfo{1: {}, 2: {}},
				dayIndex:     1,
			},
			want: map[uint64]struct{}{1: {}},
		},
		{
			name: "orders allocated in redis",
			fields: fields{
				BatchAllocateOrderRepo: &order.BatchAllocateOrderRepoImpl{},
			},
			args: args{
				ctx:          context.Background(),
				orderInfoMap: map[uint64]AllocateOrderInfo{1: {}, 2: {}},
				dayIndex:     1,
			},
			want: map[uint64]struct{}{1: {}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := &BatchAllocateServiceImpl{
				BatchAllocateOrderRepo: tt.fields.BatchAllocateOrderRepo,
			}

			// 模拟数据库调用
			patchDB := gomonkey.ApplyMethod(reflect.TypeOf(tt.fields.BatchAllocateOrderRepo), "BatchGetOrderResultByOrderID", func(_ *order.BatchAllocateOrderRepoImpl, ctx context.Context, orderIDList []uint64, dayIndex int) ([]*order.OrderResultTab, *srerr.Error) {
				if tt.name == "orders allocated in db" {
					return []*order.OrderResultTab{{OrderID: 1}}, nil
				}
				return nil, nil
			})
			defer patchDB.Reset()

			// 模拟 Redis 调用
			patchRedis := gomonkey.ApplyFunc(redisutil.MGet, func(ctx context.Context, keys []string) ([]interface{}, error) {
				if tt.name == "orders allocated in redis" {
					// 模拟 Redis MGet 返回一个非 nil 的值，表示找到了 key
					return []interface{}{"any_value"}, nil
				}
				// 模拟 Redis MGet 返回 nil，表示 key 不存在
				return []interface{}{nil, nil}, nil
			})
			defer patchRedis.Reset()

			if got := b.checkOrderAllocated(tt.args.ctx, tt.args.orderInfoMap, tt.args.dayIndex); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("checkOrderAllocated() = %v, want %v", got, tt.want)
			}
		})
	}
}
