package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

func reportSuccessEvent(ctx context.Context, interfaceName, data string) {
	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, interfaceName, monitoring.StatusSuccess, data)
}

func reportErrorEvent(ctx context.Context, interfaceName, data string) {
	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, interfaceName, monitoring.StatusError, data)
}
