package allocation

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"github.com/panjf2000/ants/v2"
	uuid "github.com/satori/go.uuid"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/aisclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	batch_allocate3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	constant2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	lastSecondOneDay int64 = 86399
)

var (
	goroutinePool     *ants.Pool
	goroutinePoolSize = 16
)

type BatchAllocateServiceImpl struct {
	BatchAllocateOrderRepo      order.BatchAllocateOrderRepo
	MaskRuleVolumeService       rulevolume.MaskRuleVolumeService
	MaskRuleRepo                rule.IMaskRuleRepo
	MaskVolumeCounter           volumecounter.MaskVolumeCounter
	SplitBatchRepo              batch_allocate2.SplitBatchRepo
	BatchMinuteOrderConfService batch_allocate3.BatchMinuteOrderConfService
	AlgoClient                  algorithm_client.AlgoClientInterface
	LpsApi                      lpsclient.LpsApi
	AisApi                      aisclient.AisApi
	LayerCache                  *layercache.LevelCache
	PickupEffCounter            pickup_efficiency_counter.PickupEffCounter
	ShopWhitelistService        whitelist.ShopWhitelistService

	// 私有变量，用来埋点allocation log
	logInfo *BatchAllocationLogInfo
}

type BatchAllocationLogInfo struct {
	BatchMinOrderConf []float64
	ZoneVolumeMap     map[int64]map[string]*allocation3.VolumeInfo
	RouteVolumeMap    map[int64]map[string]*allocation3.VolumeInfo
	CountryVolumeMap  map[int64]*allocation3.VolumeInfo
	LastSecond        int64
}

type orderCountDetail struct {
	total     int64
	cod       int64
	bulky     int64
	highValue int64
	dg        int64
}

type BatchAllocateService interface {
	BatchAllocate(ctx context.Context, maskProductID uint64) *srerr.Error
	AbnormalBatchAllocate(ctx context.Context, maskProductID uint64) *srerr.Error
	AbnormalInspection(ctx context.Context) *srerr.Error
	PushOrderResult(ctx context.Context, date time.Time) *srerr.Error
}

func NewBatchAllocateService(orderRepo order.BatchAllocateOrderRepo,
	maskRuleVolumeService rulevolume.MaskRuleVolumeService,
	maskRuleRepo rule.IMaskRuleRepo,
	maskVolumeCounter volumecounter.MaskVolumeCounter,
	splitBatchRepo batch_allocate2.SplitBatchRepo,
	batchMinuteOrderConfService batch_allocate3.BatchMinuteOrderConfService,
	lpsApi lpsclient.LpsApi,
	layerCache *layercache.LevelCache,
	pickupEffCounter pickup_efficiency_counter.PickupEffCounter,
	shopWhitelistService whitelist.ShopWhitelistService,
) *BatchAllocateServiceImpl {
	once := sync.Once{}
	once.Do(func() {
		size := goroutinePoolSize
		if configutil.GetBatchAllocationLogConf(context.Background()).GoroutinePoolSize != 0 {
			size = configutil.GetBatchAllocationLogConf(context.Background()).GoroutinePoolSize
		}
		goroutinePool, _ = ants.NewPool(size, ants.WithNonblocking(true))
	})

	return &BatchAllocateServiceImpl{
		BatchAllocateOrderRepo:      orderRepo,
		MaskRuleVolumeService:       maskRuleVolumeService,
		MaskRuleRepo:                maskRuleRepo,
		MaskVolumeCounter:           maskVolumeCounter,
		SplitBatchRepo:              splitBatchRepo,
		BatchMinuteOrderConfService: batchMinuteOrderConfService,
		AlgoClient:                  algorithm_client.NewAlgoClient(),
		LpsApi:                      lpsApi,
		AisApi:                      aisclient.NewAisApi(),
		LayerCache:                  layerCache,
		PickupEffCounter:            pickupEffCounter,
		ShopWhitelistService:        shopWhitelistService,
	}
}

func (b *BatchAllocateServiceImpl) GetProductDetailWithCache(ctx context.Context, productID int) (*lpsclient.ProductDetailInfo, *srerr.Error) {
	key := strconv.Itoa(productID)
	var ret lpsclient.ProductDetailInfo
	if err := b.LayerCache.Get(ctx, constant.LevelCacheProductDetailInfo, key, &ret,
		layercache.WithLoader(b.getProductDetailLoader),
		layercache.WithWarmExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for GetProductDetail|get_cache_fail|key=%+v, value=%+v", key, ret)
		return nil, srerr.With(srerr.GetLpsProductErr, key, err)
	}

	return &ret, nil
}

func (b *BatchAllocateServiceImpl) getProductDetailLoader(ctx context.Context, key string) (interface{}, error) {
	productID, sErr := strconv.Atoi(key)
	if sErr != nil {
		return nil, sErr
	}

	detail, err := b.LpsApi.GetProductDetail(ctx, productID)
	if err != nil {
		return nil, err
	}

	return detail, nil
}

func (b *BatchAllocateServiceImpl) BatchAllocate(ctx context.Context, maskProductID uint64) *srerr.Error {
	condition := map[string]interface{}{
		"mask_product_id = ?": maskProductID,
		"batch_status = ?":    batch_allocate2.BatchPending,
	}
	batchInfo, exist, err := b.SplitBatchRepo.GeFirstBatchByCondition(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get pending batch failed | maskProductID=%d, err=%v", maskProductID, err)
		return err
	}

	// 不存在需要调度的Batch
	if !exist {
		return nil
	}

	return b.batchAllocateByBatchInfo(ctx, batchInfo)
}

func (b *BatchAllocateServiceImpl) AbnormalBatchAllocate(ctx context.Context, maskProductID uint64) *srerr.Error {
	conf := configutil.GetBatchAllocateConf()

	// 拉取失败且需要重试的Batch
	condition := map[string]interface{}{
		"mask_product_id = ?": maskProductID,
		"batch_status = ?":    batch_allocate2.BatchFailed,
		"retry_times <= ?":    conf.MaxRetryTime,
	}
	batchInfo, exist, err := b.SplitBatchRepo.GeFirstBatchByCondition(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get abnormal batch info failed | maskProductID=%d, err=%v", maskProductID, err)

		return err
	}

	// 不存在需要调度的Batch
	if !exist {
		return nil
	}

	// 如果还没到达最大重试次数则继续做Batch Allocate
	if batchInfo.RetryTimes < conf.MaxRetryTime {
		batchInfo.RetryTimes += 1
		if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
			logger.CtxLogErrorf(ctx, "UpdateBatchObject failed: %v", err)
		}

		return b.batchAllocateByBatchInfo(ctx, batchInfo)
	}

	// 关闭退化功能
	if conf.DisableDegradation {
		logger.CtxLogInfof(ctx, "disable degradation, batch %d need manual processing", batchInfo.ID)

		return nil
	}

	// 到达最大重试次数的情况下退化为Single
	if err := b.singleAllocateByBatchInfo(ctx, int64(maskProductID), batchInfo); err != nil {
		msg := fmt.Sprintf("single allocate failed | batch=%d, err=%v", batchInfo.ID, err)
		reportErrorEvent(ctx, monitoring.BatchDegradation, msg)
		logger.CtxLogErrorf(ctx, msg)

		return err
	}

	batchInfo.RetryTimes += 1
	batchInfo.BatchStatus = batch_allocate2.BatchDone
	if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateBatchObject failed: %v", err)
	}

	logger.CtxLogInfof(ctx, "batch %d degradation to single allocate", batchInfo.ID)
	reportSuccessEvent(ctx, monitoring.BatchDegradation, strconv.Itoa(int(batchInfo.ID)))

	return nil
}

// batchAllocateByBatchInfo 执行Batch Allocate（Allocate + Batch状态更新）
func (b *BatchAllocateServiceImpl) batchAllocateByBatchInfo(ctx context.Context, batchInfo batch_allocate2.BAOnlineBatchTab) *srerr.Error {
	logger.CtxLogInfof(ctx, "start batch allocate, batch=%d", batchInfo.ID)
	requestID := uuid.NewV4().String() + "|" + fmt.Sprintf("batch=%d", batchInfo.ID) // nolint
	ctx = logger.NewLogContext(ctx, requestID)
	ctx = requestid.SetToCtx(ctx, requestID)
	b.logInfo = &BatchAllocationLogInfo{}

	// 1.抢占Batch，更新状态为Processing
	batchInfo.BatchStatus = batch_allocate2.BatchProcessing
	if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateBatchObject failed: %v", err)
		return err
	}

	// 2.执行Batch Allocate
	if err := b.executeBatchAllocate(ctx, batchInfo); err != nil {
		batchInfo.BatchStatus = batch_allocate2.BatchFailed
		if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
			logger.CtxLogErrorf(ctx, "UpdateBatchObject failed: %v", err)
			return err
		}
		return err
	}

	// 3.更新Batch状态为Done
	batchInfo.BatchStatus = batch_allocate2.BatchDone
	if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateBatchObject failed: %v", err)
		return err
	}

	return nil
}

// executeBatchAllocate 执行Batch Allocate（不做Batch的状态更新）
func (b *BatchAllocateServiceImpl) executeBatchAllocate(ctx context.Context, batchInfo batch_allocate2.BAOnlineBatchTab) *srerr.Error {
	ctx, endFunc := monitor.AwesomeReportTransactionStart2(ctx)

	startTime := timeutil.GetCurrentTime(ctx)

	// 1.根据Batch信息获取指定的订单
	orderList, err := b.BatchAllocateOrderRepo.GetOrderListByIDRange(ctx, batchInfo.LastOrderTableName, batchInfo.FirstOrderDbID, batchInfo.LastOrderDbID)
	if err != nil {
		msg := fmt.Sprintf("get order list failed | table=%s, start=%d, end=%d, err=%v",
			batchInfo.LastOrderTableName, batchInfo.FirstOrderDbID, batchInfo.LastOrderDbID, err)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return err
	}

	getOrderTime := timeutil.GetCurrentTime(ctx)
	allocation3.ReportBatchAllocateTimeConsume(
		int(batchInfo.MaskProductID), allocation3.ExecuteBatchAllocationScene, allocation3.GetOrderField, "", getOrderTime.Sub(startTime).Seconds())

	// 找不到数据，异常情况
	if len(orderList) == 0 {
		msg := fmt.Sprintf("can not find order list by batch info | batchID=%d", batchInfo.ID)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return srerr.New(srerr.BatchAllocateOrderNotFoundError, batchInfo, msg)
	}

	// 2.SDK参数组装
	allocateTime := timeutil.GetLocalTime(ctx)
	algoReq, orderInfoMap, orderInfoMapForLog, err := b.assembleSDKParam(ctx, batchInfo.ID, int64(batchInfo.MaskProductID), orderList, allocateTime)
	if err != nil {
		msg := fmt.Sprintf("assemble algo sdk parameter failed | err=%v", err)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return err
	}

	assembleParamTime := timeutil.GetCurrentTime(ctx)
	allocation3.ReportBatchAllocateTimeConsume(
		int(batchInfo.MaskProductID), allocation3.ExecuteBatchAllocationScene, allocation3.ParamAssembleField, "", assembleParamTime.Sub(getOrderTime).Seconds())

	// 3.SDK计算
	var allocate *algorithm_client.BatchAllocateRespBo
	businessTaskID := fmt.Sprintf("batch_%d_%d", batchInfo.ID, timeutil.GetCurrentUnixTimeStamp(ctx))

	// 检查开关是否命中
	if configutil.IsSwitch(ctx, aisclient.BatchAllocateSDKSwitch, businessTaskID) {
		// 灰度开关命中，调用 AIS API 进行计算
		aisResp, aisErr := b.AisApi.ComputeTaskWithEncodedInput(ctx, businessTaskID, algoReq)
		if aisErr != nil {
			// AIS 调用失败，记录错误日志并使用 SDK 兜底
			msg := fmt.Sprintf("ais compute task failed, fallback to SDK | err=%v", aisErr)
			logger.CtxLogErrorf(ctx, msg)
			// 继续执行，使用 SDK 兜底
		} else {
			// AIS 调用成功，使用 AIS 结果
			allocate = aisResp
		}
	}

	// 如果开关未命中或 AIS 调用失败，使用 SDK 兜底
	if allocate == nil {
		algoResp, algoErr := b.AlgoClient.BatchAllocate(ctx, algoReq)
		if algoErr != nil {
			msg := fmt.Sprintf("SDK fallback failed | err=%v", algoErr)
			endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
			logger.CtxLogErrorf(ctx, msg)
			return srerr.With(srerr.AlgoError, nil, algoErr)
		}
		allocate = algoResp
	}

	sdkCalcTime := timeutil.GetCurrentTime(ctx)
	allocation3.ReportBatchAllocateTimeConsume(
		int(batchInfo.MaskProductID), allocation3.ExecuteBatchAllocationScene, allocation3.SDKCalcField, "", sdkCalcTime.Sub(assembleParamTime).Seconds())

	// 4.检查Batch的状态是否正常（Processing中）
	if !b.checkBatchStatusProcessing(ctx, batchInfo.ID) {
		msg := fmt.Sprintf("batch not processing, break the processing flow | batchID=%d", batchInfo.ID)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return nil
	}

	// 5.记录调度结果(落表+更新运力)
	dayIndex, err := order.SplitDayIndexByTableName(batchInfo.LastOrderTableName)
	if err != nil {
		msg := fmt.Sprintf("split table name to get day index failed | tableName=%s, err=%v", batchInfo.LastOrderTableName, err)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return err
	}

	if err := b.createAllocateResult(ctx, int64(batchInfo.MaskProductID), allocate, orderInfoMap, dayIndex, allocateTime); err != nil {
		msg := fmt.Sprintf("create allocate result failed | err=%v", err)
		endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusError, msg)
		logger.CtxLogErrorf(ctx, msg)

		return err
	}

	saveResultTime := timeutil.GetCurrentTime(ctx)
	allocation3.ReportBatchAllocateTimeConsume(
		int(batchInfo.MaskProductID), allocation3.ExecuteBatchAllocationScene, allocation3.CreateResultField, "", saveResultTime.Sub(sdkCalcTime).Seconds())
	allocation3.ReportBatchAllocateTimeConsume(
		int(batchInfo.MaskProductID), allocation3.ExecuteBatchAllocationScene, allocation3.WholeProcessField, "", saveResultTime.Sub(startTime).Seconds())

	msg := fmt.Sprintf("batch allocate succeeded | MaskProductID=%d, BatchID=%d", batchInfo.MaskProductID, batchInfo.ID)
	logger.CtxLogInfof(ctx, msg)
	endFunc(monitoring.CatModuleBatchAllocate, monitoring.ExecuteBatchAllocate, monitoring.StatusSuccess, msg)

	b.reportShippingFee(ctx, batchInfo.MaskProductID, allocate)

	// batch allocation log
	batchAllocationLogConf := configutil.GetBatchAllocationLogConf(ctx)
	// batchAllocationLogConf.EnableLog: 控制Async链路Kafka的发送
	// batchAllocationLogConf.EnableKafkaSend: 控制Async链路batch Kafka的发送
	if batchAllocationLogConf.EnableLog && batchAllocationLogConf.EnableKafkaSend {
		//go出去异步处理
		if sErr := goroutinePool.Submit(func() {
			b.sendAllocationLogBytes(ctx, allocate, batchInfo, orderInfoMapForLog, orderList)
		}); sErr != nil {
			logger.CtxLogErrorf(ctx, "submit go routine to send batch allocation log err:%v", sErr)
		}
	}

	return nil
}

func (b *BatchAllocateServiceImpl) checkBatchStatusProcessing(ctx context.Context, batchID uint64) bool {
	condition := map[string]interface{}{
		"id = ?": batchID,
	}
	batchInfo, err := b.SplitBatchRepo.GetBatchByCondition(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetBatchFailed: %v", err)
		return true
	}

	return batchInfo.BatchStatus == batch_allocate2.BatchProcessing
}

func (b *BatchAllocateServiceImpl) assembleSDKParam(
	ctx context.Context, batchID uint64, maskProductID int64, orderList []*order.BatchAllocateHoldOrderTab, allocateTime time.Time,
) (*algorithm_client.BatchAllocateReqBo, map[uint64]AllocateOrderInfo, map[uint64]*allocation3.AllocateOrderInfoForLog, *srerr.Error) {
	var (
		todayStart         = timeutil.GetDate(allocateTime).Unix()
		lastSecond         = orderList[len(orderList)-1].Ctime - todayStart //每批最后一个订单的second，ctime 和 todayStart都需要时second级别的
		orderBos           = make([]*algorithm_client.OrderBo, 0, len(orderList))
		orderInfoMap       = make(map[uint64]AllocateOrderInfo, len(orderList))
		orderInfoMapForLog = make(map[uint64]*allocation3.AllocateOrderInfoForLog, len(orderList))
		shopIds            = make([]int64, 0)
	)
	if lastSecond < 0 {
		lastSecond = lastSecondOneDay
	}
	b.logInfo.LastSecond = lastSecond

	softRule, err := b.MaskRuleRepo.GetEffectiveRuleByCache(ctx, maskProductID, rule_mode.MplOrderRule, allocation.BatchAllocate)
	if err != nil {
		return nil, nil, nil, err
	}

	for _, o := range orderList {

		var (
			zoneCodeTarget            []string
			routeCodeTarget           []string
			productInfoBos            = make([]*algorithm_client.ProductInfoBo, 0, len(o.FulfillmentHardResult))
			fulfillmentShippingFeeMap = make(map[int]order.FulfillmentShippingFeeInfo, len(o.FulfillmentHardResult))
			parcelAttrMap             = make(map[int]order.ParcelAttribute, len(o.FulfillmentHardResult))
			inputs                    []int
		)

		for _, fulfillmentProductInfo := range o.FulfillmentHardResult {
			// product info bos
			productInfoBos = append(productInfoBos, &algorithm_client.ProductInfoBo{
				FulfillmentProductId: uint64(fulfillmentProductInfo.FulfillmentProductId),
				ShippingFee:          fulfillmentProductInfo.AllocationShippingFee,
				OrderAttr: algorithm_client.OrderAttrDetail{
					Cod:       fulfillmentProductInfo.ParcelAttribute.IsCod,
					Bulky:     fulfillmentProductInfo.ParcelAttribute.IsBulky,
					HighValue: fulfillmentProductInfo.ParcelAttribute.IsHighValue,
					Dg:        fulfillmentProductInfo.ParcelAttribute.IsDg,
				},
			})

			// zone code target
			zoneVolumes, err := b.MaskRuleVolumeService.MatchLocsToZones(
				ctx, maskProductID, int64(fulfillmentProductInfo.FulfillmentProductId),
				o.PickupAddress.LocationIdList, o.DeliverAddress.LocationIdList, rule_mode.MplOrderRule,
				o.PickupAddress.Postcode, o.DeliverAddress.Postcode, allocation.BatchAllocate,
			)
			if err != nil {
				logger.CtxLogErrorf(ctx, "MatchLocsToZones failed: %v", err)
				return nil, nil, nil, err
			}
			for _, z := range zoneVolumes {
				zoneCodeTarget = append(zoneCodeTarget, z.LocCode)
			}

			// route code target
			routeVolumes, err := b.MaskRuleVolumeService.MatchLocsToRoutes(
				ctx, maskProductID, int64(fulfillmentProductInfo.FulfillmentProductId),
				o.PickupAddress.LocationIdList, o.DeliverAddress.LocationIdList, rule_mode.MplOrderRule,
				o.PickupAddress.Postcode, o.DeliverAddress.Postcode, allocation.BatchAllocate,
			)
			if err != nil {
				logger.CtxLogErrorf(ctx, "match location to routes failed: %v", err)
				return nil, nil, nil, err
			}
			for _, r := range routeVolumes {
				routeCodeTarget = append(routeCodeTarget, r.LocCode)
			}

			// fulfillment shipping fee
			fulfillmentShippingFeeMap[fulfillmentProductInfo.FulfillmentProductId] = order.FulfillmentShippingFeeInfo{
				FulfillmentShippingFee: fulfillmentProductInfo.FulfillmentShippingFee,
				Detail:                 fulfillmentProductInfo.FulfillmentShippingFeeDetail,
			}
			// parcel attribute
			parcelAttrMap[fulfillmentProductInfo.FulfillmentProductId] = fulfillmentProductInfo.ParcelAttribute

			inputs = append(inputs, fulfillmentProductInfo.FulfillmentProductId)
		}

		zoneCodeTarget = objutil.RemoveDuplicateString(zoneCodeTarget)
		routeCodeTarget = objutil.RemoveDuplicateString(routeCodeTarget)
		orderInfoMap[o.OrderID] = AllocateOrderInfo{
			OrderID:                       o.OrderID,
			ZoneCodeList:                  zoneCodeTarget,
			RouteCodeList:                 routeCodeTarget,
			FulfillmentShippingFeeInfoMap: fulfillmentShippingFeeMap,
			ParcelAttrMap:                 parcelAttrMap,
			RequestTimeStamp:              o.RequestTimeStamp,
			ShopID:                        o.ShopID,
		}
		orderInfoMapForLog[o.OrderID] = &allocation3.AllocateOrderInfoForLog{
			OrderID:       o.OrderID,
			ZoneCodeList:  zoneCodeTarget,
			RouteCodeList: routeCodeTarget,
			Inputs:        inputs,
		}

		if softRule.BatchRuleDetail.BatchAllocationRuleConfig.PickupEfficiencyWhitelistEnabled {
			// 开启了Pickup Efficiency Whitelist，需要在进去Algo SDK之前首先筛选一遍，因为Whitelist是最高优的
			var hitWhitelist bool
			productInfoBos, hitWhitelist = b.pickupEffiWhitelistFilter(ctx, o.OrderID, maskProductID, o.ShopID, productInfoBos)
			orderInfoMapForLog[o.OrderID].PickupEffWhitelistInfo = allocation3.PickupEffWhitelistInfo{
				HitWhiteList:      hitWhitelist,
				WhitelistProducts: getProductIDsByProductInfoBos(productInfoBos),
			}
		}

		orderBos = append(orderBos, &algorithm_client.OrderBo{
			OrderId:         o.OrderID,
			ProductInfo:     productInfoBos,
			ZoneCodeTarget:  zoneCodeTarget,
			RouteCodeTarget: routeCodeTarget,
			RegionTarget:    envvar.GetCIDLower(),
			ShopID:          uint64(o.ShopID),
		})
		shopIds = append(shopIds, o.ShopID)
	}

	batchMinuteVolumeList, err := b.getBatchMinuteVolumeList(ctx, softRule)
	if err != nil {
		return nil, nil, nil, err
	}

	targetVolume, err := b.getTargetBos(ctx, softRule, maskProductID, lastSecond, batchMinuteVolumeList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "getTargetBos failed: %v", err)
		return nil, nil, nil, err
	}

	pickupEffEnable, pickupEffBudget, pickupEffSla, pickupEffUrl, shopFulfillmentProducts := b.getPickupEffParams(
		ctx, maskProductID, softRule, shopIds, allocateTime, lastSecond, batchMinuteVolumeList)

	algoConf := configutil.GetAlgoConfig()
	batchAllocateReqBo := &algorithm_client.BatchAllocateReqBo{
		BatchNo:      batchID,
		SoftRuleId:   uint64(softRule.Id),  // Algo没用到吧？
		VolumeRuleId: softRule.LocVolumeId, // Algo没用到吧？
		TargetVolume: targetVolume,
		AlgoConfig: &algorithm_client.AlgoConfigBo{
			LocalSearchSwitch:     algoConf.LocalSearchSwitch,
			LocalSearchTime:       algoConf.LocalSearchTime,
			EarlyStopIter:         algoConf.EarlyStopIter,
			PickupEffEnable:       pickupEffEnable,
			PickupEffActualBudget: uint64(pickupEffBudget),
			PickupEffSLA:          uint64(pickupEffSla),
			PickupEffUrl:          pickupEffUrl,
		},
		Orders:                  orderBos,
		ShopFulfillmentProducts: shopFulfillmentProducts,
	}

	return batchAllocateReqBo, orderInfoMap, orderInfoMapForLog, nil
}

func (b *BatchAllocateServiceImpl) getPickupEffParams(
	ctx context.Context, maskProductID int64, softRule *rule.MaskRule, shopIds []int64, allocateTime time.Time,
	lastSecond int64, batchMinuteVolumeList []float64,
) (bool, float64, int64, string, map[uint64][]uint64) {

	var (
		batchAllocateConfig  = configutil.GetBatchAllocateConf()
		enable               = batchAllocateConfig.PickupEffEnable
		sla                  = batchAllocateConfig.PickupEffSLA
		url                  = batchAllocateConfig.PickupEffUrl
		shopFulfillmentProds = make(map[uint64][]uint64, len(shopIds))
	)

	// BE侧的开关和业务侧的Feature开关要同时打开
	if !enable || !softRule.BatchRuleDetail.BatchAllocationRuleConfig.PickupEfficiencyEnabled {
		return false, 0, 0, "", shopFulfillmentProds
	}

	usedBudget, err := b.PickupEffCounter.GetMaskProductActualBudget(ctx, maskProductID, allocateTime)
	if err != nil {
		return false, 0, 0, "", shopFulfillmentProds
	}

	// 全天剩余的Budget
	actualBudget := softRule.BatchRuleDetail.BatchAllocationRuleConfig.PickupEfficiencyDetail.Budget - usedBudget
	// 平摊在Minute上
	actualBudget = float64(batch_allocate.GetBatchMinTarget(lastSecond, batchMinuteVolumeList, uint32(actualBudget)))
	if actualBudget < 0 {
		// Budget出现负数，上报 & 告警
		errMsg := fmt.Sprintf("negative pickup efficiency budget: %f", actualBudget)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.InvalidPickupEffBudget, errMsg)
	}

	shopIds = objutil.RemoveDuplicateInt64(shopIds)
	shopFulfillmentProdsInt64, err := b.PickupEffCounter.BatchGetShopFulfillmentProducts(ctx, shopIds, maskProductID, allocateTime)
	if err != nil {
		return false, 0, 0, "", shopFulfillmentProds
	}

	for shopId, fulfillmentProds := range shopFulfillmentProdsInt64 {
		shopFulfillmentProds[uint64(shopId)] = objutil.Int64ToUint64(fulfillmentProds)
	}

	return true, actualBudget, sla, url, shopFulfillmentProds
}

func (b *BatchAllocateServiceImpl) getTargetBos(
	ctx context.Context, softRule *rule.MaskRule, maskProductID, lastSecond int64, batchMinuteVolumeList []float64,
) ([]*algorithm_client.ProductTargetVolumeBo, *srerr.Error) {
	ruleVolume, err := b.MaskRuleVolumeService.GetEffectiveBatchAllocateRuleVolume(ctx, int(softRule.MaskProductId))
	if err != nil {
		return nil, err
	}

	var (
		productGroupCodeMapping = ruleVolume.GetFulfillmentProductToGroupMap()
		targetBos               = make([]*algorithm_client.ProductTargetVolumeBo, 0)
	)

	// country level
	if softRule.BatchRuleDetail.BatchAllocationRuleConfig.CountryVolumeEnabled {
		targetBos = append(targetBos, b.parseCountryVolume(ctx, ruleVolume, maskProductID, lastSecond, batchMinuteVolumeList)...)
	}

	// zone level
	if softRule.BatchRuleDetail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled {
		zoneVolumes, err := b.parseZoneVolume(ctx, ruleVolume, maskProductID, lastSecond, batchMinuteVolumeList, productGroupCodeMapping)
		if err != nil {
			return nil, err
		}
		targetBos = append(targetBos, zoneVolumes...)

		routeVolumes, err := b.parseRouteVolume(ctx, ruleVolume, maskProductID, lastSecond, batchMinuteVolumeList, productGroupCodeMapping)
		if err != nil {
			return nil, err
		}
		targetBos = append(targetBos, routeVolumes...)
	}

	return targetBos, nil
}

func (b *BatchAllocateServiceImpl) parseCountryVolume(
	ctx context.Context, r *rulevolume2.MaskRuleVolumeTab, maskProductID, lastSecond int64, batchMinuteVolumeList []float64,
) []*algorithm_client.ProductTargetVolumeBo {

	var (
		targetBos        = make([]*algorithm_client.ProductTargetVolumeBo, 0)
		countryVolumeMap = make(map[int64]*allocation3.VolumeInfo)
	)

	for _, countryVolume := range r.DefaultVolumeLimit {
		if r.MaskCombinationMode && countryVolume.LimitType == rulevolume2.MaskRuleLimitTypeFulfillmentProduct && countryVolume.MaskProductID != maskProductID {
			// Combination模式下，Volume Rule或有其他Mask Product的Volume信息，不属于该Maks Product的需要剔除掉
			continue
		}

		groupCode := countryVolume.GroupCode
		fulfillmentProductIDs := []int64{countryVolume.ID}
		if countryVolume.LimitType == rulevolume2.MaskRuleLimitTypeGroup {
			// 如果是Group的话，从Group信息里取出
			fulfillmentProductIDs = r.GetGroupFulfillmentProductIDs(maskProductID, groupCode)
		}

		for _, fulfillmentProductID := range fulfillmentProductIDs {
			targetBo := b.getProductCountryTargetVolumeBo(ctx, maskProductID, fulfillmentProductID, groupCode, countryVolume, lastSecond, batchMinuteVolumeList)
			targetBos = append(targetBos, targetBo)
			countryVolumeMap[fulfillmentProductID] = &allocation3.VolumeInfo{
				SystemVolume:           int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].AllocatedVolume),
				SystemCodVolume:        int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].CodAllocatedVolume),
				SystemBulkyVolume:      int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].BulkyAllocatedVolume),
				SystemHighValueVolume:  int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].HighValueAllocatedVolume),
				SystemDgVolume:         int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DgAllocatedVolume),
				MaxDailyLimit:          int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DailyMaxTar),
				MaxCodDailyLimit:       int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DailyCodMaxTar),
				MaxBulkyDailyLimit:     int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DailyBulkyMaxTar),
				MaxHighValueDailyLimit: int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DailyHighValueMaxTar),
				MaxDgDailyLimit:        int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].DailyDgMaxTar),
				MinBatchLimit:          int64(targetBo.TargetVolume[0].TargetVolumeDetail[0].BatchMinTar),
			}
		}
	}
	b.logInfo.CountryVolumeMap = countryVolumeMap

	return targetBos
}

func (b *BatchAllocateServiceImpl) getProductCountryTargetVolumeBo(
	ctx context.Context, maskProductID, fulfillmentProductID int64, groupCode string,
	countryVolume *rulevolume2.MaskDefaultVolumeLimitItem, lastSecond int64, batchMinuteVolumeList []float64,
) *algorithm_client.ProductTargetVolumeBo {
	return &algorithm_client.ProductTargetVolumeBo{
		FulfillmentProductId: uint64(fulfillmentProductID),
		TargetVolume: []*algorithm_client.TargetVolumeBo{
			{
				TargetType: rulevolume2.BaVolumeTypeRegion,
				TargetVolumeDetail: []*algorithm_client.TargetVolumeDetailBo{
					{
						TargetVolumeCode:         envvar.GetCIDLower(),
						AllocatedVolume:          b.getProductVolume(ctx, maskProductID, fulfillmentProductID, groupCode, parcel_type_definition.ParcelTypeNone),
						CodAllocatedVolume:       b.getProductVolume(ctx, maskProductID, fulfillmentProductID, groupCode, parcel_type_definition.ParcelTypeCod),
						BulkyAllocatedVolume:     b.getProductVolume(ctx, maskProductID, fulfillmentProductID, groupCode, parcel_type_definition.ParcelTypeBulky),
						HighValueAllocatedVolume: b.getProductVolume(ctx, maskProductID, fulfillmentProductID, groupCode, parcel_type_definition.ParcelTypeHighValue),
						DgAllocatedVolume:        b.getProductVolume(ctx, maskProductID, fulfillmentProductID, groupCode, parcel_type_definition.ParcelTypeDg),
						DailyMaxTar:              uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MaxCapacity), batch_allocate.VolumeTypeMax)),
						DailyCodMaxTar:           uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MaxCodCapacity), batch_allocate.VolumeTypeMax)),
						DailyBulkyMaxTar:         uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MaxBulkyCapacity), batch_allocate.VolumeTypeMax)),
						DailyHighValueMaxTar:     uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MaxHighValueCapacity), batch_allocate.VolumeTypeMax)),
						DailyDgMaxTar:            uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MaxDgCapacity), batch_allocate.VolumeTypeMax)),
						DailyMinTar:              uint32(batch_allocate.FillVolume(ctx, int64(countryVolume.MinVolume), batch_allocate.VolumeTypeMin)),
						// target - allocated, 小于0则传0
						BatchMinTar: b.getBatchMinTar(ctx, batch_allocate.GetBatchMinTarget(lastSecond, batchMinuteVolumeList, uint32(countryVolume.MinVolume)), maskProductID, fulfillmentProductID, groupCode),
					},
				},
			},
		},
	}
}

func (b *BatchAllocateServiceImpl) getBatchMinTar(
	ctx context.Context, minuteTar uint32, maskProductID, productID int64, groupCode string) uint32 {
	// 剩余volume 小于等于0， 传0
	if minuteTar <= b.getProductVolume(ctx, maskProductID, productID, groupCode, parcel_type_definition.ParcelTypeNone) {
		return 0
	}

	return minuteTar - b.getProductVolume(ctx, maskProductID, productID, groupCode, parcel_type_definition.ParcelTypeNone)
}

func (b *BatchAllocateServiceImpl) getProductVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, parcelType parcel_type_definition.ParcelType) uint32 {
	// Share Volume模式，取Group运力作为Fulfillment Product的运力
	if groupCode != "" {
		v, err := b.MaskVolumeCounter.GetGroupVolume(ctx, groupCode, rule_mode.MplOrderRule, parcelType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetGroupVolume failed: %v", err)
		}

		return uint32(v)
	}

	v, err := b.MaskVolumeCounter.GetProductVolume(ctx, maskProductID, productID, rule_mode.MplOrderRule, parcelType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetProductVolume failed: %v", err)
	}

	return uint32(v)
}

func (b *BatchAllocateServiceImpl) parseZoneVolume(
	ctx context.Context, r *rulevolume2.MaskRuleVolumeTab, maskProductID, lastSecond int64, batchMinuteVolumeList []float64,
	productGroupCodeMapping map[int64]string,
) ([]*algorithm_client.ProductTargetVolumeBo, *srerr.Error) {
	var (
		targetBos                []*algorithm_client.ProductTargetVolumeBo
		productZoneCodeTargetMap = make(map[int64]map[string]batch_allocate.Target, 0)
		zoneVolumeMap            = make(map[int64]map[string]*allocation3.VolumeInfo, 0)
	)

	for _, tab := range r.ZoneVolumes {
		componentProductID := int64(tab.ComponentProductID)
		if _, ok := productZoneCodeTargetMap[componentProductID]; !ok {
			codeTargetMap := make(map[string]batch_allocate.Target, 0)
			productZoneCodeTargetMap[componentProductID] = codeTargetMap
		}

		//装填target
		productZoneCodeTargetMap[componentProductID][tab.ZoneCode] = batch_allocate.Target{
			MinVolume:          uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMinVolume), batch_allocate.VolumeTypeMin)),
			MaxVolume:          uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMaxCapacity), batch_allocate.VolumeTypeMax)),
			MaxCodVolume:       uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMaxCodCapacity), batch_allocate.VolumeTypeMax)),
			MaxBulkyVolume:     uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMaxBulkyCapacity), batch_allocate.VolumeTypeMax)),
			MaxHighValueVolume: uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMaxHighValueCapacity), batch_allocate.VolumeTypeMax)),
			MaxDgVolume:        uint32(batch_allocate.FillVolume(ctx, int64(tab.DestinationMaxDgCapacity), batch_allocate.VolumeTypeMax)),
		}

		//埋点allocation log
		if _, ok := zoneVolumeMap[int64(tab.ComponentProductID)]; !ok {
			zoneVolumeMap[int64(tab.ComponentProductID)] = make(map[string]*allocation3.VolumeInfo, 0)
		}
		zoneVolumeMap[int64(tab.ComponentProductID)][tab.ZoneCode] = &allocation3.VolumeInfo{
			MaxDailyLimit:          int64(tab.DestinationMaxCapacity),
			MaxCodDailyLimit:       int64(tab.DestinationMaxCodCapacity),
			MaxBulkyDailyLimit:     int64(tab.DestinationMaxBulkyCapacity),
			MaxHighValueDailyLimit: int64(tab.DestinationMaxHighValueCapacity),
			MaxDgDailyLimit:        int64(tab.DestinationMaxDgCapacity),
		}
	}

	allocatedVolumeResults, err := b.batchGetProductZoneVolumes(ctx, maskProductID, productZoneCodeTargetMap, productGroupCodeMapping)
	if err != nil {
		return nil, err
	}

	for fulfillmentProductID, zoneCodeTargetMap := range productZoneCodeTargetMap {
		//遍历zoneCodeTargetMap，组装所有的target zone code-value
		var (
			targetVolumeDetailList = make([]*algorithm_client.TargetVolumeDetailBo, 0, len(zoneCodeTargetMap))
			groupCode              = productGroupCodeMapping[fulfillmentProductID]
		)

		for zoneCode, tempTarget := range zoneCodeTargetMap {
			batchMinTar := batch_allocate.GetBatchMinTarget(lastSecond, batchMinuteVolumeList, tempTarget.MinVolume)
			targetVolumeDetail := &algorithm_client.TargetVolumeDetailBo{
				TargetVolumeCode:         zoneCode,
				AllocatedVolume:          allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneCode, parcel_type_definition.ParcelTypeNone)],
				CodAllocatedVolume:       allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneCode, parcel_type_definition.ParcelTypeCod)],
				BulkyAllocatedVolume:     allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneCode, parcel_type_definition.ParcelTypeBulky)],
				HighValueAllocatedVolume: allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneCode, parcel_type_definition.ParcelTypeHighValue)],
				DgAllocatedVolume:        allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneCode, parcel_type_definition.ParcelTypeDg)],
				DailyMaxTar:              tempTarget.MaxVolume,
				DailyCodMaxTar:           tempTarget.MaxCodVolume,
				DailyBulkyMaxTar:         tempTarget.MaxBulkyVolume,
				DailyHighValueMaxTar:     tempTarget.MaxHighValueVolume,
				DailyDgMaxTar:            tempTarget.MaxDgVolume,
				DailyMinTar:              tempTarget.MinVolume,
				// target - allocated, 小于0则传0
				BatchMinTar: b.getZoneRouteBatchMinTar(ctx, batchMinTar, int64(fulfillmentProductID), groupCode, zoneCode, allocatedVolumeResults),
			}
			targetVolumeDetailList = append(targetVolumeDetailList, targetVolumeDetail)

			//allocation log 埋点
			enrichVolumeInfo(zoneVolumeMap[int64(fulfillmentProductID)][zoneCode], targetVolumeDetail)
		}
		//定义product target bo
		targetBo := &algorithm_client.ProductTargetVolumeBo{
			FulfillmentProductId: uint64(fulfillmentProductID),
			TargetVolume: []*algorithm_client.TargetVolumeBo{{
				TargetType:         rulevolume2.BaVolumeTypeZone,
				TargetVolumeDetail: targetVolumeDetailList,
			}},
		}
		targetBos = append(targetBos, targetBo)
	}
	b.logInfo.ZoneVolumeMap = zoneVolumeMap

	return targetBos, nil
}

func (b *BatchAllocateServiceImpl) parseRouteVolume(
	ctx context.Context, r *rulevolume2.MaskRuleVolumeTab, maskProductID, lastSecond int64, batchMinuteVolumeList []float64,
	productGroupCodeMapping map[int64]string,
) ([]*algorithm_client.ProductTargetVolumeBo, *srerr.Error) {
	var (
		targetBos                 []*algorithm_client.ProductTargetVolumeBo
		productRouteCodeTargetMap = make(map[int64]map[string]batch_allocate.Target, 0)
		routeVolumeMap            = make(map[int64]map[string]*allocation3.VolumeInfo, 0)
	)

	for _, tab := range r.RouteVolumes {
		componentProductID := int64(tab.ComponentProductID)
		if _, ok := productRouteCodeTargetMap[componentProductID]; !ok {
			codeTargetMap := make(map[string]batch_allocate.Target, 0)
			productRouteCodeTargetMap[componentProductID] = codeTargetMap
		}

		//装填target
		productRouteCodeTargetMap[componentProductID][tab.RouteCode] = batch_allocate.Target{
			MinVolume:          uint32(batch_allocate.FillVolume(ctx, int64(tab.MinVolume), batch_allocate.VolumeTypeMin)),
			MaxVolume:          uint32(batch_allocate.FillVolume(ctx, int64(tab.MaxCapacity), batch_allocate.VolumeTypeMax)),
			MaxCodVolume:       uint32(batch_allocate.FillVolume(ctx, int64(tab.MaxCodCapacity), batch_allocate.VolumeTypeMax)),
			MaxBulkyVolume:     uint32(batch_allocate.FillVolume(ctx, int64(tab.MaxBulkyCapacity), batch_allocate.VolumeTypeMax)),
			MaxHighValueVolume: uint32(batch_allocate.FillVolume(ctx, int64(tab.MaxHighValueCapacity), batch_allocate.VolumeTypeMax)),
			MaxDgVolume:        uint32(batch_allocate.FillVolume(ctx, int64(tab.MaxDgCapacity), batch_allocate.VolumeTypeMax)),
		}

		//埋点allocation log
		if _, ok := routeVolumeMap[int64(tab.ComponentProductID)]; !ok {
			routeVolumeMap[int64(tab.ComponentProductID)] = make(map[string]*allocation3.VolumeInfo, 0)
		}
		routeVolumeMap[int64(tab.ComponentProductID)][tab.RouteCode] = &allocation3.VolumeInfo{
			MaxDailyLimit:          int64(tab.MaxCapacity),
			MaxCodDailyLimit:       int64(tab.MaxCodCapacity),
			MaxBulkyDailyLimit:     int64(tab.MaxBulkyCapacity),
			MaxHighValueDailyLimit: int64(tab.MaxHighValueCapacity),
			MaxDgDailyLimit:        int64(tab.MaxDgCapacity),
		}
	}

	allocatedVolumeResults, err := b.batchGetProductRouteVolumes(ctx, maskProductID, productRouteCodeTargetMap, productGroupCodeMapping)
	if err != nil {
		return nil, err
	}

	for fulfillmentProductID, routeCodeTargetMap := range productRouteCodeTargetMap {
		//routeCodeTargetMap，组装所有的target code-value
		var (
			targetVolumeDetailList = make([]*algorithm_client.TargetVolumeDetailBo, 0, len(routeCodeTargetMap))
			groupCode              = productGroupCodeMapping[fulfillmentProductID]
		)

		for routeCode, tempTarget := range routeCodeTargetMap {
			batchMinTar := batch_allocate.GetBatchMinTarget(lastSecond, batchMinuteVolumeList, tempTarget.MinVolume)
			targetVolumeDetail := &algorithm_client.TargetVolumeDetailBo{
				TargetVolumeCode:         routeCode,
				AllocatedVolume:          allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(int64(fulfillmentProductID), groupCode, routeCode, parcel_type_definition.ParcelTypeNone)],
				CodAllocatedVolume:       allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(int64(fulfillmentProductID), groupCode, routeCode, parcel_type_definition.ParcelTypeCod)],
				BulkyAllocatedVolume:     allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(int64(fulfillmentProductID), groupCode, routeCode, parcel_type_definition.ParcelTypeBulky)],
				HighValueAllocatedVolume: allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(int64(fulfillmentProductID), groupCode, routeCode, parcel_type_definition.ParcelTypeHighValue)],
				DgAllocatedVolume:        allocatedVolumeResults[formatGetProductZoneRouteVolumeKey(int64(fulfillmentProductID), groupCode, routeCode, parcel_type_definition.ParcelTypeDg)],
				DailyMaxTar:              tempTarget.MaxVolume,
				DailyCodMaxTar:           tempTarget.MaxCodVolume,
				DailyBulkyMaxTar:         tempTarget.MaxBulkyVolume,
				DailyHighValueMaxTar:     tempTarget.MaxHighValueVolume,
				DailyDgMaxTar:            tempTarget.MaxDgVolume,
				DailyMinTar:              tempTarget.MinVolume,
				// target - allocated, 小于0则传0
				BatchMinTar: b.getZoneRouteBatchMinTar(ctx, batchMinTar, int64(fulfillmentProductID), groupCode, routeCode, allocatedVolumeResults),
			}
			targetVolumeDetailList = append(targetVolumeDetailList, targetVolumeDetail)

			//allocation log 埋点
			enrichVolumeInfo(routeVolumeMap[int64(fulfillmentProductID)][routeCode], targetVolumeDetail)
		}
		//定义product target bo
		targetBo := &algorithm_client.ProductTargetVolumeBo{
			FulfillmentProductId: uint64(fulfillmentProductID),
			TargetVolume: []*algorithm_client.TargetVolumeBo{{
				TargetType:         rulevolume2.BaVolumeTypeRoute,
				TargetVolumeDetail: targetVolumeDetailList,
			}},
		}
		targetBos = append(targetBos, targetBo)
	}
	b.logInfo.RouteVolumeMap = routeVolumeMap

	return targetBos, nil
}

func (b *BatchAllocateServiceImpl) batchGetProductZoneVolumes(
	ctx context.Context, maskProductID int64, productCodeTargetMap map[int64]map[string]batch_allocate.Target,
	productGroupCodeMapping map[int64]string,
) (map[string]uint32, *srerr.Error) {
	volumeRequests := make([]volumecounter.GetProductZoneVolumeRequest, 0)
	for fulfillmentProductID, codeTargetMap := range productCodeTargetMap {
		for zoneCode := range codeTargetMap {
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				volumeRequests = append(volumeRequests, volumecounter.GetProductZoneVolumeRequest{
					MaskProductID:        maskProductID,
					FulfillmentProductID: fulfillmentProductID,
					GroupCode:            productGroupCodeMapping[int64(fulfillmentProductID)],
					ZoneCode:             zoneCode,
					Direction:            rulevolume.MaskZoneDirectionDest,
					RuleMode:             rule_mode.MplOrderRule,
					ParcelType:           parcelType,
				})
			}
		}
	}

	volumeResults, err := b.MaskVolumeCounter.BatchGetProductZoneVolumes(ctx, volumeRequests)
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, "", err)
	}

	if len(volumeRequests) != len(volumeResults) {
		return nil, srerr.New(srerr.CodisErr, "", "get product zone volumes failed, result length not equal to request length")
	}

	results := make(map[string]uint32, len(volumeRequests))
	for index, req := range volumeRequests {
		groupCode := productGroupCodeMapping[req.FulfillmentProductID]
		results[formatGetProductZoneRouteVolumeKey(req.FulfillmentProductID, groupCode, req.ZoneCode, req.ParcelType)] = uint32(volumeResults[index])
	}

	return results, nil
}

func formatGetProductZoneRouteVolumeKey(fulfillmentProductID int64, groupCode, zoneRouteCode string, parcelType parcel_type_definition.ParcelType) string {
	if groupCode != "" {
		// 使用Group的运力
		return fmt.Sprintf("%s:%s:%s", groupCode, zoneRouteCode, parcelType)
	}
	return fmt.Sprintf("%d:%s:%s", fulfillmentProductID, zoneRouteCode, parcelType)
}

func (b *BatchAllocateServiceImpl) batchGetProductRouteVolumes(
	ctx context.Context, maskProductID int64, productCodeTargetMap map[int64]map[string]batch_allocate.Target,
	productGroupCodeMapping map[int64]string,
) (map[string]uint32, *srerr.Error) {
	volumeRequests := make([]volumecounter.GetProductRouteVolumeRequest, 0)
	for fulfillmentProductID, codeTargetMap := range productCodeTargetMap {
		for routeCode := range codeTargetMap {
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				volumeRequests = append(volumeRequests, volumecounter.GetProductRouteVolumeRequest{
					MaskProductID:        maskProductID,
					FulfillmentProductID: int64(fulfillmentProductID),
					GroupCode:            productGroupCodeMapping[int64(fulfillmentProductID)],
					RouteCode:            routeCode,
					RuleMode:             rule_mode.MplOrderRule,
					ParcelType:           parcelType,
				})
			}
		}
	}

	volumeResults, err := b.MaskVolumeCounter.BatchGetProductRouteVolumes(ctx, volumeRequests)
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, "", err)
	}

	if len(volumeRequests) != len(volumeResults) {
		return nil, srerr.New(srerr.CodisErr, "", "get product route volumes failed, result length not equal to request length")
	}

	results := make(map[string]uint32, len(volumeRequests))
	for index, req := range volumeRequests {
		results[formatGetProductZoneRouteVolumeKey(req.FulfillmentProductID, req.GroupCode, req.RouteCode, req.ParcelType)] = uint32(volumeResults[index])
	}

	return results, nil
}

func (b *BatchAllocateServiceImpl) getZoneRouteBatchMinTar(
	ctx context.Context, minuteTar uint32, fulfillmentProductID int64, groupCode, zoneRouteCode string, allocatedVolume map[string]uint32) uint32 {

	v := allocatedVolume[formatGetProductZoneRouteVolumeKey(fulfillmentProductID, groupCode, zoneRouteCode, parcel_type_definition.ParcelTypeNone)]
	// 剩余volume 小于等于0， 传0
	if minuteTar <= v {
		return 0
	}

	return minuteTar - v
}

func (b *BatchAllocateServiceImpl) createAllocateResult(
	ctx context.Context, maskProductID int64, algoResp *algorithm_client.BatchAllocateRespBo,
	orderInfoMap map[uint64]AllocateOrderInfo, dayIndex int, allocateTime time.Time,
) *srerr.Error {
	singleAllocatedOrders := b.checkOrderAllocated(ctx, orderInfoMap, dayIndex)

	orderResults := make([]*order.OrderResultTab, 0, len(algoResp.OrderResult))
	for _, r := range algoResp.OrderResult {
		orderInfo := orderInfoMap[r.OrderId]
		orderInfo.BatchAllocateResult = int(r.FulfillmentProductId)
		orderInfoMap[r.OrderId] = orderInfo

		if _, exist := singleAllocatedOrders[r.OrderId]; exist {
			logger.CtxLogDebugf(ctx, "order has been allocated, no need insert order result again | order=%v", r.OrderId)
			continue
		}

		orderResults = append(orderResults, &order.OrderResultTab{
			OrderID:                    r.OrderId,
			OrderStatus:                batch_allocate.OrderStatusTypeAsyncAllocated,
			MaskProductID:              int(maskProductID),
			AllocateResult:             int(r.FulfillmentProductId),
			FulfillmentShippingFeeInfo: orderInfo.FulfillmentShippingFeeInfoMap[int(r.FulfillmentProductId)],
			RequestTimeStamp:           orderInfo.RequestTimeStamp,
		})
	}

	// 1.更新结果到DB
	if err := b.BatchAllocateOrderRepo.BatchCreateOrderResult(ctx, orderResults, dayIndex); err != nil {
		return err
	}

	ruleVolume, err := b.MaskRuleVolumeService.GetEffectiveBatchAllocateRuleVolume(ctx, int(maskProductID))
	if err != nil {
		return err
	}

	productGroupCodeMapping := ruleVolume.GetFulfillmentProductToGroupMap()

	// 2.更新运力信息
	for fulfillmentProduct, statsResult := range algoResp.StatsResult {
		groupCode := productGroupCodeMapping[int64(fulfillmentProduct)]
		// 2.1 Fulfillment Product维度
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(fulfillmentProduct), groupCode, int64(statsResult.TotalOrderNum), allocateTime, parcel_type_definition.ParcelTypeNone); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(fulfillmentProduct), groupCode, int64(statsResult.TotalCodOrderNum), allocateTime, parcel_type_definition.ParcelTypeCod); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(fulfillmentProduct), groupCode, int64(statsResult.TotalBulkyOrderNum), allocateTime, parcel_type_definition.ParcelTypeBulky); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(fulfillmentProduct), groupCode, int64(statsResult.TotalHighValueOrderNum), allocateTime, parcel_type_definition.ParcelTypeHighValue); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(fulfillmentProduct), groupCode, int64(statsResult.TotalDgOrderNum), allocateTime, parcel_type_definition.ParcelTypeDg); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}

		// 2.2 Zone维度
		if err := b.batchIncrZoneVolume(ctx, maskProductID, int64(fulfillmentProduct), groupCode, statsResult.TargetZoneStatsResult, allocateTime); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}

		// 2.3 Route维度
		if err := b.batchIncrRouteVolume(ctx, maskProductID, int64(fulfillmentProduct), groupCode, statsResult.TargetRouteStatsResult, allocateTime); err != nil {
			return srerr.With(srerr.CodisErr, fulfillmentProduct, err)
		}
	}

	// 2.3 Mask Product维度
	if err := b.MaskVolumeCounter.IncrByMaskVolumeByDate(ctx, maskProductID, int64(len(algoResp.OrderResult)), allocateTime); err != nil {
		return srerr.With(srerr.CodisErr, maskProductID, err)
	}

	// 2.4 更新Shop Fulfillment Products
	var shopFulfillmentItemList []pickup_efficiency_counter.ShopFulfillmentItem
	for shopID, fulfillmentProductCount := range algoResp.PickupEffResult.ShopFulfillmentProductsStatsResult {
		for fulfillmentProduct, count := range fulfillmentProductCount {
			shopFulfillmentItemList = append(shopFulfillmentItemList, pickup_efficiency_counter.ShopFulfillmentItem{
				ShopID:               int64(shopID),
				FulfillmentProductID: int64(fulfillmentProduct),
				Count:                count,
			})
		}
	}

	// 更新失败不影响主流程，只记录日志和上报
	if err := b.PickupEffCounter.BatchIncrShopFulfillmentProducts(ctx, maskProductID, allocateTime, shopFulfillmentItemList); err != nil {
		logger.CtxLogErrorf(ctx, "incr shop fulfillment products failed | err: %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdateShopFulfillmentProduct, err.Error())
	}

	// 2.5 更新Pickup Efficiency Budget
	if algoResp.PickupEffResult.UsedCost > 0 {
		if err := b.PickupEffCounter.IncrMaskProductActualBudget(ctx, maskProductID, int64(algoResp.PickupEffResult.UsedCost), allocateTime); err != nil {
			logger.CtxLogErrorf(ctx, "incr mask product actual budget failed | err: %v", err)
			monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdatePickupEfficiencyBudget, err.Error())
		}
	}

	// 2.6 扣减BMOA/BMFC/DatxFix的订单
	b.deductSingleAllocatedVolume(ctx, maskProductID, singleAllocatedOrders, orderInfoMap, allocateTime, productGroupCodeMapping)

	return nil
}

// checkOrderAllocated 检查订单是否有过BOMA/BMFC，不阻断主流程
func (b *BatchAllocateServiceImpl) checkOrderAllocated(ctx context.Context, orderInfoMap map[uint64]AllocateOrderInfo, dayIndex int) map[uint64]struct{} {
	if len(orderInfoMap) == 0 {
		return make(map[uint64]struct{})
	}

	orderIDList := make([]uint64, 0, len(orderInfoMap))
	for orderID := range orderInfoMap {
		orderIDList = append(orderIDList, orderID)
	}

	allocatedOrderMap := make(map[uint64]struct{})

	// 检查DB中的分配记录
	dbCount := b.checkDBAllocatedOrders(ctx, orderIDList, dayIndex, allocatedOrderMap)
	logger.CtxLogInfof(ctx, "found %d orders with allocated records in DB", dbCount)

	// 检查Redis中的BMFC记录
	redisCount := b.checkRedisAllocatedOrders(ctx, orderIDList, allocatedOrderMap)
	logger.CtxLogInfof(ctx, "found %d orders with BMFC records in Redis", redisCount)

	return allocatedOrderMap
}

// checkDBAllocatedOrders 检查DB中的已分配订单记录
func (b *BatchAllocateServiceImpl) checkDBAllocatedOrders(ctx context.Context, orderIDList []uint64, dayIndex int, allocatedOrderMap map[uint64]struct{}) int {
	const batchSize = 1000
	count := 0

	for offset := 0; offset < len(orderIDList); offset += batchSize {
		end := offset + batchSize
		if end > len(orderIDList) {
			end = len(orderIDList)
		}

		batchOrderIDList := orderIDList[offset:end]

		// 查询当天(T)的记录
		if orderResults, err := b.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx, batchOrderIDList, dayIndex); err != nil {
			logger.CtxLogErrorf(ctx, "BatchGetOrderResultByOrderID(T) failed: %v", err)
		} else {
			// 直接写入map，避免额外遍历
			for _, orderResult := range orderResults {
				allocatedOrderMap[orderResult.OrderID] = struct{}{}
				count++
			}
		}

		// 查询前一天(T-1)的记录
		if orderResults, err := b.BatchAllocateOrderRepo.BatchGetOrderResultByOrderID(ctx, batchOrderIDList, dayIndex-1); err != nil {
			logger.CtxLogErrorf(ctx, "BatchGetOrderResultByOrderID(T-1) failed: %v", err)
		} else {
			// 直接写入map，避免额外遍历
			for _, orderResult := range orderResults {
				allocatedOrderMap[orderResult.OrderID] = struct{}{}
				count++
			}
		}
	}

	return count
}

// checkRedisAllocatedOrders 检查Redis中的BMFC记录
func (b *BatchAllocateServiceImpl) checkRedisAllocatedOrders(ctx context.Context, orderIDList []uint64, allocatedOrderMap map[uint64]struct{}) int {
	const batchSize = 1000
	count := 0

	for offset := 0; offset < len(orderIDList); offset += batchSize {
		end := offset + batchSize
		if end > len(orderIDList) {
			end = len(orderIDList)
		}

		batchOrderIDList := orderIDList[offset:end]
		redisKeys := make([]string, len(batchOrderIDList))

		// 生成Redis key列表
		for i, orderID := range batchOrderIDList {
			redisKeys[i] = allocation.GetBMFCOrderRecordKey(orderID)
		}

		// 批量查询Redis并直接处理结果
		if redisResults, err := redisutil.MGet(ctx, redisKeys); err != nil {
			logger.CtxLogErrorf(ctx, "MGet failed for BMFC keys: %v", err)
		} else {
			// 直接检测结果并写入map
			for i, result := range redisResults {
				if result != nil { // key存在表示订单有BMFC记录
					allocatedOrderMap[batchOrderIDList[i]] = struct{}{}
					count++
				}
			}
		}
	}

	return count
}

// deductSingleAllocatedVolume 扣减已经做过allocate的订单的运力
func (b *BatchAllocateServiceImpl) deductSingleAllocatedVolume(
	ctx context.Context, maskProductID int64, singleAllocatedOrders map[uint64]struct{},
	orderInfoMap map[uint64]AllocateOrderInfo, allocateTime time.Time, productGroupCodeMapping map[int64]string,
) {
	var (
		deductFulfillmentProductMap      = make(map[int]*orderCountDetail)
		deductFulfillmentProductZoneMap  = make(map[int]map[string]*orderCountDetail)
		deductFulfillmentProductRouteMap = make(map[int]map[string]*orderCountDetail)
		deductShopFulfillmentProductsMap = make(map[int64]map[int]int)
	)

	// 聚合到Key维度
	for orderId := range singleAllocatedOrders {
		orderInfo, exist := orderInfoMap[orderId]
		if !exist {
			logger.CtxLogErrorf(ctx, "can not find order info | order:%d", orderId)
			continue
		}
		// init
		if deductFulfillmentProductMap[orderInfo.BatchAllocateResult] == nil {
			deductFulfillmentProductMap[orderInfo.BatchAllocateResult] = &orderCountDetail{}
		}

		// parcel attribute
		parcelAttr := orderInfo.ParcelAttrMap[orderInfo.BatchAllocateResult]

		// fulfillment product
		deductFulfillmentProductMap[orderInfo.BatchAllocateResult].total += 1
		if parcelAttr.IsCod {
			deductFulfillmentProductMap[orderInfo.BatchAllocateResult].cod += 1
		}
		if parcelAttr.IsBulky {
			deductFulfillmentProductMap[orderInfo.BatchAllocateResult].bulky += 1
		}
		if parcelAttr.IsHighValue {
			deductFulfillmentProductMap[orderInfo.BatchAllocateResult].highValue += 1
		}
		if parcelAttr.IsDg {
			deductFulfillmentProductMap[orderInfo.BatchAllocateResult].dg += 1
		}

		// shop fulfillment product count
		if deductShopFulfillmentProductsMap[orderInfo.ShopID] == nil {
			deductShopFulfillmentProductsMap[orderInfo.ShopID] = make(map[int]int)
		}
		deductShopFulfillmentProductsMap[orderInfo.ShopID][orderInfo.BatchAllocateResult]++

		// fulfillment product's zones
		for _, zone := range orderInfo.ZoneCodeList {
			// init
			if deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult] == nil {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult] = make(map[string]*orderCountDetail)
			}
			if deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone] == nil {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone] = &orderCountDetail{}
			}

			deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone].total += 1
			if parcelAttr.IsCod {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone].cod += 1
			}
			if parcelAttr.IsBulky {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone].bulky += 1
			}
			if parcelAttr.IsHighValue {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone].highValue += 1
			}
			if parcelAttr.IsDg {
				deductFulfillmentProductZoneMap[orderInfo.BatchAllocateResult][zone].dg += 1
			}
		}

		// fulfilment product's routes
		for _, route := range orderInfo.RouteCodeList {
			if deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult] == nil {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult] = make(map[string]*orderCountDetail)
			}
			if deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route] == nil {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route] = &orderCountDetail{}
			}

			deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route].total += 1
			if parcelAttr.IsCod {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route].cod += 1
			}
			if parcelAttr.IsBulky {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route].bulky += 1
			}
			if parcelAttr.IsHighValue {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route].highValue += 1
			}
			if parcelAttr.IsDg {
				deductFulfillmentProductRouteMap[orderInfo.BatchAllocateResult][route].dg += 1
			}
		}
	}

	// 按照Key做扣减
	// fulfillment product
	for productId, countDetail := range deductFulfillmentProductMap {
		groupCode := productGroupCodeMapping[int64(productId)]
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productId), groupCode, -countDetail.total, allocateTime, parcel_type_definition.ParcelTypeNone); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productId), groupCode, -countDetail.cod, allocateTime, parcel_type_definition.ParcelTypeCod); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productId), groupCode, -countDetail.bulky, allocateTime, parcel_type_definition.ParcelTypeBulky); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productId), groupCode, -countDetail.highValue, allocateTime, parcel_type_definition.ParcelTypeHighValue); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productId), groupCode, -countDetail.dg, allocateTime, parcel_type_definition.ParcelTypeDg); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
	}

	zoneVolumeItemList := make([]volumecounter.ZoneVolumeItem, 0)
	for productId, zoneCountMap := range deductFulfillmentProductZoneMap {
		groupCode := productGroupCodeMapping[int64(productId)]
		for zoneCode, countDetail := range zoneCountMap {
			// 创建基础的 ZoneVolumeItem
			baseZoneVolumeItem := volumecounter.ZoneVolumeItem{
				MaskProductID:        maskProductID,
				FulfillmentProductID: int64(productId),
				GroupCode:            groupCode,
				ZoneCode:             zoneCode,
				Direction:            rulevolume.MaskZoneDirectionDest,
				RuleMode:             rule_mode.MplOrderRule,
			}
			// 遍历 ParcelTypeList 创建不同的 ZoneVolumeItem
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				var value int64
				switch parcelType {
				case parcel_type_definition.ParcelTypeNone:
					value = -int64(countDetail.total)
				case parcel_type_definition.ParcelTypeCod:
					value = -int64(countDetail.cod)
				case parcel_type_definition.ParcelTypeBulky:
					value = -int64(countDetail.bulky)
				case parcel_type_definition.ParcelTypeHighValue:
					value = -int64(countDetail.highValue)
				case parcel_type_definition.ParcelTypeDg:
					value = -int64(countDetail.dg)
				}

				// 创建一个新的 ZoneVolumeItem 并添加到列表中
				zoneVolumeItem := baseZoneVolumeItem
				zoneVolumeItem.Value = value
				zoneVolumeItem.ParcelType = parcelType
				zoneVolumeItemList = append(zoneVolumeItemList, zoneVolumeItem)
			}
		}
	}
	if err := b.MaskVolumeCounter.BatchIncrByZoneVolumeByDate(ctx, zoneVolumeItemList, allocateTime); err != nil {
		logger.CtxLogErrorf(ctx, "IncrByZoneVolumeByDate failed: %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdateZoneVolume, err.Error())
	}

	routeVolumeItemList := make([]volumecounter.RouteVolumeItem, 0)
	for productId, routeCountMap := range deductFulfillmentProductRouteMap {
		groupCode := productGroupCodeMapping[int64(productId)]
		for routeCode, countDetail := range routeCountMap {
			// 创建基础的 ZoneVolumeItem
			baseRouteVolumeItem := volumecounter.RouteVolumeItem{
				MaskProductID:        maskProductID,
				FulfillmentProductID: int64(productId),
				GroupCode:            groupCode,
				RouteCode:            routeCode,
				RuleMode:             rule_mode.MplOrderRule,
			}

			// 遍历 ParcelTypeList 创建不同的 RouteVolumeItem
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				var value int64
				switch parcelType {
				case parcel_type_definition.ParcelTypeNone:
					value = -int64(countDetail.total)
				case parcel_type_definition.ParcelTypeCod:
					value = -int64(countDetail.cod)
				case parcel_type_definition.ParcelTypeBulky:
					value = -int64(countDetail.bulky)
				case parcel_type_definition.ParcelTypeHighValue:
					value = -int64(countDetail.highValue)
				case parcel_type_definition.ParcelTypeDg:
					value = -int64(countDetail.dg)
				}

				// 创建一个新的 RouteVolumeItem 并添加到列表中
				routeVolumeItem := baseRouteVolumeItem
				routeVolumeItem.Value = value
				routeVolumeItem.ParcelType = parcelType
				routeVolumeItemList = append(routeVolumeItemList, routeVolumeItem)
			}
		}
	}
	if err := b.MaskVolumeCounter.BatchIncrByRouteVolumeByDate(ctx, routeVolumeItemList, allocateTime); err != nil {
		logger.CtxLogErrorf(ctx, "IncrByRouteVolumeByDate failed: %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdateRouteVolume, err.Error())
	}

	if err := b.MaskVolumeCounter.IncrByMaskVolumeByDate(ctx, maskProductID, int64(-len(singleAllocatedOrders)), allocateTime); err != nil {
		logger.CtxLogErrorf(ctx, "IncrByMaskVolumeByDate failed: %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdateMaskVolume, err.Error())
	}

	// 扣减shop fulfillment product
	var shopFulfillmentItemList []pickup_efficiency_counter.ShopFulfillmentItem
	for shopId, fulfillmentProdCount := range deductShopFulfillmentProductsMap {
		for fulfillmentProduct, count := range fulfillmentProdCount {
			shopFulfillmentItemList = append(shopFulfillmentItemList, pickup_efficiency_counter.ShopFulfillmentItem{
				ShopID:               int64(shopId),
				FulfillmentProductID: int64(fulfillmentProduct),
				Count:                -count, // 这里是扣减，所以取count的负数
			})
		}
	}
	if err := b.PickupEffCounter.BatchIncrShopFulfillmentProducts(ctx, maskProductID, allocateTime, shopFulfillmentItemList); err != nil {
		logger.CtxLogErrorf(ctx, "deduct shop fulfillment products failed: %v", err)
		monitoring.ReportError(ctx, monitoring.CatModuleBatchAllocate, monitoring.UpdateShopFulfillmentProduct, err.Error())
	}
}

func (b *BatchAllocateServiceImpl) AbnormalInspection(ctx context.Context) *srerr.Error {
	// 检查未执行完（Pending + Processing）的Batch的是否已经超时
	condition := map[string]interface{}{
		"batch_status IN (?)": []int{batch_allocate2.BatchPending, batch_allocate2.BatchProcessing},
	}
	batchInfoList, err := b.SplitBatchRepo.GetBatchListByCondition(ctx, condition)
	if err != nil {
		return err
	}

	batchAllocateConf := configutil.GetBatchAllocateConf()
	for _, batchInfo := range batchInfoList {
		// 超出SLA，置为Fail
		if timeutil.GetCurrentUnixTimeStamp(ctx) >= batchInfo.Ctime+batchAllocateConf.SLA {
			// 先做日志+监控上报
			reportErrorEvent(ctx, monitoring.InspectAbnormalBatch, strconv.Itoa(int(batchInfo.ID)))
			logger.CtxLogInfof(ctx, "batch %d exceed SLA, update status to fail", batchInfo.ID)

			// 再更新状态
			batchInfo.BatchStatus = batch_allocate2.BatchFailed
			if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
				logger.CtxLogErrorf(ctx, "update exceed SLA batch task status failed, task: %d, err: %v", batchInfo.ID, err)
				continue
			}
		}
	}

	return nil
}

func (b *BatchAllocateServiceImpl) PushOrderResult(ctx context.Context, date time.Time) *srerr.Error {
	var (
		batchSize    = configutil.GetBatchAllocateConf().PushResultBatchSize
		dayPartition = date.Day()
	)

	orderResults, err := b.BatchAllocateOrderRepo.BatchGetPendingPushOrderResult(ctx, dayPartition, batchSize)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetPendingPushOrderResult failed: %v", err)
		return err
	}

	if len(orderResults) == 0 {
		logger.CtxLogInfof(ctx, "no pending push order")
		return nil
	}

	// 阻塞式，需要等到所有子任务执行完
	workerPool, pErr := ants.NewPool(configutil.GetBatchAllocateConf().PushResultConcurrentNum)
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "new goroutine pool failed: %v", pErr)
		return srerr.With(srerr.GoroutinePoolErr, nil, pErr)
	}
	defer workerPool.Release()

	var (
		pushedOrders       = make([]uint64, 0, len(orderResults)) // 对切片进行并发append是不安全的，需要加锁
		errButPushedOrders = make([]uint64, 0, len(orderResults)) // 对切片进行并发append是不安全的，需要加锁
		mutex              = sync.Mutex{}
		waitGroup          = sync.WaitGroup{}
	)

	waitGroup.Add(len(orderResults))
	for _, tempOrderResult := range orderResults {
		orderResult := tempOrderResult
		f := func() {
			defer waitGroup.Done()
			if err := b.pushSingleOrderResult(ctx, orderResult); err != nil {
				logger.CtxLogErrorf(ctx, "push single order result failed: %v", err)
				return
			}

			mutex.Lock()
			if orderResult.OrderStatus == batch_allocate.OrderStatusTypeAsyncFailPushed {
				errButPushedOrders = append(errButPushedOrders, orderResult.OrderID)
			} else {
				pushedOrders = append(pushedOrders, orderResult.OrderID)
			}
			mutex.Unlock()
		}

		if err := workerPool.Submit(f); err != nil {
			waitGroup.Done()
			logger.CtxLogErrorf(ctx, "submit push single order result to goroutine pool failed: %v", err)
			continue
		}
	}
	waitGroup.Wait()

	if len(pushedOrders) == 0 && len(errButPushedOrders) == 0 {
		logger.CtxLogInfof(ctx, "no pushed order, no need update order result status")
		return nil
	}

	if err := b.BatchAllocateOrderRepo.BatchUpdateOrderResultToPushed(ctx, dayPartition, pushedOrders, errButPushedOrders); err != nil {
		msg := fmt.Sprintf("BatchUpdateOrderResultToPushed failed: %v", err)
		logger.CtxLogErrorf(ctx, msg)
		reportErrorEvent(ctx, monitoring.UpdateBatchOrderResult, msg)
		return err
	}

	return nil
}

func (b *BatchAllocateServiceImpl) singleAllocateByBatchInfo(ctx context.Context, maskProductID int64, batchInfo batch_allocate2.BAOnlineBatchTab) *srerr.Error {
	// 抢占Batch，更新状态
	batchInfo.BatchStatus = batch_allocate2.BatchProcessing
	if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
		return err
	}

	// 根据Batch信息获取指定的订单
	orderList, err := b.BatchAllocateOrderRepo.GetOrderListByIDRange(ctx, batchInfo.LastOrderTableName, batchInfo.FirstOrderDbID, batchInfo.LastOrderDbID)
	if err != nil {
		return err
	}

	// 找不到数据，异常情况
	if len(orderList) == 0 {
		batchInfo.BatchStatus = batch_allocate2.BatchFailed
		if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
			return err
		}

		return nil
	}

	var (
		orderInfoMap = make(map[uint64]AllocateOrderInfo)
		orderResults = make([]*order.OrderResultTab, 0, len(orderList))
	)

	for _, o := range orderList {
		var (
			zoneCodeList              = make([]string, 0)
			routeCodeList             = make([]string, 0)
			fulfillmentShippingFeeMap = make(map[int]order.FulfillmentShippingFeeInfo)
			parcelAttrMap             = make(map[int]order.ParcelAttribute)
		)

		for _, hardResult := range o.FulfillmentHardResult {
			// zone code
			zoneVolumes, err := b.MaskRuleVolumeService.MatchLocsToZones(
				ctx, int64(o.MaskProductID), int64(hardResult.FulfillmentProductId),
				o.PickupAddress.LocationIdList, o.DeliverAddress.LocationIdList, rule_mode.MplOrderRule,
				o.PickupAddress.Postcode, o.DeliverAddress.Postcode, constant2.BatchAllocate,
			)
			if err != nil {
				logger.CtxLogErrorf(ctx, "match location to zones failed | err=%v", err)
			}

			for _, z := range zoneVolumes {
				zoneCodeList = append(zoneCodeList, z.LocCode)
			}

			// route code
			routeVolumes, err := b.MaskRuleVolumeService.MatchLocsToRoutes(
				ctx, int64(o.MaskProductID), int64(hardResult.FulfillmentProductId),
				o.PickupAddress.LocationIdList, o.DeliverAddress.LocationIdList, rule_mode.MplOrderRule,
				o.PickupAddress.Postcode, o.DeliverAddress.Postcode, constant2.BatchAllocate,
			)
			if err != nil {
				logger.CtxLogErrorf(ctx, "match location to routes failed | err=%v", err)
			}

			for _, r := range routeVolumes {
				routeCodeList = append(routeCodeList, r.LocCode)
			}

			// fulfillment shipping fee
			fulfillmentShippingFeeMap[hardResult.FulfillmentProductId] = order.FulfillmentShippingFeeInfo{
				FulfillmentShippingFee: hardResult.FulfillmentShippingFee,
				Detail:                 hardResult.FulfillmentShippingFeeDetail,
			}
			parcelAttrMap[hardResult.FulfillmentProductId] = order.ParcelAttribute{
				IsCod:       hardResult.ParcelAttribute.IsCod,
				IsBulky:     hardResult.ParcelAttribute.IsBulky,
				IsHighValue: hardResult.ParcelAttribute.IsHighValue,
				IsDg:        hardResult.ParcelAttribute.IsDg,
			}
		}

		zoneCodeList = common.RemoveDuplicateString(zoneCodeList)
		routeCodeList = common.RemoveDuplicateString(routeCodeList)
		allocateResult, fulfillmentShippingFeeInfo := b.singleAllocateByOrder(o)
		orderResults = append(orderResults, &order.OrderResultTab{
			OrderID:                    o.OrderID,
			OrderStatus:                batch_allocate.OrderStatusTypeAsyncAllocated,
			MaskProductID:              o.MaskProductID,
			AllocateResult:             allocateResult,
			FulfillmentShippingFeeInfo: fulfillmentShippingFeeInfo,
		})

		orderInfoMap[o.OrderID] = AllocateOrderInfo{
			OrderID:                       o.OrderID,
			ZoneCodeList:                  zoneCodeList,
			RouteCodeList:                 routeCodeList,
			FulfillmentShippingFeeInfoMap: fulfillmentShippingFeeMap,
			BatchAllocateResult:           allocateResult,
			ParcelAttrMap:                 parcelAttrMap,
			ShopID:                        o.ShopID,
		}
	}

	dayIndex, err := order.SplitDayIndexByTableName(batchInfo.LastOrderTableName)
	if err != nil {
		return err
	}

	allocateTime := timeutil.GetLocalTime(ctx)
	allocatedOrders := b.checkOrderAllocated(ctx, orderInfoMap, dayIndex)
	if len(allocatedOrders) > 0 {
		excludedAllocatedOrders := make([]*order.OrderResultTab, 0, len(orderResults))
		for _, o := range orderResults {
			if _, exist := allocatedOrders[o.OrderID]; !exist {
				excludedAllocatedOrders = append(excludedAllocatedOrders, o)
			}
		}

		orderResults = excludedAllocatedOrders
	}

	if err := b.BatchAllocateOrderRepo.BatchCreateOrderResult(ctx, orderResults, dayIndex); err != nil {
		return err
	}

	// 因为这里走的是兜底Single，所以取的是Single的VolumeRule
	ruleVolume, err := b.MaskRuleVolumeService.GetActiveRuleVolumeByMaskProductIDWithCache(
		ctx, int64(batchInfo.MaskProductID), rule_mode.MplOrderRule, constant2.AllocateMethodSingle)
	if err != nil {
		return err
	}

	productGroupCodeMapping := ruleVolume.GetFulfillmentProductToGroupMap()
	productVolume, productZoneVolume, productRouteVolume := b.aggregateVolume(orderResults, orderInfoMap)
	for productID, countDetail := range productVolume {
		groupCode := productGroupCodeMapping[int64(productID)]
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productID), groupCode, countDetail.total, allocateTime, parcel_type_definition.ParcelTypeNone); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productID), groupCode, countDetail.cod, allocateTime, parcel_type_definition.ParcelTypeCod); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productID), groupCode, countDetail.bulky, allocateTime, parcel_type_definition.ParcelTypeBulky); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productID), groupCode, countDetail.highValue, allocateTime, parcel_type_definition.ParcelTypeHighValue); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
		if err := b.MaskVolumeCounter.IncrByProductVolumeByDate(
			ctx, maskProductID, int64(productID), groupCode, countDetail.dg, allocateTime, parcel_type_definition.ParcelTypeDg); err != nil {
			logger.CtxLogErrorf(ctx, "IncrByProductVolumeByDate failed: %v", err)
		}
	}

	zoneVolumeItemList := make([]volumecounter.ZoneVolumeItem, 0)
	for productID, zoneVolume := range productZoneVolume {
		groupCode := productGroupCodeMapping[int64(productID)]
		for zoneCode, countDetail := range zoneVolume {
			// 创建基础的 ZoneVolumeItem
			baseZoneVolumeItem := volumecounter.ZoneVolumeItem{
				MaskProductID:        maskProductID,
				FulfillmentProductID: int64(productID),
				GroupCode:            groupCode,
				ZoneCode:             zoneCode,
				Direction:            rulevolume.MaskZoneDirectionDest,
				RuleMode:             rule_mode.MplOrderRule,
			}

			// 遍历 ParcelTypeList 创建不同的 ZoneVolumeItem
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				var value int64
				switch parcelType {
				case parcel_type_definition.ParcelTypeNone:
					value = int64(countDetail.total)
				case parcel_type_definition.ParcelTypeCod:
					value = int64(countDetail.cod)
				case parcel_type_definition.ParcelTypeBulky:
					value = int64(countDetail.bulky)
				case parcel_type_definition.ParcelTypeHighValue:
					value = int64(countDetail.highValue)
				case parcel_type_definition.ParcelTypeDg:
					value = int64(countDetail.dg)
				}

				// 创建一个新的 ZoneVolumeItem 并添加到列表中
				zoneVolumeItem := baseZoneVolumeItem
				zoneVolumeItem.Value = value
				zoneVolumeItem.ParcelType = parcelType
				zoneVolumeItemList = append(zoneVolumeItemList, zoneVolumeItem)
			}
		}
	}
	if err := b.MaskVolumeCounter.BatchIncrByZoneVolumeByDate(ctx, zoneVolumeItemList, allocateTime); err != nil {
		return srerr.With(srerr.CodisErr, maskProductID, err)
	}

	routeVolumeItemList := make([]volumecounter.RouteVolumeItem, 0)
	for productID, routeVolume := range productRouteVolume {
		groupCode := productGroupCodeMapping[int64(productID)]
		for routeCode, countDetail := range routeVolume {
			// 创建基础的 ZoneVolumeItem
			baseRouteVolumeItem := volumecounter.RouteVolumeItem{
				MaskProductID:        maskProductID,
				FulfillmentProductID: int64(productID),
				GroupCode:            groupCode,
				RouteCode:            routeCode,
				RuleMode:             rule_mode.MplOrderRule,
			}

			// 遍历 ParcelTypeList 创建不同的 RouteVolumeItem
			for _, parcelType := range parcel_type_definition.ParcelTypeList {
				var value int64
				switch parcelType {
				case parcel_type_definition.ParcelTypeNone:
					value = int64(countDetail.total)
				case parcel_type_definition.ParcelTypeCod:
					value = int64(countDetail.cod)
				case parcel_type_definition.ParcelTypeBulky:
					value = int64(countDetail.bulky)
				case parcel_type_definition.ParcelTypeHighValue:
					value = int64(countDetail.highValue)
				case parcel_type_definition.ParcelTypeDg:
					value = int64(countDetail.dg)
				}

				// 创建一个新的 RouteVolumeItem 并添加到列表中
				routeVolumeItem := baseRouteVolumeItem
				routeVolumeItem.Value = value
				routeVolumeItem.ParcelType = parcelType
				routeVolumeItemList = append(routeVolumeItemList, routeVolumeItem)
			}
		}
	}
	if err := b.MaskVolumeCounter.BatchIncrByRouteVolumeByDate(ctx, routeVolumeItemList, allocateTime); err != nil {
		return srerr.With(srerr.CodisErr, maskProductID, err)
	}

	if err := b.MaskVolumeCounter.IncrByMaskVolumeByDate(ctx, int64(batchInfo.MaskProductID), int64(len(orderResults)), allocateTime); err != nil {
		logger.CtxLogErrorf(ctx, "IncrByMaskVolumeByDate failed: %v", err)
	}

	batchInfo.BatchStatus = batch_allocate2.BatchDone
	if err := b.SplitBatchRepo.UpdateBatchObject(ctx, batchInfo); err != nil {
		return err
	}

	return nil
}

// aggregateVolume 从原始调度结果信息中聚合Country/Zone、不同ParcelType的单量
func (b *BatchAllocateServiceImpl) aggregateVolume(
	orderResults []*order.OrderResultTab, orderInfoMap map[uint64]AllocateOrderInfo,
) (map[int]*orderCountDetail, map[int]map[string]*orderCountDetail, map[int]map[string]*orderCountDetail) {
	var (
		productVolume      = make(map[int]*orderCountDetail)
		productZoneVolume  = make(map[int]map[string]*orderCountDetail)
		productRouteVolume = make(map[int]map[string]*orderCountDetail)
	)

	for _, o := range orderResults {
		if productVolume[o.AllocateResult] == nil {
			productVolume[o.AllocateResult] = &orderCountDetail{}
		}

		productVolume[o.AllocateResult].total += 1

		orderInfo, exist := orderInfoMap[o.OrderID]
		if !exist {
			continue
		}

		parcelAttr := orderInfo.ParcelAttrMap[o.AllocateResult]
		if parcelAttr.IsCod {
			productVolume[o.AllocateResult].cod += 1
		}
		if parcelAttr.IsBulky {
			productVolume[o.AllocateResult].bulky += 1
		}
		if parcelAttr.IsHighValue {
			productVolume[o.AllocateResult].highValue += 1
		}
		if parcelAttr.IsDg {
			productVolume[o.AllocateResult].dg += 1
		}

		for _, z := range orderInfo.ZoneCodeList {
			if productZoneVolume[o.AllocateResult] == nil {
				productZoneVolume[o.AllocateResult] = make(map[string]*orderCountDetail)
			}
			if productZoneVolume[o.AllocateResult][z] == nil {
				productZoneVolume[o.AllocateResult][z] = &orderCountDetail{}
			}

			productZoneVolume[o.AllocateResult][z].total += 1
			if parcelAttr.IsCod {
				productZoneVolume[o.AllocateResult][z].cod += 1
			}
			if parcelAttr.IsBulky {
				productZoneVolume[o.AllocateResult][z].bulky += 1
			}
			if parcelAttr.IsHighValue {
				productZoneVolume[o.AllocateResult][z].highValue += 1
			}
			if parcelAttr.IsDg {
				productZoneVolume[o.AllocateResult][z].dg += 1
			}
		}
		for _, r := range orderInfo.RouteCodeList {
			if productRouteVolume[o.AllocateResult] == nil {
				productRouteVolume[o.AllocateResult] = make(map[string]*orderCountDetail)
			}
			if productRouteVolume[o.AllocateResult][r] == nil {
				productRouteVolume[o.AllocateResult][r] = &orderCountDetail{}
			}
			productRouteVolume[o.AllocateResult][r].total += 1
			if parcelAttr.IsCod {
				productRouteVolume[o.AllocateResult][r].cod += 1
			}
			if parcelAttr.IsBulky {
				productRouteVolume[o.AllocateResult][r].bulky += 1
			}
			if parcelAttr.IsHighValue {
				productRouteVolume[o.AllocateResult][r].highValue += 1
			}
			if parcelAttr.IsDg {
				productRouteVolume[o.AllocateResult][r].dg += 1
			}
		}
	}

	return productVolume, productZoneVolume, productRouteVolume
}

func (b *BatchAllocateServiceImpl) singleAllocateByOrder(holdOrder *order.BatchAllocateHoldOrderTab) (int, order.FulfillmentShippingFeeInfo) {
	// 做一层保护
	if len(holdOrder.FulfillmentHardResult) == 0 {
		return 0, order.FulfillmentShippingFeeInfo{}
	}

	var (
		defaultResult              = holdOrder.FulfillmentHardResult[0]
		allocateResult             = defaultResult.FulfillmentProductId
		fulfillmentShippingFeeInfo = order.FulfillmentShippingFeeInfo{
			FulfillmentShippingFee: defaultResult.FulfillmentShippingFee,
			Detail:                 defaultResult.FulfillmentShippingFeeDetail,
		}
		cheapestShippingFee = math.MaxFloat64 // default result的allocation shipping fee不一定有值，所以这里初始化用最大值更合理
	)

	for _, p := range holdOrder.FulfillmentHardResult {
		if (p.AllocationShippingFee > 0) && (p.AllocationShippingFee < cheapestShippingFee) {
			cheapestShippingFee = p.AllocationShippingFee
			allocateResult = p.FulfillmentProductId
			fulfillmentShippingFeeInfo = order.FulfillmentShippingFeeInfo{
				FulfillmentShippingFee: p.FulfillmentShippingFee,
				Detail:                 p.FulfillmentShippingFeeDetail,
			}
		}
	}

	return allocateResult, fulfillmentShippingFeeInfo
}

func (b *BatchAllocateServiceImpl) getBatchMinuteVolumeList(ctx context.Context, rule *rule.MaskRule) ([]float64, *srerr.Error) {
	batchMinuteConf, gErr := b.BatchMinuteOrderConfService.GetBatchMinuteOrderConf(ctx, allocation2.GetMinuteOrderConfReq{
		MaskProductId: uint64(rule.MaskProductId),
		ConfStatus:    batch_minute_order_conf.ConfActive,
		ConfType:      batch_allocate3.ConvertUseForCampaign(rule.BatchRuleDetail.BatchAllocationRuleConfig.UseForCampaign),
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetBatchMinuteOrderConf failed: %v", gErr)
		return nil, gErr
	}
	batchMinuteVolumeMap := make(map[int64]float64, 0)
	for _, batchMinute := range batchMinuteConf.List {
		ratio, err := strconv.ParseFloat(batchMinute.Ratio, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Parse ratio failed, ratio:%v, err:%v", batchMinute.Ratio, err)
			continue
		}
		batchMinuteVolumeMap[batchMinute.Minute] = ratio
	}

	if len(batchMinuteVolumeMap) == 0 {
		logger.CtxLogErrorf(ctx, "get empty batch minute volume")
		return nil, srerr.New(srerr.DatabaseErr, nil, "get empty batch minute volume")
	}

	result := make([]float64, len(batchMinuteVolumeMap))
	for i := 0; i < len(batchMinuteVolumeMap); i++ {
		result[i] = batchMinuteVolumeMap[int64(i)+1]
	}
	b.logInfo.BatchMinOrderConf = result

	return result, nil
}

func (b *BatchAllocateServiceImpl) marshallOrderResultMsg(ctx context.Context, o *order.OrderResultTab) ([]byte, *srerr.Error) {
	productDetail, err := b.GetProductDetailWithCache(ctx, o.AllocateResult)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get product detail failed | productID=%d, err=%v", o.AllocateResult, err)
		return nil, err
	}

	r := &PushOrderResultMsg{
		OrderID:             o.OrderID,
		ShippingChannelID:   o.AllocateResult,
		ShippingChannelName: productDetail.BuyerDisplayName,
		ChannelFlag:         productDetail.ProductFlag,
		ShippingMethod:      productDetail.ShippingMethod,
	}

	// 校验订单结果信息，获取订单结果的status、返回上游的retcode，以及返回上游的message
	retCode, orderStatus, msg := order.GetOrderStatus(ctx, o)

	// 转换install allocate结果
	var installAllocateResultList []InstallAllocateResult
	for _, result := range o.InstallAllocateResult {
		installProductDetail, iErr := b.GetProductDetailWithCache(ctx, int(result.ProductId))
		if iErr != nil {
			logger.CtxLogErrorf(ctx, "get install product detail failed | productID=%d, err=%v", result.ProductId, iErr)
			return nil, iErr
		}
		installAllocateResultList = append(installAllocateResultList, InstallAllocateResult{
			ItemUniqueId:       result.ItemUniqueId,
			ItemId:             result.ItemId,
			InstallChannelID:   result.ProductId,
			InstallChannelName: installProductDetail.BuyerDisplayName,
		})
	}
	r.InstallAllocateResult = installAllocateResultList

	o.OrderStatus = orderStatus
	r.Retcode = retCode
	r.Message = msg
	if retCode == batch_allocate.Success {
		r.FulfillmentShippingFee = o.FulfillmentShippingFeeInfo.FulfillmentShippingFee
	}

	return objutil.JsonBytes(r), nil
}

func (b *BatchAllocateServiceImpl) pushSingleOrderResult(ctx context.Context, orderResult *order.OrderResultTab) *srerr.Error {
	resultMsg, err := b.marshallOrderResultMsg(ctx, orderResult)
	if err != nil {
		msg := fmt.Sprintf("marshall order result to kafka msg entity failed | orderID=%d, err=%v", orderResult.OrderID, err)
		reportErrorEvent(ctx, monitoring.PushOrderResult, msg)
		logger.CtxLogErrorf(ctx, msg)
		return err
	}

	logger.CtxLogInfof(ctx, "result msg:%s", string(resultMsg))

	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrBANamespace
	if err := kafkahelper.DeliveryMessage(
		ctx, namespace, constant.TaskNameUpdateOrderResult, resultMsg, nil, kafkahelper.PushOrderResultType,
	); err != nil {
		msg := fmt.Sprintf("delivery order result to kafka failed | orderID=%d, err=%v", orderResult.OrderID, err)
		reportErrorEvent(ctx, monitoring.PushOrderResult, msg)
		logger.CtxLogErrorf(ctx, msg)
		return err
	}

	reportSuccessEvent(ctx, monitoring.PushOrderResult, strconv.Itoa(int(orderResult.OrderID)))

	// report sla
	endTimeStamp := timeutil.GetCurrentUnixMilliTimeStamp(ctx)
	sla := endTimeStamp - orderResult.RequestTimeStamp
	// 这里不上报order id的原因是key太离散，给容器造成压力
	allocation3.ReportBatchAllocateTimeConsume(
		orderResult.MaskProductID, allocation3.OrderSlaScene, allocation3.OrderSlaField, "", float64(sla))

	return nil
}

func (b *BatchAllocateServiceImpl) reportShippingFee(ctx context.Context, maskProductID uint64, resp *algorithm_client.BatchAllocateRespBo) {
	var (
		shippingFeeMap = make(map[uint64]float64, 0)
		conf           = configutil.GetBatchAllocateConf()
	)

	for i := 0; i < len(resp.OrderResult); i++ {
		result := resp.OrderResult[i]
		if conf.UsDollarRatio != 0 && conf.UsDollarLimiter != 0 {
			if result.ShippingFee == batch_allocate.IllegalEsf {
				continue
			}
			if result.ShippingFee/float64(conf.UsDollarRatio) >= float64(conf.UsDollarLimiter) {
				continue
			}
			shippingFeeMap[result.FulfillmentProductId] += result.ShippingFee
		}
	}
	for fulfillmentProduct, shippingFee := range shippingFeeMap {
		prometheusutil.AddBatchAllocateCounter(prometheusutil.CounterInfo{
			MaskProductID: strconv.FormatUint(maskProductID, 10),
			Scene:         "BatchShippingFee",
			Field:         "ShippingFee",
			FieldValue:    strconv.FormatUint(fulfillmentProduct, 10),
			ReportValue:   shippingFee,
		})
	}
}

func (b *BatchAllocateServiceImpl) batchIncrZoneVolume(
	ctx context.Context, maskProductID, fulfillmentProductID int64, groupCode string,
	zoneStatsResult map[string]*algorithm_client.StatsIndicator, allocateTime time.Time,
) *srerr.Error {
	zoneVolumeItemList := make([]volumecounter.ZoneVolumeItem, 0, len(zoneStatsResult)*len(parcel_type_definition.ParcelTypeList))
	for zoneCode, zoneStatsResult := range zoneStatsResult {
		// 创建基础的 ZoneVolumeItem
		baseZoneVolumeItem := volumecounter.ZoneVolumeItem{
			MaskProductID:        maskProductID,
			FulfillmentProductID: fulfillmentProductID,
			GroupCode:            groupCode,
			ZoneCode:             zoneCode,
			Direction:            rulevolume.MaskZoneDirectionDest,
			RuleMode:             rule_mode.MplOrderRule,
		}

		// 遍历 ParcelTypeList 创建不同的 ZoneVolumeItem
		for _, parcelType := range parcel_type_definition.ParcelTypeList {
			var value int64
			switch parcelType {
			case parcel_type_definition.ParcelTypeNone:
				value = int64(zoneStatsResult.OrderNum)
			case parcel_type_definition.ParcelTypeCod:
				value = int64(zoneStatsResult.CodOrderNum)
			case parcel_type_definition.ParcelTypeBulky:
				value = int64(zoneStatsResult.BulkyOrderNum)
			case parcel_type_definition.ParcelTypeHighValue:
				value = int64(zoneStatsResult.HighValueOrderNum)
			case parcel_type_definition.ParcelTypeDg:
				value = int64(zoneStatsResult.DgOrderNum)
			}

			// 创建一个新的 ZoneVolumeItem 并添加到列表中
			zoneVolumeItem := baseZoneVolumeItem
			zoneVolumeItem.Value = value
			zoneVolumeItem.ParcelType = parcelType
			zoneVolumeItemList = append(zoneVolumeItemList, zoneVolumeItem)
		}
	}

	if err := b.MaskVolumeCounter.BatchIncrByZoneVolumeByDate(ctx, zoneVolumeItemList, allocateTime); err != nil {
		return srerr.With(srerr.CodisErr, maskProductID, err)
	}

	return nil
}

func (b *BatchAllocateServiceImpl) batchIncrRouteVolume(
	ctx context.Context, maskProductID, fulfillmentProductID int64, groupCode string,
	routeStatsResult map[string]*algorithm_client.StatsIndicator, allocateTime time.Time,
) *srerr.Error {
	routeVolumeItemList := make([]volumecounter.RouteVolumeItem, 0, len(routeStatsResult)*len(parcel_type_definition.ParcelTypeList))
	for routeCode, routeStatsResult := range routeStatsResult {
		// 创建基础的 ZoneVolumeItem
		baseRouteVolumeItem := volumecounter.RouteVolumeItem{
			MaskProductID:        maskProductID,
			FulfillmentProductID: fulfillmentProductID,
			GroupCode:            groupCode,
			RouteCode:            routeCode,
			RuleMode:             rule_mode.MplOrderRule,
		}

		// 遍历 ParcelTypeList 创建不同的 RouteVolumeItem
		for _, parcelType := range parcel_type_definition.ParcelTypeList {
			var value int64
			switch parcelType {
			case parcel_type_definition.ParcelTypeNone:
				value = int64(routeStatsResult.OrderNum)
			case parcel_type_definition.ParcelTypeCod:
				value = int64(routeStatsResult.CodOrderNum)
			case parcel_type_definition.ParcelTypeBulky:
				value = int64(routeStatsResult.BulkyOrderNum)
			case parcel_type_definition.ParcelTypeHighValue:
				value = int64(routeStatsResult.HighValueOrderNum)
			case parcel_type_definition.ParcelTypeDg:
				value = int64(routeStatsResult.DgOrderNum)
			}

			// 创建一个新的 RouteVolumeItem 并添加到列表中
			routeVolumeItem := baseRouteVolumeItem
			routeVolumeItem.Value = value
			routeVolumeItem.ParcelType = parcelType
			routeVolumeItemList = append(routeVolumeItemList, routeVolumeItem)
		}
	}
	if err := b.MaskVolumeCounter.BatchIncrByRouteVolumeByDate(ctx, routeVolumeItemList, allocateTime); err != nil {
		return srerr.With(srerr.CodisErr, maskProductID, err)
	}

	return nil
}

func getProductIDsByProductInfoBos(productInfoBos []*algorithm_client.ProductInfoBo) []int {
	productIDs := make([]int, 0, len(productInfoBos))
	for _, p := range productInfoBos {
		productIDs = append(productIDs, int(p.FulfillmentProductId))
	}

	return productIDs
}

func enrichVolumeInfo(v *allocation3.VolumeInfo, target *algorithm_client.TargetVolumeDetailBo) {
	v.MaxDailyLimit = int64(target.DailyMaxTar)
	v.MaxCodDailyLimit = int64(target.DailyCodMaxTar)
	v.MaxBulkyDailyLimit = int64(target.DailyBulkyMaxTar)
	v.MaxHighValueDailyLimit = int64(target.DailyHighValueMaxTar)
	v.MaxDgDailyLimit = int64(target.DailyDgMaxTar)
	v.MinBatchLimit = int64(target.BatchMinTar)
	v.SystemVolume = int64(target.AllocatedVolume)
	v.SystemCodVolume = int64(target.CodAllocatedVolume)
	v.SystemBulkyVolume = int64(target.BulkyAllocatedVolume)
	v.SystemHighValueVolume = int64(target.HighValueAllocatedVolume)
	v.SystemDgVolume = int64(target.DgAllocatedVolume)
}
