package allocation

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"

type PushOrderResultMsg struct {
	Retcode                int                     `json:"retcode"`
	Message                string                  `json:"message"`
	OrderID                uint64                  `json:"order_id"`
	ShippingChannelID      int                     `json:"shipping_channel_id"`
	ShippingChannelName    string                  `json:"shipping_channel_name"`
	ChannelFlag            uint64                  `json:"channel_flag"`
	ShippingMethod         int                     `json:"shipping_method"`
	FulfillmentShippingFee float64                 `json:"fulfillment_shipping_fee"`
	InstallAllocateResult  []InstallAllocateResult `json:"install_allocate_result"`
}

type InstallAllocateResult struct {
	ItemUniqueId       string `json:"item_unique_id"`
	ItemId             uint64 `json:"item_id"`
	InstallChannelID   int64  `json:"install_channel_id"`
	InstallChannelName string `json:"install_channel_name"`
}

type PushOrderResultShippingFeeDetail struct {
	BasicShippingFee        float64 `json:"basic_shipping_fee"`
	InsuranceFee            float64 `json:"insurance_fee"`
	CodFee                  float64 `json:"cod_fee"`
	RemoteFee               float64 `json:"remote_fee"`
	FuelFee                 float64 `json:"fuel_fee"`
	VatFee                  float64 `json:"vat_fee"`
	AdditionalFee           float64 `json:"additional_fee"`
	CodFeeWithVat           float64 `json:"cod_fee_with_vat"`
	VatForCodFee            float64 `json:"vat_for_cod_fee"`
	AdjustmentFee           float64 `json:"adjustment_fee"`
	AdjustmentFeeWithoutCod float64 `json:"adjustment_fee_without_cod"`
}

type AllocateOrderInfo struct {
	OrderID                       uint64                                   `json:"order_id"`
	ZoneCodeList                  []string                                 `json:"zone_code_list"`
	RouteCodeList                 []string                                 `json:"route_code_list"`
	FulfillmentShippingFeeInfoMap map[int]order.FulfillmentShippingFeeInfo `json:"fulfillment_shipping_fee_info"`
	BatchAllocateResult           int                                      `json:"batch_allocate_result"`
	RequestTimeStamp              int64                                    `json:"request_time_stamp"` //unix milli of request time
	ParcelAttrMap                 map[int]order.ParcelAttribute            `json:"parcel_attr_map"`
	ShopID                        int64                                    `json:"shop_id"`
}

type VolumeInfo struct {
	MaxDailyLimit          int64
	MaxCodDailyLimit       int64
	MaxBulkyDailyLimit     int64
	MaxHighValueDailyLimit int64
	MaxDgDailyLimit        int64
	MinBatchLimit          int64
	SystemVolume           int64
	SystemCodVolume        int64
	SystemBulkyVolume      int64
	SystemHighValueVolume  int64
	SystemDgVolume         int64
}
