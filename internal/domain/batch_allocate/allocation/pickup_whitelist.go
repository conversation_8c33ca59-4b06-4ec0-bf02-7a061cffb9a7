package allocation

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"math"
)

// pickupEffiWhitelistFilter 检查Shop是否在白名单中，如果在白名单中，则根据优先级选择最优的Product
func (b *BatchAllocateServiceImpl) pickupEffiWhitelistFilter(
	ctx context.Context, orderID uint64, maskProductID, shopID int64, fulfillmentProductBos []*algorithm_client.ProductInfoBo,
) ([]*algorithm_client.ProductInfoBo, bool) {
	// 没命中白名单的直接返回，不需要Filter
	if !b.ShopWhitelistService.CheckShopWhitelistAndFulfillmentType(ctx, shopID, whitelist.MPLFulfillment) {
		return fulfillmentProductBos, false
	}

	priorityMap, err := b.ShopWhitelistService.GetPickupPriorityByMaskProductIDWithCache(ctx, maskProductID)
	if err != nil {
		// 没有找到Mask Product对应的优先级，正常业务现象，直接返回
		logger.CtxLogDebugf(ctx, "hit shop pickup efficiency whitelist but no priority found for mask product id: %d", orderID)
		return fulfillmentProductBos, false
	}

	return GetPickupEfficiencyWhitelistProduct(ctx, orderID, fulfillmentProductBos, priorityMap), true
}

func GetPickupEfficiencyWhitelistProduct(
	ctx context.Context, orderID uint64, fulfillmentProductBos []*algorithm_client.ProductInfoBo, priorityMap map[int64]int64,
) []*algorithm_client.ProductInfoBo {

	var (
		returnProductBo *algorithm_client.ProductInfoBo
		minPriority     int64 = math.MaxInt64
	)
	for _, productInfoBo := range fulfillmentProductBos {
		productPriority, exist := priorityMap[int64(productInfoBo.FulfillmentProductId)]
		if !exist {
			continue
		}
		if productPriority < minPriority {
			minPriority = productPriority
			returnProductBo = productInfoBo
		}
	}

	if returnProductBo == nil {
		return fulfillmentProductBos
	}

	logger.CtxLogInfof(ctx, "hit shop pickup efficiency whitelist | order id: %d, return product: %d", orderID, returnProductBo.FulfillmentProductId)

	return []*algorithm_client.ProductInfoBo{returnProductBo}
}
