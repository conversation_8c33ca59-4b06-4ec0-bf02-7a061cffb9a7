package order

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"testing"
)

func TestBatchAllocateOrderRepoImpl_GetOrderListByIDRange(t *testing.T) {
	// 由于借助chassis，所以需要在启动环境中手动设置参数，参数如下：
	// CHASSIS_HOME=/Users/<USER>/go/src/logistics-smart-routing;CID=id;ENV=test;GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn;SP_UNIT_SOCKET=${HOME}/run/spex/spex.sock
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	ctx := context.TODO()
	type args struct {
		ctx       context.Context
		tableName string
		startID   uint64
		endID     uint64
	}
	tests := []struct {
		name  string
		args  args
		want  []*BatchAllocateHoldOrderTab
		want1 *srerr.Error
	}{
		// TODO: Add test cases.
		{
			args: args{
				ctx:       ctx,
				tableName: "batch_allocate_hold_order_tab_00_00000000",
				startID:   1,
				endID:     1,
			},
		},
		{
			args: args{
				ctx:       ctx,
				tableName: "batch_allocate_hold_order_tab_00_00000000",
				startID:   1,
				endID:     2,
			},
		},
		{
			args: args{
				ctx:       ctx,
				tableName: "batch_allocate_hold_order_tab_00_00000000",
				startID:   1,
				endID:     1001,
			},
		},
		{
			args: args{
				ctx:       ctx,
				tableName: "batch_allocate_hold_order_tab_00_00000000",
				startID:   1,
				endID:     1002,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := BatchAllocateOrderRepoImpl{}
			got, got1 := b.GetOrderListByIDRange(tt.args.ctx, tt.args.tableName, tt.args.startID, tt.args.endID)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOrderListByIDRange() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetOrderListByIDRange() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
