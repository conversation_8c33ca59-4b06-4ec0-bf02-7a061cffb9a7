package order

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

var (
	BatchAllocateHoldOrderTabHook = &BatchAllocateHoldOrderTab{}
	OrderResultTabHook            = &OrderResultTab{}
)

const (
	batchAllocateHoldOrderTableName = "batch_allocate_hold_order_tab_%02d_%08d"
	orderResultTableName            = "order_result_tab_%08d"
)

type (
	AddressInfo struct {
		Postcode       string  `json:"postcode"`
		LocationIdList []int64 `json:"location_id_list"`
	}

	FulfillmentHardResult struct {
		FulfillmentProductId         int                          `json:"fulfillment_product_id"`
		AllocationShippingFee        float64                      `json:"allocation_shipping_fee"`
		FulfillmentShippingFee       float64                      `json:"fulfillment_shipping_fee"`
		FulfillmentShippingFeeDetail FulfillmentShippingFeeDetail `json:"fulfillment_shipping_fee_detail"`
		ParcelAttribute              ParcelAttribute              `json:"parcel_attribute"`
	}

	FulfillmentShippingFeeDetail struct {
		BasicShippingFee        float64 `json:"basic_shipping_fee"`
		InsuranceFee            float64 `json:"insurance_fee"`
		CodFee                  float64 `json:"cod_fee"`
		RemoteFee               float64 `json:"remote_fee"`
		FuelFee                 float64 `json:"fuel_fee"`
		VatFee                  float64 `json:"vat_fee"`
		AdditionalFee           float64 `json:"additional_fee"`
		CodFeeWithVat           float64 `json:"cod_fee_with_vat"`
		VatForCodFee            float64 `json:"vat_for_cod_fee"`
		AdjustmentFee           float64 `json:"adjustment_fee"`
		AdjustmentFeeWithoutCod float64 `json:"adjustment_fee_without_cod"`
	}

	ParcelAttribute struct {
		IsCod       bool `json:"is_cod"`
		IsBulky     bool `json:"is_bulky"`
		IsHighValue bool `json:"is_high_value"`
		IsDg        bool `json:"is_dg"`
	}
)

type BatchAllocateHoldOrderTab struct {
	ID                       uint64                  `gorm:"column:id" json:"id"`
	OrderID                  uint64                  `gorm:"column:order_id" json:"order_id"`
	Day                      int                     `gorm:"-" json:"day"`
	MaskProductID            int                     `gorm:"column:mask_product_id" json:"mask_product_id"`
	PickupAddressStr         string                  `gorm:"column:pickup_address" json:"-"`
	PickupAddress            AddressInfo             `gorm:"-" json:"pickup_address"`
	DeliverAddressStr        string                  `gorm:"column:deliver_address" json:"-"`
	DeliverAddress           AddressInfo             `gorm:"-" json:"deliver_address"`
	FulfillmentHardResultStr string                  `gorm:"column:fulfillment_hard_result" json:"-"`
	FulfillmentHardResult    []FulfillmentHardResult `gorm:"-" json:"fulfillment_hard_result"`
	ShopID                   int64                   `gorm:"column:shop_id" json:"shop_id"`
	RequestTimeStamp         int64                   `gorm:"column:request_time_stamp" json:"request_time_stamp"` // unix milli of request time
	Ctime                    int64                   `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	Mtime                    int64                   `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

// Unmarshall 转换成系统可读的数据结构 db=>system
func (o *BatchAllocateHoldOrderTab) Unmarshall() *srerr.Error {
	if err := jsoniter.UnmarshalFromString(o.PickupAddressStr, &o.PickupAddress); err != nil {
		return srerr.With(srerr.JsonErr, o.PickupAddressStr, err)
	}
	if err := jsoniter.UnmarshalFromString(o.DeliverAddressStr, &o.DeliverAddress); err != nil {
		return srerr.With(srerr.JsonErr, o.DeliverAddressStr, err)
	}
	if err := jsoniter.UnmarshalFromString(o.FulfillmentHardResultStr, &o.FulfillmentHardResult); err != nil {
		return srerr.With(srerr.JsonErr, o.FulfillmentHardResultStr, err)
	}
	return nil
}

// Marshall 转换成可以存储到db的数据结构 system=>db
func (o *BatchAllocateHoldOrderTab) Marshall() *srerr.Error {
	var err error
	if o.PickupAddressStr, err = jsoniter.MarshalToString(o.PickupAddress); err != nil {
		return srerr.With(srerr.JsonErr, o.PickupAddress, err)
	}
	if o.DeliverAddressStr, err = jsoniter.MarshalToString(o.DeliverAddress); err != nil {
		return srerr.With(srerr.JsonErr, o.DeliverAddress, err)
	}
	if o.FulfillmentHardResultStr, err = jsoniter.MarshalToString(o.FulfillmentHardResult); err != nil {
		return srerr.With(srerr.JsonErr, o.FulfillmentHardResult, err)
	}
	return nil
}

func (o BatchAllocateHoldOrderTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateRead
}

func (o BatchAllocateHoldOrderTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateWrite
}

func (o BatchAllocateHoldOrderTab) TableName() string {
	return batchAllocateHoldOrderTableName
}

func (o BatchAllocateHoldOrderTab) TableNameByPartition(maskProductID, day int) string {
	productPartition := configutil.GetBatchAllocateConf().MaskProductTableMapping[maskProductID]
	return fmt.Sprintf(batchAllocateHoldOrderTableName, productPartition, day)
}

func (o BatchAllocateHoldOrderTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            o.ID,
		ModelName:     o.TableName(),
		MaskProductId: uint64(o.MaskProductID),
	}
}

type FulfillmentShippingFeeInfo struct {
	FulfillmentShippingFee float64                      `json:"fulfillment_shipping_fee"`
	Detail                 FulfillmentShippingFeeDetail `json:"detail"`
}

type InstallAllocateResult struct {
	ItemUniqueId     string `json:"item_unique_id"`
	ItemId           uint64 `json:"item_id"`
	MaskingProductId int64  `json:"masking_product_id"`
	ProductId        int64  `json:"product_id"`
}

type OrderResultTab struct {
	ID                            uint64                     `gorm:"column:id" json:"id"`
	OrderID                       uint64                     `gorm:"column:order_id" json:"order_id"`
	OrderStatus                   batch_allocate.OrderStatus `gorm:"column:order_status" json:"order_status"`
	MaskProductID                 int                        `gorm:"column:mask_product_id" json:"mask_product_id"`
	AllocateResult                int                        `gorm:"column:allocate_result" json:"allocate_result"`
	FulfillmentShippingFeeInfoStr string                     `gorm:"column:fulfillment_shipping_fee_info" json:"-"`
	FulfillmentShippingFeeInfo    FulfillmentShippingFeeInfo `gorm:"-" json:"fulfillment_shipping_fee_info"`
	InstallAllocateResultStr      string                     `gorm:"column:install_allocate_result" json:"-"`
	InstallAllocateResult         []InstallAllocateResult    `gorm:"-" json:"install_allocate_result"`
	RequestTimeStamp              int64                      `gorm:"column:request_time_stamp" json:"request_time_stamp"` // unix milli of request time
	Ctime                         int64                      `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	Mtime                         int64                      `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (o OrderResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateRead
}

func (o OrderResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateWrite
}

func (o OrderResultTab) TableName() string {
	return orderResultTableName
}

func (o OrderResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            o.ID,
		ModelName:     o.TableName(),
		MaskProductId: uint64(o.MaskProductID),
	}
}

func (o OrderResultTab) TableNameByPartition(day int) string {
	return fmt.Sprintf(orderResultTableName, day)
}

// SplitDayIndexByTableName
func SplitDayIndexByTableName(tableName string) (int, *srerr.Error) {
	s := strings.Split(tableName, "_")
	if len(s) == 0 {
		return 0, srerr.New(srerr.ParamInvalid, tableName, "can not split table name by '_'")
	}

	dayIndex, err := strconv.Atoi(s[len(s)-1])
	if err != nil {
		return 0, srerr.With(srerr.ParamInvalid, tableName, err)
	}

	return dayIndex, nil
}

func (o *OrderResultTab) Marshall() *srerr.Error {
	var err error
	o.FulfillmentShippingFeeInfoStr, err = jsoniter.MarshalToString(o.FulfillmentShippingFeeInfo)
	if err != nil {
		return srerr.With(srerr.JsonErr, o.FulfillmentShippingFeeInfo, err)
	}
	// 序列化install allocate结果
	o.InstallAllocateResultStr, err = jsoniter.MarshalToString(o.InstallAllocateResult)
	if err != nil {
		return srerr.With(srerr.JsonErr, o.InstallAllocateResult, err)
	}

	return nil
}

func (o *OrderResultTab) Unmarshall() *srerr.Error {
	if err := jsoniter.UnmarshalFromString(o.FulfillmentShippingFeeInfoStr, &o.FulfillmentShippingFeeInfo); err != nil {
		return srerr.With(srerr.JsonErr, o.FulfillmentShippingFeeInfoStr, err)
	}
	if err := jsoniter.UnmarshalFromString(o.GetInstallAllocateResultStr(), &o.InstallAllocateResult); err != nil {
		return srerr.With(srerr.JsonErr, o.InstallAllocateResultStr, err)
	}

	return nil
}

func (o *OrderResultTab) GetInstallAllocateResultStr() string {
	if o.InstallAllocateResultStr == "" {
		return "[]"
	}
	return o.InstallAllocateResultStr
}

func GetOrderStatus(ctx context.Context, o *OrderResultTab) (retCode int, orderStatus batch_allocate.OrderStatus, returnMsg string) {
	if err := o.Unmarshall(); err != nil {
		msg := fmt.Sprintf("marshallOrderResultMsg|unmarshall order info failed | orderID=%d, err=%v", o.OrderID, err)
		logger.CtxLogErrorf(ctx, msg)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.UnmarshallOrderInfo, monitoring.StatusError, msg)

		retCode = batch_allocate.UnmarshalShippingFeeErr
		orderStatus = batch_allocate.OrderStatusTypeAsyncFailPushed
		returnMsg = "unmarshal esf info error"
	} else if o.FulfillmentShippingFeeInfo.FulfillmentShippingFee == batch_allocate.IllegalEsf {
		errMsg := fmt.Sprintf("illegal fulfillment esf, order:%d, esf:%f", o.OrderID, o.FulfillmentShippingFeeInfo.FulfillmentShippingFee)
		logger.CtxLogErrorf(ctx, "push err to order:%+v", errMsg)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.FulfillmentEsfIllegal, monitoring.StatusError, errMsg)

		retCode = batch_allocate.IllegalEsfRetCode
		orderStatus = batch_allocate.OrderStatusTypeAsyncFailPushed
		returnMsg = batch_allocate.IllegalEsfMessage

	} else {
		logger.CtxLogInfof(ctx, "push success to order:%+v", o)

		retCode = batch_allocate.Success
		orderStatus = o.OrderStatus // 在回推kafka写入成功再赋值枚举值4
		returnMsg = ""
	}

	return
}
