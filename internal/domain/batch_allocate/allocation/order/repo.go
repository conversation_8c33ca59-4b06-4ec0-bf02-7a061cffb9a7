package order

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"math"
	"sort"
	"strings"
	"time"
)

const (
	insertBatchSize = 1000
	deleteBatchSize = 1000
	selectBatchSize = 1000
)

type BatchAllocateOrderRepoImpl struct {
}

type BatchAllocateOrderRepo interface {
	// Order Hold
	GetOrderListByIDRange(ctx context.Context, tableName string, startID, endID uint64) ([]*BatchAllocateHoldOrderTab, *srerr.Error)
	InsertOrderToHoldTab(ctx context.Context, maskProductID int, day int, orderInfo *BatchAllocateHoldOrderTab) *srerr.Error
	GetOrderByIDAndTableFromMaster(ctx context.Context, id uint64, tableName string) (BatchAllocateHoldOrderTab, *srerr.Error)
	GetLastOrderIDFromMaster(ctx context.Context, condition map[string]interface{}, tableName string) (uint64, *srerr.Error)
	ClearHoldOrderTab(ctx context.Context, maskProductID, day int) *srerr.Error
	GetHoldOrderInfoByOrderID(ctx context.Context, orderID uint64, maskProductID, day int) (*BatchAllocateHoldOrderTab, *srerr.Error)

	// Order Result
	GetOrderResultByOrderID(ctx context.Context, orderID uint64, date time.Time) (*OrderResultTab, *srerr.Error)
	BatchCreateOrderResult(ctx context.Context, orderResults []*OrderResultTab, day int) *srerr.Error
	CreateOrUpdateOrderResult(ctx context.Context, orderResult *OrderResultTab, day int) *srerr.Error
	BatchGetPendingPushOrderResult(ctx context.Context, day int, batchSize int) ([]*OrderResultTab, *srerr.Error)
	BatchUpdateOrderResultToPushed(ctx context.Context, day int, orders []uint64, errButPushedOrders []uint64) *srerr.Error
	UpdateOrderResultByPrimaryId(ctx context.Context, id uint64, orderResult *OrderResultTab, date time.Time) *srerr.Error
	BatchGetOrderResultByOrderID(ctx context.Context, orderIDList []uint64, day int) ([]*OrderResultTab, *srerr.Error)
	ClearOrderResultTab(ctx context.Context, day int) *srerr.Error
	BatchUpdateStatusByOrderID(ctx context.Context, day int, orders []uint64, status batch_allocate.OrderStatus) *srerr.Error
	CountByCondition(ctx context.Context, day int, condition map[string]interface{}) (int64, *srerr.Error)
}

func NewBatchAllocateOrderRepo() *BatchAllocateOrderRepoImpl {
	return &BatchAllocateOrderRepoImpl{}
}

func (b BatchAllocateOrderRepoImpl) GetOrderListByIDRange(ctx context.Context, tableName string, startID, endID uint64,
) ([]*BatchAllocateHoldOrderTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, tableName, err)
	}

	// 避免一次检索的数据太多导致IO卡住，使用分批检索的策略
	var orderList []*BatchAllocateHoldOrderTab

	//确定分批检索的次数, 向上取整; 特殊处理只有一单的edge case
	batchSize := int(math.Ceil(float64(endID-startID) / selectBatchSize))
	if (endID - startID) == 0 {
		batchSize += 1 // edge case，批次数要从1开始
	}

	var tempStartID uint64
	tempEndID := startID
	for i := 0; i < batchSize; i++ {
		tempOrderList := make([]*BatchAllocateHoldOrderTab, 0)
		tempStartID = tempEndID
		tempEndID = tempStartID + selectBatchSize
		if tempEndID >= endID {
			tempEndID = endID + 1
		}
		if err := db.Table(tableName).Find(&tempOrderList, "id >= ? AND id < ?", tempStartID, tempEndID).GetError(); err != nil {
			return nil, srerr.With(srerr.DatabaseErr, tableName, err)
		}
		orderList = append(orderList, tempOrderList...)
	}

	for _, o := range orderList {
		if err := o.Unmarshall(); err != nil {
			msg := fmt.Sprintf("unmarshall order info failed | orderID=%d, err=%v", o.OrderID, err)
			logger.CtxLogErrorf(ctx, msg)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.UnmarshallOrderInfo, monitoring.StatusError, msg)
		}
	}

	return orderList, nil
}

func (b BatchAllocateOrderRepoImpl) GetOrderResultByOrderID(ctx context.Context, orderID uint64, date time.Time) (*OrderResultTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, OrderResultTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, orderID, err)
	}

	var orderResult OrderResultTab
	if err := db.Table(OrderResultTabHook.TableNameByPartition(date.Day())).
		Take(&orderResult, "order_id = ?", orderID).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, orderID, err)
	}

	if err := orderResult.Unmarshall(); err != nil {
		msg := fmt.Sprintf("unmarshall order result info failed | orderID=%d, err=%v", orderResult.OrderID, err)
		logger.CtxLogErrorf(ctx, msg)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.UnmarshallOrderResult, monitoring.StatusError, msg)
	}

	return &orderResult, nil
}

func (b BatchAllocateOrderRepoImpl) BatchGetOrderResultByOrderID(ctx context.Context, orderIDList []uint64, day int) ([]*OrderResultTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, OrderResultTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var orderResultList []*OrderResultTab
	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).
		Find(&orderResultList, "order_id IN (?)", orderIDList).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	for _, o := range orderResultList {
		if err := o.Unmarshall(); err != nil {
			msg := fmt.Sprintf("unmarshall order info failed | orderID=%d, err=%v", o.OrderID, err)
			logger.CtxLogErrorf(ctx, msg)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.UnmarshallOrderInfo, monitoring.StatusError, msg)
		}
	}

	return orderResultList, nil
}

func (b BatchAllocateOrderRepoImpl) UpdateOrderResultByPrimaryId(ctx context.Context, id uint64, orderResult *OrderResultTab, date time.Time) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}

	tableName := OrderResultTabHook.TableNameByPartition(date.Day())
	if err := db.Table(tableName).Where("id = ?", id).Updates(orderResult).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, fmt.Sprintf("id=%v", id), err)
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) BatchCreateOrderResult(ctx context.Context, orderResults []*OrderResultTab, day int) *srerr.Error {
	// marshall table field
	for _, o := range orderResults {
		if err := o.Marshall(); err != nil {
			msg := fmt.Sprintf("marshall order result failed |orderID=%d, err=%v", o.OrderID, err)
			logger.CtxLogErrorf(ctx, msg)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.MarshallOrderResult, monitoring.StatusError, msg)
		}
	}

	// insert into db
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	ctx = scormv2.BindContext(ctx, db)
	if txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		if err := scormv2.Context(ctx).Table(OrderResultTabHook.TableNameByPartition(day)).
			CreateInBatches(orderResults, insertBatchSize).GetError(); err != nil {
			return err
		}
		return nil
	}); txErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, txErr)
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) CreateOrUpdateOrderResult(ctx context.Context, orderResult *OrderResultTab, day int) *srerr.Error {
	// marshall table field
	if err := orderResult.Marshall(); err != nil {
		msg := fmt.Sprintf("marshall order result failed |orderID=%d, err=%v", orderResult.OrderID, err)
		logger.CtxLogErrorf(ctx, msg)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleBatchAllocate, monitoring.MarshallOrderResult, monitoring.StatusError, msg)
	}

	// insert into db
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Save(orderResult).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) InsertOrderToHoldTab(ctx context.Context, maskProductID int, day int, orderInfo *BatchAllocateHoldOrderTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if insertErr := db.Table(orderInfo.TableNameByPartition(maskProductID, day)).Create(orderInfo); insertErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, insertErr.GetError())
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) GetOrderByIDAndTableFromMaster(ctx context.Context, id uint64, tableName string) (BatchAllocateHoldOrderTab, *srerr.Error) {
	tab := BatchAllocateHoldOrderTab{}
	db, err := dbutil.MasterDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return tab, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err := db.Table(tableName).First(&tab, "id >= ? ", id).GetError(); err != nil {
		if err != scormv2.ErrRecordNotFound {
			return tab, srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	return tab, nil
}

func (b BatchAllocateOrderRepoImpl) GetHoldOrderInfoByOrderID(ctx context.Context,
	orderID uint64, maskProductID, day int) (*BatchAllocateHoldOrderTab, *srerr.Error) {
	var (
		tab       BatchAllocateHoldOrderTab
		tableName = BatchAllocateHoldOrderTabHook.TableNameByPartition(maskProductID, day)
	)

	db, err := dbutil.SlaveDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, orderID, err)
	}

	if err := db.Table(tableName).First(&tab, "order_id = ? ", orderID).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, orderID, err)
	}

	return &tab, nil
}

func (b BatchAllocateOrderRepoImpl) BatchGetPendingPushOrderResult(ctx context.Context, day int, batchSize int) ([]*OrderResultTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, OrderResultTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var orderResults []*OrderResultTab
	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Limit(batchSize).
		Find(&orderResults, "order_status = ?", batch_allocate.OrderStatusTypeAsyncAllocated).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return orderResults, nil
}

func (b BatchAllocateOrderRepoImpl) BatchUpdateOrderResultToPushed(ctx context.Context, day int, orders []uint64, errButPushedOrders []uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Where("order_id IN (?)", orders).
		Update("order_status", batch_allocate.OrderStatusTypeAsyncPushed).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Where("order_id IN (?)", errButPushedOrders).
		Update("order_status", batch_allocate.OrderStatusTypeAsyncFailPushed).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) GetLastOrderIDFromMaster(ctx context.Context, condition map[string]interface{}, tableName string) (uint64, *srerr.Error) {
	var maxID uint64
	db, err := dbutil.MasterDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	query, args := parseCondition(condition)
	maxRow := db.Table(tableName).Select("max(id)").Where(query, args...).Row()
	if err := maxRow.Scan(&maxID); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return maxID, nil
}

func (b BatchAllocateOrderRepoImpl) ClearHoldOrderTab(ctx context.Context, maskProductID, day int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, BatchAllocateHoldOrderTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	var (
		tableName         = BatchAllocateHoldOrderTabHook.TableNameByPartition(maskProductID, day)
		rowAffected int64 = 1
	)

	for rowAffected != 0 {
		d := db.Table(tableName).Limit(deleteBatchSize).Where("1=1").Delete(BatchAllocateHoldOrderTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, tableName, d.GetError())
		}
		rowAffected = d.RowsAffected()
		time.Sleep(time.Second)
	}

	return nil
}

func (b BatchAllocateOrderRepoImpl) ClearOrderResultTab(ctx context.Context, day int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	var (
		tableName         = OrderResultTabHook.TableNameByPartition(day)
		rowAffected int64 = 1
	)

	for rowAffected != 0 {
		d := db.Table(tableName).Limit(deleteBatchSize).Where("1=1").Delete(OrderResultTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, tableName, d.GetError())
		}
		rowAffected = d.RowsAffected()
		time.Sleep(time.Second)
	}

	return nil
}

func parseCondition(condition map[string]interface{}) (string, []interface{}) {
	if len(condition) == 0 {
		return "1 = ?", []interface{}{1}
	}
	var queries []string
	var args []interface{}
	for q := range condition {
		queries = append(queries, q)
	}
	//流量录制与回放需要保证sql参数顺序一致才能mock成功,排序为了保证入参顺序一致
	sort.Strings(queries)
	for _, param := range queries {
		if value := condition[param]; value != nil {
			args = append(args, value)
		}
	}

	return strings.Join(queries, " AND "), args
}

func (b BatchAllocateOrderRepoImpl) BatchUpdateStatusByOrderID(ctx context.Context, day int, orders []uint64, status batch_allocate.OrderStatus) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Where("order_id IN (?)", orders).
		Update("order_status", status).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}
func (b BatchAllocateOrderRepoImpl) CountByCondition(ctx context.Context, day int, condition map[string]interface{}) (int64, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, OrderResultTabHook)
	if err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	query, args := parseCondition(condition)

	var total int64
	if err := db.Table(OrderResultTabHook.TableNameByPartition(day)).Where(query, args...).Count(&total).GetError(); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return total, nil
}
