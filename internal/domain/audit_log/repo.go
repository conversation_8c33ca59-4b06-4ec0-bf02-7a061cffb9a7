package audit_log

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type AuditLogRepo interface {
	CreateAuditLogTab(ctx context.Context, tab AudiLogTab) (uint64, *srerr.Error)
	BatchCreateAuditLogTab(ctx context.Context, tabs []AudiLogTab) ([]uint64, *srerr.Error)
	SelectAuditLogTabs(ctx context.Context, condition map[string]interface{}) ([]AudiLogTab, *srerr.Error)
	FindAuditLogTab(ctx context.Context, condition map[string]interface{}) (AudiLogTab, *srerr.Error)
}

type AuditLogRepoImpl struct {
}

func NewAuditLogRepoImpl() *AuditLogRepoImpl {
	return &AuditLogRepoImpl{}
}

func (a *AuditLogRepoImpl) CreateAuditLogTab(ctx context.Context, tab AudiLogTab) (uint64, *srerr.Error) {
	if err := dbutil.SaveByObj(ctx, AudiLogTabHook, nil, &tab, nil, dbutil.ModelInfo{}); err != nil {
		return 0, srerr.With(srerr.DataErr, nil, err)
	}
	return tab.Id, nil
}

func (a *AuditLogRepoImpl) SelectAuditLogTabs(ctx context.Context, condition map[string]interface{}) ([]AudiLogTab, *srerr.Error) {
	var tabs []AudiLogTab
	if err := dbutil.Select(ctx, AudiLogTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DataErr, nil, err)
	}
	return tabs, nil
}

func (a *AuditLogRepoImpl) FindAuditLogTab(ctx context.Context, condition map[string]interface{}) (AudiLogTab, *srerr.Error) {
	var tab AudiLogTab
	if err := dbutil.Take(ctx, AudiLogTabHook, condition, &tab); err != nil {
		return tab, srerr.With(srerr.DataErr, nil, err)
	}
	return tab, nil
}

func (a *AuditLogRepoImpl) BatchCreateAuditLogTab(ctx context.Context, tabs []AudiLogTab) ([]uint64, *srerr.Error) {
	if err := dbutil.InsertBatch(ctx, AudiLogTabHook, tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	var ids []uint64
	for _, tab := range tabs {
		ids = append(ids, tab.Id)
	}
	return ids, nil
}
