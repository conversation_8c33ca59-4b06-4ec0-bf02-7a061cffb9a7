package audit_log

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var (
	AudiLogTabHook = &AudiLogTab{}
)

type AudiLogTab struct {
	Id                   uint64 `gorm:"id" json:"id"`
	ModelName            string `gorm:"model_name" json:"model_name"`
	ModelId              uint64 `gorm:"model_id" json:"model_id"`
	Operator             string `gorm:"operator" json:"operator"`
	MaskProductId        uint64 `gorm:"mask_product_id" json:"mask_product_id"`
	FulfillmentProductId uint64 `gorm:"fulfillment_product_id" json:"fulfillment_product_id"`
	RuleId               uint64 `gorm:"rule_id" json:"rule_id"` //包括allocation、smr rule，具体根据下面的module_type区分
	RuleVolumeId         uint64 `gorm:"rule_volume_id" json:"rule_volume_id"`
	TaskId               uint64 `gorm:"task_id" json:"task_id"`
	ModuleType           string `gorm:"module_type" json:"module_type"` //e.g. allocation, smr soft rule
	Interface            string `gorm:"interface" json:"interface"`     //request url
	RequestBody          string `gorm:"request_body" json:"request_body"`
	//todo:SSCSMR-1386:约束成json格式
	ExtendInfo string `gorm:"extend_info" json:"extend_info"` //用来保存额外信息，什么都可以往里面丢，用string存储
	Ctime      int64  `gorm:"ctime" json:"ctime"`
	Mtime      int64  `gorm:"mtime" json:"mtime"`
}

func (a *AudiLogTab) TableName() string {
	return "audit_log_tab"
}

func (a *AudiLogTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (a *AudiLogTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (a *AudiLogTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        a.Id,
		ModelName: a.TableName(),
	}
}
