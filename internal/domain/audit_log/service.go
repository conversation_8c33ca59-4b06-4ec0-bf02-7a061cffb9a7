package audit_log

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type AuditLogService interface {
	CreateAuditLogRecord(ctx context.Context, tab AudiLogTab) (uint64, *srerr.Error)
	BatchCreateAuditLogRecord(ctx context.Context, tabs []AudiLogTab) ([]uint64, *srerr.Error)
	SelectAuditLogRecords(ctx context.Context, condition map[string]interface{}) ([]AudiLogTab, *srerr.Error)
	FindAuditLogRecord(ctx context.Context, condition map[string]interface{}) (AudiLogTab, *srerr.Error)
}

type AuditLogServiceImpl struct {
	auditLogRepo AuditLogRepo
}

func NewAuditLogServiceImpl(auditLogRepo AuditLogRepo) *AuditLogServiceImpl {
	return &AuditLogServiceImpl{auditLogRepo: auditLogRepo}
}

func (a *AuditLogServiceImpl) CreateAuditLogRecord(ctx context.Context, tab AudiLogTab) (uint64, *srerr.Error) {
	return a.auditLogRepo.CreateAuditLogTab(ctx, tab)
}

func (a *AuditLogServiceImpl) BatchCreateAuditLogRecord(ctx context.Context, tabs []AudiLogTab) ([]uint64, *srerr.Error) {
	return a.auditLogRepo.BatchCreateAuditLogTab(ctx, tabs)
}

func (a *AuditLogServiceImpl) SelectAuditLogRecords(ctx context.Context, condition map[string]interface{}) ([]AudiLogTab, *srerr.Error) {
	return a.auditLogRepo.SelectAuditLogTabs(ctx, condition)
}

func (a *AuditLogServiceImpl) FindAuditLogRecord(ctx context.Context, condition map[string]interface{}) (AudiLogTab, *srerr.Error) {
	return a.auditLogRepo.FindAuditLogTab(ctx, condition)
}
