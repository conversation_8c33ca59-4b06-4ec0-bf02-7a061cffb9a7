package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestHandleSpecialTimeSlot(t *testing.T) {
	// 创建一个固定的本地时区位置用于测试
	location := time.FixedZone("TestZone", 8*3600) // UTC+8

	// 固定的测试时间：2023-06-15 12:30:00
	testTime := time.Date(2023, 6, 15, 12, 30, 0, 0, location)

	tests := []struct {
		name        string
		cs          *ILHCapacitySettingInfo
		currentTime time.Time
		loc         *time.Location
		expected    string
	}{
		{
			name: "空时段设置_返回默认时段",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "",
						SpecialTimeSlotEndTime:   "",
					},
				},
			},
			currentTime: testTime,
			loc:         location,
			expected:    DefaultSlotID,
		},
		{
			name: "无效的时间格式_返回默认时段",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00", // 无效的格式，应为10:00:00
						SpecialTimeSlotEndTime:   "14:00:00",
					},
				},
			},
			currentTime: testTime,
			loc:         location,
			expected:    DefaultSlotID,
		},
		{
			name: "在时段范围内_返回特定时段ID",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
				},
			},
			currentTime: testTime, // 12:30 在 10:00-14:00 范围内
			loc:         location,
			expected:    "1000-1400", // 预期通过GenerateSlotID生成
		},
		{
			name: "不在时段范围内_返回默认时段",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "14:00:00",
						SpecialTimeSlotEndTime:   "18:00:00",
					},
				},
			},
			currentTime: testTime, // 12:30 不在 14:00-18:00 范围内
			loc:         location,
			expected:    DefaultSlotID,
		},
		{
			name: "在时段边界_开始时间_返回特定时段ID",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "12:30:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
				},
			},
			currentTime: testTime, // 12:30 等于开始时间
			loc:         location,
			expected:    "1230-1400",
		},
		{
			name: "在时段边界_结束时间_返回特定时段ID",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "12:30:00",
					},
				},
			},
			currentTime: testTime, // 12:30 等于结束时间
			loc:         location,
			expected:    "1000-1230",
		},
		{
			name: "仅有开始时间_返回默认时段",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "",
					},
				},
			},
			currentTime: testTime,
			loc:         location,
			expected:    DefaultSlotID,
		},
		{
			name: "仅有结束时间_返回默认时段",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
				},
			},
			currentTime: testTime,
			loc:         location,
			expected:    DefaultSlotID,
		},
		// 注意：当前实现不支持跨零点时间段，因为它使用当天的日期创建时间范围，始终假设endTime > startTime
		{
			name: "时段开始时间大于结束时间（跨零点）_不在时段内",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "22:00:00",
						SpecialTimeSlotEndTime:   "02:00:00",
					},
				},
			},
			currentTime: time.Date(2023, 6, 15, 23, 30, 0, 0, location), // 23:30
			loc:         location,
			expected:    DefaultSlotID, // 当前实现中，会认为23:30不在时段内，因为02:00:00 < 22:00:00
		},
		{
			name: "开始和结束时间相同_返回特定时段ID",
			cs: &ILHCapacitySettingInfo{
				CapacitySetting: CapacitySetting{
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "12:30:00",
						SpecialTimeSlotEndTime:   "12:30:00",
					},
				},
			},
			currentTime: testTime, // 12:30 等于开始时间也等于结束时间
			loc:         location,
			expected:    "1230-1230",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.cs.handleSpecialTimeSlot(tt.currentTime, tt.loc)
			assert.Equal(t, tt.expected, result, "时段ID不匹配")
		})
	}
}

// 测试GenerateSlotID函数
func TestGenerateSlotID(t *testing.T) {
	tests := []struct {
		name      string
		startTime string
		endTime   string
		expected  string
	}{
		{
			name:      "标准时间格式",
			startTime: "10:00:00",
			endTime:   "14:00:00",
			expected:  "1000-1400",
		},
		{
			name:      "不完整的时间格式",
			startTime: "9:30:00",
			endTime:   "14:00:00",
			expected:  "930-1400",
		},
		{
			name:      "无效的时间格式",
			startTime: "invalid",
			endTime:   "14:00:00",
			expected:  "invalid-14:00:00", // 降级处理为原始时间
		},
		{
			name:      "空时间",
			startTime: "",
			endTime:   "",
			expected:  "-", // 降级处理
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateSlotID(tt.startTime, tt.endTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// 测试isTimeInRange函数
func TestIsTimeInRange(t *testing.T) {
	loc := time.UTC

	tests := []struct {
		name      string
		checkTime time.Time
		startTime time.Time
		endTime   time.Time
		expected  bool
	}{
		{
			name:      "时间在范围内",
			checkTime: time.Date(2023, 6, 15, 12, 30, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			expected:  true,
		},
		{
			name:      "时间等于开始时间",
			checkTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			expected:  true,
		},
		{
			name:      "时间等于结束时间",
			checkTime: time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			expected:  true,
		},
		{
			name:      "时间早于开始时间",
			checkTime: time.Date(2023, 6, 15, 9, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			expected:  false,
		},
		{
			name:      "时间晚于结束时间",
			checkTime: time.Date(2023, 6, 15, 15, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 10, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 14, 0, 0, 0, loc),
			expected:  false,
		},
		{
			name:      "开始结束时间相同且匹配",
			checkTime: time.Date(2023, 6, 15, 12, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 12, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 12, 0, 0, 0, loc),
			expected:  true,
		},
		{
			name:      "开始结束时间相同但不匹配",
			checkTime: time.Date(2023, 6, 15, 13, 0, 0, 0, loc),
			startTime: time.Date(2023, 6, 15, 12, 0, 0, 0, loc),
			endTime:   time.Date(2023, 6, 15, 12, 0, 0, 0, loc),
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTimeInRange(tt.checkTime, tt.startTime, tt.endTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}
