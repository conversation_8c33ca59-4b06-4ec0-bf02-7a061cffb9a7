package entity

type TimeIntervalType int

const (
	// TimeIntervalTypeDefault 默认类型
	TimeIntervalTypeDefault TimeIntervalType = 0

	// TimeIntervalTypeWeeklyCapacity 周容量类型
	TimeIntervalTypeWeeklyCapacity TimeIntervalType = 1

	// TimeIntervalTypeSpecialDate 特殊日期类型
	TimeIntervalTypeSpecialDate TimeIntervalType = 2

	// TimeIntervalTypeSpecialTimeSlot 特殊时间段类型
	TimeIntervalTypeSpecialTimeSlot TimeIntervalType = 3

	// TimeIntervalTypeSpecialDateAndTimeSlot 特殊日期和时间段类型
	TimeIntervalTypeSpecialDateAndTimeSlot TimeIntervalType = 4
)

// String 返回TimeIntervalType的字符串表示
func (t TimeIntervalType) String() string {
	switch t {
	case TimeIntervalTypeDefault:
		return "Default"
	case TimeIntervalTypeWeeklyCapacity:
		return "Weekly Capacity"
	case TimeIntervalTypeSpecialDate:
		return "Special Date"
	case TimeIntervalTypeSpecialTimeSlot:
		return "Special Time Slot"
	case TimeIntervalTypeSpecialDateAndTimeSlot:
		return "Special Date + Time Slot"
	default:
		return "InvalidTimeIntervalType"
	}
}

// ReserveStatus 表示是否启用保留容量
type ReserveStatus int

const (
	ReserveDisabled ReserveStatus = 0
	ReserveEnabled  ReserveStatus = 1
)
