package entity

import (
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

func TestValidateCapacitySettings(t *testing.T) {
	// 测试用例
	tests := []struct {
		name           string
		settings       []CapacitySetting
		expectError    bool
		errorSubstring string
	}{
		{
			name: "Valid settings with default type only",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
			},
			expectError: false,
		},
		{
			name: "Valid settings with multiple types but no Special Time Slot",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeWeeklyCapacity,
					TimeObject: TimeObject{
						WeekDays: []timeutil.WeekDay{timeutil.Monday, timeutil.Tuesday},
					},
					BSAWeight:   130.0,
					AdhocWeight: 65.0,
				},
			},
			expectError: false,
		},
		{
			name: "Valid settings with Special Time Slot only",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
			},
			expectError: false,
		},
		{
			name: "Invalid settings with Special Time Slot and Special Date",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
			},
			expectError:    true,
			errorSubstring: "Cannot configure Special Time Slot together with Special Date or Weekly Capacity",
		},
		{
			name: "Missing default type",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
			},
			expectError:    true,
			errorSubstring: "Missing default time interval type",
		},
		{
			name: "Duplicate special dates",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"}, // 重复的日期
					},
					BSAWeight:   140.0,
					AdhocWeight: 65.0,
				},
			},
			expectError:    true,
			errorSubstring: "Duplicate special date",
		},
		{
			name: "Overlapping time slots",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "12:00:00", // 重叠的时间段
						SpecialTimeSlotEndTime:   "16:00:00",
					},
					BSAWeight:   160.0,
					AdhocWeight: 75.0,
				},
			},
			expectError:    true,
			errorSubstring: "Overlapping time slots",
		},
		{
			name: "Non-overlapping time slots",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "14:00:01", // 不重叠的时间段
						SpecialTimeSlotEndTime:   "18:00:00",
					},
					BSAWeight:   160.0,
					AdhocWeight: 75.0,
				},
			},
			expectError: false,
		},
		{
			name: "Overlapping date time slots",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDateAndTimeSlot,
					TimeObject: TimeObject{
						SpecialDateAndTimeSlotStartTime: "2025-01-01 10:00:00",
						SpecialDateAndTimeSlotEndTime:   "2025-01-01 14:00:00",
					},
					BSAWeight:   170.0,
					AdhocWeight: 80.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDateAndTimeSlot,
					TimeObject: TimeObject{
						SpecialDateAndTimeSlotStartTime: "2025-01-01 12:00:00", // 重叠的日期时间段
						SpecialDateAndTimeSlotEndTime:   "2025-01-01 16:00:00",
					},
					BSAWeight:   180.0,
					AdhocWeight: 85.0,
				},
			},
			expectError:    true,
			errorSubstring: "Overlapping date time slots",
		},
		{
			name: "Valid multiple settings of the same type without overlap - Special Date",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-01"},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDate,
					TimeObject: TimeObject{
						SpecialDates: []string{"2025-01-02"}, // 不同的日期
					},
					BSAWeight:   130.0,
					AdhocWeight: 65.0,
				},
			},
			expectError: false,
		},
		{
			name: "Valid multiple settings of the same type without overlap - Special Time Slot",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10:00:00",
						SpecialTimeSlotEndTime:   "12:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "12:00:01", // 不重叠的时间段
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   160.0,
					AdhocWeight: 75.0,
				},
			},
			expectError: false,
		},
		// 新增测试用例：空设置列表
		{
			name:           "Empty settings list",
			settings:       []CapacitySetting{},
			expectError:    true,
			errorSubstring: "Missing default time interval type",
		},
		// 新增测试用例：无效的时间格式（时间段）
		{
			name: "Invalid time slot format",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialTimeSlot,
					TimeObject: TimeObject{
						SpecialTimeSlotStartTime: "10-00-00", // 无效的时间格式
						SpecialTimeSlotEndTime:   "14:00:00",
					},
					BSAWeight:   150.0,
					AdhocWeight: 70.0,
				},
			},
			expectError: false, // 不会出错，因为timeStringToMinutes会返回默认值0
		},
		// 新增测试用例：无效的日期时间格式
		{
			name: "Invalid date time format",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDateAndTimeSlot,
					TimeObject: TimeObject{
						SpecialDateAndTimeSlotStartTime: "2025/01/01 10:00:00", // 无效的日期时间格式
						SpecialDateAndTimeSlotEndTime:   "2025-01-01 14:00:00",
					},
					BSAWeight:   170.0,
					AdhocWeight: 80.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeSpecialDateAndTimeSlot,
					TimeObject: TimeObject{
						SpecialDateAndTimeSlotStartTime: "2025-01-02 10:00:00",
						SpecialDateAndTimeSlotEndTime:   "2025-01-02 14:00:00",
					},
					BSAWeight:   180.0,
					AdhocWeight: 85.0,
				},
			},
			expectError:    true, // 会出错，因为dateTimeOverlap会保守地认为可能重叠
			errorSubstring: "Overlapping date time slots",
		},
		// 新增测试用例：负权重值
		{
			name: "Negative weight values",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        -100.0, // 负权重值
					AdhocWeight:      50.0,
				},
			},
			expectError: false, // 验证函数没有检查权重值是否为正
		},
		// 新增测试用例：周容量设置
		{
			name: "Weekly capacity settings",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
				},
				{
					TimeIntervalType: TimeIntervalTypeWeeklyCapacity,
					TimeObject: TimeObject{
						WeekDays: []timeutil.WeekDay{timeutil.Monday, timeutil.Tuesday, timeutil.Wednesday},
					},
					BSAWeight:   120.0,
					AdhocWeight: 60.0,
				},
			},
			expectError: false,
		},
		// 新增测试用例：重复的ProductID
		{
			name: "Duplicate ProductID",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
					ProductSettings: []ProductSetting{
						{
							ProductID:  1001,
							Proportion: 30.0,
						},
						{
							ProductID:  1001, // 重复的ProductID
							Proportion: 40.0,
						},
					},
				},
			},
			expectError:    true,
			errorSubstring: "Duplicate ProductID",
		},
		// 新增测试用例：Proportion总和超过100
		{
			name: "Total proportion exceeds 100",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
					ProductSettings: []ProductSetting{
						{
							ProductID:  1001,
							Proportion: 60.0,
						},
						{
							ProductID:  1002,
							Proportion: 50.0, // 总和超过100
						},
					},
				},
			},
			expectError:    true,
			errorSubstring: "Total proportion exceeds 100",
		},
		// 新增测试用例：包含有效的ReserveStatus
		{
			name: "Valid ReserveStatus",
			settings: []CapacitySetting{
				{
					TimeIntervalType: TimeIntervalTypeDefault,
					TimeObject:       NewDefaultTimeObject(),
					BSAWeight:        100.0,
					AdhocWeight:      50.0,
					ReserveStatus:    ReserveEnabled,
					ProductSettings: []ProductSetting{
						{
							ProductID:  1001,
							Proportion: 60.0,
						},
					},
				},
			},
			expectError: false,
		},
	}

	// 运行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCapacitySettings(tt.settings)

			// 检查是否符合预期的错误状态
			if tt.expectError && err == nil {
				t.Errorf("expected error but got nil")
				return
			}

			if !tt.expectError && err != nil {
				t.Errorf("expected no error but got: %v", err)
				return
			}

			// 如果期望有错误，检查错误信息是否包含预期子串
			if tt.expectError && err != nil {
				errMsg := err.Error()
				if tt.errorSubstring != "" && !contains(errMsg, tt.errorSubstring) {
					t.Errorf("error message '%s' does not contain expected substring '%s'", errMsg, tt.errorSubstring)
				}
			}
		})
	}
}

// 辅助函数：检查字符串是否包含子串
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

func TestTimeSlotOverlap(t *testing.T) {
	tests := []struct {
		name     string
		startA   string
		endA     string
		startB   string
		endB     string
		expected bool
	}{
		{
			name:     "Overlapping time slots",
			startA:   "10:00:00",
			endA:     "14:00:00",
			startB:   "12:00:00",
			endB:     "16:00:00",
			expected: true,
		},
		{
			name:     "Non-overlapping time slots",
			startA:   "10:00:00",
			endA:     "12:00:00",
			startB:   "14:00:00",
			endB:     "16:00:00",
			expected: false,
		},
		{
			name:     "Adjacent time slots",
			startA:   "10:00:00",
			endA:     "12:00:00",
			startB:   "12:00:00",
			endB:     "14:00:00",
			expected: false, // 严格的不重叠定义，边界相等不算重叠
		},
		{
			name:     "One slot contains another",
			startA:   "10:00:00",
			endA:     "16:00:00",
			startB:   "12:00:00",
			endB:     "14:00:00",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := timeSlotOverlap(tt.startA, tt.endA, tt.startB, tt.endB)
			if result != tt.expected {
				t.Errorf("timeSlotOverlap(%s, %s, %s, %s) = %v, expected %v",
					tt.startA, tt.endA, tt.startB, tt.endB, result, tt.expected)
			}
		})
	}
}

func TestDateTimeOverlap(t *testing.T) {
	tests := []struct {
		name     string
		startA   string
		endA     string
		startB   string
		endB     string
		expected bool
	}{
		{
			name:     "Overlapping date time",
			startA:   "2025-01-01 10:00:00",
			endA:     "2025-01-01 14:00:00",
			startB:   "2025-01-01 12:00:00",
			endB:     "2025-01-01 16:00:00",
			expected: true,
		},
		{
			name:     "Non-overlapping date time (different dates)",
			startA:   "2025-01-01 10:00:00",
			endA:     "2025-01-01 14:00:00",
			startB:   "2025-01-02 10:00:00",
			endB:     "2025-01-02 14:00:00",
			expected: false,
		},
		{
			name:     "Non-overlapping date time (same date, different times)",
			startA:   "2025-01-01 10:00:00",
			endA:     "2025-01-01 12:00:00",
			startB:   "2025-01-01 14:00:00",
			endB:     "2025-01-01 16:00:00",
			expected: false,
		},
		{
			name:     "Adjacent date time",
			startA:   "2025-01-01 10:00:00",
			endA:     "2025-01-01 12:00:00",
			startB:   "2025-01-01 12:00:00",
			endB:     "2025-01-01 14:00:00",
			expected: false, // 严格的不重叠定义，边界相等不算重叠
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := dateTimeOverlap(tt.startA, tt.endA, tt.startB, tt.endB)
			if result != tt.expected {
				t.Errorf("dateTimeOverlap(%s, %s, %s, %s) = %v, expected %v",
					tt.startA, tt.endA, tt.startB, tt.endB, result, tt.expected)
			}
		})
	}
}

func TestTimeStringToMinutes(t *testing.T) {
	tests := []struct {
		name     string
		timeStr  string
		expected int
	}{
		{
			name:     "Regular time",
			timeStr:  "10:30:00",
			expected: 630, // 10 hours * 60 + 30 minutes
		},
		{
			name:     "Midnight",
			timeStr:  "00:00:00",
			expected: 0,
		},
		{
			name:     "Invalid format",
			timeStr:  "10-30-00",
			expected: 0, // 返回默认值
		},
		{
			name:     "Empty string",
			timeStr:  "",
			expected: 0,
		},
		{
			name:     "24 hour time",
			timeStr:  "23:59:59",
			expected: 1439, // 23 hours * 60 + 59 minutes
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := timeStringToMinutes(tt.timeStr)
			if result != tt.expected {
				t.Errorf("timeStringToMinutes(%s) = %v, expected %v",
					tt.timeStr, result, tt.expected)
			}
		})
	}
}
