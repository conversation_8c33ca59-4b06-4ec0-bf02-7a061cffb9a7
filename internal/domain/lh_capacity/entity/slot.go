package entity

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	// DefaultSlotID 标识默认时段
	DefaultSlotID = "default"
)

// GenerateSlotID 从时间段生成slotID
func GenerateSlotID(startTime, endTime string) string {
	// 截取时间部分 (HH:MM) 以简化ID
	startParts := strings.Split(startTime, ":")
	endParts := strings.Split(endTime, ":")

	if len(startParts) >= 2 && len(endParts) >= 2 {
		return fmt.Sprintf("%s%s-%s%s", startParts[0], startParts[1], endParts[0], endParts[1])
	}

	// 降级处理，使用原始时间
	return fmt.Sprintf("%s-%s", startTime, endTime)
}

// isValidTimeIntervalTypeForSlot 检查时间间隔类型是否适用于时间段处理
func isValidTimeIntervalTypeForSlot(intervalType TimeIntervalType) bool {
	return intervalType == TimeIntervalTypeSpecialTimeSlot || intervalType == TimeIntervalTypeSpecialDateAndTimeSlot
}

// getTWSTimezone 根据TWS代码获取时区位置
func getTWSTimezone(ctx context.Context, twsCode string) (*time.Location, bool) {
	twsConfig, exist := configutil.GetTwsCutoffTimeWithTimezone(ctx)[twsCode]
	if !exist {
		return nil, false
	}
	return time.FixedZone("", twsConfig.TimezoneOffset*timeutil.HourSecs), true
}

// handleSpecialTimeSlot 处理特殊时间段类型
func (cs *ILHCapacitySettingInfo) handleSpecialTimeSlot(currentTime time.Time, loc *time.Location) string {
	startTime := cs.CapacitySetting.TimeObject.SpecialTimeSlotStartTime
	endTime := cs.CapacitySetting.TimeObject.SpecialTimeSlotEndTime

	// 检查时间段是否有效
	if startTime == "" || endTime == "" {
		return DefaultSlotID
	}

	// 解析开始和结束时间
	startTimeParsed, errStart := timeutil.ParseTime(startTime)
	endTimeParsed, errEnd := timeutil.ParseTime(endTime)

	if errStart != nil || errEnd != nil {
		return DefaultSlotID
	}

	// 创建当天的时间范围
	startTimeToday := time.Date(
		currentTime.Year(), currentTime.Month(), currentTime.Day(),
		startTimeParsed.Hour(), startTimeParsed.Minute(), startTimeParsed.Second(),
		0, loc)

	endTimeToday := time.Date(
		currentTime.Year(), currentTime.Month(), currentTime.Day(),
		endTimeParsed.Hour(), endTimeParsed.Minute(), endTimeParsed.Second(),
		0, loc)

	// 判断当前时间是否在时间段内
	inTimeSlot := isTimeInRange(currentTime, startTimeToday, endTimeToday)

	if inTimeSlot {
		return GenerateSlotID(startTime, endTime)
	}

	return DefaultSlotID
}

// handleSpecialDateAndTimeSlot 处理特殊日期+时间段类型
func (cs *ILHCapacitySettingInfo) handleSpecialDateAndTimeSlot(currentTime time.Time, loc *time.Location) string {
	startDateTime := cs.CapacitySetting.TimeObject.SpecialDateAndTimeSlotStartTime
	endDateTime := cs.CapacitySetting.TimeObject.SpecialDateAndTimeSlotEndTime

	// 检查日期时间段是否有效
	if startDateTime == "" || endDateTime == "" {
		return DefaultSlotID
	}

	// 解析开始和结束时间
	startTime, errStart := timeutil.ParseDateTimeInLoc(startDateTime, loc)
	endTime, errEnd := timeutil.ParseDateTimeInLoc(endDateTime, loc)
	if errStart != nil || errEnd != nil {
		return DefaultSlotID
	}

	// 判断当前时间是否在日期+时间段内
	inTimeSlot := isTimeInRange(currentTime, startTime, endTime)

	if inTimeSlot {
		// 提取时间部分生成slotID
		startTimePart := startDateTime[11:19] // 提取HH:MM:SS部分
		endTimePart := endDateTime[11:19]     // 提取HH:MM:SS部分
		return GenerateSlotID(startTimePart, endTimePart)
	}

	return DefaultSlotID
}

// isTimeInRange 判断时间是否在指定范围内
func isTimeInRange(checkTime, startTime, endTime time.Time) bool {
	return (checkTime.Equal(startTime) || checkTime.After(startTime)) &&
		(checkTime.Equal(endTime) || checkTime.Before(endTime))
}

// GetRelevantSlotID 根据时间戳确定应该使用哪个slotID
func (cs *ILHCapacitySettingInfo) GetRelevantSlotID(ctx context.Context, timestamp int64) string {
	// 检查时间间隔类型是否适用于时间段处理
	if !isValidTimeIntervalTypeForSlot(cs.CapacitySetting.TimeIntervalType) {
		return DefaultSlotID
	}

	// 获取TWS代码
	var twsCode string
	if len(cs.TWS) > 0 {
		twsCode = cs.TWS[0]
	} else {
		return DefaultSlotID
	}

	// 获取时区
	loc, exist := getTWSTimezone(ctx, twsCode)
	if !exist {
		return DefaultSlotID
	}

	// 创建基于时区的当前时间
	currentTime := time.Unix(timestamp, 0).In(loc)

	// 根据时间间隔类型处理
	var slotID string
	switch cs.CapacitySetting.TimeIntervalType {
	case TimeIntervalTypeSpecialTimeSlot:
		slotID = cs.handleSpecialTimeSlot(currentTime, loc)
	case TimeIntervalTypeSpecialDateAndTimeSlot:
		slotID = cs.handleSpecialDateAndTimeSlot(currentTime, loc)
	default:
		slotID = DefaultSlotID
	}

	// 注意：如果slotID为DefaultSlotID且CoversFullDay()为true，
	// 可能表示配置有误或时间戳异常（时间段应该覆盖全天但未匹配到）
	// 上层代码可以根据CoversFullDay()和slotID判断是否是异常情况

	return slotID
}
