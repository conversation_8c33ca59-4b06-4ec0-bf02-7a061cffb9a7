package entity

import (
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

// ValidateCapacitySettings 校验容量设置是否符合要求:
// 1. 默认时间间隔规则：必须包含且仅有一个默认时间间隔类型
// 2. 互斥关系规则：Special Date/Weekly 与 Special Time Slot 互斥
// 3. 类型特定规则：如Weekly不能选择全部7天
// 4. 重叠检查规则：对于相同类型的时间设置，不能有重叠
// 5. 产品配置规则：ProductID不能重复，Proportion总和不能超过100
func ValidateCapacitySettings(settings []CapacitySetting) *srerr.Error {
	// 第一部分：基本验证和类型计数
	typeCount := collectTimeIntervalTypes(settings)

	// 第二部分：默认时间间隔规则验证
	if err := validateDefaultTimeInterval(typeCount); err != nil {
		return err
	}

	// 第三部分：互斥关系规则验证
	if err := validateMutualExclusion(typeCount); err != nil {
		return err
	}

	// 第四部分：类型特定规则验证
	if err := validateTypeSpecificRules(settings); err != nil {
		return err
	}

	// 第五部分：时间重叠检查
	if err := checkTimeOverlap(settings); err != nil {
		return err
	}

	// 第六部分：相同interval间的对象重复检查
	if err := checkDuplicateObjects(settings); err != nil {
		return err
	}

	// 第七部分：产品配置规则验证
	if err := validateProductSettings(settings); err != nil {
		return err
	}

	return nil
}

// collectTimeIntervalTypes 统计各种时间间隔类型的数量
func collectTimeIntervalTypes(settings []CapacitySetting) map[TimeIntervalType]int {
	typeCount := make(map[TimeIntervalType]int)

	for _, setting := range settings {
		typeCount[setting.TimeIntervalType]++
	}

	return typeCount
}

// validateDefaultTimeInterval 验证默认时间间隔规则
func validateDefaultTimeInterval(typeCount map[TimeIntervalType]int) *srerr.Error {
	defaultCount, hasDefault := typeCount[TimeIntervalTypeDefault]

	if !hasDefault || defaultCount == 0 {
		return srerr.New(srerr.ParamErr, nil, "Missing default time interval type capacity setting")
	}

	if defaultCount > 1 {
		return srerr.New(srerr.ParamErr, nil, "Only one default time interval type capacity setting is allowed")
	}

	return nil
}

// validateMutualExclusion 验证互斥关系规则
func validateMutualExclusion(typeCount map[TimeIntervalType]int) *srerr.Error {
	hasSpecialDate := typeCount[TimeIntervalTypeSpecialDate] > 0
	hasWeeklyCapacity := typeCount[TimeIntervalTypeWeeklyCapacity] > 0
	hasSpecialTimeSlot := typeCount[TimeIntervalTypeSpecialTimeSlot] > 0

	if (hasSpecialDate || hasWeeklyCapacity) && hasSpecialTimeSlot {
		return srerr.New(srerr.ParamErr, nil, "Cannot configure Special Time Slot together with Special Date or Weekly Capacity")
	}

	return nil
}

// validateTypeSpecificRules 验证类型特定规则
func validateTypeSpecificRules(settings []CapacitySetting) *srerr.Error {
	for _, setting := range settings {
		// 检查Weekly Capacity不能选择全部7天
		if setting.TimeIntervalType == TimeIntervalTypeWeeklyCapacity {
			if len(setting.TimeObject.WeekDays) == 7 {
				return srerr.New(srerr.ParamErr, nil, "Weekly Capacity cannot select all days of the week, as it would duplicate default configuration")
			}
		}
	}

	return nil
}

// validateProductSettings 验证产品配置规则
func validateProductSettings(settings []CapacitySetting) *srerr.Error {
	// 检查ProductID是否有重复
	if err := checkDuplicateProductID(settings); err != nil {
		return err
	}

	// 检查Proportion总和
	if err := checkProportionSum(settings); err != nil {
		return err
	}

	return nil
}

// 检查相同interval之间配置的object不能有重复
func checkDuplicateObjects(settings []CapacitySetting) *srerr.Error {
	// 按时间间隔类型分组
	typeToSettings := groupSettingsByType(settings)

	// 检查Weekly Capacity重复
	if weeklySettings, exists := typeToSettings[TimeIntervalTypeWeeklyCapacity]; exists && len(weeklySettings) > 1 {
		weekdayMap := make(map[timeutil.WeekDay]bool)
		for _, setting := range weeklySettings {
			for _, day := range setting.TimeObject.WeekDays {
				if _, exists := weekdayMap[day]; exists {
					return srerr.New(srerr.ParamErr, nil, fmt.Sprintf("Duplicate weekday: %s in Weekly Capacity settings", day))
				}
				weekdayMap[day] = true
			}
		}
	}

	// 特殊日期的重复已在checkTimeOverlap中处理
	// 特殊时间段的重复已在checkTimeOverlap中处理
	// 特殊日期和时间段的重复已在checkTimeOverlap中处理

	return nil
}

// 检查时间窗口是否重叠
func checkTimeOverlap(settings []CapacitySetting) *srerr.Error {
	// 按时间间隔类型分组
	typeToSettings := groupSettingsByType(settings)

	// 检查特殊日期是否有重叠
	if err := checkSpecialDateOverlap(typeToSettings); err != nil {
		return err
	}

	// 检查特殊时间段是否有重叠
	if err := checkSpecialTimeSlotOverlap(typeToSettings); err != nil {
		return err
	}

	// 检查特殊日期和时间段是否有重叠
	if err := checkSpecialDateTimeOverlap(typeToSettings); err != nil {
		return err
	}

	return nil
}

// groupSettingsByType 按时间间隔类型对设置进行分组
func groupSettingsByType(settings []CapacitySetting) map[TimeIntervalType][]CapacitySetting {
	typeToSettings := make(map[TimeIntervalType][]CapacitySetting)
	for _, setting := range settings {
		typeToSettings[setting.TimeIntervalType] = append(typeToSettings[setting.TimeIntervalType], setting)
	}
	return typeToSettings
}

// checkSpecialDateOverlap 检查特殊日期是否重叠
func checkSpecialDateOverlap(typeToSettings map[TimeIntervalType][]CapacitySetting) *srerr.Error {
	if specialDateSettings, exists := typeToSettings[TimeIntervalTypeSpecialDate]; exists && len(specialDateSettings) > 1 {
		dateMap := make(map[string]bool)
		for _, setting := range specialDateSettings {
			for _, dateStr := range setting.TimeObject.SpecialDates {
				if _, exists := dateMap[dateStr]; exists {
					return srerr.New(srerr.ParamErr, nil, fmt.Sprintf("Duplicate special date: %s", dateStr))
				}
				dateMap[dateStr] = true
			}
		}
	}
	return nil
}

// checkSpecialTimeSlotOverlap 检查特殊时间段是否重叠
func checkSpecialTimeSlotOverlap(typeToSettings map[TimeIntervalType][]CapacitySetting) *srerr.Error {
	if specialTimeSlotSettings, exists := typeToSettings[TimeIntervalTypeSpecialTimeSlot]; exists && len(specialTimeSlotSettings) > 1 {
		for i, settingA := range specialTimeSlotSettings {
			for j := i + 1; j < len(specialTimeSlotSettings); j++ {
				settingB := specialTimeSlotSettings[j]
				if timeSlotOverlap(
					settingA.TimeObject.SpecialTimeSlotStartTime,
					settingA.TimeObject.SpecialTimeSlotEndTime,
					settingB.TimeObject.SpecialTimeSlotStartTime,
					settingB.TimeObject.SpecialTimeSlotEndTime) {
					return srerr.New(srerr.ParamErr, nil, fmt.Sprintf(
						"Overlapping time slots: [%s-%s] and [%s-%s]",
						settingA.TimeObject.SpecialTimeSlotStartTime,
						settingA.TimeObject.SpecialTimeSlotEndTime,
						settingB.TimeObject.SpecialTimeSlotStartTime,
						settingB.TimeObject.SpecialTimeSlotEndTime))
				}
			}
		}
	}
	return nil
}

// checkSpecialDateTimeOverlap 检查特殊日期和时间段是否重叠
func checkSpecialDateTimeOverlap(typeToSettings map[TimeIntervalType][]CapacitySetting) *srerr.Error {
	if specialDateTimeSettings, exists := typeToSettings[TimeIntervalTypeSpecialDateAndTimeSlot]; exists && len(specialDateTimeSettings) > 1 {
		for i, settingA := range specialDateTimeSettings {
			for j := i + 1; j < len(specialDateTimeSettings); j++ {
				settingB := specialDateTimeSettings[j]
				if dateTimeOverlap(
					settingA.TimeObject.SpecialDateAndTimeSlotStartTime,
					settingA.TimeObject.SpecialDateAndTimeSlotEndTime,
					settingB.TimeObject.SpecialDateAndTimeSlotStartTime,
					settingB.TimeObject.SpecialDateAndTimeSlotEndTime) {
					return srerr.New(srerr.ParamErr, nil, fmt.Sprintf(
						"Overlapping date time slots: [%s-%s] and [%s-%s]",
						settingA.TimeObject.SpecialDateAndTimeSlotStartTime,
						settingA.TimeObject.SpecialDateAndTimeSlotEndTime,
						settingB.TimeObject.SpecialDateAndTimeSlotStartTime,
						settingB.TimeObject.SpecialDateAndTimeSlotEndTime))
				}
			}
		}
	}
	return nil
}

// 检查时间段是否重叠（格式如 "10:00:00"）
func timeSlotOverlap(startA, endA, startB, endB string) bool {
	// 将时间字符串转换为分钟数进行比较
	startAMinutes := timeStringToMinutes(startA)
	endAMinutes := timeStringToMinutes(endA)
	startBMinutes := timeStringToMinutes(startB)
	endBMinutes := timeStringToMinutes(endB)

	// 检查时间段是否重叠
	return startAMinutes < endBMinutes && endAMinutes > startBMinutes
}

// 检查日期时间段是否重叠（格式如 "2025-01-01 00:00:00"）
func dateTimeOverlap(startA, endA, startB, endB string) bool {
	// 解析时间字符串为time.Time
	timeA1, err1 := timeutil.ParseDateTime(startA)
	timeA2, err2 := timeutil.ParseDateTime(endA)
	timeB1, err3 := timeutil.ParseDateTime(startB)
	timeB2, err4 := timeutil.ParseDateTime(endB)

	// 如果解析失败，保守返回可能重叠
	if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
		return true
	}

	// 检查时间段是否重叠
	return timeA1.Before(timeB2) && timeA2.After(timeB1)
}

// 将时间字符串（"10:00:00"）转换为分钟数
func timeStringToMinutes(timeStr string) int {
	parts := strings.Split(timeStr, ":")
	if len(parts) < 2 {
		return 0
	}

	hours := 0
	minutes := 0

	if h, err := time.Parse("15", parts[0]); err == nil {
		hours = h.Hour()
	}

	if m, err := time.Parse("04", parts[1]); err == nil {
		minutes = m.Minute()
	}

	return hours*60 + minutes
}

// 检查每个CapacitySetting中ProductSettings的ProductID是否有重复
func checkDuplicateProductID(settings []CapacitySetting) *srerr.Error {
	for _, setting := range settings {
		productIDMap := make(map[int]bool)
		for _, productSetting := range setting.ProductSettings {
			if _, exists := productIDMap[productSetting.ProductID]; exists {
				return srerr.New(srerr.ParamErr, nil, fmt.Sprintf("Duplicate ProductID: %d in capacity settings", productSetting.ProductID))
			}
			productIDMap[productSetting.ProductID] = true
		}
	}
	return nil
}

// 检查每个CapacitySetting中ProductSettings的Proportion总和是否超过100
func checkProportionSum(settings []CapacitySetting) *srerr.Error {
	for _, setting := range settings {
		var totalProportion float64
		for _, productSetting := range setting.ProductSettings {
			totalProportion += productSetting.Proportion
		}
		if totalProportion > 100 {
			return srerr.New(srerr.ParamErr, nil, fmt.Sprintf("Total proportion exceeds 100: %.2f", totalProportion))
		}
	}
	return nil
}
