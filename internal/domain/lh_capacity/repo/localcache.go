package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

func DumpLHCapacityConfig() (map[string]interface{}, error) {
	ctx := context.Background()

	db, err := dbutil.SlaveDB(ctx, LHCapacityTabHook)
	if err != nil {
		return nil, err
	}

	// 获取所有Active状态的配置
	var lhCapacityTabs []*LHCapacityTab
	if err := db.Table(LHCapacityTabHook.TableName()).
		Where("rule_status = ?", rule.RuleStatusActive).
		Find(&lhCapacityTabs).GetError(); err != nil {
		return nil, err
	}

	lhToCapacityMap := make(map[string][]*entity.LHCapacityConfig)
	for _, lhCapacityTab := range lhCapacityTabs {
		lhToCapacityMap[lhCapacityTab.ILHLineID] = append(lhToCapacityMap[lhCapacityTab.ILHLineID], lhCapacityTab.ConvertToEntity())
	}
	result := make(map[string]interface{}, len(lhCapacityTabs))
	for ilhLineID, capacityConfigs := range lhToCapacityMap {
		result[ilhLineID] = capacityConfigs
	}

	return result, nil
}
