package repo

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var (
	LHCapacityTabHook = &LHCapacityTab{}
)

// LHCapacityTab 物流商容量配置数据库表模型
type LHCapacityTab struct {
	ID                 int                  `gorm:"column:id"`
	CapacityName       string               `gorm:"column:capacity_name"`
	ILHVendorName      string               `gorm:"column:ilh_vendor_name"`
	ILHLineID          string               `gorm:"column:ilh_line_id"`
	ILHLineName        string               `gorm:"column:ilh_line_name"`
	TWS                TWSList              `gorm:"column:tws"`
	DestinationPort    DestinationList      `gorm:"column:destination_port"`
	DGType             rule.DGFlag          `gorm:"column:dg_type"`
	CapacitySettings   CapacitySettingsList `gorm:"column:capacity_settings"`
	RuleStatus         rule.RuleStatus      `gorm:"column:rule_status"`
	Operator           string               `gorm:"column:operator"`
	EffectiveStartTime int64                `gorm:"column:effective_start_time"`
	Ctime              int64                `gorm:"column:ctime;autoCreateTime"`
	Mtime              int64                `gorm:"column:mtime;autoUpdateTime"`
}

// TableName 返回表名
func (t LHCapacityTab) TableName() string {
	return "lh_capacity_tab"
}

// DBForRead 指定读取使用的数据库
func (t LHCapacityTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

// DBForWrite 指定写入使用的数据库
func (t LHCapacityTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

// ModelInfo 返回模型信息
func (t LHCapacityTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(t.ID),
		ModelName: t.TableName(),
	}
}

// ForecastLHCapacityTab 物流商容量配置数据库表模型
type ForecastLHCapacityTab struct {
	ID               int                  `gorm:"column:id"`
	TaskID           string               `gorm:"column:task_id"`
	CapacityName     string               `gorm:"column:capacity_name"`
	ILHVendorName    string               `gorm:"column:ilh_vendor_name"`
	ILHLineID        string               `gorm:"column:ilh_line_id"`
	ILHLineName      string               `gorm:"column:ilh_line_name"`
	TWS              TWSList              `gorm:"column:tws"`
	DestinationPort  DestinationList      `gorm:"column:destination_port"`
	DGType           rule.DGFlag          `gorm:"column:dg_type"`
	CapacitySettings CapacitySettingsList `gorm:"column:capacity_settings"`
	Ctime            int64                `gorm:"column:ctime;autoCreateTime"`
	Mtime            int64                `gorm:"column:mtime;autoUpdateTime"`
}

// TableName 返回表名
func (t ForecastLHCapacityTab) TableName() string {
	return "forecast_lh_capacity_tab"
}

// DBForRead 指定读取使用的数据库
func (t ForecastLHCapacityTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

// DBForWrite 指定写入使用的数据库
func (t ForecastLHCapacityTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

// ModelInfo 返回模型信息
func (t ForecastLHCapacityTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(t.ID),
		ModelName: t.TableName(),
	}
}

// TWSList 物流商TWS列表类型
type TWSList []string

// Scan 实现 sql.Scanner 接口，从数据库读取JSON格式并解析
func (t *TWSList) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, t)
}

// Value 实现 driver.Valuer 接口，序列化为JSON格式存入数据库
func (t TWSList) Value() (driver.Value, error) {
	if len(t) == 0 {
		return "[]", nil
	}
	return json.Marshal(t)
}

// DestinationList 目的地列表类型
type DestinationList []string

// Scan 实现 sql.Scanner 接口
func (d *DestinationList) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, d)
}

// Value 实现 driver.Valuer 接口
func (d DestinationList) Value() (driver.Value, error) {
	if len(d) == 0 {
		return "[]", nil
	}
	return json.Marshal(d)
}

// CapacitySettingsList 容量设置列表类型
type CapacitySettingsList []entity.CapacitySetting

// Scan 实现 sql.Scanner 接口
func (c *CapacitySettingsList) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, c)
}

// Value 实现 driver.Valuer 接口
func (c CapacitySettingsList) Value() (driver.Value, error) {
	if len(c) == 0 {
		return "[]", nil
	}
	return json.Marshal(c)
}

// ConvertToEntity 将数据库模型转换为领域实体
func (t *LHCapacityTab) ConvertToEntity() *entity.LHCapacityConfig {
	return &entity.LHCapacityConfig{
		ID:                 t.ID,
		CapacityName:       t.CapacityName,
		ILHVendorName:      t.ILHVendorName,
		ILHLineID:          t.ILHLineID,
		ILHLineName:        t.ILHLineName,
		TWS:                []string(t.TWS),
		DestinationPort:    []string(t.DestinationPort),
		DGType:             t.DGType,
		CapacitySettings:   []entity.CapacitySetting(t.CapacitySettings),
		RuleStatus:         t.RuleStatus,
		EffectiveStartTime: t.EffectiveStartTime,
		Operator:           t.Operator,
	}
}

// ConvertFromEntity 将领域实体转换为数据库模型
func (t *LHCapacityTab) ConvertFromEntity(config *entity.LHCapacityConfig) {
	t.ID = config.ID
	t.CapacityName = config.CapacityName
	t.ILHVendorName = config.ILHVendorName
	t.ILHLineID = config.ILHLineID
	t.ILHLineName = config.ILHLineName
	t.TWS = TWSList(config.TWS)
	t.DestinationPort = DestinationList(config.DestinationPort)
	t.DGType = config.DGType
	t.CapacitySettings = CapacitySettingsList(config.CapacitySettings)
	t.RuleStatus = config.RuleStatus
	t.EffectiveStartTime = config.EffectiveStartTime
	t.Operator = config.Operator
}
