package forecasting_sub_task

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
	"time"
)

type ForecastingSubTaskService interface {
	SplitMaskingTask(ctx context.Context, maskingTask *model.AllocateForecastTaskConfigTab, orderPaidTime *schema.OrderPaidTime) ([]ForecastingSubTaskTab, *srerr.Error)
	BatchCreateSubTasks(ctx context.Context, subTasks []ForecastingSubTaskTab) *srerr.Error
	UpdateSubTaskByObj(ctx context.Context, condition map[string]interface{}, subTask ForecastingSubTaskTab) *srerr.Error
	UpdateSubTaskByMap(ctx context.Context, condition map[string]interface{}, value map[string]interface{}) *srerr.Error
	SelectSubTask(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error)
	BatchDeleteSubTaskById(ctx context.Context, ids []uint64) *srerr.Error
	BatchUpdateSubTaskById(ctx context.Context, ids []uint64, value map[string]interface{}) *srerr.Error
}

type ForecastingSubTaskServiceImpl struct {
	ForecastingSubTaskRepo ForecastingSubTaskRepo
}

func NewForecastingSubTaskServiceImpl(ForecastingSubTaskRepo ForecastingSubTaskRepo) *ForecastingSubTaskServiceImpl {
	return &ForecastingSubTaskServiceImpl{
		ForecastingSubTaskRepo: ForecastingSubTaskRepo,
	}
}

// 拆分masking task成sub tasks
func (f *ForecastingSubTaskServiceImpl) SplitMaskingTask(ctx context.Context, maskingTask *model.AllocateForecastTaskConfigTab, orderPaidTime *schema.OrderPaidTime) ([]ForecastingSubTaskTab, *srerr.Error) {
	var subTasks []ForecastingSubTaskTab
	if orderPaidTime == nil || len(orderPaidTime.TimeSections) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "empty order paid time")
	}
	timeList, err := orderPaidTime.OrderPaidTimeToTimeList()
	if err != nil {
		return nil, err
	}
	//1.获取Apollo配置
	conf := configutil.GetMaskingForecastConf(ctx)
	//2.循环时间片，根据Apollo配置生成sub task
	for _, dayTime := range timeList {
		if conf.TimeSliceString == "" { //没有配置，则默认按小时纬度拆分成sub task
			//这里不用策略模式是因为策略模式没有ctx，而打印日志需要ctx
			subTasks = append(subTasks, generateTaskByDefault(ctx, dayTime, maskingTask)...)
		} else { //按照Apollo配置进行拆分，避免数据倾斜
			subTasks = append(subTasks, generateTaskByApollo(ctx, dayTime, maskingTask)...)
		}
	}
	if len(subTasks) == 0 {
		return nil, srerr.New(srerr.EmptySubTasksError, nil, "masking forecast sub task are empty")
	}
	return subTasks, nil
}

// 批量插入sub tasks
func (f *ForecastingSubTaskServiceImpl) BatchCreateSubTasks(ctx context.Context, subTasks []ForecastingSubTaskTab) *srerr.Error {
	return f.ForecastingSubTaskRepo.BatchCreateSubTasks(ctx, subTasks)
}

func (f *ForecastingSubTaskServiceImpl) UpdateSubTaskByObj(ctx context.Context, condition map[string]interface{}, subTask ForecastingSubTaskTab) *srerr.Error {
	return f.ForecastingSubTaskRepo.UpdateSubTaskByObj(ctx, condition, subTask)
}

func (f *ForecastingSubTaskServiceImpl) UpdateSubTaskByMap(ctx context.Context, condition map[string]interface{}, value map[string]interface{}) *srerr.Error {
	return f.ForecastingSubTaskRepo.UpdateSubTaskByMap(ctx, condition, value)
}

func (f *ForecastingSubTaskServiceImpl) SelectSubTask(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error) {
	return f.ForecastingSubTaskRepo.SelectSubTask(ctx, condition)
}

func (f *ForecastingSubTaskServiceImpl) BatchDeleteSubTaskById(ctx context.Context, ids []uint64) *srerr.Error {
	return f.ForecastingSubTaskRepo.BatchDeleteSubTaskById(ctx, ids)
}

func (f *ForecastingSubTaskServiceImpl) BatchUpdateSubTaskById(ctx context.Context, ids []uint64, value map[string]interface{}) *srerr.Error {
	return f.ForecastingSubTaskRepo.BatchUpdateSubTaskById(ctx, ids, value)
}

// todo:SSCSMR-1480:固定两小时一个吗，还是天数过多的时候拆的粒度小些？
func generateTaskByDefault(ctx context.Context, dayTime time.Time, maskingTask *model.AllocateForecastTaskConfigTab) []ForecastingSubTaskTab {
	var subTasks []ForecastingSubTaskTab
	if maskingTask != nil {
		//获取起始时间，即左区间
		startTime := timeutil.GetStartTime(dayTime)
		//定义区间长度
		for i := 0; i < oneDayHour; i = i + defaultTimeGap {
			ctimeObj := timeutil.GetCurrentTimeByCid(ctx, envvar.GetCID())
			extendInfoString, _ := jsoniter.MarshalToString(ExtendInfo{})
			//获取结束时间，即右区间
			endTime := startTime + int64(defaultHourTimeGap*defaultTimeGap)
			subTask := ForecastingSubTaskTab{
				MainTaskId:     uint64(maskingTask.Id),
				ModuleName:     ModuleMaskingForecast,
				StartKey:       strconv.FormatInt(startTime, 10),
				EndKey:         strconv.FormatInt(endTime, 10),
				TaskStatus:     constant.TaskConfigStatusPending,
				Ctime:          ctimeObj.Unix(),
				Mtime:          ctimeObj.Unix(),
				LastUpdateTime: ctimeObj.Unix(),
				ExtendInfo:     extendInfoString,
			}
			subTasks = append(subTasks, subTask)
			//更新下一区间的起始时间
			startTime = endTime
		}
	}
	return subTasks
}

// 按照Apollo配置，如["0-1","1-4","4-8",...,"23-24"]，来拆分子任务
func generateTaskByApollo(ctx context.Context, dayTime time.Time, maskingTask *model.AllocateForecastTaskConfigTab) []ForecastingSubTaskTab {
	var subTasks []ForecastingSubTaskTab
	if maskingTask != nil {
		//将配置拆解成slice，每个元素为时间区间的起、止时间
		conf := configutil.GetMaskingForecastConf(ctx)
		//conf.TimeSliceString e.g. ["0-1","1-4","4-8",...,"23-24"]
		var timeSlice []string
		uErr := jsoniter.UnmarshalFromString(conf.TimeSliceString, &timeSlice)
		if uErr != nil {
			logger.CtxLogErrorf(ctx, "generateTaskByApollo| unmarshal from string:%v, err:%v", conf.TimeSliceString, uErr)
			return nil
		}
		startTime := timeutil.GetStartTime(dayTime)
		for _, timeRange := range timeSlice {
			times := strings.Split(timeRange, "-")
			//人为规定，一定要包含起、止时间
			if len(times) != 2 {
				continue
			}
			//获取开始的hour数、结束的hour数
			startTimeHour, err := strconv.ParseInt(times[0], 10, 64)
			if err != nil {
				logger.CtxLogErrorf(ctx, "generateTaskByApollo|start time str:%v convert start time err:%v", times[0], err)
				continue
			}
			endTimeHour, err := strconv.ParseInt(times[1], 10, 64)
			if err != nil {
				logger.CtxLogErrorf(ctx, "generateTaskByApollo|end time str:%v convert end time err:%v", times[1], err)
				continue
			}
			ctimeObj := timeutil.GetCurrentTimeByCid(ctx, envvar.GetCID())
			extendInfoString, _ := jsoniter.MarshalToString(ExtendInfo{})
			subTask := ForecastingSubTaskTab{
				MainTaskId:     uint64(maskingTask.Id),
				ModuleName:     ModuleMaskingForecast,
				StartKey:       strconv.FormatInt(startTime+startTimeHour*defaultHourTimeGap, 10),
				EndKey:         strconv.FormatInt(startTime+endTimeHour*defaultHourTimeGap, 10),
				TaskStatus:     constant.TaskConfigStatusPending,
				Ctime:          ctimeObj.Unix(),
				Mtime:          ctimeObj.Unix(),
				LastUpdateTime: ctimeObj.Unix(),
				ExtendInfo:     extendInfoString,
			}
			subTasks = append(subTasks, subTask)
		}
	}
	return subTasks
}
