package forecasting_sub_task

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var ForecastingSubTaskHook = &ForecastingSubTaskTab{}

type ForecastingSubTaskTab struct {
	Id               uint64 `gorm:"column:id" json:"id"`
	MainTaskId       uint64 `gorm:"column:main_task_id" json:"main_task_id"`
	ModuleName       string `gorm:"column:module_name" json:"module_name"`
	StartKey         string `gorm:"column:start_key" json:"start_key"`
	EndKey           string `gorm:"column:end_key" json:"end_key"`
	LastStartKey     string `gorm:"column:last_start_key" json:"last_start_key"`
	LastEndKey       string `gorm:"column:last_end_key" json:"last_end_key"`
	TaskStatus       int    `gorm:"column:task_status"  json:"task_status"`
	LastUpdateTime   int64  `gorm:"column:last_update_time"  json:"last_update_time"`
	Version          int    `gorm:"column:version"  json:"version"`
	ExtendInfo       string `gorm:"column:extend_info"  json:"extend_info"`
	Ctime            int64  `gorm:"column:ctime"  json:"ctime"`
	Mtime            int64  `gorm:"column:mtime"  json:"mtime"`
	ShardingNo       int32  `gorm:"-" json:"sharding_no"`        //非table字段，用来保存分片序号
	TotalShardingNum int32  `gorm:"-" json:"total_sharding_num"` //非table字段，用来保存分片总数
	JobStartTime     int64  `gorm:"-" json:"job_start_time"`     //非table字段，用来保存开始时间
}

func (f *ForecastingSubTaskTab) TableName() string {
	return "forecasting_sub_task_tab"
}

func (f *ForecastingSubTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (f *ForecastingSubTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (f *ForecastingSubTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        f.Id,
		ModelName: f.TableName(),
		TaskId:    f.Id,
	}
}
