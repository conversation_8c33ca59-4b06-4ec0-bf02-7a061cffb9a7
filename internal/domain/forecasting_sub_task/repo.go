package forecasting_sub_task

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"time"
)

type ForecastingSubTaskRepo interface {
	BatchCreateSubTasks(ctx context.Context, subTasks []ForecastingSubTaskTab) *srerr.Error
	GetTasksDesc(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error)
	UpdateSubTaskByObj(ctx context.Context, condition map[string]interface{}, subTask ForecastingSubTaskTab) *srerr.Error
	UpdateSubTaskByMap(ctx context.Context, condition map[string]interface{}, value map[string]interface{}) *srerr.Error
	SelectSubTask(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error)
	BatchDeleteSubTaskById(ctx context.Context, ids []uint64) *srerr.Error
	BatchUpdateSubTaskById(ctx context.Context, ids []uint64, value map[string]interface{}) *srerr.Error
	CreateSubTaskByTaskConfig(ctx context.Context, taskConfig *model.AllocateForecastTaskConfigTab) *srerr.Error
}

type ForecastingSubTaskRepoImpl struct {
}

func NewForecastingSubTaskRepoImpl() *ForecastingSubTaskRepoImpl {
	return &ForecastingSubTaskRepoImpl{}
}

func (f *ForecastingSubTaskRepoImpl) BatchCreateSubTasks(ctx context.Context, subTasks []ForecastingSubTaskTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, ForecastingSubTaskHook, subTasks); err != nil {
		return srerr.With(srerr.BatchCreateSubTaskError, nil, err)
	}
	return nil
}

func (f *ForecastingSubTaskRepoImpl) GetTasksDesc(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error) {
	var tasks []ForecastingSubTaskTab
	if err := dbutil.Select(ctx, ForecastingSubTaskHook, condition, &tasks, dbutil.WithOrder("last_update_time DESC")); err != nil {
		return nil, srerr.With(srerr.SelectSubTaskError, nil, err)
	}
	return tasks, nil
}

func (f *ForecastingSubTaskRepoImpl) UpdateSubTaskByObj(ctx context.Context, condition map[string]interface{}, subTask ForecastingSubTaskTab) *srerr.Error {
	if err, _ := dbutil.UpdateByObj(ctx, ForecastingSubTaskHook, condition, subTask, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.UpdateSubTaskError, nil, err)
	}
	return nil
}

func (f *ForecastingSubTaskRepoImpl) UpdateSubTaskByMap(ctx context.Context, condition map[string]interface{}, value map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, ForecastingSubTaskHook, condition, value, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.UpdateSubTaskError, nil, err)
	}
	return nil
}

func (f *ForecastingSubTaskRepoImpl) SelectSubTask(ctx context.Context, condition map[string]interface{}) ([]ForecastingSubTaskTab, *srerr.Error) {
	var tasks []ForecastingSubTaskTab
	if err := dbutil.Select(ctx, ForecastingSubTaskHook, condition, &tasks); err != nil {
		return nil, srerr.With(srerr.SelectSubTaskError, nil, err)
	}
	return tasks, nil
}

func (f *ForecastingSubTaskRepoImpl) BatchDeleteSubTaskById(ctx context.Context, ids []uint64) *srerr.Error {
	if err := dbutil.Delete(ctx, ForecastingSubTaskHook, map[string]interface{}{
		"id in (?)": ids,
	}, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.BatchDeleteSubTaskError, nil, err)
	}
	return nil
}

func (f *ForecastingSubTaskRepoImpl) BatchUpdateSubTaskById(ctx context.Context, ids []uint64, value map[string]interface{}) *srerr.Error {
	condition := map[string]interface{}{
		"id in (?)": ids,
	}
	if err := dbutil.Update(ctx, ForecastingSubTaskHook, condition, value, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.BatchUpdateSubTaskError, nil, err)
	}
	return nil
}

func (b *ForecastingSubTaskRepoImpl) CreateSubTaskByTaskConfig(ctx context.Context, taskConfig *model.AllocateForecastTaskConfigTab) *srerr.Error {
	splitRuleList := unmarshalBatchSize(ctx, taskConfig)
	if splitRuleList == nil {
		// TODO 换一个错误返回码
		return srerr.New(srerr.ParamErr, nil, "get split rule failed")
	}
	// 构建sub task
	subTaskList, sErr := buildSubTask(ctx, taskConfig, splitRuleList)
	if sErr != nil {
		return sErr
	}
	// 将db和ctx绑定起来方便后面开启事务
	newCtx, ctxErr := bindCtxWithDB(ctx, model.BatchAllocateSubTaskTabHook)
	if ctxErr != nil {
		return ctxErr
	}
	// 开启事务批量创建sub task和unit task
	if err := scormv2.PropagationRequired(newCtx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		// 批量创建sub task
		if err := tx.Table(model.BatchAllocateSubTaskTabHook.TableName()).
			CreateInBatches(subTaskList, model.BatchCreateSize).GetError(); err != nil {
			return err
		}

		// 批量创建unit task
		if err := CreateBatchAllocateUnitTask(ctx, tx, taskConfig, subTaskList); err != nil {
			return err
		}
		// 这里不能直接 return CreateBatchAllocateUnitTask(ctx, tx, taskConfig, ruleSubTaskMap),因为这里返回的是(*srerr.Error)(nil)不会纯的nil
		return nil
	}); err != nil {
		logger.CtxLogErrorf(newCtx, "batch create sub task and unit task failed %v", err)
		return srerr.With(srerr.ParamErr, nil, err)
	}

	return nil
}

func bindCtxWithDB(ctx context.Context, model dbutil.DBModel) (context.Context, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, model)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get master db failed %v", err)
		//TODO 换一个错误码
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}

	bindContext := scormv2.BindContext(ctx, db)

	return bindContext, nil
}

func unmarshalBatchSize(ctx context.Context, taskConfig *model.AllocateForecastTaskConfigTab) []*allocation.BatchSize {
	var batchSizeList []*allocation.BatchSize
	if len(taskConfig.BatchSizeList) > 0 {
		if err := jsoniter.Unmarshal(taskConfig.BatchSizeList, &batchSizeList); err != nil {
			logger.CtxLogErrorf(ctx, "Unmarshal taskConfig batchSize filed failed %v", err)
			return nil
		}
	}

	return batchSizeList
}

func buildSubTask(ctx context.Context, taskConfig *model.AllocateForecastTaskConfigTab, splitRuleList []*allocation.BatchSize) ([]model.BatchAllocateSubTaskTab, *srerr.Error) {
	subTaskList := make([]model.BatchAllocateSubTaskTab, 0, len(splitRuleList))
	//split rule和sub task映射表
	now := time.Now().Unix() // nolint
	// 按照一个拆分规则一个sub task
	for _, rule := range splitRuleList {
		marshal, err := json.Marshal(rule)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Marshal split rule failed %v", err)
			return nil, srerr.With(srerr.ParamErr, nil, err)
		}
		subTask := model.BatchAllocateSubTaskTab{
			ForecastTaskId: uint64(taskConfig.Id),
			MaskProductId:  int64(taskConfig.MaskingProductID),
			SplittingRule:  marshal,
			Ctime:          now,
			Mtime:          now,
		}
		//补充split rule entity
		subTask.SplittingRuleEntity = model.SplittingRule{
			BatchSizeName: rule.BatchSizeName,
			BatchSizeType: rule.BatchSizeType,
			FixedQuantity: int64(rule.FixedQuantity),
			FixedTime:     convertFixedTime(rule.FixedTime),
		}
		subTaskList = append(subTaskList, subTask)
	}

	return subTaskList, nil
}

func CreateBatchAllocateUnitTask(ctx context.Context, db scormv2.SQLCommon, taskConfig *model.AllocateForecastTaskConfigTab, subTaskList []model.BatchAllocateSubTaskTab) *srerr.Error {
	// 解析订单时间
	orderPaidTime := unmarshalOrderPaid(ctx, taskConfig)
	if orderPaidTime == nil {
		return srerr.New(srerr.ParamErr, nil, "get order paid time failed")
	}
	return doCreateBatchAllocateUnitTask(ctx, db, orderPaidTime, subTaskList)
}

func unmarshalOrderPaid(ctx context.Context, taskConfig *model.AllocateForecastTaskConfigTab) *allocation.OrderPaidTime {
	orderTime := allocation.OrderPaidTime{}
	if len(taskConfig.OrderPaidTime) > 0 {
		if err := jsoniter.Unmarshal(taskConfig.OrderPaidTime, &orderTime); err != nil {
			logger.CtxLogErrorf(ctx, "Unmarshal taskConfig orderTime filed failed %v", err)
			return nil
		}
	}
	return &orderTime
}

func doCreateBatchAllocateUnitTask(ctx context.Context, db scormv2.SQLCommon, orderPaidTime *allocation.OrderPaidTime, subTaskList []model.BatchAllocateSubTaskTab) *srerr.Error {
	timeList, err := orderPaidTime.OrderPaidTimeToTimeList()
	if err != nil {
		logger.CtxLogErrorf(ctx, "get time list failed %v", err)
		return err
	}
	unitTabList := make([]model.BatchAllocateForecastUnitTab, 0, len(timeList)*len(subTaskList))
	// 这里创建的unit task是订单时间长度和规则长度的笛卡尔积
	for _, orderTime := range timeList {
		for _, subTask := range subTaskList {
			tab := model.BatchAllocateForecastUnitTab{
				SubTaskId:               subTask.Id,
				SplitDateUnix:           orderTime.Unix(),
				BatchType:               subTask.SplittingRuleEntity.BatchSizeType,
				BatchStatus:             model.BatchForecastUnitPending,
				CTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
				MTime:                   timeutil.GetCurrentUnixTimeStamp(ctx),
				BatchAllocateForecastId: subTask.ForecastTaskId,
			}
			unitTabList = append(unitTabList, tab)
		}
	}

	if err := db.Table(model.BatchAllocateForecastUnitTabTabName).CreateInBatches(&unitTabList, model.BatchCreateSize).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "batch create forecast unit failed %v", err)
		return srerr.With(srerr.ParamErr, nil, err)
	}

	return nil
}

func convertFixedTime(schemaFixedTime allocation.FixedTime) model.FixedTime {
	modelFixedTime := model.FixedTime{}
	for _, schemaUnit := range schemaFixedTime.FixedTimeUnitList {
		fxTimeUnit := model.FixedTimeUnit{
			TimeRange: schemaUnit.TimeRange,
			StartTime: schemaUnit.StartTime,
			EndTime:   schemaUnit.EndTime,
		}
		modelFixedTime.FixedTimeUnitList = append(modelFixedTime.FixedTimeUnitList, fxTimeUnit)
	}
	return modelFixedTime
}
