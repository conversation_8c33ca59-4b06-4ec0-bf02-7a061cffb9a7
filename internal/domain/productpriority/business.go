//go:generate mockgen -destination=./mocks/service.go -package=productpriority . PriorityBusiness,PriorityDisplay

package productpriority

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"sort"
	"strconv"
)

// PriorityBusiness 用于api类查询数据使用
type PriorityBusiness interface {
	//GetALLProductPriorities(ctx context.Context, maskProductID int, shopGroupID int64) ([]ProductPriority, *srerr.Error)

	// Detail ProductPriority明细查询方法
	Detail(ctx context.Context, maskProductID int64, shopGroupID int64) (*entity.ProductPriority, *srerr.Error)
	GetActiveProductPrioritiesByProductId(ctx context.Context, maskProductID int) (
		priorities []entity.ProductPriority, lpsErr *srerr.Error)
}

type PriorityBusinessImpl struct {
	PriorityRepo PriorityRepo
	lpsApi       lpsclient.LpsApi
	//PriorityFilter PriorityFilter
}

func NewPriorityBusinessImpl(priorityRepo PriorityRepo, lpsApi lpsclient.LpsApi) *PriorityBusinessImpl {
	return &PriorityBusinessImpl{
		PriorityRepo: priorityRepo,
		lpsApi:       lpsApi,
		//PriorityFilter: priorityFilter,
	}
}

//func (p *PriorityBusinessImpl) GetALLProductPriorities(ctx context.Context, maskProductID int, shopGroupID int64) ([]ProductPriority, *srerr.Error) {
//	items, _, err := p.PriorityRepo.List(ctx, maskProductID, shopGroupID, 0, 0, 0)
//	if err != nil {
//		return nil, err
//	}
//	priorities := make([]ProductPriority, 0)
//	for _, item := range items {
//		priority, srerr := internal.ConvertTabToProductPriority(ctx, item, true)
//		if srerr != nil {
//			return nil, srerr
//		}
//		err := p.PriorityFilter.FilterProductPriority(ctx, priority, true)
//		if err != nil {
//			logger.CtxLogErrorf(ctx, err.Error())
//			continue
//		}
//		priorities = append(priorities, *priority)
//	}
//
//	return priorities, srerr
//}

func (p *PriorityBusinessImpl) Detail(ctx context.Context, maskProductID int64, shopGroupID int64) (
	*entity.ProductPriority, *srerr.Error) {
	return p.PriorityRepo.DetailWithCache(ctx, maskProductID, shopGroupID)
	//if srerr != nil {
	//	return nil, srerr
	//}
	// 以下方法会直接查询product_tab，business下的Detail方法，用于api接口后调用
	//srerr = p.PriorityFilter.FilterProductPriority(ctx, priority, true)
	//return priority, srerr
}

func (p *PriorityBusinessImpl) GetActiveProductPrioritiesByProductId(ctx context.Context, maskProductID int) (
	[]entity.ProductPriority, *srerr.Error) {
	items, gErr := p.PriorityRepo.GetActiveConfigByProductId(ctx, maskProductID)
	if gErr != nil {
		return nil, gErr
	}
	priorities := make([]entity.ProductPriority, 0)
	maskProduct, err := p.lpsApi.GetProductDetail(ctx, maskProductID)
	if err != nil {
		return nil, err
	}
	groupArray, gErr := p.lpsApi.GetShopGroupListByTag(ctx, uint64(lpsclient.ClientTag3PLMasking))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetActiveProductPrioritiesByProductId|get shop group list by tag err:%v", gErr)
		return nil, gErr
	}
	groupMap := convertShopArrayToMap(groupArray)
	for _, item := range items {
		priority, cErr := ConvertTabToProductPriority(ctx, *item, true)
		if cErr != nil {
			return nil, cErr
		}
		priority.MaskProductName = maskProduct.SellerDisplayName
		shopGroup, ok := groupMap[priority.ShopGroupID]
		if !ok {
			priority.ShopGroupName = "not enable now"
		} else {
			priority.ShopGroupName = shopGroup.GroupName
		}
		componentPriorities := p.enrichComponentPriorities(ctx, maskProduct, priority.ComponentPriorities)
		priority.ComponentPriorities = componentPriorities
		priorities = append(priorities, *priority)
	}

	return priorities, nil
}

func (p *PriorityBusinessImpl) enrichComponentPriorities(ctx context.Context, maskProduct *lpsclient.ProductDetailInfo, componentPriorities []entity.ComponentPriority) []entity.ComponentPriority {
	productMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	returnComponentPriorities := make([]entity.ComponentPriority, 0)
	componentProductMap := mathutil.SetIntSliceToMap(maskProduct.GetComponentProduct().ComponentProducts)
	for _, componentPriority := range componentPriorities {
		productExisted := false
		if productInterface, existed := productMap[strconv.FormatInt(componentPriority.ProductID, 10)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				componentPriority.Name = product.SellerDisplayName
				productExisted = true
			} else {
				logger.CtxLogErrorf(ctx, "enrichComponentPriorities| product id:%v, convert fail")
			}
		}
		if !productExisted {
			// 如果 component 不存在，就置为关闭
			componentPriority.Name = "not enabled now"
			componentPriority.Status = entity.Closed
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			delete(componentProductMap, int(componentPriority.ProductID))
			continue
		}
		if _, ok := componentProductMap[int(componentPriority.ProductID)]; !ok {
			componentPriority.Name = componentPriority.Name + "( not under mask product )"
			componentPriority.Status = entity.Closed
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			continue
		}
		delete(componentProductMap, int(componentPriority.ProductID))
		returnComponentPriorities = append(returnComponentPriorities, componentPriority)
	}
	for componentProductId := range componentProductMap {
		// 对于不在priority列表下的component product需要补齐
		componentPriority := entity.ComponentPriority{
			ProductID: int64(componentProductId),
			Status:    entity.Closed,
		}

		productExisted := false
		if productInterface, existed := productMap[strconv.FormatInt(componentPriority.ProductID, 10)]; existed {
			product, ok := productInterface.(*lpsclient.LogisticProductTab)
			if ok {
				componentPriority.Name = product.SellerDisplayName
				productExisted = true
			} else {
				logger.CtxLogErrorf(ctx, "enrichComponentPriorities| product id:%v, convert fail")
			}
		}

		if !productExisted {
			componentPriority.Name = "not enabled now"
			returnComponentPriorities = append(returnComponentPriorities, componentPriority)
			continue
		}
		returnComponentPriorities = append(returnComponentPriorities, componentPriority)
	}
	sort.Sort(entity.ComponentPriorities(returnComponentPriorities))
	for i := range returnComponentPriorities {
		returnComponentPriorities[i].Priority = uint32(i + 1)
	}
	return returnComponentPriorities
}

func convertShopArrayToMap(ShopGroupArray []lpsclient.ShopGroupUnit) map[int64]lpsclient.PriorityShopGroup {
	groupMap := make(map[int64]lpsclient.PriorityShopGroup)
	for _, groupUnit := range ShopGroupArray {
		groupMap[groupUnit.ShopGroupId] = groupUnit.PriorityShopGroup
	}
	return groupMap
}
