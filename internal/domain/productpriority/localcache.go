package productpriority

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	jsoniter "github.com/json-iterator/go"
	"sort"
)

func DumpMaskLogisticProductPriority() (map[string]interface{}, error) {
	var records []*LogisticProductPriorityTab
	// 只加载active状态的数据
	condition := map[string]interface{}{
		"config_status": entity.Active,
	}
	if err := dbutil.Select(context.Background(), LogisticProductPriorityTabHook, condition, &records); err != nil {
		return nil, err
	}
	data := make(map[string]interface{}, 0)
	//1. 按照ConvertTabToProductPriority的逻辑进行转换
	for _, record := range records {
		//2. ConvertTabToProductPriority的逻辑中，needConvertComponent在外层被定义为true -》 要检查componentPriorities
		var componentPriorities []entity.ComponentPriority = nil
		if record != nil && len(record.ComponentPriorities) > 0 {
			err := jsoniter.Unmarshal(record.ComponentPriorities, &componentPriorities)
			if err != nil {
				logger.LogErrorf("Unmarshal ComponentPriorities error: %v, record:%+v", err, record)
				continue
			}
		}
		sort.Sort(entity.ComponentPriorities(componentPriorities))
		priority := &entity.ProductPriority{
			ID:                  record.ID,            // nolint
			MaskProductID:       record.MaskProductID, // nolint
			ShopGroupID:         record.ShopGroupID,   // nolint
			Validity:            record.Validity,      // nolint
			RuleType:            record.RuleType,      // nolint
			Operator:            record.Operator,      // nolint
			Ctime:               record.Ctime,         // nolint
			Mtime:               record.Mtime,         // nolint
			ComponentPriorities: componentPriorities,
		}
		//3. 因为在数据库中maskProductID+shopGroupID组成一个唯一索引， 所以这里以他们作为关键key进行存缓存
		cacheKey := fmt.Sprintf("%d:%d", record.MaskProductID, record.ShopGroupID) // nolint
		data[cacheKey] = priority
	}
	return data, nil
}
