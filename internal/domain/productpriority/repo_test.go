package productpriority

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"testing"
)

func TestPriorityRepoImpl_DetailWithCache(t *testing.T) {
	ctx := context.Background()
	p := &PriorityRepoImpl{}
	var patch *gomonkey.Patches
	type args struct {
		maskProductID int64
		shopGroupID   int64
	}
	tests := []struct {
		name         string
		args         args
		wantPriority *entity.ProductPriority
		wantLpsErr   *srerr.Error
		setup        func()
	}{
		// TODO: Add test cases.
		{
			name:         "case 1: localCache not found",
			args:         args{},
			wantPriority: nil,
			wantLpsErr:   srerr.New(srerr.DatabaseErr, "localCache not found, cache name:%s", constant.MaskLogisticProductPriority),
			setup:        func() {},
		},
		{
			name:         "case 2: convert localCache err",
			args:         args{},
			wantPriority: nil,
			wantLpsErr:   srerr.New(srerr.DatabaseErr, "convert localCache err, cache name:%s", constant.MaskLogisticProductPriority),
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return 1, nil
				})
			},
		},
		{
			name:         "case 3: normal case",
			args:         args{},
			wantPriority: &entity.ProductPriority{},
			wantLpsErr:   nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &entity.ProductPriority{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			gotPriority, gotLpsErr := p.DetailWithCache(ctx, tt.args.maskProductID, tt.args.shopGroupID)
			common.AssertResult(t, gotPriority, tt.wantPriority, tt.wantLpsErr, gotLpsErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
