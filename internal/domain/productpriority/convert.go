package productpriority

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"sort"
)

func ConvertProductPriorityToTab(ctx context.Context, priority entity.ProductPriority,
	needConvertComponent bool) (*LogisticProductPriorityTab, *srerr.Error) {

	var componentPrioritiesBytes []byte = nil
	if needConvertComponent {
		componentPriorities := priority.ComponentPriorities
		if len(componentPriorities) > 0 {
			sort.Sort(entity.ComponentPriorities(componentPriorities))

			var err error
			componentPrioritiesBytes, err = json.Marshal(componentPriorities)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Marshal ComponentPriorities error: %v", err)
				return nil, srerr.With(srerr.JsonErr, "Marshal ComponentPriorities error ", err)
			}
		}
	}

	item := &LogisticProductPriorityTab{
		ID:                  priority.ID,
		MaskProductID:       priority.MaskProductID,
		ShopGroupID:         priority.ShopGroupID,
		RuleType:            priority.RuleType,
		ComponentPriorities: componentPrioritiesBytes,
		Operator:            priority.Operator,
		Ctime:               priority.Ctime,
		Mtime:               priority.Mtime,
		MaskProductName:     priority.MaskProductName,
		ShopGroupName:       priority.ShopGroupName,
		EffectiveStartTime:  int(int32(priority.EffectiveStartTime)),
		ExpiredTime:         int(int32(priority.ExpiredTime)),
		ForecastTaskId:      priority.ForecastTaskId,
		ConfigStatus:        int32(priority.Status),
	}
	return item, nil
}

// @param needConvertComponent: if true, it will convert LogisticProductPriorityTab.Priorities
func ConvertTabToProductPriority(ctx context.Context, item LogisticProductPriorityTab,
	needConvertComponent bool) (*entity.ProductPriority, *srerr.Error) {

	var componentPriorities []entity.ComponentPriority = nil

	if needConvertComponent {
		if componentPrioritiesBytes := item.ComponentPriorities; len(componentPrioritiesBytes) > 0 {

			err := json.Unmarshal(componentPrioritiesBytes, &componentPriorities)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Unmarshal ComponentPriorities error: %v", err)
				return nil, srerr.With(srerr.JsonErr, "Unmarshal ComponentPriorities error ", err)
			}
		}
	}

	sort.Sort(entity.ComponentPriorities(componentPriorities))

	priority := &entity.ProductPriority{
		ID:                  item.ID,
		MaskProductID:       item.MaskProductID,
		ShopGroupID:         item.ShopGroupID,
		RuleType:            item.RuleType,
		Operator:            item.Operator,
		Ctime:               item.Ctime,
		Mtime:               item.Mtime,
		ComponentPriorities: componentPriorities,
		Status:              int(item.ConfigStatus),
		ForecastTaskId:      item.ForecastTaskId,
		EffectiveStartTime:  int(item.EffectiveStartTime),
	}
	return priority, nil
}
