package entity

import (
	"sort"
)

// ProductPriority is used to show data of LogisticProductPriorityTab
type ProductPriority struct {
	ID                  uint64              `json:"id"`
	MaskProductID       int64               `json:"mask_product_id"`
	MaskProductName     string              `json:"mask_product_name"`
	ShopGroupID         int64               `json:"shop_group_id"`
	ShopGroupName       string              `json:"shop_group_name"`
	RuleType            DefaultRuleType     `json:"rule_type"`
	Validity            PriorityValidity    `json:"validity"`
	Operator            string              `json:"operator"` // 操作员
	Ctime               int64               `json:"ctime"`    // 创建时间
	Mtime               int64               `json:"mtime"`    // 操作时间
	ComponentPriorities []ComponentPriority `json:"component_priorities"`
	EffectiveStartTime  int                 `json:"effective_start_time"` // 生效时间
	ExpiredTime         int                 `json:"expired_time"`         // 失效时间
	ForecastTaskId      int64               `json:"forecast_task_id"`     // 预测任务id
	Status              int                 `json:"config_status"`        // 状态,1:active；2:upcoming; 3:expired'
}

func (p ProductPriority) GetPrioritiesMap() map[int]ComponentPriority {
	var retMap = make(map[int]ComponentPriority)

	if p.ComponentPriorities == nil || len(p.ComponentPriorities) == 0 {
		return retMap
	}

	var tempList = make([]ComponentPriority, 0)
	for _, componentPriority := range p.ComponentPriorities {
		if componentPriority.Status == Open {
			tempList = append(tempList, componentPriority)
		}
	}

	sort.Sort(ComponentPriorities(tempList))

	for k, v := range tempList {
		v.Priority = uint32(k + 1)
		retMap[k+1] = v
	}

	return retMap
}

func (p *ProductPriority) SetMaskedProductDisabled() {
	p.Validity = p.Validity | MaskProductDisabled
	p.MaskProductName = "not enabled now"
}

func (p *ProductPriority) SetShopGroupDisabled() {
	p.Validity = p.Validity | ShopGroupDisabled
	p.ShopGroupName = "not enabled now"
}

func (p *ProductPriority) SetMaskedProductEnabled(maskProductName string) {
	p.Validity = p.Validity & ^MaskProductDisabled
	p.MaskProductName = maskProductName
}

func (p *ProductPriority) SetShopGroupEnabled(shopGroupName string) {
	p.Validity = p.Validity & ^ShopGroupDisabled
	p.ShopGroupName = shopGroupName
}

func (p *ProductPriority) IsMaskedProductDisabled() bool {
	return p.Validity&MaskProductDisabled != 0
}

func (p *ProductPriority) IsShopGroupDisabled() bool {
	return p.Validity&ShopGroupDisabled != 0
}

type ComponentPriority struct {
	ProductID int64                   `json:"product_id"`
	Name      string                  `json:"name"`
	Priority  uint32                  `json:"priority"`
	Weightage uint32                  `json:"weightage"`
	Status    ComponentPriorityStatus `json:"status"`
}

type ComponentPriorities []ComponentPriority

func (c ComponentPriorities) Len() int {
	return len(c)
}

func (c ComponentPriorities) Less(i, j int) bool {
	//1.open的权重永远比closed的大
	if c[i].Status == Closed && c[j].Status == Open {
		return false
	} else if c[i].Status == Open && c[j].Status == Closed {
		return true
	} else { //2.open vs open, or closed vs closed, 比较其权重值
		return c[i].Priority < c[j].Priority
	}
}

func (c ComponentPriorities) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}
