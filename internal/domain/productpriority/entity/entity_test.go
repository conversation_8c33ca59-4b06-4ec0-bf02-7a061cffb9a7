package entity

import (
	"reflect"
	"testing"
)

func TestProductPriority_GetPrioritiesMap(t *testing.T) {
	p := ProductPriority{}
	tests := []struct {
		name  string
		want  map[int]ComponentPriority
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: p.ComponentPriorities == nil",
			want:  map[int]ComponentPriority{},
			setup: func() {},
		},
		{
			name: "case 2: len(p.ComponentPriorities) == 0",
			want: map[int]ComponentPriority{},
			setup: func() {
				p.ComponentPriorities = []ComponentPriority{}
			},
		},
		{
			name: "case 3: len(p.ComponentPriorities) > 0",
			want: map[int]ComponentPriority{
				1: ComponentPriority{ProductID: 1, Status: Open, Priority: 1},
				2: ComponentPriority{ProductID: 3, Status: Open, Priority: 2},
			},
			setup: func() {
				p.ComponentPriorities = []ComponentPriority{
					ComponentPriority{ProductID: 1, Status: Open, Priority: 1},
					ComponentPriority{ProductID: 2, Status: Closed, Priority: 2},
					ComponentPriority{ProductID: 3, Status: Open, Priority: 3},
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := p.GetPrioritiesMap(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPrioritiesMap() = %v, want %v", got, tt.want)
			}
			p = ProductPriority{}
		})
	}
}
