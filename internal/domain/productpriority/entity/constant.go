package entity

// PriorityValidity 表示整个的mask product priority的可用性
type PriorityValidity uint32

const (
	Enabled             PriorityValidity = iota // 0b00, 可用
	MaskProductDisabled                         // 0b01, mask product不可用
	ShopGroupDisabled                           // 0b10, shop group不可用
	AllDisabled                                 // 0b11, 均不可用
)

// ComponentPriorityStatus 表示每一个component product的开与关
type ComponentPriorityStatus uint32

const (
	Closed ComponentPriorityStatus = iota
	Open
)

// DefaultRuleType has Channel Priority/Weightage
type DefaultRuleType uint32

const (
	Priority  DefaultRuleType = 1
	Weightage DefaultRuleType = 2
)

const (
	MaskDefaultProductPriority  = "StepChannelPriority"
	MaskDefaultProductWeightage = "StepChannelWeightage"
)

const (
	SingleMaskingTypePriority  = 1
	SingleMaskingTypeWeightage = 1
)

func (drt DefaultRuleType) String() string {
	switch drt {
	case Priority:
		return MaskDefaultProductPriority
	case Weightage:
		return MaskDefaultProductWeightage
	default:
		return "UnknownStep"
	}
}

type PriorityConfigStatusType int32

const (
	Active   PriorityConfigStatusType = 1
	Upcoming PriorityConfigStatusType = 2
	Expired  PriorityConfigStatusType = 3
)

const (
	ActiveName   = "active"
	UpcomingName = "upcoming"
	ExpiredName  = "expired"
)

func (s PriorityConfigStatusType) String() string {
	switch s {
	case Active:
		return ActiveName
	case Upcoming:
		return UpcomingName
	case Expired:
		return ExpiredName
	default:
		return "UnknownStatus"
	}
}

const (
	//Active   = 1
	Inactive = 2
)
