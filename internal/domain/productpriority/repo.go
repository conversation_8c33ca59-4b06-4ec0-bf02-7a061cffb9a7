package productpriority

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type PriorityRepo interface {
	// List 遵循：
	// 如果 maskProductID == 0 查询所有 maskProductID
	// 如果 shopGroupID == 0 查询所有 shopGroupID
	// 如果 page == 0 || count == 0,则不执行分页查询, 改为全量返回
	List(ctx context.Context, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, statusList []uint32, page uint32, count uint32) (
		items []LogisticProductPriorityTab, total uint32, smrErr *srerr.Error)

	//Detail(ctx context.Context, maskProductID int, shopGroupID int64) (item internal.LogisticProductPriorityTab, lpsErr *srerr.Error)
	DetailWithCache(ctx context.Context, maskProductID int64, shopGroupID int64) (priority *entity.ProductPriority, lpsErr *srerr.Error)

	//  当更改数据时，需要设置 item 的 mask product name 与 shop group name
	Create(ctx context.Context, item *LogisticProductPriorityTab) (smrErr *srerr.Error)
	Update(ctx context.Context, item *LogisticProductPriorityTab) *srerr.Error
	GetById(ctx context.Context, id uint64) (*LogisticProductPriorityTab, *srerr.Error)
	GetActiveConfigByProductIdAndGroupId(ctx context.Context, maskProductID int, shopGroupID int64) (*LogisticProductPriorityTab, *srerr.Error)
	GetActiveConfigByProductId(ctx context.Context, maskProductID int) ([]*LogisticProductPriorityTab, *srerr.Error)
	//Create(ctx context.Context, item *internal.LogisticProductPriorityTab) (lpsErr *srerr.Error)
	//Update(ctx context.Context, item *internal.LogisticProductPriorityTab) (lpsErr *srerr.Error)

	GetProductPriority(ctx context.Context, maskProductID int64, shopGroupID int64, configStatus []int32) ([]*LogisticProductPriorityTab, *srerr.Error)
	ImportSaveProductPriority(ctx context.Context, insertList []*LogisticProductPriorityTab, updateList []*LogisticProductPriorityTab) *srerr.Error
	GetProductPriorityList(ctx context.Context, condition map[string]interface{}) ([]*LogisticProductPriorityTab, *srerr.Error)
	GetActiveConfigByProductIdAndGroupIdComplyRecordInexist(ctx context.Context, maskProductID int, shopGroupID int64) (*LogisticProductPriorityTab, *srerr.Error)
}

// PriorityRepoImpl 当更改数据时，需要传入mask product name 与 shop group name
type PriorityRepoImpl struct {
	lpsApi lpsclient.LpsApi
}

func NewPriorityRepoImpl(lpsApi lpsclient.LpsApi) *PriorityRepoImpl {
	return &PriorityRepoImpl{
		lpsApi: lpsApi,
	}
}

func (p PriorityRepoImpl) DetailWithCache(ctx context.Context, maskProductID int64, shopGroupID int64) (priority *entity.ProductPriority, lpsErr *srerr.Error) {
	//1. 从缓存获取数据
	cacheKey := fmt.Sprintf("%d:%d", maskProductID, shopGroupID)
	data, err := localcache.Get(ctx, constant.MaskLogisticProductPriority, cacheKey)
	//2. 获取失败
	if err != nil {
		return priority, srerr.New(srerr.DatabaseErr, "localCache not found, cache name:%s", constant.MaskLogisticProductPriority)
	}
	priority, ok := data.(*entity.ProductPriority)
	//3. 转换失败
	if !ok {
		return priority, srerr.New(srerr.DatabaseErr, "convert localCache err, cache name:%s", constant.MaskLogisticProductPriority)
	}

	return priority, nil
}

func (p *PriorityRepoImpl) List(ctx context.Context, maskProductID int, shopGroupID int64, ruleType entity.DefaultRuleType, statusList []uint32, page uint32, count uint32) (
	items []LogisticProductPriorityTab, total uint32, smrErr *srerr.Error) {

	condition := make(map[string]interface{})
	if maskProductID != 0 {
		condition["mask_product_id = ?"] = maskProductID
		//query = query.Where("mask_product_id = ?", maskProductID)
	}

	if shopGroupID != 0 {
		condition["shop_group_id = ?"] = shopGroupID
		//query = query.Where("shop_group_id = ?", shopGroupID)
	}

	if ruleType != 0 {
		condition["rule_type = ?"] = ruleType
		//query = query.Where("rule_type = ?", ruleType)
	}

	if len(statusList) > 0 {
		condition["config_status in (?)"] = statusList
		//query = query.Where("config_status in (?)", statusList)
	}

	// 判断是不是分页查询
	isPagingQuery := false
	pageOption := dbutil.WithPage(0, 0)
	if count != 0 && page != 0 {
		pageOption = dbutil.WithPage(int64((page-1)*count), int64(count))
		isPagingQuery = true
	}

	if err := dbutil.Select(ctx, LogisticProductPriorityTabHook, condition, &items, pageOption); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	if isPagingQuery {
		var tempTotal int64
		if err := dbutil.Count(ctx, LogisticProductPriorityTabHook, condition, &tempTotal); err != nil {
			return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
		}
		total = uint32(tempTotal)
	}

	return items, total, nil
}

func (p *PriorityRepoImpl) GetById(ctx context.Context, id uint64) (*LogisticProductPriorityTab, *srerr.Error) {
	var item LogisticProductPriorityTab

	if err := dbutil.First(ctx, LogisticProductPriorityTabHook, map[string]interface{}{"id = ?": id}, &item); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, fmt.Sprintf("get product priority config failed. id=%v", id), err)
	}

	return &item, nil
}

func (p *PriorityRepoImpl) Create(ctx context.Context, item *LogisticProductPriorityTab) (smrErr *srerr.Error) {
	// 开启事务
	// create 需要有事务保证，product id 和 shop group id下，仅有一条active 状态的配置
	db, err := dbutil.MasterDB(ctx, LogisticProductPriorityTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	ctx = scormv2.BindContext(ctx, db)
	txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		//校验逻辑，
		if item.ConfigStatus == int32(entity.Active) {
			var tabs []*LogisticProductPriorityTab
			query1 := tx.Table(LogisticProductPriorityTabHook.TableName())
			query1 = query1.Where("mask_product_id = ?", item.MaskProductID)
			query1 = query1.Where("shop_group_id = ?", item.ShopGroupID)
			query1 = query1.Where("config_status = ?", entity.Active)
			tx = query1.Table(LogisticProductPriorityTabHook.TableName()).Find(&tabs)
			if tx.GetError() != nil {
				return srerr.With(srerr.DatabaseErr, nil, tx.GetError())
			}
			if len(tabs) > 0 {
				smrErr = srerr.New(srerr.ServerErr, nil, fmt.Sprintf("there is an active configure about mask_product_id=%v, shop_group_id=%v, can't create another active configure", item.MaskProductID, item.ShopGroupID))
				return smrErr
			}
		}
		if item.ConfigStatus == int32(entity.Upcoming) {
			var tabs []*LogisticProductPriorityTab
			query1 := tx.Table(LogisticProductPriorityTabHook.TableName())
			query1 = query1.Where("mask_product_id = ?", item.MaskProductID)
			query1 = query1.Where("shop_group_id = ?", item.ShopGroupID)
			query1 = query1.Where("config_status = ?", entity.Upcoming)
			tx = query1.Table(LogisticProductPriorityTabHook.TableName()).Find(&tabs)
			if tx.GetError() != nil {
				return srerr.With(srerr.DatabaseErr, nil, tx.GetError())
			}
			if len(tabs) > 0 {
				smrErr = srerr.New(srerr.ServerErr, nil, fmt.Sprintf("there is an upcoming configure about mask_product_id=%v, shop_group_id=%v, can't create another upcoming configure", item.MaskProductID, item.ShopGroupID))
				return smrErr
			}
		}
		tx = tx.Table(LogisticProductPriorityTabHook.TableName()).Create(item)
		if tx.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, nil, tx.GetError())
		}

		return nil
	})
	//add history
	cErr := p.lpsApi.CreateHistory(ctx, item, string(lpsclient.CreateType), "create a product priority", item.ID)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	if txErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, txErr)
	}
	logger.CtxLogInfof(ctx, "create a product priority, item id:%v", item.ID)

	return nil
}

func (p *PriorityRepoImpl) Update(ctx context.Context, item *LogisticProductPriorityTab) *srerr.Error {
	// not affected row means no data change
	if err, _ := dbutil.UpdateByObj(ctx, LogisticProductPriorityTabHook, map[string]interface{}{"id = ?": item.ID}, item, dbutil.ModelInfo{
		MaskProductId: uint64(item.MaskProductID),
		Id:            item.ID,
		ModelName:     LogisticProductPriorityTabHook.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	//add history
	cErr := p.lpsApi.CreateHistory(ctx, item, string(lpsclient.UpdateType), "update a product priority", item.ID)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	itemBytes, _ := json.Marshal(item)
	logger.CtxLogInfof(ctx, "Update| update priority, item id:%v, item:%v, component products:%v", item.ID, string(itemBytes), string(item.ComponentPriorities))

	return nil
}

func (p *PriorityRepoImpl) GetActiveConfigByProductIdAndGroupId(ctx context.Context, maskProductID int, shopGroupID int64) (*LogisticProductPriorityTab, *srerr.Error) {
	var item LogisticProductPriorityTab
	if err := dbutil.First(ctx, LogisticProductPriorityTabHook, map[string]interface{}{
		"mask_product_id = ?": maskProductID,
		"shop_group_id = ?":   shopGroupID,
		"config_status = ?":   entity.Active,
	}, &item); err != nil {
		logger.CtxLogErrorf(ctx, "GetActiveConfigByProductIdAndGroupId|db err:%v", err)
		return nil, srerr.New(srerr.DatabaseErr, nil, fmt.Sprintf("get active product priority config failed. maskProductID=%v, shopGroupID=%v", maskProductID, shopGroupID))
	}

	return &item, nil
}

func (p *PriorityRepoImpl) GetActiveConfigByProductId(ctx context.Context, maskProductID int) ([]*LogisticProductPriorityTab, *srerr.Error) {
	var items []*LogisticProductPriorityTab
	if err := dbutil.Select(ctx, LogisticProductPriorityTabHook, map[string]interface{}{
		"mask_product_id = ?": maskProductID,
		"config_status = ?":   entity.Active,
	}, &items); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, fmt.Sprintf("get active product priority config failed. maskProductID=%v", maskProductID), err)

	}
	return items, nil
}

func (p *PriorityRepoImpl) GetProductPriority(ctx context.Context, maskProductID int64, shopGroupID int64, configStatus []int32) ([]*LogisticProductPriorityTab, *srerr.Error) {
	var records []*LogisticProductPriorityTab
	condition := map[string]interface{}{
		"mask_product_id = ?": maskProductID,
		"shop_group_id = ?":   shopGroupID,
		"config_status in ?":  configStatus,
	}
	if err := dbutil.Select(ctx, LogisticProductPriorityTabHook, condition, &records); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return records, nil
}

func (p *PriorityRepoImpl) ImportSaveProductPriority(ctx context.Context, insertList []*LogisticProductPriorityTab, updateList []*LogisticProductPriorityTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, LogisticProductPriorityTabHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		//保存新增的数据
		tx := scormv2.Context(ctx)
		if len(insertList) > 0 {
			if err := tx.Table(LogisticProductPriorityTabHook.TableName()).CreateInBatches(insertList, 100).GetError(); err != nil {
				return srerr.With(srerr.ImportSaveProductPriorityFail, nil, err)
			}
		}
		if len(updateList) > 0 {
			for _, update := range updateList {
				if err := tx.Table(LogisticProductPriorityTabHook.TableName()).Updates(update).GetError(); err != nil {
					return srerr.With(srerr.ImportSaveProductPriorityFail, nil, err)
				}
			}
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *PriorityRepoImpl) GetProductPriorityList(ctx context.Context, condition map[string]interface{}) ([]*LogisticProductPriorityTab, *srerr.Error) {
	var records []*LogisticProductPriorityTab
	if err := dbutil.Select(ctx, LogisticProductPriorityTabHook, condition, &records); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return records, nil
}
