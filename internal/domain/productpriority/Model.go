package productpriority

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	logisticProductPriorityTab = "logistic_product_priority_tab"
)

var (
	LogisticProductPriorityTabHook = &LogisticProductPriorityTab{}
)

type LogisticProductPriorityTab struct {
	ID                  uint64                  `gorm:"primary_key;auto_increment;column:id" json:"id"`
	MaskProductID       int64                   `gorm:"column:mask_product_id" json:"mask_product_id"`
	ShopGroupID         int64                   `gorm:"column:shop_group_id" json:"shop_group_id"`
	RuleType            entity.DefaultRuleType  `gorm:"column:rule_type" json:"rule_type"`
	ComponentPriorities []byte                  `gorm:"column:component_priorities" json:"-"`
	Validity            entity.PriorityValidity `gorm:"column:validity" json:"validity"`
	Operator            string                  `gorm:"column:operator" json:"operator"` // 操作员
	Ctime               int64                   `gorm:"column:ctime" json:"ctime"`       // 操作时间
	Mtime               int64                   `gorm:"column:mtime" json:"mtime"`       // 操作时间

	MaskProductName string `gorm:"-" json:"mask_product_name"`
	ShopGroupName   string `gorm:"-" json:"shop_group_name"`

	EffectiveStartTime int   `gorm:"column:effective_start_time" json:"effective_start_time"` // 生效时间
	ExpiredTime        int   `gorm:"column:expired_time" json:"expired_time"`                 // 失效时间
	ForecastTaskId     int64 `gorm:"column:forecast_task_id" json:"forecast_task_id"`         // 预测任务id
	ConfigStatus       int32 `gorm:"column:config_status" json:"config_status"`               // 状态
}

func (l *LogisticProductPriorityTab) TableName() string {
	return logisticProductPriorityTab
}

func (l *LogisticProductPriorityTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (l *LogisticProductPriorityTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p LogisticProductPriorityTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            p.ID,
		MaskProductId: uint64(p.MaskProductID),
		ModelName:     p.TableName(),
	}
}

func (p LogisticProductPriorityTab) ConvertToChangeData() string {
	return fmt.Sprintf("%d:%s %d:%s", p.MaskProductID, p.MaskProductName, p.ShopGroupID, p.ShopGroupName)
}
