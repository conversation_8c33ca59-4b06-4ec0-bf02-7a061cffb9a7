package productpriority

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func (p PriorityRepoImpl) GetActiveConfigByProductIdAndGroupIdComplyRecordInexist(ctx context.Context, maskProductID int, shopGroupID int64) (*LogisticProductPriorityTab, *srerr.Error) {
	db, lpsErr := dbutil.GetDB(ctx, LogisticProductPriorityTabHook.DBForWrite())
	if lpsErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, fmt.Sprintf("get active product priority config failed. maskProductID=%v,, shopGroupID=%v", maskProductID, shopGroupID), nil)
	}

	query := db.Table(LogisticProductPriorityTabHook.TableName())
	query = query.Where("mask_product_id = ?", maskProductID)
	query = query.Where("shop_group_id = ?", shopGroupID)
	query = query.Where("config_status = ?", entity.Active)

	var item LogisticProductPriorityTab
	db = query.Table(LogisticProductPriorityTabHook.TableName()).First(&item)

	if db.GetError() != nil && db.GetError() != scormv2.ErrRecordNotFound {
		return nil, srerr.With(srerr.DatabaseErr, fmt.Sprintf("db-errors,get active product priority config failed. maskProductID=%v,, shopGroupID=%v", maskProductID, shopGroupID), db.GetError())
	}
	return &item, nil
}
