package lane

import (
	"context"
	"errors"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"testing"
)

func TestLaneServiceImpl_isResourceNeedDgInLane(t *testing.T) {
	ctx := context.Background()
	l := &LaneServiceImpl{}

	var patch *gomonkey.Patches
	type args struct {
		laneCode        string
		resourceSubType uint32
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: localcache.Get error",
			args: args{},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return nil, errors.New("mock localcache get error")
				})
			},
		},
		{
			name: "case 2: convert type error",
			args: args{},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return "123", nil
				})
			},
		},
		{
			name: "case 3: lineInfo NeedDG",
			args: args{
				resourceSubType: 3,
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return &lane_entity.LaneInfo{
						Lines: map[string]*lane_entity.LineInfo{
							"line1": {
								SubType: 3,
								ModelRule: &lfspb.ModelRule{
									TwsSortFlag: proto.Int32(int32(lfspb.TwsSortFlagType_True)),
								},
							},
						},
					}, nil
				})
			},
		},
		{
			name: "case 4: lineInfo no NeedDG",
			args: args{},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return &lane_entity.LaneInfo{
						Lines: map[string]*lane_entity.LineInfo{},
					}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := l.isResourceNeedDgInLane(ctx, tt.args.laneCode, tt.args.resourceSubType); got != tt.want {
				t.Errorf("isResourceNeedDgInLane() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestLaneServiceImpl_IsResourceNeedDg(t *testing.T) {
	ctx := context.Background()
	l := &LaneServiceImpl{}

	var patch *gomonkey.Patches
	type args struct {
		laneCode        string
		laneCodeGroup   []string
		resourceSubType uint32
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: lfslib.C_FL / exist in lfslib.NeedRoutingILH",
			args: args{
				resourceSubType: lfslib.C_XAndT_ILH,
			},
			want:  true,
			setup: func() {},
		},
		{
			name: "case 2: Lane Code Group type NeedDG",
			args: args{
				laneCodeGroup:   []string{"123"},
				resourceSubType: 3,
			},
			want: true,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return &lane_entity.LaneInfo{
						Lines: map[string]*lane_entity.LineInfo{
							"line1": {
								SubType: 3,
								ModelRule: &lfspb.ModelRule{
									TwsSortFlag: proto.Int32(int32(lfspb.TwsSortFlagType_True)),
								},
							},
						},
					}, nil
				})
			},
		},
		{
			name: "case 3: Lane Code Group type no NeedDG",
			args: args{
				laneCodeGroup:   []string{"123"},
				resourceSubType: 3,
			},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return nil, errors.New("mock localcache get error")
				})
			},
		},
		{
			name: "case 4: Lane Code Group type no NeedDG",
			args: args{
				resourceSubType: 3,
			},
			want: false,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, key string) (interface{}, error) {
					return nil, errors.New("mock localcache get error")
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := l.IsResourceNeedDg(ctx, tt.args.laneCode, tt.args.laneCodeGroup, tt.args.resourceSubType); got != tt.want {
				t.Errorf("IsResourceNeedDg() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
