package lane_entity

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
)

const laneFormatTypeStr = "%d-%d"

var (
	LaneFormatTypeMultiInternation = LaneFormatType(fmt.Sprintf(laneFormatTypeStr, lfslib.LaneFormatMutilLayerCBFLisLM, lfslib.LaneSubFormatTypeInternational))
	LaneFormatTypeMultiLocal       = LaneFormatType(fmt.Sprintf(laneFormatTypeStr, lfslib.LaneFormatMutilLayerCBFLisLM, lfslib.LaneSubFormatTypeLocal))
	LaneFormatTypeMultiCBSPX       = LaneFormatType(fmt.Sprintf(laneFormatTypeStr, lfslib.LaneFormatMultiLayerCDFM, lfslib.LaneSubFormatTypeLocal))
)
