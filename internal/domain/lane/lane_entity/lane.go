package lane_entity

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"sort"
)

type LaneFormatType string

type LaneInfo struct {
	Lines          map[string]*LineInfo
	Sites          map[string]*SiteInfo
	LaneFormatType LaneFormatType // {format_sub_type}-{format_sub_format_type}
}

type LineInfo struct {
	LineID     string
	LineName   string
	MainType   int
	SubType    int
	FromRegion string
	ToRegion   string
	Sequence   int
	LastSite   *SiteInfo
	NextSite   *SiteInfo
	ModelRule  *lfspb.ModelRule
}

type SiteInfo struct {
	SiteID              string
	SiteName            string
	SiteCode            string
	MainType            int
	SubType             int
	Sequence            int
	VirtualTwsGroupSite bool
	WhsCodes            []string
	ActualPointMap      map[string]*ActualPointInfo
	ModelRule           *lfspb.ModelRule
}

type ActualPointInfo struct {
	ActualPointID      string
	SiteID             string //ActualPoint对应的site id
	ActualPointName    string
	Region             string
	StateLocationID    uint32
	CityLocationID     uint32
	DistrictLocationID uint32
	StreetLocationID   uint32
	Longitude          string
	Latitude           string
	ZipCode            string
	LocationID         uint64
}

func (l *LaneInfo) HasVirtualTws() bool {
	for _, siteInfo := range l.Sites {
		if siteInfo.VirtualTwsGroupSite {
			return true
		}
	}

	return false
}

func (l *LaneInfo) GetThirdPartyJoint() *SiteInfo {
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite && siteInfo.SubType == lfslib.ThirdPartyJointSite {
			return siteInfo
		}
	}

	return nil
}

func (l *LaneInfo) GetExportThirdPartyJoint() *SiteInfo {
	var export3plSites []*SiteInfo
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite && siteInfo.SubType == lfslib.ThirdPartyJointSite {
			export3plSites = append(export3plSites, siteInfo)
		}
	}
	//sort descending by `sequence`
	sort.Slice(export3plSites, func(i, j int) bool {
		return export3plSites[i].Sequence > export3plSites[j].Sequence
	})

	if len(export3plSites) == 0 {
		logger.LogErrorf("GetExportThirdPartyJoint|empty third party joint list")
		return nil
	}
	return export3plSites[0]
}

func (l *LaneInfo) GetJointInfo() *SiteInfo {
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite &&
			(siteInfo.SubType == lfslib.NormalJointSite || siteInfo.SubType == lfslib.ThirdPartyJointSite) {
			return siteInfo
		}
	}

	return &SiteInfo{}
}

func (l *LaneInfo) GetTwsInfo() *SiteInfo {
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite && siteInfo.SubType == lfslib.TWS {
			return siteInfo
		}
	}

	return &SiteInfo{}
}

func (l *LaneInfo) GetTwsCode() string {
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite && siteInfo.SubType == lfslib.TWS {
			return siteInfo.SiteCode
		}
	}

	return ""
}

func (l *LaneInfo) IsMultiLane() bool {
	switch l.LaneFormatType {
	case LaneFormatTypeMultiInternation, LaneFormatTypeMultiLocal:
		return true
	default:
		return false
	}
}

func (l *LaneInfo) IsMultiInternation() bool {
	if l.LaneFormatType == LaneFormatTypeMultiInternation {
		return true
	}

	return false
}

// IsLocalOfMultiLane -》true:该lane属于多段式中的本地段
func (l *LaneInfo) IsLocalOfMultiLane() bool {
	return l.IsMultiLocal() || l.IsMultiCBSPX()
}

func (l *LaneInfo) IsMultiCBSPX() bool {
	return l.LaneFormatType == LaneFormatTypeMultiCBSPX
}

func (l *LaneInfo) IsMultiLocal() bool {
	if l.LaneFormatType == LaneFormatTypeMultiLocal {
		return true
	}

	return false
}

func (l *LaneInfo) GetCFLLineInfo() *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == lfslib.C_FL {
			return line
		}
	}

	return &LineInfo{}
}

func (l *LaneInfo) GetLFMLieInfo() *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == lfslib.L_FM {
			return line
		}
	}

	return &LineInfo{}
}

func (l *LaneInfo) GetAllLineInfo() []*LineInfo {
	res := []*LineInfo{}
	for _, line := range l.Lines {
		res = append(res, line)
	}

	return res
}

func (l *LaneInfo) GetLLMLieInfo() *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == lfslib.L_LM {
			return line
		}
	}

	return &LineInfo{}
}

func (l *LaneInfo) GetLineIdCFL() string {
	for _, line := range l.Lines {
		if line.SubType == lfslib.C_FL {
			return line.LineID
		}
	}

	return ""
}

func (l *LaneInfo) GetCLMLineInfo() *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == lfslib.C_LM || line.SubType == lfslib.C_DFM {
			return line
		}
	}

	return &LineInfo{}
}

func (l *LaneInfo) GetAllLineID() []string {
	var lineIdList []string
	for _, line := range l.Lines {
		lineIdList = append(lineIdList, line.LineID)
	}
	lineIdList = objutil.RemoveDuplicateString(lineIdList)

	return lineIdList
}

func (l *LaneInfo) GetLineIdNameMap() map[string]string {
	lineIdNameMap := make(map[string]string)
	for _, line := range l.Lines {
		lineIdNameMap[line.LineID] = line.LineName
	}
	return lineIdNameMap
}

func (l *LaneInfo) GetAllSiteID() []string {
	var siteIdList []string
	for _, site := range l.Sites {
		siteIdList = append(siteIdList, site.SiteID)
	}
	siteIdList = objutil.RemoveDuplicateString(siteIdList)

	return siteIdList
}

func (l *LaneInfo) GetAllSiteInfo() []*SiteInfo {
	siteIdList := []*SiteInfo{}
	for _, site := range l.Sites {
		siteIdList = append(siteIdList, site)
	}
	return siteIdList
}

func (l *LaneInfo) HasSoc() bool {
	for _, siteInfo := range l.Sites {
		if siteInfo.MainType == lfslib.JointSite && siteInfo.SubType == lfslib.SocJointSite {
			return true
		}
	}

	return false
}

func (l *LaneInfo) GetLine(lineId string) *LineInfo {
	for _, line := range l.Lines {
		if line.LineID == lineId {
			return line
		}
	}

	return nil
}

func (l *LaneInfo) GetSite(siteId string) *SiteInfo {
	for _, site := range l.Sites {
		if site.SiteID == siteId {
			return site
		}
	}

	return nil
}

func (l *LaneInfo) GetActualPoint(siteId, actualPointId string) *ActualPointInfo {
	site := l.GetSite(siteId)
	if site == nil {
		return nil
	}

	return site.ActualPointMap[actualPointId]
}

func (l *LaneInfo) GetActualPointBySiteId(siteId string) []string {
	site := l.GetSite(siteId)
	if site == nil {
		return nil
	}
	apList := []string{}

	for apId := range site.ActualPointMap {
		apList = append(apList, apId)
	}
	//排序保证每次的到的结果是一样的
	sort.Strings(apList)
	return apList
}

func (l *LaneInfo) GetLineBySubType(resourceSubType int32) *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == int(resourceSubType) {
			return line
		}
	}

	return nil
}

func (l *LaneInfo) GetSiteBySubType(resourceSubType int32) *SiteInfo {
	for _, site := range l.Sites {
		if site.SubType == int(resourceSubType) {
			return site
		}
	}

	return nil
}

func (a *ActualPointInfo) GetLocationIds() []int {
	var result []int
	if a.StateLocationID > 0 {
		result = append(result, int(a.StateLocationID))
	}

	if a.CityLocationID > 0 {
		result = append(result, int(a.CityLocationID))
	}

	if a.DistrictLocationID > 0 {
		result = append(result, int(a.DistrictLocationID))
	}

	if a.StreetLocationID > 0 {
		result = append(result, int(a.StreetLocationID))
	}

	return result
}

func (l *LaneInfo) GetCILHLineInfo() *LineInfo {
	for _, line := range l.Lines {
		if objutil.ContainsInt(lfslib.NeedRoutingILH, line.SubType) {
			return line
		}

		// multi product 新的line模板模型，使用哪一段由lls的配置决定(lls的配置由lfs传递而来)
		if line.ModelRule != nil && line.ModelRule.GetTwsSortFlag() == int32(lfspb.TwsSortFlagType_True) {
			return line
		}
	}

	return &LineInfo{}
}

// GetImportILHLineInfo - 获取Import ILH(CC)Line
func (l *LaneInfo) GetImportILHLineInfo() *LineInfo {
	for _, line := range l.Lines {
		if line.SubType == lfslib.C_M_ILH {
			return line
		}
	}

	return &LineInfo{}
}

func (l *LineInfo) NeedDG() bool {
	if l.ModelRule != nil && l.ModelRule.GetTwsSortFlag() == int32(lfspb.TwsSortFlagType_True) {
		return true
	}

	return false
}

func (l *LaneInfo) GetAllJoinSiteID() []string {
	var siteIdList []string
	for _, site := range l.Sites {
		if site.MainType == lfslib.JointSite && site.SubType == lfslib.SocJointSite {
			siteIdList = append(siteIdList, site.SiteID)
		}
	}
	siteIdList = objutil.RemoveDuplicateString(siteIdList)

	return siteIdList
}
