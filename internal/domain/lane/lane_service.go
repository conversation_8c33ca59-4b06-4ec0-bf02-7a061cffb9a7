package lane

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type LaneService interface {
	GetLaneInfoByLaneCode(ctx context.Context, laneCode string) (*lane_entity.LaneInfo, *srerr.Error)
	BatchGetLaneInfoWithoutCache(ctx context.Context, laneCodes []string) (map[string]*lane_entity.LaneInfo, *srerr.Error)
	BatchGetLaneInfoByLaneCode(ctx context.Context, laneCodes []string) ([]*lane_entity.LaneInfo, *srerr.Error)
	GetLineIdToLineNameMap(ctx context.Context) (map[string]string, map[string]*lane_entity.LineInfo)
	GetLineSubTypeMapByLanes(laneInfos []*lane_entity.LaneInfo, roleMap map[int]int) map[int][]*lane_entity.LineInfo
	IsResourceNeedDg(ctx context.Context, laneCode string, laneCodeGroup []string, resourceSubType uint32) bool
}

type LaneServiceImpl struct {
	lfsApi lfsclient.LfsApi
	llsApi llsclient.LlsApi
}

func NewLaneService(lfs lfsclient.LfsApi, lls llsclient.LlsApi) *LaneServiceImpl {
	return &LaneServiceImpl{
		lfsApi: lfs,
		llsApi: lls,
	}
}

func (l *LaneServiceImpl) GetLaneInfoByLaneCode(ctx context.Context, laneCode string) (*lane_entity.LaneInfo, *srerr.Error) {
	data, err := localcache.Get(ctx, constant.LaneInfo, laneCode)
	if err != nil {
		return nil, srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), err)
	}
	return data.(*lane_entity.LaneInfo), nil
}

func (l LaneServiceImpl) BatchGetLaneInfoWithoutCache(ctx context.Context, laneCodes []string) (map[string]*lane_entity.LaneInfo, *srerr.Error) {
	result := make(map[string]*lane_entity.LaneInfo)
	lfsRspItems, err := l.lfsApi.BatchGetLaneInfo(ctx, laneCodes)
	if err != nil {
		return nil, err
	}
	for _, lfsLaneInfoItem := range lfsRspItems {
		lineIDList := make([]string, 0)
		siteIDList := make([]string, 0)
		if lfsLaneInfoItem.GetErrcode() != 0 {
			logger.CtxLogErrorf(ctx, "BatchQueryLaneInfo failed|laneCode=%s,err=%s", lfsLaneInfoItem.GetLaneCode(), lfsLaneInfoItem.GetMessage())
			continue
		}
		laneInfo := &lane_entity.LaneInfo{
			Lines:          map[string]*lane_entity.LineInfo{},
			Sites:          map[string]*lane_entity.SiteInfo{},
			LaneFormatType: lane_entity.LaneFormatType(lfsLaneInfoItem.GetLaneFormatType()),
		}
		modelRules := getModelRulesInEachLane(ctx, lfsLaneInfoItem)
		for _, laneCompose := range lfsLaneInfoItem.GetComposes() {
			switch laneCompose.GetResourceType() {
			case lfslib.ResourceTypeLine:
				if laneCompose.GetResourceId() == lfslib.DummyFirstMileId {
					continue
				}
				lineIDList = append(lineIDList, laneCompose.GetResourceId())
				laneInfo.Lines[laneCompose.GetResourceId()] = &lane_entity.LineInfo{
					LineID:    laneCompose.GetResourceId(),
					LineName:  laneCompose.GetResourceName(),
					MainType:  int(laneCompose.GetMainType()),
					SubType:   int(laneCompose.GetSubType()),
					Sequence:  int(laneCompose.GetSequence()),
					ModelRule: modelRules[laneCompose.GetSequence()],
				}
			case lfslib.ResourceTypeSite:
				siteIDList = append(siteIDList, laneCompose.GetResourceId())
				laneInfo.Sites[laneCompose.GetResourceId()] = &lane_entity.SiteInfo{
					SiteID:    laneCompose.GetResourceId(),
					SiteName:  laneCompose.GetResourceName(),
					MainType:  int(laneCompose.GetMainType()),
					SubType:   int(laneCompose.GetSubType()),
					Sequence:  int(laneCompose.GetSequence()),
					ModelRule: modelRules[laneCompose.GetSequence()],
				}
			}
		}
		llsResp, err := l.llsApi.MultiGetResource(ctx, lineIDList, siteIDList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "MultiGetResource failed|laneCode=%s,err=%v", lfsLaneInfoItem.GetLaneCode(), err)
			return nil, err
		}
		if llsResp.GetRespHeader().GetRetcode() != 0 {
			logger.CtxLogErrorf(ctx, "MultiGetResource failed|laneCode=%s,err=%v", lfsLaneInfoItem.GetLaneCode(), llsResp.GetRespHeader().GetMessage())
			continue
		}
		for _, llsLine := range llsResp.GetLines() {
			lineInfo, exist := laneInfo.Lines[llsLine.GetLineId()]
			if !exist {
				continue
			}
			lineInfo.FromRegion = llsLine.GetOriginRegion()
			lineInfo.ToRegion = llsLine.GetDestinationRegion()
		}
		for _, llsSite := range llsResp.GetSites() {
			siteInfo, exist := laneInfo.Sites[llsSite.GetSiteId()]
			if !exist {
				continue
			}
			siteInfo.SiteCode = llsSite.GetSiteCode()
			siteInfo.VirtualTwsGroupSite = llsSite.GetVirtualTwsGroupSite()
			siteInfo.WhsCodes = llsSite.GetWhsCodes()
		}
		result[lfsLaneInfoItem.GetLaneCode()] = laneInfo
	}

	return result, nil
}

func (l *LaneServiceImpl) BatchGetLaneInfoByLaneCode(ctx context.Context, laneCodes []string) ([]*lane_entity.LaneInfo, *srerr.Error) {
	res := make([]*lane_entity.LaneInfo, 0)
	for _, laneCode := range laneCodes {
		data, err := localcache.Get(ctx, constant.LaneInfo, laneCode)
		if err != nil {
			continue
		}
		laneInfo := data.(*lane_entity.LaneInfo)
		res = append(res, laneInfo)
	}
	return res, nil
}

func (l *LaneServiceImpl) GetLineIdToLineNameMap(ctx context.Context) (map[string]string, map[string]*lane_entity.LineInfo) {
	ret := make(map[string]string)
	lineIdInfoMap := make(map[string]*lane_entity.LineInfo)
	allLaneInfos := localcache.AllItems(ctx, constant.LaneInfo)
	for _, data := range allLaneInfos {
		laneInfo := data.(*lane_entity.LaneInfo)
		for lineId, lineInfo := range laneInfo.Lines {
			ret[lineId] = lineInfo.LineName
			lineIdInfoMap[lineId] = lineInfo
		}
	}

	return ret, lineIdInfoMap
}

func (l *LaneServiceImpl) GetLineSubTypeMapByLanes(laneInfos []*lane_entity.LaneInfo, roleMap map[int]int) map[int][]*lane_entity.LineInfo {
	checkDul := make(map[string]struct{})
	ret := make(map[int][]*lane_entity.LineInfo)
	for _, laneInfo := range laneInfos {
		for _, lineInfo := range laneInfo.Lines {
			if _, exist := checkDul[lineInfo.LineID]; exist {
				continue
			}

			// replace sub-type if config routing role map contains it
			subType := lineInfo.SubType
			if routingSubType, ok := roleMap[subType]; ok {
				subType = routingSubType
			}

			ret[subType] = append(ret[subType], lineInfo)
			checkDul[lineInfo.LineID] = struct{}{}
		}
	}

	return ret
}

// IsResourceNeedDg 判断某个特定类型Line在Lane中是否带有DG属性
func (l *LaneServiceImpl) IsResourceNeedDg(ctx context.Context, laneCode string, laneCodeGroup []string, resourceSubType uint32) bool {
	// 先用旧的固定ILH以及FL的Line类型的方式进行判断
	if resourceSubType == lfslib.C_FL || objutil.ContainsInt(lfslib.NeedRoutingILH, int(resourceSubType)) {
		return true
	}

	// 如果是Lane Code Group就拆分开单个Lane去查（这时候Lane Code是拼接起来的，非独立Lane）
	if len(laneCodeGroup) > 0 {
		for _, c := range laneCodeGroup {
			if l.isResourceNeedDgInLane(ctx, c, resourceSubType) {
				return true
			}
		}

		return false
	}

	// 非Lane Code Group的情况直接用Lane去查
	return l.isResourceNeedDgInLane(ctx, laneCode, resourceSubType)
}

// isResourceNeedDgInLane 判断某个特定类型Line在Lane中是否带有DG属性
func (l *LaneServiceImpl) isResourceNeedDgInLane(ctx context.Context, laneCode string, resourceSubType uint32) bool {
	// 1. 先获取Lane信息
	data, err := localcache.Get(ctx, constant.LaneInfo, laneCode)
	if err != nil {
		logger.CtxLogErrorf(ctx, "SmartRoutingForecastServiceImpl.isResourceNeedDgInLane fail | err=%v", srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), err))
		return false
	}

	laneInfo, ok := data.(*lane_entity.LaneInfo)
	if !ok {
		logger.CtxLogErrorf(ctx, "SmartRoutingForecastServiceImpl.isResourceNeedDgInLane fail | err= type error")
		return false
	}

	// 2. 搜寻该类型的Line在Lane的配置信息
	lineInfo := laneInfo.GetLineBySubType(int32(resourceSubType))
	if lineInfo != nil && lineInfo.ModelRule != nil && lineInfo.NeedDG() {
		return true
	}

	return false
}
