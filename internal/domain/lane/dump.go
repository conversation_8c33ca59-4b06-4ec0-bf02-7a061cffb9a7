package lane

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

func DumpLaneInfo() (map[string]interface{}, error) {
	// map[LaneCode]LaneInfo
	dumpData := make(map[string]interface{})

	// init client
	lpsClient := lpsclient.NewLpsApiImpl()
	lfsClient := lfsclient.NewLfsApiImpl()
	llsClient := llsclient.NewLlsApiImpl()
	ctx := context.Background()

	// get all lane code
	productLaneCodes, err := lpsClient.GetLaneCodes(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetLaneCodes failed|err=%s", err)
		return nil, err
	}
	laneCodeList := make([]string, 0)
	for _, productLaneCode := range productLaneCodes {
		laneCodeList = append(laneCodeList, productLaneCode.LaneCodes...)
	}

	// query all lane info from LFS
	lfsResp, err := lfsClient.BatchGetLaneInfo(ctx, laneCodeList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchQueryLaneInfo failed|err=%s", err)
		return nil, err
	}

	for _, lfsLaneInfoItem := range lfsResp {
		lineIDList := make([]string, 0)
		siteIDList := make([]string, 0)
		if lfsLaneInfoItem.GetErrcode() != 0 {
			logger.CtxLogErrorf(ctx, "BatchQueryLaneInfo failed|laneCode=%s,err=%s", lfsLaneInfoItem.GetLaneCode(), lfsLaneInfoItem.GetMessage())
			continue
		}
		laneInfo := &lane_entity.LaneInfo{
			Lines:          make(map[string]*lane_entity.LineInfo),
			Sites:          make(map[string]*lane_entity.SiteInfo),
			LaneFormatType: lane_entity.LaneFormatType(lfsLaneInfoItem.GetLaneFormatType()),
		}
		modelRules := getModelRulesInEachLane(ctx, lfsLaneInfoItem)
		resourceMap := make(map[int]string) // Sequence:ResourceID
		for _, laneCompose := range lfsLaneInfoItem.GetComposes() {
			switch laneCompose.GetResourceType() {
			case lfslib.ResourceTypeLine:
				if laneCompose.GetResourceId() == lfslib.DummyFirstMileId {
					continue
				}
				lineIDList = append(lineIDList, laneCompose.GetResourceId())
				laneInfo.Lines[laneCompose.GetResourceId()] = &lane_entity.LineInfo{
					LineID:    laneCompose.GetResourceId(),
					LineName:  laneCompose.GetResourceName(),
					MainType:  int(laneCompose.GetMainType()),
					SubType:   int(laneCompose.GetSubType()),
					Sequence:  int(laneCompose.GetSequence()),
					ModelRule: modelRules[laneCompose.GetSequence()],
				}
			case lfslib.ResourceTypeSite:
				siteIDList = append(siteIDList, laneCompose.GetResourceId())
				laneInfo.Sites[laneCompose.GetResourceId()] = &lane_entity.SiteInfo{
					SiteID:    laneCompose.GetResourceId(),
					SiteName:  laneCompose.GetResourceName(),
					MainType:  int(laneCompose.GetMainType()),
					SubType:   int(laneCompose.GetSubType()),
					Sequence:  int(laneCompose.GetSequence()),
					ModelRule: modelRules[laneCompose.GetSequence()],
				}
				laneInfo.Sites[laneCompose.GetResourceId()].ActualPointMap = make(map[string]*lane_entity.ActualPointInfo, 0)
			}
			resourceMap[int(laneCompose.GetSequence())] = laneCompose.GetResourceId()
		}
		// build line link site
		for _, line := range laneInfo.Lines {
			// sequence start from 1
			// first line has no last site
			if line.Sequence != 1 {
				line.LastSite = laneInfo.Sites[resourceMap[line.Sequence-1]]
			}
			// last line has no next site
			if line.Sequence != len(laneInfo.Lines)+len(laneInfo.Sites) {
				line.NextSite = laneInfo.Sites[resourceMap[line.Sequence+1]]
			}
		}

		// get resource detail info from LLS
		llsResp, err := llsClient.MultiGetResource(ctx, lineIDList, siteIDList)
		if err != nil {
			logger.CtxLogErrorf(ctx, "MultiGetResource failed|laneCode=%s,lineID=%+v,siteID=%+v,err=%v",
				lfsLaneInfoItem.GetLaneCode(), lineIDList, siteIDList, err)
			continue
		}
		if llsResp.GetRespHeader().GetRetcode() != 0 {
			logger.CtxLogErrorf(ctx, "MultiGetResource failed|laneCode=%s,err=%v", lfsLaneInfoItem.GetLaneCode(), llsResp.GetRespHeader().GetMessage())
			continue
		}
		for _, llsLine := range llsResp.GetLines() {
			lineInfo, exist := laneInfo.Lines[llsLine.GetLineId()]
			if !exist {
				continue
			}
			lineInfo.FromRegion = llsLine.GetOriginRegion()
			lineInfo.ToRegion = llsLine.GetDestinationRegion()
		}
		for _, llsSite := range llsResp.GetSites() {
			siteInfo, exist := laneInfo.Sites[llsSite.GetSiteId()]
			if !exist {
				continue
			}
			siteInfo.SiteCode = llsSite.GetSiteCode()
			siteInfo.VirtualTwsGroupSite = llsSite.GetVirtualTwsGroupSite()
			siteInfo.WhsCodes = llsSite.GetWhsCodes()
		}

		//1.根据site id获取对应的actual point信息
		actualPointReq := &llspb.GetActualPointRequest{
			SiteIdList: siteIDList,
		}
		actualPointResp, aErr := llsClient.GetActualPoints(ctx, actualPointReq)
		isAcPointExist := true //用来兼容老代码，实际点不存在时，不往siteInfo中写入数据
		if aErr != nil {
			logger.CtxLogErrorf(ctx, "DumpLaneInfo|site id list:%+v, Calling GetActualPoints from lls err:%v", siteIDList, aErr)
			isAcPointExist = false
			continue
		}
		//2.判空
		if actualPointResp.Data == nil {
			logger.CtxLogInfof(ctx, "DumpLaneInfo|site id list:%+v, Calling GetActualPoints from lls, data is nil", siteIDList)
			isAcPointExist = false
			continue
		}
		//3.把lls的返回转换成lps内部的actual point结构体，并存在对应siteInfo内
		if isAcPointExist {
			logger.CtxLogInfof(ctx, "DumpLaneInfo| actual point list from lls, list length:%v", len(actualPointResp.Data.List))
			for _, element := range actualPointResp.Data.List {
				logger.CtxLogInfof(ctx, "DumpLaneInfo| get actual point from lls, actual point:%+v", element)
				//组装结构体
				actualPoint := lane_entity.ActualPointInfo{
					ActualPointID:      element.GetActualPointId(),
					SiteID:             element.GetSiteId(),
					ActualPointName:    element.GetActualPointName(),
					Region:             element.GetRegion(),
					StateLocationID:    element.GetStateLocationId(),
					CityLocationID:     element.GetCityLocationId(),
					DistrictLocationID: element.GetDistrictLocationId(),
					StreetLocationID:   element.GetStreetLocationId(),
					Longitude:          element.GetLongitude(),
					Latitude:           element.GetLatitude(),
					ZipCode:            element.GetZipCode(),
					LocationID:         element.GetLocationId(),
				}
				//写入siteInfo中
				siteInfo, exist := laneInfo.Sites[element.GetSiteId()]
				if !exist {
					logger.CtxLogErrorf(ctx, "DumpLaneInfo| Writing actual point info into site info, site id:%v is not existed", element.GetSiteId())
					continue
				}
				siteInfo.ActualPointMap[element.GetActualPointId()] = &actualPoint
			}
		}

		dumpData[lfsLaneInfoItem.GetLaneCode()] = laneInfo
	}

	return dumpData, nil
}

// getModelRulesInEachLane 获取每个laneCode的模板信息，以Map[Sequence]ModelRule 的形式存储提升获取速度
// Sequence的含义：代表 每个lane中 site-line-site 每个site/line的序号，按照Sequence从小到大连接成一整条点线链路
func getModelRulesInEachLane(ctx context.Context, lfsLaneInfoItem *lfspb.BatchLaneInfoRspItem) map[uint32]*lfspb.ModelRule {
	modelRulesMapper := make(map[uint32]*lfspb.ModelRule)
	for _, sequenceInfo := range lfsLaneInfoItem.GetModelRules() {
		modelRulesMapper[sequenceInfo.GetSequence()] = sequenceInfo
	}
	// 如果没有配置modelRule规则进行错误上报
	if len(modelRulesMapper) == 0 {
		logger.CtxLogErrorf(ctx, "getModelRulesInEachLane with empty model rule, laneCode=%v", lfsLaneInfoItem.GetLaneCode())
	}

	return modelRulesMapper
}
