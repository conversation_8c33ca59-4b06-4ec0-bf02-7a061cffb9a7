package model

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/bytedance/sonic"
	"strconv"
	"strings"
)

const (
	defaultSaltSize   = 1000
	defaultResultSize = 1000
)

type OrderCollector interface {
	CollectOrdersFixedQuantity(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error)
	CollectOrdersFixedTime(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error)
	CollectOrdersFixedTimeQuantity(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error)
	ConvertStartKey(ctx context.Context, startKey string) (string, int64)
}

type OrderCollectorImpl struct {
	AddrRepo address.AddrRepo
}

func NewOrderCollectorImpl(
	AddrRepo address.AddrRepo) *OrderCollectorImpl {
	return &OrderCollectorImpl{
		AddrRepo: AddrRepo,
	}
}

type CollectCondition struct {
	MaskProductId uint64 `json:"mask_product_id"`
	//todo:SSCSMR-1698:
	StartTimeUnix int64 `json:"start_time_unix"` //fix by quantity; fix by time 都使用|历史订单row key对应的起始时间
	EndTimeUnix   int64 `json:"end_time_unix"`   //只有fix by time使用|历史订单row key对应的结束时间

	FixedQuantity int64 `json:"fixed_quantity"` //fix by quantity; fix by time 都使用; 满足fix quantity就返回

	NextStartKey string `json:"next_start_key"` //fix by quantity; fix by time 都使用; 下一次检索时用的start key
}

func (o *OrderCollectorImpl) CollectOrdersFixedQuantity(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	if collectType == ModuleBAForecast {
		return o.CollectForecastOrdersByCount(ctx, collectCondition)
	}
	if collectType == ModuleBAOnLine {
		return o.CollectOnlineOrdersByCount(ctx, collectCondition)
	}
	logger.CtxLogErrorf(ctx, "CollectOrdersFixedQuantity|wrong collect type:%v", collectType)
	return nil, srerr.New(srerr.ParamErr, nil, "CollectOrdersFixedQuantity|wrong collect type")
}

func (o *OrderCollectorImpl) CollectOrdersFixedTime(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	if collectType == ModuleBAForecast {
		return o.CollectForecastOrdersByTime(ctx, collectCondition)
	}
	if collectType == ModuleBAOnLine {
		return o.CollectOnlineOrdersByTime(ctx, collectCondition)
	}
	logger.CtxLogErrorf(ctx, "CollectOrdersFixedTime|wrong collect type:%v", collectType)
	return nil, srerr.New(srerr.ParamErr, nil, "CollectOrdersFixedTime|wrong collect type")
}

func (o *OrderCollectorImpl) CollectOrdersFixedTimeQuantity(ctx context.Context, collectType int, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	//调用collect by time，区别在于此处condition.FixedQuantity不为0
	results, cErr := o.CollectOrdersFixedTime(ctx, collectType, collectCondition)
	return results, cErr
}

// 按照固定数量检索
func (o *OrderCollectorImpl) CollectForecastOrdersByCount(ctx context.Context, condition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	//根据condition准备检索条件
	var (
		startTimeUnix, endTimeUnix int64
		salt                       string
		err                        error
		startRowKey, endRowKey     string
	)
	//应用start key
	salt, startTimeUnix = o.ConvertStartKey(ctx, condition.NextStartKey)
	logger.CtxLogInfof(ctx, "CollectForecastOrdersByCount|next start key:%v, salt:%v, start time unix:%v", condition.NextStartKey, salt, startTimeUnix)

	if startTimeUnix == 0 {
		//start key为空，使用起始的Unix time
		startTimeUnix = condition.StartTimeUnix
		endTimeUnix = timeutil.GetNextDayStartTimeByTimeStamp(startTimeUnix, 1)
		startRowKey = fmt.Sprintf("000_%v_%v", condition.MaskProductId, startTimeUnix)
		endRowKey = fmt.Sprintf("000_%v_%v", condition.MaskProductId, endTimeUnix)
	} else {
		startRowKey = condition.NextStartKey
		endTimeUnix = timeutil.GetNextDayStartTimeByTimeStamp(startTimeUnix, 1)
		endRowKey = fmt.Sprintf("%v_%v_%v", salt, condition.MaskProductId, endTimeUnix)
	}

	//检索hbase获取数据
	costTimeStart := timeutil.GetCurrentUnixTimeStamp(ctx)
	results, err := o.CollectFromHbaseByCount(ctx, condition.MaskProductId, startRowKey, endRowKey, int(condition.FixedQuantity))
	if err != nil {
		logger.CtxLogErrorf(ctx, "CollectForecastOrdersByCount|mask product id:%v, start time unix:%v, end time unix:%v, get from hbase err:%v", condition.MaskProductId, startTimeUnix, endTimeUnix, err)
		return nil, srerr.With(srerr.ScanHbaseError, nil, err)
	}
	logger.CtxLogInfof(ctx, "get from hbase cost time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-costTimeStart)
	//转换成order bo
	costTimeStart = timeutil.GetCurrentUnixTimeStamp(ctx)
	entities := o.convertToOrderEntity(ctx, results, condition.MaskProductId)
	logger.CtxLogInfof(ctx, "convert orders cost time:%v|length of entities:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-costTimeStart, len(entities))
	return entities, nil
}

func (o *OrderCollectorImpl) CollectOnlineOrdersByCount(ctx context.Context, collectCondition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	return nil, nil
}

func (o *OrderCollectorImpl) CollectFromHbaseByCount(ctx context.Context, maskProductId uint64, startRowKey, endRowKey string, countSize int) ([]*gohbase.Result, error) {
	mainHbHelper := masking_forecast_hbase.NewMainHBHelper()
	mainHbConf := configutil.GetMainHbaseConfig(ctx)
	if countSize == 0 {
		countSize = defaultResultSize
	}
	var (
		tableName      = mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
		err            error
		results        []*gohbase.Result
		tempResultSize = countSize
	)
	//按天在hbase检索数据
	//定义检索的region，开始key，结束key
	logger.CtxLogInfof(ctx, "CollectFromHbaseByKey|table name:%v, start row key:%v, end row key:%v", tableName, startRowKey, endRowKey)
	conf := configutil.GetBatchAllocateForecastConf()
	//SSCSMR-1480:可配置，其中burst配置成跟外层的workers一样的参数
	rateLimiter := &hbaseutil.RateLimiter{
		Limit: conf.RateLimit,
		Burst: conf.RateBurst,
	}
	//根据start，end key，temp result size去检索
	results, err = mainHbHelper.ScanWithResultSize(ctx, tableName, startRowKey, endRowKey, tempResultSize, rateLimiter)
	if err != nil {
		logger.CtxLogErrorf(ctx, "CollectFromHbaseByKey|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
		return nil, err
	}
	//检索满了，返回
	logger.CtxLogInfof(ctx, "CollectFromHbaseByKey|last|length of results:%v|fix quantity:%v", len(results), countSize)
	return results, nil
}

func (o *OrderCollectorImpl) CollectForecastOrdersByTime(ctx context.Context, condition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	//1.准备参数
	var (
		startTimeUnix = condition.StartTimeUnix
		endTimeUnix   = condition.EndTimeUnix
	)
	//2.调用hbase collector，检索数据
	hbResults, err := o.CollectFromHbase(ctx, condition.MaskProductId, startTimeUnix, endTimeUnix, int(condition.FixedQuantity))
	if err != nil {
		logger.CtxLogErrorf(ctx, "CollectForecastOrdersByTime|mask product id:%v, start time unix:%v, end time unix:%v, collect from hbase err:%v", condition.MaskProductId, startTimeUnix, endTimeUnix, err)
		return nil, srerr.With(srerr.ScanHbaseError, nil, err)
	}
	//3.转换模型
	entities := o.convertToOrderEntity(ctx, hbResults, condition.MaskProductId)
	//4.返回
	return entities, nil
}

func (o *OrderCollectorImpl) CollectFromHbase(ctx context.Context, maskProductId uint64, startTime, endTime int64, resultSize int) ([]*gohbase.Result, error) {
	mainHbHelper := masking_forecast_hbase.NewMainHBHelper()
	mainHbConf := configutil.GetMainHbaseConfig(ctx)
	var (
		tableName            = mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
		saltSize             = mainHbConf.SaltSize
		err                  error
		results, tempResults []*gohbase.Result
	)
	if saltSize == 0 {
		saltSize = defaultSaltSize
	}
	conf := configutil.GetBatchAllocateForecastConf()
	rateLimiter := &hbaseutil.RateLimiter{
		Limit: conf.RateLimit,
		Burst: conf.RateBurst,
	}
	//按天在hbase检索数据
	//salt是按request time整除3位数取余得到，因此遍历0～999相当于全表检索
	for i := 0; i < saltSize; i++ {
		//定义检索的region，开始key，结束key
		salt := i
		startRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, startTime)
		endRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, endTime+1)

		logger.CtxLogInfof(ctx, "CollectFromHbase| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

		//根据start，end key，temp result size去检索
		tempResults, err = mainHbHelper.ScanWithResultSize(ctx, tableName, startRowKey, endRowKey, resultSize, rateLimiter)
		if err != nil {
			logger.CtxLogErrorf(ctx, "CollectFromHbase|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
			continue
		}
		results = append(results, tempResults...)
	}
	return results, nil
}

func (o *OrderCollectorImpl) CollectOnlineOrdersByTime(ctx context.Context, condition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	return nil, nil
}

func (o *OrderCollectorImpl) CollectFOsByTimeQuantity(ctx context.Context, condition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	return o.CollectForecastOrdersByTime(ctx, condition)
}

func (o *OrderCollectorImpl) CollectOnlineOrdersByTimeQuantity(ctx context.Context, condition CollectCondition) ([]BatchAllocateOrderDataEntity, *srerr.Error) {
	return nil, nil
}

func (o *OrderCollectorImpl) ConvertStartKey(ctx context.Context, startKey string) (string, int64) {
	if startKey == "" {
		return "", 0
	}
	//拆解start key出来
	//start key := fmt.Sprintf("%s_%d_%s_%s", salt, maskProductId, keyTime, requestId)
	startKeyUnits := strings.Split(startKey, "_")
	if len(startKeyUnits) == 4 || len(startKeyUnits) == 3 {
		logger.CtxLogInfof(ctx, "CollectForecastOrdersByCount|split units:%v", startKeyUnits)
		//转换int64
		startTimeUnix, err := strconv.ParseInt(startKeyUnits[2], 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "CollectForecastOrdersByCount|parse start key:%v, err:%v", startKeyUnits[2], err)
			return "", 0
		}
		return startKeyUnits[0], startTimeUnix
	}
	return "", 0
}

// 转换hbase result to order entity
func (o *OrderCollectorImpl) convertToOrderEntity(ctx context.Context, hbResults []*gohbase.Result, maskProductId uint64) []BatchAllocateOrderDataEntity {
	var baEntities []BatchAllocateOrderDataEntity
	duplicatedOrder := make(map[uint64]struct{}, 0)
	for _, hbResult := range hbResults {
		//convert to tab
		entity, tErr := o.TransferToHbaseEntity(ctx, hbResult)
		if tErr != nil {
			logger.CtxLogErrorf(ctx, "OrderCollectorImpl|convert to tab err:%v", tErr)
			continue
		}
		//订单去重
		if _, ok := duplicatedOrder[entity.OrderId]; ok {
			continue
		}
		if entity.HardResult == nil {
			continue
		}
		if uint64(entity.MaskingProductID) != maskProductId {
			logger.CtxLogInfof(ctx, "order id:%v, order mask product id:%v, not match mask:%v, continue", entity.OrderId, entity.MaskingProductID, maskProductId)
			continue
		}
		duplicatedOrder[entity.OrderId] = struct{}{}
		baEntities = append(baEntities, entity)
	}
	return baEntities
}

// 转换成batch allocate order data entity
func (o *OrderCollectorImpl) TransferToHbaseEntity(ctx context.Context, result *gohbase.Result) (BatchAllocateOrderDataEntity, *srerr.Error) {
	var emptyEntity BatchAllocateOrderDataEntity
	//转换数据（包括解压+模型转换）
	if result == nil {
		logger.CtxLogErrorf(ctx, "TransferToHbaseEntity|cannot parse nil hbase result, will skip it")
		return emptyEntity, srerr.New(srerr.ParamErr, nil, "result is nil, can't convert it")
	}
	//转换成hbase entity
	resultEntity := AllocationHbaseEntity{}
	//此处实际v只会有一个，for循环的模式能避免result.Cells[0]空指针panic
	var rowKey string
	for _, v := range result.Cells {
		key := string(v.Qualifier[:])
		decodeBytes, err := zip.ZSTDDecompress(v.Value)
		if err != nil {
			logger.CtxLogErrorf(ctx, "TransferToHbaseEntity|key:%v, decode value err:%v", key, err)
			continue
		}
		value := string(decodeBytes)
		if err := sonic.Unmarshal(decodeBytes, &resultEntity); err != nil {
			logger.CtxLogDebugf(ctx, "TransferToHbaseEntity|value:%v, unmarshal value err:%v", value, err)
		}
		rowKey = string(v.Row)
	}
	if resultEntity.AllocationScenario == int(smart_routing_protobuf.AllocationScenario_ReturnMaskingAllocation) {
		return emptyEntity, srerr.New(srerr.ParamErr, nil, "no need to forecast return allocate")
	}
	if len(resultEntity.HardOutput) == 0 {
		logger.CtxLogErrorf(ctx, "TransferToHbaseEntity|order id:%v, got empty hard output", resultEntity.OrderId)
	}
	//转换request
	baOrderEntity := BatchAllocateOrderDataEntity{
		StartKey:             rowKey,
		OrderId:              resultEntity.OrderId,
		MaskingProductID:     resultEntity.MaskProductId,
		FulfillmentProductID: resultEntity.FulfillmentProductId,
		HardResult: &ValidateResult{
			ProductId: resultEntity.HardOutput,
		},
		HistoricalShippingFees: resultEntity.ShippingFeeList,
		ProductParcelInfoList:  resultEntity.ProductParcelInfoList,
		RequestTime:            resultEntity.RequestTime,
	}
	request := &OmsAllocRequest{}
	if err := sonic.UnmarshalString(resultEntity.RequestDataStr, request); err != nil { //报错，返回
		//logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to schema err:%v", resultEntity.OrderId, err)
		return emptyEntity, srerr.With(srerr.DataErr, nil, err)
	} else {
		allocReq, cErr := o.ConvertOmsAllocRequest(ctx, request)
		if cErr != nil { //报错，返回
			//logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to OmsAllocRequest err:%v", resultEntity.OrderId, cErr)
			return emptyEntity, cErr
		} else {
			baOrderEntity.OmsAllocateRequest = allocReq
		}
	}
	return baOrderEntity, nil
}

// 步骤2：组装oms allocate request
func (o *OrderCollectorImpl) ConvertOmsAllocRequest(ctx context.Context, originReq *OmsAllocRequest) (*OMSAllocateRequest, *srerr.Error) {
	// https://jira.shopee.io/browse/SPLPS-1972
	// Store Order Snapshot of Fulfilment Channel that Pass Hard Rule
	var snapshot *SnapShot
	if originReq.Snapshot != nil {
		snapshot = &SnapShot{IgnoreSnapshot: originReq.Snapshot.IgnoreSnapshot, ShippingChannels: originReq.Snapshot.ShippingChannels}
	} else {
		snapshot = nil
	}

	forderID := originReq.FOrderID
	if originReq.FOrderIDStr != nil {
		forderID, _ = strconv.ParseUint(*originReq.FOrderIDStr, 10, 64)
	}
	var allocReq = &OMSAllocateRequest{
		MaskingProductID:           originReq.MaskingChannelId,
		PaymentMethod:              originReq.PaymentMethod,
		TotalPrice:                 originReq.TotalPrice,
		CodAmount:                  originReq.CodAmount,
		Cogs:                       originReq.Cogs,
		PartialFulfillment:         0,
		IsWms:                      originReq.IsWms,
		WhsId:                      originReq.WhsId,
		CheckoutItems:              originReq.ConvertCheckoutItem(),
		FOrderID:                   forderID,
		OrderID:                    originReq.OrderID,
		BuyerPaidShippingFee:       originReq.BuyerPaidShippingFee,
		SellerTaxNumber:            originReq.SellerTaxNumber,
		StateRegistration:          originReq.StateRegistration,
		Snapshot:                   snapshot,
		IsCacheAfterBuyerSelection: originReq.IsCacheAfterBuyerSelection,
	}
	var pickupInfo, deliverInfo *ordentity.AddressInfo
	if originReq.PickupInfo != nil && originReq.DeliveryInfo != nil {
		pickupInfo, deliverInfo = originReq.ConvertAddressInfo()
	}
	err := o.FillLocationIds(ctx, pickupInfo)
	if err != nil {
		return nil, err
	}
	err = o.FillLocationIds(ctx, deliverInfo)
	if err != nil {
		return nil, err
	}
	allocReq.PickupInfo = pickupInfo
	allocReq.DeliveryInfo = deliverInfo
	if originReq.PartialFulfillment {
		allocReq.PartialFulfillment = 1
	}
	return allocReq, nil
}

// 步骤3：装填location id
func (o *OrderCollectorImpl) FillLocationIds(ctx context.Context, info *ordentity.AddressInfo) *srerr.Error {
	// 已经存在location id不用转换
	if info != nil && info.StateLocationId != nil && *info.StateLocationId > 0 {
		return nil
	}
	if info == nil || info.State == nil || info.City == nil {
		return srerr.New(srerr.ParamErr, nil, "missing location core information[state, city]")
	}
	locInfo, err := o.AddrRepo.GetLocationByLocFullPathName(ctx, info.GetCountry(), info.GetState(), info.GetCity(), info.GetDistrict(), info.GetStreet())
	if err != nil {
		return err
	}
	//装填 location id
	var stateLocationId = int(locInfo.GetStateLocId())
	info.StateLocationId = &stateLocationId
	var cityLocationId = int(locInfo.GetCityLocId())
	info.CityLocationId = &cityLocationId
	locIds := []int{stateLocationId, cityLocationId}
	if locInfo.GetDistrictLocId() > 0 {
		var districtLocationId = int(locInfo.GetDistrictLocId())
		info.DistrictLocationId = &districtLocationId
		locIds = append(locIds, districtLocationId)
	}
	if locInfo.GetStreetLocId() > 0 {
		var streetLocationId = int(locInfo.GetStreetLocId())
		info.StreetLocationId = &streetLocationId
		locIds = append(locIds, streetLocationId)
	}
	info.LocationIDs = locIds
	return nil
}
