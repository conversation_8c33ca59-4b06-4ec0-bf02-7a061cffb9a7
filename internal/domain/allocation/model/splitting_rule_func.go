package model

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/warning"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"runtime"
	"strconv"
	"strings"
	"sync"
)

const (
	panicMsgSize = 4096
)

type SplittingRuleServer interface {
	SplitBatchesByType(ctx context.Context, rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) (chan []BatchAllocateOrderDataEntity, *srerr.Error)
}

type SplittingRuleImpl struct {
	OrderCollector OrderCollector
}

func NewSplittingRuleImpl(OrderCollector OrderCollector) *SplittingRuleImpl {
	return &SplittingRuleImpl{
		OrderCollector: OrderCollector,
	}
}

func (s *SplittingRuleImpl) SplitBatchesByType(ctx context.Context, rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) (chan []BatchAllocateOrderDataEntity, *srerr.Error) {
	switch rule.BatchSizeType {
	case SplittingByQuantity:
		return s.splitByQuantity(ctx, rule, holdingTimeMap, shouldClose, splitModuleType, collectCondition)
	case SplittingByTime:
		return s.splitByTime(ctx, rule, holdingTimeMap, shouldClose, splitModuleType, collectCondition)
	case SplittingByQAndT:
		return s.splitByQuantityAndTime(ctx, rule, holdingTimeMap, shouldClose, splitModuleType, collectCondition)
	}
	return nil, srerr.New(srerr.ParamErr, nil, "wrong splitting type")
}

//todo:SSCSMR-1698: 先按quantity捞满，捞完一次性去重，不够再找补，此时找补的数量捞多点，然后再补（避免有重复订单导致捞取不够）
//根据固定数量拆分订单
func (s *SplittingRuleImpl) splitByQuantity(ctx context.Context, rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) (chan []BatchAllocateOrderDataEntity, *srerr.Error) {
	/*
		1.根据拆分模式获取数据源，区分"实时batch allocate" 和 "batch allocate forecast"
		2.不断地追加数据，数据满足，返回批次；不满足，继续追加
	*/

	//使用channel传递数据，对于forecast来说，可以一批一批的传；对于实时allocate来说，可以在外边等channel close后一并处理
	size := configutil.GetBatchAllocateForecastConf().ChannelSize
	splitResultsChan := make(chan []BatchAllocateOrderDataEntity, size)

	go func(rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) {
		defer func() {
			logger.CtxLogInfof(ctx, "splitByQuantity| spilt result chan closed")
			close(splitResultsChan)
		}()
		//panic兜底
		defer func() {
			if err := recover(); err != nil {
				shouldClose.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery from splitByTime] panic recovered:\n%v\n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		var (
			batchCount  int64                          //每批的订单数量
			splitOrders []BatchAllocateOrderDataEntity //暂存当前批次检索到的数据
			//todo:SSCSMR-1698: 第二个batch开始，每个batch的第一条丢掉，避免多捞数据
			nextStartKey     string                         //一次检索不满，需要这个标志位，标识下一次开始的地方
			duplicatedOrders = make(map[uint64]struct{}, 0) //去重 todo:SSCSMR-1698:可以用nil替换空struct
			batchNo          uint64                         //批次号
		)
		conf := configutil.GetBatchAllocateForecastConf()
		loopTimes := 0
		originTimeStamp := collectCondition.StartTimeUnix
		startTime := timeutil.GetCurrentUnixTimeStamp(ctx)
		for loopTimes < conf.MaxLoopSize { //SSCSMR-1698:设置防呆兜底次数
			if shouldClose.Get() {
				break
			}
			//如果next key达到第二天，break
			//todo:SSCSMR-1698: 如果检索到的数据有第二天的，需要过滤掉
			if needBreak(ctx, originTimeStamp, nextStartKey) {
				//将检索到的数据塞到管道，没检索到则不塞
				logger.CtxLogInfof(ctx, "start key need break")
				break
			}
			//1.按固定数量，指定start key，检索db
			logger.CtxLogInfof(ctx, "splitByQuantity|loop times:%v|next start key before collect:%v, not in:%v", loopTimes, collectCondition.NextStartKey, nextStartKey)
			collectCondition.FixedQuantity = rule.FixedQuantity
			collectCondition.NextStartKey = nextStartKey
			orders, gErr := s.OrderCollector.CollectOrdersFixedQuantity(ctx, splitModuleType, collectCondition)
			if gErr != nil {
				logger.CtxLogErrorf(ctx, "splitByQuantity|loop times:%v|get orders err:%v, split module type:%v, fixed quantity:%v", loopTimes, gErr, splitModuleType, rule.FixedQuantity)
				continue
			}
			//检索不到数据，需要跨域，域满则break
			if len(orders) == 0 {
				nextSalt, tempKey := s.getNextRegionAndKey(ctx, nextStartKey, collectCondition.MaskProductId, originTimeStamp)
				if nextSalt >= defaultSaltSize {
					logger.CtxLogInfof(ctx, "empty orders, and next salt:%v, finish collect from hbase", nextSalt)
					break
				}
				nextStartKey = tempKey
				logger.CtxLogInfof(ctx, "splitByQuantity|next start key :%v, and got empty order, continue", nextStartKey)
				continue
			}
			//2.如果数量不足fix quantity，直接追加，并继续检索
			if batchCount+int64(len(orders)) > rule.FixedQuantity {
				//3.数量超过，二分法往前回退
				diffCount := rule.FixedQuantity - batchCount //计算差额
				logger.CtxLogInfof(ctx, "loop times:%v|batch count:%v, order count:%v, diff count:%v", loopTimes, batchCount, len(orders), diffCount)
				orders = getOrdersDividedBy2(orders, int(diffCount), 0, len(orders)-1)
				logger.CtxLogInfof(ctx, "loop times:%v| order count after:%v", loopTimes, len(orders))
			}
			//再做一次去重, 避免二次检索时数据跟第一次有重复
			beforeCount := len(splitOrders)
			for _, order := range orders {
				if _, ok := duplicatedOrders[order.OrderId]; ok {
					continue
				}
				splitOrders = append(splitOrders, order)
				duplicatedOrders[order.OrderId] = struct{}{}
			}
			afterCount := len(splitOrders)
			logger.CtxLogInfof(ctx, "splitByQuantity|loop times:%v|hold order cost time:%v|split orders count:%v|before count:%v, after count:%v", loopTimes, timeutil.GetCurrentUnixTimeStamp(ctx)-startTime, len(splitOrders), beforeCount, afterCount)

			//4.更新start key
			//存在一种情况，检索到的数据非空，但是都是重复订单，因此可以视为本region检索完毕
			if len(splitOrders) != 0 && afterCount != beforeCount {
				nextStartKey = splitOrders[len(splitOrders)-1].StartKey
				logger.CtxLogInfof(ctx, "splitByQuantity|loop times:%v|next start key after collect:%v", loopTimes, nextStartKey)
			}
			//next start key重复，当前region检索完毕
			if collectCondition.NextStartKey == nextStartKey {
				nextSalt, tempKey := s.getNextRegionAndKey(ctx, nextStartKey, collectCondition.MaskProductId, originTimeStamp)
				if nextSalt >= defaultSaltSize {
					logger.CtxLogInfof(ctx, "duplicated next key, and next salt:%v, finish collect from hbase", nextSalt)
					break
				}
				nextStartKey = tempKey
				logger.CtxLogInfof(ctx, "splitByQuantity|same key, next start key:%v", nextStartKey)
			}

			//5.满了，添加到channel中，并继续下一批次的检索
			batchCount += int64(len(splitOrders))
			logger.CtxLogInfof(ctx, "splitByQuantity|loop times:%v|current split orders count:%v, length of duplicated orders:%v, batch count:%v, order count:%v", loopTimes, len(splitOrders), len(duplicatedOrders), batchCount, len(orders))
			if batchCount >= rule.FixedQuantity {
				loopTimes += 1
				batchNo += 1
				logger.CtxLogInfof(ctx, "splitByQuantity|loop times:%v|order count:%v,finish one batch", loopTimes, len(splitOrders))
				tempSplitOrders := splitOrders
				splitResultsChan <- tempSplitOrders
				splitOrders = make([]BatchAllocateOrderDataEntity, 0) //归零
				batchCount = 0                                        //归零
				duplicatedOrders = make(map[uint64]struct{}, 0)
				holdingTime := timeutil.GetCurrentUnixTimeStamp(ctx) - startTime
				holdingTimeMap.Store(batchNo, holdingTime)
				startTime = timeutil.GetCurrentUnixTimeStamp(ctx)
				continue
			}
		}
		//将检索到的数据塞到管道，没检索到则不塞
		if len(splitOrders) != 0 {
			splitResultsChan <- splitOrders
		}
	}(rule, holdingTimeMap, shouldClose, splitModuleType, collectCondition)

	return splitResultsChan, nil
}

//根据指定时间区间拆分订单
func (s *SplittingRuleImpl) splitByTime(ctx context.Context, rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) (chan []BatchAllocateOrderDataEntity, *srerr.Error) {
	/*
		1.根据拆分模式获取数据源，区分"实时batch allocate" 和 "batch allocate forecast"
		2.检索固定时间段内的数据
	*/
	size := configutil.GetBatchAllocateForecastConf().ChannelSize
	splitResultsChan := make(chan []BatchAllocateOrderDataEntity, size)
	go func(splitModuleType int) {
		defer func() {
			logger.CtxLogInfof(ctx, "splitByTime| spilt result chan closed")
			close(splitResultsChan)
		}()
		//panic兜底
		defer func() {
			if err := recover(); err != nil {
				shouldClose.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery from splitByTime] panic recovered:\n%v\n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		//遍历时间切片
		dayTime := collectCondition.StartTimeUnix //batch forecast unit的切片日期
		var batchNum uint64
		for _, timeUnitInfo := range rule.FixedTime.FixedTimeUnitList {
			if shouldClose.Get() {
				break
			}
			//遍历时间区间，按time range拆分
			for startTime := timeUnitInfo.StartTime; startTime < timeUnitInfo.EndTime; startTime += timeUnitInfo.TimeRange {
				if shouldClose.Get() {
					break
				}

				collectCondition.StartTimeUnix = dayTime + startTime                        //切片日期+左区间的秒数 = start time
				collectCondition.EndTimeUnix = dayTime + startTime + timeUnitInfo.TimeRange //切片日期+左区间+范围的秒数 = end time
				orders, gErr := s.OrderCollector.CollectOrdersFixedTime(ctx, splitModuleType, collectCondition)
				if gErr != nil {
					logger.CtxLogErrorf(ctx, "splitByTime|condition:%v, split by time err:%v", collectCondition, gErr)
					continue
				}
				if len(orders) == 0 {
					continue
				}
				splitResultsChan <- orders
				batchNum += 1
				holdingTimeMap.Store(batchNum, timeUnitInfo.TimeRange)
			}
		}
	}(splitModuleType)

	return splitResultsChan, nil
}

//todo:SSCSMR-1698:定义一个元函数，里面包括按时间、quantity去检索数据。不同的模式只是参数不一样
//在指定时间区间内拆分订单，如果订单数满足了设置的"固定数量"就提前返回
func (s *SplittingRuleImpl) splitByQuantityAndTime(ctx context.Context, rule *SplittingRule, holdingTimeMap *sync.Map, shouldClose *warning.ShouldCloseObj, splitModuleType int, collectCondition CollectCondition) (chan []BatchAllocateOrderDataEntity, *srerr.Error) {
	/*
		1.根据拆分模式获取数据源，区分"实时batch allocate" 和 "batch allocate forecast"
		2.在指定时间区间内拆分订单，如果订单数满足了设置的"固定数量"就提前返回
	*/
	size := configutil.GetBatchAllocateForecastConf().ChannelSize
	splitResultsChan := make(chan []BatchAllocateOrderDataEntity, size)
	go func(splitModuleType int) {
		defer func() {
			logger.CtxLogInfof(ctx, "splitByQuantityAndTime| spilt result chan closed")
			close(splitResultsChan)
		}()
		//panic兜底
		defer func() {
			if err := recover(); err != nil {
				shouldClose.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery from splitByTime] panic recovered:\n%v\n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		//遍历时间切片
		dayTime := collectCondition.StartTimeUnix //batch forecast unit的切片日期
		var batchNum uint64
		for _, timeUnitInfo := range rule.FixedTime.FixedTimeUnitList {
			if shouldClose.Get() {
				break
			}
			//遍历时间区间，按timerange拆分
			for startTime := timeUnitInfo.StartTime; startTime < timeUnitInfo.EndTime; startTime += timeUnitInfo.TimeRange {
				if shouldClose.Get() {
					break
				}
				collectCondition.StartTimeUnix = dayTime + startTime                        //切片日期+左区间的秒数 = start time
				collectCondition.EndTimeUnix = dayTime + startTime + timeUnitInfo.TimeRange //切片日期+左区间+范围的秒数 = end time
				collectCondition.FixedQuantity = rule.FixedQuantity

				holdingStartTime := timeutil.GetCurrentUnixTimeStamp(ctx)
				orders, gErr := s.OrderCollector.CollectOrdersFixedTimeQuantity(ctx, splitModuleType, collectCondition)
				if gErr != nil {
					logger.CtxLogErrorf(ctx, "splitByQuantityAndTime|condition:%v, split by time err:%v", collectCondition, gErr)
					continue
				}
				if len(orders) == 0 {
					continue
				}
				batchNum += 1
				logger.CtxLogInfof(ctx, "splitByQuantityAndTime|No.%v batch, order count:%v", batchNum, len(orders))
				splitResultsChan <- orders
				holdingTimeMap.Store(batchNum, timeutil.GetCurrentUnixTimeStamp(ctx)-holdingStartTime)
			}
		}
	}(splitModuleType)

	return splitResultsChan, nil
}

//expectedCount 一定小于 len（splitOrders）
func getOrdersDividedBy2(splitOrders []BatchAllocateOrderDataEntity, expectedCount, low, high int) []BatchAllocateOrderDataEntity {
	if len(splitOrders) == 0 {
		return nil
	}
	if expectedCount == 1 {
		return splitOrders[0:1]
	}
	if low > high {
		return nil
	}
	mid := (low + high) / 2
	if expectedCount > mid {
		return getOrdersDividedBy2(splitOrders, expectedCount, mid+1, high)
	} else if expectedCount < mid {
		return getOrdersDividedBy2(splitOrders, expectedCount, low, mid-1)
	} else {
		return splitOrders[0:mid]
	}
}

//todo:SSCSMR-1698:把today、next day time stamp定义在invocation的常量里
func needBreak(ctx context.Context, originTimeStamp int64, nextStartKey string) bool {
	logger.CtxLogInfof(ctx, "origin stamp:%v, next start key:%v", originTimeStamp, nextStartKey)
	if nextStartKey == "" {
		return false
	}
	//拆解start key出来
	//start key := fmt.Sprintf("%s_%d_%s_%s", salt, maskProductId, keyTime, requestId) //from hbase
	//start key := fmt.Sprintf("%s_%d_%s", salt, maskProductId, keyTime) //切换region时指定的next key
	startKeyUnits := strings.Split(nextStartKey, "_")
	if len(startKeyUnits) == 4 || len(startKeyUnits) == 3 {
		//转换int64
		startTimeUnix, err := strconv.ParseInt(startKeyUnits[2], 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "next start key:%v, convert err:%v", nextStartKey, err)
			return false
		}
		nextDateTimeStamp := timeutil.GetNextDayStartTimeByTimeStamp(originTimeStamp, 1)
		if startTimeUnix >= nextDateTimeStamp {
			return true
		}
	}
	return false
}

func (s *SplittingRuleImpl) getNextRegionAndKey(ctx context.Context, key string, maskProductId uint64, originTimeStamp int64) (int, string) {
	salt, _ := s.OrderCollector.ConvertStartKey(ctx, key)
	saltInt, _ := strconv.Atoi(salt)
	nextSalt := fmt.Sprintf("%03d", saltInt+1)
	nextStartKey := fmt.Sprintf("%v_%v_%v", nextSalt, maskProductId, originTimeStamp)

	return saltInt, nextStartKey
}
