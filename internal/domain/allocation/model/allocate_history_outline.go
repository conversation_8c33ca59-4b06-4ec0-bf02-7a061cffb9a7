package model

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

const (
	AllocateHistoryOutlineTabName = "allocate_history_outline_tab"
)

var (
	AllocateHistoryOutlineTabHook = &AllocateHistoryOutlineTab{}
)

type AllocateHistoryOutlineTab struct {
	Id            uint64 `gorm:"column:id" json:"id"`
	DateUnix      int64  `gorm:"column:date_unix" json:"date_unix"` //date of history order
	MaskProductId uint64 `gorm:"column:mask_product_id" json:"mask_product_id"`
	ResultType    int    `gorm:"column:result_type" json:"result_type"` //1:aggregated of product; 2.aggregated of zone; 3. aggregated of route
	ProductId     uint64 `gorm:"column:product_id" json:"product_id"`
	ZoneCode      string `gorm:"column:zone_code" json:"zone_code"`
	RouteCode     string `gorm:"column:route_code" json:"route_code"`
	OrderQuantity int64  `gorm:"column:order_quantity" json:"order_quantity"`
	//用string记录，避免精度丢失
	TotalShippingFee string `gorm:"column:total_shipping_fee" json:"total_shipping_fee"` //total shipping fee of this product, only product dimension will have value
	CTime            int64  `gorm:"column:ctime" json:"ctime"`
	MTime            int64  `gorm:"column:mtime" json:"mtime"`
}

func (b *AllocateHistoryOutlineTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *AllocateHistoryOutlineTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *AllocateHistoryOutlineTab) TableName() string {
	return AllocateHistoryOutlineTabName
}

func (b *AllocateHistoryOutlineTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   b.Id,
		ModelName:            b.TableName(),
		FulfillmentProductId: b.ProductId,
	}
}
