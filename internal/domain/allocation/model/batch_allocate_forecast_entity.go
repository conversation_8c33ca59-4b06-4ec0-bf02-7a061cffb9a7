package model

import pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"

type (
	BatchAllocationRuleConfig struct {
		UseForCampaign                   bool                   `json:"use_for_campaign"` //true:大促模板；FALSE：日常模板；simulation tool默认关闭且不允许修改
		ZoneRouteVolumeEnabled           bool                   `json:"zone_route_volume_enabled"`
		CountryVolumeEnabled             bool                   `json:"country_volume_enabled"`
		CheapestShippingFeeEnabled       bool                   `json:"cheapest_shipping_fee_enabled"`
		PickupEfficiencyEnabled          bool                   `json:"pickup_efficiency_enabled"`
		PickupEfficiencyWhitelistEnabled bool                   `json:"pickup_efficiency_whitelist_enabled"`
		ZoneRouteVolumeDetail            ZoneRouteVolumeDetail  `json:"zone_route_volume_detail"`
		CountryVolumeDetailList          []CountryVolumeDetail  `json:"country_volume_detail_list"`
		ShippingFeeConfigList            []ShippingFeeConfig    `json:"shipping_fee_config_list"`
		PickupEfficiencyDetail           PickupEfficiencyDetail `json:"pickup_efficiency_detail"`
	}
	ZoneRouteCapacityDetail struct {
		FillBlankType         int    `json:"fill_blank_type"`          //1:将Excel空白单元视为无穷大; 2:视为0
		ZoneDefinitionUrl     string `json:"zone_definition_url"`      //关于zone定义的s3连接
		ZoneCapacityValueUrl  string `json:"zone_capacity_value_url"`  //zone对应的容量值 -- s3连接
		RouteDefinitionUrl    string `json:"route_definition_url"`     //关于route定义的s3连接
		RouteCapacityValueUrl string `json:"route_capacity_value_url"` //route对应的容量值 -- s3连接
	}

	ZoneRouteVolumeDetail struct {
		ZoneDefinitionUrl   string `json:"zone_definition_url"`    //关于zone定义的s3连接
		ZoneVolumeValueUrl  string `json:"zone_volume_value_url"`  //zone对应的容量值 -- s3连接
		RouteDefinitionUrl  string `json:"route_definition_url"`   //关于route定义的s3连接
		RouteVolumeValueUrl string `json:"route_volume_value_url"` //route对应的容量值 -- s3连接
	}
	CountryVolumeDetail struct {
		ProductId               int64  `json:"product_id"`
		ProductName             string `json:"product_name"`
		MaxDailyVolume          int64  `json:"max_daily_volume"`     //region维度最大运力上限
		MinDailyVolume          int64  `json:"min_daily_volume"`     //region维度最小运力需求
		MaxDailyCodVolume       int64  `json:"max_daily_cod_volume"` //region维度最大COD运力上限
		MaxDailyHighValueVolume int64  `json:"max_daily_high_value_volume"`
		MaxDailyBulkyVolume     int64  `json:"max_daily_bulky_volume"`
		MaxDailyDgVolume        int64  `json:"max_daily_dg_volume"`
	}
	ShippingFeeConfig struct {
		ChannelId int `json:"channel_id"`
		RateId    int `json:"rate_id"`
		WmsFlag   int `json:"wms_flag"`
	}

	BatchSize struct {
		BatchSizeType int       `json:"batch_size_type"` //分单规则类型。1:fixed by quantity, 2: fixed by time; 3:fixed by quantity&time
		BatchSizeName string    `json:"batch_size_name"`
		FixedQuantity int       `json:"fixed_quantity"` //按大小分片，分片的大小
		FixedTime     FixedTime `json:"fixed_time"`     //按时间分片，分片的设置
	}

	FixedTime struct {
		FixedTimeUnitList []FixedTimeUnit `json:"fixed_time_unit_list"`
	}
	/*
		@param
			StartTime：2023-01-01 00：00 Unix time
			EndTime: 2023-01-01 24：00 unix time
			TimeRange: 3600
		将2023-01-01按3600秒拆分
	*/
	FixedTimeUnit struct {
		StartTime int64 `json:"start_time"` //时间区间的左区间，记录的是秒数
		EndTime   int64 `json:"end_time"`   //时间区间的右区间，记录的是秒数
		TimeRange int64 `json:"time_range"` //时间分片单元内的切片范围, 单位：秒
	}

	PickupEfficiencyDetail struct {
		Budget float64 `json:"budget"`
	}
)

type SplittingRule struct {
	BatchAllocateSubTaskId uint64    `json:"batch_allocate_sub_task_id"`
	BatchSizeType          int       `json:"batch_size_type"` //分单规则类型。1:fixed by quantity, 2: fixed by time; 3:fixed by quantity&time
	BatchSizeName          string    `json:"batch_size_name"`
	FixedQuantity          int64     `json:"fixed_quantity"`
	FixedTime              FixedTime `json:"fixed_time"`
}

type BatchAllocateOrderDataEntity struct {
	OrderId                uint64                  `json:"order_id" validate:"required"`
	MaskingProductID       int                     `json:"masking_product_id" validate:"required"`
	OmsAllocateRequest     *OMSAllocateRequest     `json:"oms_allocate_request" validate:"required"`
	HardResult             *ValidateResult         `json:"hard_result" validate:"required"`
	FulfillmentProductID   int                     `json:"fulfillment_product_id" validate:"required"`
	StartKey               string                  `json:"start_key"` //用来检索，记录该条订单的start key
	ShippingFeeInfoList    []ShippingFeeInfo       `json:"shipping_fee_info_list"`
	HistoricalShippingFees []HbaseShippingFee      `json:"historical_shipping_fees"`
	ProductParcelInfoList  []*pb.ProductParcelInfo `json:"product_parcel_info_list"`
	RequestTime            int64                   `json:"request_time"`
}

type ShippingFeeInfo struct {
	ProductId   int     `json:"product_id"`
	ShippingFee float64 `json:"shipping_fee"`
}
