package model

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"time"
)

const (
	AllocateForecastTaskConfigTableName = "allocate_forecast_task_config_tab"
	AllocateForecastRankTableName       = "allocate_forecast_rank_tab"
	AllocateOrderDataTabName            = "allocate_order_data_tab_%s"
	AllocateHistoricalRankTableName     = "allocate_historical_rank_tab"
	AllocateDateRankTableName           = "allocate_date_rank_tab"
)

type AllocateForecastTaskConfigTab struct {
	Id                             int64  `gorm:"column:id;primary_key" json:"id"`
	TaskName                       string `gorm:"column:task_name" json:"task_name"`
	MaskingProductID               int    `gorm:"column:mask_product_id" json:"masking_product_id"`
	ShopGroupChannelPriorityToggle int    `gorm:"column:shop_group_channel_priority_toggle" json:"shop_group_channel_priority_toggle"`
	LocalSoftCriteriaToggle        int    `gorm:"column:local_soft_criteria_toggle" json:"local_soft_criteria_toggle"`
	RunSoftRuleOnlyToggle          int    `gorm:"column:run_soft_rule_only_toggle" json:"run_soft_rule_only_toggle"`
	RecalculateShippingFeeToggle   int    `gorm:"column:re_calc_fee" json:"re_calc_fee"`
	OrderPaidTime                  []byte `gorm:"column:order_paid_time" json:"order_paid_time"`
	Status                         int    `gorm:"column:task_status" json:"status"`
	SyncDesc                       string `gorm:"column:sync_desc" json:"sync_desc"`
	ProductPriorityConfigs         []byte `gorm:"column:product_priority_configs" json:"product_priority_configs"`
	AllocationRuleConfig           []byte `gorm:"column:allocation_rule_config" json:"allocation_rule_config"`
	DeployConfigDetail             []byte `gorm:"column:deploy_config_detail" json:"deploy_config_detail"`
	ConfigSyncStatus               int    `gorm:"column:config_sync_status" json:"config_sync_status"`
	CompleteTime                   uint32 `gorm:"column:complete_time" json:"complete_time"`
	LatestDeployTime               uint32 `gorm:"column:latest_deploy_time" json:"latest_deploy_time"`
	EffectiveStartTime             uint32 `gorm:"column:effective_start_time" json:"effective_start_time"`
	OperatedBy                     string `gorm:"column:operated_by" json:"operated_by"`
	CTime                          uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                          uint32 `gorm:"column:mtime" json:"mtime"` //对应前端last update time
	DeployFailedDesc               string `gorm:"column:deploy_failed_desc" json:"deploy_failed_desc"`
	AllocationMethod               int    `gorm:"column:allocation_method" json:"allocation_method"`
	BatchSizeList                  []byte `gorm:"column:splitting_rule_list" json:"splitting_rule_list"` //对应前端batch size list
	BatchAllocationRuleConfig      []byte `gorm:"column:batch_allocation_rule_config" json:"batch_allocation_rule_config"`
}

func (rs *AllocateForecastTaskConfigTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *AllocateForecastTaskConfigTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *AllocateForecastTaskConfigTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            uint64(rs.Id),
		ModelName:     rs.TableName(),
		MaskProductId: uint64(rs.MaskingProductID),
		TaskId:        uint64(rs.Id),
	}
}

func (rs *AllocateForecastTaskConfigTab) TableName() string {
	return AllocateForecastTaskConfigTableName
}

type AllocateForecastRankTab struct {
	Id                int64  `gorm:"column:id;primary_key" json:"id"`
	ForecastTaskId    int64  `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskingProductID  int    `gorm:"column:mask_product_id" json:"masking_product_id"`
	ProductId         int    `gorm:"column:product_id" json:"product_id"`
	RankCode          string `gorm:"column:rank_code" json:"rank_code"`
	RankType          int    `gorm:"column:rank_type" json:"rank_type"`
	OrderQuantity     int    `gorm:"column:order_quantity" json:"order_quantity"`
	AverageDailyOrder int    `gorm:"column:average_daily_order" json:"average_daily_order"`
	AmongAllOrder     int    `gorm:"column:among_all_order" json:"among_all_order"`
	OperatedBy        string `gorm:"column:operated_by" json:"operated_by"`
	CTime             uint32 `gorm:"column:ctime" json:"ctime"`
	MTime             uint32 `gorm:"column:mtime" json:"mtime"`
}

func (rs *AllocateForecastRankTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *AllocateForecastRankTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *AllocateForecastRankTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(rs.Id),
		ModelName:            rs.TableName(),
		MaskProductId:        uint64(rs.MaskingProductID),
		FulfillmentProductId: uint64(rs.ProductId),
	}
}

func (rs *AllocateForecastRankTab) TableName() string {
	return AllocateForecastRankTableName
}

type ProductShippingFee struct {
	ProductId           int64   `json:"product_id"`
	AllocateShippingFee float64 `json:"allocate_shipping_fee"`
}

type AllocateOrderDataTab struct {
	OrderId                 uint64               `gorm:"column:order_id" json:"order_id"`
	MaskingProductID        int                  `gorm:"column:mask_product_id" json:"masking_product_id"`
	RequestData             []byte               `gorm:"column:request_data" json:"request_data"`
	OmsAllocateRequest      []byte               `gorm:"column:oms_allocate_request" json:"oms_allocate_request"`
	HardResult              []byte               `gorm:"column:hard_result" json:"hard_result"`
	ResponseData            []byte               `gorm:"column:response_data" json:"response_data"`
	FulfillmentProductID    int                  `gorm:"column:fulfillment_product_id" json:"fulfillment_product_id"`
	ShopGroupId             int                  `gorm:"column:shop_group_id" json:"shop_group_id"`
	ZoneCode                string               `gorm:"column:zone_code" json:"zone_code"`
	OriginZoneCode          string               `gorm:"column:origin_zone_code" json:"origin_zone_code"`
	DestZoneCode            string               `gorm:"column:dest_zone_code" json:"dest_zone_code"`
	RouteCode               string               `gorm:"column:route_code" json:"route_code"`
	OrderTime               uint32               `gorm:"column:order_time" json:"order_time"`
	OperatedBy              string               `gorm:"column:operated_by" json:"operated_by"`
	CTime                   uint32               `gorm:"column:ctime" json:"ctime"`
	MTime                   uint32               `gorm:"column:mtime" json:"mtime"`
	HardResultHbase         []int                `gorm:"-" json:"hard_result_hbase"`
	ProductShippingFeeList  []ProductShippingFee `gorm:"-" json:"shipping_fee_list"`
	OmsAllocateRequestHbase *OMSAllocateRequest  `gorm:"-" json:"oms_allocate_request_hbase"`
}

func (a *AllocateOrderDataTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingLogCidRead
}

func (a *AllocateOrderDataTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingLogCidWrite
}

func (a *AllocateOrderDataTab) TableName() string {
	return AllocateOrderDataTabName
}

func (a *AllocateOrderDataTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{}
}

func AllocateOrderDataTableName(tableIndex int) string {
	return fmt.Sprintf(AllocateOrderDataTabName, strconv.Itoa(tableIndex))
}

func TableNameByTimeStamp(t time.Time) string {
	return fmt.Sprintf(AllocateOrderDataTabName, timeutil.ConvertTimeStampToLocalTime(t.Unix()).Format(constant.TimeLayout))
}

type AllocateHistoricalRankTab struct {
	Id                int64  `gorm:"column:id;primary_key" json:"id"`
	ForecastTaskId    int64  `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskingProductID  int    `gorm:"column:mask_product_id" json:"masking_product_id"`
	ProductId         int    `gorm:"column:product_id" json:"product_id"`
	RankCode          string `gorm:"column:rank_code" json:"rank_code"`
	RankType          int    `gorm:"column:rank_type" json:"rank_type"`
	OrderQuantity     int    `gorm:"column:order_quantity" json:"order_quantity"`
	AverageDailyOrder int    `gorm:"column:average_daily_order" json:"average_daily_order"`
	AmongAllOrder     int    `gorm:"column:among_all_order" json:"among_all_order"`
	OperatedBy        string `gorm:"column:operated_by" json:"operated_by"`
	CTime             uint32 `gorm:"column:ctime" json:"ctime"`
	MTime             uint32 `gorm:"column:mtime" json:"mtime"`
}

func (rs *AllocateHistoricalRankTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *AllocateHistoricalRankTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *AllocateHistoricalRankTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(rs.Id),
		ModelName: rs.TableName(),
	}
}

func (rs *AllocateHistoricalRankTab) TableName() string {
	return AllocateHistoricalRankTableName
}

type AllocateDateRankTab struct {
	Id               int64  `gorm:"column:id;primary_key" json:"id"`
	MaskingProductID int    `gorm:"column:mask_product_id" json:"masking_product_id"`
	ProductId        int    `gorm:"column:product_id" json:"product_id"`
	RankCode         string `gorm:"column:rank_code" json:"rank_code"`
	RankType         int    `gorm:"column:rank_type" json:"rank_type"` //0:overall; 1:shop_group; 2:zone; 3:route'
	Date             string `gorm:"column:date_str" json:"date"`
	OrderQuantity    int    `gorm:"column:order_quantity" json:"order_quantity"`
	OperatedBy       string `gorm:"column:operated_by" json:"operated_by"`
	CTime            uint32 `gorm:"column:ctime" json:"ctime"`
	MTime            uint32 `gorm:"column:mtime" json:"mtime"`
}

func (rs *AllocateDateRankTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *AllocateDateRankTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *AllocateDateRankTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(rs.Id),
		ModelName: rs.TableName(),
	}
}

func (rs *AllocateDateRankTab) TableName() string {
	return AllocateDateRankTableName
}

var (
	AllocateForecastTaskConfigHook = &AllocateForecastTaskConfigTab{}
	AllocateForecastRankHook       = &AllocateForecastRankTab{}
	AllocateOrderDataHook          = &AllocateOrderDataTab{}
	AllocateHistoricalRankHook     = &AllocateHistoricalRankTab{}
	AllocateDateRankHook           = &AllocateDateRankTab{}
)
