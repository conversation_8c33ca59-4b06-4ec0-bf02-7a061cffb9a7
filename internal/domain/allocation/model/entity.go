package model

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"google.golang.org/protobuf/proto"
	"sort"
	"strconv"
)

type (
	OMSAllocateRequest struct {
		MaskingProductID           int                       `json:"masking_product_id" validate:"required"`
		CheckoutItems              []*ordentity.CheckoutItem `json:"checkout_items" validate:"required,dive,required"`
		PickupInfo                 *ordentity.AddressInfo    `json:"pickup_info" validate:"required"`
		DeliveryInfo               *ordentity.AddressInfo    `json:"deliver_info" validate:"required"`
		PaymentMethod              string                    `json:"payment_method" validate:"required"`
		CodAmount                  float64                   `json:"cod_amount"`
		TotalPrice                 float64                   `json:"total_price"`
		Cogs                       float64                   `json:"cogs"`
		PartialFulfillment         int                       `json:"fulfillment"`
		IsWms                      bool                      `json:"is_wms"`
		WhsId                      string                    `json:"whs_id"`
		OrderID                    uint64                    `json:"order_id"`
		FOrderID                   uint64                    `json:"forder_id,string"`
		BuyerPaidShippingFee       float64                   `json:"buyer_paid_shipping_fee"`
		SellerTaxNumber            string                    `json:"seller_tax_number"`
		StateRegistration          string                    `json:"state_registration"`
		Snapshot                   *SnapShot                 `json:"snapshot"`
		RosOptin                   int32                     `json:"ros_optin"`
		RosEligible                bool                      `json:"ros_eligible"`
		IsCacheAfterBuyerSelection bool                      `json:"is_cache_after_buyer_selection"`
	}
)

type (
	OmsAllocRequest struct {
		MaskingChannelId           int             `json:"channel_id" validate:"required"`
		CheckoutItems              []*CheckoutItem `json:"checkout_items" validate:"required,dive,required"`
		PickupInfo                 *PickupInfo     `json:"pickup_info" validate:"required"`
		DeliveryInfo               *DeliverInfo    `json:"deliver_info" validate:"required"`
		PaymentMethod              string          `json:"payment_method" validate:"required"`
		CodAmount                  float64         `json:"cod_amount"`
		TotalPrice                 float64         `json:"total_price"`
		Cogs                       float64         `json:"cogs"`
		PartialFulfillment         bool            `json:"partial_fulfillment"`
		IsWms                      bool            `json:"is_wms"`
		WhsId                      string          `json:"whs_id"`
		OrderID                    uint64          `json:"order_id"`
		FOrderID                   uint64          `json:"forder_id"`
		FOrderIDStr                *string         `json:"forder_id_str,omitempty" validate:"omitempty,forderid"`
		BuyerPaidShippingFee       float64         `json:"buyer_paid_shipping_fee"`
		SellerTaxNumber            string          `json:"seller_tax_number"`
		StateRegistration          string          `json:"state_registration"`
		Snapshot                   *SnapShot       `json:"snapshot"`
		RosOptin                   int32           `json:"ros_optin"`
		RosEligible                bool            `json:"ros_eligible"`
		IsCacheAfterBuyerSelection bool            `json:"is_cache_after_buyer_selection"`
	}

	SnapShot struct {
		IgnoreSnapshot   bool    `json:"ignore_snapshot"`
		ShippingChannels []int64 `json:"shipping_channels"`
	}

	PickupInfo struct {
		PickupState         string `json:"pickup_state"`
		PickupCity          string `json:"pickup_city"`
		PickupDistrict      string `json:"pickup_district"`
		PickupStreet        string `json:"pickup_street"`
		PickupPostalCode    string `json:"pickup_postal_code"`
		PickupCountry       string `json:"pickup_country"`
		PickupLongitude     string `json:"pickup_longitude"`
		PickupLatitude      string `json:"pickup_latitude"`
		PickupDetailAddress string `json:"pickup_detail_address"`
	}
	DeliverInfo struct {
		DeliverState         string `json:"deliver_state"`
		DeliverCity          string `json:"deliver_city"`
		DeliverDistrict      string `json:"deliver_district"`
		DeliverStreet        string `json:"deliver_street"`
		DeliverPostalCode    string `json:"deliver_postal_code"`
		DeliverCountry       string `json:"deliver_country"`
		DeliverLongitude     string `json:"deliver_longitude"`
		DeliverLatitude      string `json:"deliver_latitude"`
		DeliverDetailAddress string `json:"deliver_detail_address"`
	}

	CheckoutItem struct {
		ShopId                 int         `json:"shop_id" validate:"required"`
		ChannelPriorityGroupId int         `json:"channel_priority_group_id"`
		Items                  []*ItemInfo `json:"items" validate:"required,dive,required"`
	}

	ItemInfo struct {
		ItemId             uint64   `json:"item_id" validate:"required"`
		Quantity           int      `json:"quantity" validate:"required"`
		ModelId            uint64   `json:"model_id"`
		Weight             *float64 `json:"weight"`
		Length             *float64 `json:"length"`
		Width              *float64 `json:"width"`
		Height             *float64 `json:"height"`
		CoverShippingFee   *bool    `json:"cover_shipping_fee"`
		IsDg               *uint32  `json:"is_dg"`
		CategoryId         *uint64  `json:"category_id"`
		GlobalCategoryId   *uint64  `json:"global_category_id"`
		GlobalCategoryIdL1 *uint64  `json:"global_category_id_L1"`
		GlobalCategoryIdL2 *uint64  `json:"global_category_id_L2"`
		GlobalCategoryIdL3 *uint64  `json:"global_category_id_L3"`
		GlobalCategoryIdL4 *uint64  `json:"global_category_id_L4"`
		GlobalCategoryIdL5 *uint64  `json:"global_category_id_L5"`
		DgSpecificType     *uint32  `json:"dg_specific_type"` //allocate req data里的
	}

	OmsAllocResp struct {
		MaskChannelId         int                `json:"mask_channel_id"`
		MaskChannelName       string             `json:"mask_channel_name"`
		ShippingChannelId     int                `json:"shipping_channel_id"`
		ShippingChannelName   string             `json:"shipping_channel_name"`
		ShippingMethod        int                `json:"shipping_method"`
		ChannelFlag           int                `json:"channel_flag"`
		FulfilmentShippingFee float64            `json:"fulfillment_shipping_fee,omitempty"`
		ShippingFeeDetail     *ShippingFeeDetail `json:"shipping_fee_detail,omitempty"`
		XRequestId            string             `json:"x_request_id"`
	}
	ShippingFeeDetail struct {
		BasicShippingFee float64 `json:"basic_shipping_fee"`
		InsuranceFee     float64 `json:"insurance_fee"`
		CodFee           float64 `json:"cod_fee"`
		RemoteFee        float64 `json:"remote_fee"`
		FuelFee          float64 `json:"fuel_fee"`
		VatFee           float64 `json:"vat_fee"`
	}
)

func (r *OmsAllocRequest) ConvertAddressInfo() (*ordentity.AddressInfo, *ordentity.AddressInfo) {
	var pickupAddressInfo, deliverAddressInfo = &ordentity.AddressInfo{}, &ordentity.AddressInfo{}
	pickupAddressInfo.State = &r.PickupInfo.PickupState
	pickupAddressInfo.City = &r.PickupInfo.PickupCity
	pickupAddressInfo.District = &r.PickupInfo.PickupDistrict
	pickupAddressInfo.Street = &r.PickupInfo.PickupStreet
	pickupAddressInfo.PostalCode = &r.PickupInfo.PickupPostalCode
	pickupAddressInfo.Longitude = &r.PickupInfo.PickupLongitude
	pickupAddressInfo.Latitude = &r.PickupInfo.PickupLatitude
	pickupAddressInfo.Country = &r.PickupInfo.PickupCountry
	pickupAddressInfo.Address = &r.PickupInfo.PickupDetailAddress

	deliverAddressInfo.State = &r.DeliveryInfo.DeliverState
	deliverAddressInfo.City = &r.DeliveryInfo.DeliverCity
	deliverAddressInfo.District = &r.DeliveryInfo.DeliverDistrict
	deliverAddressInfo.Street = &r.DeliveryInfo.DeliverStreet
	deliverAddressInfo.PostalCode = &r.DeliveryInfo.DeliverPostalCode
	deliverAddressInfo.Longitude = &r.DeliveryInfo.DeliverLongitude
	deliverAddressInfo.Latitude = &r.DeliveryInfo.DeliverLatitude
	deliverAddressInfo.Country = &r.DeliveryInfo.DeliverCountry
	deliverAddressInfo.Address = &r.DeliveryInfo.DeliverDetailAddress

	return pickupAddressInfo, deliverAddressInfo
}

type (
	ValidateResult struct {
		Supported         bool                `json:"supported"`
		CodAvailable      bool                `json:"cod_available"`
		ProductId         []int               `json:"product_id,omitempty"`
		LaneCodes         []string            `json:"lane_codes,omitempty"`
		ValidateDetail    *CheckDetail        `json:"validate_detail,omitempty"`
		ProductDgInfoList []*pb.ProductDgInfo `json:"product_dg_info_list,omitempty"`
	}

	CheckDetail struct {
		AreaAbility                   map[int]map[string]*AreaServiceable `json:"area_ability,omitempty"`
		DgAbility                     map[int]map[string]*DgServiceable   `json:"dg_ability,omitempty"`
		DistanceData                  map[string]float64                  `json:"distance_data,omitempty"`
		ValidationWeight              map[int]int                         `json:"validation_weight,omitempty"`
		RoutingLaneInfoMap            map[int][]*rule.RoutingLaneInfo     `json:"routing_lane_info_map"`
		AvailableSiteToActualPointMap map[string][]string                 `json:"available_site_to_actual_point_map"`
	}

	AreaServiceable struct {
		CanPickup         bool `json:"can_pickup"`
		CanDeliver        bool `json:"can_deliver"`
		CanCod            bool `json:"can_cod"`
		CanCodPickup      bool `json:"can_cod_pickup"`
		RequireDeliverGeo bool `json:"require_deliver_geo"`
		RequirePickupGeo  bool `json:"require_pickup_geo"`
	}
	DgServiceable struct {
		DgType  int  `json:"dg_type"`
		CanShip bool `json:"can_ship"`
	}
)

func (r *OmsAllocRequest) ConvertCheckoutItem() []*ordentity.CheckoutItem {
	var checkoutItems []*ordentity.CheckoutItem
	for _, item := range r.CheckoutItems {
		checkoutItems = append(checkoutItems, &ordentity.CheckoutItem{
			ShopId:                 item.ShopId,
			ProductPriorityGroupId: item.ChannelPriorityGroupId,
			Items:                  r.ConvertItemInfo(item),
		})
	}
	return checkoutItems
}

func (r *OmsAllocRequest) ConvertItemInfo(coItems *CheckoutItem) []*ordentity.ItemInfo {
	var itemInfo []*ordentity.ItemInfo
	for _, item := range coItems.Items {
		//SPLPS-4643:对长宽高进行排序
		regularDimension(item)
		itemInfo = append(itemInfo, &ordentity.ItemInfo{
			BasicItemInfo: ordentity.BasicItemInfo{
				ItemId:           item.ItemId,
				ModelId:          item.ModelId,
				Quantity:         item.Quantity,
				Weight:           item.Weight,
				Length:           item.Length,
				Width:            item.Width,
				Height:           item.Height,
				CoverShippingFee: item.CoverShippingFee,
				CategoryId:       item.CategoryId,
				GlobalCategoryId: item.GlobalCategoryId,
				IsDg:             item.IsDg,
				DgSpecificType:   item.DgSpecificType,
			},
		})

	}
	return itemInfo
}

// 尺寸维度常规化
func regularDimension(itemInfo *ItemInfo) {
	length := itemInfo.Length
	width := itemInfo.Width
	height := itemInfo.Height
	//长宽高均不为空才会进行排序，进行维度常规化
	if length != nil && width != nil && height != nil {
		needSort := true
		lwh := []float64{*length, *width, *height}
		for _, v := range lwh {
			if v <= 0 {
				needSort = false
				logger.LogInfof("regularDimension|illegal value(which is less than 0):%v", v)
				break
			}
		}
		if needSort {
			//升序排序
			sort.Float64s(lwh)
		}

		itemInfo.Length, itemInfo.Width, itemInfo.Height = proto.Float64(lwh[2]), proto.Float64(lwh[1]), proto.Float64(lwh[0])
	}
}

func (i *OMSAllocateRequest) ToPbOrderData() *pb.MaskingOrderInfo {
	return &pb.MaskingOrderInfo{
		OrderId: proto.String(strconv.Itoa(int(i.OrderID))),
		//OrderType:            nil,
		IsWms:             proto.Bool(i.IsWms),
		CodAmount:         proto.Float64(i.CodAmount),
		PaymentMethod:     proto.String(i.PaymentMethod),
		Cogs:              proto.Float64(i.Cogs),
		SellerTaxNumber:   proto.String(i.SellerTaxNumber),
		StateRegistration: proto.String(i.StateRegistration),
		PickupAddress:     convertPickupAddress(i.PickupInfo),
		DeliveryAddress:   convertPickupAddress(i.DeliveryInfo),
		CheckoutItems:     convertCheckoutItems(i.CheckoutItems),
	}
}

func convertPickupAddress(addressInfo *ordentity.AddressInfo) *pb.Address {
	if addressInfo == nil {
		return nil
	}
	address := pb.Address{
		PostalCode:  addressInfo.PostalCode,
		Longitude:   addressInfo.Longitude,
		Latitude:    addressInfo.Latitude,
		LocationIds: objutil.IntToInt64Slice(addressInfo.LocationIDs),
	}
	return &address
}

func convertCheckoutItems(checkoutItem []*ordentity.CheckoutItem) []*pb.CheckOutItem {
	var smrCheckOutItems []*pb.CheckOutItem
	for _, item := range checkoutItem {
		for _, lpsCheckOutItem := range item.Items {
			smrCheckOutItem := pb.CheckOutItem{
				ItemId:     proto.Uint64(lpsCheckOutItem.ItemId),
				ModelId:    proto.Uint64(lpsCheckOutItem.ModelId),
				CategoryId: lpsCheckOutItem.CategoryId,
				Weight:     lpsCheckOutItem.Weight,
				Quantity:   proto.Int32(int32(lpsCheckOutItem.Quantity)),
				Length:     lpsCheckOutItem.Length,
				Width:      lpsCheckOutItem.Width,
				Height:     lpsCheckOutItem.Height,
			}
			smrCheckOutItems = append(smrCheckOutItems, &smrCheckOutItem)
		}
	}
	return smrCheckOutItems
}

type AllocationHbaseEntity struct {
	OrderId              uint64 `json:"order_id"`
	FulfillmentProductId int    `json:"fulfillment_product_id"`
	MaskProductId        int    `json:"mask_product_id"`
	RequestTime          int64  `json:"request_time"`

	RequestDataStr string `json:"request_data"`

	HardOutput []int `json:"hard_output"`

	ZoneDestinationCode   string                  `json:"zone_destination_code"`
	ZoneOriginCode        string                  `json:"zone_origin_code"`
	RouteCodes            []string                `json:"route_codes"`
	ShopGroupId           int64                   `json:"shop_group_id"`
	ShippingFeeList       []HbaseShippingFee      `json:"shipping_fee_list"`
	ProductParcelInfoList []*pb.ProductParcelInfo `json:"product_parcel_info_list"`

	AllocationScenario int `json:"allocation_scenario"`
}

type HbaseShippingFee struct {
	ProductId           int64   `json:"product_id"`
	AllocateShippingFee float64 `json:"allocate_shipping_fee"`
}
