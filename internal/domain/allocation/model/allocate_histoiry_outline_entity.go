package model

type OutlineEntity struct {
	MaskProductId uint64 `json:"mask_product_id"`
	ProductId     uint64 `json:"product_id"`
	Code          string `json:"code"`
	CodeType      int    `json:"code_type"`
	OrderQuantity int64  `gorm:"column:order_quantity" json:"order_quantity"`
	//用string记录，避免精度丢失
	TotalShippingFee string `json:"total_shipping_fee"` //total shipping fee of this product, only product dimension will have value
}
