package model

const (
	BatchForecastUnitPending = 1
	BatchForecastUnitProcess = 2
	BatchForecastUnitDone    = 3
	BatchForecastUnitFailed  = 4
)

const (
	BatchAllocateSubTaskPending = 1
	BatchAllocateSubTaskProcess = 2
	BatchAllocateSubTaskDone    = 3
	BatchAllocateSubTaskFailed  = 4
)

const (
	SplittingByQuantity = 1
	SplittingByTime     = 2
	SplittingByQAndT    = 3
)
const (
	ModuleBAOnLine   = 1 //实时 batch allocate
	ModuleBAForecast = 2 //预测 batch allocate
)

// 一次批量插入大小
const BatchCreateSize = 1000

const (
	AllocateHistoryTypeProduct = 1
	AllocateHistoryTypeZone    = 2
	AllocateHistoryTypeRoute   = 3
)

var (
	TypePrefixMap = map[int]string{
		AllocateHistoryTypeZone:  "Zone",
		AllocateHistoryTypeRoute: "Route",
	}
)

const BatchForecastVolumePrefix = "batch_forecast_task"
