package model

import (
	"database/sql/driver"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	jsoniter "github.com/json-iterator/go"
)

const (
	BatchAllocateSubTaskTabName           = "batch_allocate_sub_task_tab"
	BatchAllocateForecastUnitTabTabName   = "batch_allocate_forecast_unit_tab"
	OrderManagerTabName                   = "order_manager_tab"
	BatchUnitTargetResultTabName          = "batch_unit_target_result_tab"
	BatchUnitFeeResultTabName             = "batch_unit_fee_result_tab"
	BAForecastUnitResultTabName           = "batch_allocate_forecast_unit_result_tab"
	BAForecastSubTaskResultOutlineTabName = "ba_subtask_result_outline_tab"
	BAForecastPickupEffResultTabName      = "batch_allocate_forecast_pickup_eff_result_tab"
)

var (
	BatchAllocateSubTaskTabHook           = &BatchAllocateSubTaskTab{}
	BatchAllocateForecastUnitTabHook      = &BatchAllocateForecastUnitTab{}
	OrderManagerTabHook                   = &OrderManagerTab{}
	BatchUnitTargetResultTabHook          = &BatchUnitTargetResultTab{}
	BatchUnitFeeResultTabHook             = &BatchUnitFeeResultTab{}
	BAForecastUnitResultTabHook           = &BAForecastUnitResultTab{}
	BAForecastSubTaskResultOutlineTabHook = &BAForecastSubTaskResultOutlineTab{}
	BAForecastPickupEffResultTabHook      = &BAForecastPickupEffResultTab{}
)

// sub task，每一个batch allocate sub task对应一个batch size rule
type BatchAllocateSubTaskTab struct {
	Id                  uint64        `gorm:"column:id;primary_key" json:"id"`
	ForecastTaskId      uint64        `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskProductId       int64         `gorm:"column:mask_product_id" json:"mask_product_id"`
	SplittingRule       []byte        `gorm:"column:splitting_rule" json:"splitting_rule"`
	SplittingRuleEntity SplittingRule `gorm:"-" json:"splitting_rule_entity"`
	Status              int8          `gorm:"column:sub_task_status" json:"sub_task_status"`
	Ctime               int64         `gorm:"ctime" json:"ctime"`
	Mtime               int64         `gorm:"mtime" json:"mtime"`
}

func (b *BatchAllocateSubTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchAllocateSubTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchAllocateSubTaskTab) TableName() string {
	return BatchAllocateSubTaskTabName
}

func (b *BatchAllocateSubTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            b.Id,
		ModelName:     b.TableName(),
		MaskProductId: uint64(b.MaskProductId),
		TaskId:        b.ForecastTaskId,
	}
}

// 最小执行单元，根据batch allocate sub task + splitting date拆分得出
type BatchAllocateForecastUnitTab struct {
	Id                      uint64 `gorm:"column:id" json:"id"`
	SubTaskId               uint64 `gorm:"column:sub_task_id" json:"sub_task_id"`
	SplitDateUnix           int64  `gorm:"column:split_date_unix" json:"split_date_unix"`
	BatchType               int    `gorm:"column:batch_type" json:"batch_type"`     //1.fixed by quantity; 2.fixed by time; 3.fixed by quantity and time
	BatchStatus             int    `gorm:"column:batch_status" json:"batch_status"` //1.pending, 2.process, 3.done, 4.failed
	HoldingTime             int64  `gorm:"column:holding_time" json:"holding_time"`
	BatchCount              int64  `gorm:"column:batch_count" json:"batch_count"` //表示该forecast unit下细分了多少个订单批次
	ForecastCostTime        int64  `gorm:"column:forecast_cost_time" json:"forecast_cost_time"`
	CTime                   int64  `gorm:"column:ctime" json:"ctime"`
	MTime                   int64  `gorm:"column:mtime" json:"mtime"`
	BatchAllocateForecastId uint64 `gorm:"column:batch_allocate_forecast_id" json:"batch_allocate_forecast_id"`
	ShardingNo              int32  `gorm:"-" json:"sharding_no"`        //非table字段，用来保存分片序号
	TotalShardingNum        int32  `gorm:"-" json:"total_sharding_num"` //非table字段，用来保存分片总数
}

func (b *BatchAllocateForecastUnitTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchAllocateForecastUnitTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchAllocateForecastUnitTab) TableName() string {
	return BatchAllocateForecastUnitTabTabName
}

func (b *BatchAllocateForecastUnitTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        b.Id,
		ModelName: b.TableName(),
		TaskId:    b.SubTaskId,
	}
}

// 订单管理，用来记录batch unit的开始、结束订单范围
type OrderManagerTab struct {
	Id            uint64 `gorm:"column:id" json:"id"`
	BatchUnitId   uint64 `gorm:"column:batch_unit_id" json:"batch_unit_id"`
	OrderStartKey string `gorm:"column:order_start_key" json:"order_start_key"`
	OrderEndKey   string `gorm:"column:order_end_key" json:"order_end_key"`
	CTime         int64  `gorm:"column:ctime" json:"ctime"`
	MTime         int64  `gorm:"column:mtime" json:"mtime"`
	//OrderList chan OrderInfo `gorm:"column:-" json:"order_list"`
}

func (o *OrderManagerTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (o *OrderManagerTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (o *OrderManagerTab) TableName() string {
	return OrderManagerTabName
}

func (o *OrderManagerTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        o.Id,
		ModelName: o.TableName(),
	}
}

// 1个batch unit对应多个batch unit target result，用来记录执行单元下不同zone，region的运力目标及达成情况。
// 运力目标只包括region或zone维度，因为route维度可以退化成zone维度，因此不需要记录route
type BatchUnitTargetResultTab struct {
	Id                   uint64 `gorm:"column:id" json:"id"`
	SubTaskId            uint64 `gorm:"column:sub_task_id" json:"sub_task_id"`
	SplitDateUnix        int64  `gorm:"column:split_date_unix" json:"split_date_unix"`
	BatchUnitId          uint64 `gorm:"column:batch_unit_id" json:"batch_unit_id"`
	BatchNum             uint64 `gorm:"column:batch_num" json:"batch_num"` //number of batch, which is send to sdk
	FulfillmentProductId uint64 `gorm:"column:fulfillment_product_id" json:"fulfillment_product_id"`
	TargetType           int    `gorm:"column:target_type" json:"target_type"` //1.zone; 2.route; 3.country
	TargetCode           string `gorm:"column:target_code" json:"target_code"`
	DailyMaxVolume       int64  `gorm:"column:daily_max_volume" json:"daily_max_volume"` //该批次对应配置daily最大运力约束
	DailyMinVolume       int64  `gorm:"column:daily_min_volume" json:"daily_min_volume"` //该批次对应配置daily最小运力需求
	BatchMinVolume       int64  `gorm:"column:batch_min_volume" json:"batch_min_volume"` //该批次对应Apollo配置，batch最小运力需求
	ObtainVolume         int64  `gorm:"column:obtain_volume" json:"obtain_volume"`       //达成的运力
	CTime                int64  `gorm:"column:ctime" json:"ctime"`
	MTime                int64  `gorm:"column:mtime" json:"mtime"`
}

func (b *BatchUnitTargetResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchUnitTargetResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchUnitTargetResultTab) TableName() string {
	return BatchUnitTargetResultTabName
}

func (b *BatchUnitTargetResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   b.Id,
		ModelName:            b.TableName(),
		FulfillmentProductId: b.FulfillmentProductId,
		TaskId:               uint64(b.SubTaskId),
	}
}

// 1个batch unit对应多个fee result，记录该批次耗费及对比"非SDK"节省的运费
type BatchUnitFeeResultTab struct {
	Id                   uint64  `gorm:"column:id" json:"id"`
	SubTaskId            uint64  `gorm:"column:sub_task_id" json:"sub_task_id"`
	SplitDateUnix        int64   `gorm:"column:split_date_unix" json:"split_date_unix"`
	BatchUnitId          uint64  `gorm:"column:batch_unit_id" json:"batch_unit_id"`
	BatchNum             uint64  `gorm:"column:batch_num" json:"batch_num"` //number of batch, which is send to sdk
	FulfillmentProductId uint64  `gorm:"column:fulfillment_product_id" json:"fulfillment_product_id"`
	OrderQuantity        int64   `gorm:"column:order_quantity" json:"order_quantity"`
	CostFee              float64 `gorm:"column:cost_fee" json:"cost_fee"`
	SaveFee              float64 `gorm:"column:save_fee" json:"save_fee"`
	PickupEfficiencyCost float64 `gorm:"column:pickup_efficiency_cost" json:"pickup_efficiency_cost"`
	HoldingTime          int64   `gorm:"column:holding_time" json:"holding_time"`
	ExecuteTime          int64   `gorm:"column:execute_time" json:"execute_time"`
	AlgoTime             int64   `gorm:"column:algo_time" json:"algo_time"`
	CTime                int64   `gorm:"column:ctime" json:"ctime"`
	MTime                int64   `gorm:"column:mtime" json:"mtime"`
}

func (b *BatchUnitFeeResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchUnitFeeResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchUnitFeeResultTab) TableName() string {
	return BatchUnitFeeResultTabName
}

func (b *BatchUnitFeeResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   b.Id,
		ModelName:            b.TableName(),
		FulfillmentProductId: b.FulfillmentProductId,
		TaskId:               uint64(b.SubTaskId),
	}
}

// 1个预测单元对应1个result
type BAForecastUnitResultTab struct {
	Id                   uint64  `gorm:"column:id" json:"id"`
	SubTaskId            uint64  `gorm:"column:sub_task_id" json:"sub_task_id"`
	BatchUnitId          uint64  `gorm:"column:batch_unit_id" json:"batch_unit_id"`
	ResultType           int     `gorm:"column:result_type" json:"result_type"` //1.overall; 2.zone; 3.route
	FulfillmentProductId uint64  `gorm:"column:fulfillment_product_id" json:"fulfillment_product_id"`
	OrderQuantity        int64   `gorm:"column:order_quantity" json:"order_quantity"`
	TotalCostFee         float64 `gorm:"column:total_cost_fee" json:"total_cost_fee"`
	TotalSaveFee         float64 `gorm:"column:total_save_fee" json:"total_save_fee"`
	PickupEfficiencyCost float64 `gorm:"column:pickup_efficiency_cost" json:"pickup_efficiency_cost"`
	TotalForecastTime    int64   `gorm:"column:total_forecast_time" json:"total_forecast_time"`
	ZoneCode             string  `gorm:"column:zone_code" json:"zone_code"`
	RouteCode            string  `gorm:"column:route_code" json:"route_code"`
	CTime                int64   `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime                int64   `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (b *BAForecastUnitResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BAForecastUnitResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BAForecastUnitResultTab) TableName() string {
	return BAForecastUnitResultTabName
}

func (b *BAForecastUnitResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   b.Id,
		ModelName:            b.TableName(),
		FulfillmentProductId: b.FulfillmentProductId,
		TaskId:               uint64(b.SubTaskId),
	}
}

// 1个sub task对应一个result outline
// 即多个unit result汇总成1个result outline
type BAForecastSubTaskResultOutlineTab struct {
	Id                     uint64          `gorm:"column:id" json:"id"`
	SubTaskId              uint64          `gorm:"column:sub_task_id" json:"sub_task_id"`
	Batches                int             `gorm:"column:batches" json:"batches"`
	AvgHoldingTime         int64           `gorm:"column:avg_holding_time" json:"avg_holding_time"`
	MaxHoldingTime         int64           `gorm:"column:max_holding_time" json:"max_holding_time"`
	AvgOrdersPerBatch      int64           `gorm:"column:avg_orders_per_batch" json:"avg_orders_per_batch"`
	TotalShippingFee       float64         `gorm:"column:total_shipping_fee" json:"total_shipping_fee"`
	TotalCostSaving        float64         `gorm:"column:total_cost_saving" json:"total_cost_saving"`
	PickupEfficiencyCost   float64         `gorm:"column:pickup_efficiency_cost" json:"pickup_efficiency_cost"`
	PickupEfficiencyResult PickupEffResult `gorm:"column:pickup_efficiency_result" json:"pickup_efficiency_result"`
	CTime                  int64           `gorm:"column:ctime" json:"ctime"`
	MTime                  int64           `gorm:"column:mtime" json:"mtime"`
}

func (b *BAForecastSubTaskResultOutlineTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BAForecastSubTaskResultOutlineTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BAForecastSubTaskResultOutlineTab) TableName() string {
	return BAForecastSubTaskResultOutlineTabName
}

func (b *BAForecastSubTaskResultOutlineTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        b.Id,
		ModelName: b.TableName(),
		TaskId:    b.SubTaskId,
	}
}

type (
	PickupEffResult     []*PickupEffResultItem
	PickupEffResultItem struct {
		NumberOf3pls  int `json:"number_of_3pls"`
		NumberOfShops int `json:"number_of_shops"`
	}
)

func (s *PickupEffResult) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("fail to unmarshal PickupEffResult: %v", value)
	}

	return jsoniter.Unmarshal(bytes, s)
}

func (s PickupEffResult) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "[]", nil
	}

	return jsoniter.Marshal(s)
}

type BAForecastPickupEffResultTab struct {
	Id            uint64 `gorm:"column:id" json:"id"`
	SubTaskId     uint64 `gorm:"column:sub_task_id" json:"sub_task_id"`
	BatchUnitId   uint64 `gorm:"column:batch_unit_id" json:"batch_unit_id"`
	NumberOf3pls  int    `gorm:"column:number_of_3pls" json:"number_of_3pls"`
	NumberOfShops int    `gorm:"column:number_of_shops" json:"number_of_shops"`
	Ctime         int64  `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	Mtime         int64  `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (b *BAForecastPickupEffResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BAForecastPickupEffResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BAForecastPickupEffResultTab) TableName() string {
	return BAForecastPickupEffResultTabName
}

func (b *BAForecastPickupEffResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        b.Id,
		ModelName: b.TableName(),
		TaskId:    b.SubTaskId,
	}
}
