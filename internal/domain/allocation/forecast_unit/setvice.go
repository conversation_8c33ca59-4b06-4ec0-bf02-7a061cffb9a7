package forecast_unit

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchForecastUnitService interface {
	BatchCreateUnitTargetResult(ctx context.Context, unitTargets []*model.BatchUnitTargetResultTab) *srerr.Error
	BatchCreateUnitFeeResult(ctx context.Context, feeResult []*model.BatchUnitFeeResultTab) *srerr.Error
}

type BatchForecastUnitServiceImpl struct {
}

// TODO 依赖注入
func NewBatchForecastUnitServiceImpl() *BatchForecastUnitServiceImpl {
	return &BatchForecastUnitServiceImpl{}
}

func (b *BatchForecastUnitServiceImpl) BatchCreateUnitTargetResult(ctx context.Context, unitTargets []*model.BatchUnitTargetResultTab) *srerr.Error {
	err := dbutil.InsertBatch(ctx, model.BatchUnitTargetResultTabHook, &unitTargets)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchCreateUnitTargetResult get db failed %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (b *BatchForecastUnitServiceImpl) BatchCreateUnitFeeResult(ctx context.Context, feeResult []*model.BatchUnitFeeResultTab) *srerr.Error {
	err := dbutil.InsertBatch(ctx, model.BatchUnitFeeResultTabHook, &feeResult)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchCreateUnitTargetResult get db failed %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}
