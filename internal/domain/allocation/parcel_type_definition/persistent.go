package parcel_type_definition

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

type Status int

const (
	StatusDisable = 0
	StatusEnable  = 1

	parcelTypeDefinitionTableName = "parcel_type_definition_tab"
)

var (
	parcelTypeDefinitionTabHook = &ParcelTypeDefinitionTab{}
)

type ParcelTypeDefinitionTab struct {
	ID                         uint64  `gorm:"column:id" json:"id"`
	MaskProductID              int     `gorm:"column:mask_product_id" json:"mask_product_id"`
	FulfillmentProductID       int     `gorm:"column:fulfillment_product_id" json:"fulfillment_product_id"`
	LineID                     string  `gorm:"column:line_id" json:"line_id"`
	ScenarioType               int     `gorm:"column:scenario_type" json:"scenario_type"`
	BulkyLengthThreshold       float64 `gorm:"column:bulky_length_threshold" json:"bulky_length_threshold"`                 // CM
	BulkyWidthThreshold        float64 `gorm:"column:bulky_width_threshold" json:"bulky_width_threshold"`                   // CM
	BulkyHeightThreshold       float64 `gorm:"column:bulky_height_threshold" json:"bulky_height_threshold"`                 // CM
	BulkyWeightThreshold       float64 `gorm:"column:bulky_weight_threshold" json:"bulky_weight_threshold"`                 // KG
	HighValueCogPriceThreshold float64 `gorm:"column:high_value_cog_price_threshold" json:"high_value_cog_price_threshold"` // Local Currency
	Status                     Status  `gorm:"column:status" json:"status"`
	Operator                   string  `gorm:"column:operator" json:"operator"`
	Ctime                      int64   `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	Mtime                      int64   `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (p ParcelTypeDefinitionTab) TableName() string {
	return parcelTypeDefinitionTableName
}

func (p ParcelTypeDefinitionTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p ParcelTypeDefinitionTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p ParcelTypeDefinitionTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   p.ID,
		ModelName:            p.TableName(),
		MaskProductId:        uint64(p.MaskProductID),
		FulfillmentProductId: uint64(p.FulfillmentProductID),
	}
}
