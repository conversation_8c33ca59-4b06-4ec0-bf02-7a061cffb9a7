package parcel_type_definition

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ParcelTypeDefinitionRepo interface {
	CreateParcelTypeDefinition(ctx context.Context, definition *ParcelTypeDefinitionTab) *srerr.Error
	UpdateParcelTypeDefinition(ctx context.Context, definition *ParcelTypeDefinitionTab) *srerr.Error
	GetParcelTypeDefinitionByID(ctx context.Context, id uint64) (*ParcelTypeDefinitionTab, *srerr.Error)
	ListParcelTypeDefinition(ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, status *int, offset, limit, scenarioType int) ([]*ParcelTypeDefinitionTab, int64, *srerr.Error)
	DeleteParcelTypeDefinitionByID(ctx context.Context, id uint64) *srerr.Error
}

type ParcelTypeDefinitionRepoImpl struct{}

func NewParcelTypeDefinitionRepoImpl() *ParcelTypeDefinitionRepoImpl {
	return &ParcelTypeDefinitionRepoImpl{}
}

func (p ParcelTypeDefinitionRepoImpl) CreateParcelTypeDefinition(ctx context.Context, definition *ParcelTypeDefinitionTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(parcelTypeDefinitionTableName).Create(definition).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, definition, err)
	}

	return nil
}

func (p ParcelTypeDefinitionRepoImpl) UpdateParcelTypeDefinition(ctx context.Context, definition *ParcelTypeDefinitionTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(parcelTypeDefinitionTableName).Save(definition).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, definition, err)
	}

	return nil
}

func (p ParcelTypeDefinitionRepoImpl) GetParcelTypeDefinitionByID(ctx context.Context, id uint64) (*ParcelTypeDefinitionTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var d ParcelTypeDefinitionTab
	if err := db.Table(parcelTypeDefinitionTableName).First(&d, "id = ?", id).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}

	return &d, nil
}

func (p ParcelTypeDefinitionRepoImpl) ListParcelTypeDefinition(ctx context.Context, maskProductID, fulfillmentProductID int, lineID string, status *int, offset, limit, scenarioType int) ([]*ParcelTypeDefinitionTab, int64, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var (
		query = db.Table(parcelTypeDefinitionTableName)
		list  []*ParcelTypeDefinitionTab
		total int64
	)

	if maskProductID != 0 {
		query = query.Where("mask_product_id = ?", maskProductID)
	}
	if fulfillmentProductID != 0 {
		query = query.Where("fulfillment_product_id = ?", fulfillmentProductID)
	}
	if lineID != "" {
		query = query.Where("line_id = ?", lineID)
	}
	query = query.Where("scenario_type = ?", scenarioType)
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	if err := query.Count(&total).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err := query.Offset(offset).Limit(limit).Find(&list).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return list, total, nil
}

func (p ParcelTypeDefinitionRepoImpl) DeleteParcelTypeDefinitionByID(ctx context.Context, id uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(parcelTypeDefinitionTableName).Delete(parcelTypeDefinitionTabHook, "id = ?", id).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}

	return nil
}
