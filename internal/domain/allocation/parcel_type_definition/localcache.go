package parcel_type_definition

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	MaskingScenario        = 0
	SPXRoutingScenario     = 1
	LocalRoutingScenario   = 2
	CbMultiRoutingScenario = 4
)

func ParcelTypeDefinitionCacheKey(maskProductID, fulfillmentProductID int) string {
	return fmt.Sprintf("%d:%d", maskProductID, fulfillmentProductID)
}

func ParcelTypeDefinitionCacheKeyRouting(fulfillmentProductID int, lineID string, scenarioType int) string {
	return fmt.Sprintf("%d:%s:%d", fulfillmentProductID, lineID, scenarioType)
}

func DumpParcelTypeDefinition() (map[string]interface{}, error) {
	var (
		ret  = make(map[string]interface{})
		list = make([]*ParcelTypeDefinitionTab, 0)
		ctx  = context.Background()
	)

	db, err := dbutil.SlaveDB(ctx, parcelTypeDefinitionTabHook)
	if err != nil {
		return nil, err
	}

	// 区分Status，只加载Enable的
	if err := db.Table(parcelTypeDefinitionTableName).Where("status = ?", StatusEnable).Find(&list).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "load parcel type definition data failed | err=%v", err)
		return nil, err
	}

	for _, p := range list {
		key := GetParcelTypeDefinitionCacheKey(p.ScenarioType, p.MaskProductID, p.FulfillmentProductID, p.LineID)
		ret[key] = p
	}

	return ret, nil
}

func GetParcelTypeDefinitionCacheKey(scenarioType int, maskProductID int, fulfillmentProductID int, lineID string) string {
	if scenarioType == MaskingScenario {
		return ParcelTypeDefinitionCacheKey(maskProductID, fulfillmentProductID)
	} else if scenarioType == SPXRoutingScenario || scenarioType == LocalRoutingScenario ||
		scenarioType == CbMultiRoutingScenario {
		return ParcelTypeDefinitionCacheKeyRouting(fulfillmentProductID, lineID, scenarioType)
	}
	return ""
}
