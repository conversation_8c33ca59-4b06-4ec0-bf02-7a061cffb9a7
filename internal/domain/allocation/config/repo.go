package config

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type AllocationConfigRepo interface {
	GetAllocationConfigByID(ctx context.Context, id int64) (*MaskAllocationConfigTab, error)
	GetAllocationConfigByMaskProductID(ctx context.Context, maskChannelID int64) (*MaskAllocationConfigTab, error)
	ListAllAllocationConfigs(ctx context.Context, offset int32, limit int32,
		maskProductID int64) ([]*MaskAllocationConfigTab, int32, error)
	ListAllAllocationConfigsWithMaskType(ctx context.Context, offset int32, limit int32,
		maskProductID int64, maskProductType int) ([]*MaskAllocationConfigTab, int32, error)
	CreateAllocationConfig(ctx context.Context, maskChannelID int64, smartRoutingEnabled bool, defaultChannel int64, defaultWmsChannel int64, operateBy string) (*MaskAllocationConfigTab, error)
	UpdateAllocationConfig(ctx context.Context, maskChannelID int64, smartRoutingEnabled bool, defaultChannel int64, defaultWmsChannel int64, operateBy string) (*MaskAllocationConfigTab, error)
}

type AllocationConfigImpl struct {
	LpsApi lpsclient.LpsApi
}

func NewAllocationConfigImpl(lpsApi lpsclient.LpsApi) *AllocationConfigImpl {
	return &AllocationConfigImpl{
		LpsApi: lpsApi,
	}
}

func (a *AllocationConfigImpl) GetAllocationConfigByID(ctx context.Context, id int64) (*MaskAllocationConfigTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, err
	}
	routing := &MaskAllocationConfigTab{}
	err = db.Table(tableName).Take(routing, "id = ?", id).GetError()
	if err != nil {
		return nil, err
	}
	return routing, nil
}

func (a *AllocationConfigImpl) GetAllocationConfigByMaskProductID(ctx context.Context, maskChannelID int64) (*MaskAllocationConfigTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, err
	}
	routing := &MaskAllocationConfigTab{}
	err = db.Table(tableName).Take(routing, "mask_product_id = ?", maskChannelID).GetError()
	if err != nil {
		return nil, err
	}
	return routing, nil
}

func (a *AllocationConfigImpl) ListAllAllocationConfigs(ctx context.Context, offset int32, limit int32,
	maskProductID int64) ([]*MaskAllocationConfigTab, int32, error) {

	db, err := dbutil.SlaveDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, 0, err
	}

	query := db.Table(tableName)

	var routings []*MaskAllocationConfigTab

	if maskProductID != 0 {
		query = query.Where("mask_product_id = ?", maskProductID)
	}
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "db rows counting")
	}
	d = query.Order("id DESC").Offset(int(offset)).Limit(int(limit)).Find(&routings)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "db query")
	}
	return routings, int32(total), nil
}

func (a *AllocationConfigImpl) ListAllAllocationConfigsWithMaskType(ctx context.Context, offset int32, limit int32,
	maskProductID int64, maskProductType int) ([]*MaskAllocationConfigTab, int32, error) {

	//从缓存获取所有product 信息
	productInfoMap := localcache.AllItems(ctx, constant.ProductBaseInfoList)
	// 获取所有mask type为 maskProductType 的 mask product id
	var availableMaskProductIds = make([]int, 0)
	if len(productInfoMap) == 0 {
		return nil, 0, fmt.Errorf("could not get mask product ids")
	}

	for _, productInterface := range productInfoMap {
		//convert product base info
		productBaseInfo, ok := productInterface.(*lpsclient.LogisticProductTab)
		if !ok {
			logger.CtxLogErrorf(ctx, "product info:%+v, convert product base info err", productInterface)
		} else {
			//set product id and seller display name
			if maskProductType == 0 || (productBaseInfo.MaskingType == maskProductType) {
				availableMaskProductIds = append(availableMaskProductIds, productBaseInfo.ProductId)
			}
		}
	}

	if maskProductID != 0 {
		// 过滤
		availableMaskProductIds = mathutil.UnionInt(availableMaskProductIds, []int{int(maskProductID)})
	}

	if len(availableMaskProductIds) == 0 {
		return []*MaskAllocationConfigTab{}, 0, nil
	}

	db, err := dbutil.SlaveDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, 0, err
	}
	query := db.Table(tableName)

	var routings []*MaskAllocationConfigTab

	if len(availableMaskProductIds) == 1 {
		query = query.Where("mask_product_id = ?", availableMaskProductIds[0])
	} else {
		query = query.Where("mask_product_id in (?)", availableMaskProductIds)
	}

	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "db rows counting")
	}
	d = query.Order("id DESC").Offset(int(offset)).Limit(int(limit)).Find(&routings)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "db query")
	}
	return routings, int32(total), nil
}

func (a *AllocationConfigImpl) CreateAllocationConfig(ctx context.Context, maskChannelID int64, smartRoutingEnabled bool, defaultChannel int64, defaultWmsChannel int64, operateBy string) (*MaskAllocationConfigTab, error) {
	maskProduct, gErr := a.LpsApi.GetProductDetail(ctx, int(maskChannelID))
	if gErr != nil {
		return nil, fmt.Errorf("could not find mask product id %d , err: %v", maskChannelID, gErr)
	}

	db, err := dbutil.MasterDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, err
	}
	routing := new(MaskAllocationConfigTab)
	d := db.Table(tableName).Find(routing, "mask_product_id = ?", maskChannelID)
	//1.record not found以外的error报错
	if d.GetError() != nil { //find找不到不会报错
		return nil, srerr.With(srerr.DatabaseErr, maskChannelID, d.GetError())
	}
	//2.存在就报错
	if routing.ID != 0 { //find找不到不会报错，因此需要用id == 0来判断record not found
		return nil, dbutil.NewErrDataExisted("mask_product_id", maskChannelID)
	}

	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	routing = &MaskAllocationConfigTab{
		MaskProductID:          maskChannelID,
		SmartAllocationEnabled: smartRoutingEnabled,
		DefaultProduct:         defaultChannel,
		DefaultWmsProduct:      defaultWmsChannel,
		OperatedBy:             operateBy,
		CTime:                  uint32(now),
		MTime:                  uint32(now),
		MaskProductName:        maskProduct.SellerDisplayName,
	}
	d = db.Create(routing)
	if d.GetError() != nil {
		return nil, fmt.Errorf("create data: %w", d.GetError())
	}

	//写入操作记录
	cErr := a.LpsApi.CreateHistory(ctx, routing, string(lpsclient.CreateType), "create allocation config", uint64(routing.ID))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	return routing, nil
}

func (a *AllocationConfigImpl) UpdateAllocationConfig(ctx context.Context, maskChannelID int64, smartRoutingEnabled bool, defaultChannel int64, defaultWmsChannel int64, operateBy string) (*MaskAllocationConfigTab, error) {
	maskProduct, gErr := a.LpsApi.GetProductDetail(ctx, int(maskChannelID))
	if gErr != nil {
		return nil, fmt.Errorf("could not find mask product id %d , err: %v", maskChannelID, gErr)
	}

	db, err := dbutil.MasterDB(ctx, MaskAllocationConfigHook)
	if err != nil {
		return nil, err
	}
	routing := new(MaskAllocationConfigTab)

	d := db.Table(tableName).Take(routing, "mask_product_id = ?", maskChannelID)
	if d.GetError() != nil {
		//todo:SSCSMR-517:测试ErrRecordNotFound是否会出现
		if d.GetError() == scormv2.ErrRecordNotFound {
			return nil, dbutil.NewErrDataNotFound("mask_channel_routing mask_product_id", maskChannelID)
		} else {
			return nil, dbutil.NewErrDatabase(d.GetError(), "db query")
		}
	}

	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	routing.SmartAllocationEnabled = smartRoutingEnabled
	routing.DefaultProduct = defaultChannel
	routing.DefaultWmsProduct = defaultWmsChannel
	routing.OperatedBy = operateBy
	routing.MTime = uint32(now)
	routing.MaskProductName = maskProduct.SellerDisplayName
	d = db.Save(routing)
	if d.GetError() != nil {
		return nil, dbutil.NewErrDatabase(d.GetError(), "update data")
	}

	//写入操作记录
	cErr := a.LpsApi.CreateHistory(ctx, routing, string(lpsclient.UpdateType), "update allocation config", uint64(routing.ID))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	return routing, nil
}
