package config

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"strconv"
)

func DumpMaskAllocationConfig() (map[string]interface{}, error) {
	var records []*MaskAllocationConfigTab
	if err := dbutil.Select(context.Background(), MaskAllocationConfigHook, nil, &records); err != nil {
		return nil, err
	}
	ret := make(map[string]interface{})
	if len(records) > 0 {
		for _, record := range records {
			ret[strconv.FormatInt(record.MaskProductID, 10)] = record
		}
	}

	return ret, nil
}
