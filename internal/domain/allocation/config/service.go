package config

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

type IMaskConfigRepo interface {
	GetConfigByMaskingProductIDByCache(ctx context.Context, maskProductID int64) (*MaskAllocationConfigTab, *srerr.Error)
	ListAllConfigs(ctx context.Context, offset int32, limit int32, maskProductID int64, maskProductType int) (
		[]*AllocationConfig, int32, *srerr.Error)
	GetConfig(ctx context.Context, id int64) (*AllocationConfig, *srerr.Error)
	CreateConfig(ctx context.Context, maskProductID int64, maskType int, smartAllocationEnabled bool, defaultProduct int64, defaultWmsProduct int64) (*AllocationConfig, *srerr.Error)
	UpdateConfig(ctx context.Context, maskProductID int64, maskType int, smartAllocationEnabled bool, defaultProduct int64, defaultWmsProduct int64) (*AllocationConfig, *srerr.Error)
}

type maskConfigRepoImpl struct {
	AllocationRuleRepo   rule.AllocationRuleRepo
	AllocationConfigRepo AllocationConfigRepo
}

func NewMaskConfigRepo(allocationRuleRepo rule.AllocationRuleRepo, allocationConfigRepo AllocationConfigRepo) *maskConfigRepoImpl {
	return &maskConfigRepoImpl{
		AllocationRuleRepo:   allocationRuleRepo,
		AllocationConfigRepo: allocationConfigRepo,
	}
}

// @core
func (repo *maskConfigRepoImpl) GetConfigByMaskingProductIDByCache(ctx context.Context, maskProductID int64) (*MaskAllocationConfigTab, *srerr.Error) {
	value, err := localcache.Get(ctx, constant.MaskAllocationConfig, strconv.FormatInt(maskProductID, 10))
	if err != nil {
		return nil, srerr.With(srerr.AllocationConfigError, maskProductID, err)
	}
	return value.(*MaskAllocationConfigTab), nil
}

func (repo *maskConfigRepoImpl) ListAllConfigs(ctx context.Context, offset int32, limit int32, maskProductID int64, maskProductType int) (
	[]*AllocationConfig, int32, *srerr.Error) {

	// 调用方需要校验offset, limit的有效性
	if offset < 0 {
		offset = 0
	}
	if limit <= 0 {
		return nil, 0, srerr.New(srerr.ParamErr, limit, "limit should be greater than 0")
	}
	var data []*MaskAllocationConfigTab
	var total int32
	var err error
	if maskProductType == 0 {
		data, total, err = repo.AllocationConfigRepo.ListAllAllocationConfigs(ctx, offset, limit, maskProductID)
	} else {
		data, total, err = repo.AllocationConfigRepo.ListAllAllocationConfigsWithMaskType(ctx, offset, limit, maskProductID, maskProductType)
	}

	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	configs := make([]*AllocationConfig, len(data))
	for i, row := range data {
		config := repo.copyAllocationConfig(row)
		configs[i] = config
	}
	return configs, total, nil
}

func (repo *maskConfigRepoImpl) copyAllocationConfig(data *MaskAllocationConfigTab) *AllocationConfig {
	config := &AllocationConfig{}
	if data != nil {
		config.ID = data.ID
		config.MaskProductID = data.MaskProductID
		config.SmartAllocationEnabled = data.SmartAllocationEnabled
		config.DefaultProduct = data.DefaultProduct
		config.DefaultWmsProduct = data.DefaultWmsProduct
		config.OperatedBy = data.OperatedBy
		config.CTime = data.CTime
		config.MTime = data.MTime
	}

	return config
}

func (repo *maskConfigRepoImpl) GetConfig(ctx context.Context, id int64) (*AllocationConfig, *srerr.Error) {
	data, err := repo.AllocationConfigRepo.GetAllocationConfigByID(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.AllocationConfigNotFound, id, err)
	}
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}
	config := repo.copyAllocationConfig(data)
	return config, nil
}

func (repo *maskConfigRepoImpl) CreateConfig(ctx context.Context, maskProductID int64, maskType int, smartAllocationEnabled bool, defaultProduct int64, defaultWmsProduct int64) (*AllocationConfig, *srerr.Error) {
	if err := repo.validateData(ctx, maskProductID, maskType, smartAllocationEnabled, defaultProduct); err != nil {
		return nil, err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	data, err := repo.AllocationConfigRepo.CreateAllocationConfig(ctx, maskProductID, smartAllocationEnabled, defaultProduct, defaultWmsProduct, operateBy)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, maskProductID, err)
	}
	config := repo.copyAllocationConfig(data)
	return config, nil
}

func (repo *maskConfigRepoImpl) UpdateConfig(ctx context.Context, maskProductID int64, maskType int, smartAllocationEnabled bool, defaultProduct int64, defaultWmsProduct int64) (*AllocationConfig, *srerr.Error) {
	if err := repo.validateData(ctx, maskProductID, maskType, smartAllocationEnabled, defaultProduct); err != nil {
		return nil, err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	data, err := repo.AllocationConfigRepo.UpdateAllocationConfig(ctx, maskProductID, smartAllocationEnabled, defaultProduct, defaultWmsProduct, operateBy)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, maskProductID, err)
	}
	config := repo.copyAllocationConfig(data)

	return config, nil
}

func (repo *maskConfigRepoImpl) validateData(ctx context.Context, maskProductID int64, maskType int, smartAllocationEnabled bool, defaultChannel int64) *srerr.Error {
	switch maskType {
	case constant.MaskingTypeSingle:
		if smartAllocationEnabled {
			return srerr.New(srerr.InvalidAllocationConfig, maskProductID, "'smart_allocation' not allowed for 'single product' mask_type")
		}
		if defaultChannel == 0 {
			return srerr.New(srerr.InvalidAllocationConfig, maskProductID, "default_product required for 'single product' mask_type")
		}
	case constant.MaskingTypeMultiple:
		if !smartAllocationEnabled {
			if defaultChannel == 0 {
				return srerr.New(srerr.InvalidAllocationConfig, maskProductID, "default_product required when 'smart_allocation' is not enabled")
			}
		}
	}

	if smartAllocationEnabled {
		_, err := repo.AllocationRuleRepo.GetEffectiveRuleByMaskProductID(ctx, maskProductID, rule_mode.MplOrderRule)
		if err != nil {
			if dbutil.IsDataNotFound(err) {
				return srerr.New(srerr.InvalidAllocationConfig, maskProductID, "cannot enable 'smart_allocation' when there is no allocation rule for mask_product %v", maskProductID)
			} else {
				return srerr.With(srerr.DatabaseErr, maskProductID, err)
			}
		}
	}

	return nil
}
