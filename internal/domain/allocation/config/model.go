package config

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	tableName = "allocation_config_tab"
)

var (
	MaskAllocationConfigHook = &MaskAllocationConfigTab{}
)

type MaskAllocationConfigTab struct {
	ID                     int64  `gorm:"column:id" json:"id"`
	MaskProductID          int64  `gorm:"column:mask_product_id" json:"mask_product_id"`
	SmartAllocationEnabled bool   `gorm:"column:smart_allocation_enabled" json:"smart_allocation_enabled"`
	DefaultProduct         int64  `gorm:"column:default_product" json:"default_product"`
	DefaultWmsProduct      int64  `gorm:"column:default_wms_product" json:"default_wms_product"`
	OperatedBy             string `gorm:"column:operated_by" json:"operated_by"`
	CTime                  uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                  uint32 `gorm:"column:mtime" json:"mtime"`

	MaskProductName string `gorm:"-" json:"mask_product_name"`
}

func (t *MaskAllocationConfigTab) TableName() string {
	return tableName
}

func (t *MaskAllocationConfigTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *MaskAllocationConfigTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *MaskAllocationConfigTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            uint64(t.ID),
		ModelName:     t.TableName(),
		MaskProductId: uint64(t.MaskProductID),
	}
}

func (t *MaskAllocationConfigTab) ConvertToChangeData() string {
	return fmt.Sprintf("%d-%s", t.MaskProductID, t.MaskProductName)
}
