package config

import (
	"context"
	"testing"

	"git.garena.com/shopee/bg-logistics/go/chassis"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

func TestAllocationConfigImpl_CreateAllocationConfig(t *testing.T) {

	//chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	//
	//configutil.Init()
	//dbutil.Init()
	//
	//configHook := ruledata.RoutingConfigHook
	//ctx := context.Background()
	//
	//db, err := dbutil.MasterDB(ctx, configHook)
	//if err != nil {
	//	panic(err)
	//}
	//res := &ruledata.RoutingConfigTab{}
	//d := db.Table(configHook.TableName()).Take(res, "id = ?", 200000)
	//
	//if d.GetError() != nil {
	//	println("dddd")
	//}
	//
	//res = &ruledata.RoutingConfigTab{
	//	ProductID: 1008611,
	//}
	//
	//d = db.Create(res)
	//if d.GetError() != nil {
	//	panic(d.GetError())
	//}
}

func TestAllocationConfigImpl_CreateAllocationConfig1(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))

	configutil.Init()
	dbutil.Init()

	configHook := ruledata.RoutingConfigHook
	ctx := context.Background()

	db, err := dbutil.MasterDB(ctx, configHook)
	if err != nil {
		panic(err)
	}
	res := &ruledata.RoutingConfigTab{}
	d := db.Table(configHook.TableName()).Take(res, "id = ?", 200000)

	if d.GetError() != nil {
		println("dddd")
	}

	res = &ruledata.RoutingConfigTab{
		ProductID: 1008611,
	}

	d = db.Create(res)
	if d.GetError() != nil {
		panic(d.GetError())
	}
}
