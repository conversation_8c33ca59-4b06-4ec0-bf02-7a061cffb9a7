package whitelist

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	tableName = "whitelist_tab"
)

var (
	WhitelistTabHook = &WhitelistTab{}
)

type WhitelistTab struct {
	Id                uint64          `gorm:"column:id" json:"id"`
	WhitelistScenario Scenario        `gorm:"column:whitelist_scenario" json:"whitelist_scenario"`
	WhitelistId       string          `gorm:"column:whitelist_id" json:"whitelist_id"` // shop id
	Operator          string          `gorm:"column:operator" json:"operator"`
	FulfillmentType   FulfillmentType `gorm:"column:fulfillment_type" json:"fulfillment_type"`
	WhitelistStatus   Status          `gorm:"column:whitelist_status" json:"whitelist_status"`
	Ctime             int64           `gorm:"column:ctime" json:"ctime"`
	Mtime             int64           `gorm:"column:mtime" json:"mtime"`
}

func (t WhitelistTab) TableName() string {
	return tableName
}

func (t WhitelistTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t WhitelistTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t WhitelistTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        t.Id,
		ModelName: t.TableName(),
	}
}

func (t WhitelistTab) MaskingCacheKey() string {
	return fmt.Sprintf("%s", t.WhitelistId)
}
