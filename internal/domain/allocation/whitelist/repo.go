package whitelist

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ShopWhitelistRepo interface {
	ListShopWhitelist(ctx context.Context, condition map[string]interface{}, offset, size int64, needPage bool) ([]*WhitelistTab, int64, *srerr.Error)
	SetShopWhiteListExpired(ctx context.Context, shopIdList []string) *srerr.Error
	BatchUpdateOrInsert(ctx context.Context, tabs []*WhitelistTab, shopIdList []string) *srerr.Error
	BatchInsertShopWhitelist(ctx context.Context, tabs []*WhitelistTab) *srerr.Error
	BatchUpdateShopWhitelist(ctx context.Context, tabs []uint64, param map[string]interface{}) *srerr.Error
}

type ShopWhitelistRepoImpl struct {
}

func NewShopWhitelistRepoImpl() *ShopWhitelistRepoImpl {
	return &ShopWhitelistRepoImpl{}
}

func (i *ShopWhitelistRepoImpl) ListShopWhitelist(ctx context.Context, condition map[string]interface{}, offset, size int64, needPage bool) ([]*WhitelistTab, int64, *srerr.Error) {
	tabs := make([]*WhitelistTab, 0)
	if needPage { //需要分页
		if err := dbutil.Select(ctx, WhitelistTabHook, condition, &tabs, dbutil.WithPage(offset, size), dbutil.WithOrder("mtime desc"), dbutil.WithOrder("id desc")); err != nil {
			return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
		}
	} else { //不需要分页
		if err := dbutil.Select(ctx, WhitelistTabHook, condition, &tabs, dbutil.WithOrder("mtime desc")); err != nil {
			return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	var total int64
	if err := dbutil.Count(ctx, WhitelistTabHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tabs, total, nil
}

func (i *ShopWhitelistRepoImpl) SetShopWhiteListExpired(ctx context.Context, shopIdList []string) *srerr.Error {
	if err := dbutil.Update(ctx, WhitelistTabHook, map[string]interface{}{"whitelist_id in (?)": shopIdList},
		map[string]interface{}{"whitelist_status": ExpiredWhiteList}, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (i *ShopWhitelistRepoImpl) BatchUpdateOrInsert(ctx context.Context, tabs []*WhitelistTab, shopIdList []string) *srerr.Error {
	var (
		needUpdateBothTabs = make([]uint64, 0)
		needUpdateWMSTabs  = make([]uint64, 0)
		needUpdateMPLTabs  = make([]uint64, 0)
		needCreateTabs     = make([]*WhitelistTab, 0)
		param              = map[string]interface{}{
			"whitelist_status": ActiveWhitelist,
		}
	)
	// 1. 先判断哪些需要update，哪些需要Create
	tempTabs := make([]*WhitelistTab, 0)
	needUpdateTabMap := make(map[string]*WhitelistTab, 0)
	if err := dbutil.Select(ctx, WhitelistTabHook, map[string]interface{}{"whitelist_id in (?)": shopIdList}, &tempTabs); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	for idx := 0; idx < len(tempTabs); idx++ {
		if _, ok := needUpdateTabMap[tempTabs[idx].WhitelistId]; !ok {
			needUpdateTabMap[tempTabs[idx].WhitelistId] = tempTabs[idx]
		}
	}
	for _, tab := range tabs {
		// 1.1 需要更新的tab，更新Mtime及status
		if _, ok := needUpdateTabMap[tab.WhitelistId]; ok {
			tempTab := needUpdateTabMap[tab.WhitelistId]

			param["mtime"] = tab.Mtime //mtime都是一样的，可以覆盖赋值

			// 1.2 归类，不同的Fulfillment type，方便后续统一update
			if tab.FulfillmentType == BothFulfillment {
				needUpdateBothTabs = append(needUpdateBothTabs, tempTab.Id)
			} else if tab.FulfillmentType == WMSFulfillment {
				needUpdateWMSTabs = append(needUpdateWMSTabs, tempTab.Id)
			} else {
				needUpdateMPLTabs = append(needUpdateMPLTabs, tempTab.Id)
			}
		} else {
			// 1.2 需要insert的tab，直接insert
			needCreateTabs = append(needCreateTabs, tab)
		}
	}
	// 2. 批量insert
	if err := i.BatchInsertShopWhitelist(ctx, needCreateTabs); err != nil {
		return err
	}

	// 3. 按分类更新
	if len(needUpdateBothTabs) != 0 {
		param["fulfillment_type"] = BothFulfillment
		if err := i.BatchUpdateShopWhitelist(ctx, needUpdateBothTabs, param); err != nil {
			return err
		}
	}
	if len(needUpdateWMSTabs) != 0 {
		param["fulfillment_type"] = WMSFulfillment
		if err := i.BatchUpdateShopWhitelist(ctx, needUpdateWMSTabs, param); err != nil {
			return err
		}
	}
	if len(needUpdateMPLTabs) != 0 {
		param["fulfillment_type"] = MPLFulfillment
		if err := i.BatchUpdateShopWhitelist(ctx, needUpdateMPLTabs, param); err != nil {
			return err
		}
	}

	return nil
}

func (i *ShopWhitelistRepoImpl) BatchInsertShopWhitelist(ctx context.Context, tabs []*WhitelistTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, WhitelistTabHook, tabs); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (i *ShopWhitelistRepoImpl) BatchUpdateShopWhitelist(ctx context.Context, tabs []uint64, param map[string]interface{}) *srerr.Error {
	if len(tabs) == 0 {
		return nil
	}
	ids := make([][]uint64, 0)
	tempIdList := make([]uint64, 0)
	count := 0
	for idx := 0; idx < len(tabs); idx++ {
		count += 1
		tempIdList = append(tempIdList, tabs[idx])
		// 每满一批，就存储并重置temp数组
		if count%batchUpdateNum == 0 {
			ids = append(ids, tempIdList)
			tempIdList = make([]uint64, 0)
		}
	}
	if len(tempIdList) != 0 {
		ids = append(ids, tempIdList)
	}

	for idx := 0; idx < len(ids); idx++ {
		tempIdList := ids[idx]
		if err := dbutil.Update(ctx, WhitelistTabHook, map[string]interface{}{"id in (?)": tempIdList}, param, dbutil.ModelInfo{}); err != nil {
			return srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	return nil
}
