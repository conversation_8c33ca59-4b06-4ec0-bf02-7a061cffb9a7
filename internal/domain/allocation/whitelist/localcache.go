package whitelist

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func DumpMaskingWhitelist() (map[string]interface{}, error) {
	// 1.获取active的所有masking Whitelist
	condition := map[string]interface{}{
		"whitelist_scenario = ?": MaskingShopWhitelist,
		"whitelist_status = ?":   ActiveWhitelist,
	}
	tabs := make([]*WhitelistTab, 0)

	if err := dbutil.Select(context.Background(), WhitelistTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 2.拼装key，填充Local Cache
	data := make(map[string]interface{}, len(tabs))
	for _, tab := range tabs {
		data[tab.WhitelistId] = tab.FulfillmentType
	}

	return data, nil
}
