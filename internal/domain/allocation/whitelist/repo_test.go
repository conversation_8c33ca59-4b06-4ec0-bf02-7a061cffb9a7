package whitelist

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"testing"
)

func TestShopWhitelistRepoImpl_BatchUpdateShopWhitelist(t *testing.T) {
	type args struct {
		ctx   context.Context
		tabs  []uint64
		param map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want *srerr.Error
	}{
		// TODO: Add test cases.
		{
			args: args{
				param: map[string]interface{}{"id": 1},
				tabs:  []uint64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &ShopWhitelistRepoImpl{}
			if got := i.BatchUpdateShopWhitelist(tt.args.ctx, tt.args.tabs, tt.args.param); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchUpdateShopWhitelist() = %v, want %v", got, tt.want)
			}
		})
	}
}
