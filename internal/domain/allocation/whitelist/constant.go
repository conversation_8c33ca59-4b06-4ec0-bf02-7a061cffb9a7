package whitelist

type FulfillmentType int
type Status int
type Scenario string

const (
	BothType string = "Both"
	WMSType  string = "WMS"
	MPLType  string = "MP"
)

const (
	IllegalType     FulfillmentType = 0
	BothFulfillment FulfillmentType = 1
	WMSFulfillment  FulfillmentType = 2
	MPLFulfillment  FulfillmentType = 3
)

const (
	DraftWhitelist   Status = 0
	ActiveWhitelist  Status = 1
	ExpiredWhiteList Status = 2
)

const (
	MaskingShopWhitelist Scenario = "masking_shop_whitelist"
	ActionDelete                  = "-1"
	ActionAddOrUpdate             = "0"
)

const batchUpdateNum = 10

func (t FulfillmentType) Name() string {
	if t == BothFulfillment {
		return BothType
	}
	if t == WMSFulfillment {
		return WMSType
	}
	if t == MPLFulfillment {
		return MPLType
	}

	return ""
}

func (t FulfillmentType) String() string {
	if t == BothFulfillment {
		return BothType
	}
	if t == WMSFulfillment {
		return WMSType
	}
	if t == MPLFulfillment {
		return MPLType
	}

	return ""
}
