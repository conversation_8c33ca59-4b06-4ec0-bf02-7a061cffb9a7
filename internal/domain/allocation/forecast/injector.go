package forecast

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"github.com/google/wire"
)

var AllocateDateRankProviderSet = wire.NewSet(
	repo.NewAllocateDateRankRepo,
	wire.Bind(new(repo.AllocateDateRankRepo), new(*repo.AllocateDateRankRepoImpl)),
)

var AllocateHistoricalRankProviderSet = wire.NewSet(
	repo.NewAllocateHistoricalRankRepo,
	wire.Bind(new(repo.AllocateHistoricalRankRepo), new(*repo.AllocateHistoricalRankRepoImpl)),
)

var AllocateShippingFeeProviderSet = wire.NewSet(
	repo.NewAllocateShippingFeeServiceImpl,
	wire.Bind(new(repo.AllocateShippingFeeRepo), new(*repo.AllocateShippingFeeImpl)),
)

var AllocateForecastRankProviderSet = wire.NewSet(
	repo.NewAllocateForecastRankImpl,
	wire.Bind(new(repo.AllocateForecastRankRepo), new(*repo.AllocateForecastRankRepoImpl)),
)

var AllocateForecastTaskConfigProviderSet = wire.NewSet(
	repo.NewAllocateForecastTaskConfigRepoImpl,
	wire.Bind(new(repo.AllocateForecastTaskConfigRepo), new(*repo.AllocateForecastTaskConfigRepoImpl)),
)

var BatchAllocateForecastRepoProviderSet = wire.NewSet(
	repo.NewBatchAllocateForecastRepoImpl,
	wire.Bind(new(repo.BatchAllocateForecastRepo), new(*repo.BatchAllocateForecastRepoImpl)),
)

var BatchUnitTargetResultRepoProviderSet = wire.NewSet(
	repo.NewBatchUnitTargetResultRepoImap,
	wire.Bind(new(repo.BatchUnitTargetResultRepo), new(*repo.BatchUnitTargetResultRepoImpl)),
)

var BatchUnitFeeResultRepoProviderSet = wire.NewSet(
	repo.NewBatchUnitFeeResultRepoImpl,
	wire.Bind(new(repo.BatchUnitFeeResultRepo), new(*repo.BatchUnitFeeResultRepoImpl)),
)

var BatchAllocateForecastUnitResultRepoProviderSet = wire.NewSet(
	repo.NewBatchAllocateForecastUnitResultRepoImpl,
	wire.Bind(new(repo.BatchAllocateForecastUnitResultRepo), new(*repo.BatchAllocateForecastUnitResultRepoImpl)),
)

var BatchAllocateSubTaskRepoProviderSet = wire.NewSet(
	repo.NewBatchAllocateSubTaskRepoImpl,
	wire.Bind(new(repo.BatchAllocateSubTaskRepo), new(*repo.BatchAllocateSubTaskRepoImpl)),
)

var BatchAllocateForecastUnitRepoProviderSet = wire.NewSet(
	repo.NewBatchAllocateForecastUnitRepoImpl,
	wire.Bind(new(repo.BatchAllocateForecastUnitRepo), new(*repo.BatchAllocateForecastUnitRepoImpl)),
)

var BatchAllocateForecastServiceProviderSet = wire.NewSet(
	NewBatchAllocateForecastServiceImpl,
	wire.Bind(new(BatchAllocateForecastService), new(*BatchAllocateForecastServiceImpl)),
)

var AllocateHistoryOutlineProviderSet = wire.NewSet(
	repo.NewAllocateHistoryOutlineImpl,
	wire.Bind(new(repo.AllocateHistoryOutlineRepo), new(*repo.AllocateHistoryOutlineImpl)),
)

var BatchAllocateSubtaskOutlineRepoProviderSet = wire.NewSet(
	repo.NewBatchAllocateSubTaskOutlineRepoImpl,
	wire.Bind(new(repo.BatchAllocateSubTaskOutlineRepo), new(*repo.BatchAllocateSubTaskOutlineRepoImpl)),
)
