package forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type BatchAllocateForecastService interface {
	UpdateBatchAllocateTaskStatus(ctx context.Context) *srerr.Error
}

type BatchAllocateForecastServiceImpl struct {
	AllocateForecastTaskConfigRepo      repo.AllocateForecastTaskConfigRepo
	BatchAllocateForecastUnitRepo       repo.BatchAllocateForecastUnitRepo
	BatchAllocateSubTaskRepo            repo.BatchAllocateSubTaskRepo
	BatchUnitFeeResultRepo              repo.BatchUnitFeeResultRepo
	BatchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo
}

func NewBatchAllocateForecastServiceImpl(
	allocateForecastTaskConfigRepo repo.AllocateForecastTaskConfigRepo,
	batchAllocateForecastUnitRepo repo.BatchAllocateForecastUnitRepo,
	batchAllocateSubTaskRepo repo.BatchAllocateSubTaskRepo,
	batchUnitFeeResultRepo repo.BatchUnitFeeResultRepo,
	batchAllocateForecastUnitResultRepo repo.BatchAllocateForecastUnitResultRepo,
) *BatchAllocateForecastServiceImpl {
	return &BatchAllocateForecastServiceImpl{
		AllocateForecastTaskConfigRepo:      allocateForecastTaskConfigRepo,
		BatchAllocateForecastUnitRepo:       batchAllocateForecastUnitRepo,
		BatchAllocateSubTaskRepo:            batchAllocateSubTaskRepo,
		BatchUnitFeeResultRepo:              batchUnitFeeResultRepo,
		BatchAllocateForecastUnitResultRepo: batchAllocateForecastUnitResultRepo,
	}
}

func (b *BatchAllocateForecastServiceImpl) UpdateBatchAllocateTaskStatus(ctx context.Context) *srerr.Error {
	// 1. 查询BAForecast task下的任务信息
	forecastTaskConfigList, subTaskList, subTaskUnitList, err := b.GetBatchAllocateForecastTaskInfo(ctx)
	if err != nil {
		return err
	}
	// 2. 根据预测单元更新sub_task状态，根据sub_task更新呢batch allocate task状态
	needUpdateStatusTaskList, needUpdateStatusSubTaskList, successSubTaskList := updateBAForecastTaskStatus(ctx, forecastTaskConfigList, subTaskList, subTaskUnitList)
	// 3. 合并sub task的预测结果
	subTaskResultOutlineList, err := b.UniteBatchAllocateSubTaskResult(ctx, successSubTaskList)
	if err != nil {
		return err
	}
	// 4. 更新任务状态
	err = b.AllocateForecastTaskConfigRepo.UpdateBAForecastTaskStatus(ctx, needUpdateStatusTaskList, needUpdateStatusSubTaskList, subTaskResultOutlineList)
	if err != nil {
		return err
	}
	return nil
}

// GetBatchAllocateForecastTaskInfo 查询所有处理中的batch allocate forecast task及关联task
func (b *BatchAllocateForecastServiceImpl) GetBatchAllocateForecastTaskInfo(ctx context.Context) ([]*model.AllocateForecastTaskConfigTab, []*model.BatchAllocateSubTaskTab, []*model.BatchAllocateForecastUnitTab, *srerr.Error) {
	// 1. 查询所有process状态的batch forecast task
	condition := map[string]interface{}{
		"task_status = ?":       constant.TaskConfigStatusProcess,
		"allocation_method = ?": constant.BatchAllocate,
	}
	forecastTaskConfigList, err := b.AllocateForecastTaskConfigRepo.GetForecastTaskConfigByCondition(ctx, condition)
	if err != nil {
		return nil, nil, nil, err
	}
	// 2. 查询forecast task下的sub task
	var forecastTaskIdList []int64
	for _, forecastTaskConfig := range forecastTaskConfigList {
		forecastTaskIdList = append(forecastTaskIdList, forecastTaskConfig.Id)
	}
	condition = map[string]interface{}{
		"forecast_task_id in ?": forecastTaskIdList,
	}
	subTaskList, err := b.BatchAllocateSubTaskRepo.GetBASubTaskListByParam(ctx, condition)
	if err != nil {
		return nil, nil, nil, err
	}
	// 3. 查询forecast sub task下的forecast_task_unit
	var subTaskIdList []int64
	for _, subTask := range subTaskList {
		subTaskIdList = append(subTaskIdList, int64(subTask.Id))
	}
	condition = map[string]interface{}{
		"sub_task_id in ?": subTaskIdList,
	}
	subTaskUnitList, err := b.BatchAllocateForecastUnitRepo.GetForecastUnitListByParam(ctx, condition)
	if err != nil {
		return nil, nil, nil, err
	}
	return forecastTaskConfigList, subTaskList, subTaskUnitList, nil
}

// updateBAForecastTaskStatus 根据sub_task_unit更新sub_task状态，根据sub_task更新task状态
func updateBAForecastTaskStatus(ctx context.Context, forecastTaskConfigList []*model.AllocateForecastTaskConfigTab, subTaskList []*model.BatchAllocateSubTaskTab, subTaskUnitList []*model.BatchAllocateForecastUnitTab) ([]*model.AllocateForecastTaskConfigTab, []*model.BatchAllocateSubTaskTab, []*model.BatchAllocateSubTaskTab) {
	nowTime := timeutil.GetLocalTime(ctx).Unix()
	var successSubTaskList []*model.BatchAllocateSubTaskTab
	var needUpdateStatusSubTaskList []*model.BatchAllocateSubTaskTab
	var needUpdateStatusTaskList []*model.AllocateForecastTaskConfigTab
	subTaskUnitMap := make(map[uint64][]*model.BatchAllocateForecastUnitTab)
	for _, subTaskUnit := range subTaskUnitList {
		if _, ok := subTaskUnitMap[subTaskUnit.SubTaskId]; !ok {
			var tempList []*model.BatchAllocateForecastUnitTab
			subTaskUnitMap[subTaskUnit.SubTaskId] = tempList
		}
		subTaskUnitMap[subTaskUnit.SubTaskId] = append(subTaskUnitMap[subTaskUnit.SubTaskId], subTaskUnit)
	}
	for _, subTask := range subTaskList {
		// 如果subTask为成功状态，则直接跳到下一条
		if subTask.Status == constant.TaskConfigStatusComplete {
			continue
		}
		var completeNum int
		for _, subTaskUnit := range subTaskUnitMap[subTask.Id] {
			// 有一个sub task unit失败则sub task失败
			if subTaskUnit.BatchStatus == constant.TaskConfigStatusFailed {
				subTask.Status = constant.TaskConfigStatusFailed
				subTask.Mtime = nowTime
				needUpdateStatusSubTaskList = append(needUpdateStatusSubTaskList, subTask)
				break
			}
			if subTaskUnit.BatchStatus == constant.TaskConfigStatusComplete {
				completeNum += 1
			}
		}
		// sub task unit全部成功则sub task成功
		if completeNum == len(subTaskUnitMap[subTask.Id]) && completeNum > 0 {
			subTask.Status = constant.TaskConfigStatusComplete
			subTask.Mtime = nowTime
			successSubTaskList = append(successSubTaskList, subTask)
			needUpdateStatusSubTaskList = append(needUpdateStatusSubTaskList, subTask)
		}
	}
	subTaskMap := make(map[uint64][]*model.BatchAllocateSubTaskTab)
	for _, subTask := range subTaskList {
		if _, ok := subTaskMap[subTask.ForecastTaskId]; !ok {
			var tempList []*model.BatchAllocateSubTaskTab
			subTaskMap[subTask.ForecastTaskId] = tempList
		}
		subTaskMap[subTask.ForecastTaskId] = append(subTaskMap[subTask.ForecastTaskId], subTask)
	}
	for _, forecastTask := range forecastTaskConfigList {
		if forecastTask.Status == constant.TaskConfigStatusComplete {
			continue
		}
		var completeNum int
		for _, subTask := range subTaskMap[uint64(forecastTask.Id)] {
			if subTask.Status == constant.TaskConfigStatusFailed {
				forecastTask.Status = constant.TaskConfigStatusFailed
				forecastTask.CompleteTime = uint32(nowTime)
				needUpdateStatusTaskList = append(needUpdateStatusTaskList, forecastTask)
				break
			}
			if subTask.Status == constant.TaskConfigStatusComplete {
				completeNum += 1
			}
		}
		if completeNum == len(subTaskMap[uint64(forecastTask.Id)]) && completeNum > 0 {
			forecastTask.Status = constant.TaskConfigStatusComplete
			forecastTask.CompleteTime = uint32(nowTime)
			needUpdateStatusTaskList = append(needUpdateStatusTaskList, forecastTask)
		}
	}
	return needUpdateStatusTaskList, needUpdateStatusSubTaskList, successSubTaskList
}

func (b *BatchAllocateForecastServiceImpl) UniteBatchAllocateSubTaskResult(ctx context.Context, successSubTaskList []*model.BatchAllocateSubTaskTab) ([]*model.BAForecastSubTaskResultOutlineTab, *srerr.Error) {
	var successSubTaskIdList []uint64
	for _, successSubTask := range successSubTaskList {
		successSubTaskIdList = append(successSubTaskIdList, successSubTask.Id)
	}
	//1. 根据sub task id查询task unit
	condition := map[string]interface{}{
		"sub_task_id in ?": successSubTaskIdList,
	}
	unitFeeResultList, err := b.BatchUnitFeeResultRepo.GetListByParam(ctx, condition)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get unit fee result error", err)
	}
	// 2. 按sub task区分sub task unit
	subTaskUnitFeeMap := make(map[uint64][]*model.BatchUnitFeeResultTab)
	for _, unitFeeResult := range unitFeeResultList {
		if _, ok := subTaskUnitFeeMap[unitFeeResult.SubTaskId]; !ok {
			var tempList []*model.BatchUnitFeeResultTab
			subTaskUnitFeeMap[unitFeeResult.SubTaskId] = tempList
		}
		subTaskUnitFeeMap[unitFeeResult.SubTaskId] = append(subTaskUnitFeeMap[unitFeeResult.SubTaskId], unitFeeResult)
	}
	// 3. 聚合sub task的预测结果
	nowTime := timeutil.GetLocalTime(ctx).Unix()
	var (
		subTaskResultOutlineList []*model.BAForecastSubTaskResultOutlineTab
		batchResultMap           = make(map[uint64][]*model.BatchUnitFeeResultTab, 0)
	)
	for subTaskId, subTaskUnitFeeList := range subTaskUnitFeeMap {
		//按批次号归类
		var maxBatchNum uint64
		for _, unitFee := range subTaskUnitFeeList {
			batchResultMap[unitFee.BatchNum] = append(batchResultMap[unitFee.BatchNum], unitFee)

			if unitFee.BatchNum > maxBatchNum {
				maxBatchNum = unitFee.BatchNum
			}
		}

		var totalHoldingTime, maxHoldingTime, totalOrders int64
		var totalShippingFee, totalCostSaving, pickupEffCost float64
		//获取holding time
		for _, feeResultList := range batchResultMap {
			// 该批次的hold单时间, 一批包含各product的fee result，各product的holding time是一样的
			holdingTime := feeResultList[0].HoldingTime
			// 获取总hold单时间、最大hold单时间
			totalHoldingTime += holdingTime
			if maxHoldingTime < holdingTime {
				maxHoldingTime = holdingTime
			}
		}
		//获取运费、数量情况
		for _, subTaskUnitFee := range subTaskUnitFeeList {
			// 总运费，从节省运费
			totalShippingFee += subTaskUnitFee.CostFee
			totalCostSaving += subTaskUnitFee.SaveFee
			// 总订单数
			totalOrders += subTaskUnitFee.OrderQuantity
			// Pickup Eff消耗费用
			pickupEffCost += subTaskUnitFee.PickupEfficiencyCost
		}

		pickupEffResult, err := b.uniteSubTaskPickupEffResult(ctx, subTaskId)
		if err != nil {
			return nil, err
		}

		subTaskResultOutline := &model.BAForecastSubTaskResultOutlineTab{
			SubTaskId:              subTaskId,
			Batches:                int(maxBatchNum),
			AvgHoldingTime:         totalHoldingTime / int64(maxBatchNum),
			MaxHoldingTime:         maxHoldingTime,
			AvgOrdersPerBatch:      totalOrders / int64(maxBatchNum),
			TotalShippingFee:       totalShippingFee,
			TotalCostSaving:        totalCostSaving,
			PickupEfficiencyCost:   pickupEffCost,
			PickupEfficiencyResult: pickupEffResult,
			CTime:                  nowTime,
			MTime:                  nowTime,
		}
		subTaskResultOutlineList = append(subTaskResultOutlineList, subTaskResultOutline)
	}
	return subTaskResultOutlineList, nil
}

func (b *BatchAllocateForecastServiceImpl) uniteSubTaskPickupEffResult(ctx context.Context, subTaskId uint64) (model.PickupEffResult, *srerr.Error) {
	condition := map[string]interface{}{"sub_task_id = ?": subTaskId}
	resultList, err := b.BatchAllocateForecastUnitResultRepo.GetPickupEffResultsByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	var (
		pickupEffCounter = make(map[int]int)
		unitCounter      = make(map[uint64]struct{})
	)
	for _, result := range resultList {
		pickupEffCounter[result.NumberOf3pls] += result.NumberOfShops
		unitCounter[result.BatchUnitId] = struct{}{}
	}

	var pickupEffResult model.PickupEffResult
	for numberOf3pls, numberOfShops := range pickupEffCounter {
		pickupEffResult = append(pickupEffResult, &model.PickupEffResultItem{
			NumberOf3pls:  numberOf3pls,
			NumberOfShops: objutil.RoundDivideInt(numberOfShops, len(unitCounter)),
		})
	}

	return pickupEffResult, nil
}
