package repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	schema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
	"strings"
)

type AllocateShippingFeeRepo interface {
	QueryAllocatingRateFee(ctx context.Context, req *schema.ShippingFeeReq) (*chargeentity.AllocatingRateFeeListResp, *srerr.Error)
}

type AllocateShippingFeeImpl struct {
	RateClient chargeclient.ChargeApi
	LpsApi     lpsclient.LpsApi
}

func NewAllocateShippingFeeServiceImpl(RateClient chargeclient.ChargeApi, LpsApi lpsclient.LpsApi) *AllocateShippingFeeImpl {
	allocateShippingFeeServiceImpl := &AllocateShippingFeeImpl{
		RateClient: RateClient,
		LpsApi:     LpsApi,
	}
	return allocateShippingFeeServiceImpl
}

func (a AllocateShippingFeeImpl) QueryAllocatingRateFee(ctx context.Context, req *schema.ShippingFeeReq) (*chargeentity.AllocatingRateFeeListResp, *srerr.Error) {
	maskProduct, err := a.LpsApi.GetProductDetail(ctx, req.MaskingProductId)
	if err != nil {
		return nil, err
	}
	if maskProduct.ComponentProduct == nil {
		return nil, srerr.New(srerr.ParamErr, req, "masking product have no component product.")
	}
	var channelIds []string
	for _, productId := range maskProduct.ComponentProduct.ComponentProducts {
		channelIds = append(channelIds, strconv.Itoa(productId))
	}
	str := strings.Join(channelIds, ",")
	status := fmt.Sprintf("%v,%v", allocation.FeeRuleStatusActive, allocation.FeeRuleStatusForecast)
	feeReq := &chargeentity.AllocatingRateFeeListReq{
		ChannelIds: &str,
		Status:     &status,
	}
	ret, err := a.RateClient.QueryAllocatingRateFee(ctx, feeReq)
	return ret, err
}
