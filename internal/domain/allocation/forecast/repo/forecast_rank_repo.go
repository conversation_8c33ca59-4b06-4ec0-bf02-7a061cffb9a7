package repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

var (
	incrForecastColumns = []string{"forecast_task_id", "mask_product_id", "product_id", "rank_code", "rank_type",
		"order_quantity", "operated_by", "ctime", "mtime"}
	incrForecastDbSql = "insert into %s(%s) VALUES %s ON DUPLICATE KEY UPDATE %s"
)

type AllocateForecastRankRepo interface {
	BatchGetForecastRankList(ctx context.Context, taskIds, rankTypes []int) ([]*model.AllocateForecastRankTab, *srerr.Error)
	GetRankCodeList(ctx context.Context, taskId, maskingProductId, rankType int) ([]string, *srerr.Error)
	GetForecastRankList(ctx context.Context, taskId, rankType int, rankCode string) ([]*model.AllocateForecastRankTab, *srerr.Error)
	InsertAllocateForecastRank(ctx context.Context, records []*model.AllocateForecastRankTab) (int64, *srerr.Error)
}

type AllocateForecastRankRepoImpl struct {
}

func NewAllocateForecastRankImpl() *AllocateForecastRankRepoImpl {
	return &AllocateForecastRankRepoImpl{}
}

func (a AllocateForecastRankRepoImpl) BatchGetForecastRankList(ctx context.Context, taskIds, rankTypes []int) ([]*model.AllocateForecastRankTab, *srerr.Error) {
	var rankList []*model.AllocateForecastRankTab
	condition := map[string]interface{}{}
	if len(taskIds) <= 0 {
		return nil, srerr.New(srerr.ParamErr, taskIds, "the length of taskIds must large than 0.")
	}
	condition["forecast_task_id in (?)"] = taskIds
	if len(rankTypes) > 0 {
		condition["rank_type in (?)"] = rankTypes
	}

	if err := dbutil.Select(ctx, model.AllocateForecastRankHook, condition, &rankList, dbutil.WithOrder("order_quantity DESC")); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return rankList, nil
}

func (a AllocateForecastRankRepoImpl) GetRankCodeList(ctx context.Context, taskId int, maskingProductId, rankType int) ([]string, *srerr.Error) {
	var rankCodeList []string
	db, err := dbutil.SlaveDB(ctx, model.AllocateForecastRankHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get slave err", err)
	}
	var rankList []*model.AllocateForecastRankTab
	d := db.Table(model.AllocateForecastRankHook.TableName()).Where("forecast_task_id = ? AND mask_product_id = ? AND rank_type = ?", taskId, maskingProductId, rankType).Select("distinct(rank_code)").Find(&rankList)
	if d.GetError() != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	for _, rank := range rankList {
		rankCodeList = append(rankCodeList, rank.RankCode)
	}
	return rankCodeList, nil
}

func (a AllocateForecastRankRepoImpl) InsertAllocateForecastRank(ctx context.Context, records []*model.AllocateForecastRankTab) (int64, *srerr.Error) {
	if len(records) == 0 {
		return 0, nil
	}
	db, err := dbutil.MasterDB(ctx, model.AllocateForecastRankHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--db error|err=%v", err)
		return 0, srerr.With(srerr.DatabaseErr, "get master err", err)
	}
	var totalAffected int64 = 0
	insertLoops := len(records)/1000 + 1 //按1000条一批进行插入
	logger.CtxLogInfof(ctx, "InsertAllocateForecastRank|before bulk insert, length of total:%v", len(records))
	for i := 0; i < insertLoops; i++ {
		startIndex := i * 1000
		endIndex := (i + 1) * 1000
		if i == (insertLoops - 1) { //最后一批的长度需要额外设置
			endIndex = len(records)
		}
		logger.CtxLogInfof(ctx, "InsertAllocateForecastRank|start:%v, end:%v, i:%v", startIndex, endIndex, i)
		var insertValues []interface{}
		for i := startIndex; i < endIndex; i++ {
			order := records[i]
			maskingProductId := order.MaskingProductID
			productId := order.ProductId
			rankCode := order.RankCode
			rankType := order.RankType
			forecastTaskId := order.ForecastTaskId
			orderQuantity := order.OrderQuantity
			operatedBy := ""
			ctime := uint32(recorder.Now(ctx).Unix())
			mtime := uint32(recorder.Now(ctx).Unix())
			insertValues = append(insertValues, forecastTaskId, maskingProductId, productId, rankCode, rankType, orderQuantity, operatedBy, ctime, mtime)
		}
		valueColumns := buildBulkSQLPlaceholders(incrColumns, len(insertValues)/len(incrColumns))
		insertSql := fmt.Sprintf(
			incrForecastDbSql,
			model.AllocateForecastRankHook.TableName(),
			buildSQLColumns(incrForecastColumns),
			valueColumns,
			buildUpdateSetSQL("order_quantity"),
		)
		logger.CtxLogInfof(ctx, "InsertAllocateForecastRank| insert sql:%v", insertSql)
		d := db.Table(model.AllocateForecastRankHook.TableName()).Exec(insertSql, insertValues...)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--insert allocate forecast rank error|insertErr=%v", d.GetError())
			return 0, srerr.With(srerr.DatabaseErr, nil, d.GetError())
		}
		totalAffected = totalAffected + d.RowsAffected()
	}

	logger.CtxLogInfof(ctx, "InsertAllocateHistoricalRank| rows affected:%v", totalAffected)
	return totalAffected, nil
}

func (a AllocateForecastRankRepoImpl) GetForecastRankList(ctx context.Context, taskId, rankType int, rankCode string) ([]*model.AllocateForecastRankTab, *srerr.Error) {
	var rankList []*model.AllocateForecastRankTab
	condition := map[string]interface{}{}
	condition["rank_type = ?"] = rankType
	condition["forecast_task_id = ?"] = taskId
	if rankCode != "" {
		condition["rank_code = ?"] = rankCode
	}
	if err := dbutil.Select(ctx, model.AllocateForecastRankHook, condition, &rankList, dbutil.WithOrder("order_quantity DESC")); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return rankList, nil
}
