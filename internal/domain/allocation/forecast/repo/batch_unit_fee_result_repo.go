package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchUnitFeeResultRepo interface {
	GetByBatchUnitId(ctx context.Context, batchUnitId uint64) ([]*model.BatchUnitFeeResultTab, *srerr.Error)
	GetListByParam(ctx context.Context, request map[string]interface{}) ([]*model.BatchUnitFeeResultTab, *srerr.Error)
	GetLastBatchBySubTask(ctx context.Context, subTaskId uint64) (model.BatchUnitFeeResultTab, *srerr.Error)
}

type BatchUnitFeeResultRepoImpl struct {
}

func NewBatchUnitFeeResultRepoImpl() *BatchUnitFeeResultRepoImpl {
	return &BatchUnitFeeResultRepoImpl{}
}

func (b *BatchUnitFeeResultRepoImpl) GetByBatchUnitId(ctx context.Context, batchUnitId uint64) ([]*model.BatchUnitFeeResultTab, *srerr.Error) {
	var unitFeeResultList []*model.BatchUnitFeeResultTab
	if err := dbutil.Select(ctx, model.BatchUnitFeeResultTabHook, map[string]interface{}{"batch_unit_id = ?": batchUnitId}, &unitFeeResultList); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get batch unit fee result database error", err)
	}
	return unitFeeResultList, nil
}

func (b *BatchUnitFeeResultRepoImpl) GetListByParam(ctx context.Context, request map[string]interface{}) ([]*model.BatchUnitFeeResultTab, *srerr.Error) {
	var unitFeeResultList []*model.BatchUnitFeeResultTab
	if err := dbutil.SearchAllDataWithBatchesWithComplex(ctx, model.BatchUnitFeeResultTabHook, &unitFeeResultList, request); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get batch unit fee result|database error", err)
	}
	return unitFeeResultList, nil
}

func (b *BatchUnitFeeResultRepoImpl) GetLastBatchBySubTask(ctx context.Context, subTaskId uint64) (model.BatchUnitFeeResultTab, *srerr.Error) {
	var unitFeeResult model.BatchUnitFeeResultTab
	if err := dbutil.Last(ctx, model.BatchUnitFeeResultTabHook, map[string]interface{}{"sub_task_id = ?": subTaskId}, &unitFeeResult); err != nil {
		if err != scormv2.ErrRecordNotFound {
			return unitFeeResult, srerr.With(srerr.DatabaseErr, nil, err)
		}
	}
	return unitFeeResult, nil
}
