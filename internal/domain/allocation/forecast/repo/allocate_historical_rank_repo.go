package repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strings"
)

var (
	incrColumns = []string{"mask_product_id", "product_id", "rank_code", "rank_type", "date_str",
		"order_quantity", "operated_by", "ctime", "mtime"}
	//incrDbSql = "insert into %s(%s) VALUES %s ON DUPLICATE KEY UPDATE %s"
)

var (
	incrHistoricalColumns = []string{"forecast_task_id", "mask_product_id", "product_id", "rank_code", "rank_type",
		"order_quantity", "operated_by", "ctime", "mtime"}
	incrHistoricalDbSql = "insert into %s(%s) VALUES %s ON DUPLICATE KEY UPDATE %s"
)

type AllocateHistoricalRankRepo interface {
	InsertAllocateHistoricalRank(ctx context.Context, records []*model.AllocateHistoricalRankTab) (int64, *srerr.Error)
	GetRankCodeList(ctx context.Context, taskId, maskingProductId, rankType int) ([]string, *srerr.Error)
	GetHistoricalRankList(ctx context.Context, taskId, maskingProductId, rankType int, rankCode string) ([]*model.AllocateHistoricalRankTab, *srerr.Error)
}

type AllocateHistoricalRankRepoImpl struct {
}

func NewAllocateHistoricalRankRepo() *AllocateHistoricalRankRepoImpl {
	allocateHistoricalRankRepoImpl := &AllocateHistoricalRankRepoImpl{}
	return allocateHistoricalRankRepoImpl
}

func (a AllocateHistoricalRankRepoImpl) InsertAllocateHistoricalRank(ctx context.Context, records []*model.AllocateHistoricalRankTab) (int64, *srerr.Error) {
	if len(records) == 0 {
		return 0, nil
	}
	db, err := dbutil.MasterDB(ctx, model.AllocateHistoricalRankHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--db error|err=%v", err)
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	var totalAffected int64 = 0
	insertLoops := len(records)/1000 + 1 //按1000条一批进行插入
	logger.CtxLogInfof(ctx, "InsertAllocateHistoricalRank|before bulk insert, length of total:%v", len(records))
	for i := 0; i < insertLoops; i++ {
		startIndex := i * 1000
		endIndex := (i + 1) * 1000
		if i == (insertLoops - 1) { //最后一批的长度需要额外设置
			endIndex = len(records)
		}
		logger.CtxLogInfof(ctx, "InsertAllocateHistoricalRank|start:%v, end:%v, i:%v", startIndex, endIndex, i)
		var insertValues []interface{}
		for i := startIndex; i < endIndex; i++ {
			record := records[i]
			maskingProductId := record.MaskingProductID
			productId := record.ProductId
			rankCode := record.RankCode
			rankType := record.RankType
			forecastTaskId := record.ForecastTaskId
			orderQuantity := record.OrderQuantity
			operatedBy := ""
			ctime := uint32(recorder.Now(ctx).Unix())
			mtime := uint32(recorder.Now(ctx).Unix())
			insertValues = append(insertValues, forecastTaskId, maskingProductId, productId, rankCode, rankType, orderQuantity, operatedBy, ctime, mtime)
		}
		valueColumns := buildBulkSQLPlaceholders(incrColumns, len(insertValues)/len(incrColumns))
		insertSql := fmt.Sprintf(
			incrHistoricalDbSql,
			model.AllocateHistoricalRankHook.TableName(),
			buildSQLColumns(incrHistoricalColumns),
			valueColumns,
			buildUpdateSetSQL("order_quantity"),
		)
		logger.CtxLogInfof(ctx, "InsertAllocateHistoricalRank| insert value length:%v", len(insertValues))
		d := db.Table(model.AllocateHistoricalRankHook.TableName()).Exec(insertSql, insertValues...)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--insert allocate historical rank error|insertErr=%v", d.GetError())
			return 0, srerr.With(srerr.DatabaseErr, nil, d.GetError())
		}
		totalAffected = totalAffected + d.RowsAffected()
	}

	logger.CtxLogInfof(ctx, "InsertAllocateHistoricalRank| rows affected:%v", totalAffected)
	return totalAffected, nil
}

func (a AllocateHistoricalRankRepoImpl) GetRankCodeList(ctx context.Context, taskId int, maskingProductId, rankType int) ([]string, *srerr.Error) {
	var rankCodeList []string
	db, err := dbutil.SlaveDB(ctx, model.AllocateHistoricalRankHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get slave err", err)
	}
	var rankList []*model.AllocateHistoricalRankTab
	d := db.Table(model.AllocateHistoricalRankHook.TableName()).Where("forecast_task_id = ? AND mask_product_id = ? AND rank_type = ?", taskId, maskingProductId, rankType).Select("distinct(rank_code)").Find(&rankList)
	if d.GetError() != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	for _, rank := range rankList {
		rankCodeList = append(rankCodeList, rank.RankCode)
	}
	return rankCodeList, nil
}

func (a AllocateHistoricalRankRepoImpl) GetHistoricalRankList(ctx context.Context, taskId, maskingProductId, rankType int, rankCode string) ([]*model.AllocateHistoricalRankTab, *srerr.Error) {
	var rankList []*model.AllocateHistoricalRankTab
	condition := map[string]interface{}{}
	condition["mask_product_id = ?"] = maskingProductId
	condition["rank_type = ?"] = rankType
	condition["forecast_task_id = ?"] = taskId
	if rankCode != "" {
		condition["rank_code = ?"] = rankCode
	}
	if err := dbutil.Select(ctx, model.AllocateHistoricalRankHook, condition, &rankList, dbutil.WithOrder("order_quantity DESC")); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return rankList, nil
}

func buildBulkSQLPlaceholders(columns []string, num int) string {
	values := buildSQLPlaceholders(columns)
	placeholders := make([]string, num)
	for i := 0; i < num; i++ {
		placeholders[i] = "(" + values + ")"
	}
	return strings.Join(placeholders, ",")
}

func buildSQLPlaceholders(columns []string) string {
	placeholders := make([]string, len(columns))
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ",")
}

func buildSQLColumns(columns []string) string {
	columnReprs := make([]string, len(columns))
	for i, col := range columns {
		columnReprs[i] = "`" + col + "`"
	}
	return strings.Join(columnReprs, ",")
}

func buildUpdateSetSQL(name string) string {
	sql := "`" + name + "` = " + name + " + VALUES(" + name + ")"
	return sql
}
