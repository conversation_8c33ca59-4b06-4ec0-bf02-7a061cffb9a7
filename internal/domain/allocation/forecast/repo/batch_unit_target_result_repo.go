package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchUnitTargetResultRepo interface {
	GetByBatchUnitId(ctx context.Context, batchUnitId uint64) ([]*model.BatchUnitTargetResultTab, *srerr.Error)
}

type BatchUnitTargetResultRepoImpl struct {
}

func NewBatchUnitTargetResultRepoImap() *BatchUnitTargetResultRepoImpl {
	return &BatchUnitTargetResultRepoImpl{}
}

func (b *BatchUnitTargetResultRepoImpl) GetByBatchUnitId(ctx context.Context, batchUnitId uint64) ([]*model.BatchUnitTargetResultTab, *srerr.Error) {
	var targetResultList []*model.BatchUnitTargetResultTab
	if err := dbutil.Select(ctx, model.BatchUnitTargetResultTabHook, map[string]interface{}{"batch_unit_id = ?": batchUnitId}, &targetResultList); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get batch unit target result database error", err)
	}
	return targetResultList, nil
}
