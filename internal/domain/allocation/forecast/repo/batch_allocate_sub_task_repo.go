package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchAllocateSubTaskRepo interface {
	GetBASubTaskListByParam(ctx context.Context, param map[string]interface{}) ([]*model.BatchAllocateSubTaskTab, *srerr.Error)
}

type BatchAllocateSubTaskRepoImpl struct {
}

func NewBatchAllocateSubTaskRepoImpl() *BatchAllocateSubTaskRepoImpl {
	return &BatchAllocateSubTaskRepoImpl{}
}

func (b *BatchAllocateSubTaskRepoImpl) GetBASubTaskListByParam(ctx context.Context, param map[string]interface{}) ([]*model.BatchAllocateSubTaskTab, *srerr.Error) {
	var subTaskList []*model.BatchAllocateSubTaskTab
	if err := dbutil.SearchAllDataWithBatchesWithComplex(ctx, model.BatchAllocateSubTaskTabHook, &subTaskList, param); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "GetForecastUnitListByParam|database error", err)
	}
	return subTaskList, nil
}
