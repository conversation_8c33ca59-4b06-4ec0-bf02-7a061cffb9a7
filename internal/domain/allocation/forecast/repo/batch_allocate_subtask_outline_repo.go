package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchAllocateSubTaskOutlineRepo interface {
	GetOutlineByCondition(ctx context.Context, condition map[string]interface{}) (model.BAForecastSubTaskResultOutlineTab, *srerr.Error)
}

type BatchAllocateSubTaskOutlineRepoImpl struct {
}

func NewBatchAllocateSubTaskOutlineRepoImpl() *BatchAllocateSubTaskOutlineRepoImpl {
	return &BatchAllocateSubTaskOutlineRepoImpl{}
}

func (b *BatchAllocateSubTaskOutlineRepoImpl) GetOutlineByCondition(ctx context.Context, condition map[string]interface{}) (model.BAForecastSubTaskResultOutlineTab, *srerr.Error) {
	var tab model.BAForecastSubTaskResultOutlineTab
	if err := dbutil.Take(ctx, model.BAForecastSubTaskResultOutlineTabHook, condition, &tab); err != nil {
		return tab, srerr.With(srerr.DatabaseErr, "GetOutlineByCondition|database error", err)
	}
	return tab, nil
}
