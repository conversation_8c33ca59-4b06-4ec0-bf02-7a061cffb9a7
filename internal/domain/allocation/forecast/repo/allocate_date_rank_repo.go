package repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

var (
	dateIncrcolumns = []string{"mask_product_id", "product_id", "rank_code", "rank_type", "date_str",
		"order_quantity", "operated_by", "ctime", "mtime"}
	dateIncrDbSql = "insert into %s(%s) VALUES %s ON DUPLICATE KEY UPDATE %s"
)

type AllocateDateRankRepo interface {
	InsertAllocateDateRank(ctx context.Context, records []*model.AllocateDateRankTab) (int64, *srerr.Error)
	GetRankCodeList(ctx context.Context, maskingProductId, rankType int) ([]string, *srerr.Error)
	GetDateRankList(ctx context.Context, maskingProductId, rankType int, rankCode string, dateList []string) ([]*model.AllocateDateRankTab, *srerr.Error)
	GetBACodeList(ctx context.Context, condition map[string]interface{}) ([]string, *srerr.Error)
	GetDateRankListByCondition(ctx context.Context, condition map[string]interface{}) ([]model.AllocateDateRankTab, *srerr.Error)
}

type AllocateDateRankRepoImpl struct {
}

func NewAllocateDateRankRepo() *AllocateDateRankRepoImpl {
	allocateDateRankRepoImpl := &AllocateDateRankRepoImpl{}
	return allocateDateRankRepoImpl
}

func (a *AllocateDateRankRepoImpl) InsertAllocateDateRank(ctx context.Context, records []*model.AllocateDateRankTab) (int64, *srerr.Error) {
	if len(records) == 0 {
		return 0, nil
	}
	db, err := dbutil.MasterDB(ctx, model.AllocateDateRankHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--db error|err=%v", err)
		return 0, srerr.With(srerr.DatabaseErr, "get master err", err)
	}
	var insertValues []interface{}
	for _, order := range records {
		maskingProductId := order.MaskingProductID
		productId := order.ProductId
		rankCode := order.RankCode
		rankType := order.RankType
		date := order.Date
		orderQuantity := order.OrderQuantity
		operatedBy := ""
		ctime := uint32(recorder.Now(ctx).Unix())
		mtime := uint32(recorder.Now(ctx).Unix())
		insertValues = append(insertValues, maskingProductId, productId, rankCode, rankType, date, orderQuantity, operatedBy, ctime, mtime)
	}
	valueColumns := buildBulkSQLPlaceholders(dateIncrcolumns, len(insertValues)/len(dateIncrcolumns))
	insertSql := fmt.Sprintf(
		dateIncrDbSql,
		model.AllocateDateRankHook.TableName(),
		buildSQLColumns(dateIncrcolumns),
		valueColumns,
		buildUpdateSetSQL("order_quantity"),
	)
	d := db.Table(model.AllocateDateRankHook.TableName()).Exec(insertSql, insertValues...)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--insert allocate date rank error|insertErr=%s", d.GetError())
		return 0, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	rowAffected := d.RowsAffected()
	return rowAffected, nil
}

func (a *AllocateDateRankRepoImpl) GetRankCodeList(ctx context.Context, maskingProductId, rankType int) ([]string, *srerr.Error) {
	var rankCodeList []string
	db, err := dbutil.SlaveDB(ctx, model.AllocateDateRankHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get slave err", err)
	}
	var rankList []*model.AllocateDateRankTab
	d := db.Table(model.AllocateDateRankHook.TableName()).Where("mask_product_id = ? AND rank_type = ?", maskingProductId, rankType).Select("distinct(rank_code)").Find(&rankList)
	if d.GetError() != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	for _, rank := range rankList {
		rankCodeList = append(rankCodeList, rank.RankCode)
	}
	return rankCodeList, nil
}

func (a *AllocateDateRankRepoImpl) GetDateRankList(ctx context.Context, maskingProductId, rankType int, rankCode string, dateList []string) ([]*model.AllocateDateRankTab, *srerr.Error) {
	var dateRankList []*model.AllocateDateRankTab
	condition := map[string]interface{}{}
	condition["mask_product_id = ?"] = maskingProductId
	condition["rank_type = ?"] = rankType
	if dateList != nil {
		condition["date_str in (?)"] = dateList
	}
	if rankCode != "" {
		condition["rank_code = ?"] = rankCode
	}
	if err := dbutil.Select(ctx, model.AllocateDateRankHook, condition, &dateRankList, dbutil.WithOrder("order_quantity DESC")); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return dateRankList, nil
}

//获取batch allocate 历史订单code list
func (a *AllocateDateRankRepoImpl) GetBACodeList(ctx context.Context, condition map[string]interface{}) ([]string, *srerr.Error) {
	var codeList []string
	var rankList []*model.AllocateDateRankTab
	if err := dbutil.Select(ctx, model.AllocateDateRankHook, condition, &rankList); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	for _, rank := range rankList {
		codeList = append(codeList, rank.RankCode)
	}
	return codeList, nil
}

func (a *AllocateDateRankRepoImpl) GetDateRankListByCondition(ctx context.Context, condition map[string]interface{}) ([]model.AllocateDateRankTab, *srerr.Error) {
	var tabs []model.AllocateDateRankTab
	if err := dbutil.Select(ctx, model.AllocateDateRankHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tabs, nil
}
