package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type AllocateHistoryOutlineRepo interface {
	GetHistoryTabList(ctx context.Context, condition map[string]interface{}) ([]model.AllocateHistoryOutlineTab, *srerr.Error)
	GetHistoryTab(ctx context.Context, condition map[string]interface{}) (model.AllocateHistoryOutlineTab, *srerr.Error)
	BatchInsert(ctx context.Context, tabs []model.AllocateHistoryOutlineTab) *srerr.Error
}

type AllocateHistoryOutlineImpl struct {
}

func NewAllocateHistoryOutlineImpl() *AllocateHistoryOutlineImpl {
	return &AllocateHistoryOutlineImpl{}
}

func (a *AllocateHistoryOutlineImpl) GetHistoryTabList(ctx context.Context, condition map[string]interface{}) ([]model.AllocateHistoryOutlineTab, *srerr.Error) {
	var tabs []model.AllocateHistoryOutlineTab
	if err := dbutil.Select(ctx, model.AllocateHistoryOutlineTabHook, condition, &tabs); err != nil {
		logger.CtxLogErrorf(ctx, "AllocateHistoryOutlineImpl|condition:%v, get tabs err:%v", condition, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tabs, nil
}

func (a *AllocateHistoryOutlineImpl) GetHistoryTab(ctx context.Context, condition map[string]interface{}) (model.AllocateHistoryOutlineTab, *srerr.Error) {
	var tab model.AllocateHistoryOutlineTab
	if err := dbutil.Select(ctx, model.AllocateHistoryOutlineTabHook, condition, &tab); err != nil {
		logger.CtxLogErrorf(ctx, "AllocateHistoryOutlineImpl|condition:%v, get tab err:%v", condition, err)
		return tab, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tab, nil
}

func (a *AllocateHistoryOutlineImpl) BatchInsert(ctx context.Context, tabs []model.AllocateHistoryOutlineTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, model.AllocateHistoryOutlineTabHook, tabs); err != nil {
		logger.CtxLogErrorf(ctx, "AllocateHistoryOutlineImpl| batch insert err:%v", err)
		return srerr.With(srerr.DatabaseErr, "batch insert unit result database error", err)
	}
	return nil
}
