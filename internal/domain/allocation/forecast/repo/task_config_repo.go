package repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
)

type AllocateForecastTaskConfigRepo interface {
	CreateForecastTaskConfig(ctx context.Context, config *model.AllocateForecastTaskConfigTab) *srerr.Error
	UpdateForecastTaskConfig(ctx context.Context, config *model.AllocateForecastTaskConfigTab) *srerr.Error
	UpdateTaskWithCondition(ctx context.Context, condition map[string]interface{}, value map[string]interface{}, maskingProductId uint64) *srerr.Error
	DelForecastTaskConfig(ctx context.Context, taskId int64) *srerr.Error
	GetForecastTaskConfigById(ctx context.Context, taskId int64) (*model.AllocateForecastTaskConfigTab, *srerr.Error)
	GetForecastTaskConfigByStatus(ctx context.Context, status int) ([]*model.AllocateForecastTaskConfigTab, *srerr.Error)
	ListForecastTaskConfigs(ctx context.Context, info *model.AllocateForecastTaskConfigTab, offset, size int64) ([]*model.AllocateForecastTaskConfigTab, int64, *srerr.Error)
	GetForecastTaskConfigByCondition(ctx context.Context, condition map[string]interface{}) ([]*model.AllocateForecastTaskConfigTab, *srerr.Error)
	UpdateBAForecastTaskStatus(ctx context.Context, forecastTaskConfigList []*model.AllocateForecastTaskConfigTab, subTaskList []*model.BatchAllocateSubTaskTab, subTaskOutlineList []*model.BAForecastSubTaskResultOutlineTab) *srerr.Error
}

type AllocateForecastTaskConfigRepoImpl struct {
}

func NewAllocateForecastTaskConfigRepoImpl() *AllocateForecastTaskConfigRepoImpl {
	return &AllocateForecastTaskConfigRepoImpl{}
}

func (receiver *AllocateForecastTaskConfigRepoImpl) GetForecastTaskConfigById(ctx context.Context, taskId int64) (*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	condition := map[string]interface{}{
		"id = ?": taskId,
	}
	configTab := &model.AllocateForecastTaskConfigTab{}
	//todo:SSCSMR-517:测试take是否会报record not found
	logger.CtxLogDebugf(ctx, "GetForecastTaskConfigById|condition:%v", condition)
	if err := dbutil.Take(ctx, model.AllocateForecastTaskConfigHook, condition, configTab); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if configTab.Id == 0 || configTab.MaskingProductID == 0 {
		return nil, srerr.New(srerr.EmptyResultErr, nil, "forecast task not found for task_id(%v)", taskId)
	}
	return configTab, nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) ListForecastTaskConfigs(ctx context.Context, info *model.AllocateForecastTaskConfigTab, offset, size int64) ([]*model.AllocateForecastTaskConfigTab, int64, *srerr.Error) {
	var allocateForecastTaskConfigs []*model.AllocateForecastTaskConfigTab
	condition := map[string]interface{}{}
	if info.MaskingProductID != 0 {
		condition["mask_product_id = ?"] = info.MaskingProductID
	}
	if info.Status != 0 {
		condition["task_status = ?"] = info.Status
	}
	if info.AllocationMethod != 0 {
		condition["allocation_method = ?"] = info.AllocationMethod
	}
	var total int64
	if err := dbutil.Count(ctx, model.AllocateForecastTaskConfigHook, condition, &total); err != nil {
		return nil, total, srerr.With(srerr.DatabaseErr, nil, err)
	}

	if total == 0 {
		return allocateForecastTaskConfigs, total, nil
	}
	if err := dbutil.Select(ctx, model.AllocateForecastTaskConfigHook, condition, &allocateForecastTaskConfigs, dbutil.WithPage(offset, size), dbutil.WithOrder("id desc")); err != nil {
		return nil, total, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return allocateForecastTaskConfigs, total, nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) CreateForecastTaskConfig(ctx context.Context, config *model.AllocateForecastTaskConfigTab) *srerr.Error {
	if err := dbutil.Insert(ctx, config, dbutil.ModelInfo{}); err != nil {
		if strings.Contains(err.Error(), "Duplicate") {
			return srerr.New(srerr.DatabaseErr, nil, "Duplicate Data : task_name：%s", config.TaskName)
		}
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) UpdateForecastTaskConfig(ctx context.Context, config *model.AllocateForecastTaskConfigTab) *srerr.Error {
	if err := dbutil.Update(ctx, model.AllocateForecastTaskConfigHook, map[string]interface{}{
		"`id` = ?": config.Id,
	}, map[string]interface{}{
		"task_name":                          config.TaskName,
		"mask_product_id":                    config.MaskingProductID,
		"shop_group_channel_priority_toggle": config.ShopGroupChannelPriorityToggle,
		"local_soft_criteria_toggle":         config.LocalSoftCriteriaToggle,
		"run_soft_rule_only_toggle":          config.RunSoftRuleOnlyToggle,
		"order_paid_time":                    config.OrderPaidTime,
		"task_status":                        config.Status,
		"sync_desc":                          config.SyncDesc,
		"product_priority_configs":           config.ProductPriorityConfigs,
		"allocation_rule_config":             config.AllocationRuleConfig,
		"deploy_config_detail":               config.DeployConfigDetail,
		"config_sync_status":                 config.ConfigSyncStatus,
		"complete_time":                      config.CompleteTime,
		"latest_deploy_time":                 config.LatestDeployTime,
		"effective_start_time":               config.EffectiveStartTime,
		"operated_by":                        config.OperatedBy,
		"mtime":                              config.MTime,
		"deploy_failed_desc":                 config.DeployFailedDesc,
		"re_calc_fee":                        config.RecalculateShippingFeeToggle,
		"allocation_method":                  config.AllocationMethod,
	}, dbutil.ModelInfo{
		MaskProductId: uint64(config.MaskingProductID),
		ModelName:     model.AllocateForecastTaskConfigHook.TableName(),
	}); err != nil {
		if strings.Contains(err.Error(), "Duplicate") {
			return srerr.New(srerr.DatabaseErr, nil, "Duplicate Data : task_name：%s", config.TaskName)
		}
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) UpdateTaskWithCondition(ctx context.Context, condition map[string]interface{}, value map[string]interface{}, maskingProductId uint64) *srerr.Error {
	if err := dbutil.Update(ctx, model.AllocateForecastTaskConfigHook, condition, value,
		dbutil.ModelInfo{
			MaskProductId: maskingProductId,
			ModelName:     model.AllocateForecastTaskConfigHook.TableName(),
		}); err != nil {
		if strings.Contains(err.Error(), "Duplicate") {
			return srerr.New(srerr.DatabaseErr, nil, "Duplicate Data : %v", err.Error())
		}
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) DelForecastTaskConfig(ctx context.Context, taskId int64) *srerr.Error {
	if err := dbutil.Delete(ctx, model.AllocateForecastTaskConfigHook, map[string]interface{}{
		"id = ?": taskId,
	}, dbutil.ModelInfo{
		TaskId:    uint64(taskId),
		ModelName: model.AllocateForecastTaskConfigHook.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) GetForecastTaskConfigByStatus(ctx context.Context, status int) ([]*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	var allocateForecastTaskConfigs []*model.AllocateForecastTaskConfigTab
	condition := map[string]interface{}{}
	condition["task_status = ?"] = status
	if err := dbutil.Select(ctx, model.AllocateForecastTaskConfigHook, condition, &allocateForecastTaskConfigs, dbutil.WithOrder("mtime desc")); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return allocateForecastTaskConfigs, nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) GetForecastTaskConfigByCondition(ctx context.Context, condition map[string]interface{}) ([]*model.AllocateForecastTaskConfigTab, *srerr.Error) {
	var allocateForecastTaskConfigs []*model.AllocateForecastTaskConfigTab
	if err := dbutil.Select(ctx, model.AllocateForecastTaskConfigHook, condition, &allocateForecastTaskConfigs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return allocateForecastTaskConfigs, nil
}

func (receiver *AllocateForecastTaskConfigRepoImpl) UpdateBAForecastTaskStatus(ctx context.Context, forecastTaskConfigList []*model.AllocateForecastTaskConfigTab, subTaskList []*model.BatchAllocateSubTaskTab, subTaskOutlineList []*model.BAForecastSubTaskResultOutlineTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, model.AllocateForecastTaskConfigHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	ctx = scormv2.BindContext(ctx, db)
	txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		for _, forecastTaskConfig := range forecastTaskConfigList {
			dbErr := tx.Table(model.AllocateForecastTaskConfigHook.TableName()).Updates(forecastTaskConfig).GetError()
			if dbErr != nil {
				return dbErr
			}
		}
		for _, subTask := range subTaskList {
			dbErr := tx.Table(model.BatchAllocateSubTaskTabHook.TableName()).Updates(subTask).GetError()
			if dbErr != nil {
				return dbErr
			}
		}
		if len(subTaskOutlineList) > 0 {
			dbErr := tx.Table(model.BAForecastSubTaskResultOutlineTabHook.TableName()).CreateInBatches(subTaskOutlineList, 1000).GetError()
			if dbErr != nil {
				return dbErr
			}
		}
		return nil
	})
	if txErr != nil {
		return srerr.With(srerr.DatabaseErr, "update ba forecast task status error", txErr)
	}
	return nil
}

func BatchVolumeKey(taskId uint64) string {
	return fmt.Sprintf("%v:%v:%v", model.BatchForecastVolumePrefix, envvar.GetCIDLower(), taskId)
}
