package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchAllocateForecastUnitResultRepo interface {
	BatchInsertData(ctx context.Context, unitResultList []*model.BAForecastUnitResultTab) *srerr.Error
	GetResultsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastUnitResultTab, *srerr.Error)
	BatchInsertPickupEffResult(ctx context.Context, unitResultList []*model.BAForecastPickupEffResultTab) *srerr.Error
	GetPickupEffResultsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastPickupEffResultTab, *srerr.Error)
}

type BatchAllocateForecastUnitResultRepoImpl struct {
}

func NewBatchAllocateForecastUnitResultRepoImpl() *BatchAllocateForecastUnitResultRepoImpl {
	return &BatchAllocateForecastUnitResultRepoImpl{}
}

func (b *BatchAllocateForecastUnitResultRepoImpl) BatchInsertData(ctx context.Context, unitResultList []*model.BAForecastUnitResultTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, model.BAForecastUnitResultTabHook, unitResultList); err != nil {
		return srerr.With(srerr.DatabaseErr, "batch insert unit result database error", err)
	}
	return nil
}

func (b *BatchAllocateForecastUnitResultRepoImpl) BatchInsertPickupEffResult(ctx context.Context, unitResultList []*model.BAForecastPickupEffResultTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, model.BAForecastPickupEffResultTabHook, unitResultList); err != nil {
		return srerr.With(srerr.DatabaseErr, "batch insert pickup eff unit result database error", err)
	}
	return nil
}

func (b *BatchAllocateForecastUnitResultRepoImpl) GetResultsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastUnitResultTab, *srerr.Error) {
	var tabs []model.BAForecastUnitResultTab
	if err := dbutil.Select(ctx, model.BAForecastUnitResultTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get empty results", err)
	}

	return tabs, nil
}

func (b *BatchAllocateForecastUnitResultRepoImpl) GetPickupEffResultsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastPickupEffResultTab, *srerr.Error) {
	var tabs []model.BAForecastPickupEffResultTab
	if err := dbutil.Select(ctx, model.BAForecastPickupEffResultTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get empty results", err)
	}

	return tabs, nil
}
