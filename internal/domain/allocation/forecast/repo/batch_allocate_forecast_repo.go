package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchAllocateForecastRepo interface {
	GetSubTaskList(ctx context.Context, mainTaskId uint64) ([]model.BatchAllocateSubTaskTab, *srerr.Error)
	GetSubTask(ctx context.Context, condition map[string]interface{}) (model.BatchAllocateSubTaskTab, *srerr.Error)
	GetSubTaskUnitResultListByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastUnitResultTab, *srerr.Error)
	GetSubTaskUnitByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BatchAllocateForecastUnitTab, *srerr.Error)
	GetForecastUnitsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BatchAllocateForecastUnitTab, *srerr.Error)
	UpdateForecastUnit(ctx context.Context, condition map[string]interface{}, tab model.BatchAllocateForecastUnitTab) *srerr.Error
	UpdateSubTaskByCondition(ctx context.Context, condition, value map[string]interface{}) *srerr.Error
}

type BatchAllocateForecastRepoImpl struct {
}

func NewBatchAllocateForecastRepoImpl() *BatchAllocateForecastRepoImpl {
	return &BatchAllocateForecastRepoImpl{}
}

func (b *BatchAllocateForecastRepoImpl) GetSubTaskList(ctx context.Context, mainTaskId uint64) ([]model.BatchAllocateSubTaskTab, *srerr.Error) {
	var tabs []model.BatchAllocateSubTaskTab
	if err := dbutil.Select(ctx, model.BatchAllocateSubTaskTabHook, map[string]interface{}{
		"forecast_task_id = ?": mainTaskId,
	}, &tabs); err != nil {
		logger.CtxLogErrorf(ctx, "GetSubTaskList| get sub task list err:%v", err)
		return tabs, srerr.New(srerr.DatabaseErr, nil, "select batch allocate sub task list err")
	}
	return tabs, nil
}

func (b *BatchAllocateForecastRepoImpl) GetSubTaskUnitResultListByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BAForecastUnitResultTab, *srerr.Error) {
	var tabs []model.BAForecastUnitResultTab
	if err := dbutil.Select(ctx, model.BAForecastUnitResultTabHook, condition, &tabs); err != nil {
		return tabs, srerr.New(srerr.DatabaseErr, nil, "select batch allocate sub task unit result list err")
	}
	return tabs, nil
}

func (b *BatchAllocateForecastRepoImpl) GetSubTaskUnitByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BatchAllocateForecastUnitTab, *srerr.Error) {
	var tabs []model.BatchAllocateForecastUnitTab
	if err := dbutil.Select(ctx, model.BatchAllocateForecastUnitTabHook, condition, &tabs); err != nil {
		logger.CtxLogErrorf(ctx, "GetSubTaskUnitByCondition|select batch allocate sub task unit list err:%v", err)
		return tabs, srerr.New(srerr.DatabaseErr, nil, "select batch allocate sub task unit list err")
	}
	return tabs, nil
}

func (b *BatchAllocateForecastRepoImpl) GetForecastUnitsByCondition(ctx context.Context, condition map[string]interface{}) ([]model.BatchAllocateForecastUnitTab, *srerr.Error) {
	var tabs []model.BatchAllocateForecastUnitTab
	if err := dbutil.Select(ctx, model.BatchAllocateForecastUnitTabHook, condition, &tabs); err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastUnitsByCondition|select forecast unit tabs err:%v", err)
		return tabs, srerr.New(srerr.DatabaseErr, nil, "select forecast unit tabs err")
	}
	return tabs, nil
}

func (b *BatchAllocateForecastRepoImpl) UpdateForecastUnit(ctx context.Context, condition map[string]interface{}, tab model.BatchAllocateForecastUnitTab) *srerr.Error {
	if err, rowsAffected := dbutil.UpdateByObj(ctx, model.BatchAllocateForecastUnitTabHook, condition, tab, dbutil.ModelInfo{}); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateForecastUnit| update forecast unit err:%v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	} else if rowsAffected == 0 {
		logger.CtxLogErrorf(ctx, "UpdateForecastUnit| rows affected is 0, but no err occurs")
		return srerr.New(srerr.DatabaseErr, nil, "rows affected is 0")
	}
	return nil
}

func (b *BatchAllocateForecastRepoImpl) UpdateSubTaskByCondition(ctx context.Context, condition, value map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, model.BatchAllocateSubTaskTabHook, condition, value, dbutil.ModelInfo{}); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateSubTaskByCondition| update sub task err:%v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

//todo:SSCSMR-1698: 改成take，把record not found抛出去
func (b *BatchAllocateForecastRepoImpl) GetSubTask(ctx context.Context, condition map[string]interface{}) (model.BatchAllocateSubTaskTab, *srerr.Error) {
	var tab model.BatchAllocateSubTaskTab
	if err := dbutil.Select(ctx, model.BatchAllocateSubTaskTabHook, condition, &tab); err != nil {
		logger.CtxLogErrorf(ctx, "GetSubTask|condition:%v, err:%v", condition, err)
		return tab, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tab, nil
}
