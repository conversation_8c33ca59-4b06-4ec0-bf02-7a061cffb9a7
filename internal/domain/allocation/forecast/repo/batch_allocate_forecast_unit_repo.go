package repo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BatchAllocateForecastUnitRepo interface {
	GetForecastUnitListByParam(ctx context.Context, param map[string]interface{}) ([]*model.BatchAllocateForecastUnitTab, *srerr.Error)
	UpdateForecastUnit(ctx context.Context, condition, value map[string]interface{}) *srerr.Error
}

type BatchAllocateForecastUnitRepoImpl struct {
}

func NewBatchAllocateForecastUnitRepoImpl() *BatchAllocateForecastUnitRepoImpl {
	return &BatchAllocateForecastUnitRepoImpl{}
}

func (b *BatchAllocateForecastUnitRepoImpl) GetForecastUnitListByParam(ctx context.Context, param map[string]interface{}) ([]*model.BatchAllocateForecastUnitTab, *srerr.Error) {
	var forecastUnitTabList []*model.BatchAllocateForecastUnitTab
	if err := dbutil.SearchAllDataWithBatchesWithComplex(ctx, model.BatchAllocateForecastUnitTabHook, &forecastUnitTabList, param); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "GetForecastUnitListByParam|database error", err)
	}
	return forecastUnitTabList, nil
}

func (b *BatchAllocateForecastUnitRepoImpl) UpdateForecastUnit(ctx context.Context, condition, value map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, model.BatchAllocateForecastUnitTabHook, condition, value, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}
