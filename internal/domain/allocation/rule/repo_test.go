package rule

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/dms/go-proto/playback"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"github.com/go-faker/faker/v4"
	"github.com/stretchr/testify/require"
	"testing"
)

type testingWrapSyncer struct {
	result playback.Transaction
}

func (t *testingWrapSyncer) DoRequest(ctx context.Context, req *recorder.MockRequest) (isMatch bool, respData string, err error) {

	resp := t.result.OutBoundReqs[0].RspData

	t.result.OutBoundReqs = t.result.OutBoundReqs[1:]

	return true, resp, nil

}

func newTestingWrapSyncer() *testingWrapSyncer {

	return &testingWrapSyncer{}

}

func (t *testingWrapSyncer) Send(ctx context.Context, tran playback.Transaction) error {
	t.result = tran
	return nil
}

func (t *testingWrapSyncer) Stop(ctx context.Context) error {
	return nil
}

func TestWrap_SMR(t *testing.T) {
	get := func(ctx context.Context, key string) (interface{}, error) {
		a := MaskAllocationRuleTab{}
		faker.FakeData(&a)
		return &a, nil
	}
	sy := newTestingWrapSyncer()
	recorder.SetSyncer(sy)
	recorder.SetMocker(sy)
	gob.Register(&MaskAllocationRuleTab{})

	testPayload := "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"
	bs, err := base64.StdEncoding.DecodeString(testPayload)
	require.NoError(t, err)
	buf := bytes.NewBuffer(bs)
	o := recorder.Outs{}
	err = gob.NewDecoder(buf).Decode(&o)
	require.NoError(t, err)
	t.Logf("xxxxxx %+v\n", o)

	wrapped := recorder.Wrap(get).(func(ctx context.Context, key string) (val interface{}, err error))

	ctx := context.Background()
	ctx = recorder.StartRecordTransaction(ctx, "r-0001")
	val1, err1 := wrapped(ctx, "namespace1")
	recorder.EndRecordTransaction(ctx)

	ctx = context.Background()
	ctx = recorder.SetReplayId(ctx, "r-0001")
	val2, err2 := wrapped(ctx, "namespace1")
	require.Equal(t, err1, err2)

	v1, ok := val1.(*MaskAllocationRuleTab)
	require.True(t, ok)
	v2, ok := val2.(*MaskAllocationRuleTab)
	require.True(t, ok)
	b1, _ := json.Marshal(v1)
	b2, _ := json.Marshal(v2)
	require.JSONEq(t, string(b1), string(b2))
}

func TestAllocationRuleImpl_GetEffectiveRuleByMaskProductID(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	dbutil.Init()
	a := &AllocationRuleImpl{LpsApi: nil}
	got, err := a.GetEffectiveRuleByMaskProductID(context.Background(), 1105, 0)
	if err != nil {
		panic(err)
	}
	println(fmt.Sprintf("result is %+v", got))

}

func TestAllocationRuleImpl_GetRuleList(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	dbutil.Init()

	a := &AllocationRuleImpl{
		LpsApi: nil,
	}
	query := &RuleQuery{Offset: 0, Limit: 1}
	got, _, err := a.GetRuleList(context.Background(), query)
	if err != nil {
		panic(err)
	}

	println(len(got))
}

func TestAllocationRuleImpl_GetDraftRule(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	dbutil.Init()
	a := &AllocationRuleImpl{
		LpsApi: nil,
	}
	got, err := a.GetDraftRule(context.Background(), 1105, 0, 0)
	if err != nil {
		panic(err)
	}

	println(fmt.Sprintf("result is %v", got))
}
