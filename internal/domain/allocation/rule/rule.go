package rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
)

// MaskRuleSteps sort rule base priority
type MaskRuleSteps []MaskRuleStep

func (s MaskRuleSteps) Len() int { return len(s) }

func (s MaskRuleSteps) Swap(i, j int) { s[i], s[j] = s[j], s[i] }

func (s MaskRuleSteps) Less(i, j int) bool {
	if s[i].Priority == s[j].Priority {
		return s[i].MaskStepType < s[j].MaskStepType
	}
	return s[i].Priority < s[j].Priority
}

type (
	MaskRule struct {
		Id                    int64
		MaskProductId         int64
		MaskProductName       string
		Status                MaskRuleStatus
		RuleName              string
		LocVolumeType         rulevolume.MaskLocVolumeType
		LocVolumeId           uint64
		MaskRuleSteps         MaskRuleSteps // 小规则条目列表
		EnableProductPriority bool
		OperatedBy            string
		EffectiveStartTime    uint32
		CTime                 uint32
		MTime                 uint32
		ForecastTaskId        int64 // 预测任务id
		RuleMode              int32
		AllocationMethod      int64 //1:single; 2:batch
		BatchRuleDetail       BatchRuleDetail
		ApprovalTime          int64
	}

	MaskRuleInfo struct {
		Id   int64  `json:"id"`
		Name string `json:"name"`
	}

	// MaskRuleStep 小规则条目
	MaskRuleStep struct {
		Priority     int32
		MaskStepType MaskStepType

		// Rule Data for different type of MaskRuleStep
		MinVolumeData               *MinVolumeData
		MaxCapacityData             *MaxCapacityData
		MaxCodCapacityData          *MaxCapacityData
		MaxBulkyCapacityData        *MaxCapacityData
		MaxHighValueCapacityData    *MaxCapacityData
		MaxDgCapacityData           *MaxCapacityData
		BatchVolumeData             *BatchVolumeData
		IsHardCapacityData          *IsHardCapacityData
		IsCodHardCapacityData       *IsHardCapacityData
		IsBulkyHardCapacityData     *IsHardCapacityData
		IsHighValueHardCapacityData *IsHardCapacityData
		IsDgHardCapacityData        *IsHardCapacityData
	}

	MinVolumeData struct {
		MinVolumes      map[VolumeKey]int32
		GroupMinVolumes map[string]int32
	}

	IsHardCapacityData struct {
		IsHardCaps      map[VolumeKey]bool
		GroupIsHardCaps map[string]bool
	}

	MaxCapacityData struct {
		MaxCapacities      map[VolumeKey]int32
		GroupMaxCapacities map[string]int32
	}

	BatchVolumeData struct {
		BatchVolume      int32
		VolumeInOneBatch map[int64]int32
	}

	VolumeKey struct {
		MaskProductID        int64
		FulfillmentProductID int64
	}
)

func (r *MaskRule) GetBatchVolume() int32 {
	for _, step := range r.MaskRuleSteps {
		if step.MaskStepType == MaskStepMaxBatchVolume {
			return step.BatchVolumeData.BatchVolume
		}
	}
	return 0
}
