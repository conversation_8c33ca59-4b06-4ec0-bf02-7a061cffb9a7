package rule

type MaskStepType int

const (
	MaskStepMinVolume                     MaskStepType = 1 // Deprecating
	MaskStepMaxCapacity                   MaskStepType = 2 // Deprecating
	MaskStepMaxBatchVolume                MaskStepType = 3
	MaskStepCheapestFee                   MaskStepType = 4
	MaskStepMinVolumeCountry              MaskStepType = 5
	MaskStepMinVolumeZoneRoute            MaskStepType = 6
	MaskStepMaxCapacityCountry            MaskStepType = 7
	MaskStepMaxCapacityZoneRoute          MaskStepType = 8
	MaskStepPickupEfficiencyWhitelist     MaskStepType = 9
	MaskStepMaxCodCapacityCountry         MaskStepType = 10
	MaskStepMaxCodCapacityZoneRoute       MaskStepType = 11
	MaskStepMaxBulkyCapacityCountry       MaskStepType = 12
	MaskStepMaxBulkyCapacityZoneRoute     MaskStepType = 13
	MaskStepMaxHighValueCapacityCountry   MaskStepType = 14
	MaskStepMaxHighValueCapacityZoneRoute MaskStepType = 15
	MaskStepMaxDgCapacityCountry          MaskStepType = 16
	MaskStepMaxDgCapacityZoneRoute        MaskStepType = 17
)

const (
	MaskStepMinVolumeName                     = "StepMinVolume"
	MaskStepMaxCapacityName                   = "StepMaxCapacity"
	MaskStepMaxBatchVolumeName                = "StepMaxBatchVolume"
	MaskStepCheapestFeeName                   = "StepCheapestFee"
	MaskStepMinVolumeCountryName              = "StepMinVolumeCountry"
	MaskStepMinVolumeZoneRouteName            = "StepMinVolumeZoneRoute"
	MaskStepMaxCapacityCountryName            = "StepMaxCapacityCountry"
	MaskStepMaxCapacityZoneRouteName          = "StepMaxCapacityZoneRoute"
	MaskStepPickupEfficiencyWhitelistName     = "StepPickupEfficiencyWhitelist"
	MaskStepMaxCodCapacityCountryName         = "StepMaxCodCapacityCountry"
	MaskStepMaxCodCapacityZoneRouteName       = "StepMaxCodCapacityZoneRoute"
	MaskStepMaxBulkyCapacityCountryName       = "StepMaxBulkyCapacityCountry"
	MaskStepMaxBulkyCapacityZoneRouteName     = "StepMaxBulkyCapacityZoneRoute"
	MaskStepMaxHighValueCapacityCountryName   = "StepMaxHighValueCapacityCountry"
	MaskStepMaxHighValueCapacityZoneRouteName = "StepMaxHighValueCapacityZoneRoute"
	MaskStepMaxDgCapacityCountryName          = "StepMaxDgCapacityCountry"
	MaskStepMaxDgCapacityZoneRouteName        = "StepMaxDgCapacityZoneRoute"
)

var MaskAllSteps = []MaskStepType{
	//MaskStepMinVolume,
	//MaskStepMaxCapacity,
	MaskStepMaxBatchVolume,
	MaskStepCheapestFee,
	MaskStepMinVolumeCountry,
	MaskStepMinVolumeZoneRoute,
	MaskStepMaxCapacityCountry,
	MaskStepMaxCapacityZoneRoute,
	MaskStepPickupEfficiencyWhitelist,
	MaskStepMaxCodCapacityCountry,
	MaskStepMaxCodCapacityZoneRoute,
	MaskStepMaxBulkyCapacityCountry,
	MaskStepMaxBulkyCapacityZoneRoute,
	MaskStepMaxHighValueCapacityCountry,
	MaskStepMaxHighValueCapacityZoneRoute,
	MaskStepMaxDgCapacityCountry,
	MaskStepMaxDgCapacityZoneRoute,
}

func (s MaskStepType) String() string {
	switch s {
	case MaskStepMinVolume:
		return MaskStepMinVolumeName
	case MaskStepMaxCapacity:
		return MaskStepMaxCapacityName
	case MaskStepMaxBatchVolume:
		return MaskStepMaxBatchVolumeName
	case MaskStepCheapestFee:
		return MaskStepCheapestFeeName
	case MaskStepMinVolumeCountry:
		return MaskStepMinVolumeCountryName
	case MaskStepMinVolumeZoneRoute:
		return MaskStepMinVolumeZoneRouteName
	case MaskStepMaxCapacityCountry:
		return MaskStepMaxCapacityCountryName
	case MaskStepMaxCapacityZoneRoute:
		return MaskStepMaxCapacityZoneRouteName
	case MaskStepPickupEfficiencyWhitelist:
		return MaskStepPickupEfficiencyWhitelistName
	case MaskStepMaxCodCapacityCountry:
		return MaskStepMaxCodCapacityCountryName
	case MaskStepMaxCodCapacityZoneRoute:
		return MaskStepMaxCodCapacityZoneRouteName
	case MaskStepMaxBulkyCapacityCountry:
		return MaskStepMaxBulkyCapacityCountryName
	case MaskStepMaxBulkyCapacityZoneRoute:
		return MaskStepMaxBulkyCapacityZoneRouteName
	case MaskStepMaxHighValueCapacityCountry:
		return MaskStepMaxHighValueCapacityCountryName
	case MaskStepMaxHighValueCapacityZoneRoute:
		return MaskStepMaxHighValueCapacityZoneRouteName
	case MaskStepMaxDgCapacityCountry:
		return MaskStepMaxDgCapacityCountryName
	case MaskStepMaxDgCapacityZoneRoute:
		return MaskStepMaxDgCapacityZoneRouteName
	default:
		return "UnknownStep"
	}
}

func GetMaskStepType(maskStepName string) int {
	switch maskStepName {
	case MaskStepMinVolumeName:
		return int(MaskStepMinVolume)
	case MaskStepMaxCapacityName:
		return int(MaskStepMaxCapacity)
	case MaskStepMaxBatchVolumeName:
		return int(MaskStepMaxBatchVolume)
	case MaskStepCheapestFeeName:
		return int(MaskStepCheapestFee)
	case MaskStepMinVolumeCountryName:
		return int(MaskStepMinVolumeCountry)
	case MaskStepMinVolumeZoneRouteName:
		return int(MaskStepMinVolumeZoneRoute)
	case MaskStepMaxCapacityCountryName:
		return int(MaskStepMaxCapacityCountry)
	case MaskStepMaxCapacityZoneRouteName:
		return int(MaskStepMaxCapacityZoneRoute)
	case MaskStepPickupEfficiencyWhitelistName:
		return int(MaskStepPickupEfficiencyWhitelist)
	case MaskStepMaxCodCapacityCountryName:
		return int(MaskStepMaxCodCapacityCountry)
	case MaskStepMaxCodCapacityZoneRouteName:
		return int(MaskStepMaxCodCapacityZoneRoute)
	case MaskStepMaxBulkyCapacityCountryName:
		return int(MaskStepMaxBulkyCapacityCountry)
	case MaskStepMaxBulkyCapacityZoneRouteName:
		return int(MaskStepMaxBulkyCapacityZoneRoute)
	case MaskStepMaxHighValueCapacityCountryName:
		return int(MaskStepMaxHighValueCapacityCountry)
	case MaskStepMaxHighValueCapacityZoneRouteName:
		return int(MaskStepMaxHighValueCapacityZoneRoute)
	case MaskStepMaxDgCapacityCountryName:
		return int(MaskStepMaxDgCapacityCountry)
	case MaskStepMaxDgCapacityZoneRouteName:
		return int(MaskStepMaxDgCapacityZoneRoute)
	default:
		return 0
	}
}

type MaskRuleStatus int

const (
	MaskRuleStatusUncompleted     MaskRuleStatus = 0
	MaskRuleStatusDraft           MaskRuleStatus = 1
	MaskRuleStatusQueuing         MaskRuleStatus = 2 //目前实际使用中，这个状态只是用来展示的。实际获取的时候是根据active + effective time来判断是不是queueing
	MaskRuleStatusActive          MaskRuleStatus = 3
	MaskRuleStatusExpired         MaskRuleStatus = 4
	MaskRuleStatusPendingApproval MaskRuleStatus = 5
)

func (m MaskRuleStatus) Type() string {
	switch m {
	case MaskRuleStatusUncompleted:
		return "Uncompleted"
	case MaskRuleStatusDraft:
		return "Draft"
	case MaskRuleStatusQueuing:
		return "Queueing"
	case MaskRuleStatusActive:
		return "Active"
	case MaskRuleStatusExpired:
		return "Expired"
	case MaskRuleStatusPendingApproval:
		return "PendingApproval"
	}
	return ""
}
