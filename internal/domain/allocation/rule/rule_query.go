package rule

type RuleQuery struct {
	FilterByID            bool
	ID                    int64
	FilterByStatus        bool
	Status                MaskRuleStatus
	FilterByMaskProductID bool
	MaskProductID         int64
	Offset                int32
	Limit                 int32
	RuleMode              int32
	AllocationMethod      int64
}

func NewRuleQuery() *RuleQuery {
	return &RuleQuery{Offset: 0, Limit: 20}
}

func (q *RuleQuery) ByID(id int64) *RuleQuery {
	q.FilterByID = true
	q.ID = id
	return q
}

func (q *RuleQuery) ByStatus(status MaskRuleStatus) *RuleQuery {
	q.FilterByStatus = true
	q.Status = status
	return q
}

func (q *RuleQuery) ByMaskProductID(id int64) *RuleQuery {
	q.FilterByMaskProductID = true
	q.MaskProductID = id
	return q
}

func (q *RuleQuery) WithPage(pageno int32, limit int32) *RuleQuery {
	if pageno <= 0 {
		pageno = 1
	}
	if limit <= 0 {
		limit = 20
	}
	if limit > 10000 {
		limit = 10000
	}
	q.Limit = limit
	q.Offset = (pageno - 1) * limit
	return q
}

func (q *RuleQuery) WithRuleMode(rm int32) *RuleQuery {
	q.RuleMode = rm
	return q
}

func (q *RuleQuery) WithAllocationMethod(allocationMethod int64) *RuleQuery {
	q.AllocationMethod = allocationMethod
	return q
}
