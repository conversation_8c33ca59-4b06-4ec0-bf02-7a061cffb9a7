package rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"github.com/google/wire"
)

var MaskRuleProviderSet = wire.NewSet(
	NewRuleRepo,
	wire.Bind(new(IMaskRuleRepo), new(*maskRuleRepoImpl)),
	NewMaskRuleConfRepo,
	rulevolume.NewMaskRuleVolumeRepoImpl,
	NewAllocationRuleImpl,
	wire.Bind(new(AllocationRuleRepo), new(*AllocationRuleImpl)),
)
