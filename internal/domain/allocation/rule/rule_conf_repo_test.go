package rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_ruleConfRepo_GetRuleConf(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		rm rule_mode.RuleMode
	}
	tests := []struct {
		name    string
		r       *ruleConfRepo
		args    args
		want    MaskRuleConf
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: rm == rule_mode.WmsOrderRule",
			args: args{
				rm: rule_mode.WmsOrderRule,
			},
			want: MaskRuleConf{
				DisableProductPriority: false,
				Steps: []MaskStepConf{
					{
						Step: MaskStepMaxBatchVolume,
					},
					{
						Step: MaskStepCheapestFee,
					},
					{
						Step: MaskStepMinVolumeCountry,
					},
					{
						Step: MaskStepMinVolumeZoneRoute,
					},
					{
						Step: MaskStepMaxCapacityCountry,
					},
					{
						Step: MaskStepMaxCapacityZoneRoute,
					},
					{
						Step: MaskStepPickupEfficiencyWhitelist,
					},
					{
						Step: MaskStepMaxCodCapacityCountry,
					},
					{
						Step: MaskStepMaxCodCapacityZoneRoute,
					},
					{
						Step: MaskStepMaxBulkyCapacityCountry,
					},
					{
						Step: MaskStepMaxBulkyCapacityZoneRoute,
					},
					{
						Step: MaskStepMaxHighValueCapacityCountry,
					},
					{
						Step: MaskStepMaxHighValueCapacityZoneRoute,
					},
				},
			},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.RefreshWmsProductAllocationConfig, func() configutil.ProductAllocationConf {
					return configutil.ProductAllocationConf{}
				})
			},
		},
		{
			name: "case 2: rm != rule_mode.WmsOrderRule",
			args: args{
				rm: rule_mode.MplOrderRule,
			},
			want: MaskRuleConf{
				DisableProductPriority: false,
				Steps: []MaskStepConf{
					{
						Step:     MaskStepMaxBatchVolume,
						Disabled: true,
					},
					{
						Step: MaskStepCheapestFee,
					},
					{
						Step: MaskStepMinVolumeCountry,
					},
					{
						Step: MaskStepMinVolumeZoneRoute,
					},
					{
						Step: MaskStepMaxCapacityCountry,
					},
					{
						Step: MaskStepMaxCapacityZoneRoute,
					},
					{
						Step: MaskStepPickupEfficiencyWhitelist,
					},
					{
						Step: MaskStepMaxCodCapacityCountry,
					},
					{
						Step: MaskStepMaxCodCapacityZoneRoute,
					},
					{
						Step: MaskStepMaxBulkyCapacityCountry,
					},
					{
						Step: MaskStepMaxBulkyCapacityZoneRoute,
					},
					{
						Step: MaskStepMaxHighValueCapacityCountry,
					},
					{
						Step: MaskStepMaxHighValueCapacityZoneRoute,
					},
				},
			},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(configutil.GetProductAllocationConf, func(ctx context.Context) configutil.ProductAllocationConf {
					return configutil.ProductAllocationConf{
						DisableRuleSteps: "max_batch_volume",
					}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := tt.r.GetRuleConf(ctx, tt.args.rm)
			common.AssertResult(t, tt.want, got, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestMaskRuleConf_IsStepEnabled(t *testing.T) {
	var c MaskRuleConf
	type args struct {
		step MaskStepType
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: s.Step == step",
			args: args{
				step: MaskStepMaxHighValueCapacityCountry,
			},
			want: false,
			setup: func() {
				c = MaskRuleConf{
					Steps: []MaskStepConf{
						{
							Step:     MaskStepMaxHighValueCapacityCountry,
							Disabled: true,
						},
					},
				}
			},
		},
		{
			name: "case 2: s.Step not found",
			args: args{
				step: MaskStepMaxHighValueCapacityCountry,
			},
			want: true,
			setup: func() {
				c = MaskRuleConf{}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			assert.Equalf(t, tt.want, c.IsStepEnabled(tt.args.step), "IsStepEnabled(%v)", tt.args.step)
		})
	}
}
