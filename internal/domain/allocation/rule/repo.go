package rule

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/pkg/errors"
)

type AllocationRuleRepo interface {
	GetEffectiveRuleByMaskProductID(ctx context.Context, maskChannelID int64, rm rule_mode.RuleMode) (*MaskAllocationRuleTab, error)
	GetRuleByID(ctx context.Context, id int64) (*MaskAllocationRuleTab, error)
	GetRuleSimpleInfo(ctx context.Context) ([]*MaskAllocationRuleTab, error)
	GetRuleList(ctx context.Context, q *RuleQuery) ([]*MaskAllocationRuleTab, int32, error)
	GetDraftRule(ctx context.Context, maskChannelID int64, ruleMode int32, allocationMethod int64) (*MaskAllocationRuleTab, error)
	CreateRule(ctx context.Context, rule *MaskAllocationRuleTab) (*MaskAllocationRuleTab, error)
	UpdateRule(ctx context.Context, rule *MaskAllocationRuleTab) (*MaskAllocationRuleTab, error)
	DeleteRule(ctx context.Context, id int64) error
	GetRuleByCondition(ctx context.Context, condition map[string]interface{}) (*MaskAllocationRuleTab, error)
	GetRuleListByCondition(ctx context.Context, condition map[string]interface{}) ([]*MaskAllocationRuleTab, error)
}

type AllocationRuleImpl struct {
	LpsApi lpsclient.LpsApi
}

func NewAllocationRuleImpl(lpsApi lpsclient.LpsApi) *AllocationRuleImpl {
	return &AllocationRuleImpl{
		LpsApi: lpsApi,
	}
}

func (a *AllocationRuleImpl) GetEffectiveRuleByMaskProductID(ctx context.Context, maskChannelID int64, rm rule_mode.RuleMode) (*MaskAllocationRuleTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	r := &MaskAllocationRuleTab{}
	err = db.Table(tableName).
		Where("mask_product_id = ? AND rule_status = ? AND effective_start_time <= ? AND rule_mode = ?", maskChannelID, MaskRuleStatusActive, timeutil.GetCurrentUnixTimeStamp(ctx), int(rm)).
		Order("effective_start_time DESC").First(r).GetError()
	return ruleResult(ctx, err, r, dbutil.NewErrDataNotFound("mask channel rule mask_product_id", fmt.Sprintf("%v-effective", maskChannelID)))
}

func convertStatus(ctx context.Context, r *MaskAllocationRuleTab) {
	if r.Status == MaskRuleStatusActive && int64(r.EffectiveStartTime) > timeutil.GetCurrentUnixTimeStamp(ctx) {
		r.Status = MaskRuleStatusQueuing
	}
}

func (a *AllocationRuleImpl) GetRuleByID(ctx context.Context, id int64) (*MaskAllocationRuleTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	r := new(MaskAllocationRuleTab)
	err = db.Table(tableName).Where("id = ?", id).Take(r).GetError()
	return ruleResult(ctx, err, r, dbutil.NewErrDataNotFound("mask channel rule_id", id))
}

func ruleResult(ctx context.Context, err error, rule *MaskAllocationRuleTab, errNotFound *dbutil.ErrDataNotFound) (*MaskAllocationRuleTab, error) {
	if err != nil {
		if err == scormv2.ErrRecordNotFound {
			return nil, errNotFound
		}
		return nil, dbutil.NewErrDatabase(err, "db query")
	}
	convertStatus(ctx, rule)
	return rule, nil
}

func (a *AllocationRuleImpl) GetRuleSimpleInfo(ctx context.Context) ([]*MaskAllocationRuleTab, error) {
	var rules []*MaskAllocationRuleTab
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	err = db.Table(tableName).Select([]string{"id", "rule_name"}).Find(&rules).GetError()
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func (a *AllocationRuleImpl) GetRuleList(ctx context.Context, q *RuleQuery) ([]*MaskAllocationRuleTab, int32, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, 0, err
	}
	var rules []*MaskAllocationRuleTab
	query := db.Table(tableName)
	if q.FilterByID {
		query = query.Where("id=?", q.ID)
	}
	if q.FilterByMaskProductID {
		query = query.Where("mask_product_id=?", q.MaskProductID)
	}
	if q.FilterByStatus {
		ts := timeutil.GetCurrentUnixTimeStamp(ctx)
		if q.Status == MaskRuleStatusQueuing {
			query = query.Where("rule_status=? AND effective_start_time > ?", MaskRuleStatusActive, ts)
		} else if q.Status == MaskRuleStatusActive {
			query = query.Where("rule_status=? AND effective_start_time <= ?", MaskRuleStatusActive, ts)
		} else {
			query = query.Where("rule_status=?", q.Status)
		}
	}
	if q.RuleMode != int32(rule_mode.AllOrderRule) {
		query = query.Where("rule_mode = ?", q.RuleMode)
	}
	if q.AllocationMethod == allocation.BatchAllocate {
		query = query.Where("allocation_method = ?", allocation.BatchAllocate)
	} else if q.AllocationMethod == allocation.SingleAllocate {
		//兼容旧数据
		query = query.Where("allocation_method <> ?", allocation.BatchAllocate)
	}
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "count rules")
	}
	d = query.Order("id DESC").Offset(int(q.Offset)).Limit(int(q.Limit)).Find(&rules)
	if d.GetError() != nil {
		return nil, 0, dbutil.NewErrDatabase(d.GetError(), "get rule list")
	}
	for _, r := range rules {
		convertStatus(ctx, r)
	}
	return rules, int32(total), nil
}

func (a *AllocationRuleImpl) GetDraftRule(ctx context.Context, maskChannelID int64, ruleMode int32, allocationMethod int64) (*MaskAllocationRuleTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	r := new(MaskAllocationRuleTab)
	err = db.Table(tableName).Take(r, "mask_product_id = ? AND rule_status IN (?, ?) AND rule_mode = ? AND allocation_method = ?", maskChannelID, MaskRuleStatusUncompleted, MaskRuleStatusDraft, ruleMode, allocationMethod).GetError()
	return ruleResult(ctx, err, r, dbutil.NewErrDataNotFound("draft rule mask_product_id & rule_status", fmt.Sprintf("%v-%v,%v", maskChannelID, MaskRuleStatusUncompleted, MaskRuleStatusDraft)))
}

func (a *AllocationRuleImpl) CreateRule(ctx context.Context, rule *MaskAllocationRuleTab) (*MaskAllocationRuleTab, error) {
	// 加了一个校验mask product 有效性检查
	maskProduct, gErr := a.LpsApi.GetProductDetail(ctx, int(rule.MaskProductId))
	if gErr != nil {
		return nil, errors.New(fmt.Sprintf("could not find mask product %d", rule.MaskProductId))
	}
	rule.MaskProductName = maskProduct.SellerDisplayName
	db, err := dbutil.MasterDB(ctx, MaskAllocationRuleHook)

	if err != nil {
		return nil, err
	}
	ts := timeutil.GetCurrentUnixTimeStamp(ctx)
	rule.MTime, rule.CTime = uint32(ts), uint32(ts)
	d := db.Create(rule)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "create rule fail|mask_product_id=%v, status=%d, rule_name=%s, EffectiveStartTime=%d, operate_by=%s, err=%v",
			rule.MaskProductId, rule.Status, rule.RuleName, rule.EffectiveStartTime, rule.OperatedBy, d.GetError())
		return nil, dbutil.NewErrDatabase(d.GetError(), "creacte rule")
	}
	convertStatus(ctx, rule)

	//add history
	operator, _ := apiutil.GetUserInfo(ctx)
	logger.CtxLogInfof(ctx, "CreateRule|operator:%v, create allocation rule, rule id:%v", operator, rule.Id)
	cErr := a.LpsApi.CreateHistory(ctx, rule, string(lpsclient.CreateType), "create rule", uint64(rule.Id))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return rule, nil
}

func (a *AllocationRuleImpl) UpdateRule(ctx context.Context, rule *MaskAllocationRuleTab) (*MaskAllocationRuleTab, error) {
	maskProduct, gErr := a.LpsApi.GetProductDetail(ctx, int(rule.MaskProductId))
	if gErr != nil {
		return nil, errors.New(fmt.Sprintf("could not find mask product %d", rule.MaskProductId))
	}
	rule.MaskProductName = maskProduct.SellerDisplayName

	db, err := dbutil.MasterDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	d := db.Save(rule)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "update rule fail|mask_product_id=%v, status=%d, rule_name=%s, EffectiveStartTime=%d, operate_by=%s, err=%v",
			rule.MaskProductId, rule.Status, rule.RuleName, rule.EffectiveStartTime, rule.OperatedBy, d.GetError())
		return nil, dbutil.NewErrDatabase(d.GetError(), "create rule")
	}
	convertStatus(ctx, rule)

	//写入操作历史
	cErr := a.LpsApi.CreateHistory(ctx, rule, string(lpsclient.UpdateType), "update rule", uint64(rule.Id))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return rule, nil
}

func (a *AllocationRuleImpl) DeleteRule(ctx context.Context, id int64) error {
	db, err := dbutil.MasterDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return err
	}
	r := new(MaskAllocationRuleTab)
	_ = db.Table(tableName).Take(r, "id = ?", id)

	d := db.Delete(&MaskAllocationRuleTab{Id: id})
	if d.GetError() != nil {
		return dbutil.NewErrDatabase(d.GetError(), "delete rule")
	}

	maskProduct, gErr := a.LpsApi.GetProductDetail(ctx, int(r.MaskProductId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "%v", errors.New(fmt.Sprintf("could not find mask product %d", r.MaskProductId)))
		return nil
	}
	r.MaskProductName = maskProduct.SellerDisplayName

	//写入操作历史
	cErr := a.LpsApi.CreateHistory(ctx, r, string(lpsclient.HardDeleteType), "hard delete rule", uint64(id))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return nil
}

func (a *AllocationRuleImpl) GetRuleByCondition(ctx context.Context, condition map[string]interface{}) (*MaskAllocationRuleTab, error) {
	var rule *MaskAllocationRuleTab
	if err := dbutil.Take(ctx, MaskAllocationRuleHook, condition, &rule); err != nil {
		return nil, errors.New("take got no data from db")
	}

	return rule, nil
}

func (a *AllocationRuleImpl) GetRuleListByCondition(ctx context.Context, condition map[string]interface{}) ([]*MaskAllocationRuleTab, error) {
	var ruleList []*MaskAllocationRuleTab
	if err := dbutil.Select(ctx, MaskAllocationRuleHook, condition, &ruleList); err != nil {
		return nil, errors.New("take got no data from db")
	}

	return ruleList, nil
}
