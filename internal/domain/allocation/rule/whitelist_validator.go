package rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func (repo *maskRuleRepoImpl) ValidatePriority(ctx context.Context, update *MaskRule) *srerr.Error {
	// 非active态不校验
	if update.Status != MaskRuleStatusActive {
		return nil
	}
	needCheck := false
	for _, step := range update.MaskRuleSteps {
		if step.MaskStepType == MaskStepPickupEfficiencyWhitelist {
			needCheck = true
			break
		}
	}
	if !needCheck {
		return nil
	}
	// 1.根据mask product获取Priority
	pickupPriorityList, _, lErr := repo.PickupPriorityRepo.List(ctx, map[string]interface{}{
		"masking_product_id = ?": update.MaskProductId,
	}, 0, 10)
	if lErr != nil { //检索不到不会报错，报错是DB层面的报错
		return lErr
	}
	// 2.检索不到pickup Priority
	if len(pickupPriorityList) == 0 {
		return srerr.New(srerr.ParamErr, nil, "you should create pickup efficiency priority first, if you wanna toggle on pickup efficiency")
	}

	return nil
}
