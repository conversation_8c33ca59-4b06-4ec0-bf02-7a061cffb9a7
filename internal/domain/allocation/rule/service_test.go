package rule

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	rulevolume1 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/agiledragon/gomonkey/v2"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
	"reflect"
	"strconv"
	"testing"
)

func Test_maskRuleRepoImpl_GetEffectiveRuleByCache(t *testing.T) {
	ctx := context.Background()
	maskRuleConf := &ruleConfRepo{}
	repo := &maskRuleRepoImpl{
		maskRuleConf: maskRuleConf,
	}
	var patch *gomonkey.Patches
	type args struct {
		maskProductID    int64
		rm               rule_mode.RuleMode
		allocationMethod int64
	}
	tests := []struct {
		name        string
		args        args
		want        *MaskRule
		expectedErr *srerr.Error
		setup       func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: AllocationConfigError",
			args: args{
				maskProductID: 1,
			},
			want:        nil,
			expectedErr: srerr.With(srerr.AllocationConfigError, 1, errors.New("local cache manger init not finished")),
			setup:       func() {},
		},
		{
			name: "case 2: AllocationMethod is not BatchAllocate",
			args: args{
				maskProductID:    1,
				rm:               rule_mode.MplOrderRule,
				allocationMethod: allocation.SingleAllocate,
			},
			want:        nil,
			expectedErr: srerr.With(srerr.GetMaskRuleConfigError, nil, errors.New("mock maskRuleConf.GetRuleConf error")),
			setup: func() {
				localcache.InitTest(constant.MaskEffectiveRule, rule_mode.MplOrderRule.String()+strconv.FormatInt(1, 10)+":"+strconv.FormatInt(1, 10),
					&MaskAllocationRuleTab{
						RuleMode: 1,
					}, 1)
				patch = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(c *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{}, srerr.With(srerr.GetMaskRuleConfigError, nil, errors.New("mock maskRuleConf.GetRuleConf error"))
				})
			},
		},
		{
			name: "case 3: AllocationMethod is BatchAllocate",
			args: args{
				maskProductID:    1,
				rm:               rule_mode.MplOrderRule,
				allocationMethod: allocation.BatchAllocate,
			},
			want:        nil,
			expectedErr: srerr.With(srerr.DataErr, "data.Rule", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
			setup: func() {
				localcache.InitTest(constant.MaskEffectiveRule, rule_mode.MplOrderRule.String()+strconv.FormatInt(1, 10)+
					":"+strconv.FormatInt(allocation.BatchAllocate, 10),
					&MaskAllocationRuleTab{
						RuleMode:         1,
						AllocationMethod: allocation.BatchAllocate,
					}, 1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotError := repo.GetEffectiveRuleByCache(ctx, tt.args.maskProductID, tt.args.rm, tt.args.allocationMethod)
			common.AssertResult(t, got, tt.want, gotError, tt.expectedErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestUnmarshalRuleDetail(t *testing.T) {
	type args struct {
		detail *MaskRuleDetail
		conf   MaskRuleConf
		volume *rulevolume.MaskRuleVolumeTab
	}
	tests := []struct {
		name      string
		args      args
		want      MaskRuleSteps
		expectErr error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: MaskRuleStepsError",
			args: args{
				detail: &MaskRuleDetail{
					MaxBatchVolumeEnable: true,
					Limit: []*MaskRuleLimit{
						{
							ID:                 1,
							MaxVolumeEachBatch: 100,
						},
					},

					CheapestFeeEnable:        true,
					MinVolumeCountryEnable:   true,
					MinVolumeZoneRouteEnable: true,

					MaxCapacityCountryEnable:   true,
					MaxCapacityZoneRouteEnable: true,

					PickupEfficiencyWhitelistEnable: true,

					MaxCodCapacityCountryEnable:   true,
					MaxCodCapacityZoneRouteEnable: true,

					MaxBulkyCapacityCountryEnable:   true,
					MaxBulkyCapacityZoneRouteEnable: true,

					MaxHighValueCapacityCountryEnable:   true,
					MaxHighValueCapacityZoneRouteEnable: true,
				},
				conf: MaskRuleConf{},
				volume: &rulevolume.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume.MaskDefaultVolumeLimitItem{
						{
							LimitType: rulevolume.MaskRuleLimitTypeFulfillmentProduct,
						},
						{
							LimitType: rulevolume.MaskRuleLimitTypeGroup,
						},
					},
				},
			},
			want: MaskRuleSteps{
				{
					MaskStepType: MaskStepMaxBatchVolume,
					BatchVolumeData: &BatchVolumeData{
						BatchVolume:      0,
						VolumeInOneBatch: map[int64]int32{1: 100},
					},
				},
				{
					MaskStepType: MaskStepCheapestFee,
				},
				{
					MaskStepType: MaskStepMinVolumeCountry,
					MinVolumeData: &MinVolumeData{
						MinVolumes: map[VolumeKey]int32{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: 0,
						},
						GroupMinVolumes: map[string]int32{
							"": 0,
						},
					},
				},
				{
					MaskStepType: MaskStepMinVolumeZoneRoute,
				},
				{
					MaskStepType: MaskStepMaxCapacityCountry,
					MaxCapacityData: &MaxCapacityData{
						MaxCapacities: map[VolumeKey]int32{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: 0,
						},
						GroupMaxCapacities: map[string]int32{
							"": 0,
						},
					},
					IsHardCapacityData: &IsHardCapacityData{
						IsHardCaps: map[VolumeKey]bool{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: false,
						},
						GroupIsHardCaps: map[string]bool{
							"": false,
						},
					},
				},
				{
					MaskStepType: MaskStepMaxCapacityZoneRoute,
				},
				{
					MaskStepType: MaskStepPickupEfficiencyWhitelist,
				},
				{
					MaskStepType: MaskStepMaxCodCapacityCountry,
					MaxCodCapacityData: &MaxCapacityData{
						MaxCapacities: map[VolumeKey]int32{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: 0,
						},
						GroupMaxCapacities: map[string]int32{
							"": 0,
						},
					},
					IsCodHardCapacityData: &IsHardCapacityData{
						IsHardCaps: map[VolumeKey]bool{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: false,
						},
						GroupIsHardCaps: map[string]bool{
							"": false,
						},
					},
				},
				{
					MaskStepType: MaskStepMaxCodCapacityZoneRoute,
				},
				{
					MaskStepType: MaskStepMaxBulkyCapacityCountry,
					MaxBulkyCapacityData: &MaxCapacityData{
						MaxCapacities: map[VolumeKey]int32{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: 0,
						},
						GroupMaxCapacities: map[string]int32{
							"": 0,
						},
					},
					IsBulkyHardCapacityData: &IsHardCapacityData{
						IsHardCaps: map[VolumeKey]bool{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: false,
						},
						GroupIsHardCaps: map[string]bool{
							"": false,
						},
					},
				},
				{
					MaskStepType: MaskStepMaxBulkyCapacityZoneRoute,
				},
				{
					MaskStepType: MaskStepMaxHighValueCapacityCountry,
					MaxHighValueCapacityData: &MaxCapacityData{
						MaxCapacities: map[VolumeKey]int32{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: 0,
						},
						GroupMaxCapacities: map[string]int32{
							"": 0,
						},
					},
					IsHighValueHardCapacityData: &IsHardCapacityData{
						IsHardCaps: map[VolumeKey]bool{
							VolumeKey{
								MaskProductID:        0,
								FulfillmentProductID: 0,
							}: false,
						},
						GroupIsHardCaps: map[string]bool{
							"": false,
						},
					},
				},
				{
					MaskStepType: MaskStepMaxHighValueCapacityZoneRoute,
				},
			},
			expectErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotError := UnmarshalRuleDetail(tt.args.detail, tt.args.conf, tt.args.volume)
			if objutil.CompareInterfaces(got, tt.want) == false {
				t.Errorf("UnmarshalRuleDetail() got = %v, want %v", got, tt.want)
			}
			if gotError != nil && tt.expectErr != nil {
				if !assert.Equal(t, gotError.Error(), tt.expectErr.Error()) {
					t.Errorf("UnmarshalRuleDetail() gotError = %v, expectedErr %v", gotError, tt.expectErr)
				}
			} else if (gotError == nil && tt.expectErr != nil) || (gotError != nil && tt.expectErr == nil) {
				t.Errorf("UnmarshalRuleDetail() gotError and expectedErr，one is NIL and the other is not NIL ")
			}
		})
	}
}

func Test_maskRuleRepoImpl_parseRule(t *testing.T) {
	ctx := context.Background()
	bytes, _ := jsoniter.Marshal(&MaskRuleDetail{MaxCapacityEnable: true})
	bytes0, _ := jsoniter.Marshal(&MaskRuleDetail{MaxCapacityEnable: true, MinVolumeEnable: true})
	var patchGetRuleConf, patchUnmarshal, patchGetActiveRuleVolumeByMaskProductIDWithCache, patchUnmarshalRuleDetail *gomonkey.Patches
	var maskRuleConf *ruleConfRepo
	var maskRuleVolumeService *rulevolume1.MaskRuleVolumeServiceImpl
	type args struct {
		data *MaskAllocationRuleTab
	}
	tests := []struct {
		name    string
		repo    *maskRuleRepoImpl
		args    args
		want    *MaskRule
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: GetRuleConf failed",
			repo: &maskRuleRepoImpl{maskRuleConf: maskRuleConf},
			args: args{
				data: &MaskAllocationRuleTab{},
			},
			want:    nil,
			wantErr: srerr.With(srerr.GetMaskRuleConfigError, nil, errors.New("mock maskRuleConf.GetRuleConf error")),
			setup: func() {
				patchGetRuleConf = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(m *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{}, srerr.With(srerr.GetMaskRuleConfigError, nil, errors.New("mock maskRuleConf.GetRuleConf error"))
				})
			},
		},
		{
			name: "case 2: jsoniter.Unmarshal failed",
			repo: &maskRuleRepoImpl{maskRuleConf: maskRuleConf},
			args: args{
				data: &MaskAllocationRuleTab{
					RuleMode: int32(rule_mode.WmsOrderRule),
					Rule:     []byte{},
				},
			},
			want:    nil,
			wantErr: srerr.With(srerr.DataErr, "data.Rule", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
			setup: func() {
				patchGetRuleConf = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(m *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{DisableProductPriority: true}, nil
				})
				//patchUnmarshal = gomonkey.ApplyFunc(jsoniter.Unmarshal, func(data []byte, v interface{}) error {
				//	return errors.New("mock jsoniter.Unmarshal error")
				//})
			},
		},
		{
			name: "case 3: GetActiveRuleVolumeByMaskProductIDWithCache failed",
			repo: &maskRuleRepoImpl{maskRuleConf: maskRuleConf, maskRuleVolumeService: maskRuleVolumeService},
			args: args{
				data: &MaskAllocationRuleTab{
					Status:   MaskRuleStatusActive,
					RuleMode: int32(rule_mode.WmsOrderRule),
					Rule:     bytes,
				},
			},
			want:    nil,
			wantErr: srerr.With(srerr.DataErr, "", errors.New("mock GetActiveRuleVolumeByMaskProductIDWithCache get error")),
			setup: func() {
				patchGetRuleConf = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(m *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{}, nil
				})
				patchGetActiveRuleVolumeByMaskProductIDWithCache = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleVolumeService), "GetActiveRuleVolumeByMaskProductIDWithCache", func(m *rulevolume1.MaskRuleVolumeServiceImpl, ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
					return nil, srerr.With(srerr.DataErr, "", errors.New("mock GetActiveRuleVolumeByMaskProductIDWithCache get error"))
				})
			},
		},
		{
			name: "case 4: UnmarshalRuleDetail failed",
			repo: &maskRuleRepoImpl{maskRuleConf: maskRuleConf, maskRuleVolumeService: maskRuleVolumeService},
			args: args{
				data: &MaskAllocationRuleTab{
					Status:   MaskRuleStatusActive,
					RuleMode: int32(rule_mode.WmsOrderRule),
					Rule:     bytes0,
				},
			},
			want:    nil,
			wantErr: srerr.With(srerr.DataErr, "data.Rule", errors.New("mock UnmarshalRuleDetail get error")),
			setup: func() {
				patchGetRuleConf = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(m *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{}, nil
				})
				patchGetActiveRuleVolumeByMaskProductIDWithCache = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleVolumeService), "GetActiveRuleVolumeByMaskProductIDWithCache", func(m *rulevolume1.MaskRuleVolumeServiceImpl, ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
					return &rulevolume.MaskRuleVolumeTab{
						RuleType: rulevolume.LocVolumeTypeZone,
					}, nil
				})
				patchUnmarshalRuleDetail = gomonkey.ApplyFunc(UnmarshalRuleDetail, func(detail *MaskRuleDetail, conf MaskRuleConf, volume *rulevolume.MaskRuleVolumeTab) (MaskRuleSteps, error) {
					return nil, errors.New("mock UnmarshalRuleDetail get error")
				})
			},
		},
		{
			name: "case 5: success",
			repo: &maskRuleRepoImpl{maskRuleConf: maskRuleConf, maskRuleVolumeService: maskRuleVolumeService},
			args: args{
				data: &MaskAllocationRuleTab{
					Status:   MaskRuleStatusActive,
					RuleMode: int32(rule_mode.WmsOrderRule),
					Rule:     bytes0,
				},
			},
			want: &MaskRule{
				MaskRuleSteps:         MaskRuleSteps{},
				Status:                MaskRuleStatusActive,
				LocVolumeType:         rulevolume.LocVolumeTypeZone,
				EnableProductPriority: true,
				RuleMode:              int32(rule_mode.WmsOrderRule),
			},
			wantErr: nil,
			setup: func() {
				patchGetRuleConf = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleConf), "GetRuleConf", func(m *ruleConfRepo, ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
					return MaskRuleConf{}, nil
				})
				patchGetActiveRuleVolumeByMaskProductIDWithCache = gomonkey.ApplyMethod(reflect.TypeOf(maskRuleVolumeService), "GetActiveRuleVolumeByMaskProductIDWithCache", func(m *rulevolume1.MaskRuleVolumeServiceImpl, ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*rulevolume.MaskRuleVolumeTab, *srerr.Error) {
					return &rulevolume.MaskRuleVolumeTab{
						RuleType: rulevolume.LocVolumeTypeZone,
					}, nil
				})
				patchUnmarshalRuleDetail = gomonkey.ApplyFunc(UnmarshalRuleDetail, func(detail *MaskRuleDetail, conf MaskRuleConf, volume *rulevolume.MaskRuleVolumeTab) (MaskRuleSteps, error) {
					return MaskRuleSteps{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := tt.repo.parseRule(ctx, tt.args.data)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)

			if patchGetRuleConf != nil {
				patchGetRuleConf.Reset()
			}
			if patchUnmarshal != nil {
				patchUnmarshal.Reset()
			}
			if patchGetActiveRuleVolumeByMaskProductIDWithCache != nil {
				patchGetActiveRuleVolumeByMaskProductIDWithCache.Reset()
			}
			if patchUnmarshalRuleDetail != nil {
				patchUnmarshalRuleDetail.Reset()
			}
		})
	}
}

func Test_needLoadVolumeRule(t *testing.T) {
	type args struct {
		r      *MaskAllocationRuleTab
		detail *MaskRuleDetail
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: r.Status != MaskRuleStatusActive",
			args: args{
				r: &MaskAllocationRuleTab{},
			},
			want: false,
		},
		{
			name: "case 2: detail.MaxCapacityEnable || detail.MinVolumeEnable",
			args: args{
				r: &MaskAllocationRuleTab{
					Status: MaskRuleStatusActive,
				},
				detail: &MaskRuleDetail{
					MaxCapacityEnable: true,
				},
			},
			want: true,
		},
		{
			name: "case 3: Country dimension",
			args: args{
				r: &MaskAllocationRuleTab{
					Status: MaskRuleStatusActive,
				},
				detail: &MaskRuleDetail{
					MaxCapacityCountryEnable: true,
				},
			},
			want: true,
		},
		{
			name: "case 4: Zone/Route dimension",
			args: args{
				r: &MaskAllocationRuleTab{
					Status: MaskRuleStatusActive,
				},
				detail: &MaskRuleDetail{
					MaxCapacityZoneRouteEnable: true,
				},
			},
			want: true,
		},
		{
			name: "case 5: no needLoadVolumeRule",
			args: args{
				r: &MaskAllocationRuleTab{
					Status: MaskRuleStatusActive,
				},
				detail: &MaskRuleDetail{},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, needLoadVolumeRule(tt.args.r, tt.args.detail), "needLoadVolumeRule(%v, %v)", tt.args.r, tt.args.detail)
		})
	}
}

func Test_maskRuleRepoImpl_parseBatchRule(t *testing.T) {
	ctx := context.Background()
	bytes, _ := jsoniter.Marshal(&BatchRuleDetail{})
	//var patchUnmarshal *gomonkey.Patches
	type args struct {
		data *MaskAllocationRuleTab
	}
	tests := []struct {
		name    string
		repo    *maskRuleRepoImpl
		args    args
		want    *MaskRule
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: jsoniter.Unmarshal failed",
			args: args{
				data: &MaskAllocationRuleTab{},
			},
			want:    nil,
			wantErr: srerr.With(srerr.DataErr, "data.Rule", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
			setup: func() {
				//patchUnmarshal = gomonkey.ApplyFunc(jsoniter.Unmarshal, func(data []byte, v interface{}) error {
				//	return errors.New("mock jsoniter.Unmarshal error")
				//})
			},
		},
		{
			name: "case 2: success",
			args: args{
				data: &MaskAllocationRuleTab{BatchRule: bytes},
			},
			want: &MaskRule{
				AllocationMethod: allocation.SingleAllocate,
			},
			wantErr: nil,
			setup: func() {
				//patchUnmarshal = gomonkey.ApplyFunc(jsoniter.Unmarshal, func(data []byte, v interface{}) error {
				//	return errors.New("mock jsoniter.Unmarshal error")
				//})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := tt.repo.parseBatchRule(ctx, tt.args.data)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			//if patchUnmarshal != nil {
			//	patchUnmarshal.Reset()
			//}
		})
	}
}

func Test_getVolumeKey(t *testing.T) {
	type args struct {
		volume *rulevolume.MaskRuleVolumeTab
		limit  *rulevolume.MaskDefaultVolumeLimitItem
	}
	tests := []struct {
		name string
		args args
		want VolumeKey
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				volume: &rulevolume.MaskRuleVolumeTab{
					MaskCombinationMode: true,
				},
				limit: &rulevolume.MaskDefaultVolumeLimitItem{},
			},
			want: VolumeKey{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getVolumeKey(tt.args.volume, tt.args.limit), "getVolumeKey(%v, %v)", tt.args.volume, tt.args.limit)
		})
	}
}

func Test_getMaxCapAndHardCapData(t *testing.T) {
	type args struct {
		volume     *rulevolume.MaskRuleVolumeTab
		parcelType parcel_type_definition.ParcelType
	}
	tests := []struct {
		name                   string
		args                   args
		wantMaxCapacityData    *MaxCapacityData
		wantIsHardCapacityData *IsHardCapacityData
	}{
		// TODO: Add test cases.
		{
			name: "case 1: parcel_type_definition.ParcelTypeNone",
			args: args{
				volume: &rulevolume.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume.MaskDefaultVolumeLimitItem{
						{
							LimitType: rulevolume.MaskRuleLimitTypeGroup,
						},
					},
				},
			},
			wantMaxCapacityData: &MaxCapacityData{
				MaxCapacities: map[VolumeKey]int32{},
				GroupMaxCapacities: map[string]int32{
					"": 0,
				},
			},
			wantIsHardCapacityData: &IsHardCapacityData{
				IsHardCaps: map[VolumeKey]bool{},
				GroupIsHardCaps: map[string]bool{
					"": false,
				},
			},
		},
		{
			name: "case 2: parcel_type_definition.ParcelTypeCod",
			args: args{
				volume: &rulevolume.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume.MaskDefaultVolumeLimitItem{
						{},
					},
				},
				parcelType: parcel_type_definition.ParcelTypeCod,
			},
			wantMaxCapacityData: &MaxCapacityData{
				MaxCapacities: map[VolumeKey]int32{
					VolumeKey{}: 0,
				},
				GroupMaxCapacities: map[string]int32{},
			},
			wantIsHardCapacityData: &IsHardCapacityData{
				IsHardCaps: map[VolumeKey]bool{
					VolumeKey{}: false,
				},
				GroupIsHardCaps: map[string]bool{},
			},
		},
		{
			name: "case 3: parcel_type_definition.ParcelTypeBulky",
			args: args{
				volume: &rulevolume.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume.MaskDefaultVolumeLimitItem{
						{},
					},
				},
				parcelType: parcel_type_definition.ParcelTypeBulky,
			},
			wantMaxCapacityData: &MaxCapacityData{
				MaxCapacities: map[VolumeKey]int32{
					VolumeKey{}: 0,
				},
				GroupMaxCapacities: map[string]int32{},
			},
			wantIsHardCapacityData: &IsHardCapacityData{
				IsHardCaps: map[VolumeKey]bool{
					VolumeKey{}: false,
				},
				GroupIsHardCaps: map[string]bool{},
			},
		},
		{
			name: "case 3: parcel_type_definition.ParcelTypeHighValue",
			args: args{
				volume: &rulevolume.MaskRuleVolumeTab{
					DefaultVolumeLimit: []*rulevolume.MaskDefaultVolumeLimitItem{
						{},
					},
				},
				parcelType: parcel_type_definition.ParcelTypeHighValue,
			},
			wantMaxCapacityData: &MaxCapacityData{
				MaxCapacities: map[VolumeKey]int32{
					VolumeKey{}: 0,
				},
				GroupMaxCapacities: map[string]int32{},
			},
			wantIsHardCapacityData: &IsHardCapacityData{
				IsHardCaps: map[VolumeKey]bool{
					VolumeKey{}: false,
				},
				GroupIsHardCaps: map[string]bool{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getMaxCapAndHardCapData(tt.args.volume, tt.args.parcelType)
			assert.Equalf(t, tt.wantMaxCapacityData, got, "getMaxCapAndHardCapData(%v, %v)", tt.args.volume, tt.args.parcelType)
			assert.Equalf(t, tt.wantIsHardCapacityData, got1, "getMaxCapAndHardCapData(%v, %v)", tt.args.volume, tt.args.parcelType)
		})
	}
}
