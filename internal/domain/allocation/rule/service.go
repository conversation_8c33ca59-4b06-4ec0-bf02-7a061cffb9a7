package rule

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	jsoniter "github.com/json-iterator/go"
	"sort"
	"strconv"
	"sync"
)

type IMaskRuleRepo interface {
	GetEffectiveRuleByCache(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRule, *srerr.Error)
	GetRule(ctx context.Context, id int64) (*MaskRule, *srerr.Error)
	GetRuleSimpleInfo(ctx context.Context) ([]*MaskRuleInfo, *srerr.Error)
	ListRules(ctx context.Context, q *RuleQuery) ([]*MaskRule, int32, *srerr.Error)
	CreateRule(ctx context.Context, name string, maskProductID int64, ruleMode int32, allocationMethod int64) (*MaskRule, *srerr.Error)
	UpdateRule(ctx context.Context, update *MaskRule) (*MaskRule, *srerr.Error)
	DeleteRule(ctx context.Context, ruleID int64) *srerr.Error
	CopyRule(ctx context.Context, ruleID int64) *srerr.Error
	UpdateAllocationRuleStatusExpired(ctx context.Context)
	UpdateAllocationRuleVolumeStatusExpired(ctx context.Context)
	GetRuleByCondition(ctx context.Context, condition map[string]interface{}) (*MaskRule, *srerr.Error)
	DisableBatchRule(ctx context.Context, ruleID int64) *srerr.Error
}

type maskRuleRepoImpl struct {
	maskRuleVolume        rulevolume.IMaskRuleVolumeRepo
	maskRuleVolumeService rulevolume2.MaskRuleVolumeService
	maskRuleConf          MaskRuleConfRepo
	lpsApi                lpsclient.LpsApi
	allocateRuleRepo      AllocationRuleRepo
	priorityRepo          productpriority.PriorityRepo
	ApprovalExecutor      approval_manager.ApprovalExecutor
	PickupPriorityRepo    pickup_priority.PickupPriorityRepo
}

func NewRuleRepo(
	maskRuleVolume rulevolume.IMaskRuleVolumeRepo,
	maskRuleVolumeService rulevolume2.MaskRuleVolumeService,
	maskRuleConf MaskRuleConfRepo,
	lpsApi lpsclient.LpsApi,
	allocateRuleRepo AllocationRuleRepo,
	priorityRepo productpriority.PriorityRepo,
	ApprovalExecutor approval_manager.ApprovalExecutor,
	BusinessAuditRepo business_audit.BusinessAuditRepo,
	PickupPriorityRepo pickup_priority.PickupPriorityRepo,
) *maskRuleRepoImpl {

	once := sync.Once{}
	once.Do(func() {
		unit := NewAllocateRuleUnitImpl(BusinessAuditRepo, allocateRuleRepo)
		approval_unit.AppendBusinessUnitMap(unit)
	})

	return &maskRuleRepoImpl{
		maskRuleVolume:        maskRuleVolume,
		maskRuleVolumeService: maskRuleVolumeService,
		maskRuleConf:          maskRuleConf,
		lpsApi:                lpsApi,
		allocateRuleRepo:      allocateRuleRepo,
		priorityRepo:          priorityRepo,
		ApprovalExecutor:      ApprovalExecutor,
		PickupPriorityRepo:    PickupPriorityRepo,
	}
}

func (repo *maskRuleRepoImpl) GetRule(ctx context.Context, id int64) (*MaskRule, *srerr.Error) {
	data, err := repo.allocateRuleRepo.GetRuleByID(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.RuleNotFound, id, err)
	}
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}
	repo.enrichSellerName(ctx, data)
	if data.AllocationMethod != allocation.BatchAllocate {
		return repo.parseRule(ctx, data)
	} else {
		return repo.parseBatchRule(ctx, data)
	}
}

// @core
func (repo *maskRuleRepoImpl) GetEffectiveRuleByCache(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRule, *srerr.Error) {
	value, err := localcache.Get(ctx, constant.MaskEffectiveRule, rm.String()+strconv.FormatInt(maskProductID, 10)+":"+strconv.FormatInt(allocationMethod, 10))
	if err != nil {
		return nil, srerr.With(srerr.AllocationConfigError, maskProductID, err)
	}

	data := value.(*MaskAllocationRuleTab)
	if data.AllocationMethod != allocation.BatchAllocate {
		return repo.parseRule(ctx, data)
	} else {
		return repo.parseBatchRule(ctx, data)
	}
}

func (repo *maskRuleRepoImpl) GetActiveRules(ctx context.Context, mode rule_mode.RuleMode, allocationMethod int64) ([]*MaskAllocationRuleTab, error) {
	db, err := dbutil.SlaveDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return nil, err
	}
	var (
		rules             []*MaskAllocationRuleTab
		allocationMethods []int64
	)
	if allocationMethod == allocation.BatchAllocate {
		allocationMethods = []int64{allocation.BatchAllocate}
	} else {
		//兼容存量数据
		allocationMethods = []int64{allocation.Zero, allocation.SingleAllocate}
	}
	d := db.Table(MaskAllocationRuleHook.TableName()).
		Where("rule_status = ? AND effective_start_time < ? AND rule_mode = ? AND allocation_method in (?)",
			MaskRuleStatusActive, timeutil.GetCurrentUnixTimeStamp(ctx), int(mode), allocationMethods).Order("effective_start_time DESC").Find(&rules)
	if d.GetError() != nil {
		return nil, d.GetError()
	}
	return rules, nil
}

func (repo *maskRuleRepoImpl) UpdateAllocationRuleStatusExpired(ctx context.Context) {
	var reportInterfaceName = "UpdateAllocationRuleStatusExpired"
	startTime := recorder.Now(ctx).Unix()
	for _, allocationMethod := range []int64{allocation.SingleAllocate, allocation.BatchAllocate} {
		for _, mode := range []rule_mode.RuleMode{rule_mode.MplOrderRule, rule_mode.WmsOrderRule} {
			// 1. SELECT * FROM `mask_channel_rule_tab` WHERE `status` = '3' AND effective_start_time < now() AND mode = 'mode' ORDER BY effective_start_time DESC;
			rules, err := repo.GetActiveRules(ctx, mode, allocationMethod)
			if err != nil {
				reportData := fmt.Sprintf("GetActiveRules fail|err=%v", err)
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
				logger.CtxLogInfof(ctx, reportData)
				continue
			}

			// 2. group by mask_channel_id
			var needUpdateRuleID []int64
			var existRule = make(map[int64]bool)
			for _, r := range rules {
				if _, ok := existRule[r.MaskProductId]; ok {
					needUpdateRuleID = append(needUpdateRuleID, r.Id)
				} else {
					existRule[r.MaskProductId] = true
				}
			}
			if len(needUpdateRuleID) == 0 {
				logger.CtxLogInfof(ctx, "no need to update")
				continue
			}

			// 3. update rules to expire
			logger.CtxLogInfof(ctx, "updating expired rules | rule ids: %v", needUpdateRuleID)
			err = repo.UpdateRuleStatusToExpired(ctx, needUpdateRuleID)
			if err != nil {
				reportData := fmt.Sprintf("UpdateRuleStatusToExpired fail|err=%v", err)
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
				logger.CtxLogInfof(ctx, reportData)
			}

			reportData := fmt.Sprintf("Update allocation rules to expired | rule mode = %s, allocation rules = %v", mode, needUpdateRuleID)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusSuccess, reportData)
			change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "schedule_rule", "allocation change active to expire", reportData, report_constant.RiskLevelHigh)
			logger.CtxLogInfof(ctx, reportData)

			// 上报prometheus
			prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameScheduleRule, int64(len(needUpdateRuleID)))
		}
	}
}

func (repo *maskRuleRepoImpl) UpdateRuleStatusToExpired(ctx context.Context, ids []int64) error {
	db, err := dbutil.MasterDB(ctx, MaskAllocationRuleHook)
	if err != nil {
		return err
	}
	d := db.Table(MaskAllocationRuleHook.TableName()).Where("rule_status = ? AND id in (?)", MaskRuleStatusActive, ids).Update("rule_status", MaskRuleStatusExpired)
	if d.GetError() != nil {
		return d.GetError()
	}
	return nil
}

func (repo *maskRuleRepoImpl) parseRule(ctx context.Context, data *MaskAllocationRuleTab) (*MaskRule, *srerr.Error) {
	r := &MaskRule{
		Id:                    data.Id,
		MaskProductId:         data.MaskProductId,
		Status:                data.Status,
		RuleName:              data.RuleName,
		EnableProductPriority: data.EnableProductPriority,
		OperatedBy:            data.OperatedBy,
		EffectiveStartTime:    data.EffectiveStartTime,
		CTime:                 data.CTime,
		MTime:                 data.MTime,
		MaskProductName:       data.MaskProductName,
		ForecastTaskId:        data.ForecastTaskId,
		RuleMode:              data.RuleMode,
		ApprovalTime:          data.ApprovalTime,
	}

	conf, err := repo.maskRuleConf.GetRuleConf(ctx, rule_mode.RuleMode(data.RuleMode))
	if err != nil {
		return nil, srerr.With(srerr.GetMaskRuleConfigError, nil, err)
	}
	// 如果国家级的配置的不允许product_priority, 则统一改成disabled. 与PM确认，此部分逻辑可以去掉
	if conf.DisableProductPriority {
		r.EnableProductPriority = false
	}
	if rule_mode.RuleMode(data.RuleMode) == rule_mode.WmsOrderRule {
		//跟 wenzhe 对齐的，存量的mpl 先不动，wms 的 以 apollo-region 开关为准
		r.EnableProductPriority = !conf.DisableProductPriority
	}

	detail := &MaskRuleDetail{}
	if err := jsoniter.Unmarshal(data.Rule, &detail); err != nil {
		return nil, srerr.With(srerr.DataErr, data.Rule, err)
	}
	volume := &rulevolume.MaskRuleVolumeTab{}
	if needLoadVolumeRule(data, detail) {
		var srErr *srerr.Error
		volume, srErr = repo.maskRuleVolumeService.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, data.MaskProductId, rule_mode.RuleMode(data.RuleMode), allocation.SingleAllocate)
		if srErr != nil {
			return nil, srErr
		}
		r.LocVolumeType = volume.RuleType
		r.LocVolumeId = volume.ID
	}
	if detail.MinVolumeEnable && !detail.MinVolumeCountryEnable && !detail.MinVolumeZoneRouteEnable {
		detail.MinVolumeEnable = false
		detail.MinVolumeCountryEnable = true
		detail.MinVolumeCountrySort = detail.MinVolumeSort
		switch volume.RuleType {
		case rulevolume.LocVolumeTypeZone, rulevolume.LocVolumeTypeRoute:
			detail.MinVolumeZoneRouteEnable = true
			detail.MinVolumeZoneRouteSort = detail.MinVolumeSort
		}
	}
	if detail.MaxCapacityEnable && !detail.MaxCapacityCountryEnable && !detail.MaxCapacityZoneRouteEnable {
		detail.MaxCapacityEnable = false
		detail.MaxCapacityCountryEnable = true
		detail.MaxCapacityCountrySort = detail.MaxCapacitySort
		switch volume.RuleType {
		case rulevolume.LocVolumeTypeZone, rulevolume.LocVolumeTypeRoute:
			detail.MaxCapacityZoneRouteEnable = true
			detail.MaxCapacityZoneRouteSort = detail.MaxCapacitySort
		}
	}

	ruleSteps, e := UnmarshalRuleDetail(detail, conf, volume)
	if e != nil {
		return nil, srerr.With(srerr.DataErr, data.Rule, e)
	}
	r.MaskRuleSteps = ruleSteps
	return r, nil
}

// needLoadRuleVolume 检查是否需要去匹配Volume Rule
func needLoadVolumeRule(r *MaskAllocationRuleTab, detail *MaskRuleDetail) bool {
	// 首先是要该Rule是Active的
	if r.Status != MaskRuleStatusActive {
		return false
	}

	// 该字段其实已废弃，不再使用
	if detail.MaxCapacityEnable || detail.MinVolumeEnable {
		return true
	}

	// 检查Country维度
	if detail.MaxCapacityCountryEnable || detail.MinVolumeCountryEnable || detail.MaxCodCapacityCountryEnable ||
		detail.MaxBulkyCapacityCountryEnable || detail.MaxHighValueCapacityCountryEnable || detail.MaxDgCapacityCountryEnable {
		return true
	}

	// 检查Zone/Route维度
	if detail.MaxCapacityZoneRouteEnable || detail.MinVolumeZoneRouteEnable || detail.MaxCodCapacityZoneRouteEnable ||
		detail.MaxBulkyCapacityZoneRouteEnable || detail.MaxHighValueCapacityZoneRouteEnable || detail.MaxDgCapacityZoneRouteEnable {
		return true
	}

	return false
}

func (repo *maskRuleRepoImpl) parseBatchRule(ctx context.Context, data *MaskAllocationRuleTab) (*MaskRule, *srerr.Error) {
	r := &MaskRule{
		Id:                 data.Id,
		MaskProductId:      data.MaskProductId,
		Status:             data.Status,
		RuleName:           data.RuleName,
		OperatedBy:         data.OperatedBy,
		EffectiveStartTime: data.EffectiveStartTime,
		CTime:              data.CTime,
		MTime:              data.MTime,
		MaskProductName:    data.MaskProductName,
		ForecastTaskId:     data.ForecastTaskId,
		RuleMode:           data.RuleMode,
		AllocationMethod:   convertAllocationMethod(data.AllocationMethod),
	}
	detail := BatchRuleDetail{}
	if err := jsoniter.Unmarshal(data.BatchRule, &detail); err != nil {
		return nil, srerr.With(srerr.DataErr, data.Rule, err)
	}
	r.BatchRuleDetail = detail
	return r, nil
}

func UnmarshalRuleDetail(detail *MaskRuleDetail, conf MaskRuleConf, volume *rulevolume.MaskRuleVolumeTab) (MaskRuleSteps, error) {
	ruleSteps := MaskRuleSteps{}
	if detail.MaxBatchVolumeEnable && conf.IsStepEnabled(MaskStepMaxBatchVolume) {
		volumesInOneBatch := map[int64]int32{}
		for _, limit := range detail.Limit {
			volumesInOneBatch[limit.ID] = limit.MaxVolumeEachBatch
		}
		step := MaskRuleStep{
			Priority:     detail.MaxBatchVolumeSort,
			MaskStepType: MaskStepMaxBatchVolume,
			BatchVolumeData: &BatchVolumeData{
				BatchVolume:      detail.MaxBatchVolume,
				VolumeInOneBatch: volumesInOneBatch,
			},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.CheapestFeeEnable && conf.IsStepEnabled(MaskStepCheapestFee) {
		step := MaskRuleStep{
			Priority:     detail.CheapestFeeSort,
			MaskStepType: MaskStepCheapestFee,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MinVolumeCountryEnable && conf.IsStepEnabled(MaskStepMinVolumeCountry) {
		var (
			minVolumes      = map[VolumeKey]int32{}
			groupMinVolumes = map[string]int32{}
		)
		for _, limit := range volume.DefaultVolumeLimit {
			if limit.LimitType == rulevolume.MaskRuleLimitTypeFulfillmentProduct {
				minVolumes[getVolumeKey(volume, limit)] = limit.MinVolume
			} else if limit.LimitType == rulevolume.MaskRuleLimitTypeGroup {
				groupMinVolumes[limit.GroupCode] = limit.MinVolume
			}
		}
		step := MaskRuleStep{
			Priority:      detail.MinVolumeCountrySort,
			MaskStepType:  MaskStepMinVolumeCountry,
			MinVolumeData: &MinVolumeData{MinVolumes: minVolumes, GroupMinVolumes: groupMinVolumes},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MinVolumeZoneRouteEnable && conf.IsStepEnabled(MaskStepMinVolumeZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MinVolumeZoneRouteSort,
			MaskStepType: MaskStepMinVolumeZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityCountryEnable && conf.IsStepEnabled(MaskStepMaxCapacityCountry) {
		maxCapData, isHardCapData := getMaxCapAndHardCapData(volume, parcel_type_definition.ParcelTypeNone)
		step := MaskRuleStep{
			Priority:           detail.MaxCapacityCountrySort,
			MaskStepType:       MaskStepMaxCapacityCountry,
			MaxCapacityData:    maxCapData,
			IsHardCapacityData: isHardCapData,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityZoneRouteEnable && conf.IsStepEnabled(MaskStepMaxCapacityZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MaxCapacityZoneRouteSort,
			MaskStepType: MaskStepMaxCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.PickupEfficiencyWhitelistEnable && conf.IsStepEnabled(MaskStepPickupEfficiencyWhitelist) {
		step := MaskRuleStep{
			Priority:     detail.PickupEfficiencyWhitelistSort,
			MaskStepType: MaskStepPickupEfficiencyWhitelist,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCodCapacityCountryEnable && conf.IsStepEnabled(MaskStepMaxCodCapacityCountry) {
		maxCapData, isHardCapData := getMaxCapAndHardCapData(volume, parcel_type_definition.ParcelTypeCod)
		step := MaskRuleStep{
			Priority:              detail.MaxCodCapacityCountrySort,
			MaskStepType:          MaskStepMaxCodCapacityCountry,
			MaxCodCapacityData:    maxCapData,
			IsCodHardCapacityData: isHardCapData,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCodCapacityZoneRouteEnable && conf.IsStepEnabled(MaskStepMaxCodCapacityZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MaxCodCapacityZoneRouteSort,
			MaskStepType: MaskStepMaxCodCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxBulkyCapacityCountryEnable && conf.IsStepEnabled(MaskStepMaxBulkyCapacityCountry) {
		maxCapData, isHardCapData := getMaxCapAndHardCapData(volume, parcel_type_definition.ParcelTypeBulky)
		step := MaskRuleStep{
			Priority:                detail.MaxBulkyCapacityCountrySort,
			MaskStepType:            MaskStepMaxBulkyCapacityCountry,
			MaxBulkyCapacityData:    maxCapData,
			IsBulkyHardCapacityData: isHardCapData,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxBulkyCapacityZoneRouteEnable && conf.IsStepEnabled(MaskStepMaxBulkyCapacityZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MaxBulkyCapacityZoneRouteSort,
			MaskStepType: MaskStepMaxBulkyCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxHighValueCapacityCountryEnable && conf.IsStepEnabled(MaskStepMaxHighValueCapacityCountry) {
		maxCapData, isHardCapData := getMaxCapAndHardCapData(volume, parcel_type_definition.ParcelTypeHighValue)
		step := MaskRuleStep{
			Priority:                    detail.MaxHighValueCapacityCountrySort,
			MaskStepType:                MaskStepMaxHighValueCapacityCountry,
			MaxHighValueCapacityData:    maxCapData,
			IsHighValueHardCapacityData: isHardCapData,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxHighValueCapacityZoneRouteEnable && conf.IsStepEnabled(MaskStepMaxHighValueCapacityZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MaxHighValueCapacityZoneRouteSort,
			MaskStepType: MaskStepMaxHighValueCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxDgCapacityCountryEnable && conf.IsStepEnabled(MaskStepMaxDgCapacityCountry) {
		maxCapData, isHardCapData := getMaxCapAndHardCapData(volume, parcel_type_definition.ParcelTypeDg)
		step := MaskRuleStep{
			Priority:             detail.MaxDgCapacityCountrySort,
			MaskStepType:         MaskStepMaxDgCapacityCountry,
			MaxDgCapacityData:    maxCapData,
			IsDgHardCapacityData: isHardCapData,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxDgCapacityZoneRouteEnable && conf.IsStepEnabled(MaskStepMaxDgCapacityZoneRoute) {
		step := MaskRuleStep{
			Priority:     detail.MaxDgCapacityZoneRouteSort,
			MaskStepType: MaskStepMaxDgCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	sort.Sort(ruleSteps)

	return ruleSteps, nil
}

// getMaxCapAndHardCapData 抽象方法，统一根据ParcelType获取对应的MaxCap和HardCap数据
func getMaxCapAndHardCapData(
	volume *rulevolume.MaskRuleVolumeTab, parcelType parcel_type_definition.ParcelType,
) (*MaxCapacityData, *IsHardCapacityData) {
	var (
		maxCapacities         = map[VolumeKey]int32{}
		groupMaxCapacities    = map[string]int32{}
		isHardCapacities      = map[VolumeKey]bool{}
		groupIsHardCapacities = map[string]bool{}
	)

	for _, limit := range volume.DefaultVolumeLimit {
		var (
			maxCap    int32
			isHardCap bool
		)
		switch parcelType {
		case parcel_type_definition.ParcelTypeNone:
			maxCap = limit.MaxCapacity
			isHardCap = limit.IsHardCap
		case parcel_type_definition.ParcelTypeCod:
			maxCap = limit.MaxCodCapacity
			isHardCap = limit.IsCodHardCap
		case parcel_type_definition.ParcelTypeBulky:
			maxCap = limit.MaxBulkyCapacity
			isHardCap = limit.IsBulkyHardCap
		case parcel_type_definition.ParcelTypeHighValue:
			maxCap = limit.MaxHighValueCapacity
			isHardCap = limit.IsHighValueHardCap
		case parcel_type_definition.ParcelTypeDg:
			maxCap = limit.MaxDgCapacity
			isHardCap = limit.IsDgHardCap
		}
		if limit.LimitType == rulevolume.MaskRuleLimitTypeFulfillmentProduct {
			key := getVolumeKey(volume, limit)
			maxCapacities[key] = maxCap
			isHardCapacities[key] = isHardCap
		} else if limit.LimitType == rulevolume.MaskRuleLimitTypeGroup {
			groupMaxCapacities[limit.GroupCode] = maxCap
			groupIsHardCapacities[limit.GroupCode] = isHardCap
		}
	}

	return &MaxCapacityData{maxCapacities, groupMaxCapacities}, &IsHardCapacityData{isHardCapacities, groupIsHardCapacities}
}

func getVolumeKey(volume *rulevolume.MaskRuleVolumeTab, limit *rulevolume.MaskDefaultVolumeLimitItem) VolumeKey {
	maskProductID := int64(volume.MaskProductID)
	if volume.MaskCombinationMode {
		maskProductID = limit.MaskProductID
	}

	return VolumeKey{
		MaskProductID:        maskProductID,
		FulfillmentProductID: limit.ID,
	}
}

func (repo *maskRuleRepoImpl) GetRuleSimpleInfo(ctx context.Context) ([]*MaskRuleInfo, *srerr.Error) {
	items, err := repo.allocateRuleRepo.GetRuleSimpleInfo(ctx)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	var infos = make([]*MaskRuleInfo, len(items))
	for i, item := range items {
		infos[i] = &MaskRuleInfo{
			Id:   item.Id,
			Name: item.RuleName,
		}
	}
	return infos, nil
}

func (repo *maskRuleRepoImpl) ListRules(ctx context.Context, q *RuleQuery) ([]*MaskRule, int32, *srerr.Error) {
	rs, total, err := repo.allocateRuleRepo.GetRuleList(ctx, q)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, q, err)
	}
	rules := make([]*MaskRule, len(rs))
	for i, data := range rs {
		var err *srerr.Error
		repo.enrichSellerName(ctx, data)
		if data.AllocationMethod != allocation.BatchAllocate {
			rules[i], err = repo.parseRule(ctx, data)
		} else {
			rules[i], err = repo.parseBatchRule(ctx, data)
		}
		if err != nil {
			return nil, 0, err
		}
	}
	return rules, total, nil
}

func (repo *maskRuleRepoImpl) CreateRule(ctx context.Context, name string, maskProductID int64, ruleMode int32, allocationMethod int64) (*MaskRule, *srerr.Error) {
	_, err := repo.allocateRuleRepo.GetDraftRule(ctx, maskProductID, ruleMode, allocationMethod)
	if err == nil {
		return nil, srerr.New(srerr.DraftRuleOfMaskProductExisted, maskProductID, "draft rule of mask_product %v existed", maskProductID)
	}
	if !dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.DatabaseErr, maskProductID, err)
	}
	now := timeutil.GetCurrentUnixTimeStamp(ctx)

	operateBy, _ := apiutil.GetUserInfo(ctx)
	data := &MaskAllocationRuleTab{
		RuleName:         name,
		MaskProductId:    maskProductID,
		RuleDetail:       &MaskRuleDetail{},
		BatchRuleDetail:  BatchRuleDetail{},
		OperatedBy:       operateBy,
		Status:           MaskRuleStatusDraft,
		CTime:            uint32(now),
		MTime:            uint32(now),
		RuleMode:         ruleMode,
		AllocationMethod: allocationMethod,
	}

	data.Rule, _ = json.Marshal(data.RuleDetail)
	data.BatchRule, _ = json.Marshal(data.BatchRuleDetail)
	result, err := repo.allocateRuleRepo.CreateRule(ctx, data)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, maskProductID, err)
	}
	repo.enrichSellerName(ctx, result)
	if allocationMethod != allocation.BatchAllocate {
		return repo.parseRule(ctx, result)
	} else {
		return repo.parseBatchRule(ctx, result)
	}
}

func (repo *maskRuleRepoImpl) UpdateRule(ctx context.Context, update *MaskRule) (*MaskRule, *srerr.Error) {
	r, err := repo.allocateRuleRepo.GetRuleByID(ctx, update.Id)
	if dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.RuleNotFound, update.Id, err)
	}
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, update.Id, err)
	}
	if !r.IsEditable(ctx) {
		return nil, srerr.New(srerr.RuleNotEditable, update.Id, "rule not editable")
	}

	r.Status = update.Status
	r.RuleName = update.RuleName
	r.EnableProductPriority = update.EnableProductPriority
	r.EffectiveStartTime = update.EffectiveStartTime
	r.Rule, err = MarshalRuleDetail(update.MaskRuleSteps)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, update.Id, err)
	}
	r.OperatedBy, _ = apiutil.GetUserInfo(ctx)
	r.MTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	r.AllocationMethod = update.AllocationMethod

	r.BatchRule, err = jsoniter.Marshal(update.BatchRuleDetail)
	r.BatchRuleDetail = update.BatchRuleDetail
	if err != nil {
		return nil, srerr.With(srerr.TypeConvertErr, update.Id, err)
	}

	if update.Status != MaskRuleStatusUncompleted && update.Status != MaskRuleStatusDraft {
		// get product priority of default group, priority必须先存在才可以保存
		_, err := repo.priorityRepo.GetActiveConfigByProductIdAndGroupId(ctx, int(update.MaskProductId), constant.DefaultShopGroupId)
		if err != nil {
			logger.CtxLogErrorf(ctx, err.Error())
			return nil, srerr.With(srerr.RuleNotEditable, err, fmt.Errorf("mask product %d of rule %s should "+
				"have product priority of default group", update.MaskProductId, update.RuleName))
		}
	}
	if err := repo.validateEffectiveTime(ctx, r, update.MaskRuleSteps); err != nil {
		return nil, err
	}

	// SSCSMR-3635 校验新调度因子
	if vErr := repo.ValidatePriority(ctx, update); vErr != nil {
		return nil, vErr
	}

	//SSCSMR-2660:接入审批流, 目前只有single allocate需要接入
	if repo.needAudit(ctx, update) {
		if eErr := repo.ApprovalExecutor.Execute(ctx, auditclient.AllocateSoftRule, update.Id); eErr != nil {
			logger.CtxLogErrorf(ctx, "apply for approval err:%v", eErr)
			return nil, eErr
		}
		r.Status = MaskRuleStatusPendingApproval
	}

	newData, err := repo.allocateRuleRepo.UpdateRule(ctx, r)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, update.Id, err)
	}
	repo.enrichSellerName(ctx, newData)
	if update.AllocationMethod != allocation.BatchAllocate {
		return repo.parseRule(ctx, newData)
	} else {
		return repo.parseBatchRule(ctx, newData)
	}
}

// MarshalRuleDetail 这里的MaskRuleLimit只会有BatchVolume的数据，Min/Max的数据存在于Volume Rule
func MarshalRuleDetail(steps MaskRuleSteps) ([]byte, error) {
	detail := MaskRuleDetail{}
	limits := map[int64]*MaskRuleLimit{}
	getProductLimit := func(productID int64) *MaskRuleLimit {
		productLimit, ok := limits[productID]
		if !ok {
			productLimit = &MaskRuleLimit{
				ID: productID,
			}
			limits[productID] = productLimit
		}
		return productLimit
	}
	for _, step := range steps {
		switch step.MaskStepType {
		case MaskStepMinVolume:
			detail.MinVolumeEnable = true
			detail.MinVolumeSort = step.Priority
			for key, minVolume := range step.MinVolumeData.MinVolumes {
				productLimit := getProductLimit(key.FulfillmentProductID)
				productLimit.MinVolume = minVolume
			}
		case MaskStepMaxCapacity:
			detail.MaxCapacityEnable = true
			detail.MaxCapacitySort = step.Priority
			for key, maxCapacity := range step.MaxCapacityData.MaxCapacities {
				productLimit := getProductLimit(key.FulfillmentProductID)
				productLimit.MaxCapacity = maxCapacity
			}
		case MaskStepMaxBatchVolume:
			detail.MaxBatchVolumeEnable = true
			detail.MaxBatchVolumeSort = step.Priority
			detail.MaxBatchVolume = step.BatchVolumeData.BatchVolume
			for productID, volume := range step.BatchVolumeData.VolumeInOneBatch {
				productLimit := getProductLimit(productID)
				productLimit.MaxVolumeEachBatch = volume
			}
		case MaskStepCheapestFee:
			detail.CheapestFeeEnable = true
			detail.CheapestFeeSort = step.Priority
		case MaskStepMinVolumeCountry:
			detail.MinVolumeCountryEnable = true
			detail.MinVolumeCountrySort = step.Priority
		case MaskStepMinVolumeZoneRoute:
			detail.MinVolumeZoneRouteEnable = true
			detail.MinVolumeZoneRouteSort = step.Priority
		case MaskStepMaxCapacityCountry:
			detail.MaxCapacityCountryEnable = true
			detail.MaxCapacityCountrySort = step.Priority
		case MaskStepMaxCapacityZoneRoute:
			detail.MaxCapacityZoneRouteEnable = true
			detail.MaxCapacityZoneRouteSort = step.Priority
		case MaskStepPickupEfficiencyWhitelist:
			detail.PickupEfficiencyWhitelistEnable = true
			detail.PickupEfficiencyWhitelistSort = step.Priority
		case MaskStepMaxCodCapacityCountry:
			detail.MaxCodCapacityCountryEnable = true
			detail.MaxCodCapacityCountrySort = step.Priority
		case MaskStepMaxCodCapacityZoneRoute:
			detail.MaxCodCapacityZoneRouteEnable = true
			detail.MaxCodCapacityZoneRouteSort = step.Priority
		case MaskStepMaxBulkyCapacityCountry:
			detail.MaxBulkyCapacityCountryEnable = true
			detail.MaxBulkyCapacityCountrySort = step.Priority
		case MaskStepMaxBulkyCapacityZoneRoute:
			detail.MaxBulkyCapacityZoneRouteEnable = true
			detail.MaxBulkyCapacityZoneRouteSort = step.Priority
		case MaskStepMaxHighValueCapacityCountry:
			detail.MaxHighValueCapacityCountryEnable = true
			detail.MaxHighValueCapacityCountrySort = step.Priority
		case MaskStepMaxHighValueCapacityZoneRoute:
			detail.MaxHighValueCapacityZoneRouteEnable = true
			detail.MaxHighValueCapacityZoneRouteSort = step.Priority
		case MaskStepMaxDgCapacityCountry:
			detail.MaxDgCapacityCountryEnable = true
			detail.MaxDgCapacityCountrySort = step.Priority
		case MaskStepMaxDgCapacityZoneRoute:
			detail.MaxDgCapacityZoneRouteEnable = true
			detail.MaxDgCapacityZoneRouteSort = step.Priority
		}
	}
	for _, limit := range limits {
		detail.Limit = append(detail.Limit, limit)
	}

	data, err := jsoniter.Marshal(detail)
	return data, err
}

// todo:SSCSMR-1695: 校验时区分single batch，batch也要先校验是否有effective volume
func (repo *maskRuleRepoImpl) validateEffectiveTime(ctx context.Context, r *MaskAllocationRuleTab, steps MaskRuleSteps) *srerr.Error {
	if r.AllocationMethod == allocation.BatchAllocate {
		return repo.validateBatchRule(ctx, r)
	} else {
		return repo.validateSingleEffectiveTime(ctx, r, steps)
	}
}

func (repo *maskRuleRepoImpl) validateSingleEffectiveTime(ctx context.Context, r *MaskAllocationRuleTab, steps MaskRuleSteps) *srerr.Error {
	if r.Status != MaskRuleStatusActive {
		return nil
	}
	var needValidate bool
	for _, s := range steps {
		switch s.MaskStepType {
		case MaskStepMinVolume, MaskStepMaxCapacity, MaskStepMinVolumeCountry, MaskStepMinVolumeZoneRoute, MaskStepMaxCapacityCountry, MaskStepMaxCapacityZoneRoute:
			needValidate = true
			break
		}
	}
	if !needValidate {
		return nil
	}
	if v, _ := repo.maskRuleVolume.GetRuleVolumeByEffectiveStartTime(ctx, r.MaskProductId, int64(r.EffectiveStartTime), rule_mode.RuleMode(r.RuleMode), r.AllocationMethod); v == nil {
		return srerr.New(srerr.InValidEffectiveTimeError, nil, "there is no effective rule volume before %v", timeutil.ConvertTimeStampToTime(int64(r.EffectiveStartTime)))
	}

	return nil
}

func (repo *maskRuleRepoImpl) validateBatchRule(ctx context.Context, r *MaskAllocationRuleTab) *srerr.Error {
	if r.Status != MaskRuleStatusActive {
		return nil
	}
	//active态
	//1.校验有无相同mask product的active single rule
	_, err := repo.allocateRuleRepo.GetRuleByCondition(ctx, map[string]interface{}{
		"mask_product_id = ?":      r.MaskProductId,
		"allocation_method in (?)": []int64{allocation.Zero, allocation.SingleAllocate},
		"rule_status = ?":          MaskRuleStatusActive,
		"rule_mode = ?":            rule_mode.RuleMode(r.RuleMode),
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "validate batch rule|get active single rule err:%v", err)
		return srerr.New(srerr.DatabaseErr, nil, " if you want to affective Batch allocation soft rule must need have an active single soft rule firstly to support BMOA, datafix and fallback; pls save draft and create a single allocation soft rule firstly")
	}

	if !r.BatchRuleDetail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled && !r.BatchRuleDetail.BatchAllocationRuleConfig.CountryVolumeEnabled {
		return srerr.New(srerr.ParamErr, nil, "one of country or zone-route volume should be toggle on")
	}

	withoutActiveVolumeErr := srerr.New(srerr.InValidEffectiveTimeError, nil, "there is no effective rule volume before %v", timeutil.ConvertTimeStampToTime(int64(r.EffectiveStartTime)))
	//2.校验有无相同mask product的active volume
	activeVolume, gErr := repo.maskRuleVolume.GetRuleVolumeByEffectiveStartTime(ctx, r.MaskProductId, int64(r.EffectiveStartTime), rule_mode.RuleMode(r.RuleMode), r.AllocationMethod)
	if gErr != nil {
		// 找不到有效的Volume Rule，统一封装为以下错误
		return withoutActiveVolumeErr
	}

	// Soft Rule开启了Zone/Route时，Volume Rule的类型要不能为Country，否则报错拦截
	if r.BatchRuleDetail.BatchAllocationRuleConfig.ZoneRouteVolumeEnabled && activeVolume.RuleType == rulevolume.LocVolumeTypeCountry {
		return withoutActiveVolumeErr
	}

	return nil
}

func (repo *maskRuleRepoImpl) DeleteRule(ctx context.Context, ruleID int64) *srerr.Error {
	data, err := repo.allocateRuleRepo.GetRuleByID(ctx, ruleID)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, ruleID, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, ruleID, err)
	}
	if data.Status != MaskRuleStatusUncompleted && data.Status != MaskRuleStatusDraft {
		return srerr.New(srerr.RuleNotDeletable, ruleID, "active rule cannot be deleted")
	}
	err = repo.allocateRuleRepo.DeleteRule(ctx, ruleID)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, ruleID, err)
	}
	return nil
}

func (repo *maskRuleRepoImpl) CopyRule(ctx context.Context, ruleID int64) *srerr.Error {
	data, err := repo.allocateRuleRepo.GetRuleByID(ctx, ruleID)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, ruleID, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, ruleID, err)
	}
	if data.Status == MaskRuleStatusUncompleted || data.Status == MaskRuleStatusDraft {
		return srerr.With(srerr.RuleNotCopiable, ruleID, fmt.Errorf("rule %d status %d not support copy", ruleID, data.Status))
	}
	_, err = repo.allocateRuleRepo.GetDraftRule(ctx, data.MaskProductId, data.RuleMode, data.AllocationMethod)
	if err == nil {
		return srerr.New(srerr.DraftRuleOfMaskProductExisted, data.MaskProductId, "draft rule of mask_product %v existed", data.MaskProductId)
	}
	if !dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.DatabaseErr, ruleID, err)
	}

	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	operatedBy, _ := apiutil.GetUserInfo(ctx)
	newData := &MaskAllocationRuleTab{
		MaskProductId:      data.MaskProductId,
		RuleName:           data.RuleName,
		EffectiveStartTime: data.EffectiveStartTime,
		Status:             MaskRuleStatusDraft,
		OperatedBy:         operatedBy,
		Rule:               data.Rule,
		CTime:              uint32(now),
		MTime:              uint32(now),
		RuleMode:           data.RuleMode,
		AllocationMethod:   data.AllocationMethod,
		BatchRule:          data.BatchRule,
	}
	_, err = repo.allocateRuleRepo.CreateRule(ctx, newData)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, newData, err)
	}

	return nil
}

func (repo *maskRuleRepoImpl) UpdateAllocationRuleVolumeStatusExpired(ctx context.Context) {
	var (
		reportInterfaceName = "UpdateAllocationRuleVolumeStatusExpired"
		startTime           = recorder.Now(ctx).Unix()
	)

	for _, allocationMethod := range []int64{allocation.SingleAllocate, allocation.BatchAllocate} {
		for _, mode := range []rule_mode.RuleMode{rule_mode.MplOrderRule, rule_mode.WmsOrderRule} {
			// 1. SELECT * FROM `mask_channel_rule_volume_tab` WHERE `status` = '3' AND effective_start_time < now() ORDER BY effective_start_time DESC;
			ruleVolumes, err := repo.maskRuleVolume.GetActiveRuleVolumes(ctx, mode, allocationMethod)
			if err != nil {
				reportData := fmt.Sprintf("GetActiveRuleVolumes fail|err=%v", err)
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
				logger.CtxLogErrorf(ctx, reportData)
				continue
			}

			// 2. get need expire rule volumes
			needExpireRuleID := repo.getNeedExpireVolumeRuleIds(ruleVolumes)
			if len(needExpireRuleID) == 0 {
				logger.CtxLogErrorf(ctx, "no rule need to expire")
				continue
			}

			needExpireRuleID = objutil.RemoveDuplicateUint64(needExpireRuleID)
			if err := repo.maskRuleVolume.UpdateRuleVolumeStatusToExpired(ctx, needExpireRuleID); err != nil {
				reportData := fmt.Sprintf("UpdateRuleVolumeStatusToExpired fail|err=%v", err)
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
				logger.CtxLogErrorf(ctx, reportData)
				continue
			}

			for _, ruleVolumeID := range needExpireRuleID {
				if err := repo.maskRuleVolume.DeleteRouteLimitsByRuleVolumeID(ctx, ruleVolumeID); err != nil {
					logger.LogErrorf("DeleteRouteLimitsByRuleVolumeID failed| err: %v", err)
				}
				if err := repo.maskRuleVolume.DeleteZoneLimitsByRuleVolumeID(ctx, ruleVolumeID); err != nil {
					logger.LogErrorf("DeleteZoneLimitsByRuleVolumeID failed| err: %v", err)
				}
			}
			reportData := fmt.Sprintf("Update rule volumes to expired |rule mode = %s, rule volumes = %v", mode, needExpireRuleID)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusSuccess, reportData)
			change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "schedule_rule", "updating expired rule volumes", reportData, report_constant.RiskLevelHigh)
			logger.CtxLogErrorf(ctx, reportData)

			// 上报prometheus
			prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameScheduleRule, int64(len(needExpireRuleID)))
		}
	}
}

func (repo *maskRuleRepoImpl) getNeedExpireVolumeRuleIds(ruleVolumes []*rulevolume.MaskRuleVolumeTab) []uint64 {
	var (
		needExpireRuleVolumeList = make([]*rulevolume.MaskRuleVolumeTab, 0)
		needExpireRuleIDList     = make([]uint64, 0)
		existRule                = make(map[int]uint64)
	)

	// 先标记哪些Mask Product哪些冗余的Rule
	for _, v := range ruleVolumes {
		maskProductIDList := []int{v.MaskProductID}
		if v.MaskCombinationMode {
			maskProductIDList = make([]int, 0, len(v.GroupInfo.MaskProductInfos))
			for _, maskProductInfo := range v.GroupInfo.MaskProductInfos {
				maskProductIDList = append(maskProductIDList, maskProductInfo.MaskProductID)
			}
		}

		for _, maskProductID := range maskProductIDList {
			if _, exist := existRule[maskProductID]; exist {
				needExpireRuleVolumeList = append(needExpireRuleVolumeList, v)
			} else {
				existRule[maskProductID] = v.ID
			}
		}
	}

	// 找出冗余的Rule后，要校验Mask组合模式
	for _, v := range needExpireRuleVolumeList {
		needExpire := true
		if v.MaskCombinationMode {
			// 如果要失效的Rule是组合模式，需要确认组合的每一个Mask Product都有另外的更加新的Rule才能失效
			for _, maskProductInfo := range v.GroupInfo.MaskProductInfos {
				if existRule[maskProductInfo.MaskProductID] == v.ID {
					needExpire = false
				}
			}
		}

		if needExpire {
			needExpireRuleIDList = append(needExpireRuleIDList, v.ID)
		}
	}

	return needExpireRuleIDList
}

func (repo *maskRuleRepoImpl) enrichSellerName(ctx context.Context, data *MaskAllocationRuleTab) {
	if len(data.MaskProductName) == 0 {
		productInfo, gErr := repo.lpsApi.GetProductDetail(ctx, int(data.MaskProductId))
		if gErr != nil || productInfo == nil {
			//兼容smr流向逻辑，获取不到不打断进程
			logger.CtxLogErrorf(ctx, "enrichSellerName|mask product id:%v, get mask product err:%v, product info is nil:%v, but will not terminate process", data.MaskProductId, gErr, productInfo == nil)
		} else {
			data.MaskProductName = productInfo.SellerDisplayName
		}
	}
}

func convertAllocationMethod(allocationMethod int64) int64 {
	if allocationMethod == allocation.BatchAllocate {
		return allocationMethod
	} else {
		return allocation.SingleAllocate
	}
}

func (repo *maskRuleRepoImpl) GetRuleByCondition(ctx context.Context, condition map[string]interface{}) (*MaskRule, *srerr.Error) {
	tab, err := repo.allocateRuleRepo.GetRuleByCondition(ctx, condition)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get rule by condition:%v, db err:%v", condition, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	if tab.AllocationMethod != allocation.BatchAllocate {
		return repo.parseRule(ctx, tab)
	} else {
		return repo.parseBatchRule(ctx, tab)
	}
}

func (repo *maskRuleRepoImpl) DisableBatchRule(ctx context.Context, ruleID int64) *srerr.Error {
	tab, err := repo.allocateRuleRepo.GetRuleByID(ctx, ruleID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get rule by id:%v, db err:%v", ruleID, err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if tab.AllocationMethod != allocation.BatchAllocate {
		logger.CtxLogErrorf(ctx, "get rule by id:%v,rule is single, not allow to disable", ruleID)
		return srerr.New(srerr.ParamErr, nil, "rule is single, not allow to disable")
	}

	tab.Status = MaskRuleStatusExpired
	if _, uErr := repo.allocateRuleRepo.UpdateRule(ctx, tab); uErr != nil {
		logger.CtxLogErrorf(ctx, "update mask rule err:%v", uErr)
		return srerr.With(srerr.DatabaseErr, nil, uErr)
	}

	return nil
}

func (repo *maskRuleRepoImpl) needAudit(ctx context.Context, update *MaskRule) bool {
	if update.AllocationMethod != allocation.SingleAllocate {
		return false
	}
	if update.Status != MaskRuleStatusActive {
		return false
	}
	return configutil.GetBusinessAuditConf(ctx).NeedAudit
}
