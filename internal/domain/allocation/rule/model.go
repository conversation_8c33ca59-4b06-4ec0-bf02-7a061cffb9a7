package rule

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

const (
	tableName = "allocation_rule_tab"
)

var (
	MaskAllocationRuleHook = &MaskAllocationRuleTab{}
)

type MaskAllocationRuleTab struct {
	Id                    int64           `gorm:"column:id" json:"id"`
	MaskProductId         int64           `gorm:"column:mask_product_id" json:"mask_product_id"`
	Status                MaskRuleStatus  `gorm:"column:rule_status" json:"rule_status"` // 状态 0: uncompleted，1: draft 2: queueing, 3: active, 4: expired, 5: pending approval
	RuleName              string          `gorm:"column:rule_name" json:"rule_name"`
	Rule                  []byte          `gorm:"column:rule_detail" json:"-"`
	EnableProductPriority bool            `gorm:"column:enable_product_priority" json:"enable_product_priority"`
	OperatedBy            string          `gorm:"column:operated_by" json:"operated_by"`
	EffectiveStartTime    uint32          `gorm:"column:effective_start_time" json:"effective_start_time"`
	CTime                 uint32          `gorm:"column:ctime" json:"ctime"`
	MTime                 uint32          `gorm:"column:mtime" json:"mtime"`
	ApprovalTime          int64           `gorm:"column:approval_time" json:"approval_time"`
	ForecastTaskId        int64           `gorm:"column:forecast_task_id" json:"forecast_task_id"` // 预测任务id
	RuleDetail            *MaskRuleDetail `gorm:"-" json:"rule_detail,omitempty"`
	MaskProductName       string          `gorm:"-" json:"mask_product_name"`
	RuleMode              int32           `gorm:"column:rule_mode" json:"rule_mode"`
	AllocationMethod      int64           `gorm:"column:allocation_method" json:"allocation_method"`
	BatchRule             []byte          `gorm:"column:batch_rule" json:"batch_rule"`
	BatchRuleDetail       BatchRuleDetail `gorm:"-" json:"batch_rule_detail"`
}

func (MaskAllocationRuleTab) TableName() string {
	return tableName
}

func (MaskAllocationRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (MaskAllocationRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (r MaskAllocationRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            uint64(r.Id),
		ModelName:     r.TableName(),
		MaskProductId: uint64(r.MaskProductId),
	}
}

func (r *MaskAllocationRuleTab) ConvertToChangeData() string {
	return fmt.Sprintf("%d-%s", r.MaskProductId, r.MaskProductName)
}

func (r *MaskAllocationRuleTab) IsEditable(ctx context.Context) bool {
	if r.Status == MaskRuleStatusUncompleted || r.Status == MaskRuleStatusDraft || r.Status == MaskRuleStatusQueuing {
		return true
	}
	if r.Status == MaskRuleStatusActive && r.EffectiveStartTime > uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		return true
	}
	return false
}

func (r *MaskAllocationRuleTab) UnmarshalBatchRule() {
	detail := BatchRuleDetail{}
	if err := jsoniter.Unmarshal(r.BatchRule, &detail); err != nil {
		return
	}
	r.BatchRuleDetail = detail
}

type MaskRuleDetail struct {
	MinVolumeEnable                     bool             `json:"min_volume_enable,omitempty"`
	MinVolumeCountryEnable              bool             `json:"min_volume_country_enable"`
	MinVolumeZoneRouteEnable            bool             `json:"min_volume_zone_route_enable"`
	MinVolumeSort                       int32            `json:"min_volume_sort,omitempty"`
	MinVolumeCountrySort                int32            `json:"min_volume_country_sort,omitempty"`
	MinVolumeZoneRouteSort              int32            `json:"min_volume_zone_route_sort,omitempty"`
	MaxCapacityEnable                   bool             `json:"max_capacity_enable,omitempty"`
	MaxCapacityCountryEnable            bool             `json:"max_capacity_country_enable"`
	MaxCapacityZoneRouteEnable          bool             `json:"max_capacity_zone_route_enable"`
	MaxCapacitySort                     int32            `json:"max_capacity_sort,omitempty"`
	MaxCapacityCountrySort              int32            `json:"max_capacity_country_sort,omitempty"`
	MaxCapacityZoneRouteSort            int32            `json:"max_capacity_zone_route_sort,omitempty"`
	CheapestFeeEnable                   bool             `json:"cheapest_fee_enable"`
	CheapestFeeSort                     int32            `json:"cheapest_fee_sort,omitempty"`
	MaxBatchVolume                      int32            `json:"max_batch_volume"`
	MaxBatchVolumeEnable                bool             `json:"max_batch_volume_enable"`
	MaxBatchVolumeSort                  int32            `json:"max_batch_volume_sort,omitempty"`
	Limit                               []*MaskRuleLimit `json:"limit,omitempty"`
	PickupEfficiencyWhitelistEnable     bool             `json:"pickup_efficiency_whitelist_enable"`
	PickupEfficiencyWhitelistSort       int32            `json:"pickup_efficiency_whitelist_sort,omitempty"`
	MaxCodCapacityCountryEnable         bool             `json:"max_cod_capacity_country_enable"`
	MaxCodCapacityCountrySort           int32            `json:"max_cod_capacity_country_sort,omitempty"`
	MaxCodCapacityZoneRouteEnable       bool             `json:"max_cod_capacity_zone_route_enable"`
	MaxCodCapacityZoneRouteSort         int32            `json:"max_cod_capacity_zone_route_sort,omitempty"`
	MaxBulkyCapacityCountryEnable       bool             `json:"max_bulky_capacity_country_enable"`
	MaxBulkyCapacityCountrySort         int32            `json:"max_bulky_capacity_country_sort,omitempty"`
	MaxBulkyCapacityZoneRouteEnable     bool             `json:"max_bulky_capacity_zone_route_enable"`
	MaxBulkyCapacityZoneRouteSort       int32            `json:"max_bulky_capacity_zone_route_sort,omitempty"`
	MaxHighValueCapacityCountryEnable   bool             `json:"max_high_value_capacity_country_enable"`
	MaxHighValueCapacityCountrySort     int32            `json:"max_high_value_capacity_country_sort,omitempty"`
	MaxHighValueCapacityZoneRouteEnable bool             `json:"max_high_value_capacity_zone_route_enable"`
	MaxHighValueCapacityZoneRouteSort   int32            `json:"max_high_value_capacity_zone_route_sort,omitempty"`
	MaxDgCapacityCountryEnable          bool             `json:"max_dg_capacity_country_enable"`
	MaxDgCapacityCountrySort            int32            `json:"max_dg_capacity_country_sort,omitempty"`
	MaxDgCapacityZoneRouteEnable        bool             `json:"max_dg_capacity_zone_route_enable"`
	MaxDgCapacityZoneRouteSort          int32            `json:"max_dg_capacity_zone_route_sort,omitempty"`
}

type MaskRuleLimit struct {
	MaskProductID      int64  `json:"mask_product_id"`
	ID                 int64  `json:"id"`
	MinVolume          int32  `json:"min_volume"`
	MaxCapacity        int32  `json:"max_capacity"`
	Name               string `json:"name"`
	MaxVolumeEachBatch int32  `json:"max_volume_each_batch"`
}

type BatchRuleDetail struct {
	BatchAllocationRuleConfig model.BatchAllocationRuleConfig `json:"batch_allocation_rule_config"`
	BatchSize                 model.BatchSize                 `json:"batch_size"`
}
