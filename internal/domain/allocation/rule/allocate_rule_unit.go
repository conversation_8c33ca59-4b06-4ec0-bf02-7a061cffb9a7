package rule

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strings"
)

type AllocateRuleUnitImpl struct {
	BusinessAuditRepo  business_audit.BusinessAuditRepo
	AllocationRuleRepo AllocationRuleRepo
}

func NewAllocateRuleUnitImpl(BusinessAuditRepo business_audit.BusinessAuditRepo,
	AllocationRuleRepo AllocationRuleRepo) *AllocateRuleUnitImpl {
	return &AllocateRuleUnitImpl{
		BusinessAuditRepo:  BusinessAuditRepo,
		AllocationRuleRepo: AllocationRuleRepo,
	}
}

// Listener 监听审批结果
func (u *AllocateRuleUnitImpl) Listener(ctx context.Context, ticketID string, approvalStatus string, tab *business_audit.BusinessAuditTab) *srerr.Error {
	//1. 更新business audit tab
	if uErr := u.BusinessAuditRepo.Update(ctx, map[string]interface{}{"ticket_id = ?": ticketID}, map[string]interface{}{"approval_status": approvalStatus}); uErr != nil {
		logger.CtxLogErrorf(ctx, "ticket id:%s, update approval ticket status err:%v", ticketID, uErr)
		return uErr
	}
	//2. 根据ticket id获取对应的soft rule
	softRule, err := u.AllocationRuleRepo.GetRuleByID(ctx, int64(tab.ConfigDbID))
	if err != nil {
		logger.CtxLogErrorf(ctx, "allocation soft rule id:%d, get rule err:%v", tab.ConfigDbID, err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	//3. 判断approval status是否是approved(审批单状态（PENDING,APPROVED,CANCELED,REJECTED)
	if approvalStatus == string(business_audit.ApprovalStatusRejected) || approvalStatus == string(business_audit.ApprovalStatusCanceled) {
		//3.1 once rejected, rule back to draft
		softRule.Status = MaskRuleStatusDraft
	}
	if approvalStatus == string(business_audit.ApprovalStatusApproved) {
		//3.2 比较effective start time和current time（approval time）
		if int64(softRule.EffectiveStartTime) <= timeutil.GetCurrentTime(ctx).Unix() {
			// effective time <= approval time, effective immediately
			// 由schedule rule使用active + effective time来判断是否是upcoming
			softRule.Status = MaskRuleStatusActive
		} else {
			// effective time > approval time, set status to upcoming
			// 由schedule rule使用active + effective time来判断是否是upcoming
			softRule.Status = MaskRuleStatusActive
		}
		softRule.ApprovalTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}
	if _, err := u.AllocationRuleRepo.UpdateRule(ctx, softRule); err != nil {
		logger.CtxLogErrorf(ctx, "update soft rule to status:%s err:%v", softRule.Status.Type(), err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (u *AllocateRuleUnitImpl) BusinessType() string {
	return "allocate_soft_rule"
}

//获取审批请求时的extra data
func (u *AllocateRuleUnitImpl) GetExtraData() string {
	return allocation.ExtraInfo
}

//	{"application_info": {"Application Details:": {"href": "链接","text": "文案"}}}
//获取审批请求时的application info
func (u *AllocateRuleUnitImpl) GetApplicationInfo(configDbId int64) string {
	link := fmt.Sprintf(allocation.AllocationRuleLinkFormat, strings.ToLower(envvar.GetEnv())+".", configDbId, envvar.GetCID())
	if envvar.GetEnv() == "LIVE" {
		link = fmt.Sprintf(allocation.AllocationRuleLinkFormat, "", configDbId, envvar.GetCID())
	}
	info := fmt.Sprintf(allocation.ApplicationInfo, link, "update smartrouting allocation soft rule")
	return info
}
func (u *AllocateRuleUnitImpl) GenerateAuditTab(ctx context.Context, configDbId uint64) *business_audit.BusinessAuditTab {
	timeStamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	userEmail, _ := apiutil.GetUserInfo(ctx)
	tab := &business_audit.BusinessAuditTab{
		BusinessType:   u.BusinessType(),
		ApprovalStatus: string(business_audit.ApprovalStatusPending),
		Ctime:          timeStamp,
		Mtime:          timeStamp,
		Operator:       userEmail,
		ConfigDbID:     configDbId,
	}
	return tab
}
