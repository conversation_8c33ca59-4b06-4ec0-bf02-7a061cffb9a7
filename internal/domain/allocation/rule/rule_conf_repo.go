//go:generate mockgen -destination=./mock_rule_conf_repo.go -package=allocation . MaskRuleConfRepo

package rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strings"
)

// MaskRuleConfRepo 是规则的控制配置，它规定了一个市场哪些规则项可以使用，哪些规则项不可用使用
type MaskRuleConfRepo interface {
	GetRuleConf(ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error)
}

type MaskRuleConf struct {
	Steps                  []MaskStepConf
	DisableProductPriority bool
}

type MaskStepConf struct {
	Step     MaskStepType
	Disabled bool
}

func (c MaskRuleConf) IsStepEnabled(step MaskStepType) bool {
	for _, s := range c.Steps {
		if s.Step == step {
			return !s.Disabled
		}
	}
	return true
}

type ruleConfRepo struct {
}

func NewMaskRuleConfRepo() MaskRuleConfRepo {
	return &ruleConfRepo{}
}

func (r *ruleConfRepo) GetRuleConf(ctx context.Context, rm rule_mode.RuleMode) (MaskRuleConf, *srerr.Error) {
	var cnf configutil.ProductAllocationConf
	if rm == rule_mode.WmsOrderRule {
		cnf = configutil.RefreshWmsProductAllocationConfig()
	} else {
		cnf = configutil.GetProductAllocationConf(ctx)
	}
	rulesDisabled := map[MaskStepType]bool{}
	if cnf.DisableRuleSteps != "" {
		ruleStrDisabled := strings.Split(cnf.DisableRuleSteps, ",")
		for _, s := range ruleStrDisabled {
			if step, ok := stepStrInConf[s]; ok {
				rulesDisabled[step] = true
			}
		}
	}
	steps := make([]MaskStepConf, len(MaskAllSteps))
	for i, step := range MaskAllSteps {
		steps[i] = MaskStepConf{Step: step, Disabled: rulesDisabled[step]}
	}
	return MaskRuleConf{Steps: steps, DisableProductPriority: cnf.DisableProductPriority}, nil
}

var stepStrInConf = map[string]MaskStepType{
	"min_volume":                         MaskStepMinVolume,
	"max_capacity":                       MaskStepMaxCapacity,
	"max_batch_volume":                   MaskStepMaxBatchVolume,
	"cheapest_fee":                       MaskStepCheapestFee,
	"min_volume_country":                 MaskStepMinVolumeCountry,
	"min_volume_zone_route":              MaskStepMinVolumeZoneRoute,
	"max_capacity_country":               MaskStepMaxCapacityCountry,
	"max_capacity_zone_route":            MaskStepMaxCapacityZoneRoute,
	"pickup_efficiency_whitelist":        MaskStepPickupEfficiencyWhitelist,
	"max_cod_capacity_country":           MaskStepMaxCodCapacityCountry,
	"max_cod_capacity_zone_route":        MaskStepMaxCodCapacityZoneRoute,
	"max_bulky_capacity_country":         MaskStepMaxBulkyCapacityCountry,
	"max_bulky_capacity_zone_route":      MaskStepMaxBulkyCapacityZoneRoute,
	"max_high_value_capacity_country":    MaskStepMaxHighValueCapacityCountry,
	"max_high_value_capacity_zone_route": MaskStepMaxHighValueCapacityZoneRoute,
	"max_dg_capacity_country":            MaskStepMaxDgCapacityCountry,
	"max_dg_capacity_zone_route":         MaskStepMaxDgCapacityZoneRoute,
}

type SimpleRuleConfRepo struct {
	C MaskRuleConf
}

func (r *SimpleRuleConfRepo) GetRuleConf(ctx context.Context) (MaskRuleConf, *srerr.Error) {
	return r.C, nil
}
