package pickup_priority

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

func DumpPickupPriority() (map[string]interface{}, error) {
	// 获取所有Pickup Priority
	tabs := make([]PickupPriorityTab, 0)
	if err := dbutil.Select(context.Background(), PickupPriorityTabHook, nil, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	data := make(map[string]interface{}, len(tabs))
	for _, tab := range tabs {
		tab.Unmarshal()
		data[strconv.Itoa(int(tab.MaskingProductId))] = tab.GetPriorityMap()
	}

	return data, nil
}
