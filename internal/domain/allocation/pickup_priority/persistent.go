package pickup_priority

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

const tableName = "pickup_priority_tab"

var (
	PickupPriorityTabHook = &PickupPriorityTab{}
)

type PickupPriorityTab struct {
	Id               uint64 `gorm:"column:id" json:"id"`
	MaskingProductId int64  `gorm:"column:masking_product_id" json:"masking_product_id"`
	ProductPriority  []byte `gorm:"column:product_priority" json:"-"`
	Operator         string `gorm:"column:operator" json:"operator"`
	Ctime            int64  `gorm:"column:ctime" json:"ctime"`
	Mtime            int64  `gorm:"column:mtime" json:"mtime"`

	ProductPriorityDto ProductPriority `gorm:"-" json:"product_priority"`
}

func (t *PickupPriorityTab) TableName() string {
	return tableName
}

func (t *PickupPriorityTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *PickupPriorityTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *PickupPriorityTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        t.Id,
		ModelName: t.TableName(),
	}
}

func (t *PickupPriorityTab) Marshal() {
	t.ProductPriority = objutil.JsonBytes(t.ProductPriorityDto)
}

func (t *PickupPriorityTab) Unmarshal() {
	_ = objutil.UnmarshalBytes(&t.ProductPriorityDto, t.ProductPriority)
}

func (t *PickupPriorityTab) GetPriorityMap() map[int64]int64 {
	result := make(map[int64]int64, 0)

	for _, priority := range t.ProductPriorityDto.List {
		result[priority.ProductId] = priority.PrioritySequence
	}

	return result
}
