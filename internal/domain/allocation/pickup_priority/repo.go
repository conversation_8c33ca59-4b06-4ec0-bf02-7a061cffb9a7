package pickup_priority

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type PickupPriorityRepo interface {
	Create(ctx context.Context, tab *PickupPriorityTab) *srerr.Error
	List(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]*PickupPriorityTab, int64, *srerr.Error)
	Delete(ctx context.Context, maskingProductId int64) *srerr.Error
	Update(ctx context.Context, condition map[string]interface{}, tab *PickupPriorityTab) *srerr.Error
	Get(ctx context.Context, condition map[string]interface{}) (*PickupPriorityTab, *srerr.Error)
}

type PickupPriorityRepoImpl struct {
}

func NewPickupPriorityRepoImpl() *PickupPriorityRepoImpl {
	return &PickupPriorityRepoImpl{}
}

func (i *PickupPriorityRepoImpl) Create(ctx context.Context, tab *PickupPriorityTab) *srerr.Error {
	if err := dbutil.Insert(ctx, tab, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (i *PickupPriorityRepoImpl) Update(ctx context.Context, condition map[string]interface{}, tab *PickupPriorityTab) *srerr.Error {
	if err, rows := dbutil.UpdateByObj(ctx, PickupPriorityTabHook, condition, tab, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	} else if rows == 0 {
		return srerr.New(srerr.DatabaseErr, nil, "no priority updated")
	} else {
		return nil
	}
}

func (i *PickupPriorityRepoImpl) List(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]*PickupPriorityTab, int64, *srerr.Error) {
	var (
		total int64
		tabs  []*PickupPriorityTab
	)
	if err := dbutil.Count(ctx, PickupPriorityTabHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err := dbutil.Select(ctx, PickupPriorityTabHook, condition, &tabs, dbutil.WithPage(offset, size), dbutil.WithOrder("mtime desc")); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tabs, total, nil
}

func (i *PickupPriorityRepoImpl) Delete(ctx context.Context, maskingProductId int64) *srerr.Error {
	if err := dbutil.Delete(ctx, PickupPriorityTabHook, map[string]interface{}{"masking_product_id = ?": maskingProductId}, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (i *PickupPriorityRepoImpl) Get(ctx context.Context, condition map[string]interface{}) (*PickupPriorityTab, *srerr.Error) {
	var (
		tab *PickupPriorityTab
	)

	if err := dbutil.Select(ctx, PickupPriorityTabHook, condition, &tab); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tab, nil
}
