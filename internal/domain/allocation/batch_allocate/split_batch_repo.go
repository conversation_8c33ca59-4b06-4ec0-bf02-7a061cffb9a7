package batch_allocate

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type SplitBatchRepo interface {
	GetBatchByCondition(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, *srerr.Error)
	GetBatchListByCondition(ctx context.Context, condition map[string]interface{}) ([]BAOnlineBatchTab, *srerr.Error)
	GetLastBatchByConditionFromMaster(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, *srerr.Error)
	GeFirstBatchByCondition(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, bool, *srerr.Error)
	CreateBatches(ctx context.Context, tabs []BAOnlineBatchTab) *srerr.Error
	UpdateBatchObject(ctx context.Context, data BAOnlineBatchTab) *srerr.Error
}

type SplitBatchRepoImpl struct {
}

func NewSplitBatchRepoImpl() *SplitBatchRepoImpl {
	return &SplitBatchRepoImpl{}
}

func (r *SplitBatchRepoImpl) GetBatchByCondition(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, *srerr.Error) {
	var tab BAOnlineBatchTab
	if err := dbutil.Take(ctx, BAOnlineBatchTabHook, condition, &tab); err != nil {
		return tab, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tab, nil
}

func (r *SplitBatchRepoImpl) GetBatchListByCondition(ctx context.Context, condition map[string]interface{}) ([]BAOnlineBatchTab, *srerr.Error) {
	var tabs []BAOnlineBatchTab
	if err := dbutil.Select(ctx, BAOnlineBatchTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tabs, nil
}

func (r *SplitBatchRepoImpl) GetLastBatchByConditionFromMaster(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, *srerr.Error) {
	var tab BAOnlineBatchTab
	if err := dbutil.LastFromMaster(ctx, BAOnlineBatchTabHook, condition, &tab); err != nil {
		if err != scormv2.ErrRecordNotFound {
			return tab, srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	return tab, nil
}

func (r *SplitBatchRepoImpl) GeFirstBatchByCondition(ctx context.Context, condition map[string]interface{}) (BAOnlineBatchTab, bool, *srerr.Error) {
	var tab BAOnlineBatchTab
	if err := dbutil.First(ctx, BAOnlineBatchTabHook, condition, &tab); err != nil {
		if err == scormv2.ErrRecordNotFound {
			return tab, false, nil
		}
		return tab, false, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tab, true, nil
}

func (r *SplitBatchRepoImpl) CreateBatches(ctx context.Context, tabs []BAOnlineBatchTab) *srerr.Error {
	if err := dbutil.InsertBatch(ctx, BAOnlineBatchTabHook, tabs); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *SplitBatchRepoImpl) UpdateBatchObject(ctx context.Context, data BAOnlineBatchTab) *srerr.Error {
	if err := dbutil.SaveByObj(ctx, BAOnlineBatchTabHook, nil, data, nil, dbutil.ModelInfo{MaskProductId: data.MaskProductID}); err != nil {
		return srerr.With(srerr.DatabaseErr, data, err)
	}

	return nil
}
