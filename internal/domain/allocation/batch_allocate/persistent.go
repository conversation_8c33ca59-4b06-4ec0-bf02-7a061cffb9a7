package batch_allocate

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

const (
	tableName       = "batch_allocate_online_batch_tab"
	BatchPending    = 1
	BatchProcessing = 2
	BatchDone       = 3
	BatchFailed     = 4
	BatchTimeout    = 5
)

var BAOnlineBatchTabHook = &BAOnlineBatchTab{}

type BAOnlineBatchTab struct {
	ID                 uint64 `gorm:"column:id" json:"id"`
	MaskProductID      uint64 `gorm:"column:mask_product_id" json:"mask_product_id"`
	FirstOrderDbID     uint64 `gorm:"column:first_order_db_id" json:"first_order_db_id"`
	LastOrderDbID      uint64 `gorm:"column:last_order_db_id" json:"last_order_db_id"`
	LastOrderTableName string `gorm:"column:last_order_table_name" json:"last_order_table_name"`
	BatchStatus        int    `gorm:"column:batch_status" json:"batch_status"`
	RetryTimes         int    `gorm:"column:retry_times" json:"retry_times"`
	Ctime              int64  `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	Mtime              int64  `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (t *BAOnlineBatchTab) TableName() string {
	return tableName
}

func (t *BAOnlineBatchTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateRead
}

func (t *BAOnlineBatchTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingBatchAllocateWrite
}

func (t *BAOnlineBatchTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            t.ID,
		ModelName:     t.TableName(),
		MaskProductId: t.MaskProductID,
	}
}

type BatchAllocateOrderTab struct {
	Id uint64 `gorm:"column:id" json:"id"`
}
