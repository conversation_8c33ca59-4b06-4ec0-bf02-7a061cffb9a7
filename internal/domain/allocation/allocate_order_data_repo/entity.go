package allocate_order_data_repo

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"

type AllocationHbaseEntity struct {
	OrderId              uint64 `json:"order_id"`
	FulfillmentProductId int    `json:"fulfillment_product_id"`
	MaskProductId        int    `json:"mask_product_id"`
	RequestTime          int64  `json:"request_time"`

	RequestDataStr string `json:"request_data"`

	HardOutput          []int                      `json:"hard_output"`
	ZoneDestinationCode string                     `json:"zone_destination_code"`
	ZoneOriginCode      string                     `json:"zone_origin_code"`
	RouteCodes          []string                   `json:"route_codes"`
	ShopGroupId         int64                      `json:"shop_group_id"`
	ProductShippingFee  []model.ProductShippingFee `json:"shipping_fee_list"`
}
