package allocate_order_data_repo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	jsoniter "github.com/json-iterator/go"
	"runtime"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	//insertLimit = 100
	//deleteLimit = 500
	defaultReadLimit   = 256
	defaultChannelSize = 256
	maxLoopTimes       = ********
	defaultSaltSize    = 1000
	tableTimeLayout    = "********"
	panicMsgSize       = 4096
	sleepSize          = 200
)

type AllocateOrderDataRepo interface {
	CreateAllocateOrderData(ctx context.Context, order *model.AllocateOrderDataTab) *srerr.Error
	GetAllocateOrderData(ctx context.Context, maskProductId int, day time.Time) (<-chan *model.AllocateOrderDataTab, <-chan *gohbase.Result, bool, error)
	GetAllocateOrderDataFromTidb(ctx context.Context, maskProductId int, day time.Time, readLimit, channelSize int) (chan *model.AllocateOrderDataTab, error)
	GetAllocateOrderDataFromHbase(ctx context.Context, maskProductId int, day time.Time, channelSize int) (chan *gohbase.Result, error)
	GetAllocateOrderCount(ctx context.Context, maskProductId int, day time.Time) (int64, error)
	GetAllocateOrderDataFromHbaseByKey(ctx context.Context, maskProductId uint64, startTime, endTime int64, channelSize int, needSleep bool, useHardRate bool) (chan *gohbase.Result, error)
	GetAllocateOrderTotalCount(ctx context.Context, maskProductId uint64, startTime, endTime int64, channelSize int) (int64, error)
	PutAllocateOrderIntoHbase(ctx context.Context, rowKey string, value map[string]map[string][]byte) *srerr.Error
	GetAllocateOrderFromHbase(ctx context.Context, rowKey string) (*AllocationHbaseEntity, *srerr.Error)
}

type AllocateOrderDataRepoImpl struct {
	addrRepo address.AddrRepo
}

func NewAllocateOrderDataRepoImpl(AddrRepo address.AddrRepo) *AllocateOrderDataRepoImpl {
	return &AllocateOrderDataRepoImpl{
		addrRepo: AddrRepo,
	}
}

func (a *AllocateOrderDataRepoImpl) CreateAllocateOrderData(ctx context.Context, order *model.AllocateOrderDataTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, order)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	now := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	order.MTime = now
	order.CTime = now

	if err := db.Table(model.TableNameByTimeStamp(timeutil.TransferTimeStampToTime(int64(order.OrderTime)))).Create(order).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderData(ctx context.Context, maskProductId int, day time.Time) (<-chan *model.AllocateOrderDataTab, <-chan *gohbase.Result, bool, error) {
	conf := configutil.GetMaskingForecastConf(ctx)
	var (
		readLimit          = conf.ReadLimit
		channelSize        = conf.ChannelSize
		dividingTimeString = conf.DividingTime
	)
	if readLimit == 0 {
		readLimit = defaultReadLimit
	}
	if channelSize == 0 {
		channelSize = defaultChannelSize
	}

	//没设置分界线时间，以旧逻辑进行返回
	if dividingTimeString == "" {
		tidbCh, err := a.GetAllocateOrderDataFromTidb(ctx, maskProductId, day, readLimit, channelSize)
		return tidbCh, nil, false, err
	}
	//按天进行判断是否要用hbase的数据
	orderTime := timeutil.GetStartTimeByTime(day)
	dividingTime := timeutil.GetStartTimeByString(dividingTimeString)
	if dividingTime <= orderTime { //分界线后用hbase
		hbChan, gErr := a.GetAllocateOrderDataFromHbase(ctx, maskProductId, day, channelSize)
		if gErr != nil {
			logger.CtxLogErrorf(ctx, "GetAllocateOrderData| get channel from hbase err:%v", gErr)
			return nil, nil, true, gErr
		}
		return nil, hbChan, true, nil
	}
	tidbCh, err := a.GetAllocateOrderDataFromTidb(ctx, maskProductId, day, readLimit, channelSize)
	return tidbCh, nil, false, err

	//a.convertHbChannel(ctx, hbChan, channelSize), true, nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderDataFromTidb(ctx context.Context, maskProductId int, day time.Time, readLimit, channelSize int) (chan *model.AllocateOrderDataTab, error) {
	db, err := dbutil.SlaveDB(ctx, model.AllocateOrderDataHook)
	if err != nil {
		return nil, err
	}

	allocateOrderDataTableName := model.TableNameByTimeStamp(day)
	ch := make(chan *model.AllocateOrderDataTab, channelSize)
	go func() {
		defer close(ch)
		//recover goroutine panic
		defer func() {
			if err := recover(); err != nil {
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		var totalCount int64
		db.Table(allocateOrderDataTableName).Where("mask_product_id=?", maskProductId).Count(&totalCount)
		logger.CtxLogInfof(ctx, "GetAllocateOrderData|maskProductId=%v, allocateOrderDataTableName=%v, totalCount=%v", maskProductId, allocateOrderDataTableName, totalCount)
		for offset := 0; offset < int(totalCount); offset = offset + readLimit {
			logger.CtxLogInfof(ctx, "Allocate Forecast--load data process....allocateOrderDataTableName=%v,,totalCount=%v,,offset=%v", allocateOrderDataTableName, totalCount, offset)
			orderDataList := make([]*model.AllocateOrderDataTab, 0)
			if err := db.Table(allocateOrderDataTableName).Offset(offset).Limit(readLimit).
				Where("mask_product_id=?", maskProductId).Find(&orderDataList).GetError(); err != nil {
				logger.CtxLogErrorf(ctx, "Allocate Forecast--get allocate order data error|err=%v", err)
				return
			}
			for _, orderData := range orderDataList {
				ch <- orderData
			}
		}
	}()

	return ch, nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderDataFromHbase(ctx context.Context, maskProductId int, day time.Time, channelSize int) (chan *gohbase.Result, error) {
	hbHelper := masking_forecast_hbase.NewHBHelper()
	mainHbHelper := masking_forecast_hbase.NewMainHBHelper()

	//allocateOrderCh 是用来存储hbase results
	allocateOrderCh := make(chan *gohbase.Result, channelSize)
	//开协程获取数据
	go func(allocateOrderCh chan *gohbase.Result) {
		//close channel
		defer close(allocateOrderCh)
		//recover goroutine panic
		defer func() {
			if err := recover(); err != nil {
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		hbConf := configutil.GetDataHbaseConfig(ctx)
		mainHbConf := configutil.GetMainHbaseConfig(ctx)
		mfConf := configutil.GetMaskingForecastConf(ctx)
		var (
			tableName string
			saltSize  int
			batchSize uint32
			sleepTime int
			err       error
		)
		if mainHbConf.UseMain {
			tableName = mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = mainHbConf.SaltSize
			batchSize = mainHbConf.BatchSize
		} else {
			tableName = hbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = hbConf.SaltSize
			batchSize = hbConf.BatchSize
		}
		sleepTime = mfConf.SleepTime
		if saltSize == 0 {
			saltSize = defaultSaltSize
		}
		//按天在hbase检索数据
		//salt是按request time整除3位数取余得到，因此遍历0～999相当于全表检索
		for i := 0; i < saltSize; i++ {
			//定义检索的region，开始key，结束key
			salt := i
			startRowKey := GenStartRowKeyPrefix(salt, maskProductId, day)
			endRowKey := GenEndRowKeyPrefix(salt, maskProductId, day)

			logger.CtxLogInfof(ctx, "GetAllocateOrderDataFromHbase| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

			if mainHbConf.UseMain {
				err = mainHbHelper.ScanWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, nil)
			} else {
				err = hbHelper.ScanByIndexWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, nil)
			}
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetAllocateOrderDataFromHbase|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
				continue
			}

			time.Sleep(time.Second * time.Duration(sleepTime))
		}
	}(allocateOrderCh)

	return allocateOrderCh, nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderCount(ctx context.Context, maskProductId int, day time.Time) (int64, error) {
	conf := configutil.GetMaskingForecastConf(ctx)
	dividingTimeString := conf.DividingTime
	//没设置分界线时间，以旧逻辑进行返回
	if dividingTimeString == "" {
		count, err := GetAllocateOrderCountFromTidb(ctx, maskProductId, day)
		if err != nil {
			return 0, err
		}
		return count, nil
	}
	//按天进行判断是否要用hbase的数据
	orderTime := timeutil.GetStartTimeByTime(day)
	dividingTime := timeutil.GetStartTimeByString(dividingTimeString)
	if dividingTime <= orderTime { //分界线后用hbase
		count, gErr := GetAllocateOrderCountFromHbase(ctx, maskProductId, day)
		if gErr != nil {
			return 0, gErr
		}
		return count, nil
	}
	count, err := GetAllocateOrderCountFromTidb(ctx, maskProductId, day)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func GetAllocateOrderCountFromTidb(ctx context.Context, maskProductId int, day time.Time) (int64, error) {
	db, err := dbutil.SlaveDB(ctx, model.AllocateOrderDataHook)
	if err != nil {
		return 0, err
	}
	allocateOrderDataTableName := model.TableNameByTimeStamp(day)
	var totalCount int64
	db.Table(allocateOrderDataTableName).Where("mask_product_id=?", maskProductId).Count(&totalCount)
	return totalCount, nil
}

func GetAllocateOrderCountFromHbase(ctx context.Context, maskProductId int, day time.Time) (int64, error) {
	hbHelper := masking_forecast_hbase.NewHBHelper()
	//获取table name
	hbConf := configutil.GetDataHbaseConfig(ctx)
	tableName := hbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]

	defer func() {
		if err := recover(); err != nil {
			var buf [4096]byte
			n := runtime.Stack(buf[:], false)
			errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
			logger.CtxLogErrorf(ctx, errMsg)
		}
	}()

	var totalCount int64
	saltSize := hbConf.SaltSize
	if saltSize == 0 {
		saltSize = defaultSaltSize
	}
	//按天在hbase检索数据
	//salt是按request time整除5位数取余得到，因此遍历0～999相当于全表检索
	for i := 0; i < saltSize; i++ {
		//定义检索的region，开始key，结束key
		salt := i
		startRowKey := GenStartRowKeyPrefix(salt, maskProductId, day)
		endRowKey := GenEndRowKeyPrefix(salt, maskProductId, day)

		logger.CtxLogInfof(ctx, "GetAllocateOrderCountFromHbase| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

		resultList, _, err := hbHelper.ScanByIndex(ctx, tableName, startRowKey, endRowKey, 1000)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetAllocateOrderCountFromHbase|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
			continue
		}
		totalCount += int64(len(resultList))
	}

	return totalCount, nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderDataFromHbaseByKey(ctx context.Context, maskProductId uint64, startTime, endTime int64, channelSize int, needSleep bool, useHardRate bool) (chan *gohbase.Result, error) {
	hbHelper := masking_forecast_hbase.NewHBHelper()
	mainHbHelper := masking_forecast_hbase.NewMainHBHelper()

	//allocateOrderCh 是用来存储hbase results
	allocateOrderCh := make(chan *gohbase.Result, channelSize)
	//开协程获取数据
	go func(allocateOrderCh chan *gohbase.Result) {
		//close channel
		defer close(allocateOrderCh)
		//recover goroutine panic
		defer func() {
			if err := recover(); err != nil {
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		hbConf := configutil.GetDataHbaseConfig(ctx)
		mainHbConf := configutil.GetMainHbaseConfig(ctx)
		mfConf := configutil.GetMaskingForecastConf(ctx)
		var (
			tableName   string
			saltSize    int
			batchSize   uint32
			sleepTime   int
			err         error
			rateLimiter *hbaseutil.RateLimiter
		)
		//mfConf.RateLimit, mfConf.RateBurst, mfConf.HardRateLimit
		//SSCSMR-1480:初始化限速器
		if !needSleep {
			//SSCSMR-1480:可配置，其中burst配置成跟外层的workers一样的参数
			rateLimiter = &hbaseutil.RateLimiter{
				Limit: mfConf.RateLimit,
				Burst: mfConf.RateBurst,
			}
			if useHardRate {
				rateLimiter.Limit = mfConf.HardRateLimit
			}
		}
		if mainHbConf.UseMain {
			tableName = mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = mainHbConf.SaltSize
			batchSize = mainHbConf.BatchSize
		} else {
			tableName = hbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = hbConf.SaltSize
			batchSize = hbConf.BatchSize
		}
		sleepTime = mfConf.SleepTime
		if saltSize == 0 {
			saltSize = defaultSaltSize
		}
		//按天在hbase检索数据
		//salt是按request time整除3位数取余得到，因此遍历0～999相当于全表检索
		for i := 0; i < saltSize; i++ {
			//定义检索的region，开始key，结束key
			salt := i
			startRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, startTime)
			endRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, endTime)

			logger.CtxLogInfof(ctx, "GetAllocateOrderDataFromHbaseByKey| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

			if mainHbConf.UseMain {
				err = mainHbHelper.ScanWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, rateLimiter)
			} else {
				err = hbHelper.ScanByIndexWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, rateLimiter)
			}
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetAllocateOrderDataFromHbaseByKey|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
				continue
			}
			//SSCSMR-1480:如果是开启了硬性校验或cheapest shipping fee，则分批检索时，需要在检索间隔中sleep，避免消费者疯狂消费
			if needSleep {
				time.Sleep(time.Millisecond * sleepSize * time.Duration(sleepTime))
			}
		}
	}(allocateOrderCh)

	return allocateOrderCh, nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderTotalCount(ctx context.Context, maskProductId uint64, startTime, endTime int64, channelSize int) (int64, error) {
	hbHelper := masking_forecast_hbase.NewHBHelper()
	mainHbHelper := masking_forecast_hbase.NewMainHBHelper()

	//allocateOrderCh 是用来存储hbase results
	allocateOrderCh := make(chan *gohbase.Result, channelSize)
	//开协程获取数据
	go func(allocateOrderCh chan *gohbase.Result) {
		//close channel
		defer close(allocateOrderCh)
		//recover goroutine panic
		defer func() {
			if err := recover(); err != nil {
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("GetAllocateOrderTotalCount|[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		var (
			tableName string
			saltSize  int
			batchSize uint32
			err       error
		)
		//获取table name
		hbConf := configutil.GetDataHbaseConfig(ctx)
		mainHbConf := configutil.GetMainHbaseConfig(ctx)
		if mainHbConf.UseMain {
			tableName = mainHbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = mainHbConf.SaltSize
			batchSize = mainHbConf.BatchSize
		} else {
			tableName = hbConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
			saltSize = hbConf.SaltSize
			batchSize = hbConf.BatchSize
		}

		if saltSize == 0 {
			saltSize = defaultSaltSize
		}
		//按天在hbase检索数据
		//salt是按request time整除3位数取余得到，因此遍历0～999相当于全表检索
		for i := 0; i < saltSize; i++ {
			//定义检索的region，开始key，结束key
			salt := i
			startRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, startTime)
			endRowKey := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, endTime)

			logger.CtxLogInfof(ctx, "GetAllocateOrderTotalCount| salt:%v, start row key:%v, end row key:%v", salt, startRowKey, endRowKey)

			if mainHbConf.UseMain {
				err = mainHbHelper.ScanWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, nil)
			} else {
				err = hbHelper.ScanByIndexWithChannel(ctx, tableName, startRowKey, endRowKey, batchSize, allocateOrderCh, nil)
			}

			if err != nil {
				logger.CtxLogErrorf(ctx, "GetAllocateOrderTotalCount|table name:%v, start row key:%v, end row key:%v get data from hbase err:%v", tableName, startRowKey, endRowKey, err)
				continue
			}
		}
	}(allocateOrderCh)

	var totalCount int64
	for range allocateOrderCh {
		totalCount += 1
	}
	return totalCount, nil
}

func (a *AllocateOrderDataRepoImpl) PutAllocateOrderIntoHbase(ctx context.Context, rowKey string, value map[string]map[string][]byte) *srerr.Error {
	//1.获取table name
	mainConf := configutil.GetMainHbaseConfig(ctx)
	tableName := mainConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
	//2.获取main集群连接
	service := masking_forecast_hbase.NewMainHBHelper()
	//3.写入
	if err := service.CreateNewRowIntoMain(ctx, tableName, rowKey, value); err != nil {
		logger.CtxLogErrorf(ctx, "PutAllocateOrderIntoHbase|create new row into main err:%v", err)
		return srerr.With(srerr.PutHbaseError, nil, err)
	}

	return nil
}

func (a *AllocateOrderDataRepoImpl) GetAllocateOrderFromHbase(ctx context.Context, rowKey string) (*AllocationHbaseEntity, *srerr.Error) {
	// 1.获取table name
	mainConf := configutil.GetMainHbaseConfig(ctx)
	tableName := mainConf.TableNameMap[masking_forecast_hbase.MASKING_FORECASTING]
	// 2.获取main集群连接
	service := masking_forecast_hbase.NewMainHBHelper()
	// 3.查询
	result, err := service.GetByRowKey(ctx, tableName, rowKey)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get row from hbase  err:%v", err)
		return nil, srerr.With(srerr.GetHbaseError, rowKey, err)
	}

	decodeBytes, err := zip.ZSTDDecompress(result)
	if err != nil {
		logger.CtxLogErrorf(ctx, "decode data from hbase fail |key:%v, decode value err:%v", rowKey, err)
		return nil, srerr.With(srerr.GetHbaseError, rowKey, err)
	}

	var resultEntity AllocationHbaseEntity
	if err := jsoniter.Unmarshal(decodeBytes, &resultEntity); err != nil {
		logger.CtxLogErrorf(ctx, "unmarshall data to entity fail | data:%s, unmarshall err:%v", string(decodeBytes), err)
		return nil, srerr.With(srerr.GetHbaseError, decodeBytes, err)
	}

	return &resultEntity, nil
}

func GenStartRowKeyPrefix(salt, maskProductId int, day time.Time) string {
	//salt = intDiv(request_time_mill, 1000) % 1000  ps:intDiv为整除, 此处为去掉request_time_mill后5位
	rowKeyPrefix := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, timeutil.GetStartTime(day))
	return rowKeyPrefix
}

func GenEndRowKeyPrefix(salt, maskProductId int, day time.Time) string {
	//salt = intDiv(request_time_mill, 1000) % 1000  ps:intDiv为整除, 此处为去掉request_time_mill后5位
	rowKeyPrefix := fmt.Sprintf("%03d_%v_%v", salt, maskProductId, timeutil.GetStartTime(day.Add(time.Hour*24)))
	return rowKeyPrefix
}
