package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

const MaskRuleVolumeTabName = "mask_product_rule_volume_tab"

var (
	MaskRuleVolumeTabHook = &MaskRuleVolumeTab{}
)

type MaskRuleVolumeTab struct {
	ID                      uint64                        `gorm:"column:id" json:"id"`
	MaskProductID           int                           `gorm:"column:mask_product_id" json:"mask_product_id"`
	DefaultVolumeLimitByte  []byte                        `gorm:"column:default_volume_limit" json:"default_volume_limit_byte"`
	DefaultVolumeLimit      []*MaskDefaultVolumeLimitItem `gorm:"-" json:"default_volume_limit"`
	RuleStatus              MaskRuleVolumeStatus          `gorm:"column:rule_status" json:"rule_status"`
	RuleType                MaskLocVolumeType             `gorm:"column:rule_type" json:"rule_type"`
	RouteDefinitionFile     string                        `gorm:"column:route_definition_file" json:"route_definition_file"`
	RouteLimitFile          string                        `gorm:"column:route_limit_file" json:"route_limit_file"`
	ZoneDefinitionFile      string                        `gorm:"column:zone_definition_file" json:"zone_definition_file"`
	ZoneLimitFile           string                        `gorm:"column:zone_limit_file" json:"zone_limit_file"`
	OperateBy               string                        `gorm:"column:operate_by" json:"operate_by"`
	EffectiveStartTime      uint32                        `gorm:"column:effective_start_time" json:"effective_start_time"`
	SetVolumeBlankAsMinimum bool                          `gorm:"column:set_volume_blank_as_minimum" json:"set_volume_blank_as_minimum"`
	CTime                   uint32                        `gorm:"column:ctime" json:"ctime"`
	MTime                   uint32                        `gorm:"column:mtime" json:"mtime"`
	ApprovalTime            int64                         `gorm:"column:approval_time" json:"approval_time"`
	MaskProductName         string                        `gorm:"-" json:"mask_product_name"`
	RuleMode                int32                         `gorm:"column:rule_mode" json:"rule_mode"`
	ForecastTaskId          int64                         `json:"forecast_task_id"` // 预测任务id
	ZoneVolumes             []*MaskZoneVolumeTab          `gorm:"-" json:"-"`       // load in cache
	RouteVolumes            []*MaskRouteVolumeTab         `gorm:"-" json:"-"`       // load in cache
	AllocationMethod        int                           `gorm:"column:allocation_method" json:"allocation_method"`
	MaskCombinationMode     bool                          `gorm:"column:mask_combination_mode" json:"mask_combination_mode"`
	ShareVolume             bool                          `gorm:"column:share_volume" json:"share_volume"`
	GroupInfoByte           []byte                        `gorm:"column:group_info" json:"-"`
	GroupInfo               MaskRuleGroupInfo             `gorm:"-" json:"group_info"`
}

type MaskDefaultVolumeLimitItem struct {
	MaskProductID        int64             `json:"mask_product_id"`
	ID                   int64             `json:"id"` // fulfillment product id
	Name                 string            `json:"name"`
	MinVolume            int32             `json:"min_volume"`
	MaxCapacity          int32             `json:"max_capacity"`
	MaxCodCapacity       int32             `json:"max_cod_capacity"`
	MaxBulkyCapacity     int32             `json:"max_bulky_capacity"`
	MaxHighValueCapacity int32             `json:"max_high_value_capacity"`
	MaxDgCapacity        int32             `json:"max_dg_capacity"`
	IsHardCap            bool              `json:"is_hard_cap"`
	IsCodHardCap         bool              `json:"is_cod_hard_cap"`
	IsBulkyHardCap       bool              `json:"is_bulky_hard_cap"`
	IsHighValueHardCap   bool              `json:"is_high_value_hard_cap"`
	IsDgHardCap          bool              `json:"is_dg_hard_cap"`
	LimitType            MaskRuleLimitType `json:"limit_type"`
	GroupCode            string            `json:"group_code"`
}

// MaskRuleGroupInfo 组合信息
type MaskRuleGroupInfo struct {
	MaskProductInfos             []MaskRuleMaskProductInfo     `json:"mask_product_infos"`
	FulfillmentProductGroupInfos []FulfillmentProductGroupInfo `json:"fulfillment_product_group_infos"`
}

// FulfillmentProductGroupInfo 开了了Share Volume模式后，多个F Product的Group信息，BE保留可扩展性可配多个，当前FE和业务只能配一个Group
type FulfillmentProductGroupInfo struct {
	GroupCode               string                           `json:"group_code"`
	FulfillmentProductInfos []MaskRuleFulfillmentProductInfo `json:"fulfillment_product_infos"`
}

// MaskRuleMaskProductInfo 开了Mask Combination模式后，存放改Rule Volume对应的多个M Product的信息，每个M Product的Allocation Method可不一样
type MaskRuleMaskProductInfo struct {
	MaskProductID    int `json:"mask_product_id"`
	AllocationMethod int `json:"allocation_method"`
}

type MaskRuleFulfillmentProductInfo struct {
	MaskProductID        int `json:"mask_product_id"`
	FulfillmentProductID int `json:"fulfillment_product_id"`
}

func (r MaskRuleVolumeTab) TableName() string {
	return MaskRuleVolumeTabName
}

func (r MaskRuleVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (r MaskRuleVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (r MaskRuleVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            r.ID,
		ModelName:     r.TableName(),
		MaskProductId: uint64(r.MaskProductID),
		RuleVolumeId:  r.ID,
	}
}

func (r *MaskRuleVolumeTab) unmarshalDefaultVolumeLimit() error {
	return jsoniter.Unmarshal(r.DefaultVolumeLimitByte, &r.DefaultVolumeLimit)
}

func (r *MaskRuleVolumeTab) marshalDefaultVolumeLimit() error {
	data, err := jsoniter.Marshal(r.DefaultVolumeLimit)
	if err != nil {
		return err
	}
	r.DefaultVolumeLimitByte = data

	return nil
}

func (r *MaskRuleVolumeTab) UnmarshalGroupInfo() error {
	if len(r.GroupInfoByte) == 0 {
		return nil
	}

	return jsoniter.Unmarshal(r.GroupInfoByte, &r.GroupInfo)
}

func (r *MaskRuleVolumeTab) marshalGroupInfo() error {
	data, err := jsoniter.Marshal(r.GroupInfo)
	if err != nil {
		return err
	}
	r.GroupInfoByte = data

	return nil
}

func (r *MaskRuleVolumeTab) Unmarshal() error {
	if err := r.unmarshalDefaultVolumeLimit(); err != nil {
		return err
	}
	if err := r.UnmarshalGroupInfo(); err != nil {
		return err
	}

	return nil
}

func (r *MaskRuleVolumeTab) Marshal() error {
	if err := r.marshalDefaultVolumeLimit(); err != nil {
		return err
	}
	if err := r.marshalGroupInfo(); err != nil {
		return err
	}

	return nil
}

func (r *MaskRuleVolumeTab) GetGroupMap() map[string][]int64 {
	groupMap := make(map[string][]int64)
	for _, fulfillmentGroupInfo := range r.GroupInfo.FulfillmentProductGroupInfos {
		for _, f := range fulfillmentGroupInfo.FulfillmentProductInfos {
			groupMap[fulfillmentGroupInfo.GroupCode] = append(groupMap[fulfillmentGroupInfo.GroupCode], int64(f.FulfillmentProductID))
		}
	}

	return groupMap
}

func (r *MaskRuleVolumeTab) GetFulfillmentProductToGroupMap() map[int64]string {
	productGroupCodeMapping := make(map[int64]string)
	for _, groupInfo := range r.GroupInfo.FulfillmentProductGroupInfos {
		for _, productInfo := range groupInfo.FulfillmentProductInfos {
			productGroupCodeMapping[int64(productInfo.FulfillmentProductID)] = groupInfo.GroupCode
		}
	}

	return productGroupCodeMapping
}

func (r *MaskRuleVolumeTab) GetGroupFulfillmentProductIDs(maskProductID int64, groupCode string) []int64 {
	var fulfillmentProductIDs []int64
	for _, groupInfo := range r.GroupInfo.FulfillmentProductGroupInfos {
		if groupInfo.GroupCode != groupCode {
			continue
		}
		for _, productInfo := range groupInfo.FulfillmentProductInfos {
			if int64(productInfo.MaskProductID) == maskProductID {
				fulfillmentProductIDs = append(fulfillmentProductIDs, int64(productInfo.FulfillmentProductID))
			}
		}
	}

	return fulfillmentProductIDs
}

func (r *MaskRuleVolumeTab) IsEditable(ctx context.Context) bool {
	if r.RuleStatus == MaskRuleVolumeStatusUncompleted || r.RuleStatus == MaskRuleVolumeStatusDraft || r.RuleStatus == MaskRuleVolumeStatusQueuing {
		return true
	}
	if r.RuleStatus == MaskRuleVolumeStatusActive && r.EffectiveStartTime > uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		return true
	}
	return false
}

func (r *MaskRuleVolumeTab) ConvertToChangeData() string {
	return fmt.Sprintf("%v-%v", r.MaskProductID, r.MaskProductName)
}

func (r *MaskRuleVolumeTab) GetMaskProducts() []int {
	if !r.MaskCombinationMode {
		return []int{r.MaskProductID}
	}

	ret := make([]int, 0, len(r.GroupInfo.MaskProductInfos))
	for _, maskProductInfo := range r.GroupInfo.MaskProductInfos {
		ret = append(ret, maskProductInfo.MaskProductID)
	}

	return ret
}
