package rulevolume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestMaskRuleVolumeRepoImpl_BatchCreateZoneVolumes(t *testing.T) {

	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	dbutil.Init()

	r := &MaskRuleVolumeRepoImpl{
		LpsApi: nil,
	}
	arr := make([]*MaskZoneVolumeTab, 0)
	arr = append(arr, &MaskZoneVolumeTab{MaskProductID: 123})
	arr = append(arr, &MaskZoneVolumeTab{MaskProductID: 456})
	arr = append(arr, &MaskZoneVolumeTab{MaskProductID: 789})
	err := r.BatchCreateZoneVolumes(context.TODO(), arr)

	if err != nil {
		panic(err)
	}

}
