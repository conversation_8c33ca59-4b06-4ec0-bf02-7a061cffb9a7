package rulevolume

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	MaskZoneVolumeTabName         = "mask_product_zone_volume_tab"
	ForecastMaskZoneVolumeTabName = "allocate_forecast_zone_volume_tab"
)

var (
	MaskZoneVolumeTabHook         = &MaskZoneVolumeTab{}
	ForecastMaskZoneVolumeTabHook = &ForecastMaskZoneVolumeTab{}
)

type MaskZoneVolumeTab struct {
	ID                              int    `gorm:"column:id" json:"id"`
	RuleVolumeID                    uint64 `gorm:"column:rule_volume_id" json:"rule_volume_id"`
	MaskProductID                   int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID              int    `gorm:"column:component_product_id" json:"component_product_id"`
	GroupCode                       string `gorm:"column:group_code" json:"group_code"`
	ZoneCode                        string `gorm:"column:zone_code" json:"zone_code"`
	DistrictID                      int    `gorm:"column:district_id" json:"district_id"`
	Postcode                        string `gorm:"column:postcode" json:"postcode"`
	OriginMaxCapacity               int32  `gorm:"column:origin_max_capacity" json:"origin_max_capacity"`
	DestinationMaxCapacity          int32  `gorm:"column:destination_max_capacity" json:"destination_max_capacity"`
	DestinationMaxCodCapacity       int32  `gorm:"column:destination_max_cod_capacity" json:"destination_max_cod_capacity"`
	DestinationMaxBulkyCapacity     int32  `gorm:"column:destination_max_bulky_capacity" json:"destination_max_bulky_capacity"`
	DestinationMaxHighValueCapacity int32  `gorm:"column:destination_max_high_value_capacity" json:"destination_max_high_value_capacity"`
	DestinationMaxDgCapacity        int32  `gorm:"column:destination_max_dg_capacity" json:"destination_max_dg_capacity"`
	OriginMinVolume                 int32  `gorm:"column:origin_min_volume" json:"origin_min_volume"`
	DestinationMinVolume            int32  `gorm:"column:destination_min_volume" json:"destination_min_volume"`
	IsHardCap                       bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	IsCodHardCap                    bool   `gorm:"column:is_cod_hard_cap" json:"is_cod_hard_cap"`
	IsBulkyHardCap                  bool   `gorm:"column:is_bulky_hard_cap" json:"is_bulky_hard_cap"`
	IsHighValueHardCap              bool   `gorm:"column:is_high_value_hard_cap" json:"is_high_value_hard_cap"`
	IsDgHardCap                     bool   `gorm:"column:is_dg_hard_cap" json:"is_dg_hard_cap"`
	CTime                           uint32 `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime                           uint32 `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (MaskZoneVolumeTab) TableName() string {
	return MaskZoneVolumeTabName
}

func (MaskZoneVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (MaskZoneVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (m MaskZoneVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            uint64(m.ID),
		ModelName:     m.TableName(),
		MaskProductId: uint64(m.MaskProductID),
		RuleVolumeId:  m.RuleVolumeID,
	}
}

type ForecastMaskZoneVolumeTab struct {
	ID                     int    `gorm:"column:id" json:"id"`
	ForecastTaskID         uint64 `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskProductID          int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID     int    `gorm:"column:component_product_id" json:"component_product_id"`
	ZoneCode               string `gorm:"column:zone_code" json:"zone_code"`
	DistrictID             int    `gorm:"column:district_id" json:"district_id"`
	Postcode               string `gorm:"column:postcode" json:"postcode"`
	OriginMaxCapacity      int32  `gorm:"column:origin_max_capacity" json:"origin_max_capacity"`
	DestinationMaxCapacity int32  `gorm:"column:destination_max_capacity" json:"destination_max_capacity"`
	OriginMinVolume        int32  `gorm:"column:origin_min_volume" json:"origin_min_volume"`
	DestinationMinVolume   int32  `gorm:"column:destination_min_volume" json:"destination_min_volume"`
	IsHardCap              bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	CTime                  uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                  uint32 `gorm:"column:mtime" json:"mtime"`
}

func (ForecastMaskZoneVolumeTab) TableName() string {
	return ForecastMaskZoneVolumeTabName
}

func (ForecastMaskZoneVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (ForecastMaskZoneVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (m ForecastMaskZoneVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(m.ID),
		ModelName:            m.TableName(),
		TaskId:               m.ForecastTaskID,
		MaskProductId:        uint64(m.MaskProductID),
		FulfillmentProductId: uint64(m.ComponentProductID),
	}
}
