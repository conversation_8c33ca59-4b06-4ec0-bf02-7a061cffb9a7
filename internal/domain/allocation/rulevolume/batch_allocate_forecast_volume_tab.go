package rulevolume

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	BAForecastCapacityTabName = "batch_allocate_forecast_volume_tab"
)

var (
	BatchAllocateForecastVolumeTabHook = &BatchAllocateForecastVolumeTab{}
)

// capacity -> zone route limit，是硬性约束
// target -> zone route target, 运力目标
type BatchAllocateForecastVolumeTab struct {
	ID                      uint64 `gorm:"column:id" json:"id"`
	MaskProductID           uint64 `gorm:"column:mask_product_id" json:"mask_product_id"`
	BatchAllocateForecastId uint64 `gorm:"column:batch_allocate_forecast_id" json:"batch_allocate_forecast_id"`
	VolumeScene             string `gorm:"column:volume_scene" json:"volume_scene"` //capacity; target
	ComponentProductID      uint64 `gorm:"column:component_product_id" json:"component_product_id"`
	VolumeType              int    `gorm:"column:volume_type" json:"volume_type"` //1.zone; 2.route
	ZoneCode                string `gorm:"column:zone_code" json:"zone_code"`
	RouteCode               string `gorm:"column:route_code" json:"route_code"`
	ZoneMinLocationId       int64  `gorm:"column:zone_min_location_id" json:"zone_min_location_id"`       //zone -- location内最低层级的location id -- destination
	ZonePostcode            string `gorm:"column:zone_postcode" json:"zone_postcode"`                     //postcode -- zone code对应的postcode
	RouteMinOriginLocId     int64  `gorm:"column:route_min_origin_loc_id" json:"route_min_origin_loc_id"` //route --origin location内最低层级的location id
	RouteOriginPostcode     string `gorm:"column:route_origin_postcode" json:"route_origin_postcode"`
	RouteMinDestLocId       int64  `gorm:"column:route_min_dest_loc_id" json:"route_min_dest_loc_id"` //route --dest location内最低层级的location id
	RouteDestPostcode       string `gorm:"column:route_dest_postcode" json:"route_dest_postcode"`
	MaxVolumeValue          int64  `gorm:"column:max_volume_value" json:"max_volume_value"` //最大运力约束 -- -1表示无最大约束限制，即99999999
	MinVolumeValue          int64  `gorm:"column:min_volume_value" json:"min_volume_value"` //最小运力需求 -- -1表示无最小需求限制，即0
	MaxCodVolumeValue       int64  `gorm:"column:max_cod_volume_value" json:"max_cod_volume_value"`
	MaxHighValueVolumeValue int64  `gorm:"column:max_high_value_volume_value" json:"max_high_value_volume_value"`
	MaxBulkyVolumeValue     int64  `gorm:"column:max_bulky_volume_value" json:"max_bulky_volume_value"`
	MaxDgVolumeValue        int64  `gorm:"column:max_dg_volume_value" json:"max_dg_volume_value"`
	CTime                   int64  `gorm:"column:ctime" json:"ctime"`
	MTime                   int64  `gorm:"column:mtime" json:"mtime"`
}

func (b *BatchAllocateForecastVolumeTab) TableName() string {
	return BAForecastCapacityTabName
}

func (b *BatchAllocateForecastVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (b *BatchAllocateForecastVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (b *BatchAllocateForecastVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            b.ID,
		ModelName:     b.TableName(),
		MaskProductId: b.MaskProductID,
	}
}
