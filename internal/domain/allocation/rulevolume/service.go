package rulevolume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"time"
)

const (
	dbInsertBatchSize = 2000
	dbDeleteBatchSize = 1000
)

type IMaskRuleVolumeRepo interface {
	GetRuleVolumeByEffectiveStartTime(ctx context.Context, maskProductID int64, effectiveTime int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error)
	GetRuleVolumesByCondition(ctx context.Context, condition map[string]interface{}) ([]*MaskRuleVolumeTab, *srerr.Error)
	GetActiveRuleVolumeByMaskProductID(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error)
	GetActiveRuleVolumes(ctx context.Context, mode rule_mode.RuleMode, allocationMethod int64) ([]*MaskRuleVolumeTab, *srerr.Error)
	UpdateRuleVolumeStatusToExpired(ctx context.Context, ids []uint64) *srerr.Error
	GetRuleVolumeByMaskProductId(ctx context.Context, maskProductID int64, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error)
	GetRuleVolumeByID(ctx context.Context, id uint64) (*MaskRuleVolumeTab, *srerr.Error)
	ListRuleVolume(ctx context.Context, id uint64, maskProductID int, ruleStatus *MaskRuleVolumeStatus, ruleType MaskLocVolumeType, offset, limit int, ruleMode int32, allocationMethod int64) ([]*MaskRuleVolumeTab, int, *srerr.Error)
	CreateRuleVolume(ctx context.Context, ruleVolume *MaskRuleVolumeTab) (*MaskRuleVolumeTab, *srerr.Error)
	UpdateRuleVolume(ctx context.Context, ruleVolume *MaskRuleVolumeTab) (*MaskRuleVolumeTab, *srerr.Error)
	DeleteRuleVolume(ctx context.Context, id uint64, maskProductID int) *srerr.Error
	DeleteRouteLimitsByRuleVolumeID(ctx context.Context, ruleVolumeID uint64) *srerr.Error
	DeleteZoneLimitsByRuleVolumeID(ctx context.Context, ruleVolumeID uint64) *srerr.Error
	BatchCreateZoneVolumes(ctx context.Context, zoneVolumes []*MaskZoneVolumeTab) *srerr.Error
	BatchCreateZoneVolumesWithTx(ctx context.Context, db scormv2.SQLCommon, zoneVolumes []*MaskZoneVolumeTab) *srerr.Error
	BatchCreateRouteVolumes(ctx context.Context, routeVolumes []*MaskRouteVolumeTab) *srerr.Error
	BatchCreateRouteVolumesTx(ctx context.Context, db scormv2.SQLCommon, routeVolumes []*MaskRouteVolumeTab) *srerr.Error
	DeleteRouteLimitsByRuleVolumeIDWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolumeID uint64) *srerr.Error
	DeleteZoneLimitsByRuleVolumeIDWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolumeID uint64) *srerr.Error
	UpdateRuleVolumeWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolume *MaskRuleVolumeTab) *srerr.Error
	ListZoneVolumesByRuleID(ctx context.Context, ruleVolumeID uint64) ([]*MaskZoneVolumeTab, *srerr.Error)
	ListRouteVolumesByRuleID(ctx context.Context, ruleVolumeID uint64) ([]*MaskRouteVolumeTab, *srerr.Error)

	IMaskForecastRuleVolumeRepo
}

type IMaskForecastRuleVolumeRepo interface {
	BatchCreateForecastZoneVolumes(ctx context.Context, zoneVolumes []*ForecastMaskZoneVolumeTab) *srerr.Error
	BatchCreateForecastRouteVolumes(ctx context.Context, routeVolumes []*ForecastMaskRouteVolumeTab) *srerr.Error
	DeleteForecastRouteLimitsByTaskId(ctx context.Context, forecastTaskId uint64) *srerr.Error
	DeleteForecastZoneLimitsByTaskId(ctx context.Context, forecastTaskId uint64) *srerr.Error
}

type MaskRuleVolumeRepoImpl struct {
	LpsApi lpsclient.LpsApi
}

func NewMaskRuleVolumeRepoImpl(LpsApi lpsclient.LpsApi) IMaskRuleVolumeRepo {
	return &MaskRuleVolumeRepoImpl{
		LpsApi: LpsApi,
	}
}

func (r *MaskRuleVolumeRepoImpl) GetActiveRuleVolumeByMaskProductID(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error) {
	return r.GetRuleVolumeByEffectiveStartTime(ctx, maskProductID, timeutil.GetCurrentUnixTimeStamp(ctx), rm, allocationMethod)
}

func (r *MaskRuleVolumeRepoImpl) GetRuleVolumeByEffectiveStartTime(ctx context.Context, maskProductID int64, effectiveTime int64, rm rule_mode.RuleMode, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, MaskRuleVolumeTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var (
		allocationMethods []int64
		volumeRule        *MaskRuleVolumeTab
	)
	if allocationMethod == allocation.BatchAllocate {
		allocationMethods = []int64{allocation.BatchAllocate}
	} else {
		allocationMethods = []int64{allocation.Zero, allocation.SingleAllocate}
	}

	var volumeRules, combinationVolumeRules []*MaskRuleVolumeTab
	// 为了简化查询语句，这里先查常规非Combination的
	if err := db.Table(MaskRuleVolumeTabHook.TableName()).Debug().
		Where("mask_product_id = ? AND rule_status = ? AND effective_start_time <= ? AND rule_mode = ? AND allocation_method in (?)",
			maskProductID, MaskRuleVolumeStatusActive, effectiveTime, int(rm), allocationMethods).
		Order("effective_start_time DESC").Find(&volumeRules).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	// 再查Combination的
	if err := db.Table(MaskRuleVolumeTabHook.TableName()).Debug().
		Where("mask_combination_mode = ? AND rule_status = ? AND effective_start_time <= ? AND rule_mode = ?",
			true, MaskRuleVolumeStatusActive, effectiveTime, int(rm)).
		Order("effective_start_time DESC").Find(&combinationVolumeRules).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	// 合并后按照生效时间做倒序
	volumeRules = append(volumeRules, combinationVolumeRules...)
	sort.Slice(volumeRules, func(i, j int) bool {
		// 为了取倒序，所以这里是[i] > [j]
		return volumeRules[i].EffectiveStartTime > volumeRules[j].EffectiveStartTime
	})

	for _, v := range volumeRules {
		if err := v.Unmarshal(); err != nil {
			logger.CtxLogErrorf(ctx, "unmarshal volume rule failed|id:%d,err=%v", v.ID, err)
			continue
		}

		// 配置F Product维度上
		if !v.MaskCombinationMode && v.MaskProductID == int(maskProductID) {
			volumeRule = v
			break
		}

		// Group模式下需要遍历，查找是否在Group Info里
		if isVolumeRuleGroupContainMaskProduct(v, int(maskProductID), int(allocationMethod)) {
			volumeRule = v
			break
		}
	}

	// 找不到的情况报错
	if volumeRule == nil {
		return nil, srerr.New(srerr.VolumeRuleNotFount, maskProductID, "active volume rule not found")
	}

	return volumeRule, nil
}

func isVolumeRuleGroupContainMaskProduct(volumeRule *MaskRuleVolumeTab, maskProductID int, allocationMethod int) bool {
	for _, maskProductInfo := range volumeRule.GroupInfo.MaskProductInfos {
		if maskProductInfo.MaskProductID == maskProductID && maskProductInfo.AllocationMethod == allocationMethod {
			return true
		}
	}

	return false
}

func (r *MaskRuleVolumeRepoImpl) GetRuleVolumesByCondition(ctx context.Context, condition map[string]interface{}) ([]*MaskRuleVolumeTab, *srerr.Error) {
	var tabs []*MaskRuleVolumeTab
	if err := dbutil.Select(ctx, MaskRuleVolumeTabHook, condition, &tabs); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tabs, nil
}

func (r *MaskRuleVolumeRepoImpl) GetRuleVolumeByMaskProductId(ctx context.Context, maskProductID int64, allocationMethod int64) (*MaskRuleVolumeTab, *srerr.Error) {
	data, err := r.GetRuleVolumeByEffectiveStartTime(ctx, maskProductID, timeutil.GetCurrentUnixTimeStamp(ctx), rule_mode.MplOrderRule, allocationMethod)
	if err != nil {
		return nil, err
	}

	return data, err
}

func (r *MaskRuleVolumeRepoImpl) GetRuleVolumeByID(ctx context.Context, id uint64) (*MaskRuleVolumeTab, *srerr.Error) {
	ruleVolume := &MaskRuleVolumeTab{}
	if err := dbutil.Take(ctx, MaskRuleVolumeTabHook, map[string]interface{}{"id = ?": id}, ruleVolume); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := ruleVolume.Unmarshal(); err != nil {
		logger.CtxLogErrorf(ctx, "unmarshal failed|err=%v", err)
	}
	convertStatus(ctx, ruleVolume)

	return ruleVolume, nil
}

func (r *MaskRuleVolumeRepoImpl) ListRuleVolume(ctx context.Context, id uint64, maskProductID int, ruleStatus *MaskRuleVolumeStatus, ruleType MaskLocVolumeType, offset, limit int, ruleMode int32, allocationMethod int64) ([]*MaskRuleVolumeTab, int, *srerr.Error) {
	var (
		condition = make(map[string]interface{}, 0)
	)
	if id > 0 {
		condition["id = ?"] = id
	}
	if maskProductID > 0 {
		// TODO 优化这里的查询逻辑，因为Combination模式下Mask Product信息在Group Info里
		q := fmt.Sprintf("(mask_product_id = ? OR group_info LIKE '%%\"mask_product_id\":%d,%%')", maskProductID)
		condition[q] = maskProductID
	}
	if ruleType > 0 {
		condition["rule_type = ?"] = ruleType
	}
	if ruleStatus != nil {
		ts := timeutil.GetCurrentUnixTimeStamp(ctx)
		if *ruleStatus == MaskRuleVolumeStatusQueuing {
			condition["rule_status = ?"] = MaskRuleVolumeStatusActive
			condition["effective_start_time > ?"] = ts
		} else if *ruleStatus == MaskRuleVolumeStatusActive {
			condition["rule_status = ?"] = MaskRuleVolumeStatusActive
			condition["effective_start_time <= ?"] = ts
		} else {
			condition["rule_status = ?"] = ruleStatus
		}
	}
	if ruleMode != int32(rule_mode.AllOrderRule) {
		condition["rule_mode = ?"] = ruleMode
	}
	if allocationMethod != allocation.Zero {
		condition["allocation_method = ?"] = allocationMethod
	}

	var total int64
	if err := dbutil.Count(ctx, MaskRuleVolumeTabHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "db rows counting", err)
	}
	var volumes []*MaskRuleVolumeTab
	if err := dbutil.Select(ctx, MaskRuleVolumeTabHook, condition, &volumes, dbutil.WithOrder("id DESC"), dbutil.WithPage(int64(offset), int64(limit))); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	for _, v := range volumes {
		if err := v.Unmarshal(); err != nil {
			logger.CtxLogInfof(ctx, "unmarshal failed|err=%v", err)
		}
		convertStatus(ctx, v)
	}

	return volumes, int(total), nil
}

func (r *MaskRuleVolumeRepoImpl) CreateRuleVolume(ctx context.Context, ruleVolume *MaskRuleVolumeTab) (*MaskRuleVolumeTab, *srerr.Error) {
	if !ruleVolume.MaskCombinationMode {
		maskProduct, err := r.LpsApi.GetProductDetail(ctx, ruleVolume.MaskProductID)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "could not find mask product id %d , err: %v", ruleVolume.MaskProductID, err)
		}
		ruleVolume.MaskProductName = maskProduct.SellerDisplayName
	}

	ts := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	ruleVolume.MTime, ruleVolume.CTime = ts, ts
	if err := ruleVolume.Marshal(); err != nil {
		return nil, srerr.With(srerr.JsonErr, nil, err)
	}

	if err := dbutil.Insert(ctx, ruleVolume, dbutil.ModelInfo{
		Id:            ruleVolume.ID,
		MaskProductId: uint64(ruleVolume.MaskProductID),
		RuleVolumeId:  ruleVolume.ID,
		ModelName:     ruleVolume.TableName(),
	}); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	//写入操作历史
	cErr := r.LpsApi.CreateHistory(ctx, ruleVolume, string(lpsclient.CreateType), "create a rule volume", ruleVolume.ID)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	convertStatus(ctx, ruleVolume)

	return ruleVolume, nil
}

func (r *MaskRuleVolumeRepoImpl) UpdateRuleVolume(ctx context.Context, ruleVolume *MaskRuleVolumeTab) (*MaskRuleVolumeTab, *srerr.Error) {
	if !ruleVolume.MaskCombinationMode {
		maskProduct, err := r.LpsApi.GetProductDetail(ctx, ruleVolume.MaskProductID)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "could not find mask product id %d , err: %v", ruleVolume.MaskProductID, err)
		}
		ruleVolume.MaskProductName = maskProduct.SellerDisplayName
	}

	ruleVolume.MTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := ruleVolume.Marshal(); err != nil {
		return nil, srerr.With(srerr.JsonErr, nil, err)
	}

	//风险点，新增的Rule
	if err := dbutil.SaveByObj(ctx, MaskRuleVolumeTabHook, nil, ruleVolume, []string{"rule_mode"}, dbutil.ModelInfo{
		Id:            ruleVolume.ID,
		ModelName:     ruleVolume.TableName(),
		MaskProductId: uint64(ruleVolume.MaskProductID),
		RuleVolumeId:  ruleVolume.ID,
	}); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	//写入操作记录
	cErr := r.LpsApi.CreateHistory(ctx, ruleVolume, string(lpsclient.UpdateType), "update a rule volume", ruleVolume.ID)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	convertStatus(ctx, ruleVolume)

	return ruleVolume, nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteRuleVolume(ctx context.Context, id uint64, maskProductID int) *srerr.Error {
	var maskProductName string
	if maskProductID != 0 {
		//用来写入操作记录
		maskProduct, err := r.LpsApi.GetProductDetail(ctx, maskProductID)
		if err != nil {
			return srerr.New(srerr.ParamErr, nil, "could not find mask product id %d , err: %v", maskProductID, err)
		}
		maskProductName = maskProduct.SellerDisplayName
	}

	volume := MaskRuleVolumeTab{}
	_ = dbutil.Select(ctx, MaskRuleVolumeTabHook, map[string]interface{}{"id = ?": id}, &volume)
	if err := dbutil.Delete(ctx, MaskRuleVolumeTabHook, map[string]interface{}{"id = ?": id}, dbutil.ModelInfo{
		Id:           id,
		RuleVolumeId: id,
		ModelName:    MaskRuleVolumeTabHook.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	//写入操作记录
	volume.MaskProductName = maskProductName
	volume.MaskProductID = maskProductID
	cErr := r.LpsApi.CreateHistory(ctx, volume, string(lpsclient.DeleteType), "delete a rule volume", id)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteRouteLimitsByRuleVolumeID(ctx context.Context, ruleVolumeID uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, MaskRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("rule_volume_id = ?", ruleVolumeID).Limit(dbDeleteBatchSize).Delete(MaskRouteVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, ruleVolumeID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteZoneLimitsByRuleVolumeID(ctx context.Context, ruleVolumeID uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, MaskZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("rule_volume_id = ?", ruleVolumeID).Limit(dbDeleteBatchSize).Delete(MaskZoneVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, ruleVolumeID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteRouteLimitsByRuleVolumeIDWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolumeID uint64) *srerr.Error {
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("rule_volume_id = ?", ruleVolumeID).Limit(dbDeleteBatchSize).Delete(MaskRouteVolumeTabHook)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "batch delete route volume failed %v", d.GetError())
			return srerr.With(srerr.DatabaseErr, ruleVolumeID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteZoneLimitsByRuleVolumeIDWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolumeID uint64) *srerr.Error {
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("rule_volume_id = ?", ruleVolumeID).Limit(dbDeleteBatchSize).Delete(MaskZoneVolumeTabHook)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "batch delete zone volume failed %v", d.GetError())
			return srerr.With(srerr.DatabaseErr, ruleVolumeID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}

	return nil
}
func convertStatus(ctx context.Context, r *MaskRuleVolumeTab) {
	if r.RuleStatus == MaskRuleVolumeStatusActive && r.EffectiveStartTime > uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		r.RuleStatus = MaskRuleVolumeStatusQueuing
	}
}

func (r *MaskRuleVolumeRepoImpl) GetActiveRuleVolumes(ctx context.Context, mode rule_mode.RuleMode, allocationMethod int64) ([]*MaskRuleVolumeTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, MaskRuleVolumeTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	var (
		volumes           []*MaskRuleVolumeTab
		allocationMethods []int64
	)
	if allocationMethod == allocation.BatchAllocate {
		allocationMethods = []int64{allocation.BatchAllocate}
	} else {
		//兼容存量数据allocation method = 0
		allocationMethods = []int64{allocation.Zero, allocation.SingleAllocate}
	}
	if err := db.Table(MaskRuleVolumeTabHook.TableName()).
		Where("rule_status = ? AND effective_start_time <= ? AND rule_mode = ? AND allocation_method in (?)", RuleStatusActive, timeutil.GetCurrentUnixTimeStamp(ctx), int(mode), allocationMethods).
		Order("effective_start_time DESC").Find(&volumes).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	for _, volume := range volumes {
		if err := volume.Unmarshal(); err != nil {
			return nil, srerr.With(srerr.JsonErr, "", err)
		}
	}

	return volumes, nil
}

func (r *MaskRuleVolumeRepoImpl) UpdateRuleVolumeStatusToExpired(ctx context.Context, ids []uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, MaskRuleVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err := db.Table(MaskRuleVolumeTabHook.TableName()).Where("rule_status = ? AND id in (?)", RuleStatusActive, ids).Update("rule_status", RuleStatusExpired).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, ids, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateZoneVolumes(ctx context.Context, zoneVolumes []*MaskZoneVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, MaskZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(MaskZoneVolumeTabHook.TableName()).CreateInBatches(zoneVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) ListZoneVolumesByRuleID(ctx context.Context, ruleVolumeID uint64) ([]*MaskZoneVolumeTab, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, MaskZoneVolumeTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var zoneVolumes []*MaskZoneVolumeTab
	if err := db.Table(MaskZoneVolumeTabHook.TableName()).Find(&zoneVolumes, "rule_volume_id = ?", ruleVolumeID).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return zoneVolumes, nil
}

func (r *MaskRuleVolumeRepoImpl) ListRouteVolumesByRuleID(ctx context.Context, ruleVolumeID uint64) ([]*MaskRouteVolumeTab, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, MaskRouteVolumeTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var routeVolumes []*MaskRouteVolumeTab
	if err := db.Table(MaskRouteVolumeTabHook.TableName()).Find(&routeVolumes, "rule_volume_id = ?", ruleVolumeID).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return routeVolumes, nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateZoneVolumesWithTx(ctx context.Context, db scormv2.SQLCommon, zoneVolumes []*MaskZoneVolumeTab) *srerr.Error {
	if err := db.Table(MaskZoneVolumeTabHook.TableName()).CreateInBatches(zoneVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateRouteVolumes(ctx context.Context, routeVolumes []*MaskRouteVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, MaskRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(MaskRouteVolumeTabHook.TableName()).CreateInBatches(routeVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateRouteVolumesTx(ctx context.Context, db scormv2.SQLCommon, routeVolumes []*MaskRouteVolumeTab) *srerr.Error {
	if err := db.Table(MaskRouteVolumeTabHook.TableName()).CreateInBatches(routeVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateForecastZoneVolumes(ctx context.Context, zoneVolumes []*ForecastMaskZoneVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastMaskZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(ForecastMaskZoneVolumeTabHook.TableName()).CreateInBatches(zoneVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) BatchCreateForecastRouteVolumes(ctx context.Context, routeVolumes []*ForecastMaskRouteVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastMaskRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(ForecastMaskRouteVolumeTabHook.TableName()).CreateInBatches(routeVolumes, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteForecastRouteLimitsByTaskId(ctx context.Context, forecastTaskId uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastMaskRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("forecast_task_id = ?", forecastTaskId).Limit(dbDeleteBatchSize).Delete(ForecastMaskRouteVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, forecastTaskId, d.GetError())
		}
		rowsAffected = d.RowsAffected()
		time.Sleep(time.Second)
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) DeleteForecastZoneLimitsByTaskId(ctx context.Context, forecastTaskId uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastMaskZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("forecast_task_id = ?", forecastTaskId).Limit(dbDeleteBatchSize).Delete(ForecastMaskZoneVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, forecastTaskId, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}

	return nil
}

func (r *MaskRuleVolumeRepoImpl) UpdateRuleVolumeWithTx(ctx context.Context, db scormv2.SQLCommon, ruleVolume *MaskRuleVolumeTab) *srerr.Error {
	if err := db.Table(MaskRuleVolumeTabHook.TableName()).Where("id = ?", ruleVolume.ID).Updates(ruleVolume).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "update volume rule with tx failed %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}
