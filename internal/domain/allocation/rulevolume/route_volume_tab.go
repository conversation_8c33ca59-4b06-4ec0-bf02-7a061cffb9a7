package rulevolume

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	MaskRouteVolumeTabName         = "mask_product_route_volume_tab"
	ForecastMaskRouteVolumeTabName = "allocate_forecast_route_volume_tab"
)

var (
	MaskRouteVolumeTabHook         = &MaskRouteVolumeTab{}
	ForecastMaskRouteVolumeTabHook = &ForecastMaskRouteVolumeTab{}
)

type MaskRouteVolumeTab struct {
	ID                    int    `gorm:"column:id" json:"id"`
	RuleVolumeID          uint64 `gorm:"column:rule_volume_id" json:"rule_volume_id"`
	MaskProductID         int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID    int    `gorm:"column:component_product_id" json:"component_product_id"`
	GroupCode             string `gorm:"column:group_code" json:"group_code"`
	RouteCode             string `gorm:"column:route_code" json:"route_code"`
	OriginDistrictID      int    `gorm:"column:origin_district_id" json:"origin_district_id"`
	OriginPostcode        string `gorm:"column:origin_postcode" json:"origin_postcode"`
	DestinationDistrictID int    `gorm:"column:destination_district_id" json:"destination_district_id"`
	DestinationPostcode   string `gorm:"column:destination_postcode" json:"destination_postcode"`
	MaxCapacity           int32  `gorm:"column:max_capacity" json:"max_capacity"`
	MaxCodCapacity        int32  `gorm:"column:max_cod_capacity" json:"max_cod_capacity"`
	MaxBulkyCapacity      int32  `gorm:"column:max_bulky_capacity" json:"max_bulky_capacity"`
	MaxHighValueCapacity  int32  `gorm:"column:max_high_value_capacity" json:"max_high_value_capacity"`
	MaxDgCapacity         int32  `gorm:"column:max_dg_capacity" json:"max_dg_capacity"`
	MinVolume             int32  `gorm:"column:min_volume" json:"min_volume"`
	IsHardCap             bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	IsCodHardCap          bool   `gorm:"column:is_cod_hard_cap" json:"is_cod_hard_cap"`
	IsBulkyHardCap        bool   `gorm:"column:is_bulky_hard_cap" json:"is_bulky_hard_cap"`
	IsHighValueHardCap    bool   `gorm:"column:is_high_value_hard_cap" json:"is_high_value_hard_cap"`
	IsDgHardCap           bool   `gorm:"column:is_dg_hard_cap" json:"is_dg_hard_cap"`
	CTime                 uint32 `gorm:"autoCreateTime;column:ctime" json:"ctime"`
	MTime                 uint32 `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
}

func (MaskRouteVolumeTab) TableName() string {
	return MaskRouteVolumeTabName
}

func (MaskRouteVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (MaskRouteVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (m MaskRouteVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:            uint64(m.ID),
		ModelName:     m.TableName(),
		MaskProductId: uint64(m.MaskProductID),
		RuleVolumeId:  m.RuleVolumeID,
	}
}

type ForecastMaskRouteVolumeTab struct {
	ID                    int    `gorm:"column:id" json:"id"`
	ForecastTaskID        uint64 `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskProductID         int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID    int    `gorm:"column:component_product_id" json:"component_product_id"`
	RouteCode             string `gorm:"column:route_code" json:"route_code"`
	OriginDistrictID      int    `gorm:"column:origin_district_id" json:"origin_district_id"`
	OriginPostcode        string `gorm:"column:origin_postcode" json:"origin_postcode"`
	DestinationDistrictID int    `gorm:"column:destination_district_id" json:"destination_district_id"`
	DestinationPostcode   string `gorm:"column:destination_postcode" json:"destination_postcode"`
	MaxCapacity           int32  `gorm:"column:max_capacity" json:"max_capacity"`
	MinVolume             int32  `gorm:"column:min_volume" json:"min_volume"`
	IsHardCap             bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	CTime                 uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                 uint32 `gorm:"column:mtime" json:"mtime"`
}

func (ForecastMaskRouteVolumeTab) TableName() string {
	return ForecastMaskRouteVolumeTabName
}

func (ForecastMaskRouteVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (ForecastMaskRouteVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (m ForecastMaskRouteVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(m.ID),
		ModelName:            m.TableName(),
		TaskId:               m.ForecastTaskID,
		MaskProductId:        uint64(m.MaskProductID),
		FulfillmentProductId: uint64(m.ComponentProductID),
	}
}
