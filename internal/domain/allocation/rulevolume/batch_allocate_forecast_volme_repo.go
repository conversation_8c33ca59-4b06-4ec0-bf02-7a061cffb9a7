package rulevolume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

const insertBatchSize = 2000

type BatchAllocateForecastVolumeRepo interface {
	BatchCreateVolumes(ctx context.Context, volumes []BatchAllocateForecastVolumeTab) *srerr.Error
	GetForecastVolume(ctx context.Context, condition map[string]interface{}) ([]BatchAllocateForecastVolumeTab, *srerr.Error)
	GetVolumesByForecastId(ctx context.Context, forecastId int64) ([]BatchAllocateForecastVolumeTab, *srerr.Error)
}

type BatchAllocateForecastVolumeImpl struct {
}

func NewBatchAllocateForecastVolumeImpl() *BatchAllocateForecastVolumeImpl {
	return &BatchAllocateForecastVolumeImpl{}
}

func (b *BatchAllocateForecastVolumeImpl) BatchCreateVolumes(ctx context.Context, volumes []BatchAllocateForecastVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, BatchAllocateForecastVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(BatchAllocateForecastVolumeTabHook.TableName()).CreateInBatches(volumes, insertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (b *BatchAllocateForecastVolumeImpl) GetForecastVolume(ctx context.Context, condition map[string]interface{}) ([]BatchAllocateForecastVolumeTab, *srerr.Error) {
	var tabs []BatchAllocateForecastVolumeTab
	if err := dbutil.Select(ctx, BatchAllocateForecastVolumeTabHook, condition, &tabs); err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastVolume|get batch allocate forecast volume by condition:%v, err:%v", condition, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return tabs, nil
}

func (b *BatchAllocateForecastVolumeImpl) GetVolumesByForecastId(ctx context.Context, forecastId int64) ([]BatchAllocateForecastVolumeTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, BatchAllocateForecastVolumeTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	volumes := []BatchAllocateForecastVolumeTab{}
	if err := db.Table(BatchAllocateForecastVolumeTabHook.TableName()).Where("batch_allocate_forecast_id = ?", forecastId).Find(&volumes).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "get batch forecast volume failed %v", err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return volumes, nil
}
