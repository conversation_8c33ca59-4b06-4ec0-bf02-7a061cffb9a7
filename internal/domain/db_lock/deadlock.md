```go

=====================================
2022-06-25 04:45:29 0x7f04f3c70700 INNODB MONITOR OUTPUT
=====================================
Per second averages calculated from the last 8 seconds
-----------------
BACKGROUND THREAD
-----------------
srv_master_thread loops: 15314327 srv_active, 0 srv_shutdown, 28320529 srv_idle
srv_master_thread log flush and writes: 43634856
----------
SEMAPHORES
----------
OS WAIT ARRAY INFO: reservation count 16201898
OS WAIT ARRAY INFO: signal count 17998470
RW-shared spins 0, rounds ********, OS waits 636910
RW-excl spins 0, rounds ********, OS waits 314614
RW-sx spins 887767, rounds 9552813, <PERSON> waits 137126
Spin rounds per wait: ********.00 RW-shared, ********.00 RW-excl, 10.76 RW-sx
------------------------
LATEST DETECTED DEADLOCK
------------------------
2022-06-25 04:31:53 0x7f04f70f2700
*** (1) TRANSACTION:
TRANSACTION ********, ACTIVE 0 sec updating or deleting
mysql tables in use 1, locked 1
LOCK WAIT 3 lock struct(s), heap size 1136, 2 row lock(s), undo log entries 1
MySQL thread id ********, OS thread handle ***************, query id ********** <mysql_domain> sz_sc_test2 updating
DELETE FROM `smart_routing_task_lock_tab` WHERE id = 25
*** (1) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 1770 page no 4 n bits 72 index idx_biz_groupid_unique of table `shopee_ssc_smartrouting_br_db`.`smart_routing_task_lock_tab` trx id ******** lock_mode X locks rec but not gap waiting
*** (2) TRANSACTION:
TRANSACTION ********, ACTIVE 0 sec starting index read
mysql tables in use 1, locked 1
3 lock struct(s), heap size 1136, 2 row lock(s)
MySQL thread id ********, OS thread handle ***************, query id ********** <mysql_domain> sz_sc_test2 updating
DELETE FROM `smart_routing_task_lock_tab` WHERE expire_time <  ********** and  biz_mode = 'zone-import'
*** (2) HOLDS THE LOCK(S):
RECORD LOCKS space id 1770 page no 4 n bits 72 index idx_biz_groupid_unique of table `shopee_ssc_smartrouting_br_db`.`smart_routing_task_lock_tab` trx id ******** lock_mode X
*** (2) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 1770 page no 3 n bits 80 index PRIMARY of table `shopee_ssc_smartrouting_br_db`.`smart_routing_task_lock_tab` trx id ******** lock_mode X locks rec but not gap waiting
*** WE ROLL BACK TRANSACTION (2)
------------
TRANSACTIONS
------------
Trx id counter ********
Purge done for trx's n:o < ******** undo n:o < 0 state: running but idle
History list length 16
LIST OF TRANSACTIONS FOR EACH SESSION:
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION ***************, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806590496, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806610944, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806605264, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806553008, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806558688, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806565504, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806491664, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806545056, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806633664, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806632528, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806631392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806629120, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806627984, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806626848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806625712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806621168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806620032, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806601856, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806531424, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806515520, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806563232, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806497344, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806501888, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806538240, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806451904, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806551872, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806547328, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806453040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806524608, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806535968, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806600720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806560960, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806554144, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806478032, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806499616, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806630256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806468944, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806588224, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806467808, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806479168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806599584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806635936, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806528016, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806521200, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806487120, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806458720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806597312, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806520064, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806517792, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806608672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806598448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806585952, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806618896, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806539376, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806526880, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806510976, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806537104, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806493936, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806534832, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806525744, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806616624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806481440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806530288, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806573456, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806562096, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806475760, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806496208, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806583680, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806498480, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806613216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806464400, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806466672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806447360, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806555280, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806480304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806476896, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806543920, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806522336, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806482576, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806607536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806572320, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806506432, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806512112, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806485984, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806455312, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806548464, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806504160, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806550736, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806503024, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806490528, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806463264, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806471216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806584816, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806564368, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806509840, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806495072, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806556416, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806500752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806532560, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806516656, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806589360, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806542784, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806474624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806459856, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806612080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806470080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806578000, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806624576, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806615488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806460992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806507568, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806622304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806609808, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806576864, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806483712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806462128, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806606400, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806457584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806595040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806514384, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806523472, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806456448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806518928, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806571184, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806472352, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806546192, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806473488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806529152, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806581408, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806505296, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806533696, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806454176, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806593904, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806591632, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806508704, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806450768, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806488256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806559824, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806574592, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806568912, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806513248, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806465536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806489392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806575728, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806579136, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806566640, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806549600, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806492800, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806614352, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806540512, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806623440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806592768, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806602992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806570048, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806567776, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806557552, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421136806448496, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
--------
FILE I/O
--------
I/O thread 0 state: waiting for i/o request (insert buffer thread)
I/O thread 1 state: waiting for i/o request (log thread)
I/O thread 2 state: waiting for i/o request (read thread)
I/O thread 3 state: waiting for i/o request (read thread)
I/O thread 4 state: waiting for i/o request (read thread)
I/O thread 5 state: waiting for i/o request (read thread)
I/O thread 6 state: waiting for i/o request (read thread)
I/O thread 7 state: waiting for i/o request (read thread)
I/O thread 8 state: waiting for i/o request (read thread)
I/O thread 9 state: waiting for i/o request (read thread)
I/O thread 10 state: waiting for i/o request (read thread)
I/O thread 11 state: waiting for i/o request (read thread)
I/O thread 12 state: waiting for i/o request (read thread)
I/O thread 13 state: waiting for i/o request (read thread)
I/O thread 14 state: waiting for i/o request (read thread)
I/O thread 15 state: waiting for i/o request (read thread)
I/O thread 16 state: waiting for i/o request (read thread)
I/O thread 17 state: waiting for i/o request (read thread)
I/O thread 18 state: waiting for i/o request (write thread)
I/O thread 19 state: waiting for i/o request (write thread)
I/O thread 20 state: waiting for i/o request (write thread)
I/O thread 21 state: waiting for i/o request (write thread)
I/O thread 22 state: waiting for i/o request (write thread)
I/O thread 23 state: waiting for i/o request (write thread)
I/O thread 24 state: waiting for i/o request (write thread)
I/O thread 25 state: waiting for i/o request (write thread)
I/O thread 26 state: waiting for i/o request (write thread)
I/O thread 27 state: waiting for i/o request (write thread)
I/O thread 28 state: waiting for i/o request (write thread)
I/O thread 29 state: waiting for i/o request (write thread)
I/O thread 30 state: waiting for i/o request (write thread)
I/O thread 31 state: waiting for i/o request (write thread)
I/O thread 32 state: waiting for i/o request (write thread)
I/O thread 33 state: waiting for i/o request (write thread)
Pending normal aio reads: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] , aio writes: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] ,
 ibuf aio reads:, log i/o's:, sync i/o's:
Pending flushes (fsync) log: 0; buffer pool: 0
166443 OS file reads, 218049433 OS file writes, 148050804 OS fsyncs
0.00 reads/s, 0 avg bytes/read, 4.00 writes/s, 2.75 fsyncs/s
-------------------------------------
INSERT BUFFER AND ADAPTIVE HASH INDEX
-------------------------------------
Ibuf: size 1, free list len 44, seg size 46, 521 merges
merged operations:
 insert 14, delete mark 33164, delete 3794
discarded operations:
 insert 0, delete mark 0, delete 0
Hash table size 553193, node heap has 2 buffer(s)
Hash table size 553193, node heap has 4 buffer(s)
Hash table size 553193, node heap has 3 buffer(s)
Hash table size 553193, node heap has 6 buffer(s)
Hash table size 553193, node heap has 7 buffer(s)
Hash table size 553193, node heap has 63 buffer(s)
Hash table size 553193, node heap has 2 buffer(s)
Hash table size 553193, node heap has 3 buffer(s)
13.12 hash searches/s, 0.25 non-hash searches/s
---
LOG
---
Log sequence number 34405361296
Log flushed up to   34405361296
Pages flushed up to 34405361296
Last checkpoint at  34405361260
Max checkpoint age    1738750649
Checkpoint age target 1684414692
Modified age          0
Checkpoint age        36
0 pending log flushes, 0 pending chkp writes
87163178 log i/o's done, 1.75 log i/o's/second
----------------------
BUFFER POOL AND MEMORY
----------------------
Total large memory allocated 2235564032
Dictionary memory allocated 7726768
Internal hash tables (constant factor + variable factor)
    Adaptive hash index 36912576 	(35404352 + 1508224)
    Page hash           1107112 (buffer pool 0 only)
    Dictionary cache    16577856 	(8851088 + 7726768)
    File system         1494376 	(812272 + 682104)
    Lock system         5500856 	(5313416 + 187440)
    Recovery system     0 	(0 + 0)
Buffer pool size   131056
Buffer pool size, bytes 2147221504
Free buffers       2048
Database pages     128918
Old database pages 47548
Modified db pages  0
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 74649, not young 2361662
0.00 youngs/s, 0.00 non-youngs/s
Pages read 166268, created 345113, written 101444586
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 128918, unzip_LRU len: 0
I/O sum[284]:cur[0], unzip sum[0]:cur[0]
----------------------
INDIVIDUAL BUFFER POOL INFO
----------------------
---BUFFER POOL 0
Buffer pool size   65528
Buffer pool size, bytes 1073610752
Free buffers       1024
Database pages     64458
Old database pages 23774
Modified db pages  0
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 37428, not young 1202825
0.00 youngs/s, 0.00 non-youngs/s
Pages read 83426, created 172058, written 48116927
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 64458, unzip_LRU len: 0
I/O sum[142]:cur[0], unzip sum[0]:cur[0]
---BUFFER POOL 1
Buffer pool size   65528
Buffer pool size, bytes 1073610752
Free buffers       1024
Database pages     64460
Old database pages 23774
Modified db pages  0
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 37221, not young 1158837
0.00 youngs/s, 0.00 non-youngs/s
Pages read 82842, created 173055, written 53327659
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 64460, unzip_LRU len: 0
I/O sum[142]:cur[0], unzip sum[0]:cur[0]
--------------
ROW OPERATIONS
--------------
0 <USER> <GROUP> InnoDB, 0 queries in queue
0 read views open inside InnoDB
0 RW transactions active inside InnoDB
Process ID=1394, Main thread ID=139658780464896, state: sleeping
Number of rows inserted 34039195, updated 19471578, deleted 39347189, read 45953745938
0.00 inserts/s, 0.25 updates/s, 0.00 deletes/s, 96.74 reads/s
----------------------------
END OF INNODB MONITOR OUTPUT
============================



```