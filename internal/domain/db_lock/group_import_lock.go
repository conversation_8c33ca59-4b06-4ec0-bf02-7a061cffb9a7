package db_lock

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/db_lock/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const VolumeZoneImportBizmode = "zone-import"
const ZoneImportDeadLockPeriod = 12 * 60 * 60

func TryGetLock(ctx context.Context, lockPeriod int64, bizmode, groupid, taskId string) (bool, int64) {
	//orginal-checks ??,
	lockTs := timeutil.GetCurrentUnixTimeStamp(ctx)

	model := persistent.SmartRoutingTaskLockTab{
		BizMode:    bizmode,
		LockKey:    groupid,
		ResourceID: taskId,
		Ctime:      lockTs,
		Mtime:      lockTs,
		LockTime:   lockTs,
		ExpireTime: lockTs + lockPeriod,
	}
	//GetCurrentUnixTimeStamp
	db, err := dbutil.MasterDB(ctx, persistent.VolumeRoutingTaskLockHook)
	if err != nil {
		//add-errorlogs,
		return false, 0
	}
	result := db.Table(model.TableName()).Create(&model)
	if result.RowsAffected() == 1 && result.GetError() == nil {
		return true, model.ID
	}
	//delete expired lock ,wont tryGetLock again ( wait for other proc get lock OR next-cron-run),
	deleteExpiredLocks(ctx, bizmode, groupid)
	return false, 0

}

func UnLock(ctx context.Context, pk int64, lockKey string) {
	//called-cks,uat-envs,
	db, err := dbutil.MasterDB(ctx, persistent.VolumeRoutingTaskLockHook)
	if err != nil {
		//add-errorlogs,
		logger.CtxLogErrorf(ctx, " %v ", err)
		return
	}
	result := db.Table(persistent.VolumeRoutingTaskLockHook.TableName()).Delete(&persistent.SmartRoutingTaskLockTab{}, "id = ?", pk)
	logger.CtxLogInfof(ctx, "unlock-locker locker=%v res=%v err=%v", lockKey, result.RowsAffected(), result.GetError())
}

//Delete-exception-cases,cronjob-scheduler,
//saturn-5seconds 扫描， 24hour ? a----b---c,

// 5hours in secon
func deleteExpiredLocks(ctx context.Context, bizmode, groupid string) {
	db, err := dbutil.MasterDB(ctx, persistent.VolumeRoutingTaskLockHook)
	if err != nil {
		//add-errorlogs,
		return
	}

	var rowList []*persistent.SmartRoutingTaskLockTab
	tsNow := timeutil.GetCurrentUnixTimeStamp(ctx)

	db.Debug().Table(persistent.VolumeRoutingTaskLockHook.TableName()).Select("id,lock_key").Where("biz_mode = ? AND lock_key = ? AND expire_time < ?", bizmode, groupid, tsNow).Scan(&rowList)

	//fmt.Println("check ", rowList)
	if len(rowList) > 0 {
		db.Debug().Table(persistent.VolumeRoutingTaskLockHook.TableName()).Delete(persistent.VolumeRoutingTaskLockHook, "id = ?", rowList[0].ID)
		logger.CtxLogInfof(ctx, "delete lockid=%v resourceid= %v", rowList[0].ID, rowList[0].ResourceID)
	} else {
		logger.CtxLogInfof(ctx, "lockid.not.expired ,resourceid = %v", bizmode+groupid)
	}
}
