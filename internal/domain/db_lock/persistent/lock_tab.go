package persistent

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var (
	VolumeRoutingTaskLockHook = new(SmartRoutingTaskLockTab)
)

type SmartRoutingTaskLockTab struct {
	ID         int64  `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	BizMode    string `gorm:"column:biz_mode;NOT NULL"`
	LockKey    string `gorm:"column:lock_key;NOT NULL"`
	ResourceID string `gorm:"column:resource_id;NOT NULL"`
	LockTime   int64  `gorm:"column:lock_time;NOT NULL"`
	ExpireTime int64  `gorm:"column:expire_time;NOT NULL"`
	Ctime      int64  `gorm:"column:ctime;NOT NULL"`
	Mtime      int64  `gorm:"column:mtime;NOT NULL"`
}

func (p *SmartRoutingTaskLockTab) TableName() string {
	return "smart_routing_task_lock_tab"
}

func (p SmartRoutingTaskLockTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p SmartRoutingTaskLockTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p *SmartRoutingTaskLockTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(p.ID),
		ModelName: p.TableName(),
	}
}
