package db_lock

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
	"time"
)

func check(err error) {
	if err != nil {
		panic(err)
	}
}

func initDepends() {
	//
	if err := chassis.Init(
		chassis.WithChassisConfigPrefix("task_server")); err != nil {
		panic(err)
	}
	//db-cfgs ?? loadings ?,
	err := configutil.Init()
	check(err)
	dbutil.Init()
	dbutil.InitGrpcDb()
	//err = localcache.Init(lcregistry.CacheConfig...)
	//check(err)

	//cache-rules ,
}

func TestLock(t *testing.T) {
	//init-dbs ?
	initDepends()

	candidate := []string{"g1", "g6", "g7", "g5", "g4"}

	for p := 0; p < 20; p++ {

		go func(index int) {
			lockerName := candidate[index%len(candidate)]

			locker, pk := TryGetLock(context.TODO(), ZoneImportDeadLockPeriod, VolumeZoneImportBizmode, lockerName, "t1")
			fmt.Println("locker ", lockerName, locker, pk)
			time.Sleep(time.Second * 5)
			//if locker {
			//	UnLock(context.TODO(), pk, "")
			//}
		}(p)
	}

	select {}
}
