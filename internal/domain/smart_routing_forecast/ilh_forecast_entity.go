package smart_routing_forecast

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
)

type ILHForecastTask struct {
	ID                  int                                      `json:"id"`
	StartDate           string                                   `json:"start_date"`
	EndDate             string                                   `json:"end_date"`
	ShipmentResource    persistent.ShipmentResource              `json:"shipment_resource"`
	AvailableLHRuleList []forecastentity.ForecastAvailableLHRule `json:"available_lh_rule_list"`
	LHCapacityList      []forecastentity.ForecastLHCapacityItem  `json:"lh_capacity_list"`
}
