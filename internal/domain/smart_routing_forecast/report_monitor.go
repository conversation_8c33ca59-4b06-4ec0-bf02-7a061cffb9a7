package smart_routing_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

const (
	Success                  = true
	Failed                   = false
	LocalForecastMonitor     = "LocalForecastMonitor"
	InitForecast             = "InitForecast"
	ForecastsProcess         = "ForecastsProcess"
	ForecastReadOrder        = "ForecastReadOrder"
	ForecastRecordOrder      = "ForecastRecordOrder"
	ForecastResult           = "ForecastResult"
	ForecastStatisticsResult = "ForecastStatisticsResult"
	ForecastSpeed            = "ForecastSpeed"
	SingleOrder              = "SingleOrder"
)

func ForecastReportByMonitorKey(ctx context.Context, taskId int, monitorKey, message string, isSuccess bool) {
	if isSuccess {
		monitoring.ReportSuccess(ctx, LocalForecastMonitor, monitoring.GenerateLocalForecastMonitorKey(monitorKey, taskId), message)
	} else {
		monitoring.ReportError(ctx, LocalForecastMonitor, monitoring.GenerateLocalForecastMonitorKey(monitorKey, taskId), message)
	}
}

type StatisticsResultByDay struct {
	RoutingSuccess int `json:"routing_success"`
	RoutingFailed  int `json:"routing_failed"`
	Block          int `json:"block"`
}
