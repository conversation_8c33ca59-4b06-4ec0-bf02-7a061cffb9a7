package smart_routing_forecast

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/routinglogutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/zstd"
	"github.com/panjf2000/ants/v2"
	"go.uber.org/ratelimit"
	"math"
	"math/rand"
	"runtime"
	"sync"
	"time"
)

const maxPageLoop = 4000 // 分页查询最大页数
const minPageCount = 1
const minPage = 1
const maxOrderPoolLoop = 4 // 订单池最大循环次数

type SamplePool struct {
	StartDate        string
	EndDate          string
	samplingRate     float64
	HbHelper         *masking_forecast_hbase.HBHelper
	sampleDateList   []time.Time
	SampleOrderCount int
	DataApi          *dataclient.DataApi
}

func (s *SamplePool) ReadOrderFromHbase(ctx context.Context, args *forecastProcessArgs, orderCount int) chan *gohbase.Result {
	resultChs := make(chan *gohbase.Result, 1000)
	go s.readOrderBySimulationOrderCount(ctx, args, orderCount, resultChs)
	return resultChs
}

func (s *SamplePool) ReadOrderFromDataApi(ctx context.Context, args *forecastProcessArgs, orderCount int) chan *routing_log.RoutingLog {
	resultChs := make(chan *routing_log.RoutingLog, 1000)
	if configutil.ForecastSwitch() {
		go s.readOrderBySimulationOrderCountV3(ctx, args, orderCount, resultChs)
	} else {
		go s.readOrderBySimulationOrderCountV2(ctx, args, orderCount, resultChs)
	}

	return resultChs
}

// 根据模拟日期读取订单
func (s *SamplePool) readOrderBySimulationOrderCount(ctx context.Context, args *forecastProcessArgs, simulationOrderCount int, resultChs chan *gohbase.Result) {
	defer close(resultChs)
	scanOrderCount := 0
	loop := 0
	scOrderCount := 0
	samplingPoolMaxLoop := configutil.GetLocalForecastConfig(ctx).SamplingPoolMaxLoop
	//这里最多循环4次，因为采样率最低是0.3，每次循环结束都会double一下采样率，4次后是1.2表示每条数据都采集
	for simulationOrderCount > 0 && loop < samplingPoolMaxLoop {
		scanOrderCount = s.doReadOrderFromHbase(ctx, args.task.Id, args.task.ProductId, args.task.RoutingType, simulationOrderCount, resultChs)
		s.fullSampleRate()
		simulationOrderCount = simulationOrderCount - scanOrderCount
		scOrderCount += scanOrderCount
		loop++
	}
	ForecastReportByMonitorKey(ctx, args.task.Id, ForecastReadOrder, fmt.Sprintf("finish order count %d", scOrderCount), Success)
}

// 复制从hbase中返回的结果
func copyResult(target *gohbase.Result) *gohbase.Result {
	copyResult := gohbase.Result{}
	copyResult.Cells = make([]*gohbase.Cell, 1)
	copyResult.Cells[0] = &gohbase.Cell{Value: target.Cells[0].Value}
	return &copyResult
}

func (s *SamplePool) readOrderBySimulationOrderCountV2(ctx context.Context, args *forecastProcessArgs, simulationCountByDay int, resultChs chan *routing_log.RoutingLog) {
	defer close(resultChs)
	// 遍历模拟日期
	pageCount := configutil.GetDataApiSwitchConf(ctx).ReadOrderPageCount
	if pageCount == 0 { // 如果没有配置就用默认值
		pageCount = 500
	}
	orderPoolLoop := 1 // 订单池循环次数
	for simulationCountByDay > 0 && orderPoolLoop < maxOrderPoolLoop {
		n := simulationCountByDay / s.SampleOrderCount //需要将每一个order放大n倍
		scannedOrder := 0                              // 实际读取到的订单数量
		// 遍历订单池日期
		for _, orderDate := range s.sampleDateList {
			endPoolLoop := false
			totalPage := s.queryTotalPageByDay(ctx, args.task.ProductId, int(args.task.RoutingType), orderDate, pageCount)
			// 下面是分页读取每天的订单量,maxLoop=2000,假设一天200w订单，一次读取1000次，最多需要2000次
			for i := 0; i < maxPageLoop && i < totalPage; i++ {
				endPageLoop := false
				// 调用data读取订单
				orderDetailResp, err := s.queryOrderDetailWithDataApi(ctx, args.task.ProductId, int(args.task.RoutingType), orderDate, pageCount, i+1)
				if err != nil {
					logger.CtxLogErrorf(ctx, "queryOrderDetailWithDataApi failed | err=%v", err)
					continue
				}
				// 遍历每一次分页查询的结果
				for _, orderDetail := range orderDetailResp.OrderDetailList {
					if scannedOrder >= simulationCountByDay { // 订单满足数量后退出
						endPageLoop = true
						break
					}
					/* 处理订单
					例1 simulationCountByDay=109(这一天要模拟预测100单)，订单池只有10单，n=100/10，就是把每一笔订复制10次,剩下的9单随机采样
					例2 simulationCountByDay=9,订单池有100单则9单全部随机采样
					*/
					readOrderCount, err := s.processOrderDetail(ctx, orderDetail, n, resultChs)
					if err != nil {
						continue
					}
					scannedOrder = scannedOrder + readOrderCount
				}
				// 结束分页读取循环
				if endPageLoop {
					endPoolLoop = true
					break
				}
			}
			if endPoolLoop {
				break
			}
		}
		// 减去订单池扫描到的订单数
		simulationCountByDay = simulationCountByDay - scannedOrder
		orderPoolLoop++
	}
}

func (s *SamplePool) readOrderBySimulationOrderCountV3(ctx context.Context, args *forecastProcessArgs, simulationCountByDay int, resultChs chan *routing_log.RoutingLog) {
	defer close(resultChs)
	// 遍历模拟日期
	pageCount := configutil.GetDataApiSwitchConf(ctx).ReadOrderPageCount
	if pageCount == 0 { // 如果没有配置就用默认值
		pageCount = 3000
	}
	orderPoolLoop := 1 // 订单池循环次数
	for simulationCountByDay > 0 && orderPoolLoop < maxOrderPoolLoop {
		n := simulationCountByDay / s.SampleOrderCount //需要将每一个order放大n倍
		scannedOrder := 0                              // 实际读取到的订单数量
		// 遍历订单池日期
		for _, orderDate := range s.sampleDateList {
			orderDateFormat := timeutil.FormatDate(orderDate)
			endPoolLoop := false
			totalOrderCountByDay, err := s.DataApi.QueryOrderCountByDay(ctx, args.task.ProductId, int(args.task.RoutingType), orderDateFormat, orderDateFormat)
			if err != nil {
				logger.CtxLogErrorf(ctx, "query total order count failed | err=%v", err)
				continue
			}
			totalPage := int(math.Ceil(float64(totalOrderCountByDay[0].OrderCount) / float64(pageCount)))
			// 下面是分页读取每天的订单量,maxLoop=2000,假设一天200w订单，一次读取1000次，最多需要2000次
			for i := 0; i < maxPageLoop && i < totalPage; i++ {
				endPageLoop := false
				// 调用data读取订单
				orderDetailList, err := s.DataApi.QueryOrderDetailByPage(ctx, args.task.ProductId, int(args.task.RoutingType), orderDateFormat, totalPage, i)
				if err != nil {
					logger.CtxLogErrorf(ctx, "query total order detail failed | err=%v", err)
					continue
				}
				// 遍历每一次分页查询的结果
				for _, orderDetail := range orderDetailList {
					if scannedOrder >= simulationCountByDay { // 订单满足数量后退出
						endPageLoop = true
						break
					}
					/* 处理订单
					例1 simulationCountByDay=109(这一天要模拟预测100单)，订单池只有10单，n=100/10，就是把每一笔订复制10次,剩下的9单随机采样
					例2 simulationCountByDay=9,订单池有100单则9单全部随机采样
					*/
					readOrderCount, err := s.processOrderDetailV3(ctx, orderDetail, n, resultChs)
					if err != nil {
						continue
					}
					scannedOrder = scannedOrder + readOrderCount
				}
				// 结束分页读取循环
				if endPageLoop {
					endPoolLoop = true
					break
				}
			}
			if endPoolLoop {
				break
			}
		}
		// 减去订单池扫描到的订单数
		simulationCountByDay = simulationCountByDay - scannedOrder
		orderPoolLoop++
	}
}
func (s *SamplePool) queryOrderDetailWithDataApi(ctx context.Context, productId, routingType int, orderDate time.Time,
	pageCount, currentPage int) (*dataclient.QueryOrderDetailRespDetail, *srerr.Error) {
	req := dataclient.QueryOrderDetailReq{
		ProductId:   productId,
		RoutingType: routingType,
		StartDate:   timeutil.FormatDate(orderDate),
		EndDate:     timeutil.FormatDate(orderDate),
		PageCount:   pageCount,
		PageOffset:  currentPage,
		Region:      envvar.GetCID(),
	}
	resp, err := s.DataApi.OrderOrderDetail(ctx, req)
	if err != nil {
		errMsg := fmt.Sprintf("query order detail failed | err=%v", err)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.LocalForecastMonitor, monitoring.DataApiMonitor, errMsg)
		return nil, err
	}
	if !resp.IsSuccess() {
		errMsg := fmt.Sprintf("query order detail from data unsuccessful | response err: %s", resp.Message)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.LocalForecastMonitor, monitoring.DataApiMonitor, errMsg)
		return nil, srerr.New(srerr.DataApiErr, req, errMsg)
	}

	return &resp.Data, nil
}

func (s *SamplePool) queryTotalPageByDay(ctx context.Context, productId, routingType int, orderDate time.Time, pageSize int) int {
	resp, err := s.queryOrderDetailWithDataApi(ctx, productId, routingType, orderDate, minPageCount, minPage)
	if err != nil {
		errMsg := fmt.Sprintf("day=[%v] query total page err=%v", orderDate, err)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.LocalForecastMonitor, monitoring.DataApiMonitor, errMsg)
		return 0
	}
	totalPage := int(math.Ceil(float64(resp.TotalSize) / float64(pageSize)))
	logger.CtxLogErrorf(ctx, "day=[%v] total page=%d,total order count=%d", orderDate, totalPage, resp.TotalSize)
	return totalPage
}

func (s *SamplePool) processOrderDetail(ctx context.Context, orderDetail dataclient.RealOrderDetail, n int, resultChs chan *routing_log.RoutingLog) (int, error) {
	// 0.base64 decode
	order, decompressResult, derr := routinglogutil.DecodeRoutingLog(ctx, orderDetail.MessageBody)
	if derr != nil {
		monitoring.ReportError(ctx, monitoring.LocalForecastMonitor, monitoring.DataApiMonitor, fmt.Sprintf("Decode message err=%v", derr))
		return 0, derr
	}

	// 3.判断是否需要把订单方法
	if n > 0 {
		// 对单条订单放大
		return writeOrderToChannelV2(ctx, decompressResult, n, resultChs), nil
	}

	// 4.如果不需要方法则判断是否命中随机采样
	if s.isCollectOrder() {
		resultChs <- &order
		return 1, nil
	} else {
		return 0, nil
	}
}

func (s *SamplePool) processOrderDetailV3(ctx context.Context, orderDetail string, n int, resultChs chan *routing_log.RoutingLog) (int, error) {
	// 1.解压缩
	order, decompressResult, err := routinglogutil.DecodeRoutingLog(ctx, orderDetail)
	if err != nil {
		return 0, err
	}

	// 3.判断是否需要把订单方法
	if n > 0 {
		// 对单条订单放大
		return writeOrderToChannelV2(ctx, decompressResult, n, resultChs), nil
	}

	// 4.如果不需要方法则判断是否命中随机采样
	if s.isCollectOrder() {
		resultChs <- &order
		return 1, nil
	} else {
		return 0, nil
	}
}

// 把订单数据写入channel中
func writeOrderToChannel(ctx context.Context, result *gohbase.Result, n int, resultChs chan *gohbase.Result) (int, error) {
	if n > 0 {
		//此处是将n倍log放入channel中
		for i := 0; i < n; i++ {
			copyResult := copyResult(result)
			resultChs <- copyResult
		}
		return n, nil
	} else {
		resultChs <- result
	}
	return 1, nil
}

func writeOrderToChannelV2(ctx context.Context, orderBytes []byte, n int, resultChs chan *routing_log.RoutingLog) int {
	//此处是将n倍log放入channel中
	for i := 0; i < n; i++ {
		resultChs <- copyRoutingLog(ctx, orderBytes)
	}

	return n
}

// 复制从hbase中返回的结果,这里字段比较多，手动复制容易出现字段遗漏，所以暂时采用Unmarshal方式复制
func copyRoutingLog(ctx context.Context, orderBytes []byte) *routing_log.RoutingLog {
	order := routing_log.RoutingLog{}
	if err := jsoniter.Unmarshal(orderBytes, &order); err != nil {
		errMsg := fmt.Sprintf("copy routing log unmarshal order routing log failed | message=%s, err=%v,", string(orderBytes), err)
		logger.CtxLogErrorf(ctx, errMsg)
		monitoring.ReportError(ctx, monitoring.LocalForecastMonitor, monitoring.DataApiMonitor, errMsg)
	}
	return &order
}

func (s *SamplePool) doReadOrderFromHbase(ctx context.Context, taskId, productId int, routingType uint8, simulationOrderCount int, resultChs chan *gohbase.Result) int {
	hbCfg := configutil.GetDataHbaseConfig(ctx)
	tableName := hbCfg.TableNameMap[masking_forecast_hbase.ROUTING_LOG]
	hbaseReadLimit := configutil.GetLocalForecastConfig(ctx).ReadLimit
	salt := configutil.GetLocalForecastConfig(ctx).PrefixSalt
	ending := false
	scanOrderCount := 0
	for _, sampleDate := range s.sampleDateList {
		//按照前缀查询数据
		for i := 0; i < salt && !ending; i++ {
			startRowKey := s.generateRowKey(i, productId, routingType, timeutil.GetStartTime(sampleDate))
			endRowKey := s.generateRowKey(i, productId, routingType, timeutil.GetStartTime(sampleDate.Add(time.Hour*24)))
			readOrder := s.loopReadOrder(ctx, taskId, tableName, startRowKey, endRowKey, uint32(hbaseReadLimit), simulationOrderCount-scanOrderCount, resultChs)
			//simulationOrderCount = simulationOrderCount - readOrder
			scanOrderCount += readOrder
			if scanOrderCount >= simulationOrderCount {
				ending = true
				break
			}
		}
		if ending {
			break
		}
	}

	return scanOrderCount
}

/*
rowKey格式：{salt}_{productId}_{routingType}_{10位时间戳}_{requestId}
这里不能跨salt查询如果这里跨salt会查询到脏数据，比如生成的starRowKey前缀是000_10086，endRowKey前缀是001_10086，
那么在卡数据的时候会卡到000_10087这种脏数据。所以这里只能一个salt一个查询，然后一个salt在用循环查询
*/
func (s *SamplePool) loopReadOrder(ctx context.Context, taskId int, tableName, startRowKey, endRowKey string, readLimit uint32, simulationOrderCount int, resultChs chan *gohbase.Result) int {
	loop := 0
	scanOrderCount := 0
	hbaseReadMaxLoop := configutil.GetLocalForecastConfig(ctx).HbaseReadMaxLoop
	for startRowKey != "" && loop < hbaseReadMaxLoop {
		resultList, rowKey, err := s.HbHelper.ScanByIndex(ctx, tableName, startRowKey, endRowKey, readLimit)
		if err != nil {
			ForecastReportByMonitorKey(ctx, taskId, ForecastReadOrder, fmt.Sprintf("Get Forecast order err %v", err), Failed)
			logger.CtxLogErrorf(ctx, "Get Forecast order err %v", err)
			return scanOrderCount
		}
		n := simulationOrderCount / s.SampleOrderCount
		for _, result := range resultList {
			//判断余数部分订单量是否满足条件
			if scanOrderCount >= simulationOrderCount {
				break
			}
			if s.isNeedData(n) {
				if writeCount, err := writeOrderToChannel(ctx, result, n, resultChs); err == nil {
					scanOrderCount += writeCount
					continue
				}
			}
		}
		if scanOrderCount >= simulationOrderCount {
			break
		}
		loop++
		startRowKey = rowKey
	}

	return scanOrderCount
}

// 如果n > 0保证池子里面每条数据都会采集，如果n = 0表示样本池中随机采集
func (s *SamplePool) isNeedData(n int) bool {
	return n != 0 || !s.isFilterSingleData()
}

func (s *SamplePool) isCollectOrder() bool {
	randomNum := rand.Float64() // nolint
	return randomNum < s.samplingRate
}

// 是否对单量条数据过滤
func (s *SamplePool) isFilterSingleData() bool {
	randomNum := rand.Float64() // nolint
	return s.samplingRate < randomNum
}

func (s *SamplePool) generateRowKey(salt, productId int, routingType uint8, datetime int64) string {
	return fmt.Sprintf("%03d_%d_%d_%d", salt, productId, routingType, datetime)
}

func (s *SamplePool) SetSamplingRate(ctx context.Context, simulationOrderCount int) *srerr.Error {
	if s.SampleOrderCount == 0 {
		return srerr.With(srerr.ParamErr, nil, errors.New("SampleOrderCount is zero"))
	}
	//如果说超出预测样本量就用simulationOrderCount/SampleOrderCount作为采样率分子
	if simulationOrderCount > s.SampleOrderCount {
		remain := simulationOrderCount % s.SampleOrderCount
		s.samplingRate = float64(remain) / float64(s.SampleOrderCount)
	} else {
		s.samplingRate = float64(simulationOrderCount) / float64(s.SampleOrderCount)
	}
	s.checkSamplingRateValid(ctx)
	return nil
}

// 采样率如果大于1则每条数据都会采样
func (s *SamplePool) fullSampleRate() {
	s.samplingRate = 1.1
}

// 校验一下采样率是否比最低采样率0.3小，如果小的话就用默认采样率，防止采样率过低不停的从hbase查数据
func (s *SamplePool) checkSamplingRateValid(ctx context.Context) {
	rate := configutil.GetLocalForecastConfig(ctx).MinSamplingRate
	s.samplingRate = math.Max(rate, s.samplingRate)
}

// TODO 需要打印调用栈信息？？？
var PanicHandler = func(err interface{}) {
	if err != nil {
		var buf [4096]byte
		n := runtime.Stack(buf[:], false)
		errMsg := fmt.Sprintf("\n%s\n%s", err, string(buf[:n]))
		logger.LogErrorf("goroutine panic %s", errMsg)
	}
}

type forecastProcessArgs struct {
	resultCounter      *volume_counter.AsyncCounter
	pool               *ants.Pool
	waitGroup          *sync.WaitGroup
	task               *persistent.ForecastingTaskTab
	rateLimit          ratelimit.Limiter
	volumeCounter      *vrservice.ForecastVolumeRouting
	capacityCounter    volume_counter.VolumeCounter
	ruleList           []*ruledata.RoutingRuleTab
	samplePool         *SamplePool
	decoder            *zstd.Decoder //TODO 初始化
	simulationDateList []*persistent.SimulationOrderCount
}
