package smart_routing_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const (
	readOrderLimitSize = 1000
)

func (s *SmartRoutingForecastServiceImpl) readForecastOrders(ctx context.Context, productId int, isMultiProduct bool, day time.Time) (<-chan *forecastentity.LFSOrderInfo, *srerr.Error) {
	var (
		ch           = make(chan *forecastentity.LFSOrderInfo, readOrderLimitSize)
		routingLogCh chan forecastservice.RoutingLogRowKeyMap
		readErr      *srerr.Error
	)

	if configutil.GetDataApiSwitchConf(ctx).ReadOrderFromDataApiSwitch {
		routingLogCh, readErr = s.ForecastTaskService.GetForecastOrderFromDataApi(ctx, 0, productId, rule.CBRoutingType, 0, day)
		if readErr != nil {
			logger.CtxLogInfof(ctx, "read order from data api failed | day: %v ,err: %v", day, readErr)
			return nil, readErr
		}
	} else {
		routingLogCh, readErr = s.ForecastTaskService.GetForecastOrdersFromHbase(ctx, 0, 0, productId, rule.CBRoutingType, readOrderLimitSize, day)
		if readErr != nil {
			return nil, readErr
		}
	}

	go func() {
		defer close(ch)
		for hbaseData := range routingLogCh {
			var orderData pb.CreateOrderRequestData
			if err := jsoniter.UnmarshalFromString(hbaseData.LogEntry.OrderData, &orderData); err != nil {
				logger.CtxLogErrorf(ctx, "unmarshal order data failed | order data=%s, err=%v", hbaseData.LogEntry.OrderData, err)
				continue
			}
			hardResult := hbaseData.LogEntry.HCResult
			if isMultiProduct {
				hardResult = hbaseData.LogEntry.MultiProductHCResult
			}
			// 兼容旧数据的读取
			parcelLength := hbaseData.LogEntry.ExtraInfo.ParcelLength
			parcelWidth := hbaseData.LogEntry.ExtraInfo.ParcelWidth
			parcelHeight := hbaseData.LogEntry.ExtraInfo.ParcelHeight
			orderParcelDimension := hbaseData.LogEntry.ExtraInfo.SmartRoutingData.OrderParcelDimension
			if orderParcelDimension.Length != 0 || orderParcelDimension.Width != 0 || orderParcelDimension.Height != 0 {
				parcelLength = orderParcelDimension.Length
				parcelWidth = orderParcelDimension.Width
				parcelHeight = orderParcelDimension.Height
			}
			ch <- &forecastentity.LFSOrderInfo{
				OrderSN:          orderData.GetBaseInfo().GetOrdersn(),
				ForderId:         hbaseData.LogEntry.FOrderId,
				HardResult:       hardResult,
				Skus:             orderData.GetSkus(),
				WhsCode:          orderData.GetBaseInfo().GetWhsId(),
				LocationIdList:   hbaseData.LogEntry.ExtraInfo.DeliveryLocationIdList,
				Cogs:             orderData.GetForderInfo().GetCogs(),
				ValidationWeight: int(hbaseData.LogEntry.ExtraInfo.ValidationWeight),
				DgType:           hbaseData.LogEntry.ExtraInfo.DgType,
				ParcelLength:     parcelLength,
				ParcelWidth:      parcelWidth,
				ParcelHeight:     parcelHeight,
				ParcelWeight:     hbaseData.LogEntry.ExtraInfo.SmartRoutingData.OrderParcelDimension.Weight,
				IsCod:            hbaseData.LogEntry.ExtraInfo.SmartRoutingData.IsCod,
				DeliverPostCode:  hbaseData.LogEntry.ExtraInfo.SmartRoutingData.DeliveryPostCode,
				ShopId:           orderData.GetForderInfo().GetShopId(),
				LineAsfMap:       hbaseData.LogEntry.LineAsfMap,
			}
		}
	}()

	return ch, nil
}
