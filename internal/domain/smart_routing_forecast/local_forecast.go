package smart_routing_forecast

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/zstd"
	"runtime"
	"sort"
	"strings"
	"sync"
)

const (
	emptyJson  = "{}"
	defaultDay = 1 //如果没有上传模拟预测天数就用默认的一天
)

const (
	initArg = iota
	Validation
	doForecast
	clearCache
	workFlowLen
)

type LocalForecastService interface {
	StartForecast(ctx context.Context, task *persistent.ForecastingTaskTab) error
}

type LocalForecastServiceImpl struct {
	workFlow []LocalForecast
}

func NewLocalForecastServiceImpl(RoutingRuleRepo routing.RoutingRuleRepo,
	RoutingSrv routing.RoutingService, ZoneRepo locationzone.ZoneRepo, laneSrv lane.LaneService, ZoneGroupRepo vrrepo.ZoneGroupRepo,
	VolumeZoneRepo vrrepo.ZoneRepo) *LocalForecastServiceImpl {
	flows := initWorkFlow(RoutingRuleRepo, RoutingSrv, ZoneRepo, laneSrv, ZoneGroupRepo, VolumeZoneRepo)
	return &LocalForecastServiceImpl{
		workFlow: flows,
	}
}

func initWorkFlow(RoutingRuleRepo routing.RoutingRuleRepo,
	RoutingSrv routing.RoutingService, ZoneRepo locationzone.ZoneRepo, laneSrv lane.LaneService, ZoneGroupRepo vrrepo.ZoneGroupRepo,
	VolumeZoneRepo vrrepo.ZoneRepo) []LocalForecast {
	init := &InitForecastArgs{}
	verify := &VerifyArgs{}
	forecast_ := &Forecast{RoutingSrv: RoutingSrv, RoutingRuleRepo: RoutingRuleRepo, ZoneRepo: ZoneRepo, laneSrv: laneSrv, ZoneGroupRepo: ZoneGroupRepo, VolumeZoneRepo: VolumeZoneRepo}
	clearRedis := ClearRedis{}
	flows := make([]LocalForecast, workFlowLen)
	flows[initArg] = init
	flows[Validation] = verify
	flows[doForecast] = forecast_
	flows[clearCache] = &clearRedis
	return flows
}

func (l *LocalForecastServiceImpl) StartForecast(ctx context.Context, task *persistent.ForecastingTaskTab) error {
	defer func() {
		if err := recover(); err != nil {
			var buf [4096]byte
			n := runtime.Stack(buf[:], false)
			errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
			logger.CtxLogErrorf(ctx, errMsg)
		}
	}()
	args := forecastProcessArgs{task: task}
	for _, flow := range l.workFlow {
		if err := flow.Work(ctx, &args); err != nil {
			return err
		}
	}

	return nil
}

// 对预测任务处理
func processTask(task *persistent.ForecastingTaskTab) *srerr.Error {
	if task.SimulationOrderCountDetail != nil {
		mslErr := jsoniter.Unmarshal(task.SimulationOrderCountDetail, &task.SimulationOrderCount)
		if mslErr != nil {
			return srerr.With(srerr.JsonErr, nil, mslErr)
		}
	}

	if task.WeightRangeDetail != nil {
		mslErr := jsoniter.Unmarshal(task.WeightRangeDetail, &task.WeightRange)
		if mslErr != nil {
			return srerr.With(srerr.JsonErr, nil, mslErr)
		}
	}

	return nil
}

func validationUploadSimulation(task *persistent.ForecastingTaskTab) []*persistent.SimulationOrderCount {
	// 如果没有上传模拟预测单量，就按照一天算
	if len(task.SimulationOrderCount) == 0 {
		return []*persistent.SimulationOrderCount{
			{
				Day:   defaultDay,
				Count: task.OrderCount,
			},
		}
	}

	return task.SimulationOrderCount
}

func createSamplePool(ctx context.Context, task *persistent.ForecastingTaskTab) *SamplePool {
	sp := &SamplePool{}
	// OrderCount在task submit的时候有校验是否为0
	sp.StartDate = task.StartDate
	sp.EndDate = task.EndDate
	sp.SampleOrderCount = task.OrderCount
	sp.HbHelper = masking_forecast_hbase.NewHBHelper()
	sp.sampleDateList, _ = getDays(task.StartDate, task.EndDate)
	sp.DataApi = dataclient.NewDataApi()
	return sp
}

// 校验样本日期是都合格
func verificationSampleDate(start, end string) *srerr.Error {
	SampleDateList, daysErr := getDays(start, end)
	if daysErr != nil {
		return srerr.With(srerr.DataErr, nil, daysErr)
	}
	// 预测样本的日期跨度不能超过15个自然日
	if len(SampleDateList) > maxForecastDays {
		return srerr.New(srerr.ParamErr, nil, "too large time range %v - %v, max is 7 days", start, start)
	}
	return nil
}

func clearLastForecastedInfo(ctx context.Context, taskId int) {
	dayKey := fmt.Sprintf("%s_%d", forecast.LastForecastedDay, taskId)
	indexKey := fmt.Sprintf("%s_%d", forecast.LastForecastedIndex, taskId)
	orderCount := fmt.Sprintf("%s_%d", forecast.ForecastedOrderCount, taskId)

	if err := redisutil.Del(ctx, dayKey); err != nil {
		logger.CtxLogErrorf(ctx, "clear day key failed %+v", err)
	}
	if err := redisutil.Del(ctx, indexKey); err != nil {
		logger.CtxLogErrorf(ctx, "clear index key failed %+v", err)
	}
	if err := redisutil.Del(ctx, orderCount); err != nil {
		logger.CtxLogErrorf(ctx, "del order count key failed %+v, %d", err, taskId)
	}
}

func (f *Forecast) recordVolume(ctx context.Context, volumeCounter *vrservice.ForecastVolumeRouting,
	lane *rule.RoutingLaneInfo, volumeRuleList []*persistent.ForecastVolumeRuleTab,
	log *routing_log.RoutingLog, productId int, postcode string, routingType int) {
	if log == nil || log.VolumeRuleID == 0 {
		return
	}
	var volumeRule *persistent.ForecastVolumeRuleTab
	date := timeutil.FormatDate(timeutil.GetLocalTime(ctx))

	for _, rule := range volumeRuleList {
		if uint64(rule.RuleId) == log.VolumeRuleID {
			volumeRule = rule
			break
		}
	}
	if volumeRule == nil {
		return
	}
	lineGroupMap := make(map[string]string)
	zoneLimits := volumeRule.ZoneLimit
	for i := 0; i < len(zoneLimits); i++ {
		if _, ok := lineGroupMap[zoneLimits[i].LineId]; !ok {
			// local Forecast 这里用的是实时group的映射关系
			if groupId, _ := f.ZoneGroupRepo.GetGroupIdByLineWithCache(ctx, zoneLimits[i].LineId, routingType, constant.NonForecastType); groupId != "" {
				lineGroupMap[zoneLimits[i].LineId] = groupId
			}
		}
	}
	var lineIds []string
	for _, line := range lane.LineList {
		lineIds = append(lineIds, line.LineId)
	}
	logger.CtxLogInfof(ctx, "result lineIds =%v", lineIds)
	_ = volumeCounter.IncrVolumeLineDimension(ctx, int64(productId), lineIds, date, nil)
	if volumeRule.RuleType == enum.VolumeRuleTypeProduct {
		return
	}
	realPostcode := common.ReshapePostcode(postcode)
	//in new page ,fl wont bind a group ,so will set a default limit
	for _, line := range lane.LineList {
		groupId, ok := lineGroupMap[line.LineId]
		if !ok {
			logger.CtxLogInfof(ctx, "line_id=%v not bind group", line.LineId)
			continue
		}
		// local Forecast 这里用的是实时group的映射关系
		groupInfo, _ := f.ZoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, groupId, routingType, constant.NonForecastType)
		if groupInfo == nil {
			logger.CtxLogInfof(ctx, "line_id=%v group_id=%v get group_info err", line.LineId, groupId)
			continue
		}
		zoneName := f.getZoneName(ctx, groupInfo, realPostcode)
		logger.CtxLogInfof(ctx, "result lineId=%v,zoneName=%s", line, zoneName)
		_ = volumeCounter.IncrVolumeZoneDimension(ctx, int64(productId), line.LineId, date, groupId, zoneName, nil)
	}

}

// 更新line的信息包括映射关系以及dg信息
func updateLineInfo(ctx context.Context, task *persistent.ForecastingTaskTab, originOrder *routing_log.RoutingLog) map[int]int {
	roleMap := routing_role.UpdateLineResourceType(ctx, task.ProductId, task.RoutingType, originOrder.HCResult, task.IsMultiProduct, nil)
	for _, availableLane := range originOrder.HCResult {
		for _, lineInfo := range availableLane.LineList {
			lineInfo.LineId = lineInfo.ResourceId
			if !objutil.ContainInt32(dgLineList, lineInfo.RealResourceSubType) {
				lineInfo.DGFlag = rule.UndefinedDGFlag
			}
		}
	}
	return roleMap
}

func initForecastArgsAndFactors(ctx context.Context, args *forecastProcessArgs, task *persistent.ForecastingTaskTab, ruleList []*ruledata.RoutingRuleTab) *srerr.Error {
	//初始化协程池
	pool, err := poolBuilder(ctx)
	if err != nil {
		return err
	}
	args.task = task
	args.pool = pool
	args.waitGroup = &sync.WaitGroup{}
	args.volumeCounter, args.capacityCounter = forecastAndVolumeCounterBuilder()
	args.ruleList = ruleList
	args.samplePool = createSamplePool(ctx, task)
	args.resultCounter = resultCounterBuilder(task)
	args.rateLimit = rateLimitBuilder(ctx)
	args.decoder, _ = zstd.NewReader(nil)
	args.simulationDateList = validationUploadSimulation(task)
	return nil
}

func recordForecastedOrderCount(ctx context.Context, taskId int) {
	current, rErr := redisutil.Incr(ctx, forecastedOrderCountKey(taskId))
	if rErr != nil {
		ForecastReportByMonitorKey(ctx, taskId, ForecastRecordOrder, fmt.Sprintf("record Forecast order count failed %+v, current is %d", rErr, current), Failed)
		logger.CtxLogErrorf(ctx, "record Forecast order count failed %+v, current is %d", rErr, current)
	}
}

func newCtx(ctx context.Context, requestId string, FOrderId string, taskId int) context.Context {
	newRequestID := fmt.Sprintf("%s|%s__%d", requestId, FOrderId, taskId)
	orderCtx := logger.NewLogContext(ctx, newRequestID)
	orderCtx = requestid.SetToCtx(orderCtx, newRequestID)
	return orderCtx
}

//匹配routing rule

func (f *Forecast) startRouting(ctx context.Context, task *persistent.ForecastingTaskTab,
	routingRule *rule.RoutingRuleParsed, originOrder *routing_log.RoutingLog, resultCounter *volume_counter.AsyncCounter, roleMap map[int]int) (*rule.RoutingLaneInfo, *routing_log.RoutingLog) {
	//记录routing结果日志
	logEntry := new(routing_log.RoutingLog)
	setupRoutingLogBasic(logEntry, routingRule, roleMap)
	// 重新计算运费开关信息
	extraInfo := routing.ExtraInfo{
		BusinessType:  constant.ForecastRouting,
		ReCalcFee:     task.ReCalcFee,
		OldLineFeeMap: originOrder.LineFeeMap, // 记录已有运费
	}
	originOrder.ExtraInfo.SmartRoutingData.DeliveryPostCode = common.ReshapePostcode(originOrder.ExtraInfo.SmartRoutingData.DeliveryPostCode)
	resultLanes, err := f.RoutingSrv.RoutingWithExtraInfo(ctx, task.ProductId, task.IsMultiProduct, originOrder.HCResult, routingRule, nil, &originOrder.ExtraInfo.SmartRoutingData, logEntry, false, &extraInfo)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Routing fail|err=%v", err)
		resultCounter.Incr(forecast.RoutingFailed, defaultDbFlag, 0, "", 0, 0, false)
		return nil, logEntry
	}

	return resultLanes[0], logEntry
}

func (f *Forecast) recordCapacity(ctx context.Context, args *forecastProcessArgs, resultLane *rule.RoutingLaneInfo, order *routing_log.RoutingLog) {
	zoneCodeList, err := f.ZoneRepo.GetZoneCodeListFromCache(ctx, args.task.ProductId, args.task.RoutingType, locationzone.ForecastingZoneType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetZoneCodeListFromCache fail|err=%+v")
	}

	deliverZone := f.ZoneRepo.MatchDistrictToZone(
		ctx, args.task.ProductId, order.ExtraInfo.DeliveryLocationIdList, zoneCodeList, args.task.RoutingType,
		locationzone.ForecastingZoneType, order.ExtraInfo.SmartRoutingData.DeliveryPostCode,
	)
	// 3. incr volumeCounter
	for _, line := range resultLane.LineList {
		_ = args.capacityCounter.IncrLineVolume(ctx, args.task.ProductId, line.ResourceId, nil)
		if deliverZone != "" {
			_ = args.capacityCounter.IncrLineZoneVolume(ctx, args.task.ProductId, line.ResourceId, deliverZone, nil)
		}
	}
}

// 获取筛选routing rule的参数
func getMatchRoutingRuleArgs(ctx context.Context, originOrder *routing_log.RoutingLog) (*pb.CreateOrderRequestData, error) {
	orderData := &pb.CreateOrderRequestData{}
	if err := jsoniter.UnmarshalFromString(originOrder.OrderData, orderData); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal source order failed %+v", err)
		return nil, err
	}

	return orderData, nil
}

func checkHardCriteriaLane(ctx context.Context, originOrder *routing_log.RoutingLog, resultCounter *volume_counter.AsyncCounter) bool {
	if len(originOrder.HCResult) == 0 {
		logger.CtxLogErrorf(ctx, "hard result is empty")
		resultCounter.Incr(forecast.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
		return false
	}
	return true
}

// 调度结果处理
func (f *Forecast) processRoutingResult(ctx context.Context, taskId int, resultCounter *volume_counter.AsyncCounter, resultLog *routing_log.RoutingLog,
	resultLaneInfo *rule.RoutingLaneInfo, orderInfo *routing_log.RoutingLog, routingRule *rule.RoutingRuleParsed) {
	//计算运费
	shippingFee := select_lane.GetShippingFee(resultLog, resultLaneInfo)
	//从结果中相应的site id
	sites, _ := f.getSites(ctx, taskId, orderInfo.HardCriteriaResult, resultLaneInfo)
	outActualPointId := getActualPointId(resultLaneInfo)
	//保存单量结果信息到map
	resultCounter.IncrLocal(resultLaneInfo.LaneCode, sites, orderInfo.ExtraInfo.BuyerCityId, shippingFee, orderInfo.ExtraInfo.Weight, int(routingRule.ID), routingRule.Priority, outActualPointId)
}

func forecastedOrderCountKey(taskId int) string {
	return fmt.Sprintf("%s_%d", forecast.ForecastedOrderCount, taskId)
}

// 获取routing结果的site信息
func (f *Forecast) getSites(ctx context.Context, taskId int, hcResult []*pb.LaneServiceable, result *rule.RoutingLaneInfo) (string, string) {
	sites := []string{}
	actpList := []string{}
	siteActpMap := map[string][]string{}
	for _, laneInfo := range hcResult {
		if laneInfo.GetLaneCode() == result.LaneCode {
			for _, recourse := range laneInfo.GetResourceServiceableCollection() {
				if recourse.GetResourceType() == lfslib.ResourceTypeSite {
					sites = append(sites, recourse.GetResourceId())
					siteActpMap[recourse.GetResourceId()] = []string{}
					for _, point := range recourse.GetActualPoint() {
						siteActpMap[recourse.GetResourceId()] = append(siteActpMap[recourse.GetResourceId()], point.GetPointId())
					}
					break
				}
			}
			break
		}
	}
	if len(sites) == 0 {
		laneInfo, err := f.laneSrv.GetLaneInfoByLaneCode(ctx, result.LaneCode)
		if err != nil {
			ForecastReportByMonitorKey(ctx, taskId, ForecastResult, fmt.Sprintf("search lane code failed %v", err), Failed)
			logger.CtxLogErrorf(ctx, "search lane code failed %v", err)
			return "", ""
		}
		sites = laneInfo.GetAllJoinSiteID()
	}
	//这里对site排序是为了保证相同的结果顺序一致，的到的字符串也是一致的
	sort.Strings(sites)

	for _, site := range sites {
		actps := siteActpMap[site]
		sort.Strings(actps) //排序保证相同的actp数组生成的actual point字符串顺序是一致的
		actpList = append(actpList, strings.Join(actps, "_"))
	}
	return strings.Join(sites, "#"), strings.Join(actpList, "#")
}

// 预测只需要outActualPoint
func getActualPointId(resultLaneInfo *rule.RoutingLaneInfo) string {
	actualPointId := ""
	for _, point := range resultLaneInfo.ActualPointList {
		if point.PointType == lfslib.ActualPointTypeOut {
			actualPointId = point.PointId
		}
	}
	return actualPointId
}

func (f *Forecast) getZoneName(ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, postcode string) string {
	var zoneName string
	switch groupInfo.ZoneType {
	case enum.ZoneTypeCEPRange:
		zoneName, _ = f.VolumeZoneRepo.GetCepRangeZoneName(ctx, groupInfo.GroupId, postcode, groupInfo.RoutingType, groupInfo.IsForecastType)
	}
	return zoneName
}
