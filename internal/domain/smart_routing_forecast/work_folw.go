package smart_routing_forecast

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	forecast_const "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	jsoniter "github.com/json-iterator/go"
	"time"
)

type LocalForecast interface {
	Work(ctx context.Context, args *forecastProcessArgs) error
}

type InitForecastArgs struct {
}

func (i *InitForecastArgs) Work(ctx context.Context, args *forecastProcessArgs) error {
	// 对预测做一些初始化
	if err := processTask(args.task); err != nil {
		ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("process task failed %v", err), Failed)
		logger.CtxLogErrorf(ctx, "process task failed %v", err)
		return err
	}
	//解析对应的预测规则
	ruleList := parseTaskRules(ctx, args.task)
	//初始化预测相关参数
	err := initForecastArgsAndFactors(ctx, args, args.task, ruleList)
	if err != nil {
		ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("init task args failed %v", err), Failed)
		return err
	}
	return nil
}

type VerifyArgs struct {
}

func (i *VerifyArgs) Work(ctx context.Context, args *forecastProcessArgs) error {
	if err := verificationSampleDate(args.task.StartDate, args.task.EndDate); err != nil {
		ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("verification date failed %v", err), Failed)
		logger.CtxLogErrorf(ctx, "get and verification date failed %v", err)
		return err
	}
	return nil
}

type Forecast struct {
	RoutingRuleRepo routing.RoutingRuleRepo
	RoutingSrv      routing.RoutingService
	ZoneRepo        locationzone.ZoneRepo
	ZoneGroupRepo   vrrepo.ZoneGroupRepo
	VolumeZoneRepo  vrrepo.ZoneRepo
	laneSrv         lane.LaneService
}

func (f *Forecast) Work(ctx context.Context, args *forecastProcessArgs) error {
	defer func() {
		args.decoder.Close()
		args.pool.Release()
	}()
	for _, simulationOrderCount := range args.simulationDateList {
		// 设置采样比
		if err := args.samplePool.SetSamplingRate(ctx, simulationOrderCount.Count); err != nil {
			ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("SetSamplingRate failed %v", err), Failed)
			logger.CtxLogErrorf(ctx, "SetSamplingRate failed %v", err)
			return err
		}
		if !configutil.GetDataApiSwitchConf(ctx).ReadOrderFromDataApiSwitch {
			if err := f.forecastLocalByDay(ctx, args, simulationOrderCount.Day, simulationOrderCount.Count); err != nil {
				logger.CtxLogErrorf(ctx, "Forecast failed taskId is %d day is %d,err is %v", args.task.Id, simulationOrderCount.Day, err)
				return err
			}
		} else {
			if err := f.forecastLocalByDayV2(ctx, args, simulationOrderCount.Day, simulationOrderCount.Count); err != nil {
				logger.CtxLogErrorf(ctx, "V2 Forecast failed taskId is %d day is %d,err is %v", args.task.Id, simulationOrderCount.Day, err)
				return err
			}
		}
	}

	return nil
}

// 按照天维度做预测
func (f *Forecast) forecastLocalByDay(ctx context.Context, args *forecastProcessArgs, currentDay, orderCount int) *srerr.Error {
	//通过channel读取数据
	orderLogs := args.samplePool.ReadOrderFromHbase(ctx, args, orderCount)
	requestId := requestid.GetFromCtx(ctx)
	for orderBytes := range orderLogs {
		order := decompressRoutingLog(ctx, args, orderBytes)
		if order == nil {
			continue
		}
		orderCtx := newCtx(ctx, requestId, order.FOrderId, args.task.Id)
		originOrder := order
		fErr := args.pool.Submit(func() {
			defer args.waitGroup.Done()
			args.waitGroup.Add(1)
			//记录一下已经预测的单量
			recordForecastedOrderCount(orderCtx, args.task.Id)
			//检查硬性校验lane是否有剩余的lane
			if !checkHardCriteriaLane(orderCtx, originOrder, args.resultCounter) {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "hard criteria empty", Failed)
				return
			}
			//更新line相关信息包括映射关系以及dg信息
			roleMap := updateLineInfo(orderCtx, args.task, originOrder)
			//匹配routing rule
			routingRule := f.matchRoutingRule(orderCtx, originOrder, args.task, args.ruleList, args.resultCounter)
			if routingRule == nil {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "rule block", Failed)
				return
			}
			//做一下限流处理
			_ = args.rateLimit.Take()
			// routing
			resultLaneInfo, resultLog := f.startRouting(orderCtx, args.task, routingRule, originOrder, args.resultCounter, roleMap)
			if resultLaneInfo == nil || resultLog == nil {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "routing failed", Failed)
				return
			}

			//处理routing结果信息并记录
			f.processRoutingResult(ctx, args.task.Id, args.resultCounter, resultLog, resultLaneInfo, originOrder, routingRule)

			//记录对应的单量信息
			postcode := order.ExtraInfo.SmartRoutingData.DeliveryPostCode
			f.recordVolume(ctx, args.volumeCounter, resultLaneInfo, args.task.VolumeRuleList, resultLog, args.task.ProductId, postcode, int(args.task.RoutingType))
			f.recordCapacity(ctx, args, resultLaneInfo, originOrder)
			ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "routing success", Success)
			logger.CtxLogDebugf(ctx, "routing log: %s", str.JsonStringForDebugLog(resultLog))
		})
		if fErr != nil {
			ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("Forecast failed %v", fErr), Failed)
			logger.CtxLogDebugf(ctx, "Forecast failed %v", fErr)
		}
	}
	args.waitGroup.Wait()
	// 这里睡100ms，防止通道里面的数据没有写入map中导致记录出现差异
	time.Sleep(100 * time.Millisecond)
	result := args.resultCounter.GetResultByDay()
	// 上报每天统计结果
	ReportForecastStatisticsResult(ctx, args.task.Id, currentDay, result)
	if err := f.saveResult(ctx, args.task.Id, currentDay, result); err != nil {
		logger.CtxLogErrorf(ctx, "SaveResult fail|err=%v", err)
		return err
	}

	return nil
}

func (f *Forecast) forecastLocalByDayV2(ctx context.Context, args *forecastProcessArgs, currentDay, orderCount int) *srerr.Error {
	//通过channel读取数据
	orderLogs := args.samplePool.ReadOrderFromDataApi(ctx, args, orderCount)
	requestId := requestid.GetFromCtx(ctx)
	for order := range orderLogs {
		orderCtx := newCtx(ctx, requestId, order.FOrderId, args.task.Id)
		originOrder := order
		fErr := args.pool.Submit(func() {
			_, endFunc := monitor.AwesomeReportTransactionStart2(orderCtx)
			defer args.waitGroup.Done()
			defer endFunc(ForecastSpeed, SingleOrder, "0", "")
			args.waitGroup.Add(1)
			//记录一下已经预测的单量
			recordForecastedOrderCount(orderCtx, args.task.Id)
			//检查硬性校验lane是否有剩余的lane
			if !checkHardCriteriaLane(orderCtx, originOrder, args.resultCounter) {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "hard criteria empty", Failed)
				return
			}
			//更新line相关信息包括映射关系以及dg信息
			roleMap := updateLineInfo(orderCtx, args.task, originOrder)
			//匹配routing rule
			routingRule := f.matchRoutingRule(orderCtx, originOrder, args.task, args.ruleList, args.resultCounter)
			if routingRule == nil {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "rule block", Failed)
				return
			}
			//做一下限流处理
			_ = args.rateLimit.Take()
			// routing
			resultLaneInfo, resultLog := f.startRouting(orderCtx, args.task, routingRule, originOrder, args.resultCounter, roleMap)
			if resultLaneInfo == nil || resultLog == nil {
				ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "routing failed", Failed)
				return
			}
			//处理routing结果信息并记录
			f.processRoutingResult(ctx, args.task.Id, args.resultCounter, resultLog, resultLaneInfo, originOrder, routingRule)
			//记录对应的单量信息
			postcode := order.ExtraInfo.SmartRoutingData.DeliveryPostCode
			f.recordVolume(ctx, args.volumeCounter, resultLaneInfo, args.task.VolumeRuleList, resultLog, args.task.ProductId, postcode, int(args.task.RoutingType))
			f.recordCapacity(ctx, args, resultLaneInfo, originOrder)
			ForecastReportByMonitorKey(ctx, args.task.Id, ForecastsProcess, "routing success", Success)
			logger.CtxLogDebugf(ctx, "routing log: %s", str.JsonStringForDebugLog(resultLog))
		})
		if fErr != nil {
			ForecastReportByMonitorKey(ctx, args.task.Id, InitForecast, fmt.Sprintf("Forecast failed %v", fErr), Failed)
			logger.CtxLogDebugf(ctx, "Forecast failed %v", fErr)
		}
	}
	args.waitGroup.Wait()
	// 这里睡100ms，防止通道里面的数据没有写入map中导致记录出现差异
	time.Sleep(100 * time.Millisecond)
	result := args.resultCounter.GetResultByDay()
	// 上报每天统计结果
	ReportForecastStatisticsResult(ctx, args.task.Id, currentDay, result)
	if err := f.saveResult(ctx, args.task.Id, currentDay, result); err != nil {
		logger.CtxLogErrorf(ctx, "SaveResult fail|err=%v", err)
		return err
	}

	return nil
}

// 解压routing log
func decompressRoutingLog(ctx context.Context, args *forecastProcessArgs, result *gohbase.Result) *routing_log.RoutingLog {
	log := routing_log.RoutingLog{}
	logBytes, err := args.decoder.DecodeAll(result.Cells[0].Value, nil)
	if err != nil {
		logger.CtxLogErrorf(ctx, "decode routing log failed%v", err)
		return nil
	}

	if err := jsoniter.Unmarshal(logBytes, &log); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal routing log failed:%v", err)
		return nil
	}
	return &log
}

// 匹配对应的预测规则
func (f *Forecast) matchRoutingRule(ctx context.Context, originOrder *routing_log.RoutingLog, task *persistent.ForecastingTaskTab, ruleList []*ruledata.RoutingRuleTab, resultCounter *volume_counter.AsyncCounter) *rule.RoutingRuleParsed {
	//获取match rule的入参
	orderData, argErr := getMatchRoutingRuleArgs(ctx, originOrder)
	if argErr != nil { //这里的error信息已经在getMatchRoutingRuleArgs中打印了
		return nil
	}
	//获取筛选规则
	matchParam, err := formatRuleMatchParam(ctx, task, &forecastentity.LFSOrderInfo{
		OrderSN:          orderData.GetBaseInfo().GetOrdersn(),
		ForderId:         originOrder.FOrderId,
		WhsCode:          orderData.GetBaseInfo().GetWhsId(),
		LocationIdList:   originOrder.ExtraInfo.DeliveryLocationIdList,
		DeliverPostCode:  originOrder.ExtraInfo.SmartRoutingData.DeliveryPostCode,
		Cogs:             orderData.ForderInfo.GetCogs(),
		ValidationWeight: int(originOrder.ExtraInfo.ValidationWeight),
		Skus:             orderData.Skus,
		ShopId:           orderData.GetForderInfo().GetShopId(),
		LineAsfMap:       originOrder.LineAsfMap,
	}, rule.UndefinedDGFlag)
	if err != nil {
		logger.CtxLogErrorf(ctx, "formatRuleMatchParam fail|err=%v", err)
		resultCounter.Incr(forecast_const.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
		return nil
	}
	//匹配具体的routing rule
	routingRule, ruleErr := f.RoutingRuleRepo.MatchFirstPriorityRoutingRuleByRuleList(ctx, ruleList, matchParam)
	if ruleErr != nil || routingRule == nil {
		logger.CtxLogErrorf(ctx, "MatchFirstPriorityRoutingRuleByRuleList fail|err=%v", ruleErr)
		resultCounter.Incr(forecast_const.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
		return nil
	}

	return routingRule
}

// 保存结果
func (f *Forecast) saveResult(ctx context.Context, taskID, day int, result map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult) *srerr.Error {
	forecastResult := getForecastResult(ctx, taskID, day, result)

	if err := dbutil.InsertBatch(ctx, persistent.ForecastingTaskResultHook, forecastResult); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

type ClearRedis struct {
}

// 清除redis缓存
func (p *ClearRedis) Work(ctx context.Context, args *forecastProcessArgs) error {
	clearLastForecastedInfo(ctx, args.task.Id)
	return nil
}

// 按照天维度上报统计结果
func ReportForecastStatisticsResult(ctx context.Context, taskId, day int, statisticsResult map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult) {
	statResult := &StatisticsResultByDay{}
	for info, forecastResult := range statisticsResult {
		if info.LaneCode == forecast_const.RoutingFailed {
			statResult.RoutingFailed += forecastResult.Quantity
		} else if info.LaneCode == forecast_const.RuleBlocked || info.LaneCode == forecast_const.Blocked {
			statResult.Block += forecastResult.Quantity
		} else {
			statResult.RoutingSuccess += forecastResult.Quantity
		}
	}
	ForecastReportByMonitorKey(ctx, taskId, ForecastStatisticsResult, fmt.Sprintf("Statistics Result %s", objutil.JsonString(statResult)), Success)
}
