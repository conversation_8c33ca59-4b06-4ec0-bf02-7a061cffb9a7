package smart_routing_forecast

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/sync_lfs_order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	maxDays         = 31
	maxForecastDays = 15
	defaultDbFlag   = int(forecastservice.NoneDG)
)

var dgLineList = []int32{lfslib.C_FL, lfslib.C_ILH, lfslib.C_XAndT_ILH, lfslib.L_FM, lfslib.L_LM}

type SmartRoutingForecastService interface {
	StartForecast(ctx context.Context, task *persistent.ForecastingTaskTab) *srerr.Error
	DebugStartForecast(ctx context.Context, req schema.DebugSmartRoutingForecastReq) *srerr.Error
	StartILHForecast(ctx context.Context, task ILHForecastTask) *srerr.Error
}

type SmartRoutingForecastServiceImpl struct {
	ForecastRepo                forecastrepo.IForecastRepo
	OrderSyncRepo               sync_lfs_order.OrderSyncRepo
	RoutingRuleRepo             routing.RoutingRuleRepo
	RoutingSrv                  routing.RoutingService
	laneSrv                     lane.LaneService
	ZoneRepo                    locationzone.ZoneRepo
	LfsApi                      lfsclient.LfsApi
	LpsApi                      lpsclient.LpsApi
	ForecastTaskService         forecastservice.IForecastTaskService
	SoftRuleRepo                ruledata.SoftRuleRepo
	ZoneRuleRepo                vrrepo.ZoneRuleRepo
	ZoneGroupRepo               vrrepo.ZoneGroupRepo
	VolumeZoneRepo              vrrepo.ZoneRepo
	ILHForecastTaskService      ilh_forecast_task.ILHForecastTaskService
	AvailableLHService          available_lh.AvailableLHService
	LHCapacityService           lh_capacity.LHCapacityService
	ParcelTypeDefinitionService parcel_type_definition2.ParcelTypeDefinitionService
}

func NewSmartRoutingForecastServiceImpl(
	forecastRepo forecastrepo.IForecastRepo,
	orderSyncRepo sync_lfs_order.OrderSyncRepo,
	routingRuleRepo routing.RoutingRuleRepo,
	routingSrv routing.RoutingService,
	laneSrv lane.LaneService,
	zoneRepo locationzone.ZoneRepo,
	lfsApi lfsclient.LfsApi,
	lpsApi lpsclient.LpsApi,
	forecastTaskService forecastservice.IForecastTaskService,
	softRuleRepo ruledata.SoftRuleRepo,
	ZoneRuleRepo vrrepo.ZoneRuleRepo,
	ZoneGroupRepo vrrepo.ZoneGroupRepo,
	VolumeZoneRepo vrrepo.ZoneRepo,
	ilhForecastTaskService ilh_forecast_task.ILHForecastTaskService,
	availableLHService available_lh.AvailableLHService,
	lhCapacityService lh_capacity.LHCapacityService,
	ParcelTypeDefinitionService parcel_type_definition2.ParcelTypeDefinitionService,
) *SmartRoutingForecastServiceImpl {
	return &SmartRoutingForecastServiceImpl{
		ForecastRepo:                forecastRepo,
		OrderSyncRepo:               orderSyncRepo,
		RoutingRuleRepo:             routingRuleRepo,
		RoutingSrv:                  routingSrv,
		laneSrv:                     laneSrv,
		ZoneRepo:                    zoneRepo,
		LfsApi:                      lfsApi,
		LpsApi:                      lpsApi,
		ForecastTaskService:         forecastTaskService,
		SoftRuleRepo:                softRuleRepo,
		ZoneRuleRepo:                ZoneRuleRepo,
		ZoneGroupRepo:               ZoneGroupRepo,
		VolumeZoneRepo:              VolumeZoneRepo,
		ILHForecastTaskService:      ilhForecastTaskService,
		AvailableLHService:          availableLHService,
		LHCapacityService:           lhCapacityService,
		ParcelTypeDefinitionService: ParcelTypeDefinitionService,
	}
}

func (s *SmartRoutingForecastServiceImpl) StartForecast(ctx context.Context, task *persistent.ForecastingTaskTab) *srerr.Error {
	days, daysErr := getDays(task.StartDate, task.EndDate)
	if daysErr != nil {
		logger.CtxLogErrorf(ctx, "getDays fail|err=%+v", daysErr)
		return srerr.With(srerr.DataErr, task, daysErr)
	}

	if len(days) > maxDays {
		return srerr.New(srerr.ParamErr, nil, "too large time range %v - %v", task.StartDate, task.EndDate)
	}

	routingRuleList := parseTaskRules(ctx, task)

	resultCounter := volume_counter.NewAsyncCounter()
	forecastCounter := volume_counter.NewForecastVolumeCounterImpl()
	volumeCounter := vrservice.NewForecastVolumeRouting()
	initRoutingFactors(forecastCounter, volumeCounter)
	for _, dayTime := range days {
		if task.RoutingType == rule.IlhRoutingType {
			forecastCounter.SetTwsCutoffMap(configutil.GetTwsCutoffTimeWithTimezone(ctx))
			if err := s.ilhForecastByDay(ctx, task, routingRuleList, dayTime, forecastCounter, resultCounter); err != nil {
				return err
			}
		} else {
			if err := s.cbForecastByDay(ctx, task, routingRuleList, dayTime, forecastCounter, resultCounter, volumeCounter); err != nil {
				return err
			}
			forecastCounter.Clear()
		}
	}
	forecastCounter.Clear()
	cbDefaultDay := -1
	result := resultCounter.GetResult()
	logger.CtxLogInfof(ctx, "result.len = %v", len(result))
	if err := s.SaveResult(ctx, task.Id, cbDefaultDay, result); err != nil {
		logger.CtxLogErrorf(ctx, "SaveResult fail|err=%v", err)
		return err
	}

	return nil
}

// DebugStartForecast 内部方法 屏蔽所有写操作
func (s *SmartRoutingForecastServiceImpl) DebugStartForecast(ctx context.Context, req schema.DebugSmartRoutingForecastReq) *srerr.Error {
	task := &persistent.ForecastingTaskTab{
		ProductId:        req.ProductId,
		StartDate:        req.StartDate,
		EndDate:          req.EndDate,
		RoutingType:      req.RoutingType,
		IsMultiProduct:   req.IsMultiProduct,
		Id:               req.TaskId,
		ShipmentResource: persistent.ShipmentResource(req.ShipmentResource),
	}

	days, daysErr := getDays(task.StartDate, task.EndDate)
	if daysErr != nil {
		logger.CtxLogErrorf(ctx, "getDays fail|err=%+v", daysErr)
		monitoring.ReportError(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, daysErr.Error())
		return srerr.With(srerr.DataErr, task, daysErr)
	}

	if len(days) > maxDays {
		monitoring.ReportError(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, "too large time range")
		return srerr.New(srerr.ParamErr, nil, "too large time range %v - %v", task.StartDate, task.EndDate)
	}

	// 直接用线上的Rule来做预测
	routingRuleList, err := s.SoftRuleRepo.GetActiveRuleListByProduct(ctx, req.ProductId, req.RoutingType, req.IsMultiProduct)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, err.Error())
		return err
	}

	resultCounter := volume_counter.NewAsyncCounter()
	forecastCounter := volume_counter.NewForecastVolumeCounterImpl()
	volumeCounter := vrservice.NewForecastVolumeRouting()
	initRoutingFactors(forecastCounter, volumeCounter)
	for _, dayTime := range days {
		if task.RoutingType == rule.IlhRoutingType {
			forecastCounter.SetTwsCutoffMap(configutil.GetTwsCutoffTimeWithTimezone(ctx))
			if err := s.ilhForecastByDay(ctx, task, routingRuleList, dayTime, forecastCounter, resultCounter); err != nil {
				monitoring.ReportError(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, err.Error())
				return err
			}
		} else {
			if err := s.cbForecastByDay(ctx, task, routingRuleList, dayTime, forecastCounter, resultCounter, volumeCounter); err != nil {
				monitoring.ReportError(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, err.Error())
				return err
			}
			forecastCounter.Clear()
		}
	}
	forecastCounter.Clear()

	logger.CtxLogInfof(ctx, "forecast result = %s", objutil.JsonString(getForecastResult(ctx, 0, 0, resultCounter.GetResult())))
	monitoring.ReportSuccess(ctx, monitoring.CatModuleDebugSmartRoutingForecast, monitoring.DebugForecastSuccess, "")

	return nil
}

func (s *SmartRoutingForecastServiceImpl) cbForecastByDay(ctx context.Context, task *persistent.ForecastingTaskTab, ruleList []*ruledata.RoutingRuleTab, day time.Time, volumeCounter volume_counter.VolumeCounter, resultCounter *volume_counter.AsyncCounter, volumeRoutingSrv vrservice.Service) *srerr.Error {
	// get order
	orders, err := s.readForecastOrders(ctx, task.ProductId, task.IsMultiProduct, day)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ReadForecastOrderIteration fail|err=%v", err)
		return err
	}

	requestId := requestid.GetFromCtx(ctx)
	for order := range orders {
		newRequestID := fmt.Sprintf("%s|%s", requestId, order.OrderSN)
		orderCtx := logger.NewLogContext(ctx, newRequestID)
		orderCtx = requestid.SetToCtx(orderCtx, newRequestID)

		laneList := order.HardResult
		if len(laneList) == 0 {
			logger.CtxLogErrorf(orderCtx, "hard result is empty")
			resultCounter.Incr(forecast.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
			continue
		}

		dgType := rule.UndefinedDGFlag
		roleMap := routing_role.UpdateLineResourceType(ctx, task.ProductId, task.RoutingType, laneList, task.IsMultiProduct, nil)
		for _, availableLane := range laneList {
			for _, lineInfo := range availableLane.LineList {
				lineInfo.LineId = lineInfo.ResourceId
				if !objutil.ContainInt32(dgLineList, lineInfo.RealResourceSubType) {
					lineInfo.DGFlag = rule.UndefinedDGFlag
				}
				if s.laneSrv.IsResourceNeedDg(ctx, availableLane.LaneCode, availableLane.LaneCodeGroup, uint32(lineInfo.RealResourceSubType)) &&
					dgType != rule.DG {
					if lineInfo.DGFlag == rule.DG {
						dgType = rule.DG
					} else if lineInfo.DGFlag == rule.NonDG {
						dgType = rule.NonDG
					}
				}
			}

		}

		logger.CtxLogInfof(ctx, "OrderSN: %s, dgType: %v", order.OrderSN, dgType)
		// match rule
		matchParam, err := formatRuleMatchParam(ctx, task, order, dgType)
		if err != nil {
			logger.CtxLogErrorf(orderCtx, "formatRuleMatchParam fail|err=%v", err)
			resultCounter.Incr(forecast.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
			continue
		}

		routingRule, err := s.RoutingRuleRepo.MatchFirstPriorityRoutingRuleByRuleList(orderCtx, ruleList, matchParam)
		if err != nil {
			logger.CtxLogErrorf(orderCtx, "MatchFirstPriorityRoutingRuleByRuleList fail|err=%v", err)
			resultCounter.Incr(forecast.RuleBlocked, defaultDbFlag, 0, "", 0, 0, false)
			continue
		}

		// routing
		logEntry := new(routing_log.RoutingLog)
		setupRoutingLogBasic(logEntry, routingRule, roleMap)
		resultLanes, err := s.RoutingSrv.Routing(orderCtx, task.ProductId, task.IsMultiProduct, laneList, routingRule, &rule.SmartRoutingOrderData{DeliveryLocationIds: objutil.UintToInt(order.LocationIdList)}, logEntry, false)
		if err != nil {
			logger.CtxLogErrorf(orderCtx, "Routing fail|err=%v", err)
			resultCounter.Incr(forecast.Blocked, defaultDbFlag, int(routingRule.ID), "", 0, 0, false)
			continue
		}
		resultLane := resultLanes[0]
		lineParcelMap := make(map[string]*parcel_type_definition2.ParcelTypeAttr)
		for _, lineInfo := range resultLane.LineList {
			parcelTypeAttr := volumerouting.GetParcelTypeAttr(ctx, int64(task.ProductId), lineInfo.LineId,
				order.IsCod, rule.OrderParcelDimension{Length: order.ParcelLength, Width: order.ParcelWidth,
					Height: order.ParcelHeight, Weight: order.ParcelWeight}, float64(order.Cogs),
				lineInfo.DGFlag, s.ParcelTypeDefinitionService, rule.CbMultiRoutingType)
			lineParcelMap[lineInfo.LineId] = parcelTypeAttr
		}
		// 3. incr volumeCounter
		s.updateVolumeForOld(ctx, order, task, resultLane, volumeCounter, lineParcelMap)
		// 4. 更新v2运力
		s.updateVolumeForNew(ctx, int64(task.ProductId), order, task.RoutingType, routingRule.VolumeRuleId, resultLane, volumeRoutingSrv, lineParcelMap)
		dgFlag := defaultDbFlag
		for _, line := range resultLane.LineList {
			if objutil.ContainInt32(dgLineList, line.RealResourceSubType) {
				dgFlag = int(line.DGFlag)
			}
		}

		asfInfo := getCbLmAsf(resultLane, order.LineAsfMap)
		resultCounter.Incr(resultLane.LaneCode, dgFlag, int(routingRule.ID), resultLane.ServiceCode, asfInfo.Asf, asfInfo.AsfUsd, asfInfo.HasAsf)

		setupRoutingLogResult(logEntry, resultLane, int32(dgFlag))
		logEntry.FOrderId = order.ForderId
		logEntry.LineAsfMap = order.LineAsfMap
		logger.CtxLogDebugf(ctx, "routing log: %s", str.JsonStringForDebugLog(logEntry))
	}

	return nil
}

func getCbLmAsf(laneInfo *rule.RoutingLaneInfo, lineAsfInfoMap map[string]forecastentity.AsfInfo) forecastentity.AsfInfo {
	var cbLmId string
	for _, line := range laneInfo.LineList {
		if objutil.ContainInt(lfslib.CBLMLine, int(line.ResourceSubType)) {
			cbLmId = line.ResourceId
			break
		}
	}

	if cbLmId == "" {
		return forecastentity.AsfInfo{}
	}

	asfInfo, ok := lineAsfInfoMap[cbLmId]
	if !ok {
		return forecastentity.AsfInfo{}
	}

	return asfInfo
}

func (s *SmartRoutingForecastServiceImpl) loadDgGroupMapByProduct(ctx context.Context, productId int) (map[string]string, *srerr.Error) {
	dgGroupMap, err := s.RoutingRuleRepo.GetMultiProductDgGroup(ctx, productId)
	if err != nil {
		return nil, err
	}
	lineToDgGroupMap := make(map[string]string)
	for dgGroupId, dgGroup := range dgGroupMap {
		for _, lineId := range dgGroup.LineList {
			lineToDgGroupMap[lineId] = dgGroupId
		}
	}

	return lineToDgGroupMap, nil
}

func (s *SmartRoutingForecastServiceImpl) ilhForecastByDay(ctx context.Context, task *persistent.ForecastingTaskTab, ruleList []*ruledata.RoutingRuleTab, day time.Time, forecastCounter *volume_counter.ForecastVolumeCounterImpl, resultCounter *volume_counter.AsyncCounter) *srerr.Error {
	// get order
	orders, err := s.ReadILHForecastOrder(ctx, task, day)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ReadForecastOrderIteration fail|err=%v", err)
		return err
	}

	sortingCodeResp, err := s.LfsApi.ListSortingCode(ctx, &lfsentity.ListSortingCodeRequest{ProductId: strconv.Itoa(task.ProductId)})
	if err != nil {
		return err
	}

	sortingCodeToInfoMap := make(map[string]*lfsentity.SortingCodeInfo)
	keyToSortingCodeInfoMap := make(map[string]*lfsentity.SortingCodeInfo)
	for _, s := range sortingCodeResp.Data.List {
		sortingCodeToInfoMap[s.ServiceCode] = s
		if s.CodeStatus == lfsentity.CodeStatusEnable {
			keyToSortingCodeInfoMap[formatSortingCodeKey(s.DgGroup, s.LmId, s.DgFlag, s.ActualPointId)] = s
		}
	}

	requestId := requestid.GetFromCtx(ctx)
	for _, order := range orders {
		newRequestID := fmt.Sprintf("%s|%s", requestId, order.LhsTn)
		orderCtx := logger.NewLogContext(ctx, newRequestID)
		orderCtx = requestid.SetToCtx(orderCtx, newRequestID)

		logEntry := &routing_log.RoutingLog{
			ProductId: task.ProductId,
			FOrderId:  order.LhsTn,
		}

		forecastCounter.SetOrderTime(order.Ctime)
		// 获取当前订单归属到哪个tws统计周期
		twsStatDate := forecastCounter.GetDateByTwsCode(ctx, order.TwsCode).Format(constant.TimeLayout)

		parcelQuantity := order.ParcelQuantity
		routingLaneInfos := order.AvailableLanes
		if len(routingLaneInfos) == 0 {
			resultCounter.IlhIncr(forecast.Blocked, defaultDbFlag, 0, "", order.ActualWeight, parcelQuantity, twsStatDate)
			continue
		}

		roleMap := routing_role.UpdateLineResourceType(orderCtx, task.ProductId, rule.IlhRoutingType, routingLaneInfos, true, nil)

		destinationPorts := make([]string, 0, len(routingLaneInfos))
		for _, routingLaneInfo := range routingLaneInfos {
			destinationPorts = append(destinationPorts, routingLaneInfo.DestinationPort)
		}
		destinationPorts = objutil.RemoveDuplicateString(destinationPorts)

		// 2 match routing rules
		ruleParam := routing.RuleMatchParam{
			ProductId:        int64(task.ProductId),
			WhsID:            order.TwsCode,
			DgType:           order.DgType,
			DestinationPorts: destinationPorts,
			RoutingType:      rule.IlhRoutingType,
			IsMultiProduct:   true,
			ZoneType:         locationzone.ForecastingZoneType,
		}

		routingRules, err := s.RoutingRuleRepo.MatchRoutingRulesByRuleList(orderCtx, ruleList, ruleParam)
		if err != nil {
			logger.CtxLogErrorf(orderCtx, "MatchRoutingRulesByRuleList fail|err=%v", err)
			resultCounter.IlhIncr(forecast.Blocked, defaultDbFlag, 0, "", order.ActualWeight, parcelQuantity, twsStatDate)
			continue
		}

		orderData := &rule.SmartRoutingOrderData{
			TwsCode:        order.TwsCode,
			OrderWeight:    order.ActualWeight,
			ParcelQuantity: parcelQuantity,
			LmId:           order.LmId,
			CCMode:         pb.CCMode_CCRouting,
		}

		var routingResult []*rule.RoutingLaneInfo
		var routingErr *srerr.Error
		var ruleId int
		for _, routingRule := range routingRules {
			// 3.1 get intersection lanes by destination ports
			ruleRoutingLanes := select_lane.FilterLanesByDestPort(routingLaneInfos, routingRule.DestinationPort)
			if len(ruleRoutingLanes) == 0 {
				logger.CtxLogErrorf(orderCtx, "Rule [%d] have no destination port intersection", routingRule.ID)
				continue
			}

			// 3.2 routing
			setupRoutingLogBasic(logEntry, routingRule, roleMap)
			if routingRule.IsCCRoutingMode() {
				routingResult, routingErr = s.RoutingSrv.CCModeILHRouting(orderCtx, task.ProductId, ruleRoutingLanes, routingRule, orderData, logEntry)
			} else {
				notOnlySPX := false
				routingResult, routingErr = s.RoutingSrv.Routing(orderCtx, task.ProductId, true, ruleRoutingLanes, routingRule, orderData, logEntry, notOnlySPX)
			}
			if routingErr != nil {
				logger.CtxLogErrorf(orderCtx, "Routing fail using rule [%d], routing lanes [%s]", routingRule.ID, str.JsonString(ruleRoutingLanes))
				continue
			}

			// break when get routing result
			if len(routingResult) != 0 {
				ruleId = int(routingRule.ID)
				logger.CtxLogInfof(orderCtx, "Routing success by rule [%d], routing result [%s]", routingRule.ID, routingResult[0].LaneCode)
				break
			}
		}

		if len(routingResult) != 0 {
			var newSortingCode string
			oldSortingCodeInfo, exist1 := sortingCodeToInfoMap[order.ServiceCode]
			if exist1 {
				newSortingCodeInfo, exist2 := keyToSortingCodeInfoMap[formatSortingCodeKey(routingResult[0].DgGroupId, oldSortingCodeInfo.LmId, int32(order.DgType), oldSortingCodeInfo.ActualPointId)]
				if exist2 {
					newSortingCode = newSortingCodeInfo.ServiceCode
				}
			}

			setupRoutingLogResult(logEntry, routingResult[0], int32(order.DgType))
			resultCounter.IlhIncr(routingResult[0].LaneCode, order.DgType, ruleId, newSortingCode, order.ActualWeight, parcelQuantity, twsStatDate)

			var ilh, importIlh string
			for _, line := range routingResult[0].LineList {
				if !line.NeedRouting() {
					// 不需要调度不需要进行统计
					continue
				}
				if line.IsDgRelated() {
					ilh = line.LineId
				} else {
					importIlh = line.LineId
				}
				_ = forecastCounter.IncrILHCartonVolume(orderCtx, task.ProductId, line.ResourceId, order.DgType, order.TwsCode, routingResult[0].DestinationPort, int64(order.Ctime))
				_ = forecastCounter.IncrILHWeight(orderCtx, task.ProductId, line.ResourceId, order.DgType, order.TwsCode, routingResult[0].DestinationPort, int64(order.ActualWeight), int64(order.Ctime))
				_ = forecastCounter.IncrILHParcelVolume(orderCtx, task.ProductId, line.ResourceId, order.DgType, order.TwsCode, routingResult[0].DestinationPort, int64(parcelQuantity), int64(order.Ctime))
			}
			_ = forecastCounter.IncrILHCombinationStat(orderCtx, task.ProductId, ilh, importIlh, order.LmId, order.TwsCode, routingResult[0].DestinationPort, int64(parcelQuantity), int64(order.ActualWeight), int64(order.Ctime))
		} else {
			resultCounter.IlhIncr(forecast.Blocked, defaultDbFlag, 0, "", order.ActualWeight, parcelQuantity, twsStatDate)
		}

		logger.CtxLogInfof(orderCtx, "Routing Log: %s", str.JsonString(logEntry))
	}

	return nil
}

func parseTaskRules(ctx context.Context, task *persistent.ForecastingTaskTab) []*ruledata.RoutingRuleTab {
	ruleList := make([]*ruledata.RoutingRuleTab, 0, len(task.RuleList))
	for _, taskRule := range task.RuleList {
		ruleData := &ruledata.RoutingRuleTab{
			ID:                    int64(taskRule.Id),
			ProductID:             int64(task.ProductId),
			Priority:              int32(taskRule.Priority),
			RuleName:              taskRule.RuleName,
			Rules:                 taskRule.StrRuleDetails,
			StrDefaultCriteria:    taskRule.StrDefaultCriteria,
			StrCombinationSetting: taskRule.StrCombinationSetting,
			StrDisabledInfo:       taskRule.StrDisabledInfo,
			WhsId:                 taskRule.WhsId,
			DestinationPorts:      taskRule.DestinationPorts,
			ZoneCode:              taskRule.ZoneCode,
			TaskID:                int64(taskRule.TaskId),
			ItemCategoryLevel:     taskRule.ItemCategoryLevel,
			ItemCategoryIDList:    taskRule.ItemCategoryId,
			ParcelValueMin:        taskRule.ParcelValueMin,
			ParcelValueMax:        taskRule.ParcelValueMax,
			ParcelWeightMin:       taskRule.ParcelWeightMin,
			ParcelWeightMax:       taskRule.ParcelWeightMax,
			RoutingType:           taskRule.RoutingType,
			IsMultiProduct:        taskRule.IsMultiProduct,
			DgType:                taskRule.DgType,
			ParcelDimension:       taskRule.ParcelDimension,
			WmsToggleEnable:       taskRule.WmsToggleEnable,
			CCMode:                taskRule.CCMode,
			VolumeRuleId:          task.VolumeRuleId,
			ShopGroupList:         taskRule.ShopGroupList,
		}
		ruleList = append(ruleList, ruleData)
	}

	sort.SliceStable(ruleList, func(i, j int) bool {
		return ruleList[i].Priority < ruleList[j].Priority
	})

	// prepare shop group - entity infos
	prepareShopGroups(ctx, ruleList)

	return ruleList
}

func (s *SmartRoutingForecastServiceImpl) SaveResult(ctx context.Context, taskID, day int, result map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult) *srerr.Error {
	results := getForecastResult(ctx, taskID, day, result)
	return s.ForecastRepo.InsertForecastResults(ctx, results)
}

func getForecastResult(ctx context.Context, taskID, day int, result map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult) []*persistent.ForecastingTaskResultTab {
	now := timeutil.GetLocalTime(ctx).Unix()
	results := make([]*persistent.ForecastingTaskResultTab, 0)
	blockCartonTotalMap := make(map[string]int)
	blockWeightTotalMap := make(map[string]int)
	blockParcelTotalMap := make(map[string]int)
	statDateMap := make(map[string]interface{})
	for info, count := range result {
		var (
			asfList       []float64
			asfUsdList    []float64
			missingAsfQty int
		)
		for _, asfInfo := range count.AsfInfoList {
			if asfInfo.HasAsf {
				asfList = append(asfList, asfInfo.Asf)
				asfUsdList = append(asfUsdList, asfInfo.AsfUsd)
			} else {
				missingAsfQty++
			}
		}

		if isForecastResultBlocked(info) {
			blockCartonTotalMap[info.StatDate] += count.Quantity
			blockWeightTotalMap[info.StatDate] += count.IlhParcelQuantity
			blockParcelTotalMap[info.StatDate] += count.Weight
			statDateMap[info.StatDate] = nil
		}
		result := persistent.ForecastingTaskResultTab{
			TaskId:            taskID,
			RuleId:            info.RuleId,
			Quantity:          count.Quantity,
			IlhParcelQuantity: count.IlhParcelQuantity,
			Priority:          info.Priority,
			Weight:            count.Weight,
			LaneCode:          info.LaneCode,
			ServiceCode:       info.ServiceCode,
			DGFlag:            info.DGFlag,
			CTime:             now,
			Site:              info.Site,
			BuyerCity:         info.BuyerCityId,
			ShippingFee:       count.ShippingFee,
			Day:               day,
			ActualPoint:       info.OutActualPoint,
			StatDate:          info.StatDate,
			AsfInfo: persistent.ForecastResultAsfInfo{
				AverageAsf:    objutil.SafeAverageFloat64(asfList),
				AverageAsfUsd: objutil.SafeAverageFloat64(asfUsdList),
				MissingAsfQty: missingAsfQty,
			},
		}
		marshal, err := jsoniter.Marshal(info.WeightRange)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Marshal weight range failed %+v", err)
		}
		result.WeightRangeDetail = marshal

		results = append(results, &result)
	}
	for statDate := range statDateMap {
		allBlockResult := persistent.ForecastingTaskResultTab{
			TaskId:            taskID,
			Quantity:          blockCartonTotalMap[statDate],
			Weight:            blockWeightTotalMap[statDate],
			IlhParcelQuantity: blockParcelTotalMap[statDate],
			ServiceCode:       forecast.AllBlocked,
			LaneCode:          forecast.AllBlocked,
			DGFlag:            forecastservice.NoneDG,
			Day:               day,
			CTime:             now,
			StatDate:          statDate,
		}
		results = append(results, &allBlockResult)
	}

	// 如果results为空，需要手动插入一条数据保证db有数据，避免task认为无预测结果而报错
	if len(results) == 0 {
		allBlockResult := persistent.ForecastingTaskResultTab{
			TaskId:            taskID,
			Quantity:          0,
			Weight:            0,
			IlhParcelQuantity: 0,
			ServiceCode:       forecast.AllBlocked,
			LaneCode:          forecast.AllBlocked,
			DGFlag:            forecastservice.NoneDG,
			Day:               day,
			CTime:             now,
			StatDate:          "",
		}
		results = append(results, &allBlockResult)
	}

	return results
}

func isForecastResultBlocked(info forecastentity.ForecastLaneInfo) bool {
	return info.LaneCode == forecast.Blocked || info.LaneCode == forecast.RuleBlocked || info.LaneCode == forecast.RoutingFailed
}

func getDays(start, end string) ([]time.Time, error) {
	startDate, err := timeutil.ParseLocalTime("2006-01-02", start)
	if err != nil {
		return nil, errors.WithMessage(err, "parse start day")
	}
	endDate, err := timeutil.ParseLocalTime("2006-01-02", end)
	if err != nil {
		return nil, errors.WithMessage(err, "parse end day")
	}
	var ret []time.Time
	for cur := startDate; !cur.After(endDate); cur = cur.Add(time.Hour * 24) {
		ret = append(ret, cur)
	}
	return ret, nil
}

func initRoutingFactors(forecastCounter volume_counter.VolumeCounter, volumeRoutingSrv vrservice.Service) {
	var (
		lpsClient                       = lpsclient.NewLpsApiImpl()
		routingConfig                   = ruledata.NewRoutingConfigRepoImpl(lpsClient)
		parcelTypeDefinitionRepoImpl    = parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
		parcelTypeDefinitionServiceImpl = parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsClient)
	)
	schedule_factor.NewDgFactor()
	schedule_factor.NewMinVolumeFactor(forecastCounter)
	schedule_factor.NewMaxCapacityFactor(forecastCounter, parcelTypeDefinitionServiceImpl)
	schedule_factor.NewLinePriorityFactor()
	schedule_factor.NewDefaultPriorityFactor()
	schedule_factor.NewDefaultWeightageFactor()
	schedule_factor.NewMinWeightFactor(forecastCounter)
	schedule_factor.NewMaxWeightFactor(forecastCounter)
	schedule_factor.NewLineCheapestShippingFeeFactor(chargeclient.NewChargeApiImpl(), lane.NewLaneService(lfsclient.NewLfsApiImpl(), llsclient.NewLlsApiImpl()))
	zoneMrg := volumerouting.NewZoneRuleMgrImpl(volumeRoutingSrv, vrrepo.NewZoneRuleRepoImpl(), vrrepo.NewZoneGroupRepoImpl(nil, routingConfig),
		vrrepo.NewZoneRepoImpl(), vrrepo.NewTaskRepoImpl(), lpsClient, ruledata.NewRoutingConfigRepoImpl(lpsClient),
		lnpclient.NewLnpApiImpl(), parcelTypeDefinitionServiceImpl)
	schedule_factor.NewMaxVolumeV2(zoneMrg)
	schedule_factor.NewMinVolumeV2(zoneMrg)
	schedule_factor.NewILHParcelMaxCapacityFactor(forecastCounter)
	schedule_factor.NewILHParcelMinVolumeFactor(forecastCounter)
	schedule_factor.NewCombinationPriorityFactor()
}

func formatRuleMatchParam(ctx context.Context, task *persistent.ForecastingTaskTab, order *forecastentity.LFSOrderInfo, dgType rule.DGFlag) (routing.RuleMatchParam, *srerr.Error) {
	param := routing.RuleMatchParam{
		ProductId:        int64(task.ProductId),
		WhsID:            order.WhsCode,
		Cogs:             order.Cogs,
		ValidationWeight: order.ValidationWeight,
		DgType:           int(dgType),
		ParcelLength:     order.ParcelLength,
		ParcelWidth:      order.ParcelWidth,
		ParcelHeight:     order.ParcelHeight,
		RoutingType:      task.RoutingType,
		IsMultiProduct:   task.IsMultiProduct,
		Forderid:         order.OrderSN,
		ZoneType:         locationzone.ForecastingZoneType,
		DeliverPostCode:  order.DeliverPostCode,
		ShopId:           order.ShopId,
	}

	for _, locId := range order.LocationIdList {
		param.LocationIDList = append(param.LocationIDList, uint64(locId))
	}

	for _, sku := range order.Skus {
		param.ItemCategoryInfos = append(param.ItemCategoryInfos, &routing.ItemCategoryInfo{
			GlobalCategoryIdL1: int(sku.GetGlobalCategoryId_L1()),
			GlobalCategoryIdL2: int(sku.GetGlobalCategoryId_L2()),
			GlobalCategoryIdL3: int(sku.GetGlobalCategoryId_L3()),
			GlobalCategoryIdL4: int(sku.GetGlobalCategoryId_L4()),
			GlobalCategoryIdL5: int(sku.GetGlobalCategoryId_L5()),
		})
	}

	return param, nil
}

func setupRoutingLogBasic(log *routing_log.RoutingLog, rule *rule.RoutingRuleParsed, roleMap map[int]int) {
	log.RuleId = int(rule.ID)
	log.RoutingResult.RoutingRole = roleMap
}

func setupRoutingLogResult(log *routing_log.RoutingLog, result *rule.RoutingLaneInfo, dgFlag int32) {
	log.FinalResult.LaneCode = result.LaneCode
	log.FinalResult.DgFlag = dgFlag
}

func formatSortingCodeKey(dgGroupId, lmId string, dgFlag int32, actualPoint string) string {
	return fmt.Sprintf("%v:%v:%v:%v", dgGroupId, lmId, dgFlag, actualPoint)
}

func (s *SmartRoutingForecastServiceImpl) loadILHLanes(ctx context.Context, productId int, sortingCodeInfo *lfsentity.SortingCodeInfo,
	twsCode string, dgChange int, sloList []string, dgGroupMap map[string]string) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	hardCheckReq := &lpsclient.ILHHardCheckRequest{
		ProductId:     productId,
		TwsCode:       twsCode,
		DgChange:      dgChange,
		DgGroupId:     sortingCodeInfo.DgGroup,
		LmId:          sortingCodeInfo.LmId,
		DgFlag:        sortingCodeInfo.DgFlag,
		ActualPointId: sortingCodeInfo.ActualPointId,
		SloList:       sloList,
	}

	laneCodes, err := s.LpsApi.ILHHardCheck(ctx, hardCheckReq)
	if err != nil {
		return nil, err
	}

	routingLaneInfos := make([]*rule.RoutingLaneInfo, 0, len(laneCodes))
	for _, laneCode := range laneCodes {
		routingLaneInfo, err := s.RoutingSrv.LoadRoutingLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			return nil, err
		}

		for _, lineInfo := range routingLaneInfo.LineList {
			// 当Line是DG相关的时候才需要赋值，否则统一赋值为Undefined
			if objutil.ContainInt(lfslib.NeedRoutingILH, int(lineInfo.ResourceSubType)) || lineInfo.IsDgRelated() {
				lineInfo.DGFlag = rule.DGFlag(sortingCodeInfo.DgFlag)
				routingLaneInfo.DgGroupId = dgGroupMap[lineInfo.LineId]
			} else {
				lineInfo.DGFlag = rule.UndefinedDGFlag
			}
		}

		routingLaneInfos = append(routingLaneInfos, routingLaneInfo)
	}

	return routingLaneInfos, nil
}

func (s *SmartRoutingForecastServiceImpl) batchLoadOrderILHLanes(
	ctx context.Context, orders []*forecastentity.LHSOrderInfo,
	sortingCodeMap map[string]*lfsentity.SortingCodeInfo, lineToDgGroupMap map[string]string) *srerr.Error {
	// 异步任务链路，阻塞式
	workerPool, pErr := ants.NewPool(configutil.GetIlhForecastConfig().ConcurrentNum)
	if pErr != nil {
		return srerr.With(srerr.GoroutinePoolErr, nil, pErr)
	}
	defer workerPool.Release()

	var wg sync.WaitGroup
	for _, order := range orders {
		order := order
		wg.Add(1)
		if err := workerPool.Submit(func() {
			defer wg.Done()
			sortingCodeInfo, exist := sortingCodeMap[order.ServiceCode]
			if !exist || sortingCodeInfo == nil {
				logger.CtxLogErrorf(ctx, "can not find service code info | order:%s, service code:%s", order.LhsTn, order.ServiceCode)
				return
			}
			order.LmId = sortingCodeInfo.LmId
			routingLaneInfos, err := s.loadILHLanes(ctx, order.MultiProductId, sortingCodeInfo, order.TwsCode, order.DgChange, order.SloList, lineToDgGroupMap)
			if err != nil {
				logger.CtxLogErrorf(ctx, "load ilh lanes failed | order:%s, err=%v", order.LhsTn, err)
				return
			}
			order.AvailableLanes = routingLaneInfos
		}); err != nil {
			return srerr.With(srerr.GoroutinePoolErr, nil, err)
		}
	}
	wg.Wait()

	return nil
}

func (s *SmartRoutingForecastServiceImpl) updateVolumeForOld(ctx context.Context, order *forecastentity.LFSOrderInfo, task *persistent.ForecastingTaskTab, resultLane *rule.RoutingLaneInfo, volumeCounter volume_counter.VolumeCounter, lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr) {
	zoneCodeList, err := s.ZoneRepo.GetZoneCodeListFromCache(ctx, task.ProductId, task.RoutingType, locationzone.ForecastingZoneType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetZoneCodeListFromCache fail|err=%+v")
	}

	deliverZone := s.ZoneRepo.MatchDistrictToZone(ctx, task.ProductId, order.LocationIdList, zoneCodeList, task.RoutingType, locationzone.ForecastingZoneType, order.DeliverPostCode)
	// 3. incr volumeCounter
	for _, line := range resultLane.LineList {
		parcelTypeAttr, exist := lineParcelMap[line.LineId]
		if !exist {
			logger.CtxLogErrorf(ctx, "line_id=%v not exist in lineParcelMap", line.LineId)
		}
		_ = volumeCounter.IncrLineVolume(ctx, task.ProductId, line.ResourceId, parcelTypeAttr)
		if deliverZone != "" {
			_ = volumeCounter.IncrLineZoneVolume(ctx, task.ProductId, line.ResourceId, deliverZone, parcelTypeAttr)
		}
	}
}

func (s *SmartRoutingForecastServiceImpl) updateVolumeForNew(ctx context.Context, productId int64, order *forecastentity.LFSOrderInfo, routingType uint8, volumeRuleId int64, resultLane *rule.RoutingLaneInfo, volumeRoutingSrv vrservice.Service, lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr) {
	date := timeutil.FormatDate(timeutil.GetLocalTime(ctx))
	volumeRule, err := s.ZoneRuleRepo.GetRuleWithZoneLimitByProduct(ctx, productId, routingType, volumeRuleId)
	if err != nil || volumeRule == nil {
		logger.CtxLogErrorf(ctx, "Get volume rule err=%v volumeRule=%+v", err, volumeRule)
		return
	}
	// 更新line维度运力
	_ = volumeRoutingSrv.IncrVolumeLineDimension(ctx, productId, resultLane.GetLineIds(), date, lineParcelMap)
	if volumeRule.RuleType == enum.VolumeRuleTypeProduct {
		logger.CtxLogInfof(ctx, "not.add.zone.counter.rule_type product")
		return
	}

	lineGroupMap := make(map[string]string)
	zoneLimits := volumeRule.VolumeZoneLimits
	for i := 0; i < len(zoneLimits); i++ {
		if _, ok := lineGroupMap[zoneLimits[i].LineId]; !ok {
			if groupId, _ := s.ZoneGroupRepo.GetGroupIdByLineWithCache(ctx, zoneLimits[i].LineId, int(routingType), constant.ForecastType); groupId != "" {
				lineGroupMap[zoneLimits[i].LineId] = groupId
			}
		}
	}

	// 更新zone维度运力
	for _, line := range resultLane.LineList {
		groupId, ok := lineGroupMap[line.LineId]
		if !ok {
			logger.CtxLogInfof(ctx, "line_id=%v not bind with group volumeRuleId=%d", line.LineId, volumeRule.RuleId)
			continue
		}
		groupInfo, _ := s.ZoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, groupId, int(routingType), constant.ForecastType)
		if groupInfo == nil {
			logger.CtxLogInfof(ctx, "line_id=%v group_id=%v get group_info err", line.LineId, groupId)
			continue
		}
		parcelTypeAttr, exist := lineParcelMap[line.LineId]
		if !exist {
			logger.CtxLogErrorf(ctx, "line_id=%v not exist in lineParcelMap", line.LineId)
		}
		locInfo := pb.LocationInfo{LocationIds: objutil.UintToInt64(order.LocationIdList)}
		zoneNameList := s.VolumeZoneRepo.GetZoneNameWithDeliveryAddress(ctx, groupInfo, &locInfo)
		zoneNameList = objutil.RemoveDuplicatedStrings(zoneNameList) // 去重
		for _, zoneName := range zoneNameList {
			_ = volumeRoutingSrv.IncrVolumeZoneDimension(ctx, productId, line.LineId, date, groupId, zoneName, parcelTypeAttr)
		}
	}
}

func prepareShopGroups(ctx context.Context, ruleTabList []*ruledata.RoutingRuleTab) {
	entityList := DumpClientInfo(ctx, ruleTabList)
	if len(entityList) == 0 {
		return
	}
	// 分类，按rule-group-entity_list的map聚合
	ruleGroupEntityMap := make(map[string]map[int64][]int64, 0)
	for _, entity := range entityList {
		if ruleGroupEntityMap[entity.Version] == nil {
			ruleGroupEntityMap[entity.Version] = make(map[int64][]int64, 0)
		}
		groupId, _ := strconv.ParseInt(entity.ClientGroupId, 10, 64)
		entityId, _ := strconv.ParseInt(entity.ModelId, 10, 64)
		ruleGroupEntityMap[entity.Version][groupId] = append(ruleGroupEntityMap[entity.Version][groupId], entityId)
	}
	// 装填rule
	for _, ruleTab := range ruleTabList {
		if _, ok := ruleGroupEntityMap[lpsclient.FormatForecastVersion(ruleTab.ID)]; !ok {
			continue
		}
		ruleTab.ClientEntityGroupMap = make(map[int64]int64, 0)
		for groupId, tempEntityList := range ruleGroupEntityMap[lpsclient.FormatForecastVersion(ruleTab.ID)] {
			for _, entity := range tempEntityList {
				ruleTab.ClientEntityGroupMap[entity] = groupId
			}
		}
	}
}

func DumpClientInfo(ctx context.Context, rdata []*ruledata.RoutingRuleTab) (entityList []lpsclient.Model) {
	lpApi := lpsclient.NewLpsApiImpl()
	for _, ruleTab := range rdata {
		// 1.get shop group list
		ruleTab.UnmarshalShopGroupList()
		if len(ruleTab.ShopGroupListVo) == 0 {
			continue
		}
		// 2.get entity
		// fill req
		tempEntityList, _, err := lpApi.GetAllClientInfo(ctx, ruleTab.ShopGroupListVo, lpsclient.FormatForecastVersion(ruleTab.ID))
		if err != nil {
			logger.CtxLogErrorf(ctx, "get client info err:%v", err)
			continue
		}
		entityList = append(entityList, tempEntityList...)
	}

	return entityList
}
