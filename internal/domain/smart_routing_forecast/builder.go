package smart_routing_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/panjf2000/ants/v2"
	"go.uber.org/ratelimit"
)

func resultCounterBuilder(task *persistent.ForecastingTaskTab) *volume_counter.AsyncCounter {
	wrList := []persistent.WeightRange{}
	for _, weightRange := range task.WeightRange {
		wr := persistent.WeightRange{Min: weightRange.Min, Max: weightRange.Max}
		wrList = append(wrList, wr)
	}
	return volume_counter.NewAsyncCounterForLocalSpx(wrList)
}

func rateLimitBuilder(ctx context.Context) ratelimit.Limiter {
	concurrentNum := configutil.GetLocalForecastRateLimitConfig(ctx).ConcurrentNum
	return ratelimit.New(concurrentNum)
}

func poolBuilder(ctx context.Context) (*ants.Pool, *srerr.Error) {
	conf := configutil.GetAntPoolConfig(ctx)
	// 异步任务链路
	pool, pErr := ants.NewPool(conf.PoolSize, ants.WithPanicHandler(PanicHandler))
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "new goroutine pool failed %v", pErr)
		return nil, srerr.With(srerr.GoroutinePoolErr, nil, pErr)
	}
	return pool, nil
}

func forecastAndVolumeCounterBuilder() (*vrservice.ForecastVolumeRouting, volume_counter.VolumeCounter) {
	//初始化预测计数器和运力计数器
	forecastCounter := volume_counter.NewForecastVolumeCounterImpl()
	volumeCounter := vrservice.NewForecastVolumeRouting()
	//初始化调度因子
	initRoutingFactors(forecastCounter, volumeCounter)

	return volumeCounter, forecastCounter
}
