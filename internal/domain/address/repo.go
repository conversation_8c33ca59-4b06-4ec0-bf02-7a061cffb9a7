package address

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"strings"
)

const (
	maxLocationLevel = 4
)

var locationCheckCountryLevelOld = map[string]int{
	constant.ID: 3,
	constant.SG: 1,
	constant.TH: 2,
	constant.MY: 2,
	constant.PH: 4,
	constant.TW: 2,
	constant.VN: 3,
	constant.KR: 1,
	constant.CN: 3,
	constant.BR: 2,
	constant.JP: 2,
	constant.MX: 3,
	constant.AR: 2,
	constant.PL: 2,
	constant.ES: 2,
	constant.FR: 2,
	constant.IN: 2,
	constant.CO: 3,
	constant.CL: 3,
}

var locationCheckCountryLevel = map[string]int{
	constant.ID: 3,
	constant.SG: 1,
	constant.TH: 3,
	constant.MY: 4,
	constant.PH: 4,
	constant.TW: 2,
	constant.VN: 3,
	constant.KR: 1,
	constant.CN: 3,
	constant.BR: 2,
	constant.JP: 2,
	constant.MX: 3,
	constant.AR: 2,
	constant.PL: 2,
	constant.ES: 2,
	constant.FR: 2,
	constant.IN: 2,
	constant.CO: 3,
	constant.CL: 3,
}

type AddrRepo interface {
	GetLocationByLocName(ctx context.Context, cid, state, city, district, street string) (*Location, *srerr.Error)
	GetLocationByLocId(ctx context.Context, locId int64) (*Location, *srerr.Error)
	GetLocationByLocFullPathName(ctx context.Context, cid, state, city, district, street string) (*Location, *srerr.Error) // 模糊匹配，MY、TH市场会从4级地址一直匹配到2级
	FillLocationIds(ctx context.Context, info *ordentity.AddressInfo) *srerr.Error
}

type AddrRepoImpl struct {
	LocationCli locationclient.LocationClient
}

func NewAddrRepoImpl(locationCli locationclient.LocationClient) *AddrRepoImpl {
	return &AddrRepoImpl{
		LocationCli: locationCli,
	}
}

func (p *AddrRepoImpl) GetLocationByLocName(ctx context.Context, cid, state, city, district, street string) (*Location, *srerr.Error) {
	region := strings.ToUpper(cid)
	if region == "" {
		region = envvar.GetCID()
	}
	var segments []string
	if state != "" {
		segments = append(segments, state)
	}
	if city != "" {
		segments = append(segments, city)
	}
	if district != "" {
		segments = append(segments, district)
	}
	if street != "" {
		segments = append(segments, street)
	}
	if len(segments) == 0 {
		logger.CtxLogErrorf(ctx, "get location by name fail, name:%v", segments)
		return nil, srerr.New(srerr.InvalidAddressData, nil, "empty address detail")
	}
	if level, ok := locationCheckCountryLevel[region]; ok && len(segments) >= level {
		segments = segments[:level]
	}

	return p.getLocationByNameFromLayerCache(ctx, segments)
}

func (p *AddrRepoImpl) getLocationByNameFromLayerCache(ctx context.Context, splitNames []string) (*Location, *srerr.Error) {
	rsp, err := p.LocationCli.GetFullLocationByLocationNameWithCache(ctx, splitNames)
	if err != nil || rsp == nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, nil, err)
	}
	tsfRet, tsfErr := TsfLocRepoLocationByName(rsp.GetFullLocation())
	if tsfErr != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, nil, tsfErr)
	}
	return tsfRet, nil
}

func (p *AddrRepoImpl) GetLocationByLocId(ctx context.Context, locId int64) (*Location, *srerr.Error) {
	return p.getLocationTabByIdFromLayerCache(ctx, locId)
}

func (p *AddrRepoImpl) getLocationTabByIdFromLayerCache(ctx context.Context, locationId int64) (*Location, *srerr.Error) {
	rsp, err := p.LocationCli.GetFullLocationByLocationIdWithCache(ctx, locationId)
	if err != nil || rsp == nil {
		return nil, srerr.New(srerr.EmptyResultErr, nil, "location not found for location_id:%v", locationId)
	}
	tsfRet, tsfErr := TsfLocRepoLocationByName(rsp.GetFullLocation())
	if tsfErr != nil {
		return nil, srerr.New(srerr.EmptyResultErr, nil, "location not found for location_id:%v", locationId)
	}
	return tsfRet, nil
}

// GetLocationByLocFullPathName 模糊匹配地址。对MY从4级、TH从3级地址往上匹配，一直匹配到2级，匹配不到则报错（其他市场目前是根据地址精准匹配，后续其他市场也有可能会改为模糊匹配，需要注意）
func (p *AddrRepoImpl) GetLocationByLocFullPathName(ctx context.Context, cid, state, city, district, street string) (*Location, *srerr.Error) {
	region := strings.ToUpper(cid)
	if region == "" {
		monitoring.ReportError(ctx, monitoring.CatModuleLocsMonitor, monitoring.FullPathLocationNoCountry, "upstream does not pass country")
		region = envvar.GetCID()
	}
	var segments []string
	if state != "" {
		segments = append(segments, state)
	}
	if city != "" {
		segments = append(segments, city)
	}
	if district != "" {
		segments = append(segments, district)
	}
	if street != "" {
		segments = append(segments, street)
	}
	if len(segments) == 0 {
		logger.CtxLogErrorf(ctx, "get location by name fail, name:%v", segments)
		return nil, srerr.New(srerr.InvalidAddressData, nil, "empty address detail")
	}

	// 开关为true表示回滚到旧接口，调旧接口，为false表示请求新接口，默认为false。（后续清理代码只需要删除整个if语句即可）
	if configutil.GetAddressLevelUpSwitch(ctx) {
		// 旧接口，精准匹配
		if level, ok := locationCheckCountryLevelOld[region]; ok && len(segments) >= level {
			segments = segments[:level]
		}
		rsp, err := p.getLocationByNameFromLayerCache(ctx, segments)
		logger.CtxLogDebugf(ctx, "old interface|getLocationIdByName|rsp: %v, err: %v", rsp, err)
		return rsp, err
	}
	// 新接口，模糊匹配
	if level, ok := locationCheckCountryLevel[region]; ok && len(segments) >= level {
		segments = segments[:level]
	}
	rsp, err := p.getLocationByFullPathNameFromLayerCache(ctx, region, segments)
	logger.CtxLogDebugf(ctx, "new interface|getLocationIdByName|rsp: %v, err: %v", rsp, err)
	return rsp, err
}

// getLocationByFullPathNameFromLayerCache 模糊匹配location_id，必须要传country
func (p *AddrRepoImpl) getLocationByFullPathNameFromLayerCache(ctx context.Context, country string, splitNames []string) (*Location, *srerr.Error) {
	rsp, err := p.LocationCli.GetFullLocationByLocationFullPathNameWithCache(ctx, country, splitNames)
	if err != nil || rsp == nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, nil, err)
	}
	tsfRet, tsfErr := TsfLocRepoLocationByName(rsp.GetFullLocation())
	if tsfErr != nil {
		return nil, srerr.With(srerr.GetLocationInfoFailed, nil, tsfErr)
	}
	return tsfRet, nil
}

func (p *AddrRepoImpl) FillLocationIds(ctx context.Context, info *ordentity.AddressInfo) *srerr.Error {
	// 已经存在location id不用转换
	if info != nil && info.StateLocationId != nil && *info.StateLocationId > 0 {
		return nil
	}
	if info == nil || info.State == nil || info.City == nil {
		return srerr.New(srerr.ParamErr, nil, "missing location core information[state, city]")
	}
	locInfo, err := p.GetLocationByLocFullPathName(ctx, info.GetCountry(), info.GetState(), info.GetCity(), info.GetDistrict(), info.GetStreet())
	if err != nil {
		return err
	}
	//装填 location id
	var stateLocationId = int(locInfo.GetStateLocId())
	info.StateLocationId = &stateLocationId
	var cityLocationId = int(locInfo.GetCityLocId())
	info.CityLocationId = &cityLocationId
	locIds := []int{stateLocationId, cityLocationId}
	if locInfo.GetDistrictLocId() > 0 {
		var districtLocationId = int(locInfo.GetDistrictLocId())
		info.DistrictLocationId = &districtLocationId
		locIds = append(locIds, districtLocationId)
	}
	if locInfo.GetStreetLocId() > 0 {
		var streetLocationId = int(locInfo.GetStreetLocId())
		info.StreetLocationId = &streetLocationId
		locIds = append(locIds, streetLocationId)
	}
	info.LocationIDs = locIds
	return nil
}

func GetMaxLocationLevel() int {
	return maxLocationLevel
}
