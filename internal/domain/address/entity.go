package address

import (
	"errors"
	locationPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"github.com/gogo/protobuf/proto"
)

type Location struct {
	State      *string  `json:"state,omitempty"`
	City       *string  `json:"city,omitempty"`
	District   *string  `json:"district,omitempty"`
	Street     *string  `json:"street,omitempty"`
	StateId    *int64   `json:"state_id,omitempty"`
	CityId     *int64   `json:"city_id,omitempty"`
	DistrictId *int64   `json:"district_id,omitempty"`
	StreetId   *int64   `json:"street_id,omitempty"`
	Longitude  *float64 `json:"longitude,omitempty"`
	Latitude   *float64 `json:"latitude,omitempty"`
	PostCode   *string  `json:"post_code,omitempty"`
	Level      *int     `json:"level"`
	ParentId   *int64   `json:"parent_id"`
}

// location level
const (
	StateLevel = iota
	CityLevel
	DistrictLevel
	StreetLevel
)

func (p Location) GetState() string {
	if p.State == nil {
		return ""
	}
	return *p.State
}

func (p Location) GetCity() string {
	if p.City == nil {
		return ""
	}
	return *p.City
}

func (p Location) GetDistrict() string {
	if p.District == nil {
		return ""
	}
	return *p.District
}

func (p Location) GetStreet() string {
	if p.Street == nil {
		return ""
	}
	return *p.Street
}

func (p Location) GetStateLocId() int64 {
	if p.StateId == nil {
		return 0
	}
	return *p.StateId
}

func (p Location) GetCityLocId() int64 {
	if p.CityId == nil {
		return 0
	}
	return *p.CityId
}

func (p Location) GetDistrictLocId() int64 {
	if p.DistrictId == nil {
		return 0
	}
	return *p.DistrictId
}

func (p Location) GetStreetLocId() int64 {
	if p.StreetId == nil {
		return 0
	}
	return *p.StreetId
}

func (p Location) GetPostcode() string {
	if p.PostCode == nil {
		return ""
	}
	return *p.PostCode
}

func (p Location) GetLevel() int {
	if p.Level == nil {
		return -1
	}
	return *p.Level
}

func (p Location) GetLocAndParenIds() (int64, []int64) {
	if p.StreetId != nil {
		return p.GetStreetLocId(), []int64{p.GetStateLocId(), p.GetCityLocId(), p.GetDistrictLocId()}
	}
	if p.DistrictId != nil {
		return p.GetDistrictLocId(), []int64{p.GetStateLocId(), p.GetCityLocId()}
	}
	if p.CityId != nil {
		return p.GetCityLocId(), []int64{p.GetStateLocId()}
	}
	return p.GetStateLocId(), nil
}

func (p *Location) FormatLocationIdList() []int {
	var locIds []int
	if p.StateId != nil {
		locIds = append(locIds, int(*p.StateId))
	}
	if p.CityId != nil {
		locIds = append(locIds, int(*p.CityId))
	}
	if p.DistrictId != nil {
		locIds = append(locIds, int(*p.DistrictId))
	}
	if p.StreetId != nil {
		locIds = append(locIds, int(*p.StreetId))
	}
	return locIds
}

func TsfLocRepoLocationByName(data *locationPb.FullLocation) (*Location, error) {
	if data == nil {
		return &Location{}, errors.New("location tsf err")
	}
	newLocation := &Location{}
	for _, location := range data.GetLocationList() {
		// add current node info
		if location.GetName() == data.GetName() {
			// add parent id
			newLocation.UpdateBasicInfoFromFullLocation(location)
		}
		// add location id and location name in full path
		locationId := int64(location.GetLocationId())
		name := location.GetName()
		switch location.GetLevel() {
		case StateLevel:
			newLocation.StateId = &locationId
			newLocation.State = &name
		case CityLevel:
			newLocation.CityId = &locationId
			newLocation.City = &name
		case DistrictLevel:
			newLocation.DistrictId = &locationId
			newLocation.District = &name
		case StreetLevel:
			newLocation.StreetId = &locationId
			newLocation.Street = &name
		}
	}
	return newLocation, nil
}

func (p *Location) UpdateBasicInfoFromFullLocation(data *locationPb.LocationInfo) {
	if data == nil {
		return
	}

	level := int(data.GetLevel())
	p.Level = &level
	p.ParentId = proto.Int64(int64(data.GetParentId()))
	p.Longitude = proto.Float64(data.GetLongitude())
	p.Latitude = proto.Float64(data.GetLatitude())
	p.PostCode = proto.String(data.GetPostcode())
}

func (p *Location) GetLocationID() int64 {
	if p.GetStreetLocId() > 0 {
		return p.GetStreetLocId()
	}
	if p.GetDistrictLocId() > 0 {
		return p.GetDistrictLocId()
	}
	if p.GetCityLocId() > 0 {
		return p.GetCityLocId()
	}

	return p.GetStateLocId()
}

func (p *Location) GetParentID() int64 {
	if p.GetDistrictLocId() != 0 && p.GetDistrictLocId() != p.GetLocationID() {
		return p.GetDistrictLocId()
	}
	if p.GetCityLocId() != 0 && p.GetCityLocId() != p.GetLocationID() {
		return p.GetCityLocId()
	}
	if p.GetStateLocId() != 0 && p.GetStateLocId() != p.GetLocationID() {
		return p.GetStateLocId()
	}

	return 0
}

func (p *Location) GetLocationName() string {
	name := p.GetState()
	if p.GetCity() != "" {
		name += "-" + p.GetCity()
	}
	if p.GetDistrict() != "" {
		name += "-" + p.GetDistrict()
	}
	if p.GetStreet() != "" {
		name += "-" + p.GetStreet()
	}
	return name
}

type LocationInfoList []*Location

func (l LocationInfoList) Len() int {
	return len(l)
}

func (l LocationInfoList) Less(i, j int) bool {
	return l[i].GetLocationID() < l[j].GetLocationID()
}

func (l LocationInfoList) Swap(i, j int) {
	l[i], l[j] = l[j], l[i]
}
