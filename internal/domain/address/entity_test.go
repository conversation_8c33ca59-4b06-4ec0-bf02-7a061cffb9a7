package address

import (
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	locationPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"github.com/gogo/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestTsfLocRepoLocationByName(t *testing.T) {
	type args struct {
		data *locationPb.FullLocation
	}
	tests := []struct {
		name    string
		args    args
		want    *Location
		wantErr error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: TestTsfLocRepoLocationByName",
			args: args{
				data: nil,
			},
			want:    &Location{},
			wantErr: errors.New("location tsf err"),
		},
		{
			name: "case 2: TestTsfLocRepoLocationByName",
			args: args{
				data: &locationPb.FullLocation{
					LocationList: []*locationPb.LocationInfo{
						{
							Name:  "test",
							Level: StateLevel,
						},
						{
							Name:  "test",
							Level: CityLevel,
						},
						{
							Name:  "test",
							Level: DistrictLevel,
						},
						{
							Name:  "test",
							Level: StreetLevel,
						},
					},
					Name: "test",
				},
			},
			want: &Location{
				State:      proto.String("test"),
				City:       proto.String("test"),
				District:   proto.String("test"),
				Street:     proto.String("test"),
				StateId:    proto.Int64(0),
				CityId:     proto.Int64(0),
				DistrictId: proto.Int64(0),
				StreetId:   proto.Int64(0),
				Longitude:  proto.Float64(0),
				Latitude:   proto.Float64(0),
				PostCode:   proto.String(""),
				Level:      common.NewInt(3),
				ParentId:   proto.Int64(0),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := TsfLocRepoLocationByName(tt.args.data)
			if gotErr != nil && tt.wantErr != nil {
				if !assert.Equal(t, tt.wantErr.Error(), gotErr.Error()) {
					t.Errorf("gotError = %v, expectedErr %v", gotErr.Error(), tt.wantErr.Error())
				}
			} else if (gotErr == nil && gotErr != nil) || (gotErr != nil && tt.wantErr == nil) {
				t.Errorf("gotError and expectedErr，one is NIL and the other is not NIL ")
			}
			common.AssertResult(t, got, tt.want, nil, nil)
		})
	}
}
