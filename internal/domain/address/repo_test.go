package address

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	locationPb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v21/sls-location/go"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestAddrRepoImpl_GetLocationByLocFullPathName(t *testing.T) {
	ctx := context.Background()
	p := &AddrRepoImpl{}
	var patchGetAddressLevelUpSwitch, patchGetFullLocationByLocationNameWithCache, patchGetFullLocationByLocationFullPathNameWithCache *gomonkey.Patches
	type args struct {
		cid      string
		state    string
		city     string
		district string
		street   string
	}
	tests := []struct {
		name    string
		args    args
		want    *Location
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: len(segments) == 0",
			args:    args{},
			want:    nil,
			wantErr: srerr.New(srerr.InvalidAddressData, nil, "empty address detail"),
			setup:   func() {},
		},
		{
			name: "case 2: configutil.GetAddressLevelUpSwitch(ctx) == true",
			args: args{
				state:    "state",
				city:     "city",
				district: "district",
				street:   "street",
			},
			want:    nil,
			wantErr: srerr.With(srerr.GetLocationInfoFailed, nil, errors.New("location tsf err")),
			setup: func() {
				p = &AddrRepoImpl{LocationCli: &locationclient.LocationClientImpl{}}
				patchGetAddressLevelUpSwitch = gomonkey.ApplyFunc(configutil.GetAddressLevelUpSwitch, func(ctx context.Context) bool {
					return true
				})
				patchGetFullLocationByLocationNameWithCache = gomonkey.ApplyMethod(reflect.TypeOf(p.LocationCli), "GetFullLocationByLocationNameWithCache", func(l *locationclient.LocationClientImpl, ctx context.Context, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
					return &locationPb.FullLocationRsp{}, nil
				})
			},
		},
		{
			name: "case 3: configutil.GetAddressLevelUpSwitch(ctx) == false",
			args: args{
				state:    "state",
				city:     "city",
				district: "district",
				street:   "street",
			},
			want:    nil,
			wantErr: srerr.With(srerr.GetLocationInfoFailed, nil, errors.New("location tsf err")),
			setup: func() {
				p = &AddrRepoImpl{LocationCli: &locationclient.LocationClientImpl{}}
				patchGetFullLocationByLocationFullPathNameWithCache = gomonkey.ApplyMethod(reflect.TypeOf(p.LocationCli), "GetFullLocationByLocationFullPathNameWithCache", func(l *locationclient.LocationClientImpl, ctx context.Context, country string, splitNames []string) (*locationPb.FullLocationRsp, *srerr.Error) {
					return &locationPb.FullLocationRsp{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := p.GetLocationByLocFullPathName(ctx, tt.args.cid, tt.args.state, tt.args.city, tt.args.district, tt.args.street)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)

			if patchGetAddressLevelUpSwitch != nil {
				patchGetAddressLevelUpSwitch.Reset()
			}
			if patchGetFullLocationByLocationNameWithCache != nil {
				patchGetFullLocationByLocationNameWithCache.Reset()
			}
			if patchGetFullLocationByLocationFullPathNameWithCache != nil {
				patchGetFullLocationByLocationFullPathNameWithCache.Reset()
			}
		})
	}
}
