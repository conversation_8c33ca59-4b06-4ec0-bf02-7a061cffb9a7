package entity

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// AvailableLH 领域模型
type AvailableLH struct {
	ID                 int               `json:"id"`
	MultiProductID     int               `json:"multi_product_id"`
	RuleStatus         rule.RuleStatus   `json:"rule_status"`
	EffectiveStartTime int64             `json:"effective_start_time"`
	Rules              []AvailableLHRule `json:"rules"`
	Operator           string            `json:"operator"`
	Ctime              int64             `json:"ctime"`
	Mtime              int64             `json:"mtime"`
}

// AvailableLHRule 领域模型
type AvailableLHRule struct {
	RuleName            string                     `json:"rule_name"`
	Priority            int                        `json:"priority"`
	TWS                 []string                   `json:"tws"`
	DestinationPort     []string                   `json:"destination_port"`
	DGType              rule.DGFlag                `json:"dg_type"`
	AvailableLine       []AvailableLine            `json:"available_line"`
	CombinationSettings []*rule.CombinationSetting `json:"combination_settings"`
}

func (r AvailableLHRule) GetAvailableLineMap() map[string]AvailableLineBaseInfo {
	lineMap := make(map[string]AvailableLineBaseInfo)
	for _, typeLine := range r.AvailableLine {
		for _, lineInfo := range typeLine.LineList {
			lineMap[lineInfo.LineId] = lineInfo
		}
	}

	return lineMap
}

// AvailableLine 可用的3PL信息
type AvailableLine struct {
	LineSubType int                     `json:"line_sub_type"`
	LineList    []AvailableLineBaseInfo `json:"line_list"`
}

// AvailableLineBaseInfo 3PL的基本配置信息
type AvailableLineBaseInfo struct {
	rule.BaseLineInfo
	Weightage int     `json:"weightage"`  // 分配权重
	MinWeight float64 `json:"min_weight"` // 最小分配的重量范围
	MaxWeight float64 `json:"max_weight"` // 能够承载的最大重量
}
