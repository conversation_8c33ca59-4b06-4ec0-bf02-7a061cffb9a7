package repo

import (
	"context"
	"strconv"

	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

func DumpAvailableLHConfig() (map[string]interface{}, error) {
	ctx := context.Background()

	db, err := dbutil.SlaveDB(ctx, AvailableLHTabTabHook)
	if err != nil {
		return nil, err
	}

	// 获取所有Active状态的配置
	var availableLHTabs []*AvailableLHTab
	if err := db.Table(AvailableLHTabTabHook.TableName()).
		Where("rule_status = ?", rule.RuleStatusActive).
		Find(&availableLHTabs).GetError(); err != nil {
		return nil, err
	}

	// 构建缓存映射，key为product id，值为领域模型
	result := make(map[string]interface{}, len(availableLHTabs))
	for _, availableLHTab := range availableLHTabs {
		entity := availableLHTab.ConvertToEntity()
		result[strconv.Itoa(availableLHTab.MultiProductID)] = entity
	}

	return result, nil
}
