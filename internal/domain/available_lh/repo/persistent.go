package repo

import (
	"database/sql/driver"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

type Rules []entity.AvailableLHRule

var (
	AvailableLHTabTabHook      = &AvailableLHTab{}
	ForecastAvailableLHTabHook = &ForecastAvailableLHTab{}
)

type AvailableLHTab struct {
	ID                 int             `gorm:"column:id"`
	MultiProductID     int             `gorm:"column:multi_product_id"`
	RuleStatus         rule.RuleStatus `gorm:"column:rule_status"`
	EffectiveStartTime int64           `gorm:"column:effective_start_time"`
	Rules              Rules           `gorm:"column:rules"`
	Operator           string          `gorm:"column:operator"`
	Ctime              int64           `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime              int64           `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
}

func (c AvailableLHTab) TableName() string {
	return "available_lh_tab"
}

func (c AvailableLHTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (c AvailableLHTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (c AvailableLHTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(c.ID),
		ModelName:            c.TableName(),
		FulfillmentProductId: uint64(c.MultiProductID),
	}
}

type ForecastAvailableLHTab struct {
	ID             int   `gorm:"column:id"`
	TaskID         int   `gorm:"column:task_id"`
	MultiProductID int   `gorm:"column:multi_product_id"`
	Rules          Rules `gorm:"column:rules"`
	Ctime          int64 `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime          int64 `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
}

func (c ForecastAvailableLHTab) TableName() string {
	return "forecast_available_lh_tab"
}

func (c ForecastAvailableLHTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (c ForecastAvailableLHTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (c ForecastAvailableLHTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(c.ID),
		ModelName:            c.TableName(),
		FulfillmentProductId: uint64(c.MultiProductID),
	}
}

func (p *Rules) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to convert rules value to []byte, value: %v", value)
	}

	return jsoniter.Unmarshal(bytes, p)
}

func (p Rules) Value() (driver.Value, error) {
	if len(p) == 0 {
		return "[]", nil
	}

	return jsoniter.Marshal(p)
}

// ConvertToEntity 将数据库模型转换为领域模型
func (c *AvailableLHTab) ConvertToEntity() *entity.AvailableLH {
	return &entity.AvailableLH{
		ID:                 c.ID,
		MultiProductID:     c.MultiProductID,
		RuleStatus:         c.RuleStatus,
		EffectiveStartTime: c.EffectiveStartTime,
		Rules:              []entity.AvailableLHRule(c.Rules),
		Operator:           c.Operator,
		Ctime:              c.Ctime,
		Mtime:              c.Mtime,
	}
}

// ConvertFromEntity 将领域模型转换为数据库模型
func (c *AvailableLHTab) ConvertFromEntity(availableLH *entity.AvailableLH) {
	c.ID = availableLH.ID
	c.MultiProductID = availableLH.MultiProductID
	c.RuleStatus = availableLH.RuleStatus
	c.EffectiveStartTime = availableLH.EffectiveStartTime
	c.Rules = Rules(availableLH.Rules)
	c.Operator = availableLH.Operator
	c.Ctime = availableLH.Ctime
	c.Mtime = availableLH.Mtime
}
