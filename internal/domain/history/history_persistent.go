package history

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

//持久化数据模型层

var OperationHistoryHook = &OperationHistoryTab{}

const (
	operationHistoryTableName = "operation_history_tab"
)

type OperationHistoryTab struct {
	Id int64 `gorm:"column:id" json:"id"`
}

func (t *OperationHistoryTab) TableName() string {
	return operationHistoryTableName
}

func (t *OperationHistoryTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *OperationHistoryTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *OperationHistoryTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(t.Id),
		ModelName: t.TableName(),
	}
}
