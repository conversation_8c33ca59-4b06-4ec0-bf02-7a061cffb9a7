package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strings"
	"sync"
	"time"
)

type ForecastVolumeCounterImpl struct {
	mu           sync.RWMutex
	counter      map[string]int
	TwsCutoffMap map[string]configutil.CutoffTimeConfig
	OrderTime    int
}

func (v *ForecastVolumeCounterImpl) BatchGetDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetZoneDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) UpdateDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	return nil
}

func (v *ForecastVolumeCounterImpl) GetDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) UpdateShopGroupVolume(ctx context.Context, maskProductID, productID int64, groupCode string, shopGroupId int64, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	return nil
}

func (v *ForecastVolumeCounterImpl) GetMaskProductVolume(ctx context.Context, maskingProductId int64, timestamp int64, rm rule_mode.RuleMode) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetFulfillmentProductVolume(ctx context.Context, maskingProductId, productId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetGroupCodeVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetMaskZoneVolume(ctx context.Context, maskingProductId, productId int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetGroupZoneVolume(ctx context.Context, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetMaskRouteVolume(ctx context.Context, maskingProductId, productId int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetGroupRouteVolume(ctx context.Context, groupCode string, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetMaskShopGroupVolume(ctx context.Context, maskingProductId, productId int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetGroupShopGroupVolume(ctx context.Context, groupCode string, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetMaskZoneVolume(ctx context.Context, productId int64, zoneCodeList []string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetMaskRouteVolume(ctx context.Context, productId int64, routeCodeList []string, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetMaskShopGroupVolume(ctx context.Context, productId int64, shopGroupIdList []int64, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) UpdateVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, sloCreateTime int64, updateType pb.UpdateVolumeType, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, lineParcelTypeAttr *parcel_type_definition.ParcelTypeAttr, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateProductVolume(ctx context.Context, productId int64, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateLineVolume(ctx context.Context, productId int64, lineId string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateLineZoneVolume(ctx context.Context, productId int64, lineId string, zoneCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateLaneFieldVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateLaneFieldZoneVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateLaneVolume(ctx context.Context, productId int64, laneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateMultiLaneFieldVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateMultiLaneFieldZoneVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateMultiProductServiceCodeVolume(ctx context.Context, productId int64, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateMaskVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DeductProductVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateRouteVolume(ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DeductRouteVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, routeCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateZoneVolume(ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DeductZoneVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) UpdateVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func NewForecastVolumeCounterImpl() *ForecastVolumeCounterImpl {
	return &ForecastVolumeCounterImpl{counter: make(map[string]int)}
}

func (v *ForecastVolumeCounterImpl) SetTwsCutoffMap(m map[string]configutil.CutoffTimeConfig) {
	v.TwsCutoffMap = m
}

func (v *ForecastVolumeCounterImpl) SetOrderTime(t int) {
	v.OrderTime = t
}

func (v *ForecastVolumeCounterImpl) GetLineVolume(ctx context.Context, productID int, lineID, parcelType string) (int, *srerr.Error) {
	return v.readVolume(formatForecastLineKey(productID, lineID, parcelType)), nil
}

func (v *ForecastVolumeCounterImpl) GetLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode, parcelType string) (int, *srerr.Error) {
	return v.readVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, parcelType)), nil
}

func (v *ForecastVolumeCounterImpl) IncrLineVolume(ctx context.Context, productID int, lineID string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	v.incrVolume(formatForecastLineKey(productID, lineID, ""))
	if parcelTypeAttr.GetIsCod() {
		v.incrVolume(formatForecastLineKey(productID, lineID, parcel_type_definition.ParcelTypeCod.String()))
	}
	if parcelTypeAttr.GetIsBulky() {
		v.incrVolume(formatForecastLineKey(productID, lineID, parcel_type_definition.ParcelTypeBulky.String()))
	}
	if parcelTypeAttr.GetIsHighValue() {
		v.incrVolume(formatForecastLineKey(productID, lineID, parcel_type_definition.ParcelTypeHighValue.String()))
	}
	if parcelTypeAttr.GetIsDg() {
		v.incrVolume(formatForecastLineKey(productID, lineID, parcel_type_definition.ParcelTypeDg.String()))
	}
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	v.incrVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, ""))
	if parcelTypeAttr.GetIsCod() {
		v.incrVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, parcel_type_definition.ParcelTypeCod.String()))
	}
	if parcelTypeAttr.GetIsBulky() {
		v.incrVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, parcel_type_definition.ParcelTypeBulky.String()))
	}
	if parcelTypeAttr.GetIsHighValue() {
		v.incrVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, parcel_type_definition.ParcelTypeHighValue.String()))
	}
	if parcelTypeAttr.GetIsDg() {
		v.incrVolume(formatForecastLineZoneKey(productID, lineID, zoneCode, parcel_type_definition.ParcelTypeDg.String()))
	}
	return nil
}

func (v *ForecastVolumeCounterImpl) GetProductLaneVolumes(ctx context.Context, productID int, days []time.Time) (map[LaneCounter]int, int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, 0, nil
}

func (v *ForecastVolumeCounterImpl) GetProductVolume(ctx context.Context, productID int) (int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetLaneVolume(ctx context.Context, productID int, laneCode string) (int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) UpdateActualPointVolume(ctx context.Context, productId int64, laneCode string, lineIdList []string, actualPointInfo []*pb.VolumeActualPoint, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) GetActualPointVolume(ctx context.Context, productId int64, laneCode string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) GetLineActualPointVolume(ctx context.Context, productId int64, laneCode string, lineId string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return 0, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetLaneVolume(ctx context.Context, productID int, laneCodeList []string) ([]int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetLineVolume(ctx context.Context, productID int, lineIDList []string) ([]int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetActualPointVolume(ctx context.Context, actualPoints []ReportActualPointInfo) ([]int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) BatchGetLineActualPointVolume(ctx context.Context, lineActualPointList []ReportLineActualPointInfo) ([]int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, nil
}

func (v *ForecastVolumeCounterImpl) Clear() {
	v.counter = make(map[string]int)
	v.TwsCutoffMap = make(map[string]configutil.CutoffTimeConfig)
	v.OrderTime = 0
}

func (v *ForecastVolumeCounterImpl) GetDateByTwsCode(ctx context.Context, twsCode string) time.Time {
	cutOrderTimeConfig, exist := v.TwsCutoffMap[strings.ToUpper(twsCode)]
	if !exist {
		logger.CtxLogErrorf(ctx, "Tws cut order time fail no exist|tws code = %s", twsCode)
	}

	orderTime := time.Unix(int64(v.OrderTime), 0).
		In(time.FixedZone("TWS", cutOrderTimeConfig.TimezoneOffset*timeutil.HourSecs))
	relativeTime := float64(orderTime.Hour()) + float64(orderTime.Minute())/60

	// sub one day when current time is early than tws cut order time
	if relativeTime < cutOrderTimeConfig.CutoffTime {
		orderTime = orderTime.AddDate(0, 0, -1)
	}

	return orderTime
}

func formatForecastLineKey(productID int, lineID, parcelType string) string {
	if parcelType != "" {
		return fmt.Sprintf("%d:%s:%s", productID, lineID, parcelType)
	}
	return fmt.Sprintf("%d:%s", productID, lineID)
}

func formatForecastLineZoneKey(productID int, lineID string, zoneCode, parcelType string) string {
	if parcelType != "" {
		return fmt.Sprintf("%d:%s:%s:%s", productID, lineID, zoneCode, parcelType)
	}
	return fmt.Sprintf("%d:%s:%s", productID, lineID, zoneCode)
}

func (v *ForecastVolumeCounterImpl) readVolume(key string) int {
	res := 0
	v.mu.RLock()
	res = v.counter[key]
	v.mu.RUnlock()
	return res
}

func (v *ForecastVolumeCounterImpl) incrVolume(key string) {
	v.mu.Lock()
	v.counter[key]++
	v.mu.Unlock()
}
