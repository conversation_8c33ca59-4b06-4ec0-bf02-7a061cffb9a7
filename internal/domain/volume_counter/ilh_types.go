package volume_counter

// ILHOrderUsageInfo 订单容量使用信息
type ILHOrderUsageInfo struct {
	PackageNo               string           `json:"package_no"`                 // 订单号
	ILHLineID               string           `json:"ilh_line_id"`                // ILH线路ID (注意大小写)
	ReservedBSAUsage        int64            `json:"reserved_bsa_usage"`         // 预留BSA使用量
	NonReservedBSAUsage     int64            `json:"non_reserved_bsa_usage"`     // 非预留BSA使用量
	AdhocUsage              int64            `json:"adhoc_usage"`                // Adhoc使用量
	CapacityMode            string           `json:"capacity_mode"`              // 容量模式
	SlotID                  string           `json:"slot_id"`                    // 时间段ID
	InheritedBSASlotUsage   map[string]int64 `json:"inherited_bsa_slot_usage"`   // 继承的BSA使用详情
	InheritedAdhocSlotUsage map[string]int64 `json:"inherited_adhoc_slot_usage"` // 继承的Adhoc使用详情
}
