package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type AllocationVolumeCounterInterface interface {
	UpdateMaskVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error
	DeductProductVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error
	UpdateRouteVolume(ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error
	DeductRouteVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, routeCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error
	UpdateZoneVolume(ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error
	DeductZoneVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error
	UpdateVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateShopGroupVolume(ctx context.Context, maskProductID, productID int64, groupCode string, shopGroupId int64, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error
	GetMaskProductVolume(ctx context.Context, maskingProductId int64, timestamp int64, rm rule_mode.RuleMode) (int64, *srerr.Error)
	GetFulfillmentProductVolume(ctx context.Context, maskingProductId, productId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetGroupCodeVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetMaskZoneVolume(ctx context.Context, maskProductId, productId int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetGroupZoneVolume(ctx context.Context, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetMaskRouteVolume(ctx context.Context, maskProductId, productId int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetGroupRouteVolume(ctx context.Context, groupCode string, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetMaskShopGroupVolume(ctx context.Context, maskProductId, productId int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	GetGroupShopGroupVolume(ctx context.Context, groupCode string, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error)
	BatchGetMaskZoneVolume(ctx context.Context, productId int64, zoneCodeList []string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error)
	BatchGetMaskRouteVolume(ctx context.Context, productId int64, routeCodeList []string, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error)
	BatchGetMaskShopGroupVolume(ctx context.Context, productId int64, shopGroupIdList []int64, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error)
}

type AllocateCheckBatchVolumeJobMsg struct {
	RuleID      int64
	BatchVolume int32
}

func (v *VolumeCounterImpl) UpdateMaskVolume(
	ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64,
	updateType pb.UpdateVolumeType, rm rule_mode.RuleMode,
) *srerr.Error {
	key := productMaskVolumeKey(ctx, maskingProductId, rm)
	_, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}
	// SSCSMR-3055：双写country维度的redis
	key = productVolumeKey(ctx, productId, rm)
	newKey := maskProductVolumeKey(ctx, maskingProductId, productId, rm)
	if err = v.UpdateRedisWithNewKey(ctx, key, newKey, sloCreateTime, updateType); err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}

	if groupCode != "" {
		if _, err := v.UpdateRedisCounter(ctx, groupVolumeKey(ctx, groupCode, rm), redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) DeductProductVolume(
	ctx context.Context, maskingProductId int64, productId int64, groupCode string, sloCreateTime int64,
	parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	key := maskProductVolumeKey(ctx, maskingProductId, productId, rule_mode.MplOrderRule)
	if err := v.DecrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr, sloCreateTime); err != nil {
		logger.CtxLogErrorf(ctx, "update new key err:%v", err)
		monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "UpdateRedisWithNewKey", fmt.Sprintf("update new key err:%v", err))
		return srerr.With(srerr.RedisErr, key, err)
	}

	if groupCode != "" {
		if err := v.DecrParcelTypeVolumeByKey(ctx, groupVolumeKey(ctx, groupCode, rule_mode.MplOrderRule), parcelTypeAttr, sloCreateTime); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) DecrParcelTypeVolumeByKey(ctx context.Context, key string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, sloCreateTime int64) error {
	if parcelTypeAttr.IsCod {
		codKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeCod)
		if _, err := v.UpdateRedisCounter(ctx, codKey, redisutil.GetDefaultInstance(), sloCreateTime, pb.UpdateVolumeType_Cancel); err != nil {
			return fmt.Errorf("decr cod volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.COD, codKey)
	}
	if parcelTypeAttr.IsBulky {
		bulkyKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeBulky)
		if _, err := v.UpdateRedisCounter(ctx, bulkyKey, redisutil.GetDefaultInstance(), sloCreateTime, pb.UpdateVolumeType_Cancel); err != nil {
			return fmt.Errorf("decr bulky volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.Bulky, bulkyKey)
	}
	if parcelTypeAttr.IsHighValue {
		highValueKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeHighValue)
		if _, err := v.UpdateRedisCounter(ctx, highValueKey, redisutil.GetDefaultInstance(), sloCreateTime, pb.UpdateVolumeType_Cancel); err != nil {
			return fmt.Errorf("decr high value volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.HighValue, highValueKey)
	}
	if parcelTypeAttr.IsDg {
		dgKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeDg)
		if _, err := v.UpdateRedisCounter(ctx, dgKey, redisutil.GetDefaultInstance(), sloCreateTime, pb.UpdateVolumeType_Cancel); err != nil {
			return fmt.Errorf("decr dg volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatParcelTypeVolume, monitoring.DG, dgKey)
	}
	return nil
}

func AddParcelTypeSuffix(key string, parcelType parcel_type_definition.ParcelType) string {
	if parcelType == parcel_type_definition.ParcelTypeNone {
		// None类型不需要加后缀
		return key
	}

	return fmt.Sprintf("%s:%s", key, parcelType)
}

func (v *VolumeCounterImpl) UpdateRouteVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, sloCreateTime int64,
	updateType pb.UpdateVolumeType, rm rule_mode.RuleMode,
) *srerr.Error {
	// SSCSMR-3055：双写country维度的redis
	key := productRouteVolumeKey(ctx, productID, routeCode, rm)
	newKey := maskProductRouteVolumeKey(ctx, maskProductID, productID, routeCode, rm)
	if err := v.UpdateRedisWithNewKey(ctx, key, newKey, sloCreateTime, updateType); err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}

	if groupCode != "" {
		if _, err := v.UpdateRedisCounter(ctx, groupRouteVolumeKey(ctx, groupCode, routeCode, rm), redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) DeductRouteVolume(
	ctx context.Context, maskingProductId int64, productId int64, groupCode string, routeCode string, sloCreateTime int64,
	parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, rm rule_mode.RuleMode) *srerr.Error {
	key := maskProductRouteVolumeKey(ctx, maskingProductId, productId, routeCode, rm)
	if err := v.DecrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr, sloCreateTime); err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}

	if groupCode != "" {
		if err := v.DecrParcelTypeVolumeByKey(ctx, groupRouteVolumeKey(ctx, groupCode, routeCode, rm), parcelTypeAttr, sloCreateTime); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) UpdateZoneVolume(ctx context.Context, maskProductID, productID int64, groupCode string,
	zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, updateType pb.UpdateVolumeType,
	rm rule_mode.RuleMode,
) *srerr.Error {
	// SSCSMR-3055：双写country维度的redis
	key := productZoneVolumeKey(ctx, productID, zoneCode, direction, rm)
	newKey := maskProductZoneVolumeKey(ctx, maskProductID, productID, zoneCode, direction, rm)
	if err := v.UpdateRedisWithNewKey(ctx, key, newKey, sloCreateTime, updateType); err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}

	if groupCode != "" {
		if _, err := v.UpdateRedisCounter(ctx, groupZoneVolumeKey(ctx, groupCode, zoneCode, direction, rm), redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) DeductZoneVolume(ctx context.Context, maskingProductId int64, productId int64, groupCode string,
	zoneCode string, direction rulevolume.MaskZoneDirection, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
	rm rule_mode.RuleMode) *srerr.Error {
	key := maskProductZoneVolumeKey(ctx, maskingProductId, productId, zoneCode, direction, rm)
	if err := v.DecrParcelTypeVolumeByKey(ctx, key, parcelTypeAttr, sloCreateTime); err != nil {
		logger.CtxLogErrorf(ctx, "update new key err:%v", err)
		return srerr.With(srerr.RedisErr, key, err)
	}

	if groupCode != "" {
		if err := v.DecrParcelTypeVolumeByKey(ctx, groupZoneVolumeKey(ctx, groupCode, zoneCode, direction, rm), parcelTypeAttr, sloCreateTime); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}

	return nil
}

func (v *VolumeCounterImpl) UpdateVolumeForRule(ctx context.Context, productID int64, ruleID int64, batchVolume int32, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	if batchVolume == 0 {
		// if batch volume == 0 that mean rule has not enable batch volume routing factor, so no need to update batch volume counter
		logger.CtxLogDebugf(ctx, "UpdateVolumeForRule|not enable batch volume, no need update batch volume counter")
		return nil
	}

	key := allocationRuleVolumesKey(ctx, ruleID)
	field := strconv.FormatInt(productID, 10)
	_, err := v.UpdateFieldRedisCounter(ctx, key, field, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}
	if updateType == pb.UpdateVolumeType_Create {
		checkMsg, err := jsoniter.Marshal(AllocateCheckBatchVolumeJobMsg{
			RuleID:      ruleID,
			BatchVolume: batchVolume,
		})
		if err != nil {
			logger.CtxLogErrorf(ctx, "Marshal check msg failed|err=%v", err)
			return srerr.With(srerr.JsonErr, nil, err)
		}
		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		newCtx := ctxhelper.CloneTrace(ctx)
		go func(ctx context.Context, namespace string, checkMsg []byte) {
			if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameAllocateCheckBatchVolumeCounter, checkMsg, nil, ""); err != nil {
				logger.CtxLogErrorf(ctx, "SendMessage failed|err=%v", err)
			}
		}(newCtx, namespace, checkMsg)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateShopGroupVolume(ctx context.Context, maskProductID, productID int64, groupCode string,
	shopGroupId int64, sloCreateTime int64, updateType pb.UpdateVolumeType, rm rule_mode.RuleMode) *srerr.Error {
	// SSCSMR-3055: 按mask+fulfillment，双写一份运力
	key := productShopGroupVolumeKey(ctx, productID, shopGroupId, rm)
	newKey := maskProductShopGroupVolumeKey(ctx, maskProductID, productID, shopGroupId, rm)
	if err := v.UpdateRedisWithNewKey(ctx, key, newKey, sloCreateTime, updateType); err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}

	if groupCode != "" {
		if _, err := v.UpdateRedisCounter(ctx, groupShopGroupVolumeKey(ctx, groupCode, shopGroupId, rm), redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}
	return nil
}

func (v *VolumeCounterImpl) GetMaskProductVolume(ctx context.Context, maskingProductId int64, timestamp int64, rm rule_mode.RuleMode) (int64, *srerr.Error) {
	key := productMaskVolumeKeyWithTimestamp(maskingProductId, timestamp, rm)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetFulfillmentProductVolume(ctx context.Context, maskingProductId, productId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := productVolumeKeyWithTimestamp(productId, rm, timestamp)
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = maskProductVolumeKeyWithTimestamp(maskingProductId, productId, rm, timestamp)
	}
	logger.CtxLogInfof(ctx, "GetFulfillmentProductVolume|key:%s", key)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetGroupCodeVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := groupVolumeKeyWithTimestamp(groupCode, rm, timestamp)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetMaskZoneVolume(ctx context.Context, maskProductId, productId int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := productZoneVolumeKeyWithTimestamp(productId, zoneCode, direction, rm, timestamp)
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = maskProductZoneVolumeKeyWithTimestamp(maskProductId, productId, zoneCode, direction, rm, timestamp)
	}
	logger.CtxLogInfof(ctx, "GetMaskZoneVolume|key:%s", key)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetGroupZoneVolume(ctx context.Context, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := groupZoneVolumeKeyWithTimestamp(groupCode, zoneCode, direction, rm, timestamp)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetMaskRouteVolume(ctx context.Context, maskProductId, productId int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := productRouteVolumeKeyWithTimestamp(productId, routeCode, rm, timestamp)
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = maskProductRouteVolumeKeyWithTimestamp(maskProductId, productId, routeCode, rm, timestamp)
	}
	logger.CtxLogInfof(ctx, "GetMaskRouteVolume|key:%s", key)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetGroupRouteVolume(ctx context.Context, groupCode string, routeCode string, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := groupRouteVolumeKeyWithTimestamp(groupCode, routeCode, rm, timestamp)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetMaskShopGroupVolume(ctx context.Context, maskProductId, productId int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := productShopGroupVolumeKeyWithTimestamp(productId, shopGroupId, rm, timestamp)
	if configutil.GetAllocateVolumeCountConf(ctx).UseMaskPrefix {
		key = maskProductShopGroupVolumeKeyWithTimestamp(maskProductId, productId, shopGroupId, rm, timestamp)
	}
	logger.CtxLogInfof(ctx, "GetMaskShopGroupVolume|key:%s", key)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) GetGroupShopGroupVolume(ctx context.Context, groupCode string, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) (int64, *srerr.Error) {
	key := groupShopGroupVolumeKeyWithTimestamp(groupCode, shopGroupId, rm, timestamp)
	count, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return count, nil
}

func (v *VolumeCounterImpl) BatchGetMaskZoneVolume(ctx context.Context, productId int64, zoneCodeList []string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	pipeline := redisutil.GetDefaultInstance().Pipeline()
	for _, zoneCode := range zoneCodeList {
		key := productZoneVolumeKeyWithTimestamp(productId, zoneCode, direction, rm, timestamp)
		pipeline.Get(ctx, key)
	}
	resultList, err := pipeline.Exec(ctx)
	if err != nil {
		return nil, srerr.With(srerr.RedisErr, "batch get mask zone code volume error", err)
	}
	var countList []int64
	for _, result := range resultList {
		tempCmd, ok := result.(*redis.StringCmd)
		if ok {
			count, err1 := tempCmd.Int64()
			if err1 != nil {
				monitoring.ReportError(ctx, monitoring.CatMaskingDashboardMonitor, monitoring.BatchGetZoneVolumeError, "redis result transfer to int64 error")
				return nil, srerr.With(srerr.RedisErr, "batch get mask zone code volume error|get count error", err1)
			}
			countList = append(countList, count)
		}
	}
	return countList, nil
}

func (v *VolumeCounterImpl) BatchGetMaskRouteVolume(ctx context.Context, productId int64, routeCodeList []string, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	pipeline := redisutil.GetDefaultInstance().Pipeline()
	for _, routeCode := range routeCodeList {
		key := productRouteVolumeKeyWithTimestamp(productId, routeCode, rm, timestamp)
		pipeline.Get(ctx, key)
	}
	resultList, err := pipeline.Exec(ctx)
	if err != nil {
		return nil, srerr.With(srerr.RedisErr, "batch get mask route code volume error", err)
	}
	var countList []int64
	for _, result := range resultList {
		tempCmd, ok := result.(*redis.StringCmd)
		if ok {
			count, err1 := tempCmd.Int64()
			if err1 != nil {
				monitoring.ReportError(ctx, monitoring.CatMaskingDashboardMonitor, monitoring.BatchGetRouteVolumeError, "redis result transfer to int64 error")
				return nil, srerr.With(srerr.RedisErr, "batch get route code volume error|get count error", err1)
			}
			countList = append(countList, count)
		}
	}
	return countList, nil
}

func (v *VolumeCounterImpl) BatchGetMaskShopGroupVolume(ctx context.Context, productId int64, shopGroupIdList []int64, rm rule_mode.RuleMode, timestamp int64) ([]int64, *srerr.Error) {
	pipeline := redisutil.GetDefaultInstance().Pipeline()
	for _, shopGroupId := range shopGroupIdList {
		key := productShopGroupVolumeKeyWithTimestamp(productId, shopGroupId, rm, timestamp)
		pipeline.Get(ctx, key)
	}
	resultList, err := pipeline.Exec(ctx)
	if err != nil {
		return nil, srerr.With(srerr.RedisErr, "batch get mask shop group volume error", err)
	}
	var countList []int64
	for _, result := range resultList {
		tempCmd, ok := result.(*redis.StringCmd)
		if ok {
			count, err1 := tempCmd.Int64()
			if err1 != nil {
				monitoring.ReportError(ctx, monitoring.CatMaskingDashboardMonitor, monitoring.BatchGetShopGroupVolumeError, "redis result transfer to int64 error")
				return nil, srerr.With(srerr.RedisErr, "batch get shop group volume error|get count error", err1)
			}
			countList = append(countList, count)
		}
	}
	return countList, nil
}

// SSCSMR-3055: 统一双写方法
func (v *VolumeCounterImpl) UpdateRedisWithNewKey(ctx context.Context, oldKey, newKey string, sloCreateTime int64, updateType pb.UpdateVolumeType) error {
	logger.CtxLogInfof(ctx, "UpdateMaskVolume|old key:%s, new key:%s", oldKey, newKey)
	// SSCSMR-3055：切换完毕后这个key的写入可以下掉
	_, err := v.UpdateRedisCounter(ctx, oldKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "update old key err:%v", err)
		monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "UpdateRedisWithOldKey", fmt.Sprintf("update old key err:%v", err))
		return err
	}

	//SSCSMR-3055 双写，多写一份按mask+fulfillment的redis值
	if configutil.GetAllocateVolumeCountConf(ctx).AllowNewRedisKey {
		_, err = v.UpdateRedisCounter(ctx, newKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "update new key err:%v", err)
			monitoring.ReportError(ctx, monitoring.CatMaskingVolume, "UpdateRedisWithNewKey", fmt.Sprintf("update new key err:%v", err))
			return err
		}
	}

	return nil
}
