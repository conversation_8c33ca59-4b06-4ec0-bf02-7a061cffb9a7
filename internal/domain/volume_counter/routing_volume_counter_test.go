package volume_counter

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	smart_routing_protobuf "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestVolumeCounterImpl_IncrParcelTypeVolumeByKey(t *testing.T) {
	ctx := context.Background()
	v := &VolumeCounterImpl{}
	var patch *gomonkey.Patches
	type args struct {
		key            string
		sloCreateTime  int64
		parcelTypeAttr *parcel_type_definition.ParcelTypeAttr
		updateType     smart_routing_protobuf.UpdateVolumeType
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: UpdateRedisCounter error",
			args: args{
				parcelTypeAttr: &parcel_type_definition.ParcelTypeAttr{
					IsCod:       true,
					IsBulky:     true,
					IsHighValue: true,
					IsDg:        true,
				},
			},
			wantErr: fmt.Errorf("incr cod volume: %v", errors.New("mock UpdateRedisCounter error")),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(v), "UpdateRedisCounter", func(v *VolumeCounterImpl, ctx context.Context, key string, redisCounter *redis.Client, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) (int64, error) {
					return 0, errors.New("mock UpdateRedisCounter error")
				})
			},
		},
		{
			name: "case 2: IncrParcelTypeVolumeByKey success",
			args: args{
				parcelTypeAttr: &parcel_type_definition.ParcelTypeAttr{
					IsCod:       true,
					IsBulky:     true,
					IsHighValue: true,
					IsDg:        true,
				},
			},
			wantErr: nil,
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(v), "UpdateRedisCounter", func(v *VolumeCounterImpl, ctx context.Context, key string, redisCounter *redis.Client, sloCreateTime int64, updateType smart_routing_protobuf.UpdateVolumeType) (int64, error) {
					return 0, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if err := v.IncrParcelTypeVolumeByKey(ctx, tt.args.key, tt.args.sloCreateTime, tt.args.parcelTypeAttr, tt.args.updateType); (err != nil) != (tt.wantErr != nil) || (err != nil && err.Error() != tt.wantErr.Error()) {
				t.Errorf("VolumeCounterImpl.IncrParcelTypeVolumeByKey() error = %v, wantErr %v", err, tt.wantErr)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
