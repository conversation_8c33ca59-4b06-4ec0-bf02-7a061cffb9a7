package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	rc "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"regexp"
	"strconv"
)

const (
	PrefixMaskProductVolume           = "mask_product_volume:"
	PrefixProductVolume               = "product_volume:"
	PrefixGroupVolume                 = "group_volume:"
	PrefixAllocationRuleBatchVolumes  = "alloc_rule_batch_volumes:"
	PrefixOrderCount                  = "order_count:"
	PrefixWeight                      = "weight:"
	PrefixILHProductOrderCount        = "ilh_product_order_count:"
	PrefixILHProductWeight            = "ilh_product_weight:"
	PrefixILHProductLaneCartonCount   = "ilh_product_lane_carton_count:"
	PrefixILHProductLaneParcelCount   = "ilh_product_lane_parcel_count:"
	PrefixILHProductLaneWeight        = "ilh_product_lane_weight:"
	PrefixProductLanesOrderCount      = "product_lanes_order_counts:"
	PrefixMultiProductLanesOrderCount = "multi_product_lanes_order_counts:"
	PrefixLaneOrderCount              = "lane_order_count:"
	PrefixActualPointCount            = "actual_point_count:"
	PrefixILHCombination              = "ilh_combination"
	KeyCartonQuantity                 = "carton"
	KeyParcelQuantity                 = "parcel"
	KeyWeight                         = "weight"
	PrefixRoutingDashboard            = "routing_dashboard:"
)

type VolumeCounter interface {
	GetProductVolume(ctx context.Context, productID int) (int, *srerr.Error)
	GetLaneVolume(ctx context.Context, productID int, laneCode string) (int, *srerr.Error)
	BatchGetLaneVolume(ctx context.Context, productID int, laneCodeList []string) ([]int, *srerr.Error)
	GetLineVolume(ctx context.Context, productID int, lineID, parcelType string) (int, *srerr.Error)
	BatchGetLineVolume(ctx context.Context, productID int, lineIDList []string) ([]int, *srerr.Error)
	GetLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode, parcelType string) (int, *srerr.Error)
	IncrLineVolume(ctx context.Context, productID int, lineID string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error
	IncrLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error

	ILHVolumeCounter
	RoutingVolumeCounterInterface
	AllocationVolumeCounterInterface
}

type VolumeCounterImpl struct {
	redisCounter rc.RedisCounter
	laneService  lane.LaneService
}

func NewVolumeCounterImpl(redisCounter rc.RedisCounter, laneService lane.LaneService) *VolumeCounterImpl {
	return &VolumeCounterImpl{
		redisCounter: redisCounter,
		laneService:  laneService,
	}
}

func (v *VolumeCounterImpl) GetLineVolume(ctx context.Context, productID int, lineID, parcelType string) (int, *srerr.Error) {
	val, err := redisutil.GetDefaultInstance().Get(ctx, formatLineVolumeKey(ctx, productID, lineID, parcelType)).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) BatchGetLineVolume(ctx context.Context, productID int, lineIDList []string) ([]int, *srerr.Error) {
	var (
		keyList = make([]string, 0, len(lineIDList))
		valList = make([]int, 0, len(lineIDList))
	)

	if len(lineIDList) == 0 {
		return valList, nil
	}

	for _, lineID := range lineIDList {
		keyList = append(keyList, formatLineVolumeKey(ctx, productID, lineID, ""))
	}

	result, err := redisutil.GetDefaultInstance().MGet(ctx, keyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}

	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			valList = append(valList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		valList = append(valList, intVal)
	}

	return valList, nil
}

func (v *VolumeCounterImpl) GetLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode, parcelType string) (int, *srerr.Error) {
	val, err := redisutil.GetDefaultInstance().Get(ctx, formatLineZoneVolumeKey(ctx, productID, lineID, zoneCode, parcelType)).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) IncrLineVolume(ctx context.Context, productID int, lineID string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "live counter unsupported method")
	return nil
}

func (v *VolumeCounterImpl) IncrLineZoneVolume(ctx context.Context, productID int, lineID string, zoneCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "live counter unsupported method")
	return nil
}

func (v *VolumeCounterImpl) GetProductVolume(ctx context.Context, productID int) (int, *srerr.Error) {
	val, err := redisutil.GetDefaultInstance().Get(ctx, formatProductVolumeKey(ctx, productID)).Int()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetLaneVolume(ctx context.Context, productID int, laneCode string) (int, *srerr.Error) {
	val, err := redisutil.GetDefaultInstance().Get(ctx, formatLaneKey(ctx, productID, laneCode)).Int()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) BatchGetLaneVolume(ctx context.Context, productID int, laneCodeList []string) ([]int, *srerr.Error) {
	var (
		keyList = make([]string, 0, len(laneCodeList))
		valList = make([]int, 0, len(laneCodeList))
	)

	if len(laneCodeList) == 0 {
		return valList, nil
	}

	for _, laneCode := range laneCodeList {
		keyList = append(keyList, formatLaneKey(ctx, productID, laneCode))
	}

	result, err := redisutil.GetDefaultInstance().MGet(ctx, keyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}

	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			valList = append(valList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		valList = append(valList, intVal)
	}

	return valList, nil
}

func (v *VolumeCounterImpl) UpdateRedisCounter(ctx context.Context, key string, redisCounter *redis.Client, sloCreateTime int64, updateType pb.UpdateVolumeType) (int64, error) {
	switch updateType {
	case pb.UpdateVolumeType_Create:
		cnt, err := redisCounter.Incr(ctx, key).Result()
		logger.CtxLogDebugf(ctx, "UpdateRedisCounter|create scene|incr volume, key=%v, newCnt=%v, err=%v", key, cnt, err)
		return cnt, err
	case pb.UpdateVolumeType_Cancel:
		cancelOrderKey, err := replaceVolumeKey(key, sloCreateTime)
		if err != nil {
			// 运力归零的时间，目前是每个时区的零点，如果运力归零则不需要减运力。只有传了sloCreateTime的时候才需要考虑运力归零的限制。当获取取消单的key失败时，需要考虑归零时间的限制
			localZeroTime := getMakeZeroTime(ctx)
			if sloCreateTime < localZeroTime && sloCreateTime != 0 {
				logger.CtxLogDebugf(ctx, "UpdateRedisCounter|cancel scene|slo create time less than zero time, key=%v, sloCreateTIme=%v", key, sloCreateTime)
				return 0, nil
			}
		}
		key = cancelOrderKey
		// 减运力不能减成负数
		cnt, err := redisCounter.Get(ctx, key).Int64()
		// key不存在时，获取key会报错，忽略该错误
		if err != nil {
			logger.CtxLogDebugf(ctx, "UpdateRedisCounter|cancel scene|get old cnt error, key=%v, err=%v", key, err)
			return cnt, nil
		}
		if cnt <= 0 {
			logger.CtxLogDebugf(ctx, "UpdateRedisCounter|cancel scene|old cnt less than 0, key=%v, oldCnt=%v", key, cnt)
			return cnt, nil
		}
		// 减运力
		newCnt, err := redisCounter.Decr(ctx, key).Result()
		logger.CtxLogDebugf(ctx, "UpdateRedisCounter|cancel scene|decr volume, key=%v, newCnt=%v, err=%v", key, newCnt, err)
		return newCnt, err
	}
	return 0, nil
}

func (v *VolumeCounterImpl) UpdateFieldRedisCounter(ctx context.Context, key string, field string, redisCounter *redis.Client, sloCreateTime int64, updateType pb.UpdateVolumeType) (int64, error) {
	switch updateType {
	case pb.UpdateVolumeType_Create:
		cnt, err := redisCounter.HIncrBy(ctx, key, field, 1).Result()
		logger.CtxLogDebugf(ctx, "UpdateFieldRedisCounter|create scene|incr volume, key=%v, field=%v, newCnt=%v, err=%v", key, field, cnt, err)
		return cnt, err
	case pb.UpdateVolumeType_Cancel:
		cancelOrderKey, err := replaceVolumeKey(key, sloCreateTime)
		if err != nil {
			// 运力归零的时间，目前是每个时区的零点，如果运力归零则不需要减运力。只有传了sloCreateTime的时候才需要考虑运力归零的限制。当获取取消单的key失败时，需要考虑归零时间的限制
			localZeroTime := getMakeZeroTime(ctx)
			if sloCreateTime < localZeroTime && sloCreateTime != 0 {
				logger.CtxLogDebugf(ctx, "UpdateFieldRedisCounter|cancel scene|slo create time less than zero time, key=%v, field=%v, sloCreateTime=%v", key, field, sloCreateTime)
				return 0, nil
			}
		}
		key = cancelOrderKey
		// 减运力不能减成负数
		cnt, err := redisCounter.HGet(ctx, key, field).Int64()
		// key不存在时，获取key会报错，忽略该错误
		if err != nil {
			logger.CtxLogDebugf(ctx, "UpdateFieldRedisCounter|cancel scene|get oldCnt error, key=%v, field=%v, err=%v", key, field, err)
			return cnt, nil
		}
		if cnt <= 0 {
			logger.CtxLogDebugf(ctx, "UpdateFieldRedisCounter|cancel scene|oldCnt less than 0, key=%v, field=%v, oldCnt=%v", key, field, cnt)
			return cnt, nil
		}
		// 减运力
		newCnt, err := redisCounter.HIncrBy(ctx, key, field, -1).Result()
		logger.CtxLogDebugf(ctx, "UpdateFieldRedisCounter|cancel scene|decr volume, key=%v, field=%v, newCnt=%v, err=%v", key, field, newCnt, err)
		return newCnt, err
	}
	return 0, nil
}

func formatLaneKey(ctx context.Context, productID int, laneCode string) string {
	return PrefixLaneOrderCount + fmt.Sprintf("%v:%v:%v", productID, laneCode, timeutil.GetLocalTime(ctx).Format(constant.TimeLayout))
}

func formatProductVolumeKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixOrderCount, date, productID)
}

func formatProductParcelVolumeKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v:%v", PrefixOrderCount, KeyParcelQuantity, date, productID)
}

func formatProductWeightKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixWeight, date, productID)
}

func formatProductVolumeKey(ctx context.Context, productID int) string {
	return formatProductVolumeKeyByDate(productID, timeutil.GetLocalTime(ctx).Format(constant.TimeLayout))
}

func formatLineVolumeKey(ctx context.Context, productID int, lineID, parcelType string) string {
	if parcelType != "" {
		return formatProductVolumeKey(ctx, productID) + ":" + lineID + ":" + parcelType
	}
	return formatProductVolumeKey(ctx, productID) + ":" + lineID
}

func formatLineZoneVolumeKey(ctx context.Context, productID int, lineID, zoneCode, parcelType string) string {
	if parcelType != "" {
		return formatLineVolumeKey(ctx, productID, lineID, "") + ":" + zoneCode + ":" + parcelType
	}
	return formatLineVolumeKey(ctx, productID, lineID, "") + ":" + zoneCode
}

func formatProductLanesKey(ctx context.Context, productID int) string {
	return formatProductLanesKeyByDate(productID, timeutil.GetLocalTime(ctx).Format(constant.TimeLayout))
}

func formatProductLanesField(laneCode string, dgFlag int, serviceCode string) string {
	field := fmt.Sprintf("%v:%v", laneCode, dgFlag)
	if serviceCode != "" {
		field = fmt.Sprintf("%v:%v", field, serviceCode)
	}
	return field
}

func formatProductMultiLanesField(interLaneCode, localLaneCode string, dgFlag int, hoPoint, serviceCode string) string {
	field := fmt.Sprintf("%v:%v:%v:%v", interLaneCode, localLaneCode, dgFlag, hoPoint)
	if serviceCode != "" {
		field = fmt.Sprintf("%v:%v", field, serviceCode)
	}

	return field
}

func formatMultiProductLanesKey(ctx context.Context, productID int) string {
	return formatMultiProductLanesKeyByDate(productID, timeutil.GetLocalTime(ctx).Format(constant.TimeLayout))
}

func productMaskVolumeKey(ctx context.Context, maskProductID int64, rm rule_mode.RuleMode) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixMaskProductVolume + "wms:" + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(maskProductID, 10)
	}
	return PrefixMaskProductVolume + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(maskProductID, 10)
}

func productMaskVolumeKeyWithTimestamp(maskProductID int64, timestamp int64, rm rule_mode.RuleMode) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixMaskProductVolume + "wms:" + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(maskProductID, 10)
	}
	return PrefixMaskProductVolume + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(maskProductID, 10)
}

func productVolumeKey(ctx context.Context, productID int64, rm rule_mode.RuleMode) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(productID, 10)
}

func groupVolumeKey(ctx context.Context, groupCode string, rm rule_mode.RuleMode) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixGroupVolume + "wms:" + timeutil.GetTodayDateString(ctx) + ":" + groupCode
	}

	return PrefixGroupVolume + timeutil.GetTodayDateString(ctx) + ":" + groupCode
}

func groupVolumeKeyWithTimestamp(groupCode string, rm rule_mode.RuleMode, timestamp int64) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixGroupVolume + "wms:" + timeutil.GetTimestampString(timestamp) + ":" + groupCode
	}
	return PrefixGroupVolume + timeutil.GetTimestampString(timestamp) + ":" + groupCode
}

//SSCSMR-3055 按mask+fulfillment维护运力
func maskProductVolumeKey(ctx context.Context, maskProductID, productID int64, rm rule_mode.RuleMode) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
}

func productVolumeKeyWithTimestamp(productID int64, rm rule_mode.RuleMode, timestamp int64) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(productID, 10)
}

// SSCSMR-3055 using mask+fulfillment as prefix
func maskProductVolumeKeyWithTimestamp(maskProductID, productID int64, rm rule_mode.RuleMode, timestamp int64) string {
	if rm == rule_mode.WmsOrderRule {
		return PrefixProductVolume + "wms:" + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
	}
	return PrefixProductVolume + timeutil.GetTimestampString(timestamp) + ":" + strconv.FormatInt(maskProductID, 10) + ":" + strconv.FormatInt(productID, 10)
}

func productRouteVolumeKey(ctx context.Context, productID int64, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:route:%s", productVolumeKey(ctx, productID, rm), routeCode)
}

// SSCSMR-3055: 按mask+fulfillment维护运力
func maskProductRouteVolumeKey(ctx context.Context, maskProductID, productID int64, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:route:%s", maskProductVolumeKey(ctx, maskProductID, productID, rm), routeCode)
}

func groupRouteVolumeKey(ctx context.Context, groupCode, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:route:%s", groupVolumeKey(ctx, groupCode, rm), routeCode)
}

func groupRouteVolumeKeyWithTimestamp(groupCode, routeCode string, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:route:%s", groupVolumeKeyWithTimestamp(groupCode, rm, timestamp), routeCode)
}

func productRouteVolumeKeyWithTimestamp(productID int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:route:%s", productVolumeKeyWithTimestamp(productID, rm, timestamp), routeCode)
}

// SSCSMR-3055 using mask+fulfillment as prefix
func maskProductRouteVolumeKeyWithTimestamp(maskProductID, productID int64, routeCode string, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:route:%s", maskProductVolumeKeyWithTimestamp(maskProductID, productID, rm, timestamp), routeCode)
}

func productZoneVolumeKey(ctx context.Context, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:zone:%s:%s", productVolumeKey(ctx, productID, rm), zoneCode, direction)
}

func groupZoneVolumeKey(ctx context.Context, groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:zone:%s:%s", groupVolumeKey(ctx, groupCode, rm), zoneCode, direction)
}

func groupZoneVolumeKeyWithTimestamp(groupCode string, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:zone:%s:%s", groupVolumeKeyWithTimestamp(groupCode, rm, timestamp), zoneCode, direction)
}

//SSCSMR-3055: 按mask+fulfillment维护运力
func maskProductZoneVolumeKey(ctx context.Context, maskProductID, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:zone:%s:%s", maskProductVolumeKey(ctx, maskProductID, productID, rm), zoneCode, direction)
}

func productZoneVolumeKeyWithTimestamp(productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:zone:%s:%s", productVolumeKeyWithTimestamp(productID, rm, timestamp), zoneCode, direction)
}

// SSCSMR-3055:using mask+fulfillmeng as prefix
func maskProductZoneVolumeKeyWithTimestamp(maskProductID, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:zone:%s:%s", maskProductVolumeKeyWithTimestamp(maskProductID, productID, rm, timestamp), zoneCode, direction)
}

func allocationRuleVolumesKey(ctx context.Context, ruleID int64) string {
	return PrefixAllocationRuleBatchVolumes + timeutil.GetTodayDateString(ctx) + ":" + strconv.FormatInt(ruleID, 10)
}

func getMakeZeroTime(ctx context.Context) int64 {
	return timeutil.GetLocalZeroTime(ctx)
}

func replaceVolumeKey(key string, sloCreateTime int64) (string, error) {
	if sloCreateTime == 0 {
		return key, nil
	}
	reg, err := regexp.Compile(`\d{4}-\d{2}-\d{2}`)
	if err != nil {
		return key, err
	}
	if reg.MatchString(key) {
		sloCreateTimeStr := timeutil.GetTimestampDateString(sloCreateTime, timeutil.DateFormat)
		return reg.ReplaceAllString(key, sloCreateTimeStr), nil
	}
	reg, err = regexp.Compile(`\d{4}\d{2}\d{2}`)
	if err != nil {
		return key, err
	}
	if reg.MatchString(key) {
		sloCreateTimeStr := timeutil.GetTimestampDateString(sloCreateTime, constant.TimeLayout)
		return reg.ReplaceAllString(key, sloCreateTimeStr), nil
	}
	return key, nil
}

func formatLineActualPointKey(ctx context.Context, productID int64, laneCode string, lineId string, actualPoint string, actualPointType int32) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v:%v", PrefixActualPointCount, productID, timeutil.GetTodayDateString(ctx), laneCode, lineId, actualPoint, actualPointType)
}

func formatActualPointKey(ctx context.Context, productID int64, laneCode string, actualPoint string, actualPointType int32) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v", PrefixActualPointCount, productID, timeutil.GetTodayDateString(ctx), laneCode, actualPoint, actualPointType)
}

func groupShopGroupVolumeKey(ctx context.Context, groupCode string, shopGroupId int64, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:shop_group:%v", groupVolumeKey(ctx, groupCode, rm), shopGroupId)
}

func groupShopGroupVolumeKeyWithTimestamp(groupCode string, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:shop_group:%v", groupVolumeKeyWithTimestamp(groupCode, rm, timestamp), shopGroupId)
}

func productShopGroupVolumeKey(ctx context.Context, productID int64, shopGroupId int64, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:shop_group:%v", productVolumeKey(ctx, productID, rm), shopGroupId)
}

// SSCSMR-3055:using mask+fulfillment as prefix
func maskProductShopGroupVolumeKey(ctx context.Context, maskProductID, productID int64, shopGroupId int64, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:shop_group:%v", maskProductVolumeKey(ctx, maskProductID, productID, rm), shopGroupId)
}

func productShopGroupVolumeKeyWithTimestamp(productID int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:shop_group:%v", productVolumeKeyWithTimestamp(productID, rm, timestamp), shopGroupId)
}

// SSCSMR-3055:using mask+fulfillment as prefix
func maskProductShopGroupVolumeKeyWithTimestamp(maskProductID, productID int64, shopGroupId int64, rm rule_mode.RuleMode, timestamp int64) string {
	return fmt.Sprintf("%s:shop_group:%v", maskProductVolumeKeyWithTimestamp(maskProductID, productID, rm, timestamp), shopGroupId)
}

func formatRoutingDashboardKey(ctx context.Context, productID int64, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode string, timestamp int64) string {
	return fmt.Sprintf("v1_%v:%v:%v:%v:%v:%v:%v:%v:%v", PrefixRoutingDashboard, productID, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, timeutil.GetLocalTimeByTimestamp(timestamp).Format(timeutil.DateFormat))
}

func formatRoutingZoneDashboardKey(ctx context.Context, productID int64, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode string, timestamp int64) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v:%v:%v:%v", PrefixRoutingDashboard, productID, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}
