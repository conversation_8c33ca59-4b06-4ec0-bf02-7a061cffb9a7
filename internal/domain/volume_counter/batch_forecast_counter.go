package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"strconv"
)

type BatchAllocateForecastCounter struct {
	zoneCounter             map[string]int64
	routeCounter            map[string]int64
	countryCounter          map[uint64]int64
	countryParcelCounter    map[string]int64
	shopFulfillmentProducts map[uint64]map[uint64]struct{}
	pickupEffUsedBudget     float64
}

func NewBatchAllocateForecastCounter() *BatchAllocateForecastCounter {
	return &BatchAllocateForecastCounter{
		countryCounter:          make(map[uint64]int64),
		countryParcelCounter:    make(map[string]int64),
		zoneCounter:             make(map[string]int64),
		routeCounter:            make(map[string]int64),
		shopFulfillmentProducts: make(map[uint64]map[uint64]struct{}),
		pickupEffUsedBudget:     0,
	}
}

func (b *BatchAllocateForecastCounter) IncrTargetZoneVolume(ctx context.Context, fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType, volume int64) {
	key := zoneVolumeKey(fulfillmentProductId, code, parcelType)
	b.zoneCounter[key] += volume
}

func (b *BatchAllocateForecastCounter) IncrTargetRouteVolume(ctx context.Context, fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType, volume int64) {
	key := routeVolumeKey(fulfillmentProductId, code, parcelType)
	b.routeCounter[key] += volume
}

func (b *BatchAllocateForecastCounter) AddShopFulfillmentProducts(ctx context.Context, shopId, fulfillmentProductId uint64) {
	if b.shopFulfillmentProducts[shopId] == nil {
		b.shopFulfillmentProducts[shopId] = make(map[uint64]struct{})
	}
	b.shopFulfillmentProducts[shopId][fulfillmentProductId] = struct{}{}
}

func (b *BatchAllocateForecastCounter) IncrPickupEffUsedBudget(ctx context.Context, used float64) {
	b.pickupEffUsedBudget += used
}

func (b *BatchAllocateForecastCounter) GetTargetZoneVolume(ctx context.Context, fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType) int64 {
	key := zoneVolumeKey(fulfillmentProductId, code, parcelType)
	return b.zoneCounter[key]
}

func (b *BatchAllocateForecastCounter) GetTargetRouteVolume(ctx context.Context, fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType) int64 {
	key := routeVolumeKey(fulfillmentProductId, code, parcelType)
	return b.routeCounter[key]
}

func (b *BatchAllocateForecastCounter) GetCountryVolume(ctx context.Context, fulfillmentProductId uint64) int64 {
	return b.countryCounter[fulfillmentProductId]
}

func zoneVolumeKey(fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType) string {
	return fmt.Sprintf("%s:%d:%s:%s", ZoneVolumePrefix, fulfillmentProductId, code, parcelType)
}

func routeVolumeKey(fulfillmentProductId uint64, code string, parcelType parcel_type_definition.ParcelType) string {
	return fmt.Sprintf("%s:%d:%s:%s", RouteVolumePrefix, fulfillmentProductId, code, parcelType)
}

func (b *BatchAllocateForecastCounter) IncrFulfillmentProductParcelTypeVolume(ctx context.Context, productId uint64, parcelType parcel_type_definition.ParcelType, count int64) {
	if parcelType == parcel_type_definition.ParcelTypeNone {
		b.countryCounter[productId] += count
	} else {
		b.countryParcelCounter[volumecounter.AddParcelTypeSuffix(strconv.FormatUint(productId, 10), parcelType)] += count
	}
}

func (b *BatchAllocateForecastCounter) GetFulfillmentProductParcelTypeVolume(ctx context.Context, fulfillmentProductId uint64, parcelType parcel_type_definition.ParcelType) int64 {
	if parcelType == parcel_type_definition.ParcelTypeNone {
		return b.countryCounter[fulfillmentProductId]
	}
	return b.countryParcelCounter[volumecounter.AddParcelTypeSuffix(strconv.FormatUint(fulfillmentProductId, 10), parcelType)]
}

func (b *BatchAllocateForecastCounter) GetCountryVolumeResult() map[uint64]int64 {
	return b.countryCounter
}

func (b *BatchAllocateForecastCounter) GetTargetZoneResult() map[string]int64 {
	return b.zoneCounter
}

func (b *BatchAllocateForecastCounter) GetTargetRouteResult() map[string]int64 {
	return b.routeCounter
}

func (b *BatchAllocateForecastCounter) GetPickupEffUsedBudget() float64 {
	return b.pickupEffUsedBudget
}

func (b *BatchAllocateForecastCounter) GetShopFulfillmentProducts() map[uint64][]uint64 {
	shopFulfillmentProducts := make(map[uint64][]uint64, len(b.shopFulfillmentProducts))
	for shopId, products := range b.shopFulfillmentProducts {
		shopFulfillmentProducts[shopId] = make([]uint64, 0, len(products))
		for productId := range products {
			shopFulfillmentProducts[shopId] = append(shopFulfillmentProducts[shopId], productId)
		}
	}
	return shopFulfillmentProducts
}
