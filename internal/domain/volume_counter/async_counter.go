package volume_counter

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
)

type AsyncCounter struct {
	ch          chan forecastentity.ForecastLaneIncrInfo
	values      map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult
	done        chan struct{}
	weightRange []persistent.WeightRange
}

func NewAsyncCounter() *AsyncCounter {
	ch := make(chan forecastentity.ForecastLaneIncrInfo)
	c := &AsyncCounter{
		ch:     ch,
		values: map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult{},
		done:   make(chan struct{}),
	}

	go func() {
		for incrInfo := range ch {
			key := incrInfo.ForecastLane
			if _, exist := c.values[key]; !exist {
				c.values[key] = &forecastentity.ForecastLaneResult{}
			}
			c.values[key].Quantity += incrInfo.Quantity
			c.values[key].IlhParcelQuantity += incrInfo.IlhParcelQuantity
			c.values[key].Weight += incrInfo.Weight
			c.values[key].AsfInfoList = append(c.values[key].AsfInfoList, forecastentity.AsfInfo{
				HasAsf: incrInfo.HasAsf,
				Asf:    incrInfo.Asf,
				AsfUsd: incrInfo.AsfUsd,
			})
		}
		close(c.done)
	}()

	return c
}

func NewAsyncCounterForLocalSpx(weightRange []persistent.WeightRange) *AsyncCounter {
	ch := make(chan forecastentity.ForecastLaneIncrInfo)
	c := &AsyncCounter{
		ch:          ch,
		values:      map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult{},
		done:        make(chan struct{}),
		weightRange: weightRange,
	}

	go func() {
		for incrInfo := range ch {
			//取这个weight属于哪一个weight range范围
			index := -1
			for i, wr := range c.weightRange {
				if incrInfo.SkuWeight >= float32(wr.Min) && incrInfo.SkuWeight <= float32(wr.Max) {
					index = i
					break
				}
			}
			//如果weight没有在这个范围内就，就用-1，-1记录
			if index >= 0 {
				incrInfo.ForecastLane.WeightRange = c.weightRange[index]
			} else {
				incrInfo.ForecastLane.WeightRange = persistent.WeightRange{Min: -1, Max: -1}
			}

			key := incrInfo.ForecastLane
			if _, exist := c.values[key]; !exist {
				c.values[key] = &forecastentity.ForecastLaneResult{}
			}
			c.values[key].Quantity += incrInfo.Quantity
			c.values[key].Weight += incrInfo.Weight
			//c.values[key].SkuWeight += incrInfo.SkuWeight
			c.values[key].ShippingFee += incrInfo.ShippingFee
		}
		close(c.done)
	}()

	return c
}

func (c *AsyncCounter) Incr(laneCode string, dgFlag int, ruleId int, serviceCode string, asf, asfUsd float64, hasAsf bool) {
	c.ch <- forecastentity.ForecastLaneIncrInfo{
		ForecastLane: forecastentity.ForecastLaneInfo{
			LaneCode:    laneCode,
			DGFlag:      dgFlag,
			RuleId:      ruleId,
			ServiceCode: serviceCode,
		},
		Quantity: 1,
		HasAsf:   hasAsf,
		Asf:      asf,
		AsfUsd:   asfUsd,
	}
}

func (c *AsyncCounter) IlhIncr(laneCode string, dgFlag int, ruleId int, serviceCode string, weight, parcelQuantity int, statDate string) {
	c.ch <- forecastentity.ForecastLaneIncrInfo{
		ForecastLane:      forecastentity.ForecastLaneInfo{LaneCode: laneCode, DGFlag: dgFlag, RuleId: ruleId, ServiceCode: serviceCode, StatDate: statDate},
		Quantity:          1,
		Weight:            weight,
		IlhParcelQuantity: parcelQuantity,
	}
}

func (c *AsyncCounter) GetResult() map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult {
	close(c.ch)
	<-c.done
	return c.values
}

// 按照天维度保存数据
func (c *AsyncCounter) GetResultByDay() map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult {
	res := c.values
	c.values = map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult{}
	return res
}

func (c *AsyncCounter) Clear() map[forecastentity.ForecastLaneInfo]*forecastentity.ForecastLaneResult {
	close(c.ch)
	<-c.done
	return c.values
}

func (c *AsyncCounter) IncrLocal(laneCode, site string, buyerCityId int64, shippingFee float64, weight float32, ruleId int, priority int32, outActualPoint string) {
	c.ch <- forecastentity.ForecastLaneIncrInfo{
		ForecastLane: forecastentity.ForecastLaneInfo{LaneCode: laneCode, Site: site, BuyerCityId: buyerCityId, RuleId: ruleId, Priority: priority, OutActualPoint: outActualPoint},
		Quantity:     1,
		ShippingFee:  shippingFee,
		SkuWeight:    weight,
	}
}
