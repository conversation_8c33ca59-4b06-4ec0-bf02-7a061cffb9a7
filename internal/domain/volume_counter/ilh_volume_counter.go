package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
	"time"
)

type ILHVolumeCounter interface {
	// Line Carton
	GetILHTwsCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHTwsDpCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)
	IncrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error
	DecrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error

	// Line Parcel
	GetILHTwsParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHTwsDpParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)
	IncrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error
	DecrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error

	// Line Wight
	GetILHTwsWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHTwsDpWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)
	IncrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error
	DecrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error

	// Combination
	IncrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error
	DecrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error
	GetILHCombinationCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHCombinationParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHCombinationWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error)
	GetILHCombinationDpCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)
	GetILHCombinationDpParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)
	GetILHCombinationDpWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error)

	// Product Carton
	IncrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error
	DecrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error

	// Product Weight
	IncrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error
	DecrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error

	// ProductLane
	IncrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error
	DecrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error
	IncrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error
	DecrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error
	IncrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error
	DecrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error

	// Product Carton、Parcel、Weight Counter for Admin
	GetILHProductCounter(ctx context.Context, productID int, days []time.Time) (map[ILHProductCounter]*ILHProductCounterResult, int, int, int, *srerr.Error)

	GetMultiProductServiceCodeVolumes(ctx context.Context, productID int, days []time.Time) (map[string]int, int, *srerr.Error)
	GetProductLaneVolumes(ctx context.Context, productID int, days []time.Time) (map[LaneCounter]int, int, *srerr.Error)
}

func (v *VolumeCounterImpl) GetILHTwsCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh carton volume|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) GetILHTwsParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh parcel volume|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) GetILHTwsDpCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsDpCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh carton volume|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) GetILHTwsDpParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsDpParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh parcel volume|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) IncrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	key := formatILHTwsCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "incr ilh carton volume|key=%s", key)
	if res := redisutil.GetDefaultInstance().Incr(ctx, key); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().Incr(ctx, portKey); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	key := formatILHTwsParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "incr ilh parcel volume|key=%s", key)
	if res := redisutil.GetDefaultInstance().IncrBy(ctx, key, parcelQuantity); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().IncrBy(ctx, portKey, parcelQuantity); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	key := formatILHTwsCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "decr ilh carton volume|key=%s", key)
	if res := redisutil.GetDefaultInstance().Decr(ctx, key); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().Decr(ctx, portKey); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	key := formatILHTwsParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "decr ilh parcel volume|key=%s", key)
	if res := redisutil.GetDefaultInstance().Decr(ctx, key); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().Decr(ctx, portKey); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) GetILHTwsWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsWightKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh weight|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) GetILHTwsDpWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := formatILHTwsDpWightKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	logger.CtxLogDebugf(ctx, "get ilh weight|key=%s", key)
	val, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil && err != redis.Nil {
		return 0, srerr.With(srerr.CodisErr, val, err)
	}

	return int(val), nil
}

func (v *VolumeCounterImpl) IncrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHTwsWightKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "incr ilh weight|key=%s", key)
	if res := redisutil.GetDefaultInstance().IncrBy(ctx, key, weight); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpWightKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().IncrBy(ctx, portKey, weight); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {

	// 对ILH Combination的Carton、Parcel、Weight进行统计增加
	key := formatILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, timestamp)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyCartonQuantity, 1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyParcelQuantity, parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyWeight, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	// 对ILH Combination + Destination Port的Carton、Parcel、Weight进行统计增加
	portKey := formatILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort, timestamp)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyCartonQuantity, 1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyParcelQuantity, parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyWeight, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {

	// 对ILH Combination的Carton、Parcel、Weight进行统计减少
	key := formatILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, timestamp)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyCartonQuantity, -1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyParcelQuantity, -parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, KeyWeight, -weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	// 对ILH Combination + Destination Port的Carton、Parcel、Weight进行统计减少
	portKey := formatILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort, timestamp)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyCartonQuantity, -1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyParcelQuantity, -parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, portKey, KeyWeight, -weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, portKey, err)
	}

	return nil
}

func (v *VolumeCounterImpl) GetILHCombinationCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyCartonQuantity).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetILHCombinationParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyParcelQuantity).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetILHCombinationWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyWeight).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetILHCombinationDpCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyCartonQuantity).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetILHCombinationDpParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyParcelQuantity).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) GetILHCombinationDpWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID,
	lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {

	key := formatILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort, timestamp)
	val, err := redisutil.GetDefaultInstance().HGet(ctx, key, KeyWeight).Int()
	if err != nil {
		return 0, srerr.With(srerr.CodisErr, key, err)
	}

	return val, nil
}

func (v *VolumeCounterImpl) DecrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHTwsWightKey(ctx, productID, lineID, dgType, twsCode, timestamp)
	logger.CtxLogDebugf(ctx, "decr ilh weight|key=%s", key)
	if res := redisutil.GetDefaultInstance().DecrBy(ctx, key, weight); res.Err() != nil {
		return srerr.With(srerr.CodisErr, key, res.Err())
	}

	portKey := formatILHTwsDpWightKey(ctx, productID, lineID, dgType, twsCode, destPort, timestamp)
	if res := redisutil.GetDefaultInstance().DecrBy(ctx, portKey, weight); res.Err() != nil {
		return srerr.With(srerr.CodisErr, portKey, res.Err())
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	key := formatILHProductCartonCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, 1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHProductParcelCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64, parcelNum int64) *srerr.Error {
	key := formatILHProductParcelCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, parcelNum).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	key := formatILHProductCartonCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductParcelCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64, parcelNum int64) *srerr.Error {
	key := formatILHProductParcelCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -parcelNum).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHProductWeightKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHProductWeightKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", lineId, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

type ILHProductCounter struct {
	LaneCode    string
	DgFlag      int
	ServiceCode string
}

type ILHProductCounterResult struct {
	CartonOrderCount int
	ParcelOrderCount int
	Weight           int
}

func (v *VolumeCounterImpl) GetILHProductCounter(ctx context.Context, productID int, days []time.Time) (map[ILHProductCounter]*ILHProductCounterResult, int, int, int, *srerr.Error) {
	volumes := make(map[ILHProductCounter]*ILHProductCounterResult)
	orderCountTotal := 0
	weightTotal := 0
	parcelCountTotal := 0
	for _, day := range days {
		productCartonOrderKey := formatILHProductLaneCartonCountKeyByDate(productID, day.Format(constant.TimeLayout))
		result, err := redisutil.GetDefaultInstance().HGetAll(ctx, productCartonOrderKey).Result()
		if err != nil && err != redis.Nil {
			return nil, 0, 0, 0, srerr.With(srerr.CodisErr, productCartonOrderKey, err)
		}
		for field, cartonCountVal := range result {
			orderCount, err := strconv.Atoi(cartonCountVal)
			if err != nil {
				continue
			}

			parts := strings.Split(field, ":")
			if len(parts) != 3 {
				continue
			}

			dgFlag, err := strconv.Atoi(parts[1])
			if err != nil {
				continue
			}

			ilhCounter := ILHProductCounter{
				LaneCode:    parts[0],
				DgFlag:      dgFlag,
				ServiceCode: parts[2],
			}

			productWeightKey := formatILHProductLaneWeightKeyByDate(productID, day.Format(constant.TimeLayout))
			weightVal, err := redisutil.GetDefaultInstance().HGet(ctx, productWeightKey, field).Result()
			if err != nil && err != redis.Nil {
				return nil, 0, 0, 0, srerr.With(srerr.CodisErr, productWeightKey, err)
			}

			weight, err := strconv.Atoi(weightVal)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Get line weight fail|key=%s,filed=%s", productWeightKey, field)
			}

			productParcelKey := formatILHProductLaneParcelCountKeyByDate(productID, day.Format(constant.TimeLayout))
			parcelVal, err := redisutil.GetDefaultInstance().HGet(ctx, productParcelKey, field).Result()
			if err != nil && err != redis.Nil {
				return nil, 0, 0, 0, srerr.With(srerr.CodisErr, productParcelKey, err)
			}

			parcelCount, err := strconv.Atoi(parcelVal)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Get line parcel count fail | key=%s,filed=%s,val=%s,err=%v",
					productParcelKey, field, parcelVal, err)
			}
			// if not exist need init
			if _, exist := volumes[ilhCounter]; !exist {
				volumes[ilhCounter] = &ILHProductCounterResult{}
			}

			volumes[ilhCounter].CartonOrderCount += orderCount
			volumes[ilhCounter].Weight += weight
			volumes[ilhCounter].ParcelOrderCount += parcelCount
			orderCountTotal += orderCount
			weightTotal += weight
			parcelCountTotal += parcelCount
		}
	}

	return volumes, orderCountTotal, weightTotal, parcelCountTotal, nil
}

type LaneCounter struct {
	LaneCode    string
	ServiceCode string
	DgFlag      int
}

func (v *VolumeCounterImpl) GetMultiProductServiceCodeVolumes(ctx context.Context, productID int, days []time.Time) (map[string]int, int, *srerr.Error) {
	volumes := make(map[string]int)
	total := 0
	for _, day := range days {
		productKey := formatMultiProductLanesKeyByDate(productID, day.Format(constant.TimeLayout))
		result, err := redisutil.GetDefaultInstance().HGetAll(ctx, productKey).Result()
		if err != nil && err != redis.Nil {
			return nil, 0, srerr.With(srerr.CodisErr, productKey, err)
		}
		for serviceCode, data := range result {
			count, err := strconv.Atoi(data)
			if err != nil {
				continue
			}
			volumes[serviceCode] += count
			total += count
		}
	}

	return volumes, total, nil
}

func (v *VolumeCounterImpl) GetProductLaneVolumes(ctx context.Context, productID int, days []time.Time) (map[LaneCounter]int, int, *srerr.Error) {
	volumes := make(map[LaneCounter]int)
	total := 0
	for _, day := range days {
		productKey := formatProductLanesKeyByDate(productID, day.Format(constant.TimeLayout))
		result, err := redisutil.GetDefaultInstance().HGetAll(ctx, productKey).Result()
		if err != nil && err != redis.Nil {
			return nil, 0, srerr.With(srerr.CodisErr, productKey, err)
		}
		for k, data := range result {
			laneCounter := parseField(k)
			// 有Zone的情况，跳过，不统计
			if laneCounter.LaneCode == "" {
				continue
			}
			count, err := strconv.Atoi(data)
			if err != nil {
				continue
			}
			volumes[laneCounter] += count
			total += count
		}
	}

	return volumes, total, nil
}

func formatProductLanesKeyByDate(productID int, date string) string {
	return PrefixProductLanesOrderCount + fmt.Sprintf("%v:%v", productID, date)
}

func formatMultiProductLanesKeyByDate(productID int, date string) string {
	return PrefixMultiProductLanesOrderCount + fmt.Sprintf("%v:%v", productID, date)
}

func formatILHTwsCartonVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	productPrefix := formatProductVolumeKeyByDate(productID, t.Format(constant.TimeLayout))
	return fmt.Sprintf("%s:%s:%d:%s", productPrefix, lineID, dgType, twsCode)
}

func formatILHTwsParcelVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	productPrefix := formatProductParcelVolumeKeyByDate(productID, t.Format(constant.TimeLayout))
	return fmt.Sprintf("%s:%s:%d:%s", productPrefix, lineID, dgType, twsCode)
}

func formatILHTwsDpCartonVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) string {
	return fmt.Sprintf("%s:%s",
		formatILHTwsCartonVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp), destPort)
}

func formatILHTwsDpParcelVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) string {
	return fmt.Sprintf("%s:%s",
		formatILHTwsParcelVolumeKey(ctx, productID, lineID, dgType, twsCode, timestamp), destPort)
}

func formatILHTwsWightKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) string {
	t := GetDateByTwsCode(ctx, twsCode, timestamp)
	return fmt.Sprintf("%s:%s:%d:%s",
		formatProductWeightKeyByDate(productID, t.Format(constant.TimeLayout)), lineID, dgType, twsCode)
}

func formatILHTwsDpWightKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) string {
	return fmt.Sprintf("%s:%s",
		formatILHTwsWightKey(ctx, productID, lineID, dgType, twsCode, timestamp), destPort)
}

func formatILHProductCartonKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixILHProductOrderCount, date, productID)
}

func formatILHProductCartonCountKey(productID int, timestamp int64) string {
	return formatILHProductCartonKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHProductParcelCountKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v:%v%v:%v", PrefixILHProductOrderCount, KeyParcelQuantity, date, productID)
}

func formatILHProductParcelCountKey(productID int, timestamp int64) string {
	return formatILHProductParcelCountKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHProductLaneCartonCountKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixILHProductLaneCartonCount, date, productID)
}

func formatILHProductLaneParcelCountKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixILHProductLaneParcelCount, date, productID)
}

func formatILHProductLaneWeightKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixILHProductLaneWeight, date, productID)
}

func formatILHProductLaneCartonCountKey(productID int, timestamp int64) string {
	return formatILHProductLaneCartonCountKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHProductLaneParcelCountKey(productID int, timestamp int64) string {
	return formatILHProductLaneParcelCountKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHProductLaneWeightKey(productID int, timestamp int64) string {
	return formatILHProductLaneWeightKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHProductWeightKeyByDate(productID int, date string) string {
	return fmt.Sprintf("%v%v:%v", PrefixILHProductWeight, date, productID)
}

func formatILHProductWeightKey(productID int, timestamp int64) string {
	return formatILHProductWeightKeyByDate(productID, timeutil.GetLocalTimeByTimestamp(timestamp).Format(constant.TimeLayout))
}

func formatILHCombinationStatKey(ctx context.Context, productID int, ilhID, importIlhID, lmID string, twsCode string, timestamp int64) string {
	date := GetDateByTwsCode(ctx, twsCode, timestamp).Format(constant.TimeLayout)
	return formatILHCombinationStatKeyByDate(productID, ilhID, importIlhID, lmID, twsCode, date)
}

func formatILHCombinationStatKeyByDate(productID int, ilhID, importIlhID, lmID, twsCode string, date string) string {
	return fmt.Sprintf("%v:%v:%v:%v:%v:%v:%v",
		PrefixILHCombination, date, productID, ilhID, importIlhID, lmID, twsCode)
}

func formatILHCombinationDpStatKey(ctx context.Context, productID int, ilhID, importIlhID, lmID string, twsCode, destPort string, timestamp int64) string {
	date := GetDateByTwsCode(ctx, twsCode, timestamp).Format(constant.TimeLayout)
	return formatILHCombinationDpStatKeyByDate(productID, ilhID, importIlhID, lmID, twsCode, destPort, date)
}

func formatILHCombinationDpStatKeyByDate(productID int, ilhID, importIlhID, lmID, twsCode, destPort string, date string) string {
	return fmt.Sprintf("%v:%v",
		formatILHCombinationStatKeyByDate(productID, ilhID, importIlhID, lmID, twsCode, date), destPort)
}

func GetDateByTwsCode(ctx context.Context, twsCode string, timestamp int64) time.Time {
	cutOrderTimeConfig, exist := configutil.GetTwsCutoffTimeWithTimezone(ctx)[strings.ToUpper(twsCode)]
	if !exist {
		logger.CtxLogErrorf(ctx, "Tws cut order time fail no exist|tws code = %s", twsCode)
	}

	// sub one day when the time is early than tws cut order time
	relativeTime := timeutil.GetRelativeTimeInDayByOffset(timestamp, cutOrderTimeConfig.TimezoneOffset)
	curTime := timeutil.GetTimeByTimezoneOffset(timestamp, cutOrderTimeConfig.TimezoneOffset)
	if relativeTime < cutOrderTimeConfig.CutoffTime {
		curTime = curTime.AddDate(0, 0, -1)
	}

	return curTime
}

func parseField(field string) LaneCounter {
	var result LaneCounter
	parts := strings.Split(field, ":")
	switch len(parts) {
	case 2:
		dgFlag, _ := strconv.Atoi(parts[1])
		result = LaneCounter{
			LaneCode: parts[0],
			DgFlag:   dgFlag,
		}
	case 3:
		// Lane:DGFlag:ServiceCode
		dgFlag, _ := strconv.Atoi(parts[1])
		result = LaneCounter{
			LaneCode:    parts[0],
			ServiceCode: parts[2],
			DgFlag:      dgFlag,
		}
	case 5:
		// InterLane:LocalLane:DGFlag:HO:ServiceCode
		dgFlag, _ := strconv.Atoi(parts[2])
		result = LaneCounter{
			LaneCode:    parts[0] + "/" + parts[1],
			ServiceCode: parts[4],
			DgFlag:      dgFlag,
		}
	}

	return result
}

func (v *VolumeCounterImpl) IncrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	key := formatILHProductLaneCartonCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, 1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	key := formatILHProductLaneParcelCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) IncrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHProductLaneWeightKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	key := formatILHProductLaneCartonCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -1).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	key := formatILHProductLaneParcelCountKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -parcelQuantity).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) DecrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	key := formatILHProductLaneWeightKey(productId, timestamp)
	field := fmt.Sprintf("%s:%d:%s", laneCode, dgType, serviceCode)
	if err := redisutil.GetDefaultInstance().HIncrBy(ctx, key, field, -weight).Err(); err != nil {
		return srerr.With(srerr.CodisErr, key, err)
	}

	return nil
}
