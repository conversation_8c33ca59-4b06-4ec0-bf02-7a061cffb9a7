package volume_counter

import (
	"fmt"
	"sort"
	"strconv"
	"testing"
)

func TestNewVolumeCounterImpl(t *testing.T) {
	key, err := replaceVolumeKey("8001:20060102:LVN11", 1667445604)
	if err != nil {
		return
	}
	fmt.Println(key)
}

type TetStruct struct {
	Name string `json:"name"`
	Num  int64  `json:"num"`
}

func TestNewVolumeCounterImpl1(t *testing.T) {
	var testList []TetStruct
	testList = append(testList, TetStruct{
		Name: "12",
		Num:  12,
	})
	testList = append(testList, TetStruct{
		Name: "123",
		Num:  123,
	})
	testList = append(testList, TetStruct{
		Name: "124",
		Num:  124,
	})
	testList = append(testList, TetStruct{
		Name: "124",
		Num:  123,
	})
	sort.Slice(testList, func(i, j int) bool {
		return testList[i].Num > testList[j].Num
	})
	fmt.Println(testList)
}

func TestNewVolumeCounterImpl2(t *testing.T) {
	str := "abc"
	b := str[0]
	fmt.Println(b)
	fmt.Println(string(b))
	fmt.Println(int(b))
	num, err := strconv.Atoi(string(b))
	fmt.Println(num, err)
}
