package volume_counter

import "time"

const (
	ZoneVolumePrefix  = "zone_volume"
	RouteVolumePrefix = "route_volume"
)

const (
	ILHBSAWeightPrefix        = "ilh_bsa_weight"
	ILHProductBSAWeightPrefix = "ilh_product_bsa_weight"
	ILHAdhocWeightPrefix      = "ilh_adhoc_weight"
	CCWeightPrefix            = "cc_weight"

	ILHOrderUsagePrefix = "ilh_order_usage"
	ILHOrderUsageTTL    = 7 * 24 * 60 * 60 * time.Second
)
