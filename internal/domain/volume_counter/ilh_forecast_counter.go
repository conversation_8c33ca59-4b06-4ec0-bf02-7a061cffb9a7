package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"time"
)

func (v *ForecastVolumeCounterImpl) IncrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	key := v.formatForecastILHCartonTwsVolumeKey(ctx, productID, lineID, dgType, twsCode)
	v.counter[key]++

	portKey := v.formatForecastILHCartonTwsDpVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort)
	v.counter[portKey]++

	logger.CtxLogDebugf(ctx, "key=%s, port key=%s", key, portKey)

	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	key := v.formatForecastILHTwsWightKey(ctx, productID, lineID, dgType, twsCode)
	v.counter[key] += int(weight)

	portKey := v.formatForecastILHTwsDpWightKey(ctx, productID, lineID, dgType, twsCode, destPort)
	v.counter[portKey] += int(weight)

	logger.CtxLogDebugf(ctx, "key=%s, port key=%s", key, portKey)

	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCartonTwsVolumeKey(ctx, productID, lineID, dgType, twsCode)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsDpCartonVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCartonTwsDpVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHTwsWightKey(ctx, productID, lineID, dgType, twsCode)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsDpWeight(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHTwsDpWightKey(ctx, productID, lineID, dgType, twsCode, destPort)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) GetILHProductCounter(ctx context.Context, productID int, days []time.Time) (map[ILHProductCounter]*ILHProductCounterResult, int, int, int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, 0, 0, 0, nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductWeight(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) GetMultiProductServiceCodeVolumes(ctx context.Context, productID int, days []time.Time) (map[string]int, int, *srerr.Error) {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil, 0, nil
}

func (v *ForecastVolumeCounterImpl) IncrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {
	key := v.formatForecastILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode)
	v.counter[fmt.Sprintf("%s:%s", key, KeyCartonQuantity)] += 1
	v.counter[fmt.Sprintf("%s:%s", key, KeyParcelQuantity)] += int(parcelQuantity)
	v.counter[fmt.Sprintf("%s:%s", key, KeyWeight)] += int(weight)

	portKey := v.formatForecastILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort)
	v.counter[fmt.Sprintf("%s:%s", portKey, KeyCartonQuantity)] += 1
	v.counter[fmt.Sprintf("%s:%s", portKey, KeyParcelQuantity)] += int(parcelQuantity)
	v.counter[fmt.Sprintf("%s:%s", portKey, KeyWeight)] += int(weight)

	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHCombinationStat(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, parcelQuantity, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode)
	key = fmt.Sprintf("%s:%s", key, KeyCartonQuantity)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode)
	key = fmt.Sprintf("%s:%s", key, KeyParcelQuantity)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode)
	key = fmt.Sprintf("%s:%s", key, KeyWeight)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationDpCartonQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort)
	key = fmt.Sprintf("%s:%s", key, KeyCartonQuantity)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationDpParcelQuantity(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort)
	key = fmt.Sprintf("%s:%s", key, KeyParcelQuantity)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHCombinationDpWeight(ctx context.Context, productID int, ilhLineID, importIlhLineID, lmLineID string, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHCombinationDpStatKey(ctx, productID, ilhLineID, importIlhLineID, lmLineID, twsCode, destPort)
	key = fmt.Sprintf("%s:%s", key, KeyWeight)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHParcelTwsVolumeKey(ctx, productID, lineID, dgType, twsCode)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) GetILHTwsDpParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, timestamp int64) (int, *srerr.Error) {
	key := v.formatForecastILHParcelTwsDpVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort)
	logger.CtxLogDebugf(ctx, "key=%s", key)

	return v.counter[key], nil
}

func (v *ForecastVolumeCounterImpl) IncrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	key := v.formatForecastILHParcelTwsVolumeKey(ctx, productID, lineID, dgType, twsCode)
	v.counter[key] += int(parcelQuantity)

	portKey := v.formatForecastILHParcelTwsDpVolumeKey(ctx, productID, lineID, dgType, twsCode, destPort)
	v.counter[portKey] += int(parcelQuantity)

	logger.CtxLogDebugf(ctx, "key=%s, port key=%s", key, portKey)

	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHParcelVolume(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string, parcelQuantity int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductCartonCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductParcelCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64, parcelNum int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductParcelCounter(ctx context.Context, productId int, lineId string, dgType int, serviceCode string, timestamp int64, parcelNum int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductLaneCartonCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductLaneParcelCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, parcelQuantity int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) IncrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) DecrILHProductLaneWeightCounter(ctx context.Context, productId int, laneCode string, dgType int, serviceCode string, weight int64, timestamp int64) *srerr.Error {
	logger.CtxLogErrorf(ctx, "forecast counter unsupported method")
	return nil
}

func (v *ForecastVolumeCounterImpl) formatForecastILHCartonTwsVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string) string {
	curTime := v.GetDateByTwsCode(ctx, twsCode)
	productPrefix := formatProductVolumeKeyByDate(productID, curTime.Format(constant.TimeLayout))
	return fmt.Sprintf("%s:%s:%d:%s", productPrefix, lineID, dgType, twsCode)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHCartonTwsDpVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string) string {
	return fmt.Sprintf("%s:%s", v.formatForecastILHCartonTwsVolumeKey(ctx, productID, lineID, dgType, twsCode), destPort)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHParcelTwsVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string) string {
	curTime := v.GetDateByTwsCode(ctx, twsCode)
	productPrefix := formatProductVolumeKeyByDate(productID, curTime.Format(constant.TimeLayout))
	return fmt.Sprintf("%s:%s:%s:%d:%s", productPrefix, KeyParcelQuantity, lineID, dgType, twsCode)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHParcelTwsDpVolumeKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string) string {
	return fmt.Sprintf("%s:%s", v.formatForecastILHParcelTwsVolumeKey(ctx, productID, lineID, dgType, twsCode), destPort)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHTwsWightKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string) string {
	curTime := v.GetDateByTwsCode(ctx, twsCode)
	productPrefix := formatProductWeightKeyByDate(productID, curTime.Format(constant.TimeLayout))
	return fmt.Sprintf("%s:%s:%d:%s", productPrefix, lineID, dgType, twsCode)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHTwsDpWightKey(ctx context.Context, productID int, lineID string, dgType int, twsCode string, destPort string) string {
	return fmt.Sprintf("%s:%s", v.formatForecastILHTwsWightKey(ctx, productID, lineID, dgType, twsCode), destPort)
}

func (v *ForecastVolumeCounterImpl) formatForecastILHCombinationStatKey(ctx context.Context, productID int, ilhID, importIlhID, lmID string, twsCode string) string {
	curTime := v.GetDateByTwsCode(ctx, twsCode)
	return formatILHCombinationStatKeyByDate(productID, ilhID, importIlhID, lmID, twsCode, curTime.Format(constant.TimeLayout))
}

func (v *ForecastVolumeCounterImpl) formatForecastILHCombinationDpStatKey(ctx context.Context, productID int, ilhID, importIlhID, lmID string, twsCode string, destPort string) string {
	curTime := v.GetDateByTwsCode(ctx, twsCode)
	return formatILHCombinationDpStatKeyByDate(productID, ilhID, importIlhID, lmID, twsCode, destPort, curTime.Format(constant.TimeLayout))
}
