package volume_counter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strconv"
)

type ReportActualPointInfo struct {
	ProductID       int64
	LaneCode        string
	ActualPointID   string
	ActualPointName string
	ActualPointType int32
}

type ReportLineActualPointInfo struct {
	ProductID       int64
	LaneCode        string
	LineID          string
	ActualPointID   string
	ActualPointName string
	ActualPointType int32
}

type RoutingVolumeCounterInterface interface {
	UpdateVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, sloCreateTime int64, updateType pb.UpdateVolumeType, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr) *srerr.Error
	UpdateVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, lineParcelTypeAttr *parcel_type_definition.ParcelTypeAttr, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateProductVolume(ctx context.Context, productId int64, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateLineVolume(ctx context.Context, productId int64, lineId string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateLineZoneVolume(ctx context.Context, productId int64, lineId string, zoneCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateLaneFieldVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateLaneFieldZoneVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateLaneVolume(ctx context.Context, productId int64, laneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateMultiLaneFieldVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateMultiLaneFieldZoneVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateMultiProductServiceCodeVolume(ctx context.Context, productId int64, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateActualPointVolume(ctx context.Context, productId int64, laneCode string, lineIdList []string, actualPointInfo []*pb.VolumeActualPoint, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	GetActualPointVolume(ctx context.Context, productId int64, laneCode string, actualPointId string, actualPointType int32) (int64, *srerr.Error)
	BatchGetActualPointVolume(ctx context.Context, actualPoints []ReportActualPointInfo) ([]int, *srerr.Error)
	GetLineActualPointVolume(ctx context.Context, productId int64, laneCode string, lineId string, actualPointId string, actualPointType int32) (int64, *srerr.Error)
	BatchGetLineActualPointVolume(ctx context.Context, lineActualPointList []ReportLineActualPointInfo) ([]int, *srerr.Error)
	UpdateDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	UpdateZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error
	GetDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error)
	BatchGetDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error)
	GetZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error)
	BatchGetZoneDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error)
}

func GetUpdateParcelTypeList(ctx context.Context, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) []string {
	parcelTypeList := []string{parcel_type_definition.ParcelTypeNone.String()}
	if parcelTypeAttr.GetIsCod() {
		parcelTypeList = append(parcelTypeList, parcel_type_definition.ParcelTypeCod.String())
	}
	if parcelTypeAttr.GetIsBulky() {
		parcelTypeList = append(parcelTypeList, parcel_type_definition.ParcelTypeBulky.String())
	}
	if parcelTypeAttr.GetIsHighValue() {
		parcelTypeList = append(parcelTypeList, parcel_type_definition.ParcelTypeHighValue.String())
	}
	if parcelTypeAttr.GetIsDg() {
		parcelTypeList = append(parcelTypeList, parcel_type_definition.ParcelTypeDg.String())
	}
	return parcelTypeList
}

func (v *VolumeCounterImpl) UpdateVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, sloCreateTime int64, updateType pb.UpdateVolumeType, lineParcelMap map[string]*parcel_type_definition.ParcelTypeAttr) *srerr.Error {
	for _, lineId := range lineIdList {
		parcelTypeList := GetUpdateParcelTypeList(ctx, lineParcelMap[lineId])
		cnts := make([]int64, 0, len(parcelTypeList))
		for _, parcelType := range parcelTypeList {
			key := lineCounterKey(productId, lineId, date, parcelType)
			cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
			if err != nil {
				logger.CtxLogErrorf(ctx, "add.line.counter lind_id=%v key=%v IncrVolumeLineDimension err=%v", lineId, key, err)
				return srerr.With(srerr.RedisErr, nil, err)
			}
			cnts = append(cnts, cnt)
		}
		logger.CtxLogInfof(ctx, "add.line.counter lind_id=%v date=%v parcelTypeList=%v cnts=%v", lineId, date, parcelTypeList, cnts)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, lineParcelTypeAttr *parcel_type_definition.ParcelTypeAttr, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	parcelTypeList := GetUpdateParcelTypeList(ctx, lineParcelTypeAttr)
	cnts := make([]int64, 0, len(parcelTypeList))
	for _, parcelType := range parcelTypeList {
		key := zoneCounterKey(productId, lineId, date, groupId, zoneName, parcelType)
		cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "add.zone.counter zone=%s key=%s IncrVolumeZoneDimension err=%v", zoneName, key, err)
			return srerr.With(srerr.RedisErr, nil, err)
		}
		cnts = append(cnts, cnt)
	}
	logger.CtxLogInfof(ctx, "add.zone.counter zone=%s date=%v parcelTypeList=%v cnts=%v", zoneName, date, parcelTypeList, cnts)
	return nil
}

func (v *VolumeCounterImpl) UpdateProductVolume(ctx context.Context, productID int64, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatProductVolumeKey(ctx, int(productID))
	cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateLineVolume(ctx context.Context, productId int64, lineId string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatLineVolumeKey(ctx, int(productId), lineId, "")
	cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	err = v.IncrParcelTypeVolumeByKey(ctx, key, sloCreateTime, parcelTypeAttr, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, "", err)
	}
	return nil
}

// IncrParcelTypeVolumeByKey 判断订单属性增加对应ParcelType的单量(routing)
func (v *VolumeCounterImpl) IncrParcelTypeVolumeByKey(ctx context.Context, key string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) error {
	if parcelTypeAttr.GetIsCod() {
		codKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeCod)
		if _, err := v.UpdateRedisCounter(ctx, codKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			monitoring.ReportError(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.COD, err.Error())
			return fmt.Errorf("incr cod volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.COD, codKey)
	}
	if parcelTypeAttr.GetIsBulky() {
		bulkyKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeBulky)
		if _, err := v.UpdateRedisCounter(ctx, bulkyKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			monitoring.ReportError(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.Bulky, err.Error())
			return fmt.Errorf("incr bulky volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.Bulky, bulkyKey)
	}
	if parcelTypeAttr.GetIsHighValue() {
		highValueKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeHighValue)
		if _, err := v.UpdateRedisCounter(ctx, highValueKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			monitoring.ReportError(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.HighValue, err.Error())
			return fmt.Errorf("incr high value volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.HighValue, highValueKey)
	}
	if parcelTypeAttr.GetIsDg() {
		dgKey := AddParcelTypeSuffix(key, parcel_type_definition.ParcelTypeDg)
		if _, err := v.UpdateRedisCounter(ctx, dgKey, redisutil.GetDefaultInstance(), sloCreateTime, updateType); err != nil {
			monitoring.ReportError(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.DG, err.Error())
			return fmt.Errorf("incr dg volume: %v", err)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingParcelTypeVolume, monitoring.DG, dgKey)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateLineZoneVolume(ctx context.Context, productId int64, lineId string, zoneCode string, sloCreateTime int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatLineZoneVolumeKey(ctx, int(productId), lineId, zoneCode, "")
	cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	err = v.IncrParcelTypeVolumeByKey(ctx, key, sloCreateTime, parcelTypeAttr, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, "", err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateLaneFieldVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatProductLanesKey(ctx, int(productId))
	field := formatProductLanesField(laneCode, dgFlag, serviceCode)
	cnt, err := v.UpdateFieldRedisCounter(ctx, key, field, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateLaneFieldZoneVolume(ctx context.Context, productId int64, laneCode string, dgFlag int, serviceCode string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatProductLanesKey(ctx, int(productId))
	field := formatProductLanesField(laneCode, dgFlag, serviceCode) + ":" + zoneCode
	cnt, err := v.UpdateFieldRedisCounter(ctx, key, field, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateLaneVolume(ctx context.Context, productId int64, laneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatLaneKey(ctx, int(productId), laneCode)
	cnt, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateMultiLaneFieldVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatProductLanesKey(ctx, int(productId))
	var localLane, interLane string
	for _, laneCode := range laneCodeGroup {
		laneInfo, err := v.laneService.GetLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			return err
		}
		if laneInfo.IsMultiInternation() {
			interLane = laneCode
			continue
		}
		if laneInfo.IsLocalOfMultiLane() {
			localLane = laneCode
		}
	}
	field := formatProductMultiLanesField(interLane, localLane, dgFlag, hoPoint, serviceCode)
	cnt, err := v.UpdateFieldRedisCounter(ctx, key, field, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateMultiLaneFieldZoneVolume(ctx context.Context, productId int64, laneCodeGroup []string, dgFlag int, hoPoint, serviceCode, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatProductLanesKey(ctx, int(productId))
	var localLane, interLane string
	for _, laneCode := range laneCodeGroup {
		laneInfo, err := v.laneService.GetLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			return err
		}
		if laneInfo.IsMultiInternation() {
			interLane = laneCode
			continue
		}
		if laneInfo.IsLocalOfMultiLane() {
			localLane = laneCode
		}
	}
	field := formatProductMultiLanesField(interLane, localLane, dgFlag, hoPoint, serviceCode) + ":" + zoneCode
	cnt, err := v.UpdateFieldRedisCounter(ctx, key, field, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, cnt, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateMultiProductServiceCodeVolume(ctx context.Context, productId int64, serviceCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatMultiProductLanesKey(ctx, int(productId))
	_, err := v.UpdateFieldRedisCounter(ctx, key, serviceCode, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}

	return nil
}

func (v *VolumeCounterImpl) UpdateActualPointVolume(ctx context.Context, productId int64, laneCode string, lineIdList []string, actualPointInfoList []*pb.VolumeActualPoint, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	// 更新actual point运力
	for _, actualPointInfo := range actualPointInfoList {
		key := formatActualPointKey(ctx, productId, laneCode, actualPointInfo.GetPointId(), actualPointInfo.GetPointType())
		_, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
		if err != nil {
			return srerr.With(srerr.RedisErr, key, err)
		}
	}
	// 更新line_id、actual point的联合运力。大部分单lineIdList、actualPointInfoList长度均为1，很少一部分是2，所以两层for循环对redis的压力很小。且这里为了使用通用方法，无法使用pipeline
	for _, lineId := range lineIdList {
		for _, actualPointInfo := range actualPointInfoList {
			key := formatLineActualPointKey(ctx, productId, laneCode, lineId, actualPointInfo.GetPointId(), actualPointInfo.GetPointType())
			_, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
			if err != nil {
				return srerr.With(srerr.RedisErr, key, err)
			}
		}
	}
	return nil
}

func (v *VolumeCounterImpl) GetActualPointVolume(ctx context.Context, productId int64, laneCode string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	key := formatActualPointKey(ctx, productId, laneCode, actualPointId, actualPointType)
	result, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return result, nil
}

func (v *VolumeCounterImpl) BatchGetActualPointVolume(ctx context.Context, actualPointList []ReportActualPointInfo) ([]int, *srerr.Error) {
	var (
		keyList = make([]string, 0, len(actualPointList))
		valList = make([]int, 0, len(actualPointList))
	)

	if len(actualPointList) == 0 {
		return valList, nil
	}

	for _, a := range actualPointList {
		keyList = append(keyList, formatActualPointKey(ctx, a.ProductID, a.LaneCode, a.ActualPointID, a.ActualPointType))
	}

	result, err := redisutil.GetDefaultInstance().MGet(ctx, keyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}

	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			valList = append(valList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		valList = append(valList, intVal)
	}

	return valList, nil
}

func (v *VolumeCounterImpl) GetLineActualPointVolume(ctx context.Context, productId int64, laneCode string, lineId string, actualPointId string, actualPointType int32) (int64, *srerr.Error) {
	key := formatLineActualPointKey(ctx, productId, laneCode, lineId, actualPointId, actualPointType)
	result, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return result, nil
}

func (v *VolumeCounterImpl) BatchGetLineActualPointVolume(ctx context.Context, lineActualPointList []ReportLineActualPointInfo) ([]int, *srerr.Error) {
	var (
		keyList = make([]string, 0, len(lineActualPointList))
		valList = make([]int, 0, len(lineActualPointList))
	)

	if len(lineActualPointList) == 0 {
		return valList, nil
	}

	for _, a := range lineActualPointList {
		keyList = append(keyList, formatLineActualPointKey(ctx, a.ProductID, a.LaneCode, a.LineID, a.ActualPointID, a.ActualPointType))
	}

	result, err := redisutil.GetDefaultInstance().MGet(ctx, keyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}

	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			valList = append(valList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		valList = append(valList, intVal)
	}

	return valList, nil
}

func (v *VolumeCounterImpl) UpdateDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatRoutingDashboardKey(ctx, productId, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, sloCreateTime)
	_, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}
	return nil
}

func (v *VolumeCounterImpl) UpdateZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, sloCreateTime int64, updateType pb.UpdateVolumeType) *srerr.Error {
	key := formatRoutingZoneDashboardKey(ctx, productId, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, sloCreateTime)
	_, err := v.UpdateRedisCounter(ctx, key, redisutil.GetDefaultInstance(), sloCreateTime, updateType)
	if err != nil {
		return srerr.With(srerr.RedisErr, key, err)
	}
	return nil
}

func (v *VolumeCounterImpl) GetDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	key := formatRoutingDashboardKey(ctx, productId, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, timestamp)
	result, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return result, nil
}

func (v *VolumeCounterImpl) BatchGetDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	var (
		redisValueList []int64
		redisKeyList   []string
	)
	if len(keyList) == 0 {
		return redisValueList, nil
	}
	for _, key := range keyList {
		redisKeyList = append(redisKeyList, formatRoutingDashboardKey(ctx, key.ProductId, key.FmLine, key.MmLine, key.LmLine, key.SiteId, key.ActualPointId, key.ZoneCode, key.StatTime))
	}
	result, err := redisutil.GetDefaultInstance().MGet(ctx, redisKeyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}
	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			redisValueList = append(redisValueList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		redisValueList = append(redisValueList, int64(intVal))
	}
	return redisValueList, nil
}

func (v *VolumeCounterImpl) GetZoneDashboardVolume(ctx context.Context, productId int64, fmLine string, mmLine string, lmLine string, siteId string, actualPointId string, zoneCode string, timestamp int64) (int64, *srerr.Error) {
	key := formatRoutingZoneDashboardKey(ctx, productId, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode, timestamp)
	result, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	if err != nil {
		return 0, srerr.With(srerr.RedisErr, key, err)
	}
	return result, nil
}

func (v *VolumeCounterImpl) BatchGetZoneDashboardVolume(ctx context.Context, keyList []RoutingVolumeDashboardKey) ([]int64, *srerr.Error) {
	var (
		redisValueList []int64
		redisKeyList   []string
	)
	if len(keyList) == 0 {
		return redisValueList, nil
	}
	for _, key := range keyList {
		redisKeyList = append(redisKeyList, formatRoutingZoneDashboardKey(ctx, key.ProductId, key.FmLine, key.MmLine, key.LmLine, key.SiteId, key.ActualPointId, key.ZoneCode, key.StatTime))
	}
	result, err := redisutil.GetDefaultInstance().MGet(ctx, redisKeyList...).Result()
	if err != nil {
		return nil, srerr.With(srerr.CodisErr, keyList, err)
	}
	for _, r := range result {
		val, _ := r.(string)
		if val == "" {
			redisValueList = append(redisValueList, 0)
			continue
		}
		intVal, _ := strconv.Atoi(val)
		redisValueList = append(redisValueList, int64(intVal))
	}
	return redisValueList, nil
}

func lineCounterKey(productId int64, lineId, date, parcelType string) string {
	if parcelType != "" {
		return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, date, parcelType)
	}
	return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, date)
}

// v := objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date)
func zoneCounterKey(productId int64, lineId, date, groupId, zoneName, parcelType string) string {
	if parcelType != "" {
		return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date, parcelType)
	}
	return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date)
}
