package routing_rule

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
)

func initIlhRoutingRuleDetail(routingType int32, lineList []*lane_entity.LineInfo) *rule.Detail {
	ruleDetail := &rule.Detail{
		ResourceSubType:         routingType,
		DisplayResourceType:     lfslib.LineSubTypeMap[routingType],
		LineLimit:               make([]*rule.LineLimit, len(lineList)*2), // DG + NonDG
		DgRelated:               1,                                        //dgRelated of C_ILH and C_XAndT_ILH is 1
		Priority:                0,
		MaxCapacityEnable:       false,
		MaxCapacitySort:         0,
		MinVolumeEnable:         false,
		MinVolumeSort:           0,
		MaxWeightCapacityEnable: false,
		MaxWeightCapacitySort:   0,
		MinWeightEnable:         false,
		MinWeightSort:           0,
	}

	for lineIndex, line := range lineList {
		lineLimitIndex := lineIndex * 2
		//init non dg line limit
		ruleDetail.LineLimit[lineLimitIndex] = &rule.LineLimit{
			LineId:            line.LineID,
			Name:              line.LineName,
			MaxCapacity:       0,
			MinVolume:         0,
			MaxWeightCapacity: 0,
			MinWeight:         0,
			Priority:          1,
			DGFlag:            int(rule.NonDG),
		}

		//init dg line limit
		ruleDetail.LineLimit[lineLimitIndex+1] = &rule.LineLimit{
			LineId:            line.LineID,
			Name:              line.LineName,
			MaxCapacity:       0,
			MinVolume:         0,
			MaxWeightCapacity: 0,
			MinWeight:         0,
			Priority:          1,
			DGFlag:            int(rule.DG),
		}
	}

	return ruleDetail
}

func initCCMode(lineSubTypeMap map[int][]*lane_entity.LineInfo, roleMap map[int]int) rule.CCMode {
	// 只要映射到的Line包含Import ILH，就设置CC Mode为1
	for subType := range lineSubTypeMap {
		if roleMap[subType] == lfslib.C_M_ILH {
			return rule.CCModeCCRouting
		}
	}

	return rule.CCModeNoCCRouting
}
