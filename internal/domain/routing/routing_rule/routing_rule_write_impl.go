package routing_rule

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

const defaultVolumeRuleId = -1

func (s *SoftRuleImpl) CreateRule(ctx context.Context, name string, productId int64, priority int32, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error) {
	//1.初始化tab
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	fillObjEmpty := []byte("{}")
	fillSliceEmpty := []byte("[]")
	operatedBy, _ := apiutil.GetUserInfo(ctx)
	ruleData := &ruledata.RoutingRuleTab{
		ProductID:             productId,
		RuleName:              name,
		Priority:              priority,
		Status:                rule.RuleStatusDraft,
		OperatedBy:            operatedBy,
		EffectiveStartTime:    0,
		CTime:                 uint32(now),
		MTime:                 uint32(now),
		WhsId:                 "",
		ZoneCode:              "",
		ItemCategoryIDList:    "",
		StrDisabledInfo:       fillSliceEmpty,
		Rules:                 fillObjEmpty,
		StrDefaultCriteria:    fillObjEmpty,
		StrCombinationSetting: fillSliceEmpty,
		RoutingType:           routingType,
		IsMultiProduct:        isMultiProduct,
		DgType:                int(rule.UndefinedDGFlag),
		ParcelDimension:       rule.ParcelDimension{},
	}
	if isMultiProduct && routingType != rule.IlhRoutingType { //只有multi会用fillObjEmpty，ilh 和external product用的是fillSliceEmpty
		ruleData.StrDisabledInfo = fillObjEmpty
	}
	// 查询所有的3pl info列表信息，为3pl toggle开关提供参考
	resourceTypeList, rErr := s.ListResourceType(ctx, productId, routingType, isMultiProduct)
	if rErr != nil {
		return nil, rErr
	}
	strTplToggleInfo, mErr := json.Marshal(resourceTypeList)
	if mErr != nil {
		return nil, srerr.With(srerr.JsonErr, nil, mErr)
	}
	ruleData.StrTplToggleInfo = strTplToggleInfo

	//2.写入db
	result, cErr := s.softRuleRepo.CreateRoutingRule(ctx, ruleData)
	if cErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, productId, cErr)
	}
	//3.转换业务模型
	parsedRule, pErr := routing.ParseRuleData(result, false)
	if pErr != nil {
		return nil, pErr
	}
	//与lps逻辑一致，空whsid返回null
	var emptyStrSlice []string
	if len(parsedRule.WhsId) == 0 {
		parsedRule.WhsId = emptyStrSlice
	}
	if len(parsedRule.DestinationPorts) == 0 {
		parsedRule.DestinationPorts = emptyStrSlice
	}
	if len(parsedRule.ZoneCode) == 0 {
		parsedRule.ZoneCode = emptyStrSlice
	}
	return parsedRule, nil
}

func (s *SoftRuleImpl) UpdateRule(ctx context.Context, update *soft_routing.UpdateRuleRequest) (*rule.RoutingRule, *srerr.Error) {
	//1.校验入参
	vErr := s.ValidateUpdateRequest(ctx, update)
	if vErr != nil {
		return nil, vErr
	}
	//2.根据id从db获取rule，并校验状态
	ret, gErr := s.softRuleRepo.GetRoutingRuleFromDB(ctx, update.ID)
	if dbutil.IsDataNotFound(gErr) {
		return nil, srerr.With(srerr.RuleNotFound, update.ID, gErr)
	}
	if gErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, update.ID, gErr)
	}
	if !ret.IsEditable(ctx) {
		return nil, srerr.New(srerr.RuleNotEditable, update.ID, "not editable")
	}
	//3.校验rule是否重复，分active态和draft态
	strWhsId := objutil.SetSliceToString(update.WhsId)
	strZoneCode := objutil.SetSliceToString(update.ZoneCode)
	strItemCategoryID := objutil.SetIntSliceToString(update.ItemCategoryID)
	if update.Status == rule.RuleStatusActive { //active态
		currentRule, _ := s.softRuleRepo.GetEffectiveRoutingRule(ctx, ret.ProductID, update.Priority, ret.RoutingType, ret.IsMultiProduct)
		if currentRule != nil && currentRule.ID != update.ID {
			return nil, srerr.New(srerr.ParamErr, update.ProductID, fmt.Sprintf("Same priority active rule exists(rule ID:%v). Please rearrange rule's priority if you need.", currentRule.ID))
		}
	}

	if update.Status == rule.RuleStatusDraft { //draft态
		ruleList, err := s.softRuleRepo.GetDraftRoutingRuleList(ctx, ret.ProductID)
		if err == nil { //这里error不为nil，不中断，因为是draft rule
			for _, r := range ruleList {
				defaultExist := false
				if r.ID != update.ID && r.Priority == update.Priority && r.ZoneCode == strZoneCode && r.WhsId == strWhsId &&
					r.ItemCategoryLevel == update.ItemCategoryLevel && r.ItemCategoryIDList == strItemCategoryID &&
					r.ParcelValueMin == update.ParcelValueMin && r.ParcelValueMax == update.ParcelValueMax &&
					r.ParcelWeightMin == update.ParcelWeightMin && r.ParcelWeightMax == update.ParcelWeightMax &&
					r.DgType == update.DgType && r.ParcelDimension == update.ParcelDimension {
					defaultExist = true
				}
				//SSCSMR-278: if update's routing type is not the same as existed rule's, won't return error
				if update.RoutingType != nil && *update.RoutingType != r.RoutingType {
					defaultExist = false
				}
				if defaultExist {
					return nil, srerr.New(srerr.ParamErr, update.ProductID, "Duplicated with existing draft(same TWS, receiver zone, item category, parcel value or parcel weight), please configure the existing draft.")
				}
			}
		}
	}
	//4.校验可用lane
	if len(update.DisabledInfo) > 0 {
		var disableLine []string
		for _, info := range update.DisabledInfo {
			disableLine = append(disableLine, info.LineList...)
		}
		if ret.RoutingType != rule.IlhRoutingType { //ilh rule no need checking lane enable
			err := s.CheckLaneEnabled(ctx, disableLine, nil, int(update.ProductID), true)
			if err != nil {
				return nil, err
			}
		}
	}

	//5.组装参数，包括将rule details，default criteria序列化成string等，进而存储在db中
	ret.Status = update.Status
	ret.Priority = update.Priority
	ret.RuleName = update.RuleName
	ret.EffectiveStartTime = update.EffectiveStartTime
	ret.VolumeRuleId = defaultVolumeRuleId
	rules, err := jsoniter.Marshal(update.RuleDetails)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, update.ID, err)
	}
	ret.Rules = rules

	if ret.IsMultiProduct && ret.RoutingType != rule.IlhRoutingType {
		ret.StrDefaultCriteria, err = jsoniter.Marshal(update.MultiProductDefaultCriteria)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, update.ID, err)
		}
		ret.StrDisabledInfo, err = jsoniter.Marshal(update.MultiProductDisableInfo)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, update.ID, err)
		}
	} else { //will marshal default criteria of single product and ilh product
		ret.StrDefaultCriteria, err = jsoniter.Marshal(update.DefaultCriteria)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, update.ID, err)
		}
		ret.StrDisabledInfo, err = jsoniter.Marshal(update.DisabledInfo)
		if err != nil {
			return nil, srerr.With(srerr.ParamErr, update.ID, err)
		}
	}

	ret.StrCombinationSetting, err = jsoniter.Marshal(update.CombinationSetting)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, update.ID, err)
	}

	ret.DestinationPorts = objutil.SetSliceToString(update.DestinationPorts) //SSCSMR-278 destination ports, including ho ids
	ret.DgType = update.DgType                                               //dg flag
	ret.WhsId = objutil.SetSliceToString(update.WhsId)
	ret.ZoneCode = objutil.SetSliceToString(update.ZoneCode)
	ret.ItemCategoryLevel = update.ItemCategoryLevel
	ret.ItemCategoryIDList = objutil.SetIntSliceToString(update.ItemCategoryID)
	ret.ParcelValueMax = update.ParcelValueMax
	ret.ParcelValueMin = update.ParcelValueMin
	ret.ParcelWeightMax = update.ParcelWeightMax
	ret.ParcelWeightMin = update.ParcelWeightMin
	ret.ParcelDimension = update.ParcelDimension
	ret.OperatedBy, _ = apiutil.GetUserInfo(ctx)
	ret.WmsToggleEnable = update.WmsToggleEnable
	ret.CCMode = update.CCMode
	ret.MTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	ret.ShopGroupList = objutil.JsonBytes(update.ShopGroupListVo)
	//6.更新rule
	newData, uErr := s.softRuleRepo.UpdateRoutingRule(ctx, ret)
	if uErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, update.ID, uErr)
	}
	parsedRule, pErr := routing.ParseRuleData(newData, true)
	if pErr != nil {
		return nil, pErr
	}
	//与lps逻辑一致，空whsid返回null
	var emptyStrSlice []string
	if len(parsedRule.WhsId) == 0 {
		parsedRule.WhsId = emptyStrSlice
	}
	if len(parsedRule.DestinationPorts) == 0 {
		parsedRule.DestinationPorts = emptyStrSlice
	}
	if len(parsedRule.ZoneCode) == 0 {
		parsedRule.ZoneCode = emptyStrSlice
	}
	return parsedRule, nil
}

func (s *SoftRuleImpl) DeleteRule(ctx context.Context, id int64) *srerr.Error {
	//1.判断id对应的rule是否是draft态，不是则不准删除
	data, err := s.softRuleRepo.GetRoutingRuleFromDB(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, id, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	if data.Status != rule.RuleStatusDraft {
		return srerr.New(srerr.RuleNotDeletable, nil, "rule id:%d", id)
	}
	dErr := s.softRuleRepo.DeleteRoutingRule(ctx, id)
	if dErr != nil {
		return srerr.With(srerr.DatabaseErr, id, dErr)
	}
	return nil
}

func (s *SoftRuleImpl) CopyRule(ctx context.Context, id int64) *srerr.Error {
	data, err := s.softRuleRepo.GetRoutingRuleFromDB(ctx, id)

	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	if data.Status == rule.RuleStatusDraft {
		return srerr.New(srerr.RuleNotCopiable, id, "rule is draft")
	}
	//
	if checkContainLaneFee(data) {
		//如果存在 enabled-lane-fees ，则报错，不允许写入
		return srerr.With(srerr.RuleNotCopiableAsLanefee, id, errors.New("The rule could not be duplicated as it contains the lane-cheapest shipping fee criteria"))
	}
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	operatedBy, _ := apiutil.GetUserInfo(ctx)
	newData := &ruledata.RoutingRuleTab{
		ProductID:             data.ProductID,
		RuleName:              data.RuleName,
		Status:                rule.RuleStatusDraft,
		OperatedBy:            operatedBy,
		Priority:              data.Priority,
		Rules:                 data.Rules,
		StrDisabledInfo:       data.StrDisabledInfo,
		StrDefaultCriteria:    data.StrDefaultCriteria,
		StrCombinationSetting: data.StrCombinationSetting,
		WhsId:                 data.WhsId,
		ZoneCode:              data.ZoneCode,
		Soc:                   data.Soc,
		ItemCategoryLevel:     data.ItemCategoryLevel,
		ItemCategoryIDList:    data.ItemCategoryIDList,
		ParcelValueMax:        data.ParcelValueMax,
		ParcelValueMin:        data.ParcelValueMin,
		ParcelWeightMax:       data.ParcelWeightMax,
		ParcelWeightMin:       data.ParcelWeightMin,
		CTime:                 uint32(now),
		MTime:                 uint32(now),
		RoutingType:           data.RoutingType,
		IsMultiProduct:        data.IsMultiProduct,
		ParcelDimension:       data.ParcelDimension,
		DgType:                data.DgType,           //SSCSMR-278: copy dg type
		DestinationPorts:      data.DestinationPorts, //SSCSMR-278: copy destination ports
		WmsToggleEnable:       data.WmsToggleEnable,
		CCMode:                data.CCMode,
		VolumeRuleId:          data.VolumeRuleId,
		StrTplToggleInfo:      data.StrTplToggleInfo,
	}
	_, cErr := s.softRuleRepo.CreateRoutingRule(ctx, newData)
	if cErr != nil {
		return srerr.With(srerr.DatabaseErr, newData, cErr)
	}

	return nil
}

func (s *SoftRuleImpl) RollbackRule(ctx context.Context, id int64) *srerr.Error {
	//1.根据id去db检索数据
	routingRule, gErr := s.softRuleRepo.GetRoutingRuleFromDB(ctx, id)
	if dbutil.IsDataNotFound(gErr) {
		return srerr.With(srerr.RuleNotFound, id, gErr)
	}
	if gErr != nil {
		return srerr.With(srerr.DatabaseErr, id, gErr)
	}
	//2.校验rule状态
	if routingRule.Status != rule.RuleStatusExpired {
		return srerr.New(srerr.RuleNotRollbackable, nil, "rule id:%v, is active rule", id)
	}
	//3.赋值并更新rule
	routingRule.EffectiveStartTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	routingRule.Status = rule.RuleStatusActive
	routingRule.OperatedBy, _ = apiutil.GetUserInfo(ctx)
	_, err := s.softRuleRepo.UpdateRoutingRule(ctx, routingRule)
	if err != nil {
		return srerr.With(srerr.RuleNotEditable, id, err)
	}
	return nil
}

func (s *SoftRuleImpl) DisabledRule(ctx context.Context, id int64) *srerr.Error {
	//1.从db检索数据
	routingRule, err := s.softRuleRepo.GetRoutingRuleFromDB(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, id, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	if routingRule.Status != rule.RuleStatusActive && routingRule.Status != rule.RuleStatusForecast {
		return srerr.New(srerr.RuleNotRollbackable, nil, "rule id:%v, status is illegal", id)
	}
	//SSCSMR-337:seek for if there are other active rules
	tabs, gErr := s.softRuleRepo.GetRoutingRuleByCondition(ctx, map[string]interface{}{
		"product_id = ?":   routingRule.ProductID,
		"routing_type = ?": routingRule.RoutingType,
		"rule_status = ?":  rule.RuleStatusActive,
		"id <> ?":          routingRule.ID,
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "DisabledRule|error while finding active rule, product id:%v, routing type:%v, err:%v", routingRule.ProductID, routingRule.RoutingType, gErr)
		return srerr.With(srerr.RuleNotEditable, nil, gErr)
	}
	if len(tabs) == 0 {
		logger.CtxLogErrorf(ctx, "DisabledRule| not found other active rule, product id:%v, routing type:%v", routingRule.ProductID, routingRule.RoutingType)
		return srerr.New(srerr.RuleNotEditable, nil, "You cannot disable this rule , there would be no active rule for this product if you disable this rule.")
	}
	dErr := s.softRuleRepo.DisableRoutingRule(ctx, id)
	if dErr != nil {
		return srerr.With(srerr.RuleNotEditable, id, dErr)
	}
	return nil
}

func (s *SoftRuleImpl) BatchDisabledRule(ctx context.Context, idList []int64) *srerr.Error {
	// 1. 校验ID List里是否有非Queueing的Rule，有则报错
	ruleList, err := s.softRuleRepo.GetRoutingRuleByCondition(ctx, map[string]interface{}{"id IN (?)": idList})
	if err != nil {
		return err
	}

	for _, r := range ruleList {
		if r.Status != rule.RuleStatusForecast {
			return srerr.New(srerr.RuleStatusInvalid, r.ID, "rule [%d] status is not queueing", r.ID)
		}
	}

	// 2. 全部置为Expired状态
	if err := s.softRuleRepo.UpdateRulesToExpired(ctx, idList); err != nil {
		return err
	}

	return nil
}

func (s *SoftRuleImpl) ValidateUpdateRequest(ctx context.Context, update *soft_routing.UpdateRuleRequest) *srerr.Error {
	err := update.ValidateReq()
	if err != nil {
		return err
	}
	return nil
}
