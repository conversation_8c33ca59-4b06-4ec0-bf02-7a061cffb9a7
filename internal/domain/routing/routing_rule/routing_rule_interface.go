package routing_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type SoftRuleServer interface {
	GetRoutingRule(ctx context.Context, id int64) (*rule.RoutingRule, *srerr.Error)
	InitMultiProductRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error)
	InitIlhProductRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error)
	InitialRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error)
	GetMultiProductDgGroup(ctx context.Context, productId int) (map[string]rule.DgGroupInfo, *srerr.Error)
	CreateRule(ctx context.Context, name string, productId int64, priority int32, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error)
	UpdateRule(ctx context.Context, update *soft_routing.UpdateRuleRequest) (*rule.RoutingRule, *srerr.Error)
	DeleteRule(ctx context.Context, id int64) *srerr.Error
	CopyRule(ctx context.Context, id int64) *srerr.Error
	RollbackRule(ctx context.Context, id int64) *srerr.Error
	DisabledRule(ctx context.Context, id int64) *srerr.Error
	BatchDisabledRule(ctx context.Context, idList []int64) *srerr.Error
	CheckLaneEnabled(ctx context.Context, disabledLine []string, enabledLine []string, productId int, needCheckMultiProduct bool) *srerr.Error
	CheckMultiProductLaneEnabled(ctx context.Context, enabledLine []string, productId int) *srerr.Error
	ListRule(ctx context.Context, ruleQuery *rule.RuleQuery) ([]*rule.RoutingRule, int32, *srerr.Error)
	ListWhsInfo(ctx context.Context, productID int64, isMultiProduct bool) ([]string, []soft_routing.IlhWhsInfo, *srerr.Error)
	ListResourceType(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*soft_routing.ListResourceTypeResp, *srerr.Error)
	GetSmartRoutingProductList(ctx context.Context, routingType uint8, isMultiProduct bool) ([]*soft_routing.SimpleProductInfo, *srerr.Error)
	ListIlhCombinationInfo(ctx context.Context, productId int) (soft_routing.IlhCombinationInfo, *srerr.Error)
	ListILHWhsInfo(ctx context.Context, ilhLineID string) ([]soft_routing.IlhWhsInfo, *srerr.Error)
	ListILHDestinationPort(ctx context.Context, ilhLineID string) ([]soft_routing.DestinationPortInfo, *srerr.Error)
}

type SoftRuleImpl struct {
	laneSrv             lane.LaneService
	llsApi              llsclient.LlsApi
	lfsApi              lfsclient.LfsApi
	routingConfigServer routing_config.RoutingConfigService
	softRuleRepo        ruledata.SoftRuleRepo
	lpsApi              lpsclient.LpsApi
	zoneRuleRepo        vrrepo.ZoneRuleRepo
}

func NewSoftRuleImpl(
	laneSrv lane.LaneService,
	llsApi llsclient.LlsApi,
	lfsApi lfsclient.LfsApi,
	routingConfigServer routing_config.RoutingConfigService,
	softRuleRepo ruledata.SoftRuleRepo,
	lpsApi lpsclient.LpsApi,
	zoneRuleRepo vrrepo.ZoneRuleRepo,
) *SoftRuleImpl {
	return &SoftRuleImpl{
		laneSrv:             laneSrv,
		llsApi:              llsApi,
		lfsApi:              lfsApi,
		routingConfigServer: routingConfigServer,
		softRuleRepo:        softRuleRepo,
		lpsApi:              lpsApi,
		zoneRuleRepo:        zoneRuleRepo,
	}
}
