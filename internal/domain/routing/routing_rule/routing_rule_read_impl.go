package routing_rule

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
)

func (s *SoftRuleImpl) CheckLaneEnabled(ctx context.Context, disabledLine []string, enabledLine []string, productId int, needCheckMultiProduct bool) *srerr.Error {
	if len(disabledLine) == 0 && len(enabledLine) == 0 {
		return nil
	}

	productDetail, pErr := s.lpsApi.GetProductDetail(ctx, productId)
	if productDetail == nil || pErr != nil {
		logger.CtxLogErrorf(ctx, "not found product for product_id:%v", productId)
		return srerr.With(srerr.GetLpsProductErr, nil, pErr)
	}
	laneList := productDetail.LaneCodes
	logger.CtxLogInfof(ctx, "CheckLaneEnabled| lane code list:%v", laneList)

	lfsReq := &lfspb.BatchCheckLaneRoutableReqData{LaneCodeList: laneList}

	//SSCSMR-3797:兼容SPX Multi product; /check_lane_enabled接口会在非Multi场景传入Multi product，比如58007.这个product是Single Multi兼具的
	if needCheckMultiProduct && productDetail.IsMultiProduct {
		lfsReq.LaneCodeList = productDetail.MultiLaneCodes
	}

	laneInfo, lfsErr := s.lfsApi.BatchCheckLaneRoutable(ctx, lfsReq)
	if lfsErr != nil {
		return lfsErr
	}

	var relatedLineMap = make(map[string][]string)
	var lineStatusMap = make(map[string]int32)
	for _, lane := range laneInfo.GetRoutableList() {
		for _, line := range lane.GetLineList() {
			lineStatusMap[line.GetLineId()] = line.GetEnable()
			relatedLineMap[line.GetLineId()] = line.GetRelatedLineList()
		}
	}
	checkErr := s.CheckLane(ctx, disabledLine, enabledLine, lineStatusMap, relatedLineMap)
	return checkErr
}

func (s *SoftRuleImpl) CheckMultiProductLaneEnabled(ctx context.Context, enabledLine []string, productId int) *srerr.Error {
	if len(enabledLine) == 0 {
		return srerr.New(srerr.DisableLineErr, nil, "No any line enable")
	}

	resourceRsp, err := s.llsApi.MultiGetResource(ctx, enabledLine, nil)
	if err != nil {
		return err
	}
	var ilhLineList []string
	var lmLineList []string
	for _, line := range enabledLine {
		lineInfo, ok := resourceRsp.Lines[line]
		if !ok {
			logger.CtxLogErrorf(ctx, "Get line info fail|lineId=%s", line)
			continue
		}
		// 区分开ILH和LM的Line
		switch lineInfo.GetLineType() {
		// ILH的Line
		case lfslib.C_ILH, lfslib.C_XAndT_ILH, lfslib.C_OriginExport, lfslib.C_LH, lfslib.C_LHAndImport:
			ilhLineList = append(ilhLineList, line)
		// LM的Line
		case lfslib.C_LM, lfslib.C_DFM:
			lmLineList = append(lmLineList, line)
		default:
			logger.CtxLogErrorf(ctx, "line type should not in multi product|lineId=%s,line type=%d", line, lineInfo.GetLineType())
			continue
		}
	}
	//从lps获取blackListMap
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, productId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "InitIlhProductRoutingRuleData|product id:%v, get product detail err:%v", productId, gErr)
		return gErr
	}
	blackList := productDetail.BusinessBlacklist
	blackListMap := make(map[string][]string, 0)
	for _, blackItem := range blackList {
		blackListMap[blackItem.LineId] = blackItem.LineList
	}
	for _, lm := range lmLineList {
		for _, ilh := range ilhLineList {
			if pass := CheckConnectionsBetweenIlhAndLm(ilh, lm, blackListMap); pass {
				// any connection pass is ok
				return nil
			}
		}
	}
	return srerr.New(srerr.DisableLineErr, productId, "This Line is not routable")
}

func (s *SoftRuleImpl) GetRoutingRule(ctx context.Context, id int64) (*rule.RoutingRule, *srerr.Error) {
	//1.根据id从db获取routing rule
	routingRule, gErr := s.softRuleRepo.GetRoutingRuleFromDB(ctx, id)
	if dbutil.IsDataNotFound(gErr) {
		return nil, srerr.With(srerr.RuleNotFound, id, gErr)
	}
	if gErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, gErr)
	}
	ruleData, dErr := routing.ParseRuleData(routingRule, true)
	if dErr != nil {
		return nil, dErr
	}

	// 取出3pl toggle info信息传给前端
	var tplToggleInfo *rule.ListResourceType
	if err := json.Unmarshal(routingRule.StrTplToggleInfo, &tplToggleInfo); err != nil {
		return nil, srerr.With(srerr.JsonErr, id, err)
	}
	ruleData.TplToggleInfo = tplToggleInfo

	//与lps逻辑一致，空whsid返回null
	var emptyStrSlice []string
	if len(ruleData.WhsId) == 0 {
		ruleData.WhsId = emptyStrSlice
	}
	if len(ruleData.DestinationPorts) == 0 {
		ruleData.DestinationPorts = emptyStrSlice
	}
	if len(ruleData.ZoneCode) == 0 {
		ruleData.ZoneCode = emptyStrSlice
	}

	var isInitRule bool
	if ruleData.RuleDetails.Rules == nil {
		isInitRule = true
		if routingRule.IsMultiProduct && routingRule.RoutingType != rule.IlhRoutingType { //init multi product
			data, dErr := s.InitMultiProductRoutingRuleData(ctx, ruleData.ProductID, ruleData.RoutingType, routingRule.IsMultiProduct)
			if dErr != nil {
				return nil, dErr
			}
			ruleData.RuleDetails = data.RuleDetails
			ruleData.MultiProductDefaultCriteria = data.MultiProductDefaultCriteria
			ruleData.MultiProductDisableInfo = data.MultiProductDisableInfo
		} else if routingRule.IsMultiProduct && routingRule.RoutingType == rule.IlhRoutingType {
			data, dErr := s.InitIlhProductRoutingRuleData(ctx, ruleData.ProductID, ruleData.RoutingType, routingRule.IsMultiProduct)
			if dErr != nil {
				return nil, dErr
			}
			ruleData.CCMode = data.CCMode
			ruleData.RuleDetails = data.RuleDetails
			// default criteria不会为空，但做一层防护
			if ruleData.DefaultCriteria == nil || len(ruleData.DefaultCriteria.WeightageCriteria) == 0 {
				ruleData.DefaultCriteria = data.DefaultCriteria
			}
			if len(ruleData.DisabledInfo) == 0 {
				ruleData.DisabledInfo = data.DisabledInfo
			}
		} else {
			data, dErr := s.InitialRoutingRuleData(ctx, ruleData.ProductID, ruleData.RoutingType, routingRule.IsMultiProduct)
			if dErr != nil {
				return nil, dErr
			}
			ruleData.RuleDetails = data.RuleDetails
			ruleData.DefaultCriteria = data.DefaultCriteria
			ruleData.DisabledInfo = data.DisabledInfo
			ruleData.WmsToggleEnable = data.WmsToggleEnable
		}
	}

	// ilh dg group may be change, need reload when after init rule; only multi need it, ilh rule no need it
	if routingRule.IsMultiProduct && !isInitRule && routingRule.RoutingType != rule.IlhRoutingType {
		ruleData = s.reloadRoutingRuleDgGroup(ctx, ruleData)
	}

	//对应lps中 IsUseVolumeRoutingSystem
	if int(ruleData.RoutingType) == rule.LocalRoutingType {
		ruleData.IsVolumeRouting = routing.CheckSwitchToVolumeCapacityV2(ctx, ruleData.ProductID, "")
	}
	if int(ruleData.RoutingType) == rule.CBRoutingType {
		ruleData.IsVolumeRouting = volumeutil.IsOpenVolumeRouting(ctx)
	}

	return ruleData, nil
}

func (s *SoftRuleImpl) ListRule(ctx context.Context, ruleQuery *rule.RuleQuery) ([]*rule.RoutingRule, int32, *srerr.Error) {
	if ruleQuery.IsLocalForecast {
		return s.GetRuleForForecastDisplay(ctx, ruleQuery)
	}
	rs, total, err := s.softRuleRepo.GetRuleList(ctx, ruleQuery)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, ruleQuery, err)
	}
	rules := make([]*rule.RoutingRule, len(rs))
	for i, data := range rs {
		var err *srerr.Error
		rules[i], err = routing.ParseRuleData(data, false)
		//与lps逻辑一致，空whsid返回null
		var emptyStrSlice []string
		if len(rules[i].WhsId) == 0 {
			rules[i].WhsId = emptyStrSlice
		}
		if len(rules[i].DestinationPorts) == 0 {
			rules[i].DestinationPorts = emptyStrSlice
		}
		if len(rules[i].ZoneCode) == 0 {
			rules[i].ZoneCode = emptyStrSlice
		}
		if err != nil {
			return nil, 0, err
		}
	}
	return rules, total, nil
}

func (s *SoftRuleImpl) InitMultiProductRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error) {
	// 1. Get multi product's lane info, including all internal lanes
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "InitMultiProductRoutingRuleData|get product detail err:%v", gErr)
		return nil, gErr
	}
	laneCodeList := productDetail.MultiLaneCodes

	// 2. Get ILH to DG Group map
	laneInfoList, err := s.laneSrv.BatchGetLaneInfoByLaneCode(ctx, laneCodeList)
	if err != nil {
		return nil, err
	}

	//获取line id到line name的map
	lineIdToLineNameMap, _ := s.laneSrv.GetLineIdToLineNameMap(ctx)
	logger.CtxLogInfof(ctx, "InitMultiProductRoutingRuleData| lineIdToLineNameMap:%v", lineIdToLineNameMap)

	roleMap := routing_role.GetRoleMapByProductAndType(ctx, productID, routingType, true)
	lineSubTypeMap := s.laneSrv.GetLineSubTypeMapByLanes(laneInfoList, roleMap)

	var ilhLineList []string
	for _, ilhSubType := range lfslib.NeedRoutingILH {
		for _, lineInfo := range lineSubTypeMap[ilhSubType] {
			ilhLineList = append(ilhLineList, lineInfo.LineID)
		}
		for _, lineInfos := range lineSubTypeMap {
			for _, lineInfo := range lineInfos {
				if lineInfo.NeedDG() {
					ilhLineList = append(ilhLineList, lineInfo.LineID)
				}
			}
		}
	}

	ilhLineList = objutil.RemoveDuplicatedStrings(ilhLineList)

	//调用lls获取dg group
	dgGroupMap, err := s.llsApi.GetDgGroup(ctx, ilhLineList)
	if err != nil {
		return nil, err
	}
	lineDgGroup := groupLineByDgGroup(dgGroupMap)

	// 3. Init rule detail
	ruleData := &rule.RoutingRule{
		ProductID:                   productID,
		RuleDetails:                 &rule.RuleDetails{},
		MultiProductDefaultCriteria: &rule.MultiProductDefaultCriteria{},
		MultiProductDisableInfo:     &rule.MultiProductDisabledInfo{},
		IsMultiProduct:              true,
		DgType:                      int(rule.UndefinedDGFlag),
		ParcelDimension:             rule.ParcelDimension{},
	}

	// Enrich rule data dg group info
	defaultCriteriaType := rule.CriteriaWeightAge
	ruleData.MultiProductDefaultCriteria.CriteriaType = defaultCriteriaType
	ruleData.MultiProductDefaultCriteria.CriteriaName = defaultCriteriaType.String()
	var criteriaDgGroupList []rule.DgGroupCriteriaInfoItem
	var disableDgGroupList []rule.DgGroupDisableInfoItem
	for _, dgGroup := range lineDgGroup {
		dgGroupNonDgCriteriaInfo := rule.DgGroupCriteriaInfoItem{
			DgGroupId:   dgGroup.DgGroupId,
			DgGroupName: dgGroup.DgGroupName,
			DgFlag:      rule.NonDG,
		}
		dgGroupIsDgCriteriaInfo := rule.DgGroupCriteriaInfoItem{
			DgGroupId:   dgGroup.DgGroupId,
			DgGroupName: dgGroup.DgGroupName,
			DgFlag:      rule.DG,
		}
		dgGroupDisableInfo := rule.DgGroupDisableInfoItem{
			DgGroupId:   dgGroup.DgGroupId,
			DgGroupName: dgGroup.DgGroupName,
			Enable:      false,
		}
		for _, lineId := range dgGroup.LineList {
			dgGroupLine := rule.DgGroupLine{
				LineId: lineId,
				Name:   lineIdToLineNameMap[lineId],
			}
			defaultCriteriaDgGroupLineNonDg := rule.DefaultCriteriaDgGroupLine{DgGroupLine: dgGroupLine, WeightAge: 0, DGFlag: rule.NonDG}
			defaultCriteriaDgGroupLineIsDg := rule.DefaultCriteriaDgGroupLine{DgGroupLine: dgGroupLine, WeightAge: 0, DGFlag: rule.DG}
			dgGroupNonDgCriteriaInfo.LineList = append(dgGroupNonDgCriteriaInfo.LineList, defaultCriteriaDgGroupLineNonDg)
			dgGroupIsDgCriteriaInfo.LineList = append(dgGroupIsDgCriteriaInfo.LineList, defaultCriteriaDgGroupLineIsDg)
			dgGroupDisableInfo.LineList = append(dgGroupDisableInfo.LineList, dgGroupLine)
		}
		criteriaDgGroupList = append(criteriaDgGroupList, dgGroupNonDgCriteriaInfo)
		criteriaDgGroupList = append(criteriaDgGroupList, dgGroupIsDgCriteriaInfo)
		disableDgGroupList = append(disableDgGroupList, dgGroupDisableInfo)
	}
	ruleData.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList = criteriaDgGroupList
	ruleData.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList = disableDgGroupList

	subTypeMap := map[int32]bool{}
	for lineSubType, lineList := range lineSubTypeMap {
		subType := int32(lineSubType)
		// 当产品构建时没设置该line类型为NoNeedRouting的时候，在rule中不展示，不可在前端配置
		if subType == rule.LineTypeNoNeedRouting {
			continue
		}
		if _, ok := subTypeMap[subType]; ok {
			continue
		} else {
			subTypeMap[subType] = true
		}

		tmpRule := &rule.Detail{
			ResourceSubType:        subType,
			DisplayResourceType:    lfslib.LineSubTypeMap[subType],
			DgClassificationEnable: true, // multi product 默认打开
			LineLimit:              []*rule.LineLimit{},
		}

		// 是否是属于需要DG属性的ILH类型
		isDgIlh := isIlhNeedDg(int(subType), lineList)
		if isDgIlh {
			tmpRule.DgRelated = 1
			tmpRule.DgPriority = rule.NonDG
			// default prefer ilh to lm
			tmpRule.Priority = 1
		} else {
			tmpRule.Priority = 2
		}

		for _, line := range lineList {
			tmpLineLimit := &rule.LineLimit{
				LineId:      line.LineID,
				Name:        line.LineName,
				MaxCapacity: 0,
				MinVolume:   0,
				Priority:    1,
			}
			tmpRule.LineLimit = append(tmpRule.LineLimit, tmpLineLimit)
		}

		ruleData.RuleDetails.Rules = append(ruleData.RuleDetails.Rules, tmpRule)

		// 如果是DG ILH则不需要填充Disable Info和Default Criteria，因为会统一以DG Group的形式进行展示
		if isDgIlh {
			continue
		}

		tmpDisableInfo := rule.DisabledInfo{
			ResourceSubType:     subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			LineList:            []string{},
		}
		tmpDefaultCriteria := rule.CriteriaInfo{
			ResourceSubType:     subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			DgRelated:           0,
			LineInfoList:        []*rule.RuleLineConfig{},
		}

		for _, line := range lineList {
			tmpDefaultCriteria.LineInfoList = append(tmpDefaultCriteria.LineInfoList, &rule.RuleLineConfig{
				LineId:    line.LineID,
				Name:      line.LineName,
				WeightAge: 0,
				DGFlag:    0,
			})

			tmpDisableInfo.LineList = append(tmpDisableInfo.LineList, line.LineID)
		}
		ruleData.MultiProductDisableInfo.LineDisableInfo = append(ruleData.MultiProductDisableInfo.LineDisableInfo, tmpDisableInfo)
		ruleData.MultiProductDefaultCriteria.WeightageCriteria.LineCriteriaInfo = append(ruleData.MultiProductDefaultCriteria.WeightageCriteria.LineCriteriaInfo, tmpDefaultCriteria)
	}

	// 15. append tpl toggle info
	// 查询所有的3pl info列表信息，为3pl toggle开关提供参考
	resourceTypeList, rErr := s.ListResourceType(ctx, productID, routingType, isMultiProduct)
	if rErr != nil {
		return nil, rErr
	}
	ruleData.TplToggleInfo = resourceTypeList.ConvertToRuleListResourceType()

	return ruleData, nil
}

func (s *SoftRuleImpl) InitIlhProductRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error) {
	// 1. Get ilh product's lane code list
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "InitIlhProductRoutingRuleData|product id:%v, get product detail err:%v", productID, gErr)
		return nil, gErr
	}
	//调用lps admin接口获取LaneListTypeMultiLH对应的lane code
	laneCodeList := productDetail.IlhLaneCodes

	// 2. Get ILH lane info list
	laneInfoList, err := s.laneSrv.BatchGetLaneInfoByLaneCode(ctx, laneCodeList)
	if err != nil {
		return nil, err
	}

	// 3.1 get "line-type to routing type" map by product id and product routing type
	roleMap := routing_role.GetRoleMapByProductAndType(ctx, productID, routingType, true)
	// 3.2 divide lines by their sub type
	lineSubTypeMap := s.laneSrv.GetLineSubTypeMapByLanes(laneInfoList, roleMap)

	// 5. Init rule detail
	ruleData := &rule.RoutingRule{
		ProductID:          productID,
		RuleDetails:        &rule.RuleDetails{},
		DefaultCriteria:    &rule.DefaultCriteria{},
		DisabledInfo:       []*rule.DisabledInfo{},
		CombinationSetting: []*rule.CombinationSetting{},
		IsMultiProduct:     true,
		CCMode:             initCCMode(lineSubTypeMap, roleMap),
	}

	// 6. Enrich rule default criteria
	defaultCriteriaType := rule.CriteriaWeightAge
	ruleData.DefaultCriteria.CriteriaType = defaultCriteriaType
	ruleData.DefaultCriteria.CriteriaName = defaultCriteriaType.String()

	// 7. Init rule detail which is needed in ilh rule; iterate each line-sub-type which is needed
	subTypeMap := map[int32]bool{}
	for lineSubType, lineList := range lineSubTypeMap {
		//if not set routing role, will just use line-sub-type as the actual routing type
		actualRoutingType := routing_role.GetRoleType(roleMap, int32(lineSubType))
		// 当产品构建时没设置该line类型为NoNeedRouting的时候，在rule中不展示，不可在前端配置
		// ilh product rule will only show line of C_ILH or C_XAndT_ILH
		if actualRoutingType == rule.LineTypeNoNeedRouting {
			continue
		}

		if _, ok := subTypeMap[actualRoutingType]; ok {
			continue
		} else {
			subTypeMap[actualRoutingType] = true
		}

		// 只有CC Routing的情况下不需要对Rule Detail初始化
		if ruleData.CCMode != rule.CCModeCCRouting {
			ruleData.RuleDetails.Rules = append(ruleData.RuleDetails.Rules, initIlhRoutingRuleDetail(actualRoutingType, lineList))
		}

		// 11. init disable info
		tmpDisableInfo := &rule.DisabledInfo{
			ResourceSubType:     actualRoutingType,
			DisplayResourceType: lfslib.LineSubTypeMap[actualRoutingType],
			LineList:            []string{},
		}
		// 12. init default weightage info
		tmpWeightAgeCriteria := &rule.CriteriaInfo{
			ResourceSubType:     actualRoutingType,
			DisplayResourceType: lfslib.LineSubTypeMap[actualRoutingType],
			DgRelated:           1, //dgRelated of C_ILH and C_XAndT_ILH is 1
			LineInfoList:        []*rule.RuleLineConfig{},
		}

		// 13. append default weightage lines, and set all lines to disable by default
		for _, line := range lineList {
			// 对于ILH Routing场景来说，Import ILH(CC)是不区分DG的
			if actualRoutingType == lfslib.C_M_ILH {
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    line.LineID,
					Name:      line.LineName,
					WeightAge: 0,
					DGFlag:    rule.UndefinedDGFlag,
				})
			} else {
				//init non-dg line
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    line.LineID,
					Name:      line.LineName,
					WeightAge: 0,
					DGFlag:    rule.NonDG,
				})
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    line.LineID,
					Name:      line.LineName,
					WeightAge: 0,
					DGFlag:    rule.DG,
				})
			}

			//set line to disable while initing rule, need enable line manually
			tmpDisableInfo.LineList = append(tmpDisableInfo.LineList, line.LineID)
		}
		// 14. append disable info and default weightage criteria
		ruleData.DisabledInfo = append(ruleData.DisabledInfo, tmpDisableInfo)
		ruleData.DefaultCriteria.WeightageCriteria = append(ruleData.DefaultCriteria.WeightageCriteria, tmpWeightAgeCriteria)
	}

	// 15. append tpl toggle info
	// 查询所有的3pl info列表信息，为3pl toggle开关提供参考
	resourceTypeList, rErr := s.ListResourceType(ctx, productID, routingType, isMultiProduct)
	if rErr != nil {
		return nil, rErr
	}
	ruleData.TplToggleInfo = resourceTypeList.ConvertToRuleListResourceType()

	return ruleData, nil
}

func (s *SoftRuleImpl) InitialRoutingRuleData(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*rule.RoutingRule, *srerr.Error) {
	ruleData := &rule.RoutingRule{
		ProductID:       productID,
		RuleDetails:     &rule.RuleDetails{},
		DefaultCriteria: &rule.DefaultCriteria{},
		DisabledInfo:    []*rule.DisabledInfo{},
	}
	//从lps获取lane code
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "InitialRoutingRuleData|get product detail err:%v", gErr)
		return nil, gErr
	}

	laneCodes := productDetail.LaneCodes
	if routingType == rule.SPXRoutingType && productDetail.IsMultiProduct {
		laneCodes = append(laneCodes, productDetail.MultiLaneCodes...)
	}

	lfsReq := &lfspb.BatchCheckLaneRoutableReqData{LaneCodeList: laneCodes}
	LaneRoutable, rErr := s.lfsApi.BatchCheckLaneRoutable(ctx, lfsReq)
	if rErr != nil {
		return nil, rErr
	}
	roleMap := routing_role.GetRoleMapByProductAndType(ctx, productID, routingType, false)

	subTypeLineInfoMap := map[int32][]*lfspb.LineInfo{}
	for _, LaneData := range LaneRoutable.RoutableList {
		subType := routing_role.GetRoleType(roleMap, LaneData.GetLineSubType())
		if _, ok := subTypeLineInfoMap[subType]; ok {
			subTypeLineInfoMap[subType] = append(subTypeLineInfoMap[subType], LaneData.GetLineList()...)
		} else {
			subTypeLineInfoMap[subType] = LaneData.GetLineList()
		}
	}
	defaultCriteriaType := rule.CriteriaWeightAge
	ruleData.DefaultCriteria.CriteriaType = defaultCriteriaType
	ruleData.DefaultCriteria.CriteriaName = defaultCriteriaType.String()
	subTypeMap := map[int32]bool{}
	for _, LaneData := range LaneRoutable.RoutableList {
		realLineSubType := LaneData.GetLineSubType()
		subType := routing_role.GetRoleType(roleMap, realLineSubType)
		// 当产品构建时没设置该line类型为NoNeedRouting的时候，在rule中不展示，不可在前端配置
		if subType == rule.LineTypeNoNeedRouting {
			continue
		}

		if routingType == rule.SPXRoutingType && objutil.ContainInt(lfslib.SpxNoNeedRoutingLine, int(realLineSubType)) {
			logger.CtxLogInfof(ctx, "Line type [%d] no need routing in spx, skip return", realLineSubType)
			continue
		}

		if _, ok := subTypeMap[subType]; ok {
			continue
		} else {
			subTypeMap[subType] = true
		}
		LineInfoList, exist := subTypeLineInfoMap[subType]
		if !exist {
			continue
		}

		tmpRule := &rule.Detail{
			ResourceSubType:           subType,
			DisplayResourceType:       lfslib.LineSubTypeMap[subType],
			Priority:                  0,
			MaxCapacityEnable:         false,
			MaxCapacitySort:           0,
			MinVolumeEnable:           false,
			MinVolumeSort:             0,
			DgRelated:                 LaneData.GetDgRelatedFlag(),
			DgClassificationSort:      0,
			DgClassificationEnable:    false,
			CheapestShippingFeeEnable: false,
			CheapestShippingFeeSort:   0,
			LineLimit:                 []*rule.LineLimit{},
		}
		tmpDisableInfo := &rule.DisabledInfo{
			ResourceSubType:     subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			LineList:            []string{},
		}
		tmpWeightAgeCriteria := &rule.CriteriaInfo{
			ResourceSubType:     subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			DgRelated:           LaneData.GetDgRelatedFlag(),
			LineInfoList:        []*rule.RuleLineConfig{},
		}
		tmpPriorityCriteria := &rule.CriteriaInfo{
			ResourceSubType:     subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			DgRelated:           LaneData.GetDgRelatedFlag(),
			LineInfoList:        []*rule.RuleLineConfig{},
		}
		for _, Line := range LineInfoList {
			tmpLineLimit := &rule.LineLimit{
				LineId:      Line.GetLineId(),
				Name:        Line.GetLineName(),
				MaxCapacity: 0,
				MinVolume:   0,
				Priority:    1,
			}
			if *Line.Enable == 1 {
				tmpRule.LineLimit = append(tmpRule.LineLimit, tmpLineLimit)
			} else {
				tmpDisableInfo.LineList = append(tmpDisableInfo.LineList, *Line.LineId)
			}

			if tmpWeightAgeCriteria.DgRelated == 1 {
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    Line.GetLineId(),
					Name:      Line.GetLineName(),
					WeightAge: 0,
					DGFlag:    1,
				})
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    Line.GetLineId(),
					Name:      Line.GetLineName(),
					WeightAge: 0,
					DGFlag:    2,
				})
			} else {
				tmpWeightAgeCriteria.LineInfoList = append(tmpWeightAgeCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:    Line.GetLineId(),
					Name:      Line.GetLineName(),
					WeightAge: 0,
					DGFlag:    0,
				})
			}

			if tmpPriorityCriteria.DgRelated == 1 {
				tmpPriorityCriteria.LineInfoList = append(tmpPriorityCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:   Line.GetLineId(),
					Name:     Line.GetLineName(),
					Priority: 0,
					DGFlag:   1,
				})
				tmpPriorityCriteria.LineInfoList = append(tmpPriorityCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:   Line.GetLineId(),
					Name:     Line.GetLineName(),
					Priority: 0,
					DGFlag:   2,
				})
			} else {
				tmpPriorityCriteria.LineInfoList = append(tmpPriorityCriteria.LineInfoList, &rule.RuleLineConfig{
					LineId:   Line.GetLineId(),
					Name:     Line.GetLineName(),
					Priority: 0,
					DGFlag:   0,
				})
			}
		}
		ruleData.RuleDetails.Rules = append(ruleData.RuleDetails.Rules, tmpRule)
		ruleData.DefaultCriteria.WeightageCriteria = append(ruleData.DefaultCriteria.WeightageCriteria, tmpWeightAgeCriteria)
		ruleData.DefaultCriteria.PriorityCriteria = append(ruleData.DefaultCriteria.PriorityCriteria, tmpPriorityCriteria)
		ruleData.DisabledInfo = append(ruleData.DisabledInfo, tmpDisableInfo)
	}
	conf, err := s.routingConfigServer.GetRoutingConfigByProductID(ctx, productID)
	if err != nil {
		return nil, err
	}
	// Local需要初始化CheapestFee选项
	if conf.IsLocalSmartRouting() {
		ruleData.RuleDetails.Rules = append(ruleData.RuleDetails.Rules, &rule.Detail{
			ResourceSubType:           0,
			DisplayResourceType:       "Lane",
			CheapestShippingFeeEnable: true,
		})
	}

	if configutil.GetProductIsWmsToggleEnable(ctx, int(productID)) {
		ruleData.WmsToggleEnable = true
	}

	// 15. append tpl toggle info
	// 查询所有的3pl info列表信息，为3pl toggle开关提供参考
	resourceTypeList, rErr := s.ListResourceType(ctx, productID, routingType, isMultiProduct)
	if rErr != nil {
		return nil, rErr
	}
	ruleData.TplToggleInfo = resourceTypeList.ConvertToRuleListResourceType()

	return ruleData, nil
}

func (s *SoftRuleImpl) GetMultiProductDgGroup(ctx context.Context, productId int) (map[string]rule.DgGroupInfo, *srerr.Error) {
	//1.Get multi product's lane list
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, productId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetMultiProductDgGroup|get product detail err:%v", gErr)
		return nil, gErr
	}
	laneCodeList := productDetail.MultiLaneCodes

	// 2. Get ILH to DG Group map
	laneInfoList, err := s.laneSrv.BatchGetLaneInfoWithoutCache(ctx, laneCodeList)
	if err != nil {
		return nil, err
	}

	var ilhLineList []string
	for _, laneInfo := range laneInfoList {
		ilh := laneInfo.GetCILHLineInfo()
		if ilh != nil && ilh.LineID != "" {
			ilhLineList = append(ilhLineList, ilh.LineID)
		}
	}

	if len(ilhLineList) == 0 {
		return nil, srerr.New(srerr.ParamErr, productId, "Multi product %d has no ILH line", productId)
	}

	dgGroupMap, err := s.llsApi.GetDgGroup(ctx, ilhLineList)
	if err != nil {
		return nil, err
	}
	ret := groupLineByDgGroup(dgGroupMap)

	return ret, nil
}

func (s *SoftRuleImpl) reloadRoutingRuleDgGroup(ctx context.Context, r *rule.RoutingRule) *rule.RoutingRule {
	dgGroupMap, err := s.GetMultiProductDgGroup(ctx, int(r.ProductID))
	if err != nil {
		// if failed, give up reload
		logger.CtxLogErrorf(ctx, "GetMultiProductDgGroup fail|err=%+v", err)
		return r
	}
	lineIdToLineNameMap, _ := s.laneSrv.GetLineIdToLineNameMap(ctx)

	// reload default criteria
	lineDefaultCriteriaMap := make(map[string]rule.DefaultCriteriaDgGroupLine)
	for _, dgGroupCriteriaInfo := range r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList {
		for _, lineCriteriaInfo := range dgGroupCriteriaInfo.LineList {
			lineDefaultCriteriaMap[schedule_factor.GetFormatKey(lineCriteriaInfo.LineId, int(lineCriteriaInfo.DGFlag))] = rule.DefaultCriteriaDgGroupLine{
				DgGroupLine: rule.DgGroupLine{LineId: lineCriteriaInfo.LineId, Name: lineCriteriaInfo.Name},
				DGFlag:      lineCriteriaInfo.DGFlag,
				WeightAge:   lineCriteriaInfo.WeightAge,
				Priority:    lineCriteriaInfo.Priority,
			}
		}
	}

	r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList = make([]rule.DgGroupCriteriaInfoItem, 0)
	for _, dgGroupInfo := range dgGroupMap {
		dgGroupNonDgCriteriaInfo := rule.DgGroupCriteriaInfoItem{
			DgGroupId:   dgGroupInfo.DgGroupId,
			DgGroupName: dgGroupInfo.DgGroupName,
			DgFlag:      rule.NonDG,
		}
		dgGroupIsDgCriteriaInfo := rule.DgGroupCriteriaInfoItem{
			DgGroupId:   dgGroupInfo.DgGroupId,
			DgGroupName: dgGroupInfo.DgGroupName,
			DgFlag:      rule.DG,
		}

		for _, lineId := range dgGroupInfo.LineList {
			dgGroupNonDgCriteriaInfo.LineList = append(dgGroupNonDgCriteriaInfo.LineList, lineDefaultCriteriaMap[schedule_factor.GetFormatKey(lineId, int(rule.NonDG))])
			dgGroupIsDgCriteriaInfo.LineList = append(dgGroupIsDgCriteriaInfo.LineList, lineDefaultCriteriaMap[schedule_factor.GetFormatKey(lineId, int(rule.DG))])
		}
		r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList = append(r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList, dgGroupNonDgCriteriaInfo)
		r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList = append(r.MultiProductDefaultCriteria.WeightageCriteria.DgGroupCriteriaInfo.DgGroupList, dgGroupIsDgCriteriaInfo)
	}

	// reload disable info
	for index := range r.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList {
		r.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList[index].LineList = make([]rule.DgGroupLine, 0)
		for _, lineId := range dgGroupMap[r.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList[index].DgGroupId].LineList {
			r.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList[index].LineList = append(r.MultiProductDisableInfo.DgGroupDisableInfo.DgGroupList[index].LineList, rule.DgGroupLine{
				LineId: lineId,
				Name:   lineIdToLineNameMap[lineId],
			})
		}
	}

	return r
}

func groupLineByDgGroup(data map[string]*llspb.GetDgGroupData) map[string]rule.DgGroupInfo {
	ret := make(map[string]rule.DgGroupInfo)
	for line, dgGroup := range data {
		dgGroupId := dgGroup.GetDgGroupId()
		if dgGroupId == "" {
			// if ilh get no dg group info, will not display in rule
			continue
		}
		if _, exist := ret[dgGroupId]; !exist {
			ret[dgGroupId] = rule.DgGroupInfo{
				DgGroupId:   dgGroupId,
				DgGroupName: dgGroup.GetDgGroupName(),
			}
		}
		retDgGroup := ret[dgGroupId]
		retDgGroup.LineList = append(retDgGroup.LineList, line)
		ret[dgGroupId] = retDgGroup
	}

	return ret
}

func (s *SoftRuleImpl) CheckLane(ctx context.Context, disabledLine []string, enabledLine []string, lineStatusMap map[string]int32, relatedLineMap map[string][]string) *srerr.Error {
	for _, onLine := range enabledLine {
		if lineStatusMap[onLine] == 0 {
			return srerr.New(srerr.DisableLineErr, onLine, "This Line is not routable in LFS")
		}
	}
	isAllDisabled := false
	for lineID, relatedLineList := range relatedLineMap {
		isExist := false
		isExistAble := false
		if objutil.ContainStr(disabledLine, lineID) {
			continue
		}
		isAllDisabled = true
		// Local会出现单条Line无关联Line的情况，不需要进行以下校验
		if len(relatedLineList) == 0 {
			continue
		}
		for _, relatedLineID := range relatedLineList {
			if objutil.ContainStr(disabledLine, relatedLineID) {
				continue
			}
			isExist = true
			if lineStatusMap[relatedLineID] == 1 {
				isExistAble = true
			}
		}
		if !isExist {
			return srerr.New(srerr.DisableLineErr, lineID, "Line cannot be turned on alone")
		}
		if !isExistAble {
			return srerr.New(srerr.DisableLineErr, lineID, "All combination is disabled")
		}
	}
	if !isAllDisabled {
		return srerr.New(srerr.DisableLineErr, nil, "All Line cannot be turned on alone")
	}
	return nil
}

func (s *SoftRuleImpl) ListWhsInfo(ctx context.Context, productID int64, isMultiProduct bool) ([]string, []soft_routing.IlhWhsInfo, *srerr.Error) {
	var laneList []string
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ListWhsInfo|get product detail err:%v", gErr)
		return nil, nil, gErr
	}
	if isMultiProduct {
		//get multi lane
		laneList = productDetail.MultiLaneCodes
	} else {
		//get single lane
		laneList = productDetail.LaneCodes
	}

	lfsReq := &lfspb.ListBatchLaneTwsInfoReqData{LaneList: laneList}
	whsInfo, err := s.lfsApi.ListBatchLaneTwsInfo(ctx, lfsReq)
	if err != nil {
		return nil, nil, err
	}

	var whsCodes []string
	for _, whs := range whsInfo.GetTwsList() {
		if whs.GetIsVirtualSite() {
			// 取虚拟仓节点的WhsCodeList
			whsCodes = append(whsCodes, whs.GetWhsCodeList()...)
		} else {
			// 取非虚拟仓节点的SiteCode
			whsCodes = append(whsCodes, whs.GetSiteCode())
		}

	}
	// whsCode 去重
	whsCodes = objutil.RemoveDuplicateString(whsCodes)

	//SSCSMR-278: add ilh whs code and their interception time
	var ilhInfoList []soft_routing.IlhWhsInfo
	if isMultiProduct {
		//get ilh info map
		twsCutoffMap := configutil.GetTwsCutoffTimeWithTimezone(ctx)
		if len(twsCutoffMap) != 0 {
			logger.CtxLogInfof(ctx, "ListWhsInfo| tws interception map is not nil, going to build up ilh whs info")
			//iterate whs code, and then build up ilh info list
			for _, twsCode := range whsCodes {
				//only append not empty time and whs id
				t, exist := twsCutoffMap[twsCode]
				if !exist {
					logger.CtxLogErrorf(ctx, "Can not find cutoff time for [%s], will not return", twsCode)
					continue
				}
				hour, min := math.Modf(t.CutoffTime)
				ilhInfoList = append(ilhInfoList, soft_routing.IlhWhsInfo{
					WhsID:            twsCode,
					InterceptionTime: fmt.Sprintf("%d:%02d", int(hour), int(min*60)),
					Timezone:         t.TimezoneOffset,
				})
			}
		}
	}

	return whsCodes, ilhInfoList, nil
}

func (s *SoftRuleImpl) ListILHWhsInfo(ctx context.Context, ilhLineID string) ([]soft_routing.IlhWhsInfo, *srerr.Error) {
	// 1. 从LPS获取所有Product信息
	getLanesResp, err := s.lfsApi.GetLaneCodeListBySiteOrLine(ctx, &lfsentity.GetLaneCodeBySiteOrLineReq{SiteLineId: ilhLineID})
	if err != nil {
		return nil, err
	}

	laneCodes := getLanesResp.Data.LaneCodes
	if len(laneCodes) == 0 {
		return nil, srerr.New(srerr.ParamErr, ilhLineID, "can not find lanes by ilh line id")
	}

	whsInfo, err := s.lfsApi.ListBatchLaneTwsInfo(ctx, &lfspb.ListBatchLaneTwsInfoReqData{LaneList: laneCodes})
	if err != nil {
		return nil, err
	}

	var whsCodes []string
	for _, whs := range whsInfo.GetTwsList() {
		if whs.GetIsVirtualSite() {
			// 取虚拟仓节点的WhsCodeList
			whsCodes = append(whsCodes, whs.GetWhsCodeList()...)
		} else {
			// 取非虚拟仓节点的SiteCode
			whsCodes = append(whsCodes, whs.GetSiteCode())
		}
	}
	// whsCode 去重
	whsCodes = objutil.RemoveDuplicateString(whsCodes)

	twsCutoffMap := configutil.GetTwsCutoffTimeWithTimezone(ctx)
	ilhWhsInfoList := make([]soft_routing.IlhWhsInfo, 0)
	for _, whsCode := range whsCodes {
		twsCutoffConfig, exist := twsCutoffMap[whsCode]
		if !exist {
			continue
		}
		hour, minute := math.Modf(twsCutoffConfig.CutoffTime)
		ilhWhsInfoList = append(ilhWhsInfoList, soft_routing.IlhWhsInfo{
			WhsID:            whsCode,
			InterceptionTime: fmt.Sprintf("%d:%02d", int(hour), int(minute*60)),
			Timezone:         twsCutoffConfig.TimezoneOffset,
		})
	}

	return ilhWhsInfoList, nil
}

func (s *SoftRuleImpl) ListILHDestinationPort(ctx context.Context, ilhLineID string) ([]soft_routing.DestinationPortInfo, *srerr.Error) {
	// 1. 从LFS获取所有Product信息
	getLanesResp, err := s.lfsApi.GetLaneCodeListBySiteOrLine(ctx, &lfsentity.GetLaneCodeBySiteOrLineReq{SiteLineId: ilhLineID})
	if err != nil {
		return nil, err
	}
	laneCodes := getLanesResp.Data.LaneCodes
	if len(laneCodes) == 0 {
		return nil, srerr.New(srerr.ParamErr, ilhLineID, "can not find lanes by ilh line id")
	}
	laneInfos, err := s.laneSrv.BatchGetLaneInfoByLaneCode(ctx, laneCodes)
	if err != nil {
		return nil, err
	}

	// 使用map来去重
	portMap := make(map[string]soft_routing.DestinationPortInfo)
	for _, laneInfo := range laneInfos {
		if site := laneInfo.GetExportThirdPartyJoint(); site != nil {
			// 使用ID作为key来去重
			portMap[site.SiteID] = soft_routing.DestinationPortInfo{
				ID:   site.SiteID,
				Name: site.SiteName,
			}
		}
	}

	destinationPortList := make([]soft_routing.DestinationPortInfo, 0, len(portMap))
	for _, port := range portMap {
		destinationPortList = append(destinationPortList, port)
	}

	return destinationPortList, nil
}

func (s *SoftRuleImpl) ListResourceType(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) (*soft_routing.ListResourceTypeResp, *srerr.Error) {
	if isMultiProduct {
		return s.ListMultiProductResourceType(ctx, productID, int(routingType))
	}
	//获取single lane code
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ListResourceType|get product detail err:%v", gErr)
		return nil, gErr
	}
	laneCodes := productDetail.LaneCodes
	if routingType == rule.SPXRoutingType && productDetail.IsMultiProduct {
		laneCodes = append(laneCodes, productDetail.MultiLaneCodes...)
	}

	lfsReq := &lfspb.BatchCheckLaneRoutableReqData{LaneCodeList: laneCodes}
	LaneRoutable, rErr := s.lfsApi.BatchCheckLaneRoutable(ctx, lfsReq)
	if rErr != nil {
		return nil, rErr
	}
	var ret = &soft_routing.ListResourceTypeResp{
		LaneCodes:    productDetail.LaneCodes,
		ResourceList: []*rule.LaneInfo{},
	}
	//SPLPS-7388:lfs返回的C_D_FM是单独的，要映射到C_LM; 但是新增的C_D_LM不用映射到L_LM
	// 使用routing role配置替换sub type
	roleMap := routing_role.GetRoleMapByProductAndType(ctx, productID, routingType, false)
	for _, Lane := range LaneRoutable.GetRoutableList() {
		realLineSubType := Lane.GetLineSubType()
		subType := routing_role.GetRoleType(roleMap, realLineSubType)
		if subType == rule.LineTypeNoNeedRouting {
			continue
		}

		if routingType == rule.SPXRoutingType && objutil.ContainInt(lfslib.SpxNoNeedRoutingLine, int(realLineSubType)) {
			logger.CtxLogInfof(ctx, "Line type [%d] no need routing in spx, skip return", realLineSubType)
			continue
		}

		tmpLaneInfo := &rule.LaneInfo{
			RoutingType:         subType,
			DisplayResourceType: lfslib.LineSubTypeMap[subType],
			DgRelated:           Lane.GetDgRelatedFlag(),
			LineList:            []*rule.Line{},
		}
		for _, LineInfo := range Lane.GetLineList() {
			tmpLaneInfo.LineList = append(tmpLaneInfo.LineList, &rule.Line{
				LineId:  LineInfo.GetLineId(),
				Name:    LineInfo.GetLineName(),
				Enabled: LineInfo.GetEnable(),
			})
		}
		ret.ResourceList = append(ret.ResourceList, tmpLaneInfo)
	}
	return ret, nil
}

func (s *SoftRuleImpl) ListMultiProductResourceType(ctx context.Context, productID int64, routingType int) (*soft_routing.ListResourceTypeResp, *srerr.Error) {
	var laneCodes []string
	productDetail, gErr := s.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ListMultiProductResourceType|get product detail err:%v", gErr)
		return nil, gErr
	}
	if routingType == rule.IlhRoutingType {
		//获取multi ilh lane
		laneCodes = productDetail.IlhLaneCodes
	} else {
		//获取multi lane
		laneCodes = productDetail.MultiLaneCodes
	}

	laneInfoList, err := s.laneSrv.BatchGetLaneInfoByLaneCode(ctx, laneCodes)
	if err != nil {
		return nil, err
	}

	roleMap := routing_role.GetRoleMapByProductAndType(ctx, productID, uint8(routingType), true)
	lineSubTypeMap := s.laneSrv.GetLineSubTypeMapByLanes(laneInfoList, roleMap)

	var ret = &soft_routing.ListResourceTypeResp{
		LaneCodes:    laneCodes,
		ResourceList: []*rule.LaneInfo{},
	}

	for subType, lineList := range lineSubTypeMap {
		// cuz in multi product, ilh is display as dg group, so no need to return ilh line to fe
		if routingType != rule.IlhRoutingType && isIlhNeedDg(subType, lineList) {
			continue
		}

		if int32(subType) == rule.LineTypeNoNeedRouting {
			continue
		}

		tmpLaneInfo := &rule.LaneInfo{
			RoutingType:         int32(subType),
			DisplayResourceType: lfslib.LineSubTypeMap[int32(subType)],
			LineList:            []*rule.Line{},
		}

		for _, line := range lineList {
			tmpLaneInfo.LineList = append(tmpLaneInfo.LineList, &rule.Line{
				LineId:  line.LineID,
				Name:    line.LineName,
				Enabled: 1,
			})
		}
		ret.ResourceList = append(ret.ResourceList, tmpLaneInfo)
	}

	return ret, nil
}

func (s *SoftRuleImpl) GetSmartRoutingProductList(ctx context.Context, routingType uint8, isMultiProduct bool) ([]*soft_routing.SimpleProductInfo, *srerr.Error) {
	productListLps, gErr := s.lpsApi.GetProductBaseInfoList(ctx)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetSmartRoutingProductList|get product list err:%v", gErr)
		return nil, gErr
	}
	retProductInfos := make([]*soft_routing.SimpleProductInfo, 0, len(productListLps))

	for _, prd := range productListLps {
		if isMultiProduct {
			// 只取 product 属性为 multi 的，
			if objutil.IsTrue(prd.IsMultiProduct) {
				retProductInfos = append(retProductInfos, &soft_routing.SimpleProductInfo{
					ProductID:   int64(prd.ProductId),
					ProductName: prd.SellerDisplayName,
				})
			}
		} else {
			retProductInfos = append(retProductInfos, &soft_routing.SimpleProductInfo{
				ProductID:   int64(prd.ProductId),
				ProductName: prd.SellerDisplayName,
			})
		}
	}

	return retProductInfos, nil
}

func checkContainLaneFee(src *ruledata.RoutingRuleTab) bool {
	detail := rule.RuleDetails{}
	if err := json.Unmarshal(src.Rules, &detail); err != nil {
		return false
	}

	for _, ruleDetail := range detail.Rules {
		if ruleDetail.ResourceSubType == 0 && ruleDetail.CheapestShippingFeeEnable {
			return true
		}
	}
	return false
}

func CheckConnectionsBetweenIlhAndLm(ilh string, lm string, BusinessBlacklist map[string][]string) bool {
	lmList, exist := BusinessBlacklist[ilh]
	if !exist {
		// not in blacklist, pass
		return true
	}
	if exist {
		// check in blacklist
		contain := objutil.ContainStr(lmList, lm)
		if contain {
			// in black list, no pass
			return false
		} else {
			// not in black list, pass
			return true
		}
	}
	return true
}

// isIlhNeedDg 通过固定类型（旧）+ Line是否有配置规则（新）的方法判读ILH是否带有DG属性
func isIlhNeedDg(resourceSubType int, lineList []*lane_entity.LineInfo) bool {
	return objutil.ContainsInt(lfslib.NeedRoutingILH, resourceSubType) ||
		(len(lineList) > 0 && lineList[0].NeedDG())
}

// 这里forecast克隆rule的时候展示rule列表，有限展示active，然后是expire，如果active达到100条就不展示expire
func (s *SoftRuleImpl) GetRuleForForecastDisplay(ctx context.Context, ruleQuery *rule.RuleQuery) ([]*rule.RoutingRule, int32, *srerr.Error) {
	ruleList := []ruledata.RoutingRuleTab{}
	if err := dbutil.Select(ctx, ruledata.RoutingRuleHook, map[string]interface{}{
		"product_id = ?":     ruleQuery.ProductID,
		"routing_type = ?":   ruleQuery.RoutingType,
		"rule_status in (?)": []rule.RuleStatus{rule.RuleStatusActive, rule.RuleStatusExpired},
	}, &ruleList, dbutil.WithLimit(int64(ruleQuery.Limit)), dbutil.WithOrder("rule_status asc, ctime desc")); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	resp := make([]*rule.RoutingRule, len(ruleList))
	for idx, r := range ruleList {
		resp[idx] = &rule.RoutingRule{}
		resp[idx].ProductID = r.ProductID
		resp[idx].Priority = r.Priority
		resp[idx].RuleName = r.RuleName
		resp[idx].Status = r.Status
		resp[idx].ID = r.ID
	}
	return resp, int32(len(resp)), nil
}

func (s *SoftRuleImpl) ListIlhCombinationInfo(ctx context.Context, productId int) (soft_routing.IlhCombinationInfo, *srerr.Error) {
	// 1.从LPS获取Product信息（Lanes信息 + ILH和LM的黑名单关系）
	productInfo, err := s.lpsApi.GetProductDetail(ctx, productId)
	if err != nil {
		return soft_routing.IlhCombinationInfo{}, err
	}

	// 2. 从所有Lane中解析出ILH、ImportILH、LM
	var combinationInfo soft_routing.IlhCombinationInfo
	var ilhList, lmList []rule.BaseLineInfo
	for _, laneCode := range productInfo.MultiLaneCodes {
		laneInfo, err := s.laneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
		if err != nil {
			return soft_routing.IlhCombinationInfo{}, err
		}
		importIlh := laneInfo.GetImportILHLineInfo()
		if importIlh.LineID != "" {
			combinationInfo.ImportIlhList = append(combinationInfo.ImportIlhList, rule.BaseLineInfo{
				LineId:   importIlh.LineID,
				LineName: importIlh.LineName,
			})
		}

		ilh := laneInfo.GetCILHLineInfo()
		if ilh.LineID != "" {
			ilhList = append(ilhList, rule.BaseLineInfo{
				LineId:   ilh.LineID,
				LineName: ilh.LineName,
			})
		}

		lm := laneInfo.GetCLMLineInfo()
		if lm.LineID != "" {
			lmList = append(lmList, rule.BaseLineInfo{
				LineId:   lm.LineID,
				LineName: lm.LineName,
			})
		}
	}

	// 3. 去重
	combinationInfo.ImportIlhList = removeDuplicateBaseLineInfo(combinationInfo.ImportIlhList)
	ilhList = removeDuplicateBaseLineInfo(ilhList)
	lmList = removeDuplicateBaseLineInfo(lmList)

	// 4. 检查ILH和LM的黑名单，返回可用的ILH+LM组合
	blackListMap := make(map[string][]string, len(productInfo.BusinessBlacklist))
	for _, blackItem := range productInfo.BusinessBlacklist {
		blackListMap[blackItem.LineId] = blackItem.LineList
	}

	logger.CtxLogDebugf(ctx, "get black list between ILH and LM: %s", objutil.JsonString(blackListMap))

	for _, ilh := range ilhList {
		for _, lm := range lmList {
			if pass := CheckConnectionsBetweenIlhAndLm(ilh.LineId, lm.LineId, blackListMap); !pass {
				continue
			}
			combinationInfo.IlhAndLmGroupList = append(combinationInfo.IlhAndLmGroupList, soft_routing.IlhAndLmGroup{
				Ilh: ilh,
				Lm:  lm,
			})
		}
	}

	return combinationInfo, nil
}

func removeDuplicateBaseLineInfo(lineList []rule.BaseLineInfo) []rule.BaseLineInfo {
	checkMap := make(map[string]rule.BaseLineInfo, 0)
	for _, line := range lineList {
		if _, exist := checkMap[line.LineId]; exist {
			continue
		}
		checkMap[line.LineId] = line
	}

	ret := make([]rule.BaseLineInfo, 0, len(checkMap))
	for _, lineInfo := range checkMap {
		ret = append(ret, lineInfo)
	}

	return ret
}
