package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// db writes,
func InsertRoutingCfgAndRolemap(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error {
	//
	//can done, insert rows ,
	//table 切换需要保证配置是原子的，否则会出错。
	db, dbErr := dbutil.MasterDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	//fmt.Println("check ,", ruledata.RoutingConfigHook.DBForWrite())
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		tx := scormv2.Context(ctx)
		if cfgtab != nil {
			//check exist ?,
			rcfg := GetRoutingcfgByPid(ctx, cfgtab.ProductID, tx)
			if rcfg == nil || rcfg.ProductID == 0 {
				if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Create(cfgtab).GetError(); err1 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err1)
				}
			} else {
				// exist record , not exist in product_tab ,
				upd := dbutil.UpdateIncludeMapper(cfgtab,
					"smart_routing_enabled", "local_routing_enable",
					"spx_routing_enable", "ilh_routing_enable", "cb_multi_routing_enable",
					"default_lane_code", "operated_by", "mtime", "ctime")
				if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Where("product_id = ?", cfgtab.ProductID).Updates(upd).GetError(); err1 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err1)
				}
			}
		}
		if rolemapTab != nil {
			//if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(rolemapTab).Error; err2 != nil {
			//	return srerr.With(srerr.DatabaseErr, nil, err2)
			//}
			routingRoleRecord := GetRoleInfoByPid(ctx, int64(rolemapTab.ProductId), tx)
			if routingRoleRecord == nil || routingRoleRecord.ProductId == 0 {
				//Insert ??
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(rolemapTab).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}

			} else {
				// for Update-record ,
				upd := dbutil.UpdateIncludeMapper(rolemapTab,
					"cb_routing_role", "cb_multi_routing_role",
					"spx_routing_role", "local_routing_role",
					"ilh_routing_role", "mtime", "ctime")
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Where("product_id = ? ", rolemapTab.ProductId).Updates(upd).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}
			}
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func UpdateRoutingCfgAndRolemap(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error {
	//
	//can done, insert rows ,
	pid := cfgtab.ProductID
	db, dbErr := dbutil.MasterDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	//
	logger.CtxLogDebugf(ctx, "check,", ruledata.RoutingConfigHook.DBForWrite())
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		//update-modes ??
		tx := scormv2.Context(ctx)
		if cfgtab != nil {
			upd := dbutil.UpdateIncludeMapper(cfgtab,
				"smart_routing_enabled", "local_routing_enable",
				"spx_routing_enable", "ilh_routing_enable", "cb_multi_routing_enable",
				"default_lane_code", "operated_by", "mtime")
			if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Where("product_id = ?", pid).Updates(upd).GetError(); err1 != nil {
				return srerr.With(srerr.DatabaseErr, nil, err1)
			}
		}

		if rolemapTab != nil {
			//todo ,query master-db
			routingRoleRecord := GetRoleInfoByPid(ctx, pid, tx)
			if routingRoleRecord == nil || routingRoleRecord.ProductId == 0 {
				//Insert ??
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(rolemapTab).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}

			} else {
				// for Update-record ,
				upd := dbutil.UpdateIncludeMapper(rolemapTab,
					"cb_routing_role", "cb_multi_routing_role",
					"spx_routing_role", "local_routing_role",
					"ilh_routing_role", "mtime")
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Where("product_id = ? ", pid).Updates(upd).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}
			}
		}

		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func GetRoleInfoByPid(ctx context.Context, pid int64, tx scormv2.SQLCommon) *ruledata.ProductRoutingRoleTab {
	//
	ret := new(ruledata.ProductRoutingRoleTab)
	err := tx.Take(&ret, "product_id = ? ", pid).GetError()
	if err != nil {
		logger.CtxLogErrorf(ctx, "err %v", err)
		return nil
	}
	return ret
}

func GetRoleInfoByProdcutId(ctx context.Context, pid int64) (*ruledata.ProductRoutingRoleTab, error) {
	//
	db, dbErr := dbutil.SlaveDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return nil, dbErr
	}
	ret := new(ruledata.ProductRoutingRoleTab)
	err := db.Take(&ret, "product_id = ? ", pid).GetError()
	if err != nil {
		logger.CtxLogErrorf(ctx, "err %v", err)
		return nil, err
	}
	return ret, nil
}

func GetRoutingcfgByPid(ctx context.Context, pid int64, tx scormv2.SQLCommon) *ruledata.RoutingConfigTab {
	//
	ret := new(ruledata.RoutingConfigTab)
	err := tx.Take(&ret, "product_id = ? ", pid).GetError()
	if err != nil {
		logger.CtxLogErrorf(ctx, "err %v", err)
		return nil
	}
	return ret
}
