package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

func (rs *RoutingServiceImpl) filterByProhibitedLine(ctx context.Context, lanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog) []*rule.RoutingLaneInfo {
	var availableLanes []*rule.RoutingLaneInfo
	//var beforeLaneInfos []string
	before := make(map[string]map[string][]string)
	after := make(map[string]map[string][]string)
	for _, laneInfo := range lanes {
		//beforeLaneInfos = append(beforeLaneInfos, laneInfo.LaneCode)
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if before[laneInfo.LaneCode] == nil {
				before[laneInfo.LaneCode] = make(map[string][]string)
			}
			before[laneInfo.LaneCode][key] = append(before[laneInfo.LaneCode][key], line.LineId)
		}
	}
	logger.CtxLogInfof(ctx, "before FilterByAvailability |lanes=%v", before)
	lineFilterOutByWmsToggle := make([]string, 0)
	for _, laneInfo := range lanes {
		containFlag := true
		for _, line := range laneInfo.LineList {
			// available lines does not contain no need routing line, so no need to check
			if line.ResourceSubType == rule.LineTypeNoNeedRouting {
				continue
			}

			// 对于CC Line，在非CC Routing Mode下不需要校验
			if line.ResourceSubType == lfslib.C_M_ILH && !orderData.IsCCRoutingMode() {
				continue
			}

			if !objutil.ContainStr(matchedRule.AvailableLines, line.ResourceId) {
				//lane下面有1个resourceID不在 AvailableLines,则被过滤掉
				containFlag = false
				break
			}

			// wms toggle check
			if matchedRule.WmsToggleEnable && orderData.IsWms {
				if !objutil.ContainStr(matchedRule.WmsAvailableLines, line.ResourceId) {
					lineFilterOutByWmsToggle = append(lineFilterOutByWmsToggle, line.ResourceId)
					containFlag = false
					break
				}
			}
		}

		if containFlag {
			availableLanes = append(availableLanes, laneInfo)
		}
	}

	//var afterLaneInfos []string
	for _, laneInfo := range availableLanes {
		//afterLaneInfos = append(afterLaneInfos, laneInfo.LaneCode)
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if after[laneInfo.LaneCode] == nil {
				after[laneInfo.LaneCode] = make(map[string][]string)
			}
			after[laneInfo.LaneCode][key] = append(after[laneInfo.LaneCode][key], line.LineId)
		}
	}

	if matchedRule.WmsToggleEnable && orderData.IsWms && len(lineFilterOutByWmsToggle) > 0 {
		logger.CtxLogDebugf(ctx, "lines %v was filtered out by support wms toggle", lineFilterOutByWmsToggle)
	}

	logger.CtxLogInfof(ctx, "after FilterByAvailability availableLanes=%v", after)

	setProhibitFilterLogs(logEntry, before, after)

	return availableLanes
}

func (rs *RoutingServiceImpl) multiProductFilterByProhibitedLine(ctx context.Context, lanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed) []*rule.RoutingLaneInfo {
	logEntryInterface := ctx.Value(routing_log.RoutingLogContextKey)
	logEntry, _ := logEntryInterface.(*routing_log.RoutingLog)

	var availableLanes []*rule.RoutingLaneInfo
	//var beforeLaneInfos []string
	//记录multi开启的关闭的line
	before := make(map[string]map[string][]string) //lanecode:[typeName:[]string]
	after := make(map[string]map[string][]string)
	for _, laneInfo := range lanes {
		//beforeLaneInfos = append(beforeLaneInfos, laneInfo.LaneCode)、
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if before[laneInfo.LaneCode] == nil {
				before[laneInfo.LaneCode] = make(map[string][]string)
			}
			before[laneInfo.LaneCode][key] = append(before[laneInfo.LaneCode][key], line.LineId)
		}
	}
	logger.CtxLogInfof(ctx, "before FilterByAvailability |lanes=%v", before)
	for _, laneInfo := range lanes {
		containFlag := true
		for _, line := range laneInfo.LineList {
			// available lines does not contain no need routing line, so no need to check
			if line.ResourceSubType == rule.LineTypeNoNeedRouting {
				continue
			}
			if objutil.ContainsInt(lfslib.NeedRoutingILH, int(line.ResourceSubType)) {
				if !objutil.ContainStr(matchedRule.AvailableDgGroup, laneInfo.DgGroupId) {
					containFlag = false
					break
				}
			} else {
				if !objutil.ContainStr(matchedRule.AvailableLines, line.ResourceId) { //lane下面有1个resourceID不在 AvailableLines,则被过滤掉
					containFlag = false
					break
				}
			}
		}
		if containFlag {
			availableLanes = append(availableLanes, laneInfo)
		}
	}

	//var afterLaneInfos []string
	for _, laneInfo := range availableLanes {
		//afterLaneInfos = append(afterLaneInfos, laneInfo.LaneCode)
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if after[laneInfo.LaneCode] == nil {
				after[laneInfo.LaneCode] = make(map[string][]string)
			}
			after[laneInfo.LaneCode][key] = append(after[laneInfo.LaneCode][key], line.LineId)
		}
	}
	logger.CtxLogInfof(ctx, "after FilterByAvailability availableLanes=%v", after)

	setProhibitFilterLogs(logEntry, before, after)

	return availableLanes
}

func (rs *RoutingServiceImpl) filterByAvailableCombination(lanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData) []*rule.RoutingLaneInfo {
	var ret []*rule.RoutingLaneInfo
	for _, l := range lanes {
		ilh, importIlh := schedule_factor.GetIlhAndImportIlhFromLaneInfo(l)
		c := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        orderData.LmId,
		}
		_, ok := matchedRule.AvailableCombination[c]
		if ok {
			ret = append(ret, l)
		}
	}

	return ret
}
