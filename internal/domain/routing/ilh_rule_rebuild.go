package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"sort"
)

func RebuildCCModeIlhRuleData(ctx context.Context, effectiveRule *rule.RoutingRule) (*rule.RoutingRuleParsed, *srerr.Error) {
	if effectiveRule.RuleDetails == nil {
		logger.CtxLogErrorf(ctx, "get effectiveRule nil error: |%v", effectiveRule)
		return nil, srerr.New(srerr.ParamErr, nil, "invalid routing rule config")
	}
	// sort soft Criteria and compose rule struct
	ruleSteps := rule.RuleSteps{}
	lineToggle := make(map[string]*rule.Toggle)
	for _, ruleDetail := range effectiveRule.RuleDetails.Rules {
		//记录开启和关闭的line
		setLineToggle(ruleDetail.DisplayResourceType, lineToggle, ruleDetail.LineLimit, effectiveRule.DisabledInfo)

		if ruleDetail.MinVolumeEnable {
			minVolumes := map[string]int{}
			combinationMinVolumes := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationMinVolumes[key] = limit.MinVolume
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					minVolumes[key] = limit.MinVolume
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode: ruleDetail.CombinationMode,
				ResourceSubType: ruleDetail.ResourceSubType,
				Name:            schedule_factor.MinVolume,
				Priority:        ruleDetail.Priority,
				MinVolumeData:   &rule.MinVolumeData{MinVolumes: minVolumes, CombinationMinVolumes: combinationMinVolumes},
			}
			logger.CtxLogInfof(ctx, "rule min volume step=%v|MinVolumeData=%v|Priority=%d", step, step.MinVolumeData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxCapacityEnable {
			maxCapacities := map[string]int{}
			combinationMaxCapacities := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationMaxCapacities[key] = limit.MinVolume
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					maxCapacities[key] = limit.MaxCapacity
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode: ruleDetail.CombinationMode,
				ResourceSubType: ruleDetail.ResourceSubType,
				Name:            schedule_factor.MaxCapacity,
				Priority:        ruleDetail.Priority,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxCapacities, CombinationMaxCapacities: combinationMaxCapacities},
			}
			logger.CtxLogInfof(ctx, "rule max capacity step=%v|MaxCapacityData=%v|Priority=%d", step, step.MaxCapacityData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MinWeightEnable {
			lineMinWeights := make(map[string]int)
			combinationMinWeights := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationMinWeights[key] = limit.MinWeight
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					lineMinWeights[key] = limit.MinWeight
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode: ruleDetail.CombinationMode,
				ResourceSubType: ruleDetail.ResourceSubType,
				Name:            schedule_factor.MinWeight,
				Priority:        ruleDetail.Priority,
				MinWeightData:   &rule.MinWeightData{MinWeights: lineMinWeights, CombinationMinWeights: combinationMinWeights},
			}
			logger.CtxLogInfof(ctx, "min weight step=%v|MinWeightData=%v|Priority=%d", step, step.MinWeightData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxWeightCapacityEnable {
			lineMaxWeights := make(map[string]int)
			combinationMaxWeights := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationMaxWeights[key] = limit.MaxWeightCapacity
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					lineMaxWeights[key] = limit.MaxWeightCapacity
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode: ruleDetail.CombinationMode,
				ResourceSubType: ruleDetail.ResourceSubType,
				Name:            schedule_factor.MaxWeight,
				Priority:        ruleDetail.Priority,
				MaxWeightData:   &rule.MaxWeightData{MaxWeights: lineMaxWeights, CombinationMaxWeights: combinationMaxWeights},
			}
			logger.CtxLogInfof(ctx, "max weight step=%v|MaxWeightData=%v|Priority=%d", step, step.MaxWeightData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.ParcelMinVolumeEnable {
			lineParcelMinVolume := make(map[string]int)
			combinationParcelMinVolume := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationParcelMinVolume[key] = limit.IlhParcelMinVolume
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					lineParcelMinVolume[key] = limit.IlhParcelMinVolume
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode:        ruleDetail.CombinationMode,
				ResourceSubType:        ruleDetail.ResourceSubType,
				Name:                   schedule_factor.ILHParcelMinVolume,
				Priority:               ruleDetail.Priority,
				IlhParcelMinVolumeData: &rule.MinVolumeData{MinVolumes: lineParcelMinVolume, CombinationMinVolumes: combinationParcelMinVolume},
			}
			logger.CtxLogInfof(ctx, "parcel min volume step=%v|IlhParcelMinVolumeData=%v|Priority=%d", step, step.IlhParcelMinVolumeData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.ParcelMaxCapacityEnable {
			lineParcelMaxCapacity := make(map[string]int)
			combinationIlhParcelMaxCapacity := map[rule.IlhCombination]int{}
			for _, limit := range ruleDetail.LineLimit {
				if ruleDetail.CombinationMode {
					key := combinationLineListToIlhCombination(ctx, limit.CombinationLines)
					combinationIlhParcelMaxCapacity[key] = limit.IlhParcelMaxCapacity
				} else {
					key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
					lineParcelMaxCapacity[key] = limit.IlhParcelMaxCapacity
				}
			}
			step := rule.ScheduleFactorAttr{
				CombinationMode:          ruleDetail.CombinationMode,
				ResourceSubType:          ruleDetail.ResourceSubType,
				Name:                     schedule_factor.ILHParcelMaxCapacity,
				Priority:                 ruleDetail.Priority,
				IlhParcelMaxCapacityData: &rule.MaxCapacityData{MaxCapacities: lineParcelMaxCapacity, CombinationMaxCapacities: combinationIlhParcelMaxCapacity},
			}
			logger.CtxLogInfof(ctx, "parcel max capacity step=%v|IlhParcelMaxCapacityData=%v|Priority=%d", step, step.IlhParcelMaxCapacityData, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}
	}

	sort.Sort(ruleSteps)

	defaultRuleSteps, seenLines := rebuildRuleDefaultCriteria(ctx, effectiveRule)
	ruleSteps = append(ruleSteps, defaultRuleSteps...)

	ruleScheduler := &rule.RoutingRuleParsed{
		ID:                   effectiveRule.ID,
		IlhRuleSteps:         ruleSteps,
		RoutingType:          effectiveRule.RoutingType,
		WhsId:                effectiveRule.WhsId,
		DestinationPort:      effectiveRule.DestinationPorts,
		ZoneCode:             effectiveRule.ZoneCode,
		ItemCategoryLevel:    effectiveRule.ItemCategoryLevel,
		ItemCategoryID:       effectiveRule.ItemCategoryID,
		ParcelValueMax:       effectiveRule.ParcelValueMax,
		ParcelValueMin:       effectiveRule.ParcelValueMin,
		ParcelWeightMax:      effectiveRule.ParcelWeightMax,
		ParcelWeightMin:      effectiveRule.ParcelWeightMin,
		DgType:               effectiveRule.DgType,
		ParcelDimension:      effectiveRule.ParcelDimension,
		WmsToggleEnable:      effectiveRule.WmsToggleEnable,
		LineToggle:           lineToggle,
		AvailableLines:       getAvailableLines(seenLines, effectiveRule.DisabledInfo),
		DefaultPriorities:    addLinePriorities(effectiveRule.DefaultCriteria),
		DefaultCriteriaName:  getDefaultCriteriaName(effectiveRule.DefaultCriteria.CriteriaType),
		DefaultCriteriaType:  effectiveRule.DefaultCriteria.CriteriaType,
		AvailableCombination: GetAvailableCombination(ctx, effectiveRule.CombinationSetting),
		CCMode:               effectiveRule.CCMode,
	}

	return ruleScheduler, nil
}

// combinationLineListToIlhCombination 从Rule中Line List解析出ILH Combination
func combinationLineListToIlhCombination(ctx context.Context, lines []rule.BaseLineInfo) rule.IlhCombination {
	if len(lines) != 3 {
		reportData := fmt.Sprintf("combination info line list length not equal to 3, line list=%v", lines)
		logger.CtxLogErrorf(ctx, reportData)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleILHRoutingCCMode, monitoring.CombinationInfoInValid,
			monitoring.StatusError, reportData)
		return rule.IlhCombination{}
	}

	return rule.IlhCombination{
		ImportIlh: lines[0].LineId,
		Ilh:       lines[1].LineId,
		Lm:        lines[2].LineId,
	}
}

func rebuildRuleDefaultCriteria(ctx context.Context, r *rule.RoutingRule) ([]*rule.ScheduleFactorAttr, map[string]bool) {
	var ruleSteps rule.RuleSteps
	seeLines := make(map[string]bool)
	for _, c := range r.DefaultCriteria.WeightageCriteria {
		lineWeight := make(map[string]int, len(c.LineInfoList))
		for _, l := range c.LineInfoList {
			seeLines[l.LineId] = true
			lineKey := helper.FormatLineAndDgKey(l.LineId, int(l.DGFlag), rule.DgRelated)
			lineWeight[lineKey] = l.WeightAge
		}
		ruleSteps = append(ruleSteps, &rule.ScheduleFactorAttr{
			CombinationMode:    false,
			ResourceSubType:    c.ResourceSubType,
			Priority:           int32(c.Priority),
			Name:               schedule_factor.DefaultWeightage,
			DefaultCriteriaCfg: lineWeight,
		})
	}
	ruleSteps = append(ruleSteps, &rule.ScheduleFactorAttr{
		CombinationMode:       true,
		Priority:              int32(r.DefaultCriteria.CombinationPriorityCriteria.Priority),
		Name:                  schedule_factor.CombinationPriority,
		CombinationPriorities: getCombinationPriorities(ctx, r),
	})

	sort.Sort(ruleSteps)

	return ruleSteps, seeLines
}

// getCombinationPriorities 从Routing Rule中解析出ILh Combination的Priority关系
// 顺序固定: ImportIlh -> Ilh -> Lm
func getCombinationPriorities(ctx context.Context, r *rule.RoutingRule) map[rule.IlhCombination]int32 {
	p := make(map[rule.IlhCombination]int32, len(r.DefaultCriteria.CombinationPriorityCriteria.CombinationInfoList))

	for _, c := range r.DefaultCriteria.CombinationPriorityCriteria.CombinationInfoList {
		if len(c.LineList) != 3 {
			reportData := fmt.Sprintf("combination info line list length not equal to 3, line list=%v", c.LineList)
			logger.CtxLogErrorf(ctx, reportData)
			_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleILHRoutingCCMode, monitoring.CombinationInfoInValid,
				monitoring.StatusError, reportData)
			continue
		}
		p[rule.IlhCombination{
			Ilh:       c.LineList[1].LineId,
			ImportIlh: c.LineList[0].LineId,
			Lm:        c.LineList[2].LineId,
		}] = int32(c.CombinationPriority)
	}

	return p
}
