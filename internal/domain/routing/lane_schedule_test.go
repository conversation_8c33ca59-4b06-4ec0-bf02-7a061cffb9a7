package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func Test_routingTask_retrieveLineFromProcedureLaneList(t *testing.T) {
	rt := &routingTask{
		routingData: &rule.RoutingData{
			ValidateLaneList: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							ResourceId:      "line-1",
							ResourceSubType: lfslib.C_FL,
						},
						{
							ResourceId:      "line-1",
							ResourceSubType: lfslib.C_FL,
						},
						{
							ResourceId:      "line-2",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
			},
		},
	}
	tests := []struct {
		name string
		want map[int32][]*rule.LineInfo
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			want: map[int32][]*rule.LineInfo{
				lfslib.C_FL: {
					{
						ResourceId:      "line-1",
						ResourceSubType: lfslib.C_FL,
					},
					{
						ResourceId:      "line-2",
						ResourceSubType: lfslib.C_FL,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common.AssertResult(t, rt.retrieveLineFromProcedureLaneList(), tt.want, nil, nil)
			if got := rt.retrieveLineFromProcedureLaneList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("retrieveLineFromProcedureLaneList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFilterBySpxAvailableLane(t *testing.T) {
	ctx := context.Background()
	type args struct {
		availLanes    []*rule.RoutingLaneInfo
		spxAvailLanes []*rule.RoutingLaneInfo
	}
	tests := []struct {
		name string
		args args
		want []*rule.RoutingLaneInfo
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				availLanes: []*rule.RoutingLaneInfo{
					nil,
					{
						LaneCode: "lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:              "line-1",
								RealResourceSubType: lfslib.C_DLM,
								ResourceSubType:     lfslib.C_DLM,
							},
						},
					},
					{
						LaneCode: "lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId:              "line-2",
								RealResourceSubType: lfslib.C_DLM,
								ResourceSubType:     lfslib.C_DLM,
							},
						},
					},
				},
				spxAvailLanes: []*rule.RoutingLaneInfo{
					nil,
					{
						LaneCode: "lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:              "line-2",
								RealResourceSubType: lfslib.C_DLM,
								ResourceSubType:     lfslib.C_DLM,
							},
						},
					},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "lane-2",
					LineList: []*rule.LineInfo{
						{
							LineId:              "line-2",
							RealResourceSubType: lfslib.C_DLM,
							ResourceSubType:     lfslib.C_DLM,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterBySpxAvailableLane(ctx, tt.args.availLanes, tt.args.spxAvailLanes); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FilterBySpxAvailableLane() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRoutingServiceImpl_ProhibitedCheck(t *testing.T) {
	ctx := context.Background()
	rs := &RoutingServiceImpl{}
	type args struct {
		matchedRule    *rule.RoutingRuleParsed
		availableLanes []*rule.RoutingLaneInfo
		isMultiProduct bool
		orderData      *rule.SmartRoutingOrderData
		logEntry       *routing_log.RoutingLog
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.RoutingLaneInfo
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(availableLanes) == 0",
			args: args{
				matchedRule:    &rule.RoutingRuleParsed{},
				availableLanes: []*rule.RoutingLaneInfo{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.NoAvailableLane, nil, "no available lane before routing"),
		},
		{
			name: "case 2: isMultiProduct && matchedRule.RoutingType == rule.CBRoutingType",
			args: args{
				matchedRule: &rule.RoutingRuleParsed{
					RoutingType: rule.CBRoutingType,
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceSubType: rule.LineTypeNoNeedRouting,
							},
							{
								LineId:          "Line-2",
								ResourceSubType: lfslib.C_ILH,
							},
						},
					},
				},
				isMultiProduct: true,
			},
			want:    []*rule.RoutingLaneInfo{},
			wantErr: srerr.New(srerr.NoAvailableLane, "", lineToggleOffErrMsg, 0),
		},
		{
			name: "case 3: filterByProhibitedLine",
			args: args{
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceSubType: rule.LineTypeNoNeedRouting,
							},
							{
								LineId:          "Line-2",
								ResourceSubType: lfslib.C_M_ILH,
							},
							{
								LineId:          "Line-3",
								ResourceId:      "Resource-3",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-4",
								ResourceId:      "Resource-4",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
					{
						LaneCode: "Lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-5",
								ResourceId:      "Resource-5",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				matchedRule: &rule.RoutingRuleParsed{
					AvailableLines:    []string{"Resource-4", "Resource-5"},
					WmsToggleEnable:   true,
					WmsAvailableLines: []string{"Resource-5"},
				},
				orderData: &rule.SmartRoutingOrderData{
					IsWms: true,
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-3",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-5",
							ResourceId:      "Resource-5",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := rs.ProhibitedCheck(ctx, tt.args.matchedRule, tt.args.availableLanes, tt.args.isMultiProduct, tt.args.orderData, tt.args.logEntry)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func TestRoutingServiceImpl_routing(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))

	ctx := context.Background()
	rs := &RoutingServiceImpl{}
	var patchIsOpenPreVnCbToggleCheck, patchRoutingPreCalcFee *gomonkey.Patches
	type args struct {
		productID      int
		isMultiProduct bool
		availableLanes []*rule.RoutingLaneInfo
		matchedRule    *rule.RoutingRuleParsed
		matchedSPXRule *rule.RoutingRuleParsed
		orderData      *rule.SmartRoutingOrderData
		logEntry       *routing_log.RoutingLog
		isOnlySPX      bool
		extraInfo      *ExtraInfo
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.RoutingLaneInfo
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: ProhibitedCheck error",
			args: args{
				extraInfo:   &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.NoAvailableLane, nil, "no available lane before routing"),
			setup:   func() {},
		},
		{
			name: "case 2: vncb ProhibitedCheck error",
			args: args{
				extraInfo: &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{
					AvailableLines: []string{"Resource-1"},
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:              "Line-1",
								ResourceId:          "Resource-1",
								ResourceSubType:     lfslib.C_DLM,
								RealResourceSubType: lfslib.C_DLM,
							},
						},
					},
				},
				matchedSPXRule: &rule.RoutingRuleParsed{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.NoAvailableLane, "", lineToggleOffErrMsg, 0),
			setup: func() {
				patchIsOpenPreVnCbToggleCheck = gomonkey.ApplyFunc(configutil.IsOpenPreVnCbToggleCheck, func(ctx context.Context) bool {
					return true
				})
			},
		},
		{
			name: "case 3: vncb ProhibitedCheck pass",
			args: args{
				extraInfo:   &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-1",
							},
						},
					},
				},
				matchedSPXRule: &rule.RoutingRuleParsed{},
				logEntry:       &routing_log.RoutingLog{},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-1",
					LineList: []*rule.LineInfo{
						{
							LineId: "Line-1",
						},
					},
				},
			},
			wantErr: nil,
			setup: func() {
				rs = &RoutingServiceImpl{
					RoutingPreCalFeeService: &PreCalFeeServiceImpl{},
				}
				patchIsOpenPreVnCbToggleCheck = gomonkey.ApplyFunc(configutil.IsOpenPreVnCbToggleCheck, func(ctx context.Context) bool {
					return true
				})
				patchRoutingPreCalcFee = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return nil
				})
			},
		},
		{
			name: "case 4: No available lane after filter by soft criteria",
			args: args{
				extraInfo: &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{
					RuleStepResourceList: []*rule.RuleStepResource{
						{},
						{
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-1",
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-2",
							},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.NoAvailableLane, nil, "No available lane after filter by soft criteria"),
			setup: func() {
				rs = &RoutingServiceImpl{
					RoutingPreCalFeeService: &PreCalFeeServiceImpl{},
				}
				patchRoutingPreCalcFee = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return nil
				})
			},
		},
		{
			name: "case 5: filter by soft criteria len(validLanes) == 1",
			args: args{
				extraInfo: &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{
					RuleStepResourceList: []*rule.RuleStepResource{
						{
							ResourceSubType: lfslib.C_FL,
							RuleSteps: []*rule.ScheduleFactorAttr{
								{
									Name:          "MinVolume",
									MinVolumeData: &rule.MinVolumeData{},
								},
							},
						},
					},
					AvailableLines: []string{"Line-1"},
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-1",
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-2",
							},
						},
					},
					{
						LaneCode: "Lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceId:      "Line-1",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-3",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-1",
							ResourceId:      "Line-1",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
			},
			wantErr: nil,
			setup: func() {
				schedule_factor.NewMinVolumeFactor(volume_counter.NewForecastVolumeCounterImpl())
				rs = &RoutingServiceImpl{
					RoutingPreCalFeeService: &PreCalFeeServiceImpl{},
				}
				patchRoutingPreCalcFee = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return nil
				})
			},
		},
		{
			name: "case 6: filter by soft criteria len(validLanes) > 1",
			args: args{
				extraInfo: &ExtraInfo{},
				matchedRule: &rule.RoutingRuleParsed{
					RuleStepResourceList: []*rule.RuleStepResource{
						{
							ResourceSubType: lfslib.C_FL,
							RuleSteps: []*rule.ScheduleFactorAttr{
								{
									Name:          "MinVolume",
									MinVolumeData: &rule.MinVolumeData{},
								},
							},
						},
					},
					AvailableLines:      []string{"Line-1"},
					DefaultCriteriaName: schedule_factor.DefaultPriority,
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-1",
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId: "Line-2",
							},
						},
					},
					{
						LaneCode: "Lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceId:      "Line-1",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
					{
						LaneCode: "Lane-4",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceId:      "Line-1",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				logEntry: &routing_log.RoutingLog{},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-3",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-1",
							ResourceId:      "Line-1",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
				{
					LaneCode: "Lane-4",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-1",
							ResourceId:      "Line-1",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
			},
			wantErr: nil,
			setup: func() {
				schedule_factor.NewMinVolumeFactor(volume_counter.NewForecastVolumeCounterImpl())
				schedule_factor.NewDefaultPriorityFactor()
				rs = &RoutingServiceImpl{
					RoutingPreCalFeeService: &PreCalFeeServiceImpl{},
				}
				patchRoutingPreCalcFee = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return nil
				})
			},
		},
		//{
		//	name: "case 7: filter by default criteria retLane == nil",
		//	args: args{
		//		extraInfo: &ExtraInfo{},
		//		matchedRule: &rule.RoutingRuleParsed{
		//			RuleStepResourceList: []*rule.RuleStepResource{
		//				{
		//					ResourceSubType: lfslib.C_FL,
		//					RuleSteps: []*rule.ScheduleFactorAttr{
		//						{
		//							Name:          "MinVolume",
		//							MinVolumeData: &rule.MinVolumeData{},
		//						},
		//					},
		//				},
		//			},
		//			AvailableLines:      []string{"Line-1"},
		//			DefaultCriteriaName: schedule_factor.DefaultPriority,
		//		},
		//		availableLanes: []*rule.RoutingLaneInfo{
		//			{
		//				LaneCode: "Lane-1",
		//				LineList: []*rule.LineInfo{
		//					{
		//						LineId: "Line-1",
		//					},
		//				},
		//			},
		//			{
		//				LaneCode: "Lane-2",
		//				LineList: []*rule.LineInfo{
		//					{
		//						LineId: "Line-2",
		//					},
		//				},
		//			},
		//			{
		//				LaneCode: "Lane-3",
		//				LineList: []*rule.LineInfo{
		//					{
		//						LineId:          "Line-1",
		//						ResourceId:      "Line-1",
		//						ResourceSubType: lfslib.C_FL,
		//					},
		//				},
		//			},
		//			{
		//				LaneCode: "Lane-4",
		//				LineList: []*rule.LineInfo{
		//					{
		//						LineId:          "Line-1",
		//						ResourceId:      "Line-1",
		//						ResourceSubType: lfslib.C_FL,
		//					},
		//				},
		//			},
		//		},
		//		logEntry: &routing_log.RoutingLog{},
		//	},
		//	want: []*rule.RoutingLaneInfo{
		//		{
		//			LaneCode: "Lane-3",
		//			LineList: []*rule.LineInfo{
		//				{
		//					LineId:          "Line-1",
		//					ResourceId:      "Line-1",
		//					ResourceSubType: lfslib.C_FL,
		//				},
		//			},
		//		},
		//		{
		//			LaneCode: "Lane-4",
		//			LineList: []*rule.LineInfo{
		//				{
		//					LineId:          "Line-1",
		//					ResourceId:      "Line-1",
		//					ResourceSubType: lfslib.C_FL,
		//				},
		//			},
		//		},
		//	},
		//	wantErr: nil,
		//	setup: func() {
		//		schedule_factor.NewMinVolumeFactor(volume_counter.NewForecastVolumeCounterImpl())
		//		schedule_factor.NewDefaultPriorityFactor()
		//		rs = &RoutingServiceImpl{
		//			RoutingPreCalFeeService: &PreCalFeeServiceImpl{},
		//		}
		//		patchRoutingPreCalcFee = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
		//			return nil
		//		})
		//		patchGetAvailableLaneBylines = gomonkey.ApplyMethod(reflect.TypeOf(rs.RoutingPreCalFeeService), "RoutingPreCalcFee", func(rp *PreCalFeeServiceImpl, ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
		//			return nil
		//		})
		//	},
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := rs.routing(ctx, tt.args.productID, tt.args.isMultiProduct, tt.args.availableLanes, tt.args.matchedRule, tt.args.matchedSPXRule, tt.args.orderData, tt.args.logEntry, tt.args.isOnlySPX, tt.args.extraInfo)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patchIsOpenPreVnCbToggleCheck != nil {
				patchIsOpenPreVnCbToggleCheck.Reset()
			}
			if patchRoutingPreCalcFee != nil {
				patchRoutingPreCalcFee.Reset()
			}
		})
	}
}

func Test_routingTask_filterLines(t *testing.T) {
	schedule_factor.NewMinVolumeFactor(volume_counter.NewForecastVolumeCounterImpl())

	ctx := context.Background()
	rt := &routingTask{}
	type args struct {
		lines               []*rule.LineInfo
		filterStep          []*rule.ScheduleFactorAttr
		currentResourceType string
		factorNameCountMap  map[string]int
	}
	tests := []struct {
		name              string
		args              args
		wantFilteredLines []*rule.LineInfo
		wantLogFactorList []routing_log.SchedulingFactorCombination
		setup             func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(filteredLines) == 1",
			args: args{
				lines: []*rule.LineInfo{
					{
						LineId: "Line-1",
					},
				},
				filterStep: []*rule.ScheduleFactorAttr{
					{
						Name:          "MinVolume",
						MinVolumeData: &rule.MinVolumeData{},
					},
				},
				factorNameCountMap: map[string]int{},
			},
			wantFilteredLines: []*rule.LineInfo{
				{
					LineId: "Line-1",
				},
			},
			wantLogFactorList: []routing_log.SchedulingFactorCombination{
				{
					StepType:          rule.StepMinVolume,
					FactorName:        "MinVolume",
					ProcessData:       map[string]string{},
					Before:            []string{""},
					After:             []string{""},
					ActualLeftLineIds: []string{""},
				},
			},
			setup: func() {
				rt = &routingTask{
					routingData: &rule.RoutingData{
						Rule: &rule.RoutingRuleParsed{},
					},
				}
			},
		},
		{
			name: "case 2: len(filteredLines) == 0",
			args: args{
				lines: []*rule.LineInfo{},
				filterStep: []*rule.ScheduleFactorAttr{
					{
						Name:          "MinVolume",
						MinVolumeData: &rule.MinVolumeData{},
					},
				},
				factorNameCountMap: map[string]int{},
			},
			wantFilteredLines: []*rule.LineInfo{},
			wantLogFactorList: []routing_log.SchedulingFactorCombination{
				{
					StepType:    rule.StepMinVolume,
					FactorName:  "MinVolume",
					ProcessData: map[string]string{},
				},
			},
			setup: func() {
				rt = &routingTask{
					routingData: &rule.RoutingData{
						Rule: &rule.RoutingRuleParsed{},
					},
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			gotFilteredLines, gotLogFactorList := rt.filterLines(ctx, tt.args.lines, tt.args.filterStep, tt.args.currentResourceType, tt.args.factorNameCountMap)
			common.AssertResult(t, gotFilteredLines, tt.wantFilteredLines, nil, nil)
			common.AssertResult(t, gotLogFactorList, tt.wantLogFactorList, nil, nil)
		})
	}
}
