package schedule_factor

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"math"
	"strings"
)

// LineShippingFeeLocation Use to calculate line's shipping fee
type LineShippingFeeLocation struct {
	PickupLocationIds  []int
	PickupZipcode      string
	DeliverLocationIds []int
	DeliverZipCode     string
}

type LineCheapestShippingFeeFactor struct {
	RateApi chargeclient.ChargeApi
	LaneSrv lane.LaneService
}

func NewLineCheapestShippingFeeFactor(rateApi chargeclient.ChargeApi, laneSrv lane.LaneService) *LineCheapestShippingFeeFactor {
	entry := &LineCheapestShippingFeeFactor{
		RateApi: rateApi,
		LaneSrv: laneSrv,
	}
	Register(entry.Name(), entry)

	return entry
}

func (factor *LineCheapestShippingFeeFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.EnterSelectLaneShippingFeeFactor, "")
	var ret []*rule.LineInfo

	lineFeeMap := factor.GetLineShippingFee(ctx, lines, rd)
	// 上报运费结果指标
	prometheusutil.RoutingShippingFeeReport(lineFeeMap)
	logger.CtxLogInfof(ctx, "linefeemap %+v", lineFeeMap)
	var minFee = math.MaxFloat64
	for _, fee := range lineFeeMap {
		if fee == chargeentity.UnknownShippingFee {
			continue
		}
		if fee < minFee {
			minFee = fee
		}
	}

	if minFee == math.MaxFloat64 {
		logger.CtxLogErrorf(ctx, "FilterByLineCheapestShippingFee abnormal|lineFeeMap=%v", lineFeeMap)
		return lines, lineFeeMap
	}

	// match
	for _, line := range lines {
		fee, exist := lineFeeMap[line.ResourceId]
		if !exist || fee == chargeentity.UnknownShippingFee {
			continue
		}

		if fee == minFee {
			ret = append(ret, line)
		}
	}

	return ret, lineFeeMap
}

func (factor LineCheapestShippingFeeFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", factor.Name())

	return nil, nil
}

func (factor *LineCheapestShippingFeeFactor) Name() string {
	return LineCheapestShippingFee
}

func (factor *LineCheapestShippingFeeFactor) GetLineShippingFee(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
	// 预测任务重新计算运费开关，要么重新计算，要么直接返回
	if rd.BusinessType == constant.ForecastRouting {
		// true表示重新计算运费
		if rd.ReCalcFee {
			monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.ReCalcFee, "reCalcFee is true, re calc fee")
			return factor.CalcLineShippingFee(ctx, lines, rd)
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.UseRoutingLogFee, fmt.Sprintf("reCalcFee is false, use routing log fee, lines = %v, oldFee = %v", objutil.JsonString(lines), objutil.JsonString(rd.OldLineFeeMap)))
		return GetOldLineFee(ctx, lines, rd.OldLineFeeMap)
	} else {
		// 是否已提前计算运费
		preResult := GetPreCalcFee(ctx, lines, rd.PreLineFeeMap)
		logger.CtxLogDebugf(ctx, "GetLineShippingFee|need calc fee lines: %+v, get pre calc fee result: %+v", lines, preResult)
		// 可能只有部分line打开了提前计算运费开关，剩下的还是需要请求finance
		var needCalcLines []*rule.LineInfo
		for _, line := range lines {
			if _, ok := preResult[line.ResourceId]; !ok {
				needCalcLines = append(needCalcLines, line)
			}
		}
		// 没有需要补偿运费的line则直接返回
		if len(needCalcLines) == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.NeedCompensateFeeLineIsNil, fmt.Sprintf("need compensate fee line is nil, no need compensate fee. final result: %v", preResult))
			return preResult
		}
		// 对需要补偿运费的line调finance计算运费
		reCalcResult := factor.CalcLineShippingFee(ctx, needCalcLines, rd)
		logger.CtxLogDebugf(ctx, "GetLineShippingFee|need re calc fee lines: %+v, get re calc fee result: %+v", needCalcLines, reCalcResult)
		// 合并提前计算运费和补偿运费的结果
		for key, value := range reCalcResult {
			preResult[key] = value
		}
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.CompensateFeeSuccess, fmt.Sprintf("compensate fee success, compensate fee is: %v, final result: %v", reCalcResult, preResult))
		return preResult
	}
}

func (factor *LineCheapestShippingFeeFactor) CalcLineShippingFee(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
	result := make(map[string]float64)
	lineAddrMap, err := factor.getLineAddrInfo(ctx, lines, rd)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.SelectLaneShippingFeeLineAddrError, fmt.Sprintf("getLineAddrInfo fail|err=%v", err))
		logger.CtxLogErrorf(ctx, "getLineAddrInfo fail|err=%v", err)
		return result
	}
	// 可计算运费的line为空，默认返回
	if len(lineAddrMap) == 0 {
		monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.SelectLaneLineAddrMapIsNil, "lineAddrMap is nil, no need calc fee")
		logger.CtxLogInfof(ctx, "CalcLineShippingFee|lineAddrMap is nil, no need calc fee")
		return result
	}
	reqList := factor.getLineCheapestShippingFeeReqList(ctx, rd, lines, lineAddrMap)
	rateRsp, err := factor.RateApi.BatchCalcLineForecastShippingFeeWithGrpc(ctx, reqList)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatScheduleProcess, monitoring.SelectLaneShippingFeeReqError, fmt.Sprintf("BatchCalcLineForecastShippingFee fail|err=%v", err))
		logger.CtxLogErrorf(ctx, "BatchCalcLineForecastShippingFee fail|err=%v", err)
		return result
	}
	for _, lineFeeRsp := range rateRsp.Data {
		result[lineFeeRsp.LineId] = lineFeeRsp.GetShippingFeeAmount(ctx)
	}

	return result
}

func GetOldLineFee(ctx context.Context, lines []*rule.LineInfo, oldLineFeeMap map[string]float64) map[string]float64 {
	// 获取lines的提前计算运费结果，只需要返回lines的运费，防止被其他调度因子过滤的line是最小运费
	preFeeResult := make(map[string]float64)
	if oldLineFeeMap == nil {
		logger.CtxLogErrorf(ctx, "GetOldLineFee|smart routing forecast can't get old line fee")
		return preFeeResult
	}
	for _, line := range lines {
		if _, ok := oldLineFeeMap[line.ResourceId]; ok {
			preFeeResult[line.ResourceId] = oldLineFeeMap[line.ResourceId]
		}
	}
	return preFeeResult
}

func GetPreCalcFee(ctx context.Context, lines []*rule.LineInfo, preLineFeeMap map[string]float64) map[string]float64 {
	// 获取lines的提前计算运费结果，只需要返回lines的运费，防止被其他调度因子过滤的line是最小运费
	preFeeResult := make(map[string]float64)
	// 如果preLineFeeMap为nil或结果为空则直接返回
	if len(preLineFeeMap) == 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.GetPreCalcFeeIsNil, "pre line fee map is nil")
		return preFeeResult
	}
	// 从提前计算运费结果中取出需要的运费结果
	for _, line := range lines {
		if _, ok := preLineFeeMap[line.ResourceId]; ok {
			preFeeResult[line.ResourceId] = preLineFeeMap[line.ResourceId]
		}
	}
	monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.GetPreCalcFeeSuccess, fmt.Sprintf("get pre calc fee success, preFeeResult: %v", preFeeResult))
	return preFeeResult
}

func isLineShippingFeeLocationEqual(a, b LineShippingFeeLocation) bool {
	if len(a.PickupLocationIds) != len(b.PickupLocationIds) {
		return false
	}
	for index, loc := range a.PickupLocationIds {
		if loc != b.PickupLocationIds[index] {
			return false
		}
	}

	if len(a.DeliverLocationIds) != len(b.DeliverLocationIds) {
		return false
	}
	for index, loc := range a.DeliverLocationIds {
		if loc != b.DeliverLocationIds[index] {
			return false
		}
	}

	if a.PickupZipcode != b.PickupZipcode {
		return false
	}

	if a.DeliverZipCode != b.DeliverZipCode {
		return false
	}

	return true
}

func (factor *LineCheapestShippingFeeFactor) getLineAddrInfo(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) (map[string]LineShippingFeeLocation, *srerr.Error) {
	result := make(map[string]LineShippingFeeLocation)
	for _, line := range lines {
		for _, routingLane := range rd.ValidateLaneList {
			var feeLoc LineShippingFeeLocation
			laneCode := routingLane.LaneCode
			sitePointTypeMap, err := factor.checkActualPointList(ctx, routingLane.LaneCode, rd)
			if err != nil {
				logger.CtxLogErrorf(ctx, "CheckActualPointList fail|err=%v", err)
				return nil, err
			}
			//SPLPS-7388: 兼容双层链路
			if strings.Contains(laneCode, "/") {
				subLaneCodes := strings.Split(laneCode, "/")
				laneCode = subLaneCodes[len(subLaneCodes)-1] //只需要对spx段进行cheapest shipping fee计算
			}
			laneInfo, err := factor.LaneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", err)
				continue
			}

			// line not in this lane, continue
			lineInfo := laneInfo.GetLine(line.ResourceId)
			if lineInfo == nil {
				continue
			}

			// Get pickup address
			lastSite := lineInfo.LastSite
			if lastSite == nil {
				logger.CtxLogErrorf(ctx, "can not find out last site|lineId=%v", lineInfo.LineID)
				continue
			}
			// Seller virtual site，use true seller address
			if lastSite.MainType == lfslib.ShipSite && lastSite.SubType == lfslib.NormalShipSite {
				feeLoc.PickupLocationIds = rd.CreateOrderData.PickupLocationIds
				feeLoc.PickupZipcode = rd.CreateOrderData.PickupPostCode
			} else {
				point := sitePointTypeMap[lastSite.SiteID][lfslib.ActualPointTypeOut]
				if point == nil {
					continue
				}
				actualPointInfo := laneInfo.GetActualPoint(point.GetSiteId(), point.GetPointId())
				if actualPointInfo == nil {
					continue
				}
				feeLoc.PickupLocationIds = actualPointInfo.GetLocationIds()
				feeLoc.PickupZipcode = actualPointInfo.ZipCode
			}

			// Get deliver address
			nextSite := lineInfo.NextSite
			if nextSite == nil {
				logger.CtxLogErrorf(ctx, "can not find out next site|lineId=%v", lineInfo.LineID)
				continue
			}
			// Buyer virtual site，use true seller address
			if nextSite.MainType == lfslib.DeliverySite && nextSite.SubType == lfslib.NormalDeliverSite {
				feeLoc.DeliverLocationIds = rd.CreateOrderData.DeliveryLocationIds
				feeLoc.DeliverZipCode = rd.CreateOrderData.DeliveryPostCode
			} else {
				point := sitePointTypeMap[nextSite.SiteID][lfslib.ActualPointTypeIn]
				if point == nil {
					continue
				}
				actualPointInfo := laneInfo.GetActualPoint(point.GetSiteId(), point.GetPointId())
				if actualPointInfo == nil {
					continue
				}
				feeLoc.DeliverLocationIds = actualPointInfo.GetLocationIds()
				feeLoc.DeliverZipCode = actualPointInfo.ZipCode
			}

			if existFeeLoc, exist := result[line.ResourceId]; exist {
				if !isLineShippingFeeLocationEqual(existFeeLoc, feeLoc) {
					return nil, srerr.New(srerr.LineHasMultiPoint, line.ResourceId, "Line has multi pickup/deliver point, can not calc shipping fee|lineId=%s", line.ResourceId)
				}
			} else {
				result[line.ResourceId] = feeLoc
			}
		}
	}

	return result, nil
}

func (factor *LineCheapestShippingFeeFactor) checkActualPointList(ctx context.Context, laneCode string, rd *rule.RoutingData) (map[string]map[int32]*pb.ActualPoint, *srerr.Error) {
	//1.检查Actual Point List是否包含重复类型的点
	// map[SiteID]map[PointType]Site
	sitePointTypeMap := make(map[string]map[int32]*pb.ActualPoint)
	for _, actualPoint := range rd.CreateOrderData.ActualPointMap[laneCode] {
		if sitePointTypeMap[actualPoint.GetSiteId()] == nil {
			sitePointTypeMap[actualPoint.GetSiteId()] = make(map[int32]*pb.ActualPoint)
		}
		if _, exist := sitePointTypeMap[actualPoint.GetSiteId()][actualPoint.GetPointType()]; exist {
			logger.CtxLogInfof(ctx, "Site id:%s, Point type:%v already exist", actualPoint.GetSiteId(), actualPoint.GetPointType())
			return nil, srerr.New(srerr.LfsError, nil, "Site sorting check fail, duplicate point")
		}
		sitePointTypeMap[actualPoint.GetSiteId()][actualPoint.GetPointType()] = actualPoint
	}

	//2.当实际点列表的的元素不"有且仅有一个in和一个out类型"时，报错
	for _, pointMap := range sitePointTypeMap {
		if len(pointMap) != 2 {
			logger.CtxLogErrorf(ctx, "FilterByCheapestShippingFee|Length of actual point list is not 2, ActualPointList:%+v", rd.CreateOrderData.ActualPointMap[laneCode])
			return nil, srerr.New(srerr.LfsError, nil, "Length of actual point list is not 2 ")
		}
	}

	return sitePointTypeMap, nil
}

func (factor *LineCheapestShippingFeeFactor) getLineCheapestShippingFeeReqList(ctx context.Context, rd *rule.RoutingData, lines []*rule.LineInfo, lineAddrMap map[string]LineShippingFeeLocation) []*chargeentity.CalcLineForecastShippingFeeReqData {
	skuInfo := make([]*chargeentity.CalcLineForecastShippingFeeReqDataItem, 0, len(rd.CreateOrderData.Items))
	for _, item := range rd.CreateOrderData.Items {
		skuInfo = append(skuInfo, &chargeentity.CalcLineForecastShippingFeeReqDataItem{
			ItemID:     item.ItemID,
			ModelID:    item.ModelID,
			CategoryID: item.CategoryID,
			Weight:     item.Weight,
			Height:     item.Height,
			Width:      item.Width,
			Length:     item.Length,
			Quantity:   item.Quantity,
		})
	}

	var paymentMethod int
	if rd.CreateOrderData.IsCod {
		paymentMethod = chargeentity.PaymentTypeCod
	} else {
		paymentMethod = chargeentity.PaymentTypeNonCod
	}

	var forecastType int
	// 这段逻辑需要保留。后续重做4pl asf还是以forecastType做逻辑，目前finance对4pl运费是默认范围错误
	switch rd.Rule.RoutingType {
	case rule.LocalRoutingType:
		forecastType = chargeentity.ForecastTypeBRLocal
	case rule.SPXRoutingType:
		forecastType = chargeentity.ForecastTypeMYFiveLeg
	}

	// direction默认为正向单，当IsReturn为True时，表示为逆向单
	direction := chargeentity.DirectionForward
	if rd.CreateOrderData.IsReturn == constant.IsReturnOrder {
		direction = chargeentity.DirectionReverse
		monitoring.ReportSuccess(ctx, monitoring.CatModuleShippingFeeFactor, monitoring.ReverseOrder, "")
	}

	var reqList []*chargeentity.CalcLineForecastShippingFeeReqData
	for _, line := range lines {
		addrInfo, exist := lineAddrMap[line.ResourceId]
		if !exist {
			logger.CtxLogErrorf(ctx, "line addr info not found|lineId=%s", line.ResourceId)
			continue
		}
		reqList = append(reqList, &chargeentity.CalcLineForecastShippingFeeReqData{
			LineId:              line.ResourceId,
			CodAmount:           rd.CreateOrderData.CodAmount,
			Cogs:                rd.CreateOrderData.Cogs,
			CreateOrderTime:     rd.CreateOrderData.CreateOrderTime,
			Timestamp:           uint32(timeutil.GetCurrentUnixTimeStamp(ctx)),
			WmsFlag:             rd.CreateOrderData.WmsFlag,
			DgFlag:              rd.CreateOrderData.DgFlag,
			Direction:           direction,
			PickupLocationIds:   addrInfo.PickupLocationIds,
			DeliveryLocationIds: addrInfo.DeliverLocationIds,
			PickupPostcode:      addrInfo.PickupZipcode,
			DeliveryPostcode:    addrInfo.DeliverZipCode,
			ShipmentType:        rd.CreateOrderData.ShipmentType,
			SkuInfo:             skuInfo,
			ForecastType:        forecastType,
			PaymentType:         paymentMethod,
		})
	}

	return reqList
}
