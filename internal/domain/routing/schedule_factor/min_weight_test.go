package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"reflect"
	"testing"
)

func TestMinWeightFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	m := MinWeightFactor{}
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.LineInfo
		want1 interface{}
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(ret) != 0",
			args: args{
				lines: []*rule.LineInfo{
					{},
					{
						LineId:    "line-1",
						DgRelated: rule.DgRelated,
					},
					{
						LineId: "line-2",
					},
				},
				rd: &rule.RoutingData{
					Rule:            &rule.RoutingRuleParsed{},
					CreateOrderData: &rule.SmartRoutingOrderData{},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					MinWeightData: &rule.MinWeightData{
						MinWeights: map[string]int{
							helper.FormatLineLimitKey("line-1", 0, 1, 0): 100,
							helper.FormatLineLimitKey("line-2", 0, 1, 0): 100,
						},
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId:    "line-1",
					DgRelated: rule.DgRelated,
				},
				{
					LineId: "line-2",
				},
			},
			want1: map[string]int{
				"0:line-1:0":  0,
				"0:line-2:0":  0,
				"OrderWeight": 0,
			},
		},
		{
			name:  "case 2: len(ret) == 0",
			args:  args{},
			want:  nil,
			want1: map[string]int{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := m.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FilterLine() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("FilterLine() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
