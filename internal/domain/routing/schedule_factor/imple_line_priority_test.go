package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func TestLinePriorityFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	factor := &LinePriorityFactor{}
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.LineInfo
		want1 interface{}
	}{
		// TODO: Add test cases.
		{
			name: "case 1: minPriority == math.MaxInt32",
			args: args{
				lines: []*rule.LineInfo{
					{
						LineId: "line0",
					},
					{
						LineId: "line1",
					},
				},
				rd: &rule.RoutingData{
					Rule: &rule.RoutingRuleParsed{
						RoutingType: rule.LocalRoutingType,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					LinePriorityData: &rule.LinePriorityData{
						LinePriorities: map[string]int32{
							"line1": 0,
						},
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId: "line0",
				},
				{
					LineId: "line1",
				},
			},
			want1: nil,
		},
		{
			name: "case 2: normal result",
			args: args{
				lines: []*rule.LineInfo{
					{
						LineId: "line0",
					},
					{
						LineId: "line1",
					},
				},
				rd: &rule.RoutingData{
					Rule: &rule.RoutingRuleParsed{
						RoutingType: rule.LocalRoutingType,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					LinePriorityData: &rule.LinePriorityData{
						LinePriorities: map[string]int32{
							"line1": 1,
						},
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId: "line1",
				},
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := factor.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			common.AssertResult(t, got, tt.want, nil, nil)
			if got1 != tt.want1 {
				t.Errorf("FilterLine() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
