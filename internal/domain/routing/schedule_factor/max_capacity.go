package schedule_factor

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type MaxCapacityFactor struct {
	VolumeCounter               volume_counter.VolumeCounter
	ParcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService
}

func NewMaxCapacityFactor(volumeCounter volume_counter.VolumeCounter, parcelTypeDefinitionService parcel_type_definition.ParcelTypeDefinitionService) *MaxCapacityFactor {
	entry := &MaxCapacityFactor{
		VolumeCounter:               volumeCounter,
		ParcelTypeDefinitionService: parcelTypeDefinitionService,
	}
	Register(entry.Name(), entry)

	return entry
}

func (m *MaxCapacityFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	var ret []*rule.LineInfo
	volumesForLog := map[string]string{}
	productIdForVolume := rd.ProductID
	if rd.Rule.RoutingType == rule.SPXRoutingType && !rd.NotOnlySPX {
		productIdForVolume = 0
	}

	for _, line := range lines {
		limitKey := helper.FormatLineLimitKey(line.LineId, int(line.DGFlag), line.DgRelated, rd.Rule.RoutingType)
		threshold, ok := factorDetail.MaxCapacityData.MaxCapacities[limitKey]
		if !ok {
			continue
		}

		var v int
		var key string
		if rd.Rule.RoutingType == rule.IlhRoutingType {
			// 如果Line是DG相关的，就取对应DG属性的单量；
			// 否则就是不区分DG属性，取（DG+NonDG）的总单量，因为单量统计的时候粒度比较细，都统计到了DG/NonDG
			if line.DgRelated == rule.DgRelated {
				v = m.getILHVolume(ctx, rd.ProductID, line.LineId, int(line.DGFlag), rd)
			} else {
				v = m.getILHVolume(ctx, rd.ProductID, line.LineId, int(rule.NonDG), rd)
				v += m.getILHVolume(ctx, rd.ProductID, line.LineId, int(rule.DG), rd)
			}
			key = fmt.Sprintf("%v:%v:%v:%v", rd.ProductID, line.LineId, line.DGFlag, rd.CreateOrderData.TwsCode)
		} else {
			if !volumerouting.NeedFilterByParcelType(ctx, int64(rd.ProductID), line.LineId, rd.CreateOrderData.IsCod,
				rd.CreateOrderData.OrderParcelDimension, rd.CreateOrderData.Cogs, line.DGFlag,
				volumerouting.GetParcelType(factorDetail.Name), m.ParcelTypeDefinitionService, int(rd.Rule.RoutingType)) {
				ret = append(ret, line)
				continue
			}
			v = m.getLineVolume(ctx, productIdForVolume, line.LineId, rd.Rule.ZoneCode, volumesForLog, factorDetail.Name)
			key = fmt.Sprintf("%v:%v", productIdForVolume, line.LineId)
			//后面记录有每个zoneCode的运力
			//if len(rd.Rule.ZoneCode) > 0 {
			//	key = key + fmt.Sprintf(":%v", rd.Rule.ZoneCode)
			//}
		}
		//记录一下line的当前运力和最大运力
		volumesForLog[key+":max"] = strconv.Itoa(threshold)
		volumesForLog[key+":current"] = strconv.Itoa(v)
		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if v == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.RoutingLineVolumeZeroNum, "")
		}
		if v < threshold {
			ret = append(ret, line)
		}
	}
	logger.CtxLogInfof(ctx, "current volumes: %+v", volumesForLog)
	if len(ret) > 0 {
		return ret, volumesForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lines were filtered out", m.Name())

	return lines, volumesForLog
}

func (m MaxCapacityFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	var ret []*rule.RoutingLaneInfo
	currentVolumesForLog := map[string]int{}

	for _, laneInfo := range laneInfos {
		ilh, importIlh := GetIlhAndImportIlhFromLaneInfo(laneInfo)
		combination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        rd.CreateOrderData.LmId,
		}
		threshold, ok := factorDetail.MaxCapacityData.CombinationMaxCapacities[combination]
		if !ok {
			continue
		}

		v := m.getILHCombinationVolume(ctx, rd.ProductID, ilh, importIlh, rd.CreateOrderData.LmId, rd)
		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if v == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.ILHVolumeZeroNum, "")
		}
		if v <= threshold {
			ret = append(ret, laneInfo)
		}
	}

	logger.CtxLogInfof(ctx, "current volume: %+v", currentVolumesForLog)
	if len(ret) > 0 {
		return ret, currentVolumesForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lanes were filtered out", m.Name())

	return laneInfos, nil
}

func (m *MaxCapacityFactor) Name() string {
	return MaxCapacity
}

func (m *MaxCapacityFactor) getILHVolume(ctx context.Context, productId int, lineId string, dgType int, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHTwsCartonVolume(ctx, productId, lineId, dgType, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHTwsCartonVolume fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] volume: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHTwsDpCartonVolume(ctx, productId, lineId, dgType, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHTwsDpCartonVolume fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] volume: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}

func (m *MaxCapacityFactor) getLineVolume(ctx context.Context, productId int, lineId string, zoneCodes []string, volumesForLog map[string]string, factorDetailName string) int {
	parcelType := volumerouting.GetParcelType(factorDetailName)
	if len(zoneCodes) == 0 {
		val, err := m.VolumeCounter.GetLineVolume(ctx, productId, lineId, parcelType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetLineVolume fail|err=%v", err)
			return 0
		}
		return val
	}
	zoneVolume := ""
	var sum int
	for _, zoneCode := range zoneCodes {
		val, err := m.VolumeCounter.GetLineZoneVolume(ctx, productId, lineId, zoneCode, parcelType)
		zoneVolume = zoneVolume + zoneCode + ":" + strconv.Itoa(val) + ";"
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetLineZoneVolume fail|err=%v", err)
			return 0
		}
		sum += val
	}

	//记录每个zoneCode对应的运力（todo 这个应该是每个zone的当前运力）
	key := fmt.Sprintf("%v:%v:%v", productId, lineId, "zoneVolume:max")
	//就每一个zone的运力
	volumesForLog[key] = zoneVolume

	return sum
}

func (m MaxCapacityFactor) getILHCombinationVolume(ctx context.Context, productId int, ilhLineID, importIlhLineID, lmLineID string, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHCombinationCartonQuantity(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHCombinationDpCartonQuantity fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] weight: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHCombinationDpCartonQuantity(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHCombinationDpCartonQuantity fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] weight: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}
