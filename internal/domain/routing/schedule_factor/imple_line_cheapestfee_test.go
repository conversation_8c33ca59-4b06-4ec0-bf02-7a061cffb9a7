package schedule_factor

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/gogo/protobuf/proto"
	"reflect"
	"testing"
)

func TestLineCheapestShippingFeeFactor_GetLineShippingFee(t *testing.T) {
	ctx := context.Background()
	factor := &LineCheapestShippingFeeFactor{}
	var patch *gomonkey.Patches
	type args struct {
		lines []*rule.LineInfo
		rd    *rule.RoutingData
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]float64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: rd.BusinessType == constant.ForecastRouting, ReCalcFee == true",
			args: args{
				rd: &rule.RoutingData{
					BusinessType: constant.ForecastRouting,
					ReCalcFee:    true,
				},
			},
			want:  map[string]float64{},
			setup: func() {},
		},
		{
			name: "case 2: rd.BusinessType == constant.ForecastRouting, ReCalcFee == false",
			args: args{
				rd: &rule.RoutingData{
					BusinessType: constant.ForecastRouting,
					ReCalcFee:    false,
				},
			},
			want:  map[string]float64{},
			setup: func() {},
		},
		{
			name: "case 3: rd.BusinessType != constant.ForecastRouting, len(needCalcLines) == 0",
			args: args{
				rd: &rule.RoutingData{},
			},
			want:  map[string]float64{},
			setup: func() {},
		},
		{
			name: "case 4: rd.BusinessType != constant.ForecastRouting, len(needCalcLines) != 0",
			args: args{
				rd: &rule.RoutingData{},
				lines: []*rule.LineInfo{
					{},
				},
			},
			want: map[string]float64{"": 1},
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(factor), "CalcLineShippingFee", func(factor *LineCheapestShippingFeeFactor,
					ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return map[string]float64{"": 1}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got := factor.GetLineShippingFee(ctx, tt.args.lines, tt.args.rd)
			common.AssertResult(t, got, tt.want, nil, nil)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestLineCheapestShippingFeeFactor_getLineAddrInfo(t *testing.T) {
	ctx := context.Background()
	factor := &LineCheapestShippingFeeFactor{}
	var patchGetLaneInfoByLaneCode *gomonkey.Patches
	type args struct {
		lines []*rule.LineInfo
		rd    *rule.RoutingData
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]LineShippingFeeLocation
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: checkActualPointList failed",
			args: args{
				lines: []*rule.LineInfo{{}},
				rd: &rule.RoutingData{
					ValidateLaneList: []*rule.RoutingLaneInfo{
						{
							LaneCode: "lane-1",
						},
					},
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"lane-1": {
								{},
								{},
							},
						},
					},
				},
			},
			want:    nil,
			wantErr: srerr.New(srerr.LfsError, nil, "Site sorting check fail, duplicate point"),
			setup:   func() {},
		},
		{
			name: "case 2: isLineShippingFeeLocationEqual failed",
			args: args{
				lines: []*rule.LineInfo{{}},
				rd: &rule.RoutingData{
					ValidateLaneList: []*rule.RoutingLaneInfo{
						{
							LaneCode: "fm1/lm1-GetLaneInfoByLaneCode fail",
						},
						{
							LaneCode: "lm-line not in this lane",
						},
						{
							LaneCode: "lm-lastSite == nil",
						},
						{
							LaneCode: "lm-seller virtual site",
						},
						{
							LaneCode: "lm-Point == nil",
						},
						{
							LaneCode: "lm-actualPointInfo == nil",
						},
						{
							LaneCode: "lm-nextSite == nil",
						},
						{
							LaneCode: "lm-buyer virtual site",
						},
						{
							LaneCode: "lm-buyer point == nil",
						},
						{
							LaneCode: "lm-buyer actualPointInfo == nil",
						},
						{
							LaneCode: "lm-isLineShippingFeeLocationEqual failed",
						},
					},
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"lm-actualPointInfo == nil": {
								{
									SiteId:    proto.String("actualPointInfo == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									SiteId:    proto.String("actualPointInfo == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
								},
							},
							"lm-nextSite == nil": {
								{
									SiteId:    proto.String("nextSite == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
									PointId:   proto.String(""),
								},
								{
									SiteId:    proto.String("nextSite == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
								},
							},
							"lm-buyer actualPointInfo == nil": {
								{
									SiteId:    proto.String("buyer actualPointInfo == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									SiteId:    proto.String("buyer actualPointInfo == nil"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
								},
							},
							"lm-isLineShippingFeeLocationEqual failed": {
								{
									SiteId:    proto.String("isLineShippingFeeLocationEqual failed"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									SiteId:    proto.String("isLineShippingFeeLocationEqual failed"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
									PointId:   proto.String(""),
								},
							},
						},
					},
				},
			},
			want:    nil,
			wantErr: srerr.New(srerr.LineHasMultiPoint, "", "Line has multi pickup/deliver point, can not calc shipping fee|lineId=%s", ""),
			setup: func() {
				factor = &LineCheapestShippingFeeFactor{
					LaneSrv: &lane.LaneServiceImpl{},
				}
				patchGetLaneInfoByLaneCode = gomonkey.ApplyMethod(reflect.TypeOf(factor.LaneSrv), "GetLaneInfoByLaneCode", func(l *lane.LaneServiceImpl, ctx context.Context, laneCode string) (*lane_entity.LaneInfo, *srerr.Error) {
					if laneCode == "fm1/lm1-GetLaneInfoByLaneCode fail" {
						return nil, srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), errors.New("mock error"))
					}
					if laneCode == "lm-line not in this lane" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "resource-1",
								},
							},
						}, nil
					}
					if laneCode == "lm-lastSite == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
								},
							},
						}, nil
					}
					if laneCode == "lm-seller virtual site" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
								},
							},
						}, nil
					}
					if laneCode == "lm-Point == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID:   "",
									LastSite: &lane_entity.SiteInfo{},
								},
							},
						}, nil
					}
					if laneCode == "lm-actualPointInfo == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										SiteID: "actualPointInfo == nil",
									},
								},
							},
						}, nil
					}
					if laneCode == "lm-nextSite == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										SiteID: "nextSite == nil",
									},
								},
							},
							Sites: map[string]*lane_entity.SiteInfo{
								"": {
									SiteID: "nextSite == nil",
									ActualPointMap: map[string]*lane_entity.ActualPointInfo{
										"": {},
									},
								},
							},
						}, nil
					}
					if laneCode == "lm-buyer virtual site" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{
										MainType: lfslib.DeliverySite,
										SubType:  lfslib.NormalDeliverSite,
									},
								},
							},
						}, nil
					}
					if laneCode == "lm-buyer point == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{},
								},
							},
						}, nil
					}
					if laneCode == "lm-buyer actualPointInfo == nil" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{
										SiteID: "buyer actualPointInfo == nil",
									},
								},
							},
						}, nil
					}
					if laneCode == "lm-isLineShippingFeeLocationEqual failed" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{
										SiteID: "isLineShippingFeeLocationEqual failed",
									},
								},
							},
							Sites: map[string]*lane_entity.SiteInfo{
								"": {
									SiteID: "isLineShippingFeeLocationEqual failed",
									ActualPointMap: map[string]*lane_entity.ActualPointInfo{
										"": {
											ZipCode: "123456",
										},
									},
								},
							},
						}, nil
					}
					return nil, srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), errors.New("mock error"))
				})
			},
		},
		{
			name:    "case 3: normal result",
			args:    args{},
			want:    nil,
			wantErr: nil,
			setup:   func() {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := factor.getLineAddrInfo(ctx, tt.args.lines, tt.args.rd)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patchGetLaneInfoByLaneCode != nil {
				patchGetLaneInfoByLaneCode.Reset()
			}
		})
	}
}

func Test_isLineShippingFeeLocationEqual(t *testing.T) {
	type args struct {
		a LineShippingFeeLocation
		b LineShippingFeeLocation
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(a.PickupLocationIds) != len(b.PickupLocationIds)",
			args: args{
				a: LineShippingFeeLocation{PickupLocationIds: []int{1}},
				b: LineShippingFeeLocation{},
			},
			want: false,
		},
		{
			name: "case 2: a.PickupLocationIds[index] != b.PickupLocationIds[index]",
			args: args{
				a: LineShippingFeeLocation{PickupLocationIds: []int{1}},
				b: LineShippingFeeLocation{PickupLocationIds: []int{2}},
			},
			want: false,
		},
		{
			name: "case 3: len(a.DeliverLocationIds) != len(b.DeliverLocationIds)",
			args: args{
				a: LineShippingFeeLocation{DeliverLocationIds: []int{1}},
				b: LineShippingFeeLocation{},
			},
			want: false,
		},
		{
			name: "case 4: a.DeliverLocationIds[index] != b.DeliverLocationIds[index]",
			args: args{
				a: LineShippingFeeLocation{DeliverLocationIds: []int{1}},
				b: LineShippingFeeLocation{DeliverLocationIds: []int{2}},
			},
			want: false,
		},
		{
			name: "case 5: a.PickupZipcode != b.PickupZipcode",
			args: args{
				a: LineShippingFeeLocation{PickupZipcode: "123456"},
				b: LineShippingFeeLocation{},
			},
			want: false,
		},
		{
			name: "case 6: a.DeliverZipCode != b.DeliverZipCode",
			args: args{
				a: LineShippingFeeLocation{DeliverZipCode: "123456"},
				b: LineShippingFeeLocation{},
			},
			want: false,
		},
		{
			name: "case 7: isLineShippingFeeLocationEqual pass",
			args: args{
				a: LineShippingFeeLocation{},
				b: LineShippingFeeLocation{},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isLineShippingFeeLocationEqual(tt.args.a, tt.args.b); got != tt.want {
				t.Errorf("isLineShippingFeeLocationEqual() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLineCheapestShippingFeeFactor_checkActualPointList(t *testing.T) {
	ctx := context.Background()
	factor := &LineCheapestShippingFeeFactor{}
	type args struct {
		laneCode string
		rd       *rule.RoutingData
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]map[int32]*pb.ActualPoint
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: Site sorting check fail, duplicate point",
			args: args{
				laneCode: "",
				rd: &rule.RoutingData{
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"": {
								{},
								{},
							},
						},
					},
				},
			},
			want:    nil,
			wantErr: srerr.New(srerr.LfsError, nil, "Site sorting check fail, duplicate point"),
		},
		{
			name: "case 2: Length of actual point list is not 2",
			args: args{
				laneCode: "",
				rd: &rule.RoutingData{
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"": {
								{},
								{
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
								},
							},
						},
					},
				},
			},
			want:    nil,
			wantErr: srerr.New(srerr.LfsError, nil, "Length of actual point list is not 2 "),
		},
		{
			name: "case 3: checkActualPointList pass",
			args: args{
				laneCode: "",
				rd: &rule.RoutingData{
					CreateOrderData: &rule.SmartRoutingOrderData{},
				},
			},
			want:    nil,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			got, gotErr := factor.checkActualPointList(ctx, tt.args.laneCode, tt.args.rd)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func TestLineCheapestShippingFeeFactor_CalcLineShippingFee(t *testing.T) {
	ctx := context.Background()
	factor := &LineCheapestShippingFeeFactor{}
	var patchGetLaneInfoByLaneCode, patchBatchCalcLineForecastShippingFeeWithGrpc *gomonkey.Patches
	type args struct {
		lines []*rule.LineInfo
		rd    *rule.RoutingData
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]float64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: getLineAddrInfo error",
			args: args{
				lines: []*rule.LineInfo{{}},
				rd: &rule.RoutingData{
					ValidateLaneList: []*rule.RoutingLaneInfo{
						{
							LaneCode: "lane-1",
						},
					},
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"lane-1": {
								{},
								{},
							},
						},
					},
				},
			},
			want:  nil,
			setup: func() {},
		},
		{
			name:  "case 2: lineAddrMap is nil, no need calc fee",
			args:  args{},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 3: BatchCalcLineForecastShippingFeeWithGrpc error",
			args: args{
				lines: []*rule.LineInfo{{}},
				rd: &rule.RoutingData{
					ValidateLaneList: []*rule.RoutingLaneInfo{
						{
							LaneCode: "lm-1",
						},
					},
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"lm-1": {
								{
									SiteId:    proto.String("1"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									SiteId:    proto.String("1"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
									PointId:   proto.String(""),
								},
							},
						},
					},
					Rule: &rule.RoutingRuleParsed{},
				},
			},
			want: nil,
			setup: func() {
				factor = &LineCheapestShippingFeeFactor{
					LaneSrv: &lane.LaneServiceImpl{},
					RateApi: &chargeclient.ChargeApiImpl{},
				}
				patchGetLaneInfoByLaneCode = gomonkey.ApplyMethod(reflect.TypeOf(factor.LaneSrv), "GetLaneInfoByLaneCode", func(l *lane.LaneServiceImpl, ctx context.Context, laneCode string) (*lane_entity.LaneInfo, *srerr.Error) {
					if laneCode == "lm-1" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{
										SiteID: "1",
									},
								},
							},
							Sites: map[string]*lane_entity.SiteInfo{
								"": {
									SiteID: "1",
									ActualPointMap: map[string]*lane_entity.ActualPointInfo{
										"": {},
									},
								},
							},
						}, nil
					}
					return nil, srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), errors.New("mock error"))
				})
				patchBatchCalcLineForecastShippingFeeWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(factor.RateApi), "BatchCalcLineForecastShippingFeeWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, dataList []*chargeentity.CalcLineForecastShippingFeeReqData) (*chargeentity.CalcLineForecastShippingFeeResp, *srerr.Error) {
					return nil, srerr.New(srerr.ChargeApiError, nil, "mock error")
				})
			},
		},
		{
			name: "case 4: BatchCalcLineForecastShippingFeeWithGrpc error",
			args: args{
				lines: []*rule.LineInfo{{}},
				rd: &rule.RoutingData{
					ValidateLaneList: []*rule.RoutingLaneInfo{
						{
							LaneCode: "lm-1",
						},
					},
					CreateOrderData: &rule.SmartRoutingOrderData{
						ActualPointMap: map[string][]*pb.ActualPoint{
							"lm-1": {
								{
									SiteId:    proto.String("1"),
									PointType: proto.Int32(lfslib.ActualPointTypeOut),
								},
								{
									SiteId:    proto.String("1"),
									PointType: proto.Int32(lfslib.ActualPointTypeIn),
									PointId:   proto.String(""),
								},
							},
						},
					},
					Rule: &rule.RoutingRuleParsed{},
				},
			},
			want: map[string]float64{"": 0},
			setup: func() {
				factor = &LineCheapestShippingFeeFactor{
					LaneSrv: &lane.LaneServiceImpl{},
					RateApi: &chargeclient.ChargeApiImpl{},
				}
				patchGetLaneInfoByLaneCode = gomonkey.ApplyMethod(reflect.TypeOf(factor.LaneSrv), "GetLaneInfoByLaneCode", func(l *lane.LaneServiceImpl, ctx context.Context, laneCode string) (*lane_entity.LaneInfo, *srerr.Error) {
					if laneCode == "lm-1" {
						return &lane_entity.LaneInfo{
							Lines: map[string]*lane_entity.LineInfo{
								"line-1": {
									LineID: "",
									LastSite: &lane_entity.SiteInfo{
										MainType: lfslib.ShipSite,
										SubType:  lfslib.NormalShipSite,
									},
									NextSite: &lane_entity.SiteInfo{
										SiteID: "1",
									},
								},
							},
							Sites: map[string]*lane_entity.SiteInfo{
								"": {
									SiteID: "1",
									ActualPointMap: map[string]*lane_entity.ActualPointInfo{
										"": {},
									},
								},
							},
						}, nil
					}
					return nil, srerr.With(srerr.LocalCacheErr, fmt.Sprintf("[%s] lane info not found", laneCode), errors.New("mock error"))
				})
				patchBatchCalcLineForecastShippingFeeWithGrpc = gomonkey.ApplyMethod(reflect.TypeOf(factor.RateApi), "BatchCalcLineForecastShippingFeeWithGrpc", func(r *chargeclient.ChargeApiImpl, ctx context.Context, dataList []*chargeentity.CalcLineForecastShippingFeeReqData) (*chargeentity.CalcLineForecastShippingFeeResp, *srerr.Error) {
					return &chargeentity.CalcLineForecastShippingFeeResp{
						Data: []*chargeentity.CalcLineForecastShippingFeeRespDataItem{
							{},
						},
					}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got := factor.CalcLineShippingFee(ctx, tt.args.lines, tt.args.rd)
			common.AssertResult(t, got, tt.want, nil, nil)
			if patchGetLaneInfoByLaneCode != nil {
				patchGetLaneInfoByLaneCode.Reset()
			}
			if patchBatchCalcLineForecastShippingFeeWithGrpc != nil {
				patchBatchCalcLineForecastShippingFeeWithGrpc.Reset()
			}
		})
	}
}

func TestLineCheapestShippingFeeFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	factor := &LineCheapestShippingFeeFactor{}
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	var patchGetLineShippingFee *gomonkey.Patches
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.LineInfo
		want1 interface{}
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: minFee == math.MaxFloat64",
			args: args{
				rd: &rule.RoutingData{},
			},
			want:  nil,
			want1: map[string]float64{},
			setup: func() {},
		},
		{
			name: "case 2: normal result",
			args: args{
				lines: []*rule.LineInfo{
					{},
					{
						LineId:     "line-1",
						ResourceId: "resource-2",
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId:     "line-1",
					ResourceId: "resource-2",
				},
			},
			want1: map[string]float64{
				"resource-1": -1,
				"resource-2": 100,
			},
			setup: func() {
				patchGetLineShippingFee = gomonkey.ApplyMethod(reflect.TypeOf(factor), "GetLineShippingFee", func(factor *LineCheapestShippingFeeFactor, ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return map[string]float64{
						"resource-1": chargeentity.UnknownShippingFee,
						"resource-2": 100,
					}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, got1 := factor.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			common.AssertResult(t, got, tt.want, nil, nil)
			common.AssertResult(t, got1, tt.want1, nil, nil)
			if patchGetLineShippingFee != nil {
				patchGetLineShippingFee.Reset()
			}
		})
	}
}
