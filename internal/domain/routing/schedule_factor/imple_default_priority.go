package schedule_factor

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"math"
)

type DefaultPriorityFactor struct {
}

func NewDefaultPriorityFactor() *DefaultPriorityFactor {
	entry := &DefaultPriorityFactor{}
	Register(entry.Name(), entry)

	return entry
}

func (factor *DefaultPriorityFactor) Name() string {
	return DefaultPriority
}

func (factor *DefaultPriorityFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	if len(lines) == 0 {
		return lines, nil
	}

	var (
		minPriority  = math.MaxInt32
		selectedLine = lines[0]
	)
	priorityLog := make(map[string]int)
	for _, line := range lines {
		lineKey := helper.FormatLineAndDgKey(line.ResourceId, int(line.DGFlag), line.DgRelated)
		linePriority, exist := factorDetail.DefaultCriteriaCfg[lineKey]
		if !exist {
			continue
		}
		priorityLog[lineKey] = linePriority
		if linePriority < minPriority {
			minPriority = linePriority
			selectedLine = line
		}
	}

	return []*rule.LineInfo{selectedLine}, priorityLog
}

func (factor DefaultPriorityFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", factor.Name())

	return nil, nil
}

func formatKey(lineID string, dgFlag int) string {
	return fmt.Sprintf("Line:%v-DGFlag:%v", lineID, dgFlag)
}

func GetFormatKey(lineID string, dgFlag int) string {
	return formatKey(lineID, dgFlag)
}
