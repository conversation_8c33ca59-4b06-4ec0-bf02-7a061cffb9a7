package schedule_factor

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

type DgFactor struct {
}

func NewDgFactor() *DgFactor {
	entry := &DgFactor{}
	Register(entry.Name(), entry)

	return entry
}

func (dg *DgFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	var ret []*rule.LineInfo

	for _, line := range lines {
		if line.DGFlag == factorDetail.DgFlagData.DgPriority {
			ret = append(ret, line)
		}
	}
	return ret, nil
}

func (dg DgFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", dg.Name())

	return nil, nil
}

func (dg *DgFactor) Name() string {
	return Dg
}
