package schedule_factor

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"math/rand"
	"time"
)

type DefaultWeightageFactor struct {
}

func NewDefaultWeightageFactor() *DefaultWeightageFactor {
	entry := &DefaultWeightageFactor{}
	Register(entry.Name(), entry)

	return entry
}

func (factor *DefaultWeightageFactor) Name() string {
	return DefaultWeightage
}

func (factor *DefaultWeightageFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	if len(lines) == 0 {
		return lines, nil
	}
	var total = 0
	distribution := map[int]*rule.LineInfo{}
	weightageForLog := make(map[string]int)
	for _, line := range lines {
		key := helper.FormatLineAndDgKey(line.ResourceId, int(line.DGFlag), line.DgRelated)
		percent := factorDetail.DefaultCriteriaCfg[key]
		weightageForLog[key] = percent
		if percent > 0 {
			distribution[total] = line
			total = total + percent
		}
	}

	logger.CtxLogInfof(ctx, "Lines weightage: %v", weightageForLog)

	if total <= 0 {
		return lines, nil
	}

	//开启流量录制的时候记录一下随机数，回放的时候保证不因为随机数产生结果无差
	random := recorder.Wrap(recordRand).(func(ctx context.Context, seed int) int)
	number := random(ctx, total)
	diff := INTMAX
	var priorityLine *rule.LineInfo
	for d, c := range distribution {
		if number-d == 0 {
			priorityLine = c
			break
		} else if number-d < 0 {
			continue
		} else {
			if number-d < diff {
				priorityLine = c
				diff = number - d
			}
		}
	}
	weightageForLog["number"] = number
	return []*rule.LineInfo{priorityLine}, weightageForLog
}

func (factor DefaultWeightageFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", factor.Name())

	return nil, nil
}

const INTMAX = int(^uint(0) >> 1)

func randInt(ctx context.Context, number int) int {
	s1 := rand.NewSource(time.Now().UnixNano()) // nolint
	r1 := rand.New(s1)                          // nolint
	return r1.Intn(number)
}

func recordRand(ctx context.Context, number int) int {
	return randInt(ctx, number)
}
