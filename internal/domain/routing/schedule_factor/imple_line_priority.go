package schedule_factor

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"math"
)

type LinePriorityFactor struct {
}

func NewLinePriorityFactor() *LinePriorityFactor {
	entry := &LinePriorityFactor{}
	Register(entry.Name(), entry)

	return entry
}

func (factor *LinePriorityFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	var ret []*rule.LineInfo
	var minPriority int32 = math.MaxInt32
	for _, line := range lines {
		limitKey := helper.FormatLineLimitKey(line.LineId, int(line.DGFlag), line.DgRelated, rd.Rule.RoutingType)
		p, exist := factorDetail.LinePriorityData.LinePriorities[limitKey]
		if !exist {
			logger.CtxLogErrorf(ctx, "FilterByLinePriority abnormal|linePriority not found, lineId=%v", line.LineId)
			continue
		}
		if p == 0 {
			logger.CtxLogErrorf(ctx, "FilterByLinePriority abnormal|linePriority abnormal, lineId=%v", line.LineId)
			continue
		}
		if p < minPriority {
			minPriority = p
		}
	}

	if minPriority == math.MaxInt32 {
		logger.CtxLogErrorf(ctx, "FilterByLinePriority abnormal|linePriorities=%v", factorDetail.LinePriorityData.LinePriorities)
		return lines, nil
	}

	logger.CtxLogInfof(ctx, "FilterByLinePriority first priority=%d", minPriority)
	for _, line := range lines {
		limitKey := helper.FormatLineLimitKey(line.LineId, int(line.DGFlag), line.DgRelated, rd.Rule.RoutingType)
		p, exist := factorDetail.LinePriorityData.LinePriorities[limitKey]
		if !exist || p == 0 {
			continue
		}
		if p == minPriority {
			ret = append(ret, line)
		}
	}

	return ret, nil
}

func (factor LinePriorityFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", factor.Name())

	return nil, nil
}

func (factor *LinePriorityFactor) Name() string {
	return LinePriority
}
