package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func TestDgFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	dg := &DgFactor{}
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.LineInfo
		want1 interface{}
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				lines: []*rule.LineInfo{
					{
						DGFlag: rule.DG,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					DgFlagData: &rule.DgFlagData{
						DgPriority: rule.DG,
					},
				},
			},
			want: []*rule.LineInfo{
				{
					DGFlag: rule.DG,
				},
			},
			want1: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := dg.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			common.AssertResult(t, got, tt.want, nil, nil)
			common.AssertResult(t, got1, tt.want1, nil, nil)
		})
	}
}
