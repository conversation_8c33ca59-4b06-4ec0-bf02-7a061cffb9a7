package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestMaxCapacityFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	m := &MaxCapacityFactor{}
	var patch *gomonkey.Patches
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.LineInfo
		want1 interface{}
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: rd.Rule.RoutingType == rule.SPXRoutingType",
			args: args{
				lines: []*rule.LineInfo{
					{},
					{
						LineId:    "line-1",
						DgRelated: rule.DgRelated,
					},
				},
				rd: &rule.RoutingData{
					Rule: &rule.RoutingRuleParsed{
						RoutingType: rule.SPXRoutingType,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					MaxCapacityData: &rule.MaxCapacityData{
						MaxCapacities: map[string]int{
							helper.FormatLineLimitKey("line-1", 0, 0, 0): 100,
						},
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId:    "line-1",
					DgRelated: rule.DgRelated,
				},
			},
			want1: map[string]string{
				"0:line-1:current": "0",
				"0:line-1:max":     "100",
			},
			setup: func() {
				volumeCounterImpl := &volume_counter.VolumeCounterImpl{}
				m = &MaxCapacityFactor{
					VolumeCounter: volumeCounterImpl,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(m.VolumeCounter), "GetLineVolume", func(v *volume_counter.VolumeCounterImpl, ctx context.Context, productID int, lineID string) (int, *srerr.Error) {
					return 0, nil
				})
			},
		},
		{
			name: "case 2: rd.Rule.RoutingType == rule.IlhRoutingType",
			args: args{
				lines: []*rule.LineInfo{
					{
						LineId:    "line-1",
						DgRelated: rule.DgRelated,
					},
					{
						LineId: "line-2",
					},
				},
				rd: &rule.RoutingData{
					Rule: &rule.RoutingRuleParsed{
						RoutingType: rule.IlhRoutingType,
					},
					CreateOrderData: &rule.SmartRoutingOrderData{},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					MaxCapacityData: &rule.MaxCapacityData{
						MaxCapacities: map[string]int{
							helper.FormatLineLimitKey("line-1", 0, 1, rule.IlhRoutingType): 0,
							helper.FormatLineLimitKey("line-2", 0, 1, rule.IlhRoutingType): 0,
						},
					},
				},
			},
			want: []*rule.LineInfo{
				{
					LineId:    "line-1",
					DgRelated: rule.DgRelated,
				},
				{
					LineId: "line-2",
				},
			},
			want1: map[string]string{
				"0:line-1:0::current": "0",
				"0:line-1:0::max":     "0",
				"0:line-2:0::current": "0",
				"0:line-2:0::max":     "0",
			},
			setup: func() {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, got1 := m.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			common.AssertResult(t, got, tt.want, nil, nil)
			common.AssertResult(t, got1, tt.want1, nil, nil)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
