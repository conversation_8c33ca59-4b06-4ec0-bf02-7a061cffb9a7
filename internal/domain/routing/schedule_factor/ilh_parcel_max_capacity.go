package schedule_factor

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type ILHParcelMaxCapacityFactor struct {
	VolumeCounter volume_counter.VolumeCounter
}

func NewILHParcelMaxCapacityFactor(volumeCounter volume_counter.VolumeCounter) *ILHParcelMaxCapacityFactor {
	entry := &ILHParcelMaxCapacityFactor{
		VolumeCounter: volumeCounter,
	}
	Register(entry.Name(), entry)

	return entry
}

func (m *ILHParcelMaxCapacityFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	var ret []*rule.LineInfo
	currentVolumeForLog := map[string]string{}

	for _, line := range lines {
		limitKey := helper.FormatLineLimitKey(line.LineId, int(line.DGFlag), line.DgRelated, rd.Rule.RoutingType)
		threshold, ok := factorDetail.IlhParcelMaxCapacityData.MaxCapacities[limitKey]
		if !ok {
			continue
		}
		var val int
		// 如果Line是DG相关的，就取对应DG属性的单量；
		// 否则就是不区分DG属性，取（DG+NonDG）的总单量，因为单量统计的时候粒度比较细，都统计到了DG/NonDG
		if line.DgRelated == rule.DgRelated {
			val = m.getILHParcelVolume(ctx, rd.ProductID, line.LineId, int(line.DGFlag), rd)
		} else {
			val = m.getILHParcelVolume(ctx, rd.ProductID, line.LineId, int(rule.NonDG), rd)
			val += m.getILHParcelVolume(ctx, rd.ProductID, line.LineId, int(rule.DG), rd)
		}

		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if val == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.ILHVolumeZeroNum, "")
		}

		currentVolumeForLog[fmt.Sprintf("%v:%v:%v", rd.ProductID, line.LineId, line.DGFlag)] = strconv.Itoa(val)

		currentVolumeForLog["OrderParcelNum"] = strconv.Itoa(rd.CreateOrderData.ParcelQuantity)
		if val+rd.CreateOrderData.ParcelQuantity <= threshold {
			ret = append(ret, line)
		}
	}

	logger.CtxLogInfof(ctx, "current parcel volume: %+v", currentVolumeForLog)
	if len(ret) != 0 {
		return ret, currentVolumeForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lines were filtered out", m.Name())

	return lines, nil
}

func (m ILHParcelMaxCapacityFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	var ret []*rule.RoutingLaneInfo
	currentVolumesForLog := map[string]int{}

	for _, laneInfo := range laneInfos {
		ilh, importIlh := GetIlhAndImportIlhFromLaneInfo(laneInfo)
		combination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        rd.CreateOrderData.LmId,
		}
		threshold, ok := factorDetail.IlhParcelMaxCapacityData.CombinationMaxCapacities[combination]
		if !ok {
			continue
		}

		v := m.getILHCombinationVolume(ctx, rd.ProductID, ilh, importIlh, rd.CreateOrderData.LmId, rd)
		// 上报运力为0的数量，长时间运力持续为0，运力更新可能会有异常，需要告警
		if v == 0 {
			monitoring.ReportSuccess(ctx, monitoring.CatScheduleProcess, monitoring.ILHVolumeZeroNum, "")
		}
		if v <= threshold {
			ret = append(ret, laneInfo)
		}
	}

	logger.CtxLogInfof(ctx, "current volume: %+v", currentVolumesForLog)
	if len(ret) > 0 {
		return ret, currentVolumesForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lanes were filtered out", m.Name())

	return laneInfos, nil
}

func (m *ILHParcelMaxCapacityFactor) getILHParcelVolume(ctx context.Context, productId int, lineId string, dgType int, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHTwsParcelVolume(ctx, productId, lineId, dgType, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHTwsCartonVolume fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] volume: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHTwsDpParcelVolume(ctx, productId, lineId, dgType, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHTwsDpCartonVolume fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] volume: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}

func (m ILHParcelMaxCapacityFactor) getILHCombinationVolume(ctx context.Context, productId int, ilhLineID, importIlhLineID, lmLineID string, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHCombinationParcelQuantity(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHCombinationParcelQuantity fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] weight: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHCombinationDpParcelQuantity(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHCombinationDpParcelQuantity fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] weight: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}

func (m *ILHParcelMaxCapacityFactor) Name() string {
	return ILHParcelMaxCapacity
}
