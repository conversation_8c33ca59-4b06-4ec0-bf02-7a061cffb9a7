package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func TestDefaultWeightageFactor_FilterLine(t *testing.T) {
	ctx := context.Background()
	factor := &DefaultWeightageFactor{}
	type args struct {
		lines        []*rule.LineInfo
		rd           *rule.RoutingData
		factorDetail *rule.ScheduleFactorAttr
	}
	tests := []struct {
		name                string
		args                args
		want                []*rule.LineInfo
		wantWeightageForLog interface{}
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(lines) == 0",
			args: args{
				lines: []*rule.LineInfo{},
			},
			want:                []*rule.LineInfo{},
			wantWeightageForLog: nil,
		},
		{
			name: "case 2: total <= 0",
			args: args{
				lines: []*rule.LineInfo{
					{
						ResourceId: "resource-1",
						DGFlag:     0,
					},
					{
						ResourceId: "resource-2",
						DGFlag:     0,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{},
			},
			want: []*rule.LineInfo{
				{
					ResourceId: "resource-1",
					DGFlag:     0,
				},
				{
					ResourceId: "resource-2",
					DGFlag:     0,
				},
			},
			wantWeightageForLog: nil,
		},
		{
			name: "case 3: normal result",
			args: args{
				lines: []*rule.LineInfo{
					{
						ResourceId: "resource-1",
						DGFlag:     0,
					},
					{
						ResourceId: "resource-2",
						DGFlag:     0,
					},
				},
				factorDetail: &rule.ScheduleFactorAttr{
					DefaultCriteriaCfg: map[string]int{
						helper.FormatLineAndDgKey("resource-1", 0, rule.DgRelated): 1,
					},
				},
			},
			want: []*rule.LineInfo{
				{
					ResourceId: "resource-1",
					DGFlag:     0,
				},
			},
			wantWeightageForLog: map[string]int{
				helper.FormatLineAndDgKey("resource-1", 0, rule.DgRelated): 1,
				helper.FormatLineAndDgKey("resource-2", 0, rule.DgRelated): 0,
				"number": 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotWeightageForLog := factor.FilterLine(ctx, tt.args.lines, tt.args.rd, tt.args.factorDetail)
			common.AssertResult(t, got, tt.want, nil, nil)
			common.AssertResult(t, gotWeightageForLog, tt.wantWeightageForLog, nil, nil)
		})
	}
}
