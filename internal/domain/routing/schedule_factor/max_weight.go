package schedule_factor

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type MaxWeightFactor struct {
	VolumeCounter volume_counter.VolumeCounter
}

func NewMaxWeightFactor(volumeCounter volume_counter.VolumeCounter) *MaxWeightFactor {
	entry := &MaxWeightFactor{VolumeCounter: volumeCounter}
	Register(entry.Name(), entry)

	return entry
}

func (m MaxWeightFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	var ret []*rule.LineInfo
	currentWeightsForLog := map[string]string{}

	for _, line := range lines {
		limitKey := helper.FormatLineLimitKey(line.LineId, int(line.DGFlag), line.DgRelated, rd.Rule.RoutingType)
		// rule's weight is KG, need to transfer to G before compare
		threshold, ok := factorDetail.MaxWeightData.MaxWeights[limitKey]
		if !ok {
			continue
		}
		// rule's weight is KG, need to transfer to G before compare
		threshold = threshold * 1000

		var val int
		// 如果Line是DG相关的，就取对应DG属性的单量；
		// 否则就是不区分DG属性，取（DG+NonDG）的总单量，因为单量统计的时候粒度比较细，都统计到了DG/NonDG
		if line.DgRelated == rule.DgRelated {
			val = m.getILHWeight(ctx, rd.ProductID, line.LineId, int(line.DGFlag), rd)
		} else {
			val = m.getILHWeight(ctx, rd.ProductID, line.LineId, int(rule.NonDG), rd)
			val += m.getILHWeight(ctx, rd.ProductID, line.LineId, int(rule.DG), rd)
		}
		//记录当前值和最大运力
		keyMax := fmt.Sprintf("%v:%v:%v", rd.ProductID, line.LineId, "max")
		keyCurrent := fmt.Sprintf("%v:%v:%v", rd.ProductID, line.LineId, "current")
		currentWeightsForLog[keyMax] = strconv.Itoa(threshold)
		currentWeightsForLog[keyCurrent] = strconv.Itoa(val)

		currentWeightsForLog["OrderWeight"] = strconv.Itoa(rd.CreateOrderData.OrderWeight)
		if val+rd.CreateOrderData.OrderWeight <= threshold {
			ret = append(ret, line)
		}
	}

	logger.CtxLogInfof(ctx, "current weights: %+v", currentWeightsForLog)
	if len(ret) > 0 {
		return ret, currentWeightsForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lines were filtered out", m.Name())

	return lines, currentWeightsForLog
}

func (m MaxWeightFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	var ret []*rule.RoutingLaneInfo
	currentWeightsForLog := map[string]int{}

	for _, laneInfo := range laneInfos {
		ilh, importIlh := GetIlhAndImportIlhFromLaneInfo(laneInfo)
		combination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        rd.CreateOrderData.LmId,
		}
		threshold, ok := factorDetail.MaxWeightData.CombinationMaxWeights[combination]
		if !ok {
			continue
		}
		// rule's weight is KG, need to transfer to G before compare
		threshold = threshold * 1000

		v := m.getILHCombinationWeight(ctx, rd.ProductID, ilh, importIlh, rd.CreateOrderData.LmId, rd)
		if v+rd.CreateOrderData.OrderWeight <= threshold {
			ret = append(ret, laneInfo)
		}
	}

	logger.CtxLogInfof(ctx, "current weights: %+v", currentWeightsForLog)
	if len(ret) > 0 {
		return ret, currentWeightsForLog
	}

	logger.CtxLogInfof(ctx, "%s|All lanes were filtered out", m.Name())

	return laneInfos, nil
}

func (m MaxWeightFactor) getILHWeight(ctx context.Context, productId int, lineId string, dgType int, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHTwsWeight(ctx, productId, lineId, dgType, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHTwsWeight fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] weight: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHTwsDpWeight(ctx, productId, lineId, dgType, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHTwsDpWeight fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] weight: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}

func (m MaxWeightFactor) getILHCombinationWeight(ctx context.Context, productId int, ilhLineID, importIlhLineID, lmLineID string, routingData *rule.RoutingData) int {
	var vol int
	for _, ruleTwsCode := range routingData.Rule.WhsId {
		// if rule's destination port is nil, use tws all volume
		if len(routingData.Rule.DestinationPort) == 0 {
			curTwsVol, err := m.VolumeCounter.GetILHCombinationWeight(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, timeutil.GetCurrentUnixTimeStamp(ctx))
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetILHCombinationWeight fail|err=%v", err)
				continue
			}
			logger.CtxLogInfof(ctx, "rule's destination port is nil, use tws [%s] weight: %d", ruleTwsCode, curTwsVol)
			vol += curTwsVol
		} else {
			// if not nil, use tws + sum(destination port) as volume
			for _, destPort := range routingData.Rule.DestinationPort {
				curTwsDpVol, err := m.VolumeCounter.GetILHCombinationDpWeight(ctx, productId, ilhLineID, importIlhLineID, lmLineID, ruleTwsCode, destPort, timeutil.GetCurrentUnixTimeStamp(ctx))
				if err != nil {
					logger.CtxLogErrorf(ctx, "GetILHCombinationDpWeight fail|err=%v", err)
					continue
				}
				logger.CtxLogInfof(ctx, "tws [%s], dest post [%s] weight: %d", ruleTwsCode, destPort, curTwsDpVol)
				vol += curTwsDpVol
			}
		}
	}

	return vol
}

func (m MaxWeightFactor) Name() string {
	return MaxWeight
}
