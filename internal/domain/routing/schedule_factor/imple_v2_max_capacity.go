package schedule_factor

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"github.com/gogo/protobuf/proto"
)

type MaxVolumeV2 struct {
	ZoneRuleMgr volumerouting.ZoneRuleMgr
}

func NewMaxVolumeV2(depend volumerouting.ZoneRuleMgr) *MaxVolumeV2 {
	entry := MaxVolumeV2{ZoneRuleMgr: depend}
	Register(entry.Name(), &entry)
	return &entry
}

func (m MaxVolumeV2) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	locInfo := pb.LocationInfo{
		LocationIds: sliceCopy(rd.CreateOrderData.DeliveryLocationIds),
		Postcode:    proto.String(rd.CreateOrderData.DeliveryPostCode),
	}

	ret, volumeRecordMap, _ := m.ZoneRuleMgr.FilterByLinesV2(ctx, pb.RuleStep_MaxCapacity, int64(rd.ProductID), lines,
		&locInfo, rd.Rule.RoutingType, rd.Rule.VolumeRuleId, factorDetail.Name, rd.CreateOrderData.IsCod,
		rd.CreateOrderData.OrderParcelDimension, rd.CreateOrderData.Cogs)
	if len(ret) == 0 {
		// 当所有Line都被筛掉时，返回Input的Lines
		logger.CtxLogInfof(ctx, "%s|All lines were filtered out", m.Name())
		return lines, volumeRecordMap
	}

	return ret, volumeRecordMap
}

func (m MaxVolumeV2) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter lane", m.Name())

	return nil, nil
}

func (m MaxVolumeV2) Name() string {
	return MaxCapacityV2Name
}
