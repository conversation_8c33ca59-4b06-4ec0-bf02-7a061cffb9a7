package schedule_factor

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

func GetIlhAndImportIlhFromLaneInfo(laneInfo *rule.RoutingLaneInfo) (string, string) {
	var ilh, importIlh string
	for _, lineInfo := range laneInfo.LineList {
		resourceSubType := int(lineInfo.ResourceSubType)
		if objutil.ContainInt(lfslib.NeedRoutingILH, resourceSubType) {
			ilh = lineInfo.LineId
			continue
		}
		if resourceSubType == lfslib.C_M_ILH {
			importIlh = lineInfo.LineId
		}
	}

	return ilh, importIlh
}
