package schedule_factor

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

var manager = make(map[string]SchedulerFactor)

type FactorSet struct {
	DgFactor                      *DgFactor
	MinVolumeFactor               *MinVolumeFactor
	MaxCapacityFactor             *MaxCapacityFactor
	MinWeightFactor               *MinWeightFactor
	MaxWeightFactor               *MaxWeightFactor
	LinePriorityFactor            *LinePriorityFactor
	LineCheapestShippingFeeFactor *LineCheapestShippingFeeFactor
	DefaultWeightageFactor        *DefaultWeightageFactor
	DefaultPriorityFactor         *DefaultPriorityFactor
	MinVolumeFactorV2             *MinVolumeV2
	MaxVolumeV2                   *MaxVolumeV2
	ILHParcelMinVolume            *ILHParcelMinVolumeFactor
	ILHParcelMaxCapacity          *ILHParcelMaxCapacityFactor
	CombinationPriority           *CombinationPriorityFactor
}

func NewFactorSet(dg *DgFactor,
	minVol *MinVolumeFactor,
	maxCap *MaxCapacityFactor,
	minWeight *MinWeightFactor,
	maxWeight *MaxWeightFactor,
	linePriority *LinePriorityFactor,
	lineFee *LineCheapestShippingFeeFactor,
	defaultWeight *DefaultWeightageFactor,
	defaultPriority *DefaultPriorityFactor,
	minVolumeV2 *MinVolumeV2,
	maxVolumeV2 *MaxVolumeV2,
	ilhParcelMinVol *ILHParcelMinVolumeFactor,
	ilhParcelMaxCap *ILHParcelMaxCapacityFactor,
	combinationPriority *CombinationPriorityFactor) *FactorSet {
	return &FactorSet{
		DgFactor:                      dg,
		MinVolumeFactor:               minVol,
		MaxCapacityFactor:             maxCap,
		MinWeightFactor:               minWeight,
		MaxWeightFactor:               maxWeight,
		LinePriorityFactor:            linePriority,
		LineCheapestShippingFeeFactor: lineFee,
		DefaultWeightageFactor:        defaultWeight,
		DefaultPriorityFactor:         defaultPriority,
		MinVolumeFactorV2:             minVolumeV2,
		MaxVolumeV2:                   maxVolumeV2,
		ILHParcelMinVolume:            ilhParcelMinVol,
		ILHParcelMaxCapacity:          ilhParcelMaxCap,
		CombinationPriority:           combinationPriority,
	}
}

func Register(name string, factor SchedulerFactor) {
	//todo,lock,
	manager[name] = factor
}

func GetScheduleFactor(name string) SchedulerFactor {
	return manager[name]
}

type SchedulerFactor interface {
	FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{})
	FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{})
	Name() string
}

func GetStepTypeFromName(name string) rule.StepType {
	//兼容
	switch name {
	case Dg:
		return rule.StepDGFlag
	case MinVolume:
		return rule.StepMinVolume
	case MaxCapacity:
		return rule.StepMaxCapacity
	case LineCheapestShippingFee:
		return rule.StepLineCheapestShippingFee
	case LinePriority:
		return rule.StepLinePriority
	case MinWeight:
		return rule.StepMinWeight
	case MaxWeight:
		return rule.StepMaxWeight
	default:
		return rule.UnKnownStep
	}
}
