package schedule_factor

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"math"
)

type CombinationPriorityFactor struct {
}

func NewCombinationPriorityFactor() *CombinationPriorityFactor {
	entry := &CombinationPriorityFactor{}
	Register(entry.Name(), entry)

	return entry
}

func (m *CombinationPriorityFactor) FilterLine(ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.LineInfo, interface{}) {
	logger.CtxLogErrorf(ctx, "factor:%v not support filter line", m.Name())

	return lines, nil
}

func (m CombinationPriorityFactor) FilterLane(ctx context.Context, laneInfos []*rule.RoutingLaneInfo, rd *rule.RoutingData, factorDetail *rule.ScheduleFactorAttr) ([]*rule.RoutingLaneInfo, interface{}) {
	var ret []*rule.RoutingLaneInfo

	var minPriority int32 = math.MaxInt32
	for _, laneInfo := range laneInfos {
		ilh, importIlh := GetIlhAndImportIlhFromLaneInfo(laneInfo)
		ilhCombination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        rd.CreateOrderData.LmId,
		}
		p, exist := factorDetail.CombinationPriorities[ilhCombination]
		if !exist {
			logger.CtxLogErrorf(ctx, "CombinationPriorityFactor abnormal|combination Priority not found, combination=%v", ilhCombination)
			continue
		}
		if p == 0 {
			logger.CtxLogErrorf(ctx, "CombinationPriorityFactor abnormal|combination Priority abnormal, combination=%v", ilhCombination)
			continue
		}
		if p < minPriority {
			minPriority = p
		}
	}

	if minPriority == math.MaxInt32 {
		logger.CtxLogErrorf(ctx, "CombinationPriorityFactor abnormal|CombinationPriorities=%v", factorDetail.CombinationPriorities)
		return laneInfos, nil
	}

	logger.CtxLogInfof(ctx, "CombinationPriorityFactor first priority=%d", minPriority)
	for _, laneInfo := range laneInfos {
		ilh, importIlh := GetIlhAndImportIlhFromLaneInfo(laneInfo)
		ilhCombination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importIlh,
			Lm:        rd.CreateOrderData.LmId,
		}
		p, exist := factorDetail.CombinationPriorities[ilhCombination]
		if !exist || p == 0 {
			continue
		}
		if p == minPriority {
			ret = append(ret, laneInfo)
		}
	}

	return ret, nil
}

func (m *CombinationPriorityFactor) Name() string {
	return CombinationPriority
}
