package routing

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func Test_routingTask_defaultScheduling(t *testing.T) {
	ctx := context.Background()
	rt := &routingTask{}
	type args struct {
		matchedRule            *rule.RoutingRuleParsed
		logEntry               *routing_log.RoutingLog
		scheduleFactorCountMap map[string]int
	}
	tests := []struct {
		name  string
		args  args
		want  []*rule.RoutingLaneInfo
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(validLanes) == 1",
			args: args{
				matchedRule: &rule.RoutingRuleParsed{
					RuleStepResourceList: []*rule.RuleStepResource{
						{},
						{
							ResourceSubType: lfslib.C_FL,
						},
					},
					DefaultCriteriaName: schedule_factor.DefaultPriority,
				},
				logEntry: &routing_log.RoutingLog{},
				scheduleFactorCountMap: map[string]int{
					schedule_factor.DefaultPriority: 1,
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType: lfslib.C_FL,
							ResourceId:      "line-1",
						},
					},
				},
			},
			setup: func() {
				rt = &routingTask{
					routingData: &rule.RoutingData{
						ValidateLaneList: []*rule.RoutingLaneInfo{
							{
								LineList: []*rule.LineInfo{
									{
										ResourceId:      "line-1",
										ResourceSubType: lfslib.C_FL,
									},
								},
							},
						},
					},
				}
				schedule_factor.NewDefaultPriorityFactor()
			},
		},
		{
			name: "case 2: len(validLanes) > 1",
			args: args{
				matchedRule: &rule.RoutingRuleParsed{
					RoutingType: rule.SPXRoutingType,
					RuleStepResourceList: []*rule.RuleStepResource{
						{},
						{
							ResourceSubType: lfslib.C_FL,
						},
					},
					DefaultCriteriaName: schedule_factor.DefaultPriority,
				},
				logEntry: &routing_log.RoutingLog{},
				scheduleFactorCountMap: map[string]int{
					schedule_factor.DefaultPriority: 1,
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType:     lfslib.C_FL,
							RealResourceSubType: lfslib.L_SelfBuild,
							ResourceId:          "line-1",
						},
					},
				},
			},
			setup: func() {
				rt = &routingTask{
					routingData: &rule.RoutingData{
						ValidateLaneList: []*rule.RoutingLaneInfo{
							{
								LineList: []*rule.LineInfo{
									{
										ResourceId:      "line-1",
										ResourceSubType: lfslib.C_FL,
									},
								},
							},
							{
								LineList: []*rule.LineInfo{
									{
										ResourceId:          "line-1",
										ResourceSubType:     lfslib.C_FL,
										RealResourceSubType: lfslib.L_SelfBuild,
									},
								},
							},
						},
					},
				}
				schedule_factor.NewDefaultPriorityFactor()
			},
		},
		{
			name: "case 3: not use filterBySpxSelfBuild",
			args: args{
				matchedRule: &rule.RoutingRuleParsed{
					RuleStepResourceList: []*rule.RuleStepResource{
						{},
						{
							ResourceSubType: lfslib.C_FL,
						},
					},
					DefaultCriteriaName: schedule_factor.DefaultPriority,
				},
				logEntry: &routing_log.RoutingLog{},
				scheduleFactorCountMap: map[string]int{
					schedule_factor.DefaultPriority: 1,
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType: lfslib.C_FL,
							ResourceId:      "line-1",
						},
					},
				},
				{
					LineList: []*rule.LineInfo{
						{
							ResourceSubType: lfslib.C_FL,
							ResourceId:      "line-1",
						},
					},
				},
			},
			setup: func() {
				rt = &routingTask{
					routingData: &rule.RoutingData{
						ValidateLaneList: []*rule.RoutingLaneInfo{
							{
								LineList: []*rule.LineInfo{
									{
										ResourceId:      "line-1",
										ResourceSubType: lfslib.C_FL,
									},
								},
							},
							{
								LineList: []*rule.LineInfo{
									{
										ResourceId:      "line-1",
										ResourceSubType: lfslib.C_FL,
									},
								},
							},
						},
					},
				}
				schedule_factor.NewDefaultPriorityFactor()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got := rt.defaultScheduling(ctx, tt.args.matchedRule, tt.args.logEntry, tt.args.scheduleFactorCountMap)
			common.AssertResult(t, got, tt.want, nil, nil)
		})
	}
}
