package rule

const (
	UnknownRoutingType = 99
	CBRoutingType      = 0
	SPXRoutingType     = 1
	LocalRoutingType   = 2
	IlhRoutingType     = 3

	CbMultiRoutingType = 100
)

type (
	RoutingConfig struct {
		Id                         int64  `json:"id"`
		ProductID                  int64  `json:"product_id"`
		SmartRoutingEnabled        bool   `json:"routing_enable"`
		CBMultiSmartRoutingEnabled bool   `json:"cb_multi_smart_routing_enable"`
		LocalSmartRoutingEnabled   bool   `json:"local_routing_enable"`
		SpxSmartRoutingEnabled     bool   `json:"spx_routing_enable"`
		IlhSmartRoutingEnabled     bool   `json:"ilh_routing_enable"`
		SmartRoutingToggle         bool   `json:"smartrouting_toggle"`
		DefaultLaneCode            string `json:"default_lane_code"`
		OperatedBy                 string `json:"operated_by"`
		CTime                      uint32 `json:"ctime"`
		MTime                      uint32 `json:"mtime"`
	}
)

func (r *RoutingConfig) IsSupportSmartRouting() bool {
	return r.SmartRoutingEnabled || r.LocalSmartRoutingEnabled || r.SpxSmartRoutingEnabled || r.CBMultiSmartRoutingEnabled || r.IlhSmartRoutingEnabled
}

func (r *RoutingConfig) IsCBSmartRouting() bool {
	return r.SmartRoutingEnabled || r.CBMultiSmartRoutingEnabled
}

func (r *RoutingConfig) IsLocalSmartRouting() bool {
	return r.LocalSmartRoutingEnabled
}

func (r *RoutingConfig) IsSpxSmartRouting() bool {
	if r == nil {
		return false
	}
	return r.SpxSmartRoutingEnabled
}

func (r *RoutingConfig) IsIlhSmartRouting() bool {
	return r.IlhSmartRoutingEnabled
}

func (r *RoutingConfig) IsDuplicate() bool {
	sum := 0
	if r.SmartRoutingEnabled {
		sum += 1
	}
	if r.SpxSmartRoutingEnabled {
		sum += 1
	}
	if r.LocalSmartRoutingEnabled {
		sum += 1
	}

	return sum > 1
}

// CB product allows "CB && SPX enabled together", Local product only allow one of "Local enabled" and "SPX enabled"
func (r *RoutingConfig) GetRoutingType() uint8 {
	//if product only enabled CB, then return CB;
	/*if product allows CB and SPX, then will return CB,
	because here we will do smart routing of "first CB, second SPX", and we will get SPX routing type manually later
	*/
	if r.SmartRoutingEnabled || r.CBMultiSmartRoutingEnabled {
		return CBRoutingType
	}
	//if product only enabled SPX, then return SPX
	if r.SpxSmartRoutingEnabled && !r.SmartRoutingEnabled {
		return SPXRoutingType
	}
	//if product only enabled Local, then return Local
	if r.LocalSmartRoutingEnabled {
		return LocalRoutingType
	}
	if r.IlhSmartRoutingEnabled {
		return IlhRoutingType
	}

	return UnknownRoutingType
}

func GetRoutingTypeName(routingType uint8) string {
	switch routingType {
	case CBRoutingType:
		return "CBRouting"
	case SPXRoutingType:
		return "SPXRouting"
	case LocalRoutingType:
		return "LocalRouting"
	case IlhRoutingType:
		return "IlhRouting"
	}
	return "UnknownRouting"
}

func (r *RoutingConfig) CheckSmartRoutingType(routingType uint8, isMulti bool) bool {
	switch routingType {
	case CBRoutingType:
		if isMulti {
			return r.CBMultiSmartRoutingEnabled
		} else {
			return r.SmartRoutingEnabled
		}
	case SPXRoutingType:
		return r.SpxSmartRoutingEnabled
	case LocalRoutingType:
		return r.LocalSmartRoutingEnabled
	case IlhRoutingType:
		return r.IlhSmartRoutingEnabled
	default:
		return false
	}
}
