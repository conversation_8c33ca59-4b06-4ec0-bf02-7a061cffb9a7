package rule

const DgRelated = 1

type RoutingLaneInfo struct {
	LaneCode             string        `json:"lane_code"`
	LaneCodeGroup        []string      `json:"lane_code_group"`
	ServiceCode          string        `json:"service_code"`
	LineList             []*LineInfo   `json:"line_list,omitempty"`
	DgGroupId            string        `json:"dg_group_id"`
	DestinationPort      string        `json:"destination_port"`
	CustomsClearanceList []string      `json:"customs_clearance_list"`
	ActualPointList      []ActualPoint `json:"actual_point_list"`
}

type ActualPoint struct {
	PointType   int32  `json:"point_type"`
	SiteSubType int32  `json:"site_sub_type"`
	SiteId      string `json:"site_id"`
	PointId     string `json:"point_id"`
}

type LineInfo struct {
	LineId              string `json:"line_id"`
	ResourceSubType     int32  `json:"resource_sub_type"` // 线类型
	ResourceId          string `json:"resource_id"`       // 线ID
	DgRelated           int    `json:"dg_related"`        // 是否DG相关	用于weightage判断时是否使用DGFlag
	DGFlag              DGFlag `json:"dg_flag"`           // DG属性
	RealResourceSubType int32  `json:"-"`                 // 线类型
}

type DgGroupInfo struct {
	DgGroupId   string   `json:"dg_group_id"`
	DgGroupName string   `json:"dg_group_name"`
	LineList    []string `json:"line_list"`
}

type ListResourceType struct {
	LaneCodes    []string    `json:"lane_code_list"`
	ResourceList []*LaneInfo `json:"resource_sub_type_list"`
}

type LaneInfo struct {
	RoutingType         int32   `json:"resource_sub_type"`
	DisplayResourceType string  `json:"display_resource_type"`
	DgRelated           int32   `json:"dg_related"`
	LineList            []*Line `json:"line_list"`
}

type Line struct {
	LineId  string `json:"line_id"`
	Name    string `json:"line_name"`
	Enabled int32  `json:"enable"`
}

// NeedRouting 判断Line是否需要调度，由Routing Role映射后的ResourceSubType决定
func (l LineInfo) NeedRouting() bool {
	return l.ResourceSubType != LineTypeNoNeedRouting
}

// IsDgRelated 判断Line是否是DG相关
func (l LineInfo) IsDgRelated() bool {
	return l.DgRelated == DgRelated
}

func (r *RoutingLaneInfo) GetLineIds() []string {
	if r == nil || len(r.LineList) == 0 {
		return []string{}
	}
	lineIds := make([]string, 0, len(r.LineList))
	for _, line := range r.LineList {
		lineIds = append(lineIds, line.LineId)
	}
	return lineIds
}
