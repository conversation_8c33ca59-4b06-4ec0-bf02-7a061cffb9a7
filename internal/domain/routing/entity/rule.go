package rule

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type (
	RuleStatus int
	DGFlag     int
	CCMode     uint8
)

const (
	RuleStatusDraft    RuleStatus = 0
	RuleStatusActive   RuleStatus = 1
	RuleStatusExpired  RuleStatus = 2
	RuleStatusQueuing  RuleStatus = 3
	RuleStatusForecast RuleStatus = 4
)

const (
	UndefinedDGFlag DGFlag = 0
	NonDG           DGFlag = 1
	DG              DGFlag = 2
	Prohibit        DGFlag = 4
)

func (d DGFlag) String() string {
	switch d {
	case UndefinedDGFlag:
		return "UndefinedDGFlag"
	case NonDG:
		return "NonDG"
	case DG:
		return "DG"
	case Prohibit:
		return "Prohibit"
	default:
		return "UnknownDGFlag"
	}
}

const (
	MinParcelSize = 0
	MaxParcelSize = ********9
)

const (
	CCModeNoCCRouting CCMode = 0
	CCModeCCRouting   CCMode = 1
)

// LineNoNeedRouting a special routing type in smart routing's routing role, dummy resource sub type
const LineTypeNoNeedRouting = 0

type StepType int

const (
	StepDGFlag StepType = iota
	StepMinVolume
	StepMaxCapacity
	StepCheapestShippingFee
	StepLineCheapestShippingFee
	StepLinePriority
	StepMinWeight
	StepMaxWeight
	StepIlhParcelMinVolume
	StepIlhParcelMaxCapacity
	UnKnownStep
)

func (step StepType) String() string {
	switch step {
	case StepDGFlag:
		return "DGFlag"
	case StepMinVolume:
		return "MinVolume"
	case StepMaxCapacity:
		return "MaxCapacity"
	case StepCheapestShippingFee:
		return "CheapestShippingFee"
	case StepLinePriority:
		return "LinePriority"
	case StepMinWeight:
		return "MinWeight"
	case StepMaxWeight:
		return "MaxWeight"
	case StepIlhParcelMinVolume:
		return "IlhParcelMinVolume"
	case StepIlhParcelMaxCapacity:
		return "IlhParcelMaxCapacity"
	default:
		return "UnknownStep"
	}
}

// sort rule base priority
type RuleSteps []*ScheduleFactorAttr

func (s RuleSteps) Len() int           { return len(s) }
func (s RuleSteps) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }
func (s RuleSteps) Less(i, j int) bool { return s[i].Priority < s[j].Priority }

// RuleStepResources sort resource base priority
type RuleStepResources []*RuleStepResource

func (s RuleStepResources) Len() int           { return len(s) }
func (s RuleStepResources) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }
func (s RuleStepResources) Less(i, j int) bool { return s[i].Priority < s[j].Priority }

const (
	MinPriority     = 1
	MaxPriority     = 1000
	DefaultPriority = 1000
	LengthLimit     = 15
)

type CriteriaType int

const (
	CriteriaWeightAge CriteriaType = iota
	CriteriaPriority
)

func (criteriaType CriteriaType) String() string {
	switch criteriaType {
	case CriteriaWeightAge:
		return "weightage_criteria"
	case CriteriaPriority:
		return "priority_criteria"
	default:
		return "unknown_criteria"
	}
}

type RoutingData struct {
	ProductID         int
	Rule              *RoutingRuleParsed
	OriginalLaneInfos []*RoutingLaneInfo
	ValidateLaneList  []*RoutingLaneInfo // 过程中数据，
	CreateOrderData   *SmartRoutingOrderData
	//true: 非纯spx，在"vn cb二次调度的spx routing"中用来获取最大/最小运力单量
	//false: 纯spx，在"只进行单次调度的spx routing"中用来获取最大/最小运力单量
	NotOnlySPX bool
	// 提前计算的运费结果
	PreLineFeeMap map[string]float64
	// 调routing方法的业务类型
	BusinessType  int8
	ReCalcFee     bool               //是否重新计算运费，仅forecast task有这个字段
	OldLineFeeMap map[string]float64 //routing_log中的运费结果，仅在forecast_task中使用
}

type (
	RoutingRule struct {
		ID                          int64                        `json:"id"`
		ProductID                   int64                        `json:"product_id"`
		Status                      RuleStatus                   `json:"rule_status"`
		RuleName                    string                       `json:"rule_name"`
		Priority                    int32                        `json:"priority"`
		RuleDetails                 *RuleDetails                 `json:"rule_detail,omitempty"`
		DefaultCriteria             *DefaultCriteria             `json:"default_criteria,omitempty"` // 默认规则权重配置
		DisabledInfo                []*DisabledInfo              `json:"disabled_info,omitempty"`
		MultiProductDefaultCriteria *MultiProductDefaultCriteria `json:"multi_product_default_criteria,omitempty"` // Multi Product默认规则权重配置
		MultiProductDisableInfo     *MultiProductDisabledInfo    `json:"multi_product_disable_info,omitempty"`
		CombinationSetting          []*CombinationSetting        `json:"combination_setting"` // ILH组合(ILH->ImportILH->LM)的规则配置
		WhsId                       []string                     `json:"whs_id"`
		ZoneCode                    []string                     `json:"zone_code"`
		DestinationPorts            []string                     `json:"destination_ports"` //SSCSMR-278:used to store ho site id list from ilh lanes' ho
		DgType                      int                          `json:"dg_type,omitempty"` //DG; NON-DG
		ParcelDimension             ParcelDimension              `json:"parcel_dimension,omitempty"`
		Soc                         string                       `json:"soc"` //应该目前是废弃的，
		ItemCategoryLevel           int                          `json:"item_category_level,omitempty"`
		ItemCategoryID              []int                        `json:"item_category_id,omitempty"`
		ParcelValueMin              float32                      `json:"parcel_value_min,omitempty"`
		ParcelValueMax              float32                      `json:"parcel_value_max,omitempty"`
		ParcelWeightMin             int                          `json:"parcel_weight_min,omitempty"`
		ParcelWeightMax             int                          `json:"parcel_weight_max,omitempty"`
		OperatedBy                  string                       `json:"operated_by"`
		EffectiveStartTime          uint32                       `json:"effective_start_time"`
		TaskID                      int                          `json:"task_id"`
		TaskName                    string                       `json:"task_name"`
		CTime                       uint32                       `json:"ctime"`
		MTime                       uint32                       `json:"mtime"`
		RoutingType                 uint8                        `json:"routing_type"`
		IsMultiProduct              bool                         `json:"is_multi_product"`
		IsVolumeRouting             bool                         `json:"is_volume_routing"`
		WmsToggleEnable             bool                         `json:"wms_toggle_enable"`
		CCMode                      CCMode                       `json:"cc_mode"`
		VolumeRuleId                int64                        `json:"volume_rule_id"`
		TplToggleInfo               *ListResourceType            `json:"tpl_toggle_info"`
		ShopGroupListVo             []int64                      `json:"shop_group_list"`
	}

	ParcelDimension struct {
		LengthMin float64 `json:"length_min,omitempty"`
		LengthMax float64 `json:"length_max,omitempty"`
		WidthMin  float64 `json:"width_min,omitempty"`
		WidthMax  float64 `json:"width_max,omitempty"`
		HeightMin float64 `json:"height_min,omitempty"`
		HeightMax float64 `json:"height_max,omitempty"`
	}

	RuleDetails struct {
		Rules []*Detail `json:"rules,omitempty"`
	}

	Detail struct {
		ResourceSubType                       int32        `json:"resource_sub_type"`
		DisplayResourceType                   string       `json:"display_resource_type"`
		CombinationMode                       bool         `json:"combination_mode"`  // 是否为ILH组合模式
		MinVolumeEnable                       bool         `json:"min_volume_enable"` //min volume
		MinVolumeSort                         int32        `json:"min_volume_sort,omitempty"`
		MaxCapacityEnable                     bool         `json:"max_capacity_enable"` //max volume
		MaxCapacitySort                       int32        `json:"max_capacity_sort,omitempty"`
		MinWeightEnable                       bool         `json:"min_weight_enable"`
		MinWeightSort                         int32        `json:"min_weight_sort,omitempty"`
		MaxWeightCapacityEnable               bool         `json:"max_weight_capacity_enable"`
		MaxWeightCapacitySort                 int32        `json:"max_weight_capacity_sort,omitempty"`
		MaxCodCapacityEnable                  bool         `json:"max_cod_capacity_enable"`
		MaxCodCapacitySort                    int32        `json:"max_cod_capacity_sort,omitempty"`
		MaxBulkyCapacityEnable                bool         `json:"max_bulky_capacity_enable"`
		MaxBulkyCapacitySort                  int32        `json:"max_bulky_capacity_sort,omitempty"`
		MaxHighValueCapacityEnable            bool         `json:"max_high_value_capacity_enable"`
		MaxHighValueCapacitySort              int32        `json:"max_high_value_capacity_sort,omitempty"`
		MaxDgCapacityEnable                   bool         `json:"max_dg_capacity_enable"`
		MaxDgCapacitySort                     int32        `json:"max_dg_capacity_sort,omitempty"`
		Priority                              int32        `json:"priority,omitempty"`
		DgPriority                            DGFlag       `json:"dg_priority,omitempty"`
		DgRelated                             int32        `json:"dg_related"`
		DgClassificationEnable                bool         `json:"dg_classification_enable"`
		DgClassificationSort                  int32        `json:"dg_classification_sort,omitempty"`
		LineLimit                             []*LineLimit `json:"line_limit,omitempty"`
		CheapestShippingFeeEnable             bool         `json:"cheapest_shipping_fee_enable"`
		CheapestShippingFeeSort               int32        `json:"cheapest_shipping_fee_sort,omitempty"`
		LineCheapestShippingFeePriorityEnable bool         `json:"line_cheapest_shipping_fee_priority_enable"`
		LineCheapestShippingFeePrioritySort   int32        `json:"line_cheapest_shipping_fee_priority_sort,omitempty"`
		LinePriorityEnable                    bool         `json:"3pl_priority_enable"`
		LinePrioritySort                      int32        `json:"3pl_priority_sort,omitempty"`
		ParcelMinVolumeEnable                 bool         `json:"parcel_min_volume_enable"`
		ParcelMaxCapacityEnable               bool         `json:"parcel_max_capacity_enable"`
		IsVolumeRouting                       bool         `json:"is_volume_routing"`
	}

	LineLimit struct {
		LineId               string         `json:"line_id"`
		Name                 string         `json:"name"`
		MaxCapacity          int            `json:"max_capacity"`
		MinVolume            int            `json:"min_volume"`
		IlhParcelMaxCapacity int            `json:"ilh_parcel_max_capacity"`
		IlhParcelMinVolume   int            `json:"ilh_parcel_min_volume"`
		MaxWeightCapacity    int            `json:"max_weight_capacity"`
		MaxCodCapacity       int            `json:"max_cod_capacity"`
		MaxBulkyCapacity     int            `json:"max_bulky_capacity"`
		MaxHighValueCapacity int            `json:"max_high_value_capacity"`
		MaxDgCapacity        int            `json:"max_dg_capacity"`
		MinWeight            int            `json:"min_weight"`
		Priority             int32          `json:"3pl_priority"`
		DGFlag               int            `json:"dg_flag"` //1: non-dg; 2: dg
		CombinationLines     []BaseLineInfo `json:"combination_lines"`
	}

	DefaultCriteria struct {
		CriteriaType                CriteriaType                    `json:"criteria_type"`
		CriteriaName                string                          `json:"criteria_name"`
		WeightageCriteria           []*CriteriaInfo                 `json:"weightage_criteria,omitempty"`
		PriorityCriteria            []*CriteriaInfo                 `json:"priority_criteria,omitempty"`
		CombinationPriorityCriteria CombinationPriorityCriteriaInfo `json:"combination_priority_criteria"`
	}

	CriteriaInfo struct {
		ResourceSubType     int32             `json:"resource_sub_type"`
		DisplayResourceType string            `json:"display_resource_type"`
		DgRelated           int32             `json:"dg_related"`
		LineInfoList        []*RuleLineConfig `json:"line_info_list"`
		Priority            int               `json:"priority"`
	}

	CombinationPriorityCriteriaInfo struct {
		Priority            int               `json:"priority"`
		CombinationInfoList []CombinationInfo `json:"combination_info_list"`
	}

	CombinationInfo struct {
		LineList            []BaseLineInfo `json:"line_list"`
		CombinationPriority int            `json:"combination_priority"`
	}

	RuleLineConfig struct {
		LineId    string `json:"line_id"`
		Name      string `json:"name"`
		DGFlag    DGFlag `json:"dg_flag"`
		WeightAge int    `json:"weightage"`
		Priority  int    `json:"priority"`
	}

	DisabledInfo struct {
		ResourceSubType     int32    `json:"resource_sub_type"`
		DisplayResourceType string   `json:"display_resource_type"`
		LineList            []string `json:"line_list"`
		WmsLineList         []string `json:"wms_line_list"` // spx/local smart routing use this
	}

	MultiProductDefaultCriteria struct {
		CriteriaType      CriteriaType             `json:"criteria_type"`
		CriteriaName      string                   `json:"criteria_name"`
		WeightageCriteria MultiProductCriteriaInfo `json:"weightage_criteria,omitempty"`
	}

	MultiProductCriteriaInfo struct {
		DgGroupCriteriaInfo DgGroupCriteriaInfo `json:"dg_group_criteria_info"`
		LineCriteriaInfo    []CriteriaInfo      `json:"line_criteria_info"`
	}

	DgGroupCriteriaInfo struct {
		DgGroupList []DgGroupCriteriaInfoItem `json:"dg_group_list"`
	}

	DgGroupCriteriaInfoItem struct {
		DgGroupId   string                       `json:"dg_group_id"`
		DgGroupName string                       `json:"dg_group_name"`
		LineList    []DefaultCriteriaDgGroupLine `json:"line_list"`
		DgFlag      DGFlag                       `json:"dg_flag"`
	}

	DgGroupLine struct {
		LineId string `json:"line_id"`
		Name   string `json:"name"`
	}

	DefaultCriteriaDgGroupLine struct {
		DgGroupLine
		DGFlag    DGFlag `json:"dg_flag"`
		WeightAge int    `json:"weightage"`
		Priority  int    `json:"priority"`
	}

	DgGroupDisableInfoItem struct {
		DgGroupId   string        `json:"dg_group_id"`
		DgGroupName string        `json:"dg_group_name"`
		LineList    []DgGroupLine `json:"line_list"`
		Enable      bool          `json:"enable"`
	}

	DgGroupDisableInfo struct {
		DgGroupList []DgGroupDisableInfoItem `json:"dg_group_list"`
	}

	MultiProductDisabledInfo struct {
		DgGroupDisableInfo DgGroupDisableInfo `json:"dg_group_disable_info"`
		LineDisableInfo    []DisabledInfo     `json:"line_disable_info"`
	}

	BaseLineInfo struct {
		LineId   string `json:"line_id"`
		LineName string `json:"line_name"`
	}

	CombinationSetting struct {
		BaseLineInfo
		ConnectLines []CombinationLineGroup `json:"connect_lines"`
	}

	CombinationLineGroup struct {
		Lines []BaseLineInfo `json:"lines"`
	}
)

type (
	//结构属性 字段，做下聚合，包括 Cb / LOCAL / COMMON ,
	RoutingRuleParsed struct {
		ID                   int64
		Priority             int32
		AvailableLines       []string
		WmsAvailableLines    []string
		AvailableDgGroup     []string
		DefaultPriorities    map[int32]map[CriteriaType]map[string]int //map {ResourceSubType}{CriteriaType}{LineInfo}WeightAge/Priority
		DefaultCriteriaType  CriteriaType                              // weightage/priority
		WhsId                []string
		ZoneCode             []string
		ItemCategoryLevel    int     `gorm:"column:item_category_level" json:"item_category_level"`
		ItemCategoryID       []int   `gorm:"-" json:"item_category_id"`
		ParcelValueMin       float32 `gorm:"column:parcel_value_min" json:"parcel_value_min"`
		ParcelValueMax       float32 `gorm:"column:parcel_value_max" json:"parcel_value_max"`
		ParcelWeightMin      int     `gorm:"column:parcel_weight_min" json:"parcel_weight_min"`
		ParcelWeightMax      int     `gorm:"column:parcel_weight_max" json:"parcel_weight_max"`
		DestinationPort      []string
		DgType               int
		ParcelDimension      ParcelDimension
		RuleStepResourceList []*RuleStepResource // line resource list by rule types
		IlhRuleSteps         []*ScheduleFactorAttr
		RoutingType          uint8
		DefaultCriteriaName  string
		WmsToggleEnable      bool
		LineToggle           map[string]*Toggle
		AvailableCombination map[IlhCombination]struct{}
		CCMode               CCMode
		VolumeRuleId         int64
		ShopGroupList        []int64
	}

	//此处定义一个和routing_log包下面一样的toggle，如果直接用会存在循环包应用
	Toggle struct {
		On  []string
		Off []string
	}

	RuleStepResource struct {
		RuleSteps           []*ScheduleFactorAttr
		ResourceSubType     int32
		Priority            int32
		DisplayResourceType string // for debug
	}

	IlhCombination struct {
		Ilh       string
		ImportIlh string
		Lm        string
	}

	//todo ,改为Factor ，保持统一，调度因子属性的聚合
	ScheduleFactorAttr struct {
		CombinationMode          bool
		ResourceSubType          int32
		Priority                 int32
		Name                     string
		MinVolumeData            *MinVolumeData
		MaxCapacityData          *MaxCapacityData
		IlhParcelMinVolumeData   *MinVolumeData
		IlhParcelMaxCapacityData *MaxCapacityData
		MinWeightData            *MinWeightData
		MaxWeightData            *MaxWeightData
		DgFlagData               *DgFlagData
		LinePriorityData         *LinePriorityData
		CombinationPriorities    map[IlhCombination]int32
		DefaultCriteriaCfg       map[string]int
	}

	MinVolumeData struct {
		MinVolumes            map[string]int // line_id -> value
		CombinationMinVolumes map[IlhCombination]int
	}

	MaxCapacityData struct {
		MaxCapacities            map[string]int // line_id -> value
		CombinationMaxCapacities map[IlhCombination]int
	}

	DgFlagData struct {
		DgRelated  int32
		DgPriority DGFlag
	}

	LinePriorityData struct {
		LinePriorities map[string]int32 // line_id -> value
	}

	MinWeightData struct {
		MinWeights            map[string]int
		CombinationMinWeights map[IlhCombination]int
	}

	MaxWeightData struct {
		MaxWeights            map[string]int
		CombinationMaxWeights map[IlhCombination]int
	}
)

type (
	ProductLane struct {
		ProductId int64  `json:"product_id"`
		Lane      Lane   `json:"lane"`
		DGFlag    DGFlag `json:"dg_flag"`
		Enabled   bool   `json:"enabled"`
	}
	Lane struct {
		LineId       string   `json:"line_id"`
		RelatedLines []string `json:"related_line"`
		ChannelFlag  string   `json:"channel_flag"`
	}
)

func (p *ParcelDimension) Scan(value interface{}) error {
	if value == nil {
		return nil // 如果是 NULL，直接返回，p 保持为空结构
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal ParcelDimension:", value))
	}
	return json.Unmarshal(bytes, p)
}

func (p ParcelDimension) Value() (driver.Value, error) {
	if p == (ParcelDimension{}) {
		return "{}", nil
	}
	return json.Marshal(p)
}

func (d ParcelDimension) ValidateParcelDimension() error {
	// 检查长度范围
	if !isWithinRange(d.LengthMin, d.LengthMax) {
		return srerr.New(srerr.ParamErr, nil, "LengthMin and LengthMax must be between 0 and ********, LengthMin < LengthMax")
	}

	// 检查宽度范围
	if !isWithinRange(d.WidthMin, d.WidthMax) {
		return srerr.New(srerr.ParamErr, nil, "WidthMin and WidthMax must be between 0 and ********, WidthMin < WidthMax")
	}

	// 检查高度范围
	if !isWithinRange(d.HeightMin, d.HeightMax) {
		return srerr.New(srerr.ParamErr, nil, "HeightMin and HeightMax must be between 0 and ********, HeightMin < HeightMax")
	}

	return nil
}

// isWithinRange 检查值是否在MinParcelSize和MaxParcelSize之间，并且min < max
func isWithinRange(min, max float64) bool {
	if min == 0 && max == 0 {
		return true
	}
	if min < MinParcelSize || min > MaxParcelSize {
		return false
	}
	if max < MinParcelSize || max > MaxParcelSize {
		return false
	}
	return min < max
}

func (r RoutingRuleParsed) IsCCRoutingMode() bool {
	return r.CCMode == CCModeCCRouting
}
