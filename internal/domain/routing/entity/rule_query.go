package rule

type RuleQuery struct {
	FilterByID        bool
	ID                int64
	FilterByStatus    bool
	Status            RuleStatus
	FilterByProductID bool
	ProductID         int64
	FilterByTaskID    bool
	TaskID            int64
	Offset            int32
	Limit             int32
	PageNo            int
	RoutingType       uint8
	IsMultiProduct    bool
	IsLocalForecast   bool
}

func NewRuleQuery() *RuleQuery {
	return &RuleQuery{Offset: 0, Limit: 20}
}

func (q *RuleQuery) ByID(id int64) *RuleQuery {
	q.FilterByID = true
	q.ID = id
	return q
}

func (q *RuleQuery) ByStatus(status RuleStatus) *RuleQuery {
	q.FilterByStatus = true
	q.Status = status
	return q
}

func (q *RuleQuery) ByProductID(id int64) *RuleQuery {
	q.FilterByProductID = true
	q.ProductID = id
	return q
}

func (q *RuleQuery) ByTaskID(id int64) *RuleQuery {
	q.FilterByTaskID = true
	q.TaskID = id
	return q
}

func (q *RuleQuery) ByIsMultiProduct(isMultiProduct bool) *RuleQuery {
	q.IsMultiProduct = isMultiProduct
	return q
}

func (q *RuleQuery) WithPage(pageno int32, limit int32) *RuleQuery {
	if pageno <= 0 {
		pageno = 1
	}
	if limit <= 0 {
		limit = 20
	}
	if limit > 10000 {
		limit = 10000
	}
	q.Limit = limit
	q.Offset = (pageno - 1) * limit
	return q
}

func (q *RuleQuery) WithOffset(offset int32) *RuleQuery {
	q.Offset = offset
	return q
}

func (q *RuleQuery) WithLimit(limit int32) *RuleQuery {
	q.Limit = limit
	return q
}
