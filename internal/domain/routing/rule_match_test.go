package routing

import (
	"context"
	"errors"
	"fmt"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	jsoniter "github.com/json-iterator/go"
	"testing"
)

//	func GetConfig() (config.Config, error) {
//		//
//		cfg := new(config.Config)
//		err := chassisConfig.UnmarshalConfig(config.DSNKey, &cfg.DSNConfig)
//		check(err)
//		err = chassisConfig.UnmarshalConfig(config.DBConnKey, &cfg.DBConnConfig)
//		check(err)
//		return *cfg, nil
//	}
//func check(err error) {
//	if err != nil {
//		panic(err)
//	}
//}
//
//func initDepends() {
//	//
//	if err := chassis.Init(
//		chassis.WithChassisConfigPrefix("grpc_server")); err != nil {
//		panic(err)
//	}
//	//db-cfgs ?? loadings ?,
//	err := configutil.Init()
//	check(err)
//	dbutil.Init()
//
//	dbutil.InitGrpcDb()
//
//	err = localcache.Init(lcregistry.LocalCacheConfig...)
//	check(err)
//
//	//cache-rules ,
//
//	//cfg, err := GetConfig()
//	//check(err)
//	//color.Red("check-yaml,%+v", cfg.DBConnConfig)
//	//dbMapps, err := dbconn.InitDBConnection(cfg)
//	//check(err)
//	//mapper := dao.NewDataAccessImpl(dbMapps)
//	//service = NewAddressCodeServiceimpl(mapper, nil, nil)
//}
//
//func initLineList(lanecode string) []*rule.LineInfo {
//	l1 := &rule.LineInfo{
//		LineId:          lanecode + "_line1",
//		ResourceSubType: 256,
//		ResourceId:      lanecode + "_line1",
//	}
//
//	l2 := &rule.LineInfo{
//		LineId:          lanecode + "_line2",
//		ResourceSubType: 2,
//		ResourceId:      lanecode + "_line2",
//	}
//
//	return []*rule.LineInfo{l1, l2}
//
//}
//
//func initLaneList() []*rule.RoutingLaneInfo {
//	//querys-checks?
//	l1 := &rule.RoutingLaneInfo{
//		LaneCode:      "l1",
//		LaneCodeGroup: nil,
//		ServiceCode:   "",
//		LineList:      initLineList("l1"),
//	}
//
//	l2 := &rule.RoutingLaneInfo{
//		LaneCode:      "l2",
//		LaneCodeGroup: nil,
//		ServiceCode:   "",
//		LineList:      initLineList("l2"),
//	}
//	return []*rule.RoutingLaneInfo{l1, l2}
//}
//
//func formatKeytest(lineID string, dgFlag int) string {
//	return fmt.Sprintf("Line:%v-DGFlag:%v", lineID, dgFlag)
//}
//
//func initPmap() map[int32]map[rule.CriteriaType]map[string]int {
//	//cks
//	ret := make(map[int32]map[rule.CriteriaType]map[string]int)
//	ret1 := make(map[rule.CriteriaType]map[string]int)
//
//	ret1[rule.CriteriaWeightAge] = map[string]int{helper.FormatLineAndDgKey("l1_line1", 0): 100, helper.FormatLineAndDgKey("l2_line1", 0): 0}
//	ret[1] = ret1
//	return ret
//}
//
//func initRule() *rule.RoutingRuleParsed {
//	ret := new(rule.RoutingRuleParsed)
//	ret.AvailableLines = []string{"l1_line1", "l1_line2", "l2_line1", "l2_line2"}
//
//	ruleSteps := make([]*rule.RuleStepResource, 2)
//	//Dg  fee  volume
//
//	//factor1 maxCapacitys
//
//	//factor2 line-priority
//	cap := rule.MaxCapacityData{map[string]int{"l1_line1": 100000}}
//	prior := rule.LinePriorityData{LinePriorities: map[string]int32{"l1_line1": 100}}
//	dgData := rule.DgFlagData{
//		DgRelated:  1,
//		DgPriority: 1,
//	}
//
//	f1 := rule.ScheduleFactorAttr{
//		Priority:           1,
//		Name:               schedule_factor.Dg,
//		MinVolumeData:      nil,
//		MaxCapacityData:    &cap,
//		DgFlagData:         &dgData,
//		LinePriorityData:   nil,
//		DefaultCriteriaCfg: nil,
//	}
//
//	f2 := rule.ScheduleFactorAttr{
//		Priority:           3,
//		Name:               schedule_factor.LineCheapestShippingFee,
//		MinVolumeData:      nil,
//		MaxCapacityData:    nil,
//		DgFlagData:         nil,
//		LinePriorityData:   &prior,
//		DefaultCriteriaCfg: nil,
//	}
//
//	ruleSteps[0] = &rule.RuleStepResource{
//		RuleSteps:       []*rule.ScheduleFactorAttr{&f1, &f2},
//		ResourceSubType: 1,
//		Priority:        0,
//	}
//
//	ruleSteps[1] = &rule.RuleStepResource{
//		RuleSteps:       []*rule.ScheduleFactorAttr{&f1, &f2},
//		ResourceSubType: 2,
//		Priority:        0,
//	}
//
//	ret.RuleStepResourceList = ruleSteps
//
//	//fl and lm ??mapping-flows ??
//	//from-json-unmarshalled-contents- ??,
//	//fl := new(rule.RuleStepResource)
//	//fl.ResourceSubType = 1
//	//fl.RuleSteps =
//	ret.DefaultCriteriaType = rule.CriteriaWeightAge
//	ret.DefaultPriorities = initPmap()
//
//	return ret
//}
//
//// done-passed
//func passTestRolemap(t *testing.T) {
//	//UpdateLineResourceType
//	initDepends()
//
//	//	routing.UpdateLineResourceType(ctx,int(in.GetProductInfo().GetProductId()),conf,ll)
//	productid := 80022
//
//	ll := initLaneList()
//
//	routing_role.UpdateLineResourceType(context.TODO(), productid, 0, ll, false)
//
//	fmt.Println("done")
//
//}
//
//func passTestFilterprocess(t *testing.T) {
//	//func (rs *RoutingServiceImpl) Routing(ctx context.Context, productID int, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData) (*rule.RoutingLaneInfo, *srerr.Error) {
//	pid := 88001
//	srcLanes := initLaneList()
//
//	rule := initRule()
//
//	initDepends()
//	schedule_factor.NewDefaultWeightageFactor()
//	schedule_factor.NewLinePriorityFactor()
//	schedule_factor.NewDgFactor()
//	{
//		rateApiImpl := chargeclient.NewChargeApiImpl()
//		lfsApiImpl := lfsclient.NewLfsApiImpl()
//		llsApiImpl := llsclient.NewLlsApiImpl()
//		laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
//		schedule_factor.NewLineCheapestShippingFeeFactor(rateApiImpl, laneServiceImpl)
//	}
//
//	impl := new(RoutingServiceImpl)
//	//init-seqs ?
//	resLane, resErr := impl.Routing(context.TODO(), pid, false, srcLanes, rule, nil, nil)
//	fmt.Println("err ", resErr)
//	spew.Dump(resLane)
//
//}
//
//func TestDebug(t *testing.T) {
//	src := []*rule.RoutingLaneInfo{}
//	src = append(src, &rule.RoutingLaneInfo{
//		LaneCode:      "a1",
//		LaneCodeGroup: nil,
//		ServiceCode:   "",
//		LineList:      nil,
//	})
//
//	src = append(src, &rule.RoutingLaneInfo{
//		LaneCode:      "a2",
//		LaneCodeGroup: nil,
//		ServiceCode:   "",
//		LineList:      nil,
//	})
//
//	ret := getDebugLaneInfo(src)
//	fmt.Printf("res %+v", ret)
//}
//
//func passTestMatchrule(t *testing.T) {
//	initDepends()
//
//	//func (s *SmartServiceImpl) composeSmartRoutingOrderData(ctx context.Context, in *select_lane.SelectLaneReq) *rule.SmartRoutingOrderData
//	productId := 870002
//
//	zoneRepoImpl := locationzone.NewZoneRepoImpl()
//	routingRuleRepoImpl := NewRoutingRuleRepo(zoneRepoImpl)
//
//	rp := RuleMatchParam{
//		ProductId:         int64(productId),
//		WhsID:             "",
//		LocationIDList:    []uint64{123, 456},
//		Cogs:              0,
//		ValidationWeight:  0,
//		ItemCategoryInfos: nil,
//	}
//
//	//matched-seqs ??
//	rule, err := routingRuleRepoImpl.MatchFirstPriorityRoutingRule(context.TODO(), rp)
//
//	//details-listed ,
//	fmt.Println(rule, err)
//
//}

func passTestLines(t *testing.T) {
	seenLines := map[string]bool{"l1": true, "l2": false}

	disabledInfo := []*rule.DisabledInfo{
		{
			LineList: []string{"l1"},
		},
		{
			LineList: []string{"l3"},
		},
	}

	res := getAvailableLines(seenLines, disabledInfo)
	fmt.Println(res)
}

//func getAvailableLines(seeLines map[string]bool, disabledInfo []*rule.DisabledInfo) []string {

func testNewRoutingRuleRepo(t *testing.T) {
	impl := new(routingRuleRepoImpl)

	//cache-inits,
	//参数 构造， all-datas,
	rp := RuleMatchParam{}
	//rule id == 79,
	rp.ProductId = 88001
	//order infos

	impl.MatchFirstPriorityRoutingRule(context.TODO(), rp)
	//ck
}

func Test_unmarshalMultiProductDefaultCriteria(t *testing.T) {
	byteData, _ := jsoniter.Marshal(&rule.MultiProductDefaultCriteria{
		WeightageCriteria: rule.MultiProductCriteriaInfo{
			LineCriteriaInfo: []rule.CriteriaInfo{
				{
					ResourceSubType: lfslib.C_FL,
				},
			},
		},
	})
	type args struct {
		defaultData []byte
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.MultiProductDefaultCriteria
		wantErr error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: jsoniter.Unmarshal error",
			args: args{
				defaultData: []byte(""),
			},
			want:    nil,
			wantErr: errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||..."),
		},
		{
			name: "case 2: normal result",
			args: args{
				defaultData: byteData,
			},
			want: &rule.MultiProductDefaultCriteria{
				CriteriaName: "weightage_criteria",
				WeightageCriteria: rule.MultiProductCriteriaInfo{
					LineCriteriaInfo: []rule.CriteriaInfo{
						{
							ResourceSubType:     lfslib.C_FL,
							DisplayResourceType: lfslib.LineSubTypeMap[lfslib.C_FL],
						},
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := unmarshalMultiProductDefaultCriteria(tt.args.defaultData)
			common.AssertResult(t, got, tt.want, srerr.With(srerr.DataErr, "", gotErr), srerr.With(srerr.DataErr, "", tt.wantErr))
		})
	}
}

func Test_openVolumeV2(t *testing.T) {
	type args struct {
		volumeRuleId   int64
		routingType    uint8
		isMultiProduct bool
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: routingType == rule.SPXRoutingType",
			args: args{
				volumeRuleId: 1,
				routingType:  rule.SPXRoutingType,
			},
			want: true,
		},
		{
			name: "case 2: routingType == rule.CBRoutingType",
			args: args{
				volumeRuleId:   1,
				routingType:    rule.CBRoutingType,
				isMultiProduct: true,
			},
			want: true,
		},
		{
			name: "case 3: routingType == rule.LocalRoutingType",
			args: args{
				volumeRuleId: 1,
				routingType:  rule.LocalRoutingType,
			},
			want: true,
		},
		{
			name: "case 4: other routingType",
			args: args{
				routingType: 4,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := openVolumeV2(tt.args.volumeRuleId, tt.args.routingType, tt.args.isMultiProduct); got != tt.want {
				t.Errorf("openVolumeV2() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseRuleData(t *testing.T) {
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteMultiProductDefaultCriteria, _ := jsoniter.Marshal(&rule.MultiProductDefaultCriteria{})
	byteMultiProductDisableInfo, _ := jsoniter.Marshal(&rule.MultiProductDisabledInfo{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	type args struct {
		data     *ruledata.RoutingRuleTab
		isDetail bool
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRule
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: UnmarshalRuleDetail error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules: []byte(""),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 2: StrCombinationSetting Unmarshal error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: []byte("1"),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("[]*rule.CombinationSetting: decode slice: expect [ or n, but found 1, error found in #1 byte of ...|1|..., bigger context ...|1|...")),
		},
		{
			name: "case 3: unmarshalMultiProductDefaultCriteria error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					IsMultiProduct:        true,
					RoutingType:           rule.CBRoutingType,
					StrDefaultCriteria:    []byte(""),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 4: unmarshalMultiProductDisabledInfo error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					IsMultiProduct:        true,
					RoutingType:           rule.CBRoutingType,
					StrDefaultCriteria:    byteMultiProductDefaultCriteria,
					StrDisabledInfo:       []byte(""),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 5: unmarshalDefaultCriteria error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    []byte(""),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 6: unmarshalDisabledInfo error",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       []byte(""),
				},
				isDetail: true,
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("[]*rule.DisabledInfo: decode slice: expect [ or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 7: normal MultiProduct result",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					IsMultiProduct:        true,
					RoutingType:           rule.CBRoutingType,
					StrDefaultCriteria:    byteMultiProductDefaultCriteria,
					StrDisabledInfo:       byteMultiProductDisableInfo,
				},
				isDetail: true,
			},
			want: &rule.RoutingRule{
				RuleDetails: &rule.RuleDetails{},
				MultiProductDefaultCriteria: &rule.MultiProductDefaultCriteria{
					CriteriaName: "weightage_criteria",
					WeightageCriteria: rule.MultiProductCriteriaInfo{
						DgGroupCriteriaInfo: rule.DgGroupCriteriaInfo{},
					},
				},
				MultiProductDisableInfo: &rule.MultiProductDisabledInfo{
					DgGroupDisableInfo: rule.DgGroupDisableInfo{},
				},
				IsMultiProduct: true,
			},
			wantErr: nil,
		},
		{
			name: "case 8: normal SingleProduct result",
			args: args{
				data: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
				},
				isDetail: true,
			},
			want: &rule.RoutingRule{
				RuleDetails: &rule.RuleDetails{},
				DefaultCriteria: &rule.DefaultCriteria{
					CriteriaName: "weightage_criteria",
				},
				DisabledInfo: []*rule.DisabledInfo{},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := ParseRuleData(tt.args.data, tt.args.isDetail)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func TestRebuildRuleData(t *testing.T) {
	ctx := context.TODO()
	type args struct {
		effectiveRule *rule.RoutingRule
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRuleParsed
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: invalid routing rule config",
			args: args{
				effectiveRule: &rule.RoutingRule{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.ParamErr, nil, "invalid routing rule config"),
		},
		{
			name: "case 2: normal CB+MultiProduct+IsVolumeRouting result",
			args: args{
				effectiveRule: &rule.RoutingRule{
					RuleDetails: &rule.RuleDetails{
						Rules: []*rule.Detail{
							{
								LineLimit: []*rule.LineLimit{
									{},
								},
								DgClassificationEnable:                true,
								DgRelated:                             1,
								MinVolumeEnable:                       true,
								MaxCapacityEnable:                     true,
								LineCheapestShippingFeePriorityEnable: true,
								LinePriorityEnable:                    true,
								MinWeightEnable:                       true,
								MaxWeightCapacityEnable:               true,
							},
						},
					},
					IsMultiProduct:              true,
					RoutingType:                 rule.CBRoutingType,
					IsVolumeRouting:             true,
					MultiProductDefaultCriteria: &rule.MultiProductDefaultCriteria{},
					MultiProductDisableInfo:     &rule.MultiProductDisabledInfo{},
				},
			},
			want: &rule.RoutingRuleParsed{
				AvailableLines: []string{""},
				DefaultPriorities: map[int32]map[rule.CriteriaType]map[string]int{
					4096: {
						rule.CriteriaWeightAge: map[string]int{},
					},
					8192: {
						rule.CriteriaWeightAge: map[string]int{},
					},
				},
				DefaultCriteriaType: rule.CriteriaWeightAge,
				RuleStepResourceList: []*rule.RuleStepResource{
					{
						RuleSteps: []*rule.ScheduleFactorAttr{
							{
								Name:       "DG",
								DgFlagData: &rule.DgFlagData{},
							},
							{
								Name: "MinVolumeV2",
								MinVolumeData: &rule.MinVolumeData{
									MinVolumes: map[string]int{
										"": 0,
									},
								},
							},
							{
								Name: "MaxCapacityV2",
								MaxCapacityData: &rule.MaxCapacityData{
									MaxCapacities: map[string]int{
										"": 0,
									},
								},
							},
							{
								Name: "LineCheapestShippingFee",
							},
							{
								Name: "LinePriority",
								LinePriorityData: &rule.LinePriorityData{
									LinePriorities: map[string]int32{
										"": 0,
									},
								},
							},
							{
								Name: "MinWeight",
								MinWeightData: &rule.MinWeightData{
									MinWeights: map[string]int{
										"": 0,
									},
								},
							},
							{
								Name: "MaxWeight",
								MaxWeightData: &rule.MaxWeightData{
									MaxWeights: map[string]int{
										"": 0,
									},
								},
							},
						},
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				LineToggle: map[string]*rule.Toggle{
					"": {
						On: []string{""},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "case 3: normal non-(CB+MultiProduct+IsVolumeRouting) result",
			args: args{
				effectiveRule: &rule.RoutingRule{
					RuleDetails: &rule.RuleDetails{
						Rules: []*rule.Detail{
							{
								LineLimit: []*rule.LineLimit{{}},
							},
						},
					},
					DefaultCriteria: &rule.DefaultCriteria{},
					DisabledInfo: []*rule.DisabledInfo{
						{
							LineList: []string{"1"},
						},
					},
				},
			},
			want: &rule.RoutingRuleParsed{
				AvailableLines:    []string{""},
				WmsAvailableLines: []string{""},
				RuleStepResourceList: []*rule.RuleStepResource{
					{},
				},
				LineToggle: map[string]*rule.Toggle{
					"": {
						On:  []string{""},
						Off: []string{"1"},
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := RebuildRuleData(ctx, tt.args.effectiveRule)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func TestCheckItemCategoryInRule(t *testing.T) {
	type args struct {
		itemCategoryLevel  int
		ruleCategoryIdList []int
		itemInfos          []*ItemCategoryInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "case 1: levelCategoryId = itemInfo.GlobalCategoryIdL1",
			args: args{
				itemCategoryLevel:  1,
				ruleCategoryIdList: []int{1},
				itemInfos: []*ItemCategoryInfo{
					{
						GlobalCategoryIdL1: 1,
					},
				},
			},
			want: true,
		},
		{
			name: "case 2: levelCategoryId = itemInfo.GlobalCategoryIdL2",
			args: args{
				itemCategoryLevel:  2,
				ruleCategoryIdList: []int{2},
				itemInfos: []*ItemCategoryInfo{
					{
						GlobalCategoryIdL2: 2,
					},
				},
			},
			want: true,
		},
		{
			name: "case 3: levelCategoryId = itemInfo.GlobalCategoryIdL3",
			args: args{
				itemCategoryLevel:  3,
				ruleCategoryIdList: []int{3},
				itemInfos: []*ItemCategoryInfo{
					{
						GlobalCategoryIdL3: 3,
					},
				},
			},
			want: true,
		},
		{
			name: "case 4: levelCategoryId = itemInfo.GlobalCategoryIdL4",
			args: args{
				itemCategoryLevel:  4,
				ruleCategoryIdList: []int{4},
				itemInfos: []*ItemCategoryInfo{
					{
						GlobalCategoryIdL4: 4,
					},
				},
			},
			want: true,
		},
		{
			name: "case 5: levelCategoryId = itemInfo.GlobalCategoryIdL5",
			args: args{
				itemCategoryLevel:  5,
				ruleCategoryIdList: []int{},
				itemInfos: []*ItemCategoryInfo{
					{
						GlobalCategoryIdL4: 5,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CheckItemCategoryInRule(tt.args.itemCategoryLevel, tt.args.ruleCategoryIdList, tt.args.itemInfos); got != tt.want {
				t.Errorf("CheckItemCategoryInRule() = %v, want %v", got, tt.want)
			}
		})
	}
}
