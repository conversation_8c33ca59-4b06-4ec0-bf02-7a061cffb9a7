package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

// selectByWeightage 基于权重选择一条资源线路
func selectByWeightage(ctx context.Context, lineIDs []string, lineInfoMap map[string]entity.AvailableLineBaseInfo) string {
	if len(lineIDs) == 0 {
		return ""
	}

	// 如果只有一条线路，直接返回
	if len(lineIDs) == 1 {
		return lineIDs[0]
	}

	// 将线路权重转换为所需格式
	weightageMap := make(map[string]uint)
	for _, lineID := range lineIDs {
		lineInfo, exist := lineInfoMap[lineID]
		if !exist {
			continue
		}

		weightage := lineInfo.Weightage
		if weightage <= 0 {
			continue
		}

		weightageMap[lineID] = uint(weightage)
	}

	// 如果没有有效权重，返回第一条线路
	if len(weightageMap) == 0 {
		return lineIDs[0]
	}

	logger.CtxLogInfof(ctx, "LH Weightage Map: %s", objutil.JsonString(weightageMap))

	// 按权重选择
	return mathutil.ChoiceByWeightage(ctx, weightageMap)
}

// ExtractResourceLineIDs 从Lane中提取指定资源类型的唯一线路ID列表
func ExtractResourceLineIDs(lanes []*rule.RoutingLaneInfo, resourceSubTypeList []int) []string {
	lineIDMap := make(map[string]struct{})

	for _, lane := range lanes {
		for _, lineInfo := range lane.LineList {
			if objutil.ContainInt(resourceSubTypeList, int(lineInfo.ResourceSubType)) {
				lineIDMap[lineInfo.LineId] = struct{}{}
			}
		}
	}

	lineIDs := make([]string, 0, len(lineIDMap))
	for lineID := range lineIDMap {
		lineIDs = append(lineIDs, lineID)
	}

	return lineIDs
}

// filterLanesByResourceSubType 根据资源类型和线路ID过滤Lane
func filterLanesByResourceSubType(lanes []*rule.RoutingLaneInfo, lineID string, resourceSubTypeList []int) []*rule.RoutingLaneInfo {
	filteredLanes := make([]*rule.RoutingLaneInfo, 0)
	for _, lane := range lanes {
		for _, lineInfo := range lane.LineList {
			if lineInfo.LineId == lineID && objutil.ContainInt(resourceSubTypeList, int(lineInfo.ResourceSubType)) {
				filteredLanes = append(filteredLanes, lane)
				break
			}
		}
	}
	return filteredLanes
}

// extractILHLineIDFromLane 从Lane中提取ILH线路ID
func extractILHLineIDFromLane(lane *rule.RoutingLaneInfo) string {
	for _, lineInfo := range lane.LineList {
		if objutil.ContainInt(lfslib.ILHLine, int(lineInfo.ResourceSubType)) {
			return lineInfo.LineId
		}
	}
	return ""
}
