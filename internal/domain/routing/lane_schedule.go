package routing

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

const (
	MatchRuleInvalid = "0"
	MatchRuleValid   = "1"

	lineToggleOffErrMsg = "available lines were toggle off in routing rule [id:%d]"
)

type RoutingServiceImpl struct {
	LaneSrv                 lane.LaneService
	RoutingPreCalFeeService PreCalFeeService
	ilhWeightCounter        volume_counter.ILHWeightCounter
}

func NewRoutingServiceImpl(
	laneSrv lane.LaneService,
	routingPreCalFeeService PreCalFeeService,
	ilhWeightCounter volume_counter.ILHWeightCounter,
) *RoutingServiceImpl {
	return &RoutingServiceImpl{
		LaneSrv:                 laneSrv,
		RoutingPreCalFeeService: routingPreCalFeeService,
		ilhWeightCounter:        ilhWeightCounter,
	}
}

type RoutingService interface {
	Routing(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, rule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error)
	RoutingWithExtraInfo(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, rule, spxRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool, extraInfo *ExtraInfo) ([]*rule.RoutingLaneInfo, *srerr.Error)
	CCModeILHRouting(ctx context.Context, productID int, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog) ([]*rule.RoutingLaneInfo, *srerr.Error)
	DisableCheck(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData) ([]*rule.RoutingLaneInfo, *srerr.Error)
	LoadRoutingLaneInfoByLaneCode(ctx context.Context, laneCode string) (*rule.RoutingLaneInfo, *srerr.Error)
}

// @core
func (rs *RoutingServiceImpl) Routing(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo,
	matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return rs.routing(ctx, productID, isMultiProduct, availableLanes, matchedRule, nil, orderData, logEntry, isOnlySPX, nil)
}

func (rs *RoutingServiceImpl) RoutingWithExtraInfo(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo,
	matchedRule, matchedSPXRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool, extraInfo *ExtraInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return rs.routing(ctx, productID, isMultiProduct, availableLanes, matchedRule, matchedSPXRule, orderData, logEntry, isOnlySPX, extraInfo)
}

// @core
func (rs *RoutingServiceImpl) routing(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo,
	matchedRule, matchedSPXRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog, isOnlySPX bool, extraInfo *ExtraInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {

	task := new(routingTask)
	task.routingData = new(rule.RoutingData)
	task.routingData.ProductID = productID
	task.routingData.Rule = matchedRule
	task.routingData.OriginalLaneInfos = availableLanes
	task.routingData.ValidateLaneList = availableLanes
	task.routingData.CreateOrderData = orderData
	task.routingData.NotOnlySPX = isOnlySPX

	if extraInfo != nil {
		task.routingData.BusinessType = extraInfo.BusinessType
		task.routingData.ReCalcFee = extraInfo.ReCalcFee
		task.routingData.OldLineFeeMap = extraInfo.OldLineFeeMap
	}

	availLanes, err := rs.ProhibitedCheck(ctx, matchedRule, availableLanes, isMultiProduct, orderData, logEntry)
	if err != nil {
		prometheusutil.ReportSmartRoutingSuccess(productID, matchedRule.ID, MatchRuleInvalid)
		return nil, err
	}
	// matchedSPXRule不是nil表示是vncb调度，需要再做vncb的3pl toggle调度
	if matchedSPXRule != nil && configutil.IsOpenPreVnCbToggleCheck(ctx) {
		monitoring.ReportSuccess(ctx, monitoring.CatSelectLaneApi, monitoring.PreVnCbToggleCheck, "")
		tempLogEntry := logEntry
		// 二段式的isMultiProduct为false
		spxAvailLanes, err1 := rs.ProhibitedCheck(ctx, matchedSPXRule, RewriteLaneInfo(availLanes), false, orderData, logEntry)
		if err1 != nil {
			logger.CtxLogErrorf(ctx, "pre vncb toggle check error, err=%v, matchedSPXRuleId=%v", err1, matchedSPXRule.ID)
			prometheusutil.ReportSmartRoutingSuccess(productID, matchedRule.ID, MatchRuleInvalid)
			monitoring.ReportError(ctx, monitoring.CatSelectLaneApi, monitoring.PreVnCbToggleCheck, fmt.Sprintf("pre vncb toggle check error, err=%v", err1))
			return nil, err1
		}
		// 根据spxAvailLanes的DLM类型的lineIdList过滤availLanes
		availLanes = FilterBySpxAvailableLane(ctx, availLanes, spxAvailLanes)
		// 保存根据spxAvailLanes过滤后的结果
		after := make(map[string]map[string][]string)
		for _, laneInfo := range availLanes {
			//afterLaneInfos = append(afterLaneInfos, laneInfo.LaneCode)
			for _, line := range laneInfo.LineList {
				key := lfslib.LineSubTypeMap[line.ResourceSubType]
				if after[laneInfo.LaneCode] == nil {
					after[laneInfo.LaneCode] = make(map[string][]string)
				}
				after[laneInfo.LaneCode][key] = append(after[laneInfo.LaneCode][key], line.LineId)
			}
		}
		setProhibitFilterLogsForVnCb(tempLogEntry, logEntry, after)
	}
	prometheusutil.ReportSmartRoutingSuccess(productID, matchedRule.ID, MatchRuleValid)
	task.routingData.ValidateLaneList = availLanes
	// 提前计算运费
	lineFeeMap := rs.RoutingPreCalFeeService.RoutingPreCalcFee(ctx, int64(productID), task.retrieveLineFromProcedureLaneList(), task.routingData)
	logEntry.LineFeeMap = lineFeeMap
	task.routingData.PreLineFeeMap = lineFeeMap

	if len(availLanes) == 1 {
		return availLanes, nil
	}

	var lineListInput []*rule.LineInfo
	var validLanes []*rule.RoutingLaneInfo

	setNormalSchedulingLogs(logEntry, matchedRule, task)
	defer func() {
		if logEntry != nil {
			logEntry.RoutingResult.SoftCriteriaFilterProcess.After = getDebugLaneInfo(validLanes)
		}
	}()
	factorNameCountMap := make(map[string]int)
	// 2. filter by soft criteria
	//记筛选的顺序，如先LM后FL
	var resourceSeq []string
	for _, ruleEntry := range matchedRule.RuleStepResourceList {
		if ruleEntry.ResourceSubType == 0 {
			continue
		}

		resourceName := lfslib.LineSubTypeMap[ruleEntry.ResourceSubType]
		resourceSeq = append(resourceSeq, resourceName)
		//line may repeat under multiple lanes,
		logger.CtxLogInfof(ctx, "normal.select.%v.begin", ruleEntry.DisplayResourceType)

		// len(lineListInput) >= 1 ;
		lineListInput = task.retrieveLineFromProcedureLaneList()[ruleEntry.ResourceSubType]

		// len(validLines) >= 1 ;
		validLines, logFactorList := task.filterLines(ctx, lineListInput, ruleEntry.RuleSteps, ruleEntry.DisplayResourceType, factorNameCountMap)
		validLanes = task.GetAvailableLaneBylines(validLines)

		before := getDebugLaneInfo(task.routingData.ValidateLaneList)
		after := getDebugLaneInfo(validLanes)

		logger.CtxLogInfof(ctx, "normal.select.%v.end | in=%v | out = %v", ruleEntry.DisplayResourceType, before, after)
		//设置软性调度过程数据
		SetSoftCriteriaFilterProcess(ctx, logEntry, ruleEntry.ResourceSubType, ruleEntry.DisplayResourceType, logFactorList, before, after)
		if len(validLanes) == 0 {
			return nil, srerr.New(srerr.NoAvailableLane, nil, "No available lane after filter by soft criteria")
		}
		//
		if len(validLanes) == 1 {
			return validLanes, nil
		}
		//len(validlanes) > 1
		task.routingData.ValidateLaneList = validLanes
	}
	if logEntry != nil {
		result := strings.Join(resourceSeq, ";")
		logEntry.RoutingSeq = result
	}
	//len(validlanes) > 1
	//step3  FilterDefaultCriteria,todo ,
	retLane := task.defaultScheduling(ctx, matchedRule, logEntry, factorNameCountMap)
	for key, value := range factorNameCountMap {
		rs.reportPrometheus(ctx, constant.PrometheusMetricFactor, productID, key, value)
	}
	if retLane == nil {
		return nil, srerr.New(srerr.NoAvailableLane, nil, "No available lane after filter by default criteria")
	}
	return retLane, nil
}

func (rs *RoutingServiceImpl) ProhibitedCheck(ctx context.Context, matchedRule *rule.RoutingRuleParsed, availableLanes []*rule.RoutingLaneInfo,
	isMultiProduct bool, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog) ([]*rule.RoutingLaneInfo, *srerr.Error) {

	if len(availableLanes) == 0 {
		return nil, srerr.New(srerr.NoAvailableLane, nil, "no available lane before routing")
	}

	// 1. filter by prohibited
	var availLanes []*rule.RoutingLaneInfo
	if isMultiProduct && matchedRule.RoutingType == rule.CBRoutingType {
		availLanes = rs.multiProductFilterByProhibitedLine(ctx, availableLanes, matchedRule)
	} else {
		availLanes = rs.filterByProhibitedLine(ctx, availableLanes, matchedRule, orderData, logEntry)
	}
	if len(availLanes) == 0 {
		return nil, srerr.New(srerr.NoAvailableLane, matchedRule, lineToggleOffErrMsg, matchedRule.ID)
	}
	return availLanes, nil
}

func SetSoftCriteriaFilterProcess(ctx context.Context, logEntry *routing_log.RoutingLog, resourceSubType int32,
	displayResourceType string, combination []routing_log.SchedulingFactorCombination, before, after []string,
) {

	if logEntry == nil {
		return
	}

	for _, factory := range combination {
		if factory.FactorName != schedule_factor.MaxCapacityV2Name && factory.FactorName != schedule_factor.MinVolumeV2Name &&
			factory.FactorName != schedule_factor.MaxCodCapacityV2Name && factory.FactorName != schedule_factor.MaxBulkyCapacityV2Name &&
			factory.FactorName != schedule_factor.MaxHighValueCapacityV2Name && factory.FactorName != schedule_factor.MaxDgCapacityV2Name {
			continue
		}
		m := factory.ProcessData.(map[string]string)
		parseUint, err := strconv.ParseUint(m[volumerouting.BRVolumeRule], 10, 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Get volume rule id failed %+v", err)
		}
		logEntry.VolumeRuleID = parseUint
		break
	}

	logEntry.RoutingResult.SoftCriteriaFilterProcess.StageList = append(logEntry.RoutingResult.SoftCriteriaFilterProcess.StageList, routing_log.SoftCriteriaRuleStage{
		ResourceSubType:        resourceSubType,
		DisplayResourceSubType: displayResourceType,
		FactorCombination:      combination,
		Before:                 before,
		After:                  after,
	})

}

func (rs *RoutingServiceImpl) reportPrometheus(ctx context.Context, name string, productId int, factorName string, count int) {
	data := make(map[string]string, 3)

	data["product_id"] = strconv.Itoa(productId)
	data["factor_name"] = factorName
	data["count"] = strconv.Itoa(count)

	err := prometheusutil.PrometheusReportHistogram(ctx, name, data, 1)
	if err != nil {
		logger.CtxLogErrorf(ctx, "PrometheusReportHistogram error %+v", err)
	}
}

// @core
func (rs *RoutingServiceImpl) DisableCheck(ctx context.Context, productID int, isMultiProduct bool, availableLanes []*rule.RoutingLaneInfo, matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	task := new(routingTask)
	task.routingData = new(rule.RoutingData)
	task.routingData.ProductID = productID
	task.routingData.Rule = matchedRule
	task.routingData.OriginalLaneInfos = availableLanes
	task.routingData.ValidateLaneList = availableLanes
	task.routingData.Rule = matchedRule

	var availLanes []*rule.RoutingLaneInfo
	if isMultiProduct {
		availLanes = rs.multiProductFilterByProhibitedLine(ctx, availableLanes, matchedRule)
	} else {
		availLanes = rs.filterByProhibitedLine(ctx, availableLanes, matchedRule, orderData, nil)
	}
	if len(availLanes) == 0 {
		return availableLanes, srerr.New(srerr.NoAvailableLane, matchedRule, lineToggleOffErrMsg, matchedRule.ID)
	}

	return availLanes, nil
}

// @core
func (rs *RoutingServiceImpl) LoadRoutingLaneInfoByLaneCode(ctx context.Context, laneCode string) (*rule.RoutingLaneInfo, *srerr.Error) {
	laneInfo, err := rs.LaneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
	if err != nil {
		return nil, err
	}

	routingLaneInfo := &rule.RoutingLaneInfo{
		LaneCode: laneCode,
	}

	destPort := laneInfo.GetExportThirdPartyJoint()
	if destPort != nil {
		routingLaneInfo.DestinationPort = destPort.SiteID
	}

	for _, lineInfo := range laneInfo.Lines {
		var dgRelated int
		if lineInfo.NeedDG() {
			dgRelated = 1
		}
		routingLaneInfo.LineList = append(routingLaneInfo.LineList, &rule.LineInfo{
			LineId:          lineInfo.LineID,
			ResourceSubType: int32(lineInfo.SubType),
			ResourceId:      lineInfo.LineID,
			DgRelated:       dgRelated,
		})
	}

	return routingLaneInfo, nil
}

func (rt *routingTask) filterLines(ctx context.Context, lines []*rule.LineInfo, filterStep []*rule.ScheduleFactorAttr, currentResourceType string, factorNameCountMap map[string]int) ([]*rule.LineInfo, []routing_log.SchedulingFactorCombination) {
	var logFactorList []routing_log.SchedulingFactorCombination
	var processData interface{}
	filteredLines := lines

	for index, factor := range filterStep {
		monitoring.ReportSuccess(ctx, monitoring.CatScheduledFactor, factor.Name, "")
		schedulerFactor := schedule_factor.GetScheduleFactor(factor.Name)
		if factor.Name == schedule_factor.MaxCodCapacity || factor.Name == schedule_factor.MaxBulkyCapacity ||
			factor.Name == schedule_factor.MaxHighValueCapacity || factor.Name == schedule_factor.MaxDgCapacity {
			schedulerFactor = schedule_factor.GetScheduleFactor(schedule_factor.MaxCapacity)
		} else if factor.Name == schedule_factor.MaxCodCapacityV2Name || factor.Name == schedule_factor.MaxBulkyCapacityV2Name ||
			factor.Name == schedule_factor.MaxHighValueCapacityV2Name || factor.Name == schedule_factor.MaxDgCapacityV2Name {
			schedulerFactor = schedule_factor.GetScheduleFactor(schedule_factor.MaxCapacityV2Name)
		}
		factorNameCountMap[factor.Name]++
		//获取属性数据，
		lineListDebug := filteredLines
		filteredLines, processData = schedulerFactor.FilterLine(ctx, filteredLines, rt.routingData, factor)
		//记录一下命中的volume rule id
		before := getDebugInfoForLineList(lineListDebug)
		after := getDebugInfoForLineList(filteredLines)
		actualLeft := after

		logger.CtxLogInfof(ctx, "factor|linetype=%v_factor-%vth|name=%v,input: %v | output %v", currentResourceType, index, factor.Name, before, after)
		logFactorList = append(logFactorList, routing_log.SchedulingFactorCombination{
			Priority:   factor.Priority,
			StepType:   schedule_factor.GetStepTypeFromName(factor.Name), //兼容routingLog ,
			FactorName: factor.Name,
			//todo,
			ProcessData:       processData,
			Before:            before,
			After:             after,
			ActualLeftLineIds: actualLeft,
		})

		//filtered_lines == 0 , 配置引起的异常，
		if len(filteredLines) == 0 {
			filteredLines = lines
		}

		if len(filteredLines) == 1 {
			return filteredLines, logFactorList
		}
	}
	return filteredLines, logFactorList
}

func (rt *routingTask) GetAvailableLaneBylines(lines []*rule.LineInfo) []*rule.RoutingLaneInfo {
	var availableLanes []*rule.RoutingLaneInfo
	for _, laneInfo := range rt.routingData.ValidateLaneList {
		containFlag := false
		for _, lineInfo := range laneInfo.LineList {
			for _, line := range lines {
				if line.ResourceId == lineInfo.ResourceId {
					containFlag = true
					break
				}
			}
			if containFlag {
				availableLanes = append(availableLanes, laneInfo)
				break
			}
		}
	}
	return availableLanes
}

func (rt *routingTask) retrieveLineFromProcedureLaneList() map[int32][]*rule.LineInfo {
	linesByResourceType := map[int32][]*rule.LineInfo{}
	lineContains := map[string]bool{}

	for _, laneInfo := range rt.routingData.ValidateLaneList {
		for _, line := range laneInfo.LineList {
			key := fmt.Sprintf("%v_%v", line.ResourceSubType, line.ResourceId)
			_, ok := lineContains[key]
			if ok {
				continue
			}
			linesByResourceType[line.ResourceSubType] = append(linesByResourceType[line.ResourceSubType], line)
			lineContains[key] = true
		}
	}

	return linesByResourceType
}

type routingTask struct {
	routingData *rule.RoutingData
}

func RewriteLaneInfo(routingLanes []*rule.RoutingLaneInfo) []*rule.RoutingLaneInfo {
	//build up routing lanes, only containing DLM
	var spxRoutingLanes []*rule.RoutingLaneInfo
	for _, routingLane := range routingLanes {
		var dlmLineList []*rule.LineInfo
		for _, line := range routingLane.LineList {
			if line.RealResourceSubType == lfslib.C_DLM {
				dlmLineList = append(dlmLineList, &rule.LineInfo{
					ResourceId:          line.ResourceId,
					LineId:              line.ResourceId,
					ResourceSubType:     lfslib.C_DLM,
					RealResourceSubType: lfslib.C_DLM,
					DGFlag:              line.DGFlag,
					DgRelated:           line.DgRelated,
				})
				break
			}
		}
		spxRoutingLane := &rule.RoutingLaneInfo{
			LaneCode:      routingLane.LaneCode,
			LaneCodeGroup: routingLane.LaneCodeGroup,
			ServiceCode:   routingLane.ServiceCode,
			LineList:      dlmLineList,
		}
		spxRoutingLanes = append(spxRoutingLanes, spxRoutingLane)
	}

	return spxRoutingLanes
}

// FilterBySpxAvailableLane 根据spxAvailLanes的DLM类型的lineIdList过滤availLanes
func FilterBySpxAvailableLane(ctx context.Context, availLanes []*rule.RoutingLaneInfo, spxAvailLanes []*rule.RoutingLaneInfo) []*rule.RoutingLaneInfo {
	spxDLMLineIdMap := make(map[string]bool)
	for _, spxAvailLane := range spxAvailLanes {
		if spxAvailLane == nil {
			continue
		}
		for _, lineInfo := range spxAvailLane.LineList {
			if lineInfo.RealResourceSubType == lfslib.C_DLM {
				spxDLMLineIdMap[lineInfo.LineId] = true
			}
		}
	}
	logger.CtxLogInfof(ctx, "spxDLMLineIdMap = %v", spxDLMLineIdMap)
	var newAvailLanes []*rule.RoutingLaneInfo
	for _, availLane := range availLanes {
		if availLane == nil {
			continue
		}
		passVnCbCheck := true
		for _, lineInfo := range availLane.LineList {
			if lineInfo.RealResourceSubType == lfslib.C_DLM {
				// dlm的line_id不存在则表示这条lane不可用
				if _, ok := spxDLMLineIdMap[lineInfo.LineId]; !ok {
					passVnCbCheck = false
					break
				}
			}
		}
		if passVnCbCheck {
			newAvailLanes = append(newAvailLanes, availLane)
		}
	}
	before := make(map[string]map[string][]string)
	after := make(map[string]map[string][]string)
	for _, laneInfo := range availLanes {
		if laneInfo == nil {
			continue
		}
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if before[laneInfo.LaneCode] == nil {
				before[laneInfo.LaneCode] = make(map[string][]string)
			}
			before[laneInfo.LaneCode][key] = append(before[laneInfo.LaneCode][key], line.LineId)
		}
	}
	for _, laneInfo := range newAvailLanes {
		for _, line := range laneInfo.LineList {
			key := lfslib.LineSubTypeMap[line.ResourceSubType]
			if after[laneInfo.LaneCode] == nil {
				after[laneInfo.LaneCode] = make(map[string][]string)
			}
			after[laneInfo.LaneCode][key] = append(after[laneInfo.LaneCode][key], line.LineId)
		}
	}
	logger.CtxLogInfof(ctx, "FilterBySpxAvailableLane|before lane list: %v, after lane list: %v", before, after)
	return newAvailLanes
}
