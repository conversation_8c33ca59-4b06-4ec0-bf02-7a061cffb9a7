package sync_lfs_order

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type OrderSyncRepo interface {
	ReadILHForecastOrderIteration(ctx context.Context, productId int, day time.Time) ([]*forecastentity.LHSOrderInfo, *srerr.Error)
}

type OrderSyncRepoImpl struct {
}

func NewOrderSyncRepoImpl() *OrderSyncRepoImpl {
	return &OrderSyncRepoImpl{}
}

func (s *OrderSyncRepoImpl) ReadILHForecastOrderIteration(ctx context.Context, productId int, day time.Time) ([]*forecastentity.LHSOrderInfo, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, LhsLogisticOrderTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, LhsLogisticOrderTabHook, err)
	}

	startTime := day.Unix()
	endTime := day.AddDate(0, 0, 1).Unix()
	orderQuery := db.Where("lho_status != ? AND ctime >= ? AND ctime < ?", LhoStatusCanceled, startTime, endTime)

	if productId != 0 {
		orderQuery = orderQuery.Where("multi_product_id = ?", productId)
	}

	var lhsOrderList []*LhsLogisticOrderTab
	if err := orderQuery.Table(LhsLogisticOrderTabName).Find(&lhsOrderList).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	lhsOrderInfoMap := make(map[string]*forecastentity.LHSOrderInfo, len(lhsOrderList))
	lhsTnList := make([]string, 0, len(lhsOrderList))
	for _, lhsOrder := range lhsOrderList {
		lhsTnList = append(lhsTnList, lhsOrder.LhsTn)
		lhsOrderInfoMap[lhsOrder.LhsTn] = &forecastentity.LHSOrderInfo{
			LhsTn:          lhsOrder.LhsTn,
			MultiProductId: int(lhsOrder.MultiProductId),
			DgType:         int(lhsOrder.DgType),
			DgChange:       int(lhsOrder.DgChange),
			IlhGroupId:     lhsOrder.IlhGroupId,
			ServiceCode:    lhsOrder.ServiceCode,
			Ctime:          int(lhsOrder.Ctime),
		}
	}

	lhsPackageList := make([]*LhsLogisticOrderPackageTab, 0)
	db, err = dbutil.SlaveDB(ctx, LhsLogisticOrderPackageTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, LhsLogisticOrderTabHook, err)
	}
	packageQuery := db.Where("lhs_tn IN (?)", lhsTnList)
	if err := packageQuery.Table(LhsLogisticOrderPackageTabName).Find(&lhsPackageList).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	for _, packageInfo := range lhsPackageList {
		orderInfo, exist := lhsOrderInfoMap[packageInfo.LhsTn]
		if !exist {
			continue
		}

		// 获取Carton中的Parcel Quantity信息
		db, err = dbutil.SlaveDB(ctx, LhsLogisticMappingTabHook)
		if err != nil {
			return nil, srerr.With(srerr.DatabaseErr, LhsLogisticMappingTabHook, err)
		}

		var sloList []string
		if err := db.Select("ref_tn").Where("lho_id = ? AND bind_status = 1", packageInfo.LhoId).Table(LhsLogisticMappingTableName).
			Scan(&sloList).GetError(); err != nil {
			return nil, srerr.With(srerr.DatabaseErr, nil, err)
		}

		orderInfo.TwsCode = packageInfo.SenderId
		orderInfo.ActualWeight = int(packageInfo.ActualWeight.IntPart())
		orderInfo.SloList = sloList
		orderInfo.ParcelQuantity = len(sloList)
	}

	lhsOrderInfoList := make([]*forecastentity.LHSOrderInfo, 0)
	for _, lhsOrder := range lhsOrderInfoMap {
		lhsOrderInfoList = append(lhsOrderInfoList, lhsOrder)
	}

	return lhsOrderInfoList, nil
}
