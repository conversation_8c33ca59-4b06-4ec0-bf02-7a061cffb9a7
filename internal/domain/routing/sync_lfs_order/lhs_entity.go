package sync_lfs_order

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"github.com/shopspring/decimal"
)

var (
	LhsLogisticOrderTabHook        = &LhsLogisticOrderTab{}
	LhsLogisticOrderPackageTabHook = &LhsLogisticOrderPackageTab{}
	LhsLogisticMappingTabHook      = &LhsLogisticMappingTab{}
)

const (
	LhsLogisticOrderTabName        = "logistic_order_tab"
	LhsLogisticOrderPackageTabName = "logistic_shipment_package_tab"
	LhsLogisticMappingTableName    = "logistic_mapping_tab"
)

type OrderStatus uint8

// lho status
const (
	LhoStatusInit                  OrderStatus = 0
	LhoStatusCreated               OrderStatus = 1
	LhoStatusCreateException       OrderStatus = 2
	LhoStatusCanceled              OrderStatus = 3
	LhoStatusCartonUnbound         OrderStatus = 4
	LhoStatusReceived              OrderStatus = 5
	LhoStatusExportClearancePassed OrderStatus = 6
	LhoStatusTransporting          OrderStatus = 7
	LhoStatusDestRegionArrived     OrderStatus = 8
	LhoStatusImportClearancePassed OrderStatus = 9
	LhoStatusDelivered             OrderStatus = 10
	LhoStatusImportCustomsClearing OrderStatus = 11
)

type LhsLogisticOrderTab struct {
	Id             uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	LhoId          uint64 `gorm:"column:lho_id;NOT NULL" json:"lho_id"`
	LhsTn          string `gorm:"column:lhs_tn;NOT NULL" json:"lhs_tn"`
	LhoStatus      uint8  `gorm:"column:lho_status;NOT NULL" json:"lho_status"`
	PreStatus      uint8  `gorm:"column:pre_status;NOT NULL" json:"pre_status"`
	Client         uint8  `gorm:"column:client;NOT NULL" json:"client"`
	PackageNo      string `gorm:"column:package_no;NOT NULL" json:"package_no"`
	MultiProductId uint32 `gorm:"column:multi_product_id;NOT NULL" json:"multi_product_id"`
	LhProductId    string `gorm:"column:lh_product_id;NOT NULL" json:"lh_product_id"`
	LaneCode       string `gorm:"column:lane_code" json:"lane_code"`
	DgType         uint8  `gorm:"column:dg_type;NOT NULL" json:"dg_type"`
	DgChange       uint8  `gorm:"column:dg_change;NOT NULL" json:"dg_change"`
	IlhGroupId     string `gorm:"column:ilh_group_id;NOT NULL" json:"ilh_group_id"`
	IlhGroupChange uint8  `gorm:"column:ilh_group_change;NOT NULL" json:"ilh_group_change"`
	TransportType  uint8  `gorm:"column:transport_type;NOT NULL" json:"transport_type"`
	ServiceCode    string `gorm:"column:service_code;NOT NULL" json:"service_code"`
	PackageType    uint8  `gorm:"column:package_type;NOT NULL" json:"package_type"`
	Ctime          uint32 `gorm:"autoCreateTime;column:ctime;NOT NULL" json:"ctime"`
	Mtime          uint32 `gorm:"autoUpdateTime;column:mtime;NOT NULL" json:"mtime"`
}

func (*LhsLogisticOrderTab) TableName() string {
	return LhsLogisticOrderTabName
}

func (rs *LhsLogisticOrderTab) DBForRead() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (rs *LhsLogisticOrderTab) DBForWrite() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (rs *LhsLogisticOrderTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        rs.Id,
		ModelName: rs.TableName(),
	}
}

type LhsLogisticOrderPackageTab struct {
	Id                 uint64          `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	LhoId              uint64          `gorm:"column:lho_id;NOT NULL" json:"lho_id"`
	LhsTn              string          `gorm:"column:lhs_tn;NOT NULL" json:"lhs_tn"`
	Packer             string          `gorm:"column:packer;NOT NULL" json:"packer"`
	PackageNo          string          `gorm:"column:package_no;NOT NULL" json:"package_no"`
	Height             decimal.Decimal `gorm:"column:height;default:0;NOT NULL" json:"height"`
	Width              decimal.Decimal `gorm:"column:width;default:0" json:"width"`
	Length             decimal.Decimal `gorm:"column:length;default:0;NOT NULL" json:"length"`
	ActualWeight       decimal.Decimal `gorm:"column:actual_weight;default:0;NOT NULL" json:"actual_weight"`
	VolumetricWeight   decimal.Decimal `gorm:"column:volumetric_weight;default:0" json:"volumetric_weight"`
	ChargeableWeight   decimal.Decimal `gorm:"column:chargeable_weight;default:0;NOT NULL" json:"chargeable_weight"`
	SenderId           string          `gorm:"column:sender_id;default:0;NOT NULL" json:"sender_id"`
	SenderRegion       string          `gorm:"column:sender_region;NOT NULL" json:"sender_region"`
	SenderState        string          `gorm:"column:sender_state;NOT NULL" json:"sender_state"`
	SenderCity         string          `gorm:"column:sender_city" json:"sender_city"`
	SenderStreet       string          `gorm:"column:sender_street" json:"sender_street"`
	SenderDistrict     string          `gorm:"column:sender_district;NOT NULL" json:"sender_district"`
	SenderAddress      string          `gorm:"column:sender_address" json:"sender_address"`
	SenderLocationId   uint64          `gorm:"column:sender_location_id" json:"sender_location_id"`
	ReceiverId         string          `gorm:"column:receiver_id" json:"receiver_id"`
	ReceiverRegion     string          `gorm:"column:receiver_region" json:"receiver_region"`
	ReceiverState      string          `gorm:"column:receiver_state" json:"receiver_state"`
	ReceiverCity       string          `gorm:"column:receiver_city" json:"receiver_city"`
	ReceiverDistrict   string          `gorm:"column:receiver_district" json:"receiver_district"`
	ReceiverStreet     string          `gorm:"column:receiver_street" json:"receiver_street"`
	ReceiverAddress    string          `gorm:"column:receiver_address" json:"receiver_address"`
	ReceiverLocationId uint64          `gorm:"column:receiver_location_id" json:"receiver_location_id"`
	PackTime           uint64          `gorm:"column:pack_time;NOT NULL" json:"pack_time"`
	Ctime              uint32          `gorm:"autoCreateTime;column:ctime;NOT NULL" json:"ctime"`
	Mtime              uint32          `gorm:"autoUpdateTime;column:mtime;NOT NULL" json:"mtime"`
}

func (*LhsLogisticOrderPackageTab) TableName() string {
	return LhsLogisticOrderPackageTabName
}

func (*LhsLogisticOrderPackageTab) DBForRead() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (*LhsLogisticOrderPackageTab) DBForWrite() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (rs *LhsLogisticOrderPackageTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        rs.Id,
		ModelName: rs.TableName(),
	}
}

type LhsLogisticMappingTab struct {
	Id         uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	LhoId      uint64 `gorm:"column:lho_id;NOT NULL" json:"lho_id"`
	RefTn      string `gorm:"column:ref_tn;NOT NULL" json:"ref_tn"`
	BindStatus uint8  `gorm:"column:bind_status;NOT NULL" json:"bind_status"`
	Ctime      uint32 `gorm:"autoCreateTime;column:ctime;NOT NULL" json:"ctime"`
	Mtime      uint32 `gorm:"autoUpdateTime;column:mtime;NOT NULL" json:"mtime"`
}

func (*LhsLogisticMappingTab) TableName() string {
	return LhsLogisticMappingTableName
}

func (*LhsLogisticMappingTab) DBForRead() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (*LhsLogisticMappingTab) DBForWrite() dbutil.DBTag {
	return dbutil.SscLhsCidRead
}

func (rs *LhsLogisticMappingTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        rs.Id,
		ModelName: rs.TableName(),
	}
}
