package routing

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

// @core
func (rs *RoutingServiceImpl) CCModeILHRouting(ctx context.Context, productID int, availableLanes []*rule.RoutingLaneInfo,
	matchedRule *rule.RoutingRuleParsed, orderData *rule.SmartRoutingOrderData, logEntry *routing_log.RoutingLog) ([]*rule.RoutingLaneInfo, *srerr.Error) {

	// 标记CC Routing Mode有流量进入
	logger.CtxLogDebugf(ctx, "use cc routing mode")

	task := &routingTask{routingData: &rule.RoutingData{
		ProductID:         productID,
		Rule:              matchedRule,
		OriginalLaneInfos: availableLanes,
		ValidateLaneList:  availableLanes,
		CreateOrderData:   orderData,
	}}

	metricsLabel := map[string]string{
		"product_id": strconv.Itoa(productID),
		"rule_id":    strconv.Itoa(int(matchedRule.ID)),
		"status":     "1",
	}

	if len(availableLanes) == 0 {
		metricsLabel["status"] = "0"
		_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingSuccess, metricsLabel)
		return nil, srerr.New(srerr.NoAvailableLane, task, "no available lane before routing")
	}

	// 1. filter by prohibited
	// 1.1 3PL Toggle
	availLanes := rs.filterByProhibitedLine(ctx, availableLanes, matchedRule, orderData, logEntry)
	if len(availLanes) == 0 {
		metricsLabel["status"] = "0"
		_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingSuccess, metricsLabel)
		return nil, srerr.New(srerr.NoAvailableLane, nil, "Filtered Out By 3PL Toggle %v", productID)
	}
	// 1.2 Combination Settings
	if orderData.IsCCRoutingMode() {
		availLanes = rs.filterByAvailableCombination(availLanes, matchedRule, orderData)
	}
	if len(availLanes) == 0 {
		metricsLabel["status"] = "0"
		_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingSuccess, metricsLabel)
		return nil, srerr.New(srerr.NoAvailableLane, nil, "Filtered Out By Combination Settings %v", productID)
	}

	_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingSuccess, metricsLabel)
	if len(availLanes) == 1 {
		return availLanes, nil
	}
	task.routingData.ValidateLaneList = availLanes

	var lineListInput []*rule.LineInfo
	var validLanes []*rule.RoutingLaneInfo

	setNormalSchedulingLogs(logEntry, matchedRule, task)
	defer func() {
		if logEntry != nil {
			logEntry.RoutingResult.SoftCriteriaFilterProcess.After = getDebugLaneInfo(validLanes)
		}
	}()
	// 2. filter by soft criteria
	factorNameCountMap := make(map[string]int)
	for index, factor := range matchedRule.IlhRuleSteps {
		schedulerFactor := schedule_factor.GetScheduleFactor(factor.Name)
		factorNameCountMap[factor.Name]++

		if factor.CombinationMode {
			validLanes = task.routingData.ValidateLaneList
			// Combination模式只有CC Routing Mode才需要走
			if orderData.IsCCRoutingMode() {
				var processData interface{}
				validLanes, processData = schedulerFactor.FilterLane(ctx, task.routingData.ValidateLaneList, task.routingData, factor)
				before := getDebugLaneInfo(task.routingData.ValidateLaneList)
				after := getDebugLaneInfo(validLanes)
				logger.CtxLogInfof(ctx, "SoftCriteriaStep|step=%v,combination_mode,name=%v|input:%v|output:%v|process_data:%s",
					index, factor.Name, before, after, objutil.JsonString(processData))
			}
		} else {
			// 对于Import ILH，只有CC Routing Mode下才需要走
			if factor.ResourceSubType == lfslib.C_M_ILH && !orderData.IsCCRoutingMode() {
				validLanes = task.routingData.ValidateLaneList
			} else {
				lineListInput = task.retrieveLineFromProcedureLaneList()[factor.ResourceSubType]
				filteredLines, processData := schedulerFactor.FilterLine(ctx, lineListInput, task.routingData, factor)
				validLanes = task.GetAvailableLaneBylines(filteredLines)

				before := getDebugInfoForLineList(lineListInput)
				after := getDebugInfoForLineList(filteredLines)
				logger.CtxLogInfof(ctx, "SoftCriteriaStep|step=%v,line_type=%v,name=%v|input:%v|output:%v|process_data:%s",
					index, factor.ResourceSubType, factor.Name, before, after, objutil.JsonString(processData))
			}
		}

		//设置软性调度过程数据
		if len(validLanes) == 0 {
			return nil, srerr.New(srerr.NoAvailableLane, nil, "No available lane after filter by soft criteria")
		}

		if len(validLanes) == 1 {
			return validLanes, nil
		}

		task.routingData.ValidateLaneList = validLanes
	}

	//step3  FilterDefaultCriteria
	for key, value := range factorNameCountMap {
		rs.reportPrometheus(ctx, constant.PrometheusMetricFactor, productID, key, value)
	}

	if len(validLanes) == 0 {
		return nil, srerr.New(srerr.NoAvailableLane, nil, "No available lane after filter by default criteria")
	}

	return validLanes, nil
}
