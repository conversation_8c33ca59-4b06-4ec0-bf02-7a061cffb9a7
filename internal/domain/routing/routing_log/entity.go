package routing_log

import (
	"fmt"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type RoutingLog struct {
	RequestId                  string                            `json:"request_id"`
	ProductId                  int                               `json:"product_id"`
	FOrderId                   string                            `json:"forderid"`
	SlsTn                      string                            `json:"sls_tn"`
	RuleId                     int                               `json:"rule_id"`
	RoutingToggle              int                               `json:"routing_toggle"`
	RoutingRole                string                            `json:"routing_role"`
	VolumeRuleID               uint64                            `json:"volume_rule_id"`
	RoutingSeq                 string                            `json:"routing_seq"`
	LineToggle                 map[string]*Toggle                `json:"line_toggle"`
	OrderData                  string                            `json:"order_data"`
	BeforeHardCriteriaLaneList []string                          `json:"before_hard_criteria_lane_list"`
	HardCriteriaResult         []*pb.LaneServiceable             `json:"hard_criteria_result"` // 原始订单的hard result
	RoutingStatus              bool                              `json:"routing_status"`
	RoutingResult              RoutingResult                     `json:"routing_result"`
	FinalResult                FinalResult                       `json:"final_result"`
	RequestTime                uint32                            `json:"request_time"`
	HCResult                   []*rule.RoutingLaneInfo           `json:"hc_result"`               // 硬性校验任务重刷后的hard result
	MultiProductHCResult       []*rule.RoutingLaneInfo           `json:"multi_product_hc_result"` // Multi Product 硬性校验任务重刷后的hard result
	RoutingType                int                               `json:"routing_type"`
	ExtraMsg                   string                            `json:"extra_msg"`
	ExtraInfo                  ExtraInfo                         `json:"extra_info"`
	LineFeeMap                 map[string]float64                `json:"line_fee_map"`
	RequestType                int                               `json:"request_type"` // 预留字段，用于记录本次请求是否为Reroute
	CacheOrderType             int64                             `json:"cache_order_type"`
	LineAsfMap                 map[string]forecastentity.AsfInfo `json:"line_asf_map"`
}

type ExtraInfo struct {
	DeliveryLocationIdList []uint                     `json:"delivery_location_id_list"`
	ValidationWeight       int64                      `json:"validation_weight"`
	ParcelLength           float64                    `json:"parcel_length"`
	ParcelWidth            float64                    `json:"parcel_width"`
	ParcelHeight           float64                    `json:"parcel_height"`
	DgType                 int                        `json:"dg_type"`
	BuyerStateId           int64                      `json:"buyer_state_id"`
	BuyerCityId            int64                      `json:"buyer_city_id"`
	ShippingFee            float64                    `json:"shipping_fee"`
	Weight                 float32                    `json:"weight"`
	SmartRoutingData       rule.SmartRoutingOrderData `json:"smart_routing_data"`
}

type Toggle struct {
	On  []string
	Off []string
}

type RoutingResult struct {
	RoutingRole                  map[int]int                  `json:"routing_role"`
	AvailableFilterProcess       AvailableFilterProcess       `json:"available_filter_process"`
	SoftCriteriaFilterProcess    SoftCriteriaFilterProcess    `json:"soft_criteria_filter_process"`
	DefaultCriteriaFilterProcess DefaultCriteriaFilterProcess `json:"default_criteria_filter_process"`
	SpxSelfBuildProcess          SpxBuildFilterProcess        `json:"spx_build_filter_process"`
}

type SpxBuildFilterProcess struct {
	Before []string `json:"before"`
	After  []string `json:"after"`
}

type ActualPoint struct {
	PointType   int32  `json:"point_type,omitempty"`
	SiteSubType int32  `json:"site_sub_type,omitempty"`
	SiteId      string `json:"site_id,omitempty"`
	PointId     string `json:"point_id,omitempty"`
}

type FinalResult struct {
	LaneCode        string        `json:"lane_code" `
	DgFlag          int32         `json:"dg_flag"`
	ActualPointList []ActualPoint `json:"actual_point_list,omitempty"`
	LaneCodeGroup   []string      `json:"lane_code_group,omitempty"`
	Result          string        `json:"result"`
}

type AvailableFilterProcess struct {
	Processed bool                           `json:"processed"`
	Before    map[string]map[string][]string `json:"before"` //先按照lane分类，在按照resources_sub_type分类
	After     map[string]map[string][]string `json:"after"`
}

type SchedulingFactorCombination struct {
	Priority          int32         `json:"priority"`
	StepType          rule.StepType `json:"step_type"`
	FactorName        string        `json:"factor_name"`
	ProcessData       interface{}   `json:"process_data,omitempty"`
	Before            []string      `json:"before"`
	After             []string      `json:"after"`
	ActualLeftLineIds []string      `json:"actual_left_line_ids"`
}

type SoftCriteriaRuleStage struct {
	DisplayResourceSubType string                        `json:"display_resource_sub_type"`
	ResourceSubType        int32                         `json:"resource_sub_type"`
	FactorCombination      []SchedulingFactorCombination `json:"resource_rule_steps"`
	Before                 []string                      `json:"before"`
	After                  []string                      `json:"after"`
}

type SoftCriteriaFilterProcess struct {
	Processed bool                    `json:"processed"`
	StageList []SoftCriteriaRuleStage `json:"rule_steps"` //这里指 FL / LM 等
	Before    []string                `json:"before"`
	After     []string                `json:"after"`
}

// 后续 最好修改下 json tag
type DefaultCriteriaRuleStage struct {
	ResourceSubType int32 `json:"resource_sub_type"`
	FactorName      string
	BeforeLanes     []string    `json:"before_lanes"`
	AfterLanes      []string    `json:"after_lanes"`
	Before          []string    `json:"before"`
	After           []string    `json:"after"`
	ProcessData     interface{} `json:"process_data"`
}

type DefaultCriteriaFilterProcess struct {
	Processed    bool              `json:"processed"`
	CriteriaType rule.CriteriaType `json:"criteria_type"`
	FactorName   string
	StageList    []DefaultCriteriaRuleStage `json:"rule_steps"`
	Before       []string                   `json:"before"`
	After        []string                   `json:"after"`
}

var (
	RoutingLogTabHook = &RoutingLogTab{}
)

const (
	routingLogTableName = "routing_log_tab_%s"
	tableDateLayout     = "********"
)

type RoutingLogTab struct {
	//ID                 int64  `gorm:"column:id" json:"id"`
	ProductID          int    `gorm:"column:product_id" json:"product_id"`
	LaneCode           string `gorm:"column:lane_code" json:"lane_code"`
	FOrderId           string `gorm:"column:forderid" json:"forderid"`
	RuleID             int    `gorm:"column:rule_id" json:"rule_id"`
	OrderData          []byte `gorm:"column:order_data" json:"order_data"`
	HardCriteriaResult []byte `gorm:"column:hard_criteria_result" json:"hard_criteria_result"`
	RoutingResult      []byte `gorm:"column:routing_result" json:"routing_result"`
	FinalResult        []byte `gorm:"column:final_result" json:"final_result"`
	RequestTime        uint32 `gorm:"column:request_time" json:"request_time"`
	MTime              uint32 `gorm:"column:mtime" json:"mtime"`
	CTime              uint32 `gorm:"column:ctime" json:"ctime"`
}

func (r *RoutingLogTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingLogCidRead
}

func (r *RoutingLogTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingLogCidWrite
}

func (r *RoutingLogTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{}
}

func (r *RoutingLogTab) TableName() string {
	return fmt.Sprintf(routingLogTableName, timeutil.ConvertTimeStampToTimeByCountry(int64(r.RequestTime), envvar.GetCID()).Format(tableDateLayout))
}

func TableNameByDate(date string) string {
	return fmt.Sprintf(routingLogTableName, date)
}
