package routing_log

import (
	"context"
	"encoding/base64"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	zip2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"strconv"
)

var (
	goroutinePool     *ants.Pool
	goroutinePoolSize = 64
)

func init() {
	// 核心在线链路，使用非阻塞式
	p, err := ants.NewPool(goroutinePoolSize, ants.WithNonblocking(true))
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}

	goroutinePool = p
}

type RoutingLogService interface {
	SendRoutingLogToTask(ctx context.Context, orderData *pb.CreateOrderRequestData, routingLog *RoutingLog) *srerr.Error
	ProcessRoutingLog(ctx context.Context, routingLog *RoutingLog) *srerr.Error
	ReportOrderCount(ctx context.Context) *srerr.Error
	SendHardCriteriaLogToKafka(ctx context.Context, routingLog RoutingLog) *srerr.Error
}

type RoutingLogServiceImpl struct {
	laneSrv        lane.LaneService
	counter        volume_counter.VolumeCounter
	lpsApi         lpsclient.LpsApi
	routingLogRepo RoutingLogRepo
}

func NewRoutingLogServiceImpl(
	counter volume_counter.VolumeCounter,
	laneSrv lane.LaneService,
	lpsApi lpsclient.LpsApi,
	routingLogRepo RoutingLogRepo) *RoutingLogServiceImpl {
	return &RoutingLogServiceImpl{
		counter:        counter,
		laneSrv:        laneSrv,
		lpsApi:         lpsApi,
		routingLogRepo: routingLogRepo,
	}
}

func (r *RoutingLogServiceImpl) SendRoutingLogToTask(ctx context.Context, orderData *pb.CreateOrderRequestData, routingLog *RoutingLog) *srerr.Error {
	// 1. enrich routing log order data
	routingLog.ExtraInfo.Weight = r.CalculateSkuWeight(orderData)
	routingLog.OrderData = objutil.JsonString(orderData)
	routingLogJson := objutil.JsonBytes(routingLog)
	logger.CtxLogDebugf(ctx, "Routing Log: %s ", string(routingLogJson))

	// 2. zip data
	msgData := zip2.ZSTDCompress(routingLogJson)

	// 3. send msg to saturn
	ctx = ctxhelper.CloneTrace(ctx)
	if err := goroutinePool.Submit(func() {
		namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
		if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameRoutingLogVisual, msgData, nil, kafkahelper.RoutingLogType); err != nil {
			logger.CtxLogErrorf(ctx, "send routing log to kafka failed: %v", err)
		}
	}); err != nil {
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatGoroutinePool, "SendRoutingLogToTask", monitoring.StatusError, err.Error())
		logger.CtxLogErrorf(ctx, "submit async send routing log function failed: %v", err)
	}

	return nil
}

func (r *RoutingLogServiceImpl) CalculateSkuWeight(orderData *pb.CreateOrderRequestData) float32 {
	weight := float32(0.0)
	if orderData == nil {
		return weight
	}

	for _, sku := range orderData.GetSkus() {
		weight += sku.GetWeight() * float32(sku.GetQuantity())
	}

	return weight
}

func (r *RoutingLogServiceImpl) ProcessRoutingLog(ctx context.Context, routingLog *RoutingLog) *srerr.Error {
	// 1.dump into db
	if _, err := r.routingLogRepo.CreateRoutingLog(ctx, ConvertToRoutingLogTab(ctx, routingLog)); err != nil {
		return err
	}

	if routingLog.RuleId == 0 {
		// if it's not a smart routing order, no need report
		return nil
	}
	// 2.report cat and grafana
	_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingProduct, newProductReportMap(routingLog.ProductId))
	_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingLane, newLaneReportMap(routingLog.ProductId, routingLog.FinalResult.LaneCode))
	_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingRule, newRuleReportMap(routingLog.ProductId, routingLog.RuleId))
	// report all resources
	if len(routingLog.FinalResult.LaneCodeGroup) > 0 {
		for _, laneCode := range routingLog.FinalResult.LaneCodeGroup {
			laneInfo, err := r.laneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", err)
				continue
			}
			for _, lineId := range laneInfo.GetAllLineID() {
				_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingResource,
					newResourceReportMap(routingLog.ProductId, lineId))
			}
			for _, siteId := range laneInfo.GetAllSiteID() {
				_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingResource,
					newResourceReportMap(routingLog.ProductId, siteId))
			}
		}
	} else {
		laneInfo, err := r.laneSrv.GetLaneInfoByLaneCode(ctx, routingLog.FinalResult.LaneCode)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", err)
		} else {
			for _, lineId := range laneInfo.GetAllLineID() {
				_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingResource,
					newResourceReportMap(routingLog.ProductId, lineId))
			}
			for _, siteId := range laneInfo.GetAllSiteID() {
				_ = prometheusutil.CounterIncr(constant.MetricSmartRoutingResource,
					newResourceReportMap(routingLog.ProductId, siteId))
			}
		}
	}

	return nil
}

func newResourceReportMap(productId int, resourceId string) map[string]string {
	reportMap := make(map[string]string)
	reportMap["product_id"] = strconv.Itoa(productId)
	reportMap["resource_id"] = resourceId

	return reportMap
}

func newProductReportMap(productId int) map[string]string {
	reportMap := make(map[string]string)
	reportMap["product_id"] = strconv.Itoa(productId)

	return reportMap
}

func newRuleReportMap(productId int, ruleId int) map[string]string {
	reportMap := make(map[string]string)
	reportMap["product_id"] = strconv.Itoa(productId)
	reportMap["rule_id"] = strconv.Itoa(ruleId)

	return reportMap
}

func newLaneReportMap(productId int, laneCode string) map[string]string {
	reportMap := make(map[string]string)
	reportMap["product_id"] = strconv.Itoa(productId)
	reportMap["lane_code"] = laneCode

	return reportMap
}

func ConvertToRoutingLogTab(ctx context.Context, log *RoutingLog) *RoutingLogTab {
	routingResult, err := jsoniter.Marshal(log.RoutingResult)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Marshal fail|err=%v", err)
	}
	hardResult, err := jsoniter.Marshal(log.HardCriteriaResult)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Marshal fail|err=%v", err)
	}
	finalResult, err := jsoniter.Marshal(log.FinalResult)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Marshal fail|err=%v", err)
	}

	return &RoutingLogTab{
		ProductID:          log.ProductId,
		LaneCode:           log.FinalResult.LaneCode,
		FOrderId:           log.FOrderId,
		RuleID:             log.RuleId,
		OrderData:          zipOrderData(log.OrderData),
		HardCriteriaResult: hardResult,
		RoutingResult:      routingResult,
		FinalResult:        finalResult,
		RequestTime:        log.RequestTime,
	}
}

func zipOrderData(src string) []byte {
	return []byte(base64.StdEncoding.EncodeToString(zip2.ZSTDCompress([]byte(src))))
}

func generateOrderDataString(order *pb.CreateOrderRequestData) (string, *srerr.Error) {
	ret, _ := jsoniter.MarshalToString(order)
	return ret, nil
}

func (r *RoutingLogServiceImpl) ReportOrderCount(ctx context.Context) *srerr.Error {
	// 上报routing运力
	err := r.ReportRoutingVolume(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (r *RoutingLogServiceImpl) ReportRoutingVolume(ctx context.Context) *srerr.Error {
	laneCodeInfos, err := r.lpsApi.GetLaneCodes(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetAllProductLaneCodeRefs fail|err=%v", err)
		return err
	}
	productLanesMap := make(map[int][]string)
	for _, laneCodeInfo := range laneCodeInfos {
		productLanesMap[laneCodeInfo.ProductId] = append(productLanesMap[laneCodeInfo.ProductId], laneCodeInfo.LaneCodes...)
	}

	for productId, laneCodes := range productLanesMap {
		// 上报product单量
		r.reportProductCount(ctx, productId)
		// 上报lane单量
		r.batchReportLaneCount(ctx, productId, laneCodes)
		// 上报line单量
		r.batchReportResourceCount(ctx, productId, laneCodes)
		// 上报actual point单量
		r.batchReportActualPointCount(ctx, productId, laneCodes)
	}
	return nil
}

func (r *RoutingLogServiceImpl) reportProductCount(ctx context.Context, productId int) {
	count, err := r.counter.GetProductVolume(ctx, productId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetProductVolume fail|err=%v", err)
		return
	}
	prometheusutil.ReportProductCount(ctx, productId, count)
}

func (r *RoutingLogServiceImpl) batchReportLaneCount(ctx context.Context, productId int, laneCodes []string) {
	resultList, err := r.counter.BatchGetLaneVolume(ctx, productId, laneCodes)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetLaneVolume fail|err=%v", err)
		return
	}

	// 做一层保护
	if len(resultList) != len(laneCodes) {
		logger.CtxLogErrorf(ctx, "get invalid result list from codis, key len=%d, result len=%d", len(laneCodes), len(resultList))
		return
	}

	for i, laneCode := range laneCodes {
		prometheusutil.ReportLaneCount(ctx, productId, laneCode, resultList[i])
	}
}

func (r *RoutingLogServiceImpl) batchReportResourceCount(ctx context.Context, productId int, laneCodeList []string) {
	var (
		lineIDList     = make([]string, 0)
		lineIdNameMap  = make(map[string]string)
		lineToLanesMap = make(map[string][]string)
	)

	for _, laneCode := range laneCodeList {
		laneInfo, lErr := r.laneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
		if lErr != nil {
			logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", lErr)
			continue
		}

		lineIDList = append(lineIDList, laneInfo.GetAllLineID()...)
		// 获取lineId-lineName对应关系map
		for lineID, lineName := range laneInfo.GetLineIdNameMap() {
			lineIdNameMap[lineID] = lineName
			lineToLanesMap[lineID] = append(lineToLanesMap[lineID], laneCode)
		}
	}

	resultList, err := r.counter.BatchGetLineVolume(ctx, productId, lineIDList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetLineVolume fail|err=%v", err)
		return
	}

	// 做一层保护
	if len(resultList) != len(lineIDList) {
		logger.CtxLogErrorf(ctx, "get invalid result list from codis, key len=%d, result len=%d", len(lineIDList), len(resultList))
		return
	}

	for i, lineID := range lineIDList {
		resourceId := fmt.Sprintf("%v-%v", lineID, lineIdNameMap[lineID])
		for _, laneCode := range lineToLanesMap[lineID] {
			prometheusutil.ReportLineCount(ctx, productId, laneCode, resourceId, resultList[i])
		}
	}
}

func (r *RoutingLogServiceImpl) batchReportActualPointCount(ctx context.Context, productId int, laneCodeList []string) {
	var (
		actualPointInfoList     = make([]volume_counter.ReportActualPointInfo, 0)
		lineActualPointInfoList = make([]volume_counter.ReportLineActualPointInfo, 0)
	)

	for _, laneCode := range laneCodeList {
		laneInfo, lErr := r.laneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
		if lErr != nil {
			logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", lErr)
			continue
		}

		for _, siteInfo := range laneInfo.GetAllSiteInfo() {
			// Actual Point维度
			for _, actualPointInfo := range siteInfo.ActualPointMap {
				actualPointInfoList = append(actualPointInfoList, volume_counter.ReportActualPointInfo{
					ProductID:       int64(productId),
					LaneCode:        laneCode,
					ActualPointID:   actualPointInfo.ActualPointID,
					ActualPointName: actualPointInfo.ActualPointName,
					ActualPointType: lfslib.ActualPointTypeIn,
				})
				actualPointInfoList = append(actualPointInfoList, volume_counter.ReportActualPointInfo{
					ProductID:       int64(productId),
					LaneCode:        laneCode,
					ActualPointID:   actualPointInfo.ActualPointID,
					ActualPointName: actualPointInfo.ActualPointName,
					ActualPointType: lfslib.ActualPointTypeOut,
				})

				// Actual Point + Line维度
				for _, lineID := range laneInfo.GetAllLineID() {
					lineActualPointInfoList = append(lineActualPointInfoList, volume_counter.ReportLineActualPointInfo{
						ProductID:       int64(productId),
						LaneCode:        laneCode,
						LineID:          lineID,
						ActualPointID:   actualPointInfo.ActualPointID,
						ActualPointName: actualPointInfo.ActualPointName,
						ActualPointType: lfslib.ActualPointTypeIn,
					})
					lineActualPointInfoList = append(lineActualPointInfoList, volume_counter.ReportLineActualPointInfo{
						ProductID:       int64(productId),
						LaneCode:        laneCode,
						LineID:          lineID,
						ActualPointID:   actualPointInfo.ActualPointID,
						ActualPointName: actualPointInfo.ActualPointName,
						ActualPointType: lfslib.ActualPointTypeOut,
					})
				}
			}
		}
	}

	actualPointResultList, err := r.counter.BatchGetActualPointVolume(ctx, actualPointInfoList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetActualPointVolume failed|err=%v", err)
		return
	}

	// 做一层保护
	if len(actualPointResultList) != len(actualPointInfoList) {
		logger.CtxLogErrorf(ctx, "get invalid result list from codis, key len=%d, result len=%d", len(actualPointInfoList), len(actualPointResultList))
		return
	}

	for i, info := range actualPointInfoList {
		prometheusutil.ReportActualPointCount(
			ctx, productId, info.LaneCode, fmt.Sprintf("%v-%v", info.ActualPointID, info.ActualPointName),
			int(info.ActualPointType), float64(actualPointResultList[i]),
		)
	}

	lineActualPointResultList, err := r.counter.BatchGetLineActualPointVolume(ctx, lineActualPointInfoList)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchGetActualPointVolume failed|err=%v", err)
		return
	}

	// 做一层保护
	if len(lineActualPointResultList) != len(lineActualPointInfoList) {
		logger.CtxLogErrorf(ctx, "get invalid result list from codis, key len=%d, result len=%d", len(lineActualPointInfoList), len(lineActualPointResultList))
		return
	}

	for i, info := range lineActualPointInfoList {
		prometheusutil.ReportLineActualPointCount(
			ctx, productId, info.LaneCode, info.LineID, fmt.Sprintf("%v-%v", info.ActualPointID, info.ActualPointName),
			int(info.ActualPointType), float64(lineActualPointResultList[i]),
		)
	}
}

func (r *RoutingLogServiceImpl) SendHardCriteriaLogToKafka(ctx context.Context, routingLog RoutingLog) *srerr.Error {
	// 1.序列化log
	routingLogJson := objutil.JsonBytes(routingLog)
	logger.CtxLogDebugf(ctx, "Routing Log: %s ", string(routingLogJson))
	// 2.压缩数据
	msgData := zip2.ZSTDCompress(routingLogJson)

	// 3.发送消息到kafka
	namespace := configutil.GetSaturnNamespaceConf(ctx).SmrNamespace
	if err := kafkahelper.DeliveryMessage(ctx, namespace, constant.TaskNameRoutingLogVisual, msgData, nil, kafkahelper.RoutingLogType); err != nil {
		logger.CtxLogErrorf(ctx, "send routing log to kafka failed: %v", err)
		return err
	}

	return nil
}
