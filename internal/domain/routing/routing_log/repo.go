package routing_log

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type RoutingLogRepo interface {
	CreateRoutingLog(ctx context.Context, log *RoutingLogTab) (*RoutingLogTab, *srerr.Error)
}

type RoutingLogRepoImpl struct {
}

func NewRoutingLogRepoImpl() *RoutingLogRepoImpl {
	return &RoutingLogRepoImpl{}
}

func (r *RoutingLogRepoImpl) CreateRoutingLog(ctx context.Context, log *RoutingLogTab) (*RoutingLogTab, *srerr.Error) {
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	log.CTime = uint32(now)
	log.MTime = uint32(now)
	db, err := dbutil.MasterDB(ctx, RoutingLogTabHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	db = db.Table(log.TableName()).Create(log)
	if db.GetError() != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, db.GetError())
	}

	return log, nil
}
