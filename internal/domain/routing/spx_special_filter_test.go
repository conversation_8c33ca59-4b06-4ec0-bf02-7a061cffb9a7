package routing

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func Test_routingTask_filterBySpxSelfBuild(t *testing.T) {
	rt := &routingTask{
		//routingData: tt.fields.routingData,
	}
	type args struct {
		ctx            context.Context
		matchedRule    *rule.RoutingRuleParsed
		availableLanes []*rule.RoutingLaneInfo
	}
	tests := []struct {
		name string
		args args
		want []*rule.RoutingLaneInfo
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				matchedRule: &rule.RoutingRuleParsed{
					RoutingType: rule.SPXRoutingType,
				},
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LineList: []*rule.LineInfo{
							{},
						},
					},
					{
						LineList: []*rule.LineInfo{
							{
								RealResourceSubType: lfslib.L_SelfBuild,
							},
						},
					},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LineList: []*rule.LineInfo{
						{
							RealResourceSubType: lfslib.L_SelfBuild,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := rt.filterBySpxSelfBuild(tt.args.ctx, tt.args.matchedRule, tt.args.availableLanes)
			common.AssertResult(t, got, tt.want, nil, nil)
		})
	}
}
