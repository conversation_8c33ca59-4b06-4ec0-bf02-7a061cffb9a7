package helper

import (
	"fmt"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// default
func FormatLineAndDgKey(lineID string, dgFlag, dgRelated int) string {
	// 写入时：dgRelated强制设置为rule.DgRelated，主要使用dgFlag != int(rule.UndefinedDGFlag)条件区别
	// 读取时：dgRelated和dgFlag使用对应line的dgRelated和dgFlag（主要使用dgRelated == rule.DgRelated条件区别）
	// dgRelated主要用于读取时判断，dgFlag主要用于写入时判断
	if dgRelated == rule.DgRelated && dgFlag != int(rule.UndefinedDGFlag) {
		return fmt.Sprintf("Line:%v-DGFlag:%v", lineID, dgFlag)
	}
	return lineID
}

// soft
func FormatLineLimitKey(lineId string, dgFlag, dgRelated int, routingType uint8) string {
	// 写入时：dgRelated强制设置为rule.DgRelated
	// 读取时：dgRelated使用对应line的dgRelated
	if routingType != rule.IlhRoutingType {
		return lineId
	}

	return FormatLineAndDgKey(lineId, dgFlag, dgRelated)
}
