package routing_config

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"sort"
)

const configCantEnableBeforeRules = "configCantEnableBeforeRules"

func (r *RoutingConfigServiceImpl) CreateRoutingConfig(ctx context.Context, request *soft_routing.CreateConfigRequest, operateBy string) (*rule.RoutingConfig, *srerr.Error) {
	//1.判断product是否已有routing config
	productId := request.ProductId
	_, err := r.routingConfigRepo.GetConfigByProductID(ctx, productId)
	if err == nil {
		return nil, srerr.New(srerr.ConfigProductExisted, productId, "routing config of product existed")
	}
	//2.获取product的entity
	productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(productId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig|product id:%v, get product detail err:%v", productId, gErr)
		return nil, gErr
	}

	through, checkErr := r.CheckRoutingConfig(productDetail.Entity, request.SpxSmartRoutingEnabled, request.LocalSmartRoutingEnabled, request.SmartRoutingEnabled)
	if !through || checkErr != nil {
		return nil, checkErr
	}

	data, cErr := r.routingConfigRepo.CreateRoutingConfig(ctx, productId, request.SmartRoutingEnabled, request.LocalSmartRoutingEnabled,
		request.SpxSmartRoutingEnabled, request.IlhSmartRoutingEnabled, request.DefaultLaneCode, operateBy)
	if cErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, productId, cErr)
	}
	config := r.CopyRoutingConfigTab(data)
	return config, nil
}

func (r *RoutingConfigServiceImpl) UpdateRoutingConfig(ctx context.Context, request *soft_routing.UpdateConfigRequest, operateBy string) (*rule.RoutingConfig, *srerr.Error) {
	//1.判断product是否已有routing config
	productId := request.ProductId
	_, err := r.routingConfigRepo.GetConfigByProductID(ctx, productId)

	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, productId, err)
	}

	//2.获取product的entity
	productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(productId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateRoutingConfig|product id:%v, get product detail err:%v", productId, gErr)
		return nil, gErr
	}

	through, checkErr := r.CheckRoutingConfig(productDetail.Entity, request.SpxSmartRoutingEnabled, request.LocalSmartRoutingEnabled, request.SmartRoutingEnabled)
	if !through || checkErr != nil {
		return nil, checkErr
	}

	data, err := r.routingConfigRepo.UpdateRoutingConfig(ctx, productId, request.SmartRoutingEnabled, request.LocalSmartRoutingEnabled,
		request.SpxSmartRoutingEnabled, request.IlhSmartRoutingEnabled, request.DefaultLaneCode, operateBy)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, productId, err)
	}
	config := r.CopyRoutingConfigTab(data)
	return config, nil
}

// UpdateRoutingConfigLinemapping(ctx context.Context, request *soft_routing.CreateUpdateConfigRequest, operateBy string) (*rule.RoutingConfig, *srerr.Error)
func (r *RoutingConfigServiceImpl) InsertRoutingConfigLinemapping(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error {
	//if err := r.CheckProductMode(ctx, cfgtab, rolemapTab); err != nil {
	//	return err
	//}
	return insertCfgLinemappingInTx(ctx, rolemapTab, cfgtab)
}

func (r *RoutingConfigServiceImpl) UpdateRoutingConfigLinemapping(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error {
	//查record exist  ?,
	// check-validate exsit active-rules
	//1,query old record ,
	//update 模式，cfg 校验
	if cfgtab == nil {
		return nil
	}
	if err := r.CheckProductMode(ctx, cfgtab, rolemapTab); err != nil {
		return err
	}
	// 校验 规则已生效 才能开启smr
	if err := r.CheckRuleExist(ctx, cfgtab); err != nil {
		return srerr.New(srerr.CantEnableRouting, "", "%v", err)
	}
	//line-mapping 校验，edit-modes,
	if rolemapTab == nil {
		return nil
	}
	if err := r.CheckLineMapping(ctx, rolemapTab); err != nil {
		return srerr.New(srerr.LineMappingNotALlowed, "", "%v", errors.New(""))
	}
	// 开始插入数据,事务，
	return updateCfgLinemappingInTx(ctx, rolemapTab, cfgtab)
}

func (r *RoutingConfigServiceImpl) CheckProductMode(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error {
	//2.获取product的entity
	productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(cfgtab.ProductID))
	if gErr != nil {
		return gErr
	}
	_, checkErr := r.CheckRoutingConfig(productDetail.Entity, cfgtab.SpxSmartRoutingEnabled, cfgtab.LocalSmartRoutingEnabled, cfgtab.SmartRoutingEnabled)
	return checkErr
}

func (r *RoutingConfigServiceImpl) CheckRuleExist(ctx context.Context, cfgtab *ruledata.RoutingConfigTab) error {
	//1.获取product的entity
	//productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(request.ProductId))
	//if gErr != nil {
	//	return gErr
	//}
	//select-all,
	tabs, _ := r.ruleRepo.GetRoutingRuleByCondition(ctx, map[string]interface{}{
		"product_id = ?":  cfgtab.ProductID,
		"rule_status = ?": rule.RuleStatusActive,
	})

	if cfgtab.SmartRoutingEnabled {
		pass := false
		for _, tab := range tabs {
			if tab.RoutingType == rule.CBRoutingType {
				pass = true
				break
			}
		}
		if !pass {
			return errors.New(configCantEnableBeforeRules)
		}
	}
	if cfgtab.LocalSmartRoutingEnabled {
		pass := false
		for _, tab := range tabs {
			if tab.RoutingType == rule.LocalRoutingType {
				pass = true
				break
			}
		}
		if !pass {
			return errors.New(configCantEnableBeforeRules)
		}
	}
	if cfgtab.IlhSmartRoutingEnabled {
		pass := false
		for _, tab := range tabs {
			if tab.RoutingType == rule.IlhRoutingType {
				pass = true
				break
			}
		}
		if !pass {
			return errors.New(configCantEnableBeforeRules)
		}
	}
	if cfgtab.SpxSmartRoutingEnabled {
		pass := false
		for _, tab := range tabs {
			if tab.RoutingType == rule.SPXRoutingType {
				pass = true
				break
			}
		}
		if !pass {
			return errors.New(configCantEnableBeforeRules)
		}
	}
	if cfgtab.CBMultiRoutingEnabled {
		pass := false
		for _, tab := range tabs {
			if tab.RoutingType == rule.CBRoutingType && tab.IsMultiProduct {
				pass = true
				break
			}
		}
		if !pass {
			return errors.New(configCantEnableBeforeRules)
		}
	}

	return nil
}

func (r *RoutingConfigServiceImpl) CheckLineMapping(ctx context.Context, rolemapTab *ruledata.ProductRoutingRoleTab) error {
	//查询 cfg_tab ，已开启的不能变更 line-mapping ,
	db, dbErr := dbutil.MasterDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return dbErr
	}
	record := routing.GetRoleInfoByPid(ctx, int64(rolemapTab.ProductId), db)
	if record == nil {
		return errors.New("rolemap inexist")
	}
	//edit-mode,
	cfg := routing.GetRoutingcfgByPid(ctx, int64(rolemapTab.ProductId), db)
	if !checkAllowUpdateLineMapping(record, rolemapTab, cfg) {
		//
		logger.CtxLogErrorf(ctx, "checkAllowUpdateLineMapping %v", false)
		return errors.New("cant edit line mapping after enabled routing-configs")
	}
	return nil

}

func checkAllowUpdateLineMapping(src, dst *ruledata.ProductRoutingRoleTab, cfg *ruledata.RoutingConfigTab) bool {
	if cfg == nil {
		return true
	}
	fmt.Printf("debug %v %v %v\n", *src, *dst, *cfg)
	//check-diffs,false-true updated ??,未开启 则可以变更，
	if cfg.SmartRoutingEnabled && !checkEqual(src.CBRoutingRole, dst.CBRoutingRole) {
		return false
	}
	if cfg.LocalSmartRoutingEnabled && !checkEqual(src.LocalRoutingRole, dst.LocalRoutingRole) {
		return false
	}
	if cfg.SpxSmartRoutingEnabled && !checkEqual(src.SpxRoutingRole, dst.SpxRoutingRole) {
		return false
	}
	if cfg.IlhSmartRoutingEnabled && !checkEqual(src.IlhRoutingRole, dst.IlhRoutingRole) {
		return false
	}
	if cfg.CBMultiRoutingEnabled && !checkEqual(src.CBMultiRoutingRole, dst.CBMultiRoutingRole) {
		return false
	}

	//check-all-fields
	return true
}

func checkEqual(src string, target string) bool {
	//[]schema.RoutingRoleInfo,
	srcObj := routing_role.JsonRoleInfo(src)
	dstObj := routing_role.JsonRoleInfo(target)
	//use deep-eqauls ??
	//fmt.Println("check ", srcObj, dstObj)
	sort.Slice(srcObj, func(i, j int) bool {
		return srcObj[i].ResourceSubType < srcObj[j].ResourceSubType
	})

	sort.Slice(dstObj, func(i, j int) bool {
		return dstObj[i].ResourceSubType < dstObj[j].ResourceSubType
	})
	return reflect.DeepEqual(srcObj, dstObj)
}

func updateCfgLinemappingInTx(ctx context.Context, rolemapTab *ruledata.ProductRoutingRoleTab, cfgtab *ruledata.RoutingConfigTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return srerr.New(srerr.DatabaseErr, "", "%v", dbErr)
	}
	pid := cfgtab.ProductID
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		//update-modes ??
		tx := scormv2.Context(ctx)
		if cfgtab != nil {
			upd := dbutil.UpdateIncludeMapper(cfgtab,
				"smart_routing_enabled", "local_routing_enable",
				"spx_routing_enable", "ilh_routing_enable", "cb_multi_routing_enable",
				"default_lane_code", "operated_by", "mtime", "smartrouting_toggle")
			if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Where("product_id = ?", pid).Updates(upd).GetError(); err1 != nil {
				return srerr.With(srerr.DatabaseErr, nil, err1)
			}
		}

		if rolemapTab != nil {
			//todo ,query master-db
			// for Update-record ,
			upd := dbutil.UpdateIncludeMapper(rolemapTab,
				"cb_routing_role", "cb_multi_routing_role",
				"spx_routing_role", "local_routing_role",
				"ilh_routing_role", "mtime")
			if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Where("product_id = ? ", pid).Updates(upd).GetError(); err2 != nil {
				return srerr.With(srerr.DatabaseErr, nil, err2)
			}
		}
		return nil
	})

	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func insertCfgLinemappingInTx(ctx context.Context, rolemapTab *ruledata.ProductRoutingRoleTab, cfgtab *ruledata.RoutingConfigTab) *srerr.Error {
	//insert on duplicate updates ??
	//一致性的保证，可能存在 product 信息存储失败，而 routing_cfg 成功，
	db, dbErr := dbutil.MasterDB(ctx, ruledata.RoutingConfigHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		tx := scormv2.Context(ctx)
		if cfgtab != nil {
			//check exist ?,
			rcfg := routing.GetRoutingcfgByPid(ctx, cfgtab.ProductID, tx)
			if rcfg == nil || rcfg.ProductID == 0 {
				if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Create(cfgtab).GetError(); err1 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err1)
				}
			} else {
				// exist record , not exist in product_tab ,
				upd := dbutil.UpdateIncludeMapper(cfgtab,
					"smart_routing_enabled", "local_routing_enable",
					"spx_routing_enable", "ilh_routing_enable", "cb_multi_routing_enable",
					"default_lane_code", "operated_by", "mtime", "ctime", "smartrouting_toggle")
				if err1 := tx.Table(ruledata.RoutingConfigHook.TableName()).Debug().Where("product_id = ?", cfgtab.ProductID).Updates(upd).GetError(); err1 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err1)
				}
			}
		}

		if rolemapTab != nil {
			//if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(rolemapTab).Error; err2 != nil {
			//	return srerr.With(srerr.DatabaseErr, nil, err2)
			//}
			routingRoleRecord := routing.GetRoleInfoByPid(ctx, int64(rolemapTab.ProductId), tx)
			if routingRoleRecord == nil || routingRoleRecord.ProductId == 0 {
				//Insert ??
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Create(rolemapTab).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}

			} else {
				// for Update-record ,
				upd := dbutil.UpdateIncludeMapper(rolemapTab,
					"cb_routing_role", "cb_multi_routing_role",
					"spx_routing_role", "local_routing_role",
					"ilh_routing_role", "mtime", "ctime")
				if err2 := tx.Table(ruledata.ProductRoutingRoleTabHook.TableName()).Debug().Where("product_id = ? ", rolemapTab.ProductId).Updates(upd).GetError(); err2 != nil {
					return srerr.With(srerr.DatabaseErr, nil, err2)
				}
			}
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}
