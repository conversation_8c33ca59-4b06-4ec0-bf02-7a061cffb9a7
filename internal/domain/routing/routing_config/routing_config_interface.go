package routing_config

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type RoutingConfigService interface {
	GetRoutingConfigByProductID(ctx context.Context, productID int64) (*rule.RoutingConfig, *srerr.Error)
	GetRoutingConfigCacheByProductID(ctx context.Context, productID int) (*rule.RoutingConfig, *srerr.Error)
	GetRoutingConfigByID(ctx context.Context, ID int64) (*rule.RoutingConfig, *srerr.Error)
	ListRoutingConfig(ctx context.Context, productID int64, offset int32, limit int32) ([]*rule.RoutingConfig, int32, *srerr.Error)
	CreateRoutingConfig(ctx context.Context, request *soft_routing.CreateConfigRequest, operateBy string) (*rule.RoutingConfig, *srerr.Error)
	UpdateRoutingConfig(ctx context.Context, request *soft_routing.UpdateConfigRequest, operateBy string) (*rule.RoutingConfig, *srerr.Error)
	ListRoutingConfigByRoutingType(ctx context.Context, routingType uint8) ([]*rule.RoutingConfig, *srerr.Error)
	ListAllRoutingConfig(ctx context.Context) ([]*ruledata.RoutingConfigTab, *srerr.Error)
	UpdateRoutingConfigLinemapping(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error
	InsertRoutingConfigLinemapping(ctx context.Context, cfgtab *ruledata.RoutingConfigTab, rolemapTab *ruledata.ProductRoutingRoleTab) *srerr.Error
	GetSmrScenes(ctx context.Context, pid int64) *schema.SmrScenesWithActiveRules
}

type RoutingConfigServiceImpl struct {
	routingConfigRepo ruledata.RoutingConfigRepo
	lpsApi            lpsclient.LpsApi
	ruleRepo          ruledata.SoftRuleRepo
	layerCache        *layercache.LevelCache
}

func NewRoutingConfigServiceImpl(
	routingConfigRepo ruledata.RoutingConfigRepo,
	lpsApi lpsclient.LpsApi,
	ruleRepo ruledata.SoftRuleRepo,
	layerCache *layercache.LevelCache) *RoutingConfigServiceImpl {
	return &RoutingConfigServiceImpl{
		routingConfigRepo: routingConfigRepo,
		lpsApi:            lpsApi,
		ruleRepo:          ruleRepo,
		layerCache:        layerCache,
	}
}
