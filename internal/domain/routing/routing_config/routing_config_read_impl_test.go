package routing_config

import (
	"context"
	"errors"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestRoutingConfigServiceImpl_GetRoutingConfigCacheByProductID(t *testing.T) {
	ctx := context.TODO()
	layerCache := &layercache.LevelCache{}
	r := &RoutingConfigServiceImpl{}

	var patch *gomonkey.Patches
	type args struct {
		productID int
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingConfig
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: RoutingConfigNotFound",
			args:    args{},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingConfigNotFound, "", errors.New("mock RoutingConfigNotFound")),
			setup: func() {
				r = &RoutingConfigServiceImpl{
					layerCache: layerCache,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(layerCache), "Get", func(lc *layercache.LevelCache, ctx context.Context, namespace, id string, obj interface{}, opts ...layercache.LevelOption) error {
					return errors.New("mock RoutingConfigNotFound")
				})
			},
		},
		{
			name:    "case 2: success",
			args:    args{},
			want:    &rule.RoutingConfig{},
			wantErr: nil,
			setup: func() {
				r = &RoutingConfigServiceImpl{
					layerCache: layerCache,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(layerCache), "Get", func(lc *layercache.LevelCache, ctx context.Context, namespace, id string, obj interface{}, opts ...layercache.LevelOption) error {
					if ptr, ok := obj.(*rule.RoutingConfig); ok {
						if ptr != nil {
							*ptr = rule.RoutingConfig{}
						}
					}
					return nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := r.GetRoutingConfigCacheByProductID(ctx, tt.args.productID)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
