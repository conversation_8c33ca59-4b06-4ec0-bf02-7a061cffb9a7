package routing_config

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

func (r *RoutingConfigServiceImpl) GetRoutingConfigByProductID(ctx context.Context, productID int64) (*rule.RoutingConfig, *srerr.Error) {
	var routingConfig ruledata.RoutingConfigTab
	if err := dbutil.Take(ctx, ruledata.RoutingConfigHook,
		map[string]interface{}{"product_id = ?": productID},
		&routingConfig); err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingConfigByProductID| get routing config fail, err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, srerr.With(srerr.RoutingConfigNotFound, nil, err)
		} else {
			return nil, srerr.With(srerr.DatabaseErr, nil, err)
		}
	}

	routingConfigEntity := routingConfig.ToRoutingConfigEntity()

	return &routingConfigEntity, nil
}

// @core
func (r *RoutingConfigServiceImpl) GetRoutingConfigCacheByProductID(ctx context.Context, productID int) (*rule.RoutingConfig, *srerr.Error) {
	var ret rule.RoutingConfig
	if err := r.layerCache.Get(ctx, constant.LevelCacheRoutingConfig, strconv.Itoa(productID), &ret,
		layercache.WithLoader(r.getRoutingConfigLoader),
		layercache.WithWarmExpire()); err != nil {
		return nil, srerr.With(srerr.RoutingConfigNotFound, productID, err)
	}

	return &ret, nil
}

func (r *RoutingConfigServiceImpl) getRoutingConfigLoader(ctx context.Context, key string) (interface{}, error) {
	productId, convErr := strconv.Atoi(key)
	if convErr != nil {
		logger.CtxLogErrorf(ctx, "convert key to product id failed | err=%+v", convErr)
		return nil, convErr
	}

	routingConfig, err := r.GetRoutingConfigByProductID(ctx, int64(productId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingConfigByProductID failed | err=%+v", err)
		return nil, err
	}

	return routingConfig, nil
}

func (r *RoutingConfigServiceImpl) GetRoutingConfigByID(ctx context.Context, ID int64) (*rule.RoutingConfig, *srerr.Error) {
	routingConfigTab, err := r.routingConfigRepo.GetConfigByID(ctx, ID)
	if dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.RoutingConfigNotFound, ID, err)
	}
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, ID, err)
	}
	config := r.CopyRoutingConfigTab(routingConfigTab)
	return config, nil
}

func (r *RoutingConfigServiceImpl) ListRoutingConfig(ctx context.Context, productID int64, offset int32, limit int32) ([]*rule.RoutingConfig, int32, *srerr.Error) {
	if offset < 0 {
		offset = 0
	}
	if limit <= 0 {
		return nil, 0, srerr.New(srerr.ParamErr, limit, "limit should be greater than 0")
	}
	var (
		data      []*ruledata.RoutingConfigTab
		total     int32
		err       error
		condition map[string]interface{}
	)
	if productID != 0 {
		condition = map[string]interface{}{
			"product_id = ?": productID,
		}
	}
	data, total, err = r.routingConfigRepo.GetConfigListByCondition(ctx, condition, offset, limit)

	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}
	configs := make([]*rule.RoutingConfig, len(data))
	for i, dt := range data {
		configs[i] = r.CopyRoutingConfigTab(dt)
	}
	return configs, total, nil
}

func (r *RoutingConfigServiceImpl) CheckRoutingConfig(entity int, spxSmartRoutingEnable, localSmartRoutingEnable, cbSmartRoutingEnable bool) (bool, *srerr.Error) {
	if !(spxSmartRoutingEnable || localSmartRoutingEnable || cbSmartRoutingEnable) {
		return true, nil
	}
	switch entity {
	//1:local product, including SPX routing role && Local routing role
	//2:cb product
	case ruledata.LocalProduct:
		if !cbSmartRoutingEnable && ((localSmartRoutingEnable && !spxSmartRoutingEnable) || (!localSmartRoutingEnable && spxSmartRoutingEnable)) {
			return true, nil
		}
	case ruledata.CbProduct: //SPLPS-7388: delete mutex of CB smart routing role && SPX smart routing role
		if !localSmartRoutingEnable {
			return true, nil
		}
	}
	return false, srerr.New(srerr.ParamErr, nil, "local can only choose spx or local, cb can only choose spx and cb")
}

func (r *RoutingConfigServiceImpl) CopyRoutingConfigTab(data *ruledata.RoutingConfigTab) *rule.RoutingConfig {
	config := &rule.RoutingConfig{}
	if data != nil {
		config.Id = data.Id
		config.ProductID = data.ProductID
		config.SmartRoutingEnabled = data.SmartRoutingEnabled
		config.CBMultiSmartRoutingEnabled = data.CBMultiRoutingEnabled
		config.LocalSmartRoutingEnabled = data.LocalSmartRoutingEnabled
		config.SpxSmartRoutingEnabled = data.SpxSmartRoutingEnabled
		config.IlhSmartRoutingEnabled = data.IlhSmartRoutingEnabled
		config.DefaultLaneCode = data.DefaultLaneCode
		config.OperatedBy = data.OperatedBy
		config.CTime = data.CTime
		config.MTime = data.MTime
	}

	return config
}

func (r *RoutingConfigServiceImpl) ListAllRoutingConfig(ctx context.Context) ([]*ruledata.RoutingConfigTab, *srerr.Error) {
	ret, err := r.routingConfigRepo.ListAllRoutingConfig(ctx)
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (r *RoutingConfigServiceImpl) ListRoutingConfigByRoutingType(ctx context.Context, routingType uint8) ([]*rule.RoutingConfig, *srerr.Error) {
	routingConfTabList, err := r.routingConfigRepo.ListConfigByRoutingType(ctx, routingType)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	routingConfList := make([]*rule.RoutingConfig, 0, len(routingConfTabList))
	for _, r := range routingConfTabList {
		routingConfList = append(routingConfList, &rule.RoutingConfig{
			Id:                         r.Id,
			ProductID:                  r.ProductID,
			SmartRoutingEnabled:        r.SmartRoutingEnabled,
			CBMultiSmartRoutingEnabled: r.CBMultiRoutingEnabled,
			LocalSmartRoutingEnabled:   r.LocalSmartRoutingEnabled,
			SpxSmartRoutingEnabled:     r.SpxSmartRoutingEnabled,
			IlhSmartRoutingEnabled:     r.IlhSmartRoutingEnabled,
			DefaultLaneCode:            r.DefaultLaneCode,
			OperatedBy:                 r.OperatedBy,
			CTime:                      r.CTime,
			MTime:                      r.MTime,
		})
	}

	return routingConfList, nil
}

func (r *RoutingConfigServiceImpl) GetSmrScenes(ctx context.Context, pid int64) *schema.SmrScenesWithActiveRules {
	ret := &schema.SmrScenesWithActiveRules{
		CBRoutingEnabled:         true,
		CBMultiRoutingEnabled:    true,
		LocalSmartRoutingEnabled: true,
		SpxSmartRoutingEnabled:   true,
		IlhSmartRoutingEnabled:   true,
	}
	//
	tabs, _ := r.ruleRepo.GetRoutingRuleByCondition(ctx, map[string]interface{}{
		"product_id = ?":  pid,
		"rule_status = ?": rule.RuleStatusActive,
	})
	//checked,maps[],
	activeMaps := map[int]bool{}
	for _, tab := range tabs {
		if tab.RoutingType == rule.SPXRoutingType {
			activeMaps[rule.SPXRoutingType] = true
		}
		if tab.RoutingType == rule.IlhRoutingType {
			activeMaps[rule.IlhRoutingType] = true
		}
		if tab.RoutingType == rule.LocalRoutingType {
			activeMaps[rule.LocalRoutingType] = true
		}

		if tab.RoutingType == rule.CBRoutingType {
			activeMaps[rule.CBRoutingType] = true
		}

		if tab.RoutingType == rule.CBRoutingType && tab.IsMultiProduct {
			activeMaps[rule.CbMultiRoutingType] = true
		}
	}
	//
	ret.SpxSmartRoutingEnabled = activeMaps[rule.SPXRoutingType]
	ret.LocalSmartRoutingEnabled = activeMaps[rule.LocalRoutingType]
	ret.CBRoutingEnabled = activeMaps[rule.CBRoutingType]
	ret.CBMultiRoutingEnabled = activeMaps[rule.CbMultiRoutingType]
	ret.IlhSmartRoutingEnabled = activeMaps[rule.IlhRoutingType]
	return ret
}
