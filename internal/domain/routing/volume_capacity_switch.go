package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"hash/crc32"
	"strconv"
)

func CheckSwitchToVolumeCapacityV2(ctx context.Context, productId int64, forderId string) bool {

	cfg := configutil.GetVolumeSwitch(ctx)
	//fmt.Println("refresh cfg,", cfg)
	useNewPercent := cfg[strconv.FormatInt(productId, 10)]
	//fast-path
	if useNewPercent == 0 {
		return false
	}
	if useNewPercent == 100 {
		return true
	}
	//针对admin 配置页面的开关，无需灰度,只有关/闭2种状态，
	//case1 ,配为0 ，0711 ---ok ,
	//case2 ,>0 ,逐渐扩大灰度，0712 ，----ok
	if forderId == "" {
		if useNewPercent > 0 {
			return true
		}
	}
	//[0,100) ---> [1,100] ,
	return int(crc32.ChecksumIEEE([]byte(forderId))%100)+1 <= useNewPercent
}
