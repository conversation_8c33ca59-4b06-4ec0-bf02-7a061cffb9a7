package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/helper"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"sort"
)

type RuleMatchParam struct {
	ProductId         int64
	WhsID             string
	LocationIDList    []uint64
	Cogs              float32
	ValidationWeight  int
	ItemCategoryInfos []*ItemCategoryInfo
	RoutingType       uint8
	DgType            int
	ParcelLength      float64
	ParcelWidth       float64
	ParcelHeight      float64
	DestinationPorts  []string
	IsMultiProduct    bool
	Forderid          string
	ZoneType          uint8
	DeliverPostCode   string
	ShopId            string
}

type ItemCategoryInfo struct {
	GlobalCategoryIdL1 int
	GlobalCategoryIdL2 int
	GlobalCategoryIdL3 int
	GlobalCategoryIdL4 int
	GlobalCategoryIdL5 int
}

func ParseRuleData(data *ruledata.RoutingRuleTab, isDetail bool) (*rule.RoutingRule, *srerr.Error) {
	ruleData := &rule.RoutingRule{
		ID:                 data.ID,
		ProductID:          data.ProductID,
		Status:             data.Status,
		RuleName:           data.RuleName,
		Priority:           data.Priority,
		OperatedBy:         data.OperatedBy,
		EffectiveStartTime: data.EffectiveStartTime,
		TaskID:             int(data.TaskID),
		CTime:              data.CTime,
		MTime:              data.MTime,
		RoutingType:        data.RoutingType,
		ItemCategoryLevel:  data.ItemCategoryLevel,
		ParcelValueMax:     data.ParcelValueMax,
		ParcelValueMin:     data.ParcelValueMin,
		ParcelWeightMax:    data.ParcelWeightMax,
		ParcelWeightMin:    data.ParcelWeightMin,
		IsMultiProduct:     data.IsMultiProduct,
		DgType:             data.DgType,
		ParcelDimension:    data.ParcelDimension,
		WmsToggleEnable:    data.WmsToggleEnable,
		CCMode:             data.CCMode,
		CombinationSetting: make([]*rule.CombinationSetting, 0),
		VolumeRuleId:       data.VolumeRuleId,
	}
	ruleData.WhsId = objutil.SetStringToSlice(data.WhsId)
	ruleData.DestinationPorts = objutil.SetStringToSlice(data.DestinationPorts)
	if isDetail {
		ruleDetail, err := UnmarshalRuleDetail(data.Rules, ruleData)
		if err != nil {
			return nil, srerr.With(srerr.RoutingRuleException, data.ProductID, err)
		}
		ruleData.RuleDetails = ruleDetail
		// 是否启用volume2，判断的条件是开关是否打开，以及volumeRuleId是否为0
		ruleData.IsVolumeRouting = openVolumeV2(ruleData.VolumeRuleId, ruleData.RoutingType, ruleData.IsMultiProduct)
		if string(data.StrCombinationSetting) != "" {
			if err := jsoniter.Unmarshal(data.StrCombinationSetting, &ruleData.CombinationSetting); err != nil {
				return nil, srerr.With(srerr.RoutingRuleException, data.ProductID, err)
			}
		}

		if data.IsMultiProduct && data.RoutingType == rule.CBRoutingType {
			defaultCriteria, err := unmarshalMultiProductDefaultCriteria(data.StrDefaultCriteria)
			if err != nil {
				return nil, srerr.With(srerr.ParamErr, data.ProductID, err)
			}
			ruleData.MultiProductDefaultCriteria = defaultCriteria

			multiProductDisableInfo, err := unmarshalMultiProductDisabledInfo(data.StrDisabledInfo)
			if err != nil {
				return nil, srerr.With(srerr.ParamErr, data.ProductID, err)
			}
			ruleData.MultiProductDisableInfo = multiProductDisableInfo
		} else {
			defaultCriteria, err := unmarshalDefaultCriteria(data.StrDefaultCriteria)
			if err != nil {
				return nil, srerr.With(srerr.ParamErr, data.ProductID, err)
			}
			ruleData.DefaultCriteria = defaultCriteria

			disableInfo, err := unmarshalDisabledInfo(data.StrDisabledInfo)
			if err != nil {
				return nil, srerr.With(srerr.ParamErr, data.ProductID, err)
			}
			ruleData.DisabledInfo = disableInfo
		}
		ruleData.ZoneCode = objutil.SetStringToSlice(data.ZoneCode)
		ruleData.ItemCategoryID = objutil.SetStringToIntSlice(data.ItemCategoryIDList)
		ruleData.DestinationPorts = objutil.SetStringToSlice(data.DestinationPorts)
		//SSCSMR-3053 unmarshal shop group list vo
		data.UnmarshalShopGroupList()
		ruleData.ShopGroupListVo = data.ShopGroupListVo
	}

	return ruleData, nil
}

func (rr *routingRuleRepoImpl) checkRuleRoutingType(routingType uint8, conf *rule.RoutingConfig) bool {
	if conf == nil {
		return true
	}
	switch routingType {
	case rule.CBRoutingType:
		return conf.SmartRoutingEnabled
	case rule.SPXRoutingType:
		return conf.SpxSmartRoutingEnabled
	case rule.LocalRoutingType:
		return conf.LocalSmartRoutingEnabled
	}
	return true
}

func UnmarshalRuleDetail(detailData []byte, data *rule.RoutingRule) (*rule.RuleDetails, error) {
	var detail *rule.RuleDetails
	err := jsoniter.Unmarshal(detailData, &detail)
	if err != nil {
		return nil, err
	}
	for _, data := range detail.Rules {
		data.DisplayResourceType = lfslib.LineSubTypeMap[data.ResourceSubType]
	}
	return detail, nil
}

func unmarshalDefaultCriteria(defaultData []byte) (*rule.DefaultCriteria, error) {
	var detail *rule.DefaultCriteria
	err := jsoniter.Unmarshal(defaultData, &detail)
	if err != nil {
		return nil, err
	}
	detail.CriteriaName = detail.CriteriaType.String()
	for _, data := range detail.WeightageCriteria {
		data.DisplayResourceType = lfslib.LineSubTypeMap[data.ResourceSubType]
	}
	return detail, nil
}

func unmarshalDisabledInfo(InfoData []byte) ([]*rule.DisabledInfo, error) {
	detail := make([]*rule.DisabledInfo, 0)
	err := jsoniter.Unmarshal(InfoData, &detail)
	if err != nil {
		return nil, err
	}
	for _, data := range detail {
		data.DisplayResourceType = lfslib.LineSubTypeMap[data.ResourceSubType]
	}
	return detail, nil
}

func unmarshalMultiProductDefaultCriteria(defaultData []byte) (*rule.MultiProductDefaultCriteria, error) {
	var detail *rule.MultiProductDefaultCriteria
	err := jsoniter.Unmarshal(defaultData, &detail)
	if err != nil {
		return nil, err
	}
	detail.CriteriaName = detail.CriteriaType.String()
	for i, data := range detail.WeightageCriteria.LineCriteriaInfo {
		detail.WeightageCriteria.LineCriteriaInfo[i].DisplayResourceType = lfslib.LineSubTypeMap[data.ResourceSubType]
	}
	return detail, nil
}

func unmarshalMultiProductDisabledInfo(InfoData []byte) (*rule.MultiProductDisabledInfo, error) {
	detail := &rule.MultiProductDisabledInfo{}
	err := jsoniter.Unmarshal(InfoData, &detail)
	if err != nil {
		return nil, err
	}
	for i, data := range detail.LineDisableInfo {
		detail.LineDisableInfo[i].DisplayResourceType = lfslib.LineSubTypeMap[data.ResourceSubType]
	}

	return detail, nil
}

func RebuildRuleData(ctx context.Context, effectiveRule *rule.RoutingRule) (*rule.RoutingRuleParsed, *srerr.Error) {
	if effectiveRule.RuleDetails == nil || effectiveRule.RuleDetails.Rules == nil {
		logger.CtxLogErrorf(ctx, "get effectiveRule nil error: |%v", effectiveRule)
		return nil, srerr.New(srerr.ParamErr, nil, "invalid routing rule config")
	}
	// sort soft Criteria and compose rule struct
	ruleStepResources := rule.RuleStepResources{}
	seenLines := map[string]bool{}
	lineToggle := make(map[string]*rule.Toggle)
	for _, ruleDetail := range effectiveRule.RuleDetails.Rules {
		ruleSteps := rule.RuleSteps{}
		//记录开启和关闭的line
		if effectiveRule.IsMultiProduct && effectiveRule.RoutingType == rule.CBRoutingType {
			setMultiLineToggle(ruleDetail.DisplayResourceType, lineToggle, ruleDetail.LineLimit, effectiveRule.MultiProductDisableInfo)
		} else {
			setLineToggle(ruleDetail.DisplayResourceType, lineToggle, ruleDetail.LineLimit, effectiveRule.DisabledInfo)
		}

		for _, limit := range ruleDetail.LineLimit {
			seenLines[limit.LineId] = true
		}

		if ruleDetail.DgClassificationEnable && ruleDetail.DgRelated == 1 {
			step := rule.ScheduleFactorAttr{
				Name:     schedule_factor.Dg,
				Priority: ruleDetail.DgClassificationSort,
				DgFlagData: &rule.DgFlagData{
					DgPriority: ruleDetail.DgPriority,
				},
			}
			logger.CtxLogInfof(ctx, "rule dg classification step=%v", step)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MinVolumeEnable {
			minVolumes := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				minVolumes[key] = limit.MinVolume
			}
			step := rule.ScheduleFactorAttr{
				Name:          schedule_factor.MinVolume,
				Priority:      ruleDetail.MinVolumeSort,
				MinVolumeData: &rule.MinVolumeData{MinVolumes: minVolumes},
			} // IsVolumeRouting在解析rule的时候赋值(spx和cb)、local保持原来的不动
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MinVolumeV2Name
			}
			logger.CtxLogInfof(ctx, "rule min volume step=%v|MinVolumeData=%v|Priority=%d", step, step.MinVolumeData.MinVolumes, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxCapacityEnable {
			maxCapacities := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				maxCapacities[key] = limit.MaxCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:            schedule_factor.MaxCapacity,
				Priority:        ruleDetail.MaxCapacitySort,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxCapacities},
			} // IsVolumeRouting在解析rule的时候赋值(spx和cb)、local保持原来的不动
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MaxCapacityV2Name
			}
			logger.CtxLogInfof(ctx, "rule max capacity step=%v|MaxCapacityData=%v|Priority=%d", step, step.MaxCapacityData.MaxCapacities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxCodCapacityEnable {
			maxCodCapacities := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				maxCodCapacities[key] = limit.MaxCodCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:            schedule_factor.MaxCodCapacity,
				Priority:        ruleDetail.MaxCodCapacitySort,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxCodCapacities},
			}
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MaxCodCapacityV2Name
			}
			logger.CtxLogInfof(ctx, "rule max Cod capacity step=%v|MaxCodCapacityData=%v|Priority=%d", step, step.MaxCapacityData.MaxCapacities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxBulkyCapacityEnable {
			maxBulkyCapacities := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				maxBulkyCapacities[key] = limit.MaxBulkyCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:            schedule_factor.MaxBulkyCapacity,
				Priority:        ruleDetail.MaxBulkyCapacitySort,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxBulkyCapacities},
			}
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MaxBulkyCapacityV2Name
			}
			logger.CtxLogInfof(ctx, "rule max Bulky capacity step=%v|MaxBulkyCapacityData=%v|Priority=%d", step, step.MaxCapacityData.MaxCapacities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxHighValueCapacityEnable {
			maxHighValueCapacities := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				maxHighValueCapacities[key] = limit.MaxHighValueCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:            schedule_factor.MaxHighValueCapacity,
				Priority:        ruleDetail.MaxHighValueCapacitySort,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxHighValueCapacities},
			}
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MaxHighValueCapacityV2Name
			}
			logger.CtxLogInfof(ctx, "rule max HighValue capacity step=%v|MaxHighValueCapacityData=%v|Priority=%d", step, step.MaxCapacityData.MaxCapacities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxDgCapacityEnable {
			maxDgCapacities := map[string]int{}
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				maxDgCapacities[key] = limit.MaxDgCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:            schedule_factor.MaxDgCapacity,
				Priority:        ruleDetail.MaxDgCapacitySort,
				MaxCapacityData: &rule.MaxCapacityData{MaxCapacities: maxDgCapacities},
			}
			if effectiveRule.IsVolumeRouting {
				step.Name = schedule_factor.MaxDgCapacityV2Name
			}
			logger.CtxLogInfof(ctx, "rule max Dg capacity step=%v|MaxDgCapacityData=%v|Priority=%d", step, step.MaxCapacityData.MaxCapacities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.LineCheapestShippingFeePriorityEnable {
			step := rule.ScheduleFactorAttr{
				Name:     schedule_factor.LineCheapestShippingFee,
				Priority: ruleDetail.LineCheapestShippingFeePrioritySort,
			}
			logger.CtxLogInfof(ctx, "line cheapest shipping fee step=%v|Priority=%d", step, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.LinePriorityEnable {
			linePriorities := map[string]int32{}
			for _, p := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(p.LineId, p.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				linePriorities[key] = p.Priority
			}
			step := rule.ScheduleFactorAttr{
				Name:             schedule_factor.LinePriority,
				Priority:         ruleDetail.LinePrioritySort,
				LinePriorityData: &rule.LinePriorityData{LinePriorities: linePriorities},
			}
			logger.CtxLogInfof(ctx, "line priority step=%v|LinePriorityData=%v|Priority=%d", step, step.LinePriorityData.LinePriorities, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MinWeightEnable {
			lineMinWeights := make(map[string]int)
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				lineMinWeights[key] = limit.MinWeight
			}
			step := rule.ScheduleFactorAttr{
				Name:          schedule_factor.MinWeight,
				Priority:      ruleDetail.MinWeightSort,
				MinWeightData: &rule.MinWeightData{MinWeights: lineMinWeights},
			}
			logger.CtxLogInfof(ctx, "min weight step=%v|MinWeightData=%v|Priority=%d", step, step.MinWeightData.MinWeights, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		if ruleDetail.MaxWeightCapacityEnable {
			lineMaxWeights := make(map[string]int)
			for _, limit := range ruleDetail.LineLimit {
				key := helper.FormatLineLimitKey(limit.LineId, limit.DGFlag, rule.DgRelated, effectiveRule.RoutingType)
				lineMaxWeights[key] = limit.MaxWeightCapacity
			}
			step := rule.ScheduleFactorAttr{
				Name:          schedule_factor.MaxWeight,
				Priority:      ruleDetail.MaxWeightCapacitySort,
				MaxWeightData: &rule.MaxWeightData{MaxWeights: lineMaxWeights},
			}
			logger.CtxLogInfof(ctx, "max weight step=%v|MaxWeightData=%v|Priority=%d", step, step.MaxWeightData.MaxWeights, step.Priority)
			ruleSteps = append(ruleSteps, &step)
		}

		sort.Sort(ruleSteps) // sort rule priority
		ruleStepResource := &rule.RuleStepResource{
			RuleSteps:           ruleSteps,
			ResourceSubType:     ruleDetail.ResourceSubType,
			Priority:            ruleDetail.Priority,
			DisplayResourceType: ruleDetail.DisplayResourceType,
		}
		ruleStepResources = append(ruleStepResources, ruleStepResource)
	}
	// sort sub type resource list
	sort.Sort(ruleStepResources)
	//ruleScheduler.RuleStepResourceList = ruleStepResources
	ruleScheduler := &rule.RoutingRuleParsed{
		ID:                   effectiveRule.ID,
		RuleStepResourceList: ruleStepResources,
		RoutingType:          effectiveRule.RoutingType,
		WhsId:                effectiveRule.WhsId,
		DestinationPort:      effectiveRule.DestinationPorts,
		ZoneCode:             effectiveRule.ZoneCode,
		ItemCategoryLevel:    effectiveRule.ItemCategoryLevel,
		ItemCategoryID:       effectiveRule.ItemCategoryID,
		ParcelValueMax:       effectiveRule.ParcelValueMax,
		ParcelValueMin:       effectiveRule.ParcelValueMin,
		ParcelWeightMax:      effectiveRule.ParcelWeightMax,
		ParcelWeightMin:      effectiveRule.ParcelWeightMin,
		DgType:               effectiveRule.DgType,
		ParcelDimension:      effectiveRule.ParcelDimension,
		WmsToggleEnable:      effectiveRule.WmsToggleEnable,
		LineToggle:           lineToggle,
		CCMode:               effectiveRule.CCMode,
		VolumeRuleId:         effectiveRule.VolumeRuleId,
		ShopGroupList:        effectiveRule.ShopGroupListVo,
	}

	if effectiveRule.IsMultiProduct && effectiveRule.RoutingType == rule.CBRoutingType {
		ruleScheduler.AvailableLines = getMultiProductAvailableLines(seenLines, effectiveRule.MultiProductDisableInfo)
		ruleScheduler.AvailableDgGroup = getMultiProductAvailableDgGroups(effectiveRule.MultiProductDisableInfo)
		ruleScheduler.DefaultPriorities = getMultiProductLineWeightages(effectiveRule.MultiProductDefaultCriteria.WeightageCriteria)
		ruleScheduler.DefaultCriteriaName = getDefaultCriteriaName(effectiveRule.MultiProductDefaultCriteria.CriteriaType)
		ruleScheduler.DefaultCriteriaType = effectiveRule.MultiProductDefaultCriteria.CriteriaType

	} else {
		ruleScheduler.AvailableLines = getAvailableLines(seenLines, effectiveRule.DisabledInfo)
		ruleScheduler.WmsAvailableLines = getWmsAvailableLines(seenLines, effectiveRule.DisabledInfo)
		ruleScheduler.DefaultPriorities = addLinePriorities(effectiveRule.DefaultCriteria)
		ruleScheduler.DefaultCriteriaName = getDefaultCriteriaName(effectiveRule.DefaultCriteria.CriteriaType)
		ruleScheduler.DefaultCriteriaType = effectiveRule.DefaultCriteria.CriteriaType
	}

	return ruleScheduler, nil
}

func getDefaultCriteriaName(in rule.CriteriaType) string {
	switch in {
	case rule.CriteriaWeightAge:
		return schedule_factor.DefaultWeightage
	case rule.CriteriaPriority:
		return schedule_factor.DefaultPriority
	default:
		return ""
	}
}

func getAvailableLines(seeLines map[string]bool, disabledInfo []*rule.DisabledInfo) []string {
	tempSeeLines := make(map[string]bool, len(seeLines))
	for line, v := range seeLines {
		tempSeeLines[line] = v
	}
	// move disable line
	var availableLines []string
	for _, disable := range disabledInfo {
		for _, lineId := range disable.LineList {
			delete(tempSeeLines, lineId)
		}
	}
	for lineId := range tempSeeLines {
		availableLines = append(availableLines, lineId)
	}
	return availableLines
}

func getWmsAvailableLines(seeLines map[string]bool, disabledInfo []*rule.DisabledInfo) []string {
	tempSeeLines := make(map[string]bool, len(seeLines))
	for line, v := range seeLines {
		tempSeeLines[line] = v
	}
	// move disable line
	var availableLines []string
	for _, disable := range disabledInfo {
		for _, lineId := range disable.WmsLineList {
			delete(tempSeeLines, lineId)
		}
	}
	for lineId := range tempSeeLines {
		availableLines = append(availableLines, lineId)
	}
	return availableLines
}

func getMultiProductAvailableLines(seeLines map[string]bool, disabledInfo *rule.MultiProductDisabledInfo) []string {
	// move disable line
	var availableLines []string
	for _, disable := range disabledInfo.LineDisableInfo {
		for _, LineId := range disable.LineList {
			delete(seeLines, LineId)
		}
	}
	for lineId := range seeLines {
		availableLines = append(availableLines, lineId)
	}
	return availableLines
}

func setMultiLineToggle(lineType string, lineToggle map[string]*rule.Toggle, lineLimit []*rule.LineLimit, disabledInfo *rule.MultiProductDisabledInfo) {
	var disableLine []string
	if lineToggle[lineType] == nil {
		lineToggle[lineType] = &rule.Toggle{}
	}
	//没有开启开关的line
	for _, disableLane := range disabledInfo.LineDisableInfo {
		for _, line := range disableLane.LineList {
			disableLine = append(disableLine, line)
			if lineToggle[disableLane.DisplayResourceType] == nil {
				lineToggle[disableLane.DisplayResourceType] = &rule.Toggle{}
			}
			if !objutil.ContainsString(lineToggle[disableLane.DisplayResourceType].Off, line) {
				lineToggle[disableLane.DisplayResourceType].Off = append(lineToggle[disableLane.DisplayResourceType].Off, line)
			}
		}
	}
	for _, line := range lineLimit {
		if !objutil.ContainsString(disableLine, line.LineId) {
			lineToggle[lineType].On = append(lineToggle[lineType].On, line.LineId)
		}
	}
}

func setLineToggle(lineType string, lineToggle map[string]*rule.Toggle, lineLimit []*rule.LineLimit, disabledInfo []*rule.DisabledInfo) {
	var disableLine []string
	if lineToggle[lineType] == nil {
		lineToggle[lineType] = &rule.Toggle{}
	}
	//没有开启开关的line
	for _, disableLane := range disabledInfo {
		for _, line := range disableLane.LineList {
			disableLine = append(disableLine, line)
			if lineToggle[disableLane.DisplayResourceType] == nil {
				lineToggle[disableLane.DisplayResourceType] = &rule.Toggle{}
			}
			if !objutil.ContainsString(lineToggle[disableLane.DisplayResourceType].Off, line) {
				lineToggle[disableLane.DisplayResourceType].Off = append(lineToggle[disableLane.DisplayResourceType].Off, line)
			}
		}
	}
	for _, line := range lineLimit {
		if !objutil.ContainsString(disableLine, line.LineId) {
			lineToggle[lineType].On = append(lineToggle[lineType].On, line.LineId)
		}
	}
}

func getMultiProductAvailableDgGroups(disabledInfo *rule.MultiProductDisabledInfo) []string {
	// move disable line
	var availableDgGroups []string
	for _, dgGroup := range disabledInfo.DgGroupDisableInfo.DgGroupList {
		if dgGroup.Enable {
			availableDgGroups = append(availableDgGroups, dgGroup.DgGroupId)
		}
	}

	return availableDgGroups
}

func addLinePriorities(criteria *rule.DefaultCriteria) map[int32]map[rule.CriteriaType]map[string]int {
	defaultCriteria := make(map[int32]map[rule.CriteriaType]map[string]int)
	for _, w := range criteria.WeightageCriteria {
		lineWeightAge := make(map[string]int)
		for _, p := range w.LineInfoList {
			lineKey := helper.FormatLineAndDgKey(p.LineId, int(p.DGFlag), rule.DgRelated)
			lineWeightAge[lineKey] = p.WeightAge
		}
		criteriaMap := make(map[rule.CriteriaType]map[string]int)
		criteriaMap[rule.CriteriaWeightAge] = lineWeightAge
		defaultCriteria[w.ResourceSubType] = criteriaMap
	}
	for _, w := range criteria.PriorityCriteria {
		linePriority := make(map[string]int)
		for _, p := range w.LineInfoList {
			lineKey := helper.FormatLineAndDgKey(p.LineId, int(p.DGFlag), rule.DgRelated)
			linePriority[lineKey] = p.Priority
		}
		if defaultCriteria[w.ResourceSubType] == nil {
			defaultCriteria[w.ResourceSubType] = make(map[rule.CriteriaType]map[string]int)
		}
		defaultCriteria[w.ResourceSubType][rule.CriteriaPriority] = linePriority
	}

	return defaultCriteria
}

func getMultiProductLineWeightages(criteria rule.MultiProductCriteriaInfo) map[int32]map[rule.CriteriaType]map[string]int {
	defaultCriteria := make(map[int32]map[rule.CriteriaType]map[string]int)
	for _, w := range criteria.LineCriteriaInfo {
		lineWeightAge := make(map[string]int)
		for _, p := range w.LineInfoList {
			lineKey := helper.FormatLineAndDgKey(p.LineId, int(p.DGFlag), rule.DgRelated)
			lineWeightAge[lineKey] = p.WeightAge
		}
		criteriaMap := make(map[rule.CriteriaType]map[string]int)
		criteriaMap[rule.CriteriaWeightAge] = lineWeightAge
		defaultCriteria[w.ResourceSubType] = criteriaMap
	}

	ilhWeightAge := make(map[string]int)
	for _, dgGroup := range criteria.DgGroupCriteriaInfo.DgGroupList {
		for _, p := range dgGroup.LineList {
			lineKey := helper.FormatLineAndDgKey(p.LineId, int(p.DGFlag), rule.DgRelated)
			ilhWeightAge[lineKey] = p.WeightAge
		}
	}
	weightageCriteria := make(map[rule.CriteriaType]map[string]int)
	weightageCriteria[rule.CriteriaWeightAge] = ilhWeightAge
	for _, lineSubType := range lfslib.NeedRoutingILH {
		defaultCriteria[int32(lineSubType)] = weightageCriteria
	}

	return defaultCriteria
}

func CheckItemCategoryInRule(itemCategoryLevel int, ruleCategoryIdList []int, itemInfos []*ItemCategoryInfo) bool {
	for _, itemInfo := range itemInfos {
		var levelCategoryId int
		switch itemCategoryLevel {
		case 1:
			levelCategoryId = itemInfo.GlobalCategoryIdL1
		case 2:
			levelCategoryId = itemInfo.GlobalCategoryIdL2
		case 3:
			levelCategoryId = itemInfo.GlobalCategoryIdL3
		case 4:
			levelCategoryId = itemInfo.GlobalCategoryIdL4
		case 5:
			levelCategoryId = itemInfo.GlobalCategoryIdL5
		}
		if objutil.ContainsInt(ruleCategoryIdList, levelCategoryId) {
			return true
		}
	}

	return false
}

func GetAvailableCombination(ctx context.Context, combinationSettings []*rule.CombinationSetting) map[rule.IlhCombination]struct{} {
	availableCombination := make(map[rule.IlhCombination]struct{})
	for _, combinationSetting := range combinationSettings {
		for _, group := range combinationSetting.ConnectLines {
			if len(group.Lines) != 2 {
				reportData := fmt.Sprintf("combination connect line list length not equal to 2, line list=%v", group.Lines)
				logger.CtxLogErrorf(ctx, reportData)
				_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleILHRoutingCCMode, monitoring.CombinationInfoInValid,
					monitoring.StatusError, reportData)
				continue
			}
			availableCombination[rule.IlhCombination{
				ImportIlh: combinationSetting.LineId,
				Ilh:       group.Lines[0].LineId,
				Lm:        group.Lines[1].LineId,
			}] = struct{}{}
		}
	}

	return availableCombination
}

// local的保持原有的逻辑CheckSwitchToVolumeCapacityV2，spx和cb只有LM用到v2运力
func openVolumeV2(volumeRuleId int64, routingType uint8, isMultiProduct bool) bool {
	if routingType == rule.SPXRoutingType && volumeRuleId != 0 {
		return true
	} else if routingType == rule.CBRoutingType && isMultiProduct && volumeRuleId != 0 {
		return true
	} else if routingType == rule.LocalRoutingType {
		return true
	}
	return false
}
