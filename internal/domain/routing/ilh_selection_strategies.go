package routing

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
)

// ReservedBSAStrategy 预留BSA容量选择策略
type ReservedBSAStrategy struct {
	rs *ILHRoutingServiceImpl
}

// BSAStrategy BSA容量选择策略
type BSAStrategy struct {
	rs *ILHRoutingServiceImpl
}

// AdhocStrategy Adhoc容量选择策略
type AdhocStrategy struct {
	rs *ILHRoutingServiceImpl
}

// WeightageStrategy 权重选择策略(兜底策略)
type WeightageStrategy struct {
}

// StrategyName 返回策略名称
func (s *ReservedBSAStrategy) StrategyName() string {
	return "ReservedBSA"
}

// StrategyName 返回策略名称
func (s *BSAStrategy) StrategyName() string {
	return "BSA"
}

// StrategyName 返回策略名称
func (s *AdhocStrategy) StrategyName() string {
	return "Adhoc"
}

// StrategyName 返回策略名称
func (s *WeightageStrategy) StrategyName() string {
	return "Weightage"
}

// Select 根据预留BSA容量选择ILH
func (s *ReservedBSAStrategy) Select(
	ctx context.Context,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	input ILHSelectionInput,
) (string, bool, *ILHSelectionResult) {

	selectedILH, found, result, err := s.rs.selectILHByReservedBSACapacityWithUsage(
		ctx, input, ilhLines, ilhCapacitySettingMap, availableLineInfoMap,
	)

	if err != nil {
		logger.CtxLogErrorf(ctx, "Error selecting ILH by reserved BSA capacity: %v", err)
		return "", false, nil
	}

	if found && result != nil {
		logger.CtxLogInfof(ctx, "Selected ILH by reserved BSA capacity: %s, mode: %s, slot: %s, usage: %d",
			selectedILH, result.CapacityMode, result.SlotID, result.ReservedBSAUsage)
		return selectedILH, true, result
	}

	return "", false, nil
}

// Select 根据BSA容量选择ILH
func (s *BSAStrategy) Select(
	ctx context.Context,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	input ILHSelectionInput,
) (string, bool, *ILHSelectionResult) {

	selectedILH, found, result, err := s.rs.selectILHByBSACapacityWithUsage(
		ctx, input, ilhLines, ilhCapacitySettingMap, availableLineInfoMap,
	)

	if err != nil {
		logger.CtxLogErrorf(ctx, "Error selecting ILH by BSA capacity: %v", err)
		return "", false, nil
	}

	if found && result != nil {
		logger.CtxLogInfof(ctx, "Selected ILH by BSA capacity: %s, mode: %s, slot: %s, reservedUsage: %v, nonReservedUsage: %v",
			selectedILH, result.CapacityMode, result.SlotID, result.ReservedBSAUsage, result.NonReservedBSAUsage)
		return selectedILH, true, result
	}

	return "", false, nil
}

// Select 根据Adhoc容量选择ILH
func (s *AdhocStrategy) Select(
	ctx context.Context,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	input ILHSelectionInput,
) (string, bool, *ILHSelectionResult) {

	selectedILH, found, result, err := s.rs.selectILHByAdhocCapacityWithUsage(
		ctx, input, ilhLines, ilhCapacitySettingMap, availableLineInfoMap,
	)

	if err != nil {
		logger.CtxLogErrorf(ctx, "Error selecting ILH by Adhoc capacity: %v", err)
		return "", false, nil
	}

	if found && result != nil {
		logger.CtxLogInfof(ctx, "Selected ILH by Adhoc capacity: %s, mode: %s, slot: %s, adhocUsage: %v",
			selectedILH, result.CapacityMode, result.SlotID, result.AdhocUsage)
		return selectedILH, true, result
	}

	return "", false, nil
}

// Select 根据权重选择ILH (兜底方案)
func (s *WeightageStrategy) Select(
	ctx context.Context,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	input ILHSelectionInput,
) (string, bool, *ILHSelectionResult) {
	selectedILH := selectByWeightage(ctx, ilhLines, availableLineInfoMap)
	if selectedILH == "" {
		return "", false, nil
	}

	logger.CtxLogInfof(ctx, "Selected ILH by weightage (fallback): %s", selectedILH)
	// 对于纯权重选择（无容量配置或容量均不足），认为使用量都在Adhoc上，但模式标记为NoCapacity
	return selectedILH, true, &ILHSelectionResult{
		LineID:              selectedILH,
		ReservedBSAUsage:    0,
		NonReservedBSAUsage: 0,
		AdhocUsage:          input.OrderWeight,
		CapacityMode:        CapacityModeWeightage,
		SlotID:              entity2.DefaultSlotID, // 使用默认的时间段ID
	}
}
