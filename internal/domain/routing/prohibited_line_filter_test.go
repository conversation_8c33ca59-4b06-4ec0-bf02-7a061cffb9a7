package routing

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"reflect"
	"testing"
)

func TestRoutingServiceImpl_multiProductFilterByProhibitedLine(t *testing.T) {
	ctx := context.TODO()
	rs := &RoutingServiceImpl{}
	type args struct {
		lanes       []*rule.RoutingLaneInfo
		matchedRule *rule.RoutingRuleParsed
	}
	tests := []struct {
		name string
		args args
		want []*rule.RoutingLaneInfo
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				lanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceSubType: rule.LineTypeNoNeedRouting,
							},
							{
								LineId:          "Line-2",
								ResourceSubType: lfslib.C_ILH,
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-3",
								ResourceId:      "Resource-3",
								ResourceSubType: lfslib.C_ILH,
							},
						},
						DgGroupId: "DG-Group-2",
					},
					{
						LaneCode: "Lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-4",
								ResourceId:      "Resource-4",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				matchedRule: &rule.RoutingRuleParsed{
					AvailableDgGroup: []string{"DG-Group-2"},
					AvailableLines:   []string{"Resource-5"},
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-2",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-3",
							ResourceId:      "Resource-3",
							ResourceSubType: lfslib.C_ILH,
						},
					},
					DgGroupId: "DG-Group-2",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := rs.multiProductFilterByProhibitedLine(ctx, tt.args.lanes, tt.args.matchedRule); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("multiProductFilterByProhibitedLine() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRoutingServiceImpl_filterByProhibitedLine(t *testing.T) {
	ctx := context.TODO()
	rs := &RoutingServiceImpl{}
	type args struct {
		lanes       []*rule.RoutingLaneInfo
		matchedRule *rule.RoutingRuleParsed
		orderData   *rule.SmartRoutingOrderData
		logEntry    *routing_log.RoutingLog
	}
	tests := []struct {
		name string
		args args
		want []*rule.RoutingLaneInfo
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				lanes: []*rule.RoutingLaneInfo{
					{
						LaneCode: "Lane-1",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-1",
								ResourceSubType: rule.LineTypeNoNeedRouting,
							},
							{
								LineId:          "Line-2",
								ResourceSubType: lfslib.C_M_ILH,
							},
							{
								LineId:          "Line-3",
								ResourceId:      "Resource-3",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
					{
						LaneCode: "Lane-2",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-4",
								ResourceId:      "Resource-4",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
					{
						LaneCode: "Lane-3",
						LineList: []*rule.LineInfo{
							{
								LineId:          "Line-5",
								ResourceId:      "Resource-5",
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				matchedRule: &rule.RoutingRuleParsed{
					AvailableLines:    []string{"Resource-4", "Resource-5"},
					WmsToggleEnable:   true,
					WmsAvailableLines: []string{"Resource-5"},
				},
				orderData: &rule.SmartRoutingOrderData{
					IsWms: true,
				},
			},
			want: []*rule.RoutingLaneInfo{
				{
					LaneCode: "Lane-3",
					LineList: []*rule.LineInfo{
						{
							LineId:          "Line-5",
							ResourceId:      "Resource-5",
							ResourceSubType: lfslib.C_FL,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			common.AssertResult(t, rs.filterByProhibitedLine(ctx, tt.args.lanes, tt.args.matchedRule, tt.args.orderData, tt.args.logEntry), tt.want, nil, nil)
		})
	}
}
