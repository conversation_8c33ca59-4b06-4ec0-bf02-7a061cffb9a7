package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

type PreCalFeeService interface {
	RoutingPreCalcFee(ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64
}

type PreCalFeeServiceImpl struct {
	LineCheapestShippingFeeFactor *schedule_factor.LineCheapestShippingFeeFactor
}

func NewRoutingPreCalFeeServiceImpl(lineCheapestShippingFeeFactor *schedule_factor.LineCheapestShippingFeeFactor) *PreCalFeeServiceImpl {
	return &PreCalFeeServiceImpl{
		LineCheapestShippingFeeFactor: lineCheapestShippingFeeFactor,
	}
}

func (rp *PreCalFeeServiceImpl) RoutingPreCalcFee(ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
	lineFeeMap := make(map[string]float64)
	//1. 只有selectLane routing场景需要提前计算运费
	if rd.BusinessType != constant.SelectLaneRouting {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.NonSelectLaneScene, "non select lane routing scene, no need calc fee.")
		return lineFeeMap
	}
	//2. 判断总开关是否打开
	if !configutil.IsOpenGlobalSwitch(ctx) {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.GlobalSwitchClose, "global switch close, no need calc fee.")
		return lineFeeMap
	}
	//3. 过滤需要提前计算的line
	needFeeLineList := filterPreCalcFeeLine(ctx, productId, lineInfoList)
	if len(needFeeLineList) == 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.NoNeedCalcFeeLine, "need calc fee line length is zero, no need calc fee line")
		logger.CtxLogDebugf(ctx, "RoutingPreCalcFee|no need pre calc line")
		return lineFeeMap
	}
	//4. 计算运费
	lineFeeMap = rp.LineCheapestShippingFeeFactor.CalcLineShippingFee(ctx, needFeeLineList, rd)
	monitoring.ReportSuccess(ctx, monitoring.CatRoutingCalcFeeMonitor, monitoring.PreCalcFeeSuccess, fmt.Sprintf("pre calc fee success, lineList: %v, lineResult: %v", needFeeLineList, lineFeeMap))
	logger.CtxLogDebugf(ctx, "RoutingPreCalcFee|routing pre calc fee result is:%+v", lineFeeMap)
	return lineFeeMap
}

func filterPreCalcFeeLine(ctx context.Context, productId int64, lineInfoList map[int32][]*rule.LineInfo) []*rule.LineInfo {
	var needFeeLineList []*rule.LineInfo
	for resourceSubType, lineInfos := range lineInfoList {
		for _, lineInfo := range lineInfos {
			if configutil.IsOpenRoutingPreCalcFeeSwitch(ctx, productId, int(resourceSubType)) {
				needFeeLineList = append(needFeeLineList, lineInfo)
			}
		}
	}
	return needFeeLineList
}
