package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"testing"
)

func TestPreCalFeeServiceImpl_RoutingPreCalcFee(t *testing.T) {
	ctx := context.Background()
	rp := &PreCalFeeServiceImpl{}
	var patchGetRoutingPreCalcFeeConfig, patchIsOpenRoutingPreCalcFeeSwitch, patchCalcLineShippingFee *gomonkey.Patches
	type args struct {
		productId    int64
		lineInfoList map[int32][]*rule.LineInfo
		rd           *rule.RoutingData
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]float64
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: non select lane routing scene, no need calc fee.",
			args: args{
				rd: &rule.RoutingData{},
			},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 2: global switch close, no need calc fee.",
			args: args{
				rd: &rule.RoutingData{
					BusinessType: constant.SelectLaneRouting,
				},
			},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 3: need calc fee line length is zero, no need calc fee line",
			args: args{
				rd: &rule.RoutingData{
					BusinessType: constant.SelectLaneRouting,
				},
			},
			want: nil,
			setup: func() {
				patchGetRoutingPreCalcFeeConfig = gomonkey.ApplyFunc(configutil.GetRoutingPreCalcFeeConfig, func(ctx context.Context) configutil.RoutingPreCalcFeeSwitch {
					return configutil.RoutingPreCalcFeeSwitch{
						GlobalSwitch: true,
					}
				})
			},
		},
		{
			name: "case 4: pre calc fee success",
			args: args{
				rd: &rule.RoutingData{
					BusinessType: constant.SelectLaneRouting,
				},
				lineInfoList: map[int32][]*rule.LineInfo{
					1: {
						{},
					},
				},
			},
			want: map[string]float64{},
			setup: func() {
				rp = &PreCalFeeServiceImpl{
					LineCheapestShippingFeeFactor: &schedule_factor.LineCheapestShippingFeeFactor{},
				}
				patchGetRoutingPreCalcFeeConfig = gomonkey.ApplyFunc(configutil.GetRoutingPreCalcFeeConfig, func(ctx context.Context) configutil.RoutingPreCalcFeeSwitch {
					return configutil.RoutingPreCalcFeeSwitch{
						GlobalSwitch: true,
					}
				})
				patchIsOpenRoutingPreCalcFeeSwitch = gomonkey.ApplyFunc(configutil.IsOpenRoutingPreCalcFeeSwitch, func(ctx context.Context) bool {
					return true
				})
				patchCalcLineShippingFee = gomonkey.ApplyMethod(rp.LineCheapestShippingFeeFactor, "CalcLineShippingFee", func(factor *schedule_factor.LineCheapestShippingFeeFactor, ctx context.Context, lines []*rule.LineInfo, rd *rule.RoutingData) map[string]float64 {
					return map[string]float64{}
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got := rp.RoutingPreCalcFee(ctx, tt.args.productId, tt.args.lineInfoList, tt.args.rd)
			common.AssertResult(t, got, tt.want, nil, nil)
			if patchGetRoutingPreCalcFeeConfig != nil {
				patchGetRoutingPreCalcFeeConfig.Reset()
			}
			if patchIsOpenRoutingPreCalcFeeSwitch != nil {
				patchIsOpenRoutingPreCalcFeeSwitch.Reset()
			}
			if patchCalcLineShippingFee != nil {
				patchCalcLineShippingFee.Reset()
			}
		})
	}
}
