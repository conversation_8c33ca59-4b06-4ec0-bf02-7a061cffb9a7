package routing

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// ILH容量模式常量定义
const (
	CapacityModeReservedBSA = "ReservedBSA" // 使用产品预留BSA容量
	CapacityModeBSA         = "BSA"         // 使用BSA总容量（预留+非预留）
	CapacityModeAdhoc       = "Adhoc"       // 使用Adhoc容量
	CapacityModeWeightage   = "Weightage"   // 基于权重选择
	CapacityModeSingleLane  = "SingleLane"  // 只有一条可用Lane
)

// RevampILHRoutingRequest 封装ILH路由请求参数
type RevampILHRoutingRequest struct {
	ProductID             int
	LMID                  string
	DGType                int
	TWSCode               string
	OrderWeight           int64
	OrderTime             int64
	PackageNo             string
	AvailableLanes        []*rule.RoutingLaneInfo
	AvailableLHRule       entity.AvailableLHRule
	ILHCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo
}

// ILHSelectionResult 存储ILH选择结果和容量使用情况
type ILHSelectionResult struct {
	LineID                  string
	ReservedBSAUsage        int64
	NonReservedBSAUsage     int64
	AdhocUsage              int64
	InheritedBSASlotUsage   map[string]int64 // 记录各继承时间段使用的BSA容量
	InheritedAdhocSlotUsage map[string]int64 // 记录各继承时间段使用的Adhoc容量
	CapacityMode            string           // 使用的容量模式
	SlotID                  string           // 容量时间段ID
}

// RevampILHRoutingResult 封装ILH路由结果
type RevampILHRoutingResult struct {
	Lane                    *rule.RoutingLaneInfo
	ReservedBSAUsage        int64
	NonReservedBSAUsage     int64
	AdhocUsage              int64
	InheritedBSASlotUsage   map[string]int64 // 流转过来的Time Slot的BSA使用详情
	InheritedAdhocSlotUsage map[string]int64 // 流转过来的Time Slot的Adhoc使用详情
	CapacityMode            string           // 使用的容量模式
	SlotID                  string           // 容量时间段ID
}

// ILHSelectionInput 定义ILH选择策略的输入结构体
type ILHSelectionInput struct {
	ProductID   int
	DGType      int
	OrderWeight int64
	OrderTime   int64
}

// ILHSelectionStrategy 定义ILH选择策略的接口
type ILHSelectionStrategy interface {
	// Select 选择ILH线路
	// 返回值: 选中的ILH线路、是否找到合适的线路、选择结果对象
	Select(
		ctx context.Context,
		ilhLines []string,
		ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
		availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
		input ILHSelectionInput,
	) (string, bool, *ILHSelectionResult)

	// StrategyName 返回策略名称，用于业务上报
	StrategyName() string
}
