package routing_role

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestUpdateLineResourceType(t *testing.T) {
	ctx := context.Background()
	var patch *gomonkey.Patches
	type args struct {
		productID      int
		routingType    uint8
		availableLanes []*rule.RoutingLaneInfo
		isMultiProduct bool
		log            *routing_log.RoutingLog
	}
	tests := []struct {
		name  string
		args  args
		want  map[int]int
		setup func()
	}{
		// TODO: Add test cases.
		{
			name:  "case 1: getRoutingRoleInfoByCache failed",
			args:  args{},
			want:  nil,
			setup: func() {},
		},
		{
			name: "case 2: routingType == rule.CBRoutingType + isMultiProduct == true",
			args: args{
				routingType:    rule.CBRoutingType,
				isMultiProduct: true,
				availableLanes: []*rule.RoutingLaneInfo{
					{
						LineList: []*rule.LineInfo{
							{
								ResourceSubType: lfslib.C_FL,
							},
						},
					},
				},
				log: &routing_log.RoutingLog{},
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &ProductRoutingRoleMap{}, nil
				})
			},
		},
		{
			name: "case 3: routingType == rule.CBRoutingType",
			args: args{
				routingType: rule.CBRoutingType,
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &ProductRoutingRoleMap{}, nil
				})
			},
		},
		{
			name: "case 4: routingType == rule.SPXRoutingType",
			args: args{
				routingType: rule.SPXRoutingType,
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &ProductRoutingRoleMap{}, nil
				})
			},
		},
		{
			name: "case 5: routingType == rule.LocalRoutingType",
			args: args{
				routingType: rule.LocalRoutingType,
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &ProductRoutingRoleMap{}, nil
				})
			},
		},
		{
			name: "case 6: routingType == rule.IlhRoutingType",
			args: args{
				routingType: rule.IlhRoutingType,
			},
			want: nil,
			setup: func() {
				patch = gomonkey.ApplyFunc(localcache.Get, func(ctx context.Context, namespace string, key string) (interface{}, error) {
					return &ProductRoutingRoleMap{}, nil
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := UpdateLineResourceType(ctx, tt.args.productID, tt.args.routingType, tt.args.availableLanes, tt.args.isMultiProduct, tt.args.log); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateLineResourceType() = %v, want %v", got, tt.want)
			}
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
