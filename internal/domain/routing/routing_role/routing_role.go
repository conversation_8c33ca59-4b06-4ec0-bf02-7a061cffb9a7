package routing_role

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
)

type ProductRoutingRoleMap struct {
	CBRoutingRoleMap      map[int]int
	CBMultiRoutingRoleMap map[int]int
	SpxRoutingRoleMap     map[int]int
	LocalRoutingRoleMap   map[int]int
	IlhRoutingRoleMap     map[int]int
}

type RoutingRoleInfo struct {
	ResourceSubType int `json:"resource_sub_type"`
	RoutingType     int `json:"routing_type"`
}

// @core
func UpdateLineResourceType(ctx context.Context, productID int, routingType uint8, availableLanes []*rule.RoutingLaneInfo, isMultiProduct bool, log *routing_log.RoutingLog) map[int]int {
	roleInfo, err := getRoutingRoleInfoByCache(ctx, productID)
	if err != nil {
		return nil
	}

	roleMap := map[int]int{}
	switch routingType {
	case rule.CBRoutingType:
		if isMultiProduct {
			roleMap = roleInfo.CBMultiRoutingRoleMap
		} else {
			roleMap = roleInfo.CBRoutingRoleMap
		}
	case rule.SPXRoutingType:
		roleMap = roleInfo.SpxRoutingRoleMap
	case rule.LocalRoutingType:
		roleMap = roleInfo.LocalRoutingRoleMap
	case rule.IlhRoutingType:
		roleMap = roleInfo.IlhRoutingRoleMap
	}
	var lineType []string
	for _, lane := range availableLanes {
		for _, line := range lane.LineList {
			line.RealResourceSubType = line.ResourceSubType
			line.ResourceSubType = GetRoleType(roleMap, line.ResourceSubType)
			key := line.ResourceSubType
			lineId := lfslib.LineSubTypeMap[key]
			if line.ResourceSubType != 0 && !objutil.ContainsString(lineType, lineId) {
				lineType = append(lineType, lineId)
			}
		}
	}
	if log != nil {
		res := strings.Join(lineType, ";")
		log.RoutingRole = res
	}

	return roleMap
}

func GetRoleType(roleMap map[int]int, subType int32) int32 {
	if roleType, ok := roleMap[int(subType)]; ok {
		subType = int32(roleType)
	}
	return subType
}

func getRoutingRoleInfoByCache(ctx context.Context, productId int) (*ProductRoutingRoleMap, *srerr.Error) {
	cacheVal, err := localcache.Get(ctx, constant.RoutingRole, strconv.Itoa(productId))
	if cacheVal == nil || err != nil {
		return nil, srerr.New(srerr.LocalCacheErr, nil, "got product routing role from local cache fail")
	}

	routingRoleMap, ok := cacheVal.(*ProductRoutingRoleMap)
	if !ok {
		return nil, srerr.New(srerr.DataErr, nil, "convert to ProductRoutingRoleMap fail")
	}

	return routingRoleMap, nil
}

func loadRoutingRoleMap(routingRole string) map[int]int {
	roleMap := map[int]int{}
	roleInfo := JsonRoleInfo(routingRole)
	for _, role := range roleInfo {
		roleMap[role.ResourceSubType] = role.RoutingType
	}
	return roleMap
}

func JsonRoleInfo(routingRole string) []RoutingRoleInfo {
	var roleInfo []RoutingRoleInfo
	if jErr := jsoniter.UnmarshalFromString(routingRole, &roleInfo); jErr != nil {
		return []RoutingRoleInfo{}
	}
	return roleInfo
}

func DumpRoutingRole() (map[string]interface{}, error) {
	var (
		ret      = make(map[string]interface{})
		roleTabs = make([]*ruledata.ProductRoutingRoleTab, 0)
	)

	if err := dbutil.Select(context.TODO(), ruledata.ProductRoutingRoleTabHook, nil, &roleTabs); err != nil {
		return nil, err
	}
	for _, roleTab := range roleTabs {
		ret[strconv.Itoa(roleTab.ProductId)] = &ProductRoutingRoleMap{
			CBRoutingRoleMap:      loadRoutingRoleMap(roleTab.CBRoutingRole),
			CBMultiRoutingRoleMap: loadRoutingRoleMap(roleTab.CBMultiRoutingRole),
			SpxRoutingRoleMap:     loadRoutingRoleMap(roleTab.SpxRoutingRole),
			LocalRoutingRoleMap:   loadRoutingRoleMap(roleTab.LocalRoutingRole),
			IlhRoutingRoleMap:     loadRoutingRoleMap(roleTab.IlhRoutingRole),
		}
	}

	return ret, nil
}

func GetProductRoutingRoleForAdmin(ctx context.Context, productID int64) (*ProductRoutingRoleMap, *srerr.Error) {
	routingRole := &ruledata.ProductRoutingRoleTab{}
	if err := dbutil.Take(context.TODO(), ruledata.ProductRoutingRoleTabHook, map[string]interface{}{
		"product_id = ?": productID,
	}, routingRole); err != nil {
		logger.CtxLogErrorf(ctx, "GetProductRoutingRoleForAdmin| err:%v", err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "GetProductRoutingRoleForAdmin| get routing role from db, routing role:%v", routingRole)
	ret := &ProductRoutingRoleMap{
		CBRoutingRoleMap:      loadRoutingRoleMap(routingRole.CBRoutingRole),
		CBMultiRoutingRoleMap: loadRoutingRoleMap(routingRole.CBMultiRoutingRole),
		SpxRoutingRoleMap:     loadRoutingRoleMap(routingRole.SpxRoutingRole),
		LocalRoutingRoleMap:   loadRoutingRoleMap(routingRole.LocalRoutingRole),
		IlhRoutingRoleMap:     loadRoutingRoleMap(routingRole.IlhRoutingRole),
	}

	return ret, nil
}

func GetRoleMapByProductAndType(ctx context.Context, productID int64, routingType uint8, isMultiProduct bool) map[int]int {
	roleMap := map[int]int{}
	roleInfo, rErr := GetProductRoutingRoleForAdmin(ctx, productID)
	if rErr != nil {
		return roleMap
	}
	if isMultiProduct && routingType != rule.IlhRoutingType { //非ilh的multi会返回cb role
		return roleInfo.CBMultiRoutingRoleMap
	}
	switch routingType {
	case rule.CBRoutingType:
		roleMap = roleInfo.CBRoutingRoleMap
	case rule.SPXRoutingType:
		roleMap = roleInfo.SpxRoutingRoleMap
	case rule.LocalRoutingType:
		roleMap = roleInfo.LocalRoutingRoleMap
	case rule.IlhRoutingType: //ilh返回 ilh role
		roleMap = roleInfo.IlhRoutingRoleMap
	}
	return roleMap
}
