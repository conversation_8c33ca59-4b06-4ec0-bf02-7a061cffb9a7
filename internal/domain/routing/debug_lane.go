package routing

import rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"

func getDebugLaneInfo(in []*rule.RoutingLaneInfo) []string {
	ret := make([]string, 0)
	for _, Lane := range in {
		ret = append(ret, Lane.LaneCode)
	}
	return ret
}

func getDebugInfoForLineList(in []*rule.LineInfo) []string {
	var out []string
	for _, p := range in {
		out = append(out, p.ResourceId)
	}
	return out
}
