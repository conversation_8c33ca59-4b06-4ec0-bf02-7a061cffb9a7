package routing

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
)

func setDefaultSchedulingLogs(logEntry *routing_log.RoutingLog, matchedRule *rule.RoutingRuleParsed, rt *routingTask) {
	if logEntry != nil {
		logEntry.RoutingResult.DefaultCriteriaFilterProcess.Processed = true
		logEntry.RoutingResult.DefaultCriteriaFilterProcess.Before = getDebugLaneInfo(rt.routingData.ValidateLaneList)
		logEntry.RoutingResult.DefaultCriteriaFilterProcess.CriteriaType = matchedRule.DefaultCriteriaType
		logEntry.RoutingResult.DefaultCriteriaFilterProcess.FactorName = matchedRule.DefaultCriteriaType.String()
	}
}

func setNormalSchedulingLogs(logEntry *routing_log.RoutingLog, matchedRule *rule.RoutingRuleParsed, rt *routingTask) {
	if logEntry != nil {
		logEntry.RoutingResult.SoftCriteriaFilterProcess.Processed = true
		logEntry.RoutingResult.SoftCriteriaFilterProcess.Before = getDebugLaneInfo(rt.routingData.ValidateLaneList)
	}
}

func setProhibitFilterLogs(logEntry *routing_log.RoutingLog, before, after map[string]map[string][]string) {
	if logEntry != nil {
		logEntry.RoutingResult.AvailableFilterProcess.Before = before
		logEntry.RoutingResult.AvailableFilterProcess.After = after
		logEntry.RoutingResult.AvailableFilterProcess.Processed = true
	}
}

// vncb二次3pl toggle校验，需要保留第一次校验的before和第二次校验的after
func setProhibitFilterLogsForVnCb(oldLogEntry, logEntry *routing_log.RoutingLog, after map[string]map[string][]string) {
	if logEntry != nil && oldLogEntry != nil {
		logEntry.RoutingResult.AvailableFilterProcess.Before = oldLogEntry.RoutingResult.AvailableFilterProcess.Before
		logEntry.RoutingResult.AvailableFilterProcess.After = after
		logEntry.RoutingResult.AvailableFilterProcess.Processed = true
	}
}

func setupSPxSelfBuild(logEntry *routing_log.RoutingLog, before, after []string) {
	if logEntry != nil {
		logEntry.RoutingResult.SpxSelfBuildProcess = routing_log.SpxBuildFilterProcess{
			Before: before,
			After:  after,
		}
	}
}
