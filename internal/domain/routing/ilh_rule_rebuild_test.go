package routing

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"testing"
)

func TestRebuildCCModeIlhRuleData(t *testing.T) {
	ctx := context.Background()

	type args struct {
		effectiveRule *rule.RoutingRule
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRuleParsed
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: effectiveRule.RuleDetails == nil",
			args: args{
				effectiveRule: &rule.RoutingRule{},
			},
			want:    nil,
			wantErr: srerr.New(srerr.ParamErr, nil, "invalid routing rule config"),
		},
		{
			name: "case 2: normal result",
			args: args{
				effectiveRule: &rule.RoutingRule{
					RuleDetails: &rule.RuleDetails{
						Rules: []*rule.Detail{
							{
								MinVolumeEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								MinVolumeEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
							{
								MaxCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								MaxCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
							{
								MinWeightEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								MinWeightEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
							{
								MaxWeightCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								MaxWeightCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
							{
								ParcelMinVolumeEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								ParcelMinVolumeEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
							{
								ParcelMaxCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
								CombinationMode: true,
							},
							{
								ParcelMaxCapacityEnable: true,
								LineLimit: []*rule.LineLimit{
									{},
								},
							},
						},
					},
					DefaultCriteria: &rule.DefaultCriteria{},
				},
			},
			want: &rule.RoutingRuleParsed{
				DefaultCriteriaType: rule.CriteriaWeightAge,
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "MinVolume",
						MinVolumeData: &rule.MinVolumeData{
							CombinationMinVolumes: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "MinVolume",
						MinVolumeData: &rule.MinVolumeData{
							MinVolumes: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "MaxCapacity",
						MaxCapacityData: &rule.MaxCapacityData{
							CombinationMaxCapacities: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "MaxCapacity",
						MaxCapacityData: &rule.MaxCapacityData{
							MaxCapacities: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "MinWeight",
						MinWeightData: &rule.MinWeightData{
							CombinationMinWeights: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "MinWeight",
						MinWeightData: &rule.MinWeightData{
							MinWeights: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "MaxWeight",
						MaxWeightData: &rule.MaxWeightData{
							CombinationMaxWeights: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "MaxWeight",
						MaxWeightData: &rule.MaxWeightData{
							MaxWeights: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "ILHParcelMinVolume",
						IlhParcelMinVolumeData: &rule.MinVolumeData{
							CombinationMinVolumes: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "ILHParcelMinVolume",
						IlhParcelMinVolumeData: &rule.MinVolumeData{
							MinVolumes: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "ILHParcelMaxCapacity",
						IlhParcelMaxCapacityData: &rule.MaxCapacityData{
							CombinationMaxCapacities: map[rule.IlhCombination]int{
								rule.IlhCombination{}: 0,
							},
						},
					},
					{
						Name: "ILHParcelMaxCapacity",
						IlhParcelMaxCapacityData: &rule.MaxCapacityData{
							MaxCapacities: map[string]int{
								"": 0,
							},
						},
					},
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				LineToggle: map[string]*rule.Toggle{
					"": {
						On: []string{
							"",
							"",
							"",
							"",
							"",
							"",
							"",
							"",
							"",
							"",
							"",
							"",
						},
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := RebuildCCModeIlhRuleData(ctx, tt.args.effectiveRule)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}
