package ordentity

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"strconv"
	"strings"
)

type (
	OrderInfo struct {
		CheckoutItems        []*CheckoutItem `json:"checkout_items" validate:"required,dive,required"`
		PickupAddress        *AddressInfo    `json:"pickup_address"`
		DeliveryAddress      *AddressInfo    `json:"delivery_address"`
		Fulfillment          *int            `json:"fulfillment" validate:"required"`
		CodAmount            *float64        `json:"cod_amount"`
		TotalPrice           *float64        `json:"total_price" validate:"required"`
		COGS                 *float64        `json:"cogs"`
		WhsId                string          `json:"whs_id"`
		FulfillmentType      *int            `json:"fulfillment_type"` // 1 sbs
		IsWms                bool            `json:"is_wms"`
		BuyerPaidShippingFee *float64        `json:"buyer_paid_shipping_fee" validate:"required"`
		PaymentMethod        string          `json:"payment_method"`
		ShopId               *int            `json:"shop_id"`
		ShopIds              []int           `json:"shop_ids"`
		Weight               *float64        `json:"weight"`
		Distance             *int            `json:"distance"`
		AddCodFee            *int            `json:"add_cod_fee"`
		IsReturn             bool            `json:"is_return"`
		TaxNumber            *string         `json:"tax_number"`
		SellerTaxNumber      *string         `json:"seller_tax_number"`
		StateRegistration    *string         `json:"state_registration"`
		PickupTime           int             `json:"pickup_time"`
		CreateOrderTime      int             `json:"create_order_time"`
		LaneDgFlag           int32           `json:"lane_dg_flag"`
		ParcelWeight         *ParcelWeight   `json:"parcel_weight,omitempty"`
		ShopDetailList       []*ShopDetail   `json:"shop_detail_list"`
		ShippingTraceno      *string         `json:"shipping_traceno,omitempty"`
		CogsWithScsf         float64         `json:"cogs_with_scsf"` //batch_cod_check和checkout one api接口中代替totalprice做order校验，在batch_cod_check接口中代替codAmount做order校验
	}
	CheckoutItem struct {
		ShopId                 int         `json:"shop_id" validate:"required"`
		ShopInfo               *ShopInfo   `json:"shop_info,omitempty"`
		ProductPriorityGroupId int         `json:"product_priority_group_id"`
		Items                  []*ItemInfo `json:"items" validate:"required,dive,required"`
	}
	ShopInfo struct {
		ShopId          int                `json:"shop_id"`
		ShopName        string             `json:"shop_name"`
		ShopUserId      int                `json:"shop_user_id"`
		PickupAddressId int                `json:"pickup_address_id"`
		CbOption        int                `json:"cb_option"`
		LogisticsInfo   *ShopLogisticsInfo `json:"shop_logistics_info,omitempty"`
	}
	ShopLogisticsInfo struct {
		LogisticsFlag int64                             `json:"logistics_flag"`
		LogisticsInfo map[string]map[string]interface{} `json:"shop_logistics_info"`
		Crossborder   bool                              `json:"crossborder"`
	}
	BasicItemInfo struct {
		ItemId               uint64            `json:"item_id" validate:"required"`
		Quantity             int               `json:"quantity" validate:"required"`
		ModelId              uint64            `json:"model_id"`
		Weight               *float64          `json:"weight"`
		Length               *float64          `json:"length"`
		Width                *float64          `json:"width"`
		Height               *float64          `json:"height"`
		CoverShippingFee     *bool             `json:"cover_shipping_fee"`
		IsDg                 *uint32           `json:"is_dg"`
		CategoryId           *uint64           `json:"category_id"`
		GlobalCategoryId     *uint64           `json:"global_category_id"`
		GlobalCategoryIdList []uint64          `json:"global_category_id_list"`
		Price                int64             `json:"price"`
		LogisticsInfo        ItemLogisticsInfo `json:"logistics_info,omitempty"`
		DgSpecificType       *uint32           `json:"dg_specific_type,omitempty"`
	}
	ItemLogisticsInfo map[string]map[string]interface{}
	ItemInfo          struct {
		BasicItemInfo
		MtSkus []*BasicItemInfo `json:"mtskus"`
	}
	AddressInfo struct {
		Country            *string          `json:"country,omitempty"`
		State              *string          `json:"state,omitempty"`
		City               *string          `json:"city,omitempty"`
		District           *string          `json:"district,omitempty"`
		Street             *string          `json:"street,omitempty"`
		PostalCode         *string          `json:"postal_code,omitempty"`
		Longitude          *string          `json:"longitude,omitempty"`
		Latitude           *string          `json:"latitude,omitempty"`
		UserId             *int             `json:"user_id,omitempty"`
		AddressId          *int             `json:"address_id,omitempty"`
		AddressType        *int             `json:"address_type,omitempty"`
		LocationIDs        []int            `json:"location_ids"`
		StateLocationId    *int             `json:"state_location_id"`
		CityLocationId     *int             `json:"city_location_id"`
		DistrictLocationId *int             `json:"district_location_id"`
		StreetLocationId   *int             `json:"street_location_id"`
		StoreId            *string          `json:"store_id,omitempty"`
		Phone              *string          `json:"phone,omitempty"`
		CheckoutState      *string          `json:"checkout_state,omitempty"`
		Category           constant.AddrCat `json:"category"`
		Disabled           bool             `json:"disabled"`
		Address            *string          `json:"address"`
		AddrLine           *string          `json:"addr_line"`
	}
	ParcelWeight struct {
		ActualWeight     *float64 `json:"actual_weight"`
		VolumetricWeight *float64 `json:"volumetric_weight"`
		Length           *float64 `json:"length"`
		Width            *float64 `json:"width"`
		Height           *float64 `json:"height"`
	}
	ShopDetail struct {
		ShopId          int `json:"shop_id"`
		PriorityGroupId int `json:"priority_group_id"`
	}
)

func (p *OrderInfo) IsCodPayment() bool {
	return strings.ToUpper(p.PaymentMethod) == constant.PaymentMethodCod
}

func (p *OrderInfo) IsSBSFulfillment() bool {
	return p.Fulfillment != nil && *p.Fulfillment == constant.FulfillmentSbs
}

func (p *OrderInfo) GetTaxNumber() string {
	if p.TaxNumber == nil {
		return ""
	}
	return *p.TaxNumber
}

func (p *OrderInfo) GetSellerTaxNumber() string {
	if p.SellerTaxNumber == nil {
		return ""
	}
	return *p.SellerTaxNumber
}

func (p *OrderInfo) GetStateRegistration() string {
	if p.StateRegistration == nil {
		return ""
	}
	return *p.StateRegistration
}

func (p *AddressInfo) IncludeWholeGeo() bool {
	return p.Longitude != nil && p.Latitude != nil
}

func (p *AddressInfo) Lat() string {
	if p.Latitude == nil {
		return ""
	}
	return *p.Latitude
}

func (p *AddressInfo) Lon() string {
	if p.Longitude == nil {
		return ""
	}
	return *p.Longitude
}

func (p *AddressInfo) GetCountry() string {
	if p.Country == nil {
		return ""
	}
	return *p.Country
}

func (p *AddressInfo) GetState() string {
	if p.State == nil {
		return ""
	}
	return *p.State
}

func (p *AddressInfo) GetCheckoutState() string {
	if p.CheckoutState == nil {
		return ""
	}
	return *p.CheckoutState
}

func (p *AddressInfo) GetCity() string {
	if p.City == nil {
		return ""
	}
	return *p.City
}

func (p *AddressInfo) GetDistrict() string {
	if p.District == nil {
		return ""
	}
	return *p.District
}

func (p *AddressInfo) GetStreet() string {
	if p.Street == nil {
		return ""
	}
	return *p.Street
}

func (p *AddressInfo) GetUserId(defaultValue int) int {
	if p == nil || p.UserId == nil {
		return defaultValue
	}
	return *p.UserId
}

func (p *AddressInfo) GetAddressId(defaultValue int) int {
	if p == nil || p.AddressId == nil {
		return defaultValue
	}
	return *p.AddressId
}

func (p *AddressInfo) GetPostalCode() string {
	if p == nil || p.PostalCode == nil {
		return ""
	}
	return *p.PostalCode
}

func (p *AddressInfo) GetStateLocationId() int {
	if p == nil || p.StateLocationId == nil {
		return 0
	}
	return *p.StateLocationId
}

func (p *AddressInfo) GetCityLocationId() int {
	if p == nil || p.CityLocationId == nil {
		return 0
	}
	return *p.CityLocationId
}

func (p *AddressInfo) GetDistrictLocationId() int {
	if p == nil || p.DistrictLocationId == nil {
		return 0
	}
	return *p.DistrictLocationId
}

func (p *AddressInfo) GetStreetLocationId() int {
	if p == nil || p.StreetLocationId == nil {
		return 0
	}
	return *p.StreetLocationId
}

func (p *AddressInfo) ContainsAbstractInfo() bool {
	return p.UserId != nil && p.AddressId != nil && p.AddressType != nil
}

func (p *AddressInfo) ContainsConcreteInfo() bool {
	return p.Country != nil && p.State != nil && p.City != nil
}

func (p *AddressInfo) GetPhone() string {
	if p.Phone != nil {
		return *p.Phone
	}
	return ""
}

func (p *AddressInfo) ClearLocationIds() {
	p.StateLocationId = nil
	p.CityLocationId = nil
	p.DistrictLocationId = nil
	p.StreetLocationId = nil
}

func (p *AddressInfo) Denote() string {
	var b strings.Builder
	b.WriteString(strings.Join([]string{p.GetCountry(), p.GetState(), p.GetCity(), p.GetDistrict(), p.GetStreet()}, "|"))
	b.WriteByte('~')
	if p.StateLocationId != nil {
		b.WriteString(strconv.Itoa(*p.StateLocationId))
		b.WriteByte('-')
	}
	if p.CityLocationId != nil {
		b.WriteString(strconv.Itoa(*p.CityLocationId))
		b.WriteByte('-')
	}
	if p.DistrictLocationId != nil {
		b.WriteString(strconv.Itoa(*p.DistrictLocationId))
		b.WriteByte('-')
	}
	if p.StreetLocationId != nil {
		b.WriteString(strconv.Itoa(*p.StreetLocationId))
	}
	return b.String()
}

func (p *AddressInfo) IsEmpty() bool {
	return p.Country == nil && p.State == nil && p.City == nil && p.District == nil && p.Street == nil &&
		p.PostalCode == nil && p.StateLocationId == nil && p.CityLocationId == nil && p.DistrictLocationId == nil &&
		p.StreetLocationId == nil
}
func (p *AddressInfo) IsValid() bool {
	return p.StateLocationId != nil
}

func (p *AddressInfo) SetCountry(country string) {
	if country != "" {
		p.Country = &country
	}
}

func (p *AddressInfo) SetState(state string) {
	if state != "" {
		p.State = &state
	}
}

func (p *AddressInfo) SetCity(city string) {
	if city != "" {
		p.City = &city
	}
}

func (p *AddressInfo) SetDistrict(district string) {
	if district != "" {
		p.District = &district
	}
}

func (p *AddressInfo) SetStreet(street string) {
	if street != "" {
		p.Street = &street
	}
}

func (p *AddressInfo) SetPhone(phone string) {
	if phone != "" {
		p.Phone = &phone
	}
}

func (p *AddressInfo) SetPostcode(postcode string) {
	if postcode != "" {
		p.PostalCode = &postcode
	}
}

func (p *AddressInfo) SetLat(lat string) {
	if lat != "" {
		p.Latitude = &lat
	}
}

func (p *AddressInfo) SetLon(lon string) {
	if lon != "" {
		p.Longitude = &lon
	}
}

func (p *AddressInfo) EmptyRegionInfo() bool {
	return p.GetState() == "" && p.GetCity() == "" && p.GetDistrict() == ""
}

func (p *AddressInfo) GetAddressType() int {
	if p.AddressType == nil {
		return 0
	}
	return *p.AddressType
}

func (p *AddressInfo) IsStore() bool {
	return p.Category == constant.Store
}

func (p *AddressInfo) GetAddressLine() string {
	if p.AddrLine == nil {
		return ""
	}
	return *p.AddrLine
}

func (p *AddressInfo) CombineAddressLine(address *string) string {
	return str.Merge(p.GetState(), p.GetCity(), str.Value(address))
}

//func (p *ItemInfo) ExistDetail() bool {
//	return (p.Weight != nil && !number.FloatEqual(*p.Weight, 0)) ||
//		p.Length != nil || p.Width != nil || p.Height != nil
//}

func (p *OrderInfo) GetCogs() float64 {
	if p.COGS != nil {
		return *p.COGS
	}

	return 0
}

func (p *OrderInfo) GetCodAmount() float64 {
	if p.CodAmount != nil {
		return *p.CodAmount
	}

	return 0
}

func (p *ItemInfo) GetCategoryId() uint64 {
	if p.CategoryId != nil {
		return *p.CategoryId
	}

	return 0
}

func (p *ItemInfo) GetItemId() uint64 {
	return p.ItemId
}
func (p *ItemInfo) GetModelId() uint64 {
	return p.ModelId
}
func (p *ItemInfo) GetHeight() float64 {
	if p.Height != nil {
		return *p.Height
	}

	return 0
}

func (p *ItemInfo) GetWeight() float64 {
	if p.Weight != nil {
		return *p.Weight
	}

	return 0
}

func (p *ItemInfo) GetWidth() float64 {
	if p.Width != nil {
		return *p.Width
	}

	return 0
}

func (p *ItemInfo) GetLength() float64 {
	if p.Length != nil {
		return *p.Length
	}

	return 0
}

func (p *ItemInfo) GetQuantity() int {
	return p.Quantity
}

func (p *OrderInfo) GetShopId() int {
	if p.ShopId != nil {
		return *p.ShopId
	}

	return 0

}

func (p *BasicItemInfo) GetCoverShippingFee(productId int) bool {
	if len(p.LogisticsInfo) == 0 {
		return false
	}
	csf, exist := p.LogisticsInfo.boolByField(strconv.Itoa(productId), "cover_shipping_fee", false)
	if exist {
		return csf
	}
	return false
}

func (p *BasicItemInfo) SizeId(productId int) *int {
	if len(p.LogisticsInfo) == 0 {
		return nil
	}
	sizeId, exist := p.LogisticsInfo.intByField(strconv.Itoa(productId), "sizeid", -1)
	if exist {
		return &sizeId
	}
	return nil
}

func (m ItemLogisticsInfo) boolByField(productId, field string, def bool) (bool, bool) {
	if cfg, ok := m[productId]; ok {
		if val, ok := cfg[field]; ok {
			switch v := val.(type) {
			case bool:
				return v, true
			case int:
				return v == 1, true
			case float64:
				return int(v) == 1, true
			}
		}
	}
	return def, false
}

func (m ItemLogisticsInfo) intByField(productId, field string, def int) (int, bool) {
	if cfg, ok := m[productId]; ok {
		if val, ok := cfg[field]; ok {
			switch v := val.(type) {
			case int:
				return v, true
			case float64:
				return int(v), true
			}
		}
	}
	return def, false
}
