package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/lrucache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/change_report_util"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	llspb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v3/logistics-line-site-system/go"
	"git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg/change_report/constant/report_constant"
	"sort"
	"strconv"
)

type RoutingRuleRepo interface {
	MatchFirstPriorityRoutingRule(ctx context.Context, matchParam RuleMatchParam) (*rule.RoutingRuleParsed, *srerr.Error)
	MatchRoutingRules(ctx context.Context, matchParam RuleMatchParam) ([]*rule.RoutingRuleParsed, *srerr.Error)
	MatchFirstPriorityRoutingRuleByRuleList(ctx context.Context, ruleList []*ruledata.RoutingRuleTab, matchParam RuleMatchParam) (*rule.RoutingRuleParsed, *srerr.Error)
	MatchRoutingRulesByRuleList(ctx context.Context, ruleList []*ruledata.RoutingRuleTab, matchParam RuleMatchParam) ([]*rule.RoutingRuleParsed, *srerr.Error)
	ValidateUpdateRequest(ctx context.Context, update *soft_routing.UpdateRuleRequest) *srerr.Error // 内部调用的更新接口的校验
	GetMultiProductDgGroup(ctx context.Context, productId int) (map[string]DgGroupInfo, *srerr.Error)
	CreateProductRuleWithTx(ctx context.Context, db scormv2.SQLCommon, ruleData *ruledata.RoutingRuleTab) (*ruledata.RoutingRuleTab, *srerr.Error)
	UpdateForecastRuleStatusActive(ctx context.Context, routingType int, isMultiProduct bool)
	UpdateProductRuleStatusExpired(ctx context.Context, routingType int, isMultiProduct bool)
}

type routingRuleRepoImpl struct {
	zoneRepo locationzone.ZoneRepo
	lps      lpsclient.LpsApi
	LaneSrv  lane.LaneService
	lls      llsclient.LlsApi
	ruleRepo ruledata.SoftRuleRepo
}

func NewRoutingRuleRepo(zr locationzone.ZoneRepo,
	lps lpsclient.LpsApi, ls lane.LaneService,
	lls llsclient.LlsApi, ruleRepo ruledata.SoftRuleRepo,
) *routingRuleRepoImpl {
	return &routingRuleRepoImpl{
		zoneRepo: zr,
		lps:      lps,
		LaneSrv:  ls,
		lls:      lls,
		ruleRepo: ruleRepo,
	}
}

var (
	parseRoutingRuleLruCache, _ = cache.NewLruCache(cache.ParseRoutingRuleLruName)
)

// @core
func (rr *routingRuleRepoImpl) MatchFirstPriorityRoutingRule(ctx context.Context, matchParam RuleMatchParam) (*rule.RoutingRuleParsed, *srerr.Error) {
	matchedRules, err := rr.MatchRoutingRules(ctx, matchParam)
	if err != nil {
		return nil, err
	}

	logger.CtxLogInfof(ctx, "Use routing rule id = %d", matchedRules[0].ID)

	return matchedRules[0], nil
}

// @core
func (rr *routingRuleRepoImpl) MatchRoutingRules(ctx context.Context, matchParam RuleMatchParam) ([]*rule.RoutingRuleParsed, *srerr.Error) {
	data, err := localcache.Get(ctx, constant.RoutingRule, ruledata.MemKeyRoutingRule(matchParam.ProductId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "get rule list from cache fail, err:%+v", err)
		return nil, srerr.New(srerr.RuleNotFound, nil, "get rule list from cache fail|product_id=%v,err=%v", matchParam.ProductId, err)
	}

	ruleList, ok := data.([]*ruledata.RoutingRuleTab)
	if !ok {
		logger.CtxLogErrorf(ctx, "fail to convert, value:%v", data)
		return nil, srerr.New(srerr.RuleNotFound, nil, "fail to convert,product_id=%v", matchParam.ProductId)
	}

	validRules := make([]*ruledata.RoutingRuleTab, 0)
	for _, r := range ruleList {
		wrappedTime := timeutil.GetCurrentUnixTimeStamp(ctx)
		if r.ProductID == matchParam.ProductId &&
			r.Status == rule.RuleStatusActive &&
			int64(r.EffectiveStartTime) <= wrappedTime &&
			r.IsMultiProduct == matchParam.IsMultiProduct &&
			r.RoutingType == matchParam.RoutingType {
			validRules = append(validRules, r)
		}
	}
	if len(validRules) == 0 {
		return nil, srerr.New(srerr.RuleNotFound, nil, "rule not found, productID:%d", matchParam.ProductId)
	}

	sort.SliceStable(validRules, func(i, j int) bool {
		return validRules[i].Priority < validRules[j].Priority
	})
	parsedRules, matchErr := rr.MatchRoutingRulesByRuleList(ctx, validRules, matchParam)
	if matchErr != nil {
		return nil, matchErr
	}

	return parsedRules, nil
}

func (rr *routingRuleRepoImpl) MatchFirstPriorityRoutingRuleByRuleList(ctx context.Context, ruleList []*ruledata.RoutingRuleTab, matchParam RuleMatchParam) (*rule.RoutingRuleParsed, *srerr.Error) {
	matchedRules, err := rr.MatchRoutingRulesByRuleList(ctx, ruleList, matchParam)
	if err != nil {
		return nil, err
	}

	logger.CtxLogInfof(ctx, "Use routing rule id = %d", matchedRules[0].ID)

	return matchedRules[0], nil
}

func (rr *routingRuleRepoImpl) MatchRoutingRulesByRuleList(ctx context.Context, ruleList []*ruledata.RoutingRuleTab, matchParam RuleMatchParam) ([]*rule.RoutingRuleParsed, *srerr.Error) {
	var matchedRoutingRules []*ruledata.RoutingRuleTab
	for _, r := range ruleList {
		if rr.isRuleMatch(ctx, r, matchParam) {
			matchedRoutingRules = append(matchedRoutingRules, r)
		}
	}

	if len(matchedRoutingRules) == 0 {
		return nil, srerr.With(srerr.RuleNotFound, matchParam.ProductId, fmt.Errorf("rule not found %d", matchParam.ProductId))
	}

	parsedRules := make([]*rule.RoutingRuleParsed, 0, len(matchedRoutingRules))
	for _, matchedRule := range matchedRoutingRules {
		logger.CtxLogInfof(ctx, "match rule id = %v", matchedRule.ID)
		ruleParsed, err := parseAndRebuildRuleWithLru(ctx, matchedRule)
		if err != nil {
			return nil, err
		}
		parsedRules = append(parsedRules, ruleParsed)
	}

	return parsedRules, nil
}

func parseAndRebuildRuleWithLru(ctx context.Context, r *ruledata.RoutingRuleTab) (*rule.RoutingRuleParsed, *srerr.Error) {
	key := strconv.Itoa(int(r.ID))
	if cacheVal, exist := parseRoutingRuleLruCache.Get(ctx, key); exist {
		if val, ok := cacheVal.(*rule.RoutingRuleParsed); ok {
			return val, nil
		}
	}

	parsedRule, err := parseAndRebuildRule(ctx, r)
	if err != nil {
		return nil, err
	}

	parseRoutingRuleLruCache.Add(ctx, key, parsedRule)

	return parsedRule, nil
}

func parseAndRebuildRule(ctx context.Context, r *ruledata.RoutingRuleTab) (*rule.RoutingRuleParsed, *srerr.Error) {
	parseRule, parseErr := ParseRuleData(r, true)
	if parseErr != nil {
		return nil, srerr.With(srerr.RoutingRuleException, r.ProductID, parseErr)
	}

	var (
		ruleParsed *rule.RoutingRuleParsed
		buildErr   *srerr.Error
	)
	if parseRule.CCMode == rule.CCModeCCRouting {
		ruleParsed, buildErr = RebuildCCModeIlhRuleData(ctx, parseRule)
	} else {
		ruleParsed, buildErr = RebuildRuleData(ctx, parseRule)
	}
	if buildErr != nil {
		return nil, buildErr
	}
	//记录一下priority，做local预测的时候需要保存这个字段
	ruleParsed.Priority = r.Priority

	return ruleParsed, nil
}

func (rr *routingRuleRepoImpl) isRuleMatch(ctx context.Context, r *ruledata.RoutingRuleTab, matchParam RuleMatchParam) bool {
	ruleWhsIdList := objutil.SetStringToSlice(r.WhsId)
	ruleZoneCodeList := objutil.SetStringToSlice(r.ZoneCode)
	ruleCategoryIdList := objutil.SetStringToIntSlice(r.ItemCategoryIDList)
	destinationPortList := objutil.SetStringToSlice(r.DestinationPorts)
	shopId, err := strconv.ParseInt(matchParam.ShopId, 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "convert shop id:%s, err:%v", matchParam.ShopId, err)
	}

	if len(ruleWhsIdList) != 0 && !objutil.ContainStr(ruleWhsIdList, matchParam.WhsID) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|whsId=%v|rul.WhsId=%v|ruleWhsIdList=%v",
			r.ID, matchParam.WhsID, r.WhsId, ruleWhsIdList)
		return false
	}

	productIDForZone := int(matchParam.ProductId)
	if r.RoutingType == rule.SPXRoutingType {
		productIDForZone = 0
	}

	if len(ruleZoneCodeList) != 0 && !rr.zoneRepo.CheckZoneContainDistrict(ctx, productIDForZone, matchParam.LocationIDList, ruleZoneCodeList, r.RoutingType, matchParam.ZoneType, matchParam.DeliverPostCode) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|rul.ZoneCode=%v|locationIdList=%v|ruleZoneCodeList=%v",
			r.ID, r.ZoneCode, matchParam.LocationIDList, ruleZoneCodeList)
		return false
	}

	if len(ruleCategoryIdList) != 0 && r.ItemCategoryLevel != 0 && !CheckItemCategoryInRule(r.ItemCategoryLevel, ruleCategoryIdList, matchParam.ItemCategoryInfos) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleCategoryLevel=%v|ruleCategoryIdList=%v|orderItemCategoryInfo=%+v",
			r.ID, r.ItemCategoryLevel, r.ItemCategoryIDList, str.JsonString(matchParam.ItemCategoryInfos))
		return false
	}

	if r.ParcelValueMax != 0 && (matchParam.Cogs < r.ParcelValueMin || matchParam.Cogs > r.ParcelValueMax) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleParcelValueMin=%v|ruleParcelValueMax=%v|orderCogs=%v",
			r.ID, r.ParcelValueMin, r.ParcelValueMax, matchParam.Cogs)
		return false
	}

	if r.ParcelDimension.LengthMax != 0 && (matchParam.ParcelLength < r.ParcelDimension.LengthMin || matchParam.ParcelLength > r.ParcelDimension.LengthMax) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleParcelLengthMin=%v|ruleParcelLengthMax=%v|orderParcelLength=%v",
			r.ID, r.ParcelDimension.LengthMin, r.ParcelDimension.LengthMax, matchParam.ParcelLength)
		return false
	}

	if r.ParcelDimension.WidthMax != 0 && (matchParam.ParcelWidth < r.ParcelDimension.WidthMin || matchParam.ParcelWidth > r.ParcelDimension.WidthMax) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleParcelWidthMin=%v|ruleParcelWidthMax=%v|orderParcelWidth=%v",
			r.ID, r.ParcelDimension.WidthMin, r.ParcelDimension.WidthMax, matchParam.ParcelWidth)
		return false
	}

	if r.ParcelDimension.HeightMax != 0 && (matchParam.ParcelHeight < r.ParcelDimension.HeightMin || matchParam.ParcelHeight > r.ParcelDimension.HeightMax) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleParcelHeightMin=%v|ruleParcelHeightMax=%v|orderParcelHeight=%v",
			r.ID, r.ParcelDimension.HeightMin, r.ParcelDimension.HeightMax, matchParam.ParcelHeight)
		return false
	}

	if r.ParcelWeightMax != 0 && (matchParam.ValidationWeight < r.ParcelWeightMin || matchParam.ValidationWeight > r.ParcelWeightMax) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v||ruleParcelWeightMin=%v|ruleParcelWeightMax=%v|orderValidationWeight=%v",
			r.ID, r.ParcelWeightMin, r.ParcelWeightMax, matchParam.ValidationWeight)
		return false
	}

	if len(destinationPortList) != 0 && !objutil.HaveIntersection(destinationPortList, matchParam.DestinationPorts) {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleDestPorts=%v|laneDestPorts=%v",
			r.ID, destinationPortList, matchParam.DestinationPorts)
		return false
	}

	if r.DgType != 0 && r.DgType != matchParam.DgType {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|ruleDgRuleType=%v|orderDgType=%v",
			r.ID, r.DgType, matchParam.DgType)
		return false
	}

	//SSCSMR-3053:match cb shop group
	if len(r.ShopGroupListVo) != 0 {
		logger.CtxLogInfof(ctx, "filter rule rul_id=%v|rule shop group=%+v|shop_id=%d",
			r.ID, r.ShopGroupListVo, shopId)
		// forecast rule setting -> deploy to live rule -> get live shop group in cache by live rule
		// 缓存的来源就是live rule中的数据，因此只需要check是否命中缓存中的shop group
		shopGroup, matched := r.MatchShopGroup(shopId)
		if !matched {
			logger.CtxLogInfof(ctx, "shop group map:%+v, not match shop group", r.ClientEntityGroupMap)
			return false
		}
		logger.CtxLogInfof(ctx, "shop id:%d, shop group:%d, match success", shopId, shopGroup)
	}

	return true
}

func (rr *routingRuleRepoImpl) ValidateUpdateRequest(ctx context.Context, update *soft_routing.UpdateRuleRequest) *srerr.Error {
	err := update.ValidateReq()
	if err != nil {
		return err
	}
	return nil
}

func (rr *routingRuleRepoImpl) GetMultiProductDgGroup(ctx context.Context, productId int) (map[string]DgGroupInfo, *srerr.Error) {
	// 1. Get multi product's detail
	productDetail, err := rr.lps.GetProductDetail(ctx, productId)
	if err != nil {
		return nil, err
	}

	// 2. Get ILH to DG Group map
	laneInfoList, err := rr.LaneSrv.BatchGetLaneInfoWithoutCache(ctx, productDetail.MultiLaneCodes)
	if err != nil {
		return nil, err
	}

	var ilhLineList []string
	for _, laneInfo := range laneInfoList {
		ilh := laneInfo.GetCILHLineInfo()
		if ilh != nil && ilh.LineID != "" {
			ilhLineList = append(ilhLineList, ilh.LineID)
		}
	}

	if len(ilhLineList) == 0 {
		return nil, srerr.New(srerr.ParamErr, productId, "Multi product %d has no ILH line", productId)
	}

	dgGroupMap, err := rr.lls.GetDgGroup(ctx, ilhLineList)
	if err != nil {
		return nil, err
	}
	ret := groupLineByDgGroup(dgGroupMap)

	return ret, nil
}

func groupLineByDgGroup(data map[string]*llspb.GetDgGroupData) map[string]DgGroupInfo {
	ret := make(map[string]DgGroupInfo)
	for line, dgGroup := range data {
		dgGroupId := dgGroup.GetDgGroupId()
		if dgGroupId == "" {
			// if ilh get no dg group info, will not display in rule
			continue
		}
		if _, exist := ret[dgGroupId]; !exist {
			ret[dgGroupId] = DgGroupInfo{
				DgGroupId:   dgGroupId,
				DgGroupName: dgGroup.GetDgGroupName(),
			}
		}
		retDgGroup := ret[dgGroupId]
		retDgGroup.LineList = append(retDgGroup.LineList, line)
		ret[dgGroupId] = retDgGroup
	}

	return ret
}

func (rr *routingRuleRepoImpl) CreateProductRuleWithTx(ctx context.Context, db scormv2.SQLCommon, ruleData *ruledata.RoutingRuleTab) (*ruledata.RoutingRuleTab, *srerr.Error) {
	ts := timeutil.GetCurrentUnixTimeStamp(ctx)
	ruleData.MTime, ruleData.CTime = uint32(ts), uint32(ts)
	if err := db.Table(ruledata.RoutingRuleHook.TableName()).Create(ruleData).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "create rule fail|product_id=%v, status=%d, rule_name=%s, EffectiveStartTime=%d, operate_by=%s, err=%v",
			ruleData.ProductID, ruleData.Status, ruleData.RuleName, ruleData.EffectiveStartTime, ruleData.OperatedBy, err)
		return nil, srerr.With(srerr.DatabaseErr, "create soft rule error", err)
	}
	return ruleData, nil
}

func (rr *routingRuleRepoImpl) UpdateForecastRuleStatusActive(ctx context.Context, routingType int, isMultiProduct bool) {
	var reportInterfaceName = "UpdateForecastRuleStatusActive"
	startTime := recorder.Now(ctx).Unix()
	// 1. SELECT * FROM `routing_rule_tab` WHERE `status` IN ('1','4') AND effective_start_time < now() AND routing_type = routingType AND is_multi_product = isMultiProduct ORDER BY effective_start_time;
	rules, err := rr.ruleRepo.GetActiveAndForecastRoutingRule(ctx, routingType, isMultiProduct)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get forecast rule fail, err: %v", err)
		return
	}

	// 2. Group by Product
	productForecastRulesMap := make(map[int64][]int64)
	productActiveRulesMap := make(map[int64][]int64)
	needActiveRuleTabsMap := make(map[int64][]*ruledata.RoutingRuleTab) //tabs maybe need to deploy shop group
	for _, r := range rules {
		if r.Status == rule.RuleStatusForecast {
			productForecastRulesMap[r.ProductID] = append(productForecastRulesMap[r.ProductID], r.ID)
			needActiveRuleTabsMap[r.ProductID] = append(needActiveRuleTabsMap[r.ProductID], r)
		} else if r.Status == rule.RuleStatusActive {
			productActiveRulesMap[r.ProductID] = append(productActiveRulesMap[r.ProductID], r.ID)
		}
	}
	logger.CtxLogInfof(ctx, "Update forecast rule to active status, forecast rules: %v, active rules: %v", productForecastRulesMap, productActiveRulesMap)

	// 3. Update forecast rule to active, update exist active rule to expired, deploy forecasting zone to running
	for productID, rules := range productForecastRulesMap {
		// all updates in one transaction
		tx, err := dbutil.MasterDB(ctx, ruledata.RoutingRuleHook)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get db fail|err=%v", err)
			continue
		}

		needExpireIDs := productActiveRulesMap[productID]
		ctx = scormv2.BindContext(ctx, tx)
		txErr := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
			// 3.1 Update before rule status to expire
			tx := scormv2.Context(ctx)
			if err := rr.ruleRepo.UpdateProductRuleStatusExpired(ctx, needExpireIDs, tx); err != nil {
				reportData := fmt.Sprintf("Expire rules %v from active to expire fail, channel: %v, err: %v", needExpireIDs, productID, err)
				if reportE := monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData); reportE != nil {
					logger.CtxLogErrorf(ctx, "report cat error %+v", reportE)
				}
				logger.CtxLogErrorf(ctx, reportData)
				return err
			}

			// 3.2 Update deploy rule from forecast to active
			if err := rr.ruleRepo.UpdateRuleForecastToActive(ctx, rules, tx); err != nil {
				reportData := fmt.Sprintf("Active rules %v from forecast to active fail, channel: %v, err: %v", rules, productID, err)
				if reportE := monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData); reportE != nil {
					logger.CtxLogErrorf(ctx, "report cat error %+v", reportE)
				}
				logger.CtxLogErrorf(ctx, reportData)
				return err
			}

			// 3.3 Copy product's forecasting zone to running (except ILH)
			if routingType != rule.IlhRoutingType {
				if err := rr.zoneRepo.DeployZoneToRunning(ctx, int(productID), tx); err != nil {
					reportData := fmt.Sprintf("Deploy zone to running fail, channel: %v, err: %v", productID, err)
					if reportE := monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData); reportE != nil {
						logger.CtxLogErrorf(ctx, "report cat error %+v", reportE)
					}
					logger.CtxLogErrorf(ctx, reportData)
					return err
				}
			}

			// SSCSMR-3053 deploy cb shop group
			if deployErr := rr.deployShopGroup(ctx, needActiveRuleTabsMap[productID]); deployErr != nil {
				reportData := fmt.Sprintf("Active rules %v from forecast to deploy shop group fail, channel: %v, err: %v", rules, productID, deployErr)
				if reportE := monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData); reportE != nil {
					logger.CtxLogErrorf(ctx, "report cat error %+v", reportE)
				}
				logger.CtxLogErrorf(ctx, reportData)
				return deployErr //
			}

			return nil
		})
		if txErr != nil {
			logger.CtxLogErrorf(ctx, "UpdateForecastRuleStatusActive error %v", txErr)
			continue
		}
		reportData := fmt.Sprintf("Update forecast rule to active, product: %v, active rules: %v, expire rules: %v", productID, rules, needExpireIDs)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusSuccess, reportData)
		//上报变更平台
		change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "schedule_rule", "Change active rule to expire", reportData, report_constant.RiskLevelHigh)
		logger.CtxLogInfof(ctx, reportData)
		// 上报prometheus
		prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameScheduleRule, int64(len(rules)+len(needExpireIDs)))
	}
}

func (rr *routingRuleRepoImpl) UpdateProductRuleStatusExpired(ctx context.Context, routingType int, isMultiProduct bool) {
	var reportInterfaceName = "UpdateProductRuleStatusExpired"
	startTime := recorder.Now(ctx).Unix()
	rules, err := rr.ruleRepo.GetActiveProductRuleList(ctx, routingType, isMultiProduct)
	if err != nil {
		reportData := fmt.Sprintf("GetActiveProductRuleList fail|err=%v", err)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
		logger.CtxLogErrorf(ctx, reportData)
		return
	}

	var needUpdateRuleID []int64
	var existRule = make(map[string]bool)
	for _, r := range rules {
		ruleKey := formatProductRuleKey(r)
		if _, ok := existRule[ruleKey]; ok {
			needUpdateRuleID = append(needUpdateRuleID, r.ID)
		} else {
			existRule[ruleKey] = true
		}
	}

	if len(needUpdateRuleID) == 0 {
		logger.CtxLogInfof(ctx, "no need updated")
		return
	}

	err = rr.ruleRepo.UpdateProductRuleStatusExpired(ctx, needUpdateRuleID, nil)
	if err != nil {
		reportData := fmt.Sprintf("UpdateProductRuleStatusExpired fail|need update rules=%v,err=%v", needUpdateRuleID, err)
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusError, reportData)
		logger.CtxLogErrorf(ctx, reportData)
		return
	}

	reportData := fmt.Sprintf("Update product rules to expired | rules = %v", needUpdateRuleID)
	_ = monitor.AwesomeReportEvent(ctx, monitoring.CatModuleScheduleRule, reportInterfaceName, constant.StatusSuccess, reportData)
	change_report_util.ChangeReportByTask(ctx, constant.ReportDataId, startTime, "schedule_rule", "RoutingRule change active to expire", reportData, report_constant.RiskLevelHigh)
	logger.CtxLogInfof(ctx, reportData)

	// 上报prometheus
	prometheusutil.ServiceTaskSyncNumReport(constant.TaskNameScheduleRule, int64(len(needUpdateRuleID)))
}

func formatProductRuleKey(rule *ruledata.RoutingRuleTab) string {
	return fmt.Sprintf("product-rule:%v-%v-%v-%v", rule.ProductID, rule.ZoneCode, rule.WhsId, rule.Priority)
}

func (rr *routingRuleRepoImpl) deployShopGroup(ctx context.Context, rules []*ruledata.RoutingRuleTab) *srerr.Error {
	// 1.generate req
	req := &lpsclient.CopyShopGroupReq{}
	for i := 0; i < len(rules); i++ {
		ruleTab := rules[i]
		// 1.get shop group list
		ruleTab.UnmarshalShopGroupList()
		// check whether shop group need to deploy
		if len(ruleTab.ShopGroupListVo) == 0 {
			continue
		}
		// 2.generate shop group info
		shopGroupInfo := lpsclient.ShopGroupReq{
			VersionPrefix: lpsclient.LiveVersionPrefix,
			RuleId:        ruleTab.ID,
			ClientTagId:   int64(lpsclient.ClientTagCBLM),
			ShopGroupList: ruleTab.ShopGroupListVo,
		}
		req.List = append(req.List, shopGroupInfo)
	}
	if len(req.List) == 0 {
		logger.CtxLogInfof(ctx, "no shop group need to deploy, return")
		return nil
	}
	// 2.call lps-admin to deploy
	if err := rr.lps.CopyShopGroupInfo(ctx, req); err != nil {
		logger.CtxLogErrorf(ctx, "copy shop group info err:%v", err)
		return err
	}

	return nil
}
