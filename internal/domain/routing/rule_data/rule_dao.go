package ruledata

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type SoftRuleRepo interface {
	GetRoutingRuleFromDB(ctx context.Context, id int64) (*RoutingRuleTab, error)
	GetRoutingRuleByCondition(ctx context.Context, conditions map[string]interface{}) ([]*RoutingRuleTab, *srerr.Error)
	GetEffectiveRoutingRule(ctx context.Context, productID int64, priority int32, routingType uint8, isMultiProduct bool) (*RoutingRuleTab, error)
	GetDraftRoutingRuleList(ctx context.Context, productID int64) ([]*RoutingRuleTab, *srerr.Error)
	UpdateRoutingRule(ctx context.Context, rule *RoutingRuleTab) (*RoutingRuleTab, *srerr.Error)
	DeleteRoutingRule(ctx context.Context, id int64) *srerr.Error
	CreateRoutingRule(ctx context.Context, rule *RoutingRuleTab) (*RoutingRuleTab, *srerr.Error)
	DisableRoutingRule(ctx context.Context, id int64) *srerr.Error
	GetActiveAndForecastRoutingRule(ctx context.Context, routingType int, isMultiProduct bool) ([]*RoutingRuleTab, error)
	UpdateProductRuleStatusExpired(ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error
	UpdateRuleForecastToActive(ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error
	GetActiveProductRuleList(ctx context.Context, routingType int, isMultiProduct bool) ([]*RoutingRuleTab, error)
	GetRuleList(ctx context.Context, q *rule.RuleQuery) ([]*RoutingRuleTab, int32, error)
	GetActiveRuleListByProduct(ctx context.Context, productId int, routingType uint8, isMultiProduct bool) ([]*RoutingRuleTab, *srerr.Error)
	UpdateRulesToExpired(ctx context.Context, ruleIds []int64) *srerr.Error
}

type SoftRuleRepoImpl struct {
	lpsApi lpsclient.LpsApi
}

func NewSoftRuleRepoImpl(api lpsclient.LpsApi) *SoftRuleRepoImpl {
	return &SoftRuleRepoImpl{
		lpsApi: api,
	}
}

func (s *SoftRuleRepoImpl) GetRoutingRuleFromDB(ctx context.Context, id int64) (*RoutingRuleTab, error) {
	//1.根据id从db获取routing rule
	var routingRule *RoutingRuleTab
	if err := dbutil.Take(ctx, RoutingRuleHook, map[string]interface{}{
		"id = ?": id,
	}, &routingRule); err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingRuleFromDB| get rule err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, dbutil.NewErrDataNotFound("id", id)
		}
		return nil, dbutil.NewErrDatabase(err, "db query")
	}

	logger.CtxLogInfof(ctx, "GetRoutingRule| id:%v,  rule:%+v", id, routingRule)
	return routingRule, nil
}

func (s *SoftRuleRepoImpl) GetRoutingRuleByCondition(ctx context.Context, conditions map[string]interface{}) ([]*RoutingRuleTab, *srerr.Error) {
	var routingRuleList []*RoutingRuleTab

	if err := dbutil.Select(ctx, RoutingRuleHook, conditions, &routingRuleList); err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingRuleByCondition| get rule err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, srerr.With(srerr.RuleNotFound, nil, err)
		}
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	logger.CtxLogInfof(ctx, "GetRoutingRule| conditions:%v", conditions)
	return routingRuleList, nil
}

func (s *SoftRuleRepoImpl) GetEffectiveRoutingRule(ctx context.Context, productID int64, priority int32, routingType uint8, isMultiProduct bool) (*RoutingRuleTab, error) {
	var routingRuleList []*RoutingRuleTab
	if err := dbutil.Select(ctx, RoutingRuleHook, map[string]interface{}{
		"product_id = ?":       productID,
		"rule_status = ?":      rule.RuleStatusActive,
		"priority = ?":         priority,
		"routing_type = ?":     routingType,
		"is_multi_product = ?": isMultiProduct,
	}, &routingRuleList, dbutil.WithOrder("priority")); err != nil {
		logger.CtxLogErrorf(ctx, "GetEffectiveRoutingRule| get routing rule list err:%v", err)
		return nil, dbutil.NewErrDatabase(err, "db query")
	}
	if len(routingRuleList) == 0 {
		return nil, dbutil.NewErrDataNotFound("product_id", productID)
	}

	return routingRuleList[0], nil
}

func (s *SoftRuleRepoImpl) GetDraftRoutingRuleList(ctx context.Context, productID int64) ([]*RoutingRuleTab, *srerr.Error) {
	var routingRuleList []*RoutingRuleTab
	if err := dbutil.Select(ctx, RoutingRuleHook, map[string]interface{}{
		"product_id = ?":  productID,
		"rule_status = ?": rule.RuleStatusDraft,
	}, &routingRuleList); err != nil {
		logger.CtxLogErrorf(ctx, "GetDraftRoutingRule| get draft soft rule from db, err:%v", err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if len(routingRuleList) == 0 { //select如果检索不到，不会报错，所以需要手动判断切片长度
		return nil, srerr.New(srerr.RuleNotFound, nil, "GetEffectiveRoutingRule| get empty routing rule list")
	}
	for _, r := range routingRuleList {
		convertStatus(ctx, r)
	}
	return routingRuleList, nil
}

func (s *SoftRuleRepoImpl) UpdateRoutingRule(ctx context.Context, rule *RoutingRuleTab) (*RoutingRuleTab, *srerr.Error) {
	if err := dbutil.SaveByObj(ctx, RoutingRuleHook, nil, rule, nil, dbutil.ModelInfo{
		Id:                   uint64(rule.ID),
		RuleId:               uint64(rule.ID),
		FulfillmentProductId: uint64(rule.ProductID),
	}); err != nil {
		//SSCSMR-517:SaveByObj如果更新失败，affected rows == 1, 因为主键不存在时，会直接插入一条新的记录
		logger.CtxLogErrorf(ctx, "update rule fail|product_id=%v, status=%d, rule_name=%s, EffectiveStartTime=%d, operate_by=%s, err=%v",
			rule.ProductID, rule.Status, rule.RuleName, rule.EffectiveStartTime, rule.OperatedBy, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	convertStatus(ctx, rule)
	//往db写入操作记录
	cErr := s.lpsApi.CreateHistory(ctx, rule, string(lpsclient.UpdateType), "update smart-routing rule ", uint64(rule.ID))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	return rule, nil
}

func (s *SoftRuleRepoImpl) DeleteRoutingRule(ctx context.Context, id int64) *srerr.Error {
	//1.根据id检索数据,后续增加操作记录的时候，需要用到这些数据
	var routingRule *RoutingRuleTab
	_ = dbutil.Take(ctx, RoutingRuleHook, map[string]interface{}{"id = ?": id}, &routingRule)

	//2.删除draft态的rule
	if err := dbutil.Delete(ctx, RoutingRuleHook, map[string]interface{}{"id = ?": id}, dbutil.ModelInfo{
		Id:     uint64(id),
		RuleId: uint64(id),
	}); err != nil {
		logger.CtxLogErrorf(ctx, "DeleteRoutingRule|delete rule:%v, err:%v", id, err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	//记录删除前的rule的数据，然后增加操作记录
	r := new(RoutingRuleTab)
	r.ID = id
	r.ProductID = routingRule.ProductID
	r.RuleName = routingRule.RuleName

	//往db写入操作记录
	cErr := s.lpsApi.CreateHistory(ctx, r, string(lpsclient.HardDeleteType), "hard delete smart-routing rule", uint64(r.ID))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return nil
}

func (s *SoftRuleRepoImpl) CreateRoutingRule(ctx context.Context, rule *RoutingRuleTab) (*RoutingRuleTab, *srerr.Error) {
	ts := timeutil.GetCurrentUnixTimeStamp(ctx)
	rule.MTime, rule.CTime = uint32(ts), uint32(ts)
	if err := dbutil.Insert(ctx, rule, dbutil.ModelInfo{
		Id:                   uint64(rule.ID),
		FulfillmentProductId: uint64(rule.ProductID),
		RuleId:               uint64(rule.ID),
	}); err != nil {
		logger.CtxLogErrorf(ctx, "create rule fail|product_id=%v, status=%d, rule_name=%s, EffectiveStartTime=%d, operate_by=%s, err=%v",
			rule.ProductID, rule.Status, rule.RuleName, rule.EffectiveStartTime, rule.OperatedBy, err)
		return nil, srerr.With(srerr.DatabaseErr, "create soft rule error", err)
	}

	convertStatus(ctx, rule)

	//往db写入操作记录 todo 下面这段代码删掉？？？
	cErr := s.lpsApi.CreateHistory(ctx, rule, string(lpsclient.CreateType), "create smart-routing rule", uint64(rule.ID))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}
	return rule, nil
}

func (s SoftRuleRepoImpl) DisableRoutingRule(ctx context.Context, id int64) *srerr.Error {

	err, rowAffected := dbutil.UpdateWithAffectedRows(ctx, RoutingRuleHook, map[string]interface{}{"id = ?": id}, map[string]interface{}{"rule_status": rule.RuleStatusExpired}, dbutil.ModelInfo{
		Id:        uint64(id),
		ModelName: RoutingRuleHook.TableName(),
		RuleId:    uint64(id),
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "DisableRoutingRule| update rule to expired err:%v", err)
		return srerr.With(srerr.DatabaseErr, "update rule to expired", err)
	}
	if rowAffected == 0 { //SSCSMR-517:更新失败不会报错，要通过affected rows == 0来判断
		logger.CtxLogErrorf(ctx, "DisableRoutingRule|id:%v, update rule to expired failed, rows affected is 0", id)
		return srerr.New(srerr.DatabaseErr, nil, "update rule to expired failed, rows affected is 0")
	}

	return nil
}

func (s *SoftRuleRepoImpl) GetRuleList(ctx context.Context, q *rule.RuleQuery) ([]*RoutingRuleTab, int32, error) {
	var rules []*RoutingRuleTab
	query := make(map[string]interface{}, 0)
	if q.FilterByID {
		query["id=?"] = q.ID
	}
	if q.FilterByProductID {
		query["product_id=?"] = q.ProductID
	}
	if q.FilterByStatus {
		ts := timeutil.GetCurrentUnixTimeStamp(ctx)
		if q.Status == rule.RuleStatusQueuing {
			query["rule_status=?"] = rule.RuleStatusActive
			query["effective_start_time > ?"] = ts
		} else if q.Status == rule.RuleStatusActive {
			query["rule_status=?"] = rule.RuleStatusActive
			query["effective_start_time <= ?"] = ts
		} else {
			query["rule_status=?"] = q.Status
		}
	}
	if q.FilterByTaskID {
		query["task_id = ?"] = q.TaskID
	}
	query["routing_type = ?"] = q.RoutingType
	query["is_multi_product = ?"] = q.IsMultiProduct
	logger.CtxLogInfof(ctx, "GetRuleList| query condition:%v", query)
	var total int64
	if err := dbutil.Count(ctx, RoutingRuleHook, query, &total); err != nil {
		return nil, 0, dbutil.NewErrDatabase(err, "count rules")
	}
	if err := dbutil.Select(ctx, RoutingRuleHook, query, &rules, dbutil.WithOrder("id DESC"), dbutil.WithPage(int64(q.Offset), int64(q.Limit))); err != nil {
		return nil, 0, dbutil.NewErrDatabase(err, "get rule list")

	}
	for _, r := range rules {
		convertStatus(ctx, r)
	}
	return rules, int32(total), nil
}

func convertStatus(ctx context.Context, r *RoutingRuleTab) {
	if r.Status == rule.RuleStatusActive && r.EffectiveStartTime > uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		r.Status = rule.RuleStatusQueuing
	}
}

func (s *SoftRuleRepoImpl) GetActiveAndForecastRoutingRule(ctx context.Context, routingType int, isMultiProduct bool) ([]*RoutingRuleTab, error) {
	db, err := dbutil.MasterDB(ctx, RoutingRuleHook)
	if err != nil {
		return nil, err
	}
	var rules []*RoutingRuleTab
	d := db.Where("rule_status in (?) AND effective_start_time < ? AND routing_type = ? AND is_multi_product = ?",
		[]rule.RuleStatus{rule.RuleStatusActive, rule.RuleStatusForecast}, timeutil.GetCurrentUnixTimeStamp(ctx), routingType, isMultiProduct).
		Order("effective_start_time DESC").
		Find(&rules)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "active and forecast rules not found|err=%v", d.GetError())
		return nil, d.GetError()
	}
	return rules, nil
}

func (s *SoftRuleRepoImpl) UpdateProductRuleStatusExpired(ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error {
	var db scormv2.SQLCommon
	var err error
	if tx != nil {
		db = tx
	} else {
		db, err = dbutil.MasterDB(ctx, RoutingRuleHook)
		if err != nil {
			return err
		}
	}
	d := db.Table(RoutingRuleHook.TableName()).Where("rule_status=? AND id in (?)", rule.RuleStatusActive, ruleIds).Update("rule_status", rule.RuleStatusExpired)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "UpdateShippingChannelRuleStatusExpired fail|ruleIds=%v, err=%v", ruleIds, d.GetError())
		return d.GetError()
	}
	return nil
}

func (s *SoftRuleRepoImpl) UpdateRuleForecastToActive(ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error {
	var db scormv2.SQLCommon
	var err error
	if tx != nil {
		db = tx
	} else {
		db, err = dbutil.MasterDB(ctx, RoutingRuleHook)
		if err != nil {
			return err
		}
	}
	d := db.Table(RoutingRuleHook.TableName()).Where("rule_status=? AND id in (?)", rule.RuleStatusForecast, ruleIds).Update("rule_status", rule.RuleStatusActive)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "UpdateRuleForecastToActive fail|ruleIds=%v, err=%v", ruleIds, d.GetError())
		return d.GetError()
	}
	return nil
}

func (s *SoftRuleRepoImpl) GetActiveProductRuleList(ctx context.Context, routingType int, isMultiProduct bool) ([]*RoutingRuleTab, error) {
	db, err := dbutil.SlaveDB(ctx, RoutingRuleHook)
	if err != nil {
		return nil, err
	}
	var r []*RoutingRuleTab
	d := db.Where("rule_status = ? AND effective_start_time < ? AND routing_type = ? AND is_multi_product = ?",
		rule.RuleStatusActive, timeutil.GetCurrentUnixTimeStamp(ctx), routingType, isMultiProduct).Order("effective_start_time DESC").Find(&r)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "query active rules fail|err=%v", d.GetError())
		return nil, d.GetError()
	}
	return r, nil
}

// GetActiveRuleListByProduct 按照Product+RoutingType+IsMultiProduct维度查询所有Active的Rule
func (s *SoftRuleRepoImpl) GetActiveRuleListByProduct(ctx context.Context, productId int, routingType uint8, isMultiProduct bool) ([]*RoutingRuleTab, *srerr.Error) {
	conditions := map[string]interface{}{
		"product_id = ?":           productId,
		"rule_status = ?":          rule.RuleStatusActive,
		"effective_start_time < ?": timeutil.GetCurrentUnixTimeStamp(ctx),
		"routing_type = ?":         routingType,
		"is_multi_product = ?":     isMultiProduct,
	}

	var routingRuleList []*RoutingRuleTab
	if err := dbutil.Select(ctx, RoutingRuleHook, conditions, &routingRuleList); err != nil {
		logger.CtxLogErrorf(ctx, "GetActiveRuleListByProduct | get rule err:% v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, srerr.With(srerr.RuleNotFound, conditions, err)
		}
		return nil, srerr.With(srerr.DatabaseErr, conditions, err)
	}

	return routingRuleList, nil
}

func (s *SoftRuleRepoImpl) UpdateRulesToExpired(ctx context.Context, ruleIds []int64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, RoutingRuleHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	if err := db.Table(RoutingRuleHook.TableName()).
		Where("id IN (?)", ruleIds).Update("rule_status", rule.RuleStatusExpired).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, ruleIds, err)
	}

	return nil
}
