package ruledata

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const (
	CbProduct     int = 2
	LocalProduct  int = 1
	RuleTableName     = "routing_rule_tab"
	RuleTabList       = "routing_rule_all_rows"
	//ruleTableHistoryKey     = "ssc_lps_cid_read.routing_rule_tab"
)

var (
	RoutingRuleHook = &RoutingRuleTab{}
)

type (
	RoutingRuleTab struct {
		ID                    int64                `gorm:"column:id" json:"-"`
		ProductID             int64                `gorm:"column:product_id" json:"-"`
		Status                rule.RuleStatus      `gorm:"column:rule_status" json:"-"` // 状态 0: Draft，1: Published 2: Expired
		Priority              int32                `gorm:"column:priority" json:"-"`
		RuleName              string               `gorm:"column:rule_name" json:"-"`
		Rules                 []byte               `gorm:"column:rule_detail" json:"-"`
		StrDefaultCriteria    []byte               `gorm:"column:default_criteria;type:varchar(2048)" json:"-"` // 默认规则权重配置
		StrDisabledInfo       []byte               `gorm:"column:disabled_info;type:varchar(2048)" json:"-"`
		StrCombinationSetting []byte               `gorm:"column:combination_setting" json:"-"`
		OperatedBy            string               `gorm:"column:operated_by" json:"-"`
		EffectiveStartTime    uint32               `gorm:"column:effective_start_time" json:"-"`
		CTime                 uint32               `gorm:"column:ctime" json:"ctime"`
		MTime                 uint32               `gorm:"column:mtime" json:"mtime"`
		WhsId                 string               `gorm:"column:whs_id" json:"-"`
		DestinationPorts      string               `gorm:"column:destination_ports" json:"destination_ports"` //SSCSMR-278 add destination ports
		ZoneCode              string               `gorm:"column:zone_code" json:"-"`
		Soc                   string               `gorm:"column:soc" json:"soc"` //SPLPS-4189 soc 黑白名单下线后，该字段将不再使用
		TaskID                int64                `gorm:"column:task_id" json:"task_id"`
		ItemCategoryLevel     int                  `gorm:"column:item_category_level" json:"item_category_level"`
		ItemCategoryIDList    string               `gorm:"column:item_category_id" json:"-"`
		ItemCategoryID        []int                `gorm:"-" json:"item_category_id"`
		ParcelValueMin        float32              `gorm:"column:parcel_value_min" json:"parcel_value_min"`
		ParcelValueMax        float32              `gorm:"column:parcel_value_max" json:"parcel_value_max"`
		ParcelWeightMin       int                  `gorm:"column:parcel_weight_min" json:"parcel_weight_min"`
		ParcelWeightMax       int                  `gorm:"column:parcel_weight_max" json:"parcel_weight_max"` //
		RuleDetail            *RuleDetail          `gorm:"-" json:"rule_detail,omitempty"`
		RoutingType           uint8                `gorm:"column:routing_type" json:"-"`
		IsMultiProduct        bool                 `gorm:"column:is_multi_product" json:"is_multi_product"`
		DgType                int                  `gorm:"column:dg_type" json:"dg_type"` //1:non-dg; 2:dg
		ParcelDimension       rule.ParcelDimension `gorm:"column:parcel_dimension" json:"parcel_dimension,omitempty"`
		WmsToggleEnable       bool                 `gorm:"column:wms_toggle_enable" json:"wms_toggle_enable"`
		CCMode                rule.CCMode          `gorm:"column:cc_mode" json:"cc_mode"`
		VolumeRuleId          int64                `gorm:"column:volume_rule_id" json:"volume_rule_id"`
		StrTplToggleInfo      []byte               `gorm:"column:tpl_toggle_info" json:"-"` // 3pl toggle info list
		ShopGroupList         []byte               `gorm:"column:shop_group_list" json:"-"`
		ShopGroupListVo       []int64              `gorm:"-" json:"shop_group_list"`

		// dump shop group info
		ClientEntityGroupMap map[int64]int64 `gorm:"-" json:"client_group_entity_map"`
	}

	RuleDetail struct {
		Rules []*Rule `json:"rules,omitempty"`
	}

	Rule struct {
		ResourceSubType        int32        `json:"resource_sub_type"`
		MinVolumeEnable        bool         `json:"min_volume_enable"`
		MinVolumeSort          int32        `json:"min_volume_sort,omitempty"`
		MaxCapacityEnable      bool         `json:"max_capacity_enable"`
		MaxCapacitySort        int32        `json:"max_capacity_sort,omitempty"`
		Priority               int32        `json:"priority,omitempty"`
		DgPriority             rule.DGFlag  `json:"dg_priority,omitempty"`
		DgRelated              int32        `json:"dg_related"`
		DgClassificationEnable bool         `json:"dg_classification_enable"`
		DgClassificationSort   int32        `json:"dg_classification_sort,omitempty"`
		LineLimit              []*LineLimit `json:"limit,omitempty"`
	}

	LineLimit struct {
		LineId      string `json:"line_id"`
		Name        string `json:"name"`
		MaxCapacity int32  `json:"max_capacity"`
		MinVolume   int32  `json:"min_volume"`
	}

	LineInfo struct {
		LineId    string `json:"line_id"`
		Name      string `json:"name"`
		WeightAge int    `json:"weightage"`
		DGFlag    int    `json:"dg_flag"`
	}

	DisabledInfo struct {
		ResourceSubType int32    `json:"resource_sub_type"`
		LineList        []string `json:"line_list"`
	}
)

func (t *RoutingRuleTab) TableName() string {
	return RuleTableName
}

func (t *RoutingRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *RoutingRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *RoutingRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(t.ID),
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ProductID),
		TaskId:               uint64(t.TaskID),
		RuleId:               uint64(t.ID),
	}
}

func (t *RoutingRuleTab) IsEditable(ctx context.Context) bool {
	if t.Status == rule.RuleStatusDraft || t.Status == rule.RuleStatusQueuing {
		return true
	}
	if t.Status == rule.RuleStatusActive && t.EffectiveStartTime > uint32(timeutil.GetCurrentUnixTimeStamp(ctx)) {
		return true
	}
	return false
}

func (t *RoutingRuleTab) ConvertToChangeData() string {
	return fmt.Sprintf("%d-%s", t.ProductID, t.RuleName)
}

func (t *RoutingRuleTab) UnmarshalShopGroupList() {
	shopGroupListVo := make([]int64, 0)
	if len(t.ShopGroupList) != 0 {
		_ = objutil.UnmarshalBytes(&shopGroupListVo, t.ShopGroupList)
	}
	t.ShopGroupListVo = shopGroupListVo
}

func (t *RoutingRuleTab) MatchShopGroup(shopId int64) (int64, bool) {
	if len(t.ClientEntityGroupMap) == 0 {
		return 0, false
	}
	group, ok := t.ClientEntityGroupMap[shopId]
	return group, ok
}

func MemKeyRoutingRule(p int64) string {
	return strconv.FormatInt(p, 10)
}

func DumpRoutingRule() (map[string]interface{}, error) {

	var (
		ctx                = context.Background()
		rdata              []*RoutingRuleTab
		groupProductMap    = make(map[int64]int64, 0)              //需要确保Product Remark是一一对应的
		ruleGroupEntityMap = make(map[string]map[int64][]int64, 0) // rule[group[entity_list]]
	)
	cond := map[string]interface{}{
		"rule_status": rule.RuleStatusActive,
	}
	if err := dbutil.Select(context.TODO(), RoutingRuleHook, cond, &rdata); err != nil {
		return nil, err
	}

	//SSCSMR-3053: cache shop group info
	entityList, relationList := DumpClientInfo(ctx, rdata)
	if len(entityList) == 0 || len(relationList) == 0 {
		logger.CtxLogErrorf(ctx, "get empty entity list or empty relation list. Len of entity:%d,  Len of relation:%d", len(entityList), len(relationList))
	} else {
		// 组装client info
		for _, relation := range relationList {
			groupId, _ := strconv.ParseInt(relation.ClientGroupId, 10, 64)
			groupProductMap[groupId] = relation.ProductId
		}
		for _, entity := range entityList {
			groupId, _ := strconv.ParseInt(entity.ClientGroupId, 10, 64)
			// init group-entity map
			if _, ok := ruleGroupEntityMap[entity.Version]; !ok {
				groupEntityMap := make(map[int64][]int64, 0)
				ruleGroupEntityMap[(entity.Version)] = groupEntityMap
			}
			// init entity list
			if _, ok := ruleGroupEntityMap[entity.Version][groupId]; !ok {
				tempEntityList := make([]int64, 0)
				ruleGroupEntityMap[entity.Version][groupId] = tempEntityList
			}
			// append entity id into list
			entityId, _ := strconv.ParseInt(entity.ModelId, 10, 64)
			list := ruleGroupEntityMap[entity.Version][groupId]
			list = append(list, entityId)
			ruleGroupEntityMap[entity.Version][groupId] = list
		}
	}

	recordMap := map[int64][]*RoutingRuleTab{}
	for i := range rdata {
		if _, exist := recordMap[rdata[i].ProductID]; !exist {
			recordMap[rdata[i].ProductID] = []*RoutingRuleTab{}
		}
		// 校验product - rule - group的关联 (若ruleGroupEntityMap为空，不会进入该绑定流程
		groupEntityMap := ruleGroupEntityMap[lpsclient.FormatLiveVersion(rdata[i].ID)]
		for group, tempEntityList := range groupEntityMap {
			// 判断group是否与rule Product绑定上
			if rdata[i].ProductID != groupProductMap[group] {
				continue
			}
			// 绑定上了，将group-entity list保存到该rule下
			if rdata[i].ClientEntityGroupMap == nil {
				rdata[i].ClientEntityGroupMap = make(map[int64]int64, 0)
			}
			for _, entity := range tempEntityList {
				rdata[i].ClientEntityGroupMap[entity] = group
			}
		}
		logger.CtxLogInfof(ctx, "rule id:%d, entity group map:%+v", rdata[i].ID, rdata[i].ClientEntityGroupMap)
		recordMap[rdata[i].ProductID] = append(recordMap[rdata[i].ProductID], rdata[i])
	}
	dumpData := map[string]interface{}{}
	for k, v := range recordMap {
		dumpData[MemKeyRoutingRule(k)] = v
	}
	return dumpData, nil
}

func DumpClientInfo(ctx context.Context, rdata []*RoutingRuleTab) (entityList, relationList []lpsclient.Model) {
	lpsApi := lpsclient.NewLpsApiImpl()
	for _, ruleTab := range rdata {
		// 1.get shop group list
		ruleTab.UnmarshalShopGroupList()
		if len(ruleTab.ShopGroupListVo) == 0 {
			continue
		}
		tempEntityList, tempRelationList, err := lpsApi.GetAllClientInfo(ctx, ruleTab.ShopGroupListVo, lpsclient.FormatLiveVersion(ruleTab.ID))
		if err != nil {
			logger.CtxLogErrorf(ctx, "DumpClientInfo|get all client info err:%v", err)
			continue
		}
		entityList = append(entityList, tempEntityList...)
		relationList = append(relationList, tempRelationList...)
	}

	return entityList, relationList
}
