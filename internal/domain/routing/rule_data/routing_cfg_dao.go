package ruledata

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type RoutingConfigRepo interface {
	GetConfigByProductID(ctx context.Context, productID int64) (*RoutingConfigTab, *srerr.Error)
	GetConfigByID(ctx context.Context, id int64) (*RoutingConfigTab, error)
	GetConfigListByCondition(ctx context.Context, condition map[string]interface{}, offset int32, limit int32) ([]*RoutingConfigTab, int32, error)
	ListConfigByRoutingType(ctx context.Context, routingType uint8) ([]*RoutingConfigTab, error)
	CreateRoutingConfig(ctx context.Context, productID int64, smartRoutingEnabled, localSmartRoutingEnabled, spxSmartRoutingEnabled, ilhSmartRoutingEnabled bool,
		defaultLaneCode, operateBy string) (*RoutingConfigTab, *srerr.Error)
	UpdateRoutingConfig(ctx context.Context, productId int64, smartRoutingEnabled, localSmartRoutingEnabled,
		spxSmartRoutingEnabled, ilhSmartRoutingEnabled bool, defaultLaneCode, operateBy string) (*RoutingConfigTab, *srerr.Error)
	GetConfigList(ctx context.Context, offset int, limit int) ([]*RoutingConfigTab, int64, *srerr.Error)
	GetConfigListByProductId(ctx context.Context, productId int64, offset int, limit int) ([]*RoutingConfigTab, int64, *srerr.Error)
	ListRoutingConfigByRoutingType(ctx context.Context, routingType uint8) ([]*rule.RoutingConfig, *srerr.Error)
	ListAllRoutingConfig(ctx context.Context) ([]*RoutingConfigTab, *srerr.Error)
}

type RoutingConfigRepoImpl struct {
	lpsApi lpsclient.LpsApi
}

func NewRoutingConfigRepoImpl(lpsApi lpsclient.LpsApi) *RoutingConfigRepoImpl {
	return &RoutingConfigRepoImpl{
		lpsApi: lpsApi,
	}
}

func (r *RoutingConfigRepoImpl) GetConfigByProductID(ctx context.Context, productID int64) (*RoutingConfigTab, *srerr.Error) {
	var routingConfig *RoutingConfigTab
	if err := dbutil.Take(ctx, RoutingConfigHook, map[string]interface{}{"product_id = ?": productID}, &routingConfig); err != nil {
		logger.CtxLogErrorf(ctx, "GetConfigByProductId|product id:%v, get routing config err:%v", productID, err)

		if err == scormv2.ErrRecordNotFound {
			return nil, srerr.With(srerr.DataNotFound, productID, err)
		}
		return nil, srerr.With(srerr.DatabaseErr, "db query", err)
	}

	return routingConfig, nil
}

func (r *RoutingConfigRepoImpl) GetConfigByID(ctx context.Context, ID int64) (*RoutingConfigTab, error) {
	routingConfig := &RoutingConfigTab{}
	if err := dbutil.Take(ctx, RoutingConfigHook, map[string]interface{}{
		"id = ?": ID,
	}, routingConfig); err != nil {
		logger.CtxLogErrorf(ctx, "GetRoutingConfigByProductID| get routing config fail, err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, dbutil.NewErrDataNotFound("id", ID)
		} else {
			return nil, dbutil.NewErrDatabase(err, "db query")
		}
	}

	return routingConfig, nil
}

func (r *RoutingConfigRepoImpl) GetConfigListByCondition(ctx context.Context, condition map[string]interface{}, offset int32, limit int32) ([]*RoutingConfigTab, int32, error) {
	routingConfigs := make([]*RoutingConfigTab, 0)
	var total int64
	if err := dbutil.Count(ctx, RoutingConfigHook, condition, &total); err != nil {
		return nil, 0, dbutil.NewErrDatabase(err, "error when counting config")
	}
	if err := dbutil.Select(ctx, RoutingConfigHook, condition, &routingConfigs,
		dbutil.WithOrder("id DESC"), dbutil.WithPage(int64(offset), int64(limit))); err != nil {

		logger.CtxLogErrorf(ctx, "GetConfigByProductId|condition:%v, get routing config err:%v", condition, err)
		return nil, 0, dbutil.NewErrDatabase(err, "db query")
	}

	return routingConfigs, int32(total), nil
}

func (r *RoutingConfigRepoImpl) CreateRoutingConfig(ctx context.Context, productID int64, smartRoutingEnabled, localSmartRoutingEnabled, spxSmartRoutingEnabled, ilhSmartRoutingEnabled bool,
	defaultLaneCode, operateBy string) (*RoutingConfigTab, *srerr.Error) {

	//1.获取非masking的product seller display name
	productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(productID))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig|product id:%v, get product detail err:%v", productID, gErr)
		return nil, gErr
	}
	productSellerName := productDetail.SellerDisplayName

	//2.判断product id是否已有routing config
	var config RoutingConfigTab
	err := dbutil.Take(ctx, RoutingConfigHook, map[string]interface{}{"product_id = ?": productID}, &config)
	if err == nil {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig| product id:%v, routing config already existed", productID)
		return nil, srerr.New(srerr.DataDuplicated, nil, "")
	}
	if err != nil && err != scormv2.ErrRecordNotFound {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig| product id:%v, data base err:%v", err)
		return nil, srerr.New(srerr.DatabaseErr, nil, "")
	}
	//3.赋值并往db写入数据
	now := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	config = RoutingConfigTab{
		ProductID:                productID,
		DefaultLaneCode:          defaultLaneCode,
		SmartRoutingEnabled:      smartRoutingEnabled,
		LocalSmartRoutingEnabled: localSmartRoutingEnabled,
		SpxSmartRoutingEnabled:   spxSmartRoutingEnabled,
		IlhSmartRoutingEnabled:   ilhSmartRoutingEnabled,
		OperatedBy:               operateBy,
		CTime:                    now,
		MTime:                    now,
		ProductName:              productSellerName,
	}
	if err := dbutil.Insert(ctx, &config, dbutil.ModelInfo{FulfillmentProductId: uint64(productID)}); err != nil {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig| insert config into db err:%v", err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	//往db写入操作记录
	cErr := r.lpsApi.CreateHistory(ctx, &config, string(lpsclient.CreateType), "create smart-routing config ", uint64(config.Id))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return &config, nil
}

func (r *RoutingConfigRepoImpl) UpdateRoutingConfig(ctx context.Context, productId int64, smartRoutingEnabled, localSmartRoutingEnabled,
	spxSmartRoutingEnabled, ilhSmartRoutingEnabled bool, defaultLaneCode, operateBy string) (*RoutingConfigTab, *srerr.Error) {
	//1.获取非masking的product seller display name
	productDetail, gErr := r.lpsApi.GetProductDetail(ctx, int(productId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "CreateRoutingConfig|product id:%v, get product detail err:%v", productId, gErr)
		return nil, gErr
	}
	ProductSellerName := productDetail.SellerDisplayName

	//2.先检查product id是否已存在routing config，不必存在就报错
	routingConfig, gErr := r.GetConfigByProductID(ctx, productId)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateRoutingConfig| get routing config err:%v", gErr)
		return nil, gErr
	}
	//3.赋值并更新到db中
	now := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	routingConfig.SmartRoutingEnabled = smartRoutingEnabled
	routingConfig.LocalSmartRoutingEnabled = localSmartRoutingEnabled
	routingConfig.SpxSmartRoutingEnabled = spxSmartRoutingEnabled
	routingConfig.IlhSmartRoutingEnabled = ilhSmartRoutingEnabled
	routingConfig.DefaultLaneCode = defaultLaneCode
	routingConfig.OperatedBy = operateBy
	routingConfig.MTime = now
	routingConfig.ProductName = ProductSellerName
	//todo:SSCSMR-517:改成用map的形式更新，避免结构体更新导致零值更新不了
	if err := dbutil.SaveByObj(ctx, RoutingConfigHook, nil, routingConfig, nil, dbutil.ModelInfo{
		FulfillmentProductId: uint64(productId),
		ModelName:            RoutingConfigHook.TableName(),
	}); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateRoutingConfig| update data err:%v", err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	//往db写入操作记录
	cErr := r.lpsApi.CreateHistory(ctx, routingConfig, string(lpsclient.UpdateType), "update smart-routing config ", uint64(routingConfig.Id))
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "CreateAllocationConfig| create history err:%v", cErr)
	}

	return routingConfig, nil
}

func (r *RoutingConfigRepoImpl) GetConfigList(ctx context.Context, offset int, limit int) ([]*RoutingConfigTab, int64, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, RoutingConfigHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "get db error", err)
	}
	var configs []*RoutingConfigTab
	query := db.Table(RoutingConfigHook.TableName())
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "query db counter error", err)
	}
	d = query.Order("id DESC").Offset(offset).Limit(limit).Find(&configs)
	if d.GetError() != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "query db error", err)
	}
	return configs, total, nil
}

func (r *RoutingConfigRepoImpl) GetConfigListByProductId(ctx context.Context, productId int64, offset int, limit int) ([]*RoutingConfigTab, int64, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, RoutingConfigHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "get db error", err)
	}
	var configs []*RoutingConfigTab
	query := db.Table(RoutingConfigHook.TableName()).Where("product_id = ?", productId)
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "query db counter error", err)
	}
	d = query.Order("id DESC").Offset(offset).Limit(limit).Find(&configs)
	if d.GetError() != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "query db error", err)
	}
	return configs, total, nil
}

func (r *RoutingConfigRepoImpl) ListConfigByRoutingType(ctx context.Context, routingType uint8) ([]*RoutingConfigTab, error) {
	condition := make(map[string]interface{}, 0)
	switch routingType {
	case rule.CBRoutingType:
		condition["smart_routing_enabled = @enabled OR cb_multi_routing_enable = @enabled"] = map[string]interface{}{"enabled": true}
		//db = db.Where("smart_routing_enabled = ? OR cb_multi_routing_enable = ?", true, true)
	case rule.SPXRoutingType:
		condition["spx_routing_enable = ?"] = true
	case rule.LocalRoutingType:
		condition["local_routing_enable = ?"] = true
	case rule.IlhRoutingType:
		condition["ilh_routing_enable = ?"] = true
	}
	var configs []*RoutingConfigTab
	if err := dbutil.Select(ctx, RoutingConfigHook, condition, &configs); err != nil {
		return nil, err
	}

	return configs, nil
}

func (r *RoutingConfigRepoImpl) ListRoutingConfigByRoutingType(ctx context.Context, routingType uint8) ([]*rule.RoutingConfig, *srerr.Error) {
	routingConfTabList := make([]*RoutingConfigTab, 0)
	db, err := dbutil.SlaveDB(ctx, RoutingConfigHook)

	switch routingType {
	case rule.CBRoutingType:
		db = db.Where("smart_routing_enabled = ? OR cb_multi_routing_enable = ?", true, true)
	case rule.SPXRoutingType:
		db = db.Where("spx_routing_enable = ?", true)
	case rule.LocalRoutingType:
		db = db.Where("local_routing_enable = ?", true)
	case rule.IlhRoutingType:
		db = db.Where("ilh_routing_enable = ?", true)
	}

	if err := db.Find(&routingConfTabList).GetError(); err != nil {
		return nil, srerr.New(srerr.DatabaseErr, routingType, "find routing config by routing type err +%v", err)
	}

	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	routingConfList := make([]*rule.RoutingConfig, 0, len(routingConfTabList))
	for _, r := range routingConfTabList {
		routingConfList = append(routingConfList, &rule.RoutingConfig{
			Id:                         r.Id,
			ProductID:                  r.ProductID,
			SmartRoutingEnabled:        r.SmartRoutingEnabled,
			CBMultiSmartRoutingEnabled: r.CBMultiRoutingEnabled,
			LocalSmartRoutingEnabled:   r.LocalSmartRoutingEnabled,
			SpxSmartRoutingEnabled:     r.SpxSmartRoutingEnabled,
			IlhSmartRoutingEnabled:     r.IlhSmartRoutingEnabled,
			DefaultLaneCode:            r.DefaultLaneCode,
			OperatedBy:                 r.OperatedBy,
			CTime:                      r.CTime,
			MTime:                      r.MTime,
		})
	}

	return routingConfList, nil
}

func (r *RoutingConfigRepoImpl) ListAllRoutingConfig(ctx context.Context) ([]*RoutingConfigTab, *srerr.Error) {
	var routingConfigList []*RoutingConfigTab
	if err := dbutil.Select(ctx, RoutingConfigHook, nil, &routingConfigList); err != nil {
		return nil, srerr.New(srerr.DatabaseErr, nil, "get all routing config err: %+v", err)
	}

	return routingConfigList, nil
}
