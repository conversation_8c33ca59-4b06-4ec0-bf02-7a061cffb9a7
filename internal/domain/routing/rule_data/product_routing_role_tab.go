package ruledata

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	tableName = "product_routing_role_tab"
)

var (
	ProductRoutingRoleTabHook = &ProductRoutingRoleTab{}
)

type ProductRoutingRoleTab struct {
	Id                 uint64 `gorm:"column:id" json:"id"`
	ProductId          int    `gorm:"column:product_id" json:"product_id"`
	CBRoutingRole      string `gorm:"column:cb_routing_role" json:"cb_routing_role"`
	CBMultiRoutingRole string `gorm:"column:cb_multi_routing_role" json:"cb_multi_routing_role"`
	SpxRoutingRole     string `gorm:"column:spx_routing_role" json:"spx_routing_role"`
	LocalRoutingRole   string `gorm:"column:local_routing_role" json:"local_routing_role"`
	IlhRoutingRole     string `gorm:"column:ilh_routing_role" json:"ilh_routing_role"`
	Ctime              int    `gorm:"column:ctime" json:"ctime"`
	Mtime              int    `gorm:"column:mtime" json:"mtime"`
}

func (t *ProductRoutingRoleTab) TableName() string {
	return tableName
}

func (t *ProductRoutingRoleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *ProductRoutingRoleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *ProductRoutingRoleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   t.Id,
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ProductId),
	}
}
