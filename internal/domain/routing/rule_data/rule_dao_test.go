package ruledata

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestSoftRuleRepoImpl_DisableRoutingRule(t *testing.T) {

	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	if err := dbutil.Init(); err != nil {
		panic(err)
	}
	s := SoftRuleRepoImpl{}
	ctx := context.TODO()
	s.DisableRoutingRule(ctx, 28)
}
