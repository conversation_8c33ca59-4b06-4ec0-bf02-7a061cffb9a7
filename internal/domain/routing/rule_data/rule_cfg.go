package ruledata

import (
	"fmt"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var RoutingConfigHook = &RoutingConfigTab{}

const (
	configTableName     = "routing_config_tab"
	ruleTableHistoryKey = "ssc_lps_cid_read.routing_rule_tab"
)

type RoutingConfigTab struct {
	Id                       int64  `gorm:"column:id" json:"id"`
	ProductID                int64  `gorm:"column:product_id" json:"product_id"`
	SmartRoutingEnabled      bool   `gorm:"column:smart_routing_enabled" json:"smart_routing_enabled"`
	SmartRoutingToggle       bool   `gorm:"column:smartrouting_toggle" json:"smartrouting_toggle"`
	CBMultiRoutingEnabled    bool   `gorm:"column:cb_multi_routing_enable" json:"cb_multi_routing_enable"`
	LocalSmartRoutingEnabled bool   `gorm:"column:local_routing_enable" json:"local_routing_enable"`
	SpxSmartRoutingEnabled   bool   `gorm:"column:spx_routing_enable" json:"spx_routing_enable"`
	IlhSmartRoutingEnabled   bool   `gorm:"column:ilh_routing_enable" json:"ilh_routing_enable"`
	DefaultLaneCode          string `gorm:"column:default_lane_code" json:"default_lane_code"`
	OperatedBy               string `gorm:"column:operated_by" json:"operated_by"`
	CTime                    uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                    uint32 `gorm:"column:mtime" json:"mtime"`

	ProductName string `gorm:"-" json:"product_name"`
}

func (t *RoutingConfigTab) TableName() string {
	return configTableName
}

func (t *RoutingConfigTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *RoutingConfigTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t RoutingConfigTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(t.Id),
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ProductID),
	}
}

func (RoutingConfigTab) Key() string {
	return ruleTableHistoryKey
}

func (t RoutingConfigTab) GetID() uint64 {
	return uint64(t.Id)
}

func (t RoutingConfigTab) ConvertToChangeData() string {
	return fmt.Sprintf("%d-%s", t.ProductID, t.ProductName)
}

func (t RoutingConfigTab) ToRoutingConfigEntity() rule.RoutingConfig {
	return rule.RoutingConfig{
		Id:                         t.Id,
		ProductID:                  t.ProductID,
		SmartRoutingEnabled:        t.SmartRoutingEnabled,
		CBMultiSmartRoutingEnabled: t.CBMultiRoutingEnabled,
		LocalSmartRoutingEnabled:   t.LocalSmartRoutingEnabled,
		SpxSmartRoutingEnabled:     t.SpxSmartRoutingEnabled,
		IlhSmartRoutingEnabled:     t.IlhSmartRoutingEnabled,
		SmartRoutingToggle:         t.SmartRoutingToggle,
		DefaultLaneCode:            t.DefaultLaneCode,
		OperatedBy:                 t.OperatedBy,
		CTime:                      t.CTime,
		MTime:                      t.MTime,
	}
}
