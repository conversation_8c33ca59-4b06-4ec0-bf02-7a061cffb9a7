package routing

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	ruleentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/agiledragon/gomonkey/v2"
)

func TestSelectOptimalCCLine(t *testing.T) {
	// 使用gomonkey库来mock ChoiceByWeightage函数，让它返回权重最高的线路
	patch := gomonkey.ApplyFunc(mathutil.ChoiceByWeightage, func(ctx context.Context, items map[string]uint) string {
		var maxKey string
		var maxWeight uint = 0
		for key, weight := range items {
			if weight > maxWeight {
				maxWeight = weight
				maxKey = key
			}
		}
		return maxKey
	})
	// 在测试结束时恢复原始函数
	defer patch.Reset()

	ctx := context.Background()
	// 不再需要向context添加测试环境标记
	defaultProductID := 123

	// 准备测试用的基本数据
	setupAvailableLineInfo := func() map[string]entity.AvailableLineBaseInfo {
		return map[string]entity.AvailableLineBaseInfo{
			"CC001": {
				BaseLineInfo: ruleentity.BaseLineInfo{LineId: "CC001"},
				Weightage:    50,
				MaxWeight:    1.0, // 1.0 KG = 1000g
				MinWeight:    0.5, // 0.5 KG = 500g
			},
			"CC002": {
				BaseLineInfo: ruleentity.BaseLineInfo{LineId: "CC002"},
				Weightage:    30,
				MaxWeight:    1.5, // 1.5 KG = 1500g
				MinWeight:    0.8, // 0.8 KG = 800g
			},
			"CC003": {
				BaseLineInfo: ruleentity.BaseLineInfo{LineId: "CC003"},
				Weightage:    20,
				MaxWeight:    2.0, // 2.0 KG = 2000g
				MinWeight:    1.0, // 1.0 KG = 1000g
			},
		}
	}

	// 测试用例1: 只有一条CC线路
	t.Run("SingleCCLine", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001"}
		availableLineInfo := setupAvailableLineInfo()

		// 设置mock的返回值
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(100), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC001", selectedCC, "Should select the only CC line")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例2: 所有CC线路都已满
	t.Run("AllCCLinesFull", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002", "CC003"}
		availableLineInfo := setupAvailableLineInfo()

		// 设置mock的返回值，所有CC都已达到最大重量
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(1000), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(1500), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC003").Return(int64(2000), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC001", selectedCC, "Should select CC001 based on highest weightage")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例3: 过滤后只有一条未满的CC线路
	t.Run("SingleNotFullCCLine", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002", "CC003"}
		availableLineInfo := setupAvailableLineInfo()

		// 设置mock的返回值，只有CC002未达到最大重量
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(1000), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(800), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC003").Return(int64(2000), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC002", selectedCC, "Should select the only not full CC line")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例4: 有多条未满但都已达到最小启运量的CC线路
	t.Run("MultipleCCLinesAboveMinWeight", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002", "CC003"}
		availableLineInfo := setupAvailableLineInfo()

		// 设置mock的返回值，CC001和CC002都未达到最大重量，但已超过最小重量
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(600), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(900), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC003").Return(int64(2000), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC001", selectedCC, "Should select CC001 based on highest weightage")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例5: 有多条未达到最小启运量的CC线路
	t.Run("MultipleCCLinesBelowMinWeight", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002", "CC003"}
		availableLineInfo := setupAvailableLineInfo()

		// 设置mock的返回值，CC001和CC002都未达到最小重量
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(400), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(700), (*srerr.Error)(nil))
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC003").Return(int64(2000), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC001", selectedCC, "Should select CC001 based on highest weightage")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例6: GetCCWeight返回错误
	t.Run("ErrorFromGetCCWeight", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002"}
		availableLineInfo := setupAvailableLineInfo()

		expectedErr := srerr.New(srerr.CodisErr, nil, "mock codis error")
		// CC001返回错误，CC002正常返回
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(0), expectedErr)
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(300), (*srerr.Error)(nil))

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC002", selectedCC, "Should select CC002 since CC001 returns error")
		mockCounter.AssertExpectations(t)
	})

	// 测试用例7: 所有CC的GetCCWeight都返回错误
	t.Run("AllGetCCWeightError", func(t *testing.T) {
		mockCounter := new(MockILHWeightCounter)
		ilhRoutingService := NewILHRoutingServiceImpl(mockCounter)

		ccLines := []string{"CC001", "CC002"}
		availableLineInfo := setupAvailableLineInfo()

		expectedErr := srerr.New(srerr.CodisErr, nil, "mock codis error")
		// 所有CC都返回错误
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC001").Return(int64(0), expectedErr)
		mockCounter.On("GetCCWeight", mock.Anything, defaultProductID, "CC002").Return(int64(0), expectedErr)

		selectedCC, err := ilhRoutingService.selectOptimalCCLine(ctx, defaultProductID, ccLines, availableLineInfo)

		assert.Nil(t, err, "Should not return error")
		assert.Equal(t, "CC001", selectedCC, "Should select CC001 based on weightage")
		mockCounter.AssertExpectations(t)
	})
}
