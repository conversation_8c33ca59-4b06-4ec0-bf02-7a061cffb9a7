package routing

import (
	"github.com/google/wire"
)

var RoutingServiceProviderSet = wire.NewSet(NewRoutingServiceImpl, wire.Bind(new(RoutingService), new(*RoutingServiceImpl)))

var RoutingRepoProviderSet = wire.NewSet(NewRoutingRuleRepo, wire.Bind(new(RoutingRuleRepo), new(*routingRuleRepoImpl)))

var RoutingPreCalcFeeProviderSet = wire.NewSet(NewRoutingPreCalFeeServiceImpl, wire.Bind(new(PreCalFeeService), new(*PreCalFeeServiceImpl)))

var ILHRoutingServiceProviderSet = wire.NewSet(NewILHRoutingServiceImpl, wire.Bind(new(ILHRoutingService), new(*ILHRoutingServiceImpl)))
