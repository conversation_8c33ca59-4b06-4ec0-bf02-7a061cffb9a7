package forecastservice

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/gogo/protobuf/proto"
	"strconv"
)

const (
	defaultVersion = "default"
)

func (m *ForecastTaskServiceImpl) GetForecastShopGroupList(ctx context.Context) ([]lpsclient.GetClientGroupTab, *srerr.Error) {
	resp, gErr := m.LpsApi.GetWholeShopGroupList(ctx, &lpsclient.GetClientGroupTabsReq{
		ClientTagID: proto.Uint64(uint64(lpsclient.ClientTagCBLM)),
		Version:     proto.String(defaultVersion),
	})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "GetForecastShopGroupList|get shop group list err:%v", gErr)
		return nil, gErr
	}
	return resp.List, nil
}

// TODO:
func (m *ServiceImpl) copyShopGroupInfo(ctx context.Context, forecastRules []*persistent.ForecastingRuleTab) *srerr.Error {
	// 1.组装请求
	req := &lpsclient.CopyShopGroupReq{}
	for _, rule := range forecastRules {
		shopGroupInfo := lpsclient.ShopGroupReq{
			VersionPrefix: lpsclient.ForecastVersionPrefix,
			RuleId:        int64(rule.Id),
			ClientTagId:   int64(lpsclient.ClientTagCBLM),
			ShopGroupList: rule.ShopGroupListVo,
		}
		req.List = append(req.List, shopGroupInfo)
	}
	// 2.call lps api
	if err := m.lpsApi.CopyShopGroupInfo(ctx, req); err != nil {
		logger.CtxLogErrorf(ctx, "CopyShopGroupInfo|err:%v", err)
		return err
	}
	return nil
}

func (m *ServiceImpl) ExportCbShopGroup(ctx context.Context, isTask bool, ruleId int64) (string, *srerr.Error) {
	// 1.first get task
	if isTask {
		return m.ExportForecastCbShopGroup(ctx, ruleId)
	}

	return m.ExportLiveCbShopGroup(ctx, ruleId)
}

func (m *ServiceImpl) ExportForecastCbShopGroup(ctx context.Context, ruleId int64) (string, *srerr.Error) {
	// 1.get task
	rule, gErr := m.Repo.GetForecastRuleById(ctx, int(ruleId))
	if gErr != nil {
		return "", nil
	}
	rule.UnmarshalUrl()
	if rule.UrlInfo.CbShopGroupUrl != "" {
		url := fileutil.GetS3UrlByUrl(ctx, rule.UrlInfo.CbShopGroupUrl)
		return url, nil
	}
	// 2. if not exported, export client info
	rule.UnmarshalShopGroupList()
	url, gErr := m.getAndExportClientInfo(ctx, rule.ShopGroupListVo, lpsclient.FormatForecastVersion(ruleId))
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get and export client info err:%v", gErr)
		return "", gErr
	}
	// 3.update forecast rule
	rule.SetCbUrl(url)
	rule.DownloadType = DownloadCbInfo
	rule.MarshalUrl()
	if uErr := m.Repo.UpdateForecastRules(ctx, []*persistent.ForecastingRuleTab{rule}); uErr != nil {
		logger.CtxLogErrorf(ctx, "update forecast rule err:%v", uErr)
	}

	return url, nil
}

func (m *ServiceImpl) ExportLiveCbShopGroup(ctx context.Context, ruleId int64) (string, *srerr.Error) {
	// 1.get rule
	rule, gErr := m.SoftRuleRepo.GetRoutingRuleFromDB(ctx, ruleId)
	if gErr != nil {
		return "", nil
	}
	// 2.export client info
	rule.UnmarshalShopGroupList()
	return m.getAndExportClientInfo(ctx, rule.ShopGroupListVo, lpsclient.FormatLiveVersion(ruleId))
}

func (m *ServiceImpl) getAndExportClientInfo(ctx context.Context, groupIdList []int64, version string) (string, *srerr.Error) {
	if len(groupIdList) == 0 {
		logger.CtxLogInfof(ctx, "empty shop group list, return")
		return "", nil
	}
	// get client info from lps
	entityList, relationList, gErr := m.lpsApi.GetAllClientInfo(ctx, groupIdList, version)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "get empty client info, err:%v", gErr)
		return "", gErr
	}
	// convert data into excel
	if len(entityList) == 0 && len(relationList) == 0 {
		return "", srerr.New(srerr.ExportParamCanNotNull, nil, "get empty client info")
	}
	entityGroupMap := make(map[string]string, 0)
	groupProductMap := make(map[string]int64, 0)
	for _, entity := range entityList {
		entityGroupMap[entity.ModelId] = entity.ClientGroupId
	}
	for _, relation := range relationList {
		groupProductMap[relation.ClientGroupId] = relation.ProductId
	}
	// fill entity data
	entityData := make([][]string, 0)
	for entityId, groupId := range entityGroupMap {
		entityData = append(entityData, []string{
			"5", //CB client tag
			groupId,
			strconv.FormatInt(groupProductMap[groupId], 10),
			entityId,
		})
	}
	file := excelize.NewFile()
	file, aErr := fileutil.AppendExcel(ctx, file, []string{"Client Tag", "Client Group ID", "Product Remark", "Client Entity ID"}, entityData, "Entity Data", false)
	if aErr != nil {
		errMsg := fmt.Sprintf("export data to excel err:%v", aErr)
		monitoring.ReportError(ctx, monitoring.CatCbShopGroup, "getAndExportClientInfo", errMsg)
		logger.CtxLogErrorf(ctx, errMsg)
		return "", srerr.With(srerr.ParamErr, errMsg, aErr)
	}
	//删掉sheet1
	file.DeleteSheet(file.GetSheetName(0))
	//upload excel to s3
	excelBuffer, wErr := file.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.ParseExcelError, nil, wErr)
	}
	exportTime := timeutil.GetCurrentTime(ctx).Format(timeutil.DefaultTimeFormat)
	s3Key := fmt.Sprintf("CB_Shop_Group:%v.xlsx", exportTime)
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if uErr := fileutil.Upload(ctx, bucket, s3Key, excelBuffer); uErr != nil {
		return "", srerr.With(srerr.S3UploadFail, "failed to upload s3", uErr)
	}

	url := fileutil.GetS3Url(ctx, bucket, s3Key)
	return url, nil
}
