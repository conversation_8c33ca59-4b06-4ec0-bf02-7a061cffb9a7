package forecastservice

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"time"
)

const (
	separator         = "_"
	startRowKeyLength = 4
)

// GenFinishedHCTaskKey 生成已完成硬性校验刷新任务的redis key
func GenFinishedHCTaskKey(keyType string, taskId uint64, productId uint64, orderStartTime string, orderEndTime string) string {
	return fmt.Sprintf("%s_%d_%d_%s_%s", keyType, taskId, productId, orderStartTime, orderEndTime)
}

func getDaysList(start, end time.Time) (results []time.Time) {
	for cur := start; !cur.After(end); cur = cur.Add(time.Hour * 24) {
		results = append(results, cur)
	}
	return results
}

// 检查任务状态是否是执行状态,如果是stop则记录断点，如果是end则不用记录下次做的时候直接重头开始
func (m *ForecastTaskServiceImpl) checkTaskStatusAndRecordBreakpoint(ctx context.Context, taskId int, startRowKey string) bool {
	// 1.从数据库捞出对应的db
	task, err := m.ForecastRepo.GetHardCriteriaTaskById(ctx, uint64(taskId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get hard criteria from db err=%v", err)
		HardCriteriaMonitorReport(ctx, BreakPoint, taskId, fmt.Sprintf("Get hard criteria from db err=%v", err), false)
	}

	logger.CtxLogInfof(ctx, "get status is %v", task.TaskStatus)
	// 2.如果状态是stop则需要记录断点，下次扭转为process的时候直接从断点开始
	if task.TaskStatus == forecast.TaskStopped {
		logger.CtxLogErrorf(ctx, "record startRowKey=%v", startRowKey)
		if err := m.recordStartRowKey(ctx, taskId, startRowKey); err != nil {
			return false
		}
	}

	// 3.判断硬性校验任务状态是不是处于stop/end,是stop/end就需要结束当前的硬性校验
	return task.TaskStatus == forecast.TaskStopped || task.TaskStatus == forecast.TaskTerminated
}

// 保存断线到db
func (m *ForecastTaskServiceImpl) recordStartRowKey(ctx context.Context, taskId int, startRowKey string) *srerr.Error {
	if err := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, uint64(taskId), forecast.TaskStopped, map[string]interface{}{
		"break_point": startRowKey,
	}); err != nil {
		logger.CtxLogErrorf(ctx, "Set break rowkey in db err=%v", err)
		return srerr.New(srerr.DatabaseErr, startRowKey, "Set break rowkey in db err=%v", err)
	}

	return nil
}

func (m *ForecastTaskServiceImpl) getStartRowKey(ctx context.Context, taskId int) (string, *srerr.Error) {
	task, err := m.ForecastRepo.GetHardCriteriaTaskById(ctx, uint64(taskId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get hard criteria from db err=%v", err)
		HardCriteriaMonitorReport(ctx, BreakPoint, taskId, fmt.Sprintf("Get hard criteria from db err=%v", err), false)
	}
	breakRowKey := task.BreakPoint
	// 2.清空数据库里的断点
	if err := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, task.Id, task.TaskStatus, map[string]interface{}{
		"break_point": "",
	}); err != nil {
		logger.CtxLogErrorf(ctx, "Clear break RowKey in db err=%v", err)
		return "", srerr.New(srerr.DatabaseErr, nil, "Clear break RowKey in db err=%v", err)
	}
	// 3.返回对应的断点
	return breakRowKey, nil
}
