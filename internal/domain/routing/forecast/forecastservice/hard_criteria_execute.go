package forecastservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"time"
)

func (m ForecastTaskServiceImpl) getProductLaneCodes(ctx context.Context, productId int, isMultiProduct bool) ([]string, *srerr.Error) {
	prdDetail, err := m.LpsApi.GetProductDetail(ctx, productId)
	if err != nil {
		logger.CtxLogErrorf(ctx, " get product detail err:%+v", err)
		return []string{}, err
	}

	laneCodes := prdDetail.LaneCodes
	if isMultiProduct {
		laneCodes = prdDetail.MultiLaneCodes
	}

	return laneCodes, nil
}

func (m ForecastTaskServiceImpl) parseDayList(orderStartTime, orderEndTime string) ([]time.Time, *srerr.Error) {
	start, err := timeutil.ParseDateStr(orderStartTime)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, orderStartTime, err)
	}

	end, err := timeutil.ParseDateStr(orderEndTime)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, orderEndTime, err)
	}

	return timeutil.GetDays(start, end), nil
}

// checkHardCriteriaTaskIsDoing 检查任务的状态是否还在Doing，获取异常或者非Doing
func (m ForecastTaskServiceImpl) checkHardCriteriaTaskIsDoing(ctx context.Context, id uint64) bool {
	task, err := m.ForecastRepo.GetHardCriteriaTaskById(ctx, id)
	if err != nil {
		// 获取失败的时候返回False，异常情况
		logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskById failed | err:%v", err)
		return false
	}

	logger.CtxLogInfof(ctx, "check hard criteria status, now status: %v", task.TaskStatus)

	return task.TaskStatus == forecast.TaskDoing
}
