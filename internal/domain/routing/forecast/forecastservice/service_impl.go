package forecastservice

import (
	"bytes"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"math"
	"sort"
	"strconv"
	"strings"
)

import (
	"context"
)

func (m *ServiceImpl) MultiProductTaskResult(
	ctx context.Context, productId int, results []persistent.ForecastingTaskResultTab, groupBy GroupByInfo,
) (*foreschema.ShipmentSummaryResponse, *srerr.Error) {
	var total int
	for _, result := range results {
		if result.LaneCode == forecast.AllBlocked {
			result.ServiceCode = forecast.AllBlocked
			continue
		}
		total += result.Quantity
	}

	serviceCodeList, err := m.lfsCli.ListSortingCode(ctx, &lfsentity.ListSortingCodeRequest{ProductId: strconv.Itoa(productId)})
	if err != nil {
		return nil, srerr.With(srerr.LfsError, nil, err)
	}

	serviceCodeMap := make(map[string]*lfsentity.SortingCodeInfo)
	for _, serviceCodeInfo := range serviceCodeList.Data.List {
		serviceCodeMap[serviceCodeInfo.ServiceCode] = serviceCodeInfo
	}

	dgGroupMap, err := m.RuleRepo.GetMultiProductDgGroup(ctx, productId)
	if err != nil {
		return nil, err
	}

	lineNameMap, _ := m.laneSrv.GetLineIdToLineNameMap(ctx)
	var lanes []foreschema.ShipmentSummary
	for _, result := range results {
		var (
			count      = result.Quantity
			percentage = 0
		)

		if total > 0 {
			percentage = int(math.Round(float64(count) / float64(total) * 100))
		}

		serviceCodeResult := foreschema.ShipmentSummary{
			DgFlag:                     int32(result.DGFlag),
			Quantity:                   count,
			Percentage:                 percentage,
			ServiceCode:                result.ServiceCode,
			RuleId:                     result.RuleId,
			ShippingFeePerOrder:        objutil.RoundFloat64(result.AsfInfo.AverageAsf, 4),
			ShippingFeePerOrderUsd:     objutil.RoundFloat64(result.AsfInfo.AverageAsfUsd, 4),
			MissingShippingFeeQuantity: result.AsfInfo.MissingAsfQty,
		}

		if result.LaneCode == forecast.AllBlocked || result.LaneCode == forecast.Blocked || result.LaneCode == forecast.RuleBlocked {
			serviceCodeResult.ServiceCode = result.LaneCode
			serviceCodeResult.DgGroupInfo.LineList = make([]foreschema.LineInfo, 0)
		} else {
			serviceCodeInfo, exist := serviceCodeMap[result.ServiceCode]
			if !exist {
				continue
			}

			dgGroupInfo, exist := dgGroupMap[serviceCodeInfo.DgGroup]
			if !exist {
				continue
			}
			serviceCodeResult.DgGroupInfo = foreschema.DgGroupInfo{DgGroupId: dgGroupInfo.DgGroupId, DgGroupName: dgGroupInfo.DgGroupName}
			serviceCodeResult.LmId = serviceCodeInfo.LmId
			serviceCodeResult.LmName = lineNameMap[serviceCodeInfo.LmId]
			for _, line := range dgGroupInfo.LineList {
				serviceCodeResult.DgGroupInfo.LineList = append(serviceCodeResult.DgGroupInfo.LineList, foreschema.LineInfo{
					LineId:   line,
					LineName: lineNameMap[line],
				})
			}
		}

		if result.RuleId > 0 {
			tmpRule, _ := m.Repo.GetForecastRuleById(ctx, result.RuleId)
			if tmpRule != nil {
				serviceCodeResult.RuleName = tmpRule.RuleName
				serviceCodeResult.RulePriority = tmpRule.Priority
			}
		}
		lanes = append(lanes, serviceCodeResult)
	}

	lanes = groupShipmentSummary(groupBy, lanes)
	return &foreschema.ShipmentSummaryResponse{
		List:               lanes,
		ConsolidatedResult: consolidateShipmentSummary(lanes),
	}, nil
}

func groupShipmentSummary(groupByInfo GroupByInfo, shipmentSummaries []foreschema.ShipmentSummary) []foreschema.ShipmentSummary {
	var (
		groupShipmentSummaries    = make([]foreschema.ShipmentSummary, 0)
		groupShipmentSummariesMap = make(map[string][]foreschema.ShipmentSummary)
		total                     int
	)

	for _, shipmentSummary := range shipmentSummaries {
		key := fmt.Sprintf("%v:%v", shipmentSummary.RuleId, shipmentSummary.LmId)
		if groupByInfo.GroupByIlhGroup {
			key += ":" + shipmentSummary.DgGroupInfo.DgGroupId
		}
		if groupByInfo.GroupByTp {
			key += ":" + fmt.Sprint(shipmentSummary.DgFlag)
		}
		if groupByInfo.GroupByServiceCode {
			key += ":" + shipmentSummary.ServiceCode
		}

		groupShipmentSummariesMap[key] = append(groupShipmentSummariesMap[key], shipmentSummary)
		if shipmentSummary.ServiceCode != forecast.AllBlocked {
			total += shipmentSummary.Quantity
		}
	}

	for _, needGroupShipmentSummaries := range groupShipmentSummariesMap {
		if len(needGroupShipmentSummaries) == 0 {
			continue
		}
		var (
			groupTotalAsf, groupTotalAsfUsd       float64
			groupTotal, groupTotalMissingAsfCount int
			currentShipmentSummary                = needGroupShipmentSummaries[0]
		)
		for _, s := range needGroupShipmentSummaries {
			groupTotal += s.Quantity
			groupTotalMissingAsfCount += s.MissingShippingFeeQuantity
			groupTotalAsf += s.ShippingFeePerOrder * float64(s.Quantity-s.MissingShippingFeeQuantity)
			groupTotalAsfUsd += s.ShippingFeePerOrderUsd * float64(s.Quantity-s.MissingShippingFeeQuantity)
		}

		// 如果没有group的，信息要置空
		if !groupByInfo.GroupByIlhGroup {
			currentShipmentSummary.DgGroupInfo = foreschema.DgGroupInfo{}
		}
		if !groupByInfo.GroupByTp {
			currentShipmentSummary.DgFlag = 0
		}
		if !groupByInfo.GroupByServiceCode {
			currentShipmentSummary.ServiceCode = ""
		}

		// 重新算Asf和Total
		currentShipmentSummary.Quantity = groupTotal
		asfCount := float64(groupTotal - groupTotalMissingAsfCount)
		if asfCount > 0 {
			currentShipmentSummary.ShippingFeePerOrder = objutil.RoundFloat64(groupTotalAsf/asfCount, 4)
			currentShipmentSummary.ShippingFeePerOrderUsd = objutil.RoundFloat64(groupTotalAsfUsd/asfCount, 4)
		}
		currentShipmentSummary.MissingShippingFeeQuantity = groupTotalMissingAsfCount
		var percentage int
		if total > 0 {
			percentage = int(math.Round(float64(groupTotal) / float64(total) * 100))
		}
		currentShipmentSummary.Percentage = percentage
		groupShipmentSummaries = append(groupShipmentSummaries, currentShipmentSummary)
	}

	return groupShipmentSummaries
}

func consolidateShipmentSummary(shipmentSummaries []foreschema.ShipmentSummary) []foreschema.ShipmentSummary {
	// 按照ILH Group + LM +TP + Service Code做聚合
	var (
		consolidatedMap    = make(map[string][]foreschema.ShipmentSummary)
		consolidatedResult = make([]foreschema.ShipmentSummary, 0)
		total              int
	)

	for _, shipmentSummary := range shipmentSummaries {
		if shipmentSummary.ServiceCode == forecast.Blocked {
			// Rule Block不需要统计了，有All Block
			continue
		}
		if shipmentSummary.ServiceCode != forecast.AllBlocked {
			total += shipmentSummary.Quantity
		}
		key := fmt.Sprintf("%v:%v:%v:%v", shipmentSummary.DgGroupInfo.DgGroupId, shipmentSummary.LmId, shipmentSummary.DgFlag, shipmentSummary.ServiceCode)
		consolidatedMap[key] = append(consolidatedMap[key], shipmentSummary)

	}
	for _, needConsolidatedList := range consolidatedMap {
		if len(needConsolidatedList) == 0 {
			continue
		}
		var (
			conTotalAsf, conTotalAsfUsd       float64
			conTotal, conTotalMissingAsfCount int
			currentConsolidated               = needConsolidatedList[0]
		)
		for _, c := range needConsolidatedList {
			conTotal += c.Quantity
			conTotalMissingAsfCount += c.MissingShippingFeeQuantity
			conTotalAsf += c.ShippingFeePerOrder * float64(c.Quantity-c.MissingShippingFeeQuantity)
			conTotalAsfUsd += c.ShippingFeePerOrderUsd * float64(c.Quantity-c.MissingShippingFeeQuantity)
		}
		// consolidated result的Rule信息要置空
		currentConsolidated.RuleId = 0
		currentConsolidated.RuleName = ""
		currentConsolidated.RulePriority = 0
		// 重新算Asf和Total
		currentConsolidated.Quantity = conTotal
		asfCount := float64(conTotal - conTotalMissingAsfCount)
		if asfCount > 0 {
			currentConsolidated.ShippingFeePerOrder = objutil.RoundFloat64(conTotalAsf/asfCount, 4)
			currentConsolidated.ShippingFeePerOrderUsd = objutil.RoundFloat64(conTotalAsfUsd/asfCount, 4)
		}
		var percentage int
		if total > 0 {
			percentage = int(math.Round(float64(conTotal) / float64(total) * 100))
		}
		currentConsolidated.Percentage = percentage
		currentConsolidated.MissingShippingFeeQuantity = conTotalMissingAsfCount
		consolidatedResult = append(consolidatedResult, currentConsolidated)
	}

	return consolidatedResult
}

func (m *ServiceImpl) GetResource(ctx context.Context, laneCode string) forecastentity.SimpleResourceInfo {
	if strings.Contains(laneCode, "/") {
		laneCodeGroup := strings.Split(laneCode, "/")
		interLaneCode, localLocalCode := laneCodeGroup[0], laneCodeGroup[1]
		interLaneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, interLaneCode)
		if err != nil {
			return forecastentity.SimpleResourceInfo{}
		}
		localLaneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, localLocalCode)
		if err != nil {
			return forecastentity.SimpleResourceInfo{}
		}

		return forecastentity.SimpleResourceInfo{
			FlId:      interLaneInfo.GetCFLLineInfo().LineID,
			FlName:    interLaneInfo.GetCFLLineInfo().LineName,
			LmId:      localLaneInfo.GetCLMLineInfo().LineID,
			LmName:    localLaneInfo.GetCLMLineInfo().LineName,
			TwsId:     interLaneInfo.GetTwsInfo().SiteID,
			TwsName:   interLaneInfo.GetTwsInfo().SiteName,
			JointId:   interLaneInfo.GetJointInfo().SiteID,
			JointName: interLaneInfo.GetJointInfo().SiteName,
		}
	}
	laneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
	if err != nil {
		return forecastentity.SimpleResourceInfo{}
	}
	return forecastentity.SimpleResourceInfo{
		FlId:      laneInfo.GetCFLLineInfo().LineID,
		FlName:    laneInfo.GetCFLLineInfo().LineName,
		LmId:      laneInfo.GetCLMLineInfo().LineID,
		LmName:    laneInfo.GetCLMLineInfo().LineName,
		TwsId:     laneInfo.GetTwsInfo().SiteID,
		TwsName:   laneInfo.GetTwsInfo().SiteName,
		JointId:   laneInfo.GetJointInfo().SiteID,
		JointName: laneInfo.GetJointInfo().SiteName,
	}
}

func summaryByServiceCode(shipmentSummaryList []foreschema.ShipmentSummary) []foreschema.ShipmentSummary {
	summaryListByServiceCode := make([]foreschema.ShipmentSummary, 0)
	ruleServiceCodeMap := make(map[string]int)
	total := 0
	for _, summaryByLaneCode := range shipmentSummaryList {
		key := fmt.Sprintf("%v:%v", summaryByLaneCode.ServiceCode, summaryByLaneCode.RuleId)
		_, exist := ruleServiceCodeMap[key]
		if !exist {
			summary := foreschema.ShipmentSummary{
				FlId:                       summaryByLaneCode.FlId,
				FlName:                     summaryByLaneCode.FlName,
				LmId:                       summaryByLaneCode.LmId,
				LmName:                     summaryByLaneCode.LmName,
				DgFlag:                     summaryByLaneCode.DgFlag,
				ServiceCode:                summaryByLaneCode.ServiceCode,
				RuleId:                     summaryByLaneCode.RuleId,
				RuleName:                   summaryByLaneCode.RuleName,
				RulePriority:               summaryByLaneCode.RulePriority,
				ShippingFeePerOrder:        summaryByLaneCode.ShippingFeePerOrder,
				ShippingFeePerOrderUsd:     summaryByLaneCode.ShippingFeePerOrderUsd,
				MissingShippingFeeQuantity: summaryByLaneCode.MissingShippingFeeQuantity,
			}
			summaryListByServiceCode = append(summaryListByServiceCode, summary)
		}
		ruleServiceCodeMap[key] += summaryByLaneCode.Quantity
		total += summaryByLaneCode.Quantity
	}
	for _, summary := range summaryListByServiceCode {
		quantity := ruleServiceCodeMap[fmt.Sprintf("%v:%v", summary.ServiceCode, summary.RuleId)]
		percentage := 0
		if total > 0 {
			percentage = int(math.Round(float64(quantity) / float64(total) * 100))
		}
		summary.Quantity = quantity
		summary.Percentage = percentage
	}

	return summaryListByServiceCode
}

func (m *ServiceImpl) exportIlhForecastResult(ctx context.Context, results *foreschema.IlhShipmentSummaryResponse) (*bytes.Buffer, *srerr.Error) {
	sheetMap := make(map[int][]*foreschema.IlhShipmentSummary)
	for _, item := range results.List {
		if item.RuleId == 0 {
			continue
		}
		sheetMap[item.RulePriority] = append(sheetMap[item.RulePriority], item)
	}
	priorityList := make([]int, 0, len(sheetMap))
	for priority := range sheetMap {
		priorityList = append(priorityList, priority)
	}
	sort.Ints(priorityList)
	file := excelize.NewFile()
	for _, priority := range priorityList {
		summary := sheetMap[priority]
		sheetName := fmt.Sprintf("%v-%v-%v", summary[0].RuleId, summary[0].RuleName, summary[0].RulePriority) //加上rule id唯一标识，避免截断而覆盖数据
		index := file.NewSheet(sheetName)
		_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{
			"Date",
			"FL",
			"Import ILH",
			"TP",
			"Service Code",
			"Carton Quantity",
			"Carton Percentage",
			"Weight Quantity(KG)",
			"Weight Percentage",
			"Parcel Quantity",
			"Parcel Percentage"},
		)
		// 对summary按statDate进行排序
		sort.Slice(summary, func(i, j int) bool {
			return summary[i].StatDate < summary[j].StatDate
		})
		for rowN, item := range summary {
			if item == nil {
				logger.CtxLogErrorf(ctx, "summary result is empty, row:%d", rowN)
				continue
			}
			logger.CtxLogInfof(ctx, "ExportIlhForecastResult: row:%v, item:%v", rowN, *item)
			line := rowN + 1

			cartonQuantityStr := fmt.Sprintf("%v", item.CartonQuantity)
			cartonPercentStr := fmt.Sprintf("%d%%", item.QuantityPercentage)

			weightQuantityStr := fmt.Sprintf("%v", item.WeightQuantity)
			weightPercentStr := fmt.Sprintf("%d%%", item.WeightPercent)

			parcelQuantityStr := fmt.Sprintf("%v", item.ParcelQuantity)
			parcelPercentStr := fmt.Sprintf("%d%%", item.ParcelQuantityPercentage)

			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), item.StatDate)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), item.FlInfo)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), item.ImportIlhInfo)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.TP)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), item.ServiceCode)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), cartonQuantityStr)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 6), cartonPercentStr)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 7), weightQuantityStr)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 8), weightPercentStr)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 9), parcelQuantityStr)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 10), parcelPercentStr)
		}
		file.SetActiveSheet(index)
	}
	file.DeleteSheet(file.GetSheetName(0))
	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, nil, fErr)
	}

	return buffer, nil
}

func (m *ServiceImpl) exportMultiProductForecastResult(ctx context.Context, results *foreschema.ShipmentSummaryResponse) (*bytes.Buffer, *srerr.Error) {
	sheetMap := make(map[int][]foreschema.ShipmentSummary)
	for _, item := range results.List {
		if item.RuleId == 0 {
			continue
		}
		sheetMap[item.RulePriority] = append(sheetMap[item.RulePriority], item)
	}
	priorityList := make([]int, 0, len(sheetMap))
	for priority := range sheetMap {
		priorityList = append(priorityList, priority)
	}
	sort.Ints(priorityList)
	file := excelize.NewFile()

	// 每个Rule单独的Sheet
	for _, priority := range priorityList {
		summary := sheetMap[priority]
		sheetName := fmt.Sprintf("%v-%v-%v", summary[0].RuleId, summary[0].RuleName, summary[0].RulePriority) //加上rule id唯一标识，避免截断而覆盖数据
		index := file.NewSheet(sheetName)
		writeMultiSheet(file, sheetName, index, summary)
	}

	// 单独一个Sheet写Consolidated Result
	conSheetName := "Consolidated Result"
	writeMultiSheet(file, conSheetName, file.NewSheet(conSheetName), results.ConsolidatedResult)

	file.DeleteSheet(file.GetSheetName(0))
	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, nil, fErr)
	}

	return buffer, nil
}

func writeMultiSheet(file *excelize.File, sheetName string, index int, shipmentSummaries []foreschema.ShipmentSummary) {
	var (
		headers = &[]interface{}{
			"ILH Group",
			"LM",
			"TP",
			"Service Code",
			"Shipment Quantity",
			"Percentage",
			"Shipping Fee per Order",
			"Shipping Fee per Order (USD)",
			"Missing Shipping Fee Quantity",
		}
	)

	_ = file.SetSheetRow(sheetName, "A1", headers)
	for rowN, item := range shipmentSummaries {
		line := rowN + 1

		var tpDisplay string
		switch item.DgFlag {
		case 1:
			tpDisplay = "Non-DG"
		case 2:
			tpDisplay = "DG"
		}

		var ilhDisplay string
		if item.DgGroupInfo.DgGroupId != "" {
			ilhDisplay = fmt.Sprintf("%s(", item.DgGroupInfo.DgGroupId)
			for _, lineInfo := range item.DgGroupInfo.LineList {
				ilhDisplay += fmt.Sprintf("%s-%s", lineInfo.LineId, lineInfo.LineName)
			}
			ilhDisplay += ")"
		}

		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), ilhDisplay)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), fmt.Sprintf("%v - %v", item.LmId, item.LmName))
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), tpDisplay)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.ServiceCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), item.Quantity)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), fmt.Sprintf("%d%%", item.Percentage))
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 6), item.ShippingFeePerOrder)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 7), item.ShippingFeePerOrderUsd)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 8), item.MissingShippingFeeQuantity)
	}
	file.SetActiveSheet(index)
}
