package forecastservice

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	NewForecastServiceImpl,
	wire.Bind(new(IForecastService), new(*ServiceImpl)),
	volume_counter.VolumeCounterProviderSet,
	forecastrepo.ProviderSet,
)

var ForecastTaskServiceProviderSet = wire.NewSet(
	NewForecastTaskServiceImpl,
	wire.Bind(new(IForecastTaskService), new(*ForecastTaskServiceImpl)),
)
