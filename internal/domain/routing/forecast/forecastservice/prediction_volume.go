package forecastservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

var (
	predictionVolumeFileHeader = []string{"Shipment Date", "TWS", "Dest Site", "Dg Type", "Carton Qty", "Total Weight", "Parcel Qty", "LM", "Multi Product ID"}
)

func (m *ServiceImpl) UploadPredictionVolume(ctx context.Context, request *foreschema.UploadPredictionVolumeReq) *srerr.Error {
	fileResp, err := httputil.GetWithUrl(ctx, request.FileUrl)
	if err != nil {
		return srerr.With(srerr.S3DownloadFail, request.FileUrl, err)
	}

	dataList, headers, pErr := fileutil.ParseExcel(ctx, fileResp.Body, true)
	if pErr != nil {
		return srerr.With(srerr.ExcelFileOpenError, request.FileUrl, pErr)
	}

	if err := m.checkPredictionVolumeHeader(ctx, headers); err != nil {
		return err
	}

	predictionVolumes := make([]*persistent.ForecastingPredictionVolumeTab, 0, len(dataList))
	for _, data := range dataList {
		if fileutil.IsEmptyRow(data) {
			logger.CtxLogInfof(ctx, "meet empty row, total valid rows: %s", len(predictionVolumes))
			break
		}

		p, err := m.parsePredictionVolumeExcelDataToPersistent(ctx, data)
		if err != nil {
			return err
		}
		p.TaskId = request.TaskId
		predictionVolumes = append(predictionVolumes, p)
	}

	if err := m.Repo.DeletePredictionVolumesByTaskId(ctx, request.TaskId); err != nil {
		return err
	}

	if err := m.Repo.BatchCreatePredictionVolumes(ctx, predictionVolumes); err != nil {
		return err
	}

	return nil
}

func (m *ServiceImpl) parsePredictionVolumeExcelDataToPersistent(ctx context.Context, data []string) (*persistent.ForecastingPredictionVolumeTab, *srerr.Error) {
	if len(data) != len(predictionVolumeFileHeader) {
		return nil, srerr.New(srerr.ExcelValidateError, data, "data length not equal to headers")
	}

	var (
		tws            = data[1]
		destSite       = data[2]
		lm             = data[7]
		dgType         int
		multiProductId int
	)

	dateOffset, err := strconv.Atoi(data[0])
	if err != nil {
		return nil, srerr.New(srerr.ExcelValidateError, data[0], "shipment date invalid: %s", data[0])
	}
	shipmentDate := timeutil.GetTimeByExcelFloatOffset(float64(dateOffset)).Format(timeutil.DateFormat)

	if data[3] == "Dg" {
		dgType = 2
	} else if data[3] == "NonDg" {
		dgType = 1
	} else {
		return nil, srerr.New(srerr.ExcelValidateError, data[3], "dg type invalid: %s", data[3])
	}

	cartonQty, err := strconv.Atoi(data[4])
	if err != nil {
		return nil, srerr.New(srerr.ExcelValidateError, data[4], "carton qty is not int type: %s", data[4])
	}
	totalWeight, err := strconv.ParseFloat(data[5], 64)
	if err != nil {
		return nil, srerr.New(srerr.ExcelValidateError, data[5], "total weight is not float type: %s", data[5])
	}
	parcelQty, err := strconv.Atoi(data[6])
	if err != nil {
		return nil, srerr.New(srerr.ExcelValidateError, data[6], "parcel qty is not int type: %s", data[6])
	}

	// Parse Multi Product ID if provided (it's optional)
	if data[8] != "" {
		multiProductId, err = strconv.Atoi(data[8])
		if err != nil {
			return nil, srerr.New(srerr.ExcelValidateError, data[8], "multi product id is not int type: %s", data[8])
		}
	}

	return &persistent.ForecastingPredictionVolumeTab{
		ShipmentDate:   shipmentDate,
		Tws:            tws,
		DestSite:       destSite,
		TP:             dgType,
		CartonQuantity: cartonQty,
		TotalWeight:    totalWeight,
		ParcelQuantity: parcelQty,
		Lm:             lm,
		MultiProductId: multiProductId,
	}, nil
}

func (m *ServiceImpl) checkPredictionVolumeHeader(ctx context.Context, headers []string) *srerr.Error {
	if len(headers) != len(predictionVolumeFileHeader) {
		return srerr.New(srerr.ImportHeaderError, headers, "invalid header: %v, expect: %v", headers, predictionVolumeFileHeader)
	}

	for index, h := range headers {
		if h == "" {
			return srerr.New(srerr.ImportHeaderError, h, "header is empty string")
		}
		if h != predictionVolumeFileHeader[index] {
			return srerr.New(srerr.ImportHeaderError, h, "invalid header value: %s", h)
		}
	}

	return nil
}

func (m *ServiceImpl) GetPredictionVolumeByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastingPredictionVolumeTab, *srerr.Error) {
	return m.Repo.GetPredictionVolumesByCondition(ctx, map[string]interface{}{"task_id": taskId})
}
