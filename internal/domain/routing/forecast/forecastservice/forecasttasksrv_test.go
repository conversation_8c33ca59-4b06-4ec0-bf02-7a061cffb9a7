package forecastservice

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
)

func TestNewContext(t *testing.T) {
	tests := []struct {
		name           string
		setupCtx       func() context.Context
		orderLog       routing_log.RoutingLog
		taskId         int
		expectedReqId  string
		validateResult func(t *testing.T, ctx context.Context, taskId int)
	}{
		{
			name: "正常情况 - 有原始requestId",
			setupCtx: func() context.Context {
				ctx := context.Background()
				originalReqId := "original-request-123"
				return requestid.SetToCtx(ctx, originalReqId)
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "ORDER123456",
			},
			taskId:        100,
			expectedReqId: "original-request-123|ORDER123456__100",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				// 验证requestId设置正确
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "original-request-123|ORDER123456__100", actualReqId)

				// 验证forecast context设置正确
				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "空的原始requestId",
			setupCtx: func() context.Context {
				return context.Background()
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "ORDER789",
			},
			taskId:        200,
			expectedReqId: "|ORDER789__200",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "|ORDER789__200", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "空的FOrderId",
			setupCtx: func() context.Context {
				ctx := context.Background()
				return requestid.SetToCtx(ctx, "req-456")
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "",
			},
			taskId:        300,
			expectedReqId: "req-456|__300",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "req-456|__300", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "taskId为0",
			setupCtx: func() context.Context {
				ctx := context.Background()
				return requestid.SetToCtx(ctx, "req-zero")
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "ORDER000",
			},
			taskId:        0,
			expectedReqId: "req-zero|ORDER000__0",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "req-zero|ORDER000__0", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "负数taskId",
			setupCtx: func() context.Context {
				ctx := context.Background()
				return requestid.SetToCtx(ctx, "req-negative")
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "ORDER_NEG",
			},
			taskId:        -1,
			expectedReqId: "req-negative|ORDER_NEG__-1",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "req-negative|ORDER_NEG__-1", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "包含特殊字符的FOrderId",
			setupCtx: func() context.Context {
				ctx := context.Background()
				return requestid.SetToCtx(ctx, "req-special")
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "ORDER@#$%^&*()",
			},
			taskId:        999,
			expectedReqId: "req-special|ORDER@#$%^&*()__999",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "req-special|ORDER@#$%^&*()__999", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
		{
			name: "长FOrderId",
			setupCtx: func() context.Context {
				ctx := context.Background()
				return requestid.SetToCtx(ctx, "req-long")
			},
			orderLog: routing_log.RoutingLog{
				FOrderId: "VERY_LONG_ORDER_ID_WITH_MANY_CHARACTERS_1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ",
			},
			taskId:        12345,
			expectedReqId: "req-long|VERY_LONG_ORDER_ID_WITH_MANY_CHARACTERS_1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ__12345",
			validateResult: func(t *testing.T, ctx context.Context, taskId int) {
				actualReqId := requestid.GetFromCtx(ctx)
				assert.Equal(t, "req-long|VERY_LONG_ORDER_ID_WITH_MANY_CHARACTERS_1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ__12345", actualReqId)

				isForecast, actualTaskId := forecast.IsForecast(ctx)
				assert.True(t, isForecast)
				assert.Equal(t, taskId, actualTaskId)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试上下文
			ctx := tt.setupCtx()

			// 调用被测试的函数
			result := newContext(ctx, tt.orderLog, tt.taskId)

			// 验证结果不为nil
			assert.NotNil(t, result)

			// 验证结果与原始context不同（除非没有变化）
			assert.NotEqual(t, ctx, result)

			// 执行自定义验证
			tt.validateResult(t, result, tt.taskId)
		})
	}
}

func TestNewContext_LoggerContext(t *testing.T) {
	// 测试logger context是否正确设置
	ctx := context.Background()
	originalReqId := "test-logger-123"
	ctx = requestid.SetToCtx(ctx, originalReqId)

	orderLog := routing_log.RoutingLog{
		FOrderId: "LOGGER_TEST_ORDER",
	}
	taskId := 555

	result := newContext(ctx, orderLog, taskId)

	// 验证logger context设置
	expectedReqId := "test-logger-123|LOGGER_TEST_ORDER__555"
	actualReqId := requestid.GetFromCtx(result)
	assert.Equal(t, expectedReqId, actualReqId)

	// 验证logger.NewLogContext被调用（通过检查context是否包含logger信息）
	// 这里我们主要验证requestId是否正确传递
	assert.NotNil(t, result)
}

func TestNewContext_ForecastContext(t *testing.T) {
	// 专门测试forecast context的设置
	ctx := context.Background()
	ctx = requestid.SetToCtx(ctx, "forecast-test")

	orderLog := routing_log.RoutingLog{
		FOrderId: "FORECAST_ORDER",
	}
	taskId := 777

	result := newContext(ctx, orderLog, taskId)

	// 验证forecast context
	isForecast, actualTaskId := forecast.IsForecast(result)
	assert.True(t, isForecast, "Context should be marked as forecast context")
	assert.Equal(t, taskId, actualTaskId, "Task ID should match")
}

func TestNewContext_ContextChaining(t *testing.T) {
	// 测试context链式调用
	ctx := context.Background()

	// 添加一些自定义值到原始context
	type testKey struct{}
	testValue := "test-value"
	ctx = context.WithValue(ctx, testKey{}, testValue)
	ctx = requestid.SetToCtx(ctx, "chain-test")

	orderLog := routing_log.RoutingLog{
		FOrderId: "CHAIN_ORDER",
	}
	taskId := 888

	result := newContext(ctx, orderLog, taskId)

	// 验证原始context的值是否保留
	actualValue := result.Value(testKey{})
	assert.Equal(t, testValue, actualValue, "Original context values should be preserved")

	// 验证新的值是否正确设置
	actualReqId := requestid.GetFromCtx(result)
	assert.Equal(t, "chain-test|CHAIN_ORDER__888", actualReqId)

	isForecast, actualTaskId := forecast.IsForecast(result)
	assert.True(t, isForecast)
	assert.Equal(t, taskId, actualTaskId)
}
