package forecastservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type DeployEntity struct {
	MultiProductId     int
	ForecastTaskId     int
	EffectiveStartTime int64
}

var (
	batchDeployHeader = []string{"Multi Product ID", "Forecast Task ID", "Effective Start Time"}
)

func (m *ServiceImpl) BatchDeployCheck(ctx context.Context, request *forecast.BatchDeployReq) ([]forecast.BlockItem, *srerr.Error) {
	deployList, err := m.getAndCheckBatchDeployData(ctx, request.FileUrl)
	if err != nil {
		return nil, err
	}

	var blockList []forecast.BlockItem
	for _, d := range deployList {
		blockRate, err := m.getTaskBlockRate(ctx, d.ForecastTaskId)
		if err != nil {
			return nil, err
		}
		if blockRate > 0 {
			blockList = append(blockList, forecast.BlockItem{
				MultiProductId: d.MultiProductId,
				TaskId:         d.ForecastTaskId,
				BlockRate:      blockRate,
			})
		}
	}

	return blockList, nil
}

func (m *ServiceImpl) BatchDeploy(ctx context.Context, request *forecast.BatchDeployReq) *srerr.Error {
	deployList, err := m.getAndCheckBatchDeployData(ctx, request.FileUrl)
	if err != nil {
		return err
	}

	operateBy, _ := apiutil.GetUserInfo(ctx)
	for _, d := range deployList {
		task, err := m.GetTaskById(ctx, d.ForecastTaskId)
		if err != nil {
			return err
		}

		if err := m.deployForecastRules(ctx, task, d.EffectiveStartTime, operateBy); err != nil {
			return err
		}
	}

	return nil
}

func (m *ServiceImpl) getAndCheckBatchDeployData(ctx context.Context, fileUrl string) ([]*DeployEntity, *srerr.Error) {
	fileResp, err := httputil.GetWithUrl(ctx, fileUrl)
	if err != nil {
		return nil, srerr.With(srerr.S3DownloadFail, fileUrl, err)
	}

	dataList, headers, pErr := fileutil.ParseExcel(ctx, fileResp.Body, true)
	if pErr != nil {
		return nil, srerr.With(srerr.ExcelFileOpenError, fileUrl, pErr)
	}

	if len(headers) != len(batchDeployHeader) {
		return nil, srerr.New(srerr.ImportHeaderError, headers, "invalid header: %v, expect: %v", headers, batchDeployHeader)
	}

	for index, h := range headers {
		if h == "" {
			return nil, srerr.New(srerr.ImportHeaderError, h, "header is empty string")
		}
		if h != batchDeployHeader[index] {
			return nil, srerr.New(srerr.ImportHeaderError, h, "invalid header value: %s", h)
		}
	}

	deployList := make([]*DeployEntity, 0, len(dataList))
	for _, data := range dataList {
		// 空行直接截断跳过
		if fileutil.IsEmptyRow(data) {
			logger.CtxLogInfof(ctx, "meet empty row, total valid rows: %s", len(deployList))
			break
		}

		multiProductId, err := strconv.Atoi(data[0])
		if err != nil {
			return nil, srerr.With(srerr.ExcelValidateError, data[0], err)
		}
		forecastTaskId, err := strconv.Atoi(data[1])
		if err != nil {
			return nil, srerr.With(srerr.ExcelValidateError, data[1], err)
		}
		timeOffset, err := strconv.ParseFloat(data[2], 64)
		if err != nil {
			return nil, srerr.With(srerr.ExcelValidateError, data[2], err)
		}
		effectiveStartTime := timeutil.GetTimeByExcelFloatOffset(timeOffset)
		deployList = append(deployList, &DeployEntity{
			MultiProductId:     multiProductId,
			ForecastTaskId:     forecastTaskId,
			EffectiveStartTime: effectiveStartTime.Unix(),
		})
	}

	return deployList, nil
}
