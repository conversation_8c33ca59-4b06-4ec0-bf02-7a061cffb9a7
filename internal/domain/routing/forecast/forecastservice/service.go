package forecastservice

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	persistent2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient/lfsentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/soft_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	mathutil2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	jsoniter "github.com/json-iterator/go"
	"modernc.org/mathutil"
)

type IForecastService interface {
	CreateTask(ctx context.Context, request *foreschema.CreateTaskRequest) (*persistent.ForecastingTaskTab, *srerr.Error)
	UpdateTask(ctx context.Context, request *foreschema.UpdateTaskRequest) (*persistent.ForecastingTaskTab, *srerr.Error)
	StartTask(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	StopTask(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	DeleteTaskById(ctx context.Context, Id int) *srerr.Error
	GetTaskById(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	CopyTaskById(ctx context.Context, Id int) *srerr.Error
	CheckTaskZoneValid(ctx context.Context, request *foreschema.CheckTaskZoneValidRequest) (*foreschema.CheckTaskZoneValidResponse, *srerr.Error)
	GetTaskList(ctx context.Context, request *foreschema.GetTaskListRequest) (*foreschema.GetTaskListResponse, *srerr.Error)
	DeployTaskById(ctx context.Context, request *foreschema.DeployTaskRequest) (*foreschema.TaskDeployResponse, *srerr.Error)
	TaskResult(ctx context.Context, request *foreschema.TaskResultRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error)
	IlhTaskResult(ctx context.Context, request *foreschema.TaskResultRequest, businessType uint8) (*foreschema.IlhShipmentSummaryResponse, *srerr.Error)

	GetShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error)
	GetMultiProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error)
	GetIlhProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.IlhShipmentSummaryResponse, *srerr.Error)
	ExportShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error)
	ExportMultiProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error)
	ExportIlhShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error)
	ExportForecastResult(ctx context.Context, request *foreschema.TaskResultRequest) (*bytes.Buffer, *srerr.Error)
	DeleteVolumeRuleByTaskId(ctx context.Context, taskId int) *srerr.Error
	BatchCreateVolume(ctx context.Context, volumeList []*foreschema.VolumeRuleInfo, task *persistent.ForecastingTaskTab) *srerr.Error
	ImportWeightRange(ctx context.Context, taskId int64, filePath string) ([]*persistent.WeightRange, *srerr.Error)
	ImportOrderCount(ctx context.Context, taskId int64, filePath string) ([]*persistent.SimulationOrderCount, *srerr.Error)
	AggregationOrderData(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error)
	QueryOrderAggregation(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.ForecastRuleInfo, *srerr.Error)
	ExportOrderAggregation(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (string, *srerr.Error)
	GetBlockOrderList(ctx context.Context, request foreschema.BlockOrderListRequest) (map[int][]foreschema.BlockOrderListResponse, *srerr.Error)
	ExportCbShopGroup(ctx context.Context, isTask bool, ruleId int64) (string, *srerr.Error)

	// ilh prediction volume
	UploadPredictionVolume(ctx context.Context, request *foreschema.UploadPredictionVolumeReq) *srerr.Error
	GetPredictionVolumeByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastingPredictionVolumeTab, *srerr.Error)

	// batch deploy
	BatchDeployCheck(ctx context.Context, request *foreschema.BatchDeployReq) ([]foreschema.BlockItem, *srerr.Error)
	BatchDeploy(ctx context.Context, request *foreschema.BatchDeployReq) *srerr.Error
}

const (
	NoneDG            = 1
	DG                = 2
	maxSiteCode       = 2
	defaultDay        = 1
	blockRuleId       = 0
	maxSimulationDays = 7
)

type ServiceImpl struct {
	Repo             forecastrepo.IForecastRepo
	lfsCli           lfsclient.LfsApi
	RuleRepo         routing.RoutingRuleRepo
	zoneRepo         locationzone.ZoneRepo
	VolumeCounterSrv volume_counter.VolumeCounter
	laneSrv          lane.LaneService
	address          address.AddrRepo
	dataApi          *dataclient.DataApi
	zoneMgr          volumerouting.ZoneRuleMgr
	ZoneRuleRepo     vrrepo.ZoneRuleRepo
	lpsApi           lpsclient.LpsApi
	SoftRuleRepo     ruledata.SoftRuleRepo
}

func NewForecastServiceImpl(
	repo forecastrepo.IForecastRepo,
	lfsCli lfsclient.LfsApi,
	RuleRepo routing.RoutingRuleRepo,
	zoneRepo locationzone.ZoneRepo,
	VolumeCounterSrv volume_counter.VolumeCounter,
	laneSrv lane.LaneService,
	address address.AddrRepo,
	zoneMgr volumerouting.ZoneRuleMgr,
	ZoneRuleRepo vrrepo.ZoneRuleRepo,
	lpsApi lpsclient.LpsApi,
	SoftRuleRepo ruledata.SoftRuleRepo,
	dataApi *dataclient.DataApi,
) *ServiceImpl {
	return &ServiceImpl{
		Repo:             repo,
		lfsCli:           lfsCli,
		RuleRepo:         RuleRepo,
		zoneRepo:         zoneRepo,
		VolumeCounterSrv: VolumeCounterSrv,
		laneSrv:          laneSrv,
		address:          address,
		zoneMgr:          zoneMgr,
		ZoneRuleRepo:     ZoneRuleRepo,
		lpsApi:           lpsApi,
		SoftRuleRepo:     SoftRuleRepo,
		dataApi:          dataApi,
	}
}

func (m *ServiceImpl) CreateTask(ctx context.Context, request *foreschema.CreateTaskRequest) (*persistent.ForecastingTaskTab, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	draftTask, err := m.Repo.GetDraftTaskByProductId(ctx, request.ProductId, request.IsMultiProduct, request.RoutingType)
	if err == nil && draftTask.IsMultiProduct == request.IsMultiProduct {
		return nil, srerr.New(srerr.TaskExistDraft, nil, "product_id: %d task exist draft. ", request.ProductId)
	}

	model := &persistent.ForecastingTaskTab{
		ProductId:      request.ProductId,
		TaskStatus:     persistent.TaskStatusDraft,
		TaskName:       request.TaskName,
		OperateBy:      operateBy,
		RoutingType:    request.RoutingType,
		IsMultiProduct: request.IsMultiProduct,
	}
	s, err := m.Repo.CreateTask(ctx, model)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// resetPredictionVolumeTaskDate Prediction Volume类型的Task日期是从上传的Prediction Volume决定而非前端传入（前端不可见）
// 因此这里需要读取该Task的Prediction Volume进行重置Task的Start End Date
func (m *ServiceImpl) resetPredictionVolumeTaskDate(ctx context.Context, t *persistent.ForecastingTaskTab) *srerr.Error {
	predictionVolumes, err := m.GetPredictionVolumeByTaskId(ctx, t.Id)
	if err != nil {
		return err
	}

	if len(predictionVolumes) == 0 {
		logger.CtxLogErrorf(ctx, "task's prediction volume not found | task id: %d", t.Id)
		return nil
	}

	sort.Slice(predictionVolumes, func(i, j int) bool {
		// 对于格式统一的日期来说(YYYY-MM-DD)，简单使用比较符来判断日期大小是可行的
		return predictionVolumes[i].ShipmentDate < predictionVolumes[j].ShipmentDate
	})

	// 上面做了长度判断，这里根据Index获取是安全的
	t.StartDate = predictionVolumes[0].ShipmentDate
	t.EndDate = predictionVolumes[len(predictionVolumes)-1].ShipmentDate

	return nil
}

func (m *ServiceImpl) UpdateTask(ctx context.Context, request *foreschema.UpdateTaskRequest) (*persistent.ForecastingTaskTab, *srerr.Error) {
	if err := request.Validate(); err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	// validate rule
	if err := m.validateRuleList(ctx, request.RuleList); err != nil {
		return nil, err
	}
	draft, err := m.Repo.GetEditAbleTaskById(ctx, request.Id)
	if err != nil {
		return nil, srerr.With(srerr.TaskNotAllowEdit, "TaskClient for draft status not found. ", err)
	}
	draft = request.Copy2TaskTab(draft)
	if draft.ShipmentResource == persistent.ShipmentResourceTypePredictionVolume {
		if err := m.resetPredictionVolumeTaskDate(ctx, draft); err != nil {
			return nil, err
		}
	}

	tmpRules, err := request.CopyRuleTab()
	if err != nil {
		return nil, err
	}

	var tmpRuleIdList []int
	var deleteTmpRuleIdList []int
	for _, tmp := range tmpRules {
		tmpRuleIdList = append(tmpRuleIdList, tmp.Id)
	}
	if err := checkUseVolumeV2(ctx, tmpRules, draft.VolumeRuleId, draft.RoutingType); err != nil {
		return nil, err
	}
	oldTmpRules, _ := m.Repo.GetForecastRulesByTaskId(ctx, request.Id)
	for _, tmp := range oldTmpRules {
		if !objutil.ContainInt(tmpRuleIdList, tmp.Id) {
			deleteTmpRuleIdList = append(deleteTmpRuleIdList, tmp.Id)
		}
	}
	if len(deleteTmpRuleIdList) > 0 {
		err = m.Repo.DeleteForecastRuleByIds(ctx, deleteTmpRuleIdList)
		if err != nil {
			return nil, err
		}
	}
	err = m.Repo.UpdateForecastRules(ctx, tmpRules)
	if err != nil {
		return nil, err
	}

	//这里先删除旧的volume rule
	if err := m.ZoneRuleRepo.DeleteForecastRuleByTaskId(ctx, draft.Id); err != nil {
		return nil, err
	}
	if err := m.BatchCreateVolume(ctx, request.VolumeRuleList, draft); err != nil {
		logger.CtxLogErrorf(ctx, "Batch create volume rule failed:%v", err)
		return nil, err
	}
	if draft.RoutingType == rule.SPXRoutingType || draft.RoutingType == rule.LocalRoutingType {
		if len(request.SimulationOrderCount) > 0 {
			bytes, umlErr := jsoniter.Marshal(&request.SimulationOrderCount)
			if umlErr != nil {
				return nil, srerr.With(srerr.JsonErr, draft, umlErr)
			}
			draft.SimulationOrderCountDetail = bytes
			//用户上传了单量则直接根据单量表加起来
			draft.OrderCount = 0
			for _, count := range request.SimulationOrderCount {
				draft.OrderCount += count.Count
			}
		} else {
			//用户没有上传每天预测单量excel查询订单范围内总数
			if !configutil.GetDataApiSwitchConf(ctx).QueryOrderCountSwitch { // 没有开启开关走老的api
				resp, dataErr := m.dataApi.QueryOrderCount(ctx, uint64(draft.ProductId), int(draft.RoutingType), draft.StartDate, draft.EndDate)
				if dataErr != nil {
					return nil, srerr.New(srerr.DataApiErr, request, "Query order count err %+v", dataErr)
				}
				calculateOrderCount(resp.Data.List, draft)
				if draft.OrderCount == 0 {
					return nil, srerr.New(srerr.DataApiErr, request, "Order count is zero %+v", resp)
				}
				draft.SimulationOrderCountDetail = nil
			} else {
				resp, dataErr := m.dataApi.OrderAggregationV2(ctx, dataclient.QueryOrderAggregationRequest{
					ProductId:   draft.ProductId,
					RoutingType: draft.RoutingType,
					StartDate:   draft.StartDate,
					EndDate:     draft.EndDate,
					Region:      envvar.GetCID(),
				})
				if dataErr != nil {
					return nil, srerr.New(srerr.DataApiErr, request, "Query order count err %v", dataErr)
				}
				calculateOrderCountV2(resp.Data.List, draft)
				if draft.OrderCount == 0 {
					return nil, srerr.New(srerr.DataApiErr, request, "Order count is zero %+v", resp)
				}
				draft.SimulationOrderCountDetail = nil
			}
		}
	}

	//SSCSMR-3053: call lps to copy shop group info
	if draft.TaskStatus == persistent.TaskStatusWaiting {
		if cErr := m.copyShopGroupInfo(ctx, tmpRules); cErr != nil {
			logger.CtxLogErrorf(ctx, "copy shop group info err:%v", cErr)
			return nil, cErr
		}
	}

	operateBy, _ := apiutil.GetUserInfo(ctx)
	draft.OperateBy = operateBy
	s, err := m.Repo.UpdateTask(ctx, draft)
	if err != nil {
		return nil, err
	}
	s.RuleList = tmpRules

	return s, err
}

func calculateOrderCount(resp []dataclient.QueryCountOrderDetailResp, task *persistent.ForecastingTaskTab) {
	task.OrderCount = 0
	for _, orderCount := range resp {
		task.OrderCount += orderCount.Count
	}
}

// 这个方法是专门提供给新的api的，如果上线后老的api没问题可以直接删除calculateOrderCount方法
func calculateOrderCountV2(resp []dataclient.AggregationDataV2, task *persistent.ForecastingTaskTab) {
	task.OrderCount = 0
	for _, orderCount := range resp {
		task.OrderCount += orderCount.Quantity
	}
}

func (m *ServiceImpl) ImportWeightRange(ctx context.Context, taskId int64, filePath string) ([]*persistent.WeightRange, *srerr.Error) {
	timeout := 6
	data, rErr := httputil.Get(ctx, filePath, nil, timeout, nil)
	if rErr != nil {
		return nil, srerr.New(srerr.ImportWeightRangeErr, nil, "import weight range fail, err:%s", rErr.Error())
	}
	rows, _, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(data), true)
	if fErr != nil {
		return nil, srerr.With(srerr.ImportWeightRangeErr, nil, fErr)
	}
	res := []*persistent.WeightRange{}
	for i := 0; i < len(rows); i++ {
		weightRange := &persistent.WeightRange{}
		// 所有的监控上报都在最外层,这里上传的Excel最多两列
		if len(rows[i]) != 2 { // 开发打印的日志是对应的index，返回给前端的是excel对应的真实行号所以要+1
			logger.CtxLogErrorf(ctx, "row_idx %d,row content %v,the number of columns should be 2", i, rows[i])
			return nil, srerr.New(srerr.ParamErr, i+1, "row_idx %d,row columns %d,the number of columns should be 2", i+1, len(rows[i]))
		}
		min, minErr := strconv.ParseFloat(rows[i][0], 32)
		if minErr != nil {
			return nil, srerr.With(srerr.WeightRangeErr, nil, minErr)
		}
		max, maxErr := strconv.ParseFloat(rows[i][1], 32)
		if maxErr != nil {
			return nil, srerr.With(srerr.WeightRangeErr, nil, maxErr)
		}

		weightRange.Min = int(min)
		weightRange.Max = int(max)

		res = append(res, weightRange)
	}

	marshal, mslErr := jsoniter.Marshal(res)
	if mslErr != nil {
		logger.CtxLogErrorf(ctx, "Import weight range marshal failed %+v", mslErr)
		return nil, srerr.With(srerr.JsonErr, nil, mslErr)
	}
	draft, err := m.Repo.GetTaskById(ctx, int(taskId))
	if err != nil {
		return nil, err
	}

	draft.WeightRangeDetail = marshal
	_, updateErr := m.Repo.UpdateTask(ctx, draft)
	if updateErr != nil {
		logger.CtxLogErrorf(ctx, "Import weight range save failed %+v", updateErr)
		return nil, srerr.With(srerr.JsonErr, nil, updateErr)
	}

	return res, nil
}

func (m *ServiceImpl) ImportOrderCount(ctx context.Context, taskId int64, filePath string) ([]*persistent.SimulationOrderCount, *srerr.Error) {
	timeout := 6
	data, rErr := httputil.Get(ctx, filePath, nil, timeout, nil)
	if rErr != nil {
		return nil, srerr.New(srerr.ImportOrderCountErr, nil, "import weight range fail, err:%s", rErr.Error())
	}
	rows, _, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(data), true)
	if fErr != nil {
		return nil, srerr.With(srerr.ImportOrderCountErr, nil, fErr)
	}
	if len(rows) > maxSimulationDays {
		return nil, srerr.With(srerr.ImportOrderCountErr, nil, errors.New("maxSimulationDays forecast 7 days"))
	}
	res := []*persistent.SimulationOrderCount{}
	for i := 0; i < len(rows); i++ {
		//模板下载删除后解析表格会出现空出字符串
		if len(rows[i]) != 2 || rows[i][0] == "" || rows[i][1] == "" {
			continue
		}
		orderCount := &persistent.SimulationOrderCount{}
		//excel解析出来可能会出现浮点数
		day, dayErr := strconv.ParseFloat(rows[i][0], 32)
		if dayErr != nil {
			return nil, srerr.With(srerr.OrderCountErr, nil, dayErr)
		}
		count, countErr := strconv.ParseFloat(rows[i][1], 32)
		if countErr != nil {
			return nil, srerr.With(srerr.OrderCountErr, nil, countErr)
		}

		orderCount.Day = int(day)
		orderCount.Count = int(count)

		res = append(res, orderCount)
	}
	marshal, jErr := jsoniter.Marshal(res)
	if jErr != nil {
		logger.CtxLogErrorf(ctx, "Import order count marshal failed %+v", jErr)
		return nil, srerr.With(srerr.JsonErr, nil, jErr)
	}
	task, taskErr := m.Repo.GetTaskById(ctx, int(taskId))
	if taskErr != nil {
		logger.CtxLogErrorf(ctx, "Find task failed %+v", taskErr)
		return nil, srerr.With(srerr.DatabaseErr, nil, taskErr)
	}
	task.SimulationOrderCountDetail = marshal

	_, updateErr := m.Repo.UpdateTask(ctx, task)
	if updateErr != nil {
		logger.CtxLogErrorf(ctx, "Import order count save failed %+v", updateErr)
		return nil, srerr.With(srerr.JsonErr, nil, updateErr)
	}

	return res, nil
}

func (m *ServiceImpl) DeleteVolumeRuleByTaskId(ctx context.Context, taskId int) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.ForecastVolumeRuleHook, map[string]interface{}{
		"task_id": taskId,
	}, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, taskId, err)
	}

	return nil
}

func (m *ServiceImpl) BatchCreateVolume(ctx context.Context, volumeList []*foreschema.VolumeRuleInfo, task *persistent.ForecastingTaskTab) *srerr.Error {
	operator, _ := apiutil.GetUserInfo(ctx)
	for _, volumeRule := range volumeList {
		newRule := persistent2.VolumeRoutingRuleTab{
			ProductId:          int64(task.ProductId),
			TaskId:             uint64(task.Id),
			RuleName:           volumeRule.RuleName,
			Priority:           int64(volumeRule.Priority),
			RuleType:           volumeRule.RuleType,
			EffectiveStartTime: timeutil.GetCurrentUnixTimeStamp(ctx),
			RuleStatus:         enum.VolumeRuleStatusExpired,
			Operator:           operator,
			Ctime:              timeutil.GetCurrentUnixTimeStamp(ctx),
			Mtime:              timeutil.GetCurrentUnixTimeStamp(ctx),
			IsForecastType:     constant.ForecastType,
			RoutingType:        int(task.RoutingType),
		}
		newRuleId, cerr := m.ZoneRuleRepo.CreateZoneRule(ctx, &newRule)
		if cerr != nil {
			return srerr.With(srerr.DatabaseErr, volumeList, cerr)
		}
		var volumeRuleDetails []persistent2.VolumeRoutingRuleDetailTab
		for _, limits := range volumeRule.LineLimit {
			for _, line := range limits {
				volumeRuleDetails = append(volumeRuleDetails, persistent2.VolumeRoutingRuleDetailTab{
					RuleId:         newRuleId,
					LineId:         line.LineId,
					LineType:       line.LineType,
					Min:            line.MinDailyLimit,
					Max:            line.MaxDailyLimit,
					MaxCod:         line.MaxCodDailyLimit,
					MaxBulky:       line.MaxBulkyDailyLimit,
					MaxHighValue:   line.MaxHighValueDailyLimit,
					MaxDg:          line.MaxDgDailyLimit,
					RoutingType:    int(task.RoutingType),
					IsForecastType: constant.ForecastType,
				})
			}
		}

		for _, zoneLimit := range volumeRule.ZoneLimit {
			volumeRuleDetails = append(volumeRuleDetails, persistent2.VolumeRoutingRuleDetailTab{
				RuleId:         newRuleId,
				LineId:         zoneLimit.LineId,
				ZoneName:       zoneLimit.ZoneName,
				GroupId:        zoneLimit.ZoneGroupId,
				LineType:       zoneLimit.LineType,
				Min:            zoneLimit.MinDailyLimit,
				Max:            zoneLimit.MaxDailyLimit,
				MaxCod:         zoneLimit.MaxCodDailyLimit,
				MaxBulky:       zoneLimit.MaxBulkyDailyLimit,
				MaxHighValue:   zoneLimit.MaxHighValueDailyLimit,
				MaxDg:          zoneLimit.MaxDgDailyLimit,
				RoutingType:    int(task.RoutingType),
				IsForecastType: constant.ForecastType,
			})
		}

		if err := m.ZoneRuleRepo.CreateZoneRuleDetails(ctx, volumeRuleDetails); err != nil {
			return srerr.With(srerr.DatabaseErr, volumeList, cerr)
		}
	}
	return nil
}
func (m *ServiceImpl) validateRuleList(ctx context.Context, ruleList []*foreschema.RuleInfo) *srerr.Error {
	for _, ruleInfo := range ruleList {
		ruleUpdateReq := soft_routing.UpdateRuleRequest{
			Priority:          int32(ruleInfo.Priority),
			RuleDetails:       ruleInfo.RuleDetail,
			DefaultCriteria:   ruleInfo.DefaultCriteria,
			DisabledInfo:      ruleInfo.DisabledInfo,
			WhsId:             ruleInfo.WhsId,
			ZoneCode:          ruleInfo.ZoneCode,
			ItemCategoryLevel: ruleInfo.ItemCategoryLevel,
			ItemCategoryID:    ruleInfo.ItemCategoryId,
			ParcelValueMax:    ruleInfo.ParcelValueMax,
			ParcelValueMin:    ruleInfo.ParcelValueMin,
			ParcelWeightMax:   ruleInfo.ParcelWeightMax,
			ParcelWeightMin:   ruleInfo.ParcelWeightMin,
			DgType:            ruleInfo.DgType,
			ParcelDimension:   ruleInfo.ParcelDimension,
		}
		if err := m.RuleRepo.ValidateUpdateRequest(ctx, &ruleUpdateReq); err != nil {
			return err
		}
	}
	return nil
}

func (m *ServiceImpl) GetTaskById(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	task, err := m.Repo.GetTaskById(ctx, Id)
	if err != nil {
		return nil, err
	}
	return task, err
}

func (m *ServiceImpl) GetTaskList(ctx context.Context, request *foreschema.GetTaskListRequest) (*foreschema.GetTaskListResponse, *srerr.Error) {
	pageNo, offset, limit := apiutil.GetOffsetAndLimit(request.PageNo, request.Limit)
	list, total, err := m.Repo.GetTaskList(ctx, request.TaskStatus, request.Id, request.ProductId, offset, limit, request.RoutingType, request.IsMultiProduct)
	if request.RoutingType == rule.LocalRoutingType || request.RoutingType == rule.SPXRoutingType {
		calculateTaskProcess(ctx, list)
	}
	resp := &foreschema.GetTaskListResponse{List: list, PageNo: pageNo, Count: limit, Total: total}
	return resp, err
}

func calculateTaskProcess(ctx context.Context, taskList []*persistent.ForecastingTaskTab) {
	for _, task := range taskList {
		if task.TaskStatus == persistent.TaskStatusDone {
			task.TaskProgress = float64(CompletedPercentage)
			continue
		}
		key := forecastedOrderCount(task.Id)
		orderCount, err := redisutil.GetInt64(ctx, key)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Get task process order count failed: %=+v", err)
			continue
		}
		if task.OrderCount != 0 {
			//保留两位小数
			task.TaskProgress = mathutil2.FloatWithFixDecimals(float64(orderCount)/float64(task.OrderCount)*100, 2)
		}
	}
}

func forecastedOrderCount(taskId int) string {
	return fmt.Sprintf("%s_%d", forecast.ForecastedOrderCount, taskId)
}

func (m *ServiceImpl) DeleteTaskById(ctx context.Context, Id int) *srerr.Error {
	_, err := m.Repo.GetDraftTaskById(ctx, Id)
	if err != nil {
		return srerr.New(srerr.TaskNotAllowDelete, nil, "Only the tasks of state draft can be deleted. ")
	}

	// maybe not have tmp rule
	_ = m.Repo.DeleteForecastRuleByTaskId(ctx, Id)
	err = m.Repo.DeleteTaskById(ctx, Id)
	return err
}

func (m *ServiceImpl) CopyTaskById(ctx context.Context, Id int) *srerr.Error {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	// draft 状态的规则不可复制
	task, err := m.Repo.GetTaskById(ctx, Id)
	if err != nil {
		return err
	}
	if task.TaskStatus == persistent.TaskStatusDraft {
		return srerr.New(srerr.TaskNotAllowCopy, nil, "Draft task_status task do not allow copy. ")
	}
	_, err = m.Repo.GetDraftTaskByProductId(ctx, task.ProductId, task.IsMultiProduct, task.RoutingType)
	if err == nil {
		return srerr.New(srerr.TaskExistDraft, nil, "Product Id: %d task exist draft. ", task.ProductId)
	}
	newTask := &persistent.ForecastingTaskTab{
		ProductId:        task.ProductId,
		TaskName:         fmt.Sprintf("%s: copy", task.TaskName),
		StartDate:        task.StartDate,
		EndDate:          task.EndDate,
		TaskStatus:       persistent.TaskStatusDraft,
		OperateBy:        operateBy,
		RoutingType:      task.RoutingType,
		IsMultiProduct:   task.IsMultiProduct,
		VolumeRuleId:     task.VolumeRuleId,
		ShipmentResource: task.ShipmentResource,
	}

	newTask, err = m.Repo.CreateTask(ctx, newTask)
	if err != nil {
		return err
	}

	tmpRules, err := m.Repo.GetForecastRulesByTaskId(ctx, Id)
	if err != nil {
		return err
	}

	for _, tmpRule := range tmpRules {
		tmpRule.TaskId = newTask.Id
		tmpRule.Id = 0
	}
	if err = m.Repo.InsertForecastRules(ctx, tmpRules); err != nil {
		return err
	}

	if task.ShipmentResource == persistent.ShipmentResourceTypePredictionVolume {
		predictionVolumes, err := m.Repo.GetPredictionVolumesByCondition(ctx, map[string]interface{}{"task_id = ?": Id})
		if err != nil {
			return err
		}
		for _, p := range predictionVolumes {
			p.Id = 0
			p.TaskId = newTask.Id
		}
		if err := m.Repo.BatchCreatePredictionVolumes(ctx, predictionVolumes); err != nil {
			return err
		}
	}

	if task.RoutingType == rule.SPXRoutingType || task.RoutingType == rule.LocalRoutingType {
		ruleList, err := m.ZoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
			"task_id = ?": Id,
		})
		if err != nil {
			return err
		}
		// 获取对应的Forecast volume rule
		for _, volumeRule := range ruleList {
			// 获取对应的rule detail
			ruleDetails, err := m.ZoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{
				"rule_id = ?": volumeRule.Id,
			})
			if err != nil {
				return err
			}
			volumeRule.Id = 0
			volumeRule.DataVersion = 0
			volumeRule.TaskId = uint64(newTask.Id)
			ruleId, rerr := m.ZoneRuleRepo.CreateZoneRule(ctx, &volumeRule)
			if rerr != nil {
				return rerr
			}
			for i := 0; i < len(ruleDetails); i++ {
				ruleDetails[i].Id = 0
				ruleDetails[i].RuleId = ruleId
			}
			if err := m.ZoneRuleRepo.CreateZoneRuleDetails(ctx, ruleDetails); err != nil {
				return err
			}
		}
	}

	return nil
}

func (m *ServiceImpl) getTaskBlockRate(ctx context.Context, taskId int) (int, *srerr.Error) {
	results, err := m.Repo.GetResultByTaskId(ctx, taskId)
	if err != nil {
		return 0, err
	}

	var total, block int
	for _, result := range results {
		if result.LaneCode == forecast.AllBlocked {
			continue
		}
		total += result.Quantity
	}

	// 可能会存在不同日期的Block，要把所有日期的Block都加起来
	for _, result := range results {
		if total > 0 && result.LaneCode == forecast.AllBlocked {
			block += result.Quantity
		}
	}
	totalPercent := int(math.Round(float64(block) / float64(total) * 100))

	return totalPercent, nil
}

func (m *ServiceImpl) DeployTaskById(ctx context.Context, request *foreschema.DeployTaskRequest) (*foreschema.TaskDeployResponse, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	if err := request.Validate(ctx); err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	// 只有Done 状态可deploy
	task, err := m.Repo.GetTaskById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if task.TaskStatus != persistent.TaskStatusDone {
		return nil, srerr.New(srerr.TaskNotAllowDeploy, nil, "Only the tasks of state done can be deploy. ")
	}

	// 检查是否该Product下是否有相同生效时间的待生效Rule
	totalPercent, err := m.getTaskBlockRate(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	blockPercent := forecast.DefaultBlockPercent
	if !request.IsForce && totalPercent >= blockPercent {
		return nil, srerr.New(srerr.TaskNotAllowDeploy, nil, "Task block %v, can not deploy to live, must less than %v. ", totalPercent, blockPercent)
	}
	effectiveStartTime := request.EffectiveStartTime

	if request.EffectiveImmediately {
		effectiveStartTime = timeutil.GetCurrentUnixTimeStamp(ctx)
	}

	if err := m.deployForecastRules(ctx, task, effectiveStartTime, operateBy); err != nil {
		return nil, err
	}

	if totalPercent >= blockPercent {
		return &foreschema.TaskDeployResponse{
			Msg: fmt.Sprintf("There are %v%% orders blocked, please be careful. ", totalPercent),
		}, nil
	}

	return nil, nil
}

func (m *ServiceImpl) deployForecastRules(ctx context.Context, task *persistent.ForecastingTaskTab, effectiveStartTime int64, operateBy string) *srerr.Error {
	forecastRules, err := m.Repo.GetForecastRulesByTaskId(ctx, task.Id)
	if err != nil {
		return err
	}

	db, derr := dbutil.MasterDB(ctx, persistent.ForecastingTaskHook)
	if derr != nil {
		return srerr.With(srerr.DatabaseErr, task.Id, derr)
	}

	ctx = scormv2.BindContext(ctx, db)
	if err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		volumeRuleId, err := m.getOrCloneVolumeRule(ctx, task.VolumeRuleId, effectiveStartTime)
		if err != nil {
			return err
		}
		for _, forecastRule := range forecastRules {
			newRule := copyForecastRule(task, uint32(effectiveStartTime), operateBy, forecastRule)
			newRule.VolumeRuleId = int64(volumeRuleId)
			_, cErr := m.RuleRepo.CreateProductRuleWithTx(ctx, tx, newRule)
			if cErr != nil {
				return cErr
			}
		}

		return nil
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

// StartTask 只有waiting和failed 状态的task 可以开始执行
// 1. 修改数据库任务状态
// 2. 往 Kafka 发送一条消息
func (m *ServiceImpl) StartTask(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	task, err := m.Repo.GetTaskById(ctx, Id)
	if err != nil {
		return nil, err
	}
	if !(task.TaskStatus == persistent.TaskStatusWaiting || task.TaskStatus == persistent.TaskStatusFailed) {
		return nil, srerr.New(srerr.TaskNotAllowStart, nil, "Only the tasks of state waiting/failed can be start. ")
	}
	task.TaskStatus = persistent.TaskStatusProcessing
	task.OperateBy = operateBy
	task, err = m.Repo.UpdateTask(ctx, task)
	if err != nil {
		return nil, err
	}
	err = deliveryTask2Kafka(ctx, task)
	if err != nil {
		return task, nil
	}
	return task, nil
}

func (m *ServiceImpl) StopTask(ctx context.Context, Id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	task, err := m.Repo.GetTaskById(ctx, Id)
	if err != nil {
		return nil, err
	}
	if task.TaskStatus != persistent.TaskStatusProcessing {
		return nil, srerr.New(srerr.TaskNotAllowStop, nil, "Only the asks of state processing can be stop. ")
	}
	task.TaskStatus = persistent.TaskStatusStop
	task.OperateBy = operateBy
	task, err = m.Repo.UpdateTask(ctx, task)
	if err != nil {
		return nil, err
	}
	redisErr := redisutil.Set(ctx, task.StatusKey(), int(task.TaskStatus), time.Duration(86400)*time.Second)
	if redisErr != nil {
		logger.CtxLogErrorf(ctx, "ServiceImpl StopTask|Set redis error: %s, key: %s, val: %d", err.Error(), task.StatusKey(), task.TaskStatus)
	}
	return task, nil
}

func (m *ServiceImpl) TaskResult(ctx context.Context, request *foreschema.TaskResultRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error) {
	task, err := m.Repo.GetTaskById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if task.TaskStatus == persistent.TaskStatusFailed {
		taskError, err := m.Repo.GetTaskError(ctx, request.Id)
		if err != nil {
			return nil, err
		}
		return &foreschema.ShipmentSummaryResponse{Error: taskError}, nil
	}
	results, err := m.Repo.GetResultByTaskId(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	if task.IsMultiProduct {
		groupBy := GroupByInfo{
			GroupByIlhGroup:    request.GroupByIlhGroup,
			GroupByTp:          request.GroupByTp,
			GroupByServiceCode: request.GroupByServiceCode,
		}
		return m.MultiProductTaskResult(ctx, task.ProductId, results, groupBy)
	}

	laneCodeList := make([]string, 0, len(results))
	var total int
	for _, result := range results {
		if result.LaneCode == forecast.AllBlocked {
			result.ServiceCode = forecast.AllBlocked
			continue
		}
		total += result.Quantity
		laneCodeList = append(laneCodeList, result.LaneCode)
	}

	logger.CtxLogInfof(ctx, "TaskResult lane_code list: %v", laneCodeList)

	var lanes []foreschema.ShipmentSummary
	for _, result := range results {
		resource := m.GetResource(ctx, result.LaneCode)
		var (
			count      = result.Quantity
			percentage = 0
		)
		if total > 0 {
			percentage = int(math.Round(float64(count) / float64(total) * 100))
		}
		laneResult := foreschema.ShipmentSummary{
			LaneCode:                   result.LaneCode,
			DgFlag:                     int32(result.DGFlag),
			FlId:                       resource.FlId,
			FlName:                     resource.FlName,
			LmId:                       resource.LmId,
			LmName:                     resource.LmName,
			Tws:                        resource.TwsId + " - " + resource.TwsName,
			Joint:                      resource.JointId + " - " + resource.JointName,
			Quantity:                   count,
			Percentage:                 percentage,
			ServiceCode:                result.ServiceCode,
			RuleId:                     result.RuleId,
			ShippingFeePerOrder:        objutil.RoundFloat64(result.AsfInfo.AverageAsf, 4),
			ShippingFeePerOrderUsd:     objutil.RoundFloat64(result.AsfInfo.AverageAsfUsd, 4),
			MissingShippingFeeQuantity: result.AsfInfo.MissingAsfQty,
		}
		if result.LaneCode == forecast.AllBlocked || result.LaneCode == forecast.Blocked || result.LaneCode == forecast.RuleBlocked {
			laneResult.ServiceCode = result.LaneCode
		}
		if result.RuleId > 0 {
			tmpRule, _ := m.Repo.GetForecastRuleById(ctx, result.RuleId)
			if tmpRule != nil {
				laneResult.RuleName = tmpRule.RuleName
				laneResult.RulePriority = tmpRule.Priority
			}
		}
		lanes = append(lanes, laneResult)
	}

	lanes = summaryByServiceCode(lanes)

	return &foreschema.ShipmentSummaryResponse{
		List: lanes,
	}, nil
}

func (m *ServiceImpl) IlhTaskResult(ctx context.Context, request *foreschema.TaskResultRequest, businessType uint8) (*foreschema.IlhShipmentSummaryResponse, *srerr.Error) {
	task, err := m.Repo.GetTaskById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if task.TaskStatus == persistent.TaskStatusFailed {
		taskError, err := m.Repo.GetTaskError(ctx, request.Id)
		if err != nil {
			return nil, err
		}
		return &foreschema.IlhShipmentSummaryResponse{Error: taskError}, nil
	}

	results, err := m.Repo.GetResultByTaskId(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	// ilh的预测结果是按天保存的，导出时需要按天展示，ops需要合并展示
	if businessType == ILHOpsResult {
		var mergeResults []persistent.ForecastingTaskResultTab
		mergeResultMap := make(map[string]*persistent.ForecastingTaskResultTab)
		for _, result := range results {
			key := fmt.Sprintf("%v:%v:%v:%v:%v:%v:%v:%v", result.RuleId, result.Priority, result.LaneCode, result.ServiceCode, result.DGFlag, result.Site, result.BuyerCity, result.ActualPoint)
			if _, ok := mergeResultMap[key]; !ok {
				tempResult := &persistent.ForecastingTaskResultTab{
					Id:                result.Id,
					TaskId:            result.TaskId,
					Priority:          result.Priority,
					RuleId:            result.RuleId,
					Quantity:          result.Quantity,
					IlhParcelQuantity: result.IlhParcelQuantity,
					Weight:            result.Weight,
					LaneCode:          result.LaneCode,
					DGFlag:            result.DGFlag,
					ServiceCode:       result.ServiceCode,
					Site:              result.Site,
					BuyerCity:         result.BuyerCity,
					Day:               result.Day,
					ShippingFee:       result.ShippingFee,
					WeightRange:       result.WeightRange,
					WeightRangeDetail: result.WeightRangeDetail,
					ActualPoint:       result.ActualPoint,
					CTime:             result.CTime,
					StatDate:          "",
				}
				mergeResultMap[key] = tempResult
			} else {
				mergeResultMap[key].Quantity += result.Quantity
				mergeResultMap[key].Weight += result.Weight
				mergeResultMap[key].IlhParcelQuantity += result.IlhParcelQuantity
			}
		}
		for _, mergeResult := range mergeResultMap {
			mergeResults = append(mergeResults, *mergeResult)
		}
		// 将合并后的值赋给数据库查询结果
		results = mergeResults
	}

	orderTotalMap := make(map[string]int)
	weightTotalMap := make(map[string]int)
	parcelTotalMap := make(map[string]int)
	for _, result := range results {
		// 之所以这里continue掉，原因是allBlocked是额外新增的一行，统计的是所有block的订单，是统计数据，不是预测任务跑出的结果
		if result.LaneCode == forecast.AllBlocked {
			result.ServiceCode = forecast.AllBlocked
			continue
		}
		//total unblock carton quantity and weight quantity
		orderTotalMap[result.StatDate] += result.Quantity
		weightTotalMap[result.StatDate] += result.Weight
		parcelTotalMap[result.StatDate] += result.IlhParcelQuantity
	}

	var shipmentSummaryList []*foreschema.IlhShipmentSummary
	for _, result := range results {
		var (
			orderTotal       = 0
			weightTotal      = 0
			parcelTotal      = 0
			orderPercentage  = 0
			weightPercentage = 0
			parcelPercentage = 0
		)
		// 取出每一天的总数
		if _, ok := orderTotalMap[result.StatDate]; ok {
			orderTotal = orderTotalMap[result.StatDate]
		}
		if _, ok := weightTotalMap[result.StatDate]; ok {
			weightTotal = weightTotalMap[result.StatDate]
		}
		if _, ok := parcelTotalMap[result.StatDate]; ok {
			parcelTotal = parcelTotalMap[result.StatDate]
		}
		if orderTotal > 0 {
			orderPercentage = int(math.Round(float64(result.Quantity) / float64(orderTotal) * 100))
		}
		if weightTotal > 0 {
			weightPercentage = int(math.Round(float64(result.Weight) / float64(weightTotal) * 100))
		}
		if parcelTotal > 0 {
			parcelPercentage = int(math.Round(float64(result.IlhParcelQuantity) / float64(parcelTotal) * 100))
		}

		tp := "None"
		if result.DGFlag == 1 {
			tp = "Non-DG"
		} else if result.DGFlag == 2 {
			tp = "DG"
		}

		var flInfo, importIlhInfo string
		laneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, result.LaneCode)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail|err=%v", err)
		} else {
			// ilh info
			ilhLine := laneInfo.GetCILHLineInfo()
			flInfo = fmt.Sprintf("%s-%s", ilhLine.LineID, ilhLine.LineName)

			// import ilh(cc) info
			importIlhLine := laneInfo.GetImportILHLineInfo()
			importIlhInfo = fmt.Sprintf("%s-%s", importIlhLine.LineID, importIlhLine.LineName)
		}

		shipmentSummary := &foreschema.IlhShipmentSummary{
			TP:                       tp,
			FlInfo:                   flInfo,
			ImportIlhInfo:            importIlhInfo,
			ServiceCode:              result.ServiceCode,
			CartonQuantity:           result.Quantity,
			QuantityPercentage:       orderPercentage,
			WeightQuantity:           float64(result.Weight) / 1000, // G(DB) to KG
			WeightPercent:            weightPercentage,
			ParcelQuantity:           result.IlhParcelQuantity,
			ParcelQuantityPercentage: parcelPercentage,
			StatDate:                 result.StatDate,
		}
		if result.LaneCode == forecast.AllBlocked || result.LaneCode == forecast.Blocked || result.LaneCode == forecast.RuleBlocked {
			shipmentSummary.ServiceCode = result.LaneCode
		}
		if result.RuleId > 0 {
			tmpRule, err := m.Repo.GetForecastRuleById(ctx, result.RuleId)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetForecastRuleById fail|rule_id=%d", result.RuleId)
			} else {
				shipmentSummary.RuleName = tmpRule.RuleName
				shipmentSummary.RulePriority = tmpRule.Priority
				shipmentSummary.RuleId = tmpRule.Id
			}
		}
		shipmentSummaryList = append(shipmentSummaryList, shipmentSummary)
	}

	return &foreschema.IlhShipmentSummaryResponse{List: shipmentSummaryList}, nil
}

func (m *ServiceImpl) GetShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error) {
	valErr := request.Validate()
	if valErr != nil {
		return nil, srerr.With(srerr.ParamErr, nil, valErr)
	}

	days := getDaysList(request.Start, request.End)

	volumes, total, err := m.VolumeCounterSrv.GetProductLaneVolumes(ctx, request.ProductId, days)
	if err != nil {
		return nil, err
	}

	var shipmentSummaryList []foreschema.ShipmentSummary
	for laneCounter, orderQuantity := range volumes {
		resource := m.GetResource(ctx, laneCounter.LaneCode)
		laneCode := laneCounter.LaneCode
		dgFlag := laneCounter.DgFlag
		serviceCode := laneCounter.ServiceCode
		if orderQuantity == 0 {
			continue
		}

		percentage := 0
		if total > 0 {
			percentage = int(math.Round(float64(orderQuantity) / float64(total) * 100))
		}
		s := foreschema.ShipmentSummary{
			LaneCode:    laneCode,
			DgFlag:      int32(dgFlag),
			ServiceCode: serviceCode,
			Tws:         resource.TwsId + " - " + resource.TwsName,
			Joint:       resource.JointId + " - " + resource.JointName,
			FlName:      resource.FlName,
			LmName:      resource.LmName,
			FlId:        resource.FlId,
			LmId:        resource.LmId,
			Quantity:    orderQuantity,
			Percentage:  percentage,
		}
		shipmentSummaryList = append(shipmentSummaryList, s)
	}

	shipmentSummaryList = summaryByServiceCode(shipmentSummaryList)

	return &foreschema.ShipmentSummaryResponse{List: shipmentSummaryList}, nil
}

func (m *ServiceImpl) GetMultiProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.ShipmentSummaryResponse, *srerr.Error) {
	valErr := request.Validate()
	if valErr != nil {
		return nil, srerr.With(srerr.ParamErr, nil, valErr)
	}

	days := getDaysList(request.Start, request.End)
	volumes, total, err := m.VolumeCounterSrv.GetMultiProductServiceCodeVolumes(ctx, request.ProductId, days)
	if err != nil {
		return nil, err
	}

	serviceCodeList, err := m.lfsCli.ListSortingCode(ctx, &lfsentity.ListSortingCodeRequest{ProductId: strconv.Itoa(request.ProductId)})
	if err != nil {
		return nil, srerr.With(srerr.LfsError, nil, err)
	}

	serviceCodeMap := make(map[string]*lfsentity.SortingCodeInfo)
	for _, serviceCodeInfo := range serviceCodeList.Data.List {
		serviceCodeMap[serviceCodeInfo.ServiceCode] = serviceCodeInfo
	}

	dgGroupMap, err := m.RuleRepo.GetMultiProductDgGroup(ctx, request.ProductId)
	if err != nil {
		return nil, err
	}

	lineNameMap, _ := m.laneSrv.GetLineIdToLineNameMap(ctx)

	var shipmentSummaryList []foreschema.ShipmentSummary
	for serviceCode, orderQuantity := range volumes {
		if orderQuantity == 0 {
			continue
		}
		serviceCodeInfo, exist := serviceCodeMap[serviceCode]
		if !exist {
			continue
		}

		dgGroupInfo, exist := dgGroupMap[serviceCodeInfo.DgGroup]
		if !exist {
			continue
		}

		percentage := 0
		if total > 0 {
			percentage = int(math.Round(float64(orderQuantity) / float64(total) * 100))
		}
		s := foreschema.ShipmentSummary{
			DgFlag:      serviceCodeInfo.DgFlag,
			ServiceCode: serviceCodeInfo.ServiceCode,
			DgGroupInfo: foreschema.DgGroupInfo{DgGroupId: dgGroupInfo.DgGroupId, DgGroupName: dgGroupInfo.DgGroupName},
			LmId:        serviceCodeInfo.LmId,
			LmName:      lineNameMap[serviceCodeInfo.LmId],
			Quantity:    orderQuantity,
			Percentage:  percentage,
		}
		for _, lineId := range dgGroupInfo.LineList {
			s.DgGroupInfo.LineList = append(s.DgGroupInfo.LineList,
				foreschema.LineInfo{LineId: lineId, LineName: lineNameMap[lineId]})
		}
		shipmentSummaryList = append(shipmentSummaryList, s)
	}

	return &foreschema.ShipmentSummaryResponse{List: shipmentSummaryList}, nil
}

// GetIlhProductShipmentSummary :get shipment summary for forecasting about ilh product
func (m *ServiceImpl) GetIlhProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*foreschema.IlhShipmentSummaryResponse, *srerr.Error) {
	var shipmentSummaryList []*foreschema.IlhShipmentSummary

	volumes, orderTotal, weightTotal, parcelTotal, err := m.VolumeCounterSrv.GetILHProductCounter(ctx, request.ProductId, getDaysList(request.Start, request.End))
	if err != nil {
		return nil, err
	}

	for ilhCounter, ilhCounterResult := range volumes {
		//get line routing type
		laneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, ilhCounter.LaneCode)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetLaneInfoByLaneCode fail | err=%v", err)
			continue
		}

		if ilhCounterResult.CartonOrderCount == 0 {
			continue
		}
		var (
			orderPercentage  = 0
			weightPercentage = 0
			parcelPercentage = 0
		)
		if orderTotal > 0 {
			orderPercentage = int(math.Round(float64(ilhCounterResult.CartonOrderCount) / float64(orderTotal) * 100))
		}
		if weightTotal > 0 {
			weightPercentage = int(math.Round(float64(ilhCounterResult.Weight) / float64(weightTotal) * 100))
		}
		if parcelTotal > 0 {
			parcelPercentage = int(math.Round(float64(ilhCounterResult.ParcelOrderCount) / float64(parcelTotal) * 100))
		}

		tp := "None"
		if ilhCounter.DgFlag == 1 {
			tp = "Non-DG"
		} else if ilhCounter.DgFlag == 2 {
			tp = "DG"
		}
		ilh := laneInfo.GetCILHLineInfo()
		importIlh := laneInfo.GetImportILHLineInfo()
		shipmentSummaryList = append(shipmentSummaryList, &foreschema.IlhShipmentSummary{
			TP:                       tp,
			FlInfo:                   fmt.Sprintf("%s-%s", ilh.LineID, ilh.LineName),
			ImportIlhInfo:            fmt.Sprintf("%s-%s", importIlh.LineID, importIlh.LineName),
			ServiceCode:              ilhCounter.ServiceCode,
			CartonQuantity:           ilhCounterResult.CartonOrderCount,
			QuantityPercentage:       orderPercentage,
			WeightQuantity:           float64(ilhCounterResult.Weight) / 1000, // G to KG
			WeightPercent:            weightPercentage,
			ParcelQuantity:           ilhCounterResult.ParcelOrderCount,
			ParcelQuantityPercentage: parcelPercentage,
		})
	}

	return &foreschema.IlhShipmentSummaryResponse{List: shipmentSummaryList}, nil
}

func (m *ServiceImpl) ExportShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error) {
	summaryResp, err := m.GetShipmentSummary(ctx, request)
	if err != nil {
		return nil, err
	}
	file := excelize.NewFile()
	sheetName := file.GetSheetName(0)
	_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{"FL", "LM", "TP", "Service Code", "Shipment Quantity", "Percentage"})
	shipmentSummaryList := summaryByServiceCode(summaryResp.List)
	for rowN, item := range shipmentSummaryList {
		line := rowN + 1
		var tpDisplay string
		if item.DgFlag == 1 {
			tpDisplay = "Non-DG"
		} else if item.DgFlag == 2 {
			tpDisplay = "DG"
		}
		percentDisplay := fmt.Sprintf("%d%%", item.Percentage)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), item.FlId+item.FlName)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), item.LmId+item.LmName)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), tpDisplay)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.ServiceCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), item.Quantity)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), percentDisplay)
	}

	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, sheetName, fErr)
	}

	return buffer, nil
}

func (m *ServiceImpl) ExportMultiProductShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error) {
	summaryResp, err := m.GetMultiProductShipmentSummary(ctx, request)
	if err != nil {
		return nil, err
	}
	file := excelize.NewFile()
	sheetName := file.GetSheetName(0)
	_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{"ILH Group", "LM", "TP", "Service Code", "Shipment Quantity", "Percentage"})
	for rowN, item := range summaryResp.List {
		line := rowN + 1
		var tpDisplay string
		if item.DgFlag == 1 {
			tpDisplay = "Non-DG"
		} else if item.DgFlag == 2 {
			tpDisplay = "DG"
		}
		percentDisplay := fmt.Sprintf("%d%%", item.Percentage)

		ilhDisplay := fmt.Sprintf("%s(", item.DgGroupInfo.DgGroupId)
		for _, lineInfo := range item.DgGroupInfo.LineList {
			ilhDisplay += fmt.Sprintf("%s-%s", lineInfo.LineId, lineInfo.LineName)
		}
		ilhDisplay += ")"

		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), ilhDisplay)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), item.LmId+item.LmName)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), tpDisplay)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.ServiceCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), item.Quantity)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), percentDisplay)
	}

	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, sheetName, err)
	}

	return buffer, nil
}

func (m *ServiceImpl) ExportIlhShipmentSummary(ctx context.Context, request *foreschema.ShipmentSummaryRequest) (*bytes.Buffer, *srerr.Error) {
	summaryResp, err := m.GetIlhProductShipmentSummary(ctx, request)
	if err != nil {
		return nil, err
	}
	file := excelize.NewFile()
	sheetName := file.GetSheetName(0)
	_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{
		"FL",
		"Import ILH",
		"TP",
		"Service Code",
		"Carton Quantity",
		"Carton Percentage",
		"Weight Quantity(KG)",
		"Weight Percentage",
		"Parcel Quantity",
		"Parcel Percentage"},
	)
	file.SetActiveSheet(0)
	for rowN, item := range summaryResp.List {
		if item == nil {
			logger.CtxLogErrorf(ctx, "summary result is empty, row:%d", rowN)
			continue
		}
		logger.CtxLogInfof(ctx, "ExportIlhShipmentSummary: row:%v, item:%v", rowN, *item)
		line := rowN + 1

		cartonQuantityStr := fmt.Sprintf("%v", item.CartonQuantity)
		cartonPercentStr := fmt.Sprintf("%d%%", item.QuantityPercentage)

		weightQuantityStr := fmt.Sprintf("%v", item.WeightQuantity)
		weightPercentStr := fmt.Sprintf("%d%%", item.WeightPercent)

		parcelQuantityStr := fmt.Sprintf("%v", item.ParcelQuantity)
		parcelPercentStr := fmt.Sprintf("%d%%", item.ParcelQuantityPercentage)

		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), item.FlInfo)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), item.ImportIlhInfo)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), item.TP)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.ServiceCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), cartonQuantityStr)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), cartonPercentStr)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 6), weightQuantityStr)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 7), weightPercentStr)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 8), parcelQuantityStr)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 9), parcelPercentStr)
	}

	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, sheetName, err)
	}

	return buffer, nil
}

func (m *ServiceImpl) CheckTaskZoneValid(ctx context.Context, request *foreschema.CheckTaskZoneValidRequest) (*foreschema.CheckTaskZoneValidResponse, *srerr.Error) {
	task, err := m.GetTaskById(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	resp := &foreschema.CheckTaskZoneValidResponse{}
	zoneCodesDB, err := m.zoneRepo.GetZoneCodeList(ctx, task.ProductId, request.RoutingType, locationzone.ForecastingZoneType)
	zoneCodesMap := make(map[string]struct{})
	for _, zoneCode := range zoneCodesDB {
		zoneCodesMap[zoneCode] = struct{}{}
	}
	if err != nil {
		return nil, err
	}
	rules := task.RuleList
	msg := "The Zone %s in rule %s has been deleted in Forecasting Zone Setting, please create a new task or new zone %s"
	for _, ruleInfo := range rules {
		for _, zoneCode := range ruleInfo.ZoneCodeList {
			if _, exist := zoneCodesMap[zoneCode]; !exist {
				resp.RetCode = -1
				resp.RuleName = ruleInfo.RuleName
				resp.ZoneCode = zoneCode
				resp.Msg = fmt.Sprintf(msg, zoneCode, ruleInfo.RuleName, zoneCode)
			}
		}
	}

	if request.Scene == enum.DeployForecast && request.RoutingType == rule.CBRoutingType && volumeutil.IsOpenVolumeRouting(ctx) {
		volumeRuleId := task.VolumeRuleId
		if err := m.zoneMgr.CheckCanDeployForecastRule(ctx, volumeRuleId); err != nil {
			return &foreschema.CheckTaskZoneValidResponse{
				RetCode:  -1,
				RuleName: fmt.Sprintf("%v", err),
			}, nil
		}
	}
	return resp, nil
}

func (m *ServiceImpl) ExportForecastResult(ctx context.Context, request *foreschema.TaskResultRequest) (*bytes.Buffer, *srerr.Error) {
	taskResult, err := m.TaskResult(ctx, request)
	if err != nil {
		return nil, err
	}

	task, err := m.Repo.GetTaskById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if task.RoutingType == rule.IlhRoutingType {
		ilhTaskResult, iErr := m.IlhTaskResult(ctx, request, ILHExportResult)
		if iErr != nil {
			return nil, iErr
		}
		return m.exportIlhForecastResult(ctx, ilhTaskResult)
	} else if task.IsMultiProduct {
		return m.exportMultiProductForecastResult(ctx, taskResult)
	}

	sheetMap := make(map[int][]foreschema.ShipmentSummary)
	for _, item := range taskResult.List {
		if item.RuleId == 0 {
			continue
		}
		sheetMap[item.RulePriority] = append(sheetMap[item.RulePriority], item)
	}
	priorityList := make([]int, 0, len(sheetMap))
	for priority := range sheetMap {
		priorityList = append(priorityList, priority)
	}
	sort.Ints(priorityList)
	file := excelize.NewFile()
	for _, priority := range priorityList {
		summary := sheetMap[priority]
		sheetName := fmt.Sprintf("%v-%v-%v", summary[0].RuleId, summary[0].RuleName, summary[0].RulePriority) //加上rule id唯一标识，防止sheet页被覆盖
		index := file.NewSheet(sheetName)
		_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{"FL", "LM", "TP", "Service Code", "Shipment Quantity", "Percentage"})
		for rowN, item := range summary {
			line := rowN + 1
			var tpDisplay string
			if item.DgFlag == 1 {
				tpDisplay = "Non-DG"
			} else if item.DgFlag == 2 {
				tpDisplay = "DG"
			}
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), fmt.Sprintf("%v - %v", item.FlId, item.FlName))
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), fmt.Sprintf("%v - %v", item.LmId, item.LmName))
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), tpDisplay)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), item.ServiceCode)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), item.Quantity)
			_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), fmt.Sprintf("%d%%", item.Percentage))
		}
		file.SetActiveSheet(index)
	}
	file.DeleteSheet(file.GetSheetName(0))
	buffer, fErr := file.WriteToBuffer()
	if fErr != nil {
		return nil, srerr.With(srerr.SaveExcelErr, nil, fErr)
	}

	return buffer, nil
}

func (m *ServiceImpl) QueryOrderAggregation(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.ForecastRuleInfo, *srerr.Error) {
	ctx = forecast.SetDisplayOnOpsFlag(ctx)
	response, err := m.AggregationOrderData(ctx, request)
	if err != nil {
		return nil, err
	}

	ruleList, _ := m.Repo.GetForecastRulesByTaskId(ctx, request.TaskId)
	return m.composeResponseData(ruleList, response, request.IsResult), nil
}

func (m *ServiceImpl) AggregationOrderData(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error) {
	// 根据id查询对应的预测任务
	task, err := m.Repo.GetTaskById(ctx, request.TaskId)
	if err != nil {
		return nil, err
	}

	// 这里IsResult字段表示是否是结果聚合，如果是历史订单聚合则调用data接口，然后返回，否则查数据库做聚合返回返回
	if !request.IsResult {
		if configutil.ForecastSwitch() {
			return m.HistoricalOrderAggregationV2(ctx, task, request)
		} else {
			return m.HistoricalOrderAggregation(ctx, task, request)
		}
	} else {
		return m.ForecastResultAggregation(ctx, task, request)
	}
}

func (m *ServiceImpl) ForecastResultAggregation(ctx context.Context, task *persistent.ForecastingTaskTab, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error) {
	// 先判断任务状态是否为failed，如果失败就表示没有预测结果聚合
	if task.TaskStatus == persistent.TaskStatusFailed {
		taskError, err := m.Repo.GetTaskError(ctx, request.TaskId)
		if err != nil {
			return nil, err
		}
		return nil, srerr.New(srerr.ForecastFailed, nil, "forecast failed %+v", taskError)
	}
	// 根据预测任务id查询对应的预测结果
	resultList, resultErr := m.Repo.GetResultByTaskId(ctx, request.TaskId)
	if resultErr != nil {
		logger.CtxLogErrorf(ctx, "Get task result failed:%+v", resultErr)
		return nil, resultErr
	}
	for i := 0; i < len(resultList); i++ {
		if err := jsoniter.Unmarshal(resultList[i].WeightRangeDetail, &resultList[i].WeightRange); err != nil {
			logger.CtxLogErrorf(ctx, "get result weight age failed %+v")
		}
	}

	return m.OrderAggregation(ctx, request, resultList, task, nil)
}

// 历史订单聚合是调用data的接口聚合
func (m *ServiceImpl) HistoricalOrderAggregation(ctx context.Context, task *persistent.ForecastingTaskTab, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error) {
	// 获取重量范围
	weightRanges := unmarshalWeightRange(ctx, task, request)
	if !configutil.GetDataApiSwitchConf(ctx).OrderAggregationSwitch {
		// 调用data拿历史订单结果
		aggregation, aggErr := m.dataApi.OrderAggregation(ctx, dataclient.QueryOrderAggregationRequest{
			ProductId:   task.ProductId,
			StartDate:   request.StartDate,
			EndDate:     request.EndDate,
			RoutingType: task.RoutingType,
			Region:      envvar.GetCID(),
			WeightRange: weightRanges,
		})
		if aggErr != nil {
			logger.CtxLogErrorf(ctx, "AggregationOrderData failed: %+v", aggErr)
			return nil, aggErr
		}
		// 将历史结果转换成forecastingTaskResultTab结构体
		resultList := m.convertToResultTable(ctx, aggregation.Data.List)
		return m.OrderAggregation(ctx, request, resultList, task, nil)
	} else {
		aggregation, aggErr := m.dataApi.OrderAggregationV2(ctx, dataclient.QueryOrderAggregationRequest{
			ProductId:   task.ProductId,
			StartDate:   request.StartDate,
			EndDate:     request.EndDate,
			RoutingType: task.RoutingType,
			Region:      envvar.GetCID(),
			WeightRange: weightRanges,
			CurrentPage: 1,
			PageSize:    10,
		})
		if aggErr != nil {
			logger.CtxLogErrorf(ctx, "AggregationOrderData failed: %+v", aggErr)
			return nil, aggErr
		}
		// 将历史结果转换成forecastingTaskResultTab结构体
		resultList := m.convertToResultTableV2(ctx, aggregation.Data.List)
		return m.OrderAggregation(ctx, request, resultList, task, nil)
	}
}

// 反序列化用户上传的重量范围
func unmarshalWeightRange(ctx context.Context, task *persistent.ForecastingTaskTab, request foreschema.QueryOrderAggregationRequest) []*persistent.WeightRange {
	var wrList []*persistent.WeightRange
	if len(task.WeightRangeDetail) > 0 && request.WeightRangeAble {
		if wrErr := jsoniter.Unmarshal(task.WeightRangeDetail, &wrList); wrErr != nil {
			logger.CtxLogErrorf(ctx, "unmarshalWeightRange Unmarshal weight range failed: %v", wrErr)
		}
	}
	if len(wrList) == 0 {
		//如果用户没有上传重量范围就默认用int32最大值(和data协商用int32最大值)
		wrList = []*persistent.WeightRange{{
			Min: 0,
			Max: math.MaxInt32,
		}}
	}

	return wrList
}

// 对数据库预测结果进行组装
func (m *ServiceImpl) composeResponseData(ruleList []*persistent.ForecastingRuleTab, aggregationData map[int][]*foreschema.QueryOrderAggregationResponse, isResult bool) map[int][]*foreschema.ForecastRuleInfo {

	ruleMap := map[int64]*persistent.ForecastingRuleTab{}

	for _, rule := range ruleList {
		ruleMap[int64(rule.Id)] = rule
	}

	response := map[int][]*foreschema.ForecastRuleInfo{}

	for day, data := range aggregationData {
		temp := map[int64]*foreschema.ForecastRuleInfo{}
		for _, result := range data {
			if _, ok := temp[result.RuleId]; !ok {
				temp[result.RuleId] = &foreschema.ForecastRuleInfo{}
			}
			// 这里判断是预测结果还是历史订单数据，预测结果是按照天维度展示，历史订是按照一天展示
			if isResult {
				temp[result.RuleId].RuleId = result.RuleId
				temp[result.RuleId].RuleName = ruleMap[result.RuleId].RuleName
				temp[result.RuleId].Priority = ruleMap[result.RuleId].Priority
			}
			temp[result.RuleId].Data = append(temp[result.RuleId].Data, *result)
		}
		response[day] = []*foreschema.ForecastRuleInfo{}
		for _, ruleInfo := range temp {
			response[day] = append(response[day], ruleInfo)
		}
	}

	return response
}

func (m *ServiceImpl) OrderAggregation(ctx context.Context, request foreschema.QueryOrderAggregationRequest, resultList []persistent.ForecastingTaskResultTab, task *persistent.ForecastingTaskTab, simpleAggRes map[int][]*foreschema.QueryOrderAggregationResponse) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error) {
	dayMaps := map[int][]persistent.ForecastingTaskResultTab{}

	orderCountForDayMap := map[int]int{}
	for _, result := range resultList {
		if result.LaneCode != forecast.AllBlocked { // 这里是排除汇总的block数据
			orderCountForDayMap[result.Day] += result.Quantity
		}
		if isForecastBlockOrFailed(result.LaneCode) { // 如果是失败或者block的订单直接跳过
			continue
		}
		if _, ok := dayMaps[result.Day]; !ok {
			dayMaps[result.Day] = []persistent.ForecastingTaskResultTab{}
		}
		dayMaps[result.Day] = append(dayMaps[result.Day], result)
	}
	var res map[int][]*foreschema.QueryOrderAggregationResponse
	if simpleAggRes != nil {
		res = simpleAggRes
	} else {
		res = map[int][]*foreschema.QueryOrderAggregationResponse{}
	}
	//按照天维度聚合
	for day, resultList := range dayMaps {
		resultMap := map[AggregationResult]*AggregationData{}
		dayCount := 0
		for _, result := range resultList {
			if isForecastBlockOrFailed(result.LaneCode) {
				continue
			}
			// 根据lanecode查询对应的lane信息
			laneInfo, laneErr := m.laneSrv.GetLaneInfoByLaneCode(ctx, result.LaneCode)
			if laneErr != nil || laneInfo == nil {
				monitoring.ReportError(ctx, monitoring.LocalForecastAdmin, monitoring.ForecastLaneCodeNotFound, fmt.Sprintf("task id %d search lane info failed%v", request.TaskId, laneErr))
				logger.CtxLogErrorf(ctx, "Get Lane info failed or not fount :%+v,laneCode %s", laneErr, result.LaneCode)
				continue
			}
			// 根据buyer city id查询对应的address
			addr, err := m.address.GetLocationByLocId(ctx, result.BuyerCity)
			if err != nil || addr == nil {
				monitoring.ReportError(ctx, monitoring.LocalForecastAdmin, monitoring.ForecastLocationNotFound, fmt.Sprintf("task id %d  search loc info failed%v", request.TaskId, laneErr))
				logger.CtxLogErrorf(ctx, "Get address info failed or not fount:%+v,address id is %d", err, result.BuyerCity)
				continue
			}
			// 构建简单聚合结果
			aggregationResultInfo := buildAggregationResult(result, laneInfo, addr, request)
			if _, ok := resultMap[aggregationResultInfo]; !ok {
				resultMap[aggregationResultInfo] = &AggregationData{}
			}
			resultMap[aggregationResultInfo].ShippingFee += result.ShippingFee
			resultMap[aggregationResultInfo].Quantity += result.Quantity

			dayCount += result.Quantity
		}
		res[day] = []*foreschema.QueryOrderAggregationResponse{}
		for key, value := range resultMap {
			// 构建返回信息详情
			respInfo := buildAggregationRespByDay(day, key, value, request, orderCountForDayMap, task)
			res[day] = append(res[day], respInfo)
		}

	}

	return res, nil
}

func buildAggregationRespByDay(day int, aggregationResultInfo AggregationResult, orderCountInfo *AggregationData, request foreschema.QueryOrderAggregationRequest, orderCountForDayMap map[int]int, task *persistent.ForecastingTaskTab) *foreschema.QueryOrderAggregationResponse {
	resp := &foreschema.QueryOrderAggregationResponse{}
	resp.FMCode = aggregationResultInfo.FmCode
	resp.LMCode = aggregationResultInfo.LmCode
	if aggregationResultInfo.SiteCode != "" {
		resp.SiteCode1 = aggregationResultInfo.SiteCode
		resp.SiteCode = append(resp.SiteCode, aggregationResultInfo.SiteCode)
	}
	if aggregationResultInfo.SiteCode2 != "" {
		resp.SiteCode2 = aggregationResultInfo.SiteCode2
		resp.SiteCode = append(resp.SiteCode, aggregationResultInfo.SiteCode2)
	}
	if aggregationResultInfo.ActualPoint != "" {
		resp.ActualPoint1 = aggregationResultInfo.ActualPoint
		resp.ActualPoint = append(resp.ActualPoint, aggregationResultInfo.ActualPoint)
	}
	if aggregationResultInfo.ActualPoint2 != "" {
		resp.SiteCode2 = aggregationResultInfo.ActualPoint2
		resp.ActualPoint = append(resp.ActualPoint, aggregationResultInfo.ActualPoint2)
	}
	resp.BuyerState = aggregationResultInfo.BuyerState
	resp.BuyerCity = aggregationResultInfo.BuyerCity
	resp.OrderAmount = orderCountInfo.Quantity
	resp.RuleId = aggregationResultInfo.RuleId
	resp.Priority = aggregationResultInfo.Priority
	if aggregationResultInfo.WR.Max > 0 {
		min := strconv.FormatInt(int64(aggregationResultInfo.WR.Min), 10)
		max := strconv.FormatInt(int64(aggregationResultInfo.WR.Max), 10)
		resp.WeightRange = "[" + min + "," + max + "]"
	}
	if request.PercentageOfTotalAble && orderCountForDayMap[day] != 0 { // 按照天维度统计百分比
		resp.PercentageOfTotal = mathutil2.FloatWithFixDecimals(float64(orderCountInfo.Quantity)/float64(orderCountForDayMap[day])*100, 2)
	}

	if request.ShippingFee && orderCountInfo.Quantity != 0 {
		if request.IsResult {
			resp.ShippingFee = mathutil2.FloatWithFixDecimals(orderCountInfo.ShippingFee/float64(orderCountInfo.Quantity)*100, 2)
		} else {
			resp.ShippingFee = orderCountInfo.ShippingFee // 如果是历史订单这里运费data已经算好平均值了
		}
	}

	if request.ADOAble {
		resp.Ado = calculateADO(request, orderCountInfo.Quantity, task)
	}

	return resp
}

func calculateADO(request foreschema.QueryOrderAggregationRequest, orderCount int, task *persistent.ForecastingTaskTab) float64 {
	var ado float64
	if request.IsResult { // 预测结果聚合计算
		if len(task.SimulationOrderCountDetail) > 0 { // 如果用户有上传模拟单量则ADO=OrderCount
			ado = mathutil2.FloatWithFixDecimals(float64(orderCount), 2)
		} else { // 如果没有上传模拟天数ADO=orderCount/订单日期范围
			ado = calculateADOByDays(task.StartDate, task.EndDate, orderCount)
		}
	} else { // 历史订单聚合计算
		ado = calculateADOByDays(request.StartDate, request.EndDate, orderCount)
	}

	return ado
}

func calculateADOByDays(startDate, endDate string, orderCount int) float64 {
	var ado float64
	days, _ := getDays(startDate, endDate)
	if days != 0 {
		ado = mathutil2.FloatWithFixDecimals(float64(orderCount)/float64(days), 2)
	}
	return ado
}

// 根据前端传来的字段信息聚合结果
func buildAggregationResult(forecastResult persistent.ForecastingTaskResultTab, laneInfo *lane_entity.LaneInfo, addr *address.Location, request foreschema.QueryOrderAggregationRequest) AggregationResult {
	aggregationResultInfo := AggregationResult{}
	aggregationResultInfo.RuleId = int64(forecastResult.RuleId)
	aggregationResultInfo.Priority = int64(forecastResult.Priority)
	if request.FmAble { // 是否聚合FM
		aggregationResultInfo.FmCode = laneInfo.GetLFMLieInfo().LineID
	}
	if request.LmAble { // 是否聚合LM
		aggregationResultInfo.LmCode = laneInfo.GetLLMLieInfo().LineID
	}

	if request.BuyerStateAble { // 是否聚合buyer state
		aggregationResultInfo.BuyerState = addr.GetState()
	}

	if request.BuyerCityAble { // 是否聚合buyer city
		aggregationResultInfo.BuyerCity = addr.GetCity()
	}

	if request.WeightRangeAble { // 是否按重量聚合
		aggregationResultInfo.WR = forecastResult.WeightRange
	}

	CrossDockSite := 1
	if request.SiteAble { // 是否聚合lane对应的点
		sites := strings.Split(forecastResult.Site, "#")
		if len(sites) > CrossDockSite { //考虑多段式下存在多个点
			aggregationResultInfo.SiteCode = sites[0]
			aggregationResultInfo.SiteCode2 = sites[1]
		} else if len(sites) != 0 {
			aggregationResultInfo.SiteCode = sites[0]
		}
	}
	if request.ActualPoint { // 是否聚合实际的点
		actualPoints := strings.Split(forecastResult.ActualPoint, "#")
		if len(actualPoints) == 0 { // 如果解析出来的actual，展示所有的actual point
			actualPoints = getActualPoint(forecastResult.Site, laneInfo)
		}
		if len(actualPoints) > 1 {
			aggregationResultInfo.ActualPoint = actualPoints[0]  //表示inbound actual point
			aggregationResultInfo.ActualPoint2 = actualPoints[1] //表示outbound actual point
		} else if len(actualPoints) != 0 {
			aggregationResultInfo.ActualPoint2 = actualPoints[0]
		}
	}
	return aggregationResultInfo
}

func getActualPoint(siteStr string, laneInfo *lane_entity.LaneInfo) []string {
	sites := strings.Split(siteStr, "#")
	actpList := []string{}
	for _, site := range sites {
		actpList = append(actpList, strings.Join(laneInfo.GetActualPointBySiteId(site), "_"))
	}
	return actpList
}

// 判断单条订单是否为预测block或者failed
func isForecastBlockOrFailed(blockName string) bool {
	return blockName == forecast.AllBlocked || blockName == forecast.Blocked || blockName == forecast.RuleBlocked || blockName == forecast.RoutingFailed
}

func getDays(start, end string) (int, error) {
	timeFormat := "2006-01-02"
	startDate, err := timeutil.ParseLocalTime(timeFormat, start)
	if err != nil {
		return 0, errors.Unwrap(err)
	}
	endDate, err := timeutil.ParseLocalTime(timeFormat, end)
	if err != nil {
		return 0, errors.Unwrap(err)
	}
	var ret []time.Time
	for cur := startDate; !cur.After(endDate); cur = cur.Add(time.Hour * 24) {
		ret = append(ret, cur)
	}
	return len(ret), nil
}

func (m *ServiceImpl) convertToResultTable(ctx context.Context, response []dataclient.AggregationData) []persistent.ForecastingTaskResultTab {
	res := []persistent.ForecastingTaskResultTab{}
	for _, orderResult := range response {
		result := persistent.ForecastingTaskResultTab{}
		result.LaneCode = orderResult.LaneCode
		result.BuyerCity = orderResult.BuyerCityId
		result.Quantity = orderResult.Quantity
		result.ShippingFee = orderResult.ShippingFee
		wr := strings.Split(orderResult.WeightRange, "-")
		if len(wr) > 0 {
			min, minErr := strconv.Atoi(wr[0])
			if minErr != nil {
				logger.CtxLogErrorf(ctx, "convertToResultTable min failed:%+v", minErr)
			}
			max, maxErr := strconv.Atoi(wr[1])
			if maxErr != nil {
				logger.CtxLogErrorf(ctx, "convertToResultTable maxSimulationDays failed:%+v", maxErr)
			}
			result.WeightRange = persistent.WeightRange{Min: min, Max: max}
		}

		laneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, orderResult.LaneCode)
		if err != nil {
			monitoring.ReportError(ctx, monitoring.LocalForecastAdmin, monitoring.ForecastLaneCodeNotFound, fmt.Sprintf("search lane info failed%+v", err))
			logger.CtxLogErrorf(ctx, "search lane info failed%+v", err)
			continue
		}
		// 这里是为了筛选site，Sequence表示line的顺序，类似于FM为1，site为2，LM为3
		min := math.MaxInt16
		max := math.MinInt16
		for _, line := range laneInfo.GetAllLineInfo() {
			min = mathutil.Min(min, line.Sequence)
			max = mathutil.Max(max, line.Sequence)
		}
		// 这里就是从line中卡site信息，后续这里会优化直接根据site的类型来判断
		sites := []string{}
		for _, site := range laneInfo.GetAllSiteInfo() {
			if site.Sequence > min && site.Sequence < max {
				sites = append(sites, site.SiteID)
			}
		}

		result.Site = strings.Join(sites, "#")
		res = append(res, result)
	}

	return res
}

func (m *ServiceImpl) convertToResultTableV2(ctx context.Context, response []dataclient.AggregationDataV2) []persistent.ForecastingTaskResultTab {
	res := []persistent.ForecastingTaskResultTab{}
	for _, orderResult := range response {
		result := persistent.ForecastingTaskResultTab{}
		result.LaneCode = orderResult.LaneCode
		result.BuyerCity = orderResult.BuyerCityId
		result.Quantity = orderResult.Quantity
		result.ShippingFee = orderResult.ShippingFee
		wr := strings.Split(orderResult.WeightRange, "-")
		if len(wr) > 0 {
			min, minErr := strconv.Atoi(wr[0])
			if minErr != nil {
				logger.CtxLogErrorf(ctx, "convertToResultTable min failed:%+v", minErr)
			}
			max, maxErr := strconv.Atoi(wr[1])
			if maxErr != nil {
				logger.CtxLogErrorf(ctx, "convertToResultTable maxSimulationDays failed:%+v", maxErr)
			}
			result.WeightRange = persistent.WeightRange{Min: min, Max: max}
		}

		laneInfo, err := m.laneSrv.GetLaneInfoByLaneCode(ctx, orderResult.LaneCode)
		if err != nil {
			monitoring.ReportError(ctx, monitoring.LocalForecastAdmin, monitoring.ForecastLaneCodeNotFound, fmt.Sprintf("search lane info failed%+v", err))
			logger.CtxLogErrorf(ctx, "search lane info failed%+v", err)
			continue
		}
		// 这里是为了筛选site，Sequence表示line的顺序，类似于FM为1，site为2，LM为3
		min := math.MaxInt16
		max := math.MinInt16
		for _, line := range laneInfo.GetAllLineInfo() {
			min = mathutil.Min(min, line.Sequence)
			max = mathutil.Max(max, line.Sequence)
		}
		// 这里就是从line中卡site信息，后续这里会优化直接根据site的类型来判断
		sites := []string{}
		for _, site := range laneInfo.GetAllSiteInfo() {
			if site.Sequence > min && site.Sequence < max {
				sites = append(sites, site.SiteID)
			}
		}
		result.ActualPoint = orderResult.InActualPoint + "#" + orderResult.OutActualPoint
		result.Site = strings.Join(sites, "#")
		res = append(res, result)
	}

	return res
}

func (m *ServiceImpl) ExportOrderAggregation(ctx context.Context, request foreschema.QueryOrderAggregationRequest) (string, *srerr.Error) {

	aggregation, err := m.AggregationOrderData(ctx, request)
	if err != nil {
		return "", nil
	}

	header := getHeader(request)
	excelData := changeAggregationToExcel(request, aggregation)

	excelFile, fErr := fileutil.MakeExcelWithSheetNames(ctx, header, excelData)
	if fErr != nil {
		logger.CtxLogErrorf(ctx, "generate excel failed %v", fErr)
		return "", srerr.With(srerr.MakeExcelError, request, fErr)
	}

	b, wErr := excelFile.WriteToBuffer()
	if wErr != nil {
		logger.CtxLogErrorf(ctx, "excel write to buffer failed %v", wErr)
		return "", srerr.With(srerr.MakeExcelError, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	exportKey := fmt.Sprintf("Export AggregationOrderData-%v%s", timeutil.GetCurrentTime(ctx).Unix(), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, exportKey, b); err != nil {
		logger.CtxLogErrorf(ctx, "s3 upload failed %v", err)
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, exportKey), nil
}

func changeAggregationToExcel(req foreschema.QueryOrderAggregationRequest, data map[int][]*foreschema.QueryOrderAggregationResponse) map[string][][]string {
	res := map[string][][]string{}

	for day, resultList := range data {
		excelData := [][]string{}
		for i := 0; i < len(resultList); i++ {
			row := []string{}
			if req.FmAble {
				row = append(row, resultList[i].FMCode)
			}
			if req.LmAble {
				row = append(row, resultList[i].LMCode)
			}
			if req.SiteAble {
				//可能存在为nil情况
				if resultList[i].SiteCode == nil {
					resultList[i].SiteCode = []string{}
				}
				//填充数据防止导出的时候数据错位,append进去一个空格
				writeSiteCodeLen := len(resultList[i].SiteCode)
				for j := 0; j < (maxSiteCode - writeSiteCodeLen); j++ {
					resultList[i].SiteCode = append(resultList[i].SiteCode, "")
				}

				row = append(row, resultList[i].SiteCode...)
			}
			if req.ActualPoint {
				//防止导出的时候数据错位
				writeActualPointLen := len(resultList[i].ActualPoint)
				for j := 0; j < maxSiteCode-writeActualPointLen; j++ {
					resultList[i].ActualPoint = append(resultList[i].ActualPoint, "")
				}

				row = append(row, resultList[i].ActualPoint...)
			}
			if req.BuyerStateAble {
				row = append(row, resultList[i].BuyerState)
			}
			if req.BuyerCityAble {
				row = append(row, resultList[i].BuyerCity)
			}
			if req.WeightRangeAble {
				row = append(row, resultList[i].WeightRange)
			}
			if req.OrderAmountAble {
				orderAmount := strconv.Itoa(resultList[i].OrderAmount)
				row = append(row, orderAmount)
			}
			if req.PercentageOfTotalAble {
				row = append(row, fmt.Sprintf("%.2f", resultList[i].PercentageOfTotal))
			}
			if req.ADOAble {
				row = append(row, fmt.Sprintf("%.2f", resultList[i].Ado))
			}
			if req.ShippingFee {
				row = append(row, fmt.Sprintf("%.2f", resultList[i].ShippingFee))
			}
			excelData = append(excelData, row)
		}

		res[strconv.Itoa(day)] = excelData
	}

	return res
}

func getHeader(request foreschema.QueryOrderAggregationRequest) []string {
	headers := []string{}

	if request.FmAble {
		headers = append(headers, "fm_code")
	}
	if request.LmAble {
		headers = append(headers, "lm_code")
	}
	if request.SiteAble {
		headers = append(headers, "site_code1")
		headers = append(headers, "site_code2")
	}
	if request.ActualPoint {
		headers = append(headers, "actual_point1")
		headers = append(headers, "actual_point2")
	}
	if request.BuyerStateAble {
		headers = append(headers, "buyer_state")
	}
	if request.BuyerCityAble {
		headers = append(headers, "buyer_city")
	}
	if request.WeightRangeAble {
		headers = append(headers, "weight_range")
	}
	if request.OrderAmountAble {
		headers = append(headers, "order amount")
	}
	if request.PercentageOfTotalAble {
		headers = append(headers, "% of total")
	}
	if request.ADOAble {
		headers = append(headers, "ADO")
	}
	if request.ShippingFee {
		headers = append(headers, "ave shipping fee per order")
	}

	return headers
}

func (m *ServiceImpl) GetBlockOrderList(ctx context.Context, request foreschema.BlockOrderListRequest) (map[int][]foreschema.BlockOrderListResponse, *srerr.Error) {
	ForecastTask, terr := m.Repo.GetTaskById(ctx, request.TaskId)
	if terr != nil {
		return nil, srerr.With(srerr.ParamErr, nil, terr)
	}

	forecastResults, err := m.Repo.GetResultByTaskId(ctx, request.TaskId)
	if err != nil {
		return nil, srerr.With(srerr.ParamErr, nil, err)
	}
	res := map[int][]foreschema.BlockOrderListResponse{}
	simulateDateOrderCount := getSimulateDateOrderCount(ctx, ForecastTask)
	dayForecastResultMap := classifyForecastResultByDay(forecastResults)
	// 定义block类型对应的顺序
	blockSortMap := map[string]int{
		forecast.RuleBlocked:   1,
		forecast.Blocked:       2,
		forecast.RoutingFailed: 3,
		forecast.AllBlocked:    4,
	}
	for day, orderCount := range simulateDateOrderCount {
		res[day] = []foreschema.BlockOrderListResponse{}
		if dayForecastResultItemList, ok := dayForecastResultMap[day]; !ok {
			logger.CtxLogErrorf(ctx, "search forecast by day failed day=%d", day)
			continue
		} else {
			for _, resultItem := range dayForecastResultItemList {
				res[day] = append(res[day], foreschema.BlockOrderListResponse{
					BlockType:       resultItem.LaneCode,
					BlockOrderCount: resultItem.Quantity,
					BlockPercentage: mathutil2.FloatWithFixDecimals(float64(resultItem.Quantity)/float64(orderCount)*100, 2), // 这里是保留2位小数
				})
			}
		}
		//// 按照映射顺序排序
		sort.Slice(res[day], func(i, j int) bool {
			return blockSortMap[res[day][i].BlockType] < blockSortMap[res[day][j].BlockType]
		})
	}

	return res, nil
}

func getSimulateDateOrderCount(ctx context.Context, forecastTask *persistent.ForecastingTaskTab) map[int]int {
	res := map[int]int{}
	if forecastTask == nil {
		return res
	}

	if len(forecastTask.SimulationOrderCountDetail) > 0 {
		if err := jsoniter.Unmarshal(forecastTask.SimulationOrderCountDetail, &forecastTask.SimulationOrderCount); err != nil {
			logger.CtxLogInfof(ctx, "Unmarshal SimulationOrderCountDetail failed err=%v", err)
		}
	}

	for _, simulateDateOrderCount := range forecastTask.SimulationOrderCount {
		res[simulateDateOrderCount.Day] = simulateDateOrderCount.Count
	}
	// 如果用户没有上传模拟日期订单量则用默认的天数和订单总量
	if len(res) == 0 {
		res[defaultDay] = forecastTask.OrderCount
	}
	return res
}

func classifyForecastResultByDay(forecastResults []persistent.ForecastingTaskResultTab) map[int][]persistent.ForecastingTaskResultTab {
	res := map[int][]persistent.ForecastingTaskResultTab{}
	for _, result := range forecastResults {
		if result.RuleId == blockRuleId {
			if res[result.Day] == nil {
				res[result.Day] = []persistent.ForecastingTaskResultTab{}
			}
			res[result.Day] = append(res[result.Day], result)
		}
	}

	return res
}

func copyForecastRule(task *persistent.ForecastingTaskTab, effectiveStartTime uint32, operateBy string, tmpRule *persistent.ForecastingRuleTab) *ruledata.RoutingRuleTab {
	return &ruledata.RoutingRuleTab{
		ID:                    0,
		ProductID:             int64(task.ProductId),
		EffectiveStartTime:    effectiveStartTime,
		Status:                rule.RuleStatusForecast,
		OperatedBy:            operateBy,
		TaskID:                int64(task.Id),
		RoutingType:           task.RoutingType,
		RuleName:              tmpRule.RuleName,
		Rules:                 tmpRule.StrRuleDetails,
		StrDefaultCriteria:    tmpRule.StrDefaultCriteria,
		StrDisabledInfo:       tmpRule.StrDisabledInfo,
		StrCombinationSetting: tmpRule.StrCombinationSetting,
		Priority:              int32(tmpRule.Priority),
		WhsId:                 tmpRule.WhsId,
		ZoneCode:              tmpRule.ZoneCode,
		ItemCategoryIDList:    tmpRule.ItemCategoryId,
		ItemCategoryLevel:     tmpRule.ItemCategoryLevel,
		ParcelValueMax:        tmpRule.ParcelValueMax,
		ParcelValueMin:        tmpRule.ParcelValueMin,
		ParcelWeightMax:       tmpRule.ParcelWeightMax,
		ParcelWeightMin:       tmpRule.ParcelWeightMin,
		IsMultiProduct:        tmpRule.IsMultiProduct,
		DestinationPorts:      tmpRule.DestinationPorts,
		DgType:                tmpRule.DgType,
		ParcelDimension:       tmpRule.ParcelDimension,
		CCMode:                tmpRule.CCMode,
		StrTplToggleInfo:      tmpRule.StrTplToggleInfo,
		ShopGroupList:         tmpRule.ShopGroupList,
	}
}

func (m *ServiceImpl) getOrCloneVolumeRule(ctx context.Context, volumeRuleId int64, effectiveStartTime int64) (uint64, *srerr.Error) {
	operator, _ := apiutil.GetUserInfo(ctx)
	ruleList, err := m.ZoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{ // 如果rule是submit则不需要clone，直接改状态
		"id = ?":          volumeRuleId,
		"rule_status = ?": enum.VolumeRuleStatusSubmit,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "query volume rule err=%v", err)
		return 0, err
	}
	if len(ruleList) != 0 {
		oldVolumeRule := vrentity.ConvertToVolumeRuleInfo(ruleList[0])
		oldVolumeRule.EffectiveStartTime = effectiveStartTime
		oldVolumeRule.Status = enum.VolumeRuleStatusQueuing
		oldVolumeRule.IsForecastType = constant.NonForecastType
		if err := m.ZoneRuleRepo.SaveVolumeRule(ctx, &oldVolumeRule, operator); err != nil {
			return 0, err
		}

		return oldVolumeRule.RuleId, nil
	} else {
		newVolumeRuleId, err := m.zoneMgr.CopyVolumeRuleWithNewStatus(ctx, volumeRuleId, enum.VolumeRuleStatusQueuing, effectiveStartTime, constant.NonForecastType)
		if err != nil {
			return 0, err
		}
		return newVolumeRuleId, nil
	}
}

// 这里只校验cb的预测
func checkUseVolumeV2(ctx context.Context, ruleList []*persistent.ForecastingRuleTab, volumeRuleId int64, routingType uint8) *srerr.Error {
	if routingType != rule.CBRoutingType {
		return nil
	}
	for _, forecastRule := range ruleList {
		if forecastRule.RuleDetails == nil {
			continue
		}
		// 这里强制校验一下，如果是cb的Forecast，运力开关打开，且打开了最大最小运力，如果没有配置volume rule则报错返回
		for _, factor := range forecastRule.RuleDetails.Rules {
			if factor.ResourceSubType == lfslib.C_LM && (factor.MaxCapacityEnable || factor.MinVolumeEnable || factor.MaxCodCapacityEnable ||
				factor.MaxBulkyCapacityEnable || factor.MaxHighValueCapacityEnable || factor.MaxDgCapacityEnable) &&
				volumeRuleId == 0 && volumeutil.IsOpenVolumeRouting(ctx) {
				return srerr.New(srerr.VolumeRuleError, nil, "To enable the maximum and minimum transport capacity, volume rule must be selected")
			}
		}
	}

	return nil
}

func (m *ServiceImpl) HistoricalOrderAggregationV2(ctx context.Context, task *persistent.ForecastingTaskTab, request foreschema.QueryOrderAggregationRequest) (map[int][]*foreschema.QueryOrderAggregationResponse, *srerr.Error) {
	var simpleAggResult = map[int][]*foreschema.QueryOrderAggregationResponse{}

	aggregateDataList, err := m.queryOrderAggregate(ctx, task, request)
	if err != nil {
		return nil, err
	}

	for aggData := range aggregateDataList {
		resultList := m.convertToResultTableV2(ctx, aggData.Data.List)
		_, err := m.OrderAggregation(ctx, request, resultList, task, simpleAggResult)
		logger.CtxLogErrorf(ctx, "do order aggregate err=%v", err)
	}
	return simpleAggResult, nil
}

func (m *ServiceImpl) queryOrderAggregate(ctx context.Context, task *persistent.ForecastingTaskTab, request foreschema.QueryOrderAggregationRequest) (chan *dataclient.QueryOrderAggregationResponseV2, *srerr.Error) {
	weightRanges := unmarshalWeightRange(ctx, task, request)
	var (
		aggReq = dataclient.QueryOrderAggregationRequest{
			ProductId:   task.ProductId,
			StartDate:   request.StartDate,
			EndDate:     request.EndDate,
			RoutingType: task.RoutingType,
			Region:      envvar.GetCID(),
			WeightRange: weightRanges,
		}
		pageSize           = 3000
		totalPage          = 0
		simpleAggregateRes = make(chan *dataclient.QueryOrderAggregationResponseV2, 100)
	)
	// 查询聚合订单总数
	aggCount, cErr := m.dataApi.QueryOrderAggregationCount(ctx, aggReq)
	if cErr != nil {
		return nil, cErr
	}
	// 算出总的页数
	totalPage = int(int64(math.Ceil(float64(aggCount) / float64(pageSize))))
	go func() {
		defer close(simpleAggregateRes)
		for i := 0; i < totalPage; i++ { // 分页捞数据
			aggReq.CurrentPage = i
			aggReq.PageSize = pageSize
			simpeAggList, err := m.dataApi.OrderAggregationV3(ctx, aggReq)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Query aggregate from clickhouse err=%v", err)
				return
			}
			simpleAggregateRes <- simpeAggList
		}
	}()

	return simpleAggregateRes, nil
}
