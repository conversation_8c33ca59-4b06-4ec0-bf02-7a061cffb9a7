package forecastservice

import (
	"fmt"
	"runtime"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
)

type GroupByInfo struct {
	GroupByIlhGroup    bool
	GroupByTp          bool
	GroupByServiceCode bool
}

type ResultWeightRange struct {
	Min int
	Max int
}

type AggregationResult struct {
	FmCode       string
	LmCode       string
	SiteCode     string
	SiteCode2    string
	ActualPoint  string
	ActualPoint2 string
	BuyerState   string
	BuyerCity    string
	RuleId       int64
	Priority     int64
	WR           persistent.WeightRange
}

type SiteActualPoint struct {
	SiteCode    string
	ActualPoint string
}

type AggregationData struct {
	Quantity    int
	ShippingFee float64
}

func (d AggregationResult) Copy() AggregationResult {
	return AggregationResult{
		FmCode:      d.FmCode,
		LmCode:      d.LmCode,
		SiteCode:    d.SiteCode,
		ActualPoint: d.ActualPoint,
		BuyerState:  d.BuyerState,
		BuyerCity:   d.BuyerCity,
		WR:          d.WR,
		RuleId:      d.RuleId,
		Priority:    d.Priority,
	}
}

type RoutingLogRowKeyMap struct {
	RowKey   string
	LogEntry routing_log.RoutingLog
}

var HardCriteriaPanicHandler = func(err interface{}) {
	if err != nil {
		var buf [4096]byte
		n := runtime.Stack(buf[:], false)
		errMsg := fmt.Sprintf("\n%s\n%s", err, string(buf[:n]))
		logger.LogErrorf("Hard criteria panic %s", errMsg)
	}
}
