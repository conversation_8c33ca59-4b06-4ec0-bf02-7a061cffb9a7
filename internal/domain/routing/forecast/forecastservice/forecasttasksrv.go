package forecastservice

import (
	"context"
	"fmt"
	"math"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	lfspb "git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol/go"
	chargepb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v16/logistics-charge-platform/go"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/routinglogutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	jsoniter "github.com/json-iterator/go"
	"github.com/panjf2000/ants/v2"
	"go.uber.org/ratelimit"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/hbaseutil/masking_forecast_hbase"
	mathutil2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	completionProgress = 100.0
	batchDeleteSize    = 1000
	maxLoop            = 1000
	minPage            = 1
	minPageSize        = 1
	pageSize           = 3000
)

type IForecastTaskService interface {
	IHardCriteriaTaskService
}

type IHardCriteriaTaskService interface {
	CreateDraftHardCriteriaTask(ctx context.Context, request foreschema.CreateHCTaskRequest) (foreschema.DraftTaskResponse, *srerr.Error)        //SPLPS-4372 创建草稿态的硬性校验任务
	EditHardCriteriaTask(ctx context.Context, taskStatus int, request foreschema.EditHCTaskRequest) (foreschema.DraftTaskResponse, *srerr.Error) //编辑更新硬性校验任务
	QueryOrderCountByTime(ctx context.Context, request foreschema.QueryOrderCountRequest) (foreschema.QueryOrderCountResponse, *srerr.Error)     //查询订单数量
	DeleteDraftHardCriteriaTask(ctx context.Context, taskId uint64) *srerr.Error                                                                 //删除草稿态硬性校验任务
	UpdateHardCriteriaTaskStatus(ctx context.Context, req foreschema.UpdateHCTaskStatusRequest) *srerr.Error                                     //更新硬性校验任务状态
	UpdateHardCriteriaTask(ctx context.Context, req foreschema.UpdateHCTaskRequest) *srerr.Error
	GetHardCriteriaTaskList(ctx context.Context, request foreschema.GetHCTaskListRequest) (foreschema.GetHCTaskListResponse, *srerr.Error) //获取硬性校验任务列表
	GetHardCriteriaTaskDetail(ctx context.Context, taskId uint64) (foreschema.GetHCTaskDetailResponse, *srerr.Error)                       //获取硬性校验任务详情
	RefreshHardCriteriaByTask(ctx context.Context, req foreschema.ExecuteHCTaskRequest)                                                    //定时任务-执行硬性校验刷新任务
	CreateHCTaskBySystem(ctx context.Context)                                                                                              //Saturn定时创建硬性校验任务
	CheckHCTask(ctx context.Context)                                                                                                       //检查硬性校验任务是否失败
	GetForecastTaskName(ctx context.Context, id int) (string, *srerr.Error)
	GetForecastOrdersFromHbase(ctx context.Context, index int, taskId uint64, productId, routingType, readLimit int, day time.Time) (chan RoutingLogRowKeyMap, *srerr.Error)
	GetForecastOrderFromDataApi(ctx context.Context, taskId, productId, routingType, page int, day time.Time) (chan RoutingLogRowKeyMap, *srerr.Error)
	RefreshHbaseHardCriteriaByProduct(ctx context.Context, productId int, isMultiProduct bool, day time.Time, concurrency int) *srerr.Error
	DeleteHardCriteriaByTaskId(ctx context.Context, taskId int) *srerr.Error
	DeleteForecastTaskByTaskId(ctx context.Context, taskId int) *srerr.Error
	GetForecastShopGroupList(ctx context.Context) ([]lpsclient.GetClientGroupTab, *srerr.Error)
}

type ForecastTaskServiceImpl struct {
	LpsApi                        lpsclient.LpsApi
	DataApi                       *dataclient.DataApi
	ForecastRepo                  forecastrepo.IForecastRepo
	RoutingConfService            routing_config.RoutingConfigService
	CCRoutingSrv                  cc_routing.CCRoutingService
	hbHelper                      *masking_forecast_hbase.HBHelper
	LineCheapestShippingFeeFactor *schedule_factor.LineCheapestShippingFeeFactor
	RoutingLogService             routing_log.RoutingLogService
	WbcApi                        wbcclient.WbcApi
	ChargeApi                     chargeclient.ChargeApi
}

func NewForecastTaskServiceImpl(
	routingConfService routing_config.RoutingConfigService,
	forecastRepo forecastrepo.IForecastRepo,
	lpsApi lpsclient.LpsApi,
	ccRoutingSrv cc_routing.CCRoutingService,
	lineCheapestShippingFeeFactor *schedule_factor.LineCheapestShippingFeeFactor,
	RoutingLogService routing_log.RoutingLogService,
	dataApi *dataclient.DataApi,
	wbcApi wbcclient.WbcApi,
	chargeApi chargeclient.ChargeApi,
) *ForecastTaskServiceImpl {
	return &ForecastTaskServiceImpl{
		RoutingConfService:            routingConfService,
		ForecastRepo:                  forecastRepo,
		LpsApi:                        lpsApi,
		CCRoutingSrv:                  ccRoutingSrv,
		DataApi:                       dataApi,
		hbHelper:                      masking_forecast_hbase.NewHBHelper(),
		LineCheapestShippingFeeFactor: lineCheapestShippingFeeFactor,
		RoutingLogService:             RoutingLogService,
		WbcApi:                        wbcApi,
		ChargeApi:                     chargeApi,
	}
}

func (m *ForecastTaskServiceImpl) GetHardCriteriaTaskDetail(ctx context.Context, taskId uint64) (foreschema.GetHCTaskDetailResponse, *srerr.Error) {
	var response foreschema.GetHCTaskDetailResponse
	//1.组建condition
	condition := map[string]interface{}{
		"id = ?": taskId,
	}
	tabs, err := m.ForecastRepo.GetHardCriteriaTask(ctx, condition)
	if err != nil {
		return response, err
	}
	if len(tabs) == 0 {
		logger.CtxLogInfof(ctx, "GetHardCriteriaTaskDetail| get empty tab list from db, id:%v", taskId)
		return response, srerr.New(srerr.ParamErr, nil, "GetHardCriteriaTaskDetail|get empty tab list from db, id:%v", taskId)
	}

	tab := tabs[0]

	//2.组装db数据到entity
	response = foreschema.GetHCTaskDetailResponse{
		TaskType:       uint8(tab.TaskType),
		TaskId:         tab.Id,
		TaskName:       tab.TaskName,
		ProductId:      tab.ProductId,
		TaskStatus:     uint8(tab.TaskStatus),
		OrderStartTime: tab.OrderStartTime,
		OrderEndTime:   tab.OrderEndTime,
		OrderCount:     uint64(tab.OrderCount),
		TaskStartTime:  uint32(tab.TaskStartTime),
		TaskEndTime:    uint32(tab.TaskEndTime),
		LastStartTime:  uint32(tab.LastStartTime),
		LastEndTime:    uint32(tab.LastEndTime),
		Operator:       tab.Operator,
		Ctime:          uint64(tab.CTime),
	}

	//3.获取已刷新订单数据（所有的已刷新订单）
	//获取redis中对应key为productId+orderStartTime+orderEndTime的值(总共完成的订单)
	finishedNum, err := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixAllDay, tab.Id, tab.ProductId, tab.OrderStartTime, tab.OrderEndTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskDetail| get total volume err:%+v", err)
	}
	if tab.OrderCount != 0 {
		response.TaskProgress = math.Floor(float64(finishedNum) / float64(tab.OrderCount) * 100)
	}

	//4.按天获取已完成订单信息
	completionOrders, finishedOrderCount, cErr := m.GetCompletionOrdersInfo(ctx, tab.Id, tab.RoutingType, int64(tab.ProductId), tab.OrderStartTime, tab.OrderEndTime)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskDetail| get completion orders err:%+v", cErr)
		//draft态有可能存在order start time不存在的情况
		if tab.TaskStatus != forecast.TaskDraft {
			return response, cErr
		}
	}
	response.CompletionOrders = completionOrders
	blockOrderCount := int(tab.OrderCount) - finishedOrderCount
	if blockOrderCount < 0 {
		blockOrderCount = 0
	}
	response.BlockOrderQuantity = blockOrderCount
	if tab.OrderCount != 0 {
		// 保留两位小数
		response.BlockOrderPercentage = mathutil2.FloatWithFixDecimals(float64(response.BlockOrderQuantity)/float64(tab.OrderCount)*100, 2)
	}
	//5.获取硬性校验刷新qps（配置在Apollo上）,并计算预估时间
	qps := configutil.GetHardCriteriaTaskConfig(ctx).Qps
	if qps != 0 {
		response.EstimatedTime = response.OrderCount / qps / 60
	}

	if response.EstimatedTime == 0 {
		response.EstimatedTime = 1
	}
	// 处理完成状态的任务
	processFinishTask(tab, &response)
	return response, nil
}

func processFinishTask(task persistent.HardCriteriaTaskTab, resp *foreschema.GetHCTaskDetailResponse) {
	if task.TaskStatus == forecast.TaskDone {
		resp.TaskProgress = forecast.FinishTask // 把状态任务改成100%
		for i := 0; i < len(resp.CompletionOrders); i++ {
			resp.CompletionOrders[i].Percentage = forecast.FinishTask // 对应的每天的任务也改成100%
		}
	}
}

// CreateDraftHardCriteriaTask SPLPS-4372 创建草稿态的硬性校验任务
func (m *ForecastTaskServiceImpl) CreateDraftHardCriteriaTask(ctx context.Context, request foreschema.CreateHCTaskRequest) (foreschema.DraftTaskResponse, *srerr.Error) {
	createResponse := foreschema.DraftTaskResponse{}
	logger.CtxLogInfof(ctx, "CreateDraftHardCriteriaTask| req:%+v", request)

	//校验product id
	checkProduct, pErr := m.LpsApi.GetProductDetail(ctx, int(request.ProductId))
	if checkProduct == nil || pErr != nil {
		logger.CtxLogErrorf(ctx, "CreateDraftHardCriteriaTask| get product failed, pid=%v", request.ProductId)
		return createResponse, srerr.New(srerr.ParamErr, nil, "get product failed, pid=%v", request.ProductId)
	}

	//校验task name长度
	if len(request.TaskName) > 50 {
		return createResponse, srerr.New(srerr.ParamErr, nil, "task name can not be longer than 50 letters")
	}

	//1.这个设计是因为不希望一个product下有太多的draft，希望业务每次创建完一个task就立马使用，随用随建
	hartCriteriaTaskTabs, err := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"product_id = ?":       request.ProductId,
		"is_multi_product = ?": request.IsMultiProduct,
		"task_type = ?":        forecast.TaskTypeCreatedByOps,
		"task_status = ?":      forecast.TaskDraft,
		"routing_type = ?":     request.RoutingType,
	})
	//db错误
	if err != nil {
		logger.CtxLogErrorf(ctx, "CreateDraftTask|check unique key, db err=%+v", err)
		return createResponse, err
	}
	//数据已存在，返回错误
	if len(hartCriteriaTaskTabs) > 0 {
		logger.CtxLogInfof(ctx, "CreateDraftTask|task existed, request=%+v", request)
		return createResponse, srerr.New(srerr.HardCriteriaTaskExisted, nil, "This product already has one task in db")
	}

	//2. 唯一索引不存在，往数据库写入数据
	tab := persistent.HardCriteriaTaskTab{
		TaskName:       request.TaskName,
		ProductId:      request.ProductId,
		IsMultiProduct: request.IsMultiProduct,
		OrderStartTime: request.OrderStartTime,
		OrderEndTime:   request.OrderEndTime,
		OrderCount:     request.OrderCount,
		Operator:       request.Operator,
		TaskStatus:     forecast.TaskDraft,
		TaskType:       forecast.TaskTypeCreatedByOps,
		RoutingType:    request.RoutingType,
		CTime:          recorder.Now(ctx).Unix(),
		MTime:          recorder.Now(ctx).Unix(),
	}

	if iErr := m.ForecastRepo.CreateHardCriteriaTask(ctx, &tab); iErr != nil {
		logger.CtxLogErrorf(ctx, "CreateDraftTask|insert data, db err=%+v", iErr)
		return createResponse, iErr
	}

	createResponse.TaskId = tab.Id
	return createResponse, nil
}

func (m *ForecastTaskServiceImpl) EditHardCriteriaTask(ctx context.Context, taskStatus int, request foreschema.EditHCTaskRequest) (foreschema.DraftTaskResponse, *srerr.Error) {
	editResponse := foreschema.DraftTaskResponse{}
	logger.CtxLogInfof(ctx, "EditHardCriteriaTask|request:%+v", request)
	//1.校验该task_id对应的状态为draft，只有draft态的才可以编辑
	checkTabs, sErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{"id = ?": request.TaskId})
	if sErr != nil {
		logger.CtxLogErrorf(ctx, "EditHardCriteriaTask|check task type fail, db err=%+v", sErr)
		return editResponse, sErr
	}
	if len(checkTabs) == 0 {
		return editResponse, srerr.New(srerr.UpdateErr, nil, "EditHardCriteriaTask|task id:%v,task not found", request.TaskId)
	}
	if checkTabs[0].TaskType == forecast.TaskTypeCreatedByOps && checkTabs[0].TaskStatus != forecast.TaskDraft {
		logger.CtxLogInfof(ctx, "EditHardCriteriaTask| check task type fail, task status:%v is not equal to draft:%v", checkTabs[0].TaskStatus, forecast.TaskDraft)
		return editResponse, srerr.New(srerr.UpdateErr, nil, "EditHardCriteriaTask| check task type fail, task status:%v is not equal to draft:%v", checkTabs[0].TaskStatus, forecast.TaskDraft)
	}
	//2.校验订单数量，配置在Apollo上
	orderCountLimit := configutil.GetHardCriteriaTaskConfig(ctx).OrderCount
	if request.OrderCount > orderCountLimit {
		logger.CtxLogInfof(ctx, "EditHardCriteriaTask| check task order count fail, order count exceed limit, request order count:%v, limit:%v", request.OrderCount, orderCountLimit)
		return editResponse, srerr.New(srerr.UpdateErr, nil, "EditHardCriteriaTask| check task order count fail, order count exceed limit, request order count:%v, limit:%v", request.OrderCount, orderCountLimit)
	}
	//3.先按照product+is multi product+order start time+order end time进行筛选，如果已经有数据了，就不允许编辑
	finalCheckTabs, fErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"id != ?":              request.TaskId,
		"product_id = ?":       checkTabs[0].ProductId,
		"is_multi_product = ?": checkTabs[0].IsMultiProduct,
		"order_start_time = ?": request.OrderStartTime,
		"order_end_time = ?":   request.OrderEndTime,
	})
	if fErr != nil {
		logger.CtxLogErrorf(ctx, "EditHardCriteriaTask| check duplicated task err:%v", fErr)
		return editResponse, srerr.New(srerr.UpdateErr, nil, "check duplicated task err:%v", fErr)
	}

	//检查是否用当前ops任务替换掉sys任务
	if len(finalCheckTabs) != 0 {
		if finalCheckTabs[0].TaskType == forecast.TaskTypeCreatedByOps {
			logger.CtxLogErrorf(ctx, "EditHardCriteriaTask| Same product and same shipment task exists(Task Id: %v)", finalCheckTabs[0].Id)
			return editResponse, srerr.New(srerr.UpdateErr, nil, "Same product and same shipment task exists(Task Id: %v)", finalCheckTabs[0].Id)
		}

		fcOrderStartTime := finalCheckTabs[0].OrderStartTime
		fcTime, tErr := time.Parse(timeutil.DateFormat, fcOrderStartTime)
		if tErr != nil {
			logger.CtxLogErrorf(ctx, "EditHardCriteriaTask| convert order start time%v, err:%v", fcOrderStartTime, tErr)
			return editResponse, srerr.New(srerr.UpdateErr, nil, "convert order start time%v, err:%v", fcOrderStartTime, tErr)
		}
		if fcTime.Unix() >= timeutil.GetNextDayStartTimeOfUTC(ctx, -7) {
			logger.CtxLogErrorf(ctx, "EditHardCriteriaTask| Same product and same shipment task exists(Task Id: %v)", finalCheckTabs[0].Id)
			return editResponse, srerr.New(srerr.UpdateErr, nil, "Same product and same shipment task exists(Task Id: %v)", finalCheckTabs[0].Id)
		}

		//系统任务距离现在超过7天了， 就可以用ops任务覆盖系统任务, 把该sys任务给删掉
		dErr := m.ForecastRepo.DeleteHardCriteriaTaskById(ctx, finalCheckTabs[0].Id, finalCheckTabs[0].TaskStatus)
		if dErr != nil {
			logger.CtxLogInfof(ctx, "EditHardCriteriaTask|task id:%v, delete draft task err:%+v", finalCheckTabs[0].Id, dErr)
			return editResponse, dErr
		}
	}

	//4.更新对应硬性校验task
	operateBy, _ := apiutil.GetUserInfo(ctx)
	tab := persistent.HardCriteriaTaskTab{
		TaskName:       request.TaskName,
		OrderStartTime: request.OrderStartTime,
		OrderEndTime:   request.OrderEndTime,
		OrderCount:     request.OrderCount,
		TaskStatus:     taskStatus,
		Operator:       operateBy,
		MTime:          recorder.Now(ctx).Unix(),
	}
	//如果此时order的start和end time与表中其它记录重复了， 会插入失败返回error
	if err := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, request.TaskId, forecast.TaskDraft, tab); err != nil {
		logger.CtxLogInfof(ctx, "EditHardCriteriaTask| update hard criteria task err:%+v", err)
		return editResponse, err
	}

	editResponse.TaskId = request.TaskId
	return editResponse, nil
}

func (m *ForecastTaskServiceImpl) QueryOrderCountByTime(ctx context.Context, request foreschema.QueryOrderCountRequest) (foreschema.QueryOrderCountResponse, *srerr.Error) {
	productId := request.ProductId
	orderStartTime := request.OrderStartTime
	orderEndTime := request.OrderEndTime
	logger.CtxLogInfof(ctx, "QueryOrderCountByTime| product id:%v, order start time:%v, order end time:%v", productId, orderStartTime, orderEndTime)

	response := foreschema.QueryOrderCountResponse{}
	hcTaskList, hcErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"id = ?": request.TaskId,
	})
	if hcErr != nil || len(hcTaskList) == 0 {
		logger.CtxLogErrorf(ctx, "Not find task or query order count get task failed:%+v,hc list is %+v", hcErr, hcTaskList)
		return response, hcErr
	}

	completionOrders := make([]foreschema.CompletionOrder, 0)
	routingType := hcTaskList[0].RoutingType
	if !configutil.GetDataApiSwitchConf(ctx).QueryOrderCountSwitch {
		resp, dataErr := m.DataApi.QueryOrderCount(ctx, request.ProductId, routingType, request.OrderStartTime, request.OrderEndTime)
		if dataErr != nil {
			logger.CtxLogErrorf(ctx, "Query order Count from data resp err:%+v,message is %s", dataErr)
			return response, srerr.New(srerr.DatabaseErr, request, "Query order Count from data resp err:%+v", dataErr)
		}
		//计算进度条前端展示
		completionOrders = m.calculateProcess(ctx, productId, request, resp, completionOrders)
	} else { // 如果开关打开就走新的api
		resp, dataErr := m.DataApi.OrderAggregationV2(ctx, dataclient.QueryOrderAggregationRequest{
			ProductId:   int(request.ProductId),
			RoutingType: uint8(routingType),
			StartDate:   request.OrderStartTime,
			EndDate:     request.OrderEndTime,
			Region:      envvar.GetCID(),
		})
		if dataErr != nil {
			logger.CtxLogErrorf(ctx, "Query order Count from data resp err:%+v,message is %s", dataErr)
			return response, srerr.New(srerr.DatabaseErr, request, "Query order Count from data resp err:%+v", dataErr)
		}
		//计算进度条前端展示
		completionOrders = m.calculateProcessV2(ctx, productId, request, resp, completionOrders)
	}

	//2.计算订单总数
	m.calculateForecastOrderCount(&response, completionOrders)

	//3.计算预估硬性校验预估完成用时
	m.calculateEstimatedTime(ctx, &response)

	return response, nil
}

func (m *ForecastTaskServiceImpl) DeleteDraftHardCriteriaTask(ctx context.Context, taskId uint64) *srerr.Error {
	//1.校验该task_id对应的状态为draft，只有draft态的才可以编辑
	checkTabs, sErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{"id = ?": taskId})
	//判空
	if len(checkTabs) == 0 {
		if sErr != nil {
			logger.CtxLogErrorf(ctx, "DeleteDraftHardCriteriaTask|get tab id, db err=%+v", sErr)
			return sErr
		}
		logger.CtxLogErrorf(ctx, "DeleteDraftHardCriteriaTask| no task found")
		return srerr.New(srerr.ParamErr, nil, "DeleteDraftHardCriteriaTask| no task found")
	}

	if checkTabs[0].TaskStatus != forecast.TaskDraft {
		logger.CtxLogInfof(ctx, "DeleteDraftHardCriteriaTask| check task type fail, task status:%v is not equal to draft:%v", checkTabs[0].TaskStatus, forecast.TaskDraft)
		return srerr.New(srerr.UpdateErr, nil, "DeleteDraftHardCriteriaTask| check task type fail, task status:%v is not equal to draft:%v", checkTabs[0].TaskStatus, forecast.TaskDraft)
	}

	//2. 删除任务
	if err := m.ForecastRepo.DeleteHardCriteriaTaskById(ctx, taskId, forecast.TaskDraft); err != nil {
		logger.CtxLogErrorf(ctx, "DeleteDraftHardCriteriaTask| delete hard criteria task err:%v", err)
		return err
	}

	logger.CtxLogInfof(ctx, "DeleteDraftHardCriteriaTask| task has been deleted, task id:%v", taskId)

	return nil
}

// 计算进度条
func (m *ForecastTaskServiceImpl) calculateProcess(ctx context.Context, productId uint64, request foreschema.QueryOrderCountRequest, resp *dataclient.QueryOrderCountResp, completionOrders []foreschema.CompletionOrder) []foreschema.CompletionOrder {
	totalCount := 0
	for _, queryOrder := range resp.Data.List {
		totalCount += queryOrder.Count
	}
	for _, queryOrder := range resp.Data.List {
		dayDate := queryOrder.Date
		volume, hcErr := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, request.TaskId, productId, dayDate, dayDate)
		if hcErr != nil {
			logger.CtxLogErrorf(ctx, "Get volume by day err: %+v", hcErr)
			continue
		}

		completionOrders = append(completionOrders, foreschema.CompletionOrder{
			OrderPaidTime:    dayDate,
			ShipmentQuantity: uint64(queryOrder.Count),
			Percentage:       math.Floor(float64(volume) / float64(totalCount) * CompletedPercentage),
		})
	}

	return completionOrders
}

// 新接口专门提供的计算进度条，如果上线后没什么问题直接删掉calculateProcess方法即可
func (m *ForecastTaskServiceImpl) calculateProcessV2(ctx context.Context, productId uint64, request foreschema.QueryOrderCountRequest, resp *dataclient.QueryOrderAggregationResponseV2, completionOrders []foreschema.CompletionOrder) []foreschema.CompletionOrder {
	totalCount := 0
	for _, queryOrder := range resp.Data.List {
		totalCount += queryOrder.Quantity
	}
	for _, queryOrder := range resp.Data.List {
		dayDate := queryOrder.OrderCreateDate
		volume, hcErr := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, request.TaskId, productId, dayDate, dayDate)
		if hcErr != nil {
			logger.CtxLogErrorf(ctx, "Get volume by day err: %+v", hcErr)
			continue
		}

		completionOrders = append(completionOrders, foreschema.CompletionOrder{
			OrderPaidTime:    dayDate,
			ShipmentQuantity: uint64(queryOrder.Quantity),
			Percentage:       math.Floor(float64(volume) / float64(totalCount) * CompletedPercentage),
		})
	}

	return completionOrders
}

func (m *ForecastTaskServiceImpl) calculateForecastOrderCount(response *foreschema.QueryOrderCountResponse, completionOrders []foreschema.CompletionOrder) {
	response.CompletionOrders = completionOrders
	//2.求和总订单数
	var total uint64
	for _, completionOrder := range completionOrders {
		total = total + completionOrder.ShipmentQuantity
	}
	response.OrderCount = total
}

func (m *ForecastTaskServiceImpl) calculateEstimatedTime(ctx context.Context, response *foreschema.QueryOrderCountResponse) {
	//3.获取硬性校验刷新qps（配置在Apollo上）,并计算预估时间
	qps := configutil.GetHardCriteriaTaskConfig(ctx).Qps
	if qps != 0 {
		response.EstimatedTime = response.OrderCount / qps / Seconds
	}

	if response.EstimatedTime == 0 {
		response.EstimatedTime = 1
	}
}

func (m *ForecastTaskServiceImpl) UpdateHardCriteriaTaskStatus(ctx context.Context, req foreschema.UpdateHCTaskStatusRequest) *srerr.Error {
	logger.CtxLogInfof(ctx, "UpdateHardCriteriaTaskStatus, req:%v", req)
	//1.获取对应的硬性校验任务记录
	tabs, err := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"id = ?": req.TaskId,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| get task by id:%v, err :%+v", req.TaskId, err)
		return err
	}
	if len(tabs) == 0 {
		logger.CtxLogInfof(ctx, "UpdateHardCriteriaTaskStatus| get empty task list by id:%v", req.TaskId)
		return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus| get empty task list by id:%v", req.TaskId)
	}
	//2.获取对应的硬性校验任务记录
	pendingTabs, err := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"task_status = ?": forecast.TaskPending,
		"task_type = ?":   forecast.TaskTypeCreatedByOps,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| get pending tasks err :%+v", err)
		return err
	}

	//2.start操作，更新为pending态，需要进行校验
	if req.TaskStatus == forecast.TaskPending {
		//3.校验是否包含已超过30天范围外的订单
		todayObj, _ := time.Parse(timeutil.DateFormat, recorder.Now(ctx).Format(timeutil.DateFormat))
		maxTimeLimit := todayObj.Unix() - forecast.OneDay*30
		startTimeObj, tErr := time.Parse(timeutil.DateFormat, tabs[0].OrderStartTime)
		if tErr != nil {
			logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| convert task order start time err:%v", tErr)
			return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus| convert task order start time err:%v", tErr)
		}
		if startTimeObj.Unix() < maxTimeLimit {
			logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus| The order time range in this task is outside the system range. Order time:%v, now time:%v", startTimeObj.Unix(), recorder.Now(ctx).Unix())
			return srerr.New(srerr.UpdateErr, nil, "The order time range in this task is outside the system range. You could create one new task.")
		}
		//4.校验pending态任务数量
		maxPendingNum := configutil.GetHardCriteriaTaskConfig(ctx).PendingNum
		if len(pendingTabs) >= maxPendingNum {
			logger.CtxLogInfof(ctx, "UpdateHardCriteriaTaskStatus| tab's length > maxSimulationDays pending num. tabs's length:%v, maxSimulationDays pending num:%v", len(tabs), maxPendingNum)
			return srerr.New(srerr.UpdateErr, nil, "The current number of processing tasks has reached the upper limit, please wait for the task to finish running or stop some task.")
		}
		//stopped -> pending 需要把task end time归零
		if req.OldStatus == forecast.TaskStopped {
			if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, tabs[0].Id, tabs[0].TaskStatus, map[string]interface{}{
				"task_end_time": 0,
			}); uErr != nil {
				logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus|id=%v, changed task status from stopped to pending, reset task end time to 0, err:%v", tabs[0].Id, uErr)
				return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus|id=%v, reset order id err:%v", tabs[0].Id, uErr)
			}
			tabs[0].TaskEndTime = 0
		}
	}

	//状态扭转时需要更新redis的数值
	//end task
	if (req.OldStatus == forecast.TaskDoing || req.OldStatus == forecast.TaskPending) &&
		req.TaskStatus == forecast.TaskTerminated {
		//清空redis数据
		if cErr := m.clearTaskCountByDays(ctx, tabs[0].Id, tabs[0].ProductId, tabs[0].OrderStartTime, tabs[0].OrderEndTime); cErr != nil {
			return cErr
		}
		//把order id更新为0
		if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, tabs[0].Id, tabs[0].TaskStatus, map[string]interface{}{
			"last_order_id": 0,
			"break_point":   "",
		}); uErr != nil {
			logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus|id=%v, reset order id err:%v", tabs[0].Id, uErr)
			return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus|id=%v, reset order id err:%v", tabs[0].Id, uErr)
		}
		tabs[0].LastOrderId = 0

		//更新task end time(如果还没有开始，就不更新结束时间)
		if tabs[0].TaskStartTime != 0 {
			tabs[0].TaskEndTime = recorder.Now(ctx).Unix()
		}
	}
	//restart task
	if isRestartFromHead(req.OldStatus, req.TaskStatus) {
		//清空redis数据
		if cErr := m.clearTaskCountByDays(ctx, tabs[0].Id, tabs[0].ProductId, tabs[0].OrderStartTime, tabs[0].OrderEndTime); cErr != nil {
			return cErr
		}
		//把order id更新为0，把task start/end time 清空
		if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, tabs[0].Id, tabs[0].TaskStatus, map[string]interface{}{
			"last_order_id":   0,
			"task_start_time": 0,
			"task_end_time":   0,
		}); uErr != nil {
			logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTaskStatus|id=%v, reset order id err:%v", tabs[0].Id, uErr)
			return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTaskStatus|id=%v, reset order id err:%v", tabs[0].Id, uErr)
		}
		//把task结构体的值也归零，防止重新写入脏数据
		tabs[0].LastOrderId = 0
		tabs[0].TaskStartTime = 0
		tabs[0].TaskEndTime = 0
	}
	//processing to stop
	if (req.OldStatus == forecast.TaskDoing || req.OldStatus == forecast.TaskPending) &&
		req.TaskStatus == forecast.TaskStopped {
		//更新task end time(如果还没有开始，就不更新结束时间)
		if tabs[0].TaskStartTime != 0 {
			tabs[0].TaskEndTime = recorder.Now(ctx).Unix()
		}
	}

	//5.更新对应硬性校验task
	operateBy, _ := apiutil.GetUserInfo(ctx)
	tabs[0].TaskStatus = req.TaskStatus
	tabs[0].Operator = operateBy
	tabs[0].MTime = recorder.Now(ctx).Unix()
	if err := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, req.TaskId, req.OldStatus, tabs[0]); err != nil {
		logger.CtxLogInfof(ctx, "UpdateHardCriteriaTaskStatus| update hard criteria task err:%+v", err)
		return err
	}

	logger.CtxLogInfof(ctx, "UpdateHardCriteriaTaskStatus| update hard criteria task status done, req:%+v", req)

	return nil
}

func (m *ForecastTaskServiceImpl) GetHardCriteriaTaskList(ctx context.Context, request foreschema.GetHCTaskListRequest) (foreschema.GetHCTaskListResponse, *srerr.Error) {
	var response foreschema.GetHCTaskListResponse
	//1.校验page_no的合法性
	if request.PageNo < 1 {
		logger.CtxLogInfof(ctx, "GetHardCriteriaTaskList| check pageno fail, pageno:%v is less than 1", request.PageNo)
		return response, srerr.New(srerr.ParamErr, nil, "GetHardCriteriaTaskList| check pageno fail, pageno:%v is less than 1", request.PageNo)
	}
	//2.获取硬性校验任务列表
	tabs, total, err := m.ForecastRepo.GetHardCriteriaTaskList(ctx, request)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList| get tabs from db err:%+v", err)
		return response, err
	}
	//3.转换成对应的entity
	var taskList []foreschema.GetHCTaskList
	for _, tab := range tabs {
		singleReturnObj := foreschema.GetHCTaskList{
			TaskType:       uint8(tab.TaskType),
			TaskId:         tab.Id,
			TaskName:       tab.TaskName,
			ProductId:      tab.ProductId,
			IsMultiProduct: tab.IsMultiProduct,
			TaskStatus:     uint8(tab.TaskStatus),
			OrderStartTime: tab.OrderStartTime,
			OrderEndTime:   tab.OrderEndTime,
			OrderCount:     uint64(tab.OrderCount),
			TaskStartTime:  uint32(tab.TaskStartTime),
			TaskEndTime:    uint32(tab.TaskEndTime),
			Operator:       tab.Operator,
			Ctime:          uint64(tab.CTime),
		}
		//获取redis中对应的值
		finishedNum, err := m.GetFinishedHCTaskVolume(ctx, forecast.PrefixAllDay, tab.Id, tab.ProductId, tab.OrderStartTime, tab.OrderEndTime)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList| get volume err:%+v", err)
		}
		if tab.OrderCount != 0 {
			singleReturnObj.TaskProgress = math.Floor(float64(finishedNum) / float64(tab.OrderCount) * 100)
		}
		// 如果进度条是done直接展示100%，防止出现某些订单硬性校验失败进度条非100%展示，但状态是done
		if (tab.RoutingType == rule.LocalRoutingType || tab.RoutingType == rule.SPXRoutingType) && tab.TaskStatus == forecast.TaskDone {
			singleReturnObj.TaskProgress = completionProgress
		}
		taskList = append(taskList, singleReturnObj)
	}

	response.TaskList = taskList
	response.Total = total
	response.PageNo = request.PageNo
	response.PageCount = request.PageCount

	return response, nil
}

// RefreshHardCriteriaByTask 定时根据硬性校验刷新任务（存在db中的任务记录）来进行硬性校验刷新
func (m *ForecastTaskServiceImpl) RefreshHardCriteriaByTask(ctx context.Context, req foreschema.ExecuteHCTaskRequest) {
	monitorCtx := monitoring.WithTransactionMsg(ctx)
	//1.遍历获取pending态硬性校验任务，越早被更新成pending态的任务越早开始
	hcTaskList, gErr := m.ForecastRepo.GetHardCriteriaTaskByLastUpdateTime(ctx,
		map[string]interface{}{
			"task_status = ?":  forecast.TaskPending,
			"task_type = ?":    req.TaskType,
			"order_count >= ?": req.MinOrderCount,
			"order_count <= ?": req.MaxOrderCount,
		},
	)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| get 'pending' hard criteria task list err:%+v", gErr)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, gErr.Error())
		return
	}
	//最大pending数在创建时控制，这里不需要进行判断
	//遍历任务列表
	//每轮只抢一个task，拿到一个或全拿不到，才退出hcTaskList循环
	var hcTask persistent.HardCriteriaTaskTab
	for _, tempHCTask := range hcTaskList {
		//1.修改任务状态
		tempHCTask.TaskStatus = forecast.TaskDoing
		tempHCTask.TaskStartTime = recorder.Now(ctx).Unix()  //更新task起止时间
		tempHCTask.LastUpdateTime = recorder.Now(ctx).Unix() //更新心跳检查时间
		tempHCTask.TaskEndTime = 0
		if uErr := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, tempHCTask.Id, forecast.TaskPending, tempHCTask); uErr != nil {
			logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask|task id%v, update task status to 'doing' err:%+v", tempHCTask.Id, uErr)
			monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, uErr.Error())
			continue
		}
		//reset task_end_time to '0'
		if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, tempHCTask.Id, tempHCTask.TaskStatus, map[string]interface{}{
			"task_end_time": 0,
		}); uErr != nil { //got error, but don't ternimate the process
			logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask|task id%v, update task end time to '0' err:%+v", tempHCTask.Id, uErr)
			monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, fmt.Sprintf("task id%v, update task end time to '0' err:%+v", tempHCTask.Id, uErr))
		}

		hcTask = tempHCTask
		break
	}

	// 一个任务都没有的情况下，不需要执行直接返回
	if hcTask.Id == 0 {
		logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask| hard criteria task id is 0, stop refresh")
		return
	}

	//修改任务状态成功，开始执行
	logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask| set task to 'doing' success, task id:%v", hcTask.Id)
	//2.获取起始订单id等初始化数据
	var (
		orderId        = hcTask.LastOrderId
		productId      = hcTask.ProductId
		orderStartTime = hcTask.OrderStartTime
		orderEndTime   = hcTask.OrderEndTime
	)

	//获取日期列表
	days, dErr := m.parseDayList(orderStartTime, orderEndTime)
	if dErr != nil {
		logger.CtxLogErrorf(ctx, "parse start time err:%+v| order id:%v", dErr, orderId)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, dErr.Error())
		return
	}

	if len(days) == 0 {
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| get empty days list, order start time:%v, order end time:%v", orderStartTime, orderEndTime)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, fmt.Sprintf("get empty day list, order start time:%v, order end time:%v", orderStartTime, orderEndTime))
		return
	}

	//order id = 0, 非断点续做，清空redis值,spx和local断点续做与cb不一样，不需要清楚redis的值
	if orderId == 0 && hcTask.RoutingType != rule.LocalRoutingType && hcTask.RoutingType != rule.SPXRoutingType {
		logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask|task id=%v, find order id=0, start to clear finished num in redis", hcTask.Id)
		if cErr := m.ClearFinishedHCTaskVolume(ctx, forecast.PrefixAllDay, hcTask.Id, productId, orderStartTime, orderEndTime); cErr != nil {
			logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| clear finished hard criteria task volume in redis err:%+v｜task id:%v", cErr, hcTask.Id)
			monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, cErr.Error())
			return
		}

		for _, day := range days {
			if cErr := m.ClearFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, hcTask.Id, productId, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat)); cErr != nil {
				logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| clear finished hard criteria task volume in redis err:%+v｜start date:%v, end date:%v", cErr, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat))
				monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, cErr.Error())
				return
			}
		}
	}

	salt := 0
	//3.先判断是否是断点续做
	// 将Days推送Redis队列中
	// 1.获取断点的硬性校验日期列表以及断点记录的salt
	remainDays, remainSalt, err := m.getRemainDays(ctx, hcTask)
	if err != nil {
		HardCriteriaMonitorReport(ctx, BreakPoint, int(hcTask.Id), fmt.Sprintf("remainDays err=%v", err), false)
		return
	}
	// 2.如果获取剩余为执行的硬性校验天数则直接用剩余的继续做校验
	if remainDays != nil {
		days = remainDays
		salt = remainSalt
		HardCriteriaMonitorReport(ctx, BreakPoint, int(hcTask.Id), fmt.Sprintf("remainDays=%v,remainSalt=%d", remainDays, remainSalt), true)
	}

	logger.CtxLogInfof(ctx, "remain days is %v,salt =%v,days =%v", remainDays, remainSalt, days)

	//获取单任务单携程每一批次校验数量
	batchNum := configutil.GetHardCriteriaTaskConfig(ctx).BatchNum
	//遍历日期列表
	for i := len(days) - 1; i >= 0; i = i - 1 {
		var (
			day              = days[i]
			routingLogOrders chan RoutingLogRowKeyMap
			readErr          *srerr.Error
		)

		if !configutil.GetDataApiSwitchConf(ctx).ReadOrderFromDataApiSwitch {
			routingLogOrders, readErr = m.GetForecastOrdersFromHbase(ctx, salt, hcTask.Id, int(productId), hcTask.RoutingType, batchNum, day)
			if readErr != nil {
				logger.CtxLogInfof(ctx, "read order from hbase failed | day: %v ,err: %v", day, readErr)
				continue
			}
		} else {
			routingLogOrders, readErr = m.GetForecastOrderFromDataApi(ctx, int(hcTask.Id), int(productId), hcTask.RoutingType, salt, day)
			if readErr != nil {
				logger.CtxLogInfof(ctx, "read order from data api failed | day: %v ,err: %v", day, readErr)
				continue
			}
		}

		m.doHardCheckByHbaseOrder(ctx, hcTask, day, hcTask.IsMultiProduct, routingLogOrders)
		logger.CtxLogInfof(ctx, "task id:%v, day:%v, refresh order hard result done", hcTask.Id, day)

		// 检查如果硬性校验状态不正常则停止（包括获取Task失败+非Doing状态）
		if isDoing := m.checkHardCriteriaTaskIsDoing(ctx, hcTask.Id); !isDoing {
			logger.CtxLogErrorf(ctx, "hard criteria task is not doing status, break the task")
			return
		}
		// 将salt标清0
		salt = 0
	}

	beginTime := recorder.Now(ctx).Unix()
	if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, hcTask.Id, forecast.TaskDoing, map[string]interface{}{
		"last_order_id": 0,
		"break_point":   "",
	}); uErr != nil {
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| update order id to '0' err:%+v| task id:%v", uErr, hcTask.Id)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, uErr.Error())
		return
	}

	hcTask.TaskStatus = forecast.TaskDone
	hcTask.TaskEndTime = recorder.Now(ctx).Unix()
	hcTask.LastStartTime = hcTask.TaskStartTime
	hcTask.LastEndTime = hcTask.TaskEndTime
	hcTask.LastUpdateTime = recorder.Now(ctx).Unix()
	hcTask.LastOrderId = 0
	hcTask.BreakPoint = ""
	//再次赋值，避免重新写入
	if uErr := m.ForecastRepo.UpdateHardCriteriaTaskById(ctx, hcTask.Id, forecast.TaskDoing, hcTask); uErr != nil { // task更新失败,打印日志并上报
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| update task to 'done' err:%+v| task id:%v", uErr, hcTask.Id)
		monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusError, uErr.Error())
	}
	logger.CtxLogInfof(ctx, "RefreshHardCriteriaByTask|task id:%v, success to update task in db, cost time:%+v", hcTask.Id, recorder.Now(ctx).Unix()-beginTime)
	monitor.AwesomeReportTransactionEnd(monitorCtx, monitoring.CatModuleTask, constant.TaskNameExecutedHardCriteria, monitoring.StatusSuccess, fmt.Sprintf("hard criteria task:%+v", hcTask))
}

func newRateLimiter(ctx context.Context) ratelimit.Limiter {
	//用的是值类型不会出现空指针
	limitSize := configutil.GetRateLimitConfig(ctx).LimitSize[configutil.HardCriteriaLimitKey]
	return ratelimit.New(limitSize)
}

// 断点续作，获取上一次结束的天数以及salt,上报cat统一包装再getRemainDays使用的地方
func (m *ForecastTaskServiceImpl) getRemainDays(ctx context.Context, task persistent.HardCriteriaTaskTab) ([]time.Time, int, *srerr.Error) {
	// 1.从redis中获取StartRowKey
	startRowKey, err := m.getStartRowKey(ctx, int(task.Id))
	if err != nil || startRowKey == "" {
		return nil, 0, err
	}
	// 2.解析StartRowKey
	unixTime, salt, perr := parseStartRowKey(ctx, startRowKey)
	if perr != nil {
		return nil, 0, err
	}
	// 3.将硬性校验对应订单的开始时间转换成当地时间,因为硬性校验是从后往前执行的所以这里拿的是开始时间
	orderStartTime, oerr := timeutil.ParseDateStr(task.OrderStartTime)
	if oerr != nil {
		logger.CtxLogErrorf(ctx, "Parse order start time err=%v", oerr)
		return nil, 0, srerr.New(srerr.ParamErr, task.OrderStartTime, "Parse order start time err=%v", oerr)
	}
	// 4.转换成对应的日期列表
	return getDaysList(orderStartTime, timeutil.TransferTimeStampToTime(unixTime)), salt, nil
}

func parseStartRowKey(ctx context.Context, startRowKey string) (int64, int, *srerr.Error) {
	startRowKeyParts := strings.Split(startRowKey, separator)
	// 1.判断一下根据"_"拆的StarRowKey是否由4部分组成
	if len(startRowKeyParts) != startRowKeyLength {
		logger.CtxLogErrorf(ctx, "Parse StartRowKey failed startRowKey=%s,the startRowKey length is not 4", startRowKey)
		return 0, 0, srerr.New(srerr.FormatErr, startRowKey, "Parse StartRowKey failed startRowKey=%s,the startRowKey length is not 4", startRowKey)
	}
	// 2.从startRowKey中获取上次终止在那一天
	unixTime, uerr := strconv.ParseInt(startRowKeyParts[3], 10, 64)
	if uerr != nil {
		logger.CtxLogErrorf(ctx, "Parse StartRowKey to unix time err=%v", uerr)
		return 0, 0, srerr.New(srerr.FormatErr, startRowKey, "Parse StartRowKey to unix time err=%v", uerr)
	}
	// 3.从startRowKey中获取上次终止在那一个salt
	salt, uerr := strconv.Atoi(startRowKeyParts[0])
	if uerr != nil {
		logger.CtxLogErrorf(ctx, "Parse StartRowKey to salt err=%v", uerr)
		return 0, 0, srerr.New(srerr.FormatErr, startRowKey, "Parse StartRowKey to salt err=%v", uerr)
	}
	return unixTime, salt, nil
}

// CreateHCTaskBySystem Saturn定时创建硬性校验任务
func (m *ForecastTaskServiceImpl) CreateHCTaskBySystem(ctx context.Context) {
	//1.获取当前region下所有开启了smart routing功能的product
	routingConfigs, err := m.RoutingConfService.ListAllRoutingConfig(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "CreateHCTaskBySystem| get routing config fail")
		return
	}
	if len(routingConfigs) == 0 {
		logger.CtxLogInfof(ctx, "CreateHCTaskBySystem| get empty routing config")
		return
	}
	//2.获取最近七天的日期列表
	currentTimeStamp := recorder.Now(ctx).Unix()
	weekAgoTimeStart := time.Unix(currentTimeStamp-forecast.OneDay*7, 0) //前第七天的时间obj
	weekAgoTimeEnd := time.Unix(currentTimeStamp-forecast.OneDay, 0)     //前一天的时间obj
	days := getDaysList(weekAgoTimeStart, weekAgoTimeEnd)
	if len(days) == 0 {
		logger.CtxLogErrorf(ctx, "CreateHCTaskBySystem| get empty days list, start time:%v, end time:%v", weekAgoTimeStart, weekAgoTimeEnd)
		return
	}
	//3.遍历渠道列表，然后从昨天到前第七天遍历日期列表，创建任务
	yesterdayTime := configutil.GetHardCriteriaTaskConfig(ctx).CreateYesterday
	for _, routingConfig := range routingConfigs {
		if !IsSupportSmartRouting(routingConfig) {
			continue
		}
		productId := int(routingConfig.ProductID)
		for i := len(days) - 1; i >= 0; i = i - 1 {
			day := days[i]
			var tab *persistent.HardCriteriaTaskTab
			//创建cb硬性校验任务，
			if routingConfig.SmartRoutingEnabled {
				tab = m.createHardCriteriaTask(ctx, productId, rule.CBRoutingType, yesterdayTime, i == len(days)-1, day)
			}

			//分别创建spx和local的预测任务
			if routingConfig.LocalSmartRoutingEnabled {
				tab = m.createHardCriteriaTask(ctx, productId, rule.LocalRoutingType, yesterdayTime, i == len(days)-1, day)
			}

			if routingConfig.SpxSmartRoutingEnabled {
				tab = m.createHardCriteriaTask(ctx, productId, rule.SPXRoutingType, yesterdayTime, i == len(days)-1, day)
			}
			if tab != nil {
				logger.CtxLogInfof(ctx, "create hc task %+v	", tab)
			}
		}
	}
}

func (m *ForecastTaskServiceImpl) createHardCriteriaTask(ctx context.Context, productId int, routingType int, yesterdayTime int, isYesterday bool, day time.Time) *persistent.HardCriteriaTaskTab {
	condition := map[string]interface{}{
		"product_id = ?":       productId,
		"order_start_time = ?": day.Format("2006-01-02"),
		"order_end_time = ?":   day.Format("2006-01-02"),
		"routing_type = ?":     routingType,
	}
	tabs, err := m.ForecastRepo.GetHardCriteriaTask(ctx, condition)
	if err != nil {
		HardCriteriaMonitorReport(ctx, monitoring.CreateHardCriteria, routingType, fmt.Sprintf("CreateHCTaskBySystem| check if task existed err:%v", err), Failed)
		logger.CtxLogErrorf(ctx, "CreateHCTaskBySystem| check if task existed err:%v", err)
		return nil
	}
	if len(tabs) != 0 {
		HardCriteriaMonitorReport(ctx, monitoring.CreateHardCriteria, routingType, fmt.Sprintf("CreateHCTaskBySystem| task already exist, task id:%v", tabs[0].Id), Success)
		logger.CtxLogInfof(ctx, "CreateHCTaskBySystem| task already exist, task id:%v", tabs[0].Id)
		return nil
	}

	//check whether it is time to create yesterday's task, cb的才需要校验，local和spx都是实时写入到hbase
	if routingType == rule.CBRoutingType && isYesterday && (recorder.Now(ctx).Hour() < yesterdayTime) {
		HardCriteriaMonitorReport(ctx, monitoring.CreateHardCriteria, routingType, fmt.Sprintf("CreateHCTaskBySystem|now time hour:%v, yesterday time limit:%v, time is not allowed to create task", recorder.Now(ctx).Hour(), yesterdayTime), Success)
		logger.CtxLogInfof(ctx, "CreateHCTaskBySystem|now time hour:%v, yesterday time limit:%v, time is not allowed to create task", recorder.Now(ctx).Hour(), yesterdayTime)
		return nil
	}
	//3.1 创建任务
	dayTime := day.Format(timeutil.DateFormat)
	tab := persistent.HardCriteriaTaskTab{
		ProductId:      uint64(productId),
		TaskName:       fmt.Sprintf("Sys_%d_%s", productId, dayTime),
		TaskType:       forecast.TaskTypeCreatedBySystem,
		OrderStartTime: dayTime,
		OrderEndTime:   dayTime,
		TaskStatus:     forecast.TaskPending,
		Operator:       "System",
		RoutingType:    routingType,
		CTime:          recorder.Now(ctx).Unix(),
		MTime:          recorder.Now(ctx).Unix(),
	}

	if routingType == rule.CBRoutingType {
		tab.IsMultiProduct = true
	}

	// 3.2 获取该日下该渠道的订单数
	if !configutil.GetDataApiSwitchConf(ctx).QueryOrderCountSwitch {
		if m.setHCOrderCountFromHbase(ctx, &tab); tab.OrderCount == 0 {
			HardCriteriaMonitorReport(ctx, monitoring.QueryHardCriteriaOrderCount, routingType, "get order count is 0,skip this task", Success)
			logger.CtxLogInfof(ctx, "get order count is 0,skip this task")
			return nil
		}
	} else {
		if m.setHCOrderCountFromHbaseV2(ctx, &tab); tab.OrderCount == 0 {
			HardCriteriaMonitorReport(ctx, monitoring.QueryHardCriteriaOrderCount, routingType, "get order count is 0,skip this task", Success)
			logger.CtxLogInfof(ctx, "get order count is 0,skip this task")
			return nil
		}
	}

	//如果此时order的start和end time与表中其它记录重复了， 会插入失败返回error
	if err := m.ForecastRepo.CreateHardCriteriaTask(ctx, &tab); err != nil {
		HardCriteriaMonitorReport(ctx, monitoring.CreateHardCriteria, routingType, fmt.Sprintf("create hard criteria failed %v", err), Success)
		logger.CtxLogInfof(ctx, "CreateHCTaskBySystem| create hard criteria by system task err:%+v", err)
		return nil
	}

	return &tab
}

func (m *ForecastTaskServiceImpl) setHCOrderCountFromHbase(ctx context.Context, task *persistent.HardCriteriaTaskTab) {
	resp, err := m.DataApi.QueryOrderCount(ctx, task.ProductId, int(task.RoutingType), task.OrderStartTime, task.OrderEndTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get total order count failed:%+v, task id is %d,", err, task.Id)
		return
	}
	total := 0
	for _, data := range resp.Data.List {
		total += data.Count
	}

	task.OrderCount = int64(total)
}

func (m *ForecastTaskServiceImpl) setHCOrderCountFromHbaseV2(ctx context.Context, task *persistent.HardCriteriaTaskTab) {
	resp, err := m.DataApi.OrderAggregationV2(ctx, dataclient.QueryOrderAggregationRequest{
		ProductId:   int(task.ProductId),
		RoutingType: uint8(task.RoutingType),
		StartDate:   task.OrderStartTime,
		EndDate:     task.OrderEndTime,
		Region:      envvar.GetCID(),
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get total order count failed:%+v, task id is %d,", err, task.Id)
		return
	}
	total := 0
	for _, data := range resp.Data.List {
		total += data.Quantity
	}

	task.OrderCount = int64(total)
}

func IsSupportSmartRouting(r *ruledata.RoutingConfigTab) bool {
	return r.SmartRoutingEnabled || r.LocalSmartRoutingEnabled || r.SpxSmartRoutingEnabled || r.CBMultiRoutingEnabled || r.IlhSmartRoutingEnabled
}

// CheckHCTask 校验task的状态
func (m *ForecastTaskServiceImpl) CheckHCTask(ctx context.Context) {
	//1.get tasks in 'doing'&'done' status
	statusList := []int{forecast.TaskDoing, forecast.TaskDone} //list for 'doing', 'done' status
	unixWith15Days := timeutil.GetNextDayStartTime(ctx, -15)
	tasks, err := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"task_status in (?)": statusList,
		"ctime >= ?":         unixWith15Days,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "CheckHCTask| get tasks in 'doing' err:%v", err)
		return
	}
	//2.split tasks into 'doingTasks'&'doneTasks'
	var doingTasks []persistent.HardCriteriaTaskTab
	var doneTasks []persistent.HardCriteriaTaskTab
	for _, task := range tasks {
		if task.TaskStatus == forecast.TaskDoing {
			doingTasks = append(doingTasks, task)
			continue
		}

		if task.TaskStatus == forecast.TaskDone {
			doneTasks = append(doneTasks, task)
		}
	}
	//3.check 'doingTasks'
	m.checkTaskStatus(ctx, forecast.TaskDoing, doingTasks)
	//4.check 'doneTasks'
	m.checkTaskStatus(ctx, forecast.TaskDone, doneTasks)

	//5.获取pending态任务，并判断order time是否超过7天，超过则将其置为done
	pendingTask, err := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{
		"task_status = ?": forecast.TaskPending,
		"task_type = ?":   forecast.TaskTypeCreatedBySystem,
	})
	if err != nil {
		logger.CtxLogErrorf(ctx, "CheckHCTask| get tasks in 'pending' err:%v", err)
		return
	}
	//加个开关控制，以防止上线后出问题不能改降级，默认"不禁止"
	forbidCheckDonePending := configutil.GetForbidCheckDonePending(ctx)
	if !forbidCheckDonePending {
		m.checkPendingTask(ctx, pendingTask)
	}
}

func (m *ForecastTaskServiceImpl) UpdateHardCriteriaTask(ctx context.Context, req foreschema.UpdateHCTaskRequest) *srerr.Error {
	operateBy, _ := apiutil.GetUserInfo(ctx)
	logger.CtxLogInfof(ctx, "UpdateHardCriteriaTask| operator:%v", operateBy)
	tabs, gErr := m.ForecastRepo.GetHardCriteriaTask(ctx, map[string]interface{}{"id = ?": req.Id})
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTask|task id:%v, get task err:%v", req.Id, gErr)
		return gErr
	}
	if len(tabs) == 0 {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTask|task id:%v, task not found", req.Id)
		return srerr.New(srerr.UpdateErr, nil, "UpdateHardCriteriaTask|task id:%v, task not found", req.Id)
	}
	condition := map[string]interface{}{}
	if req.TaskStatus != 0 {
		condition["task_status"] = req.TaskStatus
	}
	if req.OrderStartTime != "" {
		condition["order_start_time"] = req.OrderStartTime
	}
	if req.OrderEndTime != "" {
		condition["order_end_time"] = req.OrderEndTime
	}
	if req.LastOrderId != 0 {
		condition["last_order_id"] = req.LastOrderId
	}
	if req.LastEndTime != 0 {
		condition["last_end_time"] = req.LastEndTime
	}
	if req.LastOrderTime != 0 {
		condition["last_order_time"] = req.LastOrderTime
	}
	if req.TaskName != "" {
		condition["task_name"] = req.TaskName
	}
	if req.OrderCount != 0 {
		condition["order_count"] = req.OrderCount
	}

	if uErr := m.ForecastRepo.UpdateHardCriteriaWithDefault(ctx, req.Id, tabs[0].TaskStatus, condition); uErr != nil {
		logger.CtxLogErrorf(ctx, "UpdateHardCriteriaTask|task id:%v, update task err:%v", req.Id, uErr)
		return uErr
	}
	return nil
}

func (m *ForecastTaskServiceImpl) GetForecastTaskName(ctx context.Context, id int) (string, *srerr.Error) {
	result, err := m.ForecastRepo.GetTaskById(ctx, id)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetForecastTaskName|forecast task id:%v, get task err:%v", id, err)
		return "", err
	}
	return result.TaskName, nil
}

func (m *ForecastTaskServiceImpl) GetForecastOrdersFromHbase(ctx context.Context, index int, taskId uint64, productId, routingType, readLimit int, day time.Time) (chan RoutingLogRowKeyMap, *srerr.Error) {
	chs := make(chan RoutingLogRowKeyMap, 1000)

	go func(ctx context.Context, index int, taskId uint64, productId, routingType, readLimit int, day time.Time) {
		defer func() {
			if err := recover(); err != nil {
				var buf [4096]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, "Hard criteria get order form hbase err %s", errMsg)
			}
		}()
		defer func() {
			close(chs)
		}()

		scannedOrderCount := 0
		resCh := m.doGetHCForecastOrdersFromHbase(ctx, int(taskId), index, productId, routingType, readLimit, day)
		for log := range resCh {
			for _, cell := range log.Cells {
				logEntry := decodeRoutingLog(ctx, int(taskId), cell.Value[:])
				rowKeyMap := RoutingLogRowKeyMap{RowKey: string(cell.Row), LogEntry: logEntry}
				chs <- rowKeyMap
			}
			scannedOrderCount++
		}
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaMonitor, int(taskId), fmt.Sprintf("day=%v total order count %d", day, scannedOrderCount), Success)
		logger.CtxLogInfof(ctx, "day=%v,taskId=%v,scan order %v", day, taskId, scannedOrderCount)
	}(ctx, index, taskId, productId, routingType, readLimit, day)

	return chs, nil
}

func (m *ForecastTaskServiceImpl) GetForecastOrderFromDataApi(ctx context.Context, taskId, productId, routingType, page int, day time.Time) (chan RoutingLogRowKeyMap, *srerr.Error) {
	chs := make(chan RoutingLogRowKeyMap, 1000)
	if configutil.ForecastSwitch() {
		go m.GetForecastOrderFromClickHouse(ctx, taskId, productId, routingType, page, day, chs)
		return chs, nil
	}
	// 1.查询对应的日期的订单总数
	orderByDay, err := m.GetOrderByDayFromDataApi(ctx, productId, routingType, minPageSize, minPage, day)
	if err != nil {
		return nil, err
	}
	// 2.计算页总数
	totalPage := int(math.Ceil(float64(orderByDay.TotalSize) / float64(pageSize)))
	go func() {
		defer func() {
			close(chs)
			if err := recover(); err != nil {
				var buf [4096]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery] panic recovered:\n%s\n%s", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, "Hard criteria get order form hbase err %s", errMsg)
			}
		}()
		// 3.分页读取数据
		if page == 0 {
			// 没有断点续做的情况下page拿到的是0，则从min page开始获取数据
			page = minPage
		}
		for i := page; i <= totalPage; i++ {
			if m.checkTaskStatusAndRecordBreakpoint(ctx, taskId, generateRowKey(page, productId, routingType, day.Unix())) {
				break
			}
			orderListByDay, err := m.GetOrderByDayFromDataApi(ctx, productId, routingType, pageSize, i, day)
			if err != nil {
				monitoring.ReportError(ctx, monitoring.HardCriteriaMonitor, monitoring.DataApiMonitor, fmt.Sprintf("Get order by day err=%v", err))
				continue
			}
			// 4.遍历数组将order反序列化放到ch中
			for _, orderDetail := range orderListByDay.OrderDetailList {
				order, _, err := routinglogutil.DecodeRoutingLog(ctx, orderDetail.MessageBody)
				if err != nil {
					monitoring.ReportError(ctx, monitoring.HardCriteriaMonitor, monitoring.DataApiMonitor, fmt.Sprintf("Decode message err=%v", err))
					continue
				}
				chs <- RoutingLogRowKeyMap{
					RowKey:   "",
					LogEntry: order,
				}
			}
		}
	}()

	return chs, nil
}

func (m *ForecastTaskServiceImpl) GetForecastOrderFromClickHouse(ctx context.Context, taskId, productId, routingType, page int, day time.Time, chs chan RoutingLogRowKeyMap) {
	defer close(chs)
	// 1.先查询当天有多少订单
	totalCountByDay, totalCountErr := m.DataApi.QueryOrderCountByDay(ctx, productId, routingType, timeutil.FormatDate(day), timeutil.FormatDate(day))
	if totalCountErr != nil {
		return
	}
	if len(totalCountByDay) < 1 {
		logger.CtxLogInfof(ctx, "query order count from clickhouse is zero %s", timeutil.FormatDate(day))
		return
	}
	// 2.计算出页总数
	totalPage := int(math.Ceil(float64(totalCountByDay[0].OrderCount) / float64(pageSize)))

	//if page == 0 { // 这里用的 hash(forderid)%totalPage作为分页
	//	// 没有断点续做的情况下page拿到的是0，则从min page开始获取数据
	//	page = minPage
	//}
	// 3.分页读取数据
	for i := page; i < totalPage; i++ {
		// 3.1 从clickhouse中查询当页数据
		orderDetailList, err := m.DataApi.QueryOrderDetailByPage(ctx, productId, routingType, timeutil.FormatDate(day), totalPage, i)
		if err != nil {
			logger.CtxLogInfof(ctx, "query order detail from clickhouse is zero %v", err)
			return
		}

		for _, orderDetail := range orderDetailList {
			if m.checkTaskStatusAndRecordBreakpoint(ctx, taskId, generateRowKey(i, productId, routingType, day.Unix())) {
				break
			}
			// 解压订单数据
			order, _, err := routinglogutil.DecodeRoutingLog(ctx, orderDetail)
			if err != nil {
				monitoring.ReportError(ctx, monitoring.HardCriteriaMonitor, monitoring.DataApiMonitor, fmt.Sprintf("Decompress message err=%v", err))
				continue
			}
			chs <- RoutingLogRowKeyMap{
				RowKey:   "",
				LogEntry: order,
			}
		}
	}
}

// 先用一条数据查询每天的订单数数量

func (m *ForecastTaskServiceImpl) GetOrderByDayFromDataApi(ctx context.Context, productId, routingType, pageCount, pageOffset int, day time.Time) (*dataclient.QueryOrderDetailRespDetail, *srerr.Error) {
	req := dataclient.QueryOrderDetailReq{
		ProductId:   productId,
		RoutingType: routingType,
		StartDate:   timeutil.FormatDate(day),
		EndDate:     timeutil.FormatDate(day),
		Region:      envvar.GetCID(),
		PageCount:   pageCount,
		PageOffset:  pageOffset,
	}

	resp, err := m.DataApi.OrderOrderDetail(ctx, req)
	if err != nil { // todo 上报监控
		logger.CtxLogErrorf(ctx, "day=%v hard criteria query total order count by day failed | err=%v", day, err)
		return nil, err
	}

	if !resp.IsSuccess() {
		errMsg := fmt.Sprintf("day=%v, query order detail from data unsuccessful | response err: %s", day, resp.Message)
		logger.CtxLogErrorf(ctx, errMsg)
		return nil, srerr.New(srerr.DataApiErr, req, errMsg)
	}

	return &resp.Data, nil
}

func decodeRoutingLog(ctx context.Context, taskId int, logBytes []byte) routing_log.RoutingLog {
	logEntry := routing_log.RoutingLog{}
	logByteArr, zstdErr := zip.ZSTDDecompress(logBytes)
	if zstdErr != nil {
		logger.CtxLogErrorf(ctx, "decoder routing log from hbase %+v", zstdErr)
		HardCriteriaMonitorReport(ctx, monitoring.DecodeFailed, taskId, fmt.Sprintf("decoder routing log from hbase %+v", zstdErr), Failed)
	}

	if err := jsoniter.Unmarshal(logByteArr, &logEntry); err != nil {
		logger.CtxLogErrorf(ctx, "Unmarshal routing log from hbase %+v", err)
		HardCriteriaMonitorReport(ctx, monitoring.DecodeFailed, taskId, fmt.Sprintf("Unmarshal routing log from hbase %+v", err), Failed)
	}

	return logEntry
}

func (m *ForecastTaskServiceImpl) doGetHCForecastOrdersFromHbase(ctx context.Context, taskId, index, productId, routingType, readLimit int, day time.Time) chan *gohbase.Result {
	salt := 1000
	hbConf := configutil.GetDataHbaseConfig(ctx)
	tableName := hbConf.TableNameMap[masking_forecast_hbase.ROUTING_LOG]
	resCh := make(chan *gohbase.Result)

	go func() {
		defer close(resCh)
		for i := index; i < salt; i++ {
			startKey := generateRowKey(i, productId, routingType, day.Unix())
			endKey := generateRowKey(i, productId, routingType, day.AddDate(0, 0, 1).Unix())
			if m.checkTaskStatusAndRecordBreakpoint(ctx, taskId, startKey) { // 校验如果状态是stop/End
				break
			}
			err := m.hbHelper.ScanByIndexWithChannel(ctx, tableName, startKey, endKey, uint32(readLimit), resCh, nil)
			if err != nil {
				logger.CtxLogErrorf(ctx, "Get hc data from hbase failed %v", err)
				HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaScanHbaseIndex, taskId, fmt.Sprintf("current index %d,Get hc data from hbase failed %v", index, err), Failed)
			}
		}
	}()

	return resCh
}

func generateRowKey(salt, productId, routingType int, datetime int64) string {
	return fmt.Sprintf("%03d_%d_%d_%d", salt, productId, routingType, datetime)
}

func (m *ForecastTaskServiceImpl) IncrFinishedHCTaskVolume(ctx context.Context, keyType string, taskID uint64, productID uint64, orderStartTime string, orderEndTime string, value int64) *srerr.Error {
	beginTime := recorder.Now(ctx).Unix()
	key := GenFinishedHCTaskKey(keyType, taskID, productID, orderStartTime, orderEndTime)

	_, e := redisutil.GetDefaultInstance().IncrBy(ctx, key, value).Result()
	if e != nil {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaRedis, int(taskID), fmt.Sprintf("Get redis failed %v", e), Failed)
		return srerr.With(srerr.CodisErr, productID, e)
	}
	content, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	logger.CtxLogInfof(ctx, "IncrFinishedHCTaskVolume| redis key:%v, result:%v, cost time:%v", key, content, recorder.Now(ctx).Unix()-beginTime)
	if err != nil {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaRedis, int(taskID), fmt.Sprintf("incr hard criteria order count err=%v", err), Failed)
		return srerr.With(srerr.CodisErr, productID, err)
	}

	return nil
}

func (m *ForecastTaskServiceImpl) doHardCheckByHbaseOrder(ctx context.Context, task persistent.HardCriteriaTaskTab, day time.Time, isMultiProduct bool, orderLogs chan RoutingLogRowKeyMap) {
	if orderLogs == nil {
		return
	}

	poolConfig := configutil.GetAntPoolConfig(ctx)
	// 异步任务链路，阻塞式
	pool, pErr := ants.NewPool(poolConfig.PoolSize, ants.WithPanicHandler(HardCriteriaPanicHandler))
	if pErr != nil {
		logger.CtxLogErrorf(ctx, "init hard criteria goroutine pool failed:%v", pErr)
		return
	}

	defer func() {
		if pool != nil {
			pool.Release()
		}
	}()

	wg := &sync.WaitGroup{}
	hcRateLimiter := newRateLimiter(ctx)

	criteriaOrderCount := 0
	for rowKeyMap := range orderLogs {
		orderLog := rowKeyMap.LogEntry
		rowKey := rowKeyMap.RowKey

		wg.Add(1)
		if err := pool.Submit(func() {
			defer wg.Done()
			criteriaOrderCount++
			//做一下限流处理
			hcRateLimiter.Take()
			//生成新的ctx
			newCtx := newContext(ctx, orderLog, int(task.Id))
			//调用lps执行硬性校验
			availableLanes, validationWeight, lineAsfMap := m.doHardCriteria(newCtx, int(task.Id), int(task.ProductId), isMultiProduct, orderLog, task.RoutingType)
			// available lanes可能为空，但为空的也要存下去，保证预测时准确性
			if isMultiProduct {
				orderLog.MultiProductHCResult = availableLanes
			} else {
				orderLog.HCResult = availableLanes
			}
			orderLog.ExtraInfo.ValidationWeight = int64(validationWeight)
			orderLog.LineAsfMap = lineAsfMap
			// 重新计算运费开关需求：对没有运费的line补齐运费，在软性调度时根据重新计算运费开关判断要么全部重新计算要么全部从log中取
			m.completeShippingFee(newCtx, availableLanes, &orderLog)

			//更新总的已完成订单值
			m.updateTaskVolume(newCtx, task, day)

			// 如果row key不为空则走原来逻辑
			if rowKey != "" {
				if err := m.saveRoutingLogToHbase(newCtx, int(task.Id), orderLog, rowKey); err != nil {
					logger.CtxLogErrorf(newCtx, "save routing log to hbase failed | err=%v", err)
					return
				}
			} else {
				if err := m.RoutingLogService.SendHardCriteriaLogToKafka(newCtx, orderLog); err != nil {
					logger.CtxLogErrorf(newCtx, "send hard criteria log to kafka failed | err=%v", err)
					return
				}
			}
		}); err != nil {
			logger.CtxLogErrorf(ctx, "submit refresh order hard criteria to worker pool failed | err=%v", err)
		}
	}
	wg.Wait()
	logger.CtxLogInfof(ctx, "task id: %d, day: %v, execute order count: %d", task.Id, day, criteriaOrderCount)
}

func (m *ForecastTaskServiceImpl) updateTaskVolume(ctx context.Context, task persistent.HardCriteriaTaskTab, day time.Time) {
	iErr := m.IncrFinishedHCTaskVolume(ctx, forecast.PrefixAllDay, task.Id, task.ProductId, task.OrderStartTime, task.OrderEndTime, 1)
	if iErr != nil {
		HardCriteriaMonitorReport(ctx, monitoring.UpdateHardCriteriaVolume, int(task.Id), fmt.Sprintf("Incr HCTask volume in redis err:%v| start order id:%v，start date：%v，end date：%v", iErr, 0, task.OrderStartTime, task.OrderEndTime), Failed)
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| Incr finished HCTask volume in redis err:%v| start order id:%v，start date：%v，end date：%v", iErr, 0, task.OrderStartTime, task.OrderEndTime)
	}
	////更新单天的已完成订单值
	iErr = m.IncrFinishedHCTaskVolume(ctx, forecast.PrefixCurrentDay, task.Id, task.ProductId, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat), 1)
	if iErr != nil {
		HardCriteriaMonitorReport(ctx, monitoring.UpdateHardCriteriaVolume, int(task.Id), fmt.Sprintf("Incr HCTask volume by day in redis err:%v| start order id:%v, start date：%v，end date：%v", iErr, 0, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat)), Failed)
		logger.CtxLogErrorf(ctx, "RefreshHardCriteriaByTask| Incr finished HCTask volume in redis err:%v| start order id:%v, start date：%v，end date：%v", iErr, 0, day.Format(timeutil.DateFormat), day.Format(timeutil.DateFormat))
	}
}

func newContext(ctx context.Context, orderLog routing_log.RoutingLog, taskId int) context.Context {
	requestId := fmt.Sprintf("%s|%s__%d", requestid.GetFromCtx(ctx), orderLog.FOrderId, taskId)
	newCtx := logger.NewLogContext(ctx, requestId)
	newCtx = requestid.SetToCtx(newCtx, requestId)
	// 设置Forecast Context，在调用运费计算接口时识别为预测场景，与线上场景区分开
	newCtx = forecast.NewForecastCtxWithTaskId(newCtx, taskId)

	return newCtx
}

func (m *ForecastTaskServiceImpl) doHardCriteria(
	ctx context.Context, taskId, productId int, isMultiProduct bool, orderLog routing_log.RoutingLog, routingType int,
) ([]*rule.RoutingLaneInfo, int, map[string]forecastentity.AsfInfo) {
	hardCheckReq := &lpsclient.HardCheckRequest{
		ProductId:      productId,
		IsMultiProduct: isMultiProduct,
	}
	//从日志中获取请求data
	if err := jsoniter.UnmarshalFromString(orderLog.OrderData, &hardCheckReq.OrderData); err != nil {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaFailed, taskId, fmt.Sprintf("UnmarshalFromString fail, err:%v", err), Failed)
		logger.CtxLogErrorf(ctx, "UnmarshalFromString fail, err:%v", err)
		return nil, 0, nil
	}

	availableLanes, validationWeight, hcErr := m.LpsApi.HardCheck(ctx, hardCheckReq)
	if hcErr != nil {
		logger.CtxLogErrorf(ctx, "Lps Hard Criteria fail|err=%v", hcErr)
		return nil, 0, nil
	}

	if needCCFilter(routingType) {
		var fErr *srerr.Error
		availableLanes, fErr = m.CCRoutingSrv.CCFilter(ctx, hardCheckReq.OrderData.GetBaseInfo().GetOrdersn(), availableLanes)
		if fErr != nil {
			logger.CtxLogErrorf(ctx, "no available lane after tw cc filter | err=%v", fErr)
			return nil, 0, nil
		}
	}

	if len(availableLanes) == 0 {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaFailed, taskId, fmt.Sprintf("Hard Criteria fail|err=%v", hcErr), Failed)
	}

	lineAsfMap, err := m.calcCbLmAsf(ctx, hardCheckReq.OrderData, availableLanes, routingType)
	if err != nil {
		logger.CtxLogErrorf(ctx, "calculate LM asf failed | err=%v", err)
	}

	return availableLanes, validationWeight, lineAsfMap
}

func (m *ForecastTaskServiceImpl) calcCbLmAsf(
	ctx context.Context, orderData *lfspb.CreateOrderRequestData, availableLanes []*rule.RoutingLaneInfo, routingType int,
) (map[string]forecastentity.AsfInfo, *srerr.Error) {
	var (
		resultList = make(map[string]forecastentity.AsfInfo)
		forderID   = orderData.GetBaseInfo().GetForderid()
	)

	if routingType != rule.CBRoutingType {
		// 只适用于CB LM场景
		return resultList, nil
	}

	sloInfoList, err := m.WbcApi.BatchGetSloInfo(ctx, []string{forderID})
	if err != nil {
		return resultList, err
	}

	if len(sloInfoList) == 0 {
		return nil, srerr.New(srerr.WbcApiErr, forderID, "slo info not found | forderid=%s", forderID)
	}

	sloInfo := sloInfoList[0]
	if sloInfo.Retcode != 0 {
		return resultList, srerr.New(srerr.WbcApiErr, forderID, "get slo info failed | forderid=%s, err=%s", forderID, sloInfo.Message)
	}
	if !sloInfo.TwsInfo.HasTwsAction || sloInfo.TwsInfo.ActualWeight == 0 {
		return resultList, srerr.New(srerr.WbcApiErr, forderID, "slo info without tws action or twi weight is 0 | forderid=%s", forderID)
	}

	var (
		commonCalcAsfInfo    = formatOrderDataToChargeCalcInfo(ctx, orderData, sloInfo)
		lineToCalcAsfInfoMap = make(map[string]*chargepb.LMEstimationCostCalculateInfo)
	)
	for _, availableLane := range availableLanes {
		for _, line := range availableLane.LineList {
			if !objutil.ContainInt(lfslib.CBLMLine, int(line.ResourceSubType)) {
				continue
			}
			if _, exist := lineToCalcAsfInfoMap[line.ResourceId]; exist {
				continue
			}

			lineCalcAsfInfo := commonCalcAsfInfo
			lineCalcAsfInfo.LineId = proto.String(line.ResourceId)
			lineToCalcAsfInfoMap[line.ResourceId] = &lineCalcAsfInfo
		}
	}

	calcAsfInfoList := make([]*chargepb.SingleLMEstimationCostCalculateInfo, 0, len(lineToCalcAsfInfoMap))
	for _, calcAsfInfo := range lineToCalcAsfInfoMap {
		calcAsfInfoList = append(calcAsfInfoList, &chargepb.SingleLMEstimationCostCalculateInfo{
			UniqueId:      calcAsfInfo.LineId,
			CalculateInfo: calcAsfInfo,
		})
	}
	asfList, err := m.ChargeApi.BatchLMEstimationCostCalculate(ctx, calcAsfInfoList)
	if err != nil {
		return resultList, err
	}

	for _, asfResult := range asfList {
		asf, err := strconv.ParseFloat(asfResult.GetTotalFeeLocal(), 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "convert total fee to float failed | total fee: %s", asfResult.GetTotalFeeLocal())
			continue
		}
		asfUsd, err := strconv.ParseFloat(asfResult.GetTotalFeeUsd(), 64)
		if err != nil {
			logger.CtxLogErrorf(ctx, "convert total fee to float failed | total fee: %s", asfResult.GetTotalFeeUsd())
			continue
		}
		resultList[asfResult.GetUniqueId()] = forecastentity.AsfInfo{
			HasAsf: true,
			Asf:    asf,
			AsfUsd: asfUsd,
		}
	}

	if len(resultList) != 0 {
		logger.CtxLogInfof(ctx, "forderid:%s, line asf result: %s", forderID, objutil.JsonString(resultList))
	}

	return resultList, nil
}

func formatOrderDataToChargeCalcInfo(ctx context.Context, orderData *lfspb.CreateOrderRequestData, sloInfo wbcclient.BatchGetSloInfoRespDataListItem) chargepb.LMEstimationCostCalculateInfo {
	dgFlag := chargepb.DgFlag_NoneDG
	wmsFlag := chargepb.WmsFlag_NoWms
	direction := constant.ForwardDirection
	if orderData.GetBaseInfo().GetIsReturn() == 1 {
		direction = constant.ReverseDirection
	}
	shipmentType := chargepb.ShipmentType_ShipmentTypePickup
	if orderData.GetBaseInfo().GetPickupTime() == 0 {
		shipmentType = chargepb.ShipmentType_ShipmentTypeDropOff
	}
	twsSize := &chargepb.Size{
		Length: proto.Float64(sloInfo.TwsInfo.ActualLength),
		Width:  proto.Float64(sloInfo.TwsInfo.ActualWidth),
		Height: proto.Float64(sloInfo.TwsInfo.ActualHeight),
	}

	return chargepb.LMEstimationCostCalculateInfo{
		LineId:            nil,
		RequestTime:       proto.Uint32(uint32(timeutil.GetCurrentUnixTimeStamp(ctx))),
		Region:            proto.String(envvar.GetCID()),
		SlsTn:             proto.String(sloInfo.SlsTN),
		ThreePlTn:         proto.String(sloInfo.LmTN),
		DgFlag:            dgFlag.Enum(),
		WmsFlag:           wmsFlag.Enum(),
		Direction:         proto.Int32(int32(direction)),
		PaymentMethod:     proto.String(orderData.GetForderInfo().GetPaymentMethod()),
		ShipmentType:      shipmentType.Enum(),
		CodAmount:         proto.String(strconv.FormatFloat(orderData.GetForderInfo().GetCodAmount(), 'f', -1, 64)),
		Cogs:              proto.String(strconv.FormatFloat(float64(orderData.GetForderInfo().GetCogs()), 'f', -1, 64)),
		TotalPrice:        proto.String(strconv.FormatFloat(float64(orderData.GetForderInfo().GetTotalPrice()), 'f', -1, 64)),
		SellerTaxNumber:   proto.String(orderData.GetForderInfo().GetSellerTaxNumber()),
		StateRegistration: proto.String(orderData.GetRegionPolicyInfo().GetBrStateRegisteredNo()),
		LocationInfo:      formatSloInfoLocationToChargeLocation(sloInfo.LmLocationInfo),
		TwsWeight:         proto.Int32(int32(math.Ceil(sloInfo.TwsInfo.ActualWeight))), // weight 需要做向上取整
		TwsSize:           twsSize,
		SlsTnCtime:        proto.Uint32(sloInfo.SloCtime),
		CombinedType:      proto.Uint32(sloInfo.CombinedType),
	}
}

func formatSloInfoLocationToChargeLocation(lmLocationInfo wbcclient.LmLocationInfo) *chargepb.CommonLocationInfo {
	pickup := &chargepb.LocationInfo{
		LocationIds: lmLocationInfo.PickupLocation.LocationIDs,
		Postcode:    proto.String(lmLocationInfo.PickupLocation.Postcode),
		Longitude:   proto.String(lmLocationInfo.PickupLocation.Longitude),
		Latitude:    proto.String(lmLocationInfo.PickupLocation.Latitude),
	}

	deliver := &chargepb.LocationInfo{
		LocationIds: lmLocationInfo.DeliverLocation.LocationIDs,
		Postcode:    proto.String(lmLocationInfo.DeliverLocation.Postcode),
		Longitude:   proto.String(lmLocationInfo.DeliverLocation.Longitude),
		Latitude:    proto.String(lmLocationInfo.DeliverLocation.Latitude),
	}

	return &chargepb.CommonLocationInfo{
		PickupLocation:  pickup,
		DeliverLocation: deliver,
	}
}

func (m *ForecastTaskServiceImpl) saveRoutingLogToHbase(ctx context.Context, taskId int, orderLog routing_log.RoutingLog, rowKey string) error {
	marshal, jsonErr := jsoniter.Marshal(&orderLog)
	if jsonErr != nil {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaSaveInHbase, taskId, fmt.Sprintf("json hc routing log err %v", jsonErr), Failed)
		logger.CtxLogErrorf(ctx, "json hc routing log err %v", jsonErr)
		return jsonErr
	}
	col := map[string]map[string][]byte{}
	col["c"] = map[string][]byte{}
	compress := zip.ZSTDCompress(marshal)
	col["c"]["message"] = compress

	hbConf := configutil.GetDataHbaseConfig(ctx)
	tableName := hbConf.TableNameMap[masking_forecast_hbase.ROUTING_LOG]
	if err := m.hbHelper.UpdateRow(ctx, tableName, rowKey, col); err != nil {
		HardCriteriaMonitorReport(ctx, monitoring.HardCriteriaSaveInHbase, taskId, fmt.Sprintf("save hc routing log failed %v", err), Failed)
		logger.CtxLogErrorf(ctx, "save hc routing log failed %v", err)
		return err
	}

	return nil
}

func (m *ForecastTaskServiceImpl) RefreshHbaseHardCriteriaByProduct(ctx context.Context, productId int, isMultiProduct bool, day time.Time, concurrency int) *srerr.Error {
	routingConfig, err := m.RoutingConfService.GetRoutingConfigCacheByProductID(ctx, productId)
	if err != nil {
		return err
	}

	dataChan, err := m.GetForecastOrdersFromHbase(ctx, 0, 0, productId, int(routingConfig.GetRoutingType()), 1000, day)
	if err != nil {
		return err
	}

	// 异步任务链路，阻塞式
	workerPool, poolErr := ants.NewPool(concurrency)
	if poolErr != nil {
		return srerr.With(srerr.GoroutinePoolErr, nil, poolErr)
	}
	var wg sync.WaitGroup
	for data := range dataChan {
		data := data
		if err := workerPool.Submit(func() {
			wg.Add(1)
			defer wg.Done()
			orderData := data.LogEntry
			//调用lps执行硬性校验
			availableLanes, validationWeight, lineAsfMap := m.doHardCriteria(ctx, 0, productId, isMultiProduct, orderData, int(routingConfig.GetRoutingType()))
			// available lanes可能为空，但为空的也要存下去，保证预测时准确性
			if isMultiProduct {
				orderData.MultiProductHCResult = availableLanes
			} else {
				orderData.HCResult = availableLanes
			}
			orderData.ExtraInfo.ValidationWeight = int64(validationWeight)
			orderData.LineAsfMap = lineAsfMap
			//err日志在方法里面已经打印过
			if err := m.saveRoutingLogToHbase(ctx, 0, orderData, data.RowKey); err != nil {
				return
			}
		}); err != nil {
			return srerr.With(srerr.GoroutinePoolErr, nil, err)
		}
	}
	wg.Wait()

	return nil
}

func (m *ForecastTaskServiceImpl) DeleteHardCriteriaByTaskId(ctx context.Context, taskId int) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.HardCriteriaTaskHook, map[string]interface{}{
		"id=?": taskId,
	}, dbutil.ModelInfo{TaskId: uint64(taskId)}); err != nil {
		logger.CtxLogErrorf(ctx, "delete hard criteria by debug err=%v", err)
		return srerr.With(srerr.DatabaseErr, taskId, err)
	}

	return nil
}

func (m *ForecastTaskServiceImpl) DeleteForecastTaskByTaskId(ctx context.Context, taskId int) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.ForecastingTaskHook, map[string]interface{}{
		"id=?": taskId,
	}, dbutil.ModelInfo{TaskId: uint64(taskId)}); err != nil {
		logger.CtxLogErrorf(ctx, "delete forecast task by debug err=%v", err)
		return srerr.With(srerr.DatabaseErr, taskId, err)
	}

	db, err := dbutil.MasterDB(ctx, persistent.ForecastingTaskResultHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "delete forecast task result by debug err=%v", err)
		return srerr.With(srerr.DatabaseErr, taskId, err)
	}

	rowAffect := int64(-1)
	realDB := db.Table(persistent.ForecastingTaskResultHook.TableName())
	loop := 0
	for rowAffect != 0 && loop < maxLoop {
		if err := realDB.Limit(batchDeleteSize).Delete(persistent.ForecastingTaskResultHook, "task_id=?", taskId).GetError(); err != nil {
			logger.CtxLogErrorf(ctx, "delete forecast task result by debug err=%v", err)
		}
		rowAffect = realDB.RowsAffected()
		loop++
	}

	return nil
}

// completeShippingFee 补齐运费
func (m *ForecastTaskServiceImpl) completeShippingFee(ctx context.Context, availableLanes []*rule.RoutingLaneInfo, routingLog *routing_log.RoutingLog) {
	// locationIds长度校验是避免上线之后兼容历史数据，因为历史数据的routingLog没有保存SmartRoutingData（SSCSMR-1364才加上这部分）
	if len(availableLanes) == 0 || routingLog == nil || len(routingLog.ExtraInfo.SmartRoutingData.PickupLocationIds) == 0 || len(routingLog.ExtraInfo.SmartRoutingData.DeliveryLocationIds) == 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.NoNeedCompleteFee, "no need complete shipping fee")
		return
	}
	// 经pm确认，为了避免无效运费计算，只有配置了需要提前计算运费的才需要补齐，且费率表不一致的问题可以忽略
	if !configutil.IsOpenGlobalSwitch(ctx) {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.PreCalcSwitchIsClose, "global switch close, no need calc fee.")
		return
	}
	// 提前计算运费结果为空时，lineFeeMap为nil，为了防止后面空指针异常，对lineFeeMap初始化
	if len(routingLog.LineFeeMap) == 0 {
		routingLog.LineFeeMap = map[string]float64{}
	}
	// 过滤需要计算运费的line
	var lineList []*rule.LineInfo
	lineMap := make(map[string]bool)
	for _, availableLane := range availableLanes {
		for _, line := range availableLane.LineList {
			// 已有运费的不需要重新计算
			if _, ok := routingLog.LineFeeMap[line.ResourceId]; ok {
				continue
			}
			// 去重
			if _, ok := lineMap[line.ResourceId]; ok {
				continue
			}
			// 未配置提前计算开关的不需要计算
			if !configutil.IsOpenRoutingPreCalcFeeSwitch(ctx, int64(routingLog.ProductId), int(line.ResourceSubType)) {
				continue
			}
			lineMap[line.ResourceId] = true
			lineList = append(lineList, line)
		}
	}
	// 没有需要补齐运费的line，直接返回
	if len(lineList) == 0 {
		monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.NeedCompleteLineIsNil, "need complete line is nil")
		return
	}

	// 计算运费
	routingData := rule.RoutingData{
		CreateOrderData: &routingLog.ExtraInfo.SmartRoutingData,
		Rule: &rule.RoutingRuleParsed{
			RoutingType: uint8(routingLog.RoutingType),
		},
		ValidateLaneList: availableLanes,
	}
	// 运费更新到routing_log
	completeLineFeeMap := m.LineCheapestShippingFeeFactor.CalcLineShippingFee(ctx, lineList, &routingData)
	for completeLineId, fee := range completeLineFeeMap {
		if _, ok := routingLog.LineFeeMap[completeLineId]; !ok {
			routingLog.LineFeeMap[completeLineId] = fee
		}
	}
	monitoring.ReportSuccess(ctx, monitoring.CatRoutingReCalcFeeMonitor, monitoring.CompleteFeeSuccess, fmt.Sprintf("complete shipping fee success, completeLineFeeMap=%v", completeLineFeeMap))
}

func needCCFilter(routingType int) bool {
	return envvar.GetCID() == constant.TW && routingType == rule.CBRoutingType
}
