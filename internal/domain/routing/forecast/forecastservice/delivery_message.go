package forecastservice

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type taskMessage struct {
	TaskID int `json:"task_id"`
}

func deliveryTask2Kafka(ctx context.Context, task *persistent.ForecastingTaskTab) *srerr.Error {
	//m := taskMessage{TaskID: task.Id}
	//value, err := jsoniter.Marshal(m)
	//if err != nil {
	//	logger.CtxLogErrorf(ctx, "deliveryTask2Kafka | encode task message fail. err: %+v", err)
	//	return srerr.With(srerr.JsonErr, nil, err)
	//}
	//todo 预测任务不走异步任务，目前是扫表
	//namespace := configutil.GetSaturnNamespaceConf().SmrNamespace
	//err = kafkahelper.DeliveryMessage(ctx, namespace, routingForecastJobName, value, nil, kafkahelper.RoutingForecastTaskType)
	//if err != nil {
	//	logger.CtxLogErrorf(ctx, "deliveryTask2Kafka | DeliveryMessage fail. err: %+v", err)
	//	return srerr.With(srerr.KafkaServerError, nil, err)
	//}
	return nil
}
