package persistent

import (
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var (
	ILHForecastTaskTabHook = &ILHForecastTaskTab{}
)

type ILHForecastTaskTab struct {
	ID               int              `gorm:"column:id"`
	TaskName         string           `gorm:"column:task_name"`
	StartDate        string           `gorm:"column:start_date"`
	EndDate          string           `gorm:"column:end_date"`
	TaskStatus       TaskStatus       `gorm:"column:task_status"`
	DeployStatus     DeployStatus     `gorm:"column:deploy_status"`
	ShipmentResource ShipmentResource `gorm:"column:shipment_resource"`
	TaskOperator     string           `gorm:"column:task_operator"`
	DeployOperator   string           `gorm:"column:deploy_operator"`
	CompleteTime     int64            `gorm:"column:complete_time"`
	DeployTime       int64            `gorm:"column:deploy_time"`
	Ctime            int64            `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime            int64            `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
}

func (c ILHForecastTaskTab) TableName() string {
	return "ilh_forecast_task_tab"
}

func (c ILHForecastTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (c ILHForecastTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (c ILHForecastTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(c.ID),
		ModelName: c.TableName(),
	}
}

type ILHForecastTaskResultTab struct {
	ID                      int         `gorm:"column:id"`
	TaskID                  int         `gorm:"column:task_id"`
	AvailableLHRuleName     string      `gorm:"column:available_lh_rule_name"`
	AvailableLHRulePriority int         `gorm:"column:available_lh_rule_priority"`
	MultiProductID          int         `gorm:"column:multi_product_id"`
	LaneCode                string      `gorm:"column:lane_code"`
	DestinationPort         string      `gorm:"column:destination_port"`
	DgType                  rule.DGFlag `gorm:"column:dg_type"`
	ReversedBSAWeight       float64     `gorm:"column:reversed_bsa_weight"`     // KG
	NonReversedBSAWeight    float64     `gorm:"column:non_reversed_bsa_weight"` // KG
	AdhocWeight             float64     `gorm:"column:adhoc_weight"`            // KG
	Ctime                   int64       `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime                   int64       `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
}

func (c ILHForecastTaskResultTab) TableName() string {
	return "ilh_forecast_task_result_tab"
}

func (c ILHForecastTaskResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (c ILHForecastTaskResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (c ILHForecastTaskResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(c.ID),
		ModelName: c.TableName(),
	}
}
