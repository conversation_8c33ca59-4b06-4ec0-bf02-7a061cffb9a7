package persistent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	jsoniter "github.com/json-iterator/go"
)

var (
	ForecastingTaskHook             = &ForecastingTaskTab{}
	ForecastingTaskResultHook       = &ForecastingTaskResultTab{}
	ForecastingTaskErrorHook        = &ForecastingTaskErrorTab{}
	ForecastingRuleHook             = &ForecastingRuleTab{}
	HardCriteriaTaskHook            = &HardCriteriaTaskTab{}
	ForecastVolumeRuleHook          = &ForecastVolumeRuleTab{}
	ForecastingPredictionVolumeHook = &ForecastingPredictionVolumeTab{}
)

type TaskStatus int

const (
	TaskStatusDraft      TaskStatus = 0
	TaskStatusWaiting    TaskStatus = 1
	TaskStatusProcessing TaskStatus = 2
	TaskStatusDone       TaskStatus = 3
	TaskStatusStop       TaskStatus = 4
	TaskStatusFailed     TaskStatus = 5
)

type DeployStatus int

const (
	DeployStatusInit     DeployStatus = 0
	DeployStatusPending  DeployStatus = 1
	DeployStatusInEffect DeployStatus = 2
	DeployStatusFailed   DeployStatus = 3
)

type ShipmentResource int

const (
	ShipmentResourceTypeHistoryOrders    ShipmentResource = 0 // default shipment resource
	ShipmentResourceTypePredictionVolume ShipmentResource = 1
)

const (
	taskTableName               = "forecasting_task_tab"
	resultTableName             = "forecasting_task_result_tab"
	errorTableName              = "forecasting_task_error_tab"
	taskRuleTableName           = "forecasting_rule_tab"
	hardCriteriaTaskTableName   = "smart_routing_hard_criteria_task_tab"
	forecastVolumeRuleTableName = "forecast_volume_rule_tab"
	predictionVolumeTableName   = "forecasting_prediction_volume_tab"
)

type ForecastingTaskTab struct {
	Id                         int                      `gorm:"column:id" json:"id"`
	ProductId                  int                      `gorm:"column:product_id" json:"product_id"`
	TaskStatus                 TaskStatus               `gorm:"column:task_status" json:"task_status"` // 0: draft 1: waiting 2: processing 3: done 4: stop 5: failed
	TaskName                   string                   `gorm:"column:task_name" json:"task_name"`
	StartDate                  string                   `gorm:"column:start_date" json:"start_date"` // 2006-01-02
	EndDate                    string                   `gorm:"column:end_date" json:"end_date"`
	OperateBy                  string                   `gorm:"column:operate_by" json:"operate_by"`
	RoutingType                uint8                    `gorm:"column:routing_type" json:"routing_type"`
	RuleList                   []*ForecastingRuleTab    `gorm:"-" json:"rule_list"`
	UseOpsTask                 bool                     `gorm:"column:use_ops_task" json:"use_ops_task"` //true：使用ops创建的硬性校验任务， false：不使用
	HCTaskEndTime              int64                    `gorm:"-" json:"hc_task_end_time"`               //硬性校验刷新任务的结束时间
	HCTaskId                   uint64                   `gorm:"column:hc_task_id" json:"hc_task_id"`     //硬性校验刷新任务主键id
	IsMultiProduct             bool                     `gorm:"column:is_multi_product" json:"is_multi_product"`
	VolumeRuleList             []*ForecastVolumeRuleTab `gorm:"-" json:"volume_rule_list"`
	WeightRange                []*WeightRange           `gorm:"-" json:"weight_range"`
	SimulationOrderCount       []*SimulationOrderCount  `gorm:"-" json:"simulation_order_count"`
	WeightRangeDetail          []byte                   `gorm:"column:weight_range_detail;type:varchar(2048)" json:"-"`
	SimulationOrderCountDetail []byte                   `gorm:"column:simulation_order_count_detail;type:varchar(2048)" json:"-"`
	TaskProgress               float64                  `gorm:"-" json:"task_progress"`
	OrderCount                 int                      `gorm:"column:order_count" json:"order_count"`
	CTime                      uint32                   `gorm:"column:ctime" json:"ctime"`
	MTime                      uint32                   `gorm:"column:mtime" json:"mtime"`
	ReCalcFee                  bool                     `gorm:"column:re_calc_fee" json:"re_calc_fee"`       //task是否重新计算运费，true: 重新计算运费，false：从log取运费
	VolumeRuleId               int64                    `gorm:"column:volume_rule_id" json:"volume_rule_id"` //cb预测的volume rule
	ShipmentResource           ShipmentResource         `gorm:"column:shipment_resource" json:"shipment_resource"`
	IsVolumeRouting            bool                     `gorm:"-" json:"is_volume_routing"`
}

type SimulationOrderCount struct {
	Day   int `json:"day"`
	Count int `json:"count"`
}

type WeightRange struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

func (rs *ForecastingTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastingTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastingTaskTab) TableName() string {
	return taskTableName
}

func (rs *ForecastingTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(rs.Id),
		ModelName:            rs.TableName(),
		FulfillmentProductId: uint64(rs.ProductId),
		TaskId:               uint64(rs.Id),
	}
}

// GetId return rule id
func (rs *ForecastingTaskTab) GetId() int {
	return rs.Id
}

func (rs *ForecastingTaskTab) GetProductId() int {
	return rs.ProductId
}

func (rs *ForecastingTaskTab) StatusKey() string {
	return fmt.Sprintf("forecasting_task:%d:status", rs.Id)
}

type ForecastingTaskResultTab struct {
	Id                int                   `gorm:"column:id" json:"id"`
	TaskId            int                   `gorm:"column:task_id" json:"task_id"`
	Priority          int32                 `gorm:"column:priority" json:"priority"`
	RuleId            int                   `gorm:"column:rule_id" json:"rule_id"`
	Quantity          int                   `gorm:"column:quantity" json:"quantity"`
	IlhParcelQuantity int                   `gorm:"column:ilh_parcel_quantity" json:"ilh_parcel_quantity"`
	Weight            int                   `gorm:"column:weight" json:"weight"`
	LaneCode          string                `gorm:"column:lane_code" json:"lane_code"`
	DGFlag            int                   `gorm:"column:dg_flag" json:"dg_flag"`
	ServiceCode       string                `gorm:"column:service_code" json:"service_code"`
	Site              string                `gorm:"column:site" json:"site"`
	BuyerCity         int64                 `gorm:"column:buyer_city" json:"buyer_city"`
	Day               int                   `gorm:"column:simulation_day_index" json:"simulation_day_index"`
	ShippingFee       float64               `gorm:"column:shipping_fee" json:"shipping_fee"`
	WeightRange       WeightRange           `gorm:"-" json:"weight_range"`
	WeightRangeDetail []byte                `gorm:"column:weight_range_detail;type:varchar(2048)" json:"-"`
	ActualPoint       string                `gorm:"column:out_actual_point" json:"out_actual_point"`
	CTime             int64                 `gorm:"column:ctime" json:"ctime"`
	StatDate          string                `gorm:"column:stat_date" json:"stat_date"`
	AsfInfo           ForecastResultAsfInfo `gorm:"column:asf_info" json:"asf_info"`
}

type ForecastResultAsfInfo struct {
	AverageAsf    float64 `json:"average_asf"`
	AverageAsfUsd float64 `json:"average_asf_usd"`
	MissingAsfQty int     `json:"missing_asf_qty"`
}

func (a *ForecastResultAsfInfo) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("unmarshal asf info failed :", value))
	}
	return jsoniter.Unmarshal(bytes, a)
}

func (a ForecastResultAsfInfo) Value() (driver.Value, error) {
	return jsoniter.Marshal(a)
}

func (rs *ForecastingTaskResultTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastingTaskResultTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastingTaskResultTab) TableName() string {
	return resultTableName
}

func (rs *ForecastingTaskResultTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(rs.Id),
		ModelName: rs.TableName(),
		TaskId:    uint64(rs.TaskId),
		RuleId:    uint64(rs.RuleId),
	}
}

type ForecastingTaskErrorTab struct {
	Id        int    `gorm:"column:id" json:"id"`
	TaskId    int    `gorm:"column:task_id" json:"task_id"`
	TaskError string `gorm:"column:error" json:"task_error"`
	CTime     int64  `gorm:"column:ctime" json:"ctime"`
}

func (rs *ForecastingTaskErrorTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastingTaskErrorTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastingTaskErrorTab) TableName() string {
	return errorTableName
}

func (rs *ForecastingTaskErrorTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(rs.Id),
		ModelName: rs.TableName(),
		TaskId:    uint64(rs.TaskId),
	}
}

type ForecastingRuleTab struct {
	Id                          int                               `gorm:"column:id" json:"id"`
	RuleName                    string                            `gorm:"column:rule_name" json:"rule_name"`
	TaskId                      int                               `gorm:"column:task_id" json:"task_id"`
	StrRuleDetails              []byte                            `gorm:"column:rule_detail" json:"-"`
	StrDefaultCriteria          []byte                            `gorm:"column:default_criteria;type:varchar(2048)" json:"-"` // 默认规则权重配置
	StrDisabledInfo             []byte                            `gorm:"column:disabled_info;type:varchar(2048)" json:"-"`
	StrCombinationSetting       []byte                            `gorm:"column:combination_setting" json:"-"`
	Priority                    int                               `gorm:"column:priority" json:"priority"` // 1 is high
	WhsId                       string                            `gorm:"column:whs_id" json:"-"`
	ZoneCode                    string                            `gorm:"column:zone_code" json:"-"`
	ItemCategoryLevel           int                               `gorm:"column:item_category_level" json:"item_category_level,omitempty"`
	ItemCategoryId              string                            `gorm:"column:item_category_id" json:"-"`
	ParcelValueMin              float32                           `gorm:"column:parcel_value_min" json:"parcel_value_min,omitempty"`
	ParcelValueMax              float32                           `gorm:"column:parcel_value_max" json:"parcel_value_max,omitempty"`
	ParcelWeightMin             int                               `gorm:"column:parcel_weight_min" json:"parcel_weight_min,omitempty"`
	ParcelWeightMax             int                               `gorm:"column:parcel_weight_max" json:"parcel_weight_max,omitempty"`
	IsMultiProduct              bool                              `gorm:"column:is_multi_product" json:"is_multi_product"`
	RoutingType                 uint8                             `gorm:"column:routing_type" json:"routing_type"`
	RuleDetails                 *rule.RuleDetails                 `gorm:"-" json:"rule_detail,omitempty"`
	DefaultCriteria             *rule.DefaultCriteria             `gorm:"-" json:"default_criteria,omitempty"` // 默认规则权重配置
	DisabledInfo                []*rule.DisabledInfo              `gorm:"-" json:"disabled_info,omitempty"`
	CombinationSetting          []*rule.CombinationSetting        `gorm:"-" json:"combination_setting"`
	MultiProductDefaultCriteria *rule.MultiProductDefaultCriteria `gorm:"-" json:"multi_product_default_criteria,omitempty"` // Multi Product默认规则权重配置
	MultiProductDisableInfo     *rule.MultiProductDisabledInfo    `gorm:"-" json:"multi_product_disable_info,omitempty"`
	WhsIdList                   []string                          `gorm:"-" json:"whs_id"`
	ZoneCodeList                []string                          `gorm:"-" json:"zone_code"`
	ItemCategoryIdList          []int                             `gorm:"-" json:"item_category_id,omitempty"`
	DestinationPorts            string                            `gorm:"column:destination_ports" json:"-"`       //SSCSMR-278 add destination ports, here is used to store data in table
	DestinationPortList         []string                          `gorm:"-" json:"destination_ports"`              //SSCSMR-278 add destination ports, here is used to interact with fe
	DgType                      int                               `gorm:"column:dg_type" json:"dg_type,omitempty"` //1:non-dg; 2:dg
	ParcelDimension             rule.ParcelDimension              `gorm:"column:parcel_dimension" json:"parcel_dimension,omitempty"`
	WmsToggleEnable             bool                              `gorm:"column:wms_toggle_enable" json:"wms_toggle_enable"`
	CCMode                      rule.CCMode                       `gorm:"column:cc_mode" json:"cc_mode"`
	IsVolumeRouting             bool                              `gorm:"-" json:"is_volume_routing"`
	TplToggleInfo               *rule.ListResourceType            `gorm:"-" json:"tpl_toggle_info"`
	StrTplToggleInfo            []byte                            `gorm:"column:tpl_toggle_info" json:"-"` // 3pl toggle info list
	ShopGroupList               []byte                            `gorm:"column:shop_group_list" json:"-"`
	ShopGroupListVo             []int64                           `gorm:"-" json:"shop_group_list"`
	DownloadType                string                            `gorm:"download_type" json:"-"`
	Url                         []byte                            `gorm:"url" json:"-"`
	UrlInfo                     UrlInfo                           `gorm:"-" json:"url_info"`
}

func (rs *ForecastingRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastingRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastingRuleTab) TableName() string {
	return taskRuleTableName
}

func (rs *ForecastingRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        uint64(rs.Id),
		ModelName: rs.TableName(),
		TaskId:    uint64(rs.TaskId),
	}
}

func (rs *ForecastingRuleTab) SetRuleDetails(r *rule.RuleDetails) error {
	rules, err := jsoniter.Marshal(r)
	if err != nil {
		return err
	}
	rs.StrRuleDetails = rules
	return nil
}

func (rs *ForecastingRuleTab) SetDefaultCriteria(dc *rule.DefaultCriteria) error {
	criteria, err := jsoniter.Marshal(dc)
	if err != nil {
		return err
	}
	rs.StrDefaultCriteria = criteria
	return nil
}

func (rs *ForecastingRuleTab) SetDisabledInfo(d []*rule.DisabledInfo) error {
	disabledInfo, err := jsoniter.Marshal(d)
	if err != nil {
		return err
	}
	rs.StrDisabledInfo = disabledInfo
	return nil
}

func (rs *ForecastingRuleTab) SetMultiProductDefaultCriteria(dc *rule.MultiProductDefaultCriteria) error {
	criteria, err := jsoniter.Marshal(dc)
	if err != nil {
		return err
	}
	rs.StrDefaultCriteria = criteria
	return nil
}

func (rs *ForecastingRuleTab) SetMultiProductDisabledInfo(d *rule.MultiProductDisabledInfo) error {
	disabledInfo, err := jsoniter.Marshal(d)
	if err != nil {
		return err
	}
	rs.StrDisabledInfo = disabledInfo
	return nil
}

func (rs *ForecastingRuleTab) SetTplToggleInfo(rt *rule.ListResourceType) error {
	tplToggleInfo, err := jsoniter.Marshal(rt)
	if err != nil {
		return err
	}
	rs.StrTplToggleInfo = tplToggleInfo
	return nil
}

func (rs *ForecastingRuleTab) GetRuleDetails() (*rule.RuleDetails, error) {
	r := new(rule.RuleDetails)
	err := jsoniter.Unmarshal(rs.StrRuleDetails, r)
	if err != nil {
		return nil, err
	}

	rs.RuleDetails = r
	return rs.RuleDetails, nil
}

func (rs *ForecastingRuleTab) GetDefaultCriteria() (*rule.DefaultCriteria, error) {
	if rs.IsMultiProduct && rs.RoutingType != rule.IlhRoutingType {
		defaultCriteria := new(rule.MultiProductDefaultCriteria)
		err := jsoniter.Unmarshal(rs.StrDefaultCriteria, defaultCriteria)
		if err != nil {
			return nil, err
		}
		rs.MultiProductDefaultCriteria = defaultCriteria
	} else {
		//ilh product and normal single product will use default criteria
		defaultCriteria := new(rule.DefaultCriteria)
		err := jsoniter.Unmarshal(rs.StrDefaultCriteria, defaultCriteria)
		if err != nil {
			return nil, err
		}
		rs.DefaultCriteria = defaultCriteria
	}
	return rs.DefaultCriteria, nil
}

func (rs *ForecastingRuleTab) GetDisabledInfo() ([]*rule.DisabledInfo, error) {
	if rs.IsMultiProduct && rs.RoutingType != rule.IlhRoutingType {
		var disabledInfo rule.MultiProductDisabledInfo
		err := jsoniter.Unmarshal(rs.StrDisabledInfo, &disabledInfo)
		if err != nil {
			return nil, err
		}
		rs.MultiProductDisableInfo = &disabledInfo
	} else {
		//ilh product and normal single product will use disabled info
		var disabledInfo []*rule.DisabledInfo
		err := jsoniter.Unmarshal(rs.StrDisabledInfo, &disabledInfo)
		if err != nil {
			return nil, err
		}
		rs.DisabledInfo = disabledInfo
	}
	return rs.DisabledInfo, nil
}

func (rs *ForecastingRuleTab) GetCombinationSetting() error {
	if !(rs.RoutingType == rule.IlhRoutingType && rs.CCMode == rule.CCModeCCRouting) {
		return nil
	}
	var c []*rule.CombinationSetting
	if err := jsoniter.Unmarshal(rs.StrCombinationSetting, &c); err != nil {
		return err
	}
	rs.CombinationSetting = c

	return nil
}

func (rs *ForecastingRuleTab) GetTplToggleInfo() error {
	// 取出3pl toggle info信息传给前端
	var tplToggleInfo *rule.ListResourceType
	if err := jsoniter.Unmarshal(rs.StrTplToggleInfo, &tplToggleInfo); err != nil {
		return err
	}
	rs.TplToggleInfo = tplToggleInfo

	return nil
}

func (rs *ForecastingRuleTab) GetShopGroupList() error {
	// 取出3pl toggle info信息传给前端
	shopGroupListVo := make([]int64, 0)
	if len(rs.ShopGroupList) != 0 {
		if err := jsoniter.Unmarshal(rs.ShopGroupList, &shopGroupListVo); err != nil {
			return err
		}
	}
	rs.ShopGroupListVo = shopGroupListVo

	return nil
}

type UrlInfo struct {
	CbShopGroupUrl string `json:"cb_shop_group_url"`
}

func (rs *ForecastingRuleTab) UnmarshalUrl() {
	urlInfo := UrlInfo{}
	if err := objutil.UnmarshalBytes(&urlInfo, rs.Url); err != nil {
		return
	}

	rs.UrlInfo = urlInfo
}

func (rs *ForecastingRuleTab) MarshalUrl() {
	url := objutil.JsonBytes(rs.UrlInfo)

	rs.Url = url
}

func (rs *ForecastingRuleTab) SetCbUrl(url string) {
	rs.UrlInfo.CbShopGroupUrl = url
}

func (rs *ForecastingRuleTab) LoadJsonField(ctx context.Context) error {
	if _, err := rs.GetRuleDetails(); err != nil {
		return err
	}
	if _, err := rs.GetDefaultCriteria(); err != nil {
		return err
	}
	if _, err := rs.GetDisabledInfo(); err != nil {
		return err
	}
	if err := rs.GetCombinationSetting(); err != nil {
		return err
	}
	if err := rs.GetTplToggleInfo(); err != nil {
		return err
	}
	if err := rs.GetShopGroupList(); err != nil {
		return err
	}
	rs.WhsIdList = objutil.SetStringToSliceKeepNil(rs.WhsId)
	rs.ZoneCodeList = objutil.SetStringToSliceKeepNil(rs.ZoneCode)
	rs.DestinationPortList = objutil.SetStringToSliceKeepNil(rs.DestinationPorts)
	if rs.RuleDetails != nil {
		for _, detail := range rs.RuleDetails.Rules {
			if detail.ResourceSubType != lfslib.C_LM {
				continue
			}
			detail.IsVolumeRouting = volumeutil.IsOpenVolumeRouting(ctx)
		}
	}
	return nil
}

func (rs *ForecastingRuleTab) UnmarshalShopGroupList() {
	shopGroupListVo := make([]int64, 0)
	if len(rs.ShopGroupList) != 0 {
		_ = objutil.UnmarshalBytes(&shopGroupListVo, rs.ShopGroupList)
	}
	rs.ShopGroupListVo = shopGroupListVo
}

type HardCriteriaTaskTab struct {
	Id             uint64 `gorm:"column:id" json:"id"`
	TaskName       string `gorm:"column:task_name" json:"task_name"`
	TaskType       int    `gorm:"column:task_type" json:"task_type"` // 硬性校验任务类型：1-人工创建，2-系统创建
	ProductId      uint64 `gorm:"column:product_id" json:"product_id"`
	OrderStartTime string `gorm:"column:order_start_time" json:"order_start_time"`
	OrderEndTime   string `gorm:"column:order_end_time" json:"order_end_time"`
	OrderCount     int64  `gorm:"column:order_count" json:"order_count"`
	TaskStartTime  int64  `gorm:"column:task_start_time" json:"task_start_time"`
	TaskEndTime    int64  `gorm:"column:task_end_time" json:"task_end_time"`
	TaskStatus     int    `gorm:"column:task_status" json:"task_status"` // 硬性校验任务状态：0-draft,allowing edit, 1-waiting, 2-pending, 3-doing, 4-done, 5-stopped, 6-terminated, 7-failed
	LastStartTime  int64  `gorm:"column:last_start_time" json:"last_start_time"`
	LastEndTime    int64  `gorm:"column:last_end_time" json:"last_end_time"`
	LastOrderId    uint64 `gorm:"column:last_order_id" json:"last_order_id"`       //断点续做的订单id
	LastOrderTime  int64  `gorm:"column:last_order_time" json:"last_order_time"`   //断点续做的订单id对应的那一天的时间
	LastUpdateTime int64  `gorm:"column:last_update_time" json:"last_update_time"` //上一次的更新时间，用来做心跳检查
	IsMultiProduct bool   `gorm:"column:is_multi_product" json:"is_multi_product"`
	Operator       string `gorm:"column:operator" json:"operator"`
	RoutingType    int    `gorm:"column:routing_type" json:"routing_type"`
	CTime          int64  `gorm:"column:ctime" json:"ctime"`
	MTime          int64  `gorm:"column:mtime" json:"mtime"`
	BreakPoint     string `gorm:"column:break_point" json:"break_point"`
}

func (rs *HardCriteriaTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *HardCriteriaTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *HardCriteriaTaskTab) TableName() string {
	return hardCriteriaTaskTableName
}

func (rs *HardCriteriaTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   rs.Id,
		ModelName:            rs.TableName(),
		FulfillmentProductId: rs.ProductId,
		TaskId:               rs.Id,
	}
}

// HCTaskTabs 按ctime降序排序
type HCTaskTabs []HardCriteriaTaskTab

func (s HCTaskTabs) Len() int {
	return len(s)
}
func (s HCTaskTabs) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}
func (s HCTaskTabs) Less(i, j int) bool {
	return s[i].CTime > s[j].CTime
}

type (
	ForecastVolumeRuleTab struct {
		Id              uint64                 `gorm:"column:id" json:"id"`
		TaskId          int                    `gorm:"column:task_id" json:"task_id"`
		Priority        int64                  `gorm:"column:priority" json:"priority"`
		RuleType        enum.VolumeRuleType    `gorm:"column:rule_type" json:"rule_type"`
		RuleStatus      enum.VolumeRuleStatus  `gorm:"column:rule_status" json:"rule_status"`
		ProductId       int64                  `gorm:"column:product_id" json:"product_id"`
		RuleName        string                 `gorm:"column:rule_name" json:"rule_name"`
		LineLimitDetail []byte                 `gorm:"column:line_limit_detail;type:varchar(2048)" json:"-"`
		ZoneLimitDetail []byte                 `gorm:"column:zone_limit_detail;type:varchar(2048)" json:"-"`
		LineLimit       map[string][]LineLimit `gorm:"-" json:"line_limit"`
		ZoneLimit       []ZoneLimit            `gorm:"-" json:"zone_limit"`
		RuleId          int64                  `gorm:"column:rule_id" json:"rule_id"`
	}

	LineLimit struct {
		LineId                 string `json:"line_id"`
		MinDailyLimit          int64  `json:"min_daily_limit,omitempty" validate:"required,min=1"`
		LineType               int    `json:"line_type"`
		MaxDailyLimit          int64  `json:"max_daily_limit,omitempty" validate:"required,min=1"`
		MaxCodDailyLimit       int64  `json:"max_cod_daily_limit" validate:"required,min=1"`
		MaxBulkyDailyLimit     int64  `json:"max_bulky_daily_limit" validate:"required,min=1"`
		MaxHighValueDailyLimit int64  `json:"max_high_value_daily_limit" validate:"required,min=1"`
		MaxDgDailyLimit        int64  `json:"max_dg_daily_limit" validate:"required,min=1"`
	}

	ZoneLimit struct {
		LineId                 string `json:"line_id"`
		ZoneGroupId            string `json:"zone_group_id"`
		LineType               int    `json:"line_type"`
		ZoneName               string `json:"zone_name"`
		MinDailyLimit          int64  `json:"min_daily_limit"`
		MaxDailyLimit          int64  `json:"max_daily_limit"`
		MaxCodDailyLimit       int64  `json:"max_cod_daily_limit"`
		MaxBulkyDailyLimit     int64  `json:"max_bulky_daily_limit"`
		MaxHighValueDailyLimit int64  `json:"max_high_value_daily_limit"`
		MaxDgDailyLimit        int64  `json:"max_dg_daily_limit"`
	}
)

func (rs *ForecastVolumeRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastVolumeRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastVolumeRuleTab) TableName() string {
	return forecastVolumeRuleTableName
}

func (rs *ForecastVolumeRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        rs.Id,
		ModelName: rs.TableName(),
		TaskId:    rs.Id,
	}
}

type ForecastingPredictionVolumeTab struct {
	Id             uint64  `gorm:"column:id" json:"id"`
	TaskId         int     `gorm:"column:task_id" json:"task_id"`
	MultiProductId int     `gorm:"column:multi_product_id" json:"multi_product_id"`
	ShipmentDate   string  `gorm:"column:shipment_date" json:"shipment_date"`
	Tws            string  `gorm:"column:tws" json:"tws"`
	DestSite       string  `gorm:"column:dest_site" json:"dest_site"`
	TP             int     `gorm:"column:tp" json:"tp"`
	CartonQuantity int     `gorm:"column:carton_quantity" json:"carton_quantity"`
	TotalWeight    float64 `gorm:"column:total_weight" json:"total_weight"` // KG
	ParcelQuantity int     `gorm:"column:parcel_quantity" json:"parcel_quantity"`
	Lm             string  `gorm:"column:lm" json:"lm"`
	Mtime          int64   `gorm:"autoUpdateTime;column:mtime" json:"mtime"`
	Ctime          int64   `gorm:"autoCreateTime;column:ctime" json:"ctime"`
}

func (rs *ForecastingPredictionVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (rs *ForecastingPredictionVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (rs *ForecastingPredictionVolumeTab) TableName() string {
	return predictionVolumeTableName
}

func (rs *ForecastingPredictionVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        rs.Id,
		ModelName: rs.TableName(),
		TaskId:    rs.Id,
	}
}
