package forecastrepo

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/ilh_smart_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// ILHForecastTaskRepo ILH预测任务仓储接口
type ILHForecastTaskRepo interface {
	// CreateILHForecastTask 创建ILH预测任务
	CreateILHForecastTask(ctx context.Context, task *persistent.ILHForecastTaskTab) (int, *srerr.Error)

	// UpdateILHForecastTask 更新ILH预测任务
	UpdateILHForecastTask(ctx context.Context, task *persistent.ILHForecastTaskTab) *srerr.Error

	// GetILHForecastTaskByID 根据ID获取ILH预测任务
	GetILHForecastTaskByID(ctx context.Context, id int) (*persistent.ILHForecastTaskTab, *srerr.Error)

	// ListILHForecastTasks 获取ILH预测任务列表
	ListILHForecastTasks(ctx context.Context, filter *ilh_smart_routing.ListILHForecastTaskReq) ([]*persistent.ILHForecastTaskTab, int64, *srerr.Error)

	// DeleteILHForecastTask 删除ILH预测任务
	DeleteILHForecastTask(ctx context.Context, id int) *srerr.Error

	// CopyILHForecastTask 复制ILH预测任务
	CopyILHForecastTask(ctx context.Context, id int) (int, *srerr.Error)

	// GetILHForecastTaskResultData 获取ILH预测任务结果原始数据
	GetILHForecastTaskResultData(ctx context.Context, taskId int) ([]persistent.ILHForecastTaskResultTab, *srerr.Error)

	// BatchSaveILHForecastResults 批量保存ILH预测任务结果
	BatchSaveILHForecastResults(ctx context.Context, results []*persistent.ILHForecastTaskResultTab) *srerr.Error
}

// ILHForecastTaskRepoImpl ILH预测任务仓储实现
type ILHForecastTaskRepoImpl struct {
}

// NewILHForecastTaskRepoImpl 创建ILH预测任务仓储实现
func NewILHForecastTaskRepoImpl() *ILHForecastTaskRepoImpl {
	return &ILHForecastTaskRepoImpl{}
}

// CreateILHForecastTask 创建ILH预测任务
func (r *ILHForecastTaskRepoImpl) CreateILHForecastTask(ctx context.Context, task *persistent.ILHForecastTaskTab) (int, *srerr.Error) {
	db, err := dbutil.MasterDB(ctx, task)
	if err != nil {
		return 0, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.Table(task.TableName()).Create(task).GetError(); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, task, err)
	}

	return task.ID, nil
}

// UpdateILHForecastTask 更新ILH预测任务
func (r *ILHForecastTaskRepoImpl) UpdateILHForecastTask(ctx context.Context, task *persistent.ILHForecastTaskTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, task)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.Table(task.TableName()).Save(task).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, task, err)
	}

	return nil
}

// GetILHForecastTaskByID 根据ID获取ILH预测任务
func (r *ILHForecastTaskRepoImpl) GetILHForecastTaskByID(ctx context.Context, id int) (*persistent.ILHForecastTaskTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, persistent.ILHForecastTaskTabHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	var task persistent.ILHForecastTaskTab
	if err := db.Table(persistent.ILHForecastTaskTabHook.TableName()).Where("id = ?", id).First(&task).GetError(); err != nil {
		if err == scormv2.ErrRecordNotFound {
			return nil, srerr.New(srerr.DataNotFound, id, "ilh forecast task not found with id: %d", id)
		}
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}

	return &task, nil
}

// ListILHForecastTasks 获取ILH预测任务列表
func (r *ILHForecastTaskRepoImpl) ListILHForecastTasks(ctx context.Context, filter *ilh_smart_routing.ListILHForecastTaskReq) ([]*persistent.ILHForecastTaskTab, int64, *srerr.Error) {
	var tasks []*persistent.ILHForecastTaskTab
	db, err := dbutil.SlaveDB(ctx, persistent.ILHForecastTaskTabHook)
	if err != nil {
		return nil, 0, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	query := db.Table(persistent.ILHForecastTaskTabHook.TableName())

	// 添加过滤条件
	if filter.ID > 0 {
		query = query.Where("id = ?", filter.ID)
	}
	if filter.TaskName != "" {
		query = query.Where("task_name LIKE ?", "%"+filter.TaskName+"%")
	}
	// 添加任务状态过滤
	if filter.TaskStatus > 0 {
		query = query.Where("task_status = ?", filter.TaskStatus)
	}
	// 添加部署状态过滤
	if filter.DeployStatus > 0 {
		query = query.Where("deploy_status = ?", filter.DeployStatus)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, filter, err)
	}

	// 分页查询
	offset := (filter.Pageno - 1) * filter.Limit
	if err := query.Order("id DESC").Offset(offset).Limit(filter.Limit).Find(&tasks).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, filter, err)
	}

	return tasks, total, nil
}

// DeleteILHForecastTask 删除ILH预测任务
func (r *ILHForecastTaskRepoImpl) DeleteILHForecastTask(ctx context.Context, id int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ILHForecastTaskTabHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.Table(persistent.ILHForecastTaskTabHook.TableName()).Where("id = ?", id).Delete(persistent.ILHForecastTaskTabHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}

	return nil
}

// CopyILHForecastTask 复制ILH预测任务
func (r *ILHForecastTaskRepoImpl) CopyILHForecastTask(ctx context.Context, id int) (int, *srerr.Error) {
	// 先获取原任务
	srcTask, err := r.GetILHForecastTaskByID(ctx, id)
	if err != nil {
		return 0, err
	}

	// 创建新任务
	newTask := &persistent.ILHForecastTaskTab{
		TaskName:         srcTask.TaskName + " (Copy)",
		StartDate:        srcTask.StartDate,
		EndDate:          srcTask.EndDate,
		ShipmentResource: srcTask.ShipmentResource,
		TaskOperator:     srcTask.TaskOperator,
	}

	// 保存新任务
	newId, err := r.CreateILHForecastTask(ctx, newTask)
	if err != nil {
		return 0, err
	}

	return newId, nil
}

// GetILHForecastTaskResultData 获取ILH预测任务结果原始数据
func (r *ILHForecastTaskRepoImpl) GetILHForecastTaskResultData(ctx context.Context, taskId int) ([]persistent.ILHForecastTaskResultTab, *srerr.Error) {
	// 从数据库获取预测任务结果
	db, err := dbutil.SlaveDB(ctx, persistent.ILHForecastTaskResultTab{})
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	// 查询任务结果数据
	var results []persistent.ILHForecastTaskResultTab
	if err := db.Table(persistent.ILHForecastTaskResultTab{}.TableName()).
		Where("task_id = ?", taskId).
		Find(&results).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, taskId, err)
	}

	return results, nil
}

// BatchSaveILHForecastResults 批量保存ILH预测任务结果
func (r *ILHForecastTaskRepoImpl) BatchSaveILHForecastResults(ctx context.Context, results []*persistent.ILHForecastTaskResultTab) *srerr.Error {
	if len(results) == 0 {
		return nil
	}

	// 使用数据库直接操作来批量插入结果
	model := &persistent.ILHForecastTaskResultTab{}
	db, err := dbutil.MasterDB(ctx, model)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.Table(model.TableName()).CreateInBatches(results, 100).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, results, err)
	}

	return nil
}
