package forecastrepo

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestServiceImpl_GetDraftTaskByProductId(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("grpc_server"))
	configutil.Init()
	dbutil.Init()

	ctx := context.TODO()
	hook := persistent.ForecastingTaskHook
	db, err := dbutil.MasterDB(ctx, hook)
	if err != nil {
		panic(err)
	}
	ctx = scormv2.BindContext(ctx, db)
	hook.Id = 30
	hook.TaskName = "dhdhdhdhd"
	scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		if err := scormv2.Context(ctx).Updates(hook).GetError(); err != nil {
			return err
		}

		return errors.New("dddd")
	})

}
