package forecastrepo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/volumeutil"
	"sort"
	"strconv"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
	foreschema "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	jsoniter "github.com/json-iterator/go"
)

type IForecastRepo interface {
	GetDraftTaskByProductId(ctx context.Context, productId int, isMultiProduct bool, routingType uint8) (*persistent.ForecastingTaskTab, *srerr.Error)
	GetDraftTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	GetEditAbleTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	GetTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error)
	GetTaskList(ctx context.Context, status *int, id, productId, offset, limit int, routingType uint8, isMultiProduct bool) ([]*persistent.ForecastingTaskTab, int, *srerr.Error)
	CreateTask(ctx context.Context, task *persistent.ForecastingTaskTab) (*persistent.ForecastingTaskTab, *srerr.Error)
	UpdateTask(ctx context.Context, task *persistent.ForecastingTaskTab) (*persistent.ForecastingTaskTab, *srerr.Error)
	DeleteTaskById(ctx context.Context, id int) *srerr.Error
	GetFirstPendingTaskWithRoutingType(ctx context.Context, routingType []int) (*persistent.ForecastingTaskTab, *srerr.Error)
	GetTaskError(ctx context.Context, id int) (string, *srerr.Error)
	GetResultByTaskId(ctx context.Context, taskId int) ([]persistent.ForecastingTaskResultTab, *srerr.Error)
	InsertForecastResults(ctx context.Context, results []*persistent.ForecastingTaskResultTab) *srerr.Error
	InsertForecastRules(ctx context.Context, rules []*persistent.ForecastingRuleTab) *srerr.Error
	UpdateForecastRules(ctx context.Context, rules []*persistent.ForecastingRuleTab) *srerr.Error
	GetForecastRulesByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastingRuleTab, *srerr.Error)
	GetForecastRuleById(ctx context.Context, id int) (*persistent.ForecastingRuleTab, *srerr.Error)
	DeleteForecastRuleByTaskId(ctx context.Context, taskId int) *srerr.Error
	DeleteForecastRuleByIds(ctx context.Context, ids []int) *srerr.Error

	GetHardCriteriaTask(ctx context.Context, condition map[string]interface{}) ([]persistent.HardCriteriaTaskTab, *srerr.Error)                 //获取硬性校验任务
	GetHardCriteriaTaskByLastUpdateTime(ctx context.Context, condition map[string]interface{}) ([]persistent.HardCriteriaTaskTab, *srerr.Error) //根据last update time进行升序获取
	GetHardCriteriaTaskById(ctx context.Context, id uint64) (persistent.HardCriteriaTaskTab, *srerr.Error)

	CreateHardCriteriaTask(ctx context.Context, tab *persistent.HardCriteriaTaskTab) *srerr.Error                                //插入硬性校验任务
	UpdateHardCriteriaTaskById(ctx context.Context, id uint64, taskStatus int, tab persistent.HardCriteriaTaskTab) *srerr.Error  //用结构体更新硬性校验任务
	UpdateHardCriteriaWithDefault(ctx context.Context, id uint64, taskStatus int, condition map[string]interface{}) *srerr.Error //用map更新硬性校验任务（更新0值）

	DeleteHardCriteriaTaskById(ctx context.Context, id uint64, taskStatus int) *srerr.Error //删除硬性校验刷新的任务

	GetHardCriteriaTaskList(ctx context.Context, request foreschema.GetHCTaskListRequest) (results []persistent.HardCriteriaTaskTab, total int, err *srerr.Error) //获取硬性校验刷新任务的列表

	GetForecastVolumeRuleByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastVolumeRuleTab, *srerr.Error)

	//GetPredictionVolumesByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastingPredictionVolumeTab, *srerr.Error)
	GetPredictionVolumesByCondition(ctx context.Context, condition map[string]interface{}) ([]*persistent.ForecastingPredictionVolumeTab, *srerr.Error)
	BatchCreatePredictionVolumes(ctx context.Context, predictionVolumes []*persistent.ForecastingPredictionVolumeTab) *srerr.Error
	DeletePredictionVolumesByTaskId(ctx context.Context, taskId int) *srerr.Error
}

type ServiceImpl struct {
	ZoneRuleRepo vrrepo.ZoneRuleRepo
}

func NewServiceImpl(ZoneRuleRepo vrrepo.ZoneRuleRepo) *ServiceImpl {
	return &ServiceImpl{ZoneRuleRepo: ZoneRuleRepo}
}

func (p *ServiceImpl) GetDraftTaskByProductId(ctx context.Context, productId int, isMultiProduct bool, routingType uint8) (*persistent.ForecastingTaskTab, *srerr.Error) {
	r := new(persistent.ForecastingTaskTab)
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	d := db.Table(persistent.ForecastingTaskHook.TableName()).Take(r, "product_id = ? AND task_status = ? AND is_multi_product = ? AND routing_type = ?",
		productId, persistent.TaskStatusDraft, isMultiProduct, routingType)
	return r, srerr.With(srerr.TaskNotFound, nil, d.GetError())
}

func (p *ServiceImpl) GetDraftTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	r := new(persistent.ForecastingTaskTab)
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	d := db.Table(persistent.ForecastingTaskHook.TableName()).Take(r, "id = ? AND task_status = ?",
		id, persistent.TaskStatusDraft)
	return r, srerr.With(srerr.TaskNotFound, nil, d.GetError())
}

func (p *ServiceImpl) GetEditAbleTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	r := new(persistent.ForecastingTaskTab)
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	d := db.Table(persistent.ForecastingTaskHook.TableName()).Take(r, "id = ? AND task_status in (0, 1)",
		id)
	return r, srerr.With(srerr.TaskNotFound, nil, d.GetError())
}

func returnTaskDetail(ctx context.Context, db scormv2.SQLCommon, r *persistent.ForecastingTaskTab) (*persistent.ForecastingTaskTab, *srerr.Error) {
	if db.GetError() != nil {
		return nil, srerr.With(srerr.TaskNotFound, nil, db.GetError())
	}
	for _, rule := range r.RuleList {
		if err := rule.LoadJsonField(ctx); err != nil {
			return nil, srerr.With(srerr.ParamErr, "load rule fail", err)
		}
	}
	return r, nil
}

func (p *ServiceImpl) GetTaskById(ctx context.Context, id int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	r := new(persistent.ForecastingTaskTab)
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	d := db.Table(persistent.ForecastingTaskHook.TableName()).Take(r, "id = ?", id)

	if len(r.WeightRangeDetail) > 0 {
		if err := jsoniter.Unmarshal(r.WeightRangeDetail, &r.WeightRange); err != nil {
			logger.CtxLogErrorf(ctx, "Unmarshal failed weight range %+v", err)
		}
	}

	if len(r.SimulationOrderCountDetail) > 0 {
		if err := jsoniter.Unmarshal(r.SimulationOrderCountDetail, &r.SimulationOrderCount); err != nil {
			logger.CtxLogErrorf(ctx, "Unmarshal failed forecast order count %+v", err)
		}
	}

	rules, _ := p.GetForecastRulesByTaskId(ctx, id)
	volumeRules, _ := p.GetForecastVolumeRuleByTaskId(ctx, id)
	r.RuleList = rules
	r.VolumeRuleList = volumeRules
	if r.RoutingType == rule.CBRoutingType {
		r.IsVolumeRouting = volumeutil.IsOpenVolumeRouting(ctx)
	}
	for _, forecastRule := range r.RuleList {
		forecastRule.IsVolumeRouting = r.VolumeRuleId != 0
	}
	return returnTaskDetail(ctx, d, r)
}

func (p *ServiceImpl) GetTaskList(ctx context.Context, status *int, id, productId, offset, limit int, routingType uint8, isMultiProduct bool) ([]*persistent.ForecastingTaskTab, int, *srerr.Error) {
	var rules []*persistent.ForecastingTaskTab
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, 0, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	query := db.Table(persistent.ForecastingTaskHook.TableName())
	if id > 0 {
		query = query.Where("id=?", id)
	}
	if productId > 0 {
		query = query.Where("product_id=?", productId)
	}
	if status != nil {
		query = query.Where("task_status=?", status)
	}
	query = query.Where("is_multi_product = ?", isMultiProduct)
	query = query.Where("routing_type=?", routingType)
	var total int64
	d := query.Count(&total)
	if d.GetError() != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	d = query.Order("id DESC").Offset(offset).Limit(limit).Find(&rules)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "GetTaskList fail|id=%d, productId=%d, status=%d, offset=%d, Limit=%d, err=%v",
			id, productId, status, offset, limit, d.GetError())
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	return rules, int(total), nil
}

func (p *ServiceImpl) GetFirstPendingTaskWithRoutingType(ctx context.Context, routingType []int) (*persistent.ForecastingTaskTab, *srerr.Error) {
	task := &persistent.ForecastingTaskTab{}
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, nil, err.Error())
	}
	query := db.Table(persistent.ForecastingTaskHook.TableName())
	query = query.Where("task_status = ?", persistent.TaskStatusProcessing)
	query = query.Where("routing_type in (?)", routingType)
	if err := query.First(task).GetError(); err != nil {
		if err == scormv2.ErrRecordNotFound {
			return nil, nil
		}
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	rules, ruleErr := p.GetForecastRulesByTaskId(ctx, task.Id)
	if ruleErr != nil {
		return nil, ruleErr
	}
	task.RuleList = rules
	//local 预测的时候可能会用到volume rule
	if err := p.processLocalForecast(ctx, task); err != nil {
		return nil, err
	}
	return task, nil
}

func (p *ServiceImpl) processLocalForecast(ctx context.Context, task *persistent.ForecastingTaskTab) *srerr.Error {
	if task.RoutingType == rule.LocalRoutingType || task.RoutingType == rule.SPXRoutingType {
		//获取volume rule
		volumeRuleList, err := p.GetForecastVolumeRuleByTaskId(ctx, task.Id)
		if err != nil {
			logger.CtxLogErrorf(ctx, "get forecast volume failed %v", err)
			return err
		}
		task.VolumeRuleList = volumeRuleList

		// 预测天的排序，保证预测天数是按照时间线顺序
		if len(task.SimulationOrderCount) != 0 {
			sort.Slice(task.SimulationOrderCount, func(i, j int) bool {
				return task.SimulationOrderCount[i].Day < task.SimulationOrderCount[j].Day
			})
		}
	}

	return nil
}

func (p *ServiceImpl) CreateTask(ctx context.Context, task *persistent.ForecastingTaskTab) (*persistent.ForecastingTaskTab, *srerr.Error) {
	task.CTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	task.MTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	db = db.Table(persistent.ForecastingTaskHook.TableName()).Create(task)
	if db.GetError() != nil {
		logger.CtxLogErrorf(ctx, "CreateTask fail|product_id=%v, TaskStatus=%d, TaskName=%s, operate_by=%s, err=%v",
			task.ProductId, task.TaskStatus, task.TaskName, task.OperateBy, db.GetError())
		return nil, srerr.With(srerr.DatabaseErr, nil, db.GetError())
	}
	return task, nil
}

func (p *ServiceImpl) UpdateTask(ctx context.Context, task *persistent.ForecastingTaskTab) (*persistent.ForecastingTaskTab, *srerr.Error) {
	task.MTime = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	db = db.Table(persistent.ForecastingTaskHook.TableName()).Save(task)
	if db.GetError() != nil {
		logger.CtxLogErrorf(ctx, "UpdateTask fail|product_id=%v, TaskStatus=%d, TaskName=%s, StartDate=%s, EndDate=%s, operate_by=%s, err=%v",
			task.ProductId, task.TaskStatus, task.TaskName, task.StartDate, task.EndDate, task.OperateBy, db.GetError())
		return nil, srerr.With(srerr.DatabaseErr, nil, db.GetError())
	}
	return task, nil
}

func (p *ServiceImpl) DeleteTaskById(ctx context.Context, id int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingTaskHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	r := &persistent.ForecastingTaskTab{Id: id}
	db = db.Table(persistent.ForecastingTaskHook.TableName()).Delete(r)
	if db.GetError() != nil {
		logger.CtxLogErrorf(ctx, "DeleteTaskById fail|id=%v, err=%v", id, db.GetError())
		return srerr.With(srerr.TaskNotFound, nil, db.GetError())
	}
	return nil
}

func (p *ServiceImpl) GetTaskError(ctx context.Context, id int) (string, *srerr.Error) {
	var taskError persistent.ForecastingTaskErrorTab
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskErrorHook)
	if err != nil {
		return "", srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	db = db.Table(persistent.ForecastingTaskErrorHook.TableName()).Where("task_id=?", id).Order("ctime DESC").First(&taskError)
	if db.GetError() != nil {
		return "", srerr.With(srerr.TaskErrorNotFound, nil, db.GetError())
	}
	return taskError.TaskError, nil
}

func (p *ServiceImpl) GetResultByTaskId(ctx context.Context, taskId int) ([]persistent.ForecastingTaskResultTab, *srerr.Error) {
	var results []persistent.ForecastingTaskResultTab
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingTaskResultHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	dbErr := db.Table(persistent.ForecastingTaskResultHook.TableName()).Find(&results, "task_id = ?", taskId).GetError()
	if len(results) == 0 {
		return nil, srerr.New(srerr.ResultNotFound, nil, "result not found")
	}
	if dbErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	return results, nil
}

func (p *ServiceImpl) InsertForecastResults(ctx context.Context, results []*persistent.ForecastingTaskResultTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingTaskResultHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	for _, result := range results {
		d := db.Table(persistent.ForecastingTaskResultHook.TableName()).Create(result)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, nil, d.GetError())
		}
	}
	return nil
}

func (p *ServiceImpl) InsertForecastRules(ctx context.Context, rules []*persistent.ForecastingRuleTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	for _, rule := range rules {
		d := db.Table(persistent.ForecastingRuleHook.TableName()).Create(rule)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, nil, d.GetError())
		}
	}
	return nil
}

func (p *ServiceImpl) UpdateForecastRules(ctx context.Context, rules []*persistent.ForecastingRuleTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	for _, rule := range rules {
		d := db.Table(persistent.ForecastingRuleHook.TableName()).Save(rule)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "UpdateForecastRules fail|task_id=%v, rule_id=%d, err=%v", rule.TaskId, rule.Id, d.GetError())
			return srerr.With(srerr.DatabaseErr, nil, d.GetError())
		}
	}
	return nil
}

func (p *ServiceImpl) GetForecastRulesByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastingRuleTab, *srerr.Error) {
	var rules []*persistent.ForecastingRuleTab
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return rules, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	query := db.Table(persistent.ForecastingRuleHook.TableName()).Where("task_id = ?", taskId)
	d := query.Order("priority").Find(&rules)
	if d.GetError() != nil || len(rules) == 0 {
		return rules, srerr.With(srerr.TaskRuleNotFound, nil, d.GetError())
	}
	for _, r := range rules {
		r.WhsIdList = objutil.SetStringToSliceKeepNil(r.WhsId)
		r.ZoneCodeList = objutil.SetStringToSliceKeepNil(r.ZoneCode)
		r.ItemCategoryIdList = objutil.SetStringToIntSlice(r.ItemCategoryId)
		r.DestinationPortList = objutil.SetStringToSliceKeepNil(r.DestinationPorts)
	}

	return rules, nil
}

func (p *ServiceImpl) GetForecastVolumeRuleByTaskId(ctx context.Context, taskId int) ([]*persistent.ForecastVolumeRuleTab, *srerr.Error) {
	oldRuleList, oerr := p.getForecastVolumeRuleByTaskIdOld(ctx, taskId)
	if oerr != nil {
		return nil, oerr
	}
	// 这里兼容一下老的数据读的是forecast_volume_rule_tab这张表
	if len(oldRuleList) != 0 {
		return oldRuleList, nil
	}
	forecastRules, err := p.ZoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
		"task_id = ?": taskId,
	})
	if err != nil {
		return nil, err
	}
	res := make([]*persistent.ForecastVolumeRuleTab, 0, len(forecastRules))
	for _, forecastRule := range forecastRules {
		ruleDetail, derr := p.ZoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{
			"rule_id = ?":      forecastRule.Id,
			"data_version = ?": forecastRule.DataVersion,
		})
		if derr != nil {
			logger.CtxLogErrorf(ctx, "query forecast volume rule detail err=%v", derr)
			continue
		}
		resultRule := &persistent.ForecastVolumeRuleTab{
			Id:         forecastRule.Id,
			TaskId:     taskId,
			Priority:   forecastRule.Priority,
			RuleType:   forecastRule.RuleType,
			RuleStatus: forecastRule.RuleStatus,
			ProductId:  forecastRule.ProductId,
			RuleName:   forecastRule.RuleName,
			RuleId:     int64(forecastRule.Id),
		}
		resultRule.LineLimit = map[string][]persistent.LineLimit{}
		resultRule.ZoneLimit = []persistent.ZoneLimit{}
		for _, detail := range ruleDetail {
			if detail.ZoneName != "" {
				resultRule.ZoneLimit = append(resultRule.ZoneLimit, persistent.ZoneLimit{
					LineId:                 detail.LineId,
					ZoneGroupId:            detail.GroupId,
					LineType:               detail.LineType,
					ZoneName:               detail.ZoneName,
					MinDailyLimit:          detail.Min,
					MaxDailyLimit:          detail.Max,
					MaxCodDailyLimit:       detail.MaxCod,
					MaxBulkyDailyLimit:     detail.MaxBulky,
					MaxHighValueDailyLimit: detail.MaxHighValue,
					MaxDgDailyLimit:        detail.MaxDg,
				})
			}
			resultRule.LineLimit[lfslib.LineSubTypeMap[int32(detail.LineType)]] = append(resultRule.LineLimit[lfslib.LineSubTypeMap[int32(detail.LineType)]], persistent.LineLimit{
				LineId:                 detail.LineId,
				MinDailyLimit:          detail.Min,
				MaxDailyLimit:          detail.Max,
				MaxCodDailyLimit:       detail.MaxCod,
				MaxBulkyDailyLimit:     detail.MaxBulky,
				MaxHighValueDailyLimit: detail.MaxHighValue,
				MaxDgDailyLimit:        detail.MaxDg,
				LineType:               detail.LineType,
			})
		}
		res = append(res, resultRule)
	}
	return res, nil
}

func (p *ServiceImpl) getForecastVolumeRuleByTaskIdOld(ctx context.Context, taskId int) ([]*persistent.ForecastVolumeRuleTab, *srerr.Error) {
	var result []*persistent.ForecastVolumeRuleTab
	if err := dbutil.Select(ctx, persistent.ForecastVolumeRuleHook, map[string]interface{}{
		"task_id = ?": taskId,
	}, &result); err != nil {
		logger.CtxLogErrorf(ctx, "Get forecast volume rule failed %+v", err)
		return nil, srerr.With(srerr.DatabaseErr, taskId, err)
	}

	for _, rule := range result {
		if rule.LineLimitDetail != nil {
			if err := jsoniter.Unmarshal(rule.LineLimitDetail, &rule.LineLimit); err != nil {
				logger.CtxLogErrorf(ctx, "Unmarshal LineLimit failed %+v", err)
			}
		}
		if rule.ZoneLimitDetail != nil {
			if err := jsoniter.Unmarshal(rule.ZoneLimitDetail, &rule.ZoneLimit); err != nil {
				logger.CtxLogErrorf(ctx, "Unmarshal LineLimit failed %+v", err)
			}
		}
	}

	return result, nil
}

func (p *ServiceImpl) GetForecastRuleById(ctx context.Context, id int) (*persistent.ForecastingRuleTab, *srerr.Error) {
	rs := new(persistent.ForecastingRuleTab)
	db, err := dbutil.SlaveDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	db = db.Table(persistent.ForecastingRuleHook.TableName()).Take(rs, "id = ?", id)
	if db.GetError() != nil {
		return nil, srerr.With(srerr.TaskRuleNotFound, nil, db.GetError())
	}
	rs.WhsIdList = objutil.SetStringToSlice(rs.WhsId)
	rs.ZoneCodeList = objutil.SetStringToSlice(rs.ZoneCode)
	rs.ItemCategoryIdList = objutil.SetStringToIntSlice(rs.ItemCategoryId)
	rs.DestinationPortList = objutil.SetStringToSlice(rs.DestinationPorts)

	return rs, nil
}

func (p *ServiceImpl) DeleteForecastRuleByTaskId(ctx context.Context, taskId int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	r := persistent.ForecastingRuleTab{}
	d := db.Table(persistent.ForecastingRuleHook.TableName()).Where("task_id = ?", taskId).Delete(r)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "DeleteForecastRuleByTaskId fail|taskId=%v, err=%v", taskId, d.GetError())
		return srerr.With(srerr.TaskRuleNotFound, nil, d.GetError())
	}
	return nil
}

func (p *ServiceImpl) DeleteForecastRuleByIds(ctx context.Context, ids []int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingRuleHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	r := persistent.ForecastingRuleTab{}
	d := db.Table(persistent.ForecastingRuleHook.TableName()).Where("id in (?)", ids).Delete(r)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "DeleteForecastRuleByIds fail|ids=%v, err=%v", ids, d.GetError())
		return srerr.With(srerr.TaskRuleNotFound, nil, d.GetError())
	}
	return nil
}

func (p *ServiceImpl) GetHardCriteriaTask(ctx context.Context, condition map[string]interface{}) ([]persistent.HardCriteriaTaskTab, *srerr.Error) {
	var results []persistent.HardCriteriaTaskTab
	err := dbutil.Select(ctx, persistent.HardCriteriaTaskHook, condition, &results, dbutil.WithOrder("mtime asc"))
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetHardCriteriaTask|get hard criteria fail, conditions=%v, err=%v", condition, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return results, nil
}

func (p *ServiceImpl) GetHardCriteriaTaskById(ctx context.Context, id uint64) (persistent.HardCriteriaTaskTab, *srerr.Error) {
	var result persistent.HardCriteriaTaskTab
	err := dbutil.Take(ctx, persistent.HardCriteriaTaskHook, map[string]interface{}{"id = ?": id}, &result)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get hard criteria fail, id=%v, err=%v", id, err)
		return persistent.HardCriteriaTaskTab{}, srerr.With(srerr.DatabaseErr, id, err)
	}

	return result, nil
}

func (p *ServiceImpl) CreateHardCriteriaTask(ctx context.Context, tab *persistent.HardCriteriaTaskTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.HardCriteriaTaskHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	err = db.Table(persistent.HardCriteriaTaskHook.TableName()).Create(tab).GetError()
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	return nil
}

func (p *ServiceImpl) UpdateHardCriteriaTaskById(ctx context.Context, id uint64, taskStatus int, tab persistent.HardCriteriaTaskTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.HardCriteriaTaskHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	db = db.Table(persistent.HardCriteriaTaskHook.TableName()).Where("id = ? and task_status = ?", id, taskStatus).Updates(&tab)
	if db.GetError() != nil {
		return srerr.With(srerr.DatabaseErr, nil, db.GetError())
	}
	// not affected row means no data change
	if db.RowsAffected() == 0 {
		return srerr.New(srerr.DatabaseErr, nil, "task id:%v, fail to update task", id)
	}

	return nil
}

func (p *ServiceImpl) DeleteHardCriteriaTaskById(ctx context.Context, id uint64, taskStatus int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.HardCriteriaTaskHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	r := persistent.HardCriteriaTaskTab{}
	d := db.Table(persistent.HardCriteriaTaskHook.TableName()).Where("id = ? AND task_status = ?", id, taskStatus).Delete(r)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "DeleteHardCriteriaTaskById fail|id=%v, err=%v", id, d.GetError())
		return srerr.With(srerr.DatabaseErr, nil, d.GetError())
	}
	return nil
}

func (p *ServiceImpl) GetHardCriteriaTaskList(ctx context.Context, request foreschema.GetHCTaskListRequest) (results []persistent.HardCriteriaTaskTab, total int, err *srerr.Error) {
	condition := map[string]interface{}{}
	if request.TaskType != 0 {
		condition["task_type = ?"] = request.TaskType
	}
	if request.TaskId != 0 {
		condition["id = ?"] = request.TaskId
	}
	if request.TaskName != "" {
		condition["task_name like ?"] = str.Merge("%", request.TaskName, "%")
	}
	if request.ProductId != 0 {
		condition["product_id = ?"] = request.ProductId
	}
	condition["is_multi_product = ?"] = request.IsMultiProduct
	condition["routing_type = ?"] = request.RoutingType
	//status>=0这个条件有bug，因为draft态就是0，用string来判断
	if request.TaskStatus != "" {
		v, err := strconv.Atoi(request.TaskStatus)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList| convert task status err:%+V", err)
			return results, total, srerr.With(srerr.ParamErr, nil, err)
		}
		condition["task_status = ?"] = v
	}

	//分两种情况检索：7天内的system task， 所有的ops task
	systemCondition := objutil.CopyConditionMap(condition)
	selectSys := false
	opsCondition := objutil.CopyConditionMap(condition)
	selectOps := false

	if request.TaskType != forecast.TaskTypeCreatedByOps {
		//获取最小限制时间
		todayString := recorder.Now(ctx).Format(timeutil.DateFormat)
		todayObj, tErr := time.Parse(timeutil.DateFormat, todayString)
		if tErr != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList| convert today time object err:%v", tErr)
			return results, total, srerr.With(srerr.ParseTimeErr, nil, tErr)
		}
		timeLimit := todayObj.Unix() - forecast.OneDay*7
		systemCondition["order_start_time >= ?"] = time.Unix(timeLimit, 0).Format(timeutil.DateFormat)
		systemCondition["task_type = ?"] = forecast.TaskTypeCreatedBySystem
		selectSys = true
	}

	//所有的ops task
	if request.TaskType != forecast.TaskTypeCreatedBySystem {
		opsCondition["task_type = ?"] = forecast.TaskTypeCreatedByOps
		selectOps = true
	}

	//计算system总数
	var sysTabs []persistent.HardCriteriaTaskTab
	if selectSys {
		db, err := dbutil.SlaveDB(ctx, persistent.HardCriteriaTaskHook)
		if err != nil {
			return results, total, srerr.New(srerr.DatabaseErr, "", err.Error())
		}
		where, val := objutil.WhereBuild(systemCondition)
		d := db.Table(persistent.HardCriteriaTaskHook.TableName()).Where(where, val...).Find(&sysTabs)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList fail|conditions=%v, err=%v", systemCondition, d.GetError())
			return results, total, srerr.New(srerr.DatabaseErr, "", d.GetError().Error())
		}
	}

	//计算ops总数
	var opsTabs []persistent.HardCriteriaTaskTab
	if selectOps {
		db, err := dbutil.SlaveDB(ctx, persistent.HardCriteriaTaskHook)
		if err != nil {
			return results, total, srerr.New(srerr.DatabaseErr, "", err.Error())
		}
		where, val := objutil.WhereBuild(opsCondition)
		d := db.Table(persistent.HardCriteriaTaskHook.TableName()).Where(where, val...).Find(&opsTabs)
		if d.GetError() != nil {
			logger.CtxLogErrorf(ctx, "GetHardCriteriaTaskList fail|conditions=%v, err=%v", opsCondition, d.GetError())
			return results, total, srerr.New(srerr.DatabaseErr, "", d.GetError().Error())
		}
	}

	//合并切片并排序
	allTabs := append(sysTabs, opsTabs...)
	sort.Sort(persistent.HCTaskTabs(allTabs))
	total = len(allTabs)

	//获取总列表的起止下标
	sIndex := int((request.PageNo - 1) * request.PageCount)
	eIndex := sIndex + int(request.PageCount)

	if sIndex >= total {
		return allTabs, total, nil
	}
	if eIndex >= total {
		return allTabs[sIndex:], total, nil
	}
	return allTabs[sIndex:eIndex], total, nil

}

// UpdateHardCriteriaWithDefault 用map更新硬性校验任务（更新零值）
func (p *ServiceImpl) UpdateHardCriteriaWithDefault(ctx context.Context, id uint64, taskStatus int, condition map[string]interface{}) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.HardCriteriaTaskHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}
	db = db.Table(persistent.HardCriteriaTaskHook.TableName()).Where("id = ? and task_status = ?", id, taskStatus).Updates(condition)
	if db.GetError() != nil {
		return srerr.With(srerr.DatabaseErr, nil, db.GetError())
	}

	return nil

}

func (p *ServiceImpl) GetHardCriteriaTaskByLastUpdateTime(ctx context.Context, condition map[string]interface{}) ([]persistent.HardCriteriaTaskTab, *srerr.Error) {
	var results []persistent.HardCriteriaTaskTab

	err := dbutil.Select(ctx, persistent.HardCriteriaTaskHook, condition, &results, dbutil.WithOrder("`last_end_time` asc, `ctime` asc"))
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	return results, nil
}

func (p *ServiceImpl) BatchCreatePredictionVolumes(ctx context.Context, predictionVolumes []*persistent.ForecastingPredictionVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingPredictionVolumeHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, "", err.Error())
	}

	if err := db.CreateInBatches(predictionVolumes, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (p *ServiceImpl) DeletePredictionVolumesByTaskId(ctx context.Context, taskId int) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, persistent.ForecastingPredictionVolumeHook)
	if err != nil {
		return srerr.New(srerr.DatabaseErr, taskId, err.Error())
	}

	if err := db.Where("task_id = ?", taskId).Delete(persistent.ForecastingPredictionVolumeHook).GetError(); err != nil {
		return srerr.New(srerr.DatabaseErr, taskId, err.Error())
	}

	return nil
}

func (p *ServiceImpl) GetPredictionVolumesByCondition(ctx context.Context, condition map[string]interface{}) ([]*persistent.ForecastingPredictionVolumeTab, *srerr.Error) {
	var predictionVolumes []*persistent.ForecastingPredictionVolumeTab
	if err := dbutil.Select(ctx, persistent.ForecastingPredictionVolumeHook, condition, &predictionVolumes); err != nil {
		return nil, srerr.New(srerr.DatabaseErr, nil, err.Error())
	}

	return predictionVolumes, nil
}
