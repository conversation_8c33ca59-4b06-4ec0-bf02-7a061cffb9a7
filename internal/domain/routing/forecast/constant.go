package forecast

import (
	"context"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
)

// 硬性校验任务状态 0-draft(只有draft态允许编辑更新), 1-waiting, 2-pending, 3-doing, 4-done, 5-stopped, 6-terminated, 7-failed
const (
	TaskDraft      = 0
	TaskWaiting    = 1
	TaskPending    = 2
	TaskDoing      = 3
	TaskDone       = 4
	TaskStopped    = 5
	TaskTerminated = 6
	TaskFailed     = 7
)

const (
	OneDay = 86400 //unix下， 1天的差值（2022-02-15和2022-02-14相差86400秒）
)

const (
	TaskTypeCreatedByOps    = 1
	TaskTypeCreatedBySystem = 2
)

const (
	PrefixAllDay     = "all-day"     //redis key前缀，所有的日期
	PrefixCurrentDay = "current-day" //redis key前缀，当天
)

// redis lock key for obtain refresh criteria task
const ObtainTaskKey = "obtain_refresh_criteria_task"

// Blocked is the pseudo "lane_code" for orders that can no longer be routed to a valid lane
const (
	Blocked             = "Block"
	RuleBlocked         = "RuleBlock"
	AllBlocked          = "AllBlock"
	RoutingFailed       = "RoutingFailed"
	DefaultBlockPercent = 10
)

const ForecastedOrderCount = "forecasted_order_count"
const LastForecastedIndex = "last_forecasted_index"
const LastForecastedDay = "last_forecasted_day"
const LastHCIndex = "last_hc_day"
const FinishTask = 100.0

type TaskId int
type ForecastFlag struct{}

func IsForecast(ctx context.Context) (bool, int) {
	taskId := ctx.Value(ForecastFlag{})
	if taskId == nil {
		return false, 0
	}
	id, ok := taskId.(TaskId)
	if !ok {
		return false, 0
	}
	return true, int(id)
}

func IsLocalForecast(ctx context.Context, routingType uint8) bool {
	isForecast, _ := IsForecast(ctx)
	return isForecast && routingType == rule.LocalRoutingType
}

func NewForecastCtxWithTaskId(ctx context.Context, tasId int) context.Context {
	return context.WithValue(ctx, ForecastFlag{}, TaskId(tasId))
}

func IsDisplayOnOps(ctx context.Context) bool {
	flag := ctx.Value(ForecastFlag{})
	if flag == nil {
		return false
	}
	_, ok := flag.(bool)
	return ok
}

func SetDisplayOnOpsFlag(ctx context.Context) context.Context {
	return context.WithValue(ctx, ForecastFlag{}, true)
}
