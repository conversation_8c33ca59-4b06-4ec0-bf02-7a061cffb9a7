package forecastentity

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	availableLHEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	lhCapacityEntity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/persistent"
)

type LFSOrderInfo struct {
	OrderSN          string                  `json:"order_sn"`
	ForderId         string                  `json:"forderid"`
	HardResult       []*rule.RoutingLaneInfo `json:"hard_result"`
	Skus             []*pb.CreateOrderSKU    `json:"skus"`
	WhsCode          string                  `json:"whs_code"`
	LocationIdList   []uint                  `json:"location_id_list"`
	Cogs             float32                 `json:"cogs"`
	ValidationWeight int                     `json:"validation_weight"`
	DgType           int                     `json:"dg_type"`
	ParcelLength     float64                 `json:"parcel_length"`
	ParcelWidth      float64                 `json:"parcel_width"`
	ParcelHeight     float64                 `json:"parcel_height"`
	ParcelWeight     float64                 `json:"parcel_weight"`
	IsCod            bool                    `json:"is_cod"`
	DeliverPostCode  string                  `json:"deliver_post_code"`
	ShopId           string                  `json:"shop_id"`
	LineAsfMap       map[string]AsfInfo      `json:"line_asf_map"`
}

type ForecastLaneInfo struct {
	LaneCode       string
	Site           string
	BuyerCityId    int64
	ServiceCode    string
	DGFlag         int
	RuleId         int
	Priority       int32
	OutActualPoint string
	WeightRange    persistent.WeightRange
	StatDate       string
}

type ForecastLaneResult struct {
	ShippingFee       float64
	Quantity          int
	IlhParcelQuantity int
	Weight            int
	AsfInfoList       []AsfInfo
}

type AsfInfo struct {
	HasAsf bool    `json:"has_asf"`
	Asf    float64 `json:"asf"`
	AsfUsd float64 `json:"asf_usd"`
}

type ForecastLaneIncrInfo struct {
	ForecastLane      ForecastLaneInfo
	Quantity          int
	IlhParcelQuantity int
	ShippingFee       float64
	Weight            int
	SkuWeight         float32
	HasAsf            bool
	Asf               float64
	AsfUsd            float64
}

type LaneLineInfo struct {
	FlId   string
	FlName string
	LmId   string
	LmName string
}

type SimpleLaneInfo struct {
	LaneCode    string
	FlId        string
	LmId        string
	DGFlag      int
	ServiceCode string
}

type SimpleResourceInfo struct {
	FlId      string
	FlName    string
	LmId      string
	LmName    string
	TwsId     string
	TwsName   string
	JointId   string
	JointName string
}

type LHSOrderInfo struct {
	// 基础信息，从LHS DB中直接获取
	LhsTn          string   `json:"lho_tn"`
	MultiProductId int      `json:"multi_product_id"`
	DgType         int      `json:"dg_type"`
	DgChange       int      `json:"dg_change"`
	IlhGroupId     string   `json:"ilh_group_id"`
	ServiceCode    string   `json:"service_code"`
	TwsCode        string   `json:"tws_code"`
	Ctime          int      `json:"ctime"`
	ActualWeight   int      `json:"actual_weight"` // g
	SloList        []string `json:"slo_list"`
	ParcelQuantity int      `json:"parcel_quantity"`

	// 基于基础信息从LPS、LFS中获取
	AvailableLanes []*rule.RoutingLaneInfo `json:"available_lanes"`
	LmId           string                  `json:"lm_id"`
}

type ForecastAvailableLHRule struct {
	MultiProductID int                                 `json:"multi_product_id"`
	Rules          []availableLHEntity.AvailableLHRule `json:"rules"`
}

type ForecastLHCapacityItem struct {
	CapacityName     string                             `json:"capacity_name" validate:"required"`
	ILHVendorName    string                             `json:"ilh_vendor_name" validate:"required"`
	ILHLineID        string                             `json:"ilh_line_id" validate:"required"`
	ILHLineName      string                             `json:"ilh_line_name" validate:"required"`
	TWS              []string                           `json:"tws" validate:"required"`
	DestinationPort  []string                           `json:"destination_port" validate:"required"`
	DGType           rule.DGFlag                        `json:"dg_type" validate:"required"`
	CapacitySettings []lhCapacityEntity.CapacitySetting `json:"capacity_settings" validate:"required"`
}

// ILHForecastResult ILH预测结果领域实体
type ILHForecastResult struct {
	// 任务和规则信息
	TaskID                  int
	AvailableLHRuleName     string
	AvailableLHRulePriority int

	// 产品信息
	MultiProductID int

	// 航线信息
	LaneCode        string
	DestinationPort string

	// DG类型
	DGType rule.DGFlag

	// 容量使用情况
	ReversedBSAWeight    float64
	NonReversedBSAWeight float64
	AdhocWeight          float64
}
