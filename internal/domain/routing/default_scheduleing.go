package routing

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
)

func (rt *routingTask) defaultScheduling(ctx context.Context, matchedRule *rule.RoutingRuleParsed, logEntry *routing_log.RoutingLog, scheduleFactorCountMap map[string]int) []*rule.RoutingLaneInfo {
	var validLines, lineListInput []*rule.LineInfo
	var validLanes []*rule.RoutingLaneInfo
	var processData interface{}

	setDefaultSchedulingLogs(logEntry, matchedRule, rt)
	defer func() {
		if logEntry != nil {
			logEntry.RoutingResult.DefaultCriteriaFilterProcess.After = getDebugLaneInfo(validLanes)
		}
	}()

	for _, ruleEntry := range matchedRule.RuleStepResourceList {
		if ruleEntry.ResourceSubType == 0 {
			continue
		}

		logger.CtxLogInfof(ctx, "default.select.%v.begin", ruleEntry.DisplayResourceType)

		lineListInput = rt.retrieveLineFromProcedureLaneList()[ruleEntry.ResourceSubType]

		schedulerFactor := rule.ScheduleFactorAttr{
			Name:               fmt.Sprintf("%s-%s", matchedRule.DefaultCriteriaType.String(), ruleEntry.DisplayResourceType),
			DefaultCriteriaCfg: matchedRule.DefaultPriorities[ruleEntry.ResourceSubType][matchedRule.DefaultCriteriaType],
		}

		before := getDebugInfoForLineList(lineListInput)
		sc := schedule_factor.GetScheduleFactor(matchedRule.DefaultCriteriaName)
		validLines, processData = sc.FilterLine(ctx, lineListInput, nil, &schedulerFactor)
		scheduleFactorCountMap[matchedRule.DefaultCriteriaName]++
		//monitoring.ReportSuccess(ctx, monitoring.CatScheduledFactor, matchedRule.DefaultCriteriaName, "")

		after := getDebugInfoForLineList(validLines)
		//记录默认校验每个stage的before和after lane
		beforeLanes := rt.getLaneCode(lineListInput)
		afterLanes := rt.getLaneCode(validLines)
		logger.CtxLogInfof(ctx, "facot.%v.in=%v|out=%v", sc.Name(), before, after)
		if logEntry != nil {
			logEntry.RoutingResult.DefaultCriteriaFilterProcess.StageList = append(logEntry.RoutingResult.DefaultCriteriaFilterProcess.StageList,
				routing_log.DefaultCriteriaRuleStage{
					ResourceSubType: ruleEntry.ResourceSubType,
					FactorName:      schedulerFactor.Name,
					Before:          before,
					BeforeLanes:     beforeLanes,
					AfterLanes:      afterLanes,
					After:           after,
					ProcessData:     processData,
				})
		}

		validLanes = rt.GetAvailableLaneBylines(validLines)
		logger.CtxLogInfof(ctx, "default.select.%v.end | in=%v | out = %v", ruleEntry.DisplayResourceType, getDebugLaneInfo(rt.routingData.ValidateLaneList), getDebugLaneInfo(validLanes))

		if len(validLanes) == 1 {
			return validLanes
		}
		rt.routingData.ValidateLaneList = validLanes
	}
	//len(validLanes) > 1
	//注释下,spx 应该是待废弃的。
	availableLanes := rt.filterBySpxSelfBuild(ctx, matchedRule, validLanes)
	logger.CtxLogInfof(ctx, "final.filterBySpxSelfBuild. inlen=%v|outlen=%v", len(validLanes), len(availableLanes))
	setupSPxSelfBuild(logEntry, getDebugLaneInfo(validLanes), getDebugLaneInfo(availableLanes))
	//todo ,to delete
	//if availableLanes != nil {
	//	return availableLanes[0]
	//}
	//return nil
	return availableLanes
}

func (rt *routingTask) getLaneCode(lines []*rule.LineInfo) []string {
	lanes := rt.GetAvailableLaneBylines(lines)
	res := []string{}
	for _, lane := range lanes {
		res = append(res, lane.LaneCode)
	}

	return res
}
