package routing

import (
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	jsoniter "github.com/json-iterator/go"
	"reflect"
	"strconv"
	"testing"
)

func Test_routingRuleRepoImpl_UpdateForecastRuleStatusActive(t *testing.T) {
	ctx := context.Background()
	mockRuleRepo := &ruledata.SoftRuleRepoImpl{}
	mockLpsApi := &lpsclient.LpsApiImpl{}
	rr := &routingRuleRepoImpl{}
	shopGroupList, _ := jsoniter.Marshal([]int64{1, 2, 3})
	var patch, patchMasterDB, patchUpdateProductRuleStatusExpired, patchUpdateRuleForecastToActive, patchCopyShopGroupInfo *gomonkey.Patches
	type args struct {
		routingType    int
		isMultiProduct bool
	}
	tests := []struct {
		name  string
		args  args
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: GetActiveAndForecastRoutingRule failed",
			args: args{},
			setup: func() {
				rr = &routingRuleRepoImpl{
					ruleRepo: mockRuleRepo,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockRuleRepo), "GetActiveAndForecastRoutingRule", func(s *ruledata.SoftRuleRepoImpl, ctx context.Context, routingType int, isMultiProduct bool) ([]*ruledata.RoutingRuleTab, error) {
					return nil, errors.New("GetActiveAndForecastRoutingRule failed")
				})
			},
		},
		{
			name: "case 2: dbutil.MasterDB failed",
			args: args{},
			setup: func() {
				rr = &routingRuleRepoImpl{
					ruleRepo: mockRuleRepo,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockRuleRepo), "GetActiveAndForecastRoutingRule", func(s *ruledata.SoftRuleRepoImpl, ctx context.Context, routingType int, isMultiProduct bool) ([]*ruledata.RoutingRuleTab, error) {
					return []*ruledata.RoutingRuleTab{
						{
							ProductID: 1,
							ID:        1,
							Status:    rule.RuleStatusForecast,
						},
					}, nil
				})
				patchMasterDB = gomonkey.ApplyFunc(dbutil.MasterDB, func(ctx context.Context, model dbutil.DBModel) (scormv2.SQLCommon, error) {
					return nil, errors.New("mock MasterDB failed")
				})
			},
		},
		{
			name: "case 3: Update before rule status to expire failed",
			args: args{
				routingType: rule.IlhRoutingType,
			},
			setup: func() {
				dbutil.InitTest()
				rr = &routingRuleRepoImpl{
					ruleRepo: mockRuleRepo,
					lps:      mockLpsApi,
				}
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockRuleRepo), "GetActiveAndForecastRoutingRule", func(s *ruledata.SoftRuleRepoImpl, ctx context.Context, routingType int, isMultiProduct bool) ([]*ruledata.RoutingRuleTab, error) {
					return []*ruledata.RoutingRuleTab{
						{
							ProductID:     1,
							ID:            1,
							Status:        rule.RuleStatusForecast,
							ShopGroupList: shopGroupList,
						},
						{
							ProductID: 1,
							ID:        1,
							Status:    rule.RuleStatusActive,
						},
					}, nil
				})
				patchUpdateProductRuleStatusExpired = gomonkey.ApplyMethod(reflect.TypeOf(mockRuleRepo), "UpdateProductRuleStatusExpired", func(s *ruledata.SoftRuleRepoImpl, ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error {
					return nil
				})
				patchUpdateRuleForecastToActive = gomonkey.ApplyMethod(reflect.TypeOf(mockRuleRepo), "UpdateRuleForecastToActive", func(s *ruledata.SoftRuleRepoImpl, ctx context.Context, ruleIds []int64, tx scormv2.SQLCommon) error {
					return nil
				})
				patchCopyShopGroupInfo = gomonkey.ApplyMethod(reflect.TypeOf(mockLpsApi), "CopyShopGroupInfo", func(p *lpsclient.LpsApiImpl, ctx context.Context, req *lpsclient.CopyShopGroupReq) *srerr.Error {
					return srerr.With(srerr.LpsError, nil, errors.New("mock CopyShopGroupInfo failed"))
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			rr.UpdateForecastRuleStatusActive(ctx, tt.args.routingType, tt.args.isMultiProduct)
			if patch != nil {
				patch.Reset()
			}
			if patchMasterDB != nil {
				patchMasterDB.Reset()
			}
			if patchUpdateProductRuleStatusExpired != nil {
				patchUpdateProductRuleStatusExpired.Reset()
			}
			if patchUpdateRuleForecastToActive != nil {
				patchUpdateRuleForecastToActive.Reset()
			}
			if patchCopyShopGroupInfo != nil {
				patchCopyShopGroupInfo.Reset()
			}
		})
	}
}

func Test_parseAndRebuildRule(t *testing.T) {
	ctx := context.Background()
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	var patch *gomonkey.Patches
	type args struct {
		r *ruledata.RoutingRuleTab
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRuleParsed
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: ParseRuleData failed",
			args: args{
				r: &ruledata.RoutingRuleTab{},
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
			setup:   func() {},
		},
		{
			name: "case 2: ParseRuleData failed",
			args: args{
				r: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
				},
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("buildErr")),
			setup: func() {
				patch = gomonkey.ApplyFunc(RebuildRuleData, func(ctx context.Context, effectiveRule *rule.RoutingRule) (*rule.RoutingRuleParsed, *srerr.Error) {
					return nil, srerr.With(srerr.RoutingRuleException, "", errors.New("buildErr"))
				})
			},
		},
		{
			name: "case 3: normal result",
			args: args{
				r: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
					CCMode:                rule.CCModeCCRouting,
				},
			},
			want: &rule.RoutingRuleParsed{
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				CCMode:              rule.CCModeCCRouting,
			},
			wantErr: nil,
			setup:   func() {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := parseAndRebuildRule(ctx, tt.args.r)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func Test_parseAndRebuildRuleWithLru(t *testing.T) {
	ctx := context.Background()
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	type args struct {
		r *ruledata.RoutingRuleTab
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRuleParsed
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: cacheVal.(*rule.RoutingRuleParsed) success",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ID: 1,
				},
			},
			want:    &rule.RoutingRuleParsed{},
			wantErr: nil,
			setup: func() {
				parseRoutingRuleLruCache.Add(ctx, strconv.Itoa(1), &rule.RoutingRuleParsed{})
			},
		},
		{
			name: "case 2: parseAndRebuildRule failed",
			args: args{
				r: &ruledata.RoutingRuleTab{},
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
			setup:   func() {},
		},
		{
			name: "case 3: parseAndRebuildRule success",
			args: args{
				r: &ruledata.RoutingRuleTab{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
					CCMode:                rule.CCModeCCRouting,
				},
			},
			want: &rule.RoutingRuleParsed{
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				CCMode:              rule.CCModeCCRouting,
			},
			wantErr: nil,
			setup:   func() {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := parseAndRebuildRuleWithLru(ctx, tt.args.r)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func Test_routingRuleRepoImpl_isRuleMatch(t *testing.T) {
	ctx := context.Background()
	rr := &routingRuleRepoImpl{}
	type args struct {
		r          *ruledata.RoutingRuleTab
		matchParam RuleMatchParam
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: len(ruleWhsIdList) != 0 && !objutil.ContainStr(ruleWhsIdList, matchParam.WhsID)",
			args: args{
				r: &ruledata.RoutingRuleTab{
					WhsId: `["1","2"]`,
				},
				matchParam: RuleMatchParam{
					WhsID: "3",
				},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 2: CheckZoneContainDistrict failed",
			args: args{
				r: &ruledata.RoutingRuleTab{
					RoutingType: rule.SPXRoutingType,
					ZoneCode:    `["1","2"]`,
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{
					zoneRepo: &locationzone.ZoneRepoImpl{},
				}
			},
		},
		{
			name: "case 3: CheckItemCategoryInRule failed",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ItemCategoryIDList: `[1, 2]`,
					ItemCategoryLevel:  1,
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 4: Check Cogs",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ParcelValueMax: 1000000,
					ParcelValueMin: 9999,
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 5: Check ParcelDimension.Length",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ParcelDimension: rule.ParcelDimension{
						LengthMax: 100,
						LengthMin: 99,
					},
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 6: Check ParcelDimension.Width",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ParcelDimension: rule.ParcelDimension{
						WidthMax: 100,
						WidthMin: 99,
					},
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 7: Check ParcelDimension.Height",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ParcelDimension: rule.ParcelDimension{
						HeightMax: 100,
						HeightMin: 99,
					},
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 8: Check ParcelDimension.Weight",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ParcelWeightMax: 100,
					ParcelWeightMin: 99,
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 9: Check HaveIntersection",
			args: args{
				r: &ruledata.RoutingRuleTab{
					DestinationPorts: `["1","2"]`,
				},
				matchParam: RuleMatchParam{
					DestinationPorts: []string{"3"},
				},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 10: Check DgType",
			args: args{
				r: &ruledata.RoutingRuleTab{
					DgType: int(rule.DG),
				},
				matchParam: RuleMatchParam{
					DgType: int(rule.NonDG),
				},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 11: Check cb shop group",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ShopGroupListVo: []int64{1},
				},
				matchParam: RuleMatchParam{},
			},
			want: false,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
		{
			name: "case 12: success",
			args: args{
				r: &ruledata.RoutingRuleTab{
					ShopGroupListVo: []int64{1},
					ClientEntityGroupMap: map[int64]int64{
						0: 1,
					},
				},
				matchParam: RuleMatchParam{},
			},
			want: true,
			setup: func() {
				rr = &routingRuleRepoImpl{}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := rr.isRuleMatch(ctx, tt.args.r, tt.args.matchParam); got != tt.want {
				t.Errorf("isRuleMatch() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_routingRuleRepoImpl_MatchRoutingRulesByRuleList(t *testing.T) {
	ctx := context.Background()
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	rr := &routingRuleRepoImpl{}
	type args struct {
		ruleList   []*ruledata.RoutingRuleTab
		matchParam RuleMatchParam
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.RoutingRuleParsed
		wantErr *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: len(matchedRoutingRules) == 0",
			args:    args{},
			want:    nil,
			wantErr: srerr.With(srerr.RuleNotFound, "", fmt.Errorf("rule not found %d", 0)),
		},
		{
			name: "case 2: parseAndRebuildRuleWithLru failed",
			args: args{
				ruleList: []*ruledata.RoutingRuleTab{{}},
			},
			want:    nil,
			wantErr: srerr.With(srerr.RoutingRuleException, "", errors.New("readObjectStart: expect { or n, but found \u0000, error found in #0 byte of ...||..., bigger context ...||...")),
		},
		{
			name: "case 3: success",
			args: args{
				ruleList: []*ruledata.RoutingRuleTab{
					{
						Rules:                 byteRuleDetails,
						StrCombinationSetting: byteCombinationSetting,
						StrDefaultCriteria:    byteDefaultCriteria,
						StrDisabledInfo:       byteDisabledInfo,
						CCMode:                rule.CCModeCCRouting,
					},
				},
			},
			want: []*rule.RoutingRuleParsed{{
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				CCMode:              rule.CCModeCCRouting,
			}},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotErr := rr.MatchRoutingRulesByRuleList(ctx, tt.args.ruleList, tt.args.matchParam)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func Test_routingRuleRepoImpl_MatchRoutingRules(t *testing.T) {
	ctx := context.Background()
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	rr := &routingRuleRepoImpl{}
	type args struct {
		matchParam RuleMatchParam
	}
	tests := []struct {
		name    string
		args    args
		want    []*rule.RoutingRuleParsed
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: get rule list from cache fail",
			args:    args{},
			want:    nil,
			wantErr: srerr.New(srerr.RuleNotFound, nil, "get rule list from cache fail|product_id=%v,err=%v", 0, errors.New("local cache manger init not finished")),
			setup: func() {
			},
		},
		{
			name:    "case 2: fail to convert",
			args:    args{},
			want:    nil,
			wantErr: srerr.New(srerr.RuleNotFound, nil, "fail to convert,product_id=%v", 0),
			setup: func() {
				localcache.InitTest(constant.RoutingRule, ruledata.MemKeyRoutingRule(0), 1, 1)
			},
		},
		{
			name:    "case 3: len(validRules) == 0",
			args:    args{},
			want:    nil,
			wantErr: srerr.New(srerr.RuleNotFound, nil, "rule not found, productID:%d", 0),
			setup: func() {
				localcache.InitTest(constant.RoutingRule, ruledata.MemKeyRoutingRule(0), []*ruledata.RoutingRuleTab{{}}, 1)
			},
		},
		{
			name:    "case 4: MatchRoutingRulesByRuleList failed",
			args:    args{},
			want:    nil,
			wantErr: srerr.With(srerr.RuleNotFound, 0, fmt.Errorf("rule not found %d", 0)),
			setup: func() {
				localcache.InitTest(constant.RoutingRule, ruledata.MemKeyRoutingRule(0), []*ruledata.RoutingRuleTab{
					{
						ParcelWeightMin: 99,
						ParcelWeightMax: 100,
						Status:          rule.RuleStatusActive,
					},
					{
						ParcelWeightMin: 99,
						ParcelWeightMax: 100,
						Status:          rule.RuleStatusActive,
						Priority:        11,
					},
				}, 1)
			},
		},
		{
			name: "case 5: success",
			args: args{},
			want: []*rule.RoutingRuleParsed{{
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				CCMode:              rule.CCModeCCRouting,
			}},
			wantErr: nil,
			setup: func() {
				localcache.InitTest(constant.RoutingRule, ruledata.MemKeyRoutingRule(0), []*ruledata.RoutingRuleTab{{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
					CCMode:                rule.CCModeCCRouting,
					Status:                rule.RuleStatusActive,
				}}, 1)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := rr.MatchRoutingRules(ctx, tt.args.matchParam)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}

func Test_routingRuleRepoImpl_MatchFirstPriorityRoutingRule(t *testing.T) {
	ctx := context.Background()
	rr := &routingRuleRepoImpl{}
	byteRuleDetails, _ := jsoniter.Marshal(&rule.RuleDetails{})
	byteCombinationSetting, _ := jsoniter.Marshal([]*rule.CombinationSetting{})
	byteDefaultCriteria, _ := jsoniter.Marshal(&rule.DefaultCriteria{})
	byteDisabledInfo, _ := jsoniter.Marshal([]*rule.DisabledInfo{})
	type args struct {
		matchParam RuleMatchParam
	}
	tests := []struct {
		name    string
		args    args
		want    *rule.RoutingRuleParsed
		wantErr *srerr.Error
		setup   func()
	}{
		// TODO: Add test cases.
		{
			name:    "case 1: MatchRoutingRules fail",
			args:    args{},
			want:    nil,
			wantErr: srerr.New(srerr.RuleNotFound, nil, "get rule list from cache fail|product_id=%v,err=%v", 0, errors.New("local cache manger init not finished")),
			setup:   func() {},
		},
		{
			name: "case 2: success return matchedRules[0]",
			args: args{},
			want: &rule.RoutingRuleParsed{
				IlhRuleSteps: []*rule.ScheduleFactorAttr{
					{
						CombinationMode: true,
						Name:            "CombinationPriority",
					},
				},
				DefaultCriteriaName: "DefaultWeightage",
				CCMode:              rule.CCModeCCRouting,
			},
			wantErr: nil,
			setup: func() {
				localcache.InitTest(constant.RoutingRule, ruledata.MemKeyRoutingRule(0), []*ruledata.RoutingRuleTab{{
					Rules:                 byteRuleDetails,
					StrCombinationSetting: byteCombinationSetting,
					StrDefaultCriteria:    byteDefaultCriteria,
					StrDisabledInfo:       byteDisabledInfo,
					CCMode:                rule.CCModeCCRouting,
					Status:                rule.RuleStatusActive,
				}}, 1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := rr.MatchFirstPriorityRoutingRule(ctx, tt.args.matchParam)
			common.AssertResult(t, got, tt.want, gotErr, tt.wantErr)
		})
	}
}
