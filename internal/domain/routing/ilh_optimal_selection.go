package routing

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
)

// 得分计算器类型定义
type scoreCalculatorFunc func(ilh string, weightage float64, capacitySetting entity2.ILHCapacitySettingInfo, productBSAWeight int64, timestamp int64) (float64, bool)

// selectOptimalILH 通用最优ILH选择函数 - 使用给定的得分计算器选择最优的ILH线路
func (rs *ILHRoutingServiceImpl) selectOptimalILH(
	ctx context.Context,
	ilhLineList []string,
	productID int,
	dgType int,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	timestamp int64,
	scoreCalculator scoreCalculatorFunc,
) string {
	if len(ilhLineList) == 0 {
		return ""
	}

	type lineScore struct {
		lineID string
		score  float64
	}

	var scores []lineScore
	maxScore := float64(-1) // 初始化为-1确保任何有效得分都会被选中
	maxScoreILH := ""       // 初始化为空字符串
	if len(ilhLineList) > 0 {
		maxScoreILH = ilhLineList[0] // 如果列表不为空，默认选第一个以防万一
	}

	for _, ilh := range ilhLineList {
		// 获取权重值W(i)
		lineInfo, existLineInfo := availableLineInfoMap[ilh]
		if !existLineInfo {
			logger.CtxLogErrorf(ctx, "Line info not found for ILH=%s, skipping score calculation", ilh)
			continue
		}
		weightage := float64(lineInfo.Weightage)

		// 获取产品级别BSA用量
		capacitySettingInfo, existCapacity := ilhCapacitySettingMap[ilh]
		if !existCapacity {
			logger.CtxLogErrorf(ctx, "Capacity setting not found for ILH=%s, skipping score calculation", ilh)
			continue
		}

		// 获取当前产品已使用的BSA资源重量
		slotID := capacitySettingInfo.GetRelevantSlotID(ctx, timestamp)
		productBSAWeight, err := rs.ilhWeightCounter.GetILHProductBSAWeight(ctx, productID, ilh, dgType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get product BSA weight for ilh=%s, product=%d: %v", ilh, productID, err)
			continue
		}

		// 使用得分计算器计算得分
		score, valid := scoreCalculator(ilh, weightage, capacitySettingInfo, productBSAWeight, timestamp)
		if !valid {
			continue
		}

		scores = append(scores, lineScore{
			lineID: ilh,
			score:  score,
		})

		// 记录得分最高的线路
		if score > maxScore {
			maxScore = score
			maxScoreILH = ilh
		}
	}

	if maxScoreILH == "" && len(ilhLineList) > 0 {
		// 如果所有线路都无效，但列表不为空，则返回第一个作为兜底
		maxScoreILH = ilhLineList[0]
		logger.CtxLogErrorf(ctx, "All ILH candidates failed score calculation, falling back to first candidate: %s", maxScoreILH)
	} else {
		logger.CtxLogInfof(ctx, "Selecting optimal ILH from %d valid candidates, selected=%s with score=%.2f",
			len(scores), maxScoreILH, maxScore)
	}
	return maxScoreILH
}

// selectOptimalReservedBSAILH 选择最佳预留BSA容量的ILH线路
func (rs *ILHRoutingServiceImpl) selectOptimalReservedBSAILH(
	ctx context.Context,
	ilhLineList []string,
	productID int,
	dgType int,
	timestamp int64,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) string {
	// 计算保留BSA容量得分的函数
	scoreCalculator := func(ilh string, weightage float64, capacitySettingInfo entity2.ILHCapacitySettingInfo, productBSAWeight int64, timestamp int64) (float64, bool) {
		// 获取产品预留BSA容量
		productReservedBSAWeight, exist := capacitySettingInfo.CapacitySetting.GetProductReservedBSAWeightUnitG(productID)
		if !exist || productReservedBSAWeight <= 0 {
			return 0, false
		}

		// 获取总BSA容量
		totalBSACapacity := capacitySettingInfo.CapacitySetting.GetBSAWeightUnitG()
		if totalBSACapacity <= 0 {
			return 0, false
		}

		// 计算产品剩余的预留BSA容量
		productRemainingReservedBSA := productReservedBSAWeight - productBSAWeight
		if productRemainingReservedBSA < 0 {
			productRemainingReservedBSA = 0
		}

		// ReservedBSA得分 = (Product剩余的ReservedBSA) / 总BSA
		score := weightage * float64(productRemainingReservedBSA) / float64(totalBSACapacity)

		logger.CtxLogDebugf(ctx, "Reserved BSA score calculation for ILH=%s: weightage=%.2f, product_used=%d, reserved_capacity=%d, remaining_reserved=%d, total_capacity=%d, score=%.2f",
			ilh, weightage, productBSAWeight, productReservedBSAWeight, productRemainingReservedBSA, totalBSACapacity, score)
		return score, true
	}

	logger.CtxLogInfof(ctx, "Selecting optimal ILH from %d candidates using reserved BSA strategy", len(ilhLineList))
	return rs.selectOptimalILH(ctx, ilhLineList, productID, dgType, ilhCapacitySettingMap, availableLineInfoMap, timestamp, scoreCalculator)
}

// selectOptimalBSAILH 选择最佳BSA容量的ILH线路
func (rs *ILHRoutingServiceImpl) selectOptimalBSAILH(
	ctx context.Context,
	ilhLineList []string,
	productID int,
	dgType int,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	timestamp int64,
) string {
	// 计算BSA容量得分的函数
	scoreCalculator := func(ilh string, weightage float64, capacitySettingInfo entity2.ILHCapacitySettingInfo, productBSAWeight int64, timestamp int64) (float64, bool) {
		slotID := capacitySettingInfo.GetRelevantSlotID(ctx, timestamp)
		totalBSAWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, dgType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get total BSA weight for ILH=%s: %v", ilh, err)
			return 0, false
		}

		capacitySetting := capacitySettingInfo.CapacitySetting

		// 获取总BSA容量
		totalBSACapacity := capacitySetting.GetBSAWeightUnitG()
		if totalBSACapacity <= 0 {
			return 0, false
		}

		// 获取产品预留的BSA容量
		productReservedBSAWeight, hasReserved := capacitySetting.GetProductReservedBSAWeightUnitG(productID)
		if !hasReserved {
			productReservedBSAWeight = 0
		}

		// 计算产品剩余的预留BSA容量
		productRemainingReservedBSA := productReservedBSAWeight - productBSAWeight
		if productRemainingReservedBSA < 0 {
			productRemainingReservedBSA = 0
		}

		// 计算非预留BSA的剩余量
		nonReservedBSACapacity := totalBSACapacity - productReservedBSAWeight
		nonReservedBSAUsed := totalBSAWeight - productBSAWeight
		nonReservedBSARemaining := nonReservedBSACapacity - nonReservedBSAUsed
		if nonReservedBSARemaining < 0 {
			nonReservedBSARemaining = 0
		}

		// BSA得分 = (Product剩余的ReservedBSA + NonReservedBSA剩余量) / 总BSA
		score := weightage * float64(productRemainingReservedBSA+nonReservedBSARemaining) / float64(totalBSACapacity)

		logger.CtxLogDebugf(ctx, "BSA score calculation for ILH=%s: weightage=%.2f, product_used=%d, total_used=%d, total_capacity=%d, reserved_remaining=%d, non_reserved_remaining=%d, score=%.2f",
			ilh, weightage, productBSAWeight, totalBSAWeight, totalBSACapacity, productRemainingReservedBSA, nonReservedBSARemaining, score)
		return score, true
	}

	logger.CtxLogInfof(ctx, "Selecting optimal ILH from %d candidates using BSA strategy", len(ilhLineList))
	return rs.selectOptimalILH(ctx, ilhLineList, productID, dgType, ilhCapacitySettingMap, availableLineInfoMap, timestamp, scoreCalculator)
}

// selectOptimalAdhocILH 选择最佳Adhoc容量的ILH线路
func (rs *ILHRoutingServiceImpl) selectOptimalAdhocILH(
	ctx context.Context,
	ilhLineList []string,
	productID int,
	dgType int,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	timestamp int64,
) string {
	// 计算BSA+Adhoc容量得分的函数
	scoreCalculator := func(ilh string, weightage float64, capacitySettingInfo entity2.ILHCapacitySettingInfo, productBSAWeight int64, timestamp int64) (float64, bool) {
		slotID := capacitySettingInfo.GetRelevantSlotID(ctx, timestamp)
		totalBSAWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, dgType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get total BSA weight for ILH=%s: %v", ilh, err)
			return 0, false
		}

		capacitySetting := capacitySettingInfo.CapacitySetting

		// 获取总BSA容量
		totalBSACapacity := capacitySetting.GetBSAWeightUnitG()
		if totalBSACapacity <= 0 {
			return 0, false
		}

		// 获取产品预留的BSA容量
		productReservedBSAWeight, hasReserved := capacitySetting.GetProductReservedBSAWeightUnitG(productID)
		if !hasReserved {
			productReservedBSAWeight = 0
		}

		// 计算产品剩余的预留BSA容量
		productRemainingReservedBSA := productReservedBSAWeight - productBSAWeight
		if productRemainingReservedBSA < 0 {
			productRemainingReservedBSA = 0
		}

		// 计算非预留BSA的剩余量
		nonReservedBSACapacity := totalBSACapacity - productReservedBSAWeight
		nonReservedBSAUsed := totalBSAWeight - productBSAWeight
		nonReservedBSARemaining := nonReservedBSACapacity - nonReservedBSAUsed
		if nonReservedBSARemaining < 0 {
			nonReservedBSARemaining = 0
		}

		// 计算BSA得分部分：(Product剩余的ReservedBSA + NonReservedBSA剩余量) / 总BSA
		bsaScore := float64(productRemainingReservedBSA+nonReservedBSARemaining) / float64(totalBSACapacity)

		// 获取Adhoc数据
		currentAdhocWeight, err := rs.ilhWeightCounter.GetILHAdhocWeight(ctx, ilh, dgType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get adhoc weight for ILH=%s: %v", ilh, err)
			return 0, false
		}

		// 计算Adhoc余量比例
		adhocScore := 0.0
		adhocCapacity := capacitySetting.GetAdhocWeightUnitG()
		if adhocCapacity > 0 {
			adhocRemaining := adhocCapacity - currentAdhocWeight
			if adhocRemaining < 0 {
				adhocRemaining = 0
			}
			adhocScore = float64(adhocRemaining) / float64(adhocCapacity)
		}

		// Adhoc得分 = BSA得分 + (Adhoc余量 / 总Adhoc量)
		score := weightage * (bsaScore + adhocScore)

		logger.CtxLogDebugf(ctx, "Adhoc score calculation for ILH=%s: weightage=%.2f, bsa_score=%.2f, adhoc_remaining=%d, adhoc_capacity=%d, adhoc_score=%.2f, total_score=%.2f",
			ilh, weightage, bsaScore, adhocCapacity-currentAdhocWeight, adhocCapacity, adhocScore, score)
		return score, true
	}

	logger.CtxLogInfof(ctx, "Selecting optimal ILH from %d candidates using Adhoc strategy", len(ilhLineList))
	return rs.selectOptimalILH(ctx, ilhLineList, productID, dgType, ilhCapacitySettingMap, availableLineInfoMap, timestamp, scoreCalculator)
}
