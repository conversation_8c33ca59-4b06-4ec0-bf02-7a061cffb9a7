package routing

type DgGroupInfo struct {
	DgGroupId   string   `json:"dg_group_id"`
	DgGroupName string   `json:"dg_group_name"`
	LineList    []string `json:"line_list"`
}

// ExtraInfo 调度方法Routing额外信息
type ExtraInfo struct {
	BusinessType  int8               `json:"business_type"` // 业务类型，是哪个业务场景调的routing方法
	ReCalcFee     bool               `json:"re_calc_fee"`
	OldLineFeeMap map[string]float64 `json:"old_line_fee_map"`
}
