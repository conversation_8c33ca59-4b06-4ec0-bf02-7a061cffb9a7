package routing

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
)

//checked ??
//it act-depends-

func (rt *routingTask) filterBySpxSelfBuild(ctx context.Context, matchedRule *rule.RoutingRuleParsed, availableLanes []*rule.RoutingLaneInfo) []*rule.RoutingLaneInfo {
	var spxSelfBuildLanes []*rule.RoutingLaneInfo
	if matchedRule.RoutingType == rule.SPXRoutingType && len(availableLanes) > 1 {
		for _, availableLane := range availableLanes {
			spxSelfBuildFlag := true
			for _, lineInfo := range availableLane.LineList {
				if lineInfo.RealResourceSubType != lfslib.L_SelfBuild {
					spxSelfBuildFlag = false
					break
				}
			}
			if spxSelfBuildFlag {
				logger.CtxLogInfof(ctx, "filterBySpxSelfBuild| lane code:%v", availableLane.LaneCode)
				spxSelfBuildLanes = append(spxSelfBuildLanes, availableLane)
				return spxSelfBuildLanes
			}
		}
	}
	return availableLanes
}
