package masking_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func Test_batch(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	ctx := context.TODO()
	db, err := dbutil.SlaveDB(ctx, rulevolume.MaskRuleVolumeTabHook)
	if err != nil {
		panic(err)
	}
	var ruleList []rulevolume.MaskRuleVolumeTab
	if err := db.Table(rulevolume.MaskRuleVolumeTabHook.TableName()).Offset(1).Limit(10).Find(&ruleList).GetError(); err != nil {
		panic(err)
	}
}
