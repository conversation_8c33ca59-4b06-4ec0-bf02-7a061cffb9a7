package forecast_volume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

const (
	dbInsertBatchSize = 2000
	dbDeleteBatchSize = 100000
	batchTenThousand  = 10000
)

type ForecastVolumeRepo interface {
	BatchCreateRouteVolumes(ctx context.Context, routeVolumes []*ForecastRouteVolumeTab) *srerr.Error
	BatchCreateZoneVolumes(ctx context.Context, zoneVolumes []*ForecastZoneVolumeTab) *srerr.Error
	DeleteRouteLimitsByForecastID(ctx context.Context, forecastID uint64) *srerr.Error
	DeleteZoneLimitsByForecastID(ctx context.Context, forecastID uint64) *srerr.Error
}

type ForecastVolumeRepoImpl struct {
}

func NewForecastVolumeRepo() ForecastVolumeRepo {
	return &ForecastVolumeRepoImpl{}
}

func (f ForecastVolumeRepoImpl) BatchCreateRouteVolumes(ctx context.Context, routeVolumes []*ForecastRouteVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	ts := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	insertRecords := make([]interface{}, 0, len(routeVolumes))
	for _, routeVolume := range routeVolumes {
		routeVolume.CTime = ts
		routeVolume.MTime = ts
		insertRecords = append(insertRecords, routeVolume)

	}

	if err := db.CreateInBatches(insertRecords, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)

	}

	return nil
}

func (f ForecastVolumeRepoImpl) BatchCreateZoneVolumes(ctx context.Context, zoneVolumes []*ForecastZoneVolumeTab) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	ts := uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
	insertRecords := make([]interface{}, 0, len(zoneVolumes))
	for _, zoneVolume := range zoneVolumes {
		zoneVolume.CTime = ts
		zoneVolume.MTime = ts
		insertRecords = append(insertRecords, zoneVolume)
	}

	if err := db.CreateInBatches(insertRecords, dbInsertBatchSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)

	}

	return nil
}

func (f ForecastVolumeRepoImpl) DeleteRouteLimitsByForecastID(ctx context.Context, forecastID uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastRouteVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("forecast_task_id = ?", forecastID).Limit(dbDeleteBatchSize).Delete(ForecastRouteVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, forecastID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}
	return nil
}

func (f ForecastVolumeRepoImpl) DeleteZoneLimitsByForecastID(ctx context.Context, forecastID uint64) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, ForecastZoneVolumeTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	var rowsAffected int64 = 1
	// batch delete
	for rowsAffected != 0 {
		d := db.Where("forecast_task_id = ?", forecastID).Limit(dbDeleteBatchSize).Delete(ForecastZoneVolumeTabHook)
		if d.GetError() != nil {
			return srerr.With(srerr.DatabaseErr, forecastID, d.GetError())
		}
		rowsAffected = d.RowsAffected()
	}
	return nil
}

func DumpMaskingForecastLocationVolume() (map[string]interface{}, error) {
	ctx := context.TODO()
	dumpData := make(map[string]interface{})

	if !envvar.IsTaskModule() {
		logger.CtxLogInfof(ctx, "not smart routing task, will not init forecast volume")
		return dumpData, nil
	}

	// 1. get forecast route volumes
	var forecastRouteVolumes []ForecastRouteVolumeTab
	var rMaxID, rMinID int64 //find min & max id
	rDb, rErr := dbutil.SlaveDB(ctx, ForecastRouteVolumeTabHook)
	if rErr != nil {
		return nil, rErr
	}
	rMaxRow := rDb.Table(ForecastRouteVolumeTabHook.TableName()).Select("max(id)").Row()
	err := rMaxRow.Scan(&rMaxID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get max route id err:%v", err)
	}
	rMinRow := rDb.Table(ForecastRouteVolumeTabHook.TableName()).Select("min(id)").Row()
	err = rMinRow.Scan(&rMinID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get min route id err:%v", err)
	}
	// select route volumes between min and max id, by batch
	for i := rMinID; i <= rMaxID; i += batchTenThousand {
		var tempRouteVolumes []ForecastRouteVolumeTab
		if err := dbutil.Select(ctx, ForecastRouteVolumeTabHook, map[string]interface{}{"id >= ?": i}, &tempRouteVolumes, dbutil.WithLimit(batchTenThousand)); err != nil {
			logger.CtxLogErrorf(ctx, "start id:%v, get route volumes err:%v", i, err)
			continue
		}
		forecastRouteVolumes = append(forecastRouteVolumes, tempRouteVolumes...)
	}

	// 2. get forecast zone volumes
	var forecastZoneVolumes []ForecastZoneVolumeTab
	var zMaxID, zMinID int64 //find min & max id
	zDb, zErr := dbutil.SlaveDB(ctx, ForecastZoneVolumeTabHook)
	if zErr != nil {
		return nil, rErr
	}
	zMaxRow := zDb.Table(ForecastZoneVolumeTabHook.TableName()).Select("max(id)").Row()
	err = zMaxRow.Scan(&zMaxID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get max zone id err:%v", err)
	}
	zMinRow := zDb.Table(ForecastZoneVolumeTabHook.TableName()).Select("min(id)").Row()
	err = zMinRow.Scan(&zMinID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get min zone id err:%v", err)
	}
	//select zone volumes between min and max id, by batch
	for i := zMinID; i <= zMaxID; i += batchTenThousand {
		var tempZoneVolumes []ForecastZoneVolumeTab
		if err := dbutil.Select(ctx, ForecastZoneVolumeTabHook, map[string]interface{}{"id >= ?": i}, &tempZoneVolumes, dbutil.WithLimit(batchTenThousand)); err != nil {
			logger.CtxLogErrorf(ctx, "start id:%v, get zone volumes err:%v", i, err)
			continue
		}
		forecastZoneVolumes = append(forecastZoneVolumes, tempZoneVolumes...)
	}

	// 3. store route volume into dump
	for _, routeVolume := range forecastRouteVolumes {
		uniqueID := formatRouteVolumeCacheKey(int(routeVolume.ForecastTaskID), routeVolume.MaskProductID, routeVolume.ComponentProductID, routeVolume.OriginDistrictID, routeVolume.DestinationDistrictID, routeVolume.OriginPostcode, routeVolume.DestinationPostcode)
		dumpData[uniqueID] = routeVolume
	}
	// 4. store zone volume into dump
	for _, zoneValue := range forecastZoneVolumes {
		uniqueID := formatZoneVolumeCacheKey(int(zoneValue.ForecastTaskID), zoneValue.MaskProductID, zoneValue.ComponentProductID, zoneValue.DistrictID, zoneValue.Postcode)
		dumpData[uniqueID] = zoneValue
	}

	return dumpData, nil
}
