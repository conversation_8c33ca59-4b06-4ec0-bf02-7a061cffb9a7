package forecast_volume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"time"
)

type CtxString string

const (
	//SSCSMR-1480:用ctx传递task id，避免不通预测任务使用相同的redis key
	CtxMaskForecastKey CtxString = "masking_forecast_redis"
)

type CounterEntity struct {
	Key         string
	Value       int32
	BatchVolume int32
}

type BatchCounterEntity struct {
	MaskingProductVolume *CounterEntity
	ProductVolume        *CounterEntity
	RouteVolume          *CounterEntity
	ZoneVolume           *CounterEntity
	BatchVolume          *CounterEntity
}

type AllocateForecastVolumeCounterImpl struct {
}

func NewAllocateForecastVolumeCounterImpl() volumecounter.MaskVolumeCounter {
	c := &AllocateForecastVolumeCounterImpl{}

	return c
}

func (a *AllocateForecastVolumeCounterImpl) IncrMaskVolume(
	ctx context.Context, maskingProductID, productID int64, groupCode string, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	// 1. 更新 maskProduct 当天总单量
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	maskKey := MaskProductVolumeKey(ctx, taskId, maskingProductID)
	count, err := redisutil.Incr(ctx, maskKey)
	if err != nil {
		logger.CtxLogErrorf(ctx, "IncrMaskVolume|key:%v, incr mask err:%v", maskKey, err)
	}
	if count == 1 {
		_ = redisutil.SetExpiredTime(ctx, maskKey, time.Hour*24)
	}
	// 2. 更新 product 当天总单量
	productKey := ProductVolumeKey(ctx, taskId, productID)
	count, err = redisutil.Incr(ctx, productKey)
	if err != nil {
		logger.CtxLogErrorf(ctx, "IncrMaskVolume|key:%v, incr product err:%v", productKey, err)
	}
	if count == 1 {
		_ = redisutil.SetExpiredTime(ctx, productKey, time.Hour*24)
	}

	// group code逻辑

	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrRouteVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, routeCode string, rm rule_mode.RuleMode, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := ProductRouteVolumeKey(ctx, taskId, productID, routeCode, rm)
	count, err := redisutil.Incr(ctx, key)
	if err != nil {
		logger.CtxLogErrorf(ctx, "IncrRouteVolume|key:%v, incr err:%v", key, err)
	}
	if count == 1 {
		_ = redisutil.SetExpiredTime(ctx, key, time.Hour*24)
	}

	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrZoneVolume(
	ctx context.Context, maskProductID, productID int64, groupCode string, zoneCode string,
	direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr,
) error {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := ProductZoneVolumeKey(ctx, taskId, productID, zoneCode, direction, rm)
	count, err := redisutil.Incr(ctx, key)
	if err != nil {
		logger.CtxLogErrorf(ctx, "IncrZoneVolume|key:%v, incr err:%v", key, err)
	}
	if count == 1 {
		_ = redisutil.SetExpiredTime(ctx, key, time.Hour*24)
	}

	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrProductVolume(ctx context.Context, maskProductID, productID int64, parcelTypeAttr *parcel_type_definition.ParcelTypeAttr) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrVolumeForRule(ctx context.Context, maskingProductID int64, productID int64, batchVolume int32) error {
	if batchVolume == 0 {
		// if batch volume == 0 that mean rule has not enable batch volume routing factor, so no need to update batch volume counter
		return nil
	}
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	prefix := BatchVolumeKeyPrefix(ctx, taskId)

	//获取hash key对应的所有field and value
	resultMap, err := redisutil.HGetAll(ctx, prefix)
	if err != nil {
		logger.CtxLogErrorf(ctx, "IncrVolumeForRule| hgetAll err:%v", err)
		return err
	}
	//遍历所有的field，将其value累加起来
	var totalCount int
	for field, orderCountVal := range resultMap {
		orderCount, err := strconv.Atoi(orderCountVal)
		if err != nil {
			logger.CtxLogErrorf(ctx, "IncrVolumeForRule| parse field:%v, value:%v, err:%v", field, orderCountVal, err)
			continue
		}
		totalCount += orderCount
	}

	//累加的value超出batch值，清空本批次
	if totalCount >= int(batchVolume) {
		logger.CtxLogInfof(ctx, "IncrVolumeForRule| prefix key:%v, current total count:%v, batch volume:%v, will delete this batch", prefix, totalCount, batchVolume)
		dErr := redisutil.Del(ctx, prefix)
		if dErr != nil {
			logger.CtxLogErrorf(ctx, "IncrVolumeForRule| prefix key:%v, failed to delete key", prefix)
		}
	}

	// 更新规则内的product单量
	count, err := redisutil.HIncrBy(ctx, prefix, BatchVolumeKey(productID), 1)
	if err != nil {
		return fmt.Errorf("IncrVolumeForRule|incr product in batch_volume: %v", err)
	}
	if count == 1 {
		_ = redisutil.SetExpiredTime(ctx, prefix, time.Hour*24)
	}

	return nil
}

func (a *AllocateForecastVolumeCounterImpl) GetMaskingProductVolume(ctx context.Context, maskingProductID int64) (int32, error) {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := MaskProductVolumeKey(ctx, taskId, maskingProductID)
	if value, err := redisutil.GetInt(ctx, key); err != nil {
		return 0, nil
	} else {
		return int32(value), nil
	}
}

func (a *AllocateForecastVolumeCounterImpl) GetProductVolume(ctx context.Context, maskProductID, productID int64, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := ProductVolumeKey(ctx, taskId, productID)
	if value, err := redisutil.GetInt(ctx, key); err != nil {
		return 0, nil
	} else {
		return int32(value), nil
	}
}

func (a *AllocateForecastVolumeCounterImpl) GetProductRouteVolume(ctx context.Context, maskProductID, productID int64, routeCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := ProductRouteVolumeKey(ctx, taskId, productID, routeCode, rm)
	if value, err := redisutil.GetInt(ctx, key); err != nil {
		return 0, nil
	} else {
		return int32(value), nil
	}
}

func (a *AllocateForecastVolumeCounterImpl) GetProductZoneVolume(ctx context.Context, maskProductID, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	key := ProductZoneVolumeKey(ctx, taskId, productID, zoneCode, direction, rm)
	if value, err := redisutil.GetInt(ctx, key); err != nil {
		return 0, nil
	} else {
		return int32(value), nil
	}
}

func (a *AllocateForecastVolumeCounterImpl) GetProductVolumeInBatch(ctx context.Context, ruleID int64, productID int64, maskProductID int64) (int32, error) {
	key := BatchVolumeKey(productID)
	taskId := fmt.Sprintf("%v", ctx.Value(CtxMaskForecastKey))
	if value, err := redisutil.HGetInt(ctx, BatchVolumeKeyPrefix(ctx, taskId), key); err == nil {
		return int32(value), nil
	}
	logger.CtxLogInfof(ctx, "GetProductVolumeInBatch| key:%v, got no volume", key)
	return 0, nil
}

func (a *AllocateForecastVolumeCounterImpl) UpdateVolumesForTesting(ctx context.Context, volumes map[int64]int32, rm rule_mode.RuleMode) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) UpdateRuleVolumesForTesting(ctx context.Context, ruleID int64, volumes map[int64]int32) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) SetRuleID(ctx context.Context, ruleID int64, channelID int64) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) DecrProductVolume(ctx context.Context, maskProductID, productID int64) error {
	// forecast allocate job no need to implement
	return nil
}

func MaskProductVolumeKey(ctx context.Context, taskId string, maskProductID int64) string {
	return fmt.Sprintf("%s:forecast:%s:mask_product:%s", taskId, timeutil.GetTodayDateString(ctx), strconv.FormatInt(maskProductID, 10))
}

func ProductVolumeKey(ctx context.Context, taskId string, productID int64) string {
	return fmt.Sprintf("%s:forecast:%s:product:%s", taskId, timeutil.GetTodayDateString(ctx), strconv.FormatInt(productID, 10))
}

func ProductRouteVolumeKey(ctx context.Context, taskId string, productID int64, routeCode string, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:forecast:%s:product_route:%s:%v", taskId, timeutil.GetTodayDateString(ctx), fmt.Sprintf("%s:route:%s", strconv.FormatInt(productID, 10), routeCode), rm)
}

func ProductZoneVolumeKey(ctx context.Context, taskId string, productID int64, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode) string {
	return fmt.Sprintf("%s:forecast:%s:product_zone:%s:%v", taskId, timeutil.GetTodayDateString(ctx), fmt.Sprintf("%s:zone:%s:%s", strconv.FormatInt(productID, 10), zoneCode, direction), rm)
}

func BatchVolumeKey(productID int64) string {
	return strconv.FormatInt(productID, 10)
}

func BatchVolumeKeyPrefix(ctx context.Context, taskId string) string {
	return fmt.Sprintf("%s:forecast:%s:batch_volume", taskId, timeutil.GetTodayDateString(ctx))
}

func (a *AllocateForecastVolumeCounterImpl) CheckAllocRuleBatchVolumes(ctx context.Context, ruleID int64, batchVolume int32) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrByMaskVolumeByDate(ctx context.Context, maskingProductID int64, value int64, date time.Time) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) IncrByProductVolumeByDate(ctx context.Context, maskingProductID, productID int64, groupCode string, value int64, date time.Time, parcelType parcel_type_definition.ParcelType) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) GetGroupVolume(ctx context.Context, groupCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	// forecast allocate job no need to implement
	return 0, nil
}

func (a *AllocateForecastVolumeCounterImpl) GetGroupRouteVolume(ctx context.Context, groupCode, routeCode string, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	// forecast allocate job no need to implement
	return 0, nil
}

func (a *AllocateForecastVolumeCounterImpl) GetGroupZoneVolume(ctx context.Context, groupCode, zoneCode string, direction rulevolume.MaskZoneDirection, rm rule_mode.RuleMode, parcelType parcel_type_definition.ParcelType) (int32, error) {
	// forecast allocate job no need to implement
	return 0, nil
}

func (a *AllocateForecastVolumeCounterImpl) BatchGetProductZoneVolumes(ctx context.Context, requests []volumecounter.GetProductZoneVolumeRequest) ([]int32, error) {
	// forecast allocate job no need to implement
	return nil, nil
}

func (a *AllocateForecastVolumeCounterImpl) BatchGetProductRouteVolumes(ctx context.Context, requests []volumecounter.GetProductRouteVolumeRequest) ([]int32, error) {
	// forecast allocate job no need to implement
	return nil, nil
}

func (a *AllocateForecastVolumeCounterImpl) BatchIncrByZoneVolumeByDate(ctx context.Context, items []volumecounter.ZoneVolumeItem, date time.Time) error {
	// forecast allocate job no need to implement
	return nil
}

func (a *AllocateForecastVolumeCounterImpl) BatchIncrByRouteVolumeByDate(ctx context.Context, items []volumecounter.RouteVolumeItem, date time.Time) error {
	// forecast allocate job no need to implement
	return nil
}
