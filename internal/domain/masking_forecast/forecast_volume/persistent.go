package forecast_volume

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

type ForecastRouteVolumeTab struct {
	ID                    int    `gorm:"column:id" json:"id"`
	ForecastTaskID        uint64 `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskProductID         int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID    int    `gorm:"column:component_product_id" json:"component_product_id"`
	RouteCode             string `gorm:"column:route_code" json:"route_code"`
	OriginDistrictID      int    `gorm:"column:origin_district_id" json:"origin_district_id"`
	OriginPostcode        string `gorm:"column:origin_postcode" json:"origin_postcode"`
	DestinationDistrictID int    `gorm:"column:destination_district_id" json:"destination_district_id"`
	DestinationPostcode   string `gorm:"column:destination_postcode" json:"destination_postcode"`
	MaxCapacity           int32  `gorm:"column:max_capacity" json:"max_capacity"`
	MinVolume             int32  `gorm:"column:min_volume" json:"min_volume"`
	IsHardCap             bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	CTime                 uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                 uint32 `gorm:"column:mtime" json:"mtime"`
}

func (ForecastRouteVolumeTab) TableName() string {
	return "allocate_forecast_route_volume_tab"
}

func (ForecastRouteVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (ForecastRouteVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t ForecastRouteVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(t.ID),
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ComponentProductID),
		MaskProductId:        uint64(t.MaskProductID),
		TaskId:               t.ForecastTaskID,
	}
}

var ForecastRouteVolumeTabHook = &ForecastRouteVolumeTab{}

type ForecastZoneVolumeTab struct {
	ID                     int    `gorm:"column:id" json:"id"`
	ForecastTaskID         uint64 `gorm:"column:forecast_task_id" json:"forecast_task_id"`
	MaskProductID          int    `gorm:"column:mask_product_id" json:"mask_product_id"`
	ComponentProductID     int    `gorm:"column:component_product_id" json:"component_product_id"`
	ZoneCode               string `gorm:"column:zone_code" json:"zone_code"`
	DistrictID             int    `gorm:"column:district_id" json:"district_id"`
	Postcode               string `gorm:"column:postcode" json:"postcode"`
	OriginMaxCapacity      int32  `gorm:"column:origin_max_capacity" json:"origin_max_capacity"`
	DestinationMaxCapacity int32  `gorm:"column:destination_max_capacity" json:"destination_max_capacity"`
	OriginMinVolume        int32  `gorm:"column:origin_min_volume" json:"origin_min_volume"`
	DestinationMinVolume   int32  `gorm:"column:destination_min_volume" json:"destination_min_volume"`
	IsHardCap              bool   `gorm:"column:is_hard_cap" json:"is_hard_cap"`
	CTime                  uint32 `gorm:"column:ctime" json:"ctime"`
	MTime                  uint32 `gorm:"column:mtime" json:"mtime"`
}

func (ForecastZoneVolumeTab) TableName() string {
	return "allocate_forecast_zone_volume_tab"
}

func (ForecastZoneVolumeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (ForecastZoneVolumeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t ForecastZoneVolumeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(t.ID),
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ComponentProductID),
		MaskProductId:        uint64(t.MaskProductID),
		TaskId:               t.ForecastTaskID,
	}
}

var ForecastZoneVolumeTabHook = &ForecastZoneVolumeTab{}
