package forecast_volume

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestForecastVolumeRepoImpl_DeleteRouteLimitsByForecastID(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	ctx := context.TODO()
	tab := ruledata.RoutingRuleTab{}
	db, err := dbutil.SlaveDB(ctx, &tab)
	if err != nil {
		panic(err)
	}

	db.Table(tab.TableName()).Where("id = ?", 1).Find(&tab)
	println()
}
