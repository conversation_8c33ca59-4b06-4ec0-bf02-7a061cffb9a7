package forecast_volume

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
)

type ForecastLocationVolumeService interface {
	MatchLocsToRoutes(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int, originPostcode, destPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error)
	MatchLocsToZones(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int, originPostcode, destPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error)
}

type ForecastLocationVolumeServiceImpl struct {
	ForecastVolumeRepo ForecastVolumeRepo
}

func NewForecastLocationVolumeServiceImpl(forecastVolumeRepo ForecastVolumeRepo) ForecastLocationVolumeService {
	forecastVolumeServiceImpl := &ForecastLocationVolumeServiceImpl{
		ForecastVolumeRepo: forecastVolumeRepo,
	}
	return forecastVolumeServiceImpl
}

func (f ForecastLocationVolumeServiceImpl) MatchLocsToRoutes(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int, originPostcode, destPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var result []*rulevolume.MaskLocVolume
	// 获取地址+postcode的组合信息
	originVolumeLocInfoList, destVolumeLocInfoList := rulevolume.GetComposeAddress(objutil.IntToInt64Slice(originLocIDs), objutil.IntToInt64Slice(destLocIDs), originPostcode, destPostcode)
	// 匹配运力需要考虑postcode
	for _, originVolumeLocInfo := range originVolumeLocInfoList {
		for _, destVolumeLocInfo := range destVolumeLocInfoList {
			uniqueID := formatRouteVolumeCacheKey(taskID, maskProductID, componentProductID, int(originVolumeLocInfo.LocationId), int(destVolumeLocInfo.LocationId), originVolumeLocInfo.Postcode, destVolumeLocInfo.Postcode)
			dumpData, err := localcache.Get(ctx, constant.ForecastMaskLocationVolume, uniqueID)
			if err != nil {
				//logger.CtxLogErrorf(ctx, "Allocate Forecast--not found route volume for unique_id:%v", uniqueID)
				continue
			}
			info, ok := dumpData.(ForecastRouteVolumeTab)
			if !ok {
				//logger.CtxLogErrorf(ctx, "Allocate Forecast--failed to convert dump route volume, unique_id:%v", uniqueID)
				continue
			}
			if info.RouteCode == "" {
				//logger.CtxLogErrorf(ctx, "Allocate Forecast--route code is empty, unique_id:%v", uniqueID)
				continue
			}
			logger.CtxLogDebugf(ctx, "MatchLocsToRoutes| unique id:%v, volume:%v", uniqueID, info)
			routeVolume := &rulevolume.MaskRouteVolume{
				RouteCode:   info.RouteCode,
				MinVolume:   info.MinVolume,
				MaxCapacity: info.MaxCapacity,
				IsHardCap:   info.IsHardCap,
			}
			result = append(result, &rulevolume.MaskLocVolume{
				LocCode:     routeVolume.RouteCode,
				LocType:     rulevolume.MaskLocTypeRoute,
				RouteVolume: routeVolume,
				ProductId:   int64(componentProductID),
			})
		}
	}

	return result, nil
}

func (f ForecastLocationVolumeServiceImpl) MatchLocsToZones(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int, originPostcode, destPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var result []*rulevolume.MaskLocVolume
	//Zone因为区分Origin和Dest的计数器，在LocLimit中会通过ZoneDirection区分
	// 获取地址+postcode的组合信息
	originVolumeLocInfoList, destVolumeLocInfoList := rulevolume.GetComposeAddress(objutil.IntToInt64Slice(originLocIDs), objutil.IntToInt64Slice(destLocIDs), originPostcode, destPostcode)
	// 匹配运力需要考虑postcode
	for _, originVolumeLocInfo := range originVolumeLocInfoList {
		uniqueID := formatZoneVolumeCacheKey(taskID, maskProductID, componentProductID, int(originVolumeLocInfo.LocationId), originVolumeLocInfo.Postcode)
		dumpData, err := localcache.Get(ctx, constant.ForecastMaskLocationVolume, uniqueID)
		if err != nil {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--not found zone origin volume for unique_id:%v", uniqueID)
			continue
		}
		info, ok := dumpData.(ForecastZoneVolumeTab)
		if !ok {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--convert dump data to zone origin volume failed, unique_id:%v", uniqueID)
			continue
		}
		if info.ZoneCode == "" {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--zone origin volume, zone code is empty, unique_id:%v", uniqueID)
			continue
		}
		logger.CtxLogDebugf(ctx, "MatchLocsToZones| unique id:%v, volume:%v", uniqueID, info)
		zoneVolume := &rulevolume.MaskZoneVolume{
			ZoneCode:          info.ZoneCode,
			OriginMinVolume:   info.OriginMinVolume,
			DestMinVolume:     info.DestinationMinVolume,
			OriginMaxCapacity: info.OriginMaxCapacity,
			DestMaxCapacity:   info.OriginMaxCapacity,
			IsHardCap:         info.IsHardCap,
		}
		result = append(result, &rulevolume.MaskLocVolume{
			LocCode:       zoneVolume.ZoneCode,
			LocType:       rulevolume.MaskLocTypeZone,
			ZoneDirection: rulevolume.MaskZoneDirectionOrigin,
			ZoneVolume:    zoneVolume,
			ProductId:     int64(componentProductID),
		})
	}

	for _, destVolumeLocInfo := range destVolumeLocInfoList {
		uniqueID := formatZoneVolumeCacheKey(taskID, maskProductID, componentProductID, int(destVolumeLocInfo.LocationId), destVolumeLocInfo.Postcode)
		dumpData, err := localcache.Get(ctx, constant.ForecastMaskLocationVolume, uniqueID)
		if err != nil {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--not found zone destination volume for unique_id:%v", uniqueID)
			continue
		}
		info, ok := dumpData.(ForecastZoneVolumeTab)
		if !ok {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--convert dump data to zone volume failed, unique_id:%v", uniqueID)
			continue
		}
		if info.ZoneCode == "" {
			//logger.CtxLogErrorf(ctx, "Allocate Forecast--zone destination volume, zone code is empty, unique_id:%v", uniqueID)
			continue
		}
		zoneVolume := &rulevolume.MaskZoneVolume{
			ZoneCode:          info.ZoneCode,
			OriginMinVolume:   info.OriginMinVolume,
			DestMinVolume:     info.DestinationMinVolume,
			OriginMaxCapacity: info.OriginMaxCapacity,
			DestMaxCapacity:   info.OriginMaxCapacity,
			IsHardCap:         info.IsHardCap,
		}
		result = append(result, &rulevolume.MaskLocVolume{
			LocCode:       zoneVolume.ZoneCode,
			LocType:       rulevolume.MaskLocTypeZone,
			ZoneDirection: rulevolume.MaskZoneDirectionDest,
			ZoneVolume:    zoneVolume,
			ProductId:     int64(componentProductID),
		})
	}
	return result, nil
}

func formatRouteVolumeCacheKey(taskID, maskProductID, componentProductID, originLocID, destLocID int, originPostcode, destPostcode string) string {
	return fmt.Sprintf("%v#%v#%v#%v#%v#%v#%v", taskID, maskProductID, componentProductID, originLocID, destLocID, originPostcode, destPostcode)
}

func formatZoneVolumeCacheKey(taskID, maskProductID, componentProductID, locID int, postcode string) string {
	return fmt.Sprintf("%v#%v#%v#%v#%v", taskID, maskProductID, componentProductID, locID, postcode)
}

type MockForecastLocationVolumeServiceImpl struct {
}

func (f MockForecastLocationVolumeServiceImpl) MatchLocsToRoutes(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	return []*rulevolume.MaskLocVolume{
		{
			LocCode: "R1",
		},
		{
			LocCode: "R2",
		},
	}, nil
}
func (f MockForecastLocationVolumeServiceImpl) MatchLocsToZones(ctx context.Context, taskID, maskProductID, componentProductID int, originLocIDs, destLocIDs []int) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	return []*rulevolume.MaskLocVolume{
		{
			LocCode: "Z1",
		},
		{
			LocCode: "Z2",
		},
	}, nil
}
func (f MockForecastLocationVolumeServiceImpl) MatchLocsToZonesByDestIds(ctx context.Context, taskID, maskProductID, componentProductID int, destLocIDs []int) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	return []*rulevolume.MaskLocVolume{
		{
			LocCode: "Z2",
		},
		{
			LocCode: "Z3",
		},
	}, nil
}
