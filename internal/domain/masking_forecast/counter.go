package masking_forecast

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"go.uber.org/atomic"
	"strconv"
	"sync"
)

type AllocateForecastCounter struct {
	DumpFlag     *atomic.Bool
	StatisticMap sync.Map
}

func NewAllocateForecastCounter() *AllocateForecastCounter {
	allocateForecastCounter := &AllocateForecastCounter{
		DumpFlag:     atomic.NewBool(false),
		StatisticMap: sync.Map{},
	}

	return allocateForecastCounter
}

func (c *AllocateForecastCounter) StatisticsForecastRank(ctx context.Context, result *AllocateForecastResult) {
	//dump rank
	c.dump(ctx, result.MaskingProductID, result.ForecastTaskId)

	//c.mux.Lock()
	//defer c.mux.Unlock()

	taskId := result.ForecastTaskId
	maskProductId := result.MaskingProductID
	productId := result.FulfillmentProductID
	totalKey := fmt.Sprintf("%v:%v", maskProductId, taskId)
	if value, exist := c.StatisticMap.Load(totalKey); exist {
		if value.(int)%1000 == 0 {
			c.DumpFlag.CAS(false, true)
		}
		c.StatisticMap.Store(totalKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(totalKey, 1)
	}

	if result.ForecastStatus == ForecastBlockedStatus {
		blockedType := constant.AllocateForecastRankTypeEnum[constant.Blocked]
		blockedKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, blockedType, blockedType)
		if value, exists := c.StatisticMap.Load(blockedKey); exists {
			c.StatisticMap.Store(blockedKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(blockedKey, 1)
		}
		return
	}
	overallRankType := constant.AllocateForecastRankTypeEnum[constant.Overall]
	overallRankCode := strconv.Itoa(productId)
	overallKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, overallRankType, overallRankCode)
	if value, exists := c.StatisticMap.Load(overallKey); exists {
		c.StatisticMap.Store(overallKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(overallKey, 1)
	}

	shopGroupId := result.ShopGroupId
	shopGroupType := constant.AllocateForecastRankTypeEnum[constant.ShopGroup]
	shopGroupKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, shopGroupType, shopGroupId)
	if value, exists := c.StatisticMap.Load(shopGroupKey); exists {
		c.StatisticMap.Store(shopGroupKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(shopGroupKey, 1)
	}

	zone := result.ZoneCode
	if zone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.Zone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, zone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	originZone := result.OriginZoneCode
	if originZone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.OriginZone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, originZone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	destZone := result.DestZoneCode
	if originZone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.DestZone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, destZone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	route := result.RouteCode
	if route != "" {
		routeType := constant.AllocateForecastRankTypeEnum[constant.Route]
		routeKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, routeType, route)
		if value, exists := c.StatisticMap.Load(routeKey); exists {
			c.StatisticMap.Store(routeKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(routeKey, 1)
		}
	}
}

func (c *AllocateForecastCounter) dump(ctx context.Context, maskProductId, taskId int) {
	if !c.DumpFlag.Load() {
		return
	}
	totalKey := fmt.Sprintf("%v:%v", maskProductId, taskId)
	var currentValue string
	c.StatisticMap.Range(func(key, value interface{}) bool {
		if fmt.Sprintf("%v", key) == totalKey {
			currentValue = fmt.Sprintf("%v", value)
		}
		return true
	})
	logger.CtxLogInfof(ctx, "AllocateForecastCounter| dump| key:%v, current total count:%v", totalKey, currentValue)
	c.DumpFlag.CAS(true, false)

}

type AllocateHistoricalCounter struct {
	DumpFlag     *atomic.Bool
	StatisticMap sync.Map
}

func NewAllocateHistoricalCounter() *AllocateHistoricalCounter {
	allocateHistoricalCounter := &AllocateHistoricalCounter{
		DumpFlag:     atomic.NewBool(false),
		StatisticMap: sync.Map{},
	}
	return allocateHistoricalCounter
}

func (c *AllocateHistoricalCounter) StatisticsHistoricalRank(ctx context.Context, result *AllocateForecastResult) {
	//dump rank
	c.dump(ctx, result.MaskingProductID, result.ForecastTaskId)

	//c.mux.Lock()
	//defer c.mux.Unlock()
	orderData := result.OrderData
	taskId := result.ForecastTaskId
	maskProductId := orderData.MaskingProductID
	productId := orderData.FulfillmentProductID
	overallRankType := constant.AllocateForecastRankTypeEnum[constant.Overall]
	overallRankCode := strconv.Itoa(productId)

	//total order count
	totalKey := fmt.Sprintf("%v:%v", maskProductId, taskId)
	if value, exists := c.StatisticMap.Load(totalKey); exists {
		if value.(int)%1000 == 0 {
			c.DumpFlag.CAS(false, true)
		}
		c.StatisticMap.Store(totalKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(totalKey, 1)
	}

	overallKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, overallRankType, overallRankCode)
	if value, exists := c.StatisticMap.Load(overallKey); exists {
		c.StatisticMap.Store(overallKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(overallKey, 1)
	}

	shopGroupId := orderData.ShopGroupId
	shopGroupType := constant.AllocateForecastRankTypeEnum[constant.ShopGroup]
	shopGroupKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, shopGroupType, shopGroupId)
	if value, exists := c.StatisticMap.Load(shopGroupKey); exists {
		c.StatisticMap.Store(shopGroupKey, value.(int)+1)
	} else {
		c.StatisticMap.Store(shopGroupKey, 1)
	}

	zone := orderData.ZoneCode
	if zone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.Zone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, zone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	originZone := orderData.OriginZoneCode
	if originZone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.OriginZone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, originZone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	destZone := orderData.DestZoneCode
	if destZone != "" {
		zoneType := constant.AllocateForecastRankTypeEnum[constant.DestZone]
		zoneKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, zoneType, destZone)
		if value, exists := c.StatisticMap.Load(zoneKey); exists {
			c.StatisticMap.Store(zoneKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(zoneKey, 1)
		}
	}

	route := orderData.RouteCode
	if route != "" {
		routeType := constant.AllocateForecastRankTypeEnum[constant.Route]
		routeKey := fmt.Sprintf("%v:%v:%v:%v:%v", maskProductId, productId, taskId, routeType, route)
		if value, exists := c.StatisticMap.Load(routeKey); exists {
			c.StatisticMap.Store(routeKey, value.(int)+1)
		} else {
			c.StatisticMap.Store(routeKey, 1)
		}
	}
}

func (c *AllocateHistoricalCounter) dump(ctx context.Context, maskProductId, taskId int) {
	if !c.DumpFlag.Load() {
		return
	}
	totalKey := fmt.Sprintf("%v:%v", maskProductId, taskId)
	var currentValue string
	c.StatisticMap.Range(func(key, value interface{}) bool {
		if fmt.Sprintf("%v", key) == totalKey {
			currentValue = fmt.Sprintf("%v", value)
		}
		return true
	})
	logger.CtxLogInfof(ctx, "AllocateHistoricalCounter| dump| key:%v, current total count:%v", totalKey, currentValue)
	c.DumpFlag.CAS(true, false)
}
