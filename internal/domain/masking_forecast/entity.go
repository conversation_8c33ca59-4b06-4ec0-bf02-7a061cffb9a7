package masking_forecast

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	jsoniter "github.com/json-iterator/go"
)

type AllocateOrderDataEntity struct {
	OrderId              uint64                     `json:"order_id" validate:"required"`
	MaskingProductID     int                        `json:"masking_product_id" validate:"required"`
	RequestData          *model.OmsAllocRequest     `json:"request_data" validate:"required"`
	OmsAllocateRequest   *model.OMSAllocateRequest  `json:"oms_allocate_request" validate:"required"`
	HardResult           *model.ValidateResult      `json:"hard_result" validate:"required"`
	ResponseData         *model.OmsAllocResp        `json:"response_data" validate:"required"`
	FulfillmentProductID int                        `json:"fulfillment_product_id" validate:"required"`
	ShopGroupId          int                        `json:"shop_group_id" validate:"required"`
	ZoneCode             string                     `json:"zone_code" validate:"required"`
	OriginZoneCode       string                     `json:"origin_zone_code"`
	DestZoneCode         string                     `json:"dest_zone_code"`
	RouteCode            string                     `json:"route_code" validate:"required"`
	Ignore               bool                       `json:"ignore" validate:"required"`
	OrderTime            uint32                     `json:"order_time" validate:"required"`
	ShippingFeeList      []model.ProductShippingFee `json:"shipping_fee_list"`
}

type AllocateForecastResult struct {
	OrderData            *AllocateOrderDataEntity  `json:"order_data" validate:"required"`
	ForecastTaskId       int                       `json:"forecast_task_id" validate:"required"`
	OrderId              uint64                    `json:"order_id" validate:"required"`
	MaskingProductID     int                       `json:"masking_product_id" validate:"required"`
	RequestData          *model.OmsAllocRequest    `json:"request_data" validate:"required"`
	OmsAllocateRequest   *model.OMSAllocateRequest `json:"oms_allocate_request" validate:"required"`
	HardResult           *model.ValidateResult     `json:"hard_result" validate:"required"`
	ResponseData         *model.OmsAllocResp       `json:"response_data" validate:"required"`
	FulfillmentProductID int                       `json:"fulfillment_product_id" validate:"required"`
	ShopGroupId          int64                     `json:"shop_group_id" validate:"required"`
	ZoneCode             string                    `json:"zone_code" validate:"required"`
	OriginZoneCode       string                    `json:"origin_zone_code"`
	DestZoneCode         string                    `json:"dest_zone_code"`
	RouteCode            string                    `json:"route_code" validate:"required"`
	Ignore               bool                      `json:"ignore" validate:"required"`
	OrderTime            uint32                    `json:"order_time" validate:"required"`
	ForecastStatus       int                       `json:"forecast_status" validate:"required"`
}

func convertToAllocateOrderDataEntity(a *model.AllocateOrderDataTab, useHbase bool) (*AllocateOrderDataEntity, error) {
	if useHbase {
		return ConvertHbResultToAllocateEntity(a)
	}
	return ConvertTidbToAllocateOrderDataEntity(a)
}

func ConvertHbResultToAllocateEntity(a *model.AllocateOrderDataTab) (*AllocateOrderDataEntity, error) {
	allocateOrderDataEntity := AllocateOrderDataEntity{
		OrderId:              a.OrderId,
		MaskingProductID:     a.MaskingProductID,
		FulfillmentProductID: a.FulfillmentProductID,
		ShopGroupId:          a.ShopGroupId,
		ZoneCode:             a.ZoneCode,
		OriginZoneCode:       a.OriginZoneCode,
		DestZoneCode:         a.DestZoneCode,
		RouteCode:            a.RouteCode,
		OrderTime:            a.OrderTime,
		OmsAllocateRequest:   a.OmsAllocateRequestHbase,
	}
	hardResult := model.ValidateResult{
		ProductId: a.HardResultHbase,
	}
	allocateOrderDataEntity.HardResult = &hardResult
	allocateOrderDataEntity.ShippingFeeList = a.ProductShippingFeeList
	return &allocateOrderDataEntity, nil
}

func ConvertTidbToAllocateOrderDataEntity(a *model.AllocateOrderDataTab) (*AllocateOrderDataEntity, error) {
	allocateOrderDataEntity := AllocateOrderDataEntity{
		OrderId:              a.OrderId,
		MaskingProductID:     a.MaskingProductID,
		FulfillmentProductID: a.FulfillmentProductID,
		ShopGroupId:          a.ShopGroupId,
		ZoneCode:             a.ZoneCode,
		OriginZoneCode:       a.OriginZoneCode,
		DestZoneCode:         a.DestZoneCode,
		RouteCode:            a.RouteCode,
		OrderTime:            a.OrderTime,
	}

	var req model.OmsAllocRequest
	if err := jsoniter.Unmarshal(a.RequestData, &req); err != nil {
		return nil, err
	}
	allocateOrderDataEntity.RequestData = &req

	var omsReq model.OMSAllocateRequest
	if err := jsoniter.Unmarshal(a.OmsAllocateRequest, &omsReq); err != nil {
		return nil, err
	}
	allocateOrderDataEntity.OmsAllocateRequest = &omsReq

	var hardResult model.ValidateResult
	if err := jsoniter.Unmarshal(a.HardResult, &hardResult); err != nil {
		return nil, err
	}
	allocateOrderDataEntity.HardResult = &hardResult

	var responseData model.OmsAllocResp
	if err := jsoniter.Unmarshal(a.ResponseData, &responseData); err != nil {
		return nil, err
	}
	allocateOrderDataEntity.ResponseData = &responseData
	return &allocateOrderDataEntity, nil
}

func ConvertToProductPriority(ctx context.Context,
	taskConfig *allocation.AllocateForecastTaskConfigEntity, shopGroupIds []int64) (
	priority *entity.ProductPriority, serrr *srerr.Error) {
	// consider of taskConfig un-exception
	if taskConfig.BaseInfo == nil || len(taskConfig.ProductPriorityConfigs) <= 0 {
		logger.CtxLogErrorf(ctx, "Allocate Forecast-- ConvertToProductPriority err. taskConfig == %v", taskConfig)
		return nil, srerr.New(srerr.TypeConvertErr, taskConfig, "can't convert to product priority")
	}
	priorityMap := make(map[int64]*allocation.ProductPriorityConfig)
	for _, productPriority := range taskConfig.ProductPriorityConfigs {
		priorityMap[productPriority.ShopGroupId] = productPriority
	}
	intersectionProductPriority := make(map[int64]*allocation.ProductPriorityConfig)
	for _, shopGroupId := range shopGroupIds {
		if v, exist := priorityMap[shopGroupId]; exist {
			intersectionProductPriority[shopGroupId] = v
		}
	}

	var forecastProductPriority *allocation.ProductPriorityConfig
	if len(intersectionProductPriority) > 1 || len(intersectionProductPriority) == 0 {
		// 	取default shop group 配置. default shop group 在forecast task 配置中保证了必须要配置
		forecastProductPriority = priorityMap[constant.DefaultShopGroupId]
	} else {
		for key := range intersectionProductPriority {
			forecastProductPriority = intersectionProductPriority[key]
		}
	}
	if forecastProductPriority == nil {
		return nil, srerr.New(srerr.TypeConvertErr, taskConfig, "can't convert to product priority")
	}
	result := &entity.ProductPriority{
		MaskProductID: int64(taskConfig.BaseInfo.MaskProductId),
		ShopGroupID:   forecastProductPriority.ShopGroupId,
		RuleType:      entity.DefaultRuleType(forecastProductPriority.RuleType),
	}
	componentPriorities := make([]entity.ComponentPriority, 0)
	for _, detail := range forecastProductPriority.PriorityDetails {
		componentPriority := entity.ComponentPriority{}
		componentPriority.Priority = uint32(detail.Priority)
		componentPriority.Status = entity.ComponentPriorityStatus(detail.Status)
		componentPriority.ProductID = int64(detail.ProductId)
		componentPriority.Weightage = uint32(detail.Weightage)
		componentPriorities = append(componentPriorities, componentPriority)
	}
	result.ComponentPriorities = componentPriorities
	return result, nil
}
