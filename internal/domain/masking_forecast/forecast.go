package masking_forecast

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/panjf2000/ants/v2"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient/chargeentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	allocation3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	forecastconstant "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/requestid"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil/str"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

var (
	goroutinePool     *ants.Pool
	goroutinePoolSize = 16
)

func init() {
	// Task服务链路，用于做预测数据统计，使用阻塞式
	p, err := ants.NewPool(goroutinePoolSize)
	if err != nil {
		logger.LogErrorf("New goroutine pool fail | err=%v", err)
		panic(err)
	}

	goroutinePool = p
}

func (a AllocateForecastServiceImpl) ForecastAllocate(ctx context.Context,
	orderData *AllocateOrderDataEntity, taskConfig *allocation.AllocateForecastTaskConfigEntity,
	rule *rule.MaskRule, conf *config.MaskAllocationConfigTab, productInfo *lpsclient.ProductDetailInfo, softSrv *allocation2.SoftRuleService) (*AllocateForecastResult, *srerr.Error) {
	logger.CtxLogDebugf(ctx, "forecast allocate, orderData:%v", str.JsonStringForDebugLog(orderData))
	// 记录allocate log
	allocationLog := allocation.AllocationLog{
		List: []*allocation.LogDetail{},
	}
	logDetail := &allocation.LogDetail{}
	// 记录allocate log
	allocationLog.List = append(allocationLog.List, logDetail)
	defer a.SendScheduleCountStat(ctx, &allocationLog, taskConfig.BaseInfo.Id)
	req := orderData.OmsAllocateRequest
	// forecast should not use snapshot logic. So assign snapshot to nil
	req.Snapshot = nil
	maskProductID := req.MaskingProductID
	// new forecast result
	allocateForecastResult := &AllocateForecastResult{}
	allocateForecastResult.OrderData = orderData
	allocateForecastResult.ForecastTaskId = taskConfig.BaseInfo.Id
	allocateForecastResult.MaskingProductID = maskProductID
	allocateForecastResult.OrderId = req.OrderID
	// 记录allocate log
	logDetail.MaskProductId = maskProductID
	// 使用cogs代替codAmount做cod、order校验
	req.CodAmount = req.Cogs
	// get shop group id
	shopGroupIds, getShopGroupErr := a.getShopGroupIds(ctx, req, taskConfig)
	if getShopGroupErr != nil {
		// shop group ids 可以获取不到，获取不到的情况下应该用default shop group, 即-1
		logger.CtxLogInfof(ctx, "Allocate Forecast--can't get shop group id err. err:%v", getShopGroupErr)
	}
	// get product priority
	var productPriority *entity.ProductPriority
	var productPriorityErr *srerr.Error
	if taskConfig.BaseInfo.ShopGroupChannelPriorityToggle {
		productPriority, productPriorityErr = ConvertToProductPriority(ctx, taskConfig, shopGroupIds)
	} else {
		// 预测要用live环境的product priority 配置, 查product priority 表不允许shopGroupIds 为空
		if len(shopGroupIds) == 0 {
			shopGroupIds = []int64{constant.DefaultShopGroupId}
		}
		productPriority, productPriorityErr = a.OuterCheck.GetProductFromMaskingShopGroup(ctx,
			int64(maskProductID), shopGroupIds, int64(productInfo.ComponentProduct.MaskingType), objutil.IntToInt64Slice(productInfo.ComponentProduct.ComponentProducts))
	}
	if productPriorityErr != nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast--get product priority err. err:%v", productPriorityErr)
		allocateForecastResult.ForecastStatus = ForecastBlockedStatus
	}
	allocateForecastResult.ShopGroupId = productPriority.ShopGroupID
	// 记录allocate log
	logDetail.ShopGroupId = productPriority.ShopGroupID
	// convert to hard criteria req
	// 此场景，OMS单，查询可分配logistic product时，不进行ShopCOD开关校验，故invoker设置为跳过校验
	hardRes := orderData.HardResult
	//if RunSoftRuleOnlyToggle is open, no need to validate hard criteria
	if !taskConfig.BaseInfo.RunSoftRuleOnlyToggle {
		hardReq := &lpsclient.MaskingHardCheckRequest{
			OmsRequest:      orderData.OmsAllocateRequest,
			ProductPriority: productPriority,
		}
		lpsResp, err := a.lpsApi.MaskingHardCheck(ctx, hardReq)
		if err != nil {
			allocateForecastResult.ForecastStatus = ForecastBlockedStatus
			return allocateForecastResult, err
		}
		hardRes = lpsResp.HardResult

	}
	// 这里的思路是如果关闭了重新计算运费，则用历史运费，需要补齐实时提前计算运费失败的情况
	if hardRes != nil && needRecalculate(ctx, maskProductID, taskConfig) {
		rateResp := a.completeShippingFee(ctx, orderData, hardRes.ProductId, taskConfig.BaseInfo.Id)
		ctx = setToCtx(ctx, rateResp)
	} else if hardRes != nil && isESFSwitchOpen(taskConfig) { // 这里是没有打开重新计算运费，如果开启了运费开关会根据用户配置的费率表计算
		rateResp := a.recalculateProductEsf(ctx, hardRes.ProductId, orderData.OmsAllocateRequest.ToPbOrderData(), taskConfig.AllocationRuleConfig.ShippingFeeConfig)
		ctx = setToCtx(ctx, rateResp)
	}
	allocateForecastResult.HardResult = hardRes
	var hardProdIDs []int
	if hardRes != nil {
		hardProdIDs = hardRes.ProductId
	}
	// 记录allocate log
	logDetail.HardOutput = hardProdIDs
	// 无配置不会报错，仅返回空数组
	var locVols map[int64]rulevolume.ProductLocVolumes
	if taskConfig.BaseInfo.LocalSoftCriteriaToggle {
		locVols = a.matchLocationVolumes(ctx, taskConfig.BaseInfo.Id, maskProductID, hardProdIDs, req.PickupInfo.LocationIDs, req.DeliveryInfo.LocationIDs, req.PickupInfo.GetPostalCode(), req.DeliveryInfo.GetPostalCode())
	} else {
		// soft criteria 开关未打开，使用线上的local volume 配置
		locVols = a.matchOnlineLocationVolumes(ctx, maskProductID, hardProdIDs, req.PickupInfo.LocationIDs, req.DeliveryInfo.LocationIDs, req.PickupInfo.GetPostalCode(), req.DeliveryInfo.GetPostalCode())
	}

	// 不需要校验ignore snapshot，因为forecast不用snapshot
	if len(hardProdIDs) == 1 {
		prodID := hardProdIDs[0]
		// 预测更新运力统一到一个func
		a.IncrVolume(ctx, taskConfig, int64(prodID), softSrv, req, allocateForecastResult)
		allocateForecastResult.FulfillmentProductID = prodID
		// 记录allocate log
		logDetail.FulfillmentProductId = prodID
		allocateForecastResult.ForecastStatus = ForecastCompleteStatus
		return allocateForecastResult, nil
	}

	productIDs := deleteDefaultWmsProductId(hardProdIDs, productInfo.ComponentProduct.ComponentProducts, int(conf.DefaultWmsProduct))
	priorityMap := make(map[int64]int32)
	weightageMap := make(map[int64]int32)
	// 装入component product的priority
	for _, item := range productPriority.ComponentPriorities {
		if item.Status == entity.Open {
			priorityMap[item.ProductID] = int32(item.Priority)
			weightageMap[item.ProductID] = int32(item.Weightage)
		}
	}

	var (
		allocRes                int64
		err                     *srerr.Error
		productGroupCodeMapping map[int64]string                                         // TODO Product Group在Forecast先不做
		attrMap                 = make(map[int64]*parcel_type_definition.ParcelTypeAttr) // TODO Parcel Type在Forecast先不做
	)
	if conf.SmartAllocationEnabled {
		if taskConfig.AllocationRuleConfig == nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--AllocationRuleConfig not config. taskConfig=%v", taskConfig)
			allocateForecastResult.ForecastStatus = ForecastBlockedStatus
			return allocateForecastResult, err
		}
		// 记录allocate log
		logDetail.SoftRuleId = rule.Id
		logDetail.SoftInput = productIDs
		ctx = context.WithValue(ctx, constant.AllocationLogCtxKey, allocation2.NewLog())
		if taskConfig.AllocationRuleConfig.RuleDetail.CheapestFeeEnable {
			allocRes, err = softSrv.Allocate(ctx, int64(maskProductID), productIDs, rule, locVols, productPriority.RuleType, priorityMap, weightageMap, orderData.OmsAllocateRequest.ToPbOrderData(), taskConfig.AllocationRuleConfig.ShippingFeeConfig, constant.ForecastMasking, productGroupCodeMapping, attrMap)
		} else {
			allocRes, err = softSrv.Allocate(ctx, int64(maskProductID), productIDs, rule, locVols, productPriority.RuleType, priorityMap, weightageMap, orderData.OmsAllocateRequest.ToPbOrderData(), nil, constant.ForecastMasking, productGroupCodeMapping, attrMap)
		}
		// 记录allocate log
		softCriteriaLog := GetSoftCriteriaLog(ctx)
		logDetail.SoftCriteriaList = softCriteriaLog.List

		logger.CtxLogInfof(ctx, "Allocate Forecast--soft_criteria for allocate, masking product:%v, resp:%v", maskProductID, allocRes)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--soft allocate err. err:%v", err)
			allocateForecastResult.ForecastStatus = ForecastBlockedStatus
			return allocateForecastResult, err
		}
	} else {
		logger.CtxLogInfof(ctx, "Allocate Forecast--soft_criteria use default product:%v, ", conf.DefaultProduct)
		if objutil.ContainInt64(productIDs, conf.DefaultProduct) {
			allocRes = conf.DefaultProduct
		} else {
			logger.CtxLogErrorf(ctx, "Allocate Forecast--can't use default product. productIDs=%v, DefaultProduct=%v", productIDs, conf.DefaultProduct)
			allocateForecastResult.ForecastStatus = ForecastBlockedStatus
			return allocateForecastResult, srerr.New(srerr.NoAvailableProduct, maskProductID, "can't use default product")
		}
	}
	// 记录allocate log
	logDetail.SoftOutput = []int64{allocRes}
	logDetail.FulfillmentProductId = int(allocRes)
	// 预测更新运力统一到一个func
	a.IncrVolume(ctx, taskConfig, allocRes, softSrv, req, allocateForecastResult)
	allocateForecastResult.FulfillmentProductID = int(allocRes)
	allocateForecastResult.ForecastStatus = ForecastCompleteStatus

	return allocateForecastResult, nil
}

// 是否需要重新计算运费
func needRecalculate(ctx context.Context, maskProductID int, taskConfig *allocation.AllocateForecastTaskConfigEntity) bool {
	return taskConfig != nil && taskConfig.BaseInfo != nil && taskConfig.AllocationRuleConfig != nil && taskConfig.AllocationRuleConfig.RuleDetail != nil && taskConfig.
		AllocationRuleConfig.RuleDetail.CheapestFeeEnable &&
		!taskConfig.BaseInfo.ReCalcFee &&
		configutil.IsOpenMaskingPreCalcFeeSwitch(ctx, int64(maskProductID))
}

// 运费开关是否打开
func isESFSwitchOpen(taskConfig *allocation.AllocateForecastTaskConfigEntity) bool {
	return taskConfig != nil && taskConfig.AllocationRuleConfig != nil && taskConfig.AllocationRuleConfig.RuleDetail != nil && taskConfig.
		AllocationRuleConfig.RuleDetail.CheapestFeeEnable
}

func (a AllocateForecastServiceImpl) IncrVolume(ctx context.Context, taskConfig *allocation.AllocateForecastTaskConfigEntity, allocRes int64, softSrv *allocation2.SoftRuleService, req *model.OMSAllocateRequest, allocateForecastResult *AllocateForecastResult) {
	var locVols map[int64][]*rulevolume.MaskLocVolume
	if taskConfig.BaseInfo.LocalSoftCriteriaToggle {
		locVols = a.matchLocationVolumesForUpdate(ctx, taskConfig.BaseInfo.Id, taskConfig.BaseInfo.MaskProductId, []int{int(allocRes)}, req.PickupInfo.LocationIDs, req.DeliveryInfo.LocationIDs, req.PickupInfo.GetPostalCode(), req.DeliveryInfo.GetPostalCode())
	} else {
		// soft criteria 开关未打开，使用线上的local volume 配置
		locVols = a.matchOnlineLocationVolumesForUpdateVolume(ctx, int64(taskConfig.BaseInfo.MaskProductId), []int64{allocRes}, objutil.IntToInt64Slice(req.PickupInfo.LocationIDs), objutil.IntToInt64Slice(req.DeliveryInfo.LocationIDs), req.PickupInfo.GetPostalCode(), req.DeliveryInfo.GetPostalCode())
	}

	parcelAttr := &parcel_type_definition.ParcelTypeAttr{}
	// 更新运力需要对匹配到底的（zoneCode + zoneDirection） 或 routeCode去重
	_ = softSrv.VolumeCounter.IncrMaskVolume(ctx, int64(taskConfig.BaseInfo.MaskProductId), allocRes, "", parcelAttr)
	removeDupLocVols := rulevolume.RemoveDuplicationLocVols(locVols)
	for _, locVol := range removeDupLocVols[allocRes] {
		switch locVol.LocType {
		case rulevolume.MaskLocTypeRoute:
			// TODO group code
			_ = softSrv.VolumeCounter.IncrRouteVolume(ctx, int64(taskConfig.BaseInfo.MaskProductId), allocRes, "", locVol.LocCode, rule_mode.MplOrderRule, parcelAttr)
			allocateForecastResult.RouteCode = locVol.LocCode
		case rulevolume.MaskLocTypeZone:
			_ = softSrv.VolumeCounter.IncrZoneVolume(ctx, int64(taskConfig.BaseInfo.MaskProductId), allocRes, "", locVol.LocCode, locVol.ZoneDirection, rule_mode.MplOrderRule, parcelAttr)
			allocateForecastResult.ZoneCode = locVol.LocCode
			switch locVol.ZoneDirection {
			case rulevolume.MaskZoneDirectionOrigin:
				allocateForecastResult.OriginZoneCode = locVol.LocCode
			case rulevolume.MaskZoneDirectionDest:
				allocateForecastResult.DestZoneCode = locVol.LocCode
			}
		}
	}
}

func (s AllocateForecastServiceImpl) completeShippingFee(ctx context.Context, orderData *AllocateOrderDataEntity, productId []int, taskId int) *chargeentity.BatchAllocationESFResp {
	//组装请求参数
	req := getAllocationEsfReq(ctx, orderData, objutil.IntToInt64Slice(productId))
	// 请求finance查询运费
	resp, err := s.RateApi.BatchAllocatingESFWithGrpc(ctx, req, chargeentity.SceneForecast)
	if err != nil {
		logger.CtxLogErrorf(ctx, "forecast recalculate failed%v", err)
		monitoring.ReportError(ctx, ForecastPreShippingFee, CalculationFailed, fmt.Sprintf("forecast recalculate failed task_id = %v, err=%v", taskId, err))
		return nil
	}
	// 更新运费
	return resetShippingFee(ctx, orderData, resp, taskId)
}

func getAllocationEsfReq(ctx context.Context, orderData *AllocateOrderDataEntity, productIds []int64) *chargeentity.BatchAllocationESFReq {
	// 1.算出那些剩余的product需要计算运费
	_, remainProducts := needRefillProductIds(orderData.ShippingFeeList, productIds)
	rateReq := chargeentity.BatchAllocationESFReq{
		Token: constant.FreightApiTokens[strings.ToLower(envvar.GetEnvWithCtx(ctx))],
	}
	rateReq.Data = make([]*chargeentity.BatchAllocationESFReqDataItem, len(productIds))
	commonReqDataItem := allocation2.GetCommonReqDataItemForOrder(orderData.OmsAllocateRequest.ToPbOrderData())
	//commonReqDataItem := chargeentity.BatchAllocationESFReqDataItem{}
	for idx, productID := range remainProducts {
		reqDataItem := commonReqDataItem
		reqDataItem.ProductID = productID
		reqDataItem.UniqueId = fmt.Sprintf("uniq_%v", productID)
		reqDataItem.Timestamp = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
		rateReq.Data[idx] = &reqDataItem
	}

	return &rateReq
}

// 需要补齐的运费的product
func needRefillProductIds(preShippingFee []model.ProductShippingFee, productIds []int64) (map[int64]float64, []int64) {
	var remainingProduct []int64
	productEsfMap := map[int64]float64{}
	// 1.先转换成每个productId对应的运费Map
	for _, esfDetail := range preShippingFee {
		productEsfMap[esfDetail.ProductId] = esfDetail.AllocateShippingFee
	}
	// 2.如果提前计算运费中没有对应的product则需要计算
	for _, productId := range productIds {
		if _, ok := productEsfMap[productId]; !ok {
			remainingProduct = append(remainingProduct, productId)
		}
	}

	return productEsfMap, remainingProduct
}

// 更新一下运费
func resetShippingFee(ctx context.Context, orderData *AllocateOrderDataEntity, resp *chargeentity.BatchAllocationESFResp, taskId int) *chargeentity.BatchAllocationESFResp {
	if resp == nil || resp.Retcode != 0 || resp.Data == nil {
		monitoring.ReportError(ctx, ForecastPreShippingFee, CalculationFailed, fmt.Sprintf("task_id=%v recalculate failed %s", taskId, objutil.JsonString(resp)))
		return resp
	}
	// 这里不做失败处理上报，统一在后面做失败上报
	for _, esfDetail := range orderData.ShippingFeeList {
		resp.Data.AllocatingShippingFeeResult = append(resp.Data.AllocatingShippingFeeResult, &chargeentity.AllocatingShippingFeeResultItem{
			ProductId:             esfDetail.ProductId,
			AllocatingShippingFee: esfDetail.AllocateShippingFee,
		})
	}
	return resp
}

func (a AllocateForecastServiceImpl) SendScheduleCountStat(ctx context.Context, allocationLog *allocation.AllocationLog, taskId int) {
	// 调度可视化开关，避免上线出现问题无法关闭
	if !configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) {
		return
	}

	statCtx := ctxhelper.CloneTrace(ctx)
	statFunc := func() {
		defer func() {
			if e := recover(); e != nil {
				logger.CtxLogErrorf(statCtx, "masking forecast schedule count stat error:%v", e)
			}
		}()
		if err := a.ScheduleVisualStat.ScheduleCountStat(statCtx, allocationLog, schedule_stat.AllocateForecast, strconv.FormatInt(int64(taskId), 10)); err != nil {
			logger.CtxLogInfof(statCtx, "schedule visual count stat error, error:%v", err)
		}
	}

	if err := goroutinePool.Submit(statFunc); err != nil {
		_ = monitor.AwesomeReportEvent(ctx, monitoring.CatGoroutinePool, "ForecastSendScheduleCountStat", monitoring.StatusError, err.Error())
		logger.CtxLogErrorf(ctx, "submit async stat func to goroutine pool fail | err=%v", err)
	}
}

func GetSoftCriteriaLog(ctx context.Context) *allocation2.Log {
	allocationLog := ctx.Value(constant.AllocationLogCtxKey)
	if allocationLog == nil {
		return nil
	}
	softCriteriaList, dumpLog := allocationLog.(*allocation2.Log)
	if dumpLog {
		return softCriteriaList
	}
	return nil
}

func (a *AllocateForecastServiceImpl) matchLocationVolumes(
	ctx context.Context, taskID, maskProductID int, componentProductIDs, pickupLocIDs, deliveryLocIDs []int, pickupPostcode, deliveryPostcode string,
) map[int64]rulevolume.ProductLocVolumes {
	// 上报postcode是否为空的监控
	monitoring.ReportPostcode(ctx, pickupPostcode, deliveryPostcode)

	result := make(map[int64]rulevolume.ProductLocVolumes)
	for _, componentProductID := range componentProductIDs {
		var productLocVolumes rulevolume.ProductLocVolumes
		productLocVolumes.RouteVolumes, _ = a.ForecastVolumeService.MatchLocsToRoutes(
			ctx, taskID, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode,
		)
		productLocVolumes.ZoneVolumes, _ = a.ForecastVolumeService.MatchLocsToZones(
			ctx, taskID, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode,
		)
		result[int64(componentProductID)] = productLocVolumes
	}

	logger.CtxLogDebugf(ctx, "match forecast location volumes: %s", str.JsonStringForDebugLog(result))

	return result
}

func (a *AllocateForecastServiceImpl) matchOnlineLocationVolumes(
	ctx context.Context, maskProductID int, componentProductIDs, pickupLocIDs, deliveryLocIDs []int, pickupPostcode, deliveryPostcode string,
) map[int64]rulevolume.ProductLocVolumes {
	// 上报postcode是否为空的监控
	monitoring.ReportPostcode(ctx, pickupPostcode, deliveryPostcode)

	result := make(map[int64]rulevolume.ProductLocVolumes)
	for _, componentProductID := range componentProductIDs {
		var productLocVolumes rulevolume.ProductLocVolumes

		routeVolumes, err := a.MaskRuleVolumeSrv.MatchLocsToRoutes(
			ctx, int64(maskProductID), int64(componentProductID), objutil.IntToInt64Slice(pickupLocIDs),
			objutil.IntToInt64Slice(deliveryLocIDs), rule_mode.MplOrderRule, pickupPostcode, deliveryPostcode, forecastconstant.SingleAllocate)
		if err != nil {
			return result
		}
		productLocVolumes.RouteVolumes = routeVolumes

		zoneVolumes, err := a.MaskRuleVolumeSrv.MatchLocsToZones(
			ctx, int64(maskProductID), int64(componentProductID), objutil.IntToInt64Slice(pickupLocIDs),
			objutil.IntToInt64Slice(deliveryLocIDs), rule_mode.MplOrderRule, pickupPostcode, deliveryPostcode, forecastconstant.SingleAllocate)
		if err != nil {
			return result
		}
		productLocVolumes.ZoneVolumes = zoneVolumes

		result[int64(componentProductID)] = productLocVolumes
	}

	logger.CtxLogDebugf(ctx, "match matchOnlineLocationVolumes volumes result: %s", str.JsonStringForDebugLog(result))

	return result
}

func (a *AllocateForecastServiceImpl) matchLocationVolumesForUpdate(ctx context.Context, taskID, maskProductID int, componentProductIDs, pickupLocIDs, deliveryLocIDs []int, pickupPostcode, deliveryPostcode string) map[int64][]*rulevolume.MaskLocVolume {
	// 上报postcode监控
	monitoring.ReportPostcode(ctx, pickupPostcode, deliveryPostcode)

	var result map[int64][]*rulevolume.MaskLocVolume
	locVolumes, rerr := a.matchForecastRouteVolume(ctx, taskID, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode)
	if rerr != nil {
		return result
	}
	zoneVolumes, zerr := a.matchForecastZoneVolume(ctx, taskID, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode)
	if zerr != nil {
		return result
	}

	locVolumes = append(locVolumes, zoneVolumes...)
	result = convertToMap(locVolumes)

	logger.CtxLogDebugf(ctx, "match location volumes result: %s", str.JsonStringForDebugLog(result))
	return result
}

func (a *AllocateForecastServiceImpl) matchOnlineLocationVolumesForUpdateVolume(ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64, pickupPostcode, deliveryPostcode string) map[int64][]*rulevolume.MaskLocVolume {
	var result map[int64][]*rulevolume.MaskLocVolume
	locVolumes, rerr := a.matchRouteVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rule_mode.MplOrderRule, pickupPostcode, deliveryPostcode, allocation3.SingleAllocate)
	if rerr != nil {
		return nil
	}
	zoneVolumes, zerr := a.matchZoneVolume(ctx, maskProductID, componentProductIDs, pickupLocIDs, deliveryLocIDs, rule_mode.MplOrderRule, pickupPostcode, deliveryPostcode, allocation3.SingleAllocate)
	if zerr != nil {
		return nil
	}

	locVolumes = append(locVolumes, zoneVolumes...)
	result = convertToMap(locVolumes)

	logger.CtxLogDebugf(ctx, "match location volumes result for update volume: %s", str.JsonStringForDebugLog(result))
	return result
}

func convertToMap(locations []*rulevolume.MaskLocVolume) map[int64][]*rulevolume.MaskLocVolume {
	result := make(map[int64][]*rulevolume.MaskLocVolume)
	for _, loc := range locations {
		if result[loc.ProductId] == nil {
			result[loc.ProductId] = []*rulevolume.MaskLocVolume{}
		}
		result[loc.ProductId] = append(result[loc.ProductId], loc)
	}

	return result
}

func (a *AllocateForecastServiceImpl) matchRouteVolume(ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64, rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var routeVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		// 先匹配route运力
		routeVolumes, err := a.MaskRuleVolumeSrv.MatchLocsToRoutes(ctx, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
		if err != nil {
			return nil, err
		}
		routeVolumeList = append(routeVolumeList, routeVolumes...)
	}

	return routeVolumeList, nil
}

func (a *AllocateForecastServiceImpl) matchZoneVolume(ctx context.Context, maskProductID int64, componentProductIDs, pickupLocIDs, deliveryLocIDs []int64, rm rule_mode.RuleMode, pickupPostcode, deliveryPostcode string, allocationMethod int) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var zoneVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		zoneVolumes, err := a.MaskRuleVolumeSrv.MatchLocsToZones(ctx, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, rm, pickupPostcode, deliveryPostcode, allocationMethod)
		if err != nil {
			return nil, err
		}
		zoneVolumeList = append(zoneVolumeList, zoneVolumes...)
	}

	return zoneVolumeList, nil
}

func (a *AllocateForecastServiceImpl) matchForecastRouteVolume(ctx context.Context, taskID, maskProductID int, componentProductIDs, pickupLocIDs, deliveryLocIDs []int, pickupPostcode, deliveryPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var routeVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		// 先匹配route运力
		routeVolumes, err := a.ForecastVolumeService.MatchLocsToRoutes(ctx, taskID, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode)
		if err != nil {
			return nil, err
		}
		routeVolumeList = append(routeVolumeList, routeVolumes...)
	}

	return routeVolumeList, nil
}

func (a *AllocateForecastServiceImpl) matchForecastZoneVolume(ctx context.Context, taskID, maskProductID int, componentProductIDs, pickupLocIDs, deliveryLocIDs []int, pickupPostcode, deliveryPostcode string) ([]*rulevolume.MaskLocVolume, *srerr.Error) {
	var zoneVolumeList []*rulevolume.MaskLocVolume
	for _, componentProductID := range componentProductIDs {
		zoneVolumes, err := a.ForecastVolumeService.MatchLocsToZones(ctx, taskID, maskProductID, componentProductID, pickupLocIDs, deliveryLocIDs, pickupPostcode, deliveryPostcode)
		if err != nil {
			return nil, err
		}
		zoneVolumeList = append(zoneVolumeList, zoneVolumes...)
	}

	return zoneVolumeList, nil
}

func (a *AllocateForecastServiceImpl) getShopGroupIds(ctx context.Context, req *model.OMSAllocateRequest, taskConfig *allocation.AllocateForecastTaskConfigEntity) ([]int64, *srerr.Error) {
	shopIds := make([]int, 0, len(req.CheckoutItems))
	for _, item := range req.CheckoutItems {
		shopIds = append(shopIds, item.ShopId)
	}

	clientTagId := lpsclient.ClientTag3PLMasking
	if taskConfig.BaseInfo.ShopGroupChannelPriorityToggle {
		clientTagId = lpsclient.ClientTag3PLMaskingForecast
	}

	getShopGroupReq := &lpsclient.GetShopGroupRequest{
		MaskProductId: taskConfig.BaseInfo.MaskProductId,
		ClientTagId:   uint64(clientTagId),
		ShopIds:       shopIds,
	}
	lpsResp, err := a.lpsApi.GetShopGroup(ctx, getShopGroupReq)
	if err != nil {
		return nil, err
	}

	return lpsResp.ShopGroupIds, nil
}

func deleteDefaultWmsProductId(hardProdIDs []int, componentProducts []int, defaultWmsProduct int) []int64 {
	//1.判断数组长度,长度小于2时直接复制返回
	var results []int64
	if len(hardProdIDs) < 2 {
		for _, productID := range hardProdIDs {
			results = append(results, int64(productID))
		}
		return results
	}

	//2.判断default wms product是否属于 component product
	//3.true表示需要删去default wms product， false表示需要进行去重
	needDelete := true
	//SPLPS-4315：DefaultWmsProducts是个map，里面只有一个值，目前业务上配置的default wms product是唯一的
	for _, componentID := range componentProducts {
		//4.新逻辑情况2，有交集，去重即可
		if componentID == defaultWmsProduct {
			needDelete = false
			break
		}
	}
	//5. 先去重，遇到新逻辑情况1就进行过滤
	hardProdIDs = objutil.RemoveDuplicate(hardProdIDs)
	for _, productID := range hardProdIDs {
		//新逻辑情况1，过滤（删除）该product id
		if needDelete && (productID == defaultWmsProduct) {
			continue
		}
		results = append(results, int64(productID))
	}

	//logger.LogInfof("hardProdIDs:%+v, results:%+v", hardProdIDs, results)
	return results
}

func getForecastShippingFeeReq(ctx context.Context, productIds []int, orderInfo *pb.MaskingOrderInfo, shippingFeeConfigs []*allocation.ShippingFeeConfig) *chargeentity.BatchForecastAllocationESFReq {
	rateReq := chargeentity.BatchForecastAllocationESFReq{
		Token: constant.FreightApiTokens[strings.ToLower(envvar.GetEnvWithCtx(ctx))],
	}
	rateIdMap := make(map[string]int)
	for _, shippingFeeConfig := range shippingFeeConfigs {
		key := fmt.Sprintf("%v#%v", shippingFeeConfig.ProductId, shippingFeeConfig.WmsFlag)
		rateIdMap[key] = shippingFeeConfig.RateId
	}
	var wmsFlag int
	if orderInfo != nil && *orderInfo.IsWms {
		wmsFlag = forecastconstant.Wms
	} else {
		wmsFlag = forecastconstant.NoWms
	}
	commonReqDataItem := allocation2.GetForecastCommonReqDataItemForOrder(orderInfo)
	for _, productID := range productIds {
		reqDataItem := commonReqDataItem
		bothRateKey := fmt.Sprintf("%v#%v", productID, forecastconstant.Both)
		rateKey := fmt.Sprintf("%v#%v", productID, wmsFlag)
		if value, exist := rateIdMap[bothRateKey]; exist {
			// 优先both
			reqDataItem.RateID = value
		} else if value, exist := rateIdMap[rateKey]; exist {
			reqDataItem.RateID = value
		} else {
			// 没有查到相应的rate_id, 则不计算最低运费
			continue
		}
		reqDataItem.ProductID = int64(productID)
		reqDataItem.UniqueId = fmt.Sprintf("uniq_%v", productID)
		reqDataItem.Timestamp = uint32(timeutil.GetCurrentUnixTimeStamp(ctx))
		rateReq.Data = append(rateReq.Data, &reqDataItem)
	}

	return &rateReq
}

func (a AllocateForecastServiceImpl) recalculateProductEsf(ctx context.Context, productIds []int, orderInfo *pb.MaskingOrderInfo, shippingFeeConfigs []*allocation.ShippingFeeConfig) *chargeentity.BatchAllocationESFResp {
	rateReq := getForecastShippingFeeReq(ctx, productIds, orderInfo, shippingFeeConfigs)
	rateResp, err := a.RateApi.BatchForecastAllocatingESF(ctx, rateReq)
	if err != nil {
		logger.CtxLogErrorf(ctx, "recalculate esf failed %v", err)
		monitoring.ReportError(ctx, ForecastPreShippingFee, CalculateEsfFailed, fmt.Sprintf("calculate esf failed %v", err))
		return nil
	}

	return rateResp
}

func setToCtx(ctx context.Context, rateResp *chargeentity.BatchAllocationESFResp) context.Context {
	if rateResp != nil {
		ctx = requestid.SetToCtxWithKey(ctx, rateResp, requestid.CtxKey{})
	}

	return ctx
}
