package masking_forecast

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/ctxhelper"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	gohbase "git.garena.com/shopee/bg-logistics/go/ssc-gohbase"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	ordentity "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/order_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mockutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/zip"
	jsoniter "github.com/json-iterator/go"
	"sort"
	"strconv"
	"time"
)

type AllocateForecastService interface {
	ForecastAllocate(
		ctx context.Context, orderData *AllocateOrderDataEntity, taskConfig *allocation.AllocateForecastTaskConfigEntity,
		rule *rule.MaskRule, conf *config.MaskAllocationConfigTab, productInfo *lpsclient.ProductDetailInfo, softSrv *allocation2.SoftRuleService,
	) (*AllocateForecastResult, *srerr.Error)
}

type AllocateForecastServiceImpl struct {
	AllocationConfigRepo  config.AllocationConfigRepo
	AllocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo
	AllocateRankService   service.AllocateRankService
	OuterCheck            outercheck.AllOuterCheckService
	MaskRuleVolumeSrv     rulevolume2.MaskRuleVolumeService
	ForecastVolumeService forecast_volume.ForecastLocationVolumeService
	allocationSrv         *allocation2.AllocationServiceImpl
	lpsApi                lpsclient.LpsApi
	AddrRepo              address.AddrRepo
	RedisClient           *redis.Client
	ScheduleVisualStat    schedule_visual.ScheduleCountStatInterface
	RateApi               chargeclient.ChargeApi
}

func NewAllocateForecastTaskServiceImpl(
	allocationConfigRepo config.AllocationConfigRepo,
	allocateOrderDataRepo allocate_order_data_repo.AllocateOrderDataRepo,
	allocateRankSrv service.AllocateRankService,
	outerChecker outercheck.AllOuterCheckService,
	maskRuleVolumeSrv rulevolume2.MaskRuleVolumeService,
	forecastVolumeSrv forecast_volume.ForecastLocationVolumeService,
	allocationSrv *allocation2.AllocationServiceImpl,
	lpsApi lpsclient.LpsApi,
	AddrRepo address.AddrRepo,
	redisClient *redis.Client,
	scheduleVisualStat schedule_visual.ScheduleCountStatInterface, RateApi chargeclient.ChargeApi) *AllocateForecastServiceImpl {
	return &AllocateForecastServiceImpl{
		AllocationConfigRepo:  allocationConfigRepo,
		AllocateOrderDataRepo: allocateOrderDataRepo,
		AllocateRankService:   allocateRankSrv,
		OuterCheck:            outerChecker,
		MaskRuleVolumeSrv:     maskRuleVolumeSrv,
		ForecastVolumeService: forecastVolumeSrv,
		allocationSrv:         allocationSrv,
		lpsApi:                lpsApi,
		AddrRepo:              AddrRepo,
		RedisClient:           redisClient,
		ScheduleVisualStat:    scheduleVisualStat,
		RateApi:               RateApi,
	}
}

func (a AllocateForecastServiceImpl) convertToRule(ctx context.Context, taskConfig *allocation.AllocateForecastTaskConfigEntity) (*rule.MaskRule, *srerr.Error) {
	// consider of taskConfig un-exception
	if taskConfig.BaseInfo == nil || taskConfig.AllocationRuleConfig == nil ||
		taskConfig.AllocationRuleConfig.RuleDetail == nil || taskConfig.AllocationRuleConfig.MaskProductRuleVolumeConfig == nil {
		logger.CtxLogErrorf(ctx, "Allocate Forecast-- convertToRule err. taskConfig == %v", taskConfig)
		return nil, srerr.New(srerr.TypeConvertErr, taskConfig, "can't convert to rule")
	}
	volume := taskConfig.AllocationRuleConfig.MaskProductRuleVolumeConfig
	detail := taskConfig.AllocationRuleConfig.RuleDetail
	rl := &rule.MaskRule{
		Id:                    int64(taskConfig.BaseInfo.Id),
		MaskProductId:         int64(taskConfig.BaseInfo.MaskProductId),
		Status:                rule.MaskRuleStatusActive,
		EnableProductPriority: taskConfig.BaseInfo.ShopGroupChannelPriorityToggle,
	}

	if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeRoute) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeRoute
	} else if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeZone) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeZone
	} else if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeCountry) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeCountry
	} else if volume != nil && volume.RuleType == uint32(rulevolume.LocVolumeTypeRouteAndZone) {
		rl.LocVolumeType = rulevolume.LocVolumeTypeRouteAndZone
	}

	ruleSteps := rule.MaskRuleSteps{}
	if detail.MinVolumeEnable {
		detail.MinVolumeCountryEnable = detail.MinVolumeEnable
		detail.MinVolumeCountrySort = detail.MinVolumeSort
		if rl.LocVolumeType != rulevolume.LocVolumeTypeCountry {
			detail.MinVolumeZoneRouteEnable = detail.MinVolumeEnable
			detail.MinVolumeZoneRouteSort = detail.MinVolumeSort
		}
	}
	if detail.MaxCapacityEnable {
		detail.MaxCapacityCountryEnable = detail.MaxCapacityEnable
		detail.MaxCapacityCountrySort = detail.MaxCapacitySort
		if rl.LocVolumeType != rulevolume.LocVolumeTypeCountry {
			detail.MaxCapacityZoneRouteEnable = detail.MaxCapacityEnable
			detail.MaxCapacityZoneRouteSort = detail.MaxCapacitySort
		}
	}
	if detail.MinVolumeCountryEnable {
		minVolumes := map[rule.VolumeKey]int32{}
		for _, limit := range volume.DefaultVolumeLimit {
			key := rule.VolumeKey{FulfillmentProductID: int64(limit.ProductId)}
			minVolumes[key] = int32(limit.MinVolume)
		}
		step := rule.MaskRuleStep{
			Priority:      int32(detail.MinVolumeSort),
			MaskStepType:  rule.MaskStepMinVolumeCountry,
			MinVolumeData: &rule.MinVolumeData{MinVolumes: minVolumes},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityCountryEnable {
		maxCapacities := map[rule.VolumeKey]int32{}
		isHardCaps := make(map[rule.VolumeKey]bool)
		for _, limit := range volume.DefaultVolumeLimit {
			key := rule.VolumeKey{FulfillmentProductID: int64(limit.ProductId)}
			maxCapacities[key] = int32(limit.MaxCapacity)
			isHardCaps[key] = limit.IsHardCap
		}
		step := rule.MaskRuleStep{
			Priority:           int32(detail.MaxCapacityCountrySort),
			MaskStepType:       rule.MaskStepMaxCapacityCountry,
			MaxCapacityData:    &rule.MaxCapacityData{MaxCapacities: maxCapacities},
			IsHardCapacityData: &rule.IsHardCapacityData{IsHardCaps: isHardCaps},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MinVolumeZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MinVolumeZoneRouteSort),
			MaskStepType: rule.MaskStepMinVolumeZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail.MaxCapacityZoneRouteEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxCapacityZoneRouteSort),
			MaskStepType: rule.MaskStepMaxCapacityZoneRoute,
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail != nil && detail.MaxBatchVolumeEnable {
		volumesInOneBatch := map[int64]int32{}
		for _, limit := range detail.Limit {
			volumesInOneBatch[int64(limit.ProductId)] = int32(limit.MaxVolumeEachBatch)
		}
		step := rule.MaskRuleStep{
			Priority:     int32(detail.MaxBatchVolumeSort),
			MaskStepType: rule.MaskStepMaxBatchVolume,
			BatchVolumeData: &rule.BatchVolumeData{
				BatchVolume:      int32(detail.MaxBatchVolume),
				VolumeInOneBatch: volumesInOneBatch,
			},
		}
		ruleSteps = append(ruleSteps, step)
	}
	if detail != nil && detail.CheapestFeeEnable {
		step := rule.MaskRuleStep{
			Priority:     int32(detail.CheapestFeeSort),
			MaskStepType: rule.MaskStepCheapestFee,
		}
		ruleSteps = append(ruleSteps, step)
	}
	sort.Sort(ruleSteps)
	rl.MaskRuleSteps = ruleSteps

	return rl, nil
}

func addMockInContext(ctx context.Context, mockConfig configutil.AllocateForecastUseMockConfig) context.Context {
	mockValues := map[string]string{
		mockutil.MockRequestID:  mockConfig.MockRequestID,
		mockutil.MockSystemsKey: mockConfig.MockSystemsKey,
		mockutil.MockTypeKey:    mockConfig.MockTypeKey,
	}
	ctx = context.WithValue(ctx, constant.CtxMock, mockValues)

	return ctx
}

func (a *AllocateForecastServiceImpl) TransferResultToTab(ctx context.Context, result *gohbase.Result) (*model.AllocateOrderDataTab, *srerr.Error) {
	//转换数据（包括解压+模型转换） 并写入channel
	if result == nil {
		logger.CtxLogErrorf(ctx, "TransferResultToEntity|cannot parse nil hbase result, will skip it")
		return nil, srerr.New(srerr.ParamErr, nil, "result is nil, can't convert it")
	}
	resultEntity := model.AllocationHbaseEntity{}
	//此处实际v只会有一个，for循环的模式能避免result.Cells[0]空指针panic
	for _, v := range result.Cells {
		key := string(v.Qualifier[:])
		decodeBytes, err := zip.ZSTDDecompress(v.Value)
		if err != nil {
			logger.CtxLogErrorf(ctx, "TransferResultToEntity|key:%v, decode value err:%v", key, err)
			continue
		}
		value := string(decodeBytes)
		if err := jsoniter.Unmarshal(decodeBytes, &resultEntity); err != nil {
			logger.CtxLogDebugf(ctx, "TransferResultToEntity|value:%v, unmarshal value err:%v", value, err)
		}
	}
	tab, cErr := a.convertResultToAllocationTab(ctx, resultEntity)
	if cErr != nil {
		logger.CtxLogErrorf(ctx, "TransferResultToEntity|convert result entity:%+v, err:%v", resultEntity, cErr)
		return nil, cErr
	}
	return tab, cErr
}

func (a *AllocateForecastServiceImpl) convertResultToAllocationTab(ctx context.Context, resultEntity model.AllocationHbaseEntity) (*model.AllocateOrderDataTab, *srerr.Error) {
	orderData := &model.AllocateOrderDataTab{
		OrderId:              resultEntity.OrderId,
		MaskingProductID:     resultEntity.MaskProductId,
		FulfillmentProductID: resultEntity.FulfillmentProductId,
		ShopGroupId:          int(resultEntity.ShopGroupId),
		OrderTime:            uint32(resultEntity.RequestTime),
		HardResultHbase:      resultEntity.HardOutput,
	}

	//填充 zone/route code
	//zone和route是互斥的
	if len(resultEntity.RouteCodes) != 0 {
		orderData.RouteCode = resultEntity.RouteCodes[len(resultEntity.RouteCodes)-1]
	} else {
		if resultEntity.ZoneOriginCode != "" {
			orderData.ZoneCode = resultEntity.ZoneOriginCode
		} else {
			orderData.ZoneCode = resultEntity.ZoneDestinationCode
		}
		orderData.OriginZoneCode = resultEntity.ZoneOriginCode
		orderData.DestZoneCode = resultEntity.ZoneDestinationCode
	}

	//转换request
	request := &model.OmsAllocRequest{}
	if err := jsoniter.UnmarshalFromString(resultEntity.RequestDataStr, request); err != nil { //报错，返回
		logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to schema err:%v", resultEntity.OrderId, err)
		return nil, srerr.With(srerr.DataErr, nil, err)
	} else {
		allocReq, cErr := a.ConvertOmsAllocRequest(ctx, request)
		if cErr != nil { //报错，返回
			logger.CtxLogErrorf(ctx, "convertResultToAllocationTab|order id:%v, convert request to OmsAllocRequest err:%v", resultEntity.OrderId, cErr)
			return nil, cErr
		} else {
			orderData.OmsAllocateRequestHbase = allocReq
		}
	}
	return orderData, nil
}

func (a *AllocateForecastServiceImpl) ConvertOmsAllocRequest(ctx context.Context, originReq *model.OmsAllocRequest) (*model.OMSAllocateRequest, *srerr.Error) {
	// https://jira.shopee.io/browse/SPLPS-1972
	// Store Order Snapshot of Fulfilment Channel that Pass Hard Rule
	var snapshot *model.SnapShot
	if originReq.Snapshot != nil {
		snapshot = &model.SnapShot{IgnoreSnapshot: originReq.Snapshot.IgnoreSnapshot, ShippingChannels: originReq.Snapshot.ShippingChannels}
	} else {
		snapshot = nil
	}

	forderID := originReq.FOrderID
	if originReq.FOrderIDStr != nil {
		forderID, _ = strconv.ParseUint(*originReq.FOrderIDStr, 10, 64)
	}
	var allocReq = &model.OMSAllocateRequest{
		MaskingProductID:     originReq.MaskingChannelId,
		PaymentMethod:        originReq.PaymentMethod,
		TotalPrice:           originReq.TotalPrice,
		CodAmount:            originReq.CodAmount,
		Cogs:                 originReq.Cogs,
		PartialFulfillment:   0,
		IsWms:                originReq.IsWms,
		WhsId:                originReq.WhsId,
		CheckoutItems:        originReq.ConvertCheckoutItem(),
		FOrderID:             forderID,
		OrderID:              originReq.OrderID,
		BuyerPaidShippingFee: originReq.BuyerPaidShippingFee,
		SellerTaxNumber:      originReq.SellerTaxNumber,
		StateRegistration:    originReq.StateRegistration,
		Snapshot:             snapshot,
		RosOptin:             originReq.RosOptin,
		RosEligible:          originReq.RosEligible,
	}
	var pickupInfo, deliverInfo *ordentity.AddressInfo
	if originReq.PickupInfo != nil && originReq.DeliveryInfo != nil {
		pickupInfo, deliverInfo = originReq.ConvertAddressInfo()
	}
	err := a.FillLocationIds(ctx, pickupInfo)
	if err != nil {
		return nil, err
	}
	err = a.FillLocationIds(ctx, deliverInfo)
	if err != nil {
		return nil, err
	}
	allocReq.PickupInfo = pickupInfo
	allocReq.DeliveryInfo = deliverInfo
	if originReq.PartialFulfillment {
		allocReq.PartialFulfillment = 1
	}
	return allocReq, nil
}

func (a *AllocateForecastServiceImpl) FillLocationIds(ctx context.Context, info *ordentity.AddressInfo) *srerr.Error {
	// 已经存在location id不用转换
	if info != nil && info.StateLocationId != nil && *info.StateLocationId > 0 {
		return nil
	}
	if info == nil || info.State == nil || info.City == nil {
		return srerr.New(srerr.ParamErr, nil, "missing location core information[state, city]")
	}
	locInfo, err := a.AddrRepo.GetLocationByLocFullPathName(ctx, info.GetCountry(), info.GetState(), info.GetCity(), info.GetDistrict(), info.GetStreet())
	if err != nil {
		return err
	}
	//装填 location id
	var stateLocationId = int(locInfo.GetStateLocId())
	info.StateLocationId = &stateLocationId
	var cityLocationId = int(locInfo.GetCityLocId())
	info.CityLocationId = &cityLocationId
	locIds := []int{stateLocationId, cityLocationId}
	if locInfo.GetDistrictLocId() > 0 {
		var districtLocationId = int(locInfo.GetDistrictLocId())
		info.DistrictLocationId = &districtLocationId
		locIds = append(locIds, districtLocationId)
	}
	if locInfo.GetStreetLocId() > 0 {
		var streetLocationId = int(locInfo.GetStreetLocId())
		info.StreetLocationId = &streetLocationId
		locIds = append(locIds, streetLocationId)
	}
	info.LocationIDs = locIds
	return nil
}

func (a AllocateForecastServiceImpl) GetTaskTotalDataCount(ctx context.Context, taskConfig *allocation.AllocateForecastTaskConfigEntity, dateTime time.Time) {
	if !configutil.IsOpenScheduleVisualSwitch(ctx, schedule_stat.GetBusinessTypeName(schedule_stat.AllocateForecast)) {
		return
	}
	go func() {
		defer func() {
			if e := recover(); e != nil {
				logger.CtxLogErrorf(ctx, "masking forecast get order total count error :%v", e)
			}
		}()
		newCtx := ctxhelper.CloneTrace(ctx)
		orderCount, err1 := a.AllocateOrderDataRepo.GetAllocateOrderCount(newCtx, taskConfig.BaseInfo.MaskProductId, dateTime)
		if err1 != nil {
			logger.CtxLogErrorf(newCtx, "get order count error, masking_product_id:%v, dataTime:%v, err:%v", orderCount, dateTime, err1)
		}
		a.RedisClient.IncrBy(newCtx, fmt.Sprintf(constant.TaskScheduleTotalOrderNum, taskConfig.BaseInfo.Id), orderCount)
	}()
}
