package repository

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	scheduleVisualTab = "schedule_visual_tab"
)

var ScheduleVisualTabHook = &ScheduleVisualTab{}

type ScheduleVisualTab struct {
	ID                uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ScheduleDimension string `gorm:"column:schedule_dimension;NOT NULL"`
	BusinessType      uint   `gorm:"column:business_type;NOT NULL"`
	BusinessID        string `gorm:"column:business_id;NOT NULL"`
	SupScheduleFactor string `gorm:"column:sup_schedule_factor"`
	ScheduleFactor    string `gorm:"column:schedule_factor;NOT NULL"`
	ScheduleResult    string `gorm:"column:schedule_result;NOT NULL"`
	StatNum           int64  `gorm:"column:stat_num"`
	ResultType        uint   `gorm:"column:result_type"`
	Ctime             uint   `gorm:"column:ctime;NOT NULL"`
	Mtime             uint   `gorm:"column:mtime;NOT NULL"`
	ScheduleChain     string `gorm:"column:schedule_chain"`
}

func (m *ScheduleVisualTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        m.ID,
		ModelName: m.TableName(),
	}
}

func (m *ScheduleVisualTab) TableName() string {
	return scheduleVisualTab
}

func (m *ScheduleVisualTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (m *ScheduleVisualTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}
