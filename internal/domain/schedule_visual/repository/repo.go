package repository

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

type ScheduleVisualRepoInterface interface {
	GetScheduleVisualTabByParam(ctx context.Context, params map[string]interface{}) ([]*ScheduleVisualTab, error)
	SaveScheduleVisualStat(ctx context.Context, insertList []*ScheduleVisualTab, deleteIdList []uint64) error
	ClearScheduleVisualDataByTime(ctx context.Context, clearTime int64) error
}

type ScheduleVisualRepo struct {
}

func NewScheduleVisualRepo() *ScheduleVisualRepo {
	return &ScheduleVisualRepo{}
}

func (s *ScheduleVisualRepo) GetScheduleVisualTabByParam(ctx context.Context, params map[string]interface{}) ([]*ScheduleVisualTab, error) {
	var scheduleVisualTabList []*ScheduleVisualTab
	err := dbutil.Select(ctx, ScheduleVisualTabHook, params, &scheduleVisualTabList)
	if err != nil && err != scormv2.ErrRecordNotFound {
		return nil, err
	}
	return scheduleVisualTabList, nil
}

func (s *ScheduleVisualRepo) SaveScheduleVisualStat(ctx context.Context, insertList []*ScheduleVisualTab, deleteIdList []uint64) error {
	db, err := dbutil.MasterDB(ctx, ScheduleVisualTabHook)
	if err != nil {
		return err
	}
	ctx = scormv2.BindContext(ctx, db)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if len(deleteIdList) > 0 {
			if err1 := tx.Table(ScheduleVisualTabHook.TableName()).Where("id in (?)", deleteIdList).Delete(&ScheduleVisualTab{}).GetError(); err1 != nil {
				return err1
			}
		}

		if len(insertList) > 0 {
			if err1 := tx.Table(ScheduleVisualTabHook.TableName()).CreateInBatches(insertList, 100).GetError(); err1 != nil {
				return err1
			}
		}
		return nil
	})
	return err
}

func (s *ScheduleVisualRepo) ClearScheduleVisualDataByTime(ctx context.Context, clearTime int64) error {
	err := dbutil.Delete(ctx, ScheduleVisualTabHook, map[string]interface{}{"ctime < ?": clearTime}, dbutil.ModelInfo{})
	if err != nil {
		return err
	}
	return nil
}
