package schedule_stat

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
	"sync"
)

// TODO 使用自旋锁
var lock sync.RWMutex

type MaskingForecastScheduleVisualStat struct {
	RedisClient  *redis.Client
	StatisticMap map[string]int64
}

func NewMaskingForecastScheduleVisualStat(redisClient *redis.Client) *MaskingForecastScheduleVisualStat {
	statisticMap := make(map[string]int64)
	maskingForecastScheduleVisualStat := &MaskingForecastScheduleVisualStat{
		RedisClient:  redisClient,
		StatisticMap: statisticMap,
	}
	Register(AllocateForecast, maskingForecastScheduleVisualStat)
	return maskingForecastScheduleVisualStat
}

func (m *MaskingForecastScheduleVisualStat) ScheduleResultAnalyze(ctx context.Context, log interface{}, businessId string, businessType int) (map[string]map[string]int64, []string, *srerr.Error) {
	result := make(map[string]map[string]int64)
	//1. 类型转换
	allocateLog, ok := log.(*allocation.AllocationLog)
	if !ok {
		return nil, nil, nil
	}
	//2. 遍历处理log_detail
	var scheduleDimensionList []string
	for _, logDetail := range allocateLog.List {
		currentResultMap, finalResultMap := ParseAllocateLog(logDetail)
		// currentResult stat
		currentKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, CurrentResult.String())
		result[currentKey] = currentResultMap
		// finalResult stat
		finalKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, FinalResult.String())
		result[finalKey] = finalResultMap
		// 记录schedule dimension
		scheduleDimensionList = append(scheduleDimensionList, strconv.FormatInt(int64(logDetail.MaskProductId), 10))
	}

	return result, scheduleDimensionList, nil
}

func (m *MaskingForecastScheduleVisualStat) SaveScheduleStat(ctx context.Context, statResult map[string]map[string]int64, scheduleDimensionList []string, businessType int, businessId string) *srerr.Error {
	//1. 保存数据到redis
	for key, value := range statResult {
		for field, num := range value {
			//m.RedisClient.HIncrBy(ctx, key, field, num)
			newKey := fmt.Sprintf("%v%v%v%v%v", MaskingForecastSyncMapStatPrefix+businessId, MaskingSyncMapSeparator, key, MaskingSyncMapSeparator, field)
			lock.Lock()
			if statValue, exist := m.StatisticMap[newKey]; exist {
				m.StatisticMap[newKey] = statValue + num
			} else {
				m.StatisticMap[newKey] = num
			}
			lock.Unlock()
		}
		// 设置超时时间为24小时
		//m.RedisClient.Expire(ctx, key, MaskingForecastKeyTimeOut)
	}

	//2. 记录运力key的key
	for _, scheduleDimension := range scheduleDimensionList {
		key := fmt.Sprintf(ScheduleDimensionKeyPattern, timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MaskingScheduleStatHourTimeFormat))
		field := fmt.Sprintf(ScheduleDimensionFieldPattern, businessType, businessId, scheduleDimension)
		//_ = m.RedisClient.HIncrBy(ctx, key, field, 1).Err()
		//_ = m.RedisClient.Expire(ctx, key, ScheduleDimensionKeyTimeOut).Err()
		newKey := fmt.Sprintf("%v%v%v%v%v", MaskingForecastSyncMapDimensionPrefix+businessId, MaskingSyncMapSeparator, key, MaskingSyncMapSeparator, field)
		lock.Lock()
		m.StatisticMap[newKey] = 1
		lock.Unlock()
	}

	return nil
}

func (m *MaskingForecastScheduleVisualStat) SyncScheduleStat(ctx context.Context, businessId string) *srerr.Error {
	var redisGain int
	lock.Lock()
	pipeline := m.RedisClient.Pipeline()
	for key, value := range m.StatisticMap {
		newKey := fmt.Sprintf("%v", key)
		if strings.HasPrefix(newKey, MaskingForecastSyncMapStatPrefix+businessId) {
			keyList := strings.Split(newKey, MaskingSyncMapSeparator)
			if len(keyList) == MaskingSyncMapLen {
				pipeline.HIncrBy(ctx, keyList[1], keyList[2], value)
				// 设置超时时间为24小时
				pipeline.Expire(ctx, keyList[1], MaskingForecastKeyTimeOut)
				redisGain++
			}
			delete(m.StatisticMap, key)
		}
		if strings.HasPrefix(newKey, MaskingForecastSyncMapDimensionPrefix+businessId) {
			keyList := strings.Split(newKey, MaskingSyncMapSeparator)
			if len(keyList) == MaskingSyncMapLen {
				pipeline.HSet(ctx, keyList[1], keyList[2], value)
				// 设置超时时间为2小时
				pipeline.Expire(ctx, keyList[1], ScheduleDimensionKeyTimeOut)
				redisGain++
			}
			delete(m.StatisticMap, key)
		}
	}
	lock.Unlock()

	if _, err := pipeline.Exec(ctx); err != nil {
		logger.CtxLogErrorf(ctx, "execute sync schedule status pipeline fail | err=%v", err)
	}

	logger.CtxLogDebugf(ctx, "save schedule stat to redis gain is : %d", redisGain)

	return nil
}
