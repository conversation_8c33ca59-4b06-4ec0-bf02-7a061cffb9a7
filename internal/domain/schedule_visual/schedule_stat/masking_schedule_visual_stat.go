package schedule_stat

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

type MaskingScheduleVisualStat struct {
	RedisClient *redis.Client
}

func NewMaskingScheduleVisualStat(redisClient *redis.Client) *MaskingScheduleVisualStat {
	maskingScheduleVisualStat := &MaskingScheduleVisualStat{
		RedisClient: redisClient,
	}
	Register(Allocate, maskingScheduleVisualStat)
	return maskingScheduleVisualStat
}

func (m *MaskingScheduleVisualStat) ScheduleResultAnalyze(ctx context.Context, log interface{}, businessId string, businessType int) (map[string]map[string]int64, []string, *srerr.Error) {
	if businessId == "" {
		businessId = timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MaskingScheduleStatHourTimeFormat)
	}
	result := make(map[string]map[string]int64)
	//1. 类型转换
	logDetail, ok := log.(*allocation.LogDetail)
	if !ok {
		return nil, nil, nil
	}
	//2. 遍历处理log_detail
	var scheduleDimensionList []string
	currentResultMap, finalResultMap := ParseAllocateLog(logDetail)
	// currentResult stat
	currentKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, CurrentResult.String())
	result[currentKey] = currentResultMap
	// finalResult stat
	finalKey := fmt.Sprintf(KeyPattern, businessType, businessId, logDetail.MaskProductId, FinalResult.String())
	result[finalKey] = finalResultMap
	// 记录schedule dimension
	scheduleDimensionList = append(scheduleDimensionList, strconv.FormatInt(int64(logDetail.MaskProductId), 10))

	return result, scheduleDimensionList, nil
}

func (m *MaskingScheduleVisualStat) SaveScheduleStat(ctx context.Context, statResult map[string]map[string]int64, scheduleDimensionList []string, businessType int, businessId string) *srerr.Error {
	//1. 保存数据到redis
	var redisGain int
	pipeline := m.RedisClient.Pipeline()
	for key, value := range statResult {
		for field, num := range value {
			pipeline.HIncrBy(ctx, key, field, num)
			redisGain++
		}
		// 设置超时时间为2小时
		pipeline.Expire(ctx, key, MaskingKeyTimeOut)
	}
	if _, err := pipeline.Exec(ctx); err != nil {
		logger.CtxLogErrorf(ctx, "execute save data pipeline fail | err=%v", err)
	}

	//2. 记录运力key的key
	pipeline = m.RedisClient.Pipeline()
	for _, scheduleDimension := range scheduleDimensionList {
		key := fmt.Sprintf(ScheduleDimensionKeyPattern, timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MaskingScheduleStatHourTimeFormat))
		field := fmt.Sprintf(ScheduleDimensionFieldPattern, businessType, businessId, scheduleDimension)
		pipeline.HIncrBy(ctx, key, field, 1)
		pipeline.Expire(ctx, key, ScheduleDimensionKeyTimeOut)
		redisGain++
	}
	if _, err := pipeline.Exec(ctx); err != nil {
		logger.CtxLogErrorf(ctx, "execute save volume key pipeline fail | err=%v", err)
	}

	logger.CtxLogDebugf(ctx, "save schedule visual to redis gain is : %d", redisGain)

	return nil
}

func (m *MaskingScheduleVisualStat) SyncScheduleStat(ctx context.Context, businessId string) *srerr.Error {
	return nil
}
