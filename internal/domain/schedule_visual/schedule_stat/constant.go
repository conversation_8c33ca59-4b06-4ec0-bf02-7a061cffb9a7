package schedule_stat

import "time"

// 业务类型
const (
	Allocate         = 1
	AllocateForecast = 2
	Routing          = 3
	RoutingForecast  = 4
	AllocateV2       = 5
)

const (
	AllocateName         = "Allocate"
	AllocateForecastName = "AllocateForecast"
	RoutingName          = "Routing"
	RoutingForecastName  = "RoutingForecast"
	AllocateV2Name       = "AllocateV2"
)

func GetBusinessTypeName(businessType int) string {
	switch businessType {
	case Allocate:
		return AllocateName
	case AllocateForecast:
		return AllocateForecastName
	case Routing:
		return RoutingName
	case RoutingForecast:
		return RoutingForecastName
	case AllocateV2:
		return AllocateV2Name
	}
	return ""
}

const ScheduleVisualSeparator = "&#&"
const MaskingForecastSyncMapStatPrefix = "MaskingForecastSyncMapStatPrefix"
const MaskingForecastSyncMapDimensionPrefix = "MaskingForecastSyncMapDimensionPrefix"
const MaskingSyncMapSeparator = "|&#|"
const MaskingSyncMapLen = 3
const ScheduleChainSeparator = "||"
const MaskingSyncMapStatPrefix = "MaskingSyncMapStatPrefix"
const MaskingSyncMapDimensionPrefix = "MaskingSyncMapDimensionPrefix"

// 记录key的key的前缀
const (
	ScheduleDimensionKeyPattern   = "schedule_dimension_key&#&%v"
	ScheduleDimensionFieldPattern = "%v&#&%v&#&%v"
)

// 统计时间格式
const MaskingScheduleStatHourTimeFormat = "**********"

// stat key pattern
const (
	KeyPattern   = "%v&#&%v&#&%v&#&%v"
	FieldPattern = "%v&#&%v&#&%v&#&%v"
)

const (
	KeyLength   = 4
	FieldLength = 4
)

// result type
type ResultType uint

const (
	CurrentResult ResultType = 1
	FinalResult   ResultType = 2
)

func (r ResultType) String() string {
	switch r {
	case CurrentResult:
		return "current_result"
	case FinalResult:
		return "final_result"
	}
	return "unknown_result"
}

// masking规则枚举值
const (
	Failed                 = "Failed"
	ShopGroup              = "shop_group"
	HardFactor             = "Allocated by hard criteria"
	SoftFactor             = "Allocated by soft criteria"
	SoftCriteriaRulePrefix = "RuleId:"
	SoftCriteriaRule       = "RuleId:%v"
	CriteriaFactor         = "%v %v"

	TotalOrder           = "TotalOrder"
	EnterHardCriteria    = "Enter hard criteria"
	EnterSoftCriteria    = "Enter soft criteria"
	EnterDefaultCriteria = "Enter default criteria"
)

// expired time
const (
	MaskingForecastKeyTimeOut   = 24 * time.Hour
	MaskingKeyTimeOut           = 2 * time.Hour
	RoutingForecastKeyTimeOut   = 24 * time.Hour
	RoutingKeyTimeOut           = 2 * time.Hour
	ScheduleDimensionKeyTimeOut = 2 * time.Hour
)
