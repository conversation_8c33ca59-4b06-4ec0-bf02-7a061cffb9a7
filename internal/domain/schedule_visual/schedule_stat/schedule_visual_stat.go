package schedule_stat

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ScheduleVisualSet struct {
	MaskingScheduleVisualStat         *MaskingScheduleVisualStat
	MaskingScheduleVisualStatV2       *MaskingScheduleVisualStatV2
	MaskingForecastScheduleVisualStat *MaskingForecastScheduleVisualStat
}

func NewScheduleVisualSet(maskingScheduleVisualStat *MaskingScheduleVisualStat, maskingScheduleVisualStatV2 *MaskingScheduleVisualStatV2, maskingForecastScheduleVisualStat *MaskingForecastScheduleVisualStat) *ScheduleVisualSet {
	return &ScheduleVisualSet{
		MaskingScheduleVisualStat:         maskingScheduleVisualStat,
		MaskingScheduleVisualStatV2:       maskingScheduleVisualStatV2,
		MaskingForecastScheduleVisualStat: maskingForecastScheduleVisualStat,
	}
}

type ScheduleVisualStat interface {
	ScheduleResultAnalyze(ctx context.Context, log interface{}, businessId string, businessType int) (map[string]map[string]int64, []string, *srerr.Error)
	SaveScheduleStat(ctx context.Context, statResult map[string]map[string]int64, scheduleDimensionList []string, businessType int, businessId string) *srerr.Error
	SyncScheduleStat(ctx context.Context, businessId string) *srerr.Error
}

var ScheduleVisualMap = make(map[int]ScheduleVisualStat)

func Register(businessType int, scheduleVisual ScheduleVisualStat) {
	ScheduleVisualMap[businessType] = scheduleVisual
}

func GetScheduleVisual(businessType int) ScheduleVisualStat {
	return ScheduleVisualMap[businessType]
}
