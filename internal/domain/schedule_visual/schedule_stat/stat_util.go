package schedule_stat

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/allocation"
	"strconv"
	"strings"
)

func ParseAllocateLog(log *allocation.LogDetail) (map[string]int64, map[string]int64) {
	currentResultMap := make(map[string]int64)
	finalResultMap := make(map[string]int64)
	finalResult := log.FulfillmentProductId
	// 获取调度链路
	scheduleChain := GenerateScheduleChain(log)
	// 总单量统计
	currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", TotalOrder, log.MaskProductId)] = 1
	// 本次调度失败，不记入统计
	if finalResult == 0 {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", Failed, log.MaskProductId)] = 1
		return currentResultMap, finalResultMap
	}
	//1. 统计shop_group的单量，当前版本shop_group无法决出final_result
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", log.ShopGroupId, finalResult)] = 0
	//2. 统计hard criteria
	// 是否进入硬性校验
	if len(log.HardInput) > 0 || len(log.HardOutput) > 0 {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterHardCriteria, log.MaskProductId)] = 1
	}
	for _, currentResult := range log.HardOutput {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, HardFactor, currentResult)] = 1
	}
	if len(log.HardOutput) == 1 && len(log.SoftInput) < 1 {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, HardFactor, finalResult)] = 1
	} else {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, HardFactor, finalResult)] = 0
	}
	//3. 统计soft factor
	// 是否进入软性校验
	if len(log.SoftInput) > 0 || len(log.SoftOutput) > 0 {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterSoftCriteria, log.MaskProductId)] = 1
	}
	for _, currentResult := range log.SoftOutput {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, SoftFactor, currentResult)] = 1
	}
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, SoftFactor, finalResult)] = 0
	//4. 统计soft rule
	for _, currentResult := range log.SoftOutput {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, SoftFactor, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), currentResult)] = 1
	}
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, SoftFactor, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), finalResult)] = 0
	//5. 统计soft criteria
	isEnterDefaultCriteria := false
	if len(log.SoftCriteriaList) > 0 {
		// 获取最后一个调度因子
		idx := len(log.SoftCriteriaList) - 1
		for _, currentResult := range log.SoftOutput {
			currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), fmt.Sprintf(CriteriaFactor, rule.GetMaskStepType(log.SoftCriteriaList[idx].Name), log.SoftCriteriaList[idx].Name), currentResult)] = 1
			if strings.Contains(log.SoftCriteriaList[idx].Name, entity.MaskDefaultProductPriority) || strings.Contains(log.SoftCriteriaList[idx].Name, entity.MaskDefaultProductWeightage) {
				isEnterDefaultCriteria = true
			}
		}
		// 统计是否进入default criteria
		if isEnterDefaultCriteria {
			currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterDefaultCriteria, log.MaskProductId)] = 1
		}
		if len(log.SoftOutput) == 1 {
			finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), fmt.Sprintf(CriteriaFactor, rule.GetMaskStepType(log.SoftCriteriaList[idx].Name), log.SoftCriteriaList[idx].Name), finalResult)] = 1
		} else {
			finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), fmt.Sprintf(CriteriaFactor, rule.GetMaskStepType(log.SoftCriteriaList[idx].Name), log.SoftCriteriaList[idx].Name), finalResult)] = 0
		}
	}

	return currentResultMap, finalResultMap
}

func ParseAllocateLogV2(log *allocation.LogDetail) (map[string]int64, map[string]int64) {
	// 获取调度链路
	scheduleChain := GenerateScheduleChain(log)
	// 1. 订单流向统计
	currentResultMap := make(map[string]int64)
	// 总单量
	currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", TotalOrder, log.MaskProductId)] = 1
	// 流入硬性校验
	if len(log.HardInput) > 0 {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterHardCriteria, log.MaskProductId)] = 1
	}
	for _, product := range log.HardInput {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterHardCriteria, product)] = 1
	}
	// 流入软性校验
	if len(log.SoftInput) > 0 {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterSoftCriteria, log.MaskProductId)] = 1
	}
	for _, product := range log.SoftInput {
		currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterSoftCriteria, product)] = 1
	}
	// 流入默认规则校验
	for _, softCriteria := range log.SoftCriteriaList {
		if strings.Contains(softCriteria.Name, entity.MaskDefaultProductPriority) || strings.Contains(softCriteria.Name, entity.MaskDefaultProductWeightage) {
			currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterDefaultCriteria, log.MaskProductId)] = 1
			for product := range softCriteria.Detail {
				currentResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", EnterDefaultCriteria, product)] = 1
			}
		}
	}
	// 2. 订单结果统计
	finalResultMap := make(map[string]int64)
	finalResult := log.FulfillmentProductId
	// 统计shop_group的单量，当前版本shop_group无法决出final_result
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", log.ShopGroupId, finalResult)] = 0
	// 统计被硬性校验分出的单
	if len(log.SoftInput) < 1 && finalResult != 0 {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, HardFactor, finalResult)] = 1
	} else {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, HardFactor, finalResult)] = 0
	}
	// 统计被软性校验分出的单
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, log.ShopGroupId, SoftFactor, finalResult)] = 0
	finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, SoftFactor, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), finalResult)] = 0
	if len(log.SoftCriteriaList) > 0 {
		// 获取最后一个调度因子
		idx := len(log.SoftCriteriaList) - 1
		if finalResult != 0 {
			finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), fmt.Sprintf(CriteriaFactor, rule.GetMaskStepType(log.SoftCriteriaList[idx].Name), log.SoftCriteriaList[idx].Name), finalResult)] = 1
		} else {
			finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, fmt.Sprintf(SoftCriteriaRule, log.SoftRuleId), fmt.Sprintf(CriteriaFactor, rule.GetMaskStepType(log.SoftCriteriaList[idx].Name), log.SoftCriteriaList[idx].Name), finalResult)] = 0
		}
	}
	// 统计调度失败的单
	if finalResult == 0 {
		finalResultMap[fmt.Sprintf(FieldPattern, scheduleChain, "", Failed, log.MaskProductId)] = 1
	}
	return currentResultMap, finalResultMap
}

// GenerateScheduleChain 返回调用链
func GenerateScheduleChain(log *allocation.LogDetail) string {
	var scheduleChain string
	//1. 调度维度
	scheduleChain = strconv.FormatInt(int64(log.MaskProductId), 10) + ScheduleChainSeparator
	//2. 调度结果
	scheduleChain = scheduleChain + strconv.FormatInt(int64(log.FulfillmentProductId), 10) + ScheduleChainSeparator
	//3. 调度第一级调用因子
	scheduleChain = scheduleChain + strconv.FormatInt(log.ShopGroupId, 10)
	return scheduleChain
}
