package schedule_visual

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ScheduleCountStatInterface interface {
	ScheduleCountStat(ctx context.Context, log interface{}, businessType int, businessId string) *srerr.Error
	SyncStatResult(ctx context.Context, businessType int, businessId string) *srerr.Error
}

type ScheduleCountStat struct {
	ScheduleVisualStat *schedule_stat.ScheduleVisualSet
}

func NewScheduleCountStat(scheduleVisualStat *schedule_stat.ScheduleVisualSet) *ScheduleCountStat {
	return &ScheduleCountStat{
		ScheduleVisualStat: scheduleVisualStat,
	}
}

func (s *ScheduleCountStat) ScheduleCountStat(ctx context.Context, log interface{}, businessType int, businessId string) *srerr.Error {
	//1. 获取schedule visual
	scheduleVisual := schedule_stat.GetScheduleVisual(businessType)
	//2. 获取统计数据
	statResult, scheduleDimensionList, err := scheduleVisual.ScheduleResultAnalyze(ctx, log, businessId, businessType)
	if err != nil {
		return err
	}
	//3. 保存统计数据
	err = scheduleVisual.SaveScheduleStat(ctx, statResult, scheduleDimensionList, businessType, businessId)
	if err != nil {
		logger.CtxLogErrorf(ctx, "SaveScheduleStat fail | err=%v", err)
		return err
	}
	return nil
}

func (s *ScheduleCountStat) SyncStatResult(ctx context.Context, businessType int, businessId string) *srerr.Error {
	//1. 获取schedule visual
	scheduleVisual := schedule_stat.GetScheduleVisual(businessType)
	//2. 同步数据
	return scheduleVisual.SyncScheduleStat(ctx, businessId)
}
