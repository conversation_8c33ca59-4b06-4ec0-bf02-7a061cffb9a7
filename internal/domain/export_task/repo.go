package export_task

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// repo.go is used to communicate with database
type ExportTaskRepo interface {
	GetExportTaskList(ctx context.Context, request schema.GetExportTasksRequest, businessScene string) ([]ExportTaskTab, *srerr.Error)
	CountExportTask(ctx context.Context, businessScene string) (int64, *srerr.Error)
	CreateTaskRecord(ctx context.Context, req masking_panel.CreateExportTaskReq) (uint64, *srerr.Error)
}

type ExportTaskRepoImpl struct {
}

func NewExportTaskRepoImpl() *ExportTaskRepoImpl {
	return &ExportTaskRepoImpl{}
}

func (receiver *ExportTaskRepoImpl) GetExportTaskList(ctx context.Context, request schema.GetExportTasksRequest, businessScene string) ([]ExportTaskTab, *srerr.Error) {
	var results []ExportTaskTab
	queryOption := dbutil.WithPage((request.PageNo-1)*request.PageSize, request.PageSize)
	if err := dbutil.Select(ctx, ExportTaskTabHook, map[string]interface{}{
		"task_business_scene=?": businessScene,
	}, &results, queryOption, dbutil.WithOrder("last_operate_time DESC")); err != nil {
		logger.CtxLogErrorf(ctx, "GetExportTaskList|req:%v, select from db err:%v", request, err)
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return results, nil
}

func (receiver *ExportTaskRepoImpl) CountExportTask(ctx context.Context, businessScene string) (int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, ExportTaskTabHook, map[string]interface{}{
		"task_business_scene=?": businessScene,
	}, &total); err != nil {
		logger.CtxLogErrorf(ctx, "CountExportTask| count from db err:%v", err)
		return total, srerr.With(srerr.DatabaseErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "CountExportTask| count from db, total:%v", total)
	return total, nil
}

func (receiver *ExportTaskRepoImpl) CreateTaskRecord(ctx context.Context, req masking_panel.CreateExportTaskReq) (uint64, *srerr.Error) {
	taskRecord := &ExportTaskTab{
		TaskStatus:        int(req.TaskStatus),
		TaskType:          int(req.TaskType),
		TaskBusinessScene: req.TaskBusinessScene,
		DownloadUrl:       req.DownloadUrl,
		ErrorMessage:      req.ErrorMessage,
		LastOperator:      req.LastOperator,
		LastOperateTime:   req.LastOperateTime,
		Ctime:             req.LastOperateTime,
		Mtime:             req.LastOperateTime,
	}
	if err := dbutil.Insert(ctx, taskRecord, dbutil.ModelInfo{}); err != nil {
		return 0, srerr.With(srerr.CreateTaskRecordFail, nil, err)
	}
	return taskRecord.ID, nil
}
