package export_task

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

//persistent is a reflection of data table

const (
	tableName = "export_task_tab"
)

var ExportTaskTabHook = &ExportTaskTab{}

type ExportTaskTab struct {
	ID                uint64 `gorm:"column:id" json:"id"`
	TaskStatus        int    `gorm:"column:task_status" json:"task_status"`                 //task status,1:created, 2:pending, 3:doing, 4:stopped, 5:success, 6:failed, 7:terminated
	TaskType          int    `gorm:"column:task_type" json:"task_type"`                     //task type,1:download, 2:upload
	TaskBusinessScene string `gorm:"column:task_business_scene" json:"task_business_scene"` //business scene, like "Masking Result Panel"
	LastOperator      string `gorm:"column:last_operator" json:"last_operator"`
	LastOperateTime   int64  `gorm:"column:last_operate_time" json:"last_operate_time"`
	ErrorMessage      string `gorm:"column:error_message" json:"error_message"`
	DownloadUrl       string `gorm:"column:download_url" json:"download_url"`
	Ctime             int64  `gorm:"column:ctime" json:"ctime"`
	Mtime             int64  `gorm:"column:mtime" json:"mtime"`
}

func (t *ExportTaskTab) TableName() string {
	return tableName
}

func (t *ExportTaskTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *ExportTaskTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *ExportTaskTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        t.ID,
		ModelName: t.TableName(),
	}
}
