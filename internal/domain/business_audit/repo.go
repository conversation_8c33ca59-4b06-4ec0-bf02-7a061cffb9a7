package business_audit

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BusinessAuditRepo interface {
	Create(ctx context.Context, tab *BusinessAuditTab) *srerr.Error
	GetTab(ctx context.Context, condition map[string]interface{}) (*BusinessAuditTab, *srerr.Error)
	Update(ctx context.Context, condition, value map[string]interface{}) *srerr.Error
}

type BusinessAuditRepoImpl struct {
}

func NewBusinessAuditRepoImpl() *BusinessAuditRepoImpl {
	return &BusinessAuditRepoImpl{}
}

func (i *BusinessAuditRepoImpl) Create(ctx context.Context, tab *BusinessAuditTab) *srerr.Error {
	if err := dbutil.Insert(ctx, tab, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (i *BusinessAuditRepoImpl) GetTab(ctx context.Context, condition map[string]interface{}) (*BusinessAuditTab, *srerr.Error) {
	tab := BusinessAuditTab{}
	// select找不到不会报错
	if err := dbutil.Select(ctx, BusinessAuditTabHook, condition, &tab); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return &tab, nil
}
func (i *BusinessAuditRepoImpl) Update(ctx context.Context, condition, value map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, BusinessAuditTabHook, condition, value, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}
