package business_audit

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var (
	BusinessAuditTabHook = &BusinessAuditTab{}
)

type BusinessAuditTab struct {
	ID             uint64 `gorm:"id"`
	BusinessType   string `gorm:"business_type"`   //type of business, like allocate, routing and so on
	TicketID       string `gorm:"ticket_id"`       //id of approval center ticket -> 建立索引
	ConfigDbID     uint64 `gorm:"config_db_id"`    //id in db of config which is waiting approval
	ApprovalStatus string `gorm:"approval_status"` //'status of ticket, 1-pending approval, 2-approved, 3-canceled, 4-rejected
	Operator       string `gorm:"operator"`        //operator who change the config
	ExtendInfo     []byte `gorm:"extend_info"`     //extend info used by different business condition
	Ctime          int64  `gorm:"ctime"`
	Mtime          int64  `gorm:"ctime"`
}

func (t *BusinessAuditTab) TableName() string {
	return "business_audit_tab"
}

func (t *BusinessAuditTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *BusinessAuditTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *BusinessAuditTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        t.ID,
		ModelName: t.TableName(),
	}
}
