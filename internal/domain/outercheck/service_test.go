package outercheck

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/common"
	"github.com/agiledragon/gomonkey/v2"
	"reflect"
	"testing"
)

func TestAllOuterCheckServiceImpl_GetProductFromMaskingShopGroup(t *testing.T) {
	ctx := context.TODO()
	impl := &AllOuterCheckServiceImpl{}

	var patch *gomonkey.Patches
	type args struct {
		ctx               context.Context
		maskProductID     int64
		shopGroupIds      []int64
		maskType          int64
		productComponents []int64
	}
	tests := []struct {
		name        string
		args        args
		want        *entity.ProductPriority
		expectedErr *srerr.Error
		setup       func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: shop group id is null",
			args: args{
				ctx:               ctx,
				maskProductID:     29,
				shopGroupIds:      []int64{}, //
				maskType:          1,
				productComponents: []int64{1, 2},
			},
			want:        nil,
			expectedErr: srerr.New(srerr.NilErr, "", "shop group id is null"),
			setup:       func() {},
		},
		{
			name: "case 2: product(%v) is not a masking product",
			args: args{
				ctx:           ctx,
				maskProductID: 29,
				shopGroupIds:  []int64{1, 2},
				maskType:      1,
				//productComponents: []int64{}, //
			},
			want:        nil,
			expectedErr: srerr.New(srerr.ParamErr, nil, "product(%v) is not a masking product", 29),
			setup:       func() {},
		},
		{
			name: "case 3: maskType == constant.MaskingTypeSingle",
			args: args{
				ctx:               ctx,
				maskProductID:     29,
				shopGroupIds:      []int64{1, 2},
				maskType:          constant.MaskingTypeSingle,
				productComponents: []int64{1, 2},
			},
			want: &entity.ProductPriority{
				MaskProductID: 29,
				ShopGroupID:   constant.DefaultShopGroupId,
				Validity:      entity.Enabled,
				ComponentPriorities: []entity.ComponentPriority{
					{
						ProductID: 1,
						Priority:  1,
						Weightage: 1,
						Status:    entity.Open,
					},
				},
			},
			expectedErr: nil,
			setup:       func() {},
		},
		{
			name: "case 4: maskType != constant.MaskingTypeSingle",
			args: args{
				ctx:               ctx,
				maskProductID:     29,
				shopGroupIds:      []int64{1, 2},
				maskType:          constant.MaskingTypeMultiple,
				productComponents: []int64{1, 2},
			},
			want:        nil,
			expectedErr: nil,
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(impl), "GetMaskingMultiProductPriority",
					func(a *AllOuterCheckServiceImpl, ctx context.Context, maskProductID int64, shopGroupIds []int64) (*entity.ProductPriority, *srerr.Error) {
						return nil, nil
					})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, got1 := impl.GetProductFromMaskingShopGroup(ctx, tt.args.maskProductID, tt.args.shopGroupIds, tt.args.maskType, tt.args.productComponents)
			common.AssertResult(t, got, tt.want, got1, tt.expectedErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}

func TestAllOuterCheckServiceImpl_GetMaskingMultiProductPriority(t *testing.T) {
	ctx := context.TODO()
	mockPriorityBusiness := &productpriority.PriorityBusinessImpl{}
	impl := &AllOuterCheckServiceImpl{
		PriorityBusiness: mockPriorityBusiness,
	}

	var patch *gomonkey.Patches
	type args struct {
		maskProductID int64
		shopGroupIds  []int64
	}
	tests := []struct {
		name        string
		args        args
		want        *entity.ProductPriority
		expectedErr *srerr.Error
		setup       func()
	}{
		// TODO: Add test cases.
		{
			name:        "case 1: shop group id is null",
			args:        args{},
			want:        nil,
			expectedErr: srerr.New(srerr.NilErr, "", "shop group id is null"),
			setup:       func() {},
		},
		{
			name: "case 2: could not priority from mask product %d, shop group %d",
			args: args{
				shopGroupIds: []int64{1, 2},
			},
			want:        nil,
			expectedErr: srerr.With(srerr.DatabaseErr, nil, errors.New("mock PriorityBusiness.Detail error")),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockPriorityBusiness), "Detail",
					func(a *productpriority.PriorityBusinessImpl, ctx context.Context, maskProductID int64, shopGroupID int64) (*entity.ProductPriority, *srerr.Error) {
						return nil, srerr.With(srerr.DatabaseErr, nil, errors.New("mock PriorityBusiness.Detail error"))
					})
			},
		},
		{
			name: "case 3: could not priority from mask product %d, shop group %d, use the default shop group",
			args: args{
				shopGroupIds: []int64{1},
			},
			want:        nil,
			expectedErr: srerr.With(srerr.DatabaseErr, nil, errors.New("mock PriorityBusiness.Detail error")),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockPriorityBusiness), "Detail",
					func(a *productpriority.PriorityBusinessImpl, ctx context.Context, maskProductID int64, shopGroupID int64) (*entity.ProductPriority, *srerr.Error) {
						return nil, srerr.With(srerr.DatabaseErr, nil, errors.New("mock PriorityBusiness.Detail error"))
					})
			},
		},
		{
			name: "case 4: No available products under the mask product %d shop group %d",
			args: args{
				shopGroupIds: []int64{1},
			},
			want: nil,
			expectedErr: srerr.New(srerr.NoProductUnderShopsError, nil,
				"No available products under the mask product %d shop group %d ", 0, 1),
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockPriorityBusiness), "Detail",
					func(a *productpriority.PriorityBusinessImpl, ctx context.Context, maskProductID int64, shopGroupID int64) (*entity.ProductPriority, *srerr.Error) {
						return &entity.ProductPriority{}, nil
					})
			},
		},
		{
			name: "case 5: normal result",
			args: args{
				shopGroupIds: []int64{1},
			},
			want: &entity.ProductPriority{
				ComponentPriorities: []entity.ComponentPriority{
					{ProductID: 1, Status: entity.Open, Priority: 1},
				}},
			expectedErr: nil,
			setup: func() {
				patch = gomonkey.ApplyMethod(reflect.TypeOf(mockPriorityBusiness), "Detail",
					func(a *productpriority.PriorityBusinessImpl, ctx context.Context, maskProductID int64, shopGroupID int64) (*entity.ProductPriority, *srerr.Error) {
						return &entity.ProductPriority{
							ComponentPriorities: []entity.ComponentPriority{
								{ProductID: 1, Status: entity.Open, Priority: 1},
							}}, nil
					})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			got, gotErr := impl.GetMaskingMultiProductPriority(ctx, tt.args.maskProductID, tt.args.shopGroupIds)
			common.AssertResult(t, got, tt.want, gotErr, tt.expectedErr)
			if patch != nil {
				patch.Reset()
			}
		})
	}
}
