package outercheck

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/mathutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// AllOuterCheckService 是 ALL 业务模式校验
type AllOuterCheckService interface {
	// GetProductFromMaskingShopGroup get Masking Shop group对应的Product
	GetProductFromMaskingShopGroup(ctx context.Context, maskProductID int64, shopGroupIds []int64, maskType int64, productComponents []int64) (
		*entity.ProductPriority, *srerr.Error)
	GetMaskingMultiProductPriority(ctx context.Context, maskProductID int64, shopGroupIds []int64) (
		*entity.ProductPriority, *srerr.Error)
}

type AllOuterCheckServiceImpl struct {
	PriorityBusiness productpriority.PriorityBusiness
}

func NewAllOuterCheckServiceImpl(priorityBusiness productpriority.PriorityBusiness) *AllOuterCheckServiceImpl {
	return &AllOuterCheckServiceImpl{
		PriorityBusiness: priorityBusiness,
	}
}

// @core
func (a *AllOuterCheckServiceImpl) GetProductFromMaskingShopGroup(ctx context.Context, maskProductID int64, shopGroupIds []int64,
	maskType int64, productComponents []int64) (
	*entity.ProductPriority, *srerr.Error) {
	if len(shopGroupIds) == 0 {
		return nil, srerr.New(srerr.NilErr, "", "shop group id is null")
	}

	if len(productComponents) == 0 {
		return nil, srerr.New(srerr.ParamErr, nil, "product(%v) is not a masking product", maskProductID)
	}

	if maskType == constant.MaskingTypeSingle {
		componentID := productComponents[0]
		return &entity.ProductPriority{
			MaskProductID: maskProductID,
			ShopGroupID:   constant.DefaultShopGroupId,
			Validity:      entity.Enabled,
			ComponentPriorities: []entity.ComponentPriority{
				{
					ProductID: componentID,
					Priority:  1,
					Weightage: 1,
					Status:    entity.Open,
				},
			},
		}, nil
	}

	return a.GetMaskingMultiProductPriority(ctx, maskProductID, shopGroupIds)
}

func (a *AllOuterCheckServiceImpl) GetMaskingMultiProductPriority(ctx context.Context, maskProductID int64, shopGroupIds []int64) (
	*entity.ProductPriority, *srerr.Error) {
	if len(shopGroupIds) == 0 {
		return nil, srerr.New(srerr.NilErr, "", "shop group id is null")
	}

	shopGroupID := constant.DefaultShopGroupId
	shopGroupIds = mathutil.DistinctInt64(shopGroupIds)

	if len(shopGroupIds) == 1 {
		shopGroupID = shopGroupIds[0]
	}
	logger.CtxLogInfof(ctx, "use shop group %d", shopGroupID)
	priority, err := a.PriorityBusiness.Detail(ctx, maskProductID, shopGroupID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "could not get priority from mask product %d, shop group %d, err %v",
			maskProductID, shopGroupID, err)
		if shopGroupID == constant.DefaultShopGroupId {
			return nil, err
		}
		// 如果找不到就用default shop group
		logger.CtxLogInfof(ctx, "could not get priority from mask product %d, shop group %d, "+
			"will use the default shop group", maskProductID, shopGroupID)
		shopGroupID = constant.DefaultShopGroupId
		priority, err = a.PriorityBusiness.Detail(ctx, maskProductID, shopGroupID)
		if err != nil {
			return nil, err
		}
	}

	productIds := make([]int64, 0)
	for _, v := range priority.GetPrioritiesMap() {
		productIds = append(productIds, v.ProductID)
	}
	if len(productIds) == 0 {
		return nil, srerr.New(srerr.NoProductUnderShopsError, nil,
			"No available products under the mask product %d shop group %d ", maskProductID, shopGroupID)
	}

	return priority, err
}
