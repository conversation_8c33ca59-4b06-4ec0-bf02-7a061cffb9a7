package vrservice

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

const (
	ProductRelateSymbol = "|"
)

func (p *ServiceImpl) CheckVolumeRuleCanSaveDraft(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error {
	if !rule.EffectiveImmediately && rule.EffectiveStartTime < timeutil.GetCurrentUnixTimeStamp(ctx) && rule.RoutingType != rule2.CBRoutingType {
		return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "effective start time must be later than current time")
	}

	condition := map[string]interface{}{fmt.Sprintf("(product_id = ? OR JSON_CONTAINS(product_id_list,'%d','$'))", productId): productId}
	rList, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, condition)
	if err != nil || len(rList) == 0 {
		return srerr.New(srerr.ZoneRuleQueryFail, nil, "rule (%d) not found", rule.RuleId)
	}
	for _, r := range rList {
		if r.Id != rule.RuleId && r.RuleStatus == enum.VolumeRuleStatusDraft {
			return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "Duplicated with existing draft(Rule ID: %d), "+
				"please configure the existing draft", r.Id)
		}
	}
	if rule.RuleType == enum.VolumeRuleTypeLineVolumeZone {
		zoneLimits, _ := p.zoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{"rule_id = ?": rule.RuleId,
			"data_version = ?": rule.DataVersion, "zone_name <> ?": ""})
		if len(zoneLimits) == 0 {
			return nil
		}
		lineGroupMap := make(map[string]string)
		groupZoneMap := make(map[string]map[string]bool)
		for _, zoneLimit := range zoneLimits {
			if _, ok := lineGroupMap[zoneLimit.LineId]; !ok {
				groupId, _ := p.zoneGroupRepo.GetGroupIdByLine(ctx, zoneLimit.LineId, rule.RoutingType, rule.IsForecastType)
				if groupId != zoneLimit.GroupId {
					return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "The Zone group %s of Line %s  has been "+
						"changed in Zone Management, please update the zone volume limit", zoneLimit.GroupId, zoneLimit.LineId)
				}
				lineGroupMap[zoneLimit.LineId] = groupId
			}
			if _, ok := groupZoneMap[zoneLimit.GroupId]; !ok {
				groupZoneMap[zoneLimit.GroupId] = make(map[string]bool)
				zoneType, _ := p.zoneGroupRepo.GetZoneTypeByGroupId(ctx, zoneLimit.GroupId, rule.RoutingType, rule.IsForecastType)
				if zoneSet, _ := p.zoneRepo.GetZoneNameListByGroupId(ctx, zoneLimit.GroupId, rule.RoutingType, rule.IsForecastType, zoneType); len(zoneSet) > 0 {
					groupZoneMap[zoneLimit.GroupId] = zoneSet
				}
			}
			if _, ok := groupZoneMap[zoneLimit.GroupId][zoneLimit.ZoneName]; !ok {
				return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "The Zone %s of Zone group %s  has been deleted in "+
					"Zone Management, please update the zone volume limit or create new zone.", zoneLimit.ZoneName, zoneLimit.GroupId)
			}
		}
	}
	return nil
}

func (p *ServiceImpl) CheckAndGetRuleLimitData(ctx context.Context, ruleId uint64, filePath, operator string, benchMarkValue [][]string) ([]persistent.VolumeRoutingRuleDetailTab, []vrentity.CheckZoneLimitInfoMsg, *srerr.Error) {
	//todo	这里没有校验 lineid -binded-with-groupid ,因为允许先编辑好，
	// 这里将原来的方法拆成两步,第一步解析excel，第二步校验数据的正确性并组装成volume rule detail
	rows, fileErr := GerRuleLimitData(ctx, filePath)
	if fileErr != nil {
		return nil, nil, fileErr
	}
	ruleList, qerr := p.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
		"id= ?": ruleId,
	})
	if qerr != nil || len(ruleList) == 0 {
		return nil, nil, srerr.New(srerr.DatabaseErr, nil, "query volume rule err=%v", qerr)
	}
	rule := ruleList[0]
	return p.GetVolumeRoutingRuleDetailByRows(ctx, rule, rows, operator, rule.IsForecastType, benchMarkValue)
}

func checkImportLineRelateInfo(ctx context.Context, benchMarkValue [][]string, rows [][]string) ([]vrentity.CheckZoneLimitInfoMsg, *srerr.Error) {
	// 校验rows中数据是否有缺失
	for _, row := range rows {
		if isSkipRow(row) {
			continue // 如果解析出来是空行则跳过
		}
		if len(row) != len(zoneRuleLimitHeader) {
			return nil, srerr.New(srerr.ParamErr, nil, "import data length not equal header, row len=%v, header len=%v", len(row), len(zoneRuleLimitHeader))
		}
		if row[0] == "" || row[1] == "" || row[2] == "" || row[3] == "" {
			return nil, srerr.New(srerr.ParamErr, nil, "import data have missing, row=%v", row)
		}
	}
	// 如果基准值为空则直接返回
	if benchMarkValue == nil {
		return nil, nil
	}
	// 获取基准值的line关联信息
	benchLineRelateRuleLimitMap, err := generateZoneLimitLineRelateAttr(ctx, benchMarkValue)
	if err != nil {
		return nil, err
	}
	// 获取导入数据的line关联信息
	importLineRelateRuleLimitMap, err := generateZoneLimitLineRelateAttr(ctx, rows)
	// 校验导入的line是否存在
	var checkResultList []vrentity.CheckZoneLimitInfoMsg
	for lineId := range importLineRelateRuleLimitMap {
		if _, ok := benchLineRelateRuleLimitMap[lineId]; !ok {
			return nil, srerr.New(srerr.ParamErr, nil, "import zone limit error, lineId not exist, lineId: %s", lineId)
		}
	}
	// 对比校验
	for lineId := range benchLineRelateRuleLimitMap {
		//1. 判断line是否在导入数据中，不存在则是product missing line
		if _, ok := importLineRelateRuleLimitMap[lineId]; !ok {
			productMap := benchLineRelateRuleLimitMap[lineId]
			var productList []string
			for product := range productMap {
				productList = append(productList, product)
			}
			checkResultList = append(checkResultList, vrentity.CheckZoneLimitInfoMsg{
				ProductId: strings.Join(productList, ","),
				LineId:    lineId,
				Message:   "missing line IDs",
			})
			continue
		}
		//2. line存在时，判断line下的product是否相等
		// 校验导入的product是否存在
		for product := range importLineRelateRuleLimitMap[lineId] {
			if _, ok := benchLineRelateRuleLimitMap[lineId][product]; !ok {
				return nil, srerr.New(srerr.ParamErr, nil, "import zone limit error, product not exist, lineId: %s, product: %s", lineId, product)
			}
		}
		for product := range benchLineRelateRuleLimitMap[lineId] {
			// line下的product不存在时，则属于line missing product
			if _, ok := importLineRelateRuleLimitMap[lineId][product]; !ok {
				checkResultList = append(checkResultList, vrentity.CheckZoneLimitInfoMsg{
					ProductId: product,
					LineId:    lineId,
					Message:   "missing products",
				})
				continue
			}
			// product相等时，判断zone group是否相等
			// 校验导入的zone group是否存在
			for zoneGroup := range importLineRelateRuleLimitMap[lineId][product] {
				if _, ok := benchLineRelateRuleLimitMap[lineId][product][zoneGroup]; !ok {
					return nil, srerr.New(srerr.ParamErr, nil, "import zone limit error, zone group not exist, lineId: %s, product: %s, zoneGroup: %s", lineId, product, zoneGroup)
				}
			}
			for zoneGroup := range benchLineRelateRuleLimitMap[lineId][product] {
				if _, ok := importLineRelateRuleLimitMap[lineId][product][zoneGroup]; !ok {
					checkResultList = append(checkResultList, vrentity.CheckZoneLimitInfoMsg{
						ProductId: product,
						LineId:    lineId,
						ZoneGroup: zoneGroup,
						Message:   "missing zone groups",
					})
					continue
				}
				// zone group相等时，判断zone name是否相等
				// 校验导入的zone name是否存在
				for zoneName := range importLineRelateRuleLimitMap[lineId][product][zoneGroup] {
					if _, ok := benchLineRelateRuleLimitMap[lineId][product][zoneGroup][zoneName]; !ok {
						return nil, srerr.New(srerr.ParamErr, nil, "import zone limit error, zone group not exist, lineId: %s, product: %s, zoneGroup: %s, zoneName: %s", lineId, product, zoneGroup, zoneName)
					}
				}
				for zoneName := range benchLineRelateRuleLimitMap[lineId][product][zoneGroup] {
					if _, ok := importLineRelateRuleLimitMap[lineId][product][zoneGroup][zoneName]; !ok {
						checkResultList = append(checkResultList, vrentity.CheckZoneLimitInfoMsg{
							ProductId: product,
							LineId:    lineId,
							ZoneGroup: zoneGroup,
							ZoneName:  zoneName,
							Message:   "missing zone name",
						})
						continue
					}
				}
			}
		}
	}
	return checkResultList, nil
}

func keyMapToStringList(strMap map[string]struct{}) []string {
	var strList []string
	for key := range strMap {
		strList = append(strList, key)
	}
	return strList
}

func generateZoneLimitLineRelateAttr(ctx context.Context, dataList [][]string) (map[string]map[string]map[string]map[string]struct{}, *srerr.Error) {
	lineRelateRuleLimitMap := make(map[string]map[string]map[string]map[string]struct{})
	for _, data := range dataList {
		if isSkipRow(data) {
			continue // 如果解析出来是空行则跳过
		}
		if len(data) != len(zoneRuleLimitHeader) {
			return nil, srerr.New(srerr.ParamErr, nil, "import data length not equal header, data: %v, header len: %d", data, len(zoneRuleLimitHeader))
		}
		lineInfo := strings.Split(strings.TrimSpace(data[0]), "-")
		lineId := lineInfo[0]
		zoneGroup := data[1]
		zoneName := data[2]
		productIdStr := data[3]
		productIdList := strings.Split(productIdStr, ProductRelateSymbol)
		for _, productId := range productIdList {
			if _, ok := lineRelateRuleLimitMap[lineId]; !ok {
				lineRelateRuleLimitMap[lineId] = make(map[string]map[string]map[string]struct{})
			}
			if _, ok := lineRelateRuleLimitMap[lineId][productId]; !ok {
				lineRelateRuleLimitMap[lineId][productId] = make(map[string]map[string]struct{})
			}
			if _, ok := lineRelateRuleLimitMap[lineId][productId][zoneGroup]; !ok {
				lineRelateRuleLimitMap[lineId][productId][zoneGroup] = make(map[string]struct{})
			}
			lineRelateRuleLimitMap[lineId][productId][zoneGroup][zoneName] = struct{}{}
		}
	}
	return lineRelateRuleLimitMap, nil
}

func (p *ServiceImpl) CheckVolumeRuleCanSubmit(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error {
	// 如果是cb的 Forecast则不需要校验rule的生效时间
	if !rule.EffectiveImmediately && rule.EffectiveStartTime < timeutil.GetCurrentUnixTimeStamp(ctx) && rule.RoutingType != rule2.CBRoutingType {
		return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "effective start time must be later than current time")
	}

	condition := map[string]interface{}{fmt.Sprintf("(product_id = ? OR JSON_CONTAINS(product_id_list,'%d','$'))", productId): productId}
	rList, err := p.zoneRuleRepo.QueryRuleByCondition(ctx, condition)
	if err != nil || len(rList) == 0 {
		return srerr.New(srerr.ZoneRuleQueryFail, nil, "rule (%d) not found", rule.RuleId)
	}
	for _, r := range rList {
		if r.RoutingType != rule2.CBRoutingType && r.Id != rule.RuleId && (r.RuleStatus == enum.VolumeRuleStatusActive || r.RuleStatus == enum.VolumeRuleStatusQueuing) && r.Priority == rule.Priority {
			return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "Same priority active rule exists(rule ID:%d). "+
				"Please rearrange rule's priority if you need", r.Id)
		}
	}
	if rule.RuleType == enum.VolumeRuleTypeLineVolumeZone {
		zoneLimits, _ := p.zoneRuleRepo.QueryRuleDetailByCondition(ctx, map[string]interface{}{"rule_id = ?": rule.RuleId,
			"data_version = ?": rule.DataVersion, "zone_name <> ?": ""})
		if len(zoneLimits) == 0 {
			return nil
		}
		lineGroupMap := make(map[string]string)
		groupZoneMap := make(map[string]map[string]bool)
		for _, zoneLimit := range zoneLimits {
			if _, ok := lineGroupMap[zoneLimit.LineId]; !ok {
				groupId, _ := p.zoneGroupRepo.GetGroupIdByLine(ctx, zoneLimit.LineId, rule.RoutingType, rule.IsForecastType)
				if groupId != zoneLimit.GroupId {
					return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "The Zone group %s of Line %s  has been "+
						"changed in Zone Management, please update the zone volume limit", zoneLimit.GroupId, zoneLimit.LineId)
				}
				lineGroupMap[zoneLimit.LineId] = groupId
			}
			if _, ok := groupZoneMap[zoneLimit.GroupId]; !ok {
				groupZoneMap[zoneLimit.GroupId] = make(map[string]bool)
				zoneType, _ := p.zoneGroupRepo.GetZoneTypeByGroupId(ctx, zoneLimit.GroupId, rule.RoutingType, rule.IsForecastType)
				if zoneSet, _ := p.zoneRepo.GetZoneNameListByGroupId(ctx, zoneLimit.GroupId, rule.RoutingType, rule.IsForecastType, zoneType); len(zoneSet) > 0 {
					groupZoneMap[zoneLimit.GroupId] = zoneSet
				}
			}
			if _, ok := groupZoneMap[zoneLimit.GroupId][zoneLimit.ZoneName]; !ok {
				return srerr.New(srerr.ZoneRuleCheckEditFail, nil, "The Zone %s of Zone group %s  has been deleted in "+
					"Zone Management, please update the zone volume limit or create new zone.", zoneLimit.ZoneName, zoneLimit.GroupId)
			}
		}
	}
	return nil
}

func GerRuleLimitData(ctx context.Context, filePath string) ([][]string, *srerr.Error) {
	timeout := 6
	if configutil.GetVolumeRuleConf(ctx).CheckRuleTimeout != 0 {
		timeout = configutil.GetVolumeRuleConf(ctx).CheckRuleTimeout
	}
	data, rErr := httputil.Get(ctx, filePath, nil, timeout, nil)
	if rErr != nil {
		return nil, srerr.New(srerr.ZoneRuleDetailDownloadFail, nil, "get zone limit file fail, err:%s", rErr.Error())
	}
	rows, _, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(data), true)
	if fErr != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailParseExcelFail, nil, fErr)
	}

	return rows, nil
}

// 校验并放回上传的volume rule detai数据
func (p *ServiceImpl) GetVolumeRoutingRuleDetailByRows(ctx context.Context, volumeRule persistent.VolumeRoutingRuleTab, rows [][]string, operator string, userForecastGroupCheck bool, benchMarkValue [][]string) ([]persistent.VolumeRoutingRuleDetailTab, []vrentity.CheckZoneLimitInfoMsg, *srerr.Error) {
	// 校验导入的数据是否有效，是否有遗漏
	checkResultList, err1 := checkImportLineRelateInfo(ctx, benchMarkValue, rows)
	if err1 != nil {
		return nil, nil, err1
	}
	var list []persistent.VolumeRoutingRuleDetailTab
	groupZoneSet := make(map[string]map[string]bool)
	visitedRecord := make(map[string]struct{})
	for i := 0; i < len(rows); i++ {
		if isSkipRow(rows[i]) {
			continue // 如果解析出来是空行则跳过
		}
		tab := persistent.VolumeRoutingRuleDetailTab{
			RuleId:         volumeRule.Id,
			Operator:       operator,
			Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			Min:            constant.DefaultVolumeRuleLimit,
			Max:            constant.DefaultVolumeRuleLimit,
			MaxCod:         constant.DefaultVolumeRuleLimit,
			MaxBulky:       constant.DefaultVolumeRuleLimit,
			MaxHighValue:   constant.DefaultVolumeRuleLimit,
			MaxDg:          constant.DefaultVolumeRuleLimit,
			RoutingType:    volumeRule.RoutingType,
			IsForecastType: volumeRule.IsForecastType,
		}
		lineInfo := strings.Split(strings.TrimSpace(rows[i][0]), "-")
		tab.LineId = lineInfo[0]
		tab.LineType = int(lfslib.LineSubTypeNameReveseMap[lineInfo[len(lineInfo)-1]])

		caps, pass := ParseVolume([]string{strings.TrimSpace(rows[i][4]), strings.TrimSpace(rows[i][5]), strings.TrimSpace(rows[i][6]),
			strings.TrimSpace(rows[i][7]), strings.TrimSpace(rows[i][8]), strings.TrimSpace(rows[i][9])})
		if !pass {
			return nil, nil, srerr.New(srerr.ParamErr, nil, "rows format err or capacity < 0")
		}
		tab.Min, tab.Max, tab.MaxCod, tab.MaxBulky, tab.MaxHighValue, tab.MaxDg = caps[0], caps[1], caps[2], caps[3], caps[4], caps[5]
		//if tab.Min > tab.Max {
		//	return nil, srerr.New(srerr.ParamErr, nil, "row: %d, min daily limit(%d) exceed max daily limit(%d)", i+1, tab.Min, tab.Max)
		//}
		// 下面是校验逻辑，如果是local Forecast的导入用的group是线上的，cb的有自己专门配置的group
		groupId := strings.TrimSpace(rows[i][1])
		zoneName := strings.TrimSpace(rows[i][2])
		if _, ok := groupZoneSet[groupId]; !ok {
			group, gErr := p.zoneGroupRepo.GetGroupBaseByGroupId(ctx, groupId, volumeRule.RoutingType, userForecastGroupCheck)
			if gErr != nil {
				return nil, nil, srerr.New(srerr.ParamErr, nil, "row: %d, group not found: %s", i+2, groupId)
			}
			groupZoneSet[groupId] = make(map[string]bool)
			zoneSet, zErr := p.zoneRepo.GetZoneNameListByGroupId(ctx, group.GroupId, volumeRule.RoutingType, userForecastGroupCheck, group.ZoneType)
			if zErr != nil {
				logger.CtxLogErrorf(ctx, "get zone set fail, group:%s, zoneType:%v, err:%+v", groupId, enum.ZoneTypeNameMap[group.ZoneType], zErr)
			}
			if len(zoneSet) > 0 {
				groupZoneSet[groupId] = zoneSet
			}
		}
		_, ok := groupZoneSet[groupId][zoneName]
		if !ok {
			return nil, nil, srerr.New(srerr.ParamErr, nil, "row: %d, zone: %s not in this group: %s", i+2, zoneName, groupId)
		}
		// 填充product
		productIdStrs := rows[i][3]
		productIdStrList := strings.Split(productIdStrs, ProductRelateSymbol)
		var productIdList []int64
		for _, productIdStr := range productIdStrList {
			productId, err := strconv.ParseInt(productIdStr, 10, 64)
			if err != nil {
				return nil, nil, srerr.New(srerr.ParamErr, nil, "parse productId error, err=%v, productId=%v", err, productIdStr)
			}
			productIdList = append(productIdList, productId)
		}
		tab.ProductIdList = productIdList
		// 去重，多个product要拆开与单个product做去重判断
		for _, productId := range productIdList {
			visitedKey := objutil.Join("-", tab.LineId, strconv.FormatInt(productId, 10), groupId, zoneName)
			if _, ok1 := visitedRecord[visitedKey]; ok1 {
				return nil, nil, srerr.New(srerr.ParamErr, nil, "row: %d, rule limit duplicate for line:%s, productId:%d, group: %s, zone: %s",
					i+1, tab.LineId, productId, groupId, zoneName)
			}
			visitedRecord[visitedKey] = struct{}{}
		}
		tab.GroupId = groupId
		tab.ZoneName = zoneName
		list = append(list, tab)
	}
	return list, checkResultList, nil
}

func ParseVolume(volumes []string) ([]int64, bool) {
	caps := make([]int64, 0, len(volumes))
	for _, volume := range volumes {
		var capacity int64
		if volume == "" {
			capacity = constant.DefaultVolumeRuleLimit
		} else {
			var err error
			capacity, err = strconv.ParseInt(volume, 10, 64)
			if err != nil || capacity < 0 {
				return nil, false
			}
		}
		caps = append(caps, capacity)
	}
	return caps, true
}

// 校验并放回上传的volume rule detai数据
func (p *ServiceImpl) GetVolumeRoutingRuleDetailByRowsForForecastTask(ctx context.Context, volumeRule persistent.VolumeRoutingRuleTab, rows [][]string, operator string, userForecastGroupCheck bool) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error) {
	var list []persistent.VolumeRoutingRuleDetailTab
	groupZoneSet := make(map[string]map[string]bool)
	visitedRecord := make(map[string]struct{})
	for i := 0; i < len(rows); i++ {
		if isSkipRow(rows[i]) {
			continue // 如果解析出来是空行则跳过
		}
		tab := persistent.VolumeRoutingRuleDetailTab{
			RuleId:         volumeRule.Id,
			Operator:       operator,
			Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
			Min:            constant.DefaultVolumeRuleLimit,
			Max:            constant.DefaultVolumeRuleLimit,
			MaxCod:         constant.DefaultVolumeRuleLimit,
			MaxBulky:       constant.DefaultVolumeRuleLimit,
			MaxHighValue:   constant.DefaultVolumeRuleLimit,
			MaxDg:          constant.DefaultVolumeRuleLimit,
			RoutingType:    volumeRule.RoutingType,
			IsForecastType: volumeRule.IsForecastType,
		}
		lineInfo := strings.Split(strings.TrimSpace(rows[i][0]), "-")
		tab.LineId = lineInfo[0]
		tab.LineType = int(lfslib.LineSubTypeNameReveseMap[lineInfo[len(lineInfo)-1]])
		caps, pass := ParseVolume([]string{strings.TrimSpace(rows[i][4]), strings.TrimSpace(rows[i][5]), strings.TrimSpace(rows[i][6]),
			strings.TrimSpace(rows[i][7]), strings.TrimSpace(rows[i][8]), strings.TrimSpace(rows[i][9])})
		if !pass {
			return nil, srerr.New(srerr.ParamErr, nil, "rows format err or capacity < 0")
		}
		tab.Min, tab.Max, tab.MaxCod, tab.MaxBulky, tab.MaxHighValue, tab.MaxDg = caps[0], caps[1], caps[2], caps[3], caps[4], caps[5]
		//if tab.Min > tab.Max {
		//	return nil, srerr.New(srerr.ParamErr, nil, "row: %d, min daily limit(%d) exceed max daily limit(%d)", i+1, tab.Min, tab.Max)
		//}
		// 下面是校验逻辑，如果是local Forecast的导入用的group是线上的，cb的有自己专门配置的group
		groupId := strings.TrimSpace(rows[i][1])
		zoneName := strings.TrimSpace(rows[i][2])
		if _, ok := groupZoneSet[groupId]; !ok {
			group, gErr := p.zoneGroupRepo.GetGroupBaseByGroupId(ctx, groupId, volumeRule.RoutingType, userForecastGroupCheck)
			if gErr != nil {
				return nil, srerr.New(srerr.ParamErr, nil, "row: %d, group not found: %s", i+2, groupId)
			}
			groupZoneSet[groupId] = make(map[string]bool)
			zoneSet, zErr := p.zoneRepo.GetZoneNameListByGroupId(ctx, group.GroupId, volumeRule.RoutingType, userForecastGroupCheck, group.ZoneType)
			if zErr != nil {
				logger.CtxLogErrorf(ctx, "get zone set fail, group:%s, zoneType:%v, err:%+v", groupId, enum.ZoneTypeNameMap[group.ZoneType], zErr)
			}
			if len(zoneSet) > 0 {
				groupZoneSet[groupId] = zoneSet
			}
		}
		_, ok := groupZoneSet[groupId][zoneName]
		if !ok {
			return nil, srerr.New(srerr.ParamErr, nil, "row: %d, zone: %s not in this group: %s", i+2, zoneName, groupId)
		}
		visitedKey := objutil.Join("-", tab.LineId, groupId, zoneName)
		if _, ok := visitedRecord[visitedKey]; ok {
			return nil, srerr.New(srerr.ParamErr, nil, "row: %d, rule limit duplicate for line:%s, group: %s, zone: %s",
				i+1, tab.LineId, groupId, zoneName)
		}
		visitedRecord[visitedKey] = struct{}{}
		tab.GroupId = groupId
		tab.ZoneName = zoneName
		list = append(list, tab)
	}
	return list, nil
}

// 这个接口是获取local Forecast上传的zone约束数据
func (p *ServiceImpl) CheckAndGetRuleLimitDataForLocalForecast(ctx context.Context, ruleId uint64, filePath, operator string) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error) {
	rows, fileErr := GerRuleLimitData(ctx, filePath)
	if fileErr != nil {
		return nil, fileErr
	}
	ruleList, qerr := p.zoneRuleRepo.QueryRuleByCondition(ctx, map[string]interface{}{
		"id= ?": ruleId,
	})
	if qerr != nil || len(ruleList) == 0 {
		return nil, srerr.New(srerr.DatabaseErr, nil, "query volume rule err=%v", qerr)
	}
	rule := ruleList[0]
	rule.IsForecastType = constant.ForecastType
	const userForecastGroupCheck = false // 这个参数的含义是 是否用Forecast配置的group校验，local Forecast用的是和线上一样的校验
	ruleDetailList, err := p.GetVolumeRoutingRuleDetailByRowsForForecastTask(ctx, rule, rows, operator, userForecastGroupCheck)
	return ruleDetailList, err
}
