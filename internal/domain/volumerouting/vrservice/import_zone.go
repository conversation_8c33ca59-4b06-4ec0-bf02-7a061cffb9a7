package vrservice

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"strconv"
	"strings"
)

const (
	importActionInsert = 1
	importActionRemove = -1
)

var (
	locationZoneImportResultHeader = []string{"*Action mode (1=add;-1=delete)", "*Zone Name", "*Region", "*State", "City", "District", "Street", "Result"}
	postcodeZoneImportResultHeader = []string{"*Action mode (1=add;-1=delete)", "*Zone Name", "*Region", "*Postcode", "Result"}
	cepRangeZoneImportResultHeader = []string{"*Action mode (1=add;-1=delete)", "*Zone Name", "*Region", "*CEP Initial", "*CEP Final", "Result"}
)

func (p *ServiceImpl) ImportZoneByTask(ctx context.Context, task *persistent.VolumeTaskRecordTab, param schema.ZoneImportParam) ([][]string, *srerr.Error) {
	queueStatusRuleIds, err := checkCanImport(ctx, param)
	if err != nil || len(queueStatusRuleIds) != 0 {
		return nil, srerr.New(srerr.ZoneImportGetFileFail, param.ZoneGroupId, fmt.Sprintf("GroupId check can import err=%v or The volume rule status is queue with id=%+v", err, queueStatusRuleIds))
	}
	if task.ZoneGroupId == "" {
		logger.CtxLogErrorf(ctx, "handle zone import fail, zone group not set")
		return nil, srerr.New(srerr.ZoneImportEmptyGroupId, nil, "empty group id")
	}
	if task.TaskStatus != enum.TaskStatusInProcess {
		logger.CtxLogErrorf(ctx, "handle zone import fail, invalid task status:%s", enum.TaskStatusNameMap[task.TaskStatus])
		return nil, srerr.New(srerr.ZoneImportInvalidTaskStatus, nil, "invalid task status:%s", enum.TaskStatusNameMap[task.TaskStatus])
	}
	resp, gErr := httputil.GetWithUrl(ctx, param.FileUrl)
	if gErr != nil || resp.Body == nil {
		logger.CtxLogErrorf(ctx, "handle zone import fail, get import excel fail, error:%+v", gErr)
		return nil, srerr.New(srerr.ZoneImportGetFileFail, nil, "get zone data file fail, err:%+v", gErr)
	}
	data, _, fErr := fileutil.ParseExcel(ctx, resp.Body, true)
	if fErr != nil {
		logger.CtxLogErrorf(ctx, "handle zone import fail, parse excel fail, error:%+v", fErr)
		return nil, srerr.With(srerr.ZoneImportParseExcelFail, nil, fErr)
	}
	switch param.ZoneType {
	case enum.ZoneTypeLocation:
		return p.handleLocZoneImport(ctx, task, data, param)
	case enum.ZoneTypePostcode:
		return p.handlePostcodeZoneImport(ctx, task, data, param)
	default:
		return p.handleCepRangeZoneImport(ctx, task, data, param)
	}
}

const defaultLevel = -1

func (p *ServiceImpl) handleLocZoneImport(ctx context.Context, task *persistent.VolumeTaskRecordTab, data [][]string, param schema.ZoneImportParam) ([][]string, *srerr.Error) {
	tree, tErr := p.buildLocZoneTree(ctx, task.ZoneGroupId, param.RoutingType, param.IsForecastType)
	if tErr != nil {
		logger.CtxLogErrorf(ctx, "handle loc zone import fail, build loc zone tree fail, error:%+v", tErr)
		return nil, srerr.With(srerr.ZoneImportCheckFail, nil, tErr)
	}
	var removeList, insertList []*vrentity.LocZoneImportInfo
	errRecorder := make(map[int]string)
	level := defaultLevel
	for i := 0; i < len(data); i++ {
		if isSkipRow(data[i]) {
			continue
		}
		info, err := p.parseLocZoneRow(ctx, i, data[i])
		if err != nil {
			errRecorder[i] = err.Error()
			continue
		}
		if level == defaultLevel {
			level = info.Level
		}
		// 判断是否是同级别上传
		if param.IsSameLevel && level != info.Level {
			errRecorder[i] = srerr.New(srerr.SameLevelLocationErr, nil, "\"Same level upload\" entry not support different level location").Error()
			continue
		}
		if info.ActionMode == importActionInsert {
			insertList = append(insertList, info)
			continue
		}
		removeList = append(removeList, info)
	}
	for _, removeItem := range removeList {
		if err := tree.Remove(removeItem); err != nil {
			errRecorder[removeItem.Row] = err.Error()
		}
	}
	for _, insertItem := range insertList {
		if err := tree.Insert(insertItem); err != nil {
			errRecorder[insertItem.Row] = err.Error()
		}
	}
	for i := 0; i < len(data); i++ {
		reason, ok := errRecorder[i]
		if ok {
			data[i] = append(data[i], reason)
			continue
		}
		data[i] = append(data[i], "SUCCESS")
	}
	if len(errRecorder) > 0 {
		return data, srerr.New(srerr.ZoneImportCheckFail, nil, "contains invalid row for import file, errorNum:%s", getFirstError(errRecorder))
	}
	if err := p.zoneRepo.CoverLocationZoneByGroupId(ctx, task.ZoneGroupId, tree.Format(ctx, task.Operator, param.RoutingType, param.IsForecastType), param.RoutingType, param.IsForecastType); err != nil {
		logger.CtxLogErrorf(ctx, "handle loc zone import fail, cover location zone fail, error:%+v", err)
		return data, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return data, nil
}

func (p *ServiceImpl) buildLocZoneTree(ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.LocationZoneTree, *srerr.Error) {
	list, err := p.zoneRepo.GetLocationZoneList(ctx, map[string]interface{}{constant.GroupIdSql: groupId, constant.RoutingTypeSql: routingType, constant.IsForecastTypeSql: isForecastType})
	if err != nil {
		return nil, err
	}
	tree := vrentity.LocationZoneTree{
		GroupId:      groupId,
		LocSet:       make(map[int64]*vrentity.LocZoneImportInfo),
		ZoneLocMap:   make(map[string]map[int64]struct{}),
		ParentLocSet: make(map[int64]struct{}),
	}
	for _, data := range list {
		info := vrentity.LocZoneImportInfo{
			ZoneName: data.ZoneName,
			LocId:    data.LocationId,
			State:    data.State,
			City:     data.City,
			District: data.District,
			Street:   data.Street,
		}
		if loc, _ := p.addrRepo.GetLocationByLocId(ctx, data.LocationId); loc != nil {
			_, parentIds := loc.GetLocAndParenIds()
			info.ParentLocIds = parentIds
			info.State = loc.GetState()
			info.City = loc.GetCity()
			info.District = loc.GetDistrict()
			info.Street = loc.GetStreet()
		}
		if err := tree.Insert(&info); err != nil {
			logger.CtxLogErrorf(ctx, "import zone location, insert loc tree fail, data:%s, err:%+v", objutil.JsonString(data), err)
		}
	}
	return &tree, nil
}

// the location zone import template, can refer below
// ---------------------------------------------------------------------
// | Action Mode | Zone Name | Region | State | City | District | Street |
// ---------------------------------------------------------------------
// |      1      |   A1      |  BR    |       |      |          |        |
// ---------------------------------------------------------------------
func (p *ServiceImpl) parseLocZoneRow(ctx context.Context, rowIndex int, data []string) (*vrentity.LocZoneImportInfo, *srerr.Error) {
	if len(data) < 4 {
		return nil, srerr.New(srerr.FormatErr, nil, "action mode, zone name, region, state must be set")
	}
	actionMode, pErr := strconv.Atoi(strings.TrimSpace(data[0]))
	if pErr != nil || (actionMode != importActionRemove && actionMode != importActionInsert) {
		return nil, srerr.New(srerr.FormatErr, nil, "unknown action mode")
	}
	zoneName := strings.TrimSpace(data[1])
	fixedlen := len(data)
	for i := 0; i < 7-fixedlen; i++ {
		data = append(data, "")
	}
	country := strings.TrimSpace(data[2])
	state := strings.TrimSpace(data[3])
	city := strings.TrimSpace(data[4])
	district := strings.TrimSpace(data[5])
	street := strings.TrimSpace(data[6])
	loc, err := p.addrRepo.GetLocationByLocName(ctx, country, state, city, district, street)
	if err != nil {
		return nil, srerr.New(srerr.LocationNotFound, nil, "location not found, "+
			"(state:%s,city:%s,district:%s,street:%s)", state, city, district, street)
	}

	locId, parentIds := loc.GetLocAndParenIds()
	return &vrentity.LocZoneImportInfo{
		ActionMode:   actionMode,
		Row:          rowIndex,
		ZoneName:     zoneName,
		LocId:        locId,
		ParentLocIds: parentIds,
		State:        loc.GetState(),
		City:         loc.GetCity(),
		District:     loc.GetDistrict(),
		Street:       loc.GetStreet(),
		Level:        loc.GetLevel(),
	}, nil
}

func (p *ServiceImpl) handlePostcodeZoneImport(ctx context.Context, task *persistent.VolumeTaskRecordTab, data [][]string, param schema.ZoneImportParam) ([][]string, *srerr.Error) {
	tabs, err := p.zoneRepo.GetPostcodeZoneList(ctx, map[string]interface{}{constant.GroupIdSql: task.ZoneGroupId, constant.RoutingTypeSql: param.RoutingType, constant.IsForecastTypeSql: param.IsForecastType})
	if err != nil {
		return nil, srerr.Wrap(err, nil, srerr.ZoneImportCheckFail)
	}
	tree := vrentity.PostcodeZoneTree{
		GroupId:         task.ZoneGroupId,
		PostcodeSet:     make(map[string]*vrentity.PostcodeZoneImportInfo),
		ZonePostcodeMap: make(map[string]map[string]struct{}),
	}
	for _, tab := range tabs {
		if err := tree.Insert(&vrentity.PostcodeZoneImportInfo{
			ZoneName: tab.ZoneName,
			Postcode: tab.Postcode,
		}); err != nil {
			logger.CtxLogErrorf(ctx, "build postcode zone tree error, data:%s, err:%+v", objutil.JsonString(tab), err)
		}
	}
	var removeList, insertList []*vrentity.PostcodeZoneImportInfo
	errRecorder := make(map[int]string)
	for i := 0; i < len(data); i++ {
		info, err := p.parsePostcodeZoneRow(ctx, i, data[i])
		if err != nil {
			errRecorder[i] = err.Error()
			continue
		}
		if info.ActionMode == importActionInsert {
			insertList = append(insertList, info)
			continue
		}
		removeList = append(removeList, info)
	}
	for _, removeItem := range removeList {
		if err := tree.Remove(removeItem); err != nil {
			errRecorder[removeItem.Row] = err.Error()
		}
	}
	for _, insertItem := range insertList {
		if err := tree.Insert(insertItem); err != nil {
			errRecorder[insertItem.Row] = err.Error()
		}
	}
	for i := 0; i < len(data); i++ {
		reason, ok := errRecorder[i]
		if ok {
			data[i] = append(data[i], reason)
			continue
		}
		data[i] = append(data[i], "SUCCESS")
	}
	if len(errRecorder) > 0 {
		return data, srerr.New(srerr.ZoneImportCheckFail, nil, "contains invalid row for import file, errorNum:%d", len(errRecorder))
	}
	if err := p.zoneRepo.CoverPostcodeZoneByGroupId(ctx, task.ZoneGroupId, tree.Format(ctx, task.Operator, param.RoutingType, param.IsForecastType), param.RoutingType, param.IsForecastType); err != nil {
		logger.CtxLogErrorf(ctx, "handle postcode zone import fail, cover postcode zone fail, error:%+v", err)
		return data, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return data, nil
}

// the postcode zone import template, can refer below
// -----------------------------------------------
// | Action Mode | Zone Name | Region | Postcode |
// -----------------------------------------------
// |      1      |   A1      |  BR    |          |
// -----------------------------------------------
func (p *ServiceImpl) parsePostcodeZoneRow(ctx context.Context, rowIndex int, data []string) (*vrentity.PostcodeZoneImportInfo, *srerr.Error) {
	if len(data) < 4 {
		return nil, srerr.New(srerr.FormatErr, nil, "action mode, zone name, region, postcode must be set")
	}
	actionMode, pErr := strconv.Atoi(strings.TrimSpace(data[0]))
	if pErr != nil || (actionMode != importActionRemove && actionMode != importActionInsert) {
		return nil, srerr.New(srerr.FormatErr, nil, "unknown action mode")
	}
	zoneName := strings.TrimSpace(data[1])
	postcode := strings.TrimSpace(data[3])
	if !objutil.IsPureDigit(postcode) {
		return nil, srerr.New(srerr.FormatErr, nil, "postcode: (%s) is empty or format error", postcode)
	}
	return &vrentity.PostcodeZoneImportInfo{
		ActionMode: actionMode,
		Row:        rowIndex,
		ZoneName:   zoneName,
		Postcode:   postcode,
	}, nil
}

func (p *ServiceImpl) handleCepRangeZoneImport(ctx context.Context, task *persistent.VolumeTaskRecordTab, data [][]string, param schema.ZoneImportParam) ([][]string, *srerr.Error) {
	tabs, err := p.zoneRepo.GetCepRangeZoneList(ctx, map[string]interface{}{constant.GroupIdSql: task.ZoneGroupId, constant.RoutingTypeSql: param.RoutingType, constant.IsForecastTypeSql: param.IsForecastType})
	if err != nil {
		return nil, srerr.Wrap(err, nil, srerr.ZoneImportCheckFail)
	}
	cepRangeSet := make(map[int64]*vrentity.CepRangeZoneImportInfo)
	sortedCepInitialList := make([]int64, 0)
	zoneCepRangeMap := make(map[string]map[int64]struct{})
	for _, tab := range tabs {
		cepRangeSet[tab.CepInitial] = &vrentity.CepRangeZoneImportInfo{
			ZoneName:   tab.ZoneName,
			CepInitial: tab.CepInitial,
			CepFinal:   tab.CepFinal,
		}
		sortedCepInitialList = append(sortedCepInitialList, tab.CepInitial)
		if _, ok := zoneCepRangeMap[tab.ZoneName]; !ok {
			zoneCepRangeMap[tab.ZoneName] = make(map[int64]struct{})
		}
		zoneCepRangeMap[tab.ZoneName][tab.CepInitial] = struct{}{}
	}
	objutil.SortInt64(sortedCepInitialList)
	tree := vrentity.CepRangeZoneTree{
		GroupId:           task.ZoneGroupId,
		CepRangeSet:       cepRangeSet,
		SortedInitialList: &sortedCepInitialList,
		ZoneCepRangeMap:   zoneCepRangeMap,
	}
	var removeList, insertList []*vrentity.CepRangeZoneImportInfo
	errRecorder := make(map[int]string)
	for i := 0; i < len(data); i++ {
		info, err := p.parseCepRangeRow(ctx, i, data[i])
		if err != nil {
			errRecorder[i] = err.Error()
			continue
		}
		if info.ActionMode == importActionInsert {
			insertList = append(insertList, info)
			continue
		}
		removeList = append(removeList, info)
	}
	for _, removeItem := range removeList {
		if err := tree.Remove(removeItem); err != nil {
			errRecorder[removeItem.Row] = err.Error()
		}
	}
	for _, insertItem := range insertList {
		if err := tree.Insert(insertItem); err != nil {
			errRecorder[insertItem.Row] = err.Error()
		}
	}
	for i := 0; i < len(data); i++ {
		reason, ok := errRecorder[i]
		if ok {
			data[i] = append(data[i], reason)
			continue
		}
		data[i] = append(data[i], "SUCCESS")
	}
	if len(errRecorder) > 0 {
		return data, srerr.New(srerr.ZoneImportCheckFail, nil, "contains invalid row for import file, errorNum:%d", len(errRecorder))
	}
	if err := p.zoneRepo.CoverCepRangeZoneByGroupId(ctx, task.ZoneGroupId, tree.Format(ctx, task.Operator, param.RoutingType, param.IsForecastType), param.RoutingType, param.IsForecastType); err != nil {
		logger.CtxLogErrorf(ctx, "handle postcode zone import fail, cover postcode zone fail, error:%+v", err)
		return data, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return data, nil
}

// the cep range zone import template, can refer below
// ----------------------------------------------------------------
// | Action Mode | Zone Name | Region | *CEP Initial | *CEP Final |
// ----------------------------------------------------------------
// |      1      |   A1      |  BR    |    1000000   |   5999999  |
// ----------------------------------------------------------------
func (p *ServiceImpl) parseCepRangeRow(ctx context.Context, rowIndex int, data []string) (*vrentity.CepRangeZoneImportInfo, *srerr.Error) {
	if len(data) < 5 {
		return nil, srerr.New(srerr.FormatErr, nil, "action mode, zone name, region, cep_initial cep_final must be set")
	}
	actionMode, pErr := strconv.Atoi(strings.TrimSpace(data[0]))
	if pErr != nil || (actionMode != importActionRemove && actionMode != importActionInsert) {
		return nil, srerr.New(srerr.FormatErr, nil, "unknown action mode")
	}
	zoneName := strings.TrimSpace(data[1])
	cepInitStr := strings.TrimSpace(data[3])
	cepInitial, pErr := strconv.ParseInt(cepInitStr, 10, 64)
	if pErr != nil || !objutil.IsPureDigit(cepInitStr) {
		return nil, srerr.New(srerr.FormatErr, nil, "cep initial format error, value:%s", data[3])
	}
	cepFinStr := strings.TrimSpace(data[4])
	cepFinal, pErr := strconv.ParseInt(cepFinStr, 10, 64)
	if pErr != nil || !objutil.IsPureDigit(cepFinStr) {
		return nil, srerr.New(srerr.FormatErr, nil, "cep final format error, value:%s", data[4])
	}
	if cepInitial >= cepFinal {
		return nil, srerr.New(srerr.FormatErr, nil, "cep final must be more than cep initial, "+
			"initial:%d, final:%d", cepInitial, cepFinal)
	}
	return &vrentity.CepRangeZoneImportInfo{
		ActionMode: actionMode,
		Row:        rowIndex,
		ZoneName:   zoneName,
		CepInitial: cepInitial,
		CepFinal:   cepFinal,
	}, nil
}

func (p *ServiceImpl) UploadResultFile(ctx context.Context, zoneType enum.ZoneType, s3Key string, rows [][]string) (string, *srerr.Error) {
	var header []string
	switch zoneType {
	case enum.ZoneTypeLocation:
		header = locationZoneImportResultHeader
	case enum.ZoneTypePostcode:
		header = postcodeZoneImportResultHeader
	default:
		header = cepRangeZoneImportResultHeader
	}
	emptySheetName := ""
	f, fErr := fileutil.MakeExcel(ctx, header, rows, emptySheetName)
	if fErr != nil {
		return "", srerr.With(srerr.ZoneImportUploadFail, nil, fErr)
	}
	b, wErr := f.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.ZoneImportUploadFail, nil, fErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return objutil.Join("*@*", bucket, s3Key), nil
}

// 如果某一行全是空格则跳过
func isSkipRow(data []string) bool {
	for i := 0; i < len(data); i++ {
		if data[i] != "" {
			return false
		}
	}
	return true
}

func checkCanImport(ctx context.Context, param schema.ZoneImportParam) ([]int64, error) {
	if !param.IsForecastType {
		return nil, nil
	}
	db, err := dbutil.SlaveDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport get db err=%v", err)
		return nil, err
	}
	var ruleIdList []int64
	if err := db.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).
		Where(constant.VolumeGroupSqlTemplate, param.ZoneGroupId, param.RoutingType, constant.NonForecastType).
		Select("distinct(rule_id)").Find(&ruleIdList).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport query VolumeRoutingRuleDetail err=%v", err)
		return nil, err
	}
	var queueStatusRuleId []int64
	if err := db.Table(persistent.VolumeRoutingRuleHook.TableName()).
		Where("id in (?) and rule_status = ?", ruleIdList, enum.VolumeRuleStatusQueuing).
		Select("id").Find(&queueStatusRuleId).GetError(); err != nil {
		logger.CtxLogErrorf(ctx, "checkCanImport query VolumeRoutingRuleHook err=%v", err)
		return nil, err
	}
	return queueStatusRuleId, nil
}

func getFirstError(errMap map[int]string) string {
	keyList := make([]int, 0, len(errMap))
	for idx := range errMap {
		keyList = append(keyList, idx)
	}
	sort.Ints(keyList)
	firstErr := keyList[0]
	if err, ok := errMap[firstErr]; ok {
		return err
	}
	return ""
}
