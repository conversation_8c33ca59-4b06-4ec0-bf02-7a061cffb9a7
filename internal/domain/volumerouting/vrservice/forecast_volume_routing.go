package vrservice

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"sync"
)

const forecastKey = "forecast_key"

type ForecastVolumeRouting struct {
	mu            sync.RWMutex
	volumeCounter map[string]int64
}

func NewForecastVolumeRouting() *ForecastVolumeRouting {
	return &ForecastVolumeRouting{volumeCounter: make(map[string]int64)}
}

func (s *ForecastVolumeRouting) ExportZoneListByTask(ctx context.Context, zoneType enum.ZoneType, condition map[string]interface{}, s3Key string) (string, *srerr.Error) {
	return "", nil
}
func (s *ForecastVolumeRouting) ImportZoneByTask(ctx context.Context, task *persistent.VolumeTaskRecordTab, param schema.ZoneImportParam) ([][]string, *srerr.Error) {
	return nil, nil
}
func (s *ForecastVolumeRouting) UploadResultFile(ctx context.Context, zoneType enum.ZoneType, s3Key string, rows [][]string) (string, *srerr.Error) {
	return "", nil
}
func (s *ForecastVolumeRouting) CheckVolumeRuleCanSaveDraft(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) CheckForecastVolumeCanSave(ctx context.Context, rule *vrentity.VolumeRule) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) CheckAndGetRuleLimitData(ctx context.Context, ruleId uint64, filePath, operator string, benchMarkValue [][]string) ([]persistent.VolumeRoutingRuleDetailTab, []vrentity.CheckZoneLimitInfoMsg, *srerr.Error) {
	return nil, nil, nil
}
func (s *ForecastVolumeRouting) CheckVolumeRuleCanSubmit(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) ExportRuleDetails(ctx context.Context, tabs []persistent.VolumeRoutingRuleDetailTab) (*bytes.Buffer, *srerr.Error) {
	return nil, nil
}
func (s *ForecastVolumeRouting) ImportRuleDetails(ctx context.Context, ruleId uint64, filePath string, operator string) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) CurrentVolumeLineDimension(ctx context.Context, productId int64, lineId, date, parcelType string) (int64, *srerr.Error) {
	return s.readVolume(lineForecastKey(productId, lineId, date, parcelType)), nil
}
func (s *ForecastVolumeRouting) CurrentVolumeZoneDimension(ctx context.Context, productId int64, lineId, groupId, zoneName, date, parcelType string) (int64, *srerr.Error) {
	return s.readVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, parcelType)), nil
}
func (s *ForecastVolumeRouting) IncreaseVolumeLineDimension(ctx context.Context, forderId string) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) IncreaseVolumeZoneDimension(ctx context.Context, forderId string) *srerr.Error {
	return nil
}
func (s *ForecastVolumeRouting) IncrVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr) *srerr.Error {
	for _, lineId := range lineIdList {
		parcelTypeAttr, exist := lineParcelMap[lineId]
		if !exist {
			logger.CtxLogErrorf(ctx, "line_id=%v not exist in lineParcelMap", lineId)
		}
		s.incrVolume(lineForecastKey(productId, lineId, date, ""))
		if parcelTypeAttr.GetIsCod() {
			s.incrVolume(lineForecastKey(productId, lineId, date, parcel_type_definition2.ParcelTypeCod.String()))
		}
		if parcelTypeAttr.GetIsBulky() {
			s.incrVolume(lineForecastKey(productId, lineId, date, parcel_type_definition2.ParcelTypeBulky.String()))
		}
		if parcelTypeAttr.GetIsHighValue() {
			s.incrVolume(lineForecastKey(productId, lineId, date, parcel_type_definition2.ParcelTypeHighValue.String()))
		}
		if parcelTypeAttr.GetIsDg() {
			s.incrVolume(lineForecastKey(productId, lineId, date, parcel_type_definition2.ParcelTypeDg.String()))
		}
	}
	return nil
}

func (s *ForecastVolumeRouting) IncrVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, parcelTypeAttr *parcel_type_definition2.ParcelTypeAttr) *srerr.Error {
	s.incrVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, ""))
	if parcelTypeAttr.GetIsCod() {
		s.incrVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, parcel_type_definition2.ParcelTypeCod.String()))
	}
	if parcelTypeAttr.GetIsBulky() {
		s.incrVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, parcel_type_definition2.ParcelTypeBulky.String()))
	}
	if parcelTypeAttr.GetIsHighValue() {
		s.incrVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, parcel_type_definition2.ParcelTypeHighValue.String()))
	}
	if parcelTypeAttr.GetIsDg() {
		s.incrVolume(zoneForecastKey(productId, lineId, groupId, zoneName, date, parcel_type_definition2.ParcelTypeDg.String()))
	}
	return nil
}

func (s *ForecastVolumeRouting) CheckAndGetRuleLimitDataForLocalForecast(ctx context.Context, ruleId uint64, filePath, operator string) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error) {
	return nil, nil
}

func lineForecastKey(productId int64, lineId string, date, parcelType string) string {
	if parcelType != "" {
		return fmt.Sprintf("%s_%d_%s_%s_%s", forecastKey, productId, lineId, date, parcelType)
	}
	return fmt.Sprintf("%s_%d_%s_%s", forecastKey, productId, lineId, date)
}

func zoneForecastKey(productId int64, lineId, groupId, zoneName string, date, parcelType string) string {
	if parcelType != "" {
		return fmt.Sprintf("%s_%d_%s_%s_%s_%s_%s", forecastKey, productId, lineId, groupId, zoneName, date, parcelType)
	}
	return fmt.Sprintf("%s_%d_%s_%s_%s_%s", forecastKey, productId, lineId, groupId, zoneName, date)
}

func (s *ForecastVolumeRouting) readVolume(key string) int64 {
	res := int64(0)
	s.mu.RLock()
	res = s.volumeCounter[key]
	s.mu.RUnlock()
	return res
}

func (s *ForecastVolumeRouting) incrVolume(key string) {
	s.mu.Lock()
	s.volumeCounter[key]++
	s.mu.Unlock()
}
