package vrservice

import (
	"bytes"
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
	"strings"
)

var zoneRuleLimitHeader = []string{"Line", "*Volume Zone Group ID", "*Zone Name", "Product", "Zone Min Daily Limit", "Zone Max Daily Limit",
	"Max COD Limit", "Max Bulky Limit", "Max High-Value Limit", "Max DG Limit"}

func (p *ServiceImpl) ExportRuleDetails(ctx context.Context, tabs []persistent.VolumeRoutingRuleDetailTab) (*bytes.Buffer, *srerr.Error) {
	var data [][]string
	for _, tab := range tabs {
		var line []string
		line = append(line, tab.LineId)
		line = append(line, tab.GroupId)
		line = append(line, tab.ZoneName)
		line = append(line, strings.Join(persistent.ProductIdListToString(tab.ProductIdList), ProductRelateSymbol))
		line = append(line, strconv.FormatInt(tab.Min, 10))
		line = append(line, strconv.FormatInt(tab.Max, 10))
		line = append(line, strconv.FormatInt(tab.MaxCod, 10))
		line = append(line, strconv.FormatInt(tab.MaxBulky, 10))
		line = append(line, strconv.FormatInt(tab.MaxHighValue, 10))
		line = append(line, strconv.FormatInt(tab.MaxDg, 10))
		data = append(data, line)
	}
	emptySheetName := ""
	file, err := fileutil.MakeExcel(ctx, zoneRuleLimitHeader, data, emptySheetName)
	if err != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailExportFail, nil, err)
	}
	b, bErr := file.WriteToBuffer()
	if bErr != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailExportFail, nil, bErr)
	}
	return b, nil
}

func (p *ServiceImpl) ImportRuleDetails(ctx context.Context, ruleId uint64, filePath string, operator string) *srerr.Error {
	timeout := 30
	if configutil.GetVolumeRuleConf(ctx).ImportRuleTimeout != 0 {
		timeout = configutil.GetVolumeRuleConf(ctx).ImportRuleTimeout
	}
	data, rErr := httputil.Get(ctx, filePath, nil, timeout, nil)
	if rErr != nil {
		return srerr.New(srerr.ZoneRuleDetailDownloadFail, nil, "get zone limit file fail, err:%s", rErr.Error())
	}
	rows, _, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(data), true)
	if fErr != nil {
		return srerr.With(srerr.ZoneRuleDetailParseExcelFail, nil, fErr)
	}
	var tabs []*persistent.VolumeRoutingRuleDetailTab
	for _, row := range rows {
		if len(row) < 10 {
			continue
		}
		tabs = append(tabs, p.parseRuleDetail(ctx, ruleId, row, operator))
	}
	return p.zoneRuleRepo.RefreshRuleDetailByRuleId(ctx, ruleId, tabs)
}

func (p *ServiceImpl) parseRuleDetail(ctx context.Context, ruleId uint64, dataRow []string, operator string) *persistent.VolumeRoutingRuleDetailTab {
	tab := persistent.VolumeRoutingRuleDetailTab{
		RuleId:   ruleId,
		ZoneName: dataRow[2],
		LineId:   "",
		LineType: 0, //todo
		Operator: operator,
		Ctime:    timeutil.GetCurrentUnixTimeStamp(ctx),
		Mtime:    timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	lineArr := strings.Split(dataRow[0], "-")
	tab.LineId = strings.TrimSpace(lineArr[0])
	// 这里存量是部分失败不阻塞整体的逻辑
	if v, err := strconv.ParseInt(dataRow[4], 10, 64); err == nil {
		tab.Min = v
	}
	if v, err := strconv.ParseInt(dataRow[5], 10, 64); err == nil {
		tab.Max = v
	}
	if v, err := strconv.ParseInt(dataRow[6], 10, 64); err == nil {
		tab.MaxCod = v
	}
	if v, err := strconv.ParseInt(dataRow[7], 10, 64); err == nil {
		tab.MaxBulky = v
	}
	if v, err := strconv.ParseInt(dataRow[8], 10, 64); err == nil {
		tab.MaxHighValue = v
	}
	if v, err := strconv.ParseInt(dataRow[9], 10, 64); err == nil {
		tab.MaxDg = v
	}
	return &tab
}
