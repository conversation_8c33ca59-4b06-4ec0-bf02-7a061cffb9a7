package vrservice

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

var (
	locationZoneExcelHeader = []string{"Volume Zone Group", "Zone Name", "Region", "Location ID", "State", "City", "District", "Street"}
	postcodeZoneExcelHeader = []string{"Volume Zone Group", "Zone Name", "Region", "Postcode"}
	cepRangeZoneExcelHeader = []string{"Volume Zone Group", "Zone Name", "Region", "CEP Initial", "CEP Final"}
)

func (p *ServiceImpl) ExportZoneListByTask(ctx context.Context, zoneType enum.ZoneType, condition map[string]interface{}, s3Key string) (string, *srerr.Error) {
	var tabs [][]string
	var header []string
	switch zoneType {
	case enum.ZoneTypeLocation:
		dataList, err := p.zoneRepo.GetLocationZoneList(ctx, condition)
		if err != nil {
			return "", err
		}
		tabs = p.formatLocZoneExportData(ctx, dataList)
		header = locationZoneExcelHeader
	case enum.ZoneTypePostcode:
		dataList, err := p.zoneRepo.GetPostcodeZoneList(ctx, condition)
		if err != nil {
			return "", err
		}
		tabs = p.formatPostcodeZoneExportData(ctx, dataList)
		header = postcodeZoneExcelHeader
	case enum.ZoneTypeCEPRange:
		dataList, err := p.zoneRepo.GetCepRangeZoneList(ctx, condition)
		if err != nil {
			return "", err
		}
		tabs = p.formatCepRangeZoneExportData(ctx, dataList)
		header = cepRangeZoneExcelHeader
	}
	emptySheetName := ""
	f, fErr := fileutil.MakeExcel(ctx, header, tabs, emptySheetName)
	if fErr != nil {
		return "", srerr.With(srerr.ZoneExportFail, nil, fErr)
	}
	b, wErr := f.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.ZoneExportFail, nil, fErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return objutil.Merge(bucket, "*@*", s3Key), nil
}

func (p *ServiceImpl) formatLocZoneExportData(ctx context.Context, list []persistent.VolumeZoneLocationTab) [][]string {
	var rows [][]string
	groupMap := make(map[string]string)
	for _, data := range list {
		group, ok := groupMap[data.GroupId]
		if !ok {
			if groupInfo, _ := p.zoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, data.GroupId, data.RoutingType, data.IsForecastType); groupInfo != nil {
				group = objutil.Join("-", groupInfo.GroupId, groupInfo.GroupName)
			}
			groupMap[data.GroupId] = group
		}
		rows = append(rows, []string{
			group,
			data.ZoneName,
			data.Region,
			strconv.FormatInt(data.LocationId, 10),
			data.State,
			data.City,
			data.District,
			data.Street,
		})
	}
	return rows
}

func (p *ServiceImpl) formatPostcodeZoneExportData(ctx context.Context, list []persistent.VolumeZonePostcodeTab) [][]string {
	var rows [][]string
	groupMap := make(map[string]string)
	for _, data := range list {
		group, ok := groupMap[data.GroupId]
		if !ok {
			if groupInfo, _ := p.zoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, data.GroupId, data.RoutingType, data.IsForecastType); groupInfo != nil {
				group = objutil.Join("-", groupInfo.GroupId, groupInfo.GroupName)
			}
			groupMap[data.GroupId] = group
		}
		rows = append(rows, []string{
			group,
			data.ZoneName,
			data.Region,
			data.Postcode,
		})
	}
	return rows
}

func (p *ServiceImpl) formatCepRangeZoneExportData(ctx context.Context, list []persistent.VolumeZoneCepRangeTab) [][]string {
	var rows [][]string
	groupMap := make(map[string]string)
	for _, data := range list {
		group, ok := groupMap[data.GroupId]
		if !ok {
			if groupInfo, _ := p.zoneGroupRepo.GetZoneGroupCacheByGroupId(ctx, data.GroupId, data.RoutingType, data.IsForecastType); groupInfo != nil {
				group = objutil.Join("-", groupInfo.GroupId, groupInfo.GroupName)
			}
			groupMap[data.GroupId] = group
		}
		rows = append(rows, []string{
			group,
			data.ZoneName,
			data.Region,
			strconv.FormatInt(data.CepInitial, 10),
			strconv.FormatInt(data.CepFinal, 10),
		})
	}
	return rows
}
