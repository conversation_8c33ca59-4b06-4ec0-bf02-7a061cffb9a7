package vrservice

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strconv"
	"strings"
)

type DefaultSelectGroupService interface {
	ListByPage(ctx context.Context, request *routing_volume.ListDefaultSelectGroupByPageRequest) (*routing_volume.ListDefaultSelectGroupByPageResponse, *srerr.Error)
	Create(ctx context.Context, request *routing_volume.CreateDefaultSelectGroupRequest) *srerr.Error
	Update(ctx context.Context, request *routing_volume.UpdateDefaultSelectGroupRequest) *srerr.Error
	Get(ctx context.Context, request *routing_volume.GetDefaultSelectGroupRequest) (*routing_volume.GetDefaultSelectGroupResponse, *srerr.Error)
	Delete(ctx context.Context, request *routing_volume.DeleteDefaultSelectGroupRequest) *srerr.Error
	SearchByProduct(ctx context.Context, request *routing_volume.SearchDefaultSelectGroupByProductIdRequest) (*routing_volume.SearchDefaultSelectGroupByProductIdResponse, *srerr.Error)
}

type DefaultSelectGroupServiceImpl struct {
	DefaultSelectGroupRepo vrrepo.DefaultSelectGroupRepo
	LpsApi                 lpsclient.LpsApi
}

func NewDefaultSelectGroupServiceImpl(defaultSelectGroupRepo vrrepo.DefaultSelectGroupRepo, lpsApi lpsclient.LpsApi) *DefaultSelectGroupServiceImpl {
	return &DefaultSelectGroupServiceImpl{
		DefaultSelectGroupRepo: defaultSelectGroupRepo,
		LpsApi:                 lpsApi,
	}
}

func (d *DefaultSelectGroupServiceImpl) ListByPage(ctx context.Context, request *routing_volume.ListDefaultSelectGroupByPageRequest) (*routing_volume.ListDefaultSelectGroupByPageResponse, *srerr.Error) {
	// 1. 分页查询default select group数据
	defaultSelectGroupTabList, total, err := d.DefaultSelectGroupRepo.ListByPage(ctx, nil, request.PageNo, request.PageSize)
	if err != nil {
		return nil, err
	}
	// 2. 构建返回结果
	defaultSelectGroupInfoList, err := d.convertToDefaultSelectGroupInfo(ctx, defaultSelectGroupTabList)
	if err != nil {
		return nil, err
	}
	return &routing_volume.ListDefaultSelectGroupByPageResponse{
		Total:                  total,
		DefaultSelectGroupList: defaultSelectGroupInfoList,
	}, nil
}

func (d *DefaultSelectGroupServiceImpl) Create(ctx context.Context, request *routing_volume.CreateDefaultSelectGroupRequest) *srerr.Error {
	//1. 新增default select group需要检查default select group是否存在
	oldDefaultSelectGroupTab, err := d.DefaultSelectGroupRepo.GetByParam(ctx, map[string]interface{}{"default_select_group = ?": request.DefaultSelectGroup})
	if err != nil && !errors.Is(err.Cause(), scormv2.ErrRecordNotFound) {
		return err
	}
	if oldDefaultSelectGroupTab != nil && oldDefaultSelectGroupTab.Id != 0 {
		return srerr.New(srerr.DuplicateDefaultSelectGroup, nil, "duplicate default select group, group: %v", request.DefaultSelectGroup)
	}
	//2. 需要校验product是否在其他default select group中，一个product只能属于一个group
	for _, productId := range request.ProductIdList {
		defaultSelectGroupTab, err := d.GetDefaultSelectGroupTabByProduct(ctx, productId)
		if err != nil {
			return err
		}
		if defaultSelectGroupTab != nil {
			return srerr.New(srerr.DuplicateDefaultSelectGroup, nil, "duplicate default select group, group: %v", defaultSelectGroupTab.DefaultSelectGroup)
		}
	}
	//3. 保存default select group
	defaultSelectGroupTab := &persistent.DefaultSelectGroupTab{
		DefaultSelectGroup: request.DefaultSelectGroup,
		ProductIds:         strings.Join(objutil.Int64sToStrings(request.ProductIdList), persistent.ProductIdDivideLine),
	}
	return d.DefaultSelectGroupRepo.Create(ctx, defaultSelectGroupTab)
}

func (d *DefaultSelectGroupServiceImpl) Update(ctx context.Context, request *routing_volume.UpdateDefaultSelectGroupRequest) *srerr.Error {
	//1. 判断default select group是否存在
	oldDefaultSelectGroupTab, err := d.DefaultSelectGroupRepo.GetByParam(ctx, map[string]interface{}{"default_select_group = ?": request.DefaultSelectGroup})
	if err != nil {
		return err
	}
	if oldDefaultSelectGroupTab == nil || oldDefaultSelectGroupTab.Id == 0 {
		return srerr.New(srerr.DefaultSelectGroupNotExist, nil, "duplicate default select group, group: %v", request.DefaultSelectGroup)
	}
	//2. 需要校验product是否在其他default select group中，一个product只能属于一个group
	for _, productId := range request.ProductIdList {
		defaultSelectGroupTab, err := d.GetDefaultSelectGroupTabByProduct(ctx, productId)
		if err != nil {
			return err
		}
		// product在其他default select group中出现
		if defaultSelectGroupTab != nil && defaultSelectGroupTab.Id != oldDefaultSelectGroupTab.Id {
			return srerr.New(srerr.DuplicateDefaultSelectGroup, nil, "duplicate default select group, group: %v", defaultSelectGroupTab.DefaultSelectGroup)
		}
	}
	//3. 更新default select group
	oldDefaultSelectGroupTab.DefaultSelectGroup = request.DefaultSelectGroup
	oldDefaultSelectGroupTab.ProductIds = strings.Join(objutil.Int64sToStrings(request.ProductIdList), persistent.ProductIdDivideLine)
	return d.DefaultSelectGroupRepo.Update(ctx, oldDefaultSelectGroupTab)
}

func (d *DefaultSelectGroupServiceImpl) Get(ctx context.Context, request *routing_volume.GetDefaultSelectGroupRequest) (*routing_volume.GetDefaultSelectGroupResponse, *srerr.Error) {
	//1. 查询default select group
	defaultSelectGroupTab, err := d.DefaultSelectGroupRepo.GetByParam(ctx, map[string]interface{}{"default_select_group = ?": request.DefaultSelectGroup})
	if err != nil {
		return nil, err
	}
	//2. 构建返回结果
	defaultSelectGroupInfoList, err := d.convertToDefaultSelectGroupInfo(ctx, []*persistent.DefaultSelectGroupTab{defaultSelectGroupTab})
	if err != nil {
		return nil, err
	}
	// 数据不存在
	if len(defaultSelectGroupInfoList) != 1 {
		return nil, nil
	}
	return &routing_volume.GetDefaultSelectGroupResponse{
		DefaultSelectGroupInfo: *defaultSelectGroupInfoList[0],
	}, nil
}

func (d *DefaultSelectGroupServiceImpl) Delete(ctx context.Context, request *routing_volume.DeleteDefaultSelectGroupRequest) *srerr.Error {
	//1. 删除default select group
	return d.DefaultSelectGroupRepo.DeleteByParam(ctx, map[string]interface{}{"default_select_group = ?": request.DefaultSelectGroup})
}

func (d *DefaultSelectGroupServiceImpl) SearchByProduct(ctx context.Context, request *routing_volume.SearchDefaultSelectGroupByProductIdRequest) (*routing_volume.SearchDefaultSelectGroupByProductIdResponse, *srerr.Error) {
	//1. 根据product获取default select group
	defaultSelectGroupTab, err := d.GetDefaultSelectGroupTabByProduct(ctx, request.ProductId)
	if err != nil {
		return nil, err
	}
	//2. 构建返回结果
	defaultSelectGroupInfoList, err := d.convertToDefaultSelectGroupInfo(ctx, []*persistent.DefaultSelectGroupTab{defaultSelectGroupTab})
	if err != nil {
		return nil, err
	}
	// 数据不存在
	if len(defaultSelectGroupInfoList) != 1 {
		return nil, nil
	}
	return &routing_volume.SearchDefaultSelectGroupByProductIdResponse{
		DefaultSelectGroupInfo: *defaultSelectGroupInfoList[0],
	}, nil
}

// GetDefaultSelectGroupTabByProduct 根据product获取所属的default group
func (d *DefaultSelectGroupServiceImpl) GetDefaultSelectGroupTabByProduct(ctx context.Context, productId int64) (*persistent.DefaultSelectGroupTab, *srerr.Error) {
	defaultSelectGroupTabList, err := d.DefaultSelectGroupRepo.ListByParam(ctx, map[string]interface{}{"product_ids like ?": "%" + strconv.FormatInt(productId, 10) + "%"})
	if err != nil && !errors.Is(err.Cause(), scormv2.ErrRecordNotFound) {
		return nil, err
	}
	for _, defaultSelectGroupTab := range defaultSelectGroupTabList {
		groupProductIdList, err := SplitDefaultSelectGroupProductIds(defaultSelectGroupTab.ProductIds)
		if err != nil {
			return nil, err
		}
		for _, groupProductId := range groupProductIdList {
			if groupProductId == productId {
				return defaultSelectGroupTab, nil
			}
		}
	}
	return nil, nil
}

func (d *DefaultSelectGroupServiceImpl) convertToDefaultSelectGroupInfo(ctx context.Context, defaultSelectGroupTabList []*persistent.DefaultSelectGroupTab) ([]*routing_volume.DefaultSelectGroupInfo, *srerr.Error) {
	// 1. 查询default select group中的product name信息
	productIdNameMap, err := d.LpsApi.GetAllProductIdNameList(ctx)
	if err != nil {
		return nil, err
	}
	// 2. 构建返回结果
	var defaultSelectGroupInfoList []*routing_volume.DefaultSelectGroupInfo
	for _, defaultSelectGroupTab := range defaultSelectGroupTabList {
		// 数据不存在则跳过
		if defaultSelectGroupTab == nil || defaultSelectGroupTab.Id == 0 {
			continue
		}
		defaultSelectGroup := &routing_volume.DefaultSelectGroupInfo{
			DefaultSelectGroup: defaultSelectGroupTab.DefaultSelectGroup,
		}
		productIdList, err := SplitDefaultSelectGroupProductIds(defaultSelectGroupTab.ProductIds)
		if err != nil {
			return nil, err
		}
		var productInfoList []*routing_volume.ProductInfo
		for _, productId := range productIdList {
			productInfoList = append(productInfoList, &routing_volume.ProductInfo{
				ProductId:   productId,
				ProductName: productIdNameMap[productId],
			})
		}
		defaultSelectGroup.ProductInfoList = productInfoList
		defaultSelectGroupInfoList = append(defaultSelectGroupInfoList, defaultSelectGroup)
	}
	return defaultSelectGroupInfoList, nil
}

func SplitDefaultSelectGroupProductIds(productIds string) ([]int64, *srerr.Error) {
	if productIds == "" {
		return nil, nil
	}
	productIdStrList := strings.Split(productIds, persistent.ProductIdDivideLine)
	var productIdList []int64
	for _, productIdStr := range productIdStrList {
		productId, err := strconv.ParseInt(productIdStr, 10, 64)
		if err != nil {
			return nil, srerr.New(srerr.ParamErr, nil, "parse product id error, productIdStr: %s, err: %v", productIdStr, err)
		}
		productIdList = append(productIdList, productId)
	}
	return productIdList, nil
}
