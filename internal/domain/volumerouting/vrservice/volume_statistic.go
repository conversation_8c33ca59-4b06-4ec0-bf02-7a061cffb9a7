package vrservice

import (
	"bytes"
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	rc "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strconv"
)

const (
	forderIdRecordPrefix = "forderid-record"
	lineDimensionPrefix  = "product-dimension"
	zoneDimensionPrefix  = "zone-dimension"
)

type Service interface {
	ExportZoneListByTask(ctx context.Context, zoneType enum.ZoneType, condition map[string]interface{}, s3Key string) (string, *srerr.Error)
	ImportZoneByTask(ctx context.Context, task *persistent.VolumeTaskRecordTab, param schema.ZoneImportParam) ([][]string, *srerr.Error)
	UploadResultFile(ctx context.Context, zoneType enum.ZoneType, s3Key string, rows [][]string) (string, *srerr.Error)
	CheckVolumeRuleCanSaveDraft(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error
	CheckAndGetRuleLimitData(ctx context.Context, ruleId uint64, filePath, operator string, benchMarkValue [][]string) ([]persistent.VolumeRoutingRuleDetailTab, []vrentity.CheckZoneLimitInfoMsg, *srerr.Error)
	CheckVolumeRuleCanSubmit(ctx context.Context, rule *vrentity.VolumeRule, productId int64) *srerr.Error
	ExportRuleDetails(ctx context.Context, tabs []persistent.VolumeRoutingRuleDetailTab) (*bytes.Buffer, *srerr.Error)
	ImportRuleDetails(ctx context.Context, ruleId uint64, filePath string, operator string) *srerr.Error
	CurrentVolumeLineDimension(ctx context.Context, productId int64, lineId, date, parcelType string) (int64, *srerr.Error)
	CurrentVolumeZoneDimension(ctx context.Context, productId int64, lineId, groupId, zoneName, date, parcelType string) (int64, *srerr.Error)
	IncreaseVolumeLineDimension(ctx context.Context, forderId string) *srerr.Error
	IncreaseVolumeZoneDimension(ctx context.Context, forderId string) *srerr.Error
	IncrVolumeLineDimension(ctx context.Context, productId int64, lineIdlist []string, date string, lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr) *srerr.Error
	IncrVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, parcelTypeAttr *parcel_type_definition2.ParcelTypeAttr) *srerr.Error
	CheckAndGetRuleLimitDataForLocalForecast(ctx context.Context, ruleId uint64, filePath, operator string) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error)
	//ClearPastVolumeRecords(ctx context.Context, forderId string) *srerr.Error
	//RecordVolumeLineDimension(ctx context.Context, forderId string, productId int64, lineId, date string) *srerr.Error
	//RecordVolumeZoneDimension(ctx context.Context, forderId string, productId int64, lineId, groupId, zoneName, date string) *srerr.Error
}

type ServiceImpl struct {
	zoneGroupRepo vrrepo.ZoneGroupRepo
	zoneRepo      vrrepo.ZoneRepo
	zoneRuleRepo  vrrepo.ZoneRuleRepo
	taskRepo      vrrepo.TaskRepo
	addrRepo      address.AddrRepo
	redisCounter  rc.RedisCounter
}

func NewServiceImpl(
	zoneGroupRepo vrrepo.ZoneGroupRepo,
	zoneRepo vrrepo.ZoneRepo,
	zoneRuleRepo vrrepo.ZoneRuleRepo,
	taskRepo vrrepo.TaskRepo,
	addrRepo address.AddrRepo,
	redisCounter rc.RedisCounter,
) *ServiceImpl {
	return &ServiceImpl{
		zoneGroupRepo: zoneGroupRepo,
		zoneRepo:      zoneRepo,
		zoneRuleRepo:  zoneRuleRepo,
		taskRepo:      taskRepo,
		addrRepo:      addrRepo,
		redisCounter:  redisCounter,
	}
}

func lineCounterKey(productId int64, lineId, date, parcelType string) string {
	if parcelType != "" {
		return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, date, parcelType)
	}
	return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, date)
}

// v := objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date)
func zoneCounterKey(productId int64, lineId, date, groupId, zoneName, parcelType string) string {
	//	v :=
	if parcelType != "" {
		return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date, parcelType)
	}
	return objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date)
}

func (p *ServiceImpl) IncrVolumeZoneDimension(ctx context.Context, productId int64, lineId, date, groupId, zoneName string, parcelTypeAttr *parcel_type_definition2.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "live counter unsupported method")
	return nil
}

func (p *ServiceImpl) IncrVolumeLineDimension(ctx context.Context, productId int64, lineIdList []string, date string, lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr) *srerr.Error {
	logger.CtxLogErrorf(ctx, "live counter unsupported method")
	return nil
}

func (p *ServiceImpl) CurrentVolumeLineDimension(ctx context.Context, productId int64, lineId, date, parcelType string) (int64, *srerr.Error) {
	k := lineCounterKey(productId, lineId, date, parcelType)
	v, err := p.redisCounter.GetInt64(ctx, k)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get current volume by line dimension fail, k:%s, err:%+v", k, err)
		return 0, srerr.With(srerr.RedisErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "get current volume by line dimension success, k:%s, v:%d", k, v)
	return v, nil
}

func (p *ServiceImpl) CurrentVolumeZoneDimension(ctx context.Context, productId int64, lineId, groupId, zoneName, date, parcelType string) (int64, *srerr.Error) {
	k := zoneCounterKey(productId, lineId, date, groupId, zoneName, parcelType)
	v, err := p.redisCounter.GetInt64(ctx, k)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get current volume by zone dimension fail, k:%s, err:%+v", k, err)
		return 0, srerr.With(srerr.RedisErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "get current volume by zone dimension success, k:%s, v:%d", k, v)
	return v, nil
}

//func (p *ServiceImpl) ClearPastVolumeRecords(ctx context.Context, forderId string) *srerr.Error {
//	rds, err := redisutil.Client()
//	if err != nil {
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	lineKey := objutil.Join("-", forderIdRecordPrefix, forderId, lineDimensionPrefix)
//	zoneKey := objutil.Join("-", forderIdRecordPrefix, forderId, zoneDimensionPrefix)
//	if err := rds.Del(ctx, lineKey, zoneKey).Err(); err != nil {
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	return nil
//}

//func (p *ServiceImpl) RecordVolumeLineDimension(ctx context.Context, forderId string, productId int64, lineId, date string) *srerr.Error {
//	rds, err := redisutil.Client()
//	if err != nil {
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	k := objutil.Join("-", forderIdRecordPrefix, forderId, lineDimensionPrefix)
//	v := objutil.Join("*", strconv.FormatInt(productId, 10), lineId, date)
//	if err := rds.LPush(ctx, k, v).Err(); err != nil {
//		logger.CtxLogErrorf(ctx, "record volume by line dimension fail, k:%s, err:%+v", k, err)
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	rds.Expire(ctx, k, time.Hour)
//	logger.CtxLogErrorf(ctx, "record volume by line dimension success, k:%s, v:%+v", k, v)
//	return nil
//}

//func (p *ServiceImpl) RecordVolumeZoneDimension(ctx context.Context, forderId string, productId int64, lineId, groupId, zoneName, date string) *srerr.Error {
//	rds, err := redisutil.Client()
//	if err != nil {
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	k := objutil.Join("-", forderIdRecordPrefix, forderId, zoneDimensionPrefix)
//	v := objutil.Join("*", strconv.FormatInt(productId, 10), lineId, groupId, zoneName, date)
//	if err := rds.LPush(ctx, k, v).Err(); err != nil {
//		logger.CtxLogErrorf(ctx, "record volume by zone dimension fail, k:%s, err:%+v", k, err)
//		return srerr.With(srerr.RedisErr, nil, err)
//	}
//	rds.Expire(ctx, k, time.Hour)
//	logger.CtxLogErrorf(ctx, "record volume by zone dimension success, k:%s, v:%+v", k, v)
//	return nil
//}

func (p *ServiceImpl) IncreaseVolumeLineDimension(ctx context.Context, forderId string) *srerr.Error {
	rds, err := redisutil.Client()
	if err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}
	forderKey := objutil.Join("-", forderIdRecordPrefix, forderId, lineDimensionPrefix)
	defer rds.Del(ctx, forderKey)
	keys, err := rds.LRange(ctx, forderKey, 0, -1).Result()
	if err != nil {
		logger.CtxLogErrorf(ctx, "get forder volume list by line dimension fail, k:%s, err:%+v", forderKey, err)
		return srerr.With(srerr.RedisErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "get forder volume list by line dimension success, k:%s, v:%+v", forderKey, keys)
	if len(keys) == 0 {
		return nil
	}
	// maintain idempotence
	keySet := make(map[string]struct{})
	for _, k := range keys {
		keySet[k] = struct{}{}
	}
	for k := range keySet {
		rds.Incr(ctx, k)
	}
	return nil
}

func (p *ServiceImpl) IncreaseVolumeZoneDimension(ctx context.Context, forderId string) *srerr.Error {
	rds, err := redisutil.Client()
	if err != nil {
		return srerr.With(srerr.RedisErr, nil, err)
	}
	forderKey := objutil.Join("-", forderIdRecordPrefix, forderId, zoneDimensionPrefix)
	defer rds.Del(ctx, forderKey)
	keys, err := rds.LRange(ctx, forderKey, 0, -1).Result()
	if err != nil {
		logger.CtxLogErrorf(ctx, "get forder volume list by zone dimension fail, k:%s, err:%+v", forderKey, err)
		return srerr.With(srerr.RedisErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "get forder volume list by zone dimension success, k:%s, v:%+v", forderKey, keys)
	if len(keys) == 0 {
		return nil
	}
	// maintain idempotence
	keySet := make(map[string]struct{})
	for _, k := range keys {
		keySet[k] = struct{}{}
	}
	for k := range keySet {
		rds.Incr(ctx, k)
	}
	return nil
}
