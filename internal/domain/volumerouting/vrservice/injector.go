package vrservice

import (
	rc "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
	"github.com/google/wire"
)

var ServiceProviderSet = wire.NewSet(
	NewServiceImpl,
	wire.Bind(new(Service), new(*ServiceImpl)),
	rc.RedisCounterProviderSet,
)

var DefaultSelectGroupServiceSet = wire.NewSet(
	NewDefaultSelectGroupServiceImpl,
	wire.Bind(new(DefaultSelectGroupService), new(*DefaultSelectGroupServiceImpl)),
)
