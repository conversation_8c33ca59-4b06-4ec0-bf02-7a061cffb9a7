package vrservice

import (
	"context"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"reflect"
	"sync"
	"testing"
)

func TestForecastVolumeRouting_IncrVolumeLineDimension(t *testing.T) {
	ctx := context.Background()
	s := &ForecastVolumeRouting{
		mu:            sync.RWMutex{},
		volumeCounter: map[string]int64{},
	}
	type args struct {
		productId     int64
		lineIdList    []string
		date          string
		lineParcelMap map[string]*parcel_type_definition2.ParcelTypeAttr
	}
	tests := []struct {
		name string
		args args
		want *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				lineIdList: []string{"line1", "line2"},
				date:       "2021-01-01",
				lineParcelMap: map[string]*parcel_type_definition2.ParcelTypeAttr{
					"line1": {
						IsCod:       true,
						IsBulky:     true,
						IsHighValue: true,
						IsDg:        true,
					},
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := s.IncrVolumeLineDimension(ctx, tt.args.productId, tt.args.lineIdList, tt.args.date, tt.args.lineParcelMap); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IncrVolumeLineDimension() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestForecastVolumeRouting_IncrVolumeZoneDimension(t *testing.T) {
	ctx := context.Background()
	s := &ForecastVolumeRouting{
		mu:            sync.RWMutex{},
		volumeCounter: map[string]int64{},
	}
	type args struct {
		productId      int64
		lineId         string
		date           string
		groupId        string
		zoneName       string
		parcelTypeAttr *parcel_type_definition2.ParcelTypeAttr
	}
	tests := []struct {
		name string
		args args
		want *srerr.Error
	}{
		// TODO: Add test cases.
		{
			name: "case 1: normal result",
			args: args{
				lineId:   "line1",
				groupId:  "group1",
				zoneName: "zone1",
				date:     "2021-01-01",
				parcelTypeAttr: &parcel_type_definition2.ParcelTypeAttr{
					IsCod:       true,
					IsBulky:     true,
					IsHighValue: true,
					IsDg:        true,
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := s.IncrVolumeZoneDimension(ctx, tt.args.productId, tt.args.lineId, tt.args.date, tt.args.groupId, tt.args.zoneName, tt.args.parcelTypeAttr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IncrVolumeZoneDimension() = %v, want %v", got, tt.want)
			}
		})
	}
}
