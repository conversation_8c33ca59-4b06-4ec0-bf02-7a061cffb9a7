package persistent

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var (
	VolumeTaskRecordHook = new(VolumeTaskRecordTab)
)

type VolumeTaskRecordTab struct {
	Id            uint64                       `gorm:"column:id" json:"id"`
	TaskId        string                       `gorm:"column:task_id" json:"task_id"`
	TaskName      string                       `gorm:"column:task_name" json:"task_name"`
	OperationType enum.VolumeTaskOperationType `gorm:"column:operation_type" json:"operation_type"`
	ZoneGroupId   string                       `gorm:"column:zone_group_id" json:"zone_group_id"`
	FileName      string                       `gorm:"column:file_name" json:"file_name"`
	FailReason    string                       `gorm:"column:fail_reason" json:"fail_reason"`
	RemotePath    string                       `gorm:"column:remote_path" json:"remote_path"`
	TaskStatus    enum.VolumeTaskStatus        `gorm:"column:task_status" json:"task_status"`
	Operator      string                       `gorm:"column:operator" json:"operator"`
	RoutingType   int                          `gorm:"column:routing_type" json:"routing_type"`
	Ctime         int64                        `gorm:"column:ctime" json:"ctime"`
	Mtime         int64                        `gorm:"column:mtime" json:"mtime"`
	Param         []byte                       `gorm:"column:param" json:"param"`
}

func (p VolumeTaskRecordTab) TableName() string {
	return "volume_task_record_tab"
}

func (p VolumeTaskRecordTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeTaskRecordTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeTaskRecordTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}
