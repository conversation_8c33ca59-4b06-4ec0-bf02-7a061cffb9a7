package persistent

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var (
	DefaultSelectGroupTabHook = &DefaultSelectGroupTab{}
	ProductIdDivideLine       = "|"
)

type DefaultSelectGroupTab struct {
	Id                 uint64 `gorm:"column:id"`
	DefaultSelectGroup int64  `gorm:"column:default_select_group"`
	ProductIds         string `gorm:"column:product_ids"`
	Ctime              int64  `gorm:"column:ctime;autoUpdateTime"`
	Mtime              int64  `gorm:"column:mtime;autoCreateTime"`
}

func (d DefaultSelectGroupTab) TableName() string {
	return "default_select_group_tab"
}

func (d DefaultSelectGroupTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (d DefaultSelectGroupTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (d DefaultSelectGroupTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        d.Id,
		ModelName: d.<PERSON>(),
	}
}
