package persistent

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

type (
	ProductIdList []int64
)

var (
	VolumeRoutingRuleHook       = new(VolumeRoutingRuleTab)
	VolumeRoutingRuleDetailHook = new(VolumeRoutingRuleDetailTab)
)

type VolumeRoutingRuleTab struct {
	Id                   uint64                `gorm:"column:id" json:"id"`
	RuleName             string                `gorm:"column:rule_name" json:"rule_name"`
	ProductId            int64                 `gorm:"column:product_id" json:"product_id"`
	RuleType             enum.VolumeRuleType   `gorm:"column:rule_type" json:"rule_type"`
	Priority             int64                 `gorm:"column:priority" json:"priority"`
	EffectiveStartTime   int64                 `gorm:"column:effective_start_time" json:"effective_start_time"`
	EffectiveImmediately bool                  `gorm:"column:effective_immediately" json:"effective_immediately"`
	RuleStatus           enum.VolumeRuleStatus `gorm:"column:rule_status" json:"rule_status"`
	Operator             string                `gorm:"column:operator" json:"operator"`
	Ctime                int64                 `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime                int64                 `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
	DataVersion          int64                 `gorm:"column:data_version" json:"data_version"`
	RoutingType          int                   `gorm:"column:routing_type" json:"routing_type"`
	IsForecastType       bool                  `gorm:"column:is_forecast_type" json:"is_forecast_type"`
	TaskId               uint64                `gorm:"column:task_id" json:"task_id"`
	ProductIdList        ProductIdList         `gorm:"column:product_id_list" json:"product_id_list"`
	ShareVolume          bool                  `gorm:"column:share_volume" json:"share_volume"`
}

func (p VolumeRoutingRuleTab) TableName() string {
	return "volume_routing_rule_tab"
}

func (p VolumeRoutingRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeRoutingRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeRoutingRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   p.Id,
		ModelName:            p.TableName(),
		FulfillmentProductId: uint64(p.ProductId),
	}
}

func (p *ProductIdList) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("fail to unmarshal ProductIdList: ", value))
	}

	return jsoniter.Unmarshal(bytes, p)
}

func (p ProductIdList) Value() (driver.Value, error) {
	if len(p) == 0 {
		return "[]", nil
	}

	return jsoniter.Marshal(p)
}

type VolumeRoutingRuleDetailTab struct {
	Id                 uint64        `gorm:"column:id" json:"id"`
	RuleId             uint64        `gorm:"column:rule_id" json:"rule_id"`
	ZoneName           string        `gorm:"column:zone_name" json:"zone_name"`
	LineId             string        `gorm:"column:line_id" json:"line_id"`
	GroupId            string        `gorm:"column:group_id" json:"group_id"`
	LineType           int           `gorm:"column:line_type" json:"line_type"`
	ComponentProductId int64         `gorm:"column:component_product_id" json:"component_product_id"`
	Min                int64         `gorm:"column:min_capacity" json:"min"`
	Max                int64         `gorm:"column:max_capacity" json:"max"`
	MaxCod             int64         `gorm:"column:max_cod_capacity" json:"max_cod"`
	MaxBulky           int64         `gorm:"column:max_bulky_capacity" json:"max_bulky"`
	MaxHighValue       int64         `gorm:"column:max_high_value_capacity" json:"max_high_value"`
	MaxDg              int64         `gorm:"column:max_dg_capacity" json:"max_dg"`
	Operator           string        `gorm:"column:operator" json:"operator"`
	Ctime              int64         `gorm:"column:ctime;autoCreateTime" json:"ctime"`
	Mtime              int64         `gorm:"column:mtime;autoUpdateTime" json:"mtime"`
	DataVersion        int64         `gorm:"column:data_version" json:"data_version"`
	RoutingType        int           `gorm:"column:routing_type" json:"routing_type"`
	IsForecastType     bool          `gorm:"column:is_forecast_type" json:"is_forecast_type"`
	ProductIdList      ProductIdList `gorm:"column:product_id_list" json:"product_id_list"`
}

func (p VolumeRoutingRuleDetailTab) TableName() string {
	return "volume_routing_rule_detail_tab"
}

func (p VolumeRoutingRuleDetailTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeRoutingRuleDetailTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeRoutingRuleDetailTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   p.Id,
		ModelName:            p.TableName(),
		RuleId:               p.RuleId,
		FulfillmentProductId: uint64(p.ComponentProductId),
	}
}

func ProductIdListToString(productIdList ProductIdList) []string {
	var productIdStrList []string
	for _, productId := range productIdList {
		productIdStrList = append(productIdStrList, strconv.FormatInt(productId, 10))
	}
	return productIdStrList
}
