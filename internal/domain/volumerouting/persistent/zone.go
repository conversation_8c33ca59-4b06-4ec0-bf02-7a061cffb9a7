package persistent

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var (
	VolumeZoneLocationHook = new(VolumeZoneLocationTab)
	VolumeZonePostcodeHook = new(VolumeZonePostcodeTab)
	VolumeZoneCepRangeHook = new(VolumeZoneCepRangeTab)
)

type VolumeZoneLocationTab struct {
	Id             uint64 `gorm:"column:id" json:"id"`
	GroupId        string `gorm:"column:group_id" json:"group_id"`
	ZoneName       string `gorm:"column:zone_name" json:"zone_name"`
	Region         string `gorm:"column:region" json:"region"`
	LocationId     int64  `gorm:"column:location_id" json:"location_id"`
	State          string `gorm:"column:state" json:"state"`
	City           string `gorm:"column:city" json:"city"`
	District       string `gorm:"column:district" json:"district"`
	Street         string `gorm:"column:street" json:"street"`
	Operator       string `gorm:"column:operator" json:"operator"`
	IsForecastType bool   `gorm:"column:is_forecast_type" json:"is_forecast_type"`
	RoutingType    int    `gorm:"column:routing_type" json:"routing_type"`
	Ctime          int64  `gorm:"column:ctime" json:"ctime"`
	Mtime          int64  `gorm:"column:mtime" json:"mtime"`
}

func (p VolumeZoneLocationTab) TableName() string {
	return "volume_zone_location_tab"
}

func (p VolumeZoneLocationTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeZoneLocationTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeZoneLocationTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}

type VolumeZonePostcodeTab struct {
	Id             uint64 `gorm:"column:id" json:"id"`
	GroupId        string `gorm:"column:group_id" json:"group_id"`
	ZoneName       string `gorm:"column:zone_name" json:"zone_name"`
	Region         string `gorm:"column:region" json:"region"`
	Postcode       string `gorm:"column:postcode" json:"postcode"`
	Operator       string `gorm:"column:operator" json:"operator"`
	RoutingType    int    `gorm:"column:routing_type" json:"routing_type"`
	IsForecastType bool   `gorm:"column:is_forecast_type" json:"is_forecast_type"`
	Ctime          int64  `gorm:"column:ctime" json:"ctime"`
	Mtime          int64  `gorm:"column:mtime" json:"mtime"`
}

func (p VolumeZonePostcodeTab) TableName() string {
	return "volume_zone_postcode_tab"
}

func (p VolumeZonePostcodeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeZonePostcodeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeZonePostcodeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}

type VolumeZoneCepRangeTab struct {
	Id             uint64 `gorm:"column:id" json:"id"`
	GroupId        string `gorm:"column:group_id" json:"group_id"`
	ZoneName       string `gorm:"column:zone_name" json:"zone_name"`
	Region         string `gorm:"column:region" json:"region"`
	CepInitial     int64  `gorm:"column:cep_initial" json:"cep_initial"`
	CepFinal       int64  `gorm:"column:cep_final" json:"cep_final"`
	Operator       string `gorm:"column:operator" json:"operator"`
	RoutingType    int    `gorm:"column:routing_type" json:"routing_type"`
	IsForecastType bool   `gorm:"column:is_forecast_type" json:"is_forecast_type"`
	Ctime          int64  `gorm:"column:ctime" json:"ctime"`
	Mtime          int64  `gorm:"column:mtime" json:"mtime"`
}

func (p VolumeZoneCepRangeTab) TableName() string {
	return "volume_zone_cep_range_tab"
}

func (p VolumeZoneCepRangeTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeZoneCepRangeTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p VolumeZoneCepRangeTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}
