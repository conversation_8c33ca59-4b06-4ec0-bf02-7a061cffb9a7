package persistent

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

var (
	VolumeZoneGroupHook      = new(VolumeZoneGroupTab)
	GroupProductLineRefHook  = new(GroupProductLineRefTab)
	GroupZoneCapacityRefHook = new(GroupZoneCapacityRefTab)
)

type VolumeZoneGroupTab struct {
	Id             uint64        `gorm:"column:id"`
	GroupId        string        `gorm:"column:group_id"`
	GroupName      string        `gorm:"column:group_name"`
	ZoneType       enum.ZoneType `gorm:"column:zone_type"`
	GroupCapacity  int64         `gorm:"column:group_capacity"`
	Operator       string        `gorm:"column:operator"`
	RoutingType    int           `gorm:"column:routing_type"`
	IsForecastType bool          `gorm:"column:is_forecast_type"`
	Ctime          int64         `gorm:"column:ctime"`
	Mtime          int64         `gorm:"column:mtime"`
}

func (p VolumeZoneGroupTab) TableName() string {
	return "volume_zone_group_tab"
}

func (p VolumeZoneGroupTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p VolumeZoneGroupTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p *VolumeZoneGroupTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}

type GroupProductLineRefTab struct {
	Id             uint64 `gorm:"column:id"`
	GroupId        string `gorm:"column:group_id"`
	MaskProductId  int64  `gorm:"column:mask_product_id"`
	ProductId      int64  `gorm:"column:product_id"`
	LineId         string `gorm:"column:line_id"`
	Operator       string `gorm:"column:operator"`
	IsForecastType bool   `gorm:"column:is_forecast_type"`
	RoutingType    int    `gorm:"column:routing_type"`
	Ctime          int64  `gorm:"column:ctime"`
	Mtime          int64  `gorm:"column:mtime"`
}

func (p GroupProductLineRefTab) TableName() string {
	return "group_product_line_ref_tab"
}

func (p GroupProductLineRefTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p GroupProductLineRefTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p GroupProductLineRefTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   p.Id,
		MaskProductId:        uint64(p.MaskProductId),
		FulfillmentProductId: uint64(p.ProductId),
		ModelName:            p.TableName(),
	}
}

type GroupZoneCapacityRefTab struct {
	Id          uint64 `gorm:"column:id"`
	GroupId     string `gorm:"column:group_id"`
	ZoneName    string `gorm:"column:zone_name"`
	Capacity    int64  `gorm:"column:capacity"`
	RoutingType int    `gorm:"routing_type"`
	Operator    string `gorm:"column:operator"`
	Ctime       int64  `gorm:"column:ctime"`
	Mtime       int64  `gorm:"column:mtime"`
}

func (p GroupZoneCapacityRefTab) TableName() string {
	return "group_zone_capacity_ref_tab"
}

func (p GroupZoneCapacityRefTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (p GroupZoneCapacityRefTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (p GroupZoneCapacityRefTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        p.Id,
		ModelName: p.TableName(),
	}
}
