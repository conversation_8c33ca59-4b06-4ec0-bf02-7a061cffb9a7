package vrentity

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"

type ZoneGroupInfo struct {
	GroupId        string        `json:"group_id"`
	GroupName      string        `json:"group_name"`
	ZoneType       enum.ZoneType `json:"zone_type"`
	GroupCapacity  int64         `json:"group_capacity"`
	MaskProductIds []int64       `json:"mask_product_ids"`
	ProductIds     []int64       `json:"product_ids"`
	LineIds        []string      `json:"line_ids"`
	RoutingType    int           `json:"routing_type"`
	IsForecastType bool          `json:"is_forecast_type"`
}
