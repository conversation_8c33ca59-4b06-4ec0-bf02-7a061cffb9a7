package vrentity

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type Zone struct {
	GroupId    string        `json:"group_id"`
	ZoneName   string        `json:"zone_name"`
	Region     string        `json:"region"`
	ZoneType   enum.ZoneType `json:"zone_type"`
	LocationId int64         `json:"location_id"`
	State      string        `json:"state"`
	City       string        `json:"city"`
	District   string        `json:"district"`
	Street     string        `json:"street"`
	Postcode   string        `json:"postcode"`
	CepInitial int64         `json:"cep_initial"`
	CepFinal   int64         `json:"cep_final"`
}

type (
	LocationZoneTree struct {
		GroupId      string
		LocSet       map[int64]*LocZoneImportInfo
		ZoneLocMap   map[string]map[int64]struct{}
		ParentLocSet map[int64]struct{}
	}

	LocZoneImportInfo struct {
		ActionMode                    int
		Row                           int
		ZoneName                      string
		LocId                         int64
		ParentLocIds                  []int64
		State, City, District, Street string
		Level                         int
	}
)

func (p *LocationZoneTree) Remove(info *LocZoneImportInfo) error {
	if _, ok := p.ZoneLocMap[info.ZoneName]; !ok {
		return fmt.Errorf("zone: %s not found", info.ZoneName)
	}
	if _, ok := p.LocSet[info.LocId]; !ok {
		return fmt.Errorf("location: %d not found", info.LocId)
	}
	delete(p.LocSet, info.LocId)
	delete(p.ZoneLocMap[info.ZoneName], info.LocId)
	if len(p.ZoneLocMap[info.ZoneName]) == 0 {
		delete(p.ZoneLocMap, info.ZoneName)
	}
	if len(info.ParentLocIds) > 0 {
		for _, parentId := range info.ParentLocIds {
			delete(p.ParentLocSet, parentId)
		}
	}
	return nil
}

func (p *LocationZoneTree) Insert(info *LocZoneImportInfo) error {
	if _, ok := p.LocSet[info.LocId]; ok {
		return fmt.Errorf("location ID: %d duplicated", info.LocId)
	}
	//if _, ok := p.ParentLocSet[info.LocId]; ok { // todo 去掉overlapping校验
	//	return fmt.Errorf("validation failure: zone %s have overlapping area, please change zone's setting", info.ZoneName)
	//}
	p.LocSet[info.LocId] = info
	if _, ok := p.ZoneLocMap[info.ZoneName]; !ok {
		p.ZoneLocMap[info.ZoneName] = make(map[int64]struct{})
	}
	p.ZoneLocMap[info.ZoneName][info.LocId] = struct{}{}
	//if len(info.ParentLocIds) > 0 { // todo 去掉overlapping校验
	//	for _, parentId := range info.ParentLocIds {
	//		p.ParentLocSet[parentId] = struct{}{}
	//	}
	//}
	return nil
}

func (p *LocationZoneTree) Format(ctx context.Context, operator string, routingType int, isForecastType bool) []persistent.VolumeZoneLocationTab {
	var tabs []persistent.VolumeZoneLocationTab
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, loc := range p.LocSet {
		tabs = append(tabs, persistent.VolumeZoneLocationTab{
			GroupId:        p.GroupId,
			ZoneName:       loc.ZoneName,
			Region:         envvar.GetCID(),
			LocationId:     loc.LocId,
			State:          loc.State,
			City:           loc.City,
			District:       loc.District,
			Street:         loc.Street,
			Operator:       operator,
			Ctime:          now,
			Mtime:          now,
			IsForecastType: isForecastType,
			RoutingType:    routingType,
		})
	}
	return tabs
}

type (
	PostcodeZoneTree struct {
		GroupId         string
		PostcodeSet     map[string]*PostcodeZoneImportInfo
		ZonePostcodeMap map[string]map[string]struct{}
	}

	PostcodeZoneImportInfo struct {
		ActionMode int
		Row        int
		ZoneName   string
		Postcode   string
	}
)

func (p *PostcodeZoneTree) Remove(info *PostcodeZoneImportInfo) error {
	if _, ok := p.ZonePostcodeMap[info.ZoneName]; !ok {
		return fmt.Errorf("zone: %s not found", info.ZoneName)
	}
	if _, ok := p.PostcodeSet[info.Postcode]; !ok {
		return fmt.Errorf("postcode: %s not found", info.Postcode)
	}
	delete(p.PostcodeSet, info.Postcode)
	delete(p.ZonePostcodeMap[info.ZoneName], info.Postcode)
	if len(p.ZonePostcodeMap[info.ZoneName]) == 0 {
		delete(p.ZonePostcodeMap, info.ZoneName)
	}
	return nil
}

func (p *PostcodeZoneTree) Insert(info *PostcodeZoneImportInfo) error {
	if _, ok := p.PostcodeSet[info.Postcode]; ok {
		return fmt.Errorf("postcode: %s duplicated", info.Postcode)
	}
	p.PostcodeSet[info.Postcode] = info
	if _, ok := p.ZonePostcodeMap[info.ZoneName]; !ok {
		p.ZonePostcodeMap[info.ZoneName] = make(map[string]struct{})
	}
	p.ZonePostcodeMap[info.ZoneName][info.Postcode] = struct{}{}
	return nil
}

func (p *PostcodeZoneTree) Format(ctx context.Context, operator string, routingType int, isForecastType bool) []persistent.VolumeZonePostcodeTab {
	var tabs []persistent.VolumeZonePostcodeTab
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	for postcode, info := range p.PostcodeSet {
		tabs = append(tabs, persistent.VolumeZonePostcodeTab{
			GroupId:        p.GroupId,
			ZoneName:       info.ZoneName,
			Region:         envvar.GetCID(),
			Postcode:       postcode,
			Operator:       operator,
			Ctime:          now,
			Mtime:          now,
			IsForecastType: isForecastType,
			RoutingType:    routingType,
		})
	}
	return tabs
}

type (
	CepRangeZoneTree struct {
		GroupId           string
		CepRangeSet       map[int64]*CepRangeZoneImportInfo
		SortedInitialList *[]int64
		ZoneCepRangeMap   map[string]map[int64]struct{}
	}

	CepRangeZoneImportInfo struct {
		ActionMode int
		Row        int
		ZoneName   string
		CepInitial int64
		CepFinal   int64
	}
)

func (p *CepRangeZoneTree) Remove(info *CepRangeZoneImportInfo) error {
	if _, ok := p.ZoneCepRangeMap[info.ZoneName]; !ok {
		return fmt.Errorf("zone: %s not found", info.ZoneName)
	}
	data, ok := p.CepRangeSet[info.CepInitial]
	if !ok {
		return fmt.Errorf("cep initial not found, zone:%s, initial:%d, final:%d", info.ZoneName, info.CepInitial, info.CepFinal)
	}
	if data.CepFinal != info.CepFinal {
		return fmt.Errorf("cep final not found, zone:%s, initial:%d, final:%d", info.ZoneName, info.CepInitial, info.CepFinal)
	}
	if data.ZoneName != info.ZoneName {
		return fmt.Errorf("zone not match,  zone:%s, initial:%d, final:%d", info.ZoneName, info.CepInitial, info.CepFinal)
	}
	delete(p.CepRangeSet, info.CepInitial)
	idx, _ := p.search(info)
	*(p.SortedInitialList) = append((*p.SortedInitialList)[:idx], (*p.SortedInitialList)[idx+1:]...)
	delete(p.ZoneCepRangeMap[info.ZoneName], info.CepInitial)
	if len(p.ZoneCepRangeMap[info.ZoneName]) == 0 {
		delete(p.ZoneCepRangeMap, info.ZoneName)
	}
	return nil
}

func (p *CepRangeZoneTree) Insert(info *CepRangeZoneImportInfo) error {
	if info.CepInitial >= info.CepFinal {
		return fmt.Errorf("cep range format error, cepInitial must be less than cepFinal, %s (%d-%d)",
			info.ZoneName, info.CepInitial, info.CepFinal)
	}
	if cr, ok := p.CepRangeSet[info.CepInitial]; ok {
		return fmt.Errorf("cep range overlap: %s (%d, %d)", cr.ZoneName, cr.CepInitial, cr.CepFinal)
	}
	idx, _ := p.search(info)
	if idx > 0 {
		left := p.CepRangeSet[(*p.SortedInitialList)[idx-1]]
		if info.CepInitial <= left.CepFinal {
			return fmt.Errorf("cep range overlap: %s (%d, %d)", left.ZoneName, left.CepInitial, left.CepFinal)
		}
	}
	if idx < len(*p.SortedInitialList) {
		right := p.CepRangeSet[(*p.SortedInitialList)[idx]]
		if info.CepFinal >= right.CepInitial {
			return fmt.Errorf("cep range overlap: %s (%d, %d)", right.ZoneName, right.CepInitial, right.CepFinal)
		}
	}
	if len(*p.SortedInitialList) == 0 || len(*p.SortedInitialList) == idx {
		*p.SortedInitialList = append(*p.SortedInitialList, info.CepInitial)
	} else {
		*p.SortedInitialList = append((*p.SortedInitialList)[:idx+1], (*p.SortedInitialList)[idx:]...)
		(*p.SortedInitialList)[idx] = info.CepInitial
	}
	p.CepRangeSet[info.CepInitial] = info
	if _, ok := p.ZoneCepRangeMap[info.ZoneName]; !ok {
		p.ZoneCepRangeMap[info.ZoneName] = make(map[int64]struct{})
	}
	p.ZoneCepRangeMap[info.ZoneName][info.CepInitial] = struct{}{}
	return nil
}

func (p *CepRangeZoneTree) Format(ctx context.Context, operator string, routingType int, isForecastType bool) []persistent.VolumeZoneCepRangeTab {
	var tabs []persistent.VolumeZoneCepRangeTab
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, info := range p.CepRangeSet {
		tabs = append(tabs, persistent.VolumeZoneCepRangeTab{
			GroupId:        p.GroupId,
			ZoneName:       info.ZoneName,
			Region:         envvar.GetCID(),
			CepInitial:     info.CepInitial,
			CepFinal:       info.CepFinal,
			Operator:       operator,
			Ctime:          now,
			Mtime:          now,
			IsForecastType: isForecastType,
			RoutingType:    routingType,
		})
	}
	return tabs
}

// len==1,
// [i,j],
// gap==1,
// gap >1 ,
func (p *CepRangeZoneTree) search(info *CepRangeZoneImportInfo) (int, bool) {
	if p.SortedInitialList == nil || len(*p.SortedInitialList) == 0 {
		return 0, false
	}
	i, j := 0, len(*p.SortedInitialList)
	for i < j {
		h := int(uint(i+j) >> 1)
		if info.CepInitial == (*p.SortedInitialList)[h] {
			return h, true
		}
		if info.CepInitial > (*p.SortedInitialList)[h] {
			i = h + 1
		} else {
			j = h
		}
	}
	return i, false
}
