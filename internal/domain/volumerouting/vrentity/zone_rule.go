package vrentity

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"strconv"
	"strings"
)

type VolumeRule struct {
	RuleId               uint64
	RuleName             string
	ProductId            int64
	ProductIdList        []int64
	ShareVolume          bool
	Priority             int64
	RuleType             enum.VolumeRuleType
	Status               enum.VolumeRuleStatus
	EffectiveStartTime   int64
	EffectiveImmediately bool
	LineLimits           []VolumeRuleDetail
	VolumeZoneLimits     []VolumeRuleDetail
	DataVersion          int64
	RoutingType          int
	IsForecastType       bool
}

type VolumeRuleDetail struct {
	LineId             string
	LineType           int
	ZoneGroupId        string
	ZoneName           string
	ComponentProductId int64
	Min                int64
	Max                int64
	MaxCod             int64
	MaxBulky           int64
	MaxHighValue       int64
	MaxDg              int64
	ProductIdList      []int64
}

const (
	ruleDataVersionPrefix = "ruleDataVersion-"
)

func InitVolumeRuleDataVersion(ctx context.Context, ruleId uint64) error {
	cli, err := redisutil.Client()
	if err != nil {
		return err
	}
	versionKey := objutil.Merge(ruleDataVersionPrefix, strconv.FormatUint(ruleId, 10))
	exist, err := cli.Exists(ctx, versionKey).Result()
	if err != nil {
		return err
	}
	if exist == 0 {
		cli.Set(ctx, versionKey, 1, 0)
	}
	return nil
}

func GetVolumeRuleDataVersion(ctx context.Context, ruleId uint64) (int64, error) {
	cli, err := redisutil.Client()
	if err != nil {
		return 0, err
	}
	k := objutil.Merge(ruleDataVersionPrefix, strconv.FormatUint(ruleId, 10))
	val, err := cli.Incr(ctx, k).Result()
	if err != nil {
		return 0, err
	}
	return val, nil
}

func ConvertToVolumeRuleInfo(p persistent.VolumeRoutingRuleTab) VolumeRule {
	res := VolumeRule{}
	res.RuleId = p.Id
	res.RuleName = p.RuleName
	res.ProductId = p.ProductId
	res.Priority = p.Priority
	res.RuleType = p.RuleType
	res.Status = p.RuleStatus
	res.EffectiveStartTime = p.EffectiveStartTime
	res.DataVersion = p.DataVersion
	res.RoutingType = p.RoutingType
	res.IsForecastType = p.IsForecastType
	return res
}

type CheckZoneLimitInfoMsg struct {
	ProductId string `json:"product_id"`
	LineId    string `json:"line_id"`
	ZoneGroup string `json:"zone_group"`
	ZoneName  string `json:"zone_name"`
	Message   string `json:"message"`
}

func CheckZoneLimitInfoMsgListToString(checkZoneLimitInfoMsgList []CheckZoneLimitInfoMsg) []string {
	var resultList []string
	for _, checkZoneLimitInfoMsg := range checkZoneLimitInfoMsgList {
		resultList = append(resultList, strings.Join([]string{checkZoneLimitInfoMsg.ProductId, checkZoneLimitInfoMsg.LineId, checkZoneLimitInfoMsg.ZoneGroup, checkZoneLimitInfoMsg.ZoneName, checkZoneLimitInfoMsg.Message}, "|"))
	}
	return resultList
}
