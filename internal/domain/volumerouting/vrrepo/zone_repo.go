package vrrepo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const batchCreateSize = 1000

type ZoneRepo interface {
	LocationZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeZoneLocationTab, int64, *srerr.Error)
	PostcodeZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeZonePostcodeTab, int64, *srerr.Error)
	CepRangeZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeZoneCepRangeTab, int64, *srerr.Error)
	GetLocationZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneLocationTab, *srerr.Error)
	GetPostcodeZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZonePostcodeTab, *srerr.Error)
	GetCepRangeZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneCepRangeTab, *srerr.Error)
	DeleteLocationZone(ctx context.Context, condition map[string]interface{}) *srerr.Error
	DeletePostcodeZone(ctx context.Context, condition map[string]interface{}) *srerr.Error
	DeleteCepRangeZone(ctx context.Context, condition map[string]interface{}) *srerr.Error
	CoverLocationZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZoneLocationTab, routingType int, isForecastType bool) *srerr.Error
	CoverPostcodeZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZonePostcodeTab, routingType int, isForecastType bool) *srerr.Error
	CoverCepRangeZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZoneCepRangeTab, routingType int, isForecastType bool) *srerr.Error
	GetLocationZoneName(ctx context.Context, groupId string, routingType int, isForecastType bool, locationIds []int64) ([]string, *srerr.Error)
	GetPostcodeZoneName(ctx context.Context, groupId string, postcode string, routingType int, isForecastType bool) (string, *srerr.Error)
	GetCepRangeZoneName(ctx context.Context, groupId string, postcode string, routingType int, isForecastType bool) (string, *srerr.Error)
	ExistZoneName(ctx context.Context, groupId string, zoneType enum.ZoneType, zoneName string, routingType int, isForecastType bool) bool
	GetZoneNameListByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool, zoneType enum.ZoneType) (map[string]bool, *srerr.Error)
	GetZoneNameWithDeliveryAddress(ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string
	CreateZoneWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, zoneType enum.ZoneType, routingType int) *srerr.Error
	DeleteZoneWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, model dbutil.DBModel) *srerr.Error
	CreateProductLineWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error
	DeleteProductLineWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error
	CreateZoneCapacityWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error
	GetZoneInfoById(ctx context.Context, id int64, model dbutil.DBModel, result interface{}) *srerr.Error
}

type ZoneRepoImpl struct {
}

func NewZoneRepoImpl() *ZoneRepoImpl {
	return &ZoneRepoImpl{}
}

func (p *ZoneRepoImpl) LocationZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) (
	[]persistent.VolumeZoneLocationTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeZoneLocationHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.LocationZoneCountFail, nil, err)
	}
	var list []persistent.VolumeZoneLocationTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneLocationHook, condition, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("ctime DESC")); err != nil {
		return nil, 0, srerr.With(srerr.LocationZoneQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneRepoImpl) PostcodeZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) (
	[]persistent.VolumeZonePostcodeTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeZonePostcodeHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.PostcodeZoneCountFail, nil, err)
	}
	var list []persistent.VolumeZonePostcodeTab
	if err := dbutil.Select(ctx, persistent.VolumeZonePostcodeHook, condition, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("ctime DESC")); err != nil {
		return nil, 0, srerr.With(srerr.PostcodeZoneQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneRepoImpl) CepRangeZonePage(ctx context.Context, condition map[string]interface{}, offset, size int64) (
	[]persistent.VolumeZoneCepRangeTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeZoneCepRangeHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.CepRangeZoneCountFail, nil, err)
	}
	var list []persistent.VolumeZoneCepRangeTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneCepRangeHook, condition, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("ctime DESC")); err != nil {
		return nil, 0, srerr.With(srerr.CepRangeZoneQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneRepoImpl) GetLocationZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneLocationTab, *srerr.Error) {
	var list []persistent.VolumeZoneLocationTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneLocationHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.LocationZoneQueryFail, nil, err)
	}
	return list, nil
}

func (p *ZoneRepoImpl) GetPostcodeZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZonePostcodeTab, *srerr.Error) {
	var list []persistent.VolumeZonePostcodeTab
	if err := dbutil.Select(ctx, persistent.VolumeZonePostcodeHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.PostcodeZoneQueryFail, nil, err)
	}
	return list, nil
}

func (p *ZoneRepoImpl) GetCepRangeZoneList(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneCepRangeTab, *srerr.Error) {
	var list []persistent.VolumeZoneCepRangeTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneCepRangeHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.CepRangeZoneQueryFail, nil, err)
	}
	return list, nil
}
func (p *ZoneRepoImpl) DeleteLocationZone(ctx context.Context, condition map[string]interface{}) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.VolumeZoneLocationHook, condition, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.LocationZoneDeleteFail, nil, err)
	}
	return nil
}

func (p *ZoneRepoImpl) DeletePostcodeZone(ctx context.Context, condition map[string]interface{}) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.VolumeZonePostcodeHook, condition, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.PostcodeZoneDeleteFail, nil, err)
	}
	return nil
}

func (p *ZoneRepoImpl) DeleteCepRangeZone(ctx context.Context, condition map[string]interface{}) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.VolumeZoneCepRangeHook, condition, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.CepRangeZoneDeleteFail, nil, err)
	}
	return nil
}

func (p *ZoneRepoImpl) CoverLocationZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZoneLocationTab, routingType int, isForecastType bool) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeZoneLocationHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeZoneLocationHook.TableName()).Delete(&persistent.VolumeZoneLocationTab{},
			constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).GetError(); err != nil {
			return srerr.With(srerr.LocationZoneDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeZoneLocationHook.TableName()).CreateInBatches(newData, batchCreateSize).GetError(); err != nil {
			return srerr.With(srerr.LocationZoneBatchCreateFail, nil, err)
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRepoImpl) CoverPostcodeZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZonePostcodeTab, routingType int, isForecastType bool) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeZonePostcodeHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeZonePostcodeHook.TableName()).Delete(&persistent.VolumeZonePostcodeTab{},
			constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).GetError(); err != nil {
			return srerr.With(srerr.PostcodeZoneDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeZonePostcodeHook.TableName()).CreateInBatches(newData, batchCreateSize).GetError(); err != nil {
			return srerr.With(srerr.PostcodeZoneBatchCreateFail, nil, err)
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRepoImpl) CoverCepRangeZoneByGroupId(ctx context.Context, groupId string, newData []persistent.VolumeZoneCepRangeTab, routingType int, isForecastType bool) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeZoneCepRangeHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeZoneCepRangeHook.TableName()).Delete(&persistent.VolumeZoneCepRangeTab{},
			constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).GetError(); err != nil {
			return srerr.With(srerr.CepRangeZoneDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeZoneCepRangeHook.TableName()).CreateInBatches(newData, batchCreateSize).GetError(); err != nil {
			return srerr.With(srerr.CepRangeZoneBatchCreateFail, nil, err)
		}
		return nil
	})

	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRepoImpl) GetLocationZoneName(ctx context.Context, groupId string, routingType int, isForecastType bool, locationIds []int64) ([]string, *srerr.Error) {
	var data []persistent.VolumeZoneLocationTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneLocationHook, map[string]interface{}{constant.GroupIdSql: groupId, "location_id in (?)": locationIds, constant.RoutingTypeSql: routingType,
		constant.IsForecastTypeSql: isForecastType}, &data); err != nil {
		return nil, srerr.With(srerr.LocationZoneQueryFail, nil, err)
	}
	zoneCodes := make([]string, 0, len(data))
	for _, zone := range data {
		zoneCodes = append(zoneCodes, zone.ZoneName)
	}
	return zoneCodes, nil
}

func (p *ZoneRepoImpl) GetPostcodeZoneName(ctx context.Context, groupId string, postcode string, routingType int, isForecastType bool) (string, *srerr.Error) {
	var data persistent.VolumeZonePostcodeTab
	if err := dbutil.Select(ctx, persistent.VolumeZonePostcodeHook, map[string]interface{}{constant.GroupIdSql: groupId, "postcode = ?": postcode, constant.RoutingTypeSql: routingType, constant.IsForecastTypeSql: isForecastType}, &data); err != nil {
		return "", srerr.With(srerr.PostcodeZoneQueryFail, nil, err)
	}
	return data.ZoneName, nil
}

func (p *ZoneRepoImpl) GetCepRangeZoneName(ctx context.Context, groupId string, postcode string, routingType int, isForecastType bool) (string, *srerr.Error) {
	cepCode, err := strconv.ParseInt(postcode, 10, 64)
	if err != nil {
		return "", srerr.With(srerr.FormatErr, nil, err)
	}
	var data persistent.VolumeZoneCepRangeTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneCepRangeHook, map[string]interface{}{
		constant.GroupIdSql:        groupId,
		"cep_initial <= ?":         cepCode,
		"cep_final >= ?":           cepCode,
		constant.RoutingTypeSql:    routingType,
		constant.IsForecastTypeSql: isForecastType,
	}, &data); err != nil {
		return "", srerr.With(srerr.CepRangeZoneQueryFail, nil, err)
	}
	return data.ZoneName, nil
}

func (p *ZoneRepoImpl) ExistZoneName(ctx context.Context, groupId string, zoneType enum.ZoneType, zoneName string, routingType int, isForecastType bool) bool {
	condition := map[string]interface{}{constant.GroupIdSql: groupId, "zone_name = ?": zoneName, constant.RoutingTypeSql: routingType, constant.IsForecastTypeSql: isForecastType}
	switch zoneType {
	case enum.ZoneTypeLocation:
		var data persistent.VolumeZoneLocationTab
		if err := dbutil.Take(ctx, persistent.VolumeZoneLocationHook, condition, &data); err != nil {
			return false
		}
		return true
	case enum.ZoneTypePostcode:
		var data persistent.VolumeZonePostcodeTab
		if err := dbutil.Take(ctx, persistent.VolumeZonePostcodeHook, condition, &data); err != nil {
			return false
		}
		return true
	case enum.ZoneTypeCEPRange:
		var data persistent.VolumeZoneCepRangeTab
		if err := dbutil.Take(ctx, persistent.VolumeZoneCepRangeHook, condition, &data); err != nil {
			return false
		}
		return true
	default:
		return false
	}
}

func (p *ZoneRepoImpl) GetZoneNameListByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool, zoneType enum.ZoneType) (map[string]bool, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, persistent.VolumeZoneGroupHook)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	var list []string
	switch zoneType {
	case enum.ZoneTypeLocation:
		r := db.Table(persistent.VolumeZoneLocationHook.TableName()).Select("zone_name").Where(constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).Scan(&list)
		if r.GetError() != nil {
			return nil, srerr.With(srerr.LocationZoneQueryFail, nil, r.GetError())
		}
	case enum.ZoneTypePostcode:
		r := db.Table(persistent.VolumeZonePostcodeHook.TableName()).Select("zone_name").Where(constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).Scan(&list)
		if r.GetError() != nil {
			return nil, srerr.With(srerr.PostcodeZoneQueryFail, nil, r.GetError())
		}
	case enum.ZoneTypeCEPRange:
		r := db.Table(persistent.VolumeZoneCepRangeHook.TableName()).Select("zone_name").Where(constant.VolumeGroupSqlTemplate, groupId, routingType, isForecastType).Scan(&list)
		if r.GetError() != nil {
			return nil, srerr.With(srerr.CepRangeZoneQueryFail, nil, r.GetError())
		}
	}
	set := make(map[string]bool)
	for _, data := range list {
		set[data] = false
	}
	return set, nil
}

func (z *ZoneRepoImpl) GetZoneNameWithDeliveryAddress(ctx context.Context, groupInfo *vrentity.ZoneGroupInfo, locInfo *pb.LocationInfo) []string {

	var zoneNameList []string
	switch groupInfo.ZoneType {
	case enum.ZoneTypeLocation:
		if len(locInfo.LocationIds) == 0 {
			return nil
		}
		zoneNameList, _ = z.GetLocationZoneName(ctx, groupInfo.GroupId, groupInfo.RoutingType, groupInfo.IsForecastType, locInfo.LocationIds)
	case enum.ZoneTypePostcode:
		zoneName, _ := z.GetPostcodeZoneName(ctx, groupInfo.GroupId, locInfo.GetPostcode(), groupInfo.RoutingType, groupInfo.IsForecastType)
		zoneNameList = append(zoneNameList, zoneName)
	default:
		zoneName, _ := z.GetCepRangeZoneName(ctx, groupInfo.GroupId, locInfo.GetPostcode(), groupInfo.RoutingType, groupInfo.IsForecastType)
		zoneNameList = append(zoneNameList, zoneName)
	}
	return zoneNameList
}

// todo 打印日志
func (z *ZoneRepoImpl) getZoneWithConditions(ctx context.Context, model dbutil.DBModel, condition map[string]interface{}, result interface{}) *srerr.Error {
	if err := dbutil.Select(ctx, model, condition, result); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) CreateZoneWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, zoneType enum.ZoneType, routingType int) *srerr.Error {
	switch zoneType {
	case enum.ZoneTypeLocation:
		return z.createLocationWithTx(ctx, db, condition, routingType)
	case enum.ZoneTypePostcode:
		return z.createPostcodeWithTx(ctx, db, condition, routingType)
	case enum.ZoneTypeCEPRange:
		return z.createCepRangeWithTx(ctx, db, condition, routingType)
	default:
		return srerr.New(srerr.ParamErr, zoneType, "zone type is not ZoneTypeLocation,ZoneTypePostcode or ZoneTypeCEPRange,zoneType=%d", zoneType)
	}
}

func (z *ZoneRepoImpl) createLocationWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, routingType int) *srerr.Error {
	var locationZoneList []*persistent.VolumeZoneLocationTab
	if err := z.getZoneWithConditions(ctx, persistent.VolumeZoneLocationHook, condition, &locationZoneList); err != nil {
		return err
	}
	copyCond := copyCondition(condition)
	copyCond[constant.IsForecastTypeSql] = false
	if err := z.DeleteZoneWithTx(ctx, db, copyCond, persistent.VolumeZoneLocationHook); err != nil {
		return err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	ts := timeutil.GetCurrentTime(ctx)
	for _, locationZone := range locationZoneList {
		locationZone.Id = 0
		locationZone.IsForecastType = false
		locationZone.RoutingType = routingType
		locationZone.Operator = operateBy
		locationZone.Ctime = ts.Unix()
		locationZone.Mtime = ts.Unix()
	}
	if err := db.Table(persistent.VolumeZoneLocationHook.TableName()).CreateInBatches(&locationZoneList, batchCreateSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) createPostcodeWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, routingType int) *srerr.Error {
	var postcodeList []*persistent.VolumeZonePostcodeTab
	if err := z.getZoneWithConditions(ctx, persistent.VolumeZonePostcodeHook, condition, &postcodeList); err != nil {
		return err
	}
	copyCond := copyCondition(condition)
	copyCond[constant.IsForecastTypeSql] = false
	if err := z.DeleteZoneWithTx(ctx, db, copyCond, persistent.VolumeZonePostcodeHook); err != nil {
		return err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	ts := timeutil.GetCurrentTime(ctx)
	for _, postcode := range postcodeList {
		postcode.Id = 0
		postcode.IsForecastType = false
		postcode.RoutingType = routingType
		postcode.Operator = operateBy
		postcode.Ctime = ts.Unix()
		postcode.Mtime = ts.Unix()
	}
	if err := db.Table(persistent.VolumeZonePostcodeHook.TableName()).CreateInBatches(&postcodeList, batchCreateSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) createCepRangeWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, routingType int) *srerr.Error {
	var cepRangeList []*persistent.VolumeZoneCepRangeTab
	if err := z.getZoneWithConditions(ctx, persistent.VolumeZoneCepRangeHook, condition, &cepRangeList); err != nil {
		return err
	}
	copyCond := copyCondition(condition)
	copyCond[constant.IsForecastTypeSql] = false
	if err := z.DeleteZoneWithTx(ctx, db, copyCond, persistent.VolumeZoneCepRangeHook); err != nil {
		return err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	ts := timeutil.GetCurrentTime(ctx)
	for _, cepRange := range cepRangeList {
		cepRange.Id = 0
		cepRange.IsForecastType = false
		cepRange.RoutingType = routingType
		cepRange.Operator = operateBy
		cepRange.Ctime = ts.Unix()
		cepRange.Mtime = ts.Unix()
	}
	if err := db.Table(persistent.VolumeZoneCepRangeHook.TableName()).CreateInBatches(&cepRangeList, batchCreateSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) CreateProductLineWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error {
	var productLineRefList []*persistent.GroupProductLineRefTab
	if err := z.getZoneWithConditions(ctx, persistent.GroupProductLineRefHook, condition, &productLineRefList); err != nil {
		return err
	}
	if err := z.DeleteProductLineWithTx(ctx, db, condition); err != nil {
		return err
	}
	operateBy, _ := apiutil.GetUserInfo(ctx)
	ts := timeutil.GetCurrentTime(ctx)
	for _, ref := range productLineRefList {
		ref.Id = 0
		ref.IsForecastType = false
		ref.Operator = operateBy
		ref.Ctime = ts.Unix()
		ref.Mtime = ts.Unix()
	}

	if err := db.Table(persistent.GroupProductLineRefHook.TableName()).CreateInBatches(&productLineRefList, batchCreateSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (z *ZoneRepoImpl) CreateZoneCapacityWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error {
	var zoneCapacityList []*persistent.GroupZoneCapacityRefTab
	if err := z.getZoneWithConditions(ctx, persistent.GroupZoneCapacityRefHook, condition, &zoneCapacityList); err != nil {
		return err
	}
	for _, cep := range zoneCapacityList {
		cep.Id = 0
	}
	if err := db.Table(persistent.GroupProductLineRefHook.TableName()).CreateInBatches(&zoneCapacityList, batchCreateSize).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (z *ZoneRepoImpl) DeleteZoneWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}, model dbutil.DBModel) *srerr.Error {
	sql, value := dbutil.ParseCondition(condition)
	if err := db.Table(model.TableName()).
		Where(sql, value...).Delete(model).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) DeleteProductLineWithTx(ctx context.Context, db scormv2.SQLCommon, condition map[string]interface{}) *srerr.Error {
	condition[constant.IsForecastTypeSql] = false
	sql, value := dbutil.ParseCondition(condition)
	if err := db.Table(persistent.GroupProductLineRefHook.TableName()).Where(sql, value...).Delete(persistent.GroupProductLineRefHook).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRepoImpl) GetZoneInfoById(ctx context.Context, id int64, model dbutil.DBModel, result interface{}) *srerr.Error {
	if err := dbutil.Select(ctx, model, map[string]interface{}{
		"id = ?": id,
	}, &result); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func copyCondition(condition map[string]interface{}) map[string]interface{} {
	copyCondition := make(map[string]interface{})
	if len(condition) == 0 {
		return copyCondition
	}
	for queryKey, queryValue := range condition {
		copyCondition[queryKey] = queryValue
	}
	return copyCondition
}
