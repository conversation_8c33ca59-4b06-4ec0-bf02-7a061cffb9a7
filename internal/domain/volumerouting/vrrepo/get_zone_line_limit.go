package vrrepo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	cache "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/lrucache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

var (
	volumeRoutingRuleLruCache, _ = cache.NewLruCache(cache.VolumeRoutingRuleLruName)
)

func (p *ZoneRuleRepoImpl) GetRuleZoneLimits(ctx context.Context, productId int64, ruleId uint64, dataVersion int64, groupId string, lineId, zoneName string, routingType int, isForecastType bool) (*vrentity.VolumeRuleDetail, *srerr.Error) {
	var (
		details    []persistent.VolumeRoutingRuleDetailTab
		conditions = map[string]interface{}{
			"rule_id = ?":              ruleId,
			"data_version = ?":         dataVersion,
			"zone_name = ?":            zoneName,
			constant.GroupIdSql:        groupId,
			constant.RoutingTypeSql:    routingType,
			constant.IsForecastTypeSql: isForecastType,
			"line_id = ?":              lineId,
		}
	)

	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleDetailHook, conditions, &details); err != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailQueryFail, nil, err)
	}

	// 找不到zone维度对应的运力限制直接返回nil，在外层做判断
	if len(details) == 0 {
		logger.CtxLogErrorf(ctx, "zone=%s not set limit", zoneName)
		return nil, nil
	}

	productIdList := []int64{productId}
	if len(details[0].ProductIdList) > 0 {
		productIdList = details[0].ProductIdList
	}

	return &vrentity.VolumeRuleDetail{
		ZoneName:      details[0].ZoneName,
		Min:           details[0].Min,
		Max:           details[0].Max,
		MaxCod:        details[0].MaxCod,
		MaxBulky:      details[0].MaxBulky,
		MaxHighValue:  details[0].MaxHighValue,
		MaxDg:         details[0].MaxDg,
		ProductIdList: productIdList,
	}, nil
}

func (p *ZoneRuleRepoImpl) GetRuleWithZoneLimitByProduct(ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error) {
	// ILH的理论上不会进入到这里来
	if routingType == rule.IlhRoutingType {
		return nil, nil
	}

	// CB的Forecast/线上
	if routingType == rule.CBRoutingType {
		return p.getRuleByRuleIdWithLru(ctx, volumeRuleId)
	}

	// Local/SPX的场景
	isForecastType, taskId := forecast.IsForecast(ctx)

	// Forecast
	if isForecastType {
		return p.getRuleByTaskIdWithLru(ctx, taskId)
	}

	// 线上
	return p.GetRuleByProduct(ctx, productId, int(routingType), constant.NonForecastType)
}

func (p *ZoneRuleRepoImpl) getRuleByRuleIdWithLru(ctx context.Context, ruleId int64) (*vrentity.VolumeRule, *srerr.Error) {
	cacheKey := "RuleID:" + strconv.Itoa(int(ruleId))
	if cacheVal, exist := volumeRoutingRuleLruCache.Get(ctx, cacheKey); exist {
		if val, ok := cacheVal.(*vrentity.VolumeRule); ok {
			return val, nil
		}
	}

	var (
		volumeRuleTab persistent.VolumeRoutingRuleTab
		condition     = map[string]interface{}{"id =?": ruleId}
	)

	if err := dbutil.Take(ctx, persistent.VolumeRoutingRuleHook, condition, &volumeRuleTab); err != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailQueryFail, nil, err)
	}

	volumeRuleEntity, err := enrichVolumeRuleEntity(ctx, &volumeRuleTab)
	if err != nil {
		return nil, err
	}

	volumeRoutingRuleLruCache.Add(ctx, cacheKey, volumeRuleEntity)

	return volumeRuleEntity, nil
}

func (p *ZoneRuleRepoImpl) getRuleByTaskIdWithLru(ctx context.Context, taskId int) (*vrentity.VolumeRule, *srerr.Error) {
	cacheKey := "TaskID:" + strconv.Itoa(taskId)
	if cacheVal, exist := volumeRoutingRuleLruCache.Get(ctx, cacheKey); exist {
		if val, ok := cacheVal.(*vrentity.VolumeRule); ok {
			return val, nil
		}
	}

	var (
		volumeRuleTab persistent.VolumeRoutingRuleTab
		condition     = map[string]interface{}{"task_id =?": taskId}
	)

	if err := dbutil.Take(ctx, persistent.VolumeRoutingRuleHook, condition, &volumeRuleTab); err != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailQueryFail, nil, err)
	}

	volumeRuleEntity, err := enrichVolumeRuleEntity(ctx, &volumeRuleTab)
	if err != nil {
		return nil, err
	}

	volumeRoutingRuleLruCache.Add(ctx, cacheKey, volumeRuleEntity)

	return volumeRuleEntity, nil
}

func enrichVolumeRuleEntity(ctx context.Context, volumeRule *persistent.VolumeRoutingRuleTab) (*vrentity.VolumeRule, *srerr.Error) {
	db, dbErr := dbutil.SlaveDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if dbErr != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, dbErr)
	}

	var details, tmpBatchDetails []persistent.VolumeRoutingRuleDetailTab
	if err := db.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).
		Where("rule_id = ? AND data_version = ?", volumeRule.Id, volumeRule.DataVersion).
		FindInBatches(&tmpBatchDetails, 1000, func(tx scormv2.SQLCommon, batch int) error {
			details = append(details, tmpBatchDetails...)
			return nil
		}).GetError(); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	var lineLimits, zoneLimits []vrentity.VolumeRuleDetail
	for _, detail := range details {
		// 兼容Detail中有冇ProductID的情况
		productIdList := []int64{volumeRule.ProductId}
		if len(detail.ProductIdList) != 0 {
			productIdList = detail.ProductIdList
		}
		detailEntity := vrentity.VolumeRuleDetail{
			LineId:        detail.LineId,
			LineType:      detail.LineType,
			ZoneName:      detail.ZoneName,
			Min:           detail.Min,
			Max:           detail.Max,
			MaxCod:        detail.MaxCod,
			MaxBulky:      detail.MaxBulky,
			MaxHighValue:  detail.MaxHighValue,
			MaxDg:         detail.MaxDg,
			ProductIdList: productIdList,
		}
		if detail.ZoneName == "" {
			lineLimits = append(lineLimits, detailEntity)
		} else {
			zoneLimits = append(zoneLimits, detailEntity)
		}
	}

	return &vrentity.VolumeRule{
		RuleId:               volumeRule.Id,
		RuleName:             volumeRule.RuleName,
		ProductId:            volumeRule.ProductId,
		ProductIdList:        volumeRule.ProductIdList,
		ShareVolume:          volumeRule.ShareVolume,
		Priority:             volumeRule.Priority,
		RuleType:             volumeRule.RuleType,
		Status:               volumeRule.RuleStatus,
		EffectiveStartTime:   volumeRule.EffectiveStartTime,
		EffectiveImmediately: volumeRule.EffectiveImmediately,
		DataVersion:          volumeRule.DataVersion,
		RoutingType:          volumeRule.RoutingType,
		IsForecastType:       volumeRule.IsForecastType,
		LineLimits:           lineLimits,
		VolumeZoneLimits:     zoneLimits,
	}, nil
}
