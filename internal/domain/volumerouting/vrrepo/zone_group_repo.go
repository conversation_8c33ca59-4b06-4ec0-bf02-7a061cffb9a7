package vrrepo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type ZoneGroupRepo interface {
	QueryProductLineRef(ctx context.Context, condition map[string]interface{}) (map[string][]persistent.GroupProductLineRefTab, *srerr.Error)
	Page(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeZoneGroupTab, int64, *srerr.Error)
	Create(ctx context.Context, tab *persistent.VolumeZoneGroupTab, refs []persistent.GroupProductLineRefTab) *srerr.Error
	UpdateByGroupId(ctx context.Context, groupId string, updateData map[string]interface{}, param *schema.ZoneGroupCreateOrUpdateParam) *srerr.Error
	Get(ctx context.Context, Id int) (*vrentity.ZoneGroupInfo, *srerr.Error)
	GetZoneEstimateCapacityPage(ctx context.Context, groupId, zoneName string, offset, size int64, routingType int) ([]persistent.GroupZoneCapacityRefTab, int64, *srerr.Error)
	GetZoneEstimateCapacityList(ctx context.Context, groupId, zoneName string, routingType int) ([]persistent.GroupZoneCapacityRefTab, *srerr.Error)
	SaveZoneEstimatedCapacity(ctx context.Context, groupId, zoneName string, capacity int64, operator string, routingType int, isForecastType bool) *srerr.Error
	BatchCreateGroupZoneCapacity(ctx context.Context, zoneGroupId string, routingType int, tab []persistent.GroupZoneCapacityRefTab) *srerr.Error
	QueryByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneGroupTab, *srerr.Error)
	GetZoneGroupCacheByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error)
	AllZoneGroupInfo(ctx context.Context) []*vrentity.ZoneGroupInfo
	GetGroupIdByLine(ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error)
	GetGroupIdByLineWithCache(ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error)
	GetGroupIdByProduct(ctx context.Context, productId int64) (string, *srerr.Error)
	GetZoneTypeByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (enum.ZoneType, *srerr.Error)
	DeleteGroupZoneCapacity(ctx context.Context, groupId string) *srerr.Error
	GetGroupBaseByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (*persistent.VolumeZoneGroupTab, *srerr.Error)
	CreateGroupWithTx(ctx context.Context, tab *persistent.VolumeZoneGroupTab, db scormv2.SQLCommon) *srerr.Error
	CreateProductLineRefWithTx(ctx context.Context, refs []persistent.GroupProductLineRefTab, db scormv2.SQLCommon) *srerr.Error
}

type ZoneGroupRepoImpl struct {
	zoneRule          ZoneRuleRepo
	routingConfigRepo ruledata.RoutingConfigRepo
}

func NewZoneGroupRepoImpl(zoneRule ZoneRuleRepo, routingConfigRepo ruledata.RoutingConfigRepo) *ZoneGroupRepoImpl {
	return &ZoneGroupRepoImpl{zoneRule: zoneRule, routingConfigRepo: routingConfigRepo}
}

func (p *ZoneGroupRepoImpl) QueryProductLineRef(ctx context.Context, condition map[string]interface{}) (map[string][]persistent.GroupProductLineRefTab, *srerr.Error) {
	var list []persistent.GroupProductLineRefTab
	if err := dbutil.Select(ctx, persistent.GroupProductLineRefHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	m := make(map[string][]persistent.GroupProductLineRefTab)
	for i := 0; i < len(list); i++ {
		if _, ok := m[list[i].GroupId]; ok {
			m[list[i].GroupId] = append(m[list[i].GroupId], list[i])
			continue
		}
		m[list[i].GroupId] = []persistent.GroupProductLineRefTab{list[i]}
	}
	return m, nil
}

func (p *ZoneGroupRepoImpl) Page(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeZoneGroupTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeZoneGroupHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.ZoneGroupCountFail, nil, err)
	}
	var data []persistent.VolumeZoneGroupTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneGroupHook, condition, &data, dbutil.WithPage(offset, size), dbutil.WithOrder("mtime desc")); err != nil {
		return nil, 0, srerr.With(srerr.ZoneGroupPageFail, nil, err)
	}
	return data, total, nil
}

func (p *ZoneGroupRepoImpl) Create(ctx context.Context, tab *persistent.VolumeZoneGroupTab, refs []persistent.GroupProductLineRefTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeZoneGroupHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		var count int64
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Where(constant.VolumeGroupSqlTemplate, tab.GroupId, tab.RoutingType, tab.IsForecastType).Count(&count).GetError(); err != nil {
			return srerr.With(srerr.ZoneGroupCountFail, nil, err)
		}
		if count > 0 {
			return srerr.New(srerr.ZoneGroupDuplicate, nil, "zone group duplicate, groupId:%s", tab.GroupId)
		}
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Create(tab).GetError(); err != nil {
			return srerr.With(srerr.ZoneGroupCreateFail, nil, err)
		}
		if err := tx.Table(persistent.GroupProductLineRefHook.TableName()).CreateInBatches(refs, 100).GetError(); err != nil {
			return srerr.With(srerr.ZoneGroupCreateFail, nil, err)
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneGroupRepoImpl) UpdateByGroupId(ctx context.Context, groupId string, updateData map[string]interface{}, param *schema.ZoneGroupCreateOrUpdateParam) *srerr.Error {
	// 检查group是否可以编辑
	if err := p.checkCanEdit(ctx, param.ZoneGroupId, param.RoutingType, param.IsForecastType); err != nil {
		return err
	}
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeZoneGroupHook)
	if dbErr != nil {
		return srerr.With(srerr.DataErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		var data persistent.VolumeZoneGroupTab
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Take(&data, constant.VolumeGroupSqlTemplate, groupId, param.RoutingType, param.IsForecastType).GetError(); err != nil {
			return srerr.With(srerr.ZoneGroupGetFail, nil, err)
		}
		var newRefs []persistent.GroupProductLineRefTab
		if len(param.LineIds) > 0 {
			var existData []persistent.GroupProductLineRefTab
			tx.Table(persistent.GroupProductLineRefHook.TableName()).Find(&existData, "line_id in (?) AND group_id <> ? AND routing_type = ? ", param.LineIds, groupId, param.RoutingType)
			if len(existData) > 0 {
				var groupName string
				var names []string
				tx.Table(persistent.VolumeZoneGroupHook.TableName()).Select("group_name").Where(constant.VolumeGroupSqlTemplate, existData[0].GroupId, existData[0].RoutingType, existData[0].IsForecastType).Scan(&names)
				if len(names) > 0 {
					groupName = names[0]
				}
				return srerr.New(srerr.ZoneGroupLineDuplicate, nil, "This Line already has "+
					"volume zone group %s-%s(volume zone group id-group name).", existData[0].GroupId, groupName)
			}
			for _, lineId := range param.LineIds {
				newRefs = append(newRefs, persistent.GroupProductLineRefTab{
					GroupId:        groupId,
					LineId:         lineId,
					Operator:       updateData["operator"].(string),
					RoutingType:    param.RoutingType,
					IsForecastType: param.IsForecastType,
					Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
					Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				})
			}
		}
		if len(param.ProductIds) > 0 {
			var existData []persistent.GroupProductLineRefTab
			tx.Table(persistent.GroupProductLineRefHook.TableName()).Find(&existData, "product_id in (?) AND group_id <> ? AND routing_type = ? AND is_forecast_type = ?", param.ProductIds, groupId, param.RoutingType, param.IsForecastType)
			if len(existData) > 0 {
				var groupName string
				var names []string
				tx.Table(persistent.VolumeZoneGroupHook.TableName()).Select("group_name").Where(constant.VolumeGroupSqlTemplate, existData[0].GroupId, param.RoutingType, param.IsForecastType).Scan(&names)
				if len(names) > 0 {
					groupName = names[0]
				}
				return srerr.New(srerr.ZoneGroupProductDuplicate, nil, "This Product already has "+
					"volume zone group %s-%s(volume zone group id-group name).", existData[0].GroupId, groupName)
			}
			routingType := p.getProductRoutingType(ctx, int(param.ProductIds[0]))
			for _, productId := range param.ProductIds {
				productRT := p.getProductRoutingType(ctx, int(productId))
				if routingType != productRT {
					return srerr.New(srerr.DifferentRoutingType, nil, "product=%d have different routing type %s", productId, rule.GetRoutingTypeName(productRT))
				}
				newRefs = append(newRefs, persistent.GroupProductLineRefTab{
					GroupId:        groupId,
					ProductId:      productId,
					RoutingType:    param.RoutingType,
					IsForecastType: param.IsForecastType,
					Operator:       updateData["operator"].(string),
					Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
					Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				})
			}
		}
		if len(param.MaskProductIds) > 0 {
			var existData []persistent.GroupProductLineRefTab
			tx.Table(persistent.GroupProductLineRefHook.TableName()).Find(&existData, "mask_product_id in (?) AND group_id <> ? AND routing_type = ? AND is_forecast_type = ?", param.MaskProductIds, groupId, param.RoutingType, param.IsForecastType)
			if len(existData) > 0 {
				var groupName string
				var names []string
				tx.Table(persistent.VolumeZoneGroupHook.TableName()).Select("group_name").Where(constant.VolumeGroupSqlTemplate, existData[0].GroupId, param.RoutingType, param.IsForecastType).Scan(&names)
				if len(names) > 0 {
					groupName = names[0]
				}
				return srerr.New(srerr.ZoneGroupMaskProductDuplicate, nil, "This Mask Product already has "+
					"volume zone group %s-%s(volume zone group id-group name).", existData[0].GroupId, groupName)
			}
			for _, productId := range param.MaskProductIds {
				newRefs = append(newRefs, persistent.GroupProductLineRefTab{
					GroupId:        groupId,
					MaskProductId:  productId,
					RoutingType:    param.RoutingType,
					IsForecastType: param.IsForecastType,
					Operator:       updateData["operator"].(string),
					Ctime:          timeutil.GetCurrentUnixTimeStamp(ctx),
					Mtime:          timeutil.GetCurrentUnixTimeStamp(ctx),
				})
			}
		}
		if err := tx.Table(persistent.VolumeZoneGroupHook.TableName()).Where("id = ?", data.Id).
			Updates(updateData).GetError(); err != nil {
			return srerr.With(srerr.ZoneGroupUpdateFail, nil, err)
		}
		if len(newRefs) > 0 {
			if err := tx.Table(persistent.GroupProductLineRefHook.TableName()).Delete(&persistent.GroupProductLineRefTab{},
				constant.VolumeGroupSqlTemplate, groupId, param.RoutingType, param.IsForecastType).GetError(); err != nil {
				return srerr.With(srerr.ZoneGroupUpdateFail, nil, err)
			}
			if err := tx.Table(persistent.GroupProductLineRefHook.TableName()).CreateInBatches(newRefs, 1000).GetError(); err != nil {
				return srerr.With(srerr.ZoneGroupUpdateFail, nil, err)
			}
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneGroupRepoImpl) Get(ctx context.Context, id int) (*vrentity.ZoneGroupInfo, *srerr.Error) {
	var list []*persistent.VolumeZoneGroupTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneGroupHook, map[string]interface{}{"id = ?": id}, &list); err != nil {
		return nil, srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	if len(list) == 0 {
		return nil, srerr.New(srerr.ZoneGroupNotFound, nil, "group not found, id:%d", id)
	}
	info := vrentity.ZoneGroupInfo{
		GroupId:        list[0].GroupId,
		GroupName:      list[0].GroupName,
		ZoneType:       list[0].ZoneType,
		GroupCapacity:  list[0].GroupCapacity,
		RoutingType:    list[0].RoutingType,
		IsForecastType: list[0].IsForecastType,
	}
	var refs []*persistent.GroupProductLineRefTab
	_ = dbutil.Select(ctx, persistent.GroupProductLineRefHook, map[string]interface{}{constant.GroupIdSql: info.GroupId, constant.RoutingTypeSql: info.RoutingType, constant.IsForecastTypeSql: info.IsForecastType}, &refs)
	if len(refs) == 0 {
		return &info, nil
	}
	var maskProductIds, productIds []int64
	var lineIds []string
	for _, ref := range refs {
		if ref.MaskProductId > 0 {
			maskProductIds = append(maskProductIds, ref.MaskProductId)
		}
		if ref.ProductId > 0 {
			productIds = append(productIds, ref.ProductId)
		}
		if ref.LineId != "" {
			lineIds = append(lineIds, ref.LineId)
		}
	}
	info.MaskProductIds = maskProductIds
	info.ProductIds = productIds
	info.LineIds = lineIds
	return &info, nil
}

func (p *ZoneGroupRepoImpl) GetZoneEstimateCapacityPage(ctx context.Context, groupId, zoneName string, offset, size int64, routingType int) ([]persistent.GroupZoneCapacityRefTab, int64, *srerr.Error) {
	condition := map[string]interface{}{constant.GroupIdSql: groupId, constant.RoutingTypeSql: routingType}
	if zoneName != "" {
		condition["zone_name = ?"] = zoneName
	}
	var total int64
	if err := dbutil.Count(ctx, persistent.GroupZoneCapacityRefHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.GroupCapacityRefCountFail, nil, err)
	}
	var list []persistent.GroupZoneCapacityRefTab
	if err := dbutil.Select(ctx, persistent.GroupZoneCapacityRefHook, condition, &list,
		dbutil.WithPage(offset, size)); err != nil {
		return nil, 0, srerr.With(srerr.GroupCapacityRefGetFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneGroupRepoImpl) GetZoneEstimateCapacityList(ctx context.Context, groupId, zoneName string, routingType int) ([]persistent.GroupZoneCapacityRefTab, *srerr.Error) {
	condition := map[string]interface{}{constant.GroupIdSql: groupId, constant.RoutingTypeSql: routingType}
	if zoneName != "" {
		condition["zone_name = ?"] = zoneName
	}
	var list []persistent.GroupZoneCapacityRefTab
	if err := dbutil.Select(ctx, persistent.GroupZoneCapacityRefHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.GroupCapacityRefGetFail, nil, err)
	}
	return list, nil
}

func (p *ZoneGroupRepoImpl) SaveZoneEstimatedCapacity(ctx context.Context, groupId, zoneName string, capacity int64, operator string, routingType int, isForecastType bool) *srerr.Error {
	condition := map[string]interface{}{
		constant.GroupIdSql:     groupId,
		"zone_name = ?":         zoneName,
		constant.RoutingTypeSql: routingType,
	}
	updateData := map[string]interface{}{
		"capacity": capacity,
		"operator": operator,
		"mtime":    timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	if err := dbutil.Update(ctx, persistent.GroupZoneCapacityRefHook, condition, updateData, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.GroupCapacityRefUpdateFail, nil, err)
	}
	return nil
}

func (p *ZoneGroupRepoImpl) BatchCreateGroupZoneCapacity(ctx context.Context, groupId string, routingType int, list []persistent.GroupZoneCapacityRefTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.GroupZoneCapacityRefHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.GroupZoneCapacityRefHook.TableName()).Delete(&persistent.GroupZoneCapacityRefTab{},
			constant.GroupIdSql+" and "+constant.RoutingTypeSql, groupId, routingType).GetError(); err != nil {
			return srerr.With(srerr.GroupCapacityRefUpdateFail, nil, err)
		}
		if err := tx.Table(persistent.GroupZoneCapacityRefHook.TableName()).CreateInBatches(list, 1000).GetError(); err != nil {
			return srerr.With(srerr.GroupCapacityRefUpdateFail, nil, err)
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneGroupRepoImpl) QueryByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeZoneGroupTab, *srerr.Error) {
	var results []persistent.VolumeZoneGroupTab
	if err := dbutil.Select(ctx, persistent.VolumeZoneGroupHook, condition, &results); err != nil {
		return nil, srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	return results, nil
}

func (p *ZoneGroupRepoImpl) GetZoneGroupCacheByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (*vrentity.ZoneGroupInfo, *srerr.Error) {
	data, err := localcache.Get(ctx, constant.VolumeZoneGroup, volumeZoneGroupKey(groupId, routingType, isForecastType))
	if err != nil {
		return nil, srerr.With(srerr.ZoneGroupNotFound, nil, err)
	}
	return data.(*vrentity.ZoneGroupInfo), nil
}

// todo 暂时写死
func volumeZoneGroupKey(groupId string, routingType int, isForecastType bool) string {
	return fmt.Sprintf("%v:%v:%v", groupId, routingType, isForecastType)
}

func (p *ZoneGroupRepoImpl) AllZoneGroupInfo(ctx context.Context) []*vrentity.ZoneGroupInfo {
	items := localcache.AllItems(ctx, constant.VolumeZoneGroup)
	var infoList []*vrentity.ZoneGroupInfo
	for _, item := range items {
		infoList = append(infoList, item.(*vrentity.ZoneGroupInfo))
	}
	return infoList
}

func (p *ZoneGroupRepoImpl) GetGroupIdByLine(ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
	var list []persistent.GroupProductLineRefTab
	if err := dbutil.Select(ctx, persistent.GroupProductLineRefHook, map[string]interface{}{"line_id = ?": lineId, constant.RoutingTypeSql: routingType, constant.IsForecastTypeSql: isForecastType}, &list); err != nil {
		return "", srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	if len(list) == 0 {
		return "", nil
	}
	return list[0].GroupId, nil
}

func (p *ZoneGroupRepoImpl) GetGroupIdByLineWithCache(ctx context.Context, lineId string, routingType int, isForecastType bool) (string, *srerr.Error) {
	cacheKey := LineToVolumeGroupKey(lineId, routingType, isForecastType)
	cacheVal, err := localcache.Get(ctx, constant.LineToVolumeGroup, cacheKey)
	if err != nil {
		logger.CtxLogDebugf(ctx, "line to volume group not found")
		return "", nil
	}

	groupId, ok := cacheVal.(string)
	if !ok {
		logger.CtxLogErrorf(ctx, "convert group id cache value to string fail")
		_ = monitor.AwesomeReportEvent(ctx, monitoring.VolumeManagementMonitor, monitoring.LineToVolumeGroupCacheErr, monitoring.StatusError, cacheKey)
		return "", nil
	}

	return groupId, nil
}

func (p *ZoneGroupRepoImpl) GetGroupIdByProduct(ctx context.Context, productId int64) (string, *srerr.Error) {
	var list []persistent.GroupProductLineRefTab
	if err := dbutil.Select(ctx, persistent.GroupProductLineRefHook, map[string]interface{}{"product_id = ?": productId}, &list); err != nil {
		return "", srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	if len(list) == 0 {
		return "", nil
	}
	return list[0].GroupId, nil
}

func (p *ZoneGroupRepoImpl) GetZoneTypeByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (enum.ZoneType, *srerr.Error) {
	var data persistent.VolumeZoneGroupTab
	if err := dbutil.Take(ctx, persistent.VolumeZoneGroupHook, map[string]interface{}{constant.GroupIdSql: groupId, constant.IsForecastTypeSql: isForecastType, constant.RoutingTypeSql: routingType}, &data); err != nil {
		return 0, srerr.With(srerr.ZoneGroupGetFail, nil, err)
	}
	return data.ZoneType, nil
}

func (p *ZoneGroupRepoImpl) DeleteGroupZoneCapacity(ctx context.Context, groupId string) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.GroupZoneCapacityRefHook, map[string]interface{}{"group_id = ?": groupId}, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.GroupDeleteFail, nil, err)
	}
	return nil
}

func (p *ZoneGroupRepoImpl) GetGroupBaseByGroupId(ctx context.Context, groupId string, routingType int, isForecastType bool) (*persistent.VolumeZoneGroupTab, *srerr.Error) {
	var data persistent.VolumeZoneGroupTab
	if err := dbutil.Take(ctx, persistent.VolumeZoneGroupHook, map[string]interface{}{constant.GroupIdSql: groupId, constant.RoutingTypeSql: routingType, constant.IsForecastTypeSql: isForecastType},
		&data); err != nil {
		return nil, srerr.With(srerr.ZoneGroupNotFound, nil, err)
	}
	return &data, nil
}

func (z *ZoneGroupRepoImpl) CreateGroupWithTx(ctx context.Context, tab *persistent.VolumeZoneGroupTab, db scormv2.SQLCommon) *srerr.Error {
	if err := db.Table(persistent.VolumeZoneGroupHook.TableName()).Create(tab).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneGroupRepoImpl) CreateProductLineRefWithTx(ctx context.Context, refs []persistent.GroupProductLineRefTab, db scormv2.SQLCommon) *srerr.Error {
	if err := db.Table(persistent.VolumeZoneGroupHook.TableName()).CreateInBatches(refs, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneGroupRepoImpl) getProductRoutingType(ctx context.Context, productId int) uint8 {
	config, err := z.routingConfigRepo.GetConfigByProductID(ctx, int64(productId))
	if err != nil {
		logger.CtxLogErrorf(ctx, "Get config from db err=%v", err)
		return rule.UnknownRoutingType
	}
	c := config.ToRoutingConfigEntity()
	return c.GetRoutingType()
}

func (z *ZoneGroupRepoImpl) checkCanEdit(ctx context.Context, groupId string, routingType int, isForecastType bool) *srerr.Error {
	ruleDetails, err := z.zoneRule.QueryRuleDetailByCondition(ctx, map[string]interface{}{
		constant.GroupIdSql:        groupId,
		constant.RoutingTypeSql:    routingType,
		constant.IsForecastTypeSql: isForecastType,
	})
	if err != nil {
		return err
	}

	var ruleIds []uint64
	ruleMap := make(map[uint64]struct{})
	for _, detail := range ruleDetails {
		if _, ok := ruleMap[detail.RuleId]; ok {
			continue
		}
		ruleIds = append(ruleIds, detail.RuleId)
		ruleMap[detail.RuleId] = struct{}{}
	}
	ruleList, rerr := z.zoneRule.QueryRuleByCondition(ctx, map[string]interface{}{
		"id in (?)":       ruleIds,
		"rule_status = ?": enum.VolumeRuleStatusQueuing,
	})
	if rerr != nil {
		return rerr
	}

	if len(ruleList) != 0 {
		return srerr.New(srerr.VolumeRuleError, groupId, "zone group can't edit when the used routing rule is in queueing status")
	}

	return nil
}
