package vrrepo

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func DumpZoneGroup() (map[string]interface{}, error) {
	var list []*persistent.VolumeZoneGroupTab
	if err := dbutil.Select(context.TODO(), persistent.VolumeZoneGroupHook, nil, &list); err != nil {
		return nil, err
	}
	var refs []*persistent.GroupProductLineRefTab
	if err := dbutil.Select(context.TODO(), persistent.GroupProductLineRefHook, nil, &refs); err != nil {
		return nil, err
	}
	productMap := make(map[string]map[int64]struct{})
	maskProductMap := make(map[string]map[int64]struct{})
	lineMap := make(map[string]map[string]struct{})
	for _, ref := range refs {
		if _, ok := productMap[ref.GroupId]; !ok {
			productMap[ref.GroupId] = make(map[int64]struct{})
		}
		if _, ok := maskProductMap[ref.GroupId]; !ok {
			maskProductMap[ref.GroupId] = make(map[int64]struct{})
		}
		if _, ok := lineMap[ref.GroupId]; !ok {
			lineMap[ref.GroupId] = make(map[string]struct{})
		}
		if ref.ProductId > 0 {
			productMap[ref.GroupId][ref.ProductId] = struct{}{}
		}
		if ref.MaskProductId > 0 {
			maskProductMap[ref.GroupId][ref.MaskProductId] = struct{}{}
		}
		if ref.LineId != "" {
			lineMap[ref.GroupId][ref.LineId] = struct{}{}
		}
	}
	result := make(map[string]interface{})
	for _, data := range list {
		info := vrentity.ZoneGroupInfo{
			GroupId:        data.GroupId,
			GroupName:      data.GroupName,
			ZoneType:       data.ZoneType,
			GroupCapacity:  data.GroupCapacity,
			RoutingType:    data.RoutingType,
			IsForecastType: data.IsForecastType,
		}
		if v, ok := productMap[data.GroupId]; ok {
			var productIds []int64
			for productId := range v {
				productIds = append(productIds, productId)
			}
			info.ProductIds = productIds
		}
		if v, ok := maskProductMap[data.GroupId]; ok {
			var maskProductIds []int64
			for maskProductId := range v {
				maskProductIds = append(maskProductIds, maskProductId)
			}
			info.MaskProductIds = maskProductIds
		}
		if v, ok := lineMap[data.GroupId]; ok {
			var lineIds []string
			for lineId := range v {
				lineIds = append(lineIds, lineId)
			}
			info.LineIds = lineIds
		}
		result[volumeZoneGroupKey(data.GroupId, data.RoutingType, data.IsForecastType)] = &info
	}
	return result, nil
}

func ProductVolumeRoutingRuleKey(productId int64, routingType int, isForecastType bool) string {
	return fmt.Sprintf("%d:%d:%t", productId, routingType, isForecastType)
}

func DumpProductVolumeRoutingRule() (map[string]interface{}, error) {
	var (
		ret                        = make(map[string]interface{})
		ctx                        = context.Background()
		volumeRuleList             = make([]*persistent.VolumeRoutingRuleTab, 0)
		volumeRoutingRuleCondition = map[string]interface{}{"rule_status = ?": enum.VolumeRuleStatusActive}
	)

	// 这里要取Priority的降序，当一个Product的有多个不同Priority的Rule时，Priority小的（更高优，1是最高优）要覆盖掉大的
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, volumeRoutingRuleCondition, &volumeRuleList, dbutil.WithOrder("priority DESC")); err != nil {
		return nil, srerr.With(srerr.ZoneRuleQueryFail, nil, err)
	}

	if len(volumeRuleList) == 0 {
		return ret, nil
	}

	for _, volumeRuleTab := range volumeRuleList {
		volumeRuleEntity, err := enrichVolumeRuleEntity(ctx, volumeRuleTab)
		if err != nil {
			logger.LogErrorf("enrich volume rule detail failed | ruleID=%d", volumeRuleTab.Id)
			return nil, err
		}

		productIdList := []int64{volumeRuleEntity.ProductId}
		if volumeRuleEntity.ShareVolume {
			productIdList = volumeRuleEntity.ProductIdList
		}
		for _, productId := range productIdList {
			ret[ProductVolumeRoutingRuleKey(productId, volumeRuleEntity.RoutingType, volumeRuleEntity.IsForecastType)] = volumeRuleEntity
		}
	}

	return ret, nil
}

func LineToVolumeGroupKey(lineID string, routingType int, isForecastType bool) string {
	return fmt.Sprintf("%s:%d:%t", lineID, routingType, isForecastType)
}

func DumpLineToVolumeGroup() (map[string]interface{}, error) {
	var (
		ret       = make(map[string]interface{})
		ctx       = context.Background()
		list      = make([]persistent.GroupProductLineRefTab, 0)
		condition = map[string]interface{}{"line_id != ?": ""} // 这里只取Line的Mapping关系
	)

	if err := dbutil.Select(ctx, persistent.GroupProductLineRefHook, condition, &list); err != nil {
		return ret, srerr.With(srerr.DatabaseErr, nil, err)
	}

	for _, lineRef := range list {
		ret[LineToVolumeGroupKey(lineRef.LineId, lineRef.RoutingType, lineRef.IsForecastType)] = lineRef.GroupId
	}

	return ret, nil
}
