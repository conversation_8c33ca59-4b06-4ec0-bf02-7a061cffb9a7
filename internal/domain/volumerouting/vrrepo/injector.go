package vrrepo

import (
	"github.com/google/wire"
)

var ZoneGroupRepoProvideSet = wire.NewSet(
	NewZoneGroupRepoImpl,
	wire.Bind(new(ZoneGroupRepo), new(*ZoneGroupRepoImpl)),
)

var ZoneRepoProviderSet = wire.NewSet(
	NewZoneRepoImpl,
	wire.Bind(new(ZoneRepo), new(*ZoneRepoImpl)),
)

var VolumeTaskRepoProviderSet = wire.NewSet(
	NewTaskRepoImpl,
	wire.Bind(new(TaskRepo), new(*TaskRepoImpl)),
)

var ZoneRuleRepoProviderSet = wire.NewSet(
	NewZoneRuleRepoImpl,
	wire.Bind(new(ZoneRuleRepo), new(*ZoneRuleRepoImpl)),
)

var DefaultSelectGroupRepoSet = wire.NewSet(
	NewDefaultSelectGroupRepoImpl,
	wire.Bind(new(DefaultSelectGroupRepo), new(*DefaultSelectGroupRepoImpl)),
)
