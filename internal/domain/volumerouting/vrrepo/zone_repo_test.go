package vrrepo

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler/scormhelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestZoneRepoImpl_CoverCepRangeZoneByGroupId(t *testing.T) {
	err := handler.RegisterScormHandler(
		scormhelper.WithScormV2(),
	)
	if err != nil {
		panic(err)
	}

	cErr := chassis.Init(
		chassis.WithChassisConfigPrefix("admin_server"),
	)

	if cErr != nil {
		panic(cErr)
	}

	confErr := configutil.Init()
	if confErr != nil {
		panic(confErr)
	}
	dbinitError := dbutil.InitAdminDb()
	if dbinitError != nil {
		panic(dbinitError)
	}

	//p := &ZoneRepoImpl{}
	//data := []persistent.VolumeZoneCepRangeTab{}
	//data = append(data, persistent.VolumeZoneCepRangeTab{
	//	GroupId:  "ddd",
	//	ZoneName: "ddd",
	//})
	//if got := p.CoverCepRangeZoneByGroupId(context.TODO(), "ddd", data); got != nil {
	//	panic(got)
	//}
	//repo := rulevolume.MaskRuleVolumeRepoImpl{}
	//startTime, err := repo.GetRuleVolumeByEffectiveStartTime(context.TODO(), 9100, recorder.Now(ctx).Unix())
	//if err != nil {
	//	panic(err)
	//}
	//
	//toString, jsonErr := jsoniter.MarshalToString(startTime)
	//if jsonErr != nil {
	//	panic(jsonErr)
	//}
	//
	//println(toString)

	println("success")
}
