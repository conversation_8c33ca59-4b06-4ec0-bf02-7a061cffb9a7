package vrrepo

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type DefaultSelectGroupRepo interface {
	ListByPage(ctx context.Context, condition map[string]interface{}, pageNo int64, pageSize int64) ([]*persistent.DefaultSelectGroupTab, int64, *srerr.Error)
	GetByParam(ctx context.Context, condition map[string]interface{}) (*persistent.DefaultSelectGroupTab, *srerr.Error)
	Create(ctx context.Context, defaultSelectGroupTab *persistent.DefaultSelectGroupTab) *srerr.Error
	Update(ctx context.Context, defaultSelectGroupTab *persistent.DefaultSelectGroupTab) *srerr.Error
	DeleteByParam(ctx context.Context, condition map[string]interface{}) *srerr.Error
	ListByParam(ctx context.Context, condition map[string]interface{}) ([]*persistent.DefaultSelectGroupTab, *srerr.Error)
}

type DefaultSelectGroupRepoImpl struct {
}

func NewDefaultSelectGroupRepoImpl() *DefaultSelectGroupRepoImpl {
	return &DefaultSelectGroupRepoImpl{}
}

func (d *DefaultSelectGroupRepoImpl) ListByPage(ctx context.Context, condition map[string]interface{}, pageNo int64, pageSize int64) ([]*persistent.DefaultSelectGroupTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.DefaultSelectGroupTabHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "get select group total error", err)
	}
	var data []*persistent.DefaultSelectGroupTab
	if err := dbutil.Select(ctx, persistent.DefaultSelectGroupTabHook, condition, &data, dbutil.WithPageByPageNo(pageNo, pageSize), dbutil.WithOrder("mtime desc")); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, "select select group by page error", err)
	}
	return data, total, nil
}

func (d *DefaultSelectGroupRepoImpl) GetByParam(ctx context.Context, condition map[string]interface{}) (*persistent.DefaultSelectGroupTab, *srerr.Error) {
	var defaultSelectGroupTab *persistent.DefaultSelectGroupTab
	if err := dbutil.Take(ctx, persistent.DefaultSelectGroupTabHook, condition, &defaultSelectGroupTab); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "get default select group error", err)
	}
	return defaultSelectGroupTab, nil
}

func (d *DefaultSelectGroupRepoImpl) Create(ctx context.Context, defaultSelectGroupTab *persistent.DefaultSelectGroupTab) *srerr.Error {
	if err := dbutil.Insert(ctx, defaultSelectGroupTab, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, "insert default select group error", err)
	}
	return nil
}

func (d *DefaultSelectGroupRepoImpl) Update(ctx context.Context, defaultSelectGroupTab *persistent.DefaultSelectGroupTab) *srerr.Error {
	if err, rows := dbutil.UpdateByObj(ctx, persistent.DefaultSelectGroupTabHook, map[string]interface{}{"id = ?": defaultSelectGroupTab.Id}, defaultSelectGroupTab, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, "update default select group error", err)
	} else if rows == 0 {
		return srerr.With(srerr.DatabaseErr, nil, errors.New("affected row is 0"))
	}
	return nil
}

func (d *DefaultSelectGroupRepoImpl) DeleteByParam(ctx context.Context, condition map[string]interface{}) *srerr.Error {
	if err := dbutil.Delete(ctx, persistent.DefaultSelectGroupTabHook, condition, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (d *DefaultSelectGroupRepoImpl) ListByParam(ctx context.Context, condition map[string]interface{}) ([]*persistent.DefaultSelectGroupTab, *srerr.Error) {
	var defaultSelectGroupTabList []*persistent.DefaultSelectGroupTab
	if err := dbutil.Select(ctx, persistent.DefaultSelectGroupTabHook, condition, &defaultSelectGroupTabList); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	return defaultSelectGroupTabList, nil
}
