package vrrepo

//func TestZoneRuleRepoImpl_UpdateRuleStatusByIDWithTx(t *testing.T) {
//	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
//	configutil.Init()
//	dbutil.Init()
//	z := &ZoneRuleRepoImpl{}
//	ctx := context.TODO()
//	//db, err := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
//	//if err != nil {
//	//	panic(err)
//	//}
//
//	//if err := z.UpdateRuleStatusByIDWithTx(ctx, db, 58, enum.VolumeRuleStatusSubmit); err != nil {
//	//	panic(err)
//	//}
//	flag, err := z.ChangeToVolumeManagement(ctx, 10000)
//	if err != nil {
//		panic(err)
//	}
//	println(flag)
//}
