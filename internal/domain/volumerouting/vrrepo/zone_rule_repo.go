package vrrepo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type ZoneRuleRepo interface {
	RulePage(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeRoutingRuleTab, int64, *srerr.Error)
	CreateZoneRule(ctx context.Context, data *persistent.VolumeRoutingRuleTab) (uint64, *srerr.Error)
	CreateZoneRuleDetails(ctx context.Context, list []persistent.VolumeRoutingRuleDetailTab) *srerr.Error
	QueryRuleByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeRoutingRuleTab, *srerr.Error)
	SaveZoneRule(ctx context.Context, rule *vrentity.VolumeRule, operator string) *srerr.Error
	DeleteRule(ctx context.Context, ruleId uint64) *srerr.Error
	DisableRule(ctx context.Context, ruleId uint64, operator string) *srerr.Error
	RuleDetailPage(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]persistent.VolumeRoutingRuleDetailTab, int64, *srerr.Error)
	QueryRuleDetailByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error)
	RefreshRuleDetailByRuleId(ctx context.Context, ruleId uint64, newData []*persistent.VolumeRoutingRuleDetailTab) *srerr.Error
	UpdateRuleStatusByID(ctx context.Context, ruleId uint64, ruleStatus enum.VolumeRuleStatus) *srerr.Error
	GetRuleByProduct(ctx context.Context, productId int64, routingType int, isForecastType bool) (*vrentity.VolumeRule, *srerr.Error)
	GetDraftRuleByProductId(ctx context.Context, productId int64) (*persistent.VolumeRoutingRuleTab, *srerr.Error)
	GetRuleWithZoneLimitByProduct(ctx context.Context, productId int64, routingType uint8, volumeRuleId int64) (*vrentity.VolumeRule, *srerr.Error)
	GetRuleZoneLimits(ctx context.Context, productId int64, ruleId uint64, dataVersion int64, groupId string, lineId, zoneName string, routingType int, isForecastType bool) (*vrentity.VolumeRuleDetail, *srerr.Error)
	DeleteForecastRuleByTaskId(ctx context.Context, taskId int) *srerr.Error
	SaveVolumeRule(ctx context.Context, rule *vrentity.VolumeRule, operator string) *srerr.Error
	UpdateRuleStatusByIDWithTx(ctx context.Context, tx scormv2.SQLCommon, ruleId uint64, ruleStatus enum.VolumeRuleStatus) *srerr.Error
	DeployVolumeRuleDetail(ctx context.Context, tx scormv2.SQLCommon, rule persistent.VolumeRoutingRuleTab) *srerr.Error
}

type ZoneRuleRepoImpl struct {
}

func NewZoneRuleRepoImpl() *ZoneRuleRepoImpl {
	return &ZoneRuleRepoImpl{}
}

func (p *ZoneRuleRepoImpl) RulePage(ctx context.Context, condition map[string]interface{}, offset, size int64) (
	[]persistent.VolumeRoutingRuleTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeRoutingRuleHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.ZoneRuleCountFail, nil, err)
	}
	var list []persistent.VolumeRoutingRuleTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, condition, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("ctime DESC")); err != nil {
		return nil, 0, srerr.With(srerr.ZoneRuleQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneRuleRepoImpl) CreateZoneRule(ctx context.Context, data *persistent.VolumeRoutingRuleTab) (uint64, *srerr.Error) {
	if err := dbutil.Insert(ctx, data, dbutil.ModelInfo{
		FulfillmentProductId: uint64(data.ProductId),
	}); err != nil {
		return 0, srerr.With(srerr.ZoneRuleCreateFail, nil, err)
	}
	return data.Id, nil
}

func (p *ZoneRuleRepoImpl) CreateZoneRuleDetails(ctx context.Context, list []persistent.VolumeRoutingRuleDetailTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if e := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).CreateInBatches(list, 1000).GetError(); e != nil {
			return srerr.With(srerr.ZoneRuleDetailInsertFail, nil, e)
		}
		return nil
	})

	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) QueryRuleByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeRoutingRuleTab, *srerr.Error) {
	var list []persistent.VolumeRoutingRuleTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.ZoneRuleQueryFail, nil, err)
	}
	return list, nil
}

func (p *ZoneRuleRepoImpl) SaveZoneRule(ctx context.Context, rule *vrentity.VolumeRule, operator string) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		updateModel := map[string]interface{}{
			"rule_name":             rule.RuleName,
			"rule_type":             rule.RuleType,
			"priority":              rule.Priority,
			"effective_start_time":  rule.EffectiveStartTime,
			"effective_immediately": rule.EffectiveImmediately,
			"rule_status":           rule.Status,
			"operator":              operator,
			"mtime":                 timeutil.GetCurrentUnixTimeStamp(ctx),
			"data_version":          rule.DataVersion,
		}
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).Where("id = ?", rule.RuleId).
			Updates(updateModel).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleUpdateFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).Delete(&persistent.VolumeRoutingRuleDetailTab{},
			"rule_id = ? AND zone_name = ? AND data_version = ?", rule.RuleId, "", rule.DataVersion).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleUpdateFail, nil, err)
		}
		var details []persistent.VolumeRoutingRuleDetailTab
		for _, lineLimit := range rule.LineLimits {
			details = append(details, persistent.VolumeRoutingRuleDetailTab{
				RuleId:             rule.RuleId,
				GroupId:            lineLimit.ZoneGroupId,
				ZoneName:           "",
				LineId:             lineLimit.LineId,
				LineType:           lineLimit.LineType,
				ComponentProductId: 0,
				Min:                lineLimit.Min,
				Max:                lineLimit.Max,
				MaxCod:             lineLimit.MaxCod,
				MaxBulky:           lineLimit.MaxBulky,
				MaxHighValue:       lineLimit.MaxHighValue,
				MaxDg:              lineLimit.MaxDg,
				Operator:           operator,
				DataVersion:        rule.DataVersion,
				RoutingType:        rule.RoutingType,
				IsForecastType:     rule.IsForecastType,
				ProductIdList:      lineLimit.ProductIdList,
			})
		}
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).CreateInBatches(details, 1000).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleUpdateFail, nil, err)
		}
		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) DeleteRule(ctx context.Context, ruleId uint64) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		var rule persistent.VolumeRoutingRuleTab
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).Take(&rule, "id = ?", ruleId).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleNotFound, nil, err)
		}
		if rule.RuleStatus != enum.VolumeRuleStatusDraft {
			return srerr.New(srerr.ZoneRuleCheckDeleteFail, nil, "only draft rule can delete")
		}
		if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).Delete(&persistent.VolumeRoutingRuleTab{},
			"id = ?", ruleId).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).Delete(&persistent.VolumeRoutingRuleDetailTab{},
			"rule_id = ?", ruleId).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDetailDeleteFail, nil, err)
		}

		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) DisableRule(ctx context.Context, ruleId uint64, operator string) *srerr.Error {
	var data persistent.VolumeRoutingRuleTab
	if err := dbutil.Take(ctx, persistent.VolumeRoutingRuleHook, map[string]interface{}{"id = ?": ruleId}, &data); err != nil {
		return srerr.New(srerr.ZoneRuleNotFound, nil, "zone rule(%d) not found", ruleId)
	}
	if data.RuleStatus != enum.VolumeRuleStatusActive && data.RuleStatus != enum.VolumeRuleStatusQueuing {
		return srerr.New(srerr.ZoneRuleCheckDisableFail, nil, "only queuing or active status can be disabled")
	}
	updateData := map[string]interface{}{
		"rule_status": enum.VolumeRuleStatusExpired,
		"operator":    operator,
		"mtime":       timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	if err := dbutil.Update(ctx, persistent.VolumeRoutingRuleHook, map[string]interface{}{"id = ?": ruleId}, updateData, dbutil.ModelInfo{RuleId: ruleId}); err != nil {
		return srerr.With(srerr.ZoneRuleDisableFail, nil, err)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) RuleDetailPage(ctx context.Context, condition map[string]interface{}, offset, size int64) (
	[]persistent.VolumeRoutingRuleDetailTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeRoutingRuleDetailHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.ZoneRuleDetailCountFail, nil, err)
	}
	var list []persistent.VolumeRoutingRuleDetailTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleDetailHook, condition, &list, dbutil.WithPage(offset, size)); err != nil {
		return nil, 0, srerr.With(srerr.ZoneRuleDetailQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *ZoneRuleRepoImpl) QueryRuleDetailByCondition(ctx context.Context, condition map[string]interface{}) ([]persistent.VolumeRoutingRuleDetailTab, *srerr.Error) {
	var list []persistent.VolumeRoutingRuleDetailTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleDetailHook, condition, &list); err != nil {
		return nil, srerr.With(srerr.ZoneRuleDetailQueryFail, nil, err)
	}
	return list, nil
}

func (p *ZoneRuleRepoImpl) RefreshRuleDetailByRuleId(ctx context.Context, ruleId uint64, newData []*persistent.VolumeRoutingRuleDetailTab) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleDetailHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).Delete(&persistent.VolumeRoutingRuleDetailTab{},
			`rule_id = ? AND zone_name <> ?`, ruleId, "").GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDetailDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).CreateInBatches(newData, 1000).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDetailInsertFail, nil, err)
		}
		return nil
	})

	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) UpdateRuleStatusByID(ctx context.Context, ruleId uint64, ruleStatus enum.VolumeRuleStatus) *srerr.Error {
	if err := dbutil.Update(ctx, persistent.VolumeRoutingRuleHook, map[string]interface{}{"id = ?": ruleId}, map[string]interface{}{"rule_status": ruleStatus}, dbutil.ModelInfo{
		RuleId: ruleId,
	}); err != nil {
		return srerr.With(srerr.ZoneRuleUpdateFail, nil, err)
	}
	return nil
}

func (p *ZoneRuleRepoImpl) GetRuleByProduct(ctx context.Context, productId int64, routingType int, isForecastType bool) (*vrentity.VolumeRule, *srerr.Error) {
	cacheKey := ProductVolumeRoutingRuleKey(productId, routingType, isForecastType)
	cacheVal, err := localcache.Get(ctx, constant.ProductVolumeRoutingRule, cacheKey)
	if err != nil {
		// 这里找不到只是不需要配或者没有配，属于正常的业务情况
		logger.CtxLogDebugf(ctx, "product volume rule not found")
		return nil, nil
	}

	volumeRule, ok := cacheVal.(*vrentity.VolumeRule)
	if !ok {
		logger.CtxLogErrorf(ctx, "convert cache value to volume rule failed")
		_ = monitor.AwesomeReportEvent(ctx, monitoring.VolumeManagementMonitor, monitoring.ProductVolumeRuleCacheErr, monitoring.StatusError, cacheKey)
		return nil, nil
	}

	return volumeRule, nil
}

func (p *ZoneRuleRepoImpl) GetDraftRuleByProductId(ctx context.Context, productId int64) (*persistent.VolumeRoutingRuleTab, *srerr.Error) {
	var list []*persistent.VolumeRoutingRuleTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, map[string]interface{}{
		"product_id = ?":  productId,
		"rule_status = ?": enum.VolumeRuleStatusDraft,
	}, &list); err != nil {
		return nil, srerr.With(srerr.ZoneRuleQueryFail, nil, err)
	}
	if len(list) == 0 {
		return nil, nil
	}
	return list[0], nil
}

func (p *ZoneRuleRepoImpl) DeleteForecastRuleByTaskId(ctx context.Context, taskId int) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		var ruleIdList []int64
		tx := scormv2.Context(ctx)
		if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).Where("task_id = ?", taskId).Select("id").Find(&ruleIdList).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleNotFound, nil, err)
		}

		if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).Delete(&persistent.VolumeRoutingRuleTab{},
			"task_id = ?", taskId).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDeleteFail, nil, err)
		}
		if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).Delete(&persistent.VolumeRoutingRuleDetailTab{},
			"rule_id in (?)", ruleIdList).GetError(); err != nil {
			return srerr.With(srerr.ZoneRuleDetailDeleteFail, nil, err)
		}

		return nil
	})
	if err != nil {
		return err.(*srerr.Error)
	}
	return nil
}

func (z *ZoneRuleRepoImpl) SaveVolumeRule(ctx context.Context, rule *vrentity.VolumeRule, operator string) *srerr.Error {
	db, dbErr := dbutil.MasterDB(ctx, persistent.VolumeRoutingRuleHook)
	if dbErr != nil {
		return srerr.With(srerr.DatabaseErr, nil, dbErr)
	}
	updateModel := map[string]interface{}{
		"rule_name":             rule.RuleName,
		"rule_type":             rule.RuleType,
		"priority":              rule.Priority,
		"effective_start_time":  rule.EffectiveStartTime,
		"effective_immediately": rule.EffectiveImmediately,
		"rule_status":           rule.Status,
		"operator":              operator,
		"mtime":                 timeutil.GetCurrentUnixTimeStamp(ctx),
		"data_version":          rule.DataVersion,
		"is_forecast_type":      rule.IsForecastType,
	}
	if err := db.Table(persistent.VolumeRoutingRuleHook.TableName()).Where("id = ?", rule.RuleId).
		Updates(updateModel).GetError(); err != nil {
		return srerr.With(srerr.ZoneRuleUpdateFail, nil, err)
	}
	return nil
}

func (z *ZoneRuleRepoImpl) UpdateRuleStatusByIDWithTx(ctx context.Context, tx scormv2.SQLCommon, ruleId uint64, ruleStatus enum.VolumeRuleStatus) *srerr.Error {
	if err := tx.Table(persistent.VolumeRoutingRuleHook.TableName()).
		Where("id = ?", ruleId).Updates(map[string]interface{}{"rule_status": ruleStatus}).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (z *ZoneRuleRepoImpl) DeployVolumeRuleDetail(ctx context.Context, tx scormv2.SQLCommon, rule persistent.VolumeRoutingRuleTab) *srerr.Error {
	if err := tx.Table(persistent.VolumeRoutingRuleDetailHook.TableName()).Where("rule_id = ? and data_version = ?", rule.Id, rule.DataVersion).Updates(map[string]interface{}{"is_forecast_type": constant.NonForecastType}).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}
