package vrrepo

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type TaskRepo interface {
	CreateNewTask(ctx context.Context, data *persistent.VolumeTaskRecordTab) *srerr.Error
	GetTask(ctx context.Context, taskId string) (*persistent.VolumeTaskRecordTab, *srerr.Error)
	UpdateByTaskId(ctx context.Context, taskId string, updateData map[string]interface{}) *srerr.Error
	Page(ctx context.Context, offset, size int64, routingType int) ([]persistent.VolumeTaskRecordTab, int64, *srerr.Error)
	QueryCondition(ctx context.Context, condition map[string]interface{}) ([]*persistent.VolumeTaskRecordTab, *srerr.Error)
}

type TaskRepoImpl struct {
}

func NewTaskRepoImpl() *TaskRepoImpl {
	return &TaskRepoImpl{}
}

func (p *TaskRepoImpl) CreateNewTask(ctx context.Context, data *persistent.VolumeTaskRecordTab) *srerr.Error {
	if err := dbutil.Insert(ctx, data, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.VolumeTaskCreateFail, nil, err)
	}
	return nil
}

func (p *TaskRepoImpl) GetTask(ctx context.Context, taskId string) (*persistent.VolumeTaskRecordTab, *srerr.Error) {
	var ret persistent.VolumeTaskRecordTab
	if err := dbutil.Take(ctx, persistent.VolumeTaskRecordHook, map[string]interface{}{"task_id = ?": taskId}, &ret); err != nil {
		return nil, srerr.With(srerr.VolumeTaskQueryFail, nil, err)
	}
	return &ret, nil
}

func (p *TaskRepoImpl) UpdateByTaskId(ctx context.Context, taskId string, updateData map[string]interface{}) *srerr.Error {
	if err := dbutil.Update(ctx, persistent.VolumeTaskRecordHook, map[string]interface{}{"task_id = ?": taskId}, updateData, dbutil.ModelInfo{}); err != nil {
		return srerr.With(srerr.VolumeTaskUpdateFail, nil, err)
	}
	return nil
}

func (p *TaskRepoImpl) Page(ctx context.Context, offset, size int64, routingType int) ([]persistent.VolumeTaskRecordTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, persistent.VolumeTaskRecordHook, map[string]interface{}{
		constant.RoutingTypeSql: routingType,
	}, &total); err != nil {
		return nil, 0, srerr.With(srerr.VolumeTaskQueryFail, nil, err)
	}
	var list []persistent.VolumeTaskRecordTab
	if err := dbutil.Select(ctx, persistent.VolumeTaskRecordHook, map[string]interface{}{
		constant.RoutingTypeSql: routingType,
	}, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("mtime DESC")); err != nil {
		return nil, 0, srerr.With(srerr.VolumeTaskQueryFail, nil, err)
	}
	return list, total, nil
}

func (p *TaskRepoImpl) QueryCondition(ctx context.Context, condition map[string]interface{}) ([]*persistent.VolumeTaskRecordTab, *srerr.Error) {
	var list []*persistent.VolumeTaskRecordTab
	if err := dbutil.Select(ctx, persistent.VolumeTaskRecordHook, condition, &list, dbutil.WithOrder("id asc")); err != nil {
		return nil, srerr.With(srerr.VolumeTaskQueryFail, nil, err)
	}
	return list, nil
}
