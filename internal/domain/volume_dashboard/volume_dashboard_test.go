package volume_dashboard

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"
)

func TestVolumeDashboard(t *testing.T) {
	str := "0.1234"
	ratioFloat, _ := strconv.ParseFloat(str, 64)
	newStr := fmt.Sprintf("%.2f%%", ratioFloat*100)
	fmt.Println(newStr)
}

func TestVolumeDashboard1(t *testing.T) {
	ctx := context.TODO()
	nowTime := time.Now()
	str1 := GetCarveTime(ctx, nowTime)
	str2 := GetCarveTime(ctx, nowTime.Add(15*time.Minute))
	str3 := GetCarveTime(ctx, nowTime.Add(30*time.Minute))
	str4 := GetCarveTime(ctx, nowTime.Add(45*time.Minute))
	fmt.Println(str1, str2, str3, str4)
}
