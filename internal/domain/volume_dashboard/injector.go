package volume_dashboard

import "github.com/google/wire"

var MaskingVolumeServiceProviderSet = wire.NewSet(
	NewMaskingVolumeServiceImpl,
	wire.Bind(new(MaskingVolumeService), new(*MaskingVolumeServiceImpl)),
)

var RoutingVolumeServiceProviderSet = wire.NewSet(
	NewRoutingVolumeServiceImpl,
	wire.Bind(new(RoutingVolumeService), new(*RoutingVolumeServiceImpl)),
)

var VolumeChangeServiceProviderSet = wire.NewSet(
	NewVolumeChangeServiceImpl,
	wire.Bind(new(VolumeChangeService), new(*VolumeChangeServiceImpl)),
)
