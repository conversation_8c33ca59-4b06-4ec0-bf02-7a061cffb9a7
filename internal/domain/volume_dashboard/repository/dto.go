package repository

type SaveOrderNumParam struct {
	MaskProductOrderNum           []*MaskingProductOrderNumTab `json:"mask_product_order_num"`
	FulfillmentProductOrderNum    []*MaskingProductOrderNumTab `json:"fulfillment_product_order_num"`
	GroupCodeOrderNum             []*MaskingProductOrderNumTab `json:"group_code_order_num"`
	ZoneCodeOrderNum              []*MaskingProductOrderNumTab `json:"zone_code_order_num"`
	RouteCodeOrderNum             []*MaskingProductOrderNumTab `json:"route_code_order_num"`
	ShopGroupOrderNum             []*MaskingProductOrderNumTab `json:"shop_group_order_num"`
	DelMaskProductOrderNum        []int64                      `json:"del_mask_product_order_num"`
	DelFulfillmentProductOrderNum []int64                      `json:"del_fulfillment_product_order_num"`
	DelGroupCodeOrderNum          []string                     `json:"del_group_code_order_num"`
	DelZoneCodeOrderNum           []string                     `json:"del_zone_code_order_num"`
	DelRouteCodeOrderNum          []string                     `json:"del_route_code_order_num"`
	DelShopGroupOrderNum          []int64                      `json:"del_shop_group_order_num"`
	StatTime                      string                       `json:"stat_time"`
	MergeDeleteIds                []uint64                     `json:"merge_delete_ids"`
	MergeSubDeleteIds             []uint64                     `json:"merge_sub_delete_ids"`
}
