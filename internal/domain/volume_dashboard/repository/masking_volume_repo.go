package repository

import (
	"context"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

type MaskProductOrderNumRepo interface {
	SaveOrderNum(ctx context.Context, saveOrderNumParam *SaveOrderNumParam) error
	ListByParam(ctx context.Context, param map[string]interface{}, useNewTable bool) ([]*MaskingProductOrderNumTab, error)
	GetDeleteListByParam(ctx context.Context, today string) ([]*MaskingProductOrderNumTab, error)
	DeleteAndInsert(ctx context.Context, startDay, endDay, tableName string, maskingVolumeOrderNumTabList []*MaskingProductOrderNumTab) *srerr.Error
	GetMaskingProductOrderNum(ctx context.Context, param map[string]interface{}) (*MaskingProductOrderNumTab, error)
	MergeOrderNum(ctx context.Context, yesterday string, mergeOrderNumList []*MaskingProductOrderNumTab, mergeSubOrderNumList []*MaskingProductOrderNumTab) error
	ClearMaskingVolumeDataByTime(ctx context.Context, deadlineTime string, deadlineSubTime string) error
}

type MaskProductOrderNumRepoImpl struct {
}

func NewMaskProductOrderNumRepoImpl() *MaskProductOrderNumRepoImpl {
	return &MaskProductOrderNumRepoImpl{}
}

func (m *MaskProductOrderNumRepoImpl) deleteRecords(ctx context.Context, tx scormv2.SQLCommon, tableName string, condition map[string]interface{}) error {
	var rowAffected int64 = 1
	deleteNum := 0
	for rowAffected != 0 {
		query := tx.Table(tableName)
		for k, v := range condition {
			query = query.Where(k, v)
		}
		result := query.Limit(dbutil.DefaultDeleteLimit).Delete(&MaskingProductOrderNumTab{})
		if result.GetError() != nil {
			return result.GetError()
		}
		rowAffected = result.RowsAffected()
		deleteNum++
		if deleteNum > dbutil.DefaultMaxDeleteNum {
			monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
			break
		}
	}
	return nil
}

func (m *MaskProductOrderNumRepoImpl) insertRecords(tx scormv2.SQLCommon, records []*MaskingProductOrderNumTab, tableName string) error {
	if len(records) == 0 {
		return nil
	}
	return tx.Table(tableName).CreateInBatches(records, 100).GetError()
}

func (m *MaskProductOrderNumRepoImpl) processInt64Records(ctx context.Context, tx scormv2.SQLCommon, statType uint8, fieldName string, delItems []int64, newItems []*MaskingProductOrderNumTab, tableName, statTime string) error {
	if len(delItems) > 0 {
		// 这里会把MPL跟WMS的都删掉（但在report-get运力时已经做了逻辑处理，所以这里无差别删掉没什么问题）
		if err := m.deleteRecords(ctx, tx, tableName, map[string]interface{}{
			"stat_type = ?":       statType,
			"stat_time = ?":       statTime,
			fieldName + " in (?)": delItems,
		}); err != nil {
			return err
		}
	}
	return m.insertRecords(tx, newItems, tableName)
}

func (m *MaskProductOrderNumRepoImpl) processStringRecords(ctx context.Context, tx scormv2.SQLCommon, statType uint8, fieldName string, delItems []string, newItems []*MaskingProductOrderNumTab, tableName, statTime string) error {
	if len(delItems) > 0 {
		if err := m.deleteRecords(ctx, tx, tableName, map[string]interface{}{
			"stat_type = ?":       statType,
			"stat_time = ?":       statTime,
			fieldName + " in (?)": delItems,
		}); err != nil {
			return err
		}
	}
	return m.insertRecords(tx, newItems, tableName)
}

func (m *MaskProductOrderNumRepoImpl) processMergeDeleteIds(ctx context.Context, tx scormv2.SQLCommon, mergeDeleteIds []uint64, tableName string) error {
	idsToDelete := mergeDeleteIds
	for len(idsToDelete) > 0 {
		batchSize := dbutil.DefaultDeleteLimit
		if len(idsToDelete) > batchSize {
			if err := m.deleteRecords(ctx, tx, tableName, map[string]interface{}{
				"id in (?)": idsToDelete[:batchSize],
			}); err != nil {
				return err
			}
			idsToDelete = idsToDelete[batchSize:]
		} else {
			if err := m.deleteRecords(ctx, tx, tableName, map[string]interface{}{
				"id in (?)": idsToDelete,
			}); err != nil {
				return err
			}
			idsToDelete = nil
		}
	}
	return nil
}

func (m *MaskProductOrderNumRepoImpl) processMainTableProducts(ctx context.Context, tx scormv2.SQLCommon, param *SaveOrderNumParam, tableName string) error {
	// 处理masking product运力
	if err := m.processInt64Records(
		ctx, tx,
		constant.MaskingProductVolumeStat,
		"mask_product_id",
		param.DelMaskProductOrderNum,
		param.MaskProductOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理fulfillment product运力
	if err := m.processInt64Records(
		ctx, tx,
		constant.FulfillmentProductVolumeStat,
		"fulfillment_product_id",
		param.DelFulfillmentProductOrderNum,
		param.FulfillmentProductOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理group code运力
	if err := m.processStringRecords(
		ctx, tx,
		constant.GroupCodeVolumeStat,
		"group_code",
		param.DelGroupCodeOrderNum,
		param.GroupCodeOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	return nil
}

func (m *MaskProductOrderNumRepoImpl) processMainTableRouteZoneShop(ctx context.Context, tx scormv2.SQLCommon, param *SaveOrderNumParam, tableName string) error {
	zoneCodeOrderNum, routeCodeOrderNum, shopGroupOrderNum := param.ZoneCodeOrderNum, param.RouteCodeOrderNum, param.ShopGroupOrderNum
	// 处理route code运力
	if err := m.processStringRecords(
		ctx, tx,
		constant.RouteCodeVolumeStat,
		"route_code",
		param.DelRouteCodeOrderNum,
		routeCodeOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理zone code运力
	if err := m.processStringRecords(
		ctx, tx,
		constant.ZoneCodeVolumeStat,
		"zone_code",
		param.DelZoneCodeOrderNum,
		zoneCodeOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理shop group运力
	if err := m.processInt64Records(
		ctx, tx,
		constant.ShopGroupVolumeStat,
		"shop_group_id",
		param.DelShopGroupOrderNum,
		shopGroupOrderNum,
		tableName,
		param.StatTime,
	); err != nil {
		return err
	}

	return nil
}

func (m *MaskProductOrderNumRepoImpl) processDateTable(ctx context.Context, tx scormv2.SQLCommon, param *SaveOrderNumParam, dateTable string) error {
	// 处理MergeSubDeleteIds
	if len(param.MergeSubDeleteIds) > 0 {
		if err := m.deleteRecords(ctx, tx, dateTable, map[string]interface{}{
			"id in (?)": param.MergeSubDeleteIds,
		}); err != nil {
			return err
		}
	}
	for _, orderNumTab := range param.ZoneCodeOrderNum {
		orderNumTab.ID = 0
	}
	for _, orderNumTab := range param.RouteCodeOrderNum {
		orderNumTab.ID = 0
	}
	for _, orderNumTab := range param.ShopGroupOrderNum {
		orderNumTab.ID = 0
	}

	// 处理route code
	if err := m.processStringRecords(
		ctx, tx,
		constant.RouteCodeVolumeStat,
		"route_code",
		param.DelRouteCodeOrderNum,
		param.RouteCodeOrderNum,
		dateTable,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理zone code
	if err := m.processStringRecords(
		ctx, tx,
		constant.ZoneCodeVolumeStat,
		"zone_code",
		param.DelZoneCodeOrderNum,
		param.ZoneCodeOrderNum,
		dateTable,
		param.StatTime,
	); err != nil {
		return err
	}

	// 处理shop group
	if err := m.processInt64Records(
		ctx, tx,
		constant.ShopGroupVolumeStat,
		"shop_group_id",
		param.DelShopGroupOrderNum,
		param.ShopGroupOrderNum,
		dateTable,
		param.StatTime,
	); err != nil {
		return err
	}

	return nil
}

func (m *MaskProductOrderNumRepoImpl) SaveOrderNum(ctx context.Context, saveOrderNumParam *SaveOrderNumParam) error {
	db, err := dbutil.MasterDB(ctx, MaskingProductOrderNumTabHook)
	if err != nil {
		return err
	}
	ctx = scormv2.BindContext(ctx, db)

	return scormv2.PropagationRequired(ctx, func(ctx context.Context) error {
		tx := scormv2.Context(ctx)

		// 主表名称
		mainTable := MaskingProductOrderNumTabHook.TableName()

		// 处理MergeDeleteIds
		if err := m.processMergeDeleteIds(ctx, tx, saveOrderNumParam.MergeDeleteIds, mainTable); err != nil {
			return err
		}

		// 处理主表中的Product数据
		if err := m.processMainTableProducts(ctx, tx, saveOrderNumParam, mainTable); err != nil {
			return err
		}

		// 如果未切换，同时处理在主表中的zone、route、shopGroup数据
		if !configutil.GetMaskingDashboardSwitch(ctx) {
			if err := m.processMainTableRouteZoneShop(ctx, tx, saveOrderNumParam, mainTable); err != nil {
				return err
			}
		}

		// 处理日期子表数据
		dateTable := MaskingProductOrderNumTabHook.TableNameByDate(saveOrderNumParam.StatTime)
		if err := m.processDateTable(ctx, tx, saveOrderNumParam, dateTable); err != nil {
			return err
		}

		return nil
	})
}

func (m *MaskProductOrderNumRepoImpl) ListByParam(ctx context.Context, param map[string]interface{}, useNewTable bool) ([]*MaskingProductOrderNumTab, error) {
	var orderNumTabList, orderNumTabListTemp []*MaskingProductOrderNumTab
	_, ok := param["stat_type in (?)"]
	// 目前如果传进param["stat_type in (?)"], 都是使用旧表，如果有改动需要注意这个逻辑是否需要变动
	if !useNewTable || param["stat_type = ?"] == constant.MaskingProductVolumeStat ||
		param["stat_type = ?"] == constant.FulfillmentProductVolumeStat || param["stat_type = ?"] == constant.GroupCodeVolumeStat || ok {
		if err := dbutil.Select(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabList); err != nil {
			return nil, err
		}
		return orderNumTabList, nil
	}

	if value, ok := param["stat_time = ?"]; ok {
		// 定时任务查询
		dateString, success := value.(string)
		if !success {
			return nil, srerr.New(srerr.DataErr, value, "param stat_time is not string")
		}
		if err := dbutil.SelectWithTableName(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabList,
			MaskingProductOrderNumTabHook.TableNameByDate(dateString)); err != nil {
			return nil, err
		}
	} else {
		valueStart, okStart := param["stat_time >= ?"]
		valueEnd, okEnd := param["stat_time <= ?"]
		if !okEnd {
			// SyncMaskingVolume 同步新旧表数据查询使用条件
			valueEnd, okEnd = param["stat_time < ?"]
		}
		if okStart && okEnd {
			// admin链路, 定时任务查询
			dateStringStart, success := valueStart.(string)
			if !success {
				return nil, srerr.New(srerr.DataErr, valueStart, "param stat_time is not string")
			}
			dateStringEnd, success := valueEnd.(string)
			if !success {
				return nil, srerr.New(srerr.DataErr, valueEnd, "param stat_time is not string")
			}
			tableNameList := MaskingProductOrderNumTabHook.TableNameListByDate(dateStringStart, dateStringEnd)
			for _, tableName := range tableNameList {
				if err := dbutil.SelectWithTableName(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabListTemp, tableName); err != nil {
					return nil, err
				}
				orderNumTabList = append(orderNumTabList, orderNumTabListTemp...)
			}
		}
	}
	return orderNumTabList, nil
}

func (m *MaskProductOrderNumRepoImpl) GetDeleteListByParam(ctx context.Context, today string) ([]*MaskingProductOrderNumTab, error) {
	var deleteList []*MaskingProductOrderNumTab
	var orderNumTabList []*MaskingProductOrderNumTab

	// delete today zone
	param := map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.ZoneCodeVolumeStat,
	}
	if err := dbutil.SelectWithTableName(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabList,
		MaskingProductOrderNumTabHook.TableNameByDate(today)); err != nil {
		return nil, err
	}
	deleteList = append(deleteList, orderNumTabList...)

	// delete today route
	param = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.RouteCodeVolumeStat,
	}
	if err := dbutil.SelectWithTableName(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabList,
		MaskingProductOrderNumTabHook.TableNameByDate(today)); err != nil {
		return nil, err
	}
	deleteList = append(deleteList, orderNumTabList...)

	// delete today shop group
	param = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.ShopGroupVolumeStat,
	}
	if err := dbutil.SelectWithTableName(ctx, MaskingProductOrderNumTabHook, param, &orderNumTabList,
		MaskingProductOrderNumTabHook.TableNameByDate(today)); err != nil {
		return nil, err
	}
	deleteList = append(deleteList, orderNumTabList...)
	return deleteList, nil
}

func (m *MaskProductOrderNumRepoImpl) DeleteAndInsert(ctx context.Context, startDay, endDay, tableName string, maskingVolumeOrderNumTabList []*MaskingProductOrderNumTab) *srerr.Error {
	var orderNumTab *MaskingProductOrderNumTab
	var minId, maxId uint64
	if err := dbutil.MinIdWithTableName(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"stat_time >= ?": startDay, "stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat}},
		&orderNumTab, tableName); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	minId = orderNumTab.ID

	orderNumTab = nil
	if err := dbutil.MaxIdWithTableName(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"stat_time < ?": endDay, "stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat}},
		&orderNumTab, tableName); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	maxId = orderNumTab.ID

	// 插入操作
	db, err := dbutil.MasterDB(ctx, MaskingProductOrderNumTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	if err1 := db.Table(tableName).CreateInBatches(maskingVolumeOrderNumTabList, 100).GetError(); err1 != nil {
		return srerr.With(srerr.DatabaseErr, nil, err1)
	}

	// 删除操作
	if err1 := dbutil.DeleteBatchWithTableName(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"id >= ?": minId, "id <= ?": maxId,
		"stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat},
		"stat_time >= ?":   startDay, "stat_time < ?": endDay},
		dbutil.ModelInfo{}, tableName); err1 != nil {
		// 这里删除失败的话，不能通过重新执行debug接口来解决，因为已经插入数据了，再次查询出的maxId会变，导致删除的数据会多。
		logger.CtxLogErrorf(ctx, "delete in batches error, err=%v, minId=%d, maxId=%d", err1, minId, maxId)
		return srerr.With(srerr.DatabaseErr, nil, err1)
	}
	return nil
}

func (m *MaskProductOrderNumRepoImpl) GetMaskingProductOrderNum(ctx context.Context, param map[string]interface{}) (*MaskingProductOrderNumTab, error) {
	var orderNumTab *MaskingProductOrderNumTab
	err := dbutil.First(ctx, MaskingProductOrderNumTabHook, param, &orderNumTab)
	// record not found不返回报错
	if err != nil && err != scormv2.ErrRecordNotFound {
		return nil, err
	}
	return orderNumTab, nil
}

func (m *MaskProductOrderNumRepoImpl) MergeOrderNum(ctx context.Context, yesterday string, mergeOrderNumList []*MaskingProductOrderNumTab,
	mergeSubOrderNumList []*MaskingProductOrderNumTab) error {
	db, err := dbutil.MasterDB(ctx, MaskingProductOrderNumTabHook)
	if err != nil {
		return err
	}
	ctx = scormv2.BindContext(ctx, db)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		// 删除昨天的数据 =》因为report会每隔5分钟merge一下今天的数据，因此每次更新的时候，先清理掉旧数据
		deleteNum := 0

		for {
			var loopCompleted = true
			result := tx.Table(MaskingProductOrderNumTabHook.TableName()).Where("stat_time = ?", yesterday).Limit(dbutil.DefaultDeleteLimit).Delete(&MaskingProductOrderNumTab{})
			if result.GetError() != nil {
				return result.GetError()
			}
			if rows := result.RowsAffected(); rows > 0 {
				loopCompleted = false
			}
			if !loopCompleted {
				deleteNum += 1
				if deleteNum > dbutil.DefaultMaxDeleteNum {
					monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
					break
				}
				continue
			}

			loopCompleted = true
			result = tx.Table(MaskingProductOrderNumTabHook.TableNameByDate(yesterday)).Where("stat_time = ?", yesterday).Limit(dbutil.DefaultDeleteLimit).Delete(&MaskingProductOrderNumTab{})
			if result.GetError() != nil {
				return result.GetError()
			}
			if rows := result.RowsAffected(); rows > 0 {
				loopCompleted = false
			}
			if !loopCompleted {
				deleteNum += 1
				if deleteNum > dbutil.DefaultMaxDeleteNum {
					monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
					break
				}
			}

			if loopCompleted {
				break
			}
		}

		if len(mergeOrderNumList) > 0 {
			if err1 := tx.Table(MaskingProductOrderNumTabHook.TableName()).CreateInBatches(mergeOrderNumList, 100).GetError(); err1 != nil {
				return err1
			}
		}
		if len(mergeSubOrderNumList) > 0 {
			if err1 := tx.Table(MaskingProductOrderNumTabHook.TableNameByDate(yesterday)).CreateInBatches(mergeSubOrderNumList, 100).GetError(); err1 != nil {
				return err1
			}
		}
		return nil
	})
	return err
}

func (m *MaskProductOrderNumRepoImpl) ClearMaskingVolumeDataByTime(ctx context.Context, deadlineTime string, deadlineSubTime string) error {
	time90, err := time.Parse(VolumeDashboardDayFormat, deadlineTime)
	if err != nil {
		return err
	}
	time30, err := time.Parse(VolumeDashboardDayFormat, deadlineSubTime)
	if err != nil {
		return err
	}
	statTime90, statTime30 := time90.AddDate(0, 0, 1).Format(VolumeDashboardDayFormat), time30.AddDate(0, 0, 1).Format(VolumeDashboardDayFormat)

	var orderNumTab *MaskingProductOrderNumTab
	if !configutil.GetMaskingDashboardSwitch(ctx) {
		// 旧表30天数据的清理
		if err := dbutil.MaxId(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"stat_time < ?": statTime30,
			"stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat}},
			&orderNumTab); err != nil {
			logger.CtxLogErrorf(ctx, "ClearMaskingVolumeDataByTime30|get max id error, err=%v", err)
			return err
		}
		if orderNumTab == nil {
			logger.CtxLogInfof(ctx, "ClearMaskingVolumeDataByTime30|get max id result is nil")
			return nil
		}
		if err := dbutil.DeleteBatch(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"id <= ?": orderNumTab.ID,
			"stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat},
			"stat_time < ?":    statTime30},
			dbutil.ModelInfo{}); err != nil {
			return err
		}
	}

	// 90天数据的清理
	orderNumTab = nil
	if err := dbutil.MaxId(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"stat_time < ?": statTime90}, &orderNumTab); err != nil {
		logger.CtxLogErrorf(ctx, "ClearMaskingVolumeDataByTime90|get max id error, err=%v", err)
		return err
	}
	if orderNumTab == nil {
		logger.CtxLogInfof(ctx, "ClearMaskingVolumeDataByTime90|get max id result is nil")
		return nil
	}
	if err := dbutil.DeleteBatch(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"id <= ?": orderNumTab.ID, "stat_time < ?": statTime90}, dbutil.ModelInfo{}); err != nil {
		return err
	}

	// 新表30天数据的清理
	orderNumTab = nil
	if err := dbutil.MaxIdWithTableName(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"stat_time < ?": statTime30},
		&orderNumTab, MaskingProductOrderNumTabHook.TableNameByDate(deadlineSubTime)); err != nil {
		logger.CtxLogErrorf(ctx, "ClearMaskingVolumeDataByTime30|get max id error, err=%v", err)
		return err
	}
	if err := dbutil.DeleteBatchWithTableName(ctx, MaskingProductOrderNumTabHook, map[string]interface{}{"id <= ?": orderNumTab.ID,
		"stat_time < ?": statTime30}, dbutil.ModelInfo{}, MaskingProductOrderNumTabHook.TableNameByDate(deadlineSubTime)); err != nil {
		return err
	}

	return nil
}
