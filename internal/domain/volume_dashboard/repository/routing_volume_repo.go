package repository

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
)

var (
	hourList = []string{"00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24"}
)

type RoutingVolumeRepo interface {
	SaveOrderNum(ctx context.Context, routingProductOrderNumTabList []*RoutingProductOrderNumTab, minuteTime string, dayTime string) error
	ListLatestDataByTime(ctx context.Context, startTime string, endTime string) ([]*RoutingProductOrderNumTab, error)
	MergeOrderNum(ctx context.Context, routingProductOrderNumTabList []*RoutingProductOrderNumTab, startTime string, endTime string) error
	ClearRoutingVolumeDataByTime(ctx context.Context, deadlineTime string) error
	ListByParam(ctx context.Context, param map[string]interface{}) ([]*RoutingProductOrderNumTab, error)
	ListByParamAndTimeType(ctx context.Context, param map[string]interface{}, startTime, endTime string, timeType int8) ([]*RoutingProductOrderNumTab, error)
}

type RoutingVolumeRepoImpl struct {
}

func NewRoutingVolumeRepoImpl() *RoutingVolumeRepoImpl {
	return &RoutingVolumeRepoImpl{}
}

func (r *RoutingVolumeRepoImpl) SaveOrderNum(ctx context.Context, routingProductOrderNumTabList []*RoutingProductOrderNumTab, minuteTime string, dayTime string) error {
	db, err := dbutil.MasterDB(ctx, RoutingProductOrderNumTabHook)
	if err != nil {
		return err
	}
	ctx = scormv2.BindContext(ctx, db)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		// 先删除旧数据
		if minuteTime != "" {
			var rowAffected int64 = 1
			deleteNum := 0
			for rowAffected != 0 {
				result := tx.Table(RoutingProductOrderNumTabHook.TableName()).Where("stat_time = ?", minuteTime).Limit(dbutil.DefaultDeleteLimit).Delete(&RoutingProductOrderNumTab{})
				if result.GetError() != nil {
					return result.GetError()
				}
				rowAffected = result.RowsAffected()
				deleteNum += 1
				if deleteNum > dbutil.DefaultMaxDeleteNum {
					monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
					break
				}
			}
		}
		if dayTime != "" {
			var rowAffected int64 = 1
			deleteNum := 0
			for rowAffected != 0 {
				result := tx.Table(RoutingProductOrderNumTabHook.TableName()).Where("stat_time = ?", dayTime).Limit(dbutil.DefaultDeleteLimit).Delete(&RoutingProductOrderNumTab{})
				if result.GetError() != nil {
					return result.GetError()
				}
				rowAffected = result.RowsAffected()
				deleteNum += 1
				if deleteNum > dbutil.DefaultMaxDeleteNum {
					monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
					break
				}
			}
		}
		// 再插入新数据
		if len(routingProductOrderNumTabList) > 0 {
			if err1 := tx.Table(RoutingProductOrderNumTabHook.TableName()).CreateInBatches(routingProductOrderNumTabList, 1000).GetError(); err1 != nil {
				return err1
			}
		}
		return nil
	})
	return err
}

func (r *RoutingVolumeRepoImpl) ListLatestDataByTime(ctx context.Context, startTime string, endTime string) ([]*RoutingProductOrderNumTab, error) {
	var routingProductOrderNumTabList []*RoutingProductOrderNumTab
	db, err := dbutil.SlaveDB(ctx, RoutingProductOrderNumTabHook)
	if err != nil {
		return nil, err
	}
	err = db.Table(RoutingProductOrderNumTabHook.TableName()).Select("product_id, fm_line, mm_line, lm_line, site_id, actual_point_id, zone_code, max(order_num) as order_num, max(stat_time) as stat_time").Where("stat_time >= ? and stat_time <= ?", startTime, endTime).Group("product_id, fm_line, mm_line, lm_line, site_id, actual_point_id, zone_code").Find(&routingProductOrderNumTabList).GetError()
	if err != nil {
		return nil, err
	}
	return routingProductOrderNumTabList, nil
}

func (r *RoutingVolumeRepoImpl) MergeOrderNum(ctx context.Context, routingProductOrderNumTabList []*RoutingProductOrderNumTab, startTime string, endTime string) error {
	db, err := dbutil.MasterDB(ctx, RoutingProductOrderNumTabHook)
	if err != nil {
		return err
	}
	ctx = scormv2.BindContext(ctx, db)
	err = scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		if startTime != "" && endTime != "" {
			var rowAffected int64 = 1
			deleteNum := 0
			for rowAffected != 0 {
				t := tx.Table(RoutingProductOrderNumTabHook.TableName()).Where("stat_time >= ? and stat_time <= ?", startTime, endTime).Limit(dbutil.DefaultDeleteLimit).Delete(&RoutingProductOrderNumTab{})
				if t.GetError() != nil {
					return t.GetError()
				}
				rowAffected = t.RowsAffected()
				deleteNum += 1
				if deleteNum > dbutil.DefaultMaxDeleteNum {
					monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.ExceedMaxDeleteNum, "exceed max delete num")
					break
				}
			}
		}
		if len(routingProductOrderNumTabList) > 0 {
			if err1 := tx.Table(RoutingProductOrderNumTabHook.TableName()).CreateInBatches(routingProductOrderNumTabList, 1000).GetError(); err1 != nil {
				return err1
			}
		}
		return nil
	})
	return err
}

func (r *RoutingVolumeRepoImpl) ClearRoutingVolumeDataByTime(ctx context.Context, deadlineTime string) error {
	var orderNumTab *RoutingProductOrderNumTab
	err := dbutil.MaxId(ctx, RoutingProductOrderNumTabHook, map[string]interface{}{"stat_time <= ?": deadlineTime}, &orderNumTab)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearRoutingVolumeDataByTime|get max id error,err=%v", err)
		return err
	}
	if orderNumTab == nil {
		logger.CtxLogInfof(ctx, "ClearRoutingVolumeDataByTime|get max id result is nil")
		return nil
	}
	err = dbutil.DeleteBatch(ctx, RoutingProductOrderNumTabHook, map[string]interface{}{"id <= ?": orderNumTab.ID}, dbutil.ModelInfo{})
	if err != nil {
		return err
	}
	return nil
}

func (r *RoutingVolumeRepoImpl) ListByParam(ctx context.Context, param map[string]interface{}) ([]*RoutingProductOrderNumTab, error) {
	var orderNumTabList []*RoutingProductOrderNumTab
	if err := dbutil.Select(ctx, RoutingProductOrderNumTabHook, param, &orderNumTabList); err != nil {
		return nil, err
	}
	return orderNumTabList, nil
}

func (r *RoutingVolumeRepoImpl) ListByParamAndTimeType(ctx context.Context, param map[string]interface{}, startTime, endTime string, timeType int8) ([]*RoutingProductOrderNumTab, error) {
	var totalOrderNumTabList []*RoutingProductOrderNumTab
	// timeType为3时statTime格式为********，endTime格式为************
	if timeType == 3 {
		// 由于minute统计的数据太大，因此可按分钟拆分查询，每个小时查询一次
		for i := 0; i < len(hourList)-1; i++ {
			var minuteTimeParamList []string
			minuteTimeParamList = append(minuteTimeParamList, startTime+hourList[i]+"15")
			minuteTimeParamList = append(minuteTimeParamList, startTime+hourList[i]+"30")
			minuteTimeParamList = append(minuteTimeParamList, startTime+hourList[i]+"45")
			minuteTimeParamList = append(minuteTimeParamList, startTime+hourList[i+1]+"00")
			param["stat_time in (?)"] = minuteTimeParamList
			// 数据量太大，一次查询db会超时，这里最多查询24次db，且是admin接口，接口时延可以接受
			orderNumTabList, err := r.ListByParam(ctx, param)
			if err != nil {
				return nil, err
			}
			totalOrderNumTabList = append(totalOrderNumTabList, orderNumTabList...)
			if endTime < (startTime + hourList[i+1] + "00") {
				break
			}
		}
	}
	return totalOrderNumTabList, nil
}
