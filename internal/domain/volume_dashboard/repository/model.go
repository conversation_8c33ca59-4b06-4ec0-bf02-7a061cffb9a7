package repository

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

const (
	maskingProductOrderNumTab        = "masking_product_order_num_tab"
	maskingProductOrderNumTabPattern = "masking_product_order_num_tab_%08d"
	routingProductOrderNumTab        = "routing_product_order_num_tab"
)

const (
	VolumeDashboardDayFormat  = "********"
	VolumeDashboardHourFormat = "********15"
	VolumeDashboardHourStart  = "**********"
)

const (
	DefaultClearTime    = 90
	DefaultSubClearTime = 30
)

var MaskingProductOrderNumTabHook = &MaskingProductOrderNumTab{}

type MaskingProductOrderNumTab struct {
	ID                   uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	MaskProductId        int64  `gorm:"column:mask_product_id;NOT NULL"`
	FulfillmentProductId int64  `gorm:"column:fulfillment_product_id"`
	GroupCode            string `gorm:"column:group_code"`
	ZoneCode             string `gorm:"column:zone_code"`
	RouteCode            string `gorm:"column:route_code"`
	ShopGroupId          string `gorm:"column:shop_group_id"`
	StatType             uint8  `gorm:"column:stat_type;NOT NULL"`
	StatTime             string `gorm:"column:stat_time"`
	OrderNum             int64  `gorm:"column:order_num"`
	BusinessType         uint8  `gorm:"column:business_type"`
	Ctime                int64  `gorm:"column:ctime;NOT NULL"`
	Mtime                int64  `gorm:"column:mtime;NOT NULL"`
}

func NewMaskingProductOrderNumTab(ID uint64, maskProductId int64, fulfillmentProductId int64, groupCode string, zoneCode string, routeCode string, shopGroupId string, statType uint8, statTime string, orderNum int64, businessType uint8, ctime int64, mtime int64) *MaskingProductOrderNumTab {
	return &MaskingProductOrderNumTab{ID: ID, MaskProductId: maskProductId, FulfillmentProductId: fulfillmentProductId, GroupCode: groupCode, ZoneCode: zoneCode, RouteCode: routeCode, ShopGroupId: shopGroupId, StatType: statType, StatTime: statTime, OrderNum: orderNum, BusinessType: businessType, Ctime: ctime, Mtime: mtime}
}

func (m *MaskingProductOrderNumTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        m.ID,
		ModelName: m.TableName(),
	}
}

func (m *MaskingProductOrderNumTab) TableName() string {
	return maskingProductOrderNumTab
}

func (m *MaskingProductOrderNumTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (m *MaskingProductOrderNumTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (m *MaskingProductOrderNumTab) TableNameByDate(date string) string {
	_, ok := m.validateDate(date, date)
	if !ok {
		logger.LogErrorf("parsing date error: dateStart=%s, dateEnd=%s", date, date)
		return ""
	}
	num, err := strconv.Atoi(date[6:8])
	if err != nil {
		return ""
	}
	return fmt.Sprintf(maskingProductOrderNumTabPattern, num)
}

func (m *MaskingProductOrderNumTab) validateDate(dateStart, dateEnd string) (string, bool) {
	if len(dateStart) != len(dateEnd) {
		return "", false
	}
	if dateStart > dateEnd {
		return "", false
	}

	var Format string
	if len(dateStart) == len(VolumeDashboardDayFormat) {
		Format = VolumeDashboardDayFormat
	} else if len(dateStart) == len(VolumeDashboardHourFormat) {
		Format = VolumeDashboardHourFormat
	} else {
		return "", false
	}
	_, err := timeutil.ParseLocalTime(Format, dateStart)
	_, err1 := timeutil.ParseLocalTime(Format, dateEnd)
	if err != nil || err1 != nil {
		return "", false
	}
	return Format, true
}

func (m *MaskingProductOrderNumTab) TableNameListByDate(dateStart, dateEnd string) []string {
	_, ok := m.validateDate(dateStart, dateEnd)
	if !ok {
		logger.LogErrorf("parsing date error: dateStart=%s, dateEnd=%s", dateStart, dateEnd)
		return nil
	}

	numStart, err := strconv.Atoi(dateStart[6:8])
	if err != nil {
		return nil
	}
	numEnd, err := strconv.Atoi(dateEnd[6:8])
	if err != nil {
		return nil
	}

	var result []string
	if numStart > numEnd {
		for i := numStart; i <= 31; i++ {
			result = append(result, fmt.Sprintf(maskingProductOrderNumTabPattern, i))
		}
		for i := 1; i <= numEnd; i++ {
			result = append(result, fmt.Sprintf(maskingProductOrderNumTabPattern, i))
		}
	}
	for i := numStart; i <= numEnd; i++ {
		result = append(result, fmt.Sprintf(maskingProductOrderNumTabPattern, i))
	}
	return result
}

var RoutingProductOrderNumTabHook = &RoutingProductOrderNumTab{}

type RoutingProductOrderNumTab struct {
	ID            uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ProductId     int64  `gorm:"column:product_id;NOT NULL"`
	FmLine        string `gorm:"column:fm_line"`
	MmLine        string `gorm:"column:mm_line"`
	LmLine        string `gorm:"column:lm_line"`
	SiteId        string `gorm:"column:site_id"`
	ActualPointId string `gorm:"column:actual_point_id"`
	ZoneCode      string `gorm:"column:zone_code"`
	StatTime      string `gorm:"column:stat_time"`
	TimeType      uint8  `gorm:"column:time_type"`
	OrderNum      int64  `gorm:"column:order_num"`
	Ctime         int64  `gorm:"column:ctime;NOT NULL"`
	Mtime         int64  `gorm:"column:mtime;NOT NULL"`
}

func (r *RoutingProductOrderNumTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:        r.ID,
		ModelName: r.TableName(),
	}
}

func (r *RoutingProductOrderNumTab) TableName() string {
	return routingProductOrderNumTab
}

func (r *RoutingProductOrderNumTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (r *RoutingProductOrderNumTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}
