package volume_dashboard

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	DayFormat                       = "********"
	DayExhibitFormat                = "2006-01-02"
	HourFormat                      = "**********"
	HourExhibitFormat               = "2006-01-02 15"
	MinuteFormat                    = "**********04"
	MinuteExhibitFormat             = "2006-01-02 15:04"
	DefaultRoutingClearDay          = 30
	MergeKeyPattern                 = "%v-%v"
	FilterNumLimit                  = 3
	TitleJoiner                     = "--"
	HistoryOrderNumExportSheet      = "Historical_sheet1"
	HistoryDistributionExportSheet  = "Historical_sheet2"
	RealTimeOrderNumExportSheet     = "real_time_sheet1"
	RealTimeDistributionExportSheet = "real_time_sheet2"
	SystemLimitTitle                = "System Limit"
	ConsumptionRate                 = "Consumption Rate"
	DayRequest                      = 1
	HourRequest                     = 2
)

type AttrInfo struct {
	Name   string
	Entity int
}

type RoutingVolumeService interface {
	ReportRoutingVolume(ctx context.Context) *srerr.Error
	MergeRoutingVolume(ctx context.Context, mergeDate string) *srerr.Error
	ClearRoutingVolume(ctx context.Context, clearDay string) *srerr.Error
	SearchByDay(ctx context.Context, request *admin_protocol.SearchByDayRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error)
	SearchByHour(ctx context.Context, request *admin_protocol.SearchByHourRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error)
	ExportByDay(ctx context.Context, request *admin_protocol.SearchByDayRequest) (string, *srerr.Error)
	ExportByHour(ctx context.Context, request *admin_protocol.SearchByHourRequest) (string, *srerr.Error)
	GetRoutingDict(ctx context.Context) (*admin_protocol.RoutingDictResp, *srerr.Error)
	GetRoutingProductRelateDict(ctx context.Context, request *admin_protocol.RoutingProductRelateDictRequest) ([]*admin_protocol.RoutingProductRelateDictResp, *srerr.Error)
}

type RoutingVolumeServiceImpl struct {
	LaneSrv             lane.LaneService
	LpsApi              lpsclient.LpsApi
	VolumeCounter       volume_counter.VolumeCounter
	RoutingVolumeRepo   repository.RoutingVolumeRepo
	ExportTaskRepo      export_task.ExportTaskRepo
	volumeChangeService VolumeChangeService
}

func NewRoutingVolumeServiceImpl(laneSrv lane.LaneService, lpsApi lpsclient.LpsApi, volumeCounter volume_counter.VolumeCounter, routingVolumeRepo repository.RoutingVolumeRepo, exportTaskRepo export_task.ExportTaskRepo, volumeChangeService VolumeChangeService) *RoutingVolumeServiceImpl {
	return &RoutingVolumeServiceImpl{
		LaneSrv:             laneSrv,
		LpsApi:              lpsApi,
		VolumeCounter:       volumeCounter,
		RoutingVolumeRepo:   routingVolumeRepo,
		ExportTaskRepo:      exportTaskRepo,
		volumeChangeService: volumeChangeService,
	}
}

func (r *RoutingVolumeServiceImpl) ReportRoutingVolume(ctx context.Context) *srerr.Error {
	nowTime := GetRoutingStatTime(ctx)
	carveTime := GetCarveTime(ctx, nowTime)
	dayTime := timeutil.FormatDateTimeByFormat(nowTime, DayFormat)
	// 查询所有的product、laneCode信息
	laneCodeInfos, err := r.LpsApi.GetLaneCodes(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetAllProductLaneCodeRefs fail|err=%v", err)
		return err
	}
	// 查询所有的zoneCode列表
	zoneCodeMap, err := r.GetZoneCodeList(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get zone code error, err=%v", err)
		return err
	}
	var zoneCodeList []string
	for zoneCode := range zoneCodeMap {
		zoneCodeList = append(zoneCodeList, zoneCode)
	}
	zoneCodeList = append(zoneCodeList, "")
	var routingProductOrderNumTabList []*repository.RoutingProductOrderNumTab
	for _, laneCodeInfo := range laneCodeInfos {
		productId := laneCodeInfo.ProductId
		// 根据productId判断是否走新的zone运力计数，changeToVolumeManagement为true表示走新的zone运力，为false表示走旧的逻辑
		changeToVolumeManagement, zoneRuleErr := r.volumeChangeService.ChangeToVolumeManagement(ctx, int64(productId))
		if zoneRuleErr != nil {
			logger.CtxLogErrorf(ctx, "get change to volume management info error, err=%v", zoneRuleErr)
			monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.GetVolumeManagementError, fmt.Sprintf("get change to volume management info error, err=%v", zoneRuleErr))
		}
		for _, laneCode := range laneCodeInfo.LaneCodes {
			// 批量请求redis大小
			batchGetNum := configutil.GetRoutingDashboardRedisConcurrency(ctx)
			// 查询laneCode名下的line信息
			laneInfo, err2 := r.LaneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
			if err2.Cause() == localcache.ErrKeyNotFound {
				continue
			}
			if err2 != nil {
				return err2
			}
			if laneInfo == nil {
				continue
			}
			var fmLine, mmLine, lmLine string
			for _, lineInfo := range laneInfo.Lines {
				lineType := lfslib.GetLineCategory(lineInfo.SubType)
				switch lineType {
				case lfslib.FM:
					fmLine = lineInfo.LineID
				case lfslib.MM:
					mmLine = lineInfo.LineID
				case lfslib.LM:
					lmLine = lineInfo.LineID
				}
			}
			siteIdMap := make(map[string]interface{})
			actualPointIdMap := make(map[string]interface{})
			for _, siteInfo := range laneInfo.Sites {
				siteIdMap[siteInfo.SiteID] = nil
				for _, actualPointInfo := range siteInfo.ActualPointMap {
					actualPointIdMap[actualPointInfo.ActualPointID] = nil
				}
			}
			siteIdMap[""] = nil
			actualPointIdMap[""] = nil
			var keyList []volume_counter.RoutingVolumeDashboardKey
			// 以laneCode维度做去重，减少redis访问量
			checkDuplicateMap := make(map[string]interface{})
			for siteId := range siteIdMap {
				for actualPointId := range actualPointIdMap {
					for _, zoneCode := range zoneCodeList {
						key := fmt.Sprintf("%v|%v|%v|%v|%v|%v|%v", productId, fmLine, mmLine, lmLine, siteId, actualPointId, zoneCode)
						if _, ok := checkDuplicateMap[key]; ok {
							continue
						}
						checkDuplicateMap[key] = nil
						keyList = append(keyList, volume_counter.RoutingVolumeDashboardKey{
							ProductId:     int64(productId),
							FmLine:        fmLine,
							MmLine:        mmLine,
							LmLine:        lmLine,
							SiteId:        siteId,
							ActualPointId: actualPointId,
							ZoneCode:      zoneCode,
							StatTime:      nowTime.Unix(),
						})
						if len(keyList)%batchGetNum == 0 {
							tempRoutingProductOrderNumTabList, err := r.GetDashboardVolumeFromRedis(ctx, keyList, changeToVolumeManagement, carveTime, dayTime)
							if err != nil {
								return err
							}
							routingProductOrderNumTabList = append(routingProductOrderNumTabList, tempRoutingProductOrderNumTabList...)
							keyList = make([]volume_counter.RoutingVolumeDashboardKey, 0, batchGetNum)
						}
					}
				}
			}
			// 兜底处理未满1000条key的数据
			if len(keyList) != 0 {
				tempRoutingProductOrderNumTabList, err := r.GetDashboardVolumeFromRedis(ctx, keyList, changeToVolumeManagement, carveTime, dayTime)
				if err != nil {
					return err
				}
				routingProductOrderNumTabList = append(routingProductOrderNumTabList, tempRoutingProductOrderNumTabList...)
			}
		}
	}
	// 对待保存的数据做一个去重
	duplicateOrderNumTabMap := make(map[string]struct{})
	var newRoutingProductOrderNumTabList []*repository.RoutingProductOrderNumTab
	for _, routingProductOrderNumTab := range routingProductOrderNumTabList {
		key := fmt.Sprintf("%d|%s|%s|%s|%s|%s|%s|%s|%d", routingProductOrderNumTab.ProductId, routingProductOrderNumTab.FmLine, routingProductOrderNumTab.MmLine, routingProductOrderNumTab.LmLine, routingProductOrderNumTab.SiteId, routingProductOrderNumTab.ActualPointId, routingProductOrderNumTab.ZoneCode, routingProductOrderNumTab.StatTime, routingProductOrderNumTab.TimeType)
		if _, ok := duplicateOrderNumTabMap[key]; ok {
			continue
		}
		duplicateOrderNumTabMap[key] = struct{}{}
		newRoutingProductOrderNumTabList = append(newRoutingProductOrderNumTabList, routingProductOrderNumTab)
	}
	// 保存新的routing运力，依据统计时间删除旧的运力
	err4 := r.RoutingVolumeRepo.SaveOrderNum(ctx, newRoutingProductOrderNumTabList, carveTime, dayTime)
	if err4 != nil {
		return srerr.With(srerr.DatabaseErr, "save routing volume error", err4)
	}
	return nil
}

func (r *RoutingVolumeServiceImpl) GetDashboardVolumeFromRedis(ctx context.Context, keyList []volume_counter.RoutingVolumeDashboardKey, changeToVolumeManagement bool, carveTime string, dayTime string) ([]*repository.RoutingProductOrderNumTab, *srerr.Error) {
	var (
		volumeList                    []int64
		err                           *srerr.Error
		routingProductOrderNumTabList []*repository.RoutingProductOrderNumTab
	)
	if changeToVolumeManagement {
		volumeList, err = r.VolumeCounter.BatchGetZoneDashboardVolume(ctx, keyList)
	} else {
		volumeList, err = r.VolumeCounter.BatchGetDashboardVolume(ctx, keyList)
	}
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.RoutingDashboardMGetError, fmt.Sprintf("mget error, err=%v", err))
		return nil, err
	}
	if len(keyList) != len(volumeList) {
		monitoring.ReportError(ctx, monitoring.CatRoutingDashboardMonitor, monitoring.MGetKeyAndValueInconsistent, fmt.Sprintf("the length of key and value are inconsistent, keyLen: %v, valueLen: %v", len(keyList), len(volumeList)))
		return nil, srerr.New(srerr.CodisErr, nil, "the length of key and value are inconsistent")
	}
	for idx, volume := range volumeList {
		// volume为0表示key不存在，不需要保存到db
		if volume == 0 {
			continue
		}
		// 保存分钟级数据
		minuteRoutingProductOrderNumTab := &repository.RoutingProductOrderNumTab{
			ProductId:     keyList[idx].ProductId,
			FmLine:        keyList[idx].FmLine,
			MmLine:        keyList[idx].MmLine,
			LmLine:        keyList[idx].LmLine,
			SiteId:        keyList[idx].SiteId,
			ActualPointId: keyList[idx].ActualPointId,
			ZoneCode:      keyList[idx].ZoneCode,
			StatTime:      carveTime,
			TimeType:      StatMinuteType,
			OrderNum:      volume,
			Ctime:         keyList[idx].StatTime,
			Mtime:         keyList[idx].StatTime,
		}
		routingProductOrderNumTabList = append(routingProductOrderNumTabList, minuteRoutingProductOrderNumTab)
		// 保存以天为维度的数据
		dayRoutingProductOrderNumTab := &repository.RoutingProductOrderNumTab{
			ProductId:     keyList[idx].ProductId,
			FmLine:        keyList[idx].FmLine,
			MmLine:        keyList[idx].MmLine,
			LmLine:        keyList[idx].LmLine,
			SiteId:        keyList[idx].SiteId,
			ActualPointId: keyList[idx].ActualPointId,
			ZoneCode:      keyList[idx].ZoneCode,
			StatTime:      dayTime,
			TimeType:      StatDayType,
			OrderNum:      volume,
			Ctime:         keyList[idx].StatTime,
			Mtime:         keyList[idx].StatTime,
		}
		routingProductOrderNumTabList = append(routingProductOrderNumTabList, dayRoutingProductOrderNumTab)
	}
	return routingProductOrderNumTabList, nil
}

func (r *RoutingVolumeServiceImpl) MergeRoutingVolume(ctx context.Context, mergeDate string) *srerr.Error {
	// 如果没有传入参数默认合并昨天的数据
	if mergeDate == "" {
		mergeDate = timeutil.FormatDateTimeByFormat(timeutil.GetYesterday(ctx), VolumeDashboardDayFormat)
	}
	// query the data to be merged
	startTime := mergeDate + "0000"
	endTime := mergeDate + "2400"
	routingVolumeOrderNumTabList, err := r.RoutingVolumeRepo.ListLatestDataByTime(ctx, startTime, endTime)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, "get latest data error", err)
	}
	// The latest data is the order volume calculated on a daily basis.
	nowTime := timeutil.GetLocalTime(ctx).Unix()
	for _, routingVolumeOrderNumTab := range routingVolumeOrderNumTabList {
		// time modify to date(********)
		routingVolumeOrderNumTab.StatTime = mergeDate
		routingVolumeOrderNumTab.Ctime = nowTime
		routingVolumeOrderNumTab.Mtime = nowTime
		routingVolumeOrderNumTab.TimeType = StatDayType
	}
	// save merged data and delete hour data
	err = r.RoutingVolumeRepo.MergeOrderNum(ctx, routingVolumeOrderNumTabList, mergeDate, endTime)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, "save merge data error", err)
	}
	return nil
}

func (r *RoutingVolumeServiceImpl) ClearRoutingVolume(ctx context.Context, clearDay string) *srerr.Error {
	var deadlineDay int64
	if clearDay != "" {
		clearTime, err := strconv.ParseInt(clearDay, 10, 64)
		if err != nil {
			return srerr.With(srerr.JsonErr, nil, err)
		}
		deadlineDay = clearTime
	} else {
		deadlineDay = DefaultRoutingClearDay
	}
	// 清除清理时间前的数据
	deadlineTime := timeutil.FormatDateTimeByFormat(timeutil.AddDays(timeutil.GetLocalTime(ctx), int(deadlineDay*-1)), VolumeDashboardDayFormat)
	err := r.RoutingVolumeRepo.ClearRoutingVolumeDataByTime(ctx, deadlineTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearScheduleCount|clear data error, db error: %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (r *RoutingVolumeServiceImpl) SearchByDay(ctx context.Context, request *admin_protocol.SearchByDayRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	return r.GetDaySearchData(ctx, request)
}

func (r *RoutingVolumeServiceImpl) SearchByHour(ctx context.Context, request *admin_protocol.SearchByHourRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	return r.GetHourSearchData(ctx, request)
}

func (r *RoutingVolumeServiceImpl) ExportByDay(ctx context.Context, request *admin_protocol.SearchByDayRequest) (string, *srerr.Error) {
	//create task record
	operator, _ := apiutil.GetUserInfo(ctx)
	createReq := masking_panel.CreateExportTaskReq{
		TaskStatus:        enum.Success,
		TaskType:          enum.Download,
		TaskBusinessScene: enum.RoutingVolumePanel,
		LastOperateTime:   timeutil.GetCurrentUnixTimeStamp(ctx),
		LastOperator:      operator,
	}
	// 查询统计结果
	resp, err := r.GetDaySearchData(ctx, request)
	var url string
	// 查询统计结果成功
	if err == nil {
		// 导出按天统计的数据
		url, err = r.exportRoutingVolume(ctx, resp, HistoryOrderNumExportSheet, HistoryDistributionExportSheet)
	}
	if err != nil {
		createReq.TaskStatus = enum.Failed
		createReq.ErrorMessage = err.GetMessage()
	}
	createReq.DownloadUrl = url
	if _, err1 := r.ExportTaskRepo.CreateTaskRecord(ctx, createReq); err1 != nil {
		logger.CtxLogErrorf(ctx, "create task record err:%v", err1)
		return "", err1
	}
	return url, err
}

func (r *RoutingVolumeServiceImpl) ExportByHour(ctx context.Context, request *admin_protocol.SearchByHourRequest) (string, *srerr.Error) {
	//create task record
	operator, _ := apiutil.GetUserInfo(ctx)
	createReq := masking_panel.CreateExportTaskReq{
		TaskStatus:        enum.Success,
		TaskType:          enum.Download,
		TaskBusinessScene: enum.RoutingVolumePanel,
		LastOperateTime:   timeutil.GetCurrentUnixTimeStamp(ctx),
		LastOperator:      operator,
	}
	// 查询按小时统计的结果
	resp, err := r.GetHourSearchData(ctx, request)
	var url string
	// 查询统计结果成功
	if err == nil {
		// 导出按小时统计的数据
		url, err = r.exportRoutingVolume(ctx, resp, RealTimeOrderNumExportSheet, RealTimeDistributionExportSheet)
	}
	if err != nil {
		createReq.TaskStatus = enum.Failed
		createReq.ErrorMessage = err.GetMessage()
	}
	createReq.DownloadUrl = url
	if _, err1 := r.ExportTaskRepo.CreateTaskRecord(ctx, createReq); err1 != nil {
		logger.CtxLogErrorf(ctx, "create task record err:%v", err1)
		return "", err1
	}
	return url, err
}

func (r *RoutingVolumeServiceImpl) GetRoutingDict(ctx context.Context) (*admin_protocol.RoutingDictResp, *srerr.Error) {
	filterList, productFilterInfo, err := r.GetDictValue(ctx, 0)
	if err != nil {
		return nil, err
	}
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  ProductStatType,
		FilterName:  GetStatTypeName(ProductStatType),
		FilterValue: productFilterInfo,
	})
	resp := &admin_protocol.RoutingDictResp{
		FilterList: filterList,
		Product:    productFilterInfo,
	}
	return resp, nil
}

func (r *RoutingVolumeServiceImpl) GetRoutingProductRelateDict(ctx context.Context, request *admin_protocol.RoutingProductRelateDictRequest) ([]*admin_protocol.RoutingProductRelateDictResp, *srerr.Error) {
	filterList, _, err := r.GetDictValue(ctx, request.ProductId)
	if err != nil {
		return nil, err
	}
	var resp []*admin_protocol.RoutingProductRelateDictResp
	for _, filter := range filterList {
		resp = append(resp, &admin_protocol.RoutingProductRelateDictResp{
			FilterType:  filter.FilterType,
			FilterName:  filter.FilterName,
			FilterValue: filter.FilterValue,
		})
	}
	return resp, nil
}

// GetStatTime 获取统计时间。根据saturn cron表达式，每天最后一次定时任务执行是在下一天的00:00执行，无法获取到当天最后的数据。这里做个判断，如果5分钟前是前一天，则表明是最后一次执行，返回5分钟前的时间
func GetRoutingStatTime(ctx context.Context) time.Time {
	nowTime := timeutil.GetLocalTime(ctx)
	beforeTime := nowTime.Add(BeforeDayTime)
	nowTimeStr := timeutil.FormatDateTimeByFormat(nowTime, DayFormat)
	beforeTimeStr := timeutil.FormatDateTimeByFormat(beforeTime, DayFormat)
	if nowTimeStr != beforeTimeStr {
		return beforeTime
	}
	return nowTime
}

func (r *RoutingVolumeServiceImpl) GetDaySearchData(ctx context.Context, request *admin_protocol.SearchByDayRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	condition := buildSearchByDayCondition(ctx, request)
	routingProductOrderNumTabList, err := r.RoutingVolumeRepo.ListByParam(ctx, condition)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "search routing order num error", err)
	}
	// 构建返回结果
	return r.buildResponse(ctx, request, nil, routingProductOrderNumTabList, DayRequest)
}

func (r *RoutingVolumeServiceImpl) GetHourSearchData(ctx context.Context, request *admin_protocol.SearchByHourRequest) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	if len(request.FilterList) > FilterNumLimit {
		return nil, srerr.New(srerr.ParamErr, nil, "filter num can not exceed 3")
	}
	condition := buildSearchByHourCondition(ctx, request)
	// 获取当前时间
	currentTime := timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), MinuteFormat)
	startTime := timeutil.FormatDateTimeByFormat(timeutil.GetLocalTime(ctx), DayFormat)
	routingProductOrderNumTabList, err := r.RoutingVolumeRepo.ListByParamAndTimeType(ctx, condition, startTime, currentTime, StatMinuteType)
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "search routing order num error", err)
	}
	resp, err1 := r.buildResponse(ctx, nil, request, routingProductOrderNumTabList, HourRequest)
	if err1 != nil {
		return nil, err1
	}
	return r.AddSystemLimit(ctx, request, resp)
}

// AddSystemLimit 只有Fm、Lm、Lm+zone_code三种筛选项时才需要增加volume system max limit限制
func (r *RoutingVolumeServiceImpl) AddSystemLimit(ctx context.Context, request *admin_protocol.SearchByHourRequest, resp *admin_protocol.RoutingSearchDataResp) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	// 判断返回结果是否为空，如果为空，则不需要查询system limit
	if resp == nil || len(resp.TotalOrder) == 0 {
		return resp, nil
	}
	if (len(request.FilterList) == 1 && (request.FilterList[0].FilterType == FmLineStatType || request.FilterList[0].FilterType == LmLineStatType)) ||
		(len(request.FilterList) == 2 && ((request.FilterList[0].FilterType == LmLineStatType && request.FilterList[1].FilterType == ZoneCodeStatType) || (request.FilterList[0].FilterType == ZoneCodeStatType && request.FilterList[1].FilterType == LmLineStatType))) {
		// 只有Fm、Lm、Lm+zone_code三种筛选项时才需要增加volume system max limit限制
		systemLimitMap, err := r.GetVolumeSystemLimit(ctx, request)
		if err != nil {
			return nil, err
		}
		// 查询fm或lm的system_limit，system_limit有两种限制，一个是routing_rule限制，一个是volume_routing_rule限制
		systemLimit := admin_protocol.RoutingTotalOrder{
			Title: SystemLimitTitle,
		}
		systemConsumptionRate := admin_protocol.RoutingTotalOrder{
			Title: ConsumptionRate,
		}
		var systemLimitValueList []string
		var consumptionRateValueList []string
		for _, title := range resp.TotalOrder[0].Value {
			// total没有system_limit限制
			if title == AggregationTotal {
				continue
			}
			systemLimitValueList = append(systemLimitValueList, strconv.FormatInt(systemLimitMap[title], 10))
			consumptionRateValueList = append(consumptionRateValueList, "0")
		}
		systemLimit.Value = systemLimitValueList
		systemConsumptionRate.Value = consumptionRateValueList
		for idx, systemLimitValue := range systemLimitValueList {
			if systemLimitValue == "0" {
				continue
			}
			systemLimitValueInt, _ := strconv.ParseInt(systemLimitValue, 10, 64)
			lastOrderNumInt, _ := strconv.ParseInt(resp.TotalOrder[len(resp.TotalOrder)-1].Value[idx], 10, 64)
			consumptionRateValueList[idx] = fmt.Sprintf("%.4f", float64(lastOrderNumInt)/float64(systemLimitValueInt))
		}
		resp.TotalOrder = append(resp.TotalOrder, systemLimit)
		resp.TotalOrder = append(resp.TotalOrder, systemConsumptionRate)
	}
	return resp, nil
}

func (r *RoutingVolumeServiceImpl) GetVolumeSystemLimit(ctx context.Context, request *admin_protocol.SearchByHourRequest) (map[string]int64, *srerr.Error) {
	systemLimitMap := make(map[string]int64)
	// 查询routing_rule信息
	var routingRuleTab *ruledata.RoutingRuleTab
	cond := map[string]interface{}{
		"product_id = ?":            request.ProductId,
		"rule_status = ?":           rule.RuleStatusActive,
		"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx),
	}
	if err := dbutil.Select(ctx, ruledata.RoutingRuleHook, cond, &routingRuleTab, dbutil.WithOrder("priority ASC")); err != nil {
		return nil, srerr.New(srerr.DatabaseErr, nil, "get routing rule tab error, err=%v", err)
	}
	if routingRuleTab != nil && routingRuleTab.RuleDetail != nil {
		for _, lineRule := range routingRuleTab.RuleDetail.Rules {
			// 未开启最大运力则跳过
			if !lineRule.MaxCapacityEnable {
				continue
			}
			for _, lineLimit := range lineRule.LineLimit {
				systemLimitMap[lineLimit.LineId] = int64(lineLimit.MaxCapacity)
			}
		}
	}
	// 如果开启了v2运力，则还需要查询v2的运力限制
	if routing.CheckSwitchToVolumeCapacityV2(ctx, request.ProductId, "") {
		var volumeRoutingRuleTab *persistent.VolumeRoutingRuleTab
		cond1 := map[string]interface{}{
			"product_id = ?":            request.ProductId,
			"rule_type = ?":             enum.VolumeRuleTypeLineVolumeZone,
			"rule_status = ?":           enum.VolumeRuleStatusActive,
			"effective_start_time <= ?": timeutil.GetCurrentUnixTimeStamp(ctx),
		}
		if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, cond1, &volumeRoutingRuleTab, dbutil.WithOrder("priority ASC")); err != nil {
			return nil, srerr.New(srerr.DatabaseErr, nil, "get volume routing rule tab error, err=%v", err)
		}
		if volumeRoutingRuleTab != nil {
			// 查询volume rule详情
			var volumeRoutingRuleDetailTabList []*persistent.VolumeRoutingRuleDetailTab
			cond2 := map[string]interface{}{
				"rule_id = ?": volumeRoutingRuleTab.Id,
			}
			if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleDetailHook, cond2, &volumeRoutingRuleDetailTabList); err != nil {
				return nil, srerr.New(srerr.DatabaseErr, nil, "get volume routing rule detail tab error, err=%v", err)
			}
			for _, volumeRoutingRuleDetailTab := range volumeRoutingRuleDetailTabList {
				// max capacity为0或默认运力时跳过
				if volumeRoutingRuleDetailTab.Max == 0 || volumeRoutingRuleDetailTab.Max == rulevolume.DefaultMaskMaxCapacity {
					continue
				}
				var key string
				for _, filter := range request.FilterList {
					if filter.FilterType == FmLineStatType || filter.FilterType == LmLineStatType {
						key = key + TitleJoiner + volumeRoutingRuleDetailTab.LineId
					}
					if filter.FilterType == ZoneCodeStatType {
						key = key + TitleJoiner + volumeRoutingRuleDetailTab.ZoneName
					}
				}
				systemLimitMap[key[len(TitleJoiner):]] = volumeRoutingRuleDetailTab.Max
			}
		}
	}
	return systemLimitMap, nil
}

func (r *RoutingVolumeServiceImpl) GetDictValue(ctx context.Context, productId int64) ([]admin_protocol.FilterList, []admin_protocol.FilterInfo, *srerr.Error) {
	// 查询product下的属性
	productIdNameMap, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, err1 := r.getProductAttrInfo(ctx, productId)
	if err1 != nil {
		return nil, nil, err1
	}

	// 查询所有的zone_code，包括location_zone_tab、volume_zone_cep_range_tab、volume_zone_location_tab、volume_zone_postcode_tab表的zone_code
	zoneCodeMap, err := r.GetZoneCodeList(ctx)
	if err != nil {
		return nil, nil, err
	}
	// 构建返回结果
	var filterList []admin_protocol.FilterList
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  FmLineStatType,
		FilterName:  GetStatTypeName(FmLineStatType),
		FilterValue: mapToFilterList(fmLineMap),
	})
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  LmLineStatType,
		FilterName:  GetStatTypeName(LmLineStatType),
		FilterValue: mapToFilterList(lmLineMap),
	})
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  MmLineStatType,
		FilterName:  GetStatTypeName(MmLineStatType),
		FilterValue: mapToFilterList(mmLineMap),
	})
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  SiteIdStatType,
		FilterName:  GetStatTypeName(SiteIdStatType),
		FilterValue: mapToFilterList(siteIdMap),
	})
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  ActualPointIdStatType,
		FilterName:  GetStatTypeName(ActualPointIdStatType),
		FilterValue: mapToFilterList(actualPointMap),
	})
	filterList = append(filterList, admin_protocol.FilterList{
		FilterType:  ZoneCodeStatType,
		FilterName:  GetStatTypeName(ZoneCodeStatType),
		FilterValue: mapToFilterList(zoneCodeMap),
	})
	return filterList, mapToFilterList(productIdNameMap), nil
}

func (r *RoutingVolumeServiceImpl) getProductLineInfo(ctx context.Context, productId int64) (map[string]AttrInfo, map[string]AttrInfo, map[string]AttrInfo, *srerr.Error) {
	lineMap := make(map[string]AttrInfo)
	_, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, err := r.getProductAttrInfo(ctx, productId)
	if err != nil {
		return lineMap, siteIdMap, actualPointMap, err
	}
	for key, value := range fmLineMap {
		lineMap[key] = value
	}
	for key, value := range mmLineMap {
		lineMap[key] = value
	}
	for key, value := range lmLineMap {
		lineMap[key] = value
	}
	return lineMap, siteIdMap, actualPointMap, nil
}

func (r *RoutingVolumeServiceImpl) getProductAttrInfo(ctx context.Context, productId int64) (map[string]AttrInfo, map[string]AttrInfo, map[string]AttrInfo, map[string]AttrInfo, map[string]AttrInfo, map[string]AttrInfo, *srerr.Error) {
	productIdNameMap := make(map[string]AttrInfo)
	fmLineMap := make(map[string]AttrInfo)
	mmLineMap := make(map[string]AttrInfo)
	lmLineMap := make(map[string]AttrInfo)
	siteIdMap := make(map[string]AttrInfo)
	actualPointMap := make(map[string]AttrInfo)

	// 查询各筛选项的值
	laneCodeInfoList, err := r.LpsApi.GetLaneCodes(ctx)
	if err != nil {
		return productIdNameMap, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, err
	}
	productEntityMap, err := r.getProductEntityMap(ctx)
	if err != nil {
		return productIdNameMap, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, err
	}

	for _, laneCodeInfo := range laneCodeInfoList {
		// productId不为空时，需要根据productId进行筛选
		if productId != 0 {
			if int64(laneCodeInfo.ProductId) != productId {
				continue
			}
		}

		entity := productEntityMap[laneCodeInfo.ProductId]
		productIdNameMap[strconv.Itoa(laneCodeInfo.ProductId)] = AttrInfo{laneCodeInfo.ProductName, entity}
		// 根据lane code查询lane下的line、site、actual point id信息
		for _, laneCode := range laneCodeInfo.LaneCodes {
			laneInfo, err1 := r.LaneSrv.GetLaneInfoByLaneCode(ctx, laneCode)
			if err1.Cause() == localcache.ErrKeyNotFound {
				continue
			}
			if err1 != nil {
				return productIdNameMap, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, err1
			}
			for _, lineInfo := range laneInfo.Lines {
				lineType := lfslib.GetLineCategory(lineInfo.SubType)
				switch lineType {
				case lfslib.FM:
					fmLineMap[lineInfo.LineID] = AttrInfo{lineInfo.LineName, entity}
				case lfslib.MM:
					mmLineMap[lineInfo.LineID] = AttrInfo{lineInfo.LineName, entity}
				case lfslib.LM:
					lmLineMap[lineInfo.LineID] = AttrInfo{lineInfo.LineName, entity}
				}
			}
			for _, siteInfo := range laneInfo.Sites {
				// 只展示mainType为2，subType为8的site_id，故只上报这个类型的点
				if siteInfo.MainType != lfslib.JointSite || siteInfo.SubType != lfslib.SocJointSite {
					continue
				}
				siteIdMap[siteInfo.SiteID] = AttrInfo{siteInfo.SiteName, entity}
				for _, actualPointInfo := range siteInfo.ActualPointMap {
					actualPointMap[actualPointInfo.ActualPointID] = AttrInfo{actualPointInfo.ActualPointName, entity}
				}
			}
		}
	}
	return productIdNameMap, fmLineMap, mmLineMap, lmLineMap, siteIdMap, actualPointMap, nil
}

func (r *RoutingVolumeServiceImpl) getProductEntityMap(ctx context.Context) (map[int]int, *srerr.Error) {
	productInfoList, err := r.LpsApi.GetProductBaseInfoList(ctx)
	if err != nil {
		return nil, err
	}

	productEntityMap := make(map[int]int, len(productInfoList))
	for _, productInfo := range productInfoList {
		productEntityMap[productInfo.ProductId] = productInfo.Entity
	}

	return productEntityMap, nil
}

func (r *RoutingVolumeServiceImpl) exportRoutingVolume(ctx context.Context, resp *admin_protocol.RoutingSearchDataResp, orderNumSheetName string, distributionSheetName string) (string, *srerr.Error) {
	// 构建订单数量的导出文件
	var orderNumFileHeader []string
	// 返回结果为空，则直接返回
	if resp == nil || len(resp.TotalOrder) == 0 || len(resp.Distribution) == 0 {
		return "", nil
	}
	orderNumData := make([][]string, len(resp.TotalOrder[0].Value))
	filterLen := len(strings.Split(resp.TotalOrder[0].Title, TitleJoiner))
	for i := 0; i < len(resp.TotalOrder[0].Value); i++ {
		// 实时统计导出的场景，需要将filter分割导出，故文件列会增加
		orderNumData[i] = make([]string, len(resp.TotalOrder)+filterLen-1)
	}
	// i表示列，j表示行，row表示excel表格数据的实际列
	var col int
	for i, totalOrder := range resp.TotalOrder {
		// 第一列是每一行的标题，如果是实时统计需要切分成多行
		for j, value := range totalOrder.Value {
			if i == 0 {
				// 第一列可能会由一列变成多列
				col = i
				for _, ele := range strings.Split(value, TitleJoiner) {
					orderNumData[j][col] = ele
					col++
				}
			} else {
				// 需要加上新增的列
				orderNumData[j][i+filterLen-1] = value
			}
		}
		if i == 0 {
			orderNumFileHeader = append(orderNumFileHeader, strings.Split(totalOrder.Title, TitleJoiner)...)
		} else {
			orderNumFileHeader = append(orderNumFileHeader, totalOrder.Title)
		}
	}
	// 构建订单分布的导出文件
	var distributionFileHeader []string
	distributionData := make([][]string, len(resp.Distribution[0].Value))
	distributionFilterLen := len(strings.Split(resp.Distribution[0].Title, TitleJoiner))
	for i := 0; i < len(resp.Distribution[0].Value); i++ {
		// 实时统计导出的场景，需要将filter分割导出，故文件列会增加
		distributionData[i] = make([]string, len(resp.Distribution)+distributionFilterLen-1)
	}
	// i表示列，j表示行，row表示excel表格数据的实际列
	var distributionCol int
	for i, distribution := range resp.Distribution {
		// 第一列是每一行的标题，如果是实时统计需要切分成多行
		for j, value := range distribution.Value {
			if i == 0 {
				// 第一列可能会由一列变成多列
				distributionCol = i
				for _, ele := range strings.Split(value, TitleJoiner) {
					distributionData[j][distributionCol] = ele
					distributionCol++
				}
			} else {
				// 需要加上新增的列
				distributionData[j][i+distributionFilterLen-1] = ConvertToPercentage(ctx, value)
			}
		}
		if i == 0 {
			distributionFileHeader = append(distributionFileHeader, strings.Split(distribution.Title, TitleJoiner)...)
		} else {
			distributionFileHeader = append(distributionFileHeader, distribution.Title)
		}
	}
	// 生成并上传文件
	var fileInfoList []*fileutil.FileInfo
	fileInfoList = append(fileInfoList, &fileutil.FileInfo{
		Header:    orderNumFileHeader,
		Data:      orderNumData,
		SheetName: orderNumSheetName,
	}, &fileutil.FileInfo{
		Header:    distributionFileHeader,
		Data:      distributionData,
		SheetName: distributionSheetName,
	})
	return r.UploadRoutingVolumeFile(ctx, fileInfoList)
}

func ConvertToPercentage(ctx context.Context, ratio string) string {
	ratioFloat, err := strconv.ParseFloat(ratio, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "convert ratio to float error, err=%v, ratio=%v", ratioFloat, ratio)
		return ratio
	}
	return fmt.Sprintf("%.2f%%", ratioFloat*100)
}

func (r *RoutingVolumeServiceImpl) UploadRoutingVolumeFile(ctx context.Context, fileInfoList []*fileutil.FileInfo) (string, *srerr.Error) {
	// 生成excel文件
	newFile, fErr := fileutil.MakeExcelWithMultiSheet(ctx, fileInfoList)
	if fErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, fErr)
	}
	b, wErr := newFile.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Routing-volume-dashboard-%v%s", timeutil.FormatDate(timeutil.GetCurrentTime(ctx)), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil

}

func (r *RoutingVolumeServiceImpl) GetZoneCodeList(ctx context.Context) (map[string]AttrInfo, *srerr.Error) {
	zoneCodeMap := make(map[string]AttrInfo)
	var locationZoneTabList []*zone.LocationZoneTab
	if err1 := dbutil.Distinct(ctx, zone.LocationZoneTabHook, nil, "zone_code", &locationZoneTabList); err1 != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err1)
	}
	for _, locationZoneTab := range locationZoneTabList {
		zoneCodeMap[locationZoneTab.ZoneCode] = AttrInfo{Name: locationZoneTab.ZoneCode}
	}
	var volumeZoneCepRangeTabList []*persistent.VolumeZoneCepRangeTab
	if err1 := dbutil.Distinct(ctx, persistent.VolumeZoneCepRangeHook, nil, "zone_name", &volumeZoneCepRangeTabList); err1 != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err1)
	}
	for _, volumeZoneCepRangeTab := range volumeZoneCepRangeTabList {
		zoneCodeMap[volumeZoneCepRangeTab.ZoneName] = AttrInfo{Name: volumeZoneCepRangeTab.ZoneName}
	}
	var volumeZoneLocationTabList []*persistent.VolumeZoneLocationTab
	if err1 := dbutil.Distinct(ctx, persistent.VolumeZoneLocationHook, nil, "zone_name", &volumeZoneLocationTabList); err1 != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err1)
	}
	for _, volumeZoneLocationTab := range volumeZoneLocationTabList {
		zoneCodeMap[volumeZoneLocationTab.ZoneName] = AttrInfo{Name: volumeZoneLocationTab.ZoneName}
	}
	var volumeZonePostcodeTabList []*persistent.VolumeZonePostcodeTab
	if err1 := dbutil.Distinct(ctx, persistent.VolumeZonePostcodeHook, nil, "zone_name", &volumeZonePostcodeTabList); err1 != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err1)
	}
	for _, volumeZonePostcodeTab := range volumeZonePostcodeTabList {
		zoneCodeMap[volumeZonePostcodeTab.ZoneName] = AttrInfo{Name: volumeZonePostcodeTab.ZoneName}
	}
	return zoneCodeMap, nil
}

func mapToFilterList(filterMap map[string]AttrInfo) []admin_protocol.FilterInfo {
	var filterInfoList []admin_protocol.FilterInfo
	for key, value := range filterMap {
		filterInfoList = append(filterInfoList, admin_protocol.FilterInfo{
			Id:     key,
			Name:   value.Name,
			Entity: value.Entity,
		})
	}
	return filterInfoList
}

func buildSearchByHourCondition(ctx context.Context, request *admin_protocol.SearchByHourRequest) map[string]interface{} {
	condition := map[string]interface{}{
		"time_type = ?": StatMinuteType,
		//"stat_time <= ?": currentTime,
		//"stat_time >= ?": startTime,
	}
	// product_id等于0表示选择了all，返回所有的product单量
	if request.ProductId == 0 {
		condition["product_id != ?"] = 0
		return condition
	} else {
		condition["product_id = ?"] = request.ProductId
	}
	for _, filter := range request.FilterList {
		condition = buildFilterByStatType(filter.FilterType, filter.FilterValue, condition)
	}
	return condition
}

func (r *RoutingVolumeServiceImpl) buildResponse(ctx context.Context, dayRequest *admin_protocol.SearchByDayRequest, hourRequest *admin_protocol.SearchByHourRequest, routingProductOrderNumTabList []*repository.RoutingProductOrderNumTab, requestType uint8) (*admin_protocol.RoutingSearchDataResp, *srerr.Error) {
	if len(routingProductOrderNumTabList) == 0 {
		return nil, nil
	}
	mergeOrderNumMap := make(map[string]int64)
	titleMap := make(map[string]interface{})
	statTimeMap := make(map[string]interface{})
	totalOrderNumMap := make(map[string]int64)
	var statTimeList []string
	lineMap, siteMap, actualPointMap, lineErr := r.getProductLineInfo(ctx, 0)
	if lineErr != nil {
		logger.CtxLogErrorf(ctx, "get all line info error, err=%v", lineErr)
	}
	for _, routingProductOrderNumTab := range routingProductOrderNumTabList {
		title := getMergeKey(ctx, routingProductOrderNumTab, dayRequest, hourRequest, lineMap, siteMap, actualPointMap, requestType)
		key := fmt.Sprintf(MergeKeyPattern, title, routingProductOrderNumTab.StatTime)
		mergeOrderNumMap[key] += routingProductOrderNumTab.OrderNum
		titleMap[title] = nil
		if _, ok := statTimeMap[routingProductOrderNumTab.StatTime]; !ok {
			statTimeMap[routingProductOrderNumTab.StatTime] = nil
			statTimeList = append(statTimeList, routingProductOrderNumTab.StatTime)
		}
		// 记录总数
		totalOrderNumMap[routingProductOrderNumTab.StatTime] += routingProductOrderNumTab.OrderNum
	}
	if len(statTimeList) == 0 {
		return nil, nil
	}
	// 对stat_time从小到大排序
	sort.Strings(statTimeList)
	// 按最新的统计时间对title进行排序
	var statTimeResultList []admin_protocol.StatTimeResult
	for title := range titleMap {
		key := fmt.Sprintf(MergeKeyPattern, title, statTimeList[len(statTimeList)-1])
		statTimeResultList = append(statTimeResultList, admin_protocol.StatTimeResult{
			Title:    title,
			OrderNum: mergeOrderNumMap[key],
		})
	}
	sort.Slice(statTimeResultList, func(i, j int) bool {
		return statTimeResultList[i].OrderNum > statTimeResultList[j].OrderNum
	})
	// 获取排序之后的title列表
	var titleList []string
	for _, statTimeResult := range statTimeResultList {
		titleList = append(titleList, statTimeResult.Title)
	}
	titleList = append(titleList, AggregationTotal)
	// 构建返回结果
	var totalOrderResultList []admin_protocol.RoutingTotalOrder
	var distributionResultList []admin_protocol.RoutingDistribution
	// 获取第一列header
	header := GetHeader(dayRequest, hourRequest, requestType)
	totalOrderResultList = append(totalOrderResultList, admin_protocol.RoutingTotalOrder{
		Title: header,
		Value: titleList,
	})
	distributionResultList = append(distributionResultList, admin_protocol.RoutingDistribution{
		Title: header,
		Value: titleList,
	})
	for _, statTime := range statTimeList {
		var orderNumList []string
		var distributionList []string
		totalOrderNum := totalOrderNumMap[statTime]
		for _, title := range titleList {
			// 跳过total
			if title == AggregationTotal {
				continue
			}
			key := fmt.Sprintf(MergeKeyPattern, title, statTime)
			orderNum := mergeOrderNumMap[key]
			orderNumList = append(orderNumList, strconv.FormatInt(orderNum, 10))
			if totalOrderNum != 0 {
				distributionList = append(distributionList, fmt.Sprintf("%.4f", float64(orderNum)/float64(totalOrderNum)))
			} else {
				distributionList = append(distributionList, "0")
			}
		}
		orderNumList = append(orderNumList, strconv.FormatInt(totalOrderNum, 10))
		totalOrderResultList = append(totalOrderResultList, admin_protocol.RoutingTotalOrder{
			Title: convertStatTime(ctx, statTime, requestType),
			Value: orderNumList,
		})
		distributionList = append(distributionList, "1.0000")
		distributionResultList = append(distributionResultList, admin_protocol.RoutingDistribution{
			Title: convertStatTime(ctx, statTime, requestType),
			Value: distributionList,
		})
	}
	resp := &admin_protocol.RoutingSearchDataResp{
		TotalOrder:   totalOrderResultList,
		Distribution: distributionResultList,
	}
	return resp, nil
}

// convertStatTime 后台统计格式转成前端展示格式
func convertStatTime(ctx context.Context, statTime string, requestType uint8) string {
	if requestType == HourRequest {
		// 1. 解析statTime为时间戳
		minuteTime, err := timeutil.ParseLocalTime(MinuteFormat, statTime)
		// 如果解析失败，兜底返回解析前的字符串
		if err != nil {
			logger.CtxLogErrorf(ctx, "parse hour stat time error, err=%v", err)
			return statTime
		}
		return timeutil.FormatDateTimeByFormat(minuteTime, MinuteExhibitFormat)
	}
	if requestType == DayRequest {
		// 1. 解析statTime为时间戳
		dayTime, err := timeutil.ParseLocalTime(DayFormat, statTime)
		// 如果解析失败，兜底返回解析前的字符串
		if err != nil {
			logger.CtxLogErrorf(ctx, "parse day stat time error, err=%v", err)
			return statTime
		}
		return timeutil.FormatDateTimeByFormat(dayTime, DayExhibitFormat)
	}
	return statTime
}

func GetHeader(dayRequest *admin_protocol.SearchByDayRequest, hourRequest *admin_protocol.SearchByHourRequest, requestType uint8) string {
	if requestType == DayRequest && dayRequest != nil {
		if len(dayRequest.SlicerValue) == 0 {
			return getHeaderType(dayRequest.BarType)
		} else {
			return getHeaderType(dayRequest.SlicerType)
		}

	}
	if requestType == HourRequest && hourRequest != nil {
		if len(hourRequest.FilterList) == 0 {
			return getHeaderType(ProductStatType)
		}
		var header string
		for _, filter := range hourRequest.FilterList {
			header = header + TitleJoiner + getHeaderType(filter.FilterType)
		}
		if len(header) > 0 {
			return header[len(TitleJoiner):]
		}
		return ""

	}
	return ""
}

func getHeaderType(statType uint8) string {
	switch statType {
	case ProductStatType:
		return "Product"
	case FmLineStatType:
		return "FmLine"
	case LmLineStatType:
		return "LmLine"
	case MmLineStatType:
		return "MmLine"
	case SiteIdStatType:
		return "SiteId"
	case ActualPointIdStatType:
		return "ActualPointId"
	case ZoneCodeStatType:
		return "ZoneCode"
	}
	return ""
}

func getMergeKey(ctx context.Context, routingProductOrderNumTab *repository.RoutingProductOrderNumTab, dayRequest *admin_protocol.SearchByDayRequest, hourRequest *admin_protocol.SearchByHourRequest, lineMap map[string]AttrInfo, siteMap map[string]AttrInfo, actualPointMap map[string]AttrInfo, requestType uint8) string {
	if requestType == DayRequest && dayRequest != nil {
		// 如果slicer value为空，则以bar value作为title
		if len(dayRequest.SlicerValue) == 0 {
			return getMergeTitle(dayRequest.BarType, routingProductOrderNumTab, lineMap, siteMap, actualPointMap)
		}
		return getMergeTitle(dayRequest.SlicerType, routingProductOrderNumTab, lineMap, siteMap, actualPointMap)
	}
	if requestType == HourRequest && hourRequest != nil {
		var title string
		// 如果实时统计没有选择filter，则以product作为title
		if len(hourRequest.FilterList) == 0 {
			return getMergeTitle(ProductStatType, routingProductOrderNumTab, lineMap, siteMap, actualPointMap)
		}
		for _, filter := range hourRequest.FilterList {
			title = title + TitleJoiner + getMergeTitle(filter.FilterType, routingProductOrderNumTab, lineMap, siteMap, actualPointMap)
		}
		if len(title) > 0 {
			return title[len(TitleJoiner):]
		}
		return title

	}
	return ""
}

func getMergeTitle(statType uint8, routingProductOrderNumTab *repository.RoutingProductOrderNumTab, lineMap map[string]AttrInfo, siteMap map[string]AttrInfo, actualPointMap map[string]AttrInfo) string {
	switch statType {
	case ProductStatType:
		return strconv.FormatInt(routingProductOrderNumTab.ProductId, 10)
	case FmLineStatType:
		if _, ok := lineMap[routingProductOrderNumTab.FmLine]; ok {
			return routingProductOrderNumTab.FmLine + "-" + lineMap[routingProductOrderNumTab.FmLine].Name
		}
		return routingProductOrderNumTab.FmLine
	case LmLineStatType:
		if _, ok := lineMap[routingProductOrderNumTab.LmLine]; ok {
			return routingProductOrderNumTab.LmLine + "-" + lineMap[routingProductOrderNumTab.LmLine].Name
		}
		return routingProductOrderNumTab.LmLine
	case MmLineStatType:
		if _, ok := lineMap[routingProductOrderNumTab.MmLine]; ok {
			return routingProductOrderNumTab.MmLine + "-" + lineMap[routingProductOrderNumTab.MmLine].Name
		}
		return routingProductOrderNumTab.MmLine
	case SiteIdStatType:
		if _, ok := siteMap[routingProductOrderNumTab.SiteId]; ok {
			return routingProductOrderNumTab.SiteId + "-" + siteMap[routingProductOrderNumTab.SiteId].Name
		}
		return routingProductOrderNumTab.SiteId
	case ActualPointIdStatType:
		if _, ok := actualPointMap[routingProductOrderNumTab.ActualPointId]; ok {
			return routingProductOrderNumTab.ActualPointId + "-" + actualPointMap[routingProductOrderNumTab.ActualPointId].Name
		}
		return routingProductOrderNumTab.ActualPointId
	case ZoneCodeStatType:
		return routingProductOrderNumTab.ZoneCode
	}
	return ""
}

func buildSearchByDayCondition(ctx context.Context, request *admin_protocol.SearchByDayRequest) map[string]interface{} {
	condition := map[string]interface{}{
		"time_type = ?":  StatDayType,
		"stat_time >= ?": request.StartDay,
		"stat_time <= ?": request.EndDay,
	}
	// build bar filter
	condition = buildFilterByStatType(request.BarType, []string{request.BarValue}, condition)
	// build slicer filter
	condition = buildFilterByStatType(request.SlicerType, request.SlicerValue, condition)
	return condition
}

func buildFilterByStatType(statType uint8, statValue []string, condition map[string]interface{}) map[string]interface{} {
	if len(statValue) < 1 {
		return condition
	}
	switch statType {
	case ProductStatType:
		if checkFilterIsAll(statValue) {
			condition["product_id != ?"] = 0
		} else {
			condition["product_id in (?)"] = statValue
		}
	case FmLineStatType:
		if checkFilterIsAll(statValue) {
			condition["fm_line != ?"] = ""
		} else {
			condition["fm_line in (?)"] = statValue
		}
	case LmLineStatType:
		if checkFilterIsAll(statValue) {
			condition["lm_line != ?"] = ""
		} else {
			condition["lm_line in (?)"] = statValue
		}
	case MmLineStatType:
		if checkFilterIsAll(statValue) {
			condition["mm_line != ?"] = ""
		} else {
			condition["mm_line in (?)"] = statValue
		}
	case SiteIdStatType:
		if checkFilterIsAll(statValue) {
			condition["site_id != ?"] = ""
		} else {
			condition["site_id in (?)"] = statValue
		}
	case ActualPointIdStatType:
		if checkFilterIsAll(statValue) {
			condition["actual_point_id != ?"] = ""
		} else {
			condition["actual_point_id in (?)"] = statValue
		}
	case ZoneCodeStatType:
		if checkFilterIsAll(statValue) {
			condition["zone_code != ?"] = ""
		} else {
			condition["zone_code in (?)"] = statValue
		}
	}
	return condition
}

// checkFilterIsAll true:表示all，false表示非all
func checkFilterIsAll(statValue []string) bool {
	if len(statValue) != 1 {
		return false
	}
	return statValue[0] == FilterAll
}

// GetCarveTime routing按15分钟统计运力，00-14属于00时刻运力，15-29属于15，30-44属于30，45-59属于45
func GetCarveTime(ctx context.Context, timestamp time.Time) string {
	hourResult := timeutil.FormatDateTimeByFormat(timestamp, HourFormat)
	minuteResult := timeutil.FormatDateTimeByFormat(timestamp, MinuteFormat)
	if minuteResult >= (hourResult+"00") && minuteResult < (hourResult+"15") {
		return hourResult + "15"
	}
	if minuteResult >= (hourResult+"15") && minuteResult < (hourResult+"30") {
		return hourResult + "30"
	}
	if minuteResult >= (hourResult+"30") && minuteResult < (hourResult+"45") {
		return hourResult + "45"
	}
	hourResultNum, err := strconv.ParseInt(hourResult, 10, 64)
	if err != nil {
		logger.CtxLogErrorf(ctx, "transfer hourResult to num error, err=%v", err)
		return hourResult + "00"
	}
	// 45-59分的运力由下个小时的0分表示，如23:45-23:59分的运力由24:00表示，故小时为当前小时加1
	return strconv.FormatInt(hourResultNum+1, 10) + "00"
}
