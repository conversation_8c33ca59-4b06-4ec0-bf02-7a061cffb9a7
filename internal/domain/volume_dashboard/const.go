package volume_dashboard

import "time"

const (
	VolumeDashboardHourFormat = "********15"
	VolumeDashboardDayFormat  = "********"
	BeforeDayTime             = -5 * time.Minute
)

// stat time type
const (
	StatDayType    = 1
	StatHourType   = 2
	StatMinuteType = 3
)

// routing stat type
const (
	ProductStatType       uint8 = 1
	FmLineStatType        uint8 = 2
	LmLineStatType        uint8 = 3
	MmLineStatType        uint8 = 4
	SiteIdStatType        uint8 = 5
	ActualPointIdStatType uint8 = 6
	ZoneCodeStatType      uint8 = 7
)

func GetStatTypeName(statType uint8) string {
	switch statType {
	case ProductStatType:
		return "Product ID"
	case FmLineStatType:
		return "FM Line"
	case LmLineStatType:
		return "LM Line"
	case MmLineStatType:
		return "MM Line"
	case SiteIdStatType:
		return "Site ID"
	case ActualPointIdStatType:
		return "Actual Point ID"
	case ZoneCodeStatType:
		return "Zone Code"
	}
	return "UnknownType"
}

const (
	FilterAll = "All"
)

const (
	AggregationOthers = "others"
	AggregationTotal  = "Total"
)
