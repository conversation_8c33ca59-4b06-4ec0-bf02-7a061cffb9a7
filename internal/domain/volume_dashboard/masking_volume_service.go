package volume_dashboard

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/admin_protocol"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/masking_panel"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"modernc.org/mathutil"
)

const (
	AggregationJoiner  = "||"
	AggregationPattern = "%v" + AggregationJoiner + "%v"
	StatDayTitle       = "Day/Channel"
	StatHourTitle      = "Hour/Channel"
	StatTimeHourLen    = 10
	StatTimeDayLen     = 8
	SearchStatTimeLen  = 2 //search接口独有
	ExportHourPattern  = "%02d:00-%02d:00"
	// 默认清除30天前的数据
	DefaultClearTime    = 90
	DefaultSubClearTime = 30
	oneDayUnix          = 86399
	totalRatio          = 1.00
	emptyRatio          = 0.00

	MaxSelect = 10

	MPL = 0
	WMS = 1
	ALL = 2
)

var (
	ProductVolumeHeader   = []string{"Channel_ID", "Time period", "Order count", "%Distribution"}
	ProductVolumeSheet    = "Channel order count"
	ZoneRouteVolumeHeader = []string{"Zone code", "Route code", "Period", "Product", "Business Type", "Order"}
	ZoneRouteVolumeSheet  = "Zone/Route level info"
)

type MaskingVolumeService interface {
	ReportMaskingVolume(ctx context.Context) *srerr.Error
	MergeMaskingVolume(ctx context.Context, day string) *srerr.Error
	ClearMaskingVolume(ctx context.Context, clearDay string) *srerr.Error
	Search(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) (*admin_protocol.SearchMaskingVolumeResp, *srerr.Error)
	GetDict(ctx context.Context) (*admin_protocol.MaskingVolumeDict, *srerr.Error)
	Export(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) (string, *srerr.Error)
	SyncMaskingVolume(ctx context.Context, startDay, endDay string, oldToNew bool) *srerr.Error
}

type MaskingVolumeServiceImpl struct {
	VolumeCounter           volume_counter.VolumeCounter
	MaskProductOrderNumRepo repository.MaskProductOrderNumRepo
	LpsApi                  lpsclient.LpsApi
	ExportTaskRepo          export_task.ExportTaskRepo
	RuleVolumeService       rulevolume2.MaskRuleVolumeService
}

func NewMaskingVolumeServiceImpl(
	volumeCounter volume_counter.VolumeCounter,
	maskProductOrderNumRepo repository.MaskProductOrderNumRepo,
	lpsApi lpsclient.LpsApi,
	exportTaskRepo export_task.ExportTaskRepo,
	ruleVolumeService rulevolume2.MaskRuleVolumeService,
) *MaskingVolumeServiceImpl {
	return &MaskingVolumeServiceImpl{
		VolumeCounter:           volumeCounter,
		MaskProductOrderNumRepo: maskProductOrderNumRepo,
		LpsApi:                  lpsApi,
		ExportTaskRepo:          exportTaskRepo,
		RuleVolumeService:       ruleVolumeService,
	}
}

// ReportMaskingVolume 上报masking的单量
// 在小时维度，每次存储当前小时的运力，并删除该小时的旧数据
func (m *MaskingVolumeServiceImpl) ReportMaskingVolume(ctx context.Context) *srerr.Error {
	nowTime := GetMaskingStatTime(ctx)
	statTime := timeutil.FormatDateTimeByFormat(nowTime, VolumeDashboardHourFormat) //获取时间戳，小时维度
	// 上报masking_product单量
	maskProductOrderNumList, delMaskProductList, err := m.reportMaskingProductVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	// 上报fulfillment_product单量
	fulfillmentProductOrderNumList, delFulfillmentProductList, err := m.reportFulfillmentProductVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	// 上报group_code单量
	groupCodeOrderNumList, delGroupCodeList, err := m.reportGroupCodeVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	// 上报zone_code单量
	zoneCodeOrderNumList, delZoneCodeList, err := m.reportZoneCodeVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	// 上报route_code单量
	routeCodeOrderNumList, delRouteCodeList, err := m.reportRouteCodeVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	// 上报shop_group单量
	shopGroupOrderNumList, delShopGroupList, err := m.reportShopGroupVolume(ctx, nowTime.Unix(), statTime)
	if err != nil {
		return err
	}
	saveOrderNumParam := &repository.SaveOrderNumParam{
		MaskProductOrderNum:           maskProductOrderNumList,
		FulfillmentProductOrderNum:    fulfillmentProductOrderNumList,
		GroupCodeOrderNum:             groupCodeOrderNumList,
		ZoneCodeOrderNum:              zoneCodeOrderNumList,
		RouteCodeOrderNum:             routeCodeOrderNumList,
		ShopGroupOrderNum:             shopGroupOrderNumList,
		DelMaskProductOrderNum:        delMaskProductList,
		DelFulfillmentProductOrderNum: delFulfillmentProductList,
		DelGroupCodeOrderNum:          delGroupCodeList,
		DelZoneCodeOrderNum:           delZoneCodeList,
		DelRouteCodeOrderNum:          delRouteCodeList,
		DelShopGroupOrderNum:          delShopGroupList,
		StatTime:                      statTime,
	}
	err1 := m.MaskProductOrderNumRepo.SaveOrderNum(ctx, saveOrderNumParam)
	if err1 != nil {
		return srerr.With(srerr.DatabaseErr, nil, err1)
	}
	//merge 当天的运力
	var deleteIds, subDeleteIds []uint64
	today := timeutil.FormatDateTimeByFormat(timeutil.TodayDate(ctx), VolumeDashboardDayFormat)
	mergeMaskingProductList, mErr := m.mergeMaskingProductVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's masking product volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition := map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.MaskingProductVolumeStat,
	}
	deleteTabs, gErr := m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	deleteIds = append(deleteIds, getDeleteIds(deleteTabs)...)

	// merge today f product
	mergeFulfillmentProductList, mErr := m.mergeFulfillmentProductVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's f product volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.FulfillmentProductVolumeStat,
	}
	deleteTabs, gErr = m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	deleteIds = append(deleteIds, getDeleteIds(deleteTabs)...)

	// merge today group code
	mergeGroupCodeList, mErr := m.mergeGroupCodeVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's group code volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.GroupCodeVolumeStat,
	}
	deleteTabs, gErr = m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	deleteIds = append(deleteIds, getDeleteIds(deleteTabs)...)

	//merge today zone
	mergeZoneCodeList, mErr := m.mergeZoneCodeVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's zone volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.ZoneCodeVolumeStat,
	}
	deleteTabs, gErr = m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	subDeleteIds = append(subDeleteIds, getDeleteIds(deleteTabs)...)

	//merge today route
	mergeRouteCodeList, mErr := m.mergeRouteCodeVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's route volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.RouteCodeVolumeStat,
	}
	deleteTabs, gErr = m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	subDeleteIds = append(subDeleteIds, getDeleteIds(deleteTabs)...)

	//merge today shop group
	mergeShopGroupList, mErr := m.mergeShopGroupVolume(ctx, today)
	if mErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|merge today's shop group volume err:%v", mErr)
		return srerr.With(srerr.DatabaseErr, nil, mErr)
	}
	condition = map[string]interface{}{
		"stat_time = ?": today,
		"stat_type = ?": constant.ShopGroupVolumeStat,
	}
	deleteTabs, gErr = m.getVolumeByCondition(ctx, condition)
	if gErr != nil {
		logger.CtxLogErrorf(ctx, "ReportMaskingVolume|condition:%v|get volume by condition err:%v", condition, gErr)
		return srerr.With(srerr.DatabaseErr, nil, gErr)
	}
	subDeleteIds = append(subDeleteIds, getDeleteIds(deleteTabs)...)

	if !configutil.GetMaskingDashboardSwitch(ctx) {
		deleteList, deleteErr := m.MaskProductOrderNumRepo.GetDeleteListByParam(ctx, today)
		if deleteErr != nil {
			logger.CtxLogErrorf(ctx, "ReportMaskingVolume|GetDeleteListByParam err:%v", deleteErr)
			return srerr.With(srerr.DatabaseErr, nil, deleteErr)
		}
		deleteIds = append(deleteIds, subDeleteIds...)
		subDeleteIds = getDeleteIds(deleteList)
	}

	mergeOrderNumParam := &repository.SaveOrderNumParam{
		MaskProductOrderNum:        mergeMaskingProductList,
		FulfillmentProductOrderNum: mergeFulfillmentProductList,
		GroupCodeOrderNum:          mergeGroupCodeList,
		ZoneCodeOrderNum:           mergeZoneCodeList,
		RouteCodeOrderNum:          mergeRouteCodeList,
		ShopGroupOrderNum:          mergeShopGroupList,
		MergeDeleteIds:             deleteIds,
		MergeSubDeleteIds:          subDeleteIds,
		StatTime:                   today,
	}
	//直接复用SaveOrderNum中的create部分
	err2 := m.MaskProductOrderNumRepo.SaveOrderNum(ctx, mergeOrderNumParam)
	if err2 != nil {
		return srerr.With(srerr.DatabaseErr, nil, err2)
	}
	return nil
}

// MergeMaskingVolume 合并昨天的运力数据，保留最新的运力统计
func (m *MaskingVolumeServiceImpl) MergeMaskingVolume(ctx context.Context, yesterday string) *srerr.Error {
	// 获取昨天的日期
	if yesterday == "" {
		yesterday = timeutil.FormatDateTimeByFormat(timeutil.GetYesterday(ctx), VolumeDashboardDayFormat)
	}
	//// todo:SSCSMR-2371:不再检查是否需要合并昨天的数据 =》避免受ReportMaskingVolume的影响
	//isNeedMerge, err := m.checkIsNeedMerge(ctx, yesterday)
	//if err != nil {
	//	return err
	//}
	//// true: 需要合并，false：不需要合并
	//if !isNeedMerge {
	//	return nil
	//}
	var totalMergeList []*repository.MaskingProductOrderNumTab
	var totalSubMergeList []*repository.MaskingProductOrderNumTab
	// 合并masking product的运力
	mergeMaskingProductList, err1 := m.mergeMaskingProductVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalMergeList = append(totalMergeList, mergeMaskingProductList...)

	// 合并fulfillment product的运力
	mergeFulfillmentProductList, err1 := m.mergeFulfillmentProductVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalMergeList = append(totalMergeList, mergeFulfillmentProductList...)

	// 合并group code的运力
	mergeGroupCodeList, err1 := m.mergeGroupCodeVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalMergeList = append(totalMergeList, mergeGroupCodeList...)

	// 合并route code的运力
	mergeRouteCodeList, err1 := m.mergeRouteCodeVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalSubMergeList = append(totalSubMergeList, mergeRouteCodeList...)

	// 合并zone code的运力
	mergeZoneCodeList, err1 := m.mergeZoneCodeVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalSubMergeList = append(totalSubMergeList, mergeZoneCodeList...)

	// 合并shop group的运力
	mergeShopGroupList, err1 := m.mergeShopGroupVolume(ctx, yesterday)
	if err1 != nil {
		return err1
	}
	totalSubMergeList = append(totalSubMergeList, mergeShopGroupList...)

	if !configutil.GetMaskingDashboardSwitch(ctx) {
		totalMergeList = append(totalMergeList, mergeRouteCodeList...)
		totalMergeList = append(totalMergeList, mergeZoneCodeList...)
		totalMergeList = append(totalMergeList, mergeShopGroupList...)
	}

	err2 := m.MaskProductOrderNumRepo.MergeOrderNum(ctx, yesterday, totalMergeList, copyTabsWithoutId(ctx, totalSubMergeList))
	if err2 != nil {
		return srerr.With(srerr.DatabaseErr, nil, err2)
	}
	return nil
}

func (m *MaskingVolumeServiceImpl) ClearMaskingVolume(ctx context.Context, clearDay string) *srerr.Error {
	var deadlineDay int64
	var deadlineSubDay int64
	if clearDay != "" {
		parts := strings.Split(clearDay, "|")
		if len(parts) != 2 {
			return srerr.With(srerr.DataErr, nil, errors.New("clear day param number number be 2"))
		}
		temp, err := strconv.ParseInt(parts[0], 10, 64)
		if err != nil {
			return srerr.With(srerr.JsonErr, nil, err)
		}
		deadlineDay = temp

		temp, err = strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			return srerr.With(srerr.JsonErr, nil, err)
		}
		deadlineSubDay = temp
	} else {
		deadlineDay = DefaultClearTime
		deadlineSubDay = DefaultSubClearTime
	}
	// 清除清理时间前的数据
	deadlineTime := timeutil.FormatDateTimeByFormat(timeutil.AddDays(timeutil.GetLocalTime(ctx), int(deadlineDay*-1)), VolumeDashboardDayFormat)
	deadlineSubTime := timeutil.FormatDateTimeByFormat(timeutil.AddDays(timeutil.GetLocalTime(ctx), int(deadlineSubDay*-1)), VolumeDashboardDayFormat)
	err := m.MaskProductOrderNumRepo.ClearMaskingVolumeDataByTime(ctx, deadlineTime, deadlineSubTime)
	if err != nil {
		logger.CtxLogErrorf(ctx, "ClearScheduleCount|clear data error, db error: %v", err)
		return srerr.With(srerr.DatabaseErr, nil, err)
	}
	return nil
}

func (m *MaskingVolumeServiceImpl) Search(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) (*admin_protocol.SearchMaskingVolumeResp, *srerr.Error) {
	statTimeList, titleList, productOrderNumMap, productDistributionMap, err := m.GetSearchData(ctx, request)
	if err != nil {
		return nil, err
	}
	// 构建返回结果
	return convertToResp(request, statTimeList, titleList, productOrderNumMap, productDistributionMap), nil
}

func (m *MaskingVolumeServiceImpl) GetDict(ctx context.Context) (*admin_protocol.MaskingVolumeDict, *srerr.Error) {
	maskingProductRefList, err := m.LpsApi.GetMaskingProductRefList(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get masking product ref error, err=%v", err)
		// 不存在masking product的市场接口会报错，这里屏蔽掉错误
		return nil, nil
	}
	productBaseInfoList, err := m.LpsApi.GetProductBaseInfoList(ctx)
	if err != nil {
		return nil, err
	}
	productIdNameMap := make(map[int]string)
	for _, productBaseInfo := range productBaseInfoList {
		productIdNameMap[productBaseInfo.ProductId] = productBaseInfo.SellerDisplayName
	}
	var maskingProductInfoList []admin_protocol.MaskingProductInfo
	for _, maskingProductRef := range maskingProductRefList {
		volumeGroupMapping := m.getMaskProductVolumeGroupInfoMapping(ctx, maskingProductRef.MaskingProductId)
		var fulfillmentProductInfoList []admin_protocol.FulfillmentProductInfo
		for _, componentProductId := range maskingProductRef.ComponentProductId {
			fulfillmentProductInfo := admin_protocol.FulfillmentProductInfo{
				FulfillmentProductId:   int64(componentProductId),
				FulfillmentProductName: productIdNameMap[componentProductId],
				GroupCode:              volumeGroupMapping[componentProductId],
			}
			fulfillmentProductInfoList = append(fulfillmentProductInfoList, fulfillmentProductInfo)
		}
		maskingProductInfo := admin_protocol.MaskingProductInfo{
			MaskingProductId:           int64(maskingProductRef.MaskingProductId),
			MaskingProductName:         productIdNameMap[maskingProductRef.MaskingProductId],
			FulfillmentProductInfoList: fulfillmentProductInfoList,
		}
		maskingProductInfoList = append(maskingProductInfoList, maskingProductInfo)
	}
	maskingVolumeDict := &admin_protocol.MaskingVolumeDict{
		MaskingProductInfo: maskingProductInfoList,
	}

	return maskingVolumeDict, nil
}

func (m *MaskingVolumeServiceImpl) getMaskProductVolumeGroupInfoMapping(ctx context.Context, maskProductId int) map[int]string {
	mapping := make(map[int]string)

	ruleVolume, err := m.RuleVolumeService.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, int64(maskProductId), rule_mode.MplOrderRule, allocation.SingleAllocate)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetActiveRuleVolumeByMaskProductIDWithCache failed|MaskProductId=%d, err=%v", maskProductId, err)
		return mapping
	}

	for _, groupInfo := range ruleVolume.GroupInfo.FulfillmentProductGroupInfos {
		for _, p := range groupInfo.FulfillmentProductInfos {
			mapping[p.FulfillmentProductID] = groupInfo.GroupCode
		}
	}

	return mapping
}

func (m *MaskingVolumeServiceImpl) Export(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) (string, *srerr.Error) {
	//create task record
	operator, _ := apiutil.GetUserInfo(ctx)
	createReq := masking_panel.CreateExportTaskReq{
		TaskStatus:        enum.Success,
		TaskType:          enum.Download,
		TaskBusinessScene: enum.MaskingVolumePanel,
		LastOperateTime:   timeutil.GetCurrentUnixTimeStamp(ctx),
		LastOperator:      operator,
	}

	url, err := m.exportMaskingVolume(ctx, request)
	if err != nil {
		createReq.TaskStatus = enum.Failed
		createReq.ErrorMessage = err.GetMessage()
	}

	createReq.DownloadUrl = url
	if _, err := m.ExportTaskRepo.CreateTaskRecord(ctx, createReq); err != nil {
		logger.CtxLogErrorf(ctx, "create task record err:%v", err)
		return "", err
	}

	return url, err
}

func (m *MaskingVolumeServiceImpl) SyncMaskingVolume(ctx context.Context, startDay, endDay string, newToOld bool) *srerr.Error {
	db, err := dbutil.MasterDB(ctx, repository.MaskingProductOrderNumTabHook)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	ctx = scormv2.BindContext(ctx, db)
	if newToOld {
		return m.syncNewToOld(ctx, startDay, endDay)
	} else {
		return m.syncOldToNew(ctx, startDay, endDay)
	}
}

func (m *MaskingVolumeServiceImpl) syncNewToOld(ctx context.Context, startDay, endDay string) *srerr.Error {
	condition := map[string]interface{}{
		// 两端为天维度
		"stat_time >= ?": startDay,
		"stat_time < ?":  endDay,
	}

	maskingVolumeOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, true)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return m.MaskProductOrderNumRepo.DeleteAndInsert(ctx, startDay, endDay, repository.MaskingProductOrderNumTabHook.TableName(), copyTabsWithoutId(ctx, maskingVolumeOrderNumTabList))
}

func (m *MaskingVolumeServiceImpl) syncOldToNew(ctx context.Context, startDay, endDay string) *srerr.Error {
	dates, err := getDatesBetween(startDay, endDay)
	if err != nil {
		return srerr.With(srerr.ParamErr, nil, err)
	}

	for _, date := range dates {
		if len(date) < 2 {
			continue
		}
		condition := map[string]interface{}{
			"stat_time >= ?":   date[0],
			"stat_time < ?":    date[1],
			"stat_type in (?)": []uint8{constant.ZoneCodeVolumeStat, constant.RouteCodeVolumeStat, constant.ShopGroupVolumeStat},
		}

		maskingVolumeOrderNumTabList, err1 := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, false)
		if err1 != nil {
			return srerr.With(srerr.DatabaseErr, nil, err1)
		}

		if err := m.MaskProductOrderNumRepo.DeleteAndInsert(ctx, date[0], date[1], repository.MaskingProductOrderNumTabHook.TableNameByDate(date[0]), copyTabsWithoutId(ctx, maskingVolumeOrderNumTabList)); err != nil {
			return err
		}
	}
	return nil
}

func getDatesBetween(dayStart, dayEnd string) ([][]string, error) {
	start, err := time.Parse(VolumeDashboardDayFormat, dayStart)
	if err != nil {
		return nil, err
	}
	end, err := time.Parse(VolumeDashboardDayFormat, dayEnd)
	if err != nil {
		return nil, err
	}

	var dates [][]string
	// current.Equal(end) 由于这个逻辑的存在，oldToNew同步时，左右边界对应天数的数据都会同步，如果只想同步一天，需要保证左右边界相等
	for current := start; current.Before(end) || current.Equal(end); current = current.AddDate(0, 0, 1) {
		dates = append(dates, []string{current.Format(VolumeDashboardDayFormat), current.AddDate(0, 0, 1).Format(VolumeDashboardDayFormat)})
	}

	return dates, nil
}

// GetMaskingStatTime 获取统计时间。根据saturn cron表达式，每天最后一次定时任务执行是在下一天的00:00执行，无法获取到当天最后的数据。这里做个判断，如果5分钟前是前一天，则表明是最后一次执行，返回5分钟前的时间
func GetMaskingStatTime(ctx context.Context) time.Time {
	nowTime := timeutil.GetLocalTime(ctx)
	beforeTime := nowTime.Add(BeforeDayTime)
	nowTimeStr := timeutil.FormatDateTimeByFormat(nowTime, VolumeDashboardDayFormat)
	beforeTimeStr := timeutil.FormatDateTimeByFormat(beforeTime, VolumeDashboardDayFormat)
	if nowTimeStr != beforeTimeStr {
		return beforeTime
	}
	return nowTime
}

func (m *MaskingVolumeServiceImpl) exportMaskingVolume(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) (string, *srerr.Error) {
	// 第一个sheet页根据搜索条件导出masking product或fulfillment product的运力
	statTimeList, titleList, productOrderNumMap, productDistributionMap, err := m.GetSearchData(ctx, request)
	if err != nil {
		return "", err
	}
	var productData [][]string
	var rowData []string
	for idx, statTime := range statTimeList {
		for _, title := range titleList {
			if len(productOrderNumMap[title]) < len(statTimeList) || len(productDistributionMap[title]) < len(statTimeList) {
				logger.CtxLogErrorf(ctx, "The queried data does not meet expectations")
				return "", srerr.New(srerr.TitleVolumeLengthInValid, nil, "title volume list length is invalid")
			}
			if productOrderNumMap[title][idx] == nil || productDistributionMap[title][idx] == nil {
				rowData = []string{title, convertToExportStatTime(statTime), "-", "-"}
			} else {
				rowData = []string{title, convertToExportStatTime(statTime), strconv.FormatInt(*productOrderNumMap[title][idx], 10), fmt.Sprintf("%.2f", *productDistributionMap[title][idx])}
			}
			productData = append(productData, rowData)
		}
	}
	// 导出zone_code、route_code的运力
	zoneCodeDataList, err := m.GetSubFactorVolume(ctx, request, constant.ZoneCodeVolumeStat)
	if err != nil {
		return "", nil
	}
	routeCodeDataList, err := m.GetSubFactorVolume(ctx, request, constant.RouteCodeVolumeStat)
	if err != nil {
		return "", nil
	}
	var zoneRouteDataList [][]string
	for _, zoneCodeData := range zoneCodeDataList {
		if len(zoneCodeData) != 5 {
			continue
		}
		rowData := []string{zoneCodeData[0], "", convertToExportStatTime(zoneCodeData[1]), zoneCodeData[2], zoneCodeData[4], zoneCodeData[3]}
		zoneRouteDataList = append(zoneRouteDataList, rowData)
	}
	for _, routeCodeData := range routeCodeDataList {
		if len(routeCodeData) != 5 {
			continue
		}
		rowData := []string{"", routeCodeData[0], convertToExportStatTime(routeCodeData[1]), routeCodeData[2], routeCodeData[4], routeCodeData[3]}
		zoneRouteDataList = append(zoneRouteDataList, rowData)
	}
	// 生成并上传文件
	var fileInfoList []*fileutil.FileInfo
	fileInfoList = append(fileInfoList, &fileutil.FileInfo{
		Header:    ProductVolumeHeader,
		Data:      productData,
		SheetName: ProductVolumeSheet,
	}, &fileutil.FileInfo{
		Header:    ZoneRouteVolumeHeader,
		Data:      zoneRouteDataList,
		SheetName: ZoneRouteVolumeSheet,
	})
	return m.UploadMaskingVolumeFile(ctx, fileInfoList)
}

// convertToExportStatTime 导出需要将小时维度转为00:00-01:00，天维度转为20060102
func convertToExportStatTime(statTime string) string {
	if len(statTime) == StatTimeHourLen {
		hour, _ := strconv.ParseInt(statTime[StatTimeDayLen:], 10, 64)
		return fmt.Sprintf(ExportHourPattern, 0, hour+1)
	} else {
		return statTime
	}
}

func (m *MaskingVolumeServiceImpl) GetSearchData(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) ([]string, []string, map[string][]*int64, map[string][]*float64, *srerr.Error) {
	// 获取查询数据
	// 校验参数是否合法
	vErr := validParam(ctx, request)
	if vErr != nil {
		return nil, nil, nil, nil, vErr
	}
	// 构建查询条件
	condition := buildSearchCondition(ctx, request)
	// 查询数据
	maskingVolumeOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, nil, nil, nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	if len(maskingVolumeOrderNumTabList) == 0 {
		return nil, nil, nil, nil, nil
	}
	// daily维度需要过滤
	startTimeStamp, _ := strconv.ParseInt(request.StartTime, 10, 64)
	endTimeStamp, _ := strconv.ParseInt(request.EndTime, 10, 64)
	if endTimeStamp-startTimeStamp > oneDayUnix {
		tempTabList := make([]*repository.MaskingProductOrderNumTab, 0)
		for _, tab := range maskingVolumeOrderNumTabList {
			if len(tab.StatTime) != len(VolumeDashboardDayFormat) {
				continue
			}
			tempTabList = append(tempTabList, tab)
		}
		maskingVolumeOrderNumTabList = tempTabList
	}
	// 根据业务查询的是maskingProduct的单量还是fulfillmentProduct的单量对结果进行聚合
	statTimeList, titleList, productOrderNumMap, productDistributionMap, gErr := m.getAggregationResult(ctx, request, maskingVolumeOrderNumTabList)
	if gErr != nil {
		return nil, nil, nil, nil, gErr
	}

	return statTimeList, titleList, productOrderNumMap, productDistributionMap, nil
}

func convertToResp(request *admin_protocol.SearchMaskingVolumeRequest, statTimeList []string, titleList []string, productOrderNumMap map[string][]*int64, productDistributionMap map[string][]*float64) *admin_protocol.SearchMaskingVolumeResp {
	//1.组装x轴及总title
	statTimeTableValue := admin_protocol.TableValue{
		Value: convertStatTimeToResp(statTimeList),
	}
	startTimeStamp, _ := strconv.ParseInt(request.StartTime, 10, 64)
	endTimeStamp, _ := strconv.ParseInt(request.EndTime, 10, 64)
	if endTimeStamp-startTimeStamp > oneDayUnix {
		statTimeTableValue.Title = StatDayTitle
	} else {
		statTimeTableValue.Title = StatHourTitle
	}

	//2.组装订单数及占比
	var totalOrderList []admin_protocol.TotalOrder
	var distributionList []admin_protocol.Distribution
	for _, title := range titleList {
		totalOrder := admin_protocol.TotalOrder{
			Title: title,
			Value: productOrderNumMap[title],
		}
		totalOrderList = append(totalOrderList, totalOrder)
		distribution := admin_protocol.Distribution{
			Title: title,
			Value: productDistributionMap[title],
		}
		distributionList = append(distributionList, distribution)
	}
	maskingVolumeResp := &admin_protocol.SearchMaskingVolumeResp{
		StatTime:     statTimeTableValue,
		TotalOrder:   totalOrderList,
		Distribution: distributionList,
	}
	return maskingVolumeResp
}

// convertStatTimeToResp
// 统一返回'年月日时'，由前端自行截取，例如23083015 -- 23年8月30号15点
func convertStatTimeToResp(statTimeList []string) []string {
	var statTimeRespList []string
	for _, statTime := range statTimeList {
		// 小时维度数据需要从0-23修改为1-24，天维度数据不用修改
		newStatTime := statTime
		if len(statTime) == StatTimeHourLen {
			statTimeNumber, _ := strconv.ParseInt(statTime, 10, 64)
			newStatTime = strconv.FormatInt(statTimeNumber+1, 10)
		}
		statTimeRespList = append(statTimeRespList, newStatTime[SearchStatTimeLen:]) //从年份的后两位开始保留
	}
	return statTimeRespList
}

func RemoveDuplicatesInt64(slice []int64) []int64 {
	if len(slice) == 0 {
		return slice
	}
	result := make([]int64, 0)
	uniqueMap := make(map[int64]interface{})
	for _, value := range slice {
		uniqueMap[value] = nil
	}
	for key := range uniqueMap {
		result = append(result, key)
	}
	return result
}

// getAggregationResult
// 聚合时间、订单量、订单占比
func (m *MaskingVolumeServiceImpl) getAggregationResult(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest, maskingVolumeOrderNumTabList []*repository.MaskingProductOrderNumTab) ([]string, []string, map[string][]*int64, map[string][]*float64, *srerr.Error) {
	fulfillmentProductMap := make(map[int64]bool)
	maskingProductMap := make(map[int64]bool)
	groupCodeMap := make(map[string]bool)
	for _, fulfillmentProduct := range request.FulfillmentProductList {
		fulfillmentProductMap[fulfillmentProduct] = true
	}
	for _, maskingProduct := range request.MaskingProductList {
		maskingProductMap[maskingProduct] = true
	}
	for _, groupCode := range request.GroupCodeList {
		groupCodeMap[groupCode] = true
	}

	var uniqueFulfillmentProductMap map[string][]int64
	var groupCodeFulfillmentProductMap map[int64]string
	if request.ProductType == constant.FulfillmentProductVolume {
		uniqueFulfillmentProductMap, groupCodeFulfillmentProductMap = m.getGroupCodeFulfillmentProductMap(ctx, request, groupCodeMap, fulfillmentProductMap)
	}

	groupCodeSplitTimeMap := make(map[string]map[string]struct{})
	groupCodeExistMap := make(map[string]struct{})
	statTimeList, maxStatTime, productTimeCountMap := getProductTimeCountMap(maskingVolumeOrderNumTabList, groupCodeMap, request, maskingProductMap,
		fulfillmentProductMap, groupCodeFulfillmentProductMap)

	// groupCode-fulfillmentProduct 特殊处理
	for _, groupCode := range request.GroupCodeList {
		for _, statTime := range statTimeList {
			key := fmt.Sprintf(AggregationPattern, groupCode, statTime)
			if _, ok := productTimeCountMap[key]; !ok {
				continue
			}
			// 防止后面计算totalNum多算，没用到的时间点的productTimeCountMap[fulfillmentProduct]要delete
			for _, fulfillmentProduct := range uniqueFulfillmentProductMap[groupCode] {
				delete(productTimeCountMap, fmt.Sprintf(AggregationPattern, fulfillmentProduct, statTime))
			}
			if _, exists := groupCodeSplitTimeMap[groupCode]; !exists {
				groupCodeSplitTimeMap[groupCode] = make(map[string]struct{})
			}
			groupCodeSplitTimeMap[groupCode][statTime] = struct{}{}
			groupCodeExistMap[groupCode] = struct{}{}
		}
	}

	// 对统计时间从小到大排序
	sort.Strings(statTimeList)

	titleList := getTitleList(request, fulfillmentProductMap, groupCodeExistMap, groupCodeSplitTimeMap, statTimeList, uniqueFulfillmentProductMap)
	// 对titleList按最新的统计数据进行从大到小的排序
	var statTimeResultList []admin_protocol.StatTimeResult
	for _, title := range titleList {
		key := fmt.Sprintf(AggregationPattern, title, maxStatTime)
		statTimeResultList = append(statTimeResultList, admin_protocol.StatTimeResult{
			Title:    title,
			OrderNum: productTimeCountMap[key],
		})
	}
	sort.Slice(statTimeResultList, func(i, j int) bool {
		return statTimeResultList[i].OrderNum > statTimeResultList[j].OrderNum
	})
	var sortTitleList []string
	for _, statTimeResult := range statTimeResultList {
		sortTitleList = append(sortTitleList, statTimeResult.Title)
	}

	// 如果有others列，则需要加上others
	isHaveOthers, err := m.isHaveOthers(ctx, request, true)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	if isHaveOthers {
		sortTitleList = append(sortTitleList, AggregationOthers)
	}
	sortTitleList = append(sortTitleList, AggregationTotal)

	//按product进行聚合，统计不同时间段的订单数据（一个title对应一个product）
	productOrderNumMap := getOrderNumMap(sortTitleList, statTimeList, groupCodeSplitTimeMap, groupCodeFulfillmentProductMap, groupCodeMap, productTimeCountMap)

	//按时间段聚合占比，每个时间段内，订单总数，及各product占该时段总订单数的比例
	productOrderNumMap, productDistributionMap := getDistributionMap(statTimeList, sortTitleList, productTimeCountMap, productOrderNumMap, groupCodeSplitTimeMap, groupCodeFulfillmentProductMap, groupCodeMap)

	return statTimeList, sortTitleList, productOrderNumMap, productDistributionMap, nil
}

func (m *MaskingVolumeServiceImpl) getGroupCodeFulfillmentProductMap(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest,
	groupCodeMap map[string]bool, fulfillmentProductMap map[int64]bool) (map[string][]int64, map[int64]string) {
	mapping := make(map[string][]int64)
	uniqueFulfillmentProductMap := make(map[string][]int64)
	groupCodeFulfillmentProductMap := make(map[int64]string)
	for _, maskingProduct := range request.MaskingProductList {
		ruleVolume, err := m.RuleVolumeService.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, maskingProduct, rule_mode.MplOrderRule, allocation.SingleAllocate)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetActiveRuleVolumeByMaskProductIDWithCache failed|MaskProductId=%d, err=%v", maskingProduct, err)
			continue
		}

		for _, groupInfo := range ruleVolume.GroupInfo.FulfillmentProductGroupInfos {
			for _, p := range groupInfo.FulfillmentProductInfos {
				// 按该mask product的active volume rule里是否配置group code为主，以及只有当入参存在该groupCode时进行f product的展示删除操作
				if int64(p.MaskProductID) != maskingProduct || !groupCodeMap[groupInfo.GroupCode] {
					continue
				}
				mapping[groupInfo.GroupCode] = append(mapping[groupInfo.GroupCode], int64(p.FulfillmentProductID))
				//过滤掉request中已经选中的FulfillmentProduct, 视为未选中（groupCode和对应的fulfillmentProduct不能同时选中）
				delete(fulfillmentProductMap, int64(p.FulfillmentProductID))
			}
		}
	}
	// 将传入的GroupCodeList对应的未在request中选中的fulfillmentProduct加入展示结果中
	for _, requestGroupCode := range request.GroupCodeList {
		uniqueFulfillmentProductMap[requestGroupCode] = RemoveDuplicatesInt64(mapping[requestGroupCode])
		for _, fulfillmentProductID := range uniqueFulfillmentProductMap[requestGroupCode] {
			groupCodeFulfillmentProductMap[fulfillmentProductID] = requestGroupCode
		}
	}
	return uniqueFulfillmentProductMap, groupCodeFulfillmentProductMap
}

func getProductTimeCountMap(maskingVolumeOrderNumTabList []*repository.MaskingProductOrderNumTab, groupCodeMap map[string]bool,
	request *admin_protocol.SearchMaskingVolumeRequest, maskingProductMap map[int64]bool, fulfillmentProductMap map[int64]bool,
	groupCodeFulfillmentProductMap map[int64]string) ([]string, string, map[string]int64) {
	//1.获取f product or mask product对应的订单数
	var statTimeList []string
	// 最新的statTime，用来做排序
	var maxStatTime string
	productTimeCountMap := make(map[string]int64)
	statTimeMap := make(map[string]struct{})
	for _, maskingVolumeOrderNumTab := range maskingVolumeOrderNumTabList {
		if maskingVolumeOrderNumTab.GroupCode != "" && !groupCodeMap[maskingVolumeOrderNumTab.GroupCode] {
			continue
		}
		//1.1 记录f product or mask product id，不在request中的则记录为 "others"
		var key string
		var title string
		if request.ProductType == constant.MaskingProductVolume {
			if _, ok1 := maskingProductMap[maskingVolumeOrderNumTab.MaskProductId]; ok1 {
				title = strconv.FormatInt(maskingVolumeOrderNumTab.MaskProductId, 10)
			} else {
				title = AggregationOthers
			}
		} else {
			if canUse, ok1 := fulfillmentProductMap[maskingVolumeOrderNumTab.FulfillmentProductId]; canUse && ok1 {
				title = strconv.FormatInt(maskingVolumeOrderNumTab.FulfillmentProductId, 10)
			} else if _, ok2 := groupCodeMap[maskingVolumeOrderNumTab.GroupCode]; ok2 {
				title = maskingVolumeOrderNumTab.GroupCode
			} else if _, ok3 := groupCodeFulfillmentProductMap[maskingVolumeOrderNumTab.FulfillmentProductId]; ok3 {
				title = strconv.FormatInt(maskingVolumeOrderNumTab.FulfillmentProductId, 10)
			} else {
				title = AggregationOthers
			}
		}
		//1.2 以product-statTime为key，记录对应的订单数（各product下每段时间对应的订单数）
		key = fmt.Sprintf(AggregationPattern, title, maskingVolumeOrderNumTab.StatTime)
		productTimeCountMap[key] += maskingVolumeOrderNumTab.OrderNum
		//1.3 记录每条数据的stat time，对应fe渲染时的横坐标
		if _, ok := statTimeMap[maskingVolumeOrderNumTab.StatTime]; !ok {
			statTimeMap[maskingVolumeOrderNumTab.StatTime] = struct{}{}
			statTimeList = append(statTimeList, maskingVolumeOrderNumTab.StatTime)
			if maskingVolumeOrderNumTab.StatTime > maxStatTime {
				maxStatTime = maskingVolumeOrderNumTab.StatTime
			}
		}
	}
	return statTimeList, maxStatTime, productTimeCountMap
}

func getTitleList(request *admin_protocol.SearchMaskingVolumeRequest, fulfillmentProductMap map[int64]bool, groupCodeExistMap map[string]struct{}, groupCodeSplitTimeMap map[string]map[string]struct{}, statTimeList []string, UniqueFulfillmentProductMap map[string][]int64) []string {
	var titleList []string
	if request.ProductType == constant.MaskingProductVolume {
		for _, maskingProduct := range request.MaskingProductList {
			titleList = append(titleList, strconv.FormatInt(maskingProduct, 10))
		}
		return titleList
	}
	for fulfillmentProduct := range fulfillmentProductMap {
		titleList = append(titleList, strconv.FormatInt(fulfillmentProduct, 10))
	}
	for _, groupCode := range request.GroupCodeList {
		// 如果groupcode整个时间段都没有数据，则不显示该groupCode
		if _, ok := groupCodeExistMap[groupCode]; ok {
			titleList = append(titleList, groupCode)
		}
		if len(groupCodeSplitTimeMap[groupCode]) == len(statTimeList) {
			continue
		}
		for _, fulfillmentProduct := range UniqueFulfillmentProductMap[groupCode] {
			//fulfillmentProduct为空就按默认显示0处理（兜底逻辑）,只对groupCode结果为空进行不显示判断
			titleList = append(titleList, strconv.FormatInt(fulfillmentProduct, 10))
		}
	}
	return titleList
}

func getOrderNumMap(sortTitleList []string, statTimeList []string, groupCodeSplitTimeMap map[string]map[string]struct{}, groupCodeFulfillmentProductMap map[int64]string, groupCodeMap map[string]bool, productTimeCountMap map[string]int64) map[string][]*int64 {
	productOrderNumMap := make(map[string][]*int64) //一个product对应多个时间段，每个时间段对应一个value
	for _, title := range sortTitleList {
		if title == AggregationTotal {
			continue
		}
		for _, statTime := range statTimeList {
			// 统计订单数量
			_, okGroupCode := groupCodeSplitTimeMap[title][statTime]
			value, _ := strconv.ParseInt(title, 10, 64)
			_, okFulfillmentGroupCode := groupCodeSplitTimeMap[groupCodeFulfillmentProductMap[value]][statTime]
			if (groupCodeMap[title] && !okGroupCode) || (groupCodeFulfillmentProductMap[value] != "" && okFulfillmentGroupCode) {
				// 该时间段应该展示fulfillmentProduct，过滤该时间段的groupCode数据（值为nil）
				// 或者该时间段应该展示groupCode，过滤该时间段的fulfillmentProduct数据（值为nil）
				productOrderNumMap[title] = append(productOrderNumMap[title], nil)
			} else {
				key := fmt.Sprintf(AggregationPattern, title, statTime)
				tempNum := productTimeCountMap[key]
				productOrderNumMap[title] = append(productOrderNumMap[title], &tempNum)
			}
		}
	}
	return productOrderNumMap
}

func getDistributionMap(statTimeList []string, sortTitleList []string, productTimeCountMap map[string]int64, productOrderNumMap map[string][]*int64,
	groupCodeSplitTimeMap map[string]map[string]struct{}, groupCodeFulfillmentProductMap map[int64]string, groupCodeMap map[string]bool) (map[string][]*int64, map[string][]*float64) {
	productDistributionMap := make(map[string][]*float64) //一个product对应多个时间段，每个时间段对应一个value
	for _, statTime := range statTimeList {
		//计算总订单数
		var totalNum int64
		for _, title := range sortTitleList {
			if title != AggregationTotal {
				key := fmt.Sprintf(AggregationPattern, title, statTime)
				totalNum += productTimeCountMap[key]
			}
		}
		//记录总数
		tempNum := totalNum
		productOrderNumMap[AggregationTotal] = append(productOrderNumMap[AggregationTotal], &tempNum)
		//计算比例
		for _, title := range sortTitleList {
			var ratio float64
			if totalNum == 0 {
				ratio = emptyRatio
				productDistributionMap[title] = append(productDistributionMap[title], &ratio)
				continue
			}
			if title == AggregationTotal {
				ratio = totalRatio
				ratio, _ = decimal.NewFromFloat(ratio).Round(4).Float64()
				productDistributionMap[title] = append(productDistributionMap[title], &ratio)
				continue
			}
			_, okGroupCode := groupCodeSplitTimeMap[title][statTime]
			value, _ := strconv.ParseInt(title, 10, 64)
			_, okFulfillmentGroupCode := groupCodeSplitTimeMap[groupCodeFulfillmentProductMap[value]][statTime]
			if (groupCodeMap[title] && !okGroupCode) || (groupCodeFulfillmentProductMap[value] != "" && okFulfillmentGroupCode) {
				// 该时间段应该展示fulfillmentProduct，过滤该时间段的groupCode数据（值为nil）
				// 或者该时间段应该展示groupCode，过滤该时间段的fulfillmentProduct数据（值为nil）
				productDistributionMap[title] = append(productDistributionMap[title], nil)
			} else {
				key := fmt.Sprintf(AggregationPattern, title, statTime)
				ratio = float64(productTimeCountMap[key]) / float64(totalNum)
				ratio, _ = decimal.NewFromFloat(ratio).Round(4).Float64()
				productDistributionMap[title] = append(productDistributionMap[title], &ratio)
			}
		}
	}
	return productOrderNumMap, productDistributionMap
}

// isHaveOthers true: 表示存在others列，false: 表示不存在others列
func (m *MaskingVolumeServiceImpl) isHaveOthers(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest,
	checkGroupCode bool) (bool, *srerr.Error) {
	//todo:SSCSMR-2371: 这里return err的部分可以内聚到validate param模块中
	// 获取全量masking product、fulfillment product列表
	maskingProductRefMap, err := m.getMaskingProductRefMap(ctx)
	if err != nil {
		return false, err
	}
	if request.ProductType == constant.MaskingProductVolume {
		// 业务选择的masking_product列表不等于全量masking_product列表则表示存在others列
		if len(maskingProductRefMap) != len(request.MaskingProductList) {
			return true, nil
		}
	} else if request.ProductType == constant.FulfillmentProductVolume {
		if len(request.MaskingProductList) == 1 {
			if len(maskingProductRefMap[int(request.MaskingProductList[0])]) != len(request.FulfillmentProductList) {
				return true, nil
			}
			return false, nil
		}
		count := 0
		uniqueValues := make(map[interface{}]struct{})
		for _, maskingProduct := range request.MaskingProductList {
			count += len(maskingProductRefMap[int(maskingProduct)])
			if !checkGroupCode {
				continue
			}
			ruleVolume, err := m.RuleVolumeService.GetActiveRuleVolumeByMaskProductIDWithCache(ctx, maskingProduct, rule_mode.MplOrderRule, allocation.SingleAllocate)
			if err != nil {
				logger.CtxLogErrorf(ctx, "GetActiveRuleVolumeByMaskProductIDWithCache failed|MaskProductId=%d, err=%v", maskingProduct, err)
				continue
			}
			for _, groupInfo := range ruleVolume.GroupInfo.FulfillmentProductGroupInfos {
				// 计算一个maskingProduct下的全量groupCode
				if groupInfo.GroupCode != "" {
					uniqueValues[groupInfo.GroupCode] = struct{}{}
					continue
				}
			}
		}
		count += len(uniqueValues)
		if !checkGroupCode {
			count += len(request.GroupCodeList)
		}
		if len(request.FulfillmentProductList)+len(request.GroupCodeList) != count {
			return true, nil
		}
	}
	return false, nil
}

func (m *MaskingVolumeServiceImpl) getMaskingProductRefMap(ctx context.Context) (map[int][]int, *srerr.Error) {
	maskingProductRefList, err := m.LpsApi.GetMaskingProductRefList(ctx)
	if err != nil {
		return nil, err
	}
	maskingProductRefMap := make(map[int][]int)
	for _, maskingProductRef := range maskingProductRefList {
		maskingProductRefMap[maskingProductRef.MaskingProductId] = maskingProductRef.ComponentProductId
	}
	return maskingProductRefMap, nil
}

func validParam(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) *srerr.Error {
	//1.check zone, route, shop group
	filterNum := 0
	if len(request.ZoneCodeList) > 0 {
		filterNum += 1
	}
	if len(request.RouteCodeList) > 0 {
		filterNum += 1
	}
	if len(request.ShopGroupIdList) > 0 {
		filterNum += 1
	}
	// zoneCode、routeCode、shopGroupId只能查询一个
	if filterNum > 1 {
		return srerr.New(srerr.MultiFilterParamError, nil, "zone_code、route_code、shop_group only one can search")
	}

	//2. check fulfillment product
	if request.ProductType == constant.FulfillmentProductVolume {
		if len(request.MaskingProductList) < 1 || len(request.MaskingProductList) > 3 {
			return srerr.New(srerr.ParamErr, nil, "masking_product_list length should be [1, 3]")
		}
		if len(request.GroupCodeList) == 0 && len(request.FulfillmentProductList) == 0 {
			return srerr.New(srerr.ParamErr, nil, "fulfillment product slice must need have at least 1 fulfillment product")
		}
	}
	if len(request.ZoneCodeList) > MaxSelect {
		return srerr.New(srerr.ParamErr, nil, "zone_code_list length should be less than or equal to 10")
	}
	if len(request.RouteCodeList) > MaxSelect {
		return srerr.New(srerr.ParamErr, nil, "route_code_list length should be less than or equal to 10")
	}
	if len(request.ShopGroupIdList) > MaxSelect {
		return srerr.New(srerr.ParamErr, nil, "shop_group_id_list length should be less than or equal to 10")
	}

	// 所选时间段要在指定范围内
	startTimeStamp, _ := strconv.ParseInt(request.StartTime, 10, 64)
	endTimeStamp, _ := strconv.ParseInt(request.EndTime, 10, 64)
	if endTimeStamp-startTimeStamp < 0 {
		return srerr.New(srerr.ParamErr, nil, "start_time should be less than or equal to end_time")
	}

	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	if filterNum != 0 {
		if nowTime < startTimeStamp || nowTime-startTimeStamp > DefaultSubClearTime*(oneDayUnix+1) {
			return srerr.New(srerr.ParamErr, nil, "time range(nowTime-startTimeStamp) should be less than or equal to 30 days")
		}
	} else {
		if nowTime < startTimeStamp || nowTime-startTimeStamp > DefaultClearTime*(oneDayUnix+1) {
			return srerr.New(srerr.ParamErr, nil, "time range(nowTime-startTimeStamp) should be less than or equal to 90 days")
		}
	}

	if request.BusinessType == WMS && len(request.ShopGroupIdList) > 0 {
		return srerr.New(srerr.ParamErr, nil, "WMS has no data for shop_group_id_list")
	}
	return nil
}

func buildSearchCondition(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest) map[string]interface{} {
	//SSCSMR-2371:format time
	var (
		startTimeStr, endTimeStr string
		timeFormat               string
	)
	//转换条件，判断是否跨天
	//todo:SSCSMR-2371 bug => 如何区分天和hour维度的tab？ => string的比较是按位比较的 => 只检索hour维度没问题，检索daily维度时，先检索出来再筛除
	startTimeStamp, _ := strconv.ParseInt(request.StartTime, 10, 64)
	endTimeStamp, _ := strconv.ParseInt(request.EndTime, 10, 64)
	if endTimeStamp-startTimeStamp > oneDayUnix {
		timeFormat = VolumeDashboardDayFormat
	} else {
		timeFormat = VolumeDashboardHourFormat
	}
	nowTime := timeutil.GetCurrentUnixTimeStamp(ctx)
	if endTimeStamp > nowTime {
		endTimeStamp = nowTime
	}
	startTimeStr = timeutil.ConvertTimeStampToTime(startTimeStamp).Format(timeFormat)
	endTimeStr = timeutil.ConvertTimeStampToTime(endTimeStamp).Format(timeFormat)
	condition := map[string]interface{}{
		"stat_time >= ?": startTimeStr,
		"stat_time <= ?": endTimeStr,
	}
	if request.BusinessType != ALL {
		condition["business_type = ?"] = request.BusinessType
	}
	statType := constant.MaskingProductVolumeStat
	// 如果fulfillment product不为空，则表示需要查询到fulfillment product级别
	// 考虑others设计，对于filter by fulfillment，则需要加上maskingProduct的过滤条件
	//condition["mask_product_id in (?)"] = request.MaskingProductList
	if len(request.FulfillmentProductList) > 0 {
		statType = constant.FulfillmentProductVolumeStat
	}

	var useMultiStatType bool
	//SSCSMR-2371:zone\route\shop group id 三选一
	if len(request.ZoneCodeList) > 0 {
		statType = constant.ZoneCodeVolumeStat
		condition["zone_code in (?)"] = request.ZoneCodeList
	} else if len(request.RouteCodeList) > 0 {
		statType = constant.RouteCodeVolumeStat
		condition["route_code in (?)"] = request.RouteCodeList
	} else if len(request.ShopGroupIdList) > 0 {
		statType = constant.ShopGroupVolumeStat
		condition["shop_group_id in (?)"] = request.ShopGroupIdList
	} else if len(request.GroupCodeList) > 0 {
		condition["stat_type in (?)"] = []uint8{constant.FulfillmentProductVolume, constant.GroupCodeVolumeStat}
		useMultiStatType = true
	}
	if !useMultiStatType {
		condition["stat_type = ?"] = statType
	}
	return condition
}

func (m *MaskingVolumeServiceImpl) UploadMaskingVolumeFile(ctx context.Context, fileInfoList []*fileutil.FileInfo) (string, *srerr.Error) {
	// 生成excel文件
	newFile, fErr := fileutil.MakeExcelWithMultiSheet(ctx, fileInfoList)
	if fErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, fErr)
	}
	b, wErr := newFile.WriteToBuffer()
	if wErr != nil {
		return "", srerr.With(srerr.SaveExcelErr, nil, wErr)
	}
	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	s3Key := fmt.Sprintf("Masking-volume-dashboard-%v%s", timeutil.FormatDate(timeutil.GetCurrentTime(ctx)), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, s3Key, b); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, s3Key), nil

}

func (m *MaskingVolumeServiceImpl) GetSubFactorVolume(ctx context.Context, request *admin_protocol.SearchMaskingVolumeRequest, statType uint8) ([][]string, *srerr.Error) {
	var (
		startTimeStr, endTimeStr string
		timeFormat               string
	)
	//转换条件，判断是否跨天
	//todo:SSCSMR-2371 bug => 如何区分天和hour维度的tab？ => string的比较是按位比较的 => 只检索hour维度没问题，检索daily维度时，先检索出来再筛除
	startTimeStamp, _ := strconv.ParseInt(request.StartTime, 10, 64)
	endTimeStamp, _ := strconv.ParseInt(request.EndTime, 10, 64)
	if endTimeStamp-startTimeStamp > oneDayUnix {
		timeFormat = VolumeDashboardDayFormat
	} else {
		timeFormat = VolumeDashboardHourFormat
	}
	startTimeStr = timeutil.ConvertTimeStampToTime(startTimeStamp).Format(timeFormat)
	endTimeStr = timeutil.ConvertTimeStampToTime(endTimeStamp).Format(timeFormat)
	condition := map[string]interface{}{
		"stat_time >= ?": startTimeStr,
		"stat_time <= ?": endTimeStr,
		"stat_type = ?":  statType,
	}
	if request.BusinessType != ALL {
		condition["business_type = ?"] = request.BusinessType
	}
	maskingProductOrderTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "select zone code volume error", err)
	}
	var productList []int64
	productMap := make(map[int64]bool)
	if request.ProductType == constant.MaskingProductVolume {
		productList = append(productList, request.MaskingProductList...)
	} else {
		productList = append(productList, request.FulfillmentProductList...)
	}
	for _, product := range productList {
		productMap[product] = true
	}
	aggregationResultMap := make(map[string]int64)
	var factorList []string
	factorMap := make(map[string]bool)
	var statTimeList []string
	statTimeMap := make(map[string]bool)
	var titleList []string
	for _, product := range productList {
		titleList = append(titleList, strconv.FormatInt(product, 10))
	}
	for _, maskingProductOrderTab := range maskingProductOrderTabList {
		if maskingProductOrderTab.GroupCode != "" {
			continue
		}
		var key string
		var tempProduct int64
		if request.ProductType == constant.MaskingProductVolume {
			tempProduct = maskingProductOrderTab.MaskProductId
		} else if request.ProductType == constant.FulfillmentProductVolume {
			tempProduct = maskingProductOrderTab.FulfillmentProductId
		}
		if _, ok := productMap[tempProduct]; ok {
			key = fmt.Sprintf("%v-%v-%v-%v", getFactorName(maskingProductOrderTab, statType), tempProduct,
				maskingProductOrderTab.StatTime, maskingProductOrderTab.BusinessType)
		} else {
			key = fmt.Sprintf("%v-%v-%v-%v", getFactorName(maskingProductOrderTab, statType), AggregationOthers,
				maskingProductOrderTab.StatTime, maskingProductOrderTab.BusinessType)
		}
		aggregationResultMap[key] += maskingProductOrderTab.OrderNum

		totalKey := fmt.Sprintf("%v-%v-%v-%v", getFactorName(maskingProductOrderTab, statType), AggregationTotal,
			maskingProductOrderTab.StatTime, maskingProductOrderTab.BusinessType)
		aggregationResultMap[totalKey] += maskingProductOrderTab.OrderNum

		// 记录有哪些时间区间
		if _, ok := statTimeMap[maskingProductOrderTab.StatTime]; !ok {
			statTimeMap[maskingProductOrderTab.StatTime] = true
			statTimeList = append(statTimeList, maskingProductOrderTab.StatTime)
		}
		if _, ok := factorMap[maskingProductOrderTab.ZoneCode]; !ok {
			factorMap[maskingProductOrderTab.ZoneCode] = true
			factorList = append(factorList, maskingProductOrderTab.ZoneCode)
		}
	}
	// 判断是否有others列
	isHaveOthers, err1 := m.isHaveOthers(ctx, request, false)
	if err1 != nil {
		return nil, err1
	}
	if isHaveOthers {
		titleList = append(titleList, AggregationOthers)
	}
	titleList = append(titleList, AggregationTotal)
	var data [][]string
	for _, factor := range factorList {
		for _, statTime := range statTimeList {
			for _, title := range titleList {
				if request.BusinessType == MPL || request.BusinessType == ALL {
					key := fmt.Sprintf("%v-%v-%v-%v", factor, title, statTime, MPL)
					rowData := []string{factor, statTime, title, strconv.FormatInt(aggregationResultMap[key], 10), "MPL"}
					data = append(data, rowData)
				}
				if request.BusinessType == WMS || request.BusinessType == ALL {
					key := fmt.Sprintf("%v-%v-%v-%v", factor, title, statTime, WMS)
					rowData := []string{factor, statTime, title, strconv.FormatInt(aggregationResultMap[key], 10), "WMS"}
					data = append(data, rowData)
				}
			}
		}
	}
	return data, nil
}

func getFactorName(maskingProductOrderNumTab *repository.MaskingProductOrderNumTab, statType uint8) string {
	switch statType {
	case constant.ZoneCodeVolumeStat:
		return maskingProductOrderNumTab.ZoneCode
	case constant.RouteCodeVolumeStat:
		return maskingProductOrderNumTab.RouteCode
	case constant.ShopGroupVolumeStat:
		return maskingProductOrderNumTab.ShopGroupId
	}
	return ""
}

func (m *MaskingVolumeServiceImpl) reportMaskingProductVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []int64, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var deleteMaskingProductList []int64
	deleteMaskingProductMap := make(map[int]interface{})
	maskingProductRefMap := localcache.AllItems(ctx, constant.MaskingProductRef)
	for _, value := range maskingProductRefMap {
		maskingProductRef := value.(*lpsclient.GetMaskingProductRefData)
		// 查询masking product运力
		volume, err := m.VolumeCounter.GetMaskProductVolume(ctx, int64(maskingProductRef.MaskingProductId), nowTime, rule_mode.MplOrderRule)
		volumeWms, errWms := m.VolumeCounter.GetMaskProductVolume(ctx, int64(maskingProductRef.MaskingProductId), nowTime, rule_mode.WmsOrderRule)
		if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
			continue
		}
		if err != nil && errWms != nil {
			return nil, nil, err
		}
		if err == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(maskingProductRef.MaskingProductId),
				0, "", "", "", "", constant.MaskingProductVolumeStat,
				statTime, volume, MPL, nowTime, nowTime))
		}
		if errWms == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(maskingProductRef.MaskingProductId),
				0, "", "", "", "", constant.MaskingProductVolumeStat,
				statTime, volumeWms, WMS, nowTime, nowTime))
		}
		if _, ok := deleteMaskingProductMap[maskingProductRef.MaskingProductId]; !ok {
			deleteMaskingProductList = append(deleteMaskingProductList, int64(maskingProductRef.MaskingProductId))
			deleteMaskingProductMap[maskingProductRef.MaskingProductId] = nil
		}
	}
	return orderNumTabList, deleteMaskingProductList, nil
}

func (m *MaskingVolumeServiceImpl) reportFulfillmentProductVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []int64, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var deleteFulfillmentProductList []int64
	deleteFulfillmentProductMap := make(map[int]interface{})
	maskingProductRefMap := localcache.AllItems(ctx, constant.MaskingProductRef)
	for _, value := range maskingProductRefMap {
		maskingProductRef := value.(*lpsclient.GetMaskingProductRefData)
		// 查询fulfillment product运力
		for _, fulfillmentProductId := range maskingProductRef.ComponentProductId {
			volume, err := m.VolumeCounter.GetFulfillmentProductVolume(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.MplOrderRule, nowTime)
			volumeWms, errWms := m.VolumeCounter.GetFulfillmentProductVolume(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.WmsOrderRule, nowTime)
			if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
				continue
			}
			if err != nil && errWms != nil {
				return nil, nil, err
			}
			if err == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(maskingProductRef.MaskingProductId),
					int64(fulfillmentProductId), "", "", "", "", constant.FulfillmentProductVolumeStat,
					statTime, volume, MPL, nowTime, nowTime))
			}
			if errWms == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(maskingProductRef.MaskingProductId),
					int64(fulfillmentProductId), "", "", "", "", constant.FulfillmentProductVolumeStat,
					statTime, volumeWms, WMS, nowTime, nowTime))
			}
			if _, ok := deleteFulfillmentProductMap[fulfillmentProductId]; !ok {
				deleteFulfillmentProductList = append(deleteFulfillmentProductList, int64(fulfillmentProductId))
				deleteFulfillmentProductMap[fulfillmentProductId] = nil
			}
		}
	}
	return orderNumTabList, deleteFulfillmentProductList, nil
}

func (m *MaskingVolumeServiceImpl) reportGroupCodeVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []string, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var deleteGroupCodeList []string
	deleteGroupCodeMap := make(map[string]interface{})
	groupCodeListMap := localcache.AllItems(ctx, constant.GroupCodeList)
	// 查询group code运力
	for groupCode := range groupCodeListMap {
		volume, err := m.VolumeCounter.GetGroupCodeVolume(ctx, groupCode, rule_mode.MplOrderRule, nowTime)
		volumeWms, errWms := m.VolumeCounter.GetGroupCodeVolume(ctx, groupCode, rule_mode.WmsOrderRule, nowTime)
		if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
			continue
		}
		if err != nil && errWms != nil {
			return nil, nil, err
		}
		if err == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
				0, groupCode, "", "", "", constant.GroupCodeVolumeStat,
				statTime, volume, MPL, nowTime, nowTime))
		}
		if errWms == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
				0, groupCode, "", "", "", constant.GroupCodeVolumeStat,
				statTime, volumeWms, WMS, nowTime, nowTime))
		}
		if _, ok := deleteGroupCodeMap[groupCode]; !ok {
			deleteGroupCodeList = append(deleteGroupCodeList, groupCode)
			deleteGroupCodeMap[groupCode] = nil
		}
	}
	return orderNumTabList, deleteGroupCodeList, nil
}

func (m *MaskingVolumeServiceImpl) reportZoneCodeVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []string, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var zoneVolumes []*rulevolume.MaskZoneVolumeTab
	if err := dbutil.Distinct(ctx, rulevolume.MaskZoneVolumeTabHook, nil, "mask_product_id, component_product_id, zone_code", &zoneVolumes); err != nil {
		return nil, nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	zoneCodeMap := make(map[string]bool)
	var zoneCodeList []string
	for _, zoneVolume := range zoneVolumes {
		volume, err := m.VolumeCounter.GetMaskZoneVolume(ctx, int64(zoneVolume.MaskProductID), int64(zoneVolume.ComponentProductID), zoneVolume.ZoneCode, rulevolume2.MaskZoneDirectionDest, rule_mode.MplOrderRule, nowTime)
		volumeWms, errWms := m.VolumeCounter.GetMaskZoneVolume(ctx, int64(zoneVolume.MaskProductID), int64(zoneVolume.ComponentProductID), zoneVolume.ZoneCode, rulevolume2.MaskZoneDirectionDest, rule_mode.WmsOrderRule, nowTime)
		if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
			continue
		}
		if err != nil && errWms != nil {
			return nil, nil, err
		}
		if err == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(zoneVolume.MaskProductID),
				int64(zoneVolume.ComponentProductID), "", zoneVolume.ZoneCode, "", "", constant.ZoneCodeVolumeStat,
				statTime, volume, MPL, nowTime, nowTime))
		}
		if errWms == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(zoneVolume.MaskProductID),
				int64(zoneVolume.ComponentProductID), "", zoneVolume.ZoneCode, "", "", constant.ZoneCodeVolumeStat,
				statTime, volumeWms, WMS, nowTime, nowTime))
		}
		if _, ok := zoneCodeMap[zoneVolume.ZoneCode]; !ok {
			zoneCodeMap[zoneVolume.ZoneCode] = true
			zoneCodeList = append(zoneCodeList, zoneVolume.ZoneCode)
		}
	}

	groupCodeListMap := localcache.AllItems(ctx, constant.GroupCodeList)
	for _, zoneCode := range zoneCodeList {
		for groupCode := range groupCodeListMap {
			volume, err := m.VolumeCounter.GetGroupZoneVolume(ctx, groupCode, zoneCode, rulevolume2.MaskZoneDirectionDest, rule_mode.MplOrderRule, nowTime)
			volumeWms, errWms := m.VolumeCounter.GetGroupZoneVolume(ctx, groupCode, zoneCode, rulevolume2.MaskZoneDirectionDest, rule_mode.WmsOrderRule, nowTime)
			if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
				continue
			}
			if err != nil && errWms != nil {
				return nil, nil, err
			}
			if err == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
					0, groupCode, zoneCode, "", "", constant.ZoneCodeVolumeStat,
					statTime, volume, MPL, nowTime, nowTime))
			}
			if errWms == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
					0, groupCode, zoneCode, "", "", constant.ZoneCodeVolumeStat,
					statTime, volumeWms, WMS, nowTime, nowTime))
			}
		}
	}
	return orderNumTabList, zoneCodeList, nil
}

func (m *MaskingVolumeServiceImpl) reportRouteCodeVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []string, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var routeVolumes []*rulevolume.MaskRouteVolumeTab
	if err := dbutil.Distinct(ctx, rulevolume.MaskRouteVolumeTabHook, nil, "mask_product_id, component_product_id, route_code", &routeVolumes); err != nil {
		return nil, nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	routeCodeMap := make(map[string]bool)
	var routeCodeList []string
	for _, routeVolume := range routeVolumes {
		volume, err := m.VolumeCounter.GetMaskRouteVolume(ctx, int64(routeVolume.MaskProductID), int64(routeVolume.ComponentProductID), routeVolume.RouteCode, rule_mode.MplOrderRule, nowTime)
		volumeWms, errWms := m.VolumeCounter.GetMaskRouteVolume(ctx, int64(routeVolume.MaskProductID), int64(routeVolume.ComponentProductID), routeVolume.RouteCode, rule_mode.WmsOrderRule, nowTime)
		if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
			continue
		}
		if err != nil && errWms != nil {
			return nil, nil, err
		}
		if err == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(routeVolume.MaskProductID),
				int64(routeVolume.ComponentProductID), "", "", routeVolume.RouteCode, "", constant.RouteCodeVolumeStat,
				statTime, volume, MPL, nowTime, nowTime))
		}
		if errWms == nil {
			orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, int64(routeVolume.MaskProductID),
				int64(routeVolume.ComponentProductID), "", "", routeVolume.RouteCode, "", constant.RouteCodeVolumeStat,
				statTime, volumeWms, WMS, nowTime, nowTime))
		}
		if _, ok := routeCodeMap[routeVolume.RouteCode]; !ok {
			routeCodeMap[routeVolume.RouteCode] = true
			routeCodeList = append(routeCodeList, routeVolume.RouteCode)
		}
	}

	groupCodeListMap := localcache.AllItems(ctx, constant.GroupCodeList)
	for _, routeCode := range routeCodeList {
		for groupCode := range groupCodeListMap {
			volume, err := m.VolumeCounter.GetGroupRouteVolume(ctx, groupCode, routeCode, rule_mode.MplOrderRule, nowTime)
			volumeWms, errWms := m.VolumeCounter.GetGroupRouteVolume(ctx, groupCode, routeCode, rule_mode.WmsOrderRule, nowTime)
			if err.Cause() == redis.Nil && errWms.Cause() == redis.Nil {
				continue
			}
			if err != nil && errWms != nil {
				return nil, nil, err
			}
			if err == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
					0, groupCode, "", routeCode, "", constant.RouteCodeVolumeStat,
					statTime, volume, MPL, nowTime, nowTime))
			}
			if errWms == nil {
				orderNumTabList = append(orderNumTabList, repository.NewMaskingProductOrderNumTab(0, 0,
					0, groupCode, "", routeCode, "", constant.RouteCodeVolumeStat,
					statTime, volumeWms, WMS, nowTime, nowTime))
			}
		}
	}
	return orderNumTabList, routeCodeList, nil
}

func (m *MaskingVolumeServiceImpl) reportShopGroupVolume(ctx context.Context, nowTime int64, statTime string) ([]*repository.MaskingProductOrderNumTab, []int64, *srerr.Error) {
	var orderNumTabList []*repository.MaskingProductOrderNumTab
	var logisticProductPriorityTabList []*productpriority.LogisticProductPriorityTab
	if err := dbutil.Distinct(ctx, productpriority.LogisticProductPriorityTabHook, nil, "shop_group_id", &logisticProductPriorityTabList); err != nil {
		return nil, nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	logger.CtxLogInfof(ctx, "reportShopGroupVolume|length of shop group:%v", len(logisticProductPriorityTabList))
	groupCodeListMap := localcache.AllItems(ctx, constant.GroupCodeList)
	shopGroupIdMap := make(map[int64]bool)
	var shopGroupIdList []int64
	maskingProductRefMap := localcache.AllItems(ctx, constant.MaskingProductRef)
	for _, value := range maskingProductRefMap {
		maskingProductRef := value.(*lpsclient.GetMaskingProductRefData)
		for _, fulfillmentProductId := range maskingProductRef.ComponentProductId {
			for _, logisticProductPriorityTab := range logisticProductPriorityTabList {
				volume, err := m.VolumeCounter.GetMaskShopGroupVolume(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), logisticProductPriorityTab.ShopGroupID, rule_mode.MplOrderRule, nowTime)
				if err.Cause() == redis.Nil {
					continue
				}
				if err != nil {
					return nil, nil, err
				}
				orderNumTab := &repository.MaskingProductOrderNumTab{
					MaskProductId:        int64(maskingProductRef.MaskingProductId),
					FulfillmentProductId: int64(fulfillmentProductId),
					ShopGroupId:          strconv.FormatInt(logisticProductPriorityTab.ShopGroupID, 10),
					StatType:             constant.ShopGroupVolumeStat,
					StatTime:             statTime,
					OrderNum:             volume,
					BusinessType:         MPL,
					Ctime:                nowTime,
					Mtime:                nowTime,
				}
				orderNumTabList = append(orderNumTabList, orderNumTab)
				if _, ok := shopGroupIdMap[logisticProductPriorityTab.ShopGroupID]; !ok {
					shopGroupIdMap[logisticProductPriorityTab.ShopGroupID] = true
					shopGroupIdList = append(shopGroupIdList, logisticProductPriorityTab.ShopGroupID)
				}
			}
		}
	}

	for _, shopGroupID := range shopGroupIdList {
		for groupCode := range groupCodeListMap {
			volume, err := m.VolumeCounter.GetGroupShopGroupVolume(ctx, groupCode, shopGroupID, rule_mode.MplOrderRule, nowTime)
			if err.Cause() == redis.Nil {
				continue
			}
			if err != nil {
				return nil, nil, err
			}
			orderNumTab := &repository.MaskingProductOrderNumTab{
				GroupCode:    groupCode,
				ShopGroupId:  strconv.FormatInt(shopGroupID, 10),
				StatType:     constant.ShopGroupVolumeStat,
				StatTime:     statTime,
				OrderNum:     volume,
				BusinessType: MPL,
				Ctime:        nowTime,
				Mtime:        nowTime,
			}
			orderNumTabList = append(orderNumTabList, orderNumTab)
		}
	}
	return orderNumTabList, shopGroupIdList, nil
}

//// checkIsNeedMerge 检查是否需要合并昨天的数据，true：需要合并，false：不需要合并
//func (m *MaskingVolumeServiceImpl) checkIsNeedMerge(ctx context.Context, yesterday string) (bool, *srerr.Error) {
//	// 判断昨天是否有数据或已经合并过，如果没有数据或已经合并过，则不需要再合并。通过yesterday查询，存在数据则表示已经合并过
//	condition := map[string]interface{}{
//		"stat_time": yesterday,
//	}
//	yetOrderNumTab, err := m.MaskProductOrderNumRepo.GetMaskingProductOrderNum(ctx, condition)
//	if err != nil {
//		return false, srerr.With(srerr.DatabaseErr, nil, err)
//	}
//	// yetOrderNumTab不为nil则表示已经合并过，不需要再合并
//	if yetOrderNumTab != nil && yetOrderNumTab.MaskProductId != 0 {
//		return false, nil
//	}
//	// 判断昨天是否有数据
//	condition = map[string]interface{}{
//		"stat_time >= ?": yesterday + "00",
//		"stat_time <= ?": yesterday + "23",
//	}
//	yetOrderNumTab, err = m.MaskProductOrderNumRepo.GetMaskingProductOrderNum(ctx, condition)
//	if err != nil {
//		return false, srerr.With(srerr.DatabaseErr, nil, err)
//	}
//	// 查不到昨天的数据，则表示昨天没数据，不需要继续执行
//	if yetOrderNumTab == nil || yetOrderNumTab.MaskProductId == 0 {
//		return false, nil
//	}
//	return true, nil
//}

func (m *MaskingVolumeServiceImpl) mergeMaskingProductVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.MaskingProductVolumeStat,
	}
	maskingProductOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	maskingProductMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, maskingProductOrderNumTab := range maskingProductOrderNumTabList {
		key := fmt.Sprintf("%v-%v", maskingProductOrderNumTab.MaskProductId, maskingProductOrderNumTab.BusinessType)
		if _, ok := maskingProductMergeMap[key]; ok {
			maskingProductMergeMap[key].OrderNum = mathutil.MaxInt64(maskingProductMergeMap[key].OrderNum, maskingProductOrderNumTab.OrderNum)
		} else {
			maskingProductOrderNumTab.StatTime = yesterday
			maskingProductMergeMap[key] = maskingProductOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, maskingProductOrderNum := range maskingProductMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, maskingProductOrderNum)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) mergeFulfillmentProductVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.FulfillmentProductVolumeStat,
	}
	fulfillmentProductOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	fulfillmentProductMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, fulfillmentProductOrderNumTab := range fulfillmentProductOrderNumTabList {
		key := fmt.Sprintf("%v-%v-%v", fulfillmentProductOrderNumTab.MaskProductId, fulfillmentProductOrderNumTab.FulfillmentProductId, fulfillmentProductOrderNumTab.BusinessType)
		if _, ok := fulfillmentProductMergeMap[key]; ok {
			fulfillmentProductMergeMap[key].OrderNum = mathutil.MaxInt64(fulfillmentProductMergeMap[key].OrderNum, fulfillmentProductOrderNumTab.OrderNum)
		} else {
			fulfillmentProductOrderNumTab.StatTime = yesterday
			fulfillmentProductMergeMap[key] = fulfillmentProductOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, fulfillmentProductOrderNumTab := range fulfillmentProductMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, fulfillmentProductOrderNumTab)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) mergeGroupCodeVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.GroupCodeVolumeStat,
	}
	groupCodeOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	groupCodeMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, groupCodeOrderNumTab := range groupCodeOrderNumTabList {
		key := fmt.Sprintf("%v-%v", groupCodeOrderNumTab.GroupCode, groupCodeOrderNumTab.BusinessType)
		if _, ok := groupCodeMergeMap[key]; ok {
			groupCodeMergeMap[key].OrderNum = mathutil.MaxInt64(groupCodeMergeMap[key].OrderNum, groupCodeOrderNumTab.OrderNum)
		} else {
			groupCodeOrderNumTab.StatTime = yesterday
			groupCodeMergeMap[key] = groupCodeOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, groupCodeOrderNumTab := range groupCodeMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, groupCodeOrderNumTab)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) mergeRouteCodeVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.RouteCodeVolumeStat,
	}
	routeCodeOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	routeCodeMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, routeCodeOrderNumTab := range routeCodeOrderNumTabList {
		key := fmt.Sprintf("%v-%v-%v-%v-%v", routeCodeOrderNumTab.MaskProductId, routeCodeOrderNumTab.FulfillmentProductId,
			routeCodeOrderNumTab.GroupCode, routeCodeOrderNumTab.RouteCode, routeCodeOrderNumTab.BusinessType)
		if _, ok := routeCodeMergeMap[key]; ok {
			routeCodeMergeMap[key].OrderNum = mathutil.MaxInt64(routeCodeMergeMap[key].OrderNum, routeCodeOrderNumTab.OrderNum)
		} else {
			routeCodeOrderNumTab.StatTime = yesterday
			routeCodeMergeMap[key] = routeCodeOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, routeCodeOrderNumTab := range routeCodeMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, routeCodeOrderNumTab)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) mergeZoneCodeVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.ZoneCodeVolumeStat,
	}
	zoneCodeOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	zoneCodeMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, zoneCodeOrderNumTab := range zoneCodeOrderNumTabList {
		key := fmt.Sprintf("%v-%v-%v-%v-%v", zoneCodeOrderNumTab.MaskProductId, zoneCodeOrderNumTab.FulfillmentProductId,
			zoneCodeOrderNumTab.GroupCode, zoneCodeOrderNumTab.ZoneCode, zoneCodeOrderNumTab.BusinessType)
		if _, ok := zoneCodeMergeMap[key]; ok {
			zoneCodeMergeMap[key].OrderNum = mathutil.MaxInt64(zoneCodeMergeMap[key].OrderNum, zoneCodeOrderNumTab.OrderNum)
		} else {
			zoneCodeOrderNumTab.StatTime = yesterday
			zoneCodeMergeMap[key] = zoneCodeOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, zoneCodeOrderNumTab := range zoneCodeMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, zoneCodeOrderNumTab)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) mergeShopGroupVolume(ctx context.Context, yesterday string) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	condition := map[string]interface{}{
		"stat_time >= ?": yesterday + "00",
		"stat_time <= ?": yesterday + "23",
		"stat_type = ?":  constant.ShopGroupVolumeStat,
	}
	shopGroupOrderNumTabList, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}
	shopGroupMergeMap := make(map[string]*repository.MaskingProductOrderNumTab)
	for _, shopGroupOrderNumTab := range shopGroupOrderNumTabList {
		key := fmt.Sprintf("%v-%v-%v-%v", shopGroupOrderNumTab.MaskProductId, shopGroupOrderNumTab.FulfillmentProductId, shopGroupOrderNumTab.GroupCode, shopGroupOrderNumTab.ShopGroupId)
		if _, ok := shopGroupMergeMap[key]; ok {
			shopGroupMergeMap[key].OrderNum = mathutil.MaxInt64(shopGroupMergeMap[key].OrderNum, shopGroupOrderNumTab.OrderNum)
		} else {
			shopGroupOrderNumTab.StatTime = yesterday
			shopGroupMergeMap[key] = shopGroupOrderNumTab
		}
	}
	var newMaskingProductOrderNumTabList []*repository.MaskingProductOrderNumTab
	for _, shopGroupOrderNumTab := range shopGroupMergeMap {
		newMaskingProductOrderNumTabList = append(newMaskingProductOrderNumTabList, shopGroupOrderNumTab)
	}
	return copyTabsWithoutId(ctx, newMaskingProductOrderNumTabList), nil
}

func (m *MaskingVolumeServiceImpl) getVolumeByCondition(ctx context.Context, condition map[string]interface{}) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
	tabs, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition, configutil.GetMaskingDashboardSwitch(ctx))
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return tabs, nil
}

//func (m *MaskingVolumeServiceImpl) getSubByCondition(ctx context.Context, condition map[string]interface{}) ([]*repository.MaskingProductOrderNumTab, *srerr.Error) {
//	tabs, err := m.MaskProductOrderNumRepo.ListByParam(ctx, condition)
//	if err != nil {
//		return nil, srerr.With(srerr.DatabaseErr, nil, err)
//	}
//
//	return tabs, nil
//}

func getDeleteIds(tabs []*repository.MaskingProductOrderNumTab) []uint64 {
	if len(tabs) == 0 {
		return nil
	}

	ids := make([]uint64, len(tabs))
	for i := 0; i < len(tabs); i++ {
		ids[i] = tabs[i].ID
	}

	return ids
}

func copyTabsWithoutId(ctx context.Context, originTabs []*repository.MaskingProductOrderNumTab) []*repository.MaskingProductOrderNumTab {
	if len(originTabs) == 0 {
		return nil
	}

	tabs := make([]*repository.MaskingProductOrderNumTab, len(originTabs))

	currentTimestamp := timeutil.GetCurrentUnixTimeStamp(ctx)
	for i := 0; i < len(originTabs); i++ {
		originTab := originTabs[i]
		tabs[i] = &repository.MaskingProductOrderNumTab{
			MaskProductId:        originTab.MaskProductId,
			FulfillmentProductId: originTab.FulfillmentProductId,
			GroupCode:            originTab.GroupCode,
			ZoneCode:             originTab.ZoneCode,
			RouteCode:            originTab.RouteCode,
			ShopGroupId:          originTab.ShopGroupId,
			StatType:             originTab.StatType,
			StatTime:             originTab.StatTime,
			OrderNum:             originTab.OrderNum,
			BusinessType:         originTab.BusinessType,
			Ctime:                currentTimestamp,
			Mtime:                currentTimestamp,
		}
	}

	return tabs
}
