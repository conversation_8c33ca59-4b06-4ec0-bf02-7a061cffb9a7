package volume_dashboard

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/persistent"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type VolumeChangeService interface {
	ChangeToVolumeManagement(ctx context.Context, productId int64) (bool, *srerr.Error)
}

type VolumeChangeServiceImpl struct {
	configService routing_config.RoutingConfigService
	softRuleRepo  ruledata.SoftRuleRepo
}

func NewVolumeChangeServiceImpl(configService routing_config.RoutingConfigService, softRuleRepo ruledata.SoftRuleRepo) *VolumeChangeServiceImpl {
	return &VolumeChangeServiceImpl{configService: configService, softRuleRepo: softRuleRepo}
}

func (v *VolumeChangeServiceImpl) ChangeToVolumeManagement(ctx context.Context, productId int64) (bool, *srerr.Error) {
	// 1.找到对应的product config
	config, cerr := v.configService.GetRoutingConfigByProductID(ctx, productId)
	if cerr != nil {
		return false, cerr
	}
	// 未开启smart routing走旧逻辑
	if !config.IsSupportSmartRouting() {
		return false, nil
	}
	// 2.如果是local spx的直接走volume management
	if config.SpxSmartRoutingEnabled || config.LocalSmartRoutingEnabled {
		return true, nil
	}
	// 3.找到这个渠道对应的soft rule
	const multiProduct = true
	softRuleList, serr := v.softRuleRepo.GetActiveRuleListByProduct(ctx, int(productId), config.GetRoutingType(), multiProduct)
	if serr != nil {
		return false, nil
	}
	// 4.找不到soft rule就报错
	if len(softRuleList) == 0 {
		return false, srerr.New(srerr.RuleNotFound, nil, "soft rule not found productId=%d", productId)
	}
	// 5.这里所以的soft rule都对应一个volume rule
	volumeRuleId := softRuleList[0].VolumeRuleId
	// 6.查找这个渠道对应的volume rule,这里直接调用现有的方法是存在循环导包
	var volumeRule persistent.VolumeRoutingRuleTab
	if err := dbutil.Select(ctx, persistent.VolumeRoutingRuleHook, map[string]interface{}{
		"id = ?":          volumeRuleId,
		"rule_status = ?": enum.VolumeRuleStatusActive,
	}, &volumeRule); err != nil {
		return false, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return volumeRule.Id != 0, nil
}
