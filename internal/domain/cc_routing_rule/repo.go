package cc_routing_rule

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

type CCRoutingRuleRepo interface {
	CreateRoutingRule(ctx context.Context, data *CCRoutingRuleTab) (int, *srerr.Error)
	DeleteRoutingRule(ctx context.Context, id int) *srerr.Error
	UpdateCCRoutingRuleByID(ctx context.Context, id int, updateData map[string]interface{}) *srerr.Error
	GetCCRoutingRuleByID(ctx context.Context, id int) (*CCRoutingRuleTab, *srerr.Error)
	ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]CCRoutingRuleTab, int64, *srerr.Error)
	GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*CCRoutingRuleTab, *srerr.Error)
}

type CCRoutingRuleRepoImpl struct {
}

func NewCCRoutingRuleRepoImpl() *CCRoutingRuleRepoImpl {
	return &CCRoutingRuleRepoImpl{}
}

func (c *CCRoutingRuleRepoImpl) CreateRoutingRule(ctx context.Context, data *CCRoutingRuleTab) (int, *srerr.Error) {
	cur := uint64(timeutil.GetCurrentUnixTimeStamp(ctx))
	data.Ctime = cur
	data.Mtime = cur

	if err := dbutil.Insert(ctx, data, dbutil.ModelInfo{
		FulfillmentProductId: uint64(data.ProductId),
		ModelName:            data.TableName(),
	}); err != nil {
		return 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return data.Id, nil
}

func (c *CCRoutingRuleRepoImpl) DeleteRoutingRule(ctx context.Context, id int) *srerr.Error {
	if err := dbutil.Delete(ctx, CCRoutingRuleTabHook, map[string]interface{}{"id = ?": id}, dbutil.ModelInfo{
		RuleId:    uint64(id),
		ModelName: CCRoutingRuleTabHook.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, nil, err)
	}

	return nil
}

func (c *CCRoutingRuleRepoImpl) UpdateCCRoutingRuleByID(ctx context.Context, id int, updateData map[string]interface{}) *srerr.Error {
	updateData["mtime"] = uint64(timeutil.GetCurrentUnixTimeStamp(ctx))
	if err := dbutil.Update(ctx, CCRoutingRuleTabHook, map[string]interface{}{"id = ?": id}, updateData, dbutil.ModelInfo{
		RuleId:    uint64(id),
		ModelName: CCRoutingRuleTabHook.TableName(),
	}); err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}

	return nil
}

func (c *CCRoutingRuleRepoImpl) GetCCRoutingRuleByID(ctx context.Context, id int) (*CCRoutingRuleTab, *srerr.Error) {
	var ret CCRoutingRuleTab
	if err := dbutil.Select(ctx, CCRoutingRuleTabHook, map[string]interface{}{"id = ?": id}, &ret); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, id, err)
	}

	return &ret, nil
}

func (c *CCRoutingRuleRepoImpl) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]CCRoutingRuleTab, int64, *srerr.Error) {
	var total int64
	if err := dbutil.Count(ctx, CCRoutingRuleTabHook, condition, &total); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, condition, err)
	}

	var list []CCRoutingRuleTab
	if err := dbutil.Select(ctx, CCRoutingRuleTabHook, condition, &list, dbutil.WithPage(offset, size), dbutil.WithOrder("id DESC")); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, condition, err)
	}

	return list, total, nil
}

func (c *CCRoutingRuleRepoImpl) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*CCRoutingRuleTab, *srerr.Error) {
	var ret CCRoutingRuleTab
	if err := dbutil.Take(ctx, CCRoutingRuleTabHook, map[string]interface{}{"product_id = ?": productId}, &ret); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, productId, err)
	}

	return &ret, nil
}
