package cc_routing_rule

import "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"

var (
	CCRoutingRuleTabHook = &CCRoutingRuleTab{}
)

type CCRoutingRuleTab struct {
	Id          int           `gorm:"column:id"`
	ProductId   int           `gorm:"column:product_id"`
	RoutingType CCRoutingType `gorm:"column:routing_type"`
	RuleDetail  string        `gorm:"column:rule_detail"`
	OperatedBy  string        `gorm:"column:operated_by"`
	Ctime       uint64        `gorm:"column:ctime;autoCreateTime"`
	Mtime       uint64        `gorm:"column:mtime;autoUpdateTime"`
}

func (c CCRoutingRuleTab) TableName() string {
	return "cc_routing_rule_tab"
}

func (c CCRoutingRuleTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (c CCRoutingRuleTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (c CCRoutingRuleTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   uint64(c.Id),
		ModelName:            c.TableName(),
		FulfillmentProductId: uint64(c.ProductId),
	}
}
