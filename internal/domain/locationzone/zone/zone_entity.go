package zone

// 业务数据模型层
type (
	LocationZone struct {
		Id          uint64 `json:"id"`
		ProductID   uint64 `json:"product_id"`
		ZoneCode    string `json:"zone_code"`
		State       string `json:"state"`
		City        string `json:"city"`
		District    string `json:"district"`
		Street      string `json:"street"`
		DistrictId  uint   `json:"district_id"`
		ZoneType    uint8  `json:"zone_type"` // 0:Running 1:Forecasting
		RoutingType uint8  `json:"routing_type"`
		OperateBy   string `json:"operate_by"`
		Ctime       uint   `json:"ctime"` // ctime
		Mtime       uint   `json:"mtime"` // mtime
	}

	ZoneQuery struct {
		PageNO             int32
		Limit              int64
		Offset             int64
		ZoneCode           string
		FilterByZoneCode   bool
		DistrictId         uint
		FilterByDistrictId bool
		State              string
		FilterByState      bool
		City               string
		FilterByCity       bool
		District           string
		FilterByDistrict   bool
		Street             string
		FilterByStreet     bool
		ProductID          int
		FilterByProductID  bool
		ZoneType           int
		FilterByZoneType   bool
		RoutingType        uint8
	}
)

func NewZoneQuery() *ZoneQuery {
	return &ZoneQuery{Offset: 0, Limit: 20}
}

func (q *ZoneQuery) ByZoneCode(zoneCode string) *ZoneQuery {
	q.FilterByZoneCode = true
	q.ZoneCode = zoneCode
	return q
}

func (q *ZoneQuery) ByDistrictId(districtId uint) *ZoneQuery {
	q.FilterByDistrictId = true
	q.DistrictId = districtId
	return q
}

func (q *ZoneQuery) ByState(state string) *ZoneQuery {
	q.FilterByState = true
	q.State = state
	return q
}

func (q *ZoneQuery) ByCity(city string) *ZoneQuery {
	q.FilterByCity = true
	q.City = city
	return q
}

func (q *ZoneQuery) ByDistrict(district string) *ZoneQuery {
	q.FilterByDistrict = true
	q.District = district
	return q
}

func (q *ZoneQuery) ByStreet(street string) *ZoneQuery {
	q.FilterByStreet = true
	q.Street = street
	return q
}

func (q *ZoneQuery) WithPage(pageno int64, limit int64) *ZoneQuery {
	if pageno <= 0 {
		pageno = 1
	}
	if limit <= 0 {
		limit = 20
	}
	if limit > 10000 {
		limit = 10000
	}
	q.Limit = limit
	q.Offset = (pageno - 1) * limit
	return q
}

func (q *ZoneQuery) ByProductID(productID int) *ZoneQuery {
	q.FilterByProductID = true
	q.ProductID = productID
	return q
}

func (q *ZoneQuery) ByZoneType(zoneType int) *ZoneQuery {
	q.FilterByZoneType = true
	q.ZoneType = zoneType
	return q
}

func (q *ZoneQuery) WithOffset(offset int64) *ZoneQuery {
	q.Offset = offset
	return q
}

func (q *ZoneQuery) WithLimit(limit int64) *ZoneQuery {
	q.Limit = limit
	return q
}
