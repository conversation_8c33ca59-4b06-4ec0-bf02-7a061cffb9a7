package zone

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
)

//db 数据交互层

const DefaultPagSize = 100

type LocationZoneDaoServer interface {
	BulkInsertZoneCode(ctx context.Context, zoneData []*LocationZone, zoneType uint8) *srerr.Error
	DeleteZoneCodeByProductList(ctx context.Context, productIDList []int, zoneType uint8) *srerr.Error
	FilterLocationZones(ctx context.Context, q *ZoneQuery) ([]*LocationZoneTab, int64, *srerr.Error)
	ListZoneCode(ctx context.Context, productID int, routingType, zoneType uint8) ([]*LocationZoneTab, *srerr.Error)
	DeleteZoneCodeByProductListWithTX(ctx context.Context, productIDList []int, zoneType uint8, tx scormv2.SQLCommon) *srerr.Error
	BatchCreateZoneCode(ctx context.Context, zoneData []*LocationZoneTab, tx scormv2.SQLCommon) *srerr.Error
	GetLocationZoneById(ctx context.Context, id int64) (*LocationZoneTab, error)
	UpdateZoneCode(ctx context.Context, id int64, zoneCode string, districtId uint, state, city, district, street string) error
	DeleteZoneCode(ctx context.Context, id int64) error
	ListPostCodeCode(ctx context.Context, productId int, zoneType uint8, routingType uint8) ([]*LocationZoneTab, *srerr.Error)
	QueryPostCodeByPage(ctx context.Context, condition map[string]interface{}, page, pageSize int64) ([]LocationZoneTab, int64, *srerr.Error)
}

type LocationZoneDaoImpl struct {
}

func NewLocationZoneDaoImpl() *LocationZoneDaoImpl {
	return &LocationZoneDaoImpl{}
}

func (l *LocationZoneDaoImpl) BulkInsertZoneCode(ctx context.Context, zoneData []*LocationZone, zoneType uint8) *srerr.Error {
	//1.给每个元素赋上zoneType
	for i := 0; i < len(zoneData); i++ {
		zoneData[i].ZoneType = zoneType
	}
	//2.插入db
	if err := dbutil.InsertBatch(ctx, LocationZoneTabHook, zoneData); err != nil {
		logger.CtxLogErrorf(ctx, "BulkInsertZoneCode| insert location zone err:%v", err)
		return srerr.With(srerr.DatabaseErr, "insert zone", err)
	}

	return nil
}

func (l *LocationZoneDaoImpl) DeleteZoneCodeByProductList(ctx context.Context, productIDList []int, zoneType uint8) *srerr.Error {
	for _, productID := range productIDList {
		if err := dbutil.Delete(ctx, LocationZoneTabHook, map[string]interface{}{
			"product_id = ?": productID,
			"zone_type = ?":  zoneType,
			"post_code = ?":  "",
		}, dbutil.ModelInfo{FulfillmentProductId: uint64(productID)}); err != nil {
			logger.CtxLogErrorf(ctx, "DeleteZoneCodeByProductList| product id:%v, zone type:%v", productID, zoneType)
			return srerr.With(srerr.DatabaseErr, "delete zone", err)
		}
	}

	return nil
}

func (l *LocationZoneDaoImpl) FilterLocationZones(ctx context.Context, q *ZoneQuery) ([]*LocationZoneTab, int64, *srerr.Error) {
	queryCondition := make(map[string]interface{})
	if q.FilterByProductID {
		queryCondition["product_id = ?"] = q.ProductID
	}
	if q.FilterByZoneCode {
		queryCondition["zone_code = ?"] = q.ZoneCode
	}
	if q.FilterByDistrictId {
		queryCondition["district_id = ?"] = q.DistrictId
	}
	if q.FilterByState {
		queryCondition["state = ?"] = q.State
	}
	if q.FilterByCity {
		queryCondition["city = ?"] = q.City
	}
	if q.FilterByDistrict {
		queryCondition["district = ?"] = q.District
	}
	if q.FilterByStreet {
		queryCondition["street = ?"] = q.Street
	}
	if q.FilterByZoneType {
		queryCondition["zone_type = ?"] = q.ZoneType
	}
	queryCondition["routing_type = ?"] = q.RoutingType
	// 这里读取的是location zone不是post code
	queryCondition["post_code = ?"] = ""

	var total int64
	if err := dbutil.Count(ctx, LocationZoneTabHook, queryCondition, &total); err != nil {
		logger.CtxLogErrorf(ctx, "GetLocationZones fail|err=%v", err)
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	db, err := dbutil.SlaveDB(ctx, LocationZoneTabHook)
	if err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	query, args := dbutil.ParseCondition(queryCondition)
	var locationZones, batchLocationZones []*LocationZoneTab
	if err := db.Table(LocationZoneTabHook.TableName()).Where(query, args...).Offset(int(q.Offset)).Limit(int(q.Limit)).
		FindInBatches(&batchLocationZones, 1000, func(tx scormv2.SQLCommon, batch int) error {
			locationZones = append(locationZones, batchLocationZones...)
			return nil
		}).GetError(); err != nil {
		return nil, 0, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return locationZones, total, nil
}

func (l *LocationZoneDaoImpl) GetLocationZoneById(ctx context.Context, id int64) (*LocationZoneTab, error) {
	var locationZone *LocationZoneTab
	if err := dbutil.Take(ctx, LocationZoneTabHook, map[string]interface{}{"id = ?": id}, &locationZone); err != nil {
		logger.CtxLogErrorf(ctx, "GetLocationZoneById| get location zone by id:%v, err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return nil, dbutil.NewErrDataNotFound("id", id)
		}
		return nil, dbutil.NewErrDatabase(err, "db update")
	}

	return locationZone, nil
}

func (l *LocationZoneDaoImpl) UpdateZoneCode(ctx context.Context, id int64, zoneCode string, districtId uint, state, city, district, street string) error {
	var locationZone *LocationZoneTab
	if err := dbutil.Take(ctx, LocationZoneTabHook, map[string]interface{}{"id = ?": id}, &locationZone); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateZoneCode| get location zone by id:%v, err:%v", err)
		if err == scormv2.ErrRecordNotFound {
			return dbutil.NewErrDataNotFound("id", id)
		}
		return dbutil.NewErrDatabase(err, "db update")
	}
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	locationZone.ZoneCode = zoneCode
	locationZone.DistrictId = districtId
	locationZone.State = state
	locationZone.City = city
	locationZone.District = district
	locationZone.Street = street
	locationZone.Mtime = uint(now)
	if err := dbutil.SaveByObj(ctx, LocationZoneTabHook, nil, locationZone, nil, dbutil.ModelInfo{
		Id:        uint64(id),
		ModelName: LocationZoneTabHook.TableName(),
	}); err != nil {
		logger.CtxLogErrorf(ctx, "UpdateZoneCode| update location zone:%v, err:%v", locationZone, err)
		return dbutil.NewErrDatabase(err, "update data")
	}
	return nil
}

func (l *LocationZoneDaoImpl) ListZoneCode(ctx context.Context, productID int, routingType, zoneType uint8) ([]*LocationZoneTab, *srerr.Error) {
	db, err := dbutil.SlaveDB(ctx, LocationZoneTabHook)
	if err != nil {
		return nil, srerr.New(srerr.DatabaseErr, nil, err.Error())
	}

	var zoneCodes []*LocationZoneTab
	dErr := db.Table(tableName).Where("product_id = ? AND routing_type = ? AND zone_type = ?", productID, routingType, zoneType).Select("distinct(zone_code)").Find(&zoneCodes).GetError()
	if dErr != nil {
		logger.CtxLogErrorf(ctx, "GetLocationZones fail|err=%v", dErr)
		return nil, srerr.New(srerr.DatabaseErr, nil, dErr.Error())
	}
	return zoneCodes, nil
}

func (l *LocationZoneDaoImpl) DeleteZoneCode(ctx context.Context, id int64) error {
	if err := dbutil.Delete(ctx, LocationZoneTabHook, map[string]interface{}{"id = ?": id}, dbutil.ModelInfo{
		Id:        uint64(id),
		ModelName: LocationZoneTabHook.TableName(),
	}); err != nil {
		return dbutil.NewErrDatabase(err, "delete zone")
	}
	return nil
}

func (l *LocationZoneDaoImpl) DeleteZoneCodeByProductListWithTX(ctx context.Context, productIDList []int, zoneType uint8, tx scormv2.SQLCommon) *srerr.Error {
	var db scormv2.SQLCommon
	var err error
	if tx != nil {
		db = tx
	} else {
		if db, err = dbutil.MasterDB(ctx, LocationZoneTabHook); err != nil {
			return srerr.New(srerr.DatabaseErr, nil, err.Error())
		}
	}

	d := db.Table(LocationZoneTabHook.TableName()).Where("product_id in ? AND zone_type = ?", productIDList, zoneType).Delete(LocationZoneTabHook)
	if d.GetError() != nil {
		return srerr.New(srerr.DatabaseErr, nil, err.Error())
	}

	return nil
}

func (l *LocationZoneDaoImpl) BatchCreateZoneCode(ctx context.Context, zoneData []*LocationZoneTab, tx scormv2.SQLCommon) *srerr.Error {
	var db scormv2.SQLCommon
	var err error
	if tx != nil {
		db = tx.Table(LocationZoneTabHook.TableName())
	} else {
		if db, err = dbutil.MasterDB(ctx, LocationZoneTabHook); err != nil {
			return srerr.New(srerr.DatabaseErr, nil, err.Error())
		}
	}

	if err := db.Model(&LocationZoneTab{}).CreateInBatches(zoneData, 1000).GetError(); err != nil {
		return srerr.With(srerr.DatabaseErr, "", err)
	}

	return nil
}

func (l *LocationZoneDaoImpl) ListPostCodeCode(ctx context.Context, productId int, zoneType uint8, routingType uint8) ([]*LocationZoneTab, *srerr.Error) {
	var postCodes []*LocationZoneTab

	if err := dbutil.Select(ctx, LocationZoneTabHook, map[string]interface{}{
		"routing_type = ?": routingType,
		"product_id = ?":   productId,
		"zone_type = ?":    zoneType,
		"post_code != ?":   "",
	}, &postCodes); err != nil {
		return nil, srerr.With(srerr.DatabaseErr, nil, err)
	}

	return postCodes, nil
}

func (l *LocationZoneDaoImpl) QueryPostCodeByPage(ctx context.Context, condition map[string]interface{}, page, pageSize int64) ([]LocationZoneTab, int64, *srerr.Error) {
	if pageSize > 100 || pageSize < 0 {
		pageSize = DefaultPagSize
	}
	var postCodes []LocationZoneTab
	var total int64
	// 1.按条件查询PostCode总数
	if err := dbutil.Count(ctx, LocationZoneTabHook, condition, &total); err != nil {
		logger.CtxLogErrorf(ctx, "query post code failed err=%v", err)
		return nil, 0, srerr.With(srerr.DatabaseErr, condition, err)
	}
	// 2.按条件分页从数据库中捞出postcode
	if err := dbutil.Select(ctx, LocationZoneTabHook, condition, &postCodes, dbutil.WithPage(page, pageSize)); err != nil {
		logger.CtxLogErrorf(ctx, "query post code list err=%v", err)
		return nil, 0, srerr.With(srerr.QueryListError, condition, err)
	}

	return postCodes, total, nil
}
