package zone

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestLocationZoneDaoImpl_DeleteZoneCodeByProductListWithTX(t *testing.T) {
	//scErr := handler.RegisterScormHandler(
	//	scormhelper.WithScormV2(),
	//)
	//if scErr != nil {
	//	panic(scErr)
	//}
	_ = chassis.Init(
		chassis.WithChassisConfigPrefix("grpc_server"))

	_ = configutil.Init()
	dberr := dbutil.Init()

	if dberr != nil {
		panic(dberr)
	}
	ctx := context.Background()
	db, e := dbutil.MasterDB(ctx, rulevolume.ForecastMaskRouteVolumeTabHook)
	//db, e := dbutil.MasterDB(ctx, LocationZoneTabHook)
	if e != nil {
		panic(e)
	}
	//db.Begin()
	l := &LocationZoneDaoImpl{}
	ctx = scormv2.BindContext(ctx, db)
	err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		tx := scormv2.Context(ctx)
		return l.DeleteZoneCodeByProductListWithTX(ctx, []int{18025}, 0, tx)
	})
	if err == nil {
		//db.Rollback()
	} else {
		//db.Commit()
		panic(err)
	}
}
