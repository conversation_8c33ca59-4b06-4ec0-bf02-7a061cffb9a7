package zone

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
)

const (
	tableName = "location_zone_tab"
)

var (
	LocationZoneTabHook = &LocationZoneTab{}
)

type LocationZoneTab struct {
	Id          uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	ProductID   int    `gorm:"product_id" json:"product_id"`
	ZoneCode    string `gorm:"column:zone_code;NOT NULL" json:"zone_code"`
	State       string `gorm:"column:state;NOT NULL" json:"state"`
	City        string `gorm:"column:city;NOT NULL" json:"city"`
	District    string `gorm:"column:district;NOT NULL" json:"district"`
	Street      string `gorm:"column:street;NOT NULL" json:"street"`
	DistrictId  uint   `gorm:"column:district_id;default:0;NOT NULL" json:"district_id"`
	OperateBy   string `gorm:"column:operate_by;NOT NULL" json:"operate_by"`
	ZoneType    uint8  `gorm:"column:zone_type;NOT NULL" json:"zone_type"`
	RoutingType uint8  `gorm:"column:routing_type;default:0;NOT NULL" json:"routing_type"`
	Ctime       uint   `gorm:"column:ctime;NOT NULL" json:"ctime"` // ctime
	Mtime       uint   `gorm:"column:mtime;NOT NULL" json:"mtime"` // mtime
	PostCode    string `gorm:"column:post_code;NOT NULL" json:"post_code"`
}

func (t *LocationZoneTab) TableName() string {
	return tableName
}

func (t *LocationZoneTab) DBForRead() dbutil.DBTag {
	return dbutil.SmartRoutingRead
}

func (t *LocationZoneTab) DBForWrite() dbutil.DBTag {
	return dbutil.SmartRoutingWrite
}

func (t *LocationZoneTab) ModelInfo() dbutil.ModelInfo {
	return dbutil.ModelInfo{
		Id:                   t.Id,
		ModelName:            t.TableName(),
		FulfillmentProductId: uint64(t.ProductID),
	}
}
