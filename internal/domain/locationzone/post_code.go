package locationzone

import (
	"bytes"
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/httputil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"strconv"
)

// QueryPostCodeList 查询postCode列表
func (repo *ZoneRepoImpl) QueryPostCodeList(ctx context.Context, req *schema.QueryPostCodeListReq) (*schema.QueryPostCodeListResp, *srerr.Error) {
	// 1.组装参数条件
	condition := genQueryCondition(req)

	// 2.按照条件查询
	postCodeList, totalCount, err := repo.zoneDao.QueryPostCodeByPage(ctx, condition, (req.Page-1)*req.PagSize, req.PagSize)
	if err != nil {
		return nil, err
	}
	return convertPostCodeListResp(totalCount, req.Page, postCodeList), nil
}

// postCode和LocationZone是一张表，二者的区别在于postCode是否为空，如果postCode为空则是postCode类型，否则是LocationZone类型
func genQueryCondition(req *schema.QueryPostCodeListReq) map[string]interface{} {
	res := map[string]interface{}{}
	// 如果前端传来的postCode为空则应该加一个post_code != ""的条件
	if req.PostCode == "" {
		res["post_code != ?"] = ""
	} else {
		res["post_code = ?"] = req.PostCode
	}

	if req.ZoneCode != "" {
		res["zone_code = ?"] = req.ZoneCode
	}
	res["routing_type = ?"] = req.RoutingType
	res["zone_type = ?"] = req.PostCodeType
	if req.RoutingType == rule.CBRoutingType && req.ProductId != 0 {
		res["product_id = ?"] = req.ProductId
	} else if req.RoutingType == rule.SPXRoutingType {
		res["product_id = ?"] = 0
	}
	return res
}

// 将对应的postcode数组转换成返回给前端的resp,
func convertPostCodeListResp(totalCount int64, currentPage int64, postCodes []zone.LocationZoneTab) *schema.QueryPostCodeListResp {
	resp := &schema.QueryPostCodeListResp{Total: totalCount, Page: currentPage}
	resp.List = make([]schema.QueryPostCodeListRespDetail, 0, len(postCodes))
	for _, postCodeItem := range postCodes {
		resp.List = append(resp.List, schema.QueryPostCodeListRespDetail{
			Id:             int(postCodeItem.Id),
			ProductId:      postCodeItem.ProductID,
			ZoneCode:       postCodeItem.ZoneCode,
			PostCode:       postCodeItem.PostCode,
			LastOperator:   postCodeItem.OperateBy,
			LastUpdateTime: int64(postCodeItem.Mtime),
		})
	}
	return resp
}

func (repo *ZoneRepoImpl) EditSinglePostCode(ctx context.Context, req *schema.EditSinglePostCode) *srerr.Error {
	condition := genEditCondition(ctx, req)
	// 1.校验编辑的postCode对应的zone code是否存在于Location zone中
	if err := repo.verifyZoneExistInLocationZone(ctx, req); err != nil {
		return srerr.New(srerr.EditPostCodeError, req, "zone code existed in location zone %v or err=%v", req.ZoneCode, err)
	}
	// 2.将save到数据库
	if err := dbutil.Update(ctx, zone.LocationZoneTabHook, map[string]interface{}{
		"id = ?": req.Id,
	}, condition, dbutil.ModelInfo{}); err != nil {
		logger.CtxLogErrorf(ctx, "update post code failed err=%v", err)
		return srerr.With(srerr.EditPostCodeError, req, err)
	}

	return nil
}

func (repo *ZoneRepoImpl) verifyZoneExistInLocationZone(ctx context.Context, req *schema.EditSinglePostCode) *srerr.Error {
	// 1.按照条件查询编辑后的zone code是否已经存在
	_, totalCount, qerr := repo.zoneDao.QueryPostCodeByPage(ctx, map[string]interface{}{
		"routing_type = ?": req.RoutingType,
		"zone_code = ?":    req.ZoneCode,
		"post_code = ?":    "",
		"zone_type = ?":    req.PostCodeType,
		"product_id = ?":   req.ProductId,
	}, 0, zone.DefaultPagSize)
	if qerr != nil {
		logger.CtxLogErrorf(ctx, "verify zoneCode exist in location zone failed err=%v", qerr)
		return srerr.With(srerr.EditPostCodeError, req.ZoneCode, qerr)
	}
	// 2.如果已经存在则报错
	if totalCount != 0 {
		logger.CtxLogErrorf(ctx, "verify zoneCode exist in location zone ")
		return srerr.With(srerr.EditPostCodeError, req.ZoneCode, nil)
	}

	return nil
}

func genEditCondition(ctx context.Context, req *schema.EditSinglePostCode) map[string]interface{} {
	res := map[string]interface{}{}
	if req.ZoneCode != "" {
		res["zone_code"] = req.ZoneCode
	}

	if req.PostCode != "" {
		res["post_code"] = req.PostCode
	}

	res["mtime"] = recorder.Now(ctx).Unix()
	res["operate_by"] = req.LastOperator
	return res
}

func (repo *ZoneRepoImpl) ImportPostCode(ctx context.Context, req *schema.ImportPostCodeReq) *srerr.Error {
	// 1.解析excel表格
	excelContent, header, err := parseExcel(ctx, req)
	if err != nil {
		return err
	}

	// 2. 校验excel内容
	if err := repo.excelVerify(ctx, excelContent, header, req); err != nil {
		return err
	}

	// 3.按照操作类型分类，1表示新增，2表示更新
	addPostCodeMap, deletePostCodeMap := classifyData(excelContent, req)

	// 4.保存excel数据到db
	return repo.saveExcelContent(ctx, deletePostCodeMap, addPostCodeMap, req)
}

func parseExcel(ctx context.Context, req *schema.ImportPostCodeReq) ([][]string, []string, *srerr.Error) {
	timeout := 6
	resp, err := httputil.Get(ctx, req.PostCodeUrl, nil, timeout, nil)
	if err != nil {
		logger.CtxLogErrorf(ctx, "import post code err=%v", err)
		return nil, nil, srerr.With(srerr.ImportPostCodeError, req, err)
	}

	data, header, fErr := fileutil.ParseExcel(ctx, bytes.NewReader(resp), true)
	if fErr != nil {
		logger.CtxLogErrorf(ctx, "parse excel err=%v", fErr)
		return nil, nil, srerr.With(srerr.ParsePostCodeExcelError, req, fErr)
	}

	columnNum := spxTotalColumns
	if req.RoutingType == rule.CBRoutingType {
		columnNum = cbTotalColumns
	}

	return removeSpaces(data, columnNum), header, nil
}

// 解析出来的excel可能存在某一行全是空的
func removeSpaces(excelContent [][]string, columnNum int) [][]string {
	var realContent [][]string
	for _, row := range excelContent {
		isAllSpace := true // 判断这一行是不是都是为空格
		for i := 0; i < columnNum; i++ {
			if row[i] != "" {
				isAllSpace = false
				break
			}
		}
		if !isAllSpace {
			realContent = append(realContent, row)
		}
	}

	return realContent
}

func (repo *ZoneRepoImpl) saveExcelContent(ctx context.Context, deletePostCodeMap, addPostCodeMap []ImportPostCode, req *schema.ImportPostCodeReq) *srerr.Error {
	// 1.获取对应的db链接
	db, dberr := dbutil.MasterDB(ctx, zone.LocationZoneTabHook)
	if dberr != nil {
		logger.CtxLogErrorf(ctx, "update post code failed err=%v", dberr)
		return srerr.With(srerr.DatabaseErr, req, dberr)
	}
	// 2.将对应的db绑定到ctx中,并开启事务，这里不用手动执行回滚PropagationRequired会根据err==nil来判断是否需要回滚
	ctx = scormv2.BindContext(ctx, db)
	if err := scormv2.PropagationRequired(ctx, func(ctx context.Context) (e error) {
		// 2.1从ctx中获取对应的db连接
		tx := scormv2.Context(ctx)
		// 2.2 删除post code
		if err := repo.deletePostCodeByZoneCode(ctx, deletePostCodeMap, req, tx); err != nil {
			return err
		}
		// 2.3 更新或者新增post code
		if err := repo.createOrUpdatePostCode(ctx, addPostCodeMap, req, tx); err != nil {
			return err
		}

		return nil
	}); err != nil {
		logger.CtxLogErrorf(ctx, "save post code content in db err=%v", err)
		return srerr.New(srerr.DatabaseErr, nil, "save post code content in db err=%v", err)
	}

	return nil
}

func (repo *ZoneRepoImpl) excelVerify(ctx context.Context, excelContent [][]string, header []string, req *schema.ImportPostCodeReq) *srerr.Error {
	// 1.基础校验，包括header和数据总量校验
	if err := baseVerify(ctx, excelContent, header, req); err != nil {
		return err
	}

	// 3.校验Excel内容是否合法
	if err := repo.postCodeExcelContentVerify(ctx, excelContent, req); err != nil {
		return err
	}

	return nil
}

// 基础校验，包括Excel数据量和header的合法性
func baseVerify(ctx context.Context, excelContent [][]string, header []string, req *schema.ImportPostCodeReq) *srerr.Error {
	// 0.首先校验Content是否为空
	if len(excelContent) == 0 {
		logger.CtxLogErrorf(ctx, "excel Content is empty %d", len(excelContent))
		return srerr.New(srerr.PostCodeExcelContentError, nil, "excel Content is empty %d", len(excelContent))
	}
	excelMaxRows := configutil.GetPostCodeMaxUploadConf().MaxRow
	// 1.校验数据量是否超过最大行数
	if len(excelContent) > excelMaxRows {
		logger.CtxLogErrorf(ctx, "excel content exceeded maximum number of rows,limit is %d,but now is %d", excelMaxRows, len(excelContent))
		return srerr.New(srerr.PostCodeExcelContentError, nil, "excel content exceeded maximum number of rows,limit is %d,but now is %d", excelMaxRows, len(excelContent))
	}

	// 2.校验如果是cb的导入模板多一列productId，其他市场只有4列
	realHeaderCount := spxTotalColumns
	if req.RoutingType == rule.CBRoutingType {
		realHeaderCount = cbTotalColumns
	}
	// 因为excel解析出来会有空的列，所以这里会校验规定列的值是否为空
	for i := 0; i < realHeaderCount; i++ {
		if header[i] == "" { // 前几列是否有值
			logger.CtxLogErrorf(ctx, "excel content exceeded maximum number of rows,limit is %d,but now is %d", excelMaxRows, len(excelContent))
			return srerr.New(srerr.PostCodeExcelContentError, nil, "header content is empty column=%v", i+1)
		}
	}
	return nil
}

// 校验上传的postcode内容是否合法
func (repo *ZoneRepoImpl) postCodeExcelContentVerify(ctx context.Context, excelContent [][]string, req *schema.ImportPostCodeReq) *srerr.Error {
	var zoneCodes []string
	duplicateMap := map[string]string{}      // 用于检查是否有重复的数据
	productZoneCodeMap := map[int][]string{} // 记录product对应的zone codes

	// 获取lps对应市场所有的product
	productList, pErr := repo.lpsApi.GetProductBaseInfoList(ctx)
	if pErr != nil {
		return srerr.With(srerr.GetProductBaseFail, "", pErr)
	}
	productIDMap := make(map[int]struct{})
	for _, product := range productList {
		productIDMap[product.ProductId] = struct{}{}
	}

	for rowIdx, row := range excelContent {
		// 1.按照routingType获取真实的下标
		totalColumns, actionIdx, zoneCodeIdx, postCodeIdx := getRealIdx(req.RoutingType)
		if err := verifyRow(ctx, row, totalColumns, rowIdx); err != nil {
			return err
		}
		/* 2.校验操作类型，类型不能为空，且必须是1和2，1表示新增或者更新，2表示删除
		2.1 如果类型是1，如果postcode在数据库中存在，则更新对应的zone code，如果不存在则直接插入。
		2.2 如果类型是2，如果postcode和对应的zone code在数据库中存在则删除数据，不存在则不做操作。
		*/
		if err := verifyAction(ctx, row, actionIdx); err != nil {
			return err
		}
		// 3.校验上传excel内容，zone code和postcode是不能为空的
		if err := verifyContent(ctx, row, actionIdx, zoneCodeIdx, postCodeIdx, req.RoutingType, productZoneCodeMap, productIDMap); err != nil {
			return err
		}
		// 4.1 校验一下是否有重复的数据
		if err := verifyDuplicateData(ctx, duplicateMap, row, postCodeIdx, zoneCodeIdx, actionIdx, req.RoutingType); err != nil {
			return err
		}
		// 4.按照操作类型分类
		if row[actionIdx] == addOperation {
			// 4.2 只有action=1的类型才需要校验，需要校验的内容是对应的zone code在location zone类型中是否存在
			zoneCodes = append(zoneCodes, row[zoneCodeIdx])
		}
	}
	// 校验zone code是否在location zone中存在
	if err := repo.verifyZoneCodeExisted(ctx, zoneCodes, req, productZoneCodeMap); err != nil {
		return err
	}

	return nil
}

// 这里校验的是一个post code只能对应一个zone code
func verifyDuplicateData(ctx context.Context, duplicateMap map[string]string, row []string, postCodeIdx, zoneCodeIdx, actionIdx, routingType int) *srerr.Error {
	key := fmt.Sprintf("%v", row[postCodeIdx])
	if routingType == rule.CBRoutingType { // 1.cb需要挂载Product校验
		key = fmt.Sprintf("%v:%v", row[cbProductIdIdx], row[postCodeIdx])
	}
	// 2.校验内容(1)post code是否对应唯一的zone code，(2)同样的数据存在两种action mode,比如 zA -> P1 -> action1，zA -> P1 -> action2就是错误的
	if v, ok := duplicateMap[key]; ok && v != jointZoneCodeAction(row[zoneCodeIdx], row[actionIdx]) {
		logger.CtxLogErrorf(ctx, "Duplicate data %s", key)
		return srerr.New(srerr.PostCodeExcelContentError, row, "Duplicate data %s", key)
	} else {
		// 这里存储的是zone_code+action，可能会出现 zoneA->postCode1 1,zoneA->postCode1 2
		duplicateMap[key] = jointZoneCodeAction(row[zoneCodeIdx], row[actionIdx])
	}
	return nil
}

func jointZoneCodeAction(zoneCode string, postCode string) string {
	return fmt.Sprintf("%s_%s", zoneCode, postCode)
}

// 校验zone code在location zone类型中是否存在
func (repo *ZoneRepoImpl) verifyZoneCodeExisted(ctx context.Context, zoneCodes []string, req *schema.ImportPostCodeReq, productPostCodeMap map[int][]string) *srerr.Error {
	// 1.如果是spx则不区分product
	if req.RoutingType == rule.SPXRoutingType {
		productPostCodeMap = map[int][]string{
			0: zoneCodes,
		}
	}
	// 2.这里是校验新增的zone code是否存在于location zone对应的zone code中
	for productId, zoneCodes := range productPostCodeMap {
		locationZoneList, totalCount, qerr := repo.zoneDao.QueryPostCodeByPage(ctx, map[string]interface{}{
			"routing_type = ?": req.RoutingType,
			"zone_code in (?)": zoneCodes,
			"post_code = ?":    "",
			"zone_type = ?":    req.PostCodeType,
			"product_id = ?":   productId,
		}, 0, zone.DefaultPagSize)
		if qerr != nil {
			return srerr.New(srerr.DatabaseErr, nil, "Query zone code from location zone failed err=%v", qerr)
		}
		// 2.如果上传的zone code在Location zone中则报错返回
		if totalCount != 0 {
			// 2.1 筛选出那些zone code在Location zone中
			var existZoneCode []string
			for _, location := range locationZoneList {
				existZoneCode = append(existZoneCode, location.ZoneCode)
			}
			return srerr.New(srerr.PostCodeExcelContentError, nil, "zone code existed in location zone existed=%+v", objutil.RemoveDuplicatedStrings(existZoneCode))
		}
	}

	return nil
}

func classifyData(data [][]string, req *schema.ImportPostCodeReq) ([]ImportPostCode, []ImportPostCode) {
	if req.RoutingType == rule.CBRoutingType {
		return CBRoutingClassifyData(data)
	} else {
		return nonCBRoutingClassifyData(data)
	}
}

func CBRoutingClassifyData(data [][]string) ([]ImportPostCode, []ImportPostCode) {
	var addPostCodeMap []ImportPostCode
	var deletePostCodeMap []ImportPostCode
	// 1.cb routing只有productId，zone code，postcode，action三列,下面是按照操作类型分类，1表示add/update，2表示delete
	for i := 0; i < len(data); i++ {
		// 1.1 这里忽略错误是因为在前面的时候已经校验过productId了
		productId, _ := strconv.Atoi(data[i][cbProductIdIdx])
		if data[i][cbActionModeIdx] == addOperation {
			addPostCodeMap = append(addPostCodeMap, ImportPostCode{
				ProductId: productId,
				ZoneCode:  data[i][cbZoneCodeIdx],
				PostCode:  data[i][cbPostCodeIdx],
			})
		} else if data[i][cbActionModeIdx] == deleteOperation {
			deletePostCodeMap = append(deletePostCodeMap, ImportPostCode{
				ProductId: productId,
				ZoneCode:  data[i][cbZoneCodeIdx],
				PostCode:  data[i][cbPostCodeIdx],
			})
		}
	}
	// 2.校验一下如果一个post code先删除后新增，则直接变为修改
	return addPostCodeMap, realDeletePostCodes(addPostCodeMap, deletePostCodeMap)
}

func realDeletePostCodes(addPostCodes, deletePostCodes []ImportPostCode) []ImportPostCode {
	var res []ImportPostCode
	for _, deleteData := range deletePostCodes {
		isExist := false
		for _, postCodeDetail := range addPostCodes {
			if deleteData.ProductId == postCodeDetail.ProductId && deleteData.PostCode == postCodeDetail.PostCode {
				isExist = true
				break
			}
		}
		if !isExist {
			res = append(res, deleteData)
		}
	}

	return res
}

func nonCBRoutingClassifyData(data [][]string) ([]ImportPostCode, []ImportPostCode) {
	addPostCodeMap := []ImportPostCode{}
	deletePostCodeMap := []ImportPostCode{}
	// 1.spx只有zone code，postcode，action三列,下面是按照操作类型分类，1表示add/update，2表示delete
	for i := 0; i < len(data); i++ {
		// 根据倒数第二列判断更新类型，1表示新增，2表示更新已有的postCode
		if data[i][spxActionModeIdx] == addOperation {
			addPostCodeMap = append(addPostCodeMap, ImportPostCode{
				ZoneCode: data[i][spxZoneCodeIdx],
				PostCode: data[i][spxPostCodeIdx],
			})
		} else if data[i][spxActionModeIdx] == deleteOperation {
			deletePostCodeMap = append(deletePostCodeMap, ImportPostCode{
				ZoneCode: data[i][spxZoneCodeIdx],
				PostCode: data[i][spxPostCodeIdx],
			})
		}
	}
	// 2.校验一下如果一个post code先删除后新增，则直接变为修改
	return addPostCodeMap, realDeletePostCodes(addPostCodeMap, deletePostCodeMap)
}

func (repo *ZoneRepoImpl) createOrUpdatePostCode(ctx context.Context, postCodeMap []ImportPostCode, req *schema.ImportPostCodeReq, tx scormv2.SQLCommon) *srerr.Error {
	// 1.获取需要更新的postCodes
	postCodeList := getPostCodeList(postCodeMap)
	// 2.查询出数据库中需要更新的post code
	existedPostCode, serr := selectExistPostCodeFromDB(ctx, postCodeMap, postCodeList, req)
	if serr != nil {
		return serr
	}

	// 4.清除已经有的existed
	needCreateData := removeExistData(existedPostCode, postCodeMap)
	// 5.创建需要导入的postcode
	createPostCode := batchCreatePostCode(ctx, needCreateData, req)

	// 6.更新已经存在的post code
	for _, existPostCode := range existedPostCode {
		if err := tx.Table(zone.LocationZoneTabHook.TableName()).Updates(&existPostCode).GetError(); err != nil {
			return srerr.With(srerr.DatabaseErr, req, err)
		}
	}
	// 7.保存新建的postcode
	if len(createPostCode) != 0 {
		if err := tx.Table(zone.LocationZoneTabHook.TableName()).CreateInBatches(&createPostCode, createBatchSize).GetError(); err != nil {
			return srerr.With(srerr.DatabaseErr, req, err)
		}
	}
	return nil
}

func removeExistData(existedData []zone.LocationZoneTab, postCodes []ImportPostCode) []ImportPostCode {
	var needCreateData []ImportPostCode
	for _, excelContent := range postCodes {
		isExist := false
		for _, exist := range existedData {
			if excelContent.ProductId == exist.ProductID && excelContent.ZoneCode == exist.ZoneCode && excelContent.PostCode == exist.PostCode {
				isExist = true
				break
			}
		}
		if !isExist {
			needCreateData = append(needCreateData, excelContent)
		}
	}
	return needCreateData
}

// 查询数据库中存在的post code
func selectExistPostCodeFromDB(ctx context.Context, postCodes []ImportPostCode, postCodeList []string, req *schema.ImportPostCodeReq) ([]zone.LocationZoneTab, *srerr.Error) {
	var existedPostCode []zone.LocationZoneTab
	nowTime := recorder.Now(ctx)
	// 1.从数据库中捞出需要修改postcode
	if err := dbutil.Select(ctx, zone.LocationZoneTabHook, map[string]interface{}{
		"post_code in (?)": postCodeList,
		"routing_type = ?": req.RoutingType,
		"zone_type = ?":    req.PostCodeType,
	}, &existedPostCode); err != nil {
		logger.CtxLogErrorf(ctx, "query post code in db for update err=%v", err)
		return nil, srerr.With(srerr.DatabaseErr, req, err)
	}
	// 2.更新数据库中存在的postcode对应的zone code
	for i := range existedPostCode {
		for _, postCodeDetail := range postCodes {
			if existedPostCode[i].PostCode == postCodeDetail.PostCode && existedPostCode[i].ProductID == postCodeDetail.ProductId {
				existedPostCode[i].ZoneCode = postCodeDetail.ZoneCode
				existedPostCode[i].Mtime = uint(nowTime.Unix())
				existedPostCode[i].OperateBy = req.LastOperator
			}
		}
	}

	return existedPostCode, nil
}

// 批量创建post code
func batchCreatePostCode(ctx context.Context, needCreateData []ImportPostCode, req *schema.ImportPostCodeReq) []zone.LocationZoneTab {
	createPostCode := make([]zone.LocationZoneTab, 0, len(needCreateData))
	nowTime := recorder.Now(ctx)
	for _, postCodeDetail := range needCreateData {
		createPostCode = append(createPostCode, zone.LocationZoneTab{
			ProductID:   postCodeDetail.ProductId,
			ZoneCode:    postCodeDetail.ZoneCode,
			PostCode:    postCodeDetail.PostCode,
			Ctime:       uint(nowTime.Unix()),
			Mtime:       uint(nowTime.Unix()),
			OperateBy:   req.LastOperator,
			ZoneType:    uint8(req.PostCodeType),
			RoutingType: uint8(req.RoutingType),
		})

	}
	return createPostCode
}

func (repo *ZoneRepoImpl) deletePostCodeByZoneCode(ctx context.Context, deleteData []ImportPostCode, req *schema.ImportPostCodeReq, tx scormv2.SQLCommon) *srerr.Error {
	// 1.获取需要删除的postCode
	updatePostCode := getPostCodeList(deleteData)
	var postCodes []*zone.LocationZoneTab
	// 2.从db中捞出需要删除的postCodes，todo 分批查询
	if err := dbutil.Select(ctx, zone.LocationZoneTabHook, map[string]interface{}{
		"post_code in (?)": updatePostCode,
		"routing_type = ?": req.RoutingType,
		"zone_type = ?":    req.PostCodeType,
	}, &postCodes); err != nil {
		logger.CtxLogErrorf(ctx, "query post code for update failed %v", err)
		return srerr.With(srerr.QueryPostForUpdateError, nil, err)
	}

	// 3.删除Excel中postCode和zoneCode都相等的db数据
	var needDeletePostCodeList []*zone.LocationZoneTab
	for _, code := range postCodes {
		for _, excelData := range deleteData {
			if code.ProductID == excelData.ProductId && code.PostCode == excelData.PostCode && code.ZoneCode == excelData.ZoneCode {
				needDeletePostCodeList = append(needDeletePostCodeList, code)
			}
		}
	}
	// 4.删除数据 todo 删除数据这里待优化
	for _, code := range needDeletePostCodeList {
		if err := tx.Table(zone.LocationZoneTabHook.TableName()).Delete(code, "id = ?", code.Id).GetError(); err != nil {
			return srerr.With(srerr.DatabaseErr, nil, err)
		}
	}
	return nil
}

// 去重获取post code
func getPostCodeList(data []ImportPostCode) []string {
	postCodeList := make([]string, 0, len(data))
	postCodeMap := map[string]struct{}{}
	for _, postCode := range data {
		if _, ok := postCodeMap[postCode.PostCode]; !ok {
			postCodeList = append(postCodeList, postCode.PostCode)
		} else {
			postCodeMap[postCode.PostCode] = struct{}{}
		}
	}
	return postCodeList
}

func (repo *ZoneRepoImpl) ExportPostCode(ctx context.Context, req *schema.ExportPostCodeReq) (string, *srerr.Error) {
	condition := generateExportCondition(req)
	var postCodes []zone.LocationZoneTab
	if err := dbutil.Select(ctx, zone.LocationZoneTabHook, condition, &postCodes); err != nil {
		logger.CtxLogErrorf(ctx, "query post code for export err=%v", err)
		return "", srerr.With(srerr.DatabaseErr, req, err)
	}

	return generatePostCodeExcel(ctx, postCodes, req)
}

func generatePostCodeExcel(ctx context.Context, postCodes []zone.LocationZoneTab, req *schema.ExportPostCodeReq) (string, *srerr.Error) {
	// 1.生成导出的header,如果是cb的多一列productId
	header := []string{"zone code", "post code"}
	if req.RoutingType == rule.CBRoutingType {
		header = []string{"product", "zone code", "post code"}
	}
	// 2.按照不同类型生成导出数据
	var exportData [][]string
	for _, code := range postCodes {
		if req.RoutingType == rule.CBRoutingType {
			exportData = append(exportData, []string{strconv.Itoa(code.ProductID), code.ZoneCode, code.PostCode})
		} else {
			exportData = append(exportData, []string{code.ZoneCode, code.PostCode})
		}
	}

	// 3.生成s3的链接返回给前端
	return generateS3Link(ctx, header, exportData)
}

func generateS3Link(ctx context.Context, header []string, exportData [][]string) (string, *srerr.Error) {
	excel, err := fileutil.MakeExcelWithSheetName(ctx, header, exportData, "post_code_export")
	if err != nil {
		logger.CtxLogErrorf(ctx, "generate excel err=%v", err)
		return "", srerr.With(srerr.ExcelFileOpenError, nil, err)
	}
	buffer, bufferErr := excel.WriteToBuffer()
	if bufferErr != nil {
		logger.CtxLogErrorf(ctx, "write to buffer err=%v", bufferErr)
		return "", srerr.With(srerr.ExcelFileOpenError, nil, bufferErr)
	}

	bucket := fileutil.GetBucketName(timeutil.GetCurrentUnixTimeStamp(ctx))
	exportKey := fmt.Sprintf("Export PostCode-%s%s", timeutil.FormatLocalDatestamp(timeutil.GetCurrentUnixTimeStamp(ctx)), ".xlsx")
	if err := fileutil.Upload(ctx, bucket, exportKey, buffer); err != nil {
		return "", srerr.With(srerr.S3UploadFail, nil, err)
	}
	return fileutil.GetS3Url(ctx, bucket, exportKey), nil
}

func generateExportCondition(req *schema.ExportPostCodeReq) map[string]interface{} {
	condition := map[string]interface{}{}
	if req.ZoneCode != "" {
		condition["zone_code = ?"] = req.ZoneCode
	}
	if req.PostCode == "" {
		condition["post_code != ?"] = ""
	} else {
		condition["post_code = ?"] = req.PostCode
	}

	condition["routing_type = ?"] = req.RoutingType
	condition["zone_type = ?"] = req.PostCodeType
	if req.RoutingType == rule.CBRoutingType && req.ProductId != 0 {
		condition["product_id = ?"] = req.ProductId
	} else if req.RoutingType == rule.SPXRoutingType || req.RoutingType == rule.LocalRoutingType {
		condition["product_id = ?"] = 0
	}

	return condition
}

func matchPostCodeToZoneCode(ctx context.Context, productId int, routingType int, zoneType int, postCode string, zoneCodeList []string) string {
	// spx的postcode匹配不区分product
	if routingType == rule.SPXRoutingType {
		productId = 0
	}
	for _, zoneCode := range zoneCodeList {
		key := formatPostCodeCacheKey(productId, zoneCode, uint8(routingType), uint8(zoneType), postCode)
		data, err := localcache.Get(ctx, constant.LocationZone, key)
		if err != nil {
			return ""
		}
		if v, ok := data.(bool); ok && v {
			return zoneCode
		}
	}
	return ""
}
