package locationzone

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"strconv"
)

func verifyRow(ctx context.Context, row []string, totalColumns int, rowIdx int) *srerr.Error {
	for i := 0; i < totalColumns; i++ {
		if row[i] == "" {
			logger.CtxLogErrorf(ctx, "Content is empty row=%v column=%v", rowIdx, i)
			return srerr.New(srerr.PostCodeExcelContentError, row, "Content is empty row=%v column=%v", rowIdx+2, i+1)
		}
	}
	return nil
}

func verifyAction(ctx context.Context, row []string, actionIdx int) *srerr.Error {
	// 1.2校验是校验action是否为空，如果为空则直接报错返回
	actionMode := row[actionIdx]
	if actionMode == "" {
		logger.CtxLogErrorf(ctx, "action mode is empty=%v", row)
		return srerr.New(srerr.ActionError, row, "action mode is empty=%v", row)
	}
	// 1.3校验是校验action是否为指定类型，1为add，2为delete
	if actionMode != addOperation && actionMode != deleteOperation {
		logger.CtxLogErrorf(ctx, "action mode is not 1 or 2 but your action is %v,row=%v", actionMode, row)
		return srerr.New(srerr.ActionError, row, "action mode is not 1 or 2 but your action is %v,row=%v", actionMode, row)
	} else {
		return nil
	}
}

// 校验zone code，post code，cb需要额外校验Product
func verifyContent(ctx context.Context, row []string, actionIdx, zoneCodeIdx, postCodeIdx int, routingType int, productZoneCodeMap map[int][]string, productIdMap map[int]struct{}) *srerr.Error {
	// 1.cb的routing需要校验productId
	if routingType == rule.CBRoutingType {
		productId, err := strconv.Atoi(row[cbProductIdIdx])
		if productId == 0 || err != nil {
			logger.CtxLogErrorf(ctx, "cb productId=%v,err=%v", productId, err)
			return srerr.New(srerr.PostCodeExcelContentError, nil, "cb productId=%v,err=%v", productId, err)
		}
		// 1.1校验product是否存在
		if _, ok := productIdMap[productId]; !ok {
			logger.CtxLogErrorf(ctx, "productId=%v not existed, err=%v", productId, err)
			return srerr.New(srerr.PostCodeExcelContentError, row, "productId=%v not existed, err=%v", productId, err)
		}

		// 1.2 保存Product对应的zone code,后续需要校验每个Product新增的zone code是否已经存在，存在则报错
		if row[actionIdx] == addOperation {
			// 保存一下post code对应的productId
			if _, ok := productZoneCodeMap[productId]; !ok {
				productZoneCodeMap[productId] = []string{}
			}
			productZoneCodeMap[productId] = append(productZoneCodeMap[productId], row[zoneCodeIdx])
		}
	}
	// 2. 校验zone code或者postcode是否为空
	if row[postCodeIdx] == "" || row[zoneCodeIdx] == "" {
		logger.CtxLogErrorf(ctx, "zone code or post code is empty ")
		return srerr.New(srerr.PostCodeExcelContentError, row, "zone code or post code is empty ")
	} else {
		return nil
	}
}

// 最后一列不读取数据，是提示列
func getRealIdx(routingType int) (int, int, int, int) {
	if routingType == rule.CBRoutingType {
		return cbTotalColumns - 1, cbActionModeIdx, cbZoneCodeIdx, cbPostCodeIdx
	} else {
		return spxTotalColumns - 1, spxActionModeIdx, spxZoneCodeIdx, spxPostCodeIdx
	}
}
