package locationzone

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"testing"
)

func Test_matchPostCodeToZoneCode(t *testing.T) {
	ctx := context.Background()
	type args struct {
		productId    int
		routingType  int
		zoneType     int
		postCode     string
		zoneCodeList []string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: localcache.Get failed",
			args: args{
				routingType:  rule.SPXRoutingType,
				zoneCodeList: []string{"1"},
			},
			want:  "",
			setup: func() {},
		},
		{
			name: "case 2: localcache.Get success",
			args: args{
				zoneCodeList: []string{"1"},
			},
			want: "1",
			setup: func() {
				localcache.InitTest(constant.LocationZone, fmt.Sprintf("routing-location-zone:%v-%v-%v-%v-%v", 0, "1", 0, 0, ""), true, 1)
			},
		},
		{
			name: "case 3: len(zoneCodeList) == 0",
			args: args{},
			want: "",
			setup: func() {
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := matchPostCodeToZoneCode(ctx, tt.args.productId, tt.args.routingType, tt.args.zoneType, tt.args.postCode, tt.args.zoneCodeList); got != tt.want {
				t.Errorf("matchPostCodeToZoneCode() = %v, want %v", got, tt.want)
			}
		})
	}
}
