package locationzone

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"testing"
)

func TestZoneRepoImpl_checkIsExistZoneCodeInPostCode(t *testing.T) {
	chassis.Init(chassis.WithChassisConfigPrefix("admin_server"))
	configutil.Init()
	dbutil.Init()
	repo := &ZoneRepoImpl{}
	ctx := context.TODO()

	exist, err := repo.checkIsExistZoneCodeInPostCode(ctx, map[string]interface{}{
		"post_code != ?":   "",
		"product_id = ?":   28051,
		"routing_type = ?": 0,
		"zone_code in (?)": []string{"B", "A", "A", "A", "A", "A", "A", "C", "A", "A", "C", "A", "A", "A", "A", "A", "A", "A", "C", "A"},
		"zone_type = ?":    1,
	})
	if err != nil {
		panic(err)
	}
	println(exist)
}

func TestZoneRepoImpl_CheckZoneContainDistrict(t *testing.T) {
	ctx := context.TODO()
	repo := &ZoneRepoImpl{}
	type args struct {
		productId      int
		locationIdList []uint64
		ZoneCodeList   []string
		routingType    uint8
		zoneType       uint8
		postCode       string
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		setup func()
	}{
		// TODO: Add test cases.
		{
			name: "case 1: matchPostCodeToZoneCode success",
			args: args{
				ZoneCodeList: []string{"1"},
				routingType:  rule.SPXRoutingType,
			},
			want: true,
			setup: func() {
				localcache.InitTest(constant.LocationZone, fmt.Sprintf("routing-location-zone:%v-%v-%v-%v-%v", 0, "1", 1, 0, ""), true, 1)
			},
		},
		{
			name: "case 2: isDistrictIdInZoneList success",
			args: args{
				ZoneCodeList:   []string{"1"},
				locationIdList: []uint64{1},
			},
			want: true,
			setup: func() {
				localcache.InitTest(constant.LocationZone, fmt.Sprintf("routing-location-zone:%v-%v-%v-%v-%v", 0, 1, "1", 0, 0), true, 2)
			},
		},
		{
			name: "case 3: matchPostCodeToZoneCode fail && isDistrictIdInZoneList fail",
			args: args{},
			want: false,
			setup: func() {
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup()
			if got := repo.CheckZoneContainDistrict(ctx, tt.args.productId, tt.args.locationIdList, tt.args.ZoneCodeList, tt.args.routingType, tt.args.zoneType, tt.args.postCode); got != tt.want {
				t.Errorf("CheckZoneContainDistrict() = %v, want %v", got, tt.want)
			}
		})
	}
}
