package locationzone

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"mime/multipart"
	"sort"
	"strconv"
	"strings"
)

type ZoneRepo interface {
	CheckZoneContainDistrict(ctx context.Context, productId int, locationIdList []uint64, ZoneCodeList []string, routingType, zoneType uint8, postCode string) bool
	MatchDistrictToZone(ctx context.Context, productId int, locationIdList []uint, ZoneCodeList []string, routingType, zoneType uint8, postCode string) string
	ImportZone(ctx context.Context, file multipart.File, routingType uint8) *srerr.Error
	ImportProductZone(ctx context.Context, file multipart.File, routingType, zoneType uint8) *srerr.Error
	ExportZone(ctx context.Context, query *zone.ZoneQuery) (*bytes.Buffer, *srerr.Error)
	ExportProductZone(ctx context.Context, query *zone.ZoneQuery) (*bytes.Buffer, *srerr.Error)
	UpdateZone(ctx context.Context, id int64, ZoneCode string, districtID uint) *srerr.Error
	DeleteZone(ctx context.Context, id int64) *srerr.Error
	GetReceiverZoneList(ctx context.Context, query *zone.ZoneQuery) ([]*zone.LocationZone, int32, *srerr.Error)
	GetZoneCodeList(ctx context.Context, productID int, routingType, zoneType uint8) ([]string, *srerr.Error)
	GetZoneCodeListFromCache(ctx context.Context, productID int, routingType, zoneType uint8) ([]string, *srerr.Error)
	DeployZoneToRunning(ctx context.Context, productID int, tx scormv2.SQLCommon) *srerr.Error
	QueryPostCodeList(ctx context.Context, req *schema.QueryPostCodeListReq) (*schema.QueryPostCodeListResp, *srerr.Error)
	EditSinglePostCode(ctx context.Context, req *schema.EditSinglePostCode) *srerr.Error
	ImportPostCode(ctx context.Context, req *schema.ImportPostCodeReq) *srerr.Error
	ExportPostCode(ctx context.Context, req *schema.ExportPostCodeReq) (string, *srerr.Error)
}

const (
	spxTotalColumns  = 4
	spxActionModeIdx = 2
	spxPostCodeIdx   = 1
	spxZoneCodeIdx   = 0
	addOperation     = "1"
	deleteOperation  = "2"
	cbTotalColumns   = 5
	cbActionModeIdx  = 3
	cbPostCodeIdx    = 2
	cbZoneCodeIdx    = 1
	cbProductIdIdx   = 0
	createBatchSize  = 1000
)

type ZoneRepoImpl struct {
	zoneDao    zone.LocationZoneDaoServer
	lpsApi     lpsclient.LpsApi
	addrRepo   address.AddrRepo
	layerCache *layercache.LevelCache
}

func NewZoneRepoImpl(
	zoneDao zone.LocationZoneDaoServer,
	lpsApi lpsclient.LpsApi,
	addrRepo address.AddrRepo,
	layerCache *layercache.LevelCache) *ZoneRepoImpl {
	return &ZoneRepoImpl{
		zoneDao:    zoneDao,
		lpsApi:     lpsApi,
		addrRepo:   addrRepo,
		layerCache: layerCache,
	}
}

func (repo *ZoneRepoImpl) CheckZoneContainDistrict(ctx context.Context, productId int, locationIdList []uint64, ZoneCodeList []string, routingType, zoneType uint8, postCode string) bool {
	logger.CtxLogInfof(ctx, "CheckZoneContainDistrict ProductID=%v|LocationIdList=%v|ZoneCodeList=%v|RoutingType=%v|ZoneType=%v", productId, locationIdList, ZoneCodeList, routingType, zoneType)
	// todo 加注释
	if zoneCode := matchPostCodeToZoneCode(ctx, productId, int(routingType), int(zoneType), postCode, ZoneCodeList); zoneCode != "" {
		monitoring.ReportSuccess(ctx, monitoring.PostCodeMonitor, monitoring.MatchPostCode, fmt.Sprintf("match post code=%s", zoneCode))
		return true
	}

	for _, districtId := range locationIdList {
		zoneCode := repo.isDistrictIdInZoneList(ctx, productId, districtId, ZoneCodeList, routingType, zoneType)
		if zoneCode != "" {
			return true
		}
	}

	return false
}

func (repo *ZoneRepoImpl) MatchDistrictToZone(ctx context.Context, productId int, locationIdList []uint, ZoneCodeList []string, routingType, zoneType uint8, postCode string) string {
	logger.CtxLogInfof(ctx, "MatchDistrictToZone ProductID=%v|LocationIdList=%v|ZoneCodeList=%v|RoutingType=%v|ZoneType=%v", productId, locationIdList, ZoneCodeList, routingType, zoneType)
	if matchZoneCode := matchPostCodeToZoneCode(ctx, productId, int(routingType), int(zoneType), postCode, ZoneCodeList); matchZoneCode != "" {
		monitoring.ReportSuccess(ctx, monitoring.PostCodeMonitor, monitoring.MatchPostCode, fmt.Sprintf("match post code=%s", matchZoneCode))
		return matchZoneCode
	}
	for _, districtId := range locationIdList {
		zoneCode := repo.isDistrictIdInZoneList(ctx, productId, uint64(districtId), ZoneCodeList, routingType, zoneType)
		if zoneCode != "" {
			return zoneCode
		}
	}
	return ""
}

func (repo *ZoneRepoImpl) ImportZone(ctx context.Context, file multipart.File, routingType uint8) *srerr.Error {
	rows, err := fileutil.GetRows(file)
	if err != nil {
		return err
	}
	if len(rows) > 5000 {
		return srerr.New(srerr.ExcelFileOpenError, nil, "can not import rows > 5000")
	}

	mustField := []string{
		"Zone Code",
		"State",
	}
	header := rows[0]
	if ok := fileutil.CheckMustFieldExisted(mustField, header); !ok {
		return srerr.New(srerr.ExcelValidateError, nil, "")
	}

	operator, _ := apiutil.GetUserInfo(ctx)
	logger.CtxLogInfof(ctx, "excel log - rows:%v", len(rows))
	var zoneList []*zone.LocationZone
	var zoneCodes []string
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, row := range rows[1:] {
		row = extendLoc(row)
		if fileutil.CheckEmpty(row[0]) || fileutil.CheckLenGrater(row[0], 32) || row[1] == "" {
			logger.CtxLogErrorf(ctx, "invalid zone code, row:%v", row)
			continue
		}
		loc, locErr := repo.addrRepo.GetLocationByLocName(ctx, envvar.GetCID(), row[1], row[2], row[3], row[4])
		if locErr != nil {
			return srerr.New(srerr.InsertZoneCodeErr, row, "parse location id failed")
		}
		tmpZone := &zone.LocationZone{
			ZoneCode:    row[0],
			State:       loc.GetState(),
			City:        loc.GetCity(),
			District:    loc.GetDistrict(),
			Street:      loc.GetStreet(),
			RoutingType: routingType,
			OperateBy:   operator,
			Ctime:       uint(now),
			Mtime:       uint(now),
		}
		for _, locationId := range []int{int(loc.GetStreetLocId()), int(loc.GetDistrictLocId()), int(loc.GetCityLocId()), int(loc.GetStateLocId())} {
			if locationId > 0 {
				tmpZone.DistrictId = uint(locationId)
				break
			}
		}
		zoneCodes = append(zoneCodes, tmpZone.ZoneCode)
		zoneList = append(zoneList, tmpZone)
	}
	exist, eerr := repo.checkIsExistZoneCodeInPostCode(ctx, map[string]interface{}{
		"routing_type = ?": routingType,
		"product_id = ?":   0,
		"post_code != ?":   "",
		"zone_code in (?)": zoneCodes,
	})
	if exist || eerr != nil {
		return srerr.New(srerr.ParamErr, nil, "zone code exist in post code %v", objutil.RemoveDuplicatedStrings(zoneCodes))
	}
	if len(zoneList) == 0 {
		return srerr.New(srerr.InsertZoneCodeErr, nil, "data all invalid or empty file")
	}
	if err := repo.zoneDao.BulkInsertZoneCode(ctx, zoneList, RunningZoneType); err != nil {
		return srerr.With(srerr.InsertZoneCodeErr, zoneList[0].ZoneCode, err)
	}

	return nil
}

func (repo *ZoneRepoImpl) ImportProductZone(ctx context.Context, file multipart.File, routingType, zoneType uint8) *srerr.Error {
	rows, err := fileutil.GetRows(file)
	if err != nil {
		return err
	}
	if len(rows) > 5000 {
		return srerr.New(srerr.ExcelFileOpenError, nil, "can not import rows > 5000")
	}

	mustField := []string{
		"Product ID",
		"Zone Code",
		"State",
	}
	header := rows[0]
	if ok := fileutil.CheckMustFieldExisted(mustField, header); !ok {
		return srerr.New(srerr.ExcelValidateError, nil, "")
	}
	operator, _ := apiutil.GetUserInfo(ctx)
	logger.CtxLogInfof(ctx, "excel log - rows:%v", len(rows))
	//从logistic_product_tab中获取所有的product
	productList, pErr := repo.lpsApi.GetProductBaseInfoList(ctx)
	if pErr != nil {
		return srerr.With(srerr.GetProductBaseFail, "", err)
	}
	// Product对应的zone codes
	productZoneCodes := map[int][]string{}
	productIDMap := make(map[int]struct{})
	for _, product := range productList {
		productIDMap[product.ProductId] = struct{}{}
	}
	productLocationCheckMap := make(map[int][]*address.Location)
	var zoneList []*zone.LocationZone
	now := timeutil.GetCurrentUnixTimeStamp(ctx)
	for _, row := range rows[1:] {
		if row == nil || !checkRow(row) {
			break
		}
		row = extendLoc(row)
		zoneCode := row[1]
		if fileutil.CheckEmpty(zoneCode) || fileutil.CheckLenGrater(zoneCode, 32) || zoneCode == "" {
			logger.CtxLogErrorf(ctx, "invalid zone code, row:%v", row)
			return srerr.New(srerr.InsertZoneCodeErr, row, "invalid zone code")
		}
		loc, locErr := repo.addrRepo.GetLocationByLocName(ctx, envvar.GetCID(), row[2], row[3], row[4], row[5])
		if locErr != nil {
			return srerr.New(srerr.InsertZoneCodeErr, row, "parse location id failed")
		}
		productID, err := strconv.Atoi(row[0])
		if err != nil {
			return srerr.New(srerr.ParseProductIDErr, row, "parse product id failed")
		}
		if _, exist := productIDMap[productID]; !exist {
			return srerr.New(srerr.ProductNotFound, row, fmt.Sprintf("ProductID: %v not exist", productID))
		}
		productLocationCheckMap[productID] = append(productLocationCheckMap[productID], loc)
		tmpZone := &zone.LocationZone{
			ProductID:   uint64(productID),
			ZoneCode:    zoneCode,
			State:       loc.GetState(),
			City:        loc.GetCity(),
			District:    loc.GetDistrict(),
			Street:      loc.GetStreet(),
			RoutingType: routingType,
			OperateBy:   operator,
			Ctime:       uint(now),
			Mtime:       uint(now),
		}
		for _, locationId := range []int{int(loc.GetStreetLocId()), int(loc.GetDistrictLocId()), int(loc.GetCityLocId()), int(loc.GetStateLocId())} {
			if locationId > 0 {
				tmpZone.DistrictId = uint(locationId)
				break
			}
		}
		if _, ok := productIDMap[productID]; !ok {
			productZoneCodes[productID] = []string{}
		}
		productZoneCodes[productID] = append(productZoneCodes[productID], zoneCode)
		zoneList = append(zoneList, tmpZone)
	}
	if len(zoneList) <= 0 {
		return srerr.New(srerr.InsertZoneCodeErr, "", "data all invalid or empty file")
	}

	// 检查上传的zone codes中是否和postCode中对应的zone code有重叠
	for productId, zoneCodes := range productZoneCodes {
		if exist, eerr := repo.checkIsExistZoneCodeInPostCode(ctx, map[string]interface{}{
			"routing_type = ?": routingType,
			"post_code != ?":   "",
			"zone_code in (?)": zoneCodes,
			"product_id = ?":   productId,
			"zone_type = ?":    zoneType,
		}); exist || eerr != nil {
			return srerr.New(srerr.ParamErr, nil, "product=%v,zone code exist in post code mapping zone code %v", productId, zoneCodes)
		}
	}

	if err := repo.checkLocationCover(ctx, productLocationCheckMap); err != nil {
		return err
	}

	productIDList := make([]int, 0)
	for productID := range productLocationCheckMap {
		productIDList = append(productIDList, productID)
	}
	if err := repo.zoneDao.DeleteZoneCodeByProductList(ctx, objutil.RemoveDuplicate(productIDList), zoneType); err != nil {
		return srerr.With(srerr.DeleteZoneCodeErr, "", err)
	}
	if err := repo.zoneDao.BulkInsertZoneCode(ctx, zoneList, zoneType); err != nil {
		return srerr.With(srerr.InsertZoneCodeErr, "", err)
	}

	return nil
}

func (repo ZoneRepoImpl) ExportZone(ctx context.Context, query *zone.ZoneQuery) (*bytes.Buffer, *srerr.Error) {
	data, _, fErr := repo.zoneDao.FilterLocationZones(ctx, query)
	if fErr != nil {
		return nil, fErr
	}

	Zones := make([]*zone.LocationZone, len(data))
	for i, zoneDt := range data {
		tmpZone := repo.copyLocationZoneTab(zoneDt)
		Zones[i] = tmpZone
	}
	// 生成新的xlsx导出
	file := excelize.NewFile()
	sheetName := file.GetSheetName(0)

	_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{"Zone Code", "State", "City", "District", "Street", "Operate By"})
	//w := new(io.Writer)
	for rowN, zdt := range Zones {
		line := rowN + 1
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), zdt.ZoneCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), zdt.State)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), zdt.City)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), zdt.District)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), zdt.Street)
	}
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return nil, srerr.With(srerr.SaveExcelErr, sheetName, err)
	}
	return buffer, nil
}

func (repo *ZoneRepoImpl) ExportProductZone(ctx context.Context, query *zone.ZoneQuery) (*bytes.Buffer, *srerr.Error) {
	data, _, fErr := repo.zoneDao.FilterLocationZones(ctx, query)
	if fErr != nil {
		return nil, fErr
	}
	Zones := make([]*zone.LocationZone, len(data))
	for i, zoneDt := range data {
		tmpZone := repo.copyLocationZoneTab(zoneDt)
		Zones[i] = tmpZone
	}
	// 生成新的xlsx导出
	file := excelize.NewFile()
	sheetName := file.GetSheetName(0)

	_ = file.SetSheetRow(sheetName, "A1", &[]interface{}{"Product ID", "Zone Code", "State", "City", "District", "Street", "Operate By"})
	//w := new(io.Writer)
	for rowN, zdt := range Zones {
		line := rowN + 1
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 0), zdt.ProductID)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 1), zdt.ZoneCode)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 2), zdt.State)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 3), zdt.City)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 4), zdt.District)
		_ = file.SetCellValue(sheetName, fileutil.GetAxis(line, 5), zdt.Street)
	}
	buffer, err := file.WriteToBuffer()
	if err != nil {
		return nil, srerr.With(srerr.SaveExcelErr, sheetName, err)
	}
	return buffer, nil
}

func (repo *ZoneRepoImpl) UpdateZone(ctx context.Context, id int64, ZoneCode string, districtID uint) *srerr.Error {
	locationZone, err := repo.zoneDao.GetLocationZoneById(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, id, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	loc, lErr := repo.addrRepo.GetLocationByLocId(ctx, int64(districtID))
	if lErr != nil {
		return srerr.With(srerr.DatabaseErr, districtID, lErr)
	}
	// 检查编辑的zone code是否存在于postCode类型对应的zone code中
	condition := generateEditCheckCondition(locationZone, []string{ZoneCode})
	existed, cerr := repo.checkIsExistZoneCodeInPostCode(ctx, condition)
	if existed || cerr != nil {
		logger.CtxLogErrorf(ctx, "zone code[%v] exist in postcode,err=%v", ZoneCode, err)
		return srerr.New(srerr.ParamErr, ZoneCode, "zone code[%v] exist in postcode,err=%v", ZoneCode, err)
	}
	err = repo.zoneDao.UpdateZoneCode(ctx, id, ZoneCode, districtID, loc.GetState(), loc.GetCity(), loc.GetDistrict(), loc.GetStreet())
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	return nil
}

func (repo *ZoneRepoImpl) DeleteZone(ctx context.Context, id int64) *srerr.Error {
	_, err := repo.zoneDao.GetLocationZoneById(ctx, id)
	if dbutil.IsDataNotFound(err) {
		return srerr.With(srerr.RuleNotFound, id, err)
	}
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	err = repo.zoneDao.DeleteZoneCode(ctx, id)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, id, err)
	}
	return nil
}

func (repo *ZoneRepoImpl) GetReceiverZoneList(ctx context.Context, query *zone.ZoneQuery) ([]*zone.LocationZone, int32, *srerr.Error) {
	data, total, err := repo.zoneDao.FilterLocationZones(ctx, query)

	if err != nil {
		return nil, 0, err
	}
	Zones := make([]*zone.LocationZone, len(data))

	for i, zoneDt := range data {
		tmpZone := repo.copyLocationZoneTab(zoneDt)

		Zones[i] = tmpZone
	}

	return Zones, int32(total), nil
}

func (repo *ZoneRepoImpl) isDistrictIdInZoneList(ctx context.Context, productId int, districtId uint64, ZoneCodeList []string, routingType, zoneType uint8) string {
	for _, zoneCode := range ZoneCodeList {
		zoneExist := repo.getLocationZoneFromCache(ctx, productId, uint(districtId), zoneCode, routingType, zoneType)
		logger.CtxLogInfof(ctx, "isDistrictIdInZoneList productId=%v|districtId=%v|zoneCode=%v|routingType=%v|zoneType=%v|zoneExist=%v", productId, districtId, zoneCode, routingType, zoneType, zoneExist)
		if zoneExist {
			return zoneCode
		}
	}
	return ""
}

func (repo *ZoneRepoImpl) getLocationZoneFromCache(ctx context.Context, productId int, districtId uint, zoneCode string, routingType, zoneType uint8) bool {
	key := formatLocationZoneCacheKey(productId, districtId, zoneCode, routingType, zoneType)
	data, err := localcache.Get(ctx, constant.LocationZone, key)
	if err != nil {
		return false
	}
	if data.(bool) {
		return true
	}
	return false
}

func (repo *ZoneRepoImpl) checkLocationCover(ctx context.Context, checkMap map[int][]*address.Location) *srerr.Error {
	for _, locList := range checkMap {
		sort.Sort(address.LocationInfoList(locList))
		checkMap := make(map[int64]*address.Location)
		for _, loc := range locList {
			locID := loc.GetLocationID()
			existLoc, exist := checkMap[locID]
			if exist {
				return srerr.New(srerr.LocationCoverErr, "", fmt.Sprintf("Location cover, [%s] and [%s]", loc.GetLocationName(), existLoc.GetLocationName()))
			}
			for parentID := loc.GetParentID(); parentID != 0; {
				existLoc, exist := checkMap[parentID]
				if exist {
					return srerr.New(srerr.LocationCoverErr, "", fmt.Sprintf("Location cover, [%s] and [%s]", loc.GetLocationName(), existLoc.GetLocationName()))
				}
				parentLoc, err := repo.addrRepo.GetLocationByLocId(ctx, parentID)
				if err != nil {
					return err
				}
				parentID = parentLoc.GetParentID()
			}
			checkMap[locID] = loc
		}
	}

	return nil
}

func DumpLocationZone() (map[string]interface{}, error) {
	var locationZones []*zone.LocationZoneTab

	if err := dbutil.Select(context.TODO(), zone.LocationZoneTabHook, nil, &locationZones); err != nil {
		return nil, err
	}

	locationZoneMap := map[string]interface{}{}
	for _, locationZone := range locationZones {
		key := formatLocationZoneCacheKey(locationZone.ProductID, locationZone.DistrictId, locationZone.ZoneCode, locationZone.RoutingType, locationZone.ZoneType)
		if locationZone.PostCode != "" { // todo 封装方法
			key = formatPostCodeCacheKey(locationZone.ProductID, locationZone.ZoneCode, locationZone.RoutingType, locationZone.ZoneType, locationZone.PostCode)
		}
		locationZoneMap[key] = true
	}
	return locationZoneMap, nil

}

func formatLocationZoneCacheKey(productId int, districtId uint, zoneCode string, routingType, zoneType uint8) string {
	return fmt.Sprintf("routing-location-zone:%v-%v-%v-%v-%v", productId, districtId, zoneCode, routingType, zoneType)
}

func formatPostCodeCacheKey(productId int, zoneCode string, routingType, zoneType uint8, postCode string) string {
	return fmt.Sprintf("routing-location-zone:%v-%v-%v-%v-%v", productId, zoneCode, routingType, zoneType, postCode)
}

func extendLoc(loc []string) []string {
	for k := len(loc); k < 6; k++ {
		loc = append(loc, "")
	}
	return loc
}

func checkRow(row []string) bool {
	for _, item := range row {
		if item != "" {
			return true
		}
	}

	return false
}

func (repo *ZoneRepoImpl) copyLocationZoneTab(data *zone.LocationZoneTab) *zone.LocationZone {
	tmpZone := &zone.LocationZone{}
	if data != nil {
		tmpZone.Id = data.Id
		tmpZone.ProductID = uint64(data.ProductID)
		tmpZone.ZoneCode = data.ZoneCode
		tmpZone.State = data.State
		tmpZone.City = data.City
		tmpZone.District = data.District
		tmpZone.Street = data.Street
		tmpZone.DistrictId = data.DistrictId
		tmpZone.ZoneType = data.ZoneType
		tmpZone.RoutingType = data.RoutingType
		tmpZone.Ctime = data.Ctime
		tmpZone.Mtime = data.Mtime
	}

	return tmpZone
}

func (repo *ZoneRepoImpl) GetZoneCodeList(ctx context.Context, productId int, routingType, zoneType uint8) ([]string, *srerr.Error) {
	// local/spx postcode不区分product
	if routingType == rule.SPXRoutingType {
		productId = 0
	}
	data, err := repo.zoneDao.ListZoneCode(ctx, productId, routingType, zoneType)
	if dbutil.IsDataNotFound(err) {
		return nil, srerr.With(srerr.GetProductZonesFailed, "list zone code", err)
	}
	if err != nil {
		return nil, srerr.With(srerr.DatabaseErr, "list zone code", err)
	}
	zoneCode := make([]string, 0, len(data))
	for _, zoneDt := range data { // 加载Location zone
		zoneCode = append(zoneCode, zoneDt.ZoneCode)
	}
	return zoneCode, nil
}

func (repo *ZoneRepoImpl) GetZoneCodeListFromCache(ctx context.Context, productId int, routingType, zoneType uint8) ([]string, *srerr.Error) {
	key := fmt.Sprintf("%d:%d:%d", productId, routingType, zoneType)
	var ret []string
	if err := repo.layerCache.Get(ctx, constant.LevelCacheProductZoneCodes, key, &ret,
		layercache.WithLoader(repo.getProductZoneCodes),
		layercache.WithWarmExpire(),
	); err != nil {
		logger.CtxLogErrorf(ctx, "get cache for ProductZoneCodes|get_cache_fail|key=%+v, value=%+v", key, ret)
		return nil, srerr.With(srerr.GetProductZonesFailed, key, err)
	}

	return ret, nil
}

func (repo *ZoneRepoImpl) getProductZoneCodes(ctx context.Context, key string) (interface{}, error) {
	splits := strings.Split(key, ":")
	if len(splits) != 3 {
		return nil, errors.New("cache key invalid")
	}
	productId, err := strconv.Atoi(splits[0])
	if err != nil {
		return nil, errors.New("cache key invalid")
	}
	routingType, err := strconv.Atoi(splits[1])
	if err != nil {
		return nil, errors.New("cache key invalid")
	}
	zoneType, err := strconv.Atoi(splits[2])
	if err != nil {
		return nil, errors.New("cache key invalid")
	}

	return repo.GetZoneCodeList(ctx, productId, uint8(routingType), uint8(zoneType))
}

func (repo *ZoneRepoImpl) DeployZoneToRunning(ctx context.Context, productID int, tx scormv2.SQLCommon) *srerr.Error {
	// 1.Get forecasting zone
	query := zone.NewZoneQuery()
	query = query.ByProductID(productID)
	query = query.ByZoneType(int(ForecastingZoneType))
	query = query.WithLimit(5000)
	forecastLocationZones, _, err := repo.zoneDao.FilterLocationZones(ctx, query)
	if err != nil {
		return srerr.With(srerr.DatabaseErr, "", err)
	}

	runningLocationZones := make([]*zone.LocationZoneTab, 0, len(forecastLocationZones))
	now := uint(recorder.Now(ctx).Unix())
	for _, forecastZone := range forecastLocationZones {
		runningLocationZones = append(runningLocationZones, &zone.LocationZoneTab{
			ProductID:   forecastZone.ProductID,
			ZoneCode:    forecastZone.ZoneCode,
			State:       forecastZone.State,
			City:        forecastZone.City,
			District:    forecastZone.District,
			Street:      forecastZone.Street,
			DistrictId:  forecastZone.DistrictId,
			OperateBy:   forecastZone.OperateBy,
			ZoneType:    RunningZoneType,
			RoutingType: forecastZone.RoutingType,
			Ctime:       now,
			Mtime:       now,
		})
	}

	// 2.Delete running zone
	if err := repo.zoneDao.DeleteZoneCodeByProductListWithTX(ctx, []int{productID}, RunningZoneType, tx); err != nil {
		return srerr.With(srerr.DatabaseErr, "", err)
	}

	// 3.Copy forecasting zone to running
	if err := repo.zoneDao.BatchCreateZoneCode(ctx, runningLocationZones, tx); err != nil {
		return srerr.With(srerr.InsertZoneCodeErr, "", err)
	}

	return nil
}

func generateEditCheckCondition(locationZone *zone.LocationZoneTab, zoneCodes []string) map[string]interface{} {
	condition := map[string]interface{}{}
	if locationZone.ProductID != 0 {
		condition["product_id = ?"] = locationZone.ProductID
	}
	condition["post_code != ?"] = ""
	condition["zone_code in (?)"] = zoneCodes
	condition["routing_type = ?"] = locationZone.RoutingType

	return condition
}

// 编辑的zone code是否在postCode对应的zone code存在
func (repo *ZoneRepoImpl) checkIsExistZoneCodeInPostCode(ctx context.Context, condition map[string]interface{}) (bool, *srerr.Error) {
	var locationZones []zone.LocationZoneTab
	if err := dbutil.Select(ctx, zone.LocationZoneTabHook, condition, &locationZones); err != nil {
		logger.CtxLogErrorf(ctx, "check zone code exist err=%v ", err)
		return false, srerr.New(srerr.DatabaseErr, nil, "check zone code exist err=%v ", err)
	}
	existed := len(locationZones) != 0
	if existed {
		logger.CtxLogErrorf(ctx, "location zone code exist in post code zone code")
	}
	return existed, nil
}
