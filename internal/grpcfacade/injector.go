package grpc_api

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/smartrouting_debug"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volume_counter_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"github.com/google/wire"
)

var VolumeRoutingServerProviderSet = wire.NewSet(
	NewVolumeRoutingServer,
	wire.Bind(new(pb.VolumeRoutingServerServer), new(*VolumeRoutingServer)),
	vrservice.ServiceProviderSet,
	lpsclient.LpsApiProviderSet,
	llsclient.LlsApiProviderSet,
	lane.LaneServiceProviderSet,
	volumerouting.ZoneRuleMgrProviderSet,
	volume_counter_service.VolumeCounterServiceProviderSet,
	vrrepo.ZoneRuleRepoProviderSet,
	vrrepo.ZoneRepoProviderSet,
	vrrepo.ZoneGroupRepoProvideSet,
	vrrepo.VolumeTaskRepoProviderSet,
	address.AddrRepoProviderSet,
	chargeclient.RateApiProviderSet,
	volume_counter.VolumeCounterProviderSet,
	volume_counter.ILHWeightCounterProviderSet,
	product.ProdRepoProviderSet,
	lnpclient.LnpApiProviderSet,
)

var SmartRoutingDebugServerProviderSet = wire.NewSet(
	NewSmartRoutingDebugServer,
	wire.Bind(new(pb.SmartRoutingDebugServer), new(*SmartRoutingDebugServer)),
	smartrouting_debug.SmartRoutingDebugProviderSet,
)
