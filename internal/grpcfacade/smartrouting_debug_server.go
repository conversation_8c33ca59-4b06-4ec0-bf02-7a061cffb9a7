package grpc_api

import (
	"context"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/smartrouting_debug"
)

type SmartRoutingDebugServer struct {
	SmartRoutingDebug smartrouting_debug.SmartRoutingDebugInterface
}

func NewSmartRoutingDebugServer(smartRoutingDebug smartrouting_debug.SmartRoutingDebugInterface) *SmartRoutingDebugServer {
	return &SmartRoutingDebugServer{
		SmartRoutingDebug: smartRoutingDebug,
	}
}

func (s *SmartRoutingDebugServer) RefreshLocalCache(ctx context.Context, req *pb.RefreshLocalCacheReq) (*pb.RefreshLocalCacheResp, error) {
	return s.SmartRoutingDebug.RefreshLocalCacheImpl(ctx, req)
}
