package grpc_api

import (
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/server"
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	available_lh "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_rule"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	available_lh2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"github.com/google/wire"
)

type GrpcAPI struct {
	SmartRoutingServer      pb.SmartRoutingServer
	VolumeRoutingService    pb.VolumeRoutingServerServer
	MaskAllocateServer      pb.MaskingServer
	SmartRoutingDebugServer pb.SmartRoutingDebugServer
}

func (api *GrpcAPI) BindGrpcServer() {
	chassis.RegisterSchema("grpc", api.SmartRoutingServer, server.WithRPCServiceRegisterFunc(pb.RegisterSmartRoutingServer))
	chassis.RegisterSchema("grpc", api.VolumeRoutingService, server.WithRPCServiceRegisterFunc(pb.RegisterVolumeRoutingServerServer))
	chassis.RegisterSchema("grpc", api.MaskAllocateServer, server.WithRPCServiceRegisterFunc(pb.RegisterMaskingServer))
	chassis.RegisterSchema("grpc", api.SmartRoutingDebugServer, server.WithRPCServiceRegisterFunc(pb.RegisterSmartRoutingDebugServer))
}

var ProviderSet = wire.NewSet(
	wire.Struct(new(GrpcAPI), "*"),
	select_lane.SmartRoutingServiceProviderSet,
	VolumeRoutingServerProviderSet,
	SmartRoutingDebugServerProviderSet,
	schedule_factor.NewDgFactor,
	schedule_factor.NewMinVolumeFactor,
	schedule_factor.NewMaxCapacityFactor,
	schedule_factor.NewLinePriorityFactor,
	schedule_factor.NewLineCheapestShippingFeeFactor,
	schedule_factor.NewMinWeightFactor,
	schedule_factor.NewMaxWeightFactor,
	schedule_factor.NewDefaultPriorityFactor,
	schedule_factor.NewDefaultWeightageFactor,
	schedule_factor.NewMinVolumeV2,
	schedule_factor.NewMaxVolumeV2,
	schedule_factor.NewILHParcelMinVolumeFactor,
	schedule_factor.NewILHParcelMaxCapacityFactor,
	schedule_factor.NewCombinationPriorityFactor,
	schedule_factor.NewFactorSet,
	allocation.MaskServerProviderSet,
	routing_rule.SoftRoutingProviderSet,
	ruledata.SoftRuleRepoProviderSet,
	config.AllocationConfigProviderSet,
	layercache.NewLayerCache,
	locationzone.LocationZoneProviderSet,
	routing.RoutingServiceProviderSet,
	routing.ILHRoutingServiceProviderSet,
	routing.RoutingRepoProviderSet,
	lfsclient.LfsClientPset,
	routing_log.RoutingLogSrvProviderSet,
	routing_log.RoutingLogRepoProviderSet,
	ccclient.CCApiProviderSet,
	cc_routing.CCRoutingServiceProviderSet,
	cc_routing_rule.CCRoutingRuleRepoProviderSet,
	productpriority.RepoProviderSet,
	allocation.NewSoftRuleService,
	rulevolume.MaskRuleVolumeProviderSet,
	rule.MaskRuleProviderSet,
	config.MaskConfigProviderSet,
	outercheck.AllOutCheckProviderSet,
	volumecounter.MaskVolumeCounterProviderSet,
	productpriority.BusinessProviderSet,
	schedule_stat.NewMaskingForecastScheduleVisualStat,
	schedule_stat.NewMaskingScheduleVisualStatV2,
	schedule_stat.NewMaskingScheduleVisualStat,
	schedule_stat.NewScheduleVisualSet,
	schedule_visual.ScheduleCountStatProviderSet,
	routing.RoutingPreCalcFeeProviderSet,
	volume_dashboard.VolumeChangeServiceProviderSet,
	order.BatchAllocateOrderRepoProviderSet,
	batch_allocate.GreyServiceProviderSet,
	allocpath.AllocationPathSrvProviderSet,
	dataclient.NewDataApi,
	rulevolume.CheckVolumeFinderProvider,
	business_audit.BusinessAuditRepoProvider,
	approval_manager.ApprovalExecutorProvider,
	auditclient.AuditApiProvider,
	pickup_priority.PickupPriorityRepoProviderSet,
	parcel_type_definition2.ParcelTypeDefinitionRepoProviderSet,
	parcel_type_definition.ParcelTypeDefinitionServiceProviderSet,
	pickup_efficiency_counter.PickupEffCounterProviderSet,
	wbcclient.WbcApiProviderSet,
	spex_service.SpexServiceProviderSet,
	lcosclient.LcosApiProviderSet,
	whitelist.ShopWhiteListServiceProviderSet,
	whitelist2.ShopWhitelistRepoProviderSet,
	available_lh.AvailableLHRepoProviderSet,
	available_lh2.AvailableLHServiceProviderSet,
	repo.LHCapacityRepoProviderSet,
	lh_capacity.LHCapacityServiceProviderSet,
)
