package envvar

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant/enum"
	uuid "github.com/satori/go.uuid"
	"log"
	"os"
	"strconv"
	"strings"
	"sync"
)

var (
	cid        string    // 对应环境变量中的 CID
	env        string    // 对应环境变量中的 ENV
	idc        string    // 对应环境变量中的 IDC
	GetEnvOnce sync.Once // 保证 Env 只获取一次
	GetCidOnce sync.Once // 保证 Cid 只获取一次
	GetIdcOnce sync.Once // 保证 Idc 只获取一次
)

const (
	idcUs3 = "us3"
	idcUs2 = "us2"
)

func GetEnv() enum.Env {
	GetEnvOnce.Do(func() {
		env = os.Getenv("ENV") // nolint
		if env == "" {
			env = os.Getenv("env") // nolint
		}
		env = strings.ToUpper(env)
	})

	switch env {
	case enum.TEST, enum.UAT, enum.STAGING, enum.STABLE, enum.LIVEISH, enum.LIVE:
	default:
		env = enum.LOCAL
	}
	return env
}

func GetEnvWithCtx(ctx context.Context) enum.Env {
	return recorder.Wrap(getEnvWithCtx).(func(ctx context.Context) enum.Env)(ctx)
}

var getEnvWithCtx = func(ctx context.Context) enum.Env {
	return GetEnv()
}

func GetEnvLower(ctx context.Context) enum.Env {
	return strings.ToLower(GetEnvWithCtx(ctx))
}

func IsLivetest() bool {
	return strings.Contains(
		strings.ToLower(os.Getenv("MODULE_NAME")), // nolint
		"livetest",
	)
}

func GetConfPath() string {
	return os.Getenv("CHASSIS_CONF_DIR") // nolint
}

func GetPort() (int, bool) {
	p := os.Getenv("PORT") // nolint
	if p == "" {
		return 0, false
	}
	port, err := strconv.Atoi(p)
	if err != nil {
		log.Printf("Port env var %v can't convert to int, err=%v", p, err)
		return 0, false
	}
	return port, true
}

func GetModuleName() string {
	mn := os.Getenv("MODULE_NAME") // nolint
	return strings.ToUpper(mn)
}

func IsTaskModule() bool {
	return GetModuleName() == "TASK"
}

func GetCID() string {
	GetCidOnce.Do(func() {
		cid = strings.ToUpper(os.Getenv("CID")) // nolint
		if cid == "" {
			cid = "SG"
		}
	})

	return cid
}

func GetCIDLower() string {
	return strings.ToLower(GetCID())
}

func GetIDC() string {
	GetIdcOnce.Do(func() {
		idc = strings.ToLower(os.Getenv("IDC")) // nolint
	})

	return idc
}

func IsIDCUs3() bool {
	return GetIDC() == idcUs3
}

func IsIDCUs2() bool {
	return GetIDC() == idcUs2
}

func IsIDCUs() bool {
	return IsIDCUs2() || IsIDCUs3()
}

func GetFte() string {
	fteName := os.Getenv("FTE_NAME") // nolint
	if fteName == "" {
		fteName = os.Getenv("fte_name") // nolint
	}
	e := strings.ToLower(fteName)
	return e
}

func IsFte(ctx context.Context) bool {
	env := GetEnvWithCtx(ctx)
	if env == enum.LIVE || env == enum.LIVEISH {
		return false
	}
	if os.Getenv("FTE_NAME") == "" { // nolint
		return false
	}

	return true
}

func GetRegion() enum.Region {
	return enum.Region(GetCID())
}

func GetDataServiceMock() string {
	return strings.ToUpper(os.Getenv("DATA_SERVICE_MOCK")) // nolint
}

func IsMockDataService() bool {
	if strings.ToUpper(os.Getenv("DATA_SERVICE_MOCK")) != "" { // nolint
		return true
	}
	return false
}

func EnsureModuleName(name string) {
	mn := os.Getenv("MODULE_NAME") // nolint
	if mn == "" {
		_ = os.Setenv("MODULE_NAME", fmt.Sprintf("%s-%s", name, uuid.NewV4().String())) // nolint
	}
}

func GetRequestEmail(ctx context.Context) string {
	if emailObj := ctx.Value(constant.EmailKey); emailObj != nil {
		if email, ok := emailObj.(string); ok {
			return email
		}
	}
	return ""
}

func GetRequestUrl(ctx context.Context) string {
	if urlObj := ctx.Value(constant.URLKey); urlObj != nil {
		if url, ok := urlObj.(string); ok {
			return url
		}
	}
	return ""
}
