package grpc_util

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"github.com/gogo/protobuf/proto"
)

func GenerateRespHeader(requestId string, err *srerr.Error) *pb.RespHeader {
	h := pb.RespHeader{
		RequestId: proto.String(requestId),
		Retcode:   proto.Int32(0),
		Message:   proto.String("success"),
	}
	if err != nil {
		h.Retcode = proto.Int32(int32(err.GetCode()))
		h.Message = proto.String(err.GetMessage())
		h.Cause = proto.String(err.Error())
	}
	// 上报retcode
	if h.Retcode != nil {
		prometheusutil.ServiceRetcodeReport(*h.Retcode)
	}
	return &h
}
