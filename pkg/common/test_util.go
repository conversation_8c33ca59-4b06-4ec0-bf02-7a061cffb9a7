package common

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"github.com/stretchr/testify/assert"
	"testing"
)

func AssertResult(t *testing.T, got interface{}, want interface{}, gotErr *srerr.Error, wantErr *srerr.Error) {
	if !objutil.CompareInterfaces(got, want) {
		t.Errorf("got = %v, want %v", got, want)
	}
	if gotErr != nil && wantErr != nil {
		if !assert.Equal(t, wantErr.Error(), gotErr.Error()) {
			t.<PERSON><PERSON>("gotError = %v, expectedErr %v", gotErr.Error(), wantErr.Error())
		}
	} else if (gotErr == nil && wantErr != nil) || (gotErr != nil && wantErr == nil) {
		t.<PERSON><PERSON><PERSON>("gotError and expectedErr，one is NIL and the other is not NIL ")
	}
}

func NewInt(v int) *int {
	return &v
}
