package common

import (
	jsoniter "github.com/json-iterator/go"
	"strconv"
)

func InterfaceToStr(value interface{}) string {
	// interface 转 string
	var key string
	if value == nil {
		return ""
	}
	switch value := value.(type) {
	case bool:
		key = strconv.FormatBool(value)
	case float64:
		key = strconv.FormatFloat(value, 'f', -1, 64)
	case float32:
		key = strconv.FormatFloat(float64(value), 'f', -1, 64)
	case int:
		key = strconv.Itoa(value)
	case uint:
		key = strconv.Itoa(int(value))
	case int8:
		key = strconv.Itoa(int(value))
	case uint8:
		key = strconv.Itoa(int(value))
	case int16:
		key = strconv.Itoa(int(value))
	case uint16:
		key = strconv.Itoa(int(value))
	case int32:
		key = strconv.Itoa(int(value))
	case uint32:
		key = strconv.Itoa(int(value))
	case int64:
		key = strconv.FormatInt(value, 10)
	case uint64:
		key = strconv.FormatUint(value, 10)
	case string:
		key = value
	case []byte:
		key = string(value)
	case *bool:
		key = strconv.FormatBool(*value)
	case *float64:
		key = strconv.FormatFloat(*value, 'f', -1, 64)
	case *float32:
		key = strconv.FormatFloat(float64(*value), 'f', -1, 64)
	case *int:
		key = strconv.Itoa(*value)
	case *uint:
		key = strconv.Itoa(int(*value))
	case *int8:
		key = strconv.Itoa(int(*value))
	case *uint8:
		key = strconv.Itoa(int(*value))
	case *int16:
		key = strconv.Itoa(int(*value))
	case *uint16:
		key = strconv.Itoa(int(*value))
	case *int32:
		key = strconv.Itoa(int(*value))
	case *uint32:
		key = strconv.Itoa(int(*value))
	case *int64:
		key = strconv.FormatInt(*value, 10)
	case *uint64:
		key = strconv.FormatUint(*value, 10)
	default:
		newValue, _ := jsoniter.MarshalToString(value)
		key = newValue
	}
	return key
}
