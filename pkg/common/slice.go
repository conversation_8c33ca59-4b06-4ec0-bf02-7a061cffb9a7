package common

func Int32ToInt64List(oldList []int32) []int64 {
	var newList []int64
	for _, oldEle := range oldList {
		newList = append(newList, int64(oldEle))
	}
	return newList
}

func Uint32ToUint64List(oldList []uint32) []uint64 {
	var newList []uint64
	for _, oldEle := range oldList {
		newList = append(newList, uint64(oldEle))
	}
	return newList
}

func Uint32ToInt64List(oldList []uint32) []int64 {
	var newList []int64
	for _, oldEle := range oldList {
		newList = append(newList, int64(oldEle))
	}
	return newList
}

func Uint32ToUntList(oldList []uint32) []uint {
	var newList []uint
	for _, oldEle := range oldList {
		newList = append(newList, uint(oldEle))
	}
	return newList
}

func RemoveDuplicateString(arr []string) []string {
	uniq := make([]string, 0, len(arr))
	checkMap := make(map[string]struct{}, len(arr))
	for _, item := range arr {
		_, exist := checkMap[item]
		if !exist {
			uniq = append(uniq, item)
			checkMap[item] = struct{}{}
			continue
		}
	}

	return uniq
}
