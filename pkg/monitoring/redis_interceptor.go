package monitoring

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
)

//func Process(ctx context.Context, opts *redis.Options, reportName string) func(func(redis.Cmder) error) func(redis.Cmder) error {
//	return func(oldProcess func(redis.Cmder) error) func(redis.Cmder) error {
//		return func(cmd redis.Cmder) error {
//			monitorCtx := WithTransactionMsg(ctx)
//
//			err := oldProcess(cmd)
//
//			status := StatusSuccess
//			if err != nil {
//				status = StatusError
//			}
//			method := cmd.Name()
//			data := fmt.Sprintf("[addr=%v, db=%v] %v %v", opts.Addr, opts.DB, method, cmd.Args())
//			if len(data) > 1000 {
//				data = data[:1000]
//			}
//			reportInterface := method
//			if reportName != "" {
//				reportInterface = reportName + "." + method
//			}
//			monitor.AwesomeReportTransactionEnd(monitorCtx, CatModuleRedis+"."+opts.Addr, reportInterface, status, data)
//
//			return err
//		}
//	}
//}
//
//func ProcessPipeline(ctx context.Context, opts *redis.Options, reportName string) func(func([]redis.Cmder) error) func([]redis.Cmder) error {
//	return func(oldProcess func([]redis.Cmder) error) func([]redis.Cmder) error {
//		return func(cmds []redis.Cmder) error {
//			monitorCtx := WithTransactionMsg(ctx)
//
//			err := oldProcess(cmds)
//
//			status := StatusSuccess
//			if err != nil {
//				status = StatusError
//			}
//
//			commands := make([]string, len(cmds))
//			for i, cmd := range cmds {
//				commands[i] = fmt.Sprintf("%v %v", cmd.Name(), cmd.Args())
//			}
//			data := fmt.Sprintf("[addr=%v, db=%v] %s",
//				opts.Addr, opts.DB, strings.Join(commands, "; "))
//			reportInterface := "pipeline"
//			if reportName != "" {
//				reportInterface = "pipeline." + reportName
//			}
//			monitor.AwesomeReportTransactionEnd(monitorCtx, CatModuleRedis, reportInterface, status, data)
//
//			return err
//		}
//	}
//}

// RedisHook is redis hook
type RedisHook struct {
	redisAddr  string
	redisDB    int
	reportName string
}

func NewRedisHook(redisAddr string, redisDB int) *RedisHook {
	return &RedisHook{
		redisAddr:  redisAddr,
		redisDB:    redisDB,
		reportName: "smart_routing_redis",
	}
}

func (h *RedisHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	monitorCtx := WithTransactionMsg(ctx)
	return monitorCtx, nil
}

func (h *RedisHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	method := cmd.Name()
	data := fmt.Sprintf("[addr=%v, db=%v] %v %v", h.redisAddr, h.redisDB, method, cmd.Args())
	if len(data) > 1000 {
		data = data[:1000]
	}
	monitor.AwesomeReportTransactionEnd(ctx, CatModuleRedis+"."+h.redisAddr, method, StatusSuccess, data)
	return nil
}

func (h *RedisHook) BeforeProcessPipeline(ctx context.Context, cmds []redis.Cmder) (context.Context, error) {
	monitorCtx := WithTransactionMsg(ctx)
	return monitorCtx, nil
}

func (h *RedisHook) AfterProcessPipeline(ctx context.Context, cmds []redis.Cmder) error {
	commands := make([]string, len(cmds))
	for i, cmd := range cmds {
		commands[i] = fmt.Sprintf("%v %v", cmd.Name(), cmd.Args())
	}
	data := fmt.Sprintf("[addr=%v, db=%v] %s",
		h.redisAddr, h.redisDB, strings.Join(commands, "; "))
	if len(data) > 1000 {
		data = data[:1000]
	}
	monitor.AwesomeReportTransactionEnd(ctx, CatModuleRedis+"."+h.redisAddr, "pipeline", StatusSuccess, data)
	return nil
}
