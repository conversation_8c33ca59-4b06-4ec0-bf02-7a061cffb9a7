package monitoring

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
)

func ReportSuccess(ctx context.Context, moduleName string, interfaceName string, data string) {
	_ = monitor.AwesomeReportEvent(ctx, moduleName, interfaceName, StatusSuccess, data)
}

func ReportError(ctx context.Context, moduleName string, interfaceName string, data string) {
	_ = monitor.AwesomeReportEvent(ctx, moduleName, interfaceName, StatusError, data)
}

func GenerateLocalForecastMonitorKey(monitorKey string, taskId int) string {
	return fmt.Sprintf("%s_%d", monitorKey, taskId)
}
