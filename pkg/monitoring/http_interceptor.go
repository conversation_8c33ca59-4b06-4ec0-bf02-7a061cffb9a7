package monitoring

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis/pkg/apm"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
)

func Trace(ctx context.Context, moduleName string, interfaceName string, fn func(ctx context.Context) error) error {
	monCtx := monitor.AwesomeReportTransactionStart(ctx)
	err := fn(monCtx)
	if err != nil {
		monitor.AwesomeReportTransactionEnd(monCtx, moduleName, interfaceName, StatusError, err.Error())
	} else {
		monitor.AwesomeReportTransactionEnd(monCtx, moduleName, interfaceName, StatusSuccess, "")
	}
	return err
}

type ReportContainer struct {
	moduleName    string
	interfaceName string
	rootMsgId     string
	parentMsgId   string
	childMsgId    string
}

func (c *ReportContainer) Error(ctx context.Context, err error) {
	msg := "error"
	if err != nil {
		msg = err.Error()
	}
	_ = monitor.AwesomeReportEvent(ctx, apm.RemoteCall, "", "0", c.childMsgId)
	monitor.AwesomeReportTransactionEnd(ctx, c.moduleName, c.interfaceName, StatusError, msg)
}

func (c *ReportContainer) Success(ctx context.Context) {
	_ = monitor.AwesomeReportEvent(ctx, apm.RemoteCall, "", "0", c.childMsgId)
	monitor.AwesomeReportTransactionEnd(ctx, c.moduleName, c.interfaceName, StatusSuccess, "")
}

func (c *ReportContainer) GetRootMsgId(ctx context.Context) string {
	return c.rootMsgId
}

func (c *ReportContainer) GetParentMsgId(ctx context.Context) string {
	return c.parentMsgId
}

func (c *ReportContainer) GetChildMsgId(ctx context.Context) string {
	return c.childMsgId
}

func Transaction(ctx context.Context, moduleName string, interfaceName string) (*ReportContainer, context.Context) {
	monCtx := monitor.AwesomeReportTransactionStart(ctx)
	rootMsgId, parentMsgId, childMsgId := getCatTracingIds(monCtx)
	return &ReportContainer{
		moduleName:    moduleName,
		interfaceName: interfaceName,
		rootMsgId:     rootMsgId,
		parentMsgId:   parentMsgId,
		childMsgId:    childMsgId,
	}, monCtx
}

func getCatTracingIds(ctx context.Context) (rootMsgId, parentMsgId, childMsgId string) {
	rootId := monitor.GetRootMsgId(ctx)
	msgId, _ := monitor.GetMsgId(ctx)
	if rootId == "" {
		rootId = msgId
	}
	childId := monitor.CreateMessageId("")

	return rootId, msgId, childId
}

//func New() string {
//	host, _ := os.Hostname()
//	return fmt.Sprintf("sls_api_%d_%s", recorder.Now(ctx).UnixNano(), host)
//}
