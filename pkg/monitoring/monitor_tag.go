package monitoring

const (
	GormContextKey     = "catCtx"
	GormDBTagKey       = "dbTag"
	CatContextTransKey = "catCtxTrans"
)

const (
	StatusSuccess   = "0"
	StatusError     = "error"
	StatusPanic     = "panic"
	StatusNotEnable = "not-enable"
	StatusLru       = "lru"
	StatusExpired   = "expired"
	StatusNotExist  = "not-exist"
)

const (
	CatModuleMySQL             = "MySQL"
	CatModuleAPI               = "API"
	CatModuleMemoryCache       = "MemoryCache"
	CatModuleRedis             = "Redis"
	CatModuleGrpcTimeout       = "GrpcTimeout"
	CatModuleHttpTimeout       = "HttpTimeout"
	CatModuleRPC               = "RPC.Client"
	CatModuleKafkaSend         = "Kafka.Send"
	CatSelectLaneApi           = "SelectLane"
	CatRerouteSelectLaneApi    = "RerouteSelectLane"
	CatAllocateApi             = "Allocate"
	CatScheduledFactor         = "ScheduledFactor"
	CatModuleSystem            = "System"
	CatILHRoutingApi           = "ILHRouting"
	CatModuleLruCache          = "LruCache"
	DefaultWeightage           = "DefaultWeightage"
	CatModuleTask              = "Task" //定义定时任务上报cat的类型
	CatModuleScheduleRule      = "ScheduleRule"
	CatModuleHBase             = "HBase"
	CatModuleMainHBase         = "MainHbase"
	CatModuleMaskingForecast   = "MaskingForecast"
	CatModuleAuditLog          = "AuditLog"
	CatScheduleProcess         = "ScheduleProcess"
	CatPreFulfillmentCheckApi  = "PreFulfillmentCheck"
	CatPanic                   = "Panic"
	CatUpdateVolumeApi         = "UpdateVolume"
	CatGoroutinePool           = "GoroutinePool"
	CatModuleLocsMonitor       = "LocsMonitor"
	CatModuleAdminMonitor      = "AdminApiMonitor"
	CatModuleVolumeMonitor     = "VolumeMonitor"
	CatModuleShippingFeeFactor = "ShippingFeeFactor"
	CatIncrAllocateVolumeApi   = "IncrAllocateVolumeApi"
	CatModuleMsgTask           = "CatModuleMsgTask"
	VnCbRouting                = "VnCbRouting"
	CatRoutingCalcFeeMonitor   = "RoutingCalcFeeMonitor"
	CatMaskingCalcFeeMonitor   = "MaskingCalcFeeMonitor"
	LocalForecastMonitor       = "LocalForecastMonitor"
	CatRoutingReCalcFeeMonitor = "RoutingReCalcFeeMonitor"
	CatModuleILHRoutingCCMode  = "ILHRoutingCCMode"
	HardCriteriaMonitor        = "HardCriteriaMonitor"
	DataApiMonitor             = "DataApiMonitor"
	CatMaskingDashboardMonitor = "MaskingDashboardMonitor"
	CatRoutingDashboardMonitor = "RoutingDashboardMonitor"
	CatBatchAllocateForecast   = "BatchAllocateForecast"
	CatScheduleVisualMonitor   = "ScheduleVisualMonitor"
	CatModuleBatchAllocate     = "BatchAllocate"
	CatMaskingVolume           = "MaskingVolume"
	CatCbShopGroup             = "CbShopGroup"
	CatParcelTypeVolume        = "ParcelTypeVolume"
	CatRoutingParcelTypeVolume = "RoutingParcelTypeVolume"
	CatVnCbPreToggleCheck      = "VnCbPreToggleCheck"
	CatSpexService             = "SpexService"
	CatAllocationPath          = "AllocationPath"
	CatParcelTypeDimension     = "ParcelTypeDimension"

	// 内部工具上报
	CatModuleDebugSmartRoutingForecast = "DebugSmartRoutingForecast"

	// checkout fulfillment product单量统计
	CatFulfillmentProductCounter = "FulfillmentProductCounter"

	// common migrate
	SwitchModuleName = "SwitchModuleName"

	// install allocate上报
	CatInstallAllocate             = "InstallAllocate"
	CatInstallAllocateUpdateVolume = "InstallAllocateUpdateVolume"

	// Revamp ILH Smart Routing
	CatModuleRevampILHRouting = "RevampILHRouting"
)

const (
	ServicePanic = "panic"
)

// selectLane接口业务上报
const (
	SelectLaneParamValid              = MetricsLabelCB + MetricsLabelLocal + "ParamValidError"
	SelectLaneAvailableListCheck      = MetricsLabelCB + MetricsLabelLocal + "NoAvailableList"
	SelectLaneRoutingConfigError      = MetricsLabelCB + MetricsLabelLocal + "RoutingConfigError"
	SelectLaneTooManyAvailableLane    = MetricsLabelCB + MetricsLabelLocal + "TooManyAvailableLane"
	SelectLaneNoSupportRoutingSuccess = MetricsLabelCB + MetricsLabelLocal + "NoSupportRoutingSuccess"
	SelectLaneMatchRuleError          = MetricsLabelCB + MetricsLabelLocal + "MatchRuleError"
	SelectLaneMatchSpxRuleError       = MetricsLabelCB + MetricsLabelLocal + "MatchSpxRuleError"
	SelectLaneMockError               = MetricsLabelCB + MetricsLabelLocal + "MockError"
	SelectLaneMockSuccess             = MetricsLabelCB + MetricsLabelLocal + "MockSuccess"
	SelectLaneSmartRoutingError       = MetricsLabelCB + MetricsLabelLocal + "RoutingError"
	SelectLaneSmartRoutingSuccess     = MetricsLabelCB + MetricsLabelLocal + "RoutingSuccess"
	SelectLaneVnCbRoutingError        = MetricsLabelCB + "VnCbRoutingError"
	SelectLaneVnCbRoutingSuccess      = MetricsLabelCB + "VnCbRoutingSuccess"
	SelectLaneTwCCError               = MetricsLabelCB + "SelectLaneTwCCError"
	PreVnCbToggleCheck                = "PreVnCbToggleCheck"
	LineParcelSetError                = "LineParcelSetError"
)

const (
	MetricsLabelCB    = "[CB]"
	MetricsLabelLocal = "[Local]"
)

// Allocate接口上报
const (
	AllocateError                 = MetricsLabelLocal + "AllocateError"
	AllocateSuccess               = MetricsLabelLocal + "AllocateSuccess"
	EstimateMaskingChannelError   = MetricsLabelLocal + "EstimateMaskingChannelError"
	EstimateMaskingChannelSuccess = MetricsLabelLocal + "EstimateMaskingChannelSuccess"
	AdjustCapacityError           = MetricsLabelLocal + "AdjustCapacityError"
	AdjustLocVolumeError          = MetricsLabelLocal + "AdjustLocVolumeError"
	RouteAndZoneMode              = MetricsLabelLocal + "RouteAndZoneMode"
	RouteAndZoneSuccess           = MetricsLabelLocal + "RouteAndZoneSuccess"
	RouteAndZoneError             = MetricsLabelLocal + "RouteAndZoneError"
)

// PreFulfillmentCheck接口上报
const (
	PreFulfillmentCheckSuccess              = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckSuccess"
	PreFulfillmentCheckConfigError          = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckConfigError"
	PreFulfillmentCheckFindRuleError        = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckFindRuleError"
	PreFulfillmentCheckDisableCheckError    = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckDisableCheckError"
	PreFulfillmentCheckNotSupportError      = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckNotSupportError"
	PreFulfillmentCheckParcelDimensionError = MetricsLabelCB + MetricsLabelLocal + "PreFulfillmentCheckParcelDimensionError"
)

// 调度过程异常情况上报
const (
	EnterSelectLaneShippingFeeFactor   = MetricsLabelLocal + "EnterSelectLaneShippingFeeFactor"
	SelectLaneShippingFeeLineAddrError = MetricsLabelLocal + "SelectLaneShippingFeeLineAddrError"
	SelectLaneShippingFeeReqError      = MetricsLabelLocal + "SelectLaneShippingFeeReqError"
	SelectLaneLineAddrMapIsNil         = MetricsLabelLocal + "SelectLaneLineAddrMapIsNil"
	AllocateShippingFeeReqError        = MetricsLabelLocal + "AllocateShippingFeeReqError"
	AllocateShippingFeeEsfResultError  = MetricsLabelLocal + "AllocateShippingFeeEsfResultError"
	EnterAllocateShippingFeeFactor     = MetricsLabelLocal + "EnterAllocateShippingFeeFactor"
	ParcelAttrNotFound                 = MetricsLabelLocal + "ParcelAttrNotFound"
	DgNotFound                         = MetricsLabelLocal + "DgNotFound"
	// 运力规则异常监控
	MaskingCountryVolumeZeroNum       = MetricsLabelCB + MetricsLabelLocal + "MaskingCountryVolumeZeroNum"
	MaskingZoneRouteVolumeZeroNum     = MetricsLabelCB + MetricsLabelLocal + "MaskingZoneRouteVolumeZeroNum"
	RoutingLineVolumeZeroNum          = MetricsLabelCB + MetricsLabelLocal + "RoutingLineVolumeZeroNum"
	RoutingLineZoneRouteVolumeZeroNum = MetricsLabelCB + MetricsLabelLocal + "RoutingLineZoneRouteVolumeZeroNum"
	ILHVolumeZeroNum                  = MetricsLabelCB + "ILHVolumeZeroNum"
)

// system error上报
const (
	RateLimiterErr = "RateLimiterErr"
)

const (
	ILHRoutingError   = MetricsLabelCB + "ILHRoutingError"
	ILHRoutingSuccess = MetricsLabelCB + "ILHRoutingSuccess"
)

// updateVolume业务上报
const (
	ValidateParamError           = "ValidateParamError"
	DuplicateCheckError          = "DuplicateCheckError"
	SmartRoutingVolumeError      = "SmartRoutingVolumeError"
	AllocateVolumeError          = "AllocateVolumeError"
	SetDuplicateTagError         = "SetDuplicateTagError"
	IncrAllocateVolumeError      = "IncrAllocateVolumeError"
	IncrAllocateVolumeSuccess    = "IncrAllocateVolumeSuccess"
	UpdateActualPointVolumeError = "UpdateActualPointVolumeError"
	LineParcelNotFoundError      = "LineParcelNotFoundError"
	LineParcelFormatError        = "LineParcelFormatError"
	LineParcelGetError           = "LineParcelGetError"
	LineParcelDelError           = "LineParcelDelError"
)

// locs服务异常上报
const (
	FullPathLocationReqParamError    = "FullPathLocationReqParamError"
	FullPathLocationReqParamNilError = "FullPathLocationReqParamNilError"
	FullPathLocationNoCountry        = "FullPathLocationNoCountry"
	FullPathLocationRequestError     = "FullPathLocationRequestError"
	FullPathLocationError            = "FullPathLocationError"
	FullPathLocationSuccess          = "FullPathLocationSuccess"
)

// admin接口监控项
const (
	RouteVolumeFileExceedLimit    = "RouteVolumeFileExceedLimit"
	ZoneVolumeFileExceedLimit     = "ZoneVolumeFileExceedLimit"
	RouteAndZoneParseError        = "RouteAndZoneParseError"
	RouteAndZoneAllZero           = "RouteAndZoneAllZero"
	RouteParseError               = "RouteParseError"
	ZoneParseError                = "ZoneParseError"
	ParcelTypeDimensionParamError = "ParcelTypeDimensionParamError"
)

// 运力监控
const (
	PickupPostcodeIsNull   = "PickupPostcodeIsNull"
	DeliveryPostcodeIsNull = "DeliveryPostcodeIsNull"
)

// 运费调度因子业务打点上报
const (
	ReverseOrder = "ReverseOrder"
)

// routing提前计算运费异常上报
const (
	NonSelectLaneScene         = "NonSelectLaneScene"
	GlobalSwitchClose          = "GlobalSwitchClose"
	NoNeedCalcFeeLine          = "NoNeedCalcFeeLine"
	PreCalcFeeSuccess          = "PreCalcFeeSuccess"
	GetPreCalcFeeIsNil         = "GetPreCalcFeeIsNil"
	GetPreCalcFeeSuccess       = "GetPreCalcFeeSuccess"
	NeedCompensateFeeLineIsNil = "NeedCompensateFeeLineIsNil"
	CompensateFeeSuccess       = "CompensateFeeSuccess"
)

// masking提前计算运费异常上报
const (
	NonAllocateScene                  = "NonAllocateScene"
	MaskingGlobalSwitchClose          = "MaskingGlobalSwitchClose"
	MaskingCalcFeeError               = "MaskingCalcFeeError"
	MaskingPreCalcFeeSuccess          = "MaskingPreCalcFeeSuccess"
	MaskingGetPreCalcFeeIsNil         = "MaskingGetPreCalcFeeIsNil"
	MaskingPreCalcFeeIsNil            = "MaskingPreCalcFeeIsNil"
	MaskingGetPreCalcFeeSuccess       = "MaskingGetPreCalcFeeSuccess"
	MaskingNeedCompensateFee          = "MaskingNeedCompensateFee"
	MaskingFeeFactorRequestFinanceNum = "MaskingFeeFactorRequestFinanceNum"
)

// kafka 异常监控
const (
	KafkaSendPanic = "KafkaSendPanic"
)

const (
	HardCriteriaScanHbaseIndex  = "HardCriteriaScanHbaseIndex"
	HardCriteriaTotalOrder      = "HardCriteriaTotalOrder"
	HardCriteriaFailed          = "HardCriteriaFailed"
	InitDecoderFailed           = "InitDecoderFailed"
	DecodeFailed                = "DecodeFailed"
	HardCriteriaRedis           = "HardCriteriaRedis"
	UpdateHardCriteriaVolume    = "UpdateHardCriteriaVolume"
	HardCriteriaSaveInHbase     = "HardCriteriaSaveInHbase"
	QueryHardCriteriaOrderCount = "QueryHardCriteriaOrderCount"
	CreateHardCriteria          = "CreateHardCriteria"
)

const (
	LocalForecastAdmin               = "LocalForecastAdmin"
	ImportWeightFailed               = "ImportWeightFailed"
	ImportSimulationOrderCountFailed = "ImportSimulationOrderCountFailed"
	ExportOrderAggregationFailed     = "ExportOrderAggregationFailed"
	ForecastLaneCodeNotFound         = "ForecastLaneCodeNotFound"
	ForecastLocationNotFound         = "ForecastLocationNotFound"
	ForecastBlockOrder               = "ForecastBlockOrder"
)

// routing re calc fee monitor
const (
	ReCalcFee             = "ReCalcFee"
	UseRoutingLogFee      = "UseRoutingLogFee"
	NoNeedCompleteFee     = "NoNeedCompleteFee"
	PreCalcSwitchIsClose  = "PreCalcSwitchIsClose"
	NeedCompleteLineIsNil = "NeedCompleteLineIsNil"
	CompleteFeeSuccess    = "CompleteFeeSuccess"
)

// Debug Smart Routing Forecast
const (
	DebugForecastSuccess = "DebugForecastSuccess"
	DebugForecastFail    = "DebugForecastFail"
)

// ILH Routing
const (
	CCRoutingRuleNotReady  = "CCRoutingRuleNotReady"
	CombinationInfoInValid = "CombinationInfoInValid"
)

// post code
const (
	PostCodeMonitor         = "PostCodeMonitor"
	QueryPostCodeListErr    = "QueryPostCodeListErr"
	DeleteSinglePostCodeErr = "DeleteSinglePostCodeErr"
	EditSinglePostCodeErr   = "EditSinglePostCodeErr"
	ImportPostCodeErr       = "DeleteSinglePostCodeErr"
	ExportPostCodeErr       = "ExportPostCodeErr"
	MatchPostCode           = "MatchPostCode"
)

// masking volume dashboard monitor
const (
	BatchGetZoneVolumeError      = "BatchGetZoneVolumeError"
	BatchGetRouteVolumeError     = "BatchGetRouteVolumeError"
	BatchGetShopGroupVolumeError = "BatchGetShopGroupVolumeError"
)

// routing volume dashboard monitor
const (
	MatchLineTypeError          = "MatchLineTypeError"
	ReportDashboardVolumeError  = "ReportDashboardVolumeError"
	GetVolumeManagementError    = "GetVolumeManagementError"
	ExceedMaxDeleteNum          = "ExceedMaxDeleteNum"
	MGetKeyAndValueInconsistent = "MGetKeyAndValueInconsistent"
	RoutingDashboardMGetError   = "RoutingDashboardMGetError"
)

// 同步数据
const (
	SyncData = "Sync Data"
)

// schedule visual monitor
const (
	BatchConsumeCommitError     = "BatchConsumeCommitError"
	SyncAllocateStatResultError = "SyncAllocateStatResultError"
)

// Smart Routing Volume Management
const (
	// Module Name
	VolumeManagementMonitor = "VolumeManagement"
	// Interface Name
	FmUseZoneLimitErr         = "FmUseZoneLimitErr"
	ZoneLimitNotSet           = "ZoneLimitNotSet"
	ProductVolumeRuleCacheErr = "ProductVolumeRuleCacheErr"
	LineToVolumeGroupCacheErr = "LineToVolumeGroupCacheErr"
)

const (
	LayerCacheHotKeyNotInDB = "LayerCacheHotKeyNotInDB"
	LayerCacheErr           = "LayerCacheErr"
	LayerCacheLoaderIsNil   = "LayerCacheLoaderIsNil"
)

// Batch Allocate
const (
	ExecuteBatchAllocate               = "ExecuteBatchAllocate"
	InspectAbnormalBatch               = "InspectAbnormalBatch"
	PushOrderResult                    = "PushOrderResult"
	BatchDegradation                   = "BatchDegradation"
	UnmarshallOrderInfo                = "UnmarshallOrderInfo"
	UnmarshallOrderResult              = "UnmarshallOrderResult"
	MarshallOrderResult                = "MarshallOrderResult"
	FulfillmentEsfIllegal              = "FulfillmentEsfIllegal"
	UpdateBatchOrderResult             = "UpdateBatchOrderResult"
	InvalidPickupEffBudget             = "InvalidPickupEffBudget"
	InvalidProductZoneRouteVolumeValue = "InvalidProductZoneRouteVolumeValue"
	UpdateShopFulfillmentProduct       = "UpdateShopFulfillmentProduct"
	UpdatePickupEfficiencyBudget       = "UpdatePickupEfficiencyBudget"
	UpdateMaskVolume                   = "UpdateMaskVolume"
	UpdateZoneVolume                   = "UpdateZoneVolume"
	UpdateRouteVolume                  = "UpdateRouteVolume"
)

const (
	CBShopGroupLpsApi = "CBShopGroupLpsApi"
)

// Parcel Type Volume
const (
	IncrParcelTypeVolume = "IncrParcelTypeVolume"
	COD                  = "COD"
	Bulky                = "Bulky"
	HighValue            = "HighValue"
	DG                   = "DG"
)

// Lru Cache
const (
	LruCacheGetErr = "LruCacheGetErr"
)

// allocate path上报
const (
	GetFirstAllocateLogError = "GetFirstAllocateLogError"
	FirstAllocateLogIsNil    = "FirstAllocateLogIsNil"
)

// install allocate
const (
	NoNeedInstallAllocate                  = "NoNeedInstallAllocate"
	InstallAllocateTotalNum                = "InstallAllocateTotalNum"
	InstallAllocateError                   = "InstallAllocateError"
	InstallAllocateHeaderError             = "InstallAllocateHeaderError"
	InstallAllocateSuccess                 = "InstallAllocateSuccess"
	InstallAllocateValidParamError         = "InstallAllocateValidParamError"
	InstallAllocateAllocateError           = "InstallAllocateAllocateError"
	SingleInstallAllocateError             = "SingleInstallAllocateError"
	SingleInstallAllocateRetcodeError      = "SingleInstallAllocateRetcodeError"
	SingleInstallAllocateUpdateVolumeError = "SingleInstallAllocateUpdateVolumeError"
	InstallAllocateConvertError            = "InstallAllocateConvertError"
)

// install allocate update volume
const (
	NonWmsUpdateVolumeTotalNum      = "NonWmsUpdateVolumeTotalNum"
	NonWmsUpdateVolumeError         = "NonWmsUpdateVolumeError"
	AsyncNonWmsUpdateVolumeTotalNum = "AsyncNonWmsUpdateVolumeTotalNum"
	AsyncNonWmsUpdateVolumeError    = "AsyncNonWmsUpdateVolumeError"
	WmsUpdateVolumeTotalNum         = "WmsUpdateVolumeTotalNum"
	WmsUpdateVolumeError            = "WmsUpdateVolumeError"
)

// Revamp ILH Routing Interface
const (
	ILHCapacitySettingNotFound    = "ILHCapacitySettingNotFound"
	AvailableLHRuleNotFound       = "AvailableLHRuleNotFound"
	NoAvailableLane               = "NoAvailableLane"
	NoAvailableILH                = "NoAvailableILH"
	NoAvailableLaneAfterILHFilter = "NoAvailableLaneAfterILHFilter"
	NoAvailableCC                 = "NoAvailableCC"
	AllILHStrategiesFailed        = "AllILHStrategiesFailed"

	// ILH容量模式相关
	ILHCapacityModeReservedBSA = "ILHCapacityModeReservedBSA"
	ILHCapacityModeBSA         = "ILHCapacityModeBSA"
	ILHCapacityModeAdhoc       = "ILHCapacityModeAdhoc"
	ILHCapacityModeSingleLane  = "ILHCapacityModeSingleLane"

	// CC策略相关
	CCStrategySingleLineAfterMaxFilter   = "CCStrategySingleLineAfterMaxFilter"
	CCStrategySingleLineAfterMinFilter   = "CCStrategySingleLineAfterMinFilter"
	CCStrategyWeightageForFullLines      = "CCStrategyWeightageForFullLines"
	CCStrategyWeightageForMinWeightLines = "CCStrategyWeightageForMinWeightLines"
	CCStrategyWeightageForMultipleLines  = "CCStrategyWeightageForMultipleLines"
)
