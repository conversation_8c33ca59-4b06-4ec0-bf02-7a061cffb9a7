package monitoring

import "context"

// ReportPostcode 上报postcode监控
func ReportPostcode(ctx context.Context, pickupPostcode string, deliveryPostcode string) {
	// 上报监控postcode信息
	if pickupPostcode == "" {
		ReportError(ctx, CatModuleVolumeMonitor, PickupPostcodeIsNull, "pickup postcode is nil")
	} else {
		ReportSuccess(ctx, CatModuleVolumeMonitor, PickupPostcodeIsNull, "pickup postcode not nil")
	}
	if deliveryPostcode == "" {
		ReportError(ctx, CatModuleVolumeMonitor, DeliveryPostcodeIsNull, "delivery postcode is nil")
	} else {
		ReportError(ctx, CatModuleVolumeMonitor, DeliveryPostcodeIsNull, "delivery postcode not nil")
	}
}
