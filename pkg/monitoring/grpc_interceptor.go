package monitoring

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"google.golang.org/grpc"
)

func UnaryClientInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) (err error) {
		monitorCtx := monitor.AwesomeReportTransactionStart(ctx)
		defer func() {
			// Intercept panic and log cat transaction
			if r := recover(); r != nil {
				monitor.AwesomeReportTransactionEnd(monitorCtx, CatModuleRPC, method, StatusPanic, fmt.Sprintf("%v", r))
			}
			status, msg := getClientTransactionInfo(req, reply, err)
			monitor.AwesomeReportTransactionEnd(monitorCtx, CatModuleRPC, method, status, msg)
			logger.CtxLogInfof(ctx, "grpc invocation target=%s method=%v, status=%v, %v", cc.Target(), method, status, msg)
		}()

		return invoker(monitorCtx, method, req, reply, cc, opts...)
	}
}

func getClientTransactionInfo(req, reply interface{}, err error) (string, string) {
	var (
		status string
		msg    string
	)
	if err != nil {
		status = StatusError
		msg = fmt.Sprintf("err=%v||req=%v||reply=%v", err.Error(), req, reply)
	} else {
		status = StatusSuccess
		msg = fmt.Sprintf("req=%v||reply=%v", req, reply)
	}
	return status, msg
}
