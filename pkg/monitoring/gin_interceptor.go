package monitoring

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
)

func WithTransactionMsg(ctx context.Context) context.Context {
	return monitor.AwesomeReportTransactionStart(ctx)
}

//
//func RequestStartReport(ctx context.Context, req *restful.Request) (context.Context, monitor.ReportTransactionEndFunc) {
//	// 从request headers中获得tracing信息
//	var err error
//	rootMessageId := req.Request.Header.Get(apm.ROOT_MESSAGE_ID)
//	if rootMessageId != "" {
//		ctx, err = monitor.SetRootMsgId(ctx, rootMessageId)
//		if err != nil {
//			Logger.CtxLogErrorf(ctx, "set cat root id|%s", rootMessageId)
//		}
//	}
//	parentMessageId := req.Request.Header.Get(apm.PARENT_MESSAGE_ID)
//	if parentMessageId != "" {
//		ctx, err = monitor.SetParentMsgId(ctx, parentMessageId)
//		if err != nil {
//			Logger.CtxLogErrorf(ctx, "set cat parent id|%s", parentMessageId)
//		}
//	}
//	childMessageId := monitor.CreateMessageId("")
//	monCtx, endFunc := monitor.AwesomeRemoteServerTransactionStart2(ctx, childMessageId)
//	return monCtx, endFunc
//}
//
//func DiagnoseResponseReport(c context.Context, endFunc monitor.ReportTransactionEndFunc, statusCode int, path string, queryParameter string, requestBody interface{}, retCode string, requestId string) {
//	fullPath := path
//	if queryParameter != "" {
//		fullPath = fullPath + "?" + queryParameter
//	}
//
//	reportData := fmt.Sprintf("%s|path=%s", requestId, fullPath)
//	if statusCode != http.StatusOK {
//		httpStatus := fmt.Sprintf("http_%d", statusCode)
//		endFunc(CatModuleURL, path, httpStatus, reportData)
//		return
//	}
//
//	if retCode == constant.StatusSuccess { // success
//		endFunc(CatModuleURL, path, StatusSuccess, reportData)
//	} else {
//		httpStatus := retCode
//		endFunc(CatModuleURL, path, httpStatus, reportData)
//	}
//}
