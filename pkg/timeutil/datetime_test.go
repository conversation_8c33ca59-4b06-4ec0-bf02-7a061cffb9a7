package timeutil

import (
	"context"
	"fmt"
	"testing"
)

func TestAddDays(t *testing.T) {
	str := ""
	interval := 15 * 60
	timestamp, _ := ParseDatetimeStr(str)
	ctx := context.Background()
	result := CompareTimeInterval(ctx, timestamp, int64(interval))
	fmt.Println(result)
}

func TestGetLocalZeroTime(t *testing.T) {
	ctx := context.Background()
	zeroTime := GetLocalZeroTime(ctx)
	fmt.Println(zeroTime)
}
