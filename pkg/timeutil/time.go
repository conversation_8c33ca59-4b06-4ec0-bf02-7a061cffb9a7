package timeutil

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"strings"
	"time"
)

func ParseLocalTime(layout string, value string) (time.Time, error) {
	return time.ParseInLocation(layout, value, GetTimeLocationZone())
}

func RegularTime(tm string) string {
	tm = strings.Trim(tm, " ")
	if len(tm) == 8 || len(tm) == 0 {
		return tm
	}
	dateBlock := strings.Split(tm, ":")
	for i := 0; i < len(dateBlock); i++ {
		if len(dateBlock[i]) == 1 {
			dateBlock[i] = fmt.Sprintf("0%s", dateBlock[i])
		}
	}
	return strings.Join(dateBlock, ":")
}

func GetTimeLocationZone() *time.Location {
	return timeZone[envvar.GetCID()]
}

func GetTimeLocationZoneByCid(cid string) *time.Location {
	return timeZone[strings.ToUpper(cid)]
}

func GetLocalTime(ctx context.Context) time.Time {
	return recorder.Now(ctx).In(GetTimeLocationZone())
}

func GetCurrentRelativeTimeInDayByCid(ctx context.Context, cid string) float64 {
	cur := recorder.Now(ctx).In(timeZone[cid])

	return float64(cur.Hour()) + float64(cur.Minute())/60
}

func HasTime(dt time.Time) bool {
	return dt.Hour() != 0 || dt.Minute() != 0 || dt.Second() != 0 || dt.Nanosecond() != 0
}

func GetCurrentTime(ctx context.Context) time.Time {
	return recorder.Now(ctx).In(GetTimeLocationZone())
}

func GetCurrentTimeByCid(ctx context.Context, cid string) time.Time {
	return recorder.Now(ctx).In(timeZone[cid])
}

// GetNextDayStartTime
// @param: nextDay -> e.g. nextDay is -1, returns yesterday's start time; nextDay is 1 return tomorrow's start time
func GetNextDayStartTime(ctx context.Context, nextDay int) int64 {
	t := recorder.Now(ctx)
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return nextDate.Unix()
}

func GetNextDayStartTimeByCid(ctx context.Context, nextDay int, cid string) int64 {
	t := recorder.Now(ctx).In(timeZone[cid])
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return nextDate.Unix()
}

// GetDays 通过给定的起止时间来获得这段区间内的time.Time列表 -> [start, end]
func GetDays(start, end time.Time) (results []time.Time) {
	for cur := start; !cur.After(end); cur = cur.Add(time.Hour * 24) {
		results = append(results, cur)
	}
	return results
}

func GetTimeByExcelFloatOffset(offset float64) time.Time {
	// 计算时间，Excel 从 1900-01-01 开始， 而 Excel 在处理日期时会将 1900 年 2 月 29 日视为存在，这导致了日期计算上的偏差。具体来说，Excel 认为 1900 年是一个闰年，因此会多出一天。
	excelBase := time.Date(1899, 12, 30, 0, 0, 0, 0, GetTimeLocationZone())

	// 计算Unix时间戳
	days := int(offset)
	seconds := (offset - float64(days)) * 86400 // 86400秒为一天
	return excelBase.AddDate(0, 0, days).Add(time.Duration(seconds) * time.Second)
}
