package timeutil

import (
	"testing"
	"time"
)

func TestParseLocalTime(t *testing.T) {
	time, _ := ParseLocalTime("2006-01-02", "2023-03-04")
	println(time.Unix())
}

func TestExcelTimeToGoTime(t *testing.T) {
	tests := []struct {
		name      string
		excelTime float64
		want      time.Time
	}{
		{
			name:      "Excel epoch",
			excelTime: 0,
			want:      time.Date(1899, time.December, 30, 0, 0, 0, 0, GetTimeLocationZone()),
		},
		{
			name:      "1 day after Excel epoch",
			excelTime: 1,
			want:      time.Date(1899, time.December, 31, 0, 0, 0, 0, GetTimeLocationZone()),
		},
		{
			name:      "Midday on 1 Jan 2021",
			excelTime: 44197.5,
			want:      time.Date(2021, time.January, 1, 12, 0, 0, 0, GetTimeLocationZone()),
		},
		{
			name:      "3:30 PM on 15 May 2023",
			excelTime: 45061.645834,
			want:      time.Date(2023, time.May, 15, 15, 30, 0, 0, GetTimeLocationZone()),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetTimeByExcelFloatOffset(tt.excelTime)
			if !got.Equal(tt.want) {
				t.Errorf("excelTimeToGoTime(%v) = %v, want %v", tt.excelTime, got, tt.want)
			}
		})
	}
}
