package timeutil

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
)

var timeZone map[string]*time.Location

func init() {
	sgTimeZone, _ := time.LoadLocation(constant.SGLocationZone)

	timeZone = make(map[string]*time.Location)
	for cid, zone := range countryToLocationZoneMap {
		tz, err := time.LoadLocation(zone)
		if err != nil {
			tz = sgTimeZone
		}
		timeZone[cid] = tz
	}
}

var countryToLocationZoneMap = map[string]string{
	"ID": constant.IDLocationZone,
	"TH": constant.THLocationZone,
	"VN": constant.VNLocationZone,
	"MY": constant.MYLocationZone,
	"PH": constant.PHLocationZone,
	"SG": constant.SGLocationZone,
	"TW": constant.TWLocationZone,
	"BR": constant.BRLocationZone,
	"MX": constant.MXLocationZone,
	"CO": constant.COLocationZone,
	"CL": constant.CLLocationZone,
	"AR": constant.ARLocationZone,
	"PL": constant.PLLocationZone,
	"ES": constant.ESLocationZone,
	"FR": constant.FRLocationZone,
	"IN": constant.INLocationZone,
}

const (
	OneDay            = "24h"
	DefaultTimeFormat = "2006-01-02 15:04:05"
	DateFormat        = "2006-01-02"
	TimeFormat        = "15:04:05"
	OneDayUnix        = 86400 //unix time for a whole day
	OneSecond         = 1
	MaxDays           = 31
)

const (
	HourSecs           = 60 * 60
	mxLocationZoneName = "America/Mexico_City"
	totalTimeFormat    = "2022062812"
)

func GetCurrentUnixTimeStamp(ctx context.Context) int64 {
	return recorder.Now(ctx).Unix()
}

func GetCurrentUnixMilliTimeStamp(ctx context.Context) int64 {
	return recorder.Now(ctx).UnixNano() / 1e6
}

func GetCurrentUnixTimeStampUnWrapped() int64 {
	return time.Now().Unix() // nolint
}

func LogTime(name string, s time.Time) {
	logger.LogInfof("%s done, duration=%.3fs", name, time.Since(s).Seconds())
}

func PrintDurationTime(name string, s time.Time) {
	fmt.Printf("%s done, duration=%.3fs\n", name, time.Since(s).Seconds())
}

func TransferTimeStampToTime(stamp int64) time.Time {
	return time.Unix(stamp, 0).In(GetTimeLocationZone())
}

func TransferTimeStampToTimeByCid(stamp int64, cid string) time.Time {
	return time.Unix(stamp, 0).In(timeZone[cid])
}

func GetTimeLocation(country string) *time.Location {
	return timeZone[envvar.GetCID()]
}

func GetIntervalDays(startTime time.Time, endTime time.Time) int {
	startDate := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local)
	endDate := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, time.Local)

	d := endDate.Sub(startDate)
	days := d.Hours() / 24
	return int(days)
}

func AddDays(startDateTime time.Time, days int) time.Time {
	oneDayTime, _ := time.ParseDuration(OneDay)
	addDay := oneDayTime * time.Duration(days)
	addDateTime := startDateTime.Add(addDay)

	return addDateTime
}

func IsToday(ctx context.Context, timeStamp int64) bool {
	if timeStamp == 0 {
		return false
	}
	checkDate := TransferTimeStampToTime(timeStamp).Format(DateFormat)
	todayDate := GetCurrentTime(ctx).Format(DateFormat)
	return checkDate == todayDate
}

func IsTodayByCid(ctx context.Context, timeStamp int64, cid string) bool {
	if timeStamp == 0 {
		return false
	}
	checkDate := TransferTimeStampToTimeByCid(timeStamp, cid).Format(DateFormat)
	todayDate := GetCurrentTime(ctx).Format(DateFormat)
	return checkDate == todayDate
}

func IsWeekend(timeStamp int) bool {
	if timeStamp == 0 {
		return false
	}
	w := TransferTimeStampToTime(int64(timeStamp)).Weekday()
	if w == 0 || w == 6 {
		return true
	}
	return false
}

func GetTimeByCid(timestamp int64, cid string) time.Time {
	return time.Unix(timestamp, 0).In(timeZone[cid])
}

// GetTimeByTimezoneOffset get time by timezone offset(eg: 8)
func GetTimeByTimezoneOffset(timestamp int64, timezoneOffset int) time.Time {
	return time.Unix(timestamp, 0).In(time.FixedZone("TWS", timezoneOffset*HourSecs))
}

func TodayDate(ctx context.Context) time.Time {
	now := GetCurrentTime(ctx)
	return GetDate(now)
}

func GetDate(dt time.Time) time.Time {
	return time.Date(dt.Year(), dt.Month(), dt.Day(), 0, 0, 0, 0, GetTimeLocationZone())
}

func FormatDate(dt time.Time) string {
	return dt.Format(DateFormat)
}

func FormatDateTime(dt time.Time) string {
	return dt.Format(DefaultTimeFormat)
}

func FormatLocalTimestamp(timestamp int64) string {
	return FormatDateTime(GetLocalTimeByTimestamp(timestamp))
}

func FormatLocalDatestamp(timestamp int64) string {
	return FormatDate(GetLocalTimeByTimestamp(timestamp))
}

func FormatDateTimeByFormat(dt time.Time, format string) string {
	return dt.Format(format)
}

func ReplaceTime(dt time.Time, hour, min, sec int) time.Time {
	ret := time.Date(dt.Year(), dt.Month(), dt.Day(), hour, min, sec, 0, dt.Location())
	return ret
}

func ParseDateStr(dateStr string) (time.Time, error) {
	return time.ParseInLocation(DateFormat, dateStr, GetTimeLocation(envvar.GetCID()))
}

func ParseDatetimeStr(datetimeStr string) (time.Time, error) {
	return time.ParseInLocation(DefaultTimeFormat, datetimeStr, GetTimeLocation(envvar.GetCID()))
}

func ParseDatetimeStrByFormat(datetimeStr string, dateTimeFormat string) (time.Time, error) {
	return time.ParseInLocation(dateTimeFormat, datetimeStr, GetTimeLocation(envvar.GetCID()))
}

func WhichDayForWeek(dt time.Time) int {
	w := int(dt.Weekday())
	if w == 0 {
		w = 7
	}
	return w
}

func RegularDate(date string) string {
	date = strings.Trim(date, " ")
	if len(date) == 10 {
		return date
	}
	dateBlock := strings.Split(date, "-")
	for i := 1; i < len(dateBlock); i++ {
		if len(dateBlock[i]) == 1 {
			dateBlock[i] = fmt.Sprintf("0%s", dateBlock[i])
		}
	}
	return strings.Join(dateBlock, "-")
}

func GetLocalTimeByTimestamp(timestamp int64) time.Time {
	return time.Unix(timestamp, 0).In(GetTimeLocationZone())
}

// GetRelativeTimeInDayByOffset return the relative time in day by hour offset
func GetRelativeTimeInDayByOffset(timestamp int64, hourOffset int) float64 {
	t := time.Unix(timestamp, 0).In(time.FixedZone("", hourOffset*HourSecs))

	return float64(t.Hour()) + float64(t.Minute())/60
}

// GetDayListByTime :Get day list from start-date to end-date
// e.g.: input (1, 7) -> output (1,2,3,4,5,6,7)
func GetDayListByTime(startDate, endDate time.Time) []time.Time {
	var results []time.Time
	for cur := startDate; !cur.After(endDate); cur = cur.Add(time.Hour * 24) {
		results = append(results, cur)
	}
	return results
}

func GetDayListByTimeStamp(startTimeStamp, endTimeStamp int64) []time.Time {
	startDate := TransferTimeStampToTime(startTimeStamp)
	endDate := TransferTimeStampToTime(endTimeStamp)
	return GetDayListByTime(startDate, endDate)
}

func GetNextDayStartTimeByTimeStamp(timeStamp int64, nextDay int) int64 {
	t := TransferTimeStampToTime(timeStamp)
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return nextDate.Unix()
}

// 获取当前时区的零点
func GetLocalZeroTime(ctx context.Context) int64 {
	currentTime := recorder.Now(ctx)
	zeroTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return zeroTime.Unix()
}

// SplitTotalTimeToStringDateAndHour
// @param: totalTime -> type:uint64, format:2022062812 (yyyyMMddHH)
// @return: date time -> type:string, format:2022-06-28 (yyyy-MM-dd)
// @return: hour -> type:string, format:06
func SplitDateToDayAndHour(ctx context.Context, totalTime string) (string, string) {
	if len(totalTime) != len(totalTimeFormat) {
		logger.CtxLogErrorf(ctx, "SplitTotalTimeToStringDateAndHour| total time:%v, length is illegal", totalTime)
		return "", ""
	}
	var dayTimeStrings []string
	var hourTimeStrings []string
	for i := 0; i < len(totalTime); i++ {
		if i < 8 {
			dayTimeStrings = append(dayTimeStrings, string(totalTime[i]))
		} else {
			hourTimeStrings = append(hourTimeStrings, string(totalTime[i]))
		}
	}
	dayTime := ""
	for i := 0; i < 4; i++ {
		dayTime = dayTime + dayTimeStrings[i]
	}
	dayTime = dayTime + "-"
	for i := 4; i < 6; i++ {
		dayTime = dayTime + dayTimeStrings[i]
	}
	dayTime = dayTime + "-"
	for i := 6; i < len(dayTimeStrings); i++ {
		dayTime = dayTime + dayTimeStrings[i]
	}
	hourTime := ""
	for i := 0; i < len(hourTimeStrings); i++ {
		hourTime = hourTime + hourTimeStrings[i]
	}
	return dayTime, hourTime
}

func ConvertTimeStampToTimeByCountry(timestamp int64, country string) time.Time {
	return time.Unix(timestamp, 0).In(timeZone[country])
}

func GetTodayDateString(ctx context.Context) string {
	return FormatTimeToVolumeDateString(GetCurrentTime(ctx))
}

func FormatTimeToVolumeDateString(t time.Time) string {
	return t.Format(constant.TimeLayout)
}

func GetTimestampString(timestamp int64) string {
	return ConvertTimeStampToTimeByCountry(timestamp, envvar.GetCID()).Format(constant.TimeLayout)
}

// GetNextDayStartTimeOfUTC :returns Unix() of UTC time
func GetNextDayStartTimeOfUTC(ctx context.Context, nextDay int) int64 {
	t := recorder.Now(ctx)
	nextTime := t.AddDate(0, 0, nextDay)
	nextDate := time.Date(nextTime.Year(), nextTime.Month(), nextTime.Day(), 0, 0, 0, 0, time.UTC)
	return nextDate.Unix()
}

func GetYesterdayStartTime(ctx context.Context) int64 {
	t := recorder.Now(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return timeYesterday.Unix()
}

func GetYesterdayEndTime(ctx context.Context) int64 {
	t := recorder.Now(ctx)
	yesTime := t.AddDate(0, 0, -1)
	timeYesterday := time.Date(yesTime.Year(), yesTime.Month(), yesTime.Day(), 23, 59, 59, 59, GetTimeLocationZone())
	return timeYesterday.Unix()
}

func GetYesterday(ctx context.Context) time.Time {
	nowTime := GetLocalTime(ctx)
	yesterdayTime := nowTime.AddDate(0, 0, -1)
	return yesterdayTime
}

func ConvertTimeStampToTime(timestamp int64) time.Time {
	return ConvertTimeStampToTimeByCountry(timestamp, envvar.GetCID())
}

func ConvertTimeStampToLocalTime(timestamp int64) time.Time {
	return time.Unix(timestamp, 0).In(GetTimeLocationZone())
}

func GetStartTimeByTime(t time.Time) int64 {
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return today.Unix()
}

func GetStartTimeByString(value string) int64 {
	t, _ := ParseLocalTime("2006-01-02", value)
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return today.Unix()
}

func GetStartTime(t time.Time) int64 {
	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetTimeLocationZone())
	return today.Unix()
}

// timestamp是否在时间间隔外
func CompareTimeInterval(ctx context.Context, timestamp time.Time, interval int64) bool {
	currentTime := GetCurrentTime(ctx)
	return currentTime.Unix()+interval <= timestamp.Unix()
}

func GetTimestampDateString(timestamp int64, format string) string {
	return GetLocalTimeByTimestamp(timestamp).Format(format)
}

func GetDayByTimeStamp(timeStamp int64) int {
	t := TransferTimeStampToTime(timeStamp)
	return t.Day()
}

func GetMinuteByTimeStamp(timeStamp int64) int {
	t := TransferTimeStampToTime(timeStamp)
	return t.Minute()
}

// GetCutoffTimePoint 根据CutoffTime和时区偏移计算对应的时间点
// cutoffTime格式为小时.分钟，例如14.5表示14:30
// timezoneOffset为时区偏移量，单位为小时
func GetCutoffTimePoint(ctx context.Context, cutoffTime float64, timezoneOffset int) int64 {
	// 计算CutoffTime对应的小时和分钟
	cutoffHours := int(cutoffTime)
	cutoffMinutes := int((cutoffTime - float64(cutoffHours)) * 60)

	// 创建本地时区
	loc := time.FixedZone("", timezoneOffset*HourSecs)

	// 获取当前时间
	currentTime := GetCurrentUnixTimeStamp(ctx)
	currentTimeObj := time.Unix(currentTime, 0).In(loc)

	// 创建基于CutoffTime的时间点
	timePoint := time.Date(
		currentTimeObj.Year(), currentTimeObj.Month(), currentTimeObj.Day(),
		cutoffHours, cutoffMinutes, 0, 0, loc)

	return timePoint.Unix()
}

// GetTimezoneByOffset 根据时区偏移量创建时区对象
// timezoneOffset为时区偏移量，单位为小时
func GetTimezoneByOffset(timezoneOffset int) *time.Location {
	return time.FixedZone("", timezoneOffset*HourSecs)
}

// ParseTime 解析时间字符串，格式为 "15:04:05"
func ParseTime(timeStr string) (time.Time, error) {
	return time.Parse(TimeFormat, timeStr)
}

// ParseDate 解析日期字符串，格式为 "2006-01-02"
func ParseDate(dateStr string) (time.Time, error) {
	return time.Parse(DateFormat, dateStr)
}

// ParseDateTime 解析日期时间字符串，格式为 "2006-01-02 15:04:05"
func ParseDateTime(dateTimeStr string) (time.Time, error) {
	return time.Parse(DefaultTimeFormat, dateTimeStr)
}

// ParseDateTimeInLoc 解析带时区的日期时间字符串，格式为 "2006-01-02 15:04:05"
func ParseDateTimeInLoc(dateTimeStr string, loc *time.Location) (time.Time, error) {
	return time.ParseInLocation(DefaultTimeFormat, dateTimeStr, loc)
}
