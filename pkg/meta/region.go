package meta

type Region string

const (
	BR Region = "BR"
	ID Region = "ID"
	MY Region = "MY"
	PH Region = "PH"
	SG Region = "SG"
	TH Region = "TH"
	TW Region = "TW"
	VN Region = "VN"
	MX Region = "MX"
	CO Region = "CO"
	CL Region = "CL"
	IR Region = "IR"
	HK Region = "HK"
	MM Region = "MM"
	AR Region = "AR"
	PL Region = "PL"
	ES Region = "ES"
	FR Region = "FR"
	IN Region = "IN"
)

var (
	shopeeDomainSuffix = map[Region]string{
		SG: "sg",
		VN: "vn",
		PH: "ph",
		TW: "tw",
		ID: "co.id",
		TH: "co.th",
		MY: "com.my",
		BR: "com.br",
		MX: "com.mx",
		CO: "com.co",
		CL: "cl",
		IR: "co.ir",
		HK: "hk",
		MM: "com.mm",
		AR: "com.ar",
		PL: "pl",
		ES: "es",
		FR: "fr",
		IN: "in",
	}

	enableBatchAllocationRegions = []Region{ID, PH, MY, VN, TH, SG}
)

func GetSupportedRegion() []Region {
	return []Region{BR, ID, MY, PH, SG, TH, TW, VN, MX, AR, PL, FR, ES, IN, CO, CL}
}

func GetDomainFromRegion(region Region) string {
	return shopeeDomainSuffix[region]
}

func IsEnableBatchAllocationRegion(region string) bool {
	for _, r := range enableBatchAllocationRegions {
		if r == Region(region) {
			return true
		}
	}

	return false
}
