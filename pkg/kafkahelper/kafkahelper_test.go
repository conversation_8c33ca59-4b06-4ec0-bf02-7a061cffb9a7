package kafkahelper

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"testing"
)

func TestDeliveryMessage(t *testing.T) {
	if err := chassis.Init(chassis.WithChassisConfigPrefix("task_server")); err != nil {
		panic(err)
	}

	producer := saturn.NewProducer("smartrouting.ssc.test.shopee.id")

	resp, err := producer.SendMessageWithContext(context.TODO(), "batch_allocate_hold_orders",
		&saturn.SaturnPubMessage{Header: map[string]string{
			"shopee-baggage": "PFB=pfb-dms-dev-batch-allocate",
		}})
	if err != nil {
		panic(err)
	}

	println(fmt.Sprintf("分区：%d, offset: %d", resp.Partition, resp.Offset))
}
