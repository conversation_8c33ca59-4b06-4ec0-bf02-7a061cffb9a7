package kafkahelper

// kafka client

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"
	"time"
)

const (
	defaultWriteTimeout = 3 * time.Second
)

var (
	producers = make(map[string]saturn.MsgJobPublisher)
)

func InitSaturnProducer() error {
	conf := configutil.GetSaturnNamespaceConf(context.Background())
	if err := initProducerClient(conf.SmrNamespace); err != nil {
		return err
	}
	if err := initProducerClient(conf.SmrBANamespace); err != nil {
		return err
	}

	return nil
}

func initProducerClient(namespace string) *srerr.Error {
	if namespace == "" {
		return srerr.New(srerr.KafkaInitError, nil, "namespace can not be empty")
	}
	producer := saturn.NewProducer(namespace, saturn.WithConfigPrefix(configutil.SaturnKafkaConfigKey))
	producers[namespace] = producer
	return nil
}

func getContextTimeout(ctx context.Context) (context.Context, context.CancelFunc) {
	return context.WithTimeout(ctx, defaultWriteTimeout)
}

func DeliveryMessage(ctx context.Context, namespace, jobName string, value []byte, headers map[string]string, monitorKey string) *srerr.Error {
	// 捕获发送kafka的panic异常
	defer func() {
		if err := recover(); err != nil {
			logger.CtxLogErrorf(ctx, "[Recovery] kafka delivery message panic, err=%v", err)
			monitoring.ReportError(ctx, monitoring.CatModuleKafkaSend, monitoring.KafkaSendPanic, fmt.Sprintf("[Recovery] kafka delivery message panic, err=%v", err))
		}
	}()
	if envvar.IsLivetest() { // live test不发送数据
		logger.CtxLogErrorf(ctx, "livetest don't send kafka, just return nil")
		return nil
	}
	monitorCtx, endFunc := monitor.AwesomeReportTransactionStart2(ctx)
	publisher, ok := producers[namespace]
	if !ok {
		errMsg := fmt.Sprintf("Get producer by key is nil and key is %s", namespace)
		logger.CtxLogErrorf(monitorCtx, errMsg)
		endFunc(monitoring.CatModuleKafkaSend, monitorKey, constant.StatusError, errMsg)
		return srerr.New(srerr.SendMessage, namespace, errMsg)
	}

	msg := &saturn.SaturnPubMessage{Header: headers, Body: value}
	ctxTimeout, _ := getContextTimeout(monitorCtx)
	resp, respErr := publisher.SendMessageWithContext(ctxTimeout, jobName, msg)
	if respErr != nil {
		errMsg := fmt.Sprintf("send message(namespace:%s, value:%s, header.key:%v) error:%+v", namespace, string(value), headers, respErr)
		logger.CtxLogErrorf(monitorCtx, errMsg)
		endFunc(monitoring.CatModuleKafkaSend, monitorKey, constant.StatusError, errMsg)
		return srerr.With(srerr.KafkaServerError, nil, respErr)
	}
	logger.CtxLogInfof(monitorCtx, "topic is %s offset is %s", resp.Topic, resp.Offset)
	if monitorKey != "" && configutil.GetKafkaMonitorSwitch(ctx).Switch {
		endFunc(monitoring.CatModuleKafkaSend, monitorKey, constant.StatusSuccess, "")
	}

	return nil
}
