package objutil

import (
	"math"
	"testing"
)

func TestSafeAverageFloat64(t *testing.T) {
	// 测试用例
	testCases := []struct {
		name     string
		numbers  []float64
		expected float64
	}{
		{
			name:     "空数组",
			numbers:  []float64{},
			expected: 0,
		},
		{
			name:     "单个元素",
			numbers:  []float64{1.23},
			expected: 1.23,
		},
		{
			name:     "多个元素",
			numbers:  []float64{1.0, 2.0, 3.0, 4.0, 5.0},
			expected: 3.0,
		},
		{
			name:     "大数",
			numbers:  []float64{1e10, 1e10, 1e10},
			expected: 1e10,
		},
		{
			name:     "极端情况",
			numbers:  []float64{math.MaxFloat64, math.MaxFloat64 / 2},
			expected: math.Inf(1), // 预期结果是 Inf，因为会发生溢出
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := SafeAverageFloat64(tc.numbers)
			if actual != tc.expected {
				t.<PERSON><PERSON><PERSON>("期望值: %v, 实际值: %v", tc.expected, actual)
			}
		})
	}
}

func TestRoundFloat64(t *testing.T) {
	testCases := []struct {
		name     string
		input    float64
		places   int
		expected float64
	}{
		{"Round to 0 decimal places", 3.1415926, 0, 3.0},
		{"Round to 2 decimal places", 3.1415926, 2, 3.14},
		{"Round to 4 decimal places", 3.1415926, 4, 3.1416},
		{"Round to negative decimal places", 1234.5678, -2, 1200.0},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := RoundFloat64(tc.input, tc.places)
			if result != tc.expected {
				t.Errorf("RoundFloat64(%f, %d) = %f, expected %f", tc.input, tc.places, result, tc.expected)
			}
		})
	}
}
