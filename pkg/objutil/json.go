package objutil

import (
	"context"
	"github.com/bytedance/sonic"
	jsoniter "github.com/json-iterator/go"
)

func JsonBytes(a interface{}) []byte {
	data, err := jsoniter.Marshal(a)
	if err != nil {
		return nil
	}
	return data
}

func UnmarshalBytes(a interface{}, b []byte) error {
	if a == nil {
		return nil
	}
	return jsoniter.Unmarshal(b, &a)
}

func JsonString(a interface{}) string {
	data, err := jsoniter.MarshalToString(a)
	if err != nil {
		return ""
	}
	return data
}

// LogJsonBytes 序列化为Json，用的是性能较高Sonic库，非日志打印的场景请确认清楚再用
func LogJsonBytes(ctx context.Context, a interface{}) []byte {
	data, err := sonic.Marshal(a)
	if err != nil {
		return nil
	}
	return data
}
