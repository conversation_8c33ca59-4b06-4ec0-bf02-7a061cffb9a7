package objutil

import "strings"

func CopyConditionMap(condition map[string]interface{}) map[string]interface{} {
	copiedCondition := map[string]interface{}{}

	for key, val := range condition {
		copiedCondition[key] = val
	}

	return copiedCondition
}

// WhereBuild build sql where
func WhereBuild(where map[string]interface{}) (whereSQL string, val []interface{}) {
	cond := make([]string, 0, len(where))
	for k, v := range where {
		cond = append(cond, k)
		val = append(val, v)
	}
	whereSQL = strings.Join(cond, " AND ")
	return
}
