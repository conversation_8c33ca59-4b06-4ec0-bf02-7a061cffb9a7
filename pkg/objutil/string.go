package objutil

import (
	"sort"
	"strings"
	"unsafe"
)

func Bytes2Str(b []byte) string {
	if len(b) == 0 {
		return ""
	}
	return *(*string)(unsafe.Pointer(&b))
}

func Merge(args ...string) string {
	if len(args) == 0 {
		return ""
	}
	var b strings.Builder
	for _, arg := range args {
		b.WriteString(arg)
	}
	return b.String()
}

func Join(joint string, args ...string) string {
	if len(args) == 0 {
		return ""
	}
	var s []string
	s = append(s, args...)
	return strings.Join(s, joint)
}

func IsPureDigit(s string) bool {
	if len(s) == 0 {
		return false
	}
	for i := 0; i < len(s); i++ {
		if s[i]-'0' > 9 {
			return false
		}
	}
	return true
}

// remove duplicated strings
func RemoveDuplicatedStrings(stringSlice []string) []string {
	var result []string
	exist := map[string]struct{}{}
	for _, s := range stringSlice {
		if _, ok := exist[s]; !ok {
			result = append(result, s)
		}
		exist[s] = struct{}{}
	}
	return result
}

func SortString(s string, p string) string {
	if !strings.Contains(s, p) {
		return s
	}
	subStrings := strings.Split(s, p)
	sort.Slice(subStrings, func(i, j int) bool {
		return subStrings[i] < subStrings[j]
	})
	return strings.Join(subStrings, p)
}
