package objutil

import (
	"github.com/pkg/errors"
	"net"
)

const (
	retryTime = 3
)

var localIP string

func init() {
	for i := 0; i < retryTime; i++ {
		if err := fetchLocalIP(); err == nil {
			return
		}
	}
}

func fetchLocalIP() error {
	addrList, err := net.InterfaceAddrs()
	if err != nil {
		return err
	}
	for _, addr := range addrList {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				localIP = ipNet.IP.String()
				return nil
			}
		}
	}
	return errors.Errorf("init get IP error, IP Address: %v\n", addrList)
}

func GetLocalIP() string {
	if localIP == "" {
		_ = fetchLocalIP()
	}
	return localIP
}
