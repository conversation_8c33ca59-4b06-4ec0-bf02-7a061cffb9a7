package objutil

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"math"
	"strconv"
)

func Max(a, b int32) int32 {
	if a > b {
		return a
	} else {
		return b
	}
}

func MaxForInt64(a, b int64) int64 {
	if a > b {
		return a
	} else {
		return b
	}
}

// IsTrue only converts 0 and 1 to bool
func IsTrue(i int) bool {
	return i == 1
}

func Int2Bool(intVal int) bool {
	return intVal > 0
}

func Bool2Int(boolVal bool) int {
	if boolVal {
		return 1
	}
	return 0
}

// SafeAverageFloat 安全地计算大量Float64数值的平均值
func SafeAverageFloat64(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}

	// 使用 Kahan 求和算法来减少累加过程中的精度损失
	var sum, c float64
	for _, x := range numbers {
		y := x - c
		t := sum + y
		c = (t - sum) - y
		sum = t
	}

	return sum / float64(len(numbers))
}

func RoundFloat64(value float64, places int) float64 {
	var round float64
	pow := math.Pow(10, float64(places))
	round = round + value*pow
	round = math.Round(round)
	round = round / pow
	return round
}

func StringToInt(ctx context.Context, s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		logger.CtxLogErrorf(ctx, "StringToInt failed, s = %s, err = %v", s, err)
		return 0
	}

	return i
}

// RoundDivideInt 计算A/B的四舍五入结果
func RoundDivideInt(A, B int) int {
	if B == 0 {
		return math.MaxInt
	}

	result := float64(A) / float64(B)
	return int(math.Round(result))
}
