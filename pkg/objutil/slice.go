package objutil

import (
	jsoniter "github.com/json-iterator/go"
	"sort"
	"strconv"
)

type Int64Slice []int64

func (p Int64Slice) Len() int           { return len(p) }
func (p Int64Slice) Less(i, j int) bool { return p[i] < p[j] }
func (p Int64Slice) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }

func SortInt64(list []int64) {
	sort.Sort(Int64Slice(list))
}

func ContainsInt(list []int, target int) bool {
	if len(list) == 0 {
		return false
	}
	for _, item := range list {
		if item == target {
			return true
		}
	}
	return false
}

func ContainsUint64(list []uint64, target uint64) bool {
	if len(list) == 0 {
		return false
	}
	for _, item := range list {
		if item == target {
			return true
		}
	}
	return false
}

func SetStringToSlice(s string) []string {
	array := []string{}
	err := jsoniter.Unmarshal([]byte(s), &array)
	if err != nil {
		return array
	}
	return array
}

func SetStringToSliceKeepNil(s string) []string {
	var array []string
	err := jsoniter.Unmarshal([]byte(s), &array)
	if err != nil {
		return array
	}
	return array
}

func SetSliceToString(array []string) string {
	if len(array) == 0 {
		return ""
	}
	bytes, err := jsoniter.MarshalToString(array)
	if err != nil {
		return ""
	}
	s := bytes
	return s
}

func SetInt64SliceToString(array []int64) string {
	if len(array) == 0 {
		return ""
	}
	bytes, err := jsoniter.MarshalToString(array)
	if err != nil {
		return ""
	}
	s := bytes
	return s
}

func SetIntSliceToString(array []int) string {
	if len(array) == 0 {
		return ""
	}
	bytes, err := jsoniter.MarshalToString(array)
	if err != nil {
		return ""
	}
	s := bytes
	return s
}

func ContainStr(list []string, target string) bool {
	if len(list) == 0 {
		return false
	}
	for _, data := range list {
		if data == target {
			return true
		}
	}
	return false
}

func HaveIntersection(arr1, arr2 []string) bool {
	checkExist := make(map[string]struct{})
	for _, item := range arr1 {
		checkExist[item] = struct{}{}
	}
	for _, item := range arr2 {
		if _, exist := checkExist[item]; exist {
			return true
		}
	}

	return false
}

func DistinctSlice4S(ss []string) []string {
	if len(ss) == 0 {
		return ss
	}
	filter := make(map[string]bool)

	var result []string
	for _, s := range ss {
		if filter[s] {
			continue
		}

		filter[s] = true
		result = append(result, s)
	}

	return result
}

func DistinctSlice4I(ss []int) []int {
	if len(ss) == 0 {
		return ss
	}
	filter := make(map[int]bool)

	var result []int
	for _, s := range ss {
		if filter[s] {
			continue
		}

		filter[s] = true
		result = append(result, s)
	}

	return result
}

func ContainsString(array []string, val string) bool {
	for _, elem := range array {
		if elem == val {
			return true
		}
	}
	return false
}

func RemoveDuplicate(arr []int) []int {
	uniq := make([]int, 0)
	checkMap := make(map[int]struct{})
	for _, item := range arr {
		_, exist := checkMap[item]
		if !exist {
			uniq = append(uniq, item)
			checkMap[item] = struct{}{}
			continue
		}
	}

	return uniq
}

func RemoveDuplicateInt64(arr []int64) []int64 {
	uniq := make([]int64, 0)
	checkMap := make(map[int64]struct{})
	for _, item := range arr {
		_, exist := checkMap[item]
		if !exist {
			uniq = append(uniq, item)
			checkMap[item] = struct{}{}
			continue
		}
	}
	return uniq
}

func RemoveDuplicateUint64(arr []uint64) []uint64 {
	uniq := make([]uint64, 0)
	checkMap := make(map[uint64]struct{})
	for _, item := range arr {
		_, exist := checkMap[item]
		if !exist {
			uniq = append(uniq, item)
			checkMap[item] = struct{}{}
			continue
		}
	}
	return uniq
}

func RemoveDuplicateString(arr []string) []string {
	uniq := make([]string, 0)
	checkMap := make(map[string]struct{})
	for _, item := range arr {
		_, exist := checkMap[item]
		if !exist {
			uniq = append(uniq, item)
			checkMap[item] = struct{}{}
			continue
		}
	}

	return uniq
}

func RemoveEmptyString(arr []string) []string {
	result := make([]string, 0, len(arr))
	for _, item := range arr {
		if item != "" {
			result = append(result, item)
		}
	}

	return result
}

func CopySliceUint64(arr []uint64) []uint64 {
	if arr == nil {
		return arr
	}
	res := make([]uint64, len(arr))
	for i, v := range arr {
		res[i] = v
	}
	return res
}

func GetMinUint64(arr []uint64) uint64 {
	if len(arr) == 0 {
		return 0
	}
	min := arr[0]
	for _, item := range arr {
		if item < min {
			min = item
		}
	}

	return min
}

func GetMaxUint64(arr []uint64) uint64 {
	if len(arr) == 0 {
		return 0
	}
	max := arr[0]
	for _, item := range arr {
		if item > max {
			max = item
		}
	}

	return max
}

func ContainInt64(list []int64, target int64) bool {
	if len(list) == 0 {
		return false
	}
	for _, data := range list {
		if data == target {
			return true
		}
	}
	return false
}

func ContainInt32(list []int32, target int32) bool {
	if len(list) == 0 {
		return false
	}
	for _, data := range list {
		if data == target {
			return true
		}
	}
	return false
}

func ContainInt(list []int, target int) bool {
	if len(list) == 0 {
		return false
	}
	for _, data := range list {
		if data == target {
			return true
		}
	}
	return false
}

func RemoveInt64Item(origin []int64, removeItem int64) []int64 {
	if len(origin) == 0 {
		return origin
	}
	target := origin[:0]
	for _, item := range origin {
		if item != removeItem {
			target = append(target, item)
		}
	}
	return target
}

func RemoveIntItem(origin []int, removeItem int) []int {
	if len(origin) == 0 {
		return origin
	}
	target := origin[:0]
	for _, item := range origin {
		if item != removeItem {
			target = append(target, item)
		}
	}
	return target
}

func IntToInt64Slice(slice []int) []int64 {
	var newSlice []int64
	for _, value := range slice {
		newSlice = append(newSlice, int64(value))
	}
	return newSlice
}

func Int64DifferenceSet(first []int64, second []int64) []int64 {
	if len(first) == 0 {
		return second
	}
	if len(second) == 0 {
		return first
	}
	result := []int64{}
	for _, num := range first {
		if !ContainInt64(second, num) {
			result = append(result, num)
		}
	}

	return result
}

func IntToUint32Slice(slice []int) []uint32 {
	newSlice := make([]uint32, 0, len(slice))
	for _, value := range slice {
		newSlice = append(newSlice, uint32(value))
	}
	return newSlice
}

func Uint64ToInt64(target []uint64) []int64 {
	result := make([]int64, 0, len(target))
	for _, i := range target {
		result = append(result, int64(i))
	}

	return result
}

func Uint32ToInt64(target []uint32) []int64 {
	result := make([]int64, len(target))
	for _, i := range target {
		result = append(result, int64(i))
	}

	return result
}

func UintToInt64(target []uint) []int64 {
	result := make([]int64, len(target))
	for _, i := range target {
		result = append(result, int64(i))
	}

	return result
}

func UintToInt(target []uint) []int {
	result := make([]int, len(target))
	for _, i := range target {
		result = append(result, int(i))
	}

	return result
}

func Int64ToUint64(target []int64) []uint64 {
	result := make([]uint64, 0, len(target))
	for _, i := range target {
		result = append(result, uint64(i))
	}

	return result
}

func IntsToStrings(origin []int) []string {
	result := make([]string, len(origin))
	for i := 0; i < len(origin); i++ {
		result[i] = strconv.Itoa(origin[i])
	}
	return result
}

func Int64sToStrings(origin []int64) []string {
	result := make([]string, len(origin))
	for i := 0; i < len(origin); i++ {
		result[i] = strconv.FormatInt(origin[i], 10)
	}
	return result
}

// AreStringSlicesEqual 检查两个字符串数组是否包含相同的元素（忽略顺序，假设元素唯一）
func AreStringSlicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// 将 a 中的元素放入一个 set 中以便快速查找
	setA := make(map[string]struct{}, len(a))
	for _, item := range a {
		setA[item] = struct{}{}
	}

	// 检查 b 中的每个元素是否存在于 setA 中
	for _, item := range b {
		if _, ok := setA[item]; !ok {
			// 如果 b 中的某个元素不在 a 中，则两个数组不相等
			return false
		}
	}

	// 如果 b 中的所有元素都在 a 中，并且长度相等，则它们包含相同的元素
	return true
}
