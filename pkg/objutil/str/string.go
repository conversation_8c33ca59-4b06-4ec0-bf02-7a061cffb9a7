package str

import (
	"bytes"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	jsoniter "github.com/json-iterator/go"
	"strconv"
	"strings"
	"unicode"
)

// 打印json格式
func JsonString(a interface{}) string {
	data, err := jsoniter.MarshalToString(a)
	if err != nil {
		return ""
	}
	return data
}

// 驼峰转下划线格式
func UnderscoreName(name string) string {
	var builder strings.Builder
	for i, r := range name {
		if unicode.IsUpper(r) {
			if i != 0 {
				builder.WriteRune('_')
			}
			builder.WriteRune(unicode.ToLower(r))
		} else {
			builder.WriteRune(r)
		}
	}
	return builder.String()
}

// 类型字符量直连
func Merge(a ...interface{}) string {
	if len(a) == 0 {
		return ""
	}
	var builder strings.Builder
	for _, data := range a {
		builder.WriteString(ToStr(data))
	}
	return builder.String()
}

// 类型字符串连接
func Join(sep string, a ...interface{}) string {
	if len(a) == 0 {
		return ""
	}
	var strArr []string
	for _, data := range a {
		strArr = append(strArr, ToStr(data))
	}
	return strings.Join(strArr, sep)
}

func ToStr(data interface{}) string {
	switch cont := data.(type) {
	case float64:
		return strconv.FormatFloat(cont, 'f', 2, 64)
	case float32:
		return strconv.FormatFloat(float64(cont), 'f', 2, 32)
	case int64:
		return strconv.FormatInt(cont, 10)
	case int32:
		return strconv.FormatInt(int64(cont), 10)
	case int:
		return strconv.Itoa(cont)
	case uint64:
		return strconv.FormatUint(cont, 10)
	case uint32:
		return strconv.FormatUint(uint64(cont), 10)
	case string:
		return cont
	case bool:
		return strconv.FormatBool(cont)
	default:
		return fmt.Sprintf("%v", cont)
	}
}

func SplitStrByComma(s string) []string {
	var ret = []string{}
	temp := strings.Split(s, ",")
	for _, t := range temp {
		if tt := strings.TrimSpace(t); len(tt) > 0 {
			ret = append(ret, tt)
		}
	}
	return ret
}

func SplitStrByCommaToIntSlice(s string) []int {
	var ret []int = nil
	temp := strings.Split(s, ",")
	for _, t := range temp {
		if tt := strings.TrimSpace(t); len(tt) > 0 {
			n, err := strconv.Atoi(tt)
			if err != nil {
				logger.LogError(err)
			}
			ret = append(ret, n)
		}
	}
	return ret
}

func JoinIntSliceWithComma(data []int) string {
	if data == nil || len(data) == 0 {
		return ""
	}
	dataStr := make([]string, len(data))
	for i, n := range data {
		dataStr[i] = strconv.Itoa(n)
	}
	return strings.Join(dataStr, ",")
}

func Value(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func Ptr(s string) *string {
	return &s
}

// JsonStringForDebugLog 用于日志打印json格式，会进行logLevel判断，只适用于debug日志输出复杂参数，切勿随处使用
//
//	ps：与logger.CtxLogDebugf()方法联合使用
//	@param obj 待marshal序列化的实例
//	@return 实例序列化后，json字符串
//	 1.序列化失败，返回空字符串
//	 2.日志级别非debug时，返回特殊提示内容：logLevel is not debug, please after considering the code, analyze whether you need to use debug log output
//	<AUTHOR> Bo | SLS BE | <EMAIL>
func JsonStringForDebugLog(obj interface{}) string {
	// 日志级别为debug，才进行序列化
	if logger.IsDebugEnabled() {
		data, err := jsoniter.MarshalToString(obj)
		if err != nil {
			return ""
		}
		return data
	}
	return "logLevel is not debug, please after considering the code, analyze whether you need to use debug log output"
}

func GenKey(spliter string, keys ...string) string {
	if len(keys) == 0 {
		return ""
	}
	b := bytes.Buffer{}
	for i, key := range keys {
		if i != 0 && spliter != "" {
			b.WriteString(spliter)
		}
		b.WriteString(key)
	}
	return b.String()
}
