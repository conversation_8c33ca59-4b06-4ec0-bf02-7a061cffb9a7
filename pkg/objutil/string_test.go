package objutil

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestIsPureDigit(t *testing.T) {
	tests := []struct {
		k     string
		input string
	}{
		{
			k:     "f1",
			input: "-1",
		},
		{
			k:     "f2",
			input: "adsa",
		},
		{
			k:     "t1",
			input: "0123456789",
		},
		{
			k:     "f3",
			input: "%#$@!(*^%",
		},
		{
			k:     "f4",
			input: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			ret := IsPureDigit(tt.input)
			switch tt.k {
			case "f1", "f2", "f3":
				assert.Equal(t, false, ret)
			case "t1":
				assert.Equal(t, true, ret)
			}
		})
	}
}
