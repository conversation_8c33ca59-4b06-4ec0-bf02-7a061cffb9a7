package objutil

import "reflect"

func CompareInterfaces(a, b interface{}) bool {
	// 如果 a 和 b 都是 nil，则认为相等
	if a == nil && b == nil {
		return true
	}
	// 如果 a 和 b 只有一个是 nil，则不相等
	if a == nil || b == nil {
		return false
	}

	// 获取两个参数的反射值
	vA := reflect.ValueOf(a)
	vB := reflect.ValueOf(b)

	// 如果类型不一致，直接返回 false
	if vA.Type() != vB.Type() {
		return false
	}

	// 如果是指针类型，解引用后进行比较
	if vA.Kind() == reflect.Ptr && vB.Kind() == reflect.Ptr {
		// 如果指针都为 nil，则认为相等
		if vA.IsNil() && vB.IsNil() {
			return true
		}
		// 如果其中一个指针为 nil，返回 false
		if vA.IsNil() || vB.IsNil() {
			return false
		}
		// 对指针解引用，递归进行比较
		return CompareInterfaces(vA.Elem().Interface(), vB.Elem().Interface())
	}

	// 对于非指针类型，直接比较
	if vA.Kind() != vB.Kind() {
		return false
	}

	switch vA.Kind() {
	// 处理基础类型（int, string, float, bool等）
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
		reflect.Float32, reflect.Float64, reflect.String:
		return vA.Interface() == vB.Interface()

	// 处理数组和切片类型
	case reflect.Array, reflect.Slice:
		if vA.Len() != vB.Len() {
			return false
		}
		for i := 0; i < vA.Len(); i++ {
			if !CompareInterfaces(vA.Index(i).Interface(), vB.Index(i).Interface()) {
				return false
			}
		}
		return true

	// 处理结构体类型，递归比较每个字段
	case reflect.Struct:
		for i := 0; i < vA.NumField(); i++ {
			if !CompareInterfaces(vA.Field(i).Interface(), vB.Field(i).Interface()) {
				return false
			}
		}
		return true

	// 处理映射类型（map）
	case reflect.Map:
		if vA.Len() != vB.Len() {
			return false
		}
		for _, keyA := range vA.MapKeys() {
			valA := vA.MapIndex(keyA)
			valB := vB.MapIndex(keyA)
			if !valB.IsValid() || !CompareInterfaces(valA.Interface(), valB.Interface()) {
				return false
			}
		}
		return true

	// 处理接口类型
	case reflect.Interface:
		if vA.IsNil() && vB.IsNil() {
			return true
		}
		if vA.IsNil() || vB.IsNil() {
			return false
		}
		// 如果接口非空，递归比较底层类型的值
		return CompareInterfaces(vA.Elem().Interface(), vB.Elem().Interface())

	default:
		// 对于不支持的类型，直接返回 false
		return false
	}
}
