package ratelimiter

import (
	"context"
	"errors"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	jsoniter "github.com/json-iterator/go"
	"sync"

	"github.com/juju/ratelimit"
)

var (
	gBucketMap sync.Map
	QpsLimit   = errors.New("qps limit")
)

type RateLimitParam struct {
	BucketName string  `json:"bucket_name"`
	Rate       float64 `json:"rate"`
	Capacity   int64   `json:"capacity"`
	Enable     bool    `json:"enable"`
}

const (
	defaultRate     = 200
	defaultCapacity = 1000
)

// GenRateLimitParam 将入参解析为[服务模块][接口名字]{接口限流配置}的map
// rateLimiterCon： 1.key为服务模块，如http，grpc，kafka等；2.value为该模块下的限流配置
func GenRateLimitParam(rateLimiterCon map[string]string) map[string]map[string]RateLimitParam {
	rateLimitMap := make(map[string]map[string]RateLimitParam)
	for systemCode, rateLimitConfig := range rateLimiterCon {
		if len(rateLimitConfig) == 0 {
			continue
		}
		configList := make([]RateLimitParam, 0)
		err := jsoniter.Unmarshal([]byte(rateLimitConfig), &configList)
		if err != nil {
			continue
		}
		configMap := make(map[string]RateLimitParam)
		for _, tmpConfig := range configList {
			configMap[tmpConfig.BucketName] = tmpConfig
		}
		rateLimitMap[systemCode] = configMap
	}
	return rateLimitMap
}

// LoadRateLimitConfig 解析限流配置
// bucket_name 表示是限流唯一标识。rate，表示每秒允许多少个请求。capacity，表示对应的令牌桶的大小，可以理解为突发时允许的访问量。
// See: http://en.wikipedia.org/wiki/Token_bucket
// rateLimiterConfig： 1.key为服务模块，如http，grpc，kafka等；2.value为该模块下的限流配置
func LoadRateLimitConfig(rateLimiterConfig map[string]string) {
	rateLimitConfig := GenRateLimitParam(rateLimiterConfig)
	beforeBucketMap := make(map[string]bool, 0)
	afterBucketMap := make(map[string]bool, 0)
	gBucketMap.Range(func(key, value interface{}) bool {
		beforeBucketMap[key.(string)] = true
		return true
	})
	for _, tmpRateLimitConfig := range rateLimitConfig {
		for bucketName, rateLimitParam := range tmpRateLimitConfig {
			if !rateLimitParam.Enable {
				continue
			}
			rate := rateLimitParam.Rate
			capacity := rateLimitParam.Capacity

			afterBucketMap[bucketName] = true
			NewRateLimitBucket(bucketName, rate, capacity)
		}
	}

	// delete disable bucket
	for tmpBucketName := range beforeBucketMap {
		if _, ok := afterBucketMap[tmpBucketName]; !ok {
			gBucketMap.Delete(tmpBucketName)
		}
	}
}

func NewRateLimitBucket(bucketName string, rate float64, capacity int64) {
	if rate <= 0 {
		rate = defaultRate
	}
	if capacity <= 0 {
		capacity = defaultCapacity
	}
	bucket := ratelimit.NewBucketWithRate(rate, capacity)
	gBucketMap.Store(bucketName, bucket)

	logger.LogInfof("NewRateLimitBucket| bucket name:%v, rate:%v, capacity:%v", bucketName, rate, capacity)
}

func CheckRateLimit(ctx context.Context, bucketName string) bool {
	value, ok := gBucketMap.Load(bucketName)
	if !ok {
		return true
	}
	bucket := value.(*ratelimit.Bucket)
	result := bucket.TakeAvailable(1)
	if result == 0 {
		logger.CtxLogErrorf(ctx, "check-limiter,rejected", bucketName)
		_ = monitor.AwesomeReportEvent(ctx, "RateLimit", bucketName, "0", "")
	}
	return result != 0
}
