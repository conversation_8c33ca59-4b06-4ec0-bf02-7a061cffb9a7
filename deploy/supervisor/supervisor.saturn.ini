[unix_http_server]
file=/workspace/deploy/supervisor.saturn.sock

[supervisord]
logfile=/workspace/log/supervisor.saturn.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/workspace/log/supervisor.saturn.pid
nodaemon=false
minfds=1024
minprocs=200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///workspace/deploy/supervisor.saturn.sock

[program:task_server]
environment=CHASSIS_CONF_DIR="/workspace/conf",REST_PORT=%(ENV_PORT)s,GOLANG_PROTOBUF_REGISTRATION_CONFLICT="warn"
command=/bin/sh -c ./task_server
directory=/workspace/
user=root
autorestart=true
redirect_stderr=true
stdout_logfile=/workspace/log/daemon.log
loglevel=info
startsecs=300

[include]
files = /workspace/saturn-executor/saturn-executor-master-SNAPSHOT/bin/*.conf
