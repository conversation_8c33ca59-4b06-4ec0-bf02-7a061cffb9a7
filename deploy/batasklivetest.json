{"project_name": "smartrouting", "module_name": "batasklivetest", "build": {"docker_image": {"base_image": "harbor.shopeemobile.com/shopee/sls-cicd-base:go1.15-slim-0316", "squash": false, "dependent_libraries_files": ["."], "run_commands": ["GOPROXY=''", "GOPRIVATE=git.garena.com go mod download", "GO111MODULE=on GOPRIVATE=git.garena.com go build -o batask_server ./cmd/batask_server", "go clean -cache", "go clean -modcache", "go get -u git.garena.com/shopee/bg-logistics/go/go-module-collector", "go-module-collector"], "image_language": "golang", "enable_multi_stage_golang_image": true, "multi_stage_base_image": "harbor.shopeemobile.com/shopee/saturn-executor:v0.4.0", "workdir": "/workspace", "generate_dockerignore": true}, "template_files": [], "commands": ["ln -sf ./application.apollo.json ./config.apollo.json", "chmod +x deploy/supervisor/batask_server_start.sh", "chmod +x deploy/supervisor/is_batask_running.sh", "chmod +x deploy/scripts/batasklivetest-saturn-launch.sh", "go-module-collector -step=1"]}, "run": {"depend_services": [], "template_files": [], "pre_hook_commands": [], "nurse_commands": [], "command": "sscinit -n -c deploy/supervisor/supervisor.batasklivetest.ini", "check": {"protocol": "COMMAND", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "./deploy/supervisor/is_batask_running.sh", "grace_period": 15}, "liveness": {"protocol": "COMMAND", "timeout": 2, "retry": 12, "max_fails": 12, "interval": 10, "endpoint": "./deploy/supervisor/is_batask_running.sh", "grace_period": 180}, "smoke": {"protocol": "COMMAND", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "./deploy/supervisor/is_batask_running.sh", "grace_period": 180}, "enable_prometheus": true, "acquire_prometheus_port": true, "prometheus_path": "/metrics"}, "deploy": {"deploy_timeout": 1800, "idcs": {"live": {"sg": ["sg", "sg1"], "my": ["sg", "sg1"], "th": ["sg", "sg1"], "ph": ["sg", "sg1"], "vn": ["sg", "sg1"], "id": ["sg", "sg1"], "tw": ["sg", "sg1"], "br": ["sg", "sg1"], "mx": ["sg", "sg1"], "ar": ["sg", "sg1"], "pl": ["sg", "sg1"], "es": ["sg", "sg1"], "fr": ["sg", "sg1"], "in": ["sg", "sg1"], "co": ["sg", "sg1"], "cl": ["sg", "sg1"]}}, "resources": {"test": {"cpu": 4, "mem": 4098}, "staging": {"cpu": 8, "mem": 8192}, "uat": {"cpu": 8, "mem": 8192}, "live": {"cpu": 8, "mem": 16384}}, "instances": {"test": {"vn": 1, "my": 1, "th": 1, "ph": 1, "id": 1, "sg": 1, "tw": 1, "br": 1, "mx": 1}, "staging": {"vn": 1, "my": 1, "th": 1, "ph": 1, "id": 1, "sg": 1, "tw": 1, "br": 1, "mx": 1}, "uat": {"vn": 1, "my": 1, "th": 1, "ph": 1, "id": 1, "sg": 1, "tw": 1, "br": 1, "mx": 1}, "live": {"vn": 2, "my": 2, "th": 2, "ph": 2, "id": 2, "sg": 2, "tw": 2, "br": 2, "mx": 2, "co": 2, "cl": 2}}}}