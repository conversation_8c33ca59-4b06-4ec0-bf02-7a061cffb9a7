{"#": "for a detail explaination of this file.", "project_name": "smartrouting", "module_name": "admin", "build": {"commands": ["go-module-collector -step=1", "mkdir -p /tmp && cd /tmp && GO111MODULE=on GOPRIVATE=git.garena.com go get git.garena.com/shopee/bg-logistics/techplatform/replayer-agent/v2 && cd -", "mv $GOPATH/bin/replayer-agent /workspace/", "mkdir -p /workspace/agentconf/agent_client", "mv $GOPATH/pkg/mod/git.garena.com/shopee/bg-logistics/techplatform/replayer-agent/*/conf/agent_client/* /workspace/agentconf/agent_client/"], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/sls-cicd-base:go1.15-slim-0316", "squash": false, "run_commands": ["mkdir -p /tmp/goc && cd /tmp/goc && go mod init test && GO111MODULE=on go install git.garena.com/shopee/bg-logistics/logistics/goc && cd - && rm -rf /tmp/goc", "GOPRIVATE=git.garena.com go mod download", "GO111MODULE=on GOPRIVATE=git.garena.com goc build --buildflags=\"-ldflags '-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn'\" -o admin_server ./cmd/admin_server", "go clean -cache", "go clean -modcache", "mv $GOPATH/bin/goc /workspace", "go get -u git.garena.com/shopee/bg-logistics/go/go-module-collector", "go-module-collector"], "dependent_libraries_files": ["."], "res_files": [], "image_language": "golang", "enable_multi_stage_golang_image": true, "multi_stage_base_image": "harbor.shopeemobile.com/shopee/sls-runtime-base:slim-0224", "workdir": "/workspace", "generate_dockerignore": true}}, "run": {"depend_services": [], "command": "sscinit -n -c deploy/supervisor/supervisor.admin.goc.ini", "#": "The time unit for `grace_period` and `timeout` is second.", "check": {"protocol": "HTTP", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "/ping", "grace_period": 15}, "liveness": {"protocol": "HTTP", "timeout": 2, "retry": 12, "max_fails": 12, "interval": 10, "endpoint": "/ping", "grace_period": 480}, "smoke": {"protocol": "HTTP", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "/ping", "grace_period": 480}, "enable_prometheus": true, "acquire_prometheus_port": true, "prometheus_path": "/metrics", "pre_hook_commands": []}, "deploy": {"deploy_timeout": 1800, "idcs": {"live": {"sg": ["sg", "sg1"], "my": ["sg", "sg1"], "th": ["sg", "sg1"], "ph": ["sg", "sg1"], "vn": ["sg", "sg1"], "id": ["sg", "sg1"], "tw": ["sg", "sg1"], "br": ["sg", "sg1"], "mx": ["sg", "sg1"], "ar": ["sg", "sg1"], "pl": ["sg", "sg1"], "es": ["sg", "sg1"], "fr": ["sg", "sg1"], "in": ["sg", "sg1"], "co": ["sg", "sg1"], "cl": ["sg", "sg1"]}}, "resources": {"live": {"cpu": 8, "mem": 8192}, "staging": {"cpu": 8, "mem": 8192}, "uat": {"cpu": 8, "mem": 8192}, "test": {"cpu": 4, "mem": 4096}}, "instances": {"live": {"sg": 2, "my": 2, "ph": 2, "th": 2, "vn": 2, "id": 2, "tw": 2, "br": 2, "mx": 2, "ar": 2, "pl": 2, "es": 2, "fr": 2, "in": 2, "co": 2, "cl": 2}, "canary": {"sg": 1, "my": 1, "ph": 1, "th": 1, "vn": 1, "id": 1, "tw": 1, "br": 1, "mx": 1, "ar": 1, "pl": 1, "es": 1, "fr": 1, "in": 1}}}}