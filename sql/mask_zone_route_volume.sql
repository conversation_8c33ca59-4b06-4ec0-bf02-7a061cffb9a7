CREATE TABLE `mask_product_route_volume_tab` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `route_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
 `rule_volume_id` bigint(20) unsigned NOT NULL COMMENT 'id of mask_product_rule_volume_tab, every route volume is belong to one rule volume',
 `mask_product_id` bigint(20) unsigned NOT NULL,
 `component_product_id` bigint(20) unsigned NOT NULL,
 `origin_district_id` int(10) unsigned NOT NULL COMMENT 'pickup address',
 `destination_district_id` int(10) unsigned NOT NULL COMMENT 'pickup address',
 `max_capacity` int(10) unsigned NOT NULL COMMENT 'default 999999999',
 `min_volume` int(10) unsigned NOT NULL COMMENT 'default 999999999',
 `ctime` int(10) unsigned NOT NULL,
 `mtime` int(10) unsigned NOT NULL,
 `is_hard_cap` tinyint(4) DEFAULT '0',
 <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
 <PERSON>EY `idx_rule_volume_id` (`rule_volume_id`),
 <PERSON><PERSON><PERSON> `uniq_rule_mask_component_origin_dest` (`rule_volume_id`,`mask_product_id`,`component_product_id`,`origin_district_id`,`destination_district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE TABLE `mask_product_zone_volume_tab` (
`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
`zone_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
`rule_volume_id` bigint(20) unsigned NOT NULL COMMENT 'id of mask_product_rule_volume_tab, every route volume is belong to one rule volume',
`mask_product_id` bigint(20) unsigned NOT NULL,
`component_product_id` bigint(20) unsigned NOT NULL,
`district_id` int(10) unsigned NOT NULL COMMENT 'does not distinguish pickup or deliver address',
`origin_max_capacity` int(10) unsigned NOT NULL COMMENT 'default 999999999',
`origin_min_volume` int(10) unsigned NOT NULL COMMENT 'default 999999999',
`destination_max_capacity` int(10) unsigned NOT NULL COMMENT 'default 999999999',
`destination_min_volume` int(10) unsigned NOT NULL COMMENT 'default 999999999',
`ctime` int(10) unsigned NOT NULL,
`mtime` int(10) unsigned NOT NULL,
`is_hard_cap` tinyint(4) DEFAULT '0',
PRIMARY KEY (`id`),
KEY `idx_rule_volume_id` (`rule_volume_id`),
KEY `uniq_rule_mask_component_district` (`rule_volume_id`,`mask_product_id`,`component_product_id`,`district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;