# Requirements Document

## Introduction

This feature involves modifying the CC routing rule update logic to remove the routing type validation check. Currently, when updating existing rules, the system validates that the routing type in the request matches the existing rule's type and throws an error if they don't match. The requirement is to remove this validation and allow the routing type from the request to take precedence.

## Requirements

### Requirement 1

**User Story:** As an admin user, I want to update CC routing rules without being restricted by the existing rule's routing type, so that I can change the routing type during updates without encountering validation errors.

#### Acceptance Criteria

1. WH<PERSON> calling UpdateShopGroupRules method THEN the system SHALL NOT validate that the request routing type matches the existing rule's routing type
2. <PERSON><PERSON><PERSON> calling UpdateCategoryRules method THEN the system SHALL NOT validate that the request routing type matches the existing rule's routing type
3. <PERSON><PERSON><PERSON> calling UpdateWeightCategoryRules method THEN the system SHALL NOT validate that the request routing type matches the existing rule's routing type
4. WHEN updating an existing CC routing rule THEN the system SHALL use the routing type provided in the request parameters
5. WHEN the routing type validation is removed THEN the system SHALL maintain all other existing validation logic

### Requirement 2

**User Story:** As a system administrator, I want the update process to maintain existing functionality for supported routing types, so that the system continues to work correctly for valid operations.

#### Acceptance Criteria

1. WHEN the routing type is not supported (not 3, 4, or 5) THEN the system SHALL return an error with message "unsupported routing type"
2. WHEN the update operation fails for business logic reasons THEN the system SHALL return the appropriate error message
3. WHEN the update operation succeeds THEN the system SHALL return a success response
4. WHEN logging update operations THEN the system SHALL continue to log the routing type, product ID, operator, and other relevant information

### Requirement 3

**User Story:** As a developer, I want the code changes to be minimal and focused on the three specific update methods, so that the risk of introducing bugs is minimized while achieving the desired functionality.

#### Acceptance Criteria

1. WHEN modifying UpdateShopGroupRules method THEN the system SHALL only remove the routing type validation check
2. WHEN modifying UpdateCategoryRules method THEN the system SHALL only remove the routing type validation check  
3. WHEN modifying UpdateWeightCategoryRules method THEN the system SHALL only remove the routing type validation check
4. WHEN processing updates THEN the system SHALL maintain all existing error handling for other validation failures
5. WHEN processing updates THEN the system SHALL maintain all existing logging functionality
6. WHEN processing updates THEN the system SHALL maintain the existing operator and validateOnly parameter handling