# Design Document

## Overview

This design addresses the removal of routing type validation checks in the CC routing rule update methods. Currently, the three update methods (`UpdateShopGroupRules`, `UpdateCategoryRules`, and `UpdateWeightCategoryRules`) validate that the existing rule's routing type matches the expected type for that method. The requirement is to remove this validation and allow the routing type from the request to take precedence.

## Architecture

The change affects the service layer in the CC routing usecase, specifically three methods in the `CCRoutingServiceImpl` struct:

- `UpdateShopGroupRules` in `internal/usecase/cc_routing/shop_group_rules.go`
- `UpdateCategoryRules` in `internal/usecase/cc_routing/category_rules.go`  
- `UpdateWeightCategoryRules` in `internal/usecase/cc_routing/weight_category_rules.go`

The facade layer (`internal/adminfacade/cc_routing.go`) will remain unchanged as it already correctly routes to the appropriate update method based on the request's routing type.

## Components and Interfaces

### Current Flow
1. Facade receives update request with routing type and rule ID
2. Facade calls appropriate update method based on routing type
3. Update method validates existing rule type matches expected type
4. If validation passes, method processes the update

### Modified Flow
1. Facade receives update request with routing type and rule ID
2. <PERSON><PERSON>de calls appropriate update method based on routing type
3. Update method skips type validation (removed)
4. Method processes the update directly

### Affected Methods

#### UpdateShopGroupRules
**Location**: `internal/usecase/cc_routing/shop_group_rules.go`
**Current validation code to remove**:
```go
// 检查规则类型是否匹配
if rule.RoutingType != cc_routing_rule.CCRoutingTypeShopGroup {
    return srerr.New(srerr.ParamErr, nil, "rule id %d is not a shop group rule", id)
}
```

#### UpdateCategoryRules  
**Location**: `internal/usecase/cc_routing/category_rules.go`
**Current validation code to remove**:
```go
// 检查规则类型是否匹配
if rule.RoutingType != cc_routing_rule.CCRoutingTypeCategory {
    return srerr.New(srerr.ParamErr, nil, "rule id %d is not a category rule", id)
}
```

#### UpdateWeightCategoryRules
**Location**: `internal/usecase/cc_routing/weight_category_rules.go`  
**Current validation code to remove**:
```go
// 检查规则类型是否匹配
if rule.RoutingType != cc_routing_rule.CCRoutingTypeWeightCategory {
    return srerr.New(srerr.ParamErr, nil, "rule id %d is not a weight category rule", id)
}
```

## Data Models

No changes to data models are required. The existing `CCRoutingRule` struct and related types will continue to be used as-is.

## Error Handling

### Removed Error Cases
- Type mismatch errors will no longer be thrown during updates
- Error messages like "rule id X is not a [type] rule" will be eliminated

### Maintained Error Cases
- Rule not found errors (when ID doesn't exist)
- File parsing errors
- Data validation errors (empty fields, invalid formats, etc.)
- Business logic validation errors (duplicate IDs, invalid weight ranges, etc.)
- CC value validation errors

## Testing Strategy

### Unit Tests
- Update existing unit tests to remove expectations for type validation errors
- Verify that update methods can process rules regardless of existing type
- Ensure all other validation logic continues to work correctly

### Integration Tests  
- Test updating rules with different routing types than originally created
- Verify that the facade layer correctly routes to update methods based on request type
- Confirm that successful updates maintain data integrity

### Edge Cases
- Update a shop group rule using category update method (should work)
- Update with invalid rule ID (should still fail appropriately)
- Update with malformed file data (should still fail appropriately)

## Implementation Notes

### Code Changes Required
1. Remove type validation checks from three update methods
2. Keep all other existing validation logic intact
3. Maintain existing logging and error handling for other cases

### Backward Compatibility
- API contracts remain unchanged
- Existing clients will continue to work without modification
- Only the internal validation behavior changes

### Risk Mitigation
- Changes are minimal and focused
- All other validation logic remains in place
- Existing unit tests will catch any regression issues
- The facade layer routing logic ensures appropriate method selection