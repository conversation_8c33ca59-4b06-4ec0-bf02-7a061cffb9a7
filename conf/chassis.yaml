admin_server:
  application:
    name: smartrouting-${MODULE_NAME||api}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
  initial:
    config:
      appId: smart-routing
      cluster: ${CID||default}
      namespaceList: admin_server,config,wbc_public
      accessKey: efcf9b54d1b03db081112170ba2c8f98
    cat:
      domain: smartrouting-${MODULE_NAME||api}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: sg11,us3,us2
  client:
    rest:
      default:
        dialTimeoutInMs: 1000 #建立连接超时时间，单位ms，默认值为100
        timeoutInMs: 3000 #HTTP请求超时时间，单位ms，默认值为0，表示不设置超时（但不影响context里已有的超时）。
        maxIdleConns: 0 #连接池最大的空闲连接数，默认为10240，0表示不限制
        maxIdleConnsPerHost: 50 #连接池每个ip地址的最大空闲连接数，默认为10240，0表示不限制
        maxConnsPerHost: 10 #连接池每个ip地址的最大连接数，默认为10240，0表示不限制
    saturn:
      default:
        retryMax: 3
        writeTimeoutSec: 3

grpc_server:
  application:
    name: smartrouting-${MODULE_NAME||api}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    grpc:
      listenAddress: 0.0.0.0:${PORT||6100}       # 设置为0.0.0.0 默认会查找网卡地址 双竖线后为默认值
      jsonGateway:
        enable: true
        useProtoName: true
        omitEmpty: false
  client:
    rest:
      default:
        dialTimeoutInMs: 1000 #建立连接超时时间，单位ms，默认值为100
        timeoutInMs: 3000 #HTTP请求超时时间，单位ms，默认值为0，表示不设置超时（但不影响context里已有的超时）。
        maxIdleConns: 0 #连接池最大的空闲连接数，默认为10240，0表示不限制
        maxIdleConnsPerHost: 50 #连接池每个ip地址的最大空闲连接数，默认为10240，0表示不限制
        maxConnsPerHost: 10 #连接池每个ip地址的最大连接数，默认为10240，0表示不限制
    saturn:
      default:
        retryMax: 3
        writeTimeoutSec: 3
  initial:
    config:
      appId: smart-routing
      cluster: ${APOLLO_CLUSTER||default}
      namespaceList: grpc_server,config,wbc_public
      accessKey: efcf9b54d1b03db081112170ba2c8f98
    cat:
      domain: smartrouting-${MODULE_NAME||api}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: sg11,us3,us2
    log:
      disableShadowLog: true
  plugins:
    logProvider:
      maxLogLen: 10240 #请求日志的最大长度
      logRequestHeader: false
      logRequestParameter: false
      logRequestBody: true
      logResponseHeader: false
      logResponseBody: true
    rateLimit: #限频(限流)插件
      service: #服务端入口流量的频限
        /smart_routing_protobuf.SmartRouting/SelectLane: #服务端接口名为 /smart_routing_protobuf.SmartRouting/SelectLane 的频限配置(如果是http服务，此处的名称为对应的处理方法的名称，大小写敏感; grpc 服务则对应pb文件里的FullMethod）
          __total: #接口下所有来源(来源是指谁调用我们的接口，谁就是一种来源)的流量的频限配置
            enabled: false
            local:
              qps: 1000
    recorder:
      enabled: false
      address: "https://athena.ssc.shopeemobile.com"
      allowIps: "127.0.0.1"
      expireTimestamp: **********
      ratio: 0.5
      pathWhiteList:
        - requestType: grpc
          interfaceName: "/smart_routing_protobuf.SmartRouting/SelectLane"
    replayer:
      enabled: false
      agent_use: true
      address: https://athena.ssc.${ENV||local}.shopeemobile.com
    router:
      mock:
        - serviceName: "lps.${ENV||local}.shopee.${CID||sg}"  # 目标服务的名字，支持正则，只有发给该目标服务的请求才会进行mock处理
          interfaceName: "process_routing_log"            # 接口名称，可以包含多个，多个接口名称之间使用","分隔，每个接口名称支持正则。可以不指定，表示发给上述目标服务下所有接口的请求都会进行mock处理。
          forLiveShadow: true                       # 表示是否对live环境的shadow流量生效。默认情况下，只对non-live环境的任意流量生效。
          forShadowOnly: true                       # 表示是否只对shadow流量生效。默认情况下，non-live环境的任意流量都生效。
          # kafka生产端不支持将请求发送给mock服务
          mockWhenInfNotMatch: false                 # 当service name和interface name都不匹配时才进行mock, 默认为false（匹配上的都不mock，即取反）
  bypass_vncb: 1


task_server:
  application:
    name: smartrouting-${MODULE_NAME||api}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
    saturn:
      listenAddress: smartrouting.ssc.${ENV||local}.shopee.${CID||sg} #在配置中此处写上saturn的namespace
    admin:
      listenAddress: 0.0.0.0:9393
  initial:
    config:
      appId: smart-routing
      cluster: ${CID||default}
      namespaceList: task_server,config
      accessKey: efcf9b54d1b03db081112170ba2c8f98
    cat:
      domain: smartrouting-${MODULE_NAME||api}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: sg11,us3,us2
  client:
    rest:
      default:
        dialTimeoutInMs: 1000 #建立连接超时时间，单位ms，默认值为100
        timeoutInMs: 3000 #HTTP请求超时时间，单位ms，默认值为0，表示不设置超时（但不影响context里已有的超时）。
        maxIdleConns: 0 #连接池最大的空闲连接数，默认为10240，0表示不限制
        maxIdleConnsPerHost: 50 #连接池每个ip地址的最大空闲连接数，默认为10240，0表示不限制
        maxConnsPerHost: 10 #连接池每个ip地址的最大连接数，默认为10240，0表示不限制
    saturn:
      default:
        retryMax: 3
        writeTimeoutSec: 3

batask_server:
  application:
    name: smartrouting-${MODULE_NAME||api}-${ENV||local}-${CID||sg}
    environment: ${SSC_ENV||local}
  service:
    rest:
      listenAddress: 0.0.0.0:${PORT||8090}
    saturn:
      listenAddress: smartrouting-batask.ssc.${ENV||local}.shopee.${CID||sg} #在配置中此处写上saturn的namespace
  initial:
    config:
      appId: smart-routing
      cluster: ${APOLLO_CLUSTER||default}
      namespaceList: batask_server,config
      accessKey: efcf9b54d1b03db081112170ba2c8f98
    cat:
      domain: smartrouting-${MODULE_NAME||api}-${ENV||LOCAL}-${CID||SG}
      drDomainForIdc: sg11,us3,us2
  client:
    rest:
      default:
        dialTimeoutInMs: 1000 #建立连接超时时间，单位ms，默认值为100
        timeoutInMs: 3000 #HTTP请求超时时间，单位ms，默认值为0，表示不设置超时（但不影响context里已有的超时）。
        maxIdleConns: 0 #连接池最大的空闲连接数，默认为10240，0表示不限制
        maxIdleConnsPerHost: 50 #连接池每个ip地址的最大空闲连接数，默认为10240，0表示不限制
        maxConnsPerHost: 10 #连接池每个ip地址的最大连接数，默认为10240，0表示不限制
    saturn:
      default:
        retryMax: 3
        writeTimeoutSec: 3

config.saturn_namespace:
  smr_namespace: "smartrouting.ssc.${ENV||local}.shopee.${CID||sg}"
  smr_ba_namespace: "smartrouting-batask.ssc.${ENV||local}.shopee.${CID||sg}"
config:
  prometheus_report:
    switch: true
#config.rate_limiter:
#  http: '[{"bucket_name": "/api/admin/product/get_line_dict_list","rate": 200,"capacity": 1000,"enable": true}]'
config.redis.LivetestAddr: "proxy.cache-codis3-ctl.i.sz.shopee.io:8173"
config.redis.LivetestPass: "3.nBEn3HfKwBjczj"


#config.breaker_switch:
#  api_switch: '{"/api/admin/product/get_line_dict_list":true}'
#  global_switch: false

config.layer_cache:
  HavePassword: true
  IsMonitor: true
  MemLogicTTL: 60
  MemNumOfCounters: *********0
  MemPhysicalTTL: 180
  MemSize: *********
  Password: test
  PenetrationQpsLimit: 2000
  RedisAddr: proxy.cache-codis-sg2.i.test.sz.shopee.io:9999
  RedisDB: 1
  RedisDialTimeout: 5
  RedisMinIdleConns: 15
  RedisPoolSize: 80
  RedisPoolTimeout: 10
  RedisReadTimeout: 2
  RedisWriteTimeout: 3

config.enable_allocation_log: true

#config.product_allocation:
#  DisableProductPriority: false

# routing forecast kafka config
config.routing_forecast_task_kafka.Servers:
  - "kafka.public-test.sg2.i.sz.shopee.io:9092"
config.routing_forecast_task_kafka.Topic: "lps_smart_routing_forecasting_${ENV||test}_${CID||sg}"
config.routing_forecast_task_kafka.GroupID: "forecasting-worker-${ENV||test}_${CID||sg}"
config.routing_forecast_task_kafka.MaxWait: 1

config.hard_criteria_task.BatchNum: 500
config.hard_criteria_task.CheckTTL: 1800
config.hard_criteria_task.OrderCount: 1500000
config.hard_criteria_task.PendingNum: 10
config.hard_criteria_task.Qps: 100

# config schedule visual clear time, default 60 days ago
config.schedule_visual_config:
  clear_time: 60

# SSCSMR-3055 double write mask+fulfillment redis key
config.allocate_volume_count.allow_new_redis_key: true